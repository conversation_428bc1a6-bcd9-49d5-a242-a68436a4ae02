# See http://help.github.com/ignore-files/ for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles. #
/log/*.log
/tmp

# Rubbish MacOS and Windows files #
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.swp


# Logs and databases #
*.log
*.sqlite3
*.sqlite3-journal
/pgdata
.byebug_history
#/config/database.yml

# Code coverage report and generated docs #
/coverage
/doc

# Ignore env file
.env
.vscode

/.ruby-gemset

/public/packs
/public/packs-test
/node_modules
/yarn-error.log
yarn-debug.log*
.yarn-integrity

# Ignore master key for decrypting credentials and more.
/config/master.key

# Ignore automatically generated js-routes files.
/assets/routes.js
/assets/routes.d.ts
