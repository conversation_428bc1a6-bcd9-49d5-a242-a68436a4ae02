Style/Documentation:
  Enabled: false
Style/FrozenStringLiteralComment:
  Enabled: false
Style/ClassAndModuleChildren:
  Enabled: false
Layout/AccessModifierIndentation:
  Enabled: false
Layout/HashAlignment:
  Enabled: false
Style/TrailingCommaInHashLiteral:
  Enabled: false
Layout/IndentationStyle:
  Enabled: false
Layout/IndentationWidth:
  Enabled: false
Layout/SpaceBeforeBlockBraces:
  Enabled: false
Layout/SpaceInsideBlockBraces:
  Enabled: false
Layout/TrailingEmptyLines:
  Enabled: false
Layout/EmptyLinesAroundClassBody:
  Enabled: false
Layout/EmptyLinesAroundBlockBody:
  Enabled: false
Layout/EmptyLinesAroundModuleBody:
  Enabled: false
Style/EmptyCaseCondition:
  Enabled: false
Layout/CaseIndentation:
  Enabled: false
Style/RaiseArgs:
  Enabled: false
Style/NumericPredicate:
  Enabled: false
Layout/LineLength:
  Enabled: false
Metrics/AbcSize:
  Enabled: false
Metrics/MethodLength:
  Enabled: false
Style/ConditionalAssignment:
  Enabled: false
Layout/FirstHashElementIndentation:
  Enabled: false
Layout/CommentIndentation:
  Enabled: false
Style/EmptyElse:
  Enabled: false
Style/NegatedIf:
  Enabled: false
Layout/ArgumentAlignment:
  Enabled: false
Layout/FirstHashElementIndentation:
  Enabled: false
Layout/IndentationConsistency:
  Enabled: false
Style/CommentedKeyword:
  Enabled: false
Metrics/BlockLength:
  Enabled: false
Layout/ClosingParenthesisIndentation:
  Enabled: false
Layout/EndAlignment:
  Enabled: false
Naming/PredicateName:
  Enabled: false
Naming/MemoizedInstanceVariableName:
  Enabled: false
Style/IfUnlessModifier:
  Enabled: false
Layout/MultilineMethodCallBraceLayout:
  Enabled: false

# app/services specific excludes
Lint/MissingSuper:
  Enabled: false
Naming/RescuedExceptionsVariableName:
  Enabled: false
Metrics/CyclomaticComplexity:
  Enabled: false
Metrics/PerceivedComplexity:
  Enabled: false
Metrics/ClassLength:
  Enabled: false
Style/RescueStandardError:
  Enabled: false
Naming/RescuedExceptionsVariableName:
  Enabled: false
Style/RescueModifier:
  Enabled: false
Style/RedundantBegin:
  Enabled: false
Style/GuardClause:
  Enabled: false
Style/RedundantAssignment:
  Enabled: false
Style/RedundantException:
  Enabled: false
Naming/VariableNumber:
  Enabled: false
Metrics/ParameterLists:
  Enabled: false

AllCops:
  TargetRubyVersion: 2.7.1
  Exclude:
    - 'app/assets/**/*'
    - 'app/views/**/*'
    - 'node_modules/**/*'
    - 'tmp/**/*'
    - 'vendor/**/*'
    - '.git/**/*'
    - 'db/**/*'
    - 'config/**/*'
    - 'script/**/*'
    - 'bin/**/*'
    - 'Gemfile.lock'
    - 'Guardfile'