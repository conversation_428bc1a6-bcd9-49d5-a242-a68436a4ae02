class Invoices::RecipientGeneratedInvoice < YordarPdf

  def initialize(invoice:)
    @invoice = invoice

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    font_size 9
    generate_header
    generate_table
  end

private

  attr_reader :invoice

  def report_folder
    'tax_invoice'
  end

  def report_reference
   "#{supplier.id}-#{orders.first.id}#{orders.last.id}"
  end

  def generate_header
    supplier_suburb = supplier.company_address_suburb.present? ? supplier.company_address_suburb.label : ''

    bounding_box [210, bounds.top - 20], width: 320, height: 100 do
      font_size 20 do
        text '<b>Recipient Generated Invoice</b>', align: :right, inline_format: true
        text "<b>##{invoice.number}</b>", align: :right, inline_format: true
      end
      font_size 10 do
        text "Invoice from: #{invoice.from_at.to_s(:date)} to #{invoice.to_at.to_s(:date)}", align: :right, inline_format: true
        text supplier.company_name.to_s, align: :right, inline_format: true
        text supplier.company_address.to_s, align: :right, inline_format: true
        text "#{supplier_suburb}, Australia", align: :right, inline_format: true
        text "<b>Commission %     #{supplier.commission_rate.to_f}</b>", align: :right, inline_format: true
      end
    end
  end

  def generate_table
    grid([3, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      ################################################################ Bill To
      font_size 10 do
        text '<b>Bill To:</b>', inline_format: true
        text 'yordar.com.au'
        text 'Suite 1B, Level 16, 56 Pitt Street'
        text 'Sydney, NSW 2000, Australia'
      end

      move_down 20

      orders_grouped_by_address = orders.sort_by(&:delivery_at).group_by(&:address_label)

      last_location = nil
      orders_content = []
      order_header_row = []
      invoice_subtotal = invoice_delivery = invoice_gst = invoice_topup = invoice_total = 0

      orders_grouped_by_address.each do |location, location_orders|
        if last_location.blank? || last_location != location
          last_location = location

          customer = location_orders.first.customer_profile
          customer_company = customer.company.try(:name) || customer.company_name
          location_label = customer_company.present? ? "#{customer_company}<br/>#{location}" : location

          # == order header green ==
          order_header_row = []
          order_header_row << { content: location_label, colspan: 3, borders: BORDER_LTB, background_color: LOCATION_COLOR, font_style: :bold, width: 240, inline_format: true }
          order_header_row << { content: 'Delivery date', borders: BORDER_LTB, background_color: LOCATION_COLOR, font_style: :bold, align: :center, width: 90 }
          order_header_row << { content: 'GST', borders: BORDER_LTB, background_color: LOCATION_COLOR, font_style: :bold, align: :center, width: 90 }
          order_header_row << { content: 'Total', borders: BORDER_ALL, background_color: LOCATION_COLOR, font_style: :bold, align: :center, width: 105 }

          # printing header on it's own so that repeat is easy
          # prawn doesn't like  dynamic header
          table([order_header_row], width: PAGE_PRINT_WIDTH)
        end

        location_orders.each do |location_order|
          totals = Orders::CalculateSupplierTotals.new(order: location_order, supplier: supplier, save_totals: true).call

          # Update invoice summary
          invoice_subtotal += totals.subtotal
          invoice_delivery += totals.delivery
          invoice_gst += totals.gst
          invoice_topup += totals.topup if totals.topup.present? && totals.topup > 0
          invoice_total += totals.total
          # print the total row
          order_row = []
          order_row << { content: "Order ##{location_order.id} #{location_order.name}", align: :left, colspan: 3 }
          order_row << { content: location_order.delivery_at.to_s(:date), align: :right, borders: BORDER_LTB, width: 90 }
          order_row << { content: totals.gst.round(2).to_s, align: :right, borders: BORDER_LTB, width: 90 }
          order_row << { content: totals.total.round(2).to_s, align: :right, borders: BORDER_ALL, width: 105 }

          orders_content << order_row
        end

        # if we are at the bottom of the page, let's reprint the
        # location header
        if cursor < 50
          start_new_page
          table([order_header_row], width: PAGE_PRINT_WIDTH)
        end

        # content rows
        table(orders_content, width: PAGE_PRINT_WIDTH)
        orders_content = [] # reset what was printed already!
      end

      ################################################################ Summary Footer
      if cursor < 70
        start_new_page
      end

      has_topup = invoice_topup.present? && invoice_topup > 0
      bounding_box [0, cursor - 20], width: PAGE_PRINT_WIDTH, height: has_topup ? 90 : 70 do
        font_size 10 do
          table_content = [
            ['<b>Subtotal:</b>', number_to_currency(invoice_subtotal)],
            ['<b>Delivery:</b>', number_to_currency(invoice_delivery)],
            ['<b>GST:</b>', number_to_currency(invoice_gst)]
          ]
          table_content << ['<b>Topup:</b>', number_to_currency(invoice_topup)] if has_topup
          table_content << ['<b>Total (inc. GST):</b>', number_to_currency(invoice_total)]
          table(table_content, cell_style: { inline_format: true, align: :right, border_width: 0, padding: [5, 8, 0, 10] }, position: :right)
        end
        # Draw a box around the summary area
        transparent(0.5) {stroke_bounds}
      end
    end # === grid end ===
  end

  def pdf_footer
    # do nothing - don't have a footer
  end

  def supplier
    @_supplier ||= invoice.supplier_profile
  end

  def orders
    @_orders ||= invoice.orders
  end

  def supplementary_footer
    float do
      text '<b>Payment details:</b>', inline_format: true, size: META_FONT_SIZE
      text "Cheques payable to: #{supplier.company_name}", size: META_FONT_SIZE
      text "BSB: #{supplier.bsb_number}", size: META_FONT_SIZE
      text "Account: #{supplier.bank_account_number}, ABN: #{supplier.abn_acn}", size: META_FONT_SIZE
    end
  end
end
