class Invoices::TaxInvoice < YordarPdf
  include Rails.application.routes.url_helpers

  def initialize(invoice:)
    @invoice = invoice

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    font_size 9
    case
    when invoice_orders.size == 1 # single order invoice
      single_order_invoice(invoice_orders.first)
    else
      bulk_order_invoice
    end
  end

private

  attr_reader :invoice

  def report_folder
    'tax_invoice'
  end

  def report_reference
   "#{invoice.id}-#{invoice.number}-tax_invoice"
  end

  def invoice_orders
    @_invoice_orders ||= invoice.invoice_orders.order(delivery_at: :asc, id: :asc)
  end

  def invoice_customer
    @_invoice_customer ||= invoice_orders.first.customer_profile
  end

  def invoice_po
    @_invoice_po ||= invoice.customer_purchase_order
  end

  def invoice_order_grouping
    @_invoice_order_grouping ||= invoice_customer.invoice_order_grouping.presence || 'address_po'
  end

  def grouped_orders
    grouping_field = case invoice_order_grouping
    when 'purchase_order'
      :po_number
    when 'address_po'
      :address_po_label
    when 'address'
      :address_label
    when 'delivery_date'
      :delivery_date
    else # default to address_po
      :address_po_label
    end

    invoice_orders.group_by(&grouping_field)
  end

  def order_lines_for(order)
    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    if has_gst_split_invoicing? && order.has_gst_split_pos?
      gst_split = order.gst_free_cpo_id == invoice_po.id ? 'GST-FREE' : 'GST'
      lister_options[:gst_split] = gst_split
    end
    order_lines = OrderLines::List.new(options: lister_options, includes: %i[location supplier_profile]).call
    order_lines.sort_by do |order_line|
      [
        order_line.location_id,
        order_line.supplier_profile.company_name.downcase,
        order_line.id
      ]
    end
  end

  def hide_delivery_pricing?
    invoice_customer.hide_delivery_pricing
  end

  def payment_term_days
    return @_payment_term_days if @_payment_term_days.present?

    company = invoice_customer.company
    term_days = company.payment_term_days if company.present?
    # if customer was pay on account before but then was removed from their company,
    # the existing orders will still be pay on account even tho they don't belong to a company
    # which means payment term days will be nil, so we need to set a default value for it
    # TODO: shouldn't allow customers to be pay on account if they don't belong to a company any more?
    term_days ||= 7
    @_payment_term_days = term_days
  end

  def invoice_dates
    @_invoice_dates ||= OpenStruct.new(
      from: invoice.from_at.to_s(:date),
      to: invoice.to_at.to_s(:date),
      due: invoice.due_at.to_s(:date)
      )
  end

  def table_cell_style
    { inline_format: true }
  end

  def single_order_invoice(order)
    payment_card = order.credit_card
    payment_card = nil if payment_card&.pay_on_account?

    payment_card_brand = payment_card.present? && payment_card.label.present? ? payment_card.label.split(' (')[0] : ''

    ################################################################ Title
    bounding_box [150, bounds.top - 20], width: 370, height: 50 do
      font_size(20) { text 'Tax Invoice', align: :right, inline_format: true }
      if payment_card.present?
        (text "<i>This invoice will be automatically paid with card ending in #{payment_card.last4} within the next 24 hours</i>", align: :right, inline_format: true, color: 'C71E2C')
      else
        (text "<i>Terms are strictly #{payment_term_days} days from date of invoice</i>", align: :right, inline_format: true)
      end
    end

    move_down 20
    grid([3, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      ################################################################ Order information Row
      info_table = []
      info_table << "Date: #{invoice_dates.to}"
      info_table << "Inv #: INV#{invoice.number}"
      info_table << "Cust: #{order.customer_profile.user.name}"

      payment_str = case
      when payment_card_brand.present?
        "Payment: #{payment_card_brand}"
      else
        "Inv. Due Date: #{invoice_dates.due}"
      end
      info_table << payment_str
      table([info_table], width: PAGE_PRINT_WIDTH, cell_style: table_cell_style.merge(background_color: BACKGROUND_COLOR)) do
        column(3).style text_color: 'FF0000' if payment_card.blank?
      end

      ################################################################ Order details Row
      order_details = "<b>Order Information</b>:\n"
      if has_gst_split_invoicing? && order.has_gst_split_pos?
        po_number = invoice.po_number.presence || order.po_number
      else
        po_number = order.po_number
      end
      if po_number.present?
        order_details += "PO #: #{po_number}\n"
      end
      order_details += "Order Number: #{order.id}\n"
      order_details += "Order Name: #{order.name}\n"
      order_details += "Delivery Date: #{order.delivery_at.to_s(:full_date)}\n"
      order_details += "Delivery Time: #{order.delivery_at.to_s(:time_only)}\n"

      delivery_details = "<b>Deliver To</b>:\n"
      delivery_details += "#{order.company_name || order.contact_name}\n"
      delivery_details += "#{order.delivery_address}\n"
      delivery_details += "#{order.delivery_suburb.suburb_state}\n"
      delivery_details += "#{order.delivery_suburb.postcode}\n"

      billing_details = "<b>Bill To</b>:\n"
      if (customer_billing_details = order.customer_profile.billing_details.presence)
        billing_details += "#{customer_billing_details.name}\n"
        billing_details += "Cost Centre: #{order.department_identity}\n"
        billing_details += "#{customer_billing_details.address}\n"
        if customer_billing_details.suburb.present?
          billing_details += "#{customer_billing_details.suburb.suburb_state}\n"
          billing_details += "#{customer_billing_details.suburb.postcode}\n"
        else
          billing_details += "\n\n"
        end
      else
        billing_details += "#{order.customer_profile.customer_or_company_name}\n"
        billing_details += "#{order.delivery_address}\n"
        billing_details += "#{order.delivery_suburb.suburb_state}\n"
        billing_details += "#{order.delivery_suburb.postcode}\n"
      end

      table([[order_details, delivery_details, billing_details]], width: PAGE_PRINT_WIDTH, cell_style: table_cell_style, column_widths: { 0 => PAGE_PRINT_WIDTH / 3, 1 => PAGE_PRINT_WIDTH / 3 })

      move_down 10
      ################################################################ Order lines Table
      header = ['Partner', 'Qty', 'Unit', 'Item', 'Per Item', 'Total'].map{|str| "<b>#{str}</b>" }

      order_lines_table = [header]

      order_lines_table += order_lines_grouping_data_for(order)

      table(order_lines_table) do |table|
        table.cell_style = table_cell_style
        table.width = PAGE_PRINT_WIDTH
        # Repeats the header on each page
        table.header = true
        table.rows(0).background_color = BACKGROUND_COLOR
        # Align the numerical columns right
        table.rows(1..-1).column(1).align = :right
        table.rows(1..-1).column(3).align = :right
        table.rows(1..-1).column(4).align = :right
      end

      ################################################################ Footer
      has_delivery = invoice_totals.delivery.present? && invoice_totals.delivery > 0
      has_discount = invoice_totals.discount.present? && invoice_totals.discount > 0
      has_topup = invoice_totals.topup.present? && invoice_totals.topup > 0

      totals_box_height = 60 # Space for Subtotal, GST & Total (by default)
      totals_box_height += 10 if payment_card_brand.present?
      totals_box_height += 10 if !hide_delivery_pricing? # if showing delivery fee
      totals_box_height += 10 if has_discount

      if cursor < totals_box_height
        start_new_page
      end

      bounding_box [0, cursor - 20], width: PAGE_PRINT_WIDTH, height: totals_box_height do
        float do
          supplier_markups = supplier_markups_for_order(order)
          if supplier_markups.present?
            indent(10) do
              font 'Helvetica', style: :italic do
                text 'Thank you for using yordar.com.au', valign: :top
              end
            end
            indent(10) do
              font_size 8 do
                markup_text = "<b>Markup</b>\n"
                supplier_markups.each do |supplier_markup|
                  markup_precission = supplier_markup.markup == supplier_markup.markup.to_i ? 0 : 2
                  markup_text += "#{supplier_markup.supplier.name}: #{number_to_percentage(supplier_markup.markup, precision: markup_precission)}\n"
                end
                text markup_text, valign: :center, align: :left, inline_format: true
              end
            end
          else
            indent(40) do
              font 'Helvetica', style: :italic do
                text 'Thank you for using yordar.com.au', valign: :center
              end
            end
          end
          indent(10) do
            font_size 8 do
              split_spends = []
              split_spends << ['<b>Non GST spend</b>', number_to_currency(invoice_totals.non_gst_total)]
              split_spends << ['<b>GST spend</b>', number_to_currency(invoice_totals.gst_total)]
              text split_spends.map{|spends| spends.join(': ') }.join(' '), valign: :bottom, align: :left, inline_format: true
            end
          end
        end
        table_content = []
        table_content << ['<b>Subtotal:</b>', number_to_currency(invoice_totals.subtotal)]
        table_content << ['<b>Delivery:</b>', number_to_currency(invoice_totals.delivery)] if has_delivery
        table_content << ["<b>#{discount_label}:</b>", "-#{number_to_currency(invoice_totals.discount)}"] if has_discount
        table_content << ['<b>GST:</b>', number_to_currency(invoice_totals.gst)]
        table_content << ['<b>Topup:</b>', number_to_currency(invoice_totals.topup)] if has_topup
        table_content << ["<b>#{payment_card_brand} surcharge:</b>", number_to_currency(invoice_totals.surcharge)] if payment_card_brand.present?
        table_content << ['<b>Total (inc. GST):</b>', number_to_currency(invoice_totals.total)]

        font_size 10 do
          table(table_content, cell_style: table_cell_style.merge(align: :right, border_width: 0, padding: [5, 8, 0, 10]), position: :right)
          transparent(0.5) {stroke_bounds}
        end
      end
    end
  end

  def order_lines_grouping_data_for(order)
    table_data = []

    # Order lines grouped by location
    sorted_order_lines = order_lines_for(order)
    sorted_order_lines.group_by(&:location).each do |location, order_lines|
      if order.is_team_order?
        # Order lines grouped by Team Order Attendee
        order_lines.group_by(&:team_order_attendee).each do |team_order_attendee, attendee_order_lines|
          ordered_by = team_order_attendee.present? ? team_order_attendee.name : "#{order.customer_profile.name} - ADMIN"
          location_label = "Ordered By: #{ordered_by}"
          table_data << [{ content: location_label, colspan: 6, background_color: LOCATION_COLOR }]

          table_data += order_lines_data_for(order: order, order_lines: attendee_order_lines)
        end
      else
        # Display the location across the whole table using colspan
        location_label = location.label
        table_data << [{ content: location_label, colspan: 6, background_color: LOCATION_COLOR }]

        table_data += order_lines_data_for(order: order, order_lines: order_lines)

        location_total = order_lines.map(&:total_price).sum
        table_data << [
          { content: '', colspan: 5 },
          { content: number_to_currency(location_total) }
        ]
      end
    end

    table_data
  end

  def order_lines_data_for(order:, order_lines:)
    country_code = order.symbolized_country_code || :au
    table_data = []
    order_lines.each do |order_line|
      order_line_name = order_line.is_gst_free ? "* #{order_line.name}" : order_line.name
      order_line_name = order_line_name.truncate(50)
      table_data << [
        order_line.supplier_profile.company_name,
        order_line.quantity,
        order_line.menu_item.present? ? order_line.menu_item.sub_quantity : nil,
        order_line_name,
        number_to_currency(order_line.price),
        number_to_currency(order_line.total_price(gst_country: country_code))
      ]
    end
    table_data
  end

  def bulk_order_invoice
    has_pay_on_account_order = invoice_orders.any?{|order| order.credit_card.pay_on_account? }

    ################################################################ Title
    bounding_box [210, bounds.top - 20], width: 320, height: 100 do
      font_size(20) { text '<b>Tax Invoice</b>', align: :right, inline_format: true }
      font_size 10 do
        (text "<i>Terms are strictly #{payment_term_days} days from date of invoice</i>", align: :right, inline_format: true) if has_pay_on_account_order
        text "Invoice from: #{invoice_dates.from} to #{invoice_dates.to}", align: :right, inline_format: true
        text "<b>Invoice Number: BK#{invoice.number}</b>", align: :right, inline_format: true
        text "<b>Invoice Date: #{invoice_dates.to}</b>", align: :right, inline_format: true
        (text "<b>Invoice Due: #{invoice_dates.due}</b>", align: :right, inline_format: true, color: 'FF0000') if has_pay_on_account_order
      end
    end

    move_down 20
    grid([3, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      ################################################################ Bill To
      font_size 10 do
        text '<b>Bill To:</b>', inline_format: true
        if (customer_billing_details = invoice_customer.billing_details.presence)
          text customer_billing_details.name
          text customer_billing_details.address
          text customer_billing_details.suburb.label
        else
          text invoice_customer.customer_or_company_name
          text invoice_orders.first.delivery_address
          text invoice_orders.first.delivery_suburb.label
        end
      end
      move_down 20

      ################################################################ Orders
      orders_table = []

      header_row = []
      header_row << 'Date'
      header_row << 'PO #' if %w[purchase_order address_po].exclude?(invoice_order_grouping)
      header_row << 'Order #'
      header_row << 'Approved By'
      header_row << 'Description'
      header_row << 'GST'
      header_row << 'Total(inc)'
      formatted_header_row = header_row.map{|str| "<b>#{str}</b>"}
      orders_table << formatted_header_row

      last_grouping = nil
      grouped_orders.each do |grouping, orders|
        if last_grouping.blank? || last_grouping != grouping
          last_grouping = grouping
          grouping_str = case invoice_order_grouping
          when 'purchase_order'
            grouping.present? ? "PO - #{grouping}" : 'Orders without PO'
          when 'delivery_date'
            grouping.to_s(:date_verbose)
          else # 'address_po', 'address'
            grouping
          end
          grouping_total = cumulative_total_for(orders)
          orders_table << [
            { content: grouping_str, colspan: header_row.size - 1, background_color: LOCATION_COLOR, inline_format: true },
            { content: "<b>#{number_to_currency(grouping_total)}</b>", align: :right, width: 75, background_color: LOCATION_COLOR, inline_format: true }
          ]
        end

        orders.each do |order|
          order_row = []
          order_row << (invoice_order_grouping == 'delivery_date' ? order.delivery_at.to_s(:time_only) : order.delivery_at.to_s(:date))
          if %w[purchase_order address_po].exclude?(invoice_order_grouping)
            if has_gst_split_invoicing? && order.has_gst_split_pos?
              po_number = invoice.po_number.presence || order.po_number
            else
              po_number = order.po_number
            end
            order_row << po_number
          end
          if (order_link = latest_document_link_for(order).presence)
            order_row << { content: "<a href='#{order_link}'>#{order.id}</a>", inline_format: true, text_color: LINK_COLOR }
          else
            order_row << order.id
          end
          order_row << order.customer_profile.user.name
          order_row << order.name.truncate(50, omission: '...')

          order_totals = order_totals_for(order)
          order_row << { content: number_to_currency(order_totals.gst), align: :right }
          order_row << { content: number_to_currency(order_totals.total), align: :right, width: 75 }

          orders_table << order_row
        end
      end

      table(orders_table) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.cell_style = table_cell_style
        # Repeat header on each page
        table.header = true
      end

      ################################################################ Footer
      # Calculate the totals for this invoice
      has_delivery = invoice_totals.delivery.present? && invoice_totals.delivery > 0
      has_discount = invoice_totals.discount.present? && invoice_totals.discount > 0
      has_topup = invoice_totals.topup.present? && invoice_totals.topup > 0

      totals_box_height = 60 # Space for Subtotal, GST & Total (by default)
      totals_box_height += 10 if !hide_delivery_pricing? # if showing delivery fee
      totals_box_height += 10 if has_discount

      if cursor < totals_box_height
        start_new_page
      end

      bounding_box [0, cursor - 20], width: PAGE_PRINT_WIDTH, height: totals_box_height do
        # Float doesn't increment the cursor position
        float do
          indent(40) do
            font 'Helvetica', style: :italic do
              text 'Thank you for using yordar.com.au', valign: :center
            end
          end
          indent(10) do
            font_size 8 do
              split_spends = []
              split_spends << ['<b>Non GST spend</b>', number_to_currency(invoice_totals.non_gst_total)]
              split_spends << ['<b>GST spend</b>', number_to_currency(invoice_totals.gst_total)]
              text split_spends.map{|spends| spends.join(': ') }.join(' '), valign: :bottom, align: :left, inline_format: true
            end
          end
        end

        totals_subtotal = invoice_totals.subtotal
        # subtotal to include delivery fee when explicitly hiding delivery fee line
        if hide_delivery_pricing? && has_delivery
          totals_subtotal += invoice_totals.delivery
        end
        # subtotal to include topup of any order has a topup
        if has_topup
          totals_subtotal += invoice_totals.topup
        end

        font_size 10 do
          table_content = []
          table_content << ['<b>Subtotal:</b>', number_to_currency(totals_subtotal)]
          if !hide_delivery_pricing?
            table_content << ['<b>Delivery:</b>', number_to_currency(invoice_totals.delivery)]
          end
          table_content << ["<b>#{discount_label}:</b>", "-#{number_to_currency(invoice_totals.discount)}"] if has_discount
          table_content << ['<b>GST:</b>', number_to_currency(invoice_totals.gst)]
          table_content << ['<b>Total (inc. GST):</b>', number_to_currency(invoice_totals.total)]
          table(table_content, cell_style: table_cell_style.merge(inline_format: true, align: :right, border_width: 0, padding: [5, 8, 0, 10]), position: :right)
        end

        # Draw a box around the summary area
        transparent(0.5) {stroke_bounds}
      end
    end
  end

  def invoice_totals
    @_invoice_totals ||= Invoices::CalculateTotals.new(invoice: invoice).call
  end

  def cumulative_total_for(orders)
    orders.map do |order|
      order_totals_for(order)
    end.sum(&:total)
  end

  def order_totals_for(order)
    invoice_totals.order_totals[order]
  end

  def has_gst_split_invoicing?
    return @_has_gst_split_invoicing if defined? @_has_gst_split_invoicing

    @_has_gst_split_invoicing ||= invoice_po.present? && invoice_orders.sample.customer_profile&.has_gst_split_invoicing
  end

  def supplier_markups_for_order(order)
    customer = order.customer_profile
    return nil if !customer.requires_supplier_markup

    Orders::FetchSupplierMarkups.new(order: order, order_lines: order_lines_for(order)).call
  end

  def supplementary_footer
    float do
      text '<b>Payment details:</b>', inline_format: true, size: META_FONT_SIZE
      text 'Cheques payable to: Your Order Pty Ltd trading as Yordar', size: META_FONT_SIZE
      text "BSB: #{yordar_credentials(:yordar, :account, :bsb)}, SWIFT: #{yordar_credentials(:yordar, :account, :swift_code)}", size: META_FONT_SIZE
      text "Account: #{yordar_credentials(:yordar, :account, :number)}, ABN: #{yordar_credentials(:yordar, :abn_acn)}", size: META_FONT_SIZE
      text '<b>Pay by credit card:</b>', inline_format: true, size: META_FONT_SIZE
      text "<a href='#{invoice_pay_link}'>#{invoice_pay_link}</a>", inline_format: true, size: META_FONT_SIZE
    end
  end

  def discount_label
    order_promotion = invoice_orders.map(&:promotion).uniq.first
    order_promotion.present? ? "#{order_promotion.name} (#{order_promotion.discount_note})" : 'Discount'
  end

  def latest_document_link_for(order)
    order.documents.where(kind: 'customer_order_details').order(version: :desc).first&.url
  end

  def invoice_pay_link
    path = pay_invoice_path(invoice)
    url_shortner = Shortener::ShortenedUrl.generate(path)
    shortened_url(url_shortner.unique_key, host: app_host)
  end

end
