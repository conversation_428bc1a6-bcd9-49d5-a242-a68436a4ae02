class Invoices::TaxInvoiceReceipt < YordarPdf
  include Rails.application.routes.url_helpers

  def initialize(invoice:)
    @invoice = invoice

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    font_size 9
    receipt_pdf
  end

private

  attr_reader :invoice

  def report_folder
    'tax_invoice'
  end

  def report_reference
   "#{invoice.id}-#{invoice.number}-tax_invoice_report"
  end

  def invoice_orders
    @_invoice_orders ||= invoice.invoice_orders.order(delivery_at: :asc, id: :asc)
  end

  def invoice_order
    @_invoice_order ||= invoice_orders.first
  end

  def invoice_customer
    @_invoice_customer ||= invoice_order.customer_profile
  end

  def invoice_order_grouping
    @_invoice_order_grouping ||= invoice_customer.invoice_order_grouping.presence || 'address_po'
  end

  def grouped_orders
    grouping_field = case invoice_order_grouping
    when 'purchase_order'
      :po_number
    when 'address_po'
      :address_po_label
    when 'address'
      :address_label
    when 'delivery_date'
      :delivery_date
    else # default to address_po
      :address_po_label
    end

    invoice_orders.group_by(&grouping_field)
  end

  def receipt_pdf
    ############################ Summary/Cover page ######################
    ################################################################ Title
    payment_card = invoice_order.credit_card
    payment_card = nil if payment_card.present? && payment_card.pay_on_account?

    bounding_box [210, bounds.top - 20], width: 320, height: 100 do
      font_size(20) { text '<b>Tax Receipt</b>', align: :right, inline_format: true }
      font_size 10 do
        text "<b>Invoice Number: BK#{invoice.number}</b>", align: :right, inline_format: true
        text "<b>Invoice Date: #{invoice.to_at.to_s(:date)}</b>", align: :right, inline_format: true
        if payment_card.present?
          text "<b>Paid with card ending in #{payment_card.last4}</b>", align: :right, inline_format: true
        end
      end
    end

    move_down 20
    grid([3, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      ################################################################ Bill To
      font_size 10 do
        text '<b>Paid by:</b>', inline_format: true
        if (billing_details = invoice_customer.billing_details.presence)
          text billing_details.name
          text billing_details.address
          text billing_details.suburb.label
        else
          text invoice_customer.customer_or_company_name
          text invoice_order.delivery_address
          text invoice_order.delivery_suburb.label
        end
      end
      move_down 20

      ################################################################ Orders
      orders_table = []

      header_row = []
      header_row << 'Date'
      header_row << 'PO #' if %w[purchase_order address_po].exclude?(invoice_order_grouping)
      header_row << 'Order #'
      header_row << 'Approved By'
      header_row << 'Description'
      header_row << 'GST'
      header_row << 'Total(inc)'
      formatted_header_row = header_row.map{|str| "<b>#{str}</b>"}
      orders_table << formatted_header_row

      last_grouping = nil
      grouped_orders.each do |grouping, orders|

        if last_grouping.blank? || last_grouping != grouping
          last_grouping = grouping
          grouping_str = case invoice_order_grouping
          when 'purchase_order'
            grouping.present? ? "PO - #{grouping}" : 'Orders without PO'
          when 'delivery_date'
            grouping.to_s(:date_verbose)
          else # 'address_po', 'address'
            grouping
          end
          grouping_total = cumulative_total_for(orders)
          orders_table << [
            { content: grouping_str, colspan: header_row.size - 1, background_color: LOCATION_COLOR, inline_format: true },
            { content: "<b>#{number_to_currency(grouping_total)}</b>", align: :right, width: 75, background_color: LOCATION_COLOR, inline_format: true }
          ]
        end

        orders.each do |order|
          order_row = []
          order_row << (invoice_order_grouping == 'delivery_date' ? order.delivery_at.to_s(:time_only) : order.delivery_at.to_s(:date))
          order_row << order.po_number if %w[purchase_order address_po].exclude?(invoice_order_grouping)
          if (order_link = latest_document_link_for(order).presence)
            order_row << { content: "<a href='#{order_link}'>#{order.id}</a>", inline_format: true, text_color: LINK_COLOR }
          else
            order_row << order.id
          end

          order_totals = order_totals_for(order)
          order_row << order.customer_profile.user.name
          order_row << order.name.truncate(45, omission: '...')
          order_row << number_to_currency(order_totals.gst)
          order_row << number_to_currency(order_totals.total)

          orders_table << order_row
        end
      end

      table(orders_table) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.cell_style = { inline_format: true }
        # Repeat header on each page
        table.header = true
        # Align numerical columns right
        table.rows(1..-1).column(header_row.size - 1).align = :right
        table.rows(1..-1).column(header_row.size - 2).align = :right
      end

      ################################################################ Footer
      if (cursor < 110) || (invoice_totals.discount.present? && invoice_totals.discount > 0 && cursor < 120)
        start_new_page
      end

      totals_box_height = invoice_totals.discount.present? ? 120 : 110

      bounding_box [0, cursor - 20], width: PAGE_PRINT_WIDTH, height: totals_box_height do
        # Float doesn't increment the cursor position
        float do
          indent(40) do
            font 'Helvetica', style: :italic do
              text 'Thank you for using yordar.com.au', valign: :center
            end
          end
        end
        font_size 10 do
          table_content = []
          table_content << ['<b>Delivery:</b>', number_to_currency(invoice_totals.delivery)]
          table_content << ["<b>#{discount_label}:</b>", "-#{number_to_currency(invoice_totals.discount)}"] if invoice_totals.discount.present? && invoice_totals.discount > 0
          table_content << ['<b>Surcharge:</b>', number_to_currency(invoice_totals.surcharge)]
          table_content << ['<b>Non GST Total:</b>', number_to_currency(invoice_totals.non_gst_total)]
          table_content << ['<b>GST Total:</b>', number_to_currency(invoice_totals.gst_total)]
          table_content << ['<b>GST:</b>', number_to_currency(invoice_totals.gst)]
          table_content << ['<b>Total (inc. GST):</b>', number_to_currency(invoice_totals.total)]

          table(table_content, cell_style: { inline_format: true, align: :right, border_width: 0, padding: [5, 8, 0, 10] }, position: :right)
        end
        # Draw a box around the summary area
        transparent(0.5) {stroke_bounds}
      end
    end
  end

  def discount_label
    order_promotion = invoice_orders.map(&:promotion).uniq.first
    order_promotion.present? ? "#{order_promotion.name} (#{order_promotion.discount_note})" : 'Discount'
  end

  def latest_document_link_for(order)
    order.documents.where(kind: 'customer_order_details').order(version: :desc).first&.url
  end

  def invoice_totals
    @_invoice_totals ||= Invoices::CalculateTotals.new(invoice: invoice).call
  end

  def cumulative_total_for(orders)
    orders.map do |order|
      order_totals_for(order)
    end.sum(&:total)
  end

  def order_totals_for(order)
    invoice_totals.order_totals[order]
  end

  def supplementary_footer
    # nothing to show
  end

end
