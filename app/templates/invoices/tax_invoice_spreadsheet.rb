class Invoices::TaxInvoiceSpreadsheet < YordarCsv

  def initialize(invoice:)
    @invoice = invoice

    @csv_data = ''
  end

  def generate
    @csv_data = CSV.generate do |csv_rows|
      csv_rows << header_fields
      grouped_orders.each do |_, orders|
        orders.each do |order|
          csv_rows << order_row_for(order)
        end
      end
    end
  end

private

  attr_reader :invoice, :csv_data

  def report_folder
    'tax_invoice'
  end

  def report_reference
   "#{invoice.id}-#{invoice.number}-tax_invoice_spreadsheet"
  end

  def header_fields
    ['Date', 'Order #', 'Approved By', 'Description', 'PO Number', 'GST', 'Total (inc gst)']
  end

  def invoice_orders
    @_invoice_orders ||= invoice.invoice_orders.order(:delivery_at)
  end

  def invoice_customer
    @_invoice_customer ||= invoice_orders.first.customer_profile
  end

  def invoice_order_grouping
    @_invoice_order_grouping ||= invoice_customer.invoice_order_grouping.presence || 'address_po'
  end

  def grouped_orders
    grouping_field = case invoice_order_grouping
    when 'purchase_order'
      :po_number
    when 'address_po'
      :address_po_label
    when 'address'
      :address_label
    when 'delivery_date'
      :delivery_date
    else # default to address_po
      :address_po_label
    end

    invoice_orders.order(:delivery_at, :id).group_by(&grouping_field)
  end

  def order_row_for(order)
    order_row = []
    order_row << order.delivery_at.to_s(:date)
    order_row << order.id
    order_row << order.customer_profile.user.name
    order_row << order.name
    order_row << order.po_number.presence || '-'
    order_totals = order_totals_for(order)
    order_row << order_totals.gst
    order_row << order_totals.total
    order_row
  end

  def invoice_totals
    @_invoice_totals ||= Invoices::CalculateTotals.new(invoice: invoice).call
  end

  def order_totals_for(order)
    invoice_totals.order_totals[order]
  end

end
