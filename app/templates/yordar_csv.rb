class YordarCsv

  def report_path(ext: '.csv')
    @_report_path ||= begin
      hashable_text = report_reference + yordar_credentials(:secret_token)
      md5_hash = Digest::MD5.hexdigest(hashable_text)
      "#{report_folder}/#{md5_hash}"
    end + "#{ext}"
  end

  def render_file
    local_file_path = prepare_report_folder
    local_file = File.open(local_file_path, 'wb')
    local_file.write @csv_data
    local_file.close
    local_file
  end

  def file_path
    Rails.root.join(yordar_credentials(:yordar, :report_folder_path), report_path)
  end

private

  def prepare_report_folder
    directory = File.dirname(file_path)
    unless File.directory?(directory)
      FileUtils.mkdir_p(directory)
    end
    file_path
  end

end
