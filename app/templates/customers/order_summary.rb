class Customers::OrderSummary < YordarPdf

  def initialize(customer:, orders:, summary_day:)
    @customer = customer
    @orders = orders
    @summary_day = summary_day

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    repeat :all do
      page_header
    end

    grid([5, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      orders.each do |order|
        order_lines = order_lines_for(order: order)
        details_for(order)
        order_lines_table_for(order: order, order_lines: order_lines)
      end
    end
  end

private

  def report_folder
    'customer_summary'
  end

  def report_reference
   "#{customer.id}/#{summary_day.to_s(:date_compact)}"
  end

  def page_header
    # ===  report title ===
    grid([0, 3], [0, LAST_COL]).bounding_box do
      text 'DAILY SUMMARY', align: :right, size: 20, style: :bold, valign: :bottom
      # transparent(0.5) {stroke_bounds}
    end

    grid([1, 3], [1, LAST_COL]).bounding_box do
      text "FOR #{summary_day.to_s(:full_date).upcase}", align: :right, size: 18, style: :bold, valign: :bottom
      # transparent(0.5) {stroke_bounds}
    end

    # == supplier name ==
    grid([3, 0], [3, LAST_COL]).bounding_box do
      row = [["Customer: #{@customer.name}"]]
      table(row, column_widths: { 0 => PAGE_PRINT_WIDTH }, cell_style: { font_style: :bold, border_width: 0.5 })
    end

    # == grey sub header ==
    # todo add the Tick mark image..
    grid([4, 0], [4, LAST_COL]).bounding_box do
      header_row = []
      header_row << { content: 'Qty', align: :center }
      header_row << { content: 'Item', align: :right }
      header_row << { content: 'Total Price' }
      table([header_row], row_colors: ['F0F0F0'], column_widths: [80, 345, 100], cell_style: { font_style: :bold, height: LINE_HEIGHT, size: 10, border_width: 0.5 })
    end
  end

  def order_lines_for(order:)
    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    OrderLines::List.new(options: lister_options).call
  end

  def details_for(order)
    order_name = order.is_recurrent? ? '(S) ' : '' # S -> means 'standing' aka. recurring order
    order_name += "#{order.name} - ##{order.id}"

    order_header_rows = []
    order_header_rows << { content: '', borders: BORDER_LTB }
    order_header_rows << { content: order_name, borders: BORDER_TB, font_style: :bold }
    order_header_rows << { content: "Delivery Time: #{order.delivery_at.to_s(:time_only)}", borders: BORDER_TRB, font_style: :bold }

    table([order_header_rows], row_colors: ['ECD6D6'], column_widths: [170, 230, 125], cell_style: { size: 10, border_width: 0.5 })

    delivery_address_row = []
    delivery_address_row << { content: "<strong>Address</strong>: #{order.address_label}", inline_format: true, borders: BORDER_LTR, font_style: :normal }
    table([delivery_address_row], row_colors: ['ECD6D6'], column_widths: [PAGE_PRINT_WIDTH], cell_style: { height: LINE_HEIGHT, size: 10, border_width: 0.5 })

    if order.delivery_instruction.present?
      delivery_instruction_row = []
      delivery_instruction_row << { content: "<strong>Delivery Instructions</strong>: #{order.delivery_instruction}", inline_format: true, borders: BORDER_LTR, font_style: :normal }
      table([delivery_instruction_row], row_colors: ['ECD6D6'], column_widths: [PAGE_PRINT_WIDTH], cell_style: { size: 10, border_width: 0.5 })
    end
  end

  def order_lines_table_for(order:, order_lines:)
    country_code = order.symbolized_country_code || :au
    order_lines.group_by(&:supplier_profile).each do |supplier, supplier_order_lines|
      # supplier
      supplier_rows = []
      supplier_rows << { content: supplier.company_name }

      table([supplier_rows], row_colors: ['FFFFFF'], column_widths: [PAGE_PRINT_WIDTH], cell_style: { height: LINE_HEIGHT, size: 10, font_style: :italic, border_width: 0.5 })

      supplier_order_lines.group_by(&:location).each do |location, location_order_lines|
        # location
        location_rows = []
        location_rows << { content: location.label }

        table([location_rows], row_colors: ['FFFFFF'], column_widths: [PAGE_PRINT_WIDTH], cell_style: { height: LINE_HEIGHT, size: 10, font_style: :italic, border_width: 0.5 })

        # == order lines ==
        order_data_rows = []
        location_order_lines.each do |order_line|
          order_data_row = []
          order_line_name =  order_line.name
          order_line_name += " - #{order_line.note}" if order_line.note.present?
          order_data_row << { content: order_line.quantity.to_s, align: :center }
          order_data_row << { content: order_line_name, align: :right }
          order_data_row << { content: number_to_currency(order_line.total_price(gst_country: country_code), precision: 2) }
          order_data_rows << order_data_row
        end

        table(order_data_rows, row_colors: ['FFFFFF'], column_widths: [80, 345, 100], cell_style: { height: LINE_HEIGHT, size: 10, border_width: 0.5 })

        move_down 5 # we want gap after the table
      end # locations
    end # suppliers
  end

  attr_reader :customer, :orders, :summary_day

  def supplementary_footer
    # do nothing!
  end

end
