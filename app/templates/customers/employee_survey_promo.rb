class Customers::EmployeeSurveyPromo < YordarPdf
  include Rails.application.routes.url_helpers

  DEFAULT_PAGE_SIZE = 'A5'.freeze
  DEFAULT_FONT_FAMILY = 'MuseoSlab'.freeze
  SANS_FONT_FAMILY = 'MuseoSans'.freeze

  def initialize(employee_survey:)
    @employee_survey = employee_survey
    @customer = employee_survey.customer_profile

    # prawn/pdf setup in YordarPdf
    super(page_size: DEFAULT_PAGE_SIZE, font_family: DEFAULT_FONT_FAMILY)
  end

  def generate
    yo_logo

    report_header

    survey_barcode

    feedback_text

    yordar_logo
  end

private

  attr_reader :employee_survey, :customer

  def report_folder
    'employee_survey'
  end

  def report_reference
    employee_survey.id.to_s
  end

  def yo_logo
    logo_file = "#{Rails.root}/app/assets/images/yo-black.svg"

    grid([0, 0], [5, LAST_COL]).bounding_box do
      svg IO.read(logo_file), width: 120, height: 120, position: :center, border: 2
    end
  end

  def report_header
    grid([6, 0], [7, LAST_COL]).bounding_box do
      survey_name = employee_survey.name || "#{employee_survey.category_group_name} Survey"
      (text survey_name.upcase, align: :center, size: 14, style: :bold, valign: :middle)
    end
  end

  def survey_barcode
    grid([8, 0], [22, LAST_COL]).bounding_box do
      svg qr_image_svg, width: 250, height: 250, position: :center, border: 2
    end
  end

  def feedback_text
    grid([23, 0], [25, LAST_COL]).bounding_box do
      font(SANS_FONT_FAMILY) do
        (text "We'd love to hear from you!", align: :center, size: 18, style: :extra_bold, height: 100)
      end
      move_down 10
      (text 'Your Food. Your Culture.', align: :center, size: 14)
      # transparent(0.5) {stroke_bounds}
    end
  end

  def yordar_logo
    logo_image = "#{Rails.root}/app/assets/images/logo-black.svg"

    grid([26, 0], [27, LAST_COL]).bounding_box do
      svg IO.read(logo_image), width: 105, height: 30, position: :center, border: 2
      # transparent(0.5) {stroke_bounds}
    end
  end

  def qr_image_svg
    survey_url = employee_survey_url(uuid: employee_survey.uuid, host: next_app_host)
    qrcode = RQRCode::QRCode.new(survey_url)
    qrcode.as_svg(
      color: '000',
      module_size: 11,
      shape_rendering: 'crispEdges',
      standalone: true,
      use_path: true
    )
  end

  def with_pagination?
    false
  end

  def pdf_header
    # do nothing
  end

  def pdf_footer
    # do nothing
  end

  def supplementary_footer
    # do nothing
  end

end