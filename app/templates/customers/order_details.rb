class Customers::OrderDetails < YordarPdf

  TABLE_CELL_STYLE = { inline_format: true }.freeze

  def initialize(order:, reference:, version:, is_quote: false)
    @order = order
    @customer = order.customer_profile
    @reference = reference
    @version = version
    @is_quote = is_quote

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    report_header

    grid([4, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      order_info

      move_down 5

      delivery_instruction

      move_down 5

      if order_suppliers.size == 1
        supplier_information

        move_down 5
      end

      order_lines_table
    end
  end

private

  attr_reader :order, :customer, :reference, :version, :is_quote, :result

  def report_folder
    is_quote ? 'order_quote' : 'order_details'
  end

  def report_reference
    "#{order.id}/#{reference}"
  end

  def report_header
    grid([0, 3], [0, LAST_COL]).bounding_box do
      title_text = is_quote ? 'ORDER QUOTE' : 'ORDER DETAILS'
      (text title_text, align: :right, size: 20, style: :bold, valign: :bottom)
    end

    # order title
    grid([3, 0], [3, LAST_COL]).bounding_box do
      order_name = order.name.present? ? order.name.upcase.strip : ''
      customer_name = order.customer_profile.present? && order.customer_profile.customer_or_company_name.present? ? order.customer_profile.customer_or_company_name.upcase : ''
      customer_name = customer_name.strip
      title_row = [
        ["#{order_name} - ##{order.id} - #{customer_name}"]
      ]
      table(title_row, column_widths: { 0 => PAGE_PRINT_WIDTH }, cell_style: { font_style: :bold, border_width: 0.5 })
    end
  end

  def order_info
    delivery_address = order.delivery_address_arr.join(', ')
    company_name = order.company_name || order.customer_profile.company_name

    column_1 = []
    column_1 << ['Delivery date & time:', order.delivery_at.to_s(:full)]
    column_1 << ['Delivery address:', delivery_address]
    column_1 << ['Number of People:', order.number_of_people] if !order.is_team_order? && order.number_of_people.present?
    column_1 << ['Po Number:', order.po_number] if order.po_number.present?

    column_2 = []
    column_2 << ['Company name:', company_name] if company_name.present?
    column_2 << ['Contact name:', order.contact_name]
    column_2 << ['Contact phone:', order.phone] if order.phone.present?
    column_2 << ['Cost Centre ID:', order.department_identity] if order.department_identity.present?

    row_size = [column_1.size, column_2.size].max

    order_info = []
    row_size.times do |num|
      row = []
      row += column_1[num].presence || ['', '']
      row += column_2[num].presence || ['', '']
      order_info << row
    end

    table(order_info) do |table|
      table.width = PAGE_PRINT_WIDTH
      table.column(0).borders = BORDER_LTB
      table.column_widths = [80, 182.5, 85, 177.5]
      table.column(1).borders = BORDER_TRB
      table.column(2).borders = BORDER_TB
      table.column(3).borders = BORDER_TRB
      table.column(4).borders = BORDER_TRB unless order.is_team_order?

      table.cell_style = { size: 10, border_width: 0.5 }
    end
  end

  def delivery_instruction
    instructions = order.delivery_instruction
    table([['Delivery instructions:', instructions]]) do |table|
      table.width = PAGE_PRINT_WIDTH
      table.column(0).width = 115
      table.column(0).borders = BORDER_LTB
      table.column(1).borders = BORDER_TRB
      table.cell_style = { size: 10, border_width: 0.5 }
    end
  end

  def supplier_information
    table([['Partner:', order_suppliers.first.name]]) do |table|
      table.width = PAGE_PRINT_WIDTH
      table.column(0).width = 115
      table.column(0).borders = BORDER_LTB
      table.column(1).borders = BORDER_TRB
      table.cell_style = { size: 10, border_width: 0.5 }
    end
  end

  def order_lines_table
    order_lines_table = []
    header_row = []

    column_widths = case
    when order.is_recurrent?
      [40, 325, 40, 60, 60] # day , item, qty, price/cost, item-total
    else
      [0, 365, 40, 60, 60] # - , item, qty, price/cost, item-total
    end
    location_colspan = order.is_recurrent? ? 5 : 4
    totals_colspan = order.is_recurrent? ? 5 : 4

    header_row << { content: '<b>Day</b>', size: 10, inline_format: true, borders: BORDER_LTB, width: column_widths[0] } if order.is_recurrent?
    header_row << { content: '<b>Item</b>', borders: BORDER_LTB, width: column_widths[1] }
    header_row << { content: '<b>Qty</b>', borders: BORDER_LTB, width: column_widths[2] }
    header_row << { content: '<b>Per Item</b>', borders: BORDER_LTB, align: :right, width: column_widths[3] }
    header_row << { content: '<b>Total</b>', borders: BORDER_ALL, align: :right, width: column_widths[4] }

    order_lines_table << header_row

    # Order lines grouped by location
    order_lines.group_by(&:location).each do |location, location_order_lines|
      # Display the location across the whole table using colspan
      location_label = location.label
      location_row = [
        { content: location_label, colspan: location_colspan, background_color: LOCATION_COLOR }
      ]
      order_lines_table << location_row

      location_order_lines.group_by(&:supplier_profile).each do |supplier, supplier_order_lines|

        if order_suppliers.size > 1
          supplier_label = supplier.name.upcase
          supplier_row = [
            { content: supplier_label, colspan: location_colspan, background_color: SUPPLIER_COLOR }
          ]
          order_lines_table << supplier_row
        end

        supplier_order_lines.each do |order_line|
          item_value = order_line.is_gst_free ? '* ' : ''
          item_value += order_line.name
          if order_line.selected_menu_extras.present?
            grouped_extra = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call
            grouped_extra.each do |section, menu_extras|
              item_value += "\n"
              item_value += "<font size='9'>#{section.name}: #{menu_extras.map(&:name).map(&:strip).join(', ')}</font>"
            end
          end
          if order_line.note.present?
            item_value += "\n"
            item_value += "<font size='8'><i>#{order_line.note}</i></font>"
          end
          order_line_value = order_line.price_exc_gst(gst_country: order.symbolized_country_code)
          order_line_total_value = order_line_value * order_line.quantity

          order_line_row = []
          order_line_row << { content: order.delivery_at.to_s(:short_weekday), borders: BORDER_LB, width: column_widths[0] } if order.is_recurrent?
          order_line_row << { content: item_value, inline_format: true, borders: BORDER_LB, width: column_widths[1] }
          order_line_row << { content: order_line.quantity.to_s, borders: BORDER_LB, width: column_widths[2] }
          order_line_row << { content: number_to_currency(order_line_value), borders: BORDER_LB, align: :right, width: column_widths[3] }
          order_line_row << { content: number_to_currency(order_line_total_value), borders: BORDER_LRB, align: :right, width: column_widths[4] }

          order_lines_table << order_line_row
        end # order lines each
      end # suppliers row
    end # locations each


    totals_row = []
    ################################################################ Markup info
    if supplier_markups.present?
      markup_colspan = order.is_recurrent? ? 2 : 1
      totals_colspan = location_colspan - markup_colspan
      markup_row_content = "<b>Markup</b>\n"
      supplier_markups.each do |supplier_markup|
        markup_precission = supplier_markup.markup == supplier_markup.markup.to_i ? 0 : 2
        markup_row_content += "#{supplier_markup.supplier.name}: #{number_to_percentage(supplier_markup.markup, precision: markup_precission)}\n"
      end
      totals_row << { content: markup_row_content, align: :left, valign: :bottom, size: 15, colspan: markup_colspan, borders: BORDER_LTB }
    end

    ################################################################ Total info
    if (promotion = order.promotion.presence)
      discount_label = "#{promotion.name} (#{promotion.discount_note})"
    else
      discount_label = 'Discount'
    end

    total_row_content = "<b>Subtotal:</b>  #{number_to_currency(order.customer_subtotal)}\n"
    total_row_content += "<b>Delivery:</b>  #{number_to_currency(order.customer_delivery)}\n" if order.customer_delivery.present? && order.customer_delivery > 0
    total_row_content += "<b>#{discount_label}:</b> -#{number_to_currency(order.discount)}\n" if order.discount.present? && order.discount > 0.00
    total_row_content += "<b>GST:</b>  #{number_to_currency(order.customer_gst)}\n"
    total_row_content += "<b>Topup:</b>  #{number_to_currency(order.customer_topup)}\n" if order.customer_topup.present? && order.customer_topup > 0
    total_row_content += "<b>Surcharge:</b>  #{number_to_currency(order.customer_surcharge)}\n" if order.customer_surcharge.present? && order.customer_surcharge > 0
    total_row_content += "\n<b>Total (inc. GST):</b>  #{number_to_currency(order.customer_total)}"

    totals_row << { content: total_row_content, align: :right, colspan: totals_colspan, borders: supplier_markups.present? ? BORDER_TRB : BORDER_ALL }
    order_lines_table << totals_row

    table(order_lines_table) do |table|
      table.cell_style = TABLE_CELL_STYLE
      table.width = PAGE_PRINT_WIDTH
      table.row(0).background_color = BACKGROUND_COLOR
      table.header = true
      table.cell_style = { border_width: 0.5, size: 10 }
    end
  end

  def order_lines
    @_order_lines ||= begin
      lister_options = {
        order: order,
        confirmed_attendees_only: order.is_team_order?,
      }
      order_lines = OrderLines::List.new(options: lister_options).call
      order_lines.sort_by do |order_line|
        [
          order_line.location.id,
          order_line.supplier_profile.company_name.downcase,
          order_line.id
        ]
      end
    end
  end

  def order_suppliers
    @_order_suppliers ||= order.supplier_profiles
  end

  def supplier_markups
    return nil if is_quote || !customer.requires_supplier_markup

    @_supplier_markups ||= Orders::FetchSupplierMarkups.new(order: order, order_lines: order_lines).call
  end

  def supplementary_footer
    disclaimer_text = order_lines.any?(&:is_gst_free) ? '*GST-free item. ' : ''
    disclaimer_text += '**All prices in AUD.'
    float do
      text ' ', inline_format: true, size: META_FONT_SIZE # add spacing to push text to bottom of PDF
      text ' ', inline_format: true, size: META_FONT_SIZE
      text ' ', inline_format: true, size: META_FONT_SIZE
      text "<b>#{disclaimer_text}</b>", inline_format: true, size: META_FONT_SIZE
    end
  end

end
