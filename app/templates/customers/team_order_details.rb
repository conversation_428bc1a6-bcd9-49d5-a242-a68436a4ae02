class Customers::TeamOrderDetails < YordarPdf

  def initialize(team_order:, reference:, version: 1)
    @team_order = team_order
    @reference = reference
    @version = version

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    single_data_table
  end

private

  attr_reader :team_order, :reference, :version

  def report_folder
    'order_details'
  end

  def report_reference
    "#{team_order.id}/#{reference}"
  end

  # order info table, including delivery information, order lines details and totals
  def single_data_table
    table_cell_style = { inline_format: true }
    ################################################################ Header
    # ===  report title ===
    grid([0, 3], [0, LAST_COL]).bounding_box do
      heading = 'TEAM ORDER MANIFEST'
      text heading, align: :right, size: 20, style: :bold, valign: :bottom
    end

    # == order title info ==
    grid([3, 0], [3, LAST_COL]).bounding_box do
      order_name = team_order.name.present? ? team_order.name.upcase : ''
      customer_name = team_order.customer_profile.present? && team_order.customer_profile.customer_or_company_name.present? ? team_order.customer_profile.customer_or_company_name.upcase : ''
      customer_name = customer_name.strip
      title_row = [
        ["#{order_name} - ##{team_order.id} - #{customer_name}"]
      ]
      table(title_row, column_widths: { 0 => PAGE_PRINT_WIDTH }, cell_style: { font_style: :bold, border_width: 0.5 })
    end

    ################################################################ Order info
    grid([4, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      delivery_address = team_order.delivery_address_arr.join(', ')
      company_name = team_order.company_name || team_admin.company_name

      column_1 = []
      column_1 << ['Delivery date & time:', team_order.delivery_at.to_s(:full)]
      column_1 << ['Delivery address:', delivery_address]
      # column_1 << ['Number of People:', team_order.number_of_people] if !team_order.is_team_order? && team_order.number_of_people.present?
      column_1 << ['Po Number:', team_order.po_number] if team_order.po_number.present?

      column_2 = []
      column_2 << ['Company name:', company_name] if company_name.present?
      column_2 << ['Contact name:', team_order.contact_name]
      column_2 << ['Contact phone:', team_order.phone] if team_order.phone.present?
      column_2 << ['Cost Centre ID:', team_order.department_identity] if team_order.department_identity.present?

      row_size = [column_1.size, column_2.size].max

      order_info = []
      row_size.times do |num|
        row = []
        row += column_1[num].presence || ['', '']
        row += column_2[num].presence || ['', '']
        order_info << row
      end

      table(order_info) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.column(2).borders = BORDER_TB
        table.column(3).borders = BORDER_TRB

        table.column_widths = [80, 182, 85, 178]
        table.cell_style = { size: 10, border_width: 0.5 }
      end
      move_down 5
      ################################################################ Delivery instructions
      table([['Delivery instructions:', team_order.delivery_instruction]]) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).width = 115
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.cell_style = { size: 10, border_width: 0.5 }
      end
      move_down 5

      ################################################################ Supplier Info
      # display Supplier(s) name
      supplier_names = team_order.team_supplier_profiles.map(&:company_name) 
      table([['Partner:', supplier_names.join(', ') ]]) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).width = 115
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.cell_style = { size: 10, border_width: 0.5 }
      end
      move_down 5

      ################################################################ Levels based orders
      if has_team_order_levels?
        levels_info_message = '<b>Note</b>: This order contains multiple delivery levels.'
        info_row = { content: levels_info_message }
        table([[info_row]]) do |table|
          table.cell_style = table_cell_style
          table.width = PAGE_PRINT_WIDTH
          table.cell_style = { size: 10, border_width: 0.5 }
        end
        move_down 5
      end

      ################################################################ Attendee Order lines
      order_line_rows = []
      header_row = []
      header_row << { content: '<b>Item</b>', borders: BORDER_LTB, width: 425 }
      header_row << { content: '<b>Qty</b>', borders: BORDER_LTB, width: 40 }
      header_row << { content: '<b>Price</b>', borders: BORDER_ALL, align: :right, width: 60 }

      order_line_rows << header_row

      # Order lines grouped by Attendee
      attendee_grouped_order_lines = order_lines.group_by(&:team_order_attendee)
      attendee_grouped_order_lines = attendee_grouped_order_lines.sort_by{|attendee, _| attendee.present? ? attendee_name(attendee).downcase : '00000' }

      attendee_grouped_order_lines.each do |attendee, attendee_order_lines|
        order_line_rows << attendee_header_row(attendee, attendee_order_lines)
        attendee_order_lines.each do |attendee_order_line|
          order_line_rows << attendee_order_line_row(attendee_order_line)
        end
      end

      ################################################################ Total info
      totals = Orders::CalculateCustomerTotals.new(order: team_order).call

      total_row_content = "<b>Subtotal:</b>  #{number_to_currency(totals.subtotal)}\n"
      total_row_content += "<b>Delivery:</b>  #{number_to_currency(totals.delivery)}\n" if totals.delivery.present? && totals.delivery > 0
      total_row_content += "<b>GST:</b>  #{number_to_currency(totals.gst)}\n"
      total_row_content += "<b>Top-up:</b>  #{number_to_currency(totals.topup)}\n" if totals.topup.present? && totals.topup > 0
      total_row_content += "\n<b>Total (inc. GST):</b>  #{number_to_currency(totals.total)}"
      total_row = [
        { content: total_row_content, align: :right, colspan: 3 }
      ]
      order_line_rows << total_row

      table(order_line_rows) do |table|
        table.cell_style = table_cell_style
        table.width = PAGE_PRINT_WIDTH
        table.row(0).background_color = BACKGROUND_COLOR
        table.header = true
        table.cell_style = { border_width: 0.5, size: 10 }
      end
    end
  end

  def attendee_header_row(attendee, attendee_order_lines)
    attendee_name = attendee_name(attendee)
    if has_team_order_levels? && (level_name = attendee&.level&.name.presence)    
      attendee_name += " - #{level_name}" 
    end
    quantity = attendee_order_lines.sum(&:quantity)
    item_quanity = "<b>#{quantity}</b>"
    attendee_row = []
    attendee_row << { content: "<b>#{attendee_name}</b>", borders: BORDER_LTB, width: 425, background_color: LOCATION_COLOR }
    attendee_row << { content: item_quanity, borders: BORDER_LTB, background_color: LOCATION_COLOR, colspan: 2 }
    attendee_row
  end

  def attendee_order_line_row(attendee_order_line)
    item_name = attendee_order_line.is_gst_free ? '* ' : ''
    item_name += attendee_order_line.name
    if attendee_order_line.selected_menu_extras.present?
      grouped_extras = OrderLines::FetchSelectedMenuExtras.new(order_line: attendee_order_line).call
      grouped_extras.each do |section, menu_extras|
        item_name += "\n"
        item_name += "<font size='9'>#{section.name}: #{menu_extras.map(&:name).map(&:strip).join(', ')}</font>"
      end
    end
    if attendee_order_line.note.present?
      item_name += "\n"
      item_name += "<font size='8'><i>#{attendee_order_line.note}</i></font>"
    end

    quantity = attendee_order_line.quantity
    order_line_value = number_to_currency(attendee_order_line.price*quantity, precision: 2)

    attendee_row = []
    attendee_row << { content: item_name, borders: BORDER_LTB, width: 425 }
    attendee_row << { content: quantity.to_s, borders: BORDER_LTB, width: 40 }
    attendee_row << { content: order_line_value, borders: BORDER_ALL }
    attendee_row
  end

  def team_admin
    @_team_admin ||= team_order.customer_profile
  end

  def order_lines
    return @_order_lines if @_order_lines.present?

    lister_options = {
      order: team_order,
      confirmed_attendees_only: true,
    }
    @_order_lines = OrderLines::List.new(options: lister_options, includes: [:team_order_attendee]).call
  end

  def attendee_name(attendee)
    if attendee.present?
      attendee.name.upcase
    else
      "#{team_admin.name} (admin)"
    end
  end

  def has_team_order_levels?
    @_has_team_order_levels ||= team_order.team_order_levels.present?
  end

  def supplementary_footer
    # do nothing!
  end
end
