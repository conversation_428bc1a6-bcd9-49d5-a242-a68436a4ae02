class Suppliers::OrderSummary < YordarPdf
  include Rails.application.routes.url_helpers

  def initialize(supplier:, order_lines:, summary_day:, summary_type:)
    @supplier = supplier
    @order_lines = order_lines
    @summary_day = summary_day
    @summary_type = summary_type

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    repeat :all do
      page_header
    end

    grid([4, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      sorted_order_lines.each do |order, order_lines_per_order|
        if cursor < 90
          start_new_page
        end
        details_for(order)
        if order.is_team_order?
          team_order_lines_table_for(order_lines_per_order, order: order)
        else
          order_lines_table_for(order_lines_per_order)
        end
      end
    end
  end

private

  attr_reader :supplier, :order_lines, :summary_day, :summary_type

  def sorted_order_lines
    grouped_order_lines = order_lines.group_by(&:order)
    grouped_order_lines.sort_by{|order, _| [order.customer_profile.customer_or_company_name.downcase, order.delivery_at] }
  end

  def report_folder
    'supplier_summary'
  end

  def report_reference
    date_str = (summary_day.beginning_of_day).to_s(:date_compact)
    date_str += "_#{summary_version_number}" if summary_version_number > 1
    "#{supplier.id}/#{date_str}"
  end

  def summary_version_number
    [
      'daily', # 1
      'morning', # 2
      'reminder' # 3
    ].find_index(summary_type) + 1
  end

  def page_header
    # ===  report title ===
    grid([0, 3], [0, LAST_COL]).bounding_box do
      case summary_type
      when 'daily'
        text 'DAILY SUMMARY', align: :right, size: 20, style: :bold, valign: :bottom
      when 'morning'
        text 'DAILY MORNING SUMMARY', align: :right, size: 20, style: :bold, valign: :bottom
      when 'reminder'
        text 'ORDER REMINDER', align: :right, size: 20, style: :bold, valign: :bottom
      end
    end

    grid([1, 3], [1, LAST_COL]).bounding_box do
      text "FOR #{summary_day.end_of_day.to_s(:full_date).upcase}", align: :right, size: 18, style: :bold, valign: :bottom
      # transparent(0.5) {stroke_bounds}
    end

    # == supplier name ==
    grid([3, 0], [3, LAST_COL]).bounding_box do
      row = [["Supplier: #{supplier.name}"]]
      table(row, column_widths: { 0 => PAGE_PRINT_WIDTH }, cell_style: { font_style: :bold, border_width: 0.5 })
    end
  end

  def details_for(order)
    customer = order.customer_profile

    order_header_row = []
    order_header_row << { content: customer.customer_or_company_name, borders: BORDER_LTB, font_style: :bold }
    order_header_row << { content: "Delivery Time: #{order.delivery_at.to_s(:time_only)}", borders: BORDER_TRB, font_style: :bold }

    table([order_header_row], row_colors: ['ECD6D6'], column_widths: [400, 125], cell_style: { size: 10, border_width: 0.5 })

    order_name = order.is_recurrent? ? '(S) ' : '' # S -> means 'standing' aka. recurring order
    order_name += order.name
    latest_pdf = order.order_suppliers.map(&:documents).flatten.compact.max_by(&:version)
    latest_pdf_version = latest_pdf.present? ? latest_pdf.version : 1

    order_name_row = []
    order_name_row << { content: 'Order', borders: BORDER_LTR, font_style: :bold }
    order_name_row << { content: "#{order_name} - ##{order.id} (Ver.#{latest_pdf_version})", borders: BORDER_LTR, font_style: :normal }
    table([order_name_row], row_colors: ['ECD6D6'], column_widths: [125, 400], cell_style: { height: LINE_HEIGHT, size: 10, border_width: 0.5 })

    delivery_address_row = []
    delivery_address_row << { content: 'Address', borders: BORDER_LTR, font_style: :bold }
    delivery_address_row << { content: order.delivery_address_arr.join(', '), borders: BORDER_LTR, font_style: :normal }

    table([delivery_address_row], row_colors: ['ECD6D6'], column_widths: [125, 400], cell_style: { height: LINE_HEIGHT, size: 10, border_width: 0.5 })

    if order.delivery_instruction.present?
      delivery_instruction_row = []
      delivery_instruction_row << { content: 'Delivery Instructions', borders: BORDER_LTR, font_style: :bold }
      delivery_instruction_row << { content: order.delivery_instruction, borders: BORDER_LTR, font_style: :normal }

      table([delivery_instruction_row], row_colors: ['ECD6D6'], column_widths: [125, 400], cell_style: { height: LINE_HEIGHT, size: 10, border_width: 0.5 })
    end

    if order.delivery_type == 'loading_dock' && order.loading_dock.present?
      loading_dock_url = loading_dock_url(uuid: order.uuid, host: next_app_host)
      loading_dock_row = []
      loading_dock_row << { content: 'Loading Dock Code', borders: BORDER_LTR, font_style: :bold }
      loading_dock_row << { content: "#{order.loading_dock.code.truncate(50)} (<a href='#{loading_dock_url}'><u>Click here to view full</u></a>)", borders: BORDER_LTR, font_style: :normal, inline_format: true }

      table([loading_dock_row], row_colors: ['ECD6D6'], column_widths: [125, 400], cell_style: { height: LINE_HEIGHT, size: 10, border_width: 0.5 })
    end
  end

  def team_order_lines_table_for(order_lines, order:)
    order_lines.group_by(&:team_order_attendee).each do |attendee, attendee_order_lines|
      ordered_by = attendee.present? ? attendee.name : "#{order.customer_profile.name} (admin)"

      attendee_row = []
      attendee_row << { content: "Ordered By: <em>#{ordered_by}</em>", inline_format: true }
      table([attendee_row], row_colors: ['FFFFFF'], column_widths: [PAGE_PRINT_WIDTH], cell_style: { height: LINE_HEIGHT, size: 10, font_style: :italic, border_width: 0.5 })

      # == order lines ==
      order_line_rows = []
      attendee_order_lines.each do |order_line|
        order_line_rows << order_line_row_for(order_line)
      end

      table(order_line_rows, row_colors: ['FFFFFF'], column_widths: [40, 485], cell_style: { height: LINE_HEIGHT, size: 10, border_width: 0.5 })

      move_down 5 # we want gap after the table
    end
  end

  def order_lines_table_for(order_lines)
    location_grouped_order_lines = order_lines.group_by(&:location)
    has_single_default_location = location_grouped_order_lines.size == 1 && location_grouped_order_lines.first.first.label.downcase == 'your office'

    location_grouped_order_lines.each do |location, location_order_lines|
      if !has_single_default_location
        location_row = []
        location_row << { content: "<em>#{location.label}</em>", inline_format: true }
        table([location_row], row_colors: ['FFFFFF'], column_widths: [PAGE_PRINT_WIDTH], cell_style: { height: LINE_HEIGHT, size: 10, font_style: :italic, border_width: 0.5 })
      end

      # == order lines ==
      order_line_rows = []
      location_order_lines.each do |order_line|
        order_line_rows << order_line_row_for(order_line)
      end
      table(order_line_rows, row_colors: ['FFFFFF'], column_widths: [40, 485], cell_style: { height: LINE_HEIGHT, size: 10, border_width: 0.5 })

      move_down 5 # we want gap after the table
    end
  end

  def order_line_row_for(order_line)
    order_line_name = order_line.name
    order_line_name += " - <em>#{order_line.note}</em>" if order_line.note.present?
    order_line_row = []
    order_line_row << { content: "<b>#{order_line.quantity}</b> x", inline_format: true, align: :right, borders: BORDER_LTB }
    order_line_row << { content: order_line_name, inline_format: true, borders: BORDER_TRB }
    order_line_row
  end

  def supplementary_footer
    # do nothing!
  end

end
