class Suppliers::TeamOrderAveryLabels < YordarCsv

  def initialize(team_order:, supplier:, reference:)
    @team_order = team_order
    @supplier = supplier
    @report_reference = reference
    @csv_data = ''
  end

  def generate
    @csv_data = CSV.generate do |csv_rows|
      csv_rows << header_fields
      attendee_grouped_order_lines.each do |attendee, attendee_order_lines|
        attendee_rows = attendee_orderline_rows(attendee: attendee, attendee_order_lines: attendee_order_lines)
        attendee_rows.each do |attendee_row|
          csv_rows << attendee_row
        end
      end
    end
  end

private

  attr_reader :team_order, :supplier, :csv_data, :report_reference

  def report_folder
    'team_order_details'
  end

  def attendee_grouped_order_lines
    lister_options = {
      order: team_order,
      confirmed_attendees_only: true,
      supplier: supplier
    }
    order_lines = OrderLines::List.new(options: lister_options, includes: [:team_order_attendee]).call
    order_lines.group_by(&:team_order_attendee).sort_by do |attendee, _|
      attendee.blank? ? [1, nil] : [2, attendee.name]
    end
  end

  def has_attendee_levels?
    @_has_attendee_levels ||= team_order.team_order_levels.present?
  end

  def header_fields
    if has_attendee_levels?
      %w[first_name last_name item_name level]
    else
      %w[first_name last_name item_name]
    end
  end

  def attendee_orderline_rows(attendee:, attendee_order_lines:)
    attendee_order_lines.map do |order_line|
      item_name = order_line.name.gsub(/\t|\n/, '')
      if attendee.present?
        first_name = attendee.first_name
        last_name = attendee.last_name
        level_name = attendee.level&.name
      else
        first_name = team_admin.name
        last_name = 'ADMIN'
        level_name = nil
      end
      if has_attendee_levels?
        [first_name, last_name, item_name, level_name]
      else
        [first_name, last_name, item_name]
      end
    end
  end

  def attendee_orderline_rowsx(attendee:, attendee_order_lines:)
    attendee_order_lines.map do |order_line|
      item_name = order_line.name.gsub(/\t|\n/, '')
      if attendee.present?
        [
          attendee.first_name,
          attendee.last_name,
          item_name
        ]
      else
        [
          team_admin.name,
          'ADMIN',
          item_name
        ]
      end
    end
  end

  def team_admin
    @_team_admin ||= team_order.customer_profile
  end

end
