class Suppliers::OrderDetails < YordarPdf

  def initialize(order:, supplier:, reference:, variation: 'normal', version: 1)
    @order = order
    @supplier = supplier
    @reference = reference
    @variation = variation
    @version = version

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    single_data_table
  end

private

  attr_reader :order, :supplier, :reference, :variation, :version

  def report_folder
    'order_details'
  end

  def is_heads_up?
    variation == 'heads_up'
  end

  def is_delivery_docket?
    variation == 'delivery_docket'
  end

  def report_reference
    case
    when is_delivery_docket?
      "#{order.id}/#{reference}-delivery"
    else
      "#{order.id}/#{reference}"
    end
  end

  # order info table, including delivery information, order lines details and totals
  def single_data_table
    # get the current suppliers's order_lines
    lister_options = {
      order: order,
      supplier: supplier,
    }
    order_order_lines = OrderLines::List.new(options: lister_options).call

    table_cell_style = { inline_format: true }
    ################################################################ Header
    # ===  report title ===
    grid([0, 3], [0, LAST_COL]).bounding_box do
      title_text = case
      when order.is_team_order? && is_heads_up?
        'UPCOMING TEAM ORDER'
      when is_heads_up?
        'UPCOMING ORDER'
      when is_delivery_docket?
        'DELIVERY DOCKET'
      else
        'PURCHASE ORDER'
      end
      text title_text, align: :right, size: 20, style: :bold, valign: :bottom
    end

    warning_text = case
    when is_heads_up?
      'ORDER STILL IN PROGRESS'
    when !is_delivery_docket?
      'DO NOT TAKE WITH DELIVERY'
    end
    if warning_text.present?
      text warning_text, size: 20, style: :bold, color: 'C71E2C', align: :right
    end

    # == order title info ==
    grid([3, 0], [3, LAST_COL]).bounding_box do
      order_name = order.name.present? ? order.name.upcase : ''
      customer_name = order.customer_profile.present? && order.customer_profile.customer_or_company_name.present? ? order.customer_profile.customer_or_company_name.upcase : ''
      title_row = [
        ["#{order_name} - ##{order.id} (Ver.#{version}) - #{customer_name}"]
      ]
      table(title_row, column_widths: { 0 => PAGE_PRINT_WIDTH }, cell_style: { font_style: :bold, border_width: 0.5 })
    end

    grid([4, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      ################################################################ Order info

      delivery_address = order.delivery_address_arr.join(', ')
      company_name = order.company_name || order.customer_profile.company_name

      if order.is_team_order?
        invited_attendees = order.team_order_attendees.where.not(status: %w[cancelled declined])
        invited_attendees_count = invited_attendees.count
        invited_attendees_count = order.number_of_people if order.number_of_people.present? && order.number_of_people > invited_attendees_count
        confirmed_attendees_count = invited_attendees.select{|attendee| attendee.status == 'ordered' }.size
        number_of_people = case
        when confirmed_attendees_count > 0 && confirmed_attendees_count >= invited_attendees_count
          "#{confirmed_attendees_count} ordered"
        when confirmed_attendees_count > 0
          "#{confirmed_attendees_count} ordered out of #{invited_attendees_count}"
        else
          "#{invited_attendees_count} invited"
        end
      else
        number_of_people = order.number_of_people
      end

      column_1 = []
      column_1 << ['Delivery date & time:', order.delivery_at.to_s(:full)]
      column_1 << ['Delivery address:', delivery_address]
      column_1 << ['Number of People:', number_of_people] if number_of_people.present?

      column_2 = []
      column_2 << ['Company name:', company_name] if company_name.present?
      column_2 << ['Contact name:', order.contact_name]
      column_2 << ['Contact phone:', order.phone] if order.phone.present?

      row_size = [column_1.size, column_2.size].max

      order_info = []
      row_size.times do |num|
        row = []
        row += column_1[num].presence || ['', '']
        row += column_2[num].presence || ['', '']
        order_info << row
      end

      table(order_info) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.column(2).borders = BORDER_TB
        table.column(3).borders = BORDER_TRB

        table.column_widths = [80, 182, 85, 178]
        table.cell_style = { size: 10, border_width: 0.5 }
      end

      move_down 5

      ################################################################ Supplier Info
      # display supplier name
      table([['Partner:', "<b>#{supplier.name}</b>"]]) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).width = 115
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.cell_style = { inline_format: true, size: 10, border_width: 0.5 }
      end
      move_down 5

      ################################################################ Delivery instructions
      if order.is_contactless_delivery? && supplier.provides_contactless_delivery
        delivery_type_info = 'This is a contactless delivery order. Please read delivery instructions for more info.'
        table([[delivery_type_info]]) do |table|
          table.width = PAGE_PRINT_WIDTH
          table.cell_style = { font_style: :bold, size: 10, border_width: 0.5 }
        end
        move_down 5
      end
      # display supplier specific instructions if there is any
      instructions = order.delivery_instruction
      if order.is_event_order?
        # custom order has supplier specific settings
        custom_order_supplier = CustomOrderSupplier.where(order: order, supplier_profile: supplier).first
        instructions = custom_order_supplier.delivery_note if custom_order_supplier.present?
      end
      table([['Delivery instructions:', instructions]]) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).width = 115
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.cell_style = { size: 10, border_width: 0.5 }
      end
      move_down 5
      ################################################################ Order lines
      order_lines_table = []
      header_row = []

      column_widths = case
      when order.is_recurrent?
        [40, 385, 40, 60] # day , item, qty, per_item
      else
        [nil, 425, 40, 60] # - , item, qty, per_item
      end
      colspan = column_widths.compact.size

      header_row << { content: '<b>Day</b>', size: 10, inline_format: true, borders: BORDER_LTB, width: column_widths[0] } if order.is_recurrent?
      header_row << { content: '<b>Item</b>', borders: BORDER_LTB, width: column_widths[1] }
      header_row << { content: '<b>Qty</b>', borders: BORDER_LTB, width: column_widths[2] }
      header_row << { content: '<b>Per Item</b>', borders: BORDER_ALL, align: :right, width: column_widths[3] }

      order_lines_table << header_row

      # Order lines grouped by location
      order_order_lines.sort_by { |order_line| [order_line.location_id, order_line.supplier_profile.company_name.downcase, order_line.id] }.group_by(&:location).each do |location, order_lines|
        # Display the location across the whole table using colspan
        location_label = location&.label || 'Your Office'
        location_row = [
          { content: location_label, colspan: colspan, background_color: LOCATION_COLOR }
        ]
        order_lines_table << location_row

        order_lines.each do |order_line|
          item_value = order_line.is_gst_free ? '* ' : ''
          item_value += order_line.name
          if order_line.selected_menu_extras.present?
            grouped_extra = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call
            grouped_extra.each do |section, menu_extras|
              item_value += "\n"
              item_value += "<font size='9'>#{section.name}: #{menu_extras.map(&:name).map(&:strip).join(', ')}</font>"
            end
          end
          if order_line.note.present?
            item_value += "\n"
            item_value += "<font size='8'><i>#{order_line.note}</i></font>"
          end
          order_line_value = ''
          if show_pricing?
            baseline_value = order_line.baseline
            if order_line.is_gst_free? || order_line.is_gst_inc?
              baseline_value_with_gst = baseline_value
            else
              baseline_value_with_gst = baseline_value.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)
            end
            order_line_value = number_to_currency(baseline_value_with_gst, precision: 2)
          end

          order_line_row = []
          order_line_row << { content: order.delivery_at.to_s(:short_weekday), borders: BORDER_LB, width: column_widths[0] } if order.is_recurrent?
          order_line_row << { content: item_value, inline_format: true, borders: BORDER_LB, width: column_widths[1] }
          order_line_row << { content: order_line.quantity.to_s, borders: BORDER_LB, width: column_widths[2] }
          order_line_row << { content: order_line_value, borders: BORDER_LRB, align: :right, width: column_widths[3] }

          order_lines_table << order_line_row
        end
      end

      ################################################################ Total info
      if show_pricing?
        totals = Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, save_totals: true).call

        baseline_totals = Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, baseline: true).call
        total_row_content = ''
        if baseline_totals.delivery.present? && baseline_totals.delivery > 0
          delivery_with_gst = baseline_totals.gst.present? && baseline_totals.gst > 0 ? baseline_totals.delivery.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, country_code).to_f) : baseline_totals.delivery.round(2)
          total_row_content += "<b>Delivery:</b>  #{number_to_currency(delivery_with_gst)}\n"
        end
        total_row_content += "<b>GST:</b>  #{number_to_currency(baseline_totals.gst)}\n"
        total_row_content += "<b>Top-up:</b>  #{number_to_currency(baseline_totals.topup)}\n" if baseline_totals.topup.present? && baseline_totals.topup > 0
        total_row_content += "\n<b>Total (inc. GST):</b>  #{number_to_currency(baseline_totals.total)}"
        if totals.total != baseline_totals.total
          total_row_content += "\n<em>Total with discount (inc. GST):  #{number_to_currency(totals.total)}</em>"
        else
          total_row_content += "\n<em>Total payable to supplier</em>"
        end

        total_row = [
          { content: total_row_content, align: :right, colspan: colspan }
        ]
        order_lines_table << total_row
      end
      table(order_lines_table) do |table|
        table.cell_style = table_cell_style
        table.width = PAGE_PRINT_WIDTH
        table.row(0).background_color = BACKGROUND_COLOR
        table.header = true
        table.cell_style = { border_width: 0.5, size: 10 }
      end
    end
  end

  def country_code
    @_country_code ||= order.symbolized_country_code
  end

  def supplementary_footer
    # do nothing!
  end

  def show_pricing?
    !is_heads_up? && !is_delivery_docket?
  end
end
