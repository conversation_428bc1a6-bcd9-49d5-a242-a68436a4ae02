class Suppliers::CurrentMenu < YordarCsv

  def initialize(supplier:, reference:)
    @supplier = supplier
    @report_reference = reference
    @csv_data = ''
  end

  def generate
    @csv_data = CSV.generate do |csv_rows|
      csv_rows << header_fields
      menu_items.group_by(&:menu_section).each do |menu_section, section_items|
        section_items.each do |menu_item|
          item_rows = item_rows_for(menu_section: menu_section, menu_item: menu_item)
          item_rows.each do |item_row|
            csv_rows << item_row
          end
        end
      end
    end
  end

private

  attr_reader :supplier, :csv_data, :report_reference

  def report_folder
    'supplier_menu'
  end

  def header_fields
    ['Section Name', 'Item Name', 'Serving Size', 'Baseline Price (exc gst)']
  end

  def item_rows_for(menu_section:, menu_item:)
    if (serving_sizes = menu_item.serving_sizes.where(archived_at: nil).presence)
      serving_sizes.map do |serving_size|
        [
          menu_section.name,
          menu_item.name,
          serving_size.name,
          serving_size.price
        ]
      end
    else
      [
        [
          menu_section.name,
          menu_item.name,
          '',
          menu_item.price
        ]
      ]
    end
  end

  def menu_items
    @_menu_items ||= begin
      lister_options = {
        show_visible: true,
        show_active: true,
        supplier: supplier,
        ignore_custom_menu_sections: true,
        order_by: { weight: :asc },
      }
      MenuItems::List.new(includes: %i[menu_section serving_sizes], options: lister_options).call
    end
  end

end
