class Suppliers::OrderJsonDetails < Yorda<PERSON><PERSON>son

  def initialize(order:, supplier:, reference:, version:)
    @order = order
    @supplier = supplier
    @report_reference = reference
    @version = version
    @json_data = ''
  end

  def generate
    @json_data = generate_json_data
  end

private

  attr_reader :order, :supplier, :json_data, :report_reference, :version

  def report_folder
    'order_details'
  end

  def generate_json_data
    if json_template.present?
      ActionController::Base.new.render_to_string(
        partial: json_template,
        locals: {
          order: order,
          supplier: supplier,
          version: version,
        })
    else
      { id: order.id, name: order.name, supplier: supplier.company_name }.to_json
    end
  end

  def json_template
    @_json_template = case
    when supplier.uses_flex_catering
      'api/external/orders/flex_order'
    else
      nil
    end
  end

end
