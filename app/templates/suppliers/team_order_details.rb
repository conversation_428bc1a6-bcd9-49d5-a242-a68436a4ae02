class Suppliers::TeamOrderDetails < YordarPdf

  def initialize(team_order:, supplier:, reference:, variation: 'normal', version: 1)
    @team_order = team_order
    @supplier = supplier
    @reference = reference
    @variation = variation
    @version = version

    # prawn/pdf setup in YordarPdf
    super
  end

  def generate
    single_data_table
  end

private

  attr_reader :team_order, :supplier, :reference, :variation, :version

  def report_folder
    'order_details'
  end

  def is_delivery_docket?
    variation == 'delivery_docket'
  end

  def report_reference
    case
    when is_delivery_docket?
      "#{team_order.id}/#{reference}-delivery"
    else
      "#{team_order.id}/#{reference}"
    end
  end

  # order info table, including delivery information, order lines details and totals
  def single_data_table
    table_cell_style = { inline_format: true }
    ################################################################ Header
    # ===  report title ===
    grid([0, 3], [0, LAST_COL]).bounding_box do
      heading = is_delivery_docket? ? 'TEAM DELIVERY DOCKET' : 'TEAM PURCHASE ORDER'
      text heading, align: :right, size: 20, style: :bold, valign: :bottom
    end

    if !is_delivery_docket?
      (text 'DO NOT TAKE WITH DELIVERY', size: 20, style: :bold, color: 'C71E2C', align: :right)
    end

    # == order title info ==
    grid([3, 0], [3, LAST_COL]).bounding_box do
      order_name = team_order.name.present? ? team_order.name.upcase : ''
      row = [["#{order_name} - ##{team_order.id} (Ver.#{version}) - #{company_or_team_admin_name.upcase}"]]
      table(row, column_widths: { 0 => PAGE_PRINT_WIDTH }, cell_style: { font_style: :bold, border_width: 0.5 })
    end

    ################################################################ Order info
    grid([4, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      delivery_address = team_order.delivery_address_arr.join(', ')
      company_name = team_order.company_name || team_admin.company_name

      column_1 = []
      column_1 << ['Delivery date & time:', team_order.delivery_at.to_s(:full)]
      column_1 << ['Delivery address:', delivery_address]
      column_1 << ['Po Number:', team_order.po_number] if team_order.po_number.present?
      column_1 << ['Cost Centre ID:', team_order.department_identity] if team_order.department_identity.present?

      column_2 = []
      column_2 << ['Contact name:', team_order.contact_name]
      column_2 << ['Company name:', company_name] if company_name.present?
      column_2 << ['Contact phone:', team_order.phone] if team_order.phone.present?

      row_size = [column_1.size, column_2.size].max

      order_info = []
      row_size.times do |num|
        row = []
        row += column_1[num].presence || ['', '']
        row += column_2[num].presence || ['', '']
        order_info << row
      end

      table(order_info) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.column(2).borders = BORDER_TB
        table.column(3).borders = BORDER_TRB

        table.column_widths = [80, 182, 85, 178]
        table.cell_style = { size: 10, border_width: 0.5 }
      end
      move_down 5
      ################################################################ Delivery instructions
      # display supplier specific instructions if there is any
      table([['Delivery instructions:', team_order.delivery_instruction]]) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).width = 115
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.cell_style = { size: 10, border_width: 0.5 }
      end
      move_down 5

      ################################################################ Supplier Info
      # display supplier name
      table([['Supplier:', supplier.name]]) do |table|
        table.width = PAGE_PRINT_WIDTH
        table.column(0).width = 115
        table.column(0).borders = BORDER_LTB
        table.column(1).borders = BORDER_TRB
        table.cell_style = { size: 10, border_width: 0.5 }
      end
      move_down 5

      ################################################################ Levels based orders
      if has_team_order_levels?
        levels_info_message = '<b>Note</b>: This order contains multiple delivery levels, make sure to add the level next to the name on the individually packed item.'
        info_row = { content: levels_info_message }
        table([[info_row]]) do |table|
          table.cell_style = table_cell_style
          table.width = PAGE_PRINT_WIDTH
          table.cell_style = { size: 10, border_width: 0.5 }
        end
        move_down 5
      end

      ################################################################ Order lines
      ols = []
      header_row = []
      if has_team_order_levels?
        header_row << { content: '<b>Label Information</b>', borders: BORDER_LTB, width: 125, colspan: 2 }
        header_row << { content: '<b>Item</b>', borders: BORDER_LTB, width: 200 }
      else
        header_row << { content: '<b>Label Information</b>', borders: BORDER_LTB, width: 125 }
        header_row << { content: '<b>Item</b>', borders: BORDER_LTB, width: 300 }
      end
      header_row << { content: '<b>Qty</b>', borders: BORDER_LTB, width: 40 }
      header_row << { content: '<b>Per Item</b>', borders: BORDER_ALL, align: :right, width: 60 }

      ols << header_row

      # Order lines grouped by menu item id
      menu_item_grouped_order_lines = order_lines.group_by(&:menu_item).sort_by{|menu_item, _| menu_item.name }

      menu_item_grouped_order_lines.each do |menu_item, item_order_lines|
        ols << menu_item_row(menu_item, item_order_lines)

        attendee_ordered_order_lines = item_order_lines.group_by(&:team_order_attendee)
        attendee_ordered_order_lines = attendee_ordered_order_lines.sort_by{|attendee, attendee_order_lines| [-attendee_order_lines.sum(&:quantity), attendee_name(attendee).downcase] }

        attendee_ordered_order_lines.each do |attendee, attendee_order_lines|
          order_line_rows = attendee_orderline_rows(attendee: attendee, attendee_order_lines: attendee_order_lines)
          order_line_rows.each do |order_line_row|
            ols << order_line_row
          end
        end
      end

      if !is_delivery_docket?
        ################################################################ Total info
        totals = Orders::CalculateSupplierTotals.new(order: team_order, supplier: supplier, order_lines: order_lines, save_totals: true).call

        baseline_totals = Orders::CalculateSupplierTotals.new(order: team_order, supplier: supplier, order_lines: order_lines, baseline: true).call
        total_quanity = order_lines.map(&:quantity).sum
        total_row_content = "<b>Total meals:</b> #{total_quanity}\n"
        if baseline_totals.delivery.present? && baseline_totals.delivery > 0
          delivery_with_gst = baseline_totals.delivery.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)
          total_row_content += "<b>Delivery:</b>  #{number_to_currency(delivery_with_gst)}\n"
        end
        total_row_content += "<b>GST:</b>  #{number_to_currency(baseline_totals.gst)}\n"
        total_row_content += "<b>Top-up:</b>  #{number_to_currency(baseline_totals.topup)}\n" if baseline_totals.topup.present? && baseline_totals.topup > 0
        total_row_content += "\n<b>Total (inc. GST):</b>  #{number_to_currency(baseline_totals.total)}"
        if totals.total != baseline_totals.total
          total_row_content += "\n<em>Total with discount (inc. GST):  #{number_to_currency(totals.total)}</em>"
        else
          total_row_content += "\n<em>Total payable to supplier</em>"
        end

        totals_colspan = has_team_order_levels? ? 5 : 4
        total_row = [
          { content: total_row_content, align: :right, colspan: totals_colspan }
        ]
        ols << total_row
      end

      table(ols) do |table|
        table.cell_style = table_cell_style
        table.width = PAGE_PRINT_WIDTH
        table.row(0).background_color = BACKGROUND_COLOR
        table.header = true
        table.cell_style = { border_width: 0.5, size: 10 }
      end
    end
  end

  def menu_item_row(menu_item, item_order_lines)
    item_name = "<b>#{menu_item.name}</b>"
    quantity = item_order_lines.sum(&:quantity)
    item_quanity = "<b>#{quantity}</b>"
    item_row = []
    if has_team_order_levels?
      item_row << { content: '<b>Name / Level</b>', borders: BORDER_LTB, width: 125, background_color: LOCATION_COLOR, colspan: 2 }
      item_row << { content: item_name, borders: BORDER_LTB, width: 200, background_color: LOCATION_COLOR }
    else
      item_row << { content: '<b>Name</b>', borders: BORDER_LTB, width: 125, background_color: LOCATION_COLOR }
      item_row << { content: item_name, borders: BORDER_LTB, width: 300, background_color: LOCATION_COLOR }
    end
    item_row << { content: item_quanity, borders: BORDER_LTB, background_color: LOCATION_COLOR, colspan: 2 }
    item_row
  end

  def attendee_orderline_rows(attendee:, attendee_order_lines:)
    serving_size_grouped_order_lines = attendee_order_lines.group_by(&:serving_size)
    serving_size_grouped_order_lines.map do |_, serving_order_lines|
      attendee_order_line = serving_order_lines.first
      item_name = attendee_order_line.is_gst_free ? '* ' : ''
      item_name += attendee_order_line.name
      if attendee_order_line.selected_menu_extras.present?
        grouped_extras = OrderLines::FetchSelectedMenuExtras.new(order_line: attendee_order_line).call
        grouped_extras.each do |section, menu_extras|
          item_name += "\n"
          item_name += "<font size='9'>#{section.name}: #{menu_extras.map(&:name).map(&:strip).join(', ')}</font>"
        end
      end
      if attendee_order_line.note.present?
        item_name += "\n"
        item_name += "<font size='8'><i>#{attendee_order_line.note}</i></font>"
      end

      quantity = serving_order_lines.sum(&:quantity)

      order_line_value = ''
      if !is_delivery_docket?
        baseline_value = attendee_order_line.baseline
        if attendee_order_line.is_gst_free? || attendee_order_line.is_gst_inc?
          baseline_value_with_gst = baseline_value
        else
          baseline_value_with_gst = baseline_value.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)
        end
        order_line_value = number_to_currency(baseline_value_with_gst, precision: 2)
      end

      attendee_row = []
      attendee_row << { content: attendee_name(attendee), borders: BORDER_LTB, width: 125 }
      if has_team_order_levels?
        level_name = attendee.present? ? attendee.level&.name : ''
        attendee_row << { content: level_name, borders: BORDER_LTB, width: 100 }
        attendee_row << { content: item_name, borders: BORDER_LTB, width: 200 }
      else
        attendee_row << { content: item_name, borders: BORDER_LTB, width: 300 }
      end
      attendee_row << { content: quantity.to_s, borders: BORDER_LTB, width: 40 }
      attendee_row << { content: order_line_value, borders: BORDER_ALL }
      attendee_row
    end
  end

  def team_admin
    @_team_admin ||= team_order.customer_profile
  end

  def order_lines
    return @_order_lines if @_order_lines.present?

    lister_options = {
      order: team_order,
      supplier: supplier,
      confirmed_attendees_only: true,
    }
    @_order_lines = OrderLines::List.new(options: lister_options, includes: %i[menu_item team_order_attendee]).call
  end

  def attendee_name(attendee)
    if attendee.present?
      attendee.name.upcase
    else
      "#{team_admin.name} (admin)"
    end
  end

  def company_or_team_admin_name
    case
    when team_admin.present? && team_admin.customer_or_company_name.present?
      team_admin.customer_or_company_name
    else
      ''
    end
  end

  def has_team_order_levels?
    @_has_team_order_levels ||= team_order.team_order_levels.present?
  end

  def country_code
    @_country_code ||= team_order.symbolized_country_code
  end

  def supplementary_footer
    # do nothing!
  end
end
