class Admins::StaffingSpend < YordarCsv

  def initialize(staffing_spends:, with_orders: true)
    @staffing_spends = staffing_spends
    @with_orders = with_orders

    @csv_data = ''
  end

  def generate
    @csv_data = CSV.generate do |csv_rows|
      csv_rows << header_fields
      staffing_spends.each do |staffing_spend|
        manager = staffing_spend.manager
        manager_quantities = {
          quantity: 0,
          item_hours: 0,
          hours: 0,
        }
        staffing_spend.customer_spends.each do |customer_spend|
          customer_spend.item_spends.each do |item_spend|
            manager_quantities[:quantity] += item_spend.quantity
            manager_quantities[:item_hours] += item_spend.item_hours
            manager_quantities[:hours] += item_spend.hours
            csv_rows << spends_row_for(manager: manager, customer_spend: customer_spend, item_spend: item_spend)
          end # item spends
        end # customer spends
        next if manager_quantities[:hours] == 0 || staffing_spend.customer_spends.size == 1

        csv_rows << totals_row_for(manager: manager, quantities: manager_quantities)
      end # staffing spends
    end
  end

private

  attr_reader :staffing_spends, :with_orders, :csv_data

  def report_folder
    'staffing_log'
  end

  def report_reference
    "staffing-log-#{Time.zone.now.to_s(:filename)}"
  end

  def header_fields
    fields = ['Pantry Manager', 'Customer', 'Item', 'Quantity', 'Hours per item', 'Total Hours']
    fields << 'Order IDs' if with_orders
    fields
  end

  def spends_row_for(manager:, customer_spend:, item_spend:)
    row = []
    row << manager.name
    row << customer_spend.customer.name
    row << item_spend.item.name
    row << item_spend.quantity
    row << item_spend.item_hours
    row << item_spend.hours
    if with_orders
      row << customer_spend.orders.map(&:id).join(' | ')
    end
    row
  end

  def totals_row_for(manager:, quantities:)
    row = []
    row << manager.name
    row << ''
    row << 'Total'
    row << quantities[:quantity]
    row << quantities[:item_hours]
    row << quantities[:hours]
    row
  end
  
end
