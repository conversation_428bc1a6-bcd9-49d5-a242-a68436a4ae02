class RateCards::CheckUsage

  def initialize(rate_card:, since: Time.zone.now)
    @rate_card = rate_card
    @since = since
  end

  def call
    future_orders.present?
  end

private

  attr_reader :rate_card, :since

  def future_orders
    orders = Order.where(status: %w[pending quoted new amended confirmed])
    orders = orders.where('delivery_at >= ?', since)
    orders = orders.joins(:order_lines).where(order_lines: { menu_item: menu_item })
    orders = orders.where(order_lines: { serving_size: serving_size }) if serving_size.present?
    orders
  end

  def menu_item
    @_menu_item = rate_card.menu_item
  end

  def serving_size
    @_serving_size = rate_card.serving_size
  end

end