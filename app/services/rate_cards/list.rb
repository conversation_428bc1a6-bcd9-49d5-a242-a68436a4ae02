class RateCards::List

  def initialize(customer:, options: {})
    @customer = customer
    @filter_options = [default_options, options.symbolize_keys].inject(&:merge)
  end

  def call
    return [] if customer_company.blank?

    @rate_cards = base_rate_cards
    filter_by_customer_company
    filter_by_supplier if filter_options[:supplier].present?
    filter_by_menu_item if filter_options[:menu_item].present?
    filter_by_serving_size if filter_options[:serving_size].present?
    filter_active if filter_options[:active_only].present?

    rate_cards
  end

private

  attr_reader :customer, :filter_options
  attr_accessor :rate_cards

  def customer_company
    @_customer_company ||= customer.present? && customer.instance_of?(CustomerProfile) && customer.company.presence
  end

  def base_rate_cards
    RateCard.all
  end

  def filter_by_customer_company
    @rate_cards = rate_cards.where(company: customer_company)
  end

  def filter_by_supplier
    return if filter_options[:menu_item].present? || filter_options[:serving_size].present?

    @rate_cards = rate_cards.joins(:supplier_profile).where(supplier_profiles: { id: filter_options[:supplier].id })
  end

  def filter_by_menu_item
    return if filter_options[:serving_size].present?

    @rate_cards = rate_cards.where(menu_item: filter_options[:menu_item])
  end

  def filter_by_serving_size
    @rate_cards = rate_cards.where(serving_size: filter_options[:serving_size])
  end

  def filter_active
    @rate_cards = rate_cards.joins(:menu_item).where(menu_items: { archived_at: nil })
  end

  def default_options
    {
      supplier: nil,
      menu_item: nil,
      serving_size: nil,
      active_only: false,
    }
  end

end
