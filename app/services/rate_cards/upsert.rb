class RateCards::Upsert

  ORDERLINE_CHANGE_FIELDS = %w[price cost].freeze

  def initialize(rate_card_params:, rate_card: nil)
    @rate_card_params = rate_card_params
    @rate_card = rate_card.presence || fetch_rate_card
    @result = Result.new
  end

  def call
    if can_upsert?
      has_potential_order_line_changes = rate_card.new_record? || detect_changes
      if rate_card.update(rate_card_params)
        result.rate_card = rate_card
        update_future_order_lines if has_potential_order_line_changes
      else
        result.errors += rate_card.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :rate_card, :rate_card_params, :result

  def can_upsert?
    case
    when serving_size.present? && serving_size.archived_at.present?
      result.errors << 'Cannot create a rate card for an archived serving size'
    when serving_size.present? && (menu_item.blank? || serving_size.menu_item != menu_item)
      result.errors << 'Serving Size does not belong to the Menu Item'
    when menu_item.blank?
      result.errors << 'Cannot create a rate card without a menu item'
    when menu_item.archived_at.present?
      result.errors << 'Cannot create a rate card for an archived menu item'
    when menu_item.serving_sizes.where(archived_at: nil).present? && serving_size.blank?
      result.errors << 'Cannot create a non-serving rate card for a menu item with serving sizes'
    end
    result.errors.blank?
  end

  def fetch_rate_card
    retrieval_params = {
      menu_item: menu_item,
      serving_size: serving_size,
      company: company,
    }
    RateCard.where(retrieval_params).first_or_initialize
  end

  def detect_changes
    rate_card.assign_attributes(sanitized_params)
    ORDERLINE_CHANGE_FIELDS.detect do |field|
      rate_card.changes[field].present? && rate_card.changes[field].first != rate_card.changes[field].last
    end.present?
  end

  def sanitized_params
    rate_card_params.except(:menu_item_id, :serving_size_id, :company_id)
  end

  def menu_item
    @_menu_item ||= case
    when rate_card_params[:menu_item].present?
      rate_card_params[:menu_item]
    when menu_item_id = rate_card_params[:menu_item_id].presence
      MenuItem.where(id: menu_item_id).first
    when rate_card.present?
      rate_card.menu_item
    end
  end

  def serving_size
    @_serving_size ||= case
    when rate_card_params[:serving_size].present?
      rate_card_params[:serving_size]
    when serving_size_id = rate_card_params[:serving_size_id].presence
      ServingSize.where(id: serving_size_id).first
    when rate_card.present?
      rate_card.serving_size
    end
  end

  def company
    @_company ||= case
    when rate_card_params[:company].present?
      rate_card_params[:company]
    when company_id = rate_card_params[:company_id].presence
      Company.where(id: company_id).first
    when rate_card.present?
      rate_card.company
    end
  end

  def update_future_order_lines
    has_existing_update = OrderLines::FutureUpdateExists.new(item: rate_card).call
    return if has_existing_update

    MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size, rate_card: rate_card).delay(queue: :data_integrity).call
  end

  class Result
    attr_accessor :rate_card, :errors

    def initialize
      @rate_card = nil
      @errors = []
    end

    def success?
      errors.blank? && rate_card.present? && rate_card.valid? && rate_card.persisted?
    end
  end
end
