class RateCards::Archive

  def initialize(rate_card:, forced: nil)
    @rate_card = rate_card
    @is_forced_archive = forced.presence || false
    @result = Result.new(rate_card: rate_card)
  end

  def call
    if can_archive? && rate_card.destroy
      update_future_order_lines
    end
    result
  end

private

  attr_accessor :rate_card, :is_forced_archive, :result

  def can_archive?
    case
    when rate_card.blank?
      result.errors << 'Cannot archive a missing rate card'
    when !is_forced_archive && rate_card_in_use?
      result.warnings << 'The item is still in use!'
      result.warnings << 'Please be aware that the future order lines will revert back to menu item pricing.'
    end
    result.errors.blank? && result.warnings.blank?
  end

  def update_future_order_lines
    has_existing_update = OrderLines::FutureUpdateExists.new(item: rate_card, is_archived: true).call
    return if has_existing_update

    MenuItems::UpdateFutureOrderLines.new(menu_item: rate_card.menu_item, serving_size: rate_card.serving_size).delay(queue: :data_integrity).call
  end

  def rate_card_in_use?
    RateCards::CheckUsage.new(rate_card: rate_card).call
  end

  class Result
    attr_accessor :rate_card, :errors, :warnings

    def initialize(rate_card:)
      @rate_card = rate_card
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && warnings.blank?
    end
  end
end
