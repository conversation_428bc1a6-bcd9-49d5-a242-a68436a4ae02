class EmployeeSurveys::Submit

  def initialize(employee_survey:, submission_params: {})
    @employee_survey = employee_survey
    @submission_params = submission_params

    @result = Result.new
  end

  def call
    return result if !can_submit?

    @survey_submission = create_submission
    save_answers

    result
  end

private

  attr_reader :employee_survey, :submission_params, :survey_submission, :result

  def can_submit?
    case
    when employee_survey.blank?
      result.errors << 'Missing Survey'
    when submission_params[:overall_rating].blank? && submission_answers.blank?
      result.errors << 'Cannot submit survey without any answers'
    end

    result.errors.blank?
  end

  def create_submission
    sanitized_submission_params = submission_params.except(:survey_answers)
    submission = employee_survey.submissions.new(sanitized_submission_params)
    if submission.save
      result.submission = submission
    else
      result.errors += submission.errors.full_messages
    end
    submission
  end

  def save_answers
    return if !survey_submission.persisted?

    submission_answers.each do |submission_answer|
      answer_submitter = EmployeeSurveys::SubmitAnswer.new(employee_survey_submission: @survey_submission, answer_params: submission_answer).call

      result.errors += answer_submitter.errors if !answer_submitter.success?
    end
  end

  def submission_answers
    @_submission_answers ||= submission_params[:survey_answers]
  end

  class Result
    attr_accessor :submission, :errors

    def initialize
      @submission = nil
      @errors = []
    end

    def success?
      errors.blank? && submission.present? && submission.persisted?
    end
  end

end