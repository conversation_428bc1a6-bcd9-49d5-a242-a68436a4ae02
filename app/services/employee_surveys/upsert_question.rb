class EmployeeSurveys::UpsertQuestion

  def initialize(employee_survey:, question_params:, question: nil)
    @employee_survey = employee_survey
    @question_params = question_params
    @question = question.presence || new_survey_question

    @result = Result.new
  end

  def call
    return result if !can_upsert?

    result.survey_question = question

    if question.update(sanitized_params)
      result.survey_question = question
    else
      result.errors += question.errors.full_messages
    end

    result
  end

private

  attr_reader :employee_survey, :question_params, :question, :result

  def can_upsert?
    case
    when employee_survey.blank?
      result.errors << 'Missing Survey to attach question'
    when mismatched_survey
      result.errors << 'This question does not belong to this survey'
    end

    result.errors.blank?
  end

  def mismatched_survey
    return false if question_params.blank? || question_params[:employee_survey_id].blank?

    question.employee_survey_id != question_params[:employee_survey_id]
  end

  def sanitized_params
    [
      question_params,
      default_params,
      position_params
    ].inject(&:merge)
  end

  def default_params
    return {} if !question.new_record?

    {
      active: true
    }
  end

  def position_params
    return {} if !question.new_record?

    max_position = employee_survey.survey_questions.where(active: true).map(&:position).max
    {
      position: (max_position || 0) + 1
    }
  end

  def new_survey_question
    return nil if employee_survey.blank?

    employee_survey.survey_questions.new
  end

  class Result
    attr_accessor :survey_question, :errors

    def initialize
      @survey_question = nil
      @errors = []
    end

    def success?
      @errors.blank? && survey_question.present? && survey_question.persisted?
    end
  end

end
