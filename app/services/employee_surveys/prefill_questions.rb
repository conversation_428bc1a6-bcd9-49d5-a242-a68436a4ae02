class EmployeeSurveys::PrefillQuestions

  def initialize(employee_survey:)
    @employee_survey = employee_survey

    @result = Result.new
  end

  def call
    return result if !can_prefill?

    prefillable_questions.each do |prefillable_question|
      add_questions_for(prefillable_question)
    end

    result
  end

private

  attr_reader :employee_survey, :result

  def can_prefill?
    case
    when employee_survey.blank?
      result.errors << 'Missing Employee Survey'
    when employee_survey.survey_questions.where(active: true).present?
      result.errors << 'Survey Already has questions'
    when prefillable_questions.blank?
      result.errors << "Could not find questions for #{employee_survey.category_group}"
    end
    result.errors.blank?
  end

  def add_questions_for(prefillable_question)
    question_upserter = EmployeeSurveys::UpsertQuestion.new(employee_survey: employee_survey, question_params: prefillable_question).call
    if question_upserter.success?
      result.questions << question_upserter.survey_question
    else
      result.errors += question_upserter.errors
    end
  end

  def prefillable_questions
    @_prefillable_questions ||= begin
      path = Rails.root.join('lib/files/employee_survey_questions.yml')
      questionaire = YAML.load_file(path)
      questionaire[employee_survey.category_group]&.map(&:deep_symbolize_keys)
    end
  end

  class Result
    attr_accessor :questions, :errors

    def initialize
      @questions = []
      @errors = []
    end

    def success?
      errors.blank? && questions.present?
    end
  end

end