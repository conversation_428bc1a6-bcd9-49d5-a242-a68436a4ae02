class EmployeeSurveys::Upsert

  CATEGORY_NAME_MAP = {
    'catering-services' => 'Catering',
    'kitchen-supplies' => 'Pantry'
  }.freeze

  def initialize(customer:, survey_params:, employee_survey: nil, prefill: false)
    @customer = customer
    @survey_params = survey_params.present? ? survey_params.to_h.symbolize_keys : {}

    @employee_survey = employee_survey.presence || new_employee_survey
    @prefill = prefill

    @result = Result.new
  end

  def call
    return result if !can_upsert?

    result.employee_survey = employee_survey

    if employee_survey.update(sanitized_params)
      prefill_questions if prefill
      result.employee_survey = employee_survey
    else
      result.errors += employee_survey.errors.full_messages
    end

    result
  end

private

  attr_reader :customer, :survey_params, :employee_survey, :prefill, :result

  def can_upsert?
    case
    when customer.blank?
      result.errors << 'Customer is missing'
    when employee_survey.blank? || employee_survey.customer_profile != customer
      result.errors << 'Survey does not belong to the customer'
    end

    result.errors.blank?
  end

  def sanitized_params
    [
      default_params,
      survey_params.except(:category_group)
    ].inject(&:merge)
  end

  def default_params
    return {} if !employee_survey.new_record?

    default_name = survey_params[:category_group].present? ? "#{CATEGORY_NAME_MAP[survey_params[:category_group]]} Survey" : nil
    {
      name: default_name,
      uuid: SecureRandom.uuid,
      active: true
    }
  end

  def new_employee_survey
    return nil if customer.blank? || survey_params.blank? || survey_params[:category_group].blank?

    customer.employee_surveys.where(category_group: survey_params[:category_group]).first_or_initialize
  end

  def prefill_questions
    EmployeeSurveys::PrefillQuestions.new(employee_survey: employee_survey).call
  end

  class Result
    attr_accessor :employee_survey, :errors

    def initialize
      @employee_survey = nil
      @errors = []
    end

    def success?
      errors.blank? && employee_survey.present? && employee_survey.persisted?
    end
  end

end