class EmployeeSurveys::SubmitAnswer

  def initialize(employee_survey_submission:, answer_params:)
    @employee_survey_submission = employee_survey_submission
    @answer_params = answer_params

    @result = Result.new
  end

  def call
    return result if !can_submit?

    survey_answer = employee_survey_submission.survey_answers.new

    if survey_answer.update(sanitized_params)
      result.survey_answer = survey_answer
    else
      result.errors += survey_answer.errors.full_messages
    end

    result
  end

private

  attr_reader :employee_survey_submission, :answer_params, :result

  def can_submit?
    case
    when employee_survey_submission.blank?
      result.errors << 'Cannot submit an answer without a submission'
    when answer_params.blank? || answer_params[:value].blank?
      result.errors << 'Answer is missing'
    when question.blank?
      result.errors << 'Could not find the question'
    end

    result.errors.blank?
  end

  def sanitized_params
    [
      default_params,
      answer_params
    ].inject(&:merge)
  end

  def default_params
    {
      question_label: question.label,
    }
  end

  def question
    return nil if employee_survey_submission.blank?

    employee_survey = employee_survey_submission.employee_survey
    return nil if employee_survey.blank?

    @question ||= employee_survey.survey_questions.where(id: answer_params[:survey_question_id]).first
  end

  class Result
    attr_accessor :survey_answer, :errors

    def initialize
      @survey_answer = nil
      @errors = []
    end

    def success?
      errors.blank? && survey_answer.present? && survey_answer.persisted?
    end
  end

end