class EmployeeSurveys::ListSubmissions

  def initialize(employee_survey:, options: {}, includes: []) 
    @employee_survey = employee_survey
    @options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
    @includes = includes
  end

  def call
    return [] if employee_survey.blank?

    @submissions = base
    filter_by_date if options[:starts_on].present? && options[:ends_on].present?

    submissions.includes(includes).distinct
  end

private
  
  attr_reader :employee_survey, :options, :includes, :submissions

  def filter_by_date
    starts_on = options[:starts_on].is_a?(String) ? Time.zone.parse(options[:starts_on]).beginning_of_month : options[:starts_on]
    ends_on = options[:ends_on].is_a?(String) ? Time.zone.parse(options[:ends_on]).end_of_month : options[:ends_on]
    @submissions = submissions.where(created_at: [starts_on..ends_on])
  end

  def base
    @submissions = employee_survey.submissions
  end

  def default_options
    {
      starts_on: (Time.zone.now - 6.months).beginning_of_month,
      ends_on: Time.zone.now.end_of_month
    }
  end
end