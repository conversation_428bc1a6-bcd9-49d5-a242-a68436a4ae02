class SupplierClosures::Upsert

  def initialize(supplier:, closure_params: {}, supplier_closure: nil)
    @supplier = supplier
    @closure_params = closure_params
    @supplier_closure = supplier_closure.presence || supplier&.closure_dates&.new
    @result = Result.new
  end

  def call
    if can_upsert?
      result.supplier_closure = supplier_closure
      if !supplier_closure.update(sanitized_params)
        result.errors = supplier_closure.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :supplier, :closure_params, :supplier_closure, :result

  def can_upsert?
    case
    when supplier.blank?
      result.errors << 'Cannot create/update closure date without a supplier'
    when supplier_closure.blank? || supplier_closure.supplier_profile != supplier
      result.errors << 'You do not have access to this closure date'
    end
    result.errors.blank?
  end

  def sanitized_params
    [closure_params.except(:starts_at, :ends_at), date_params].inject(&:merge)
  end

  def date_params
    dates = {}
    %i[starts_at ends_at].each do |field|
      date = closure_params[field]
      if date.present?
        datetime = date.is_a?(String) ? Time.zone.parse(date) : date
        dates[field] = field == :starts_at ? datetime.beginning_of_day : datetime.end_of_day
      end
    end
    dates
  end

  class Result
    attr_accessor :supplier_closure, :errors

    def initialize
      @supplier_closure = nil
      @errors = []
    end

    def success?
      errors.blank? && supplier_closure.present? && supplier_closure.persisted?
    end
  end

end
