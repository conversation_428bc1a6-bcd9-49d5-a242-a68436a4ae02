class Reports::RetrieveInvoiceData

  def initialize(options:)
    @filter_options = [default_options, options.symbolize_keys].inject(&:merge)
    @result = Result.new(options: filter_options)
  end

  def call
    grouped_invoices.each do |group, invoices|
      report_result = ReportResult.new(key_date: get_key_date_for(invoices), group: group)
      report_result.report_data += invoices

      result.data << report_result
    end
    result
  end

private

  attr_reader :filter_options, :result

  def customer
    @_customer ||= filter_options[:customer_id].present? && CustomerProfile.where(id: filter_options[:customer_id]).first
  end

  def customers
    company_customers = filter_options[:company_wide] ? customer.company&.customer_profiles : []
    company_customers.presence || [customer]
  end

  def report_invoices
    start_date = Time.zone.parse(filter_options[:start_date].to_s).beginning_of_day
    end_date = Time.zone.parse(filter_options[:end_date].to_s).end_of_day

    invoices = Invoice.all
    invoices = invoices.joins(orders: :customer_profile).where(orders: { customer_profile: customers })
    if filter_options[:purchase_order_id].present?
      cpo_id = filter_options[:purchase_order_id] == 'no-po' ? nil : filter_options[:purchase_order_id]
      invoices = invoices.where(orders: { cpo_id: cpo_id })
    end
    invoices = invoices.where(created_at: [start_date..end_date])
    invoices.order(:created_at).distinct
  end

  def grouped_invoices
    report_invoices.group_by do |invoice|
      key_date = invoice.created_at.to_date
      case filter_options[:report_type]
      when 'monthly'
        [key_date.year, key_date.month]
      else # 'weekly'
        [key_date.year, key_date.cweek]
      end
    end
  end

  def get_key_date_for(invoices)
    key_date = invoices.map(&:created_at).min
    key_date_label = case filter_options[:report_type]
    when 'monthly'
      key_date.to_s(:month_year)
    else # 'weekly'
      key_date.to_s(:date_spreadsheet)
    end
    {
      date: key_date,
      label: key_date_label
    }
  end

  def default_options
    today = Time.zone.today
    {
      customer_id: nil,
      report_type: 'monthly',
      start_date: today.beginning_of_month,
      end_date: today.end_of_month,
      purchase_order_id: nil,
      company_wide: false,
    }
  end

  class ReportResult
    attr_accessor :key_date, :group, :report_data

    def initialize(key_date:, group:)
      @key_date = key_date
      @group = group
      @report_data = []
    end
  end

  class Result
    attr_accessor :options, :data

    def initialize(options:)
      @options = OpenStruct.new(options)
      @data = []
    end
  end
end
