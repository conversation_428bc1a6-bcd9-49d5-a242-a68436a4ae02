class Reports::CaptureCategorySpends

  def initialize(report_data:, categorised_orders:, verbose: false)
    @report_data = report_data
    @categorised_orders = categorised_orders
    @verbose = verbose
    @result = Result.new
  end

  def call
    capture_order_data
    save_spend_data
    result
  end

private

  attr_reader :report_data, :categorised_orders, :verbose, :result

  def capture_order_data
    categorised_orders.each do |categorized_order|
      order = categorized_order.order
      country_code = order.symbolized_country_code || :au
      capture_order_line_data_for(order_lines: categorized_order.categorised_order_lines, country_code: country_code)
      customer_delivery = order.customer_delivery
      next if customer_delivery.blank? || customer_delivery <= 0.0

      print 'od-' if verbose
      delivery_with_gst = order.customer_delivery * (1.0 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)
      result.category_spends << CategorySpend.new(category: 'Delivery', spend: delivery_with_gst)
    end
  end

  def save_spend_data
    result.category_spends.group_by(&:category).each do |category, grouped_category_spends|
      order_datum = report_data.order_data.where(data_kind: 'Category', kind: category).first_or_create
      print 'cod-' if verbose
      if order_datum.update(total_spend: grouped_category_spends.sum(&:spend))
        result.order_data << order_datum
      end
    end
  end

  def capture_order_line_data_for(order_lines:, country_code:)
    order_lines.group_by(&:category).each do |category, categorised_order_lines|
      category_spend = categorised_order_lines.map do |categorised_order_line|
        order_line = categorised_order_line.order_line
        order_line.price_inc_gst(gst_country: country_code) * order_line.quantity
      end.sum
      print 'olc-' if verbose
      result.category_spends << CategorySpend.new(category: category.name, spend: category_spend)
    end
  end

  class CategorySpend
    attr_reader :category, :spend

    def initialize(category:, spend:)
      @category = category
      @spend = spend
    end

  end

  class Result
    attr_accessor :category_spends, :order_data

    def initialize
      @category_spends = []
      @order_data = []
    end
  end

end