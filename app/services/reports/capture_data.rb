class Reports::CaptureData

  def initialize(starts:, ends:, data_type: 'both', verbose: false)
    @starts = starts
    @ends = ends
    @data_type = data_type
    @verbose = verbose
    @result = Result.new
  end

  def call
    capture_start = starts

    while capture_start < ends
      if verbose
        puts ''
        puts capture_start
      end
      capture_end = capture_start.end_of_week
      capture_end = capture_start.end_of_month if capture_end.month != capture_start.month

      if %w[both customer].include?(data_type)
        customer_report_capture = Reports::CaptureCustomerRangedData.new(capture_start: capture_start, capture_end: capture_end, verbose: verbose).call
        result.weekly_stats += customer_report_capture.report_data
      end

      if %w[both supplier].include?(data_type)
        supplier_report_capture = Reports::CaptureSupplierRangedData.new(capture_start: capture_start, capture_end: capture_end, verbose: verbose).call
        result.weekly_stats += supplier_report_capture.report_data
      end

      next_date = capture_start + 1.week
      if next_date.month != capture_start.month
        next_date = next_date.beginning_of_month
      else
        next_date = next_date.beginning_of_week
      end
      capture_start = next_date
    end
    result
  end

private

  attr_accessor :starts, :ends, :data_type, :verbose, :result

  class Result
    attr_accessor :weekly_stats

    def initialize
      @weekly_stats = []
    end
  end

end

