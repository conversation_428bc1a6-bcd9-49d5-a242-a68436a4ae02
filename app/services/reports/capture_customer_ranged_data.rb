class Reports::CaptureCustomerRangedData

  def initialize(capture_start:, capture_end:, capture_date: Time.zone.today, verbose: false)
    @capture_start = capture_start
    @capture_end = capture_end
    @capture_date = capture_date
    @verbose = verbose    
    @result = Result.new
  end

  def call
    customer_grouped_report_orders.each do |customer, customer_orders|
      report_customer = capture_report_customer(customer: customer)
      customer_orders.group_by(&:customer_purchase_order).each do |purchase_order, po_orders|
        capture_category_spend_for(report_customer: report_customer, purchase_order: purchase_order, orders: po_orders)
      end
    end
    remove_old_report_customers
    result
  end

private

  attr_reader :capture_start, :capture_end, :capture_date, :verbose, :result

  def order_statuses
    yesterday = (capture_date - 1.day).to_date
    if capture_start > yesterday || capture_end > yesterday
      %w[delivered new confirmed amended]
    else
      ['delivered']
    end
  end

  def customer_grouped_report_orders
    orders = Order.where(status: order_statuses)
    orders = orders.where(delivery_at: [capture_start..capture_end])
    orders = orders.order(:delivery_at)
    orders = orders.includes(:customer_purchase_order, order_lines: { menu_item: { menu_section: :categories } })
    orders.group_by(&:customer_profile)
  end

  def capture_report_customer(customer:)
    print 'rc-' if verbose
    report_customer = Report::Source.where(
      source: customer,
      key_date: capture_start.to_date
    ).first_or_create
    report_customer.report_data.each(&:destroy) # refresh data for that time
    result.report_customers << report_customer
    report_customer
  end

  def capture_category_spend_for(report_customer:, purchase_order:, orders:)
    category_spends = Reports::CaptureCategoryGroupSpends.new(report_customer: report_customer, purchase_order: purchase_order, orders: orders, verbose: verbose).call
    print 'rdp-' if verbose
    result.report_data += category_spends.report_data
  end

  def remove_old_report_customers
    report_customers = Report::Source.where(source_type: 'CustomerProfile', key_date: capture_start.to_date)
    report_customers = report_customers.where.not(id: result.report_customers.map(&:id))
    report_customers.each(&:destroy)
  end

  class Result
    attr_accessor :report_customers, :report_data

    def initialize
      @report_customers = []
      @report_data = []
    end
  end

end
