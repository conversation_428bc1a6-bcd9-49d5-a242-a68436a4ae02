class Reports::Retrieve<PERSON>ust<PERSON><PERSON>udget

  def initialize(customer:, since:, options:)
    @customer = customer
    @since = since
    @filter_options = options
  end

  def call
    customer_budgets.present? ? customer_budgets.first.value : nil
  end

private

  attr_reader :customer, :since, :filter_options

  def customer_budgets
    budgets = customer.budgets.where(frequency: filter_options[:report_type])
    if filter_options[:purchase_order_id].present?
      budgets = budgets.where(customer_purchase_order_id: filter_options[:purchase_order_id])
    else
      budgets = budgets.where(customer_purchase_order_id: nil)
    end
    if ranged_budgets_within(budgets).present?
      budgets = ranged_budgets_within(budgets)
    else
      budgets = budgets.where('starts_on <= ?', since).where(ends_on: nil) # revert back to open budgets
    end
    budgets = budgets.order(starts_on: :desc)
    budgets
  end

  def ranged_budgets_within(budgets)
    @_ranged_budgets ||= budgets.where.not(ends_on: nil).where('ends_on >= :retrieval_start AND starts_on <= :retrieval_end', retrieval_start: since.beginning_of_month, retrieval_end: since.end_of_month).presence
  end

  def default_options
    {
      report_type: 'monthly',
      purchase_order_id: nil,
    }
  end

end
