class Reports::RetrieveData

  def initialize(options:)
    @filter_options = [default_options, options.symbolize_keys].inject(&:merge)
    @result = Result.new(options: filter_options)
  end

  def call
    grouped_report_sources.each do |group_date, report_sources|
      key_date = get_key_date_for(report_sources)
      report_result = ReportResult.new(key_date: key_date, group: group_date)

      if reportable_source.is_a?(CustomerProfile)
        get_budget_for(report_result: report_result, date: key_date[:date])
      end

      report_sources.map do |report_source|
        source_report_data = get_report_data_for(report_source)

        report_result.total_spend += source_report_data.map(&:total_spend).sum

        next if filter_options[:with_order_data].blank?

        report_result.report_sources << report_source
        report_result.report_data += source_report_data
        report_result.order_ids += source_report_data.map(&:order_ids).flatten
      end
      result.data << report_result
    end
    result
  end

private

  attr_reader :filter_options, :result

  def reportable_source
    supplier || customer
  end

  def supplier
    @_supplier ||= filter_options[:supplier_id].present? ? SupplierProfile.where(id: filter_options[:supplier_id]).first : nil
  end

  def customer
    @_customer ||= filter_options[:customer_id].present? ? CustomerProfile.where(id: filter_options[:customer_id]).first : nil
  end

  def grouped_report_sources
    report_data.group_by do |report_source|
      key_date = report_source.key_date.to_date
      case filter_options[:report_type]
      when 'monthly'
        [key_date.year, key_date.month]
      else # 'weekly'
        [key_date.year, key_date.cweek]
      end
    end
  end

  def report_data
    start_date = Date.parse(filter_options[:start_date].to_s)
    end_date = Date.parse(filter_options[:end_date].to_s)

    start_date = start_date.beginning_of_week if start_date != start_date.beginning_of_month
    end_date = end_date.end_of_week if end_date != end_date.end_of_month

    report_sources = Report::Source.all
    if filter_options[:source_types].present?
      report_sources = report_sources.where(source_type: filter_options[:source_types])
      if supplier.present?
        report_arel = Report::Source.arel_table
        supplier_condition = report_arel[:source_type].eq('SupplierProfile').and(report_arel[:source_id].eq(supplier.id))
        customer_condition = report_arel[:source_type].eq('CustomerProfile')
        report_sources = report_sources.where(supplier_condition.or(customer_condition))
      elsif filter_options[:exclude_staffing].present?
        report_sources = report_sources.where.not(source_id: yordar_credentials(:yordar, :staffing_supplier_id))
      end
    elsif reportable_sources.present?
      report_sources = report_sources.where(source: reportable_sources)
    end
    report_sources = report_sources.where(source_type: filter_options[:source_type]) if filter_options[:source_type].present? && reportable_sources.blank?
    report_sources = report_sources.where(key_date: [start_date..end_date])
    report_sources.order(:key_date)
  end

  def reportable_sources
    @_reportable_sources ||= begin
      case
      when reportable_source.blank? && filter_options[:admin_user].present?
        lister_options = {
          for_user: filter_options[:admin_user],
          page: 1,
          limit: nil
        }
        ::Admin::ListCustomers.new(options: lister_options).call
      when reportable_source.blank?
        nil
      when filter_options[:company_wide] && reportable_source.respond_to?(:company) && reportable_source.company.present?
        reportable_source.company.customer_profiles
      else
        [reportable_source]
      end
    end
  end

  def get_key_date_for(report_sources)
    key_date = report_sources.map(&:key_date).min
    key_date_label = case filter_options[:report_type]
    when 'monthly'
      key_date.to_s(:month_year)
    else # 'weekly'
      key_date.to_s(:date_spreadsheet)
    end
    {
      date: key_date,
      label: key_date_label
    }
  end

  def get_budget_for(report_result:, date:)
    report_result.budget = Reports::RetrieveCustomerBudget.new(customer: reportable_source, since: date, options: filter_options.slice(:purchase_order_id, :report_type)).call
    report_result.budget ||= 0
  end

  def get_report_data_for(report_source)
    report_data = report_source.report_data
    if filter_options[:purchase_order_id].present?
      cpo_id = filter_options[:purchase_order_id] == 'no-po' ? nil : filter_options[:purchase_order_id]
      report_data = report_data.where(customer_purchase_order_id: cpo_id)
    end
    report_data = report_data.where(category: filter_options[:category_group]) if filter_options[:category_group].present?
    report_data
  end

  def default_options
    today = Time.zone.today
    {
      customer_id: nil,
      supplier_id: nil,
      source_type: nil,
      source_types: [],
      report_type: 'monthly',
      start_date: (today - 5.months).beginning_of_month,
      end_date: today.end_of_month,
      purchase_order_id: nil,
      category_group: nil,
      company_wide: false,
      with_supplier_costs: false,
      with_order_data: false,
      admin_user: nil,
      react_data: false,
      exclude_staffing: false,
    }
  end

  class ReportResult
    attr_accessor :key_date, :group, :budget, :total_spend, :report_sources, :report_data, :order_ids

    def initialize(key_date:, group:)
      @key_date = key_date
      @group = group
      @budget = nil
      @total_spend = 0
      @report_sources = []
      @report_data = []
      @order_ids = []
    end
  end

  class Result
    attr_accessor :options, :data

    def initialize(options:)
      @options = OpenStruct.new(options)
      @data = []
    end
  end

end
