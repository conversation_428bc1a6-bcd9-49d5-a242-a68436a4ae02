class Reports::CaptureCategoryGroupSpends

  def initialize(report_customer:, orders:, purchase_order:, verbose: false)
    @report_customer = report_customer
    @orders = orders
    @purchase_order = purchase_order
    @verbose = verbose
    @result = Result.new
  end

  def call
    capture_order_data
    capture_spend_data
    result
  end

private

  attr_reader :report_customer, :orders, :purchase_order, :verbose, :result

  def capture_order_data
    order_grouped_order_lines.each do |order, order_lines|
      print 'omc-' if verbose
      result.category_orders << CategorisedOrder.new(order: order, order_lines: order_lines)
    end
  end

  def capture_spend_data
    result.category_orders.group_by(&:major_category_group).each do |major_category_group, group_categorised_orders|
      result.report_data << capture_category_group_data_for(major_category_group: major_category_group, group_categorised_orders: group_categorised_orders)
    end
  end

  def capture_category_group_data_for(major_category_group:, group_categorised_orders:)
    report_data = report_customer.report_data.where(customer_purchase_order: purchase_order, category: major_category_group).first_or_create
    orders = group_categorised_orders.map(&:order)
    report_data.update(
      total_spend: orders.map(&:customer_total).reject(&:blank?).sum,
      order_ids: orders.map(&:id)
    )
    print 'rdg-' if verbose
    report_data.order_data.destroy_all
    capture_category_spend_for(report_data: report_data, categorised_orders: group_categorised_orders)
    capture_supplier_spend_for(report_data: report_data, categorised_orders: group_categorised_orders)
    report_data
  end

  def capture_category_spend_for(report_data:, categorised_orders:)
    Reports::CaptureCategorySpends.new(report_data: report_data, categorised_orders: categorised_orders, verbose: verbose).call
  end

  def capture_supplier_spend_for(report_data:, categorised_orders:)
    Reports::CaptureSupplierSpends.new(report_data: report_data, categorised_orders: categorised_orders, verbose: verbose).call
  end

  def order_grouped_order_lines
    lister_options = {
      orders: orders,
      confirmed_attendees_only: true,
    }
    result.order_lines = order_lines = OrderLines::List.new(options: lister_options, includes: [:order, :category, { menu_section: :categories }]).call
    order_lines.group_by(&:order)
  end

  class CategorisedOrder
    attr_reader :order, :order_lines

    def initialize(order:, order_lines:)
      @order = order
      @order_lines = order_lines
    end

    def categorised_order_lines
      @_categorised_order_lines ||= begin
        order_lines.map do |order_line|
          CategorisedOrderLine.new(order: order, order_line: order_line)
        end
      end
    end

    def major_category_group
      @_major_category_group ||= begin
        categorised_order_lines.group_by(&:category).max_by do |_, category_order_lines|
          category_order_lines.size * category_order_lines.sum{|categorised_order_line| categorised_order_line.order_line.quantity }
        end.first&.group
      end
    end
  end

  class CategorisedOrderLine
    attr_reader :order, :order_line

    def initialize(order:, order_line:)
      @order = order
      @order_line = order_line
    end

    def category
      @category ||= begin
        case
        when (order_line_category = order_line.category.presence)
          order_line_category
        when (menu_section_categories = order_line.menu_item&.menu_section&.categories&.order(weight: :asc).presence)
          menu_section_categories.first
        when order.is_event_order? && (order_major_category = order.major_category.presence)
          order_major_category
        when (supplier = order_line.supplier_profile.presence)
          supplier_categories = []
          supplier_categories << Category.generic_category_for(group_name: 'catering-services') if supplier.has_catering_services
          supplier_categories << Category.generic_category_for(group_name: 'kitchen-supplies') if supplier.has_kitchen_supplies
          supplier_categories.compact.first
        else
          Category.new(name: 'Others', slug: 'others', group: 'others')
        end
      end
    end

    def category_group
      @_category_group ||= category&.group
    end
  end

  class Result
    attr_accessor :order_lines, :category_orders, :report_data

    def initialize
      @order_lines = []
      @category_orders = []
      @report_data = []
    end
  end

end