class Reports::CaptureSupplierRangedData

  def initialize(capture_start:, capture_end:, capture_date: Time.zone.today, verbose: false)
    @capture_start = capture_start
    @capture_end = capture_end
    @capture_date = capture_date
    @verbose = verbose
    @result = Result.new
  end

  def call
    supplier_grouped_orders.each do |supplier, supplier_orders|
      report_supplier = capture_report_supplier(supplier: supplier)
      capture_report_data(report_supplier: report_supplier, supplier_orders: supplier_orders)
    end
    remove_old_report_suppliers
    result
  end

private

  attr_reader :capture_start, :capture_end, :capture_date, :verbose, :result

  def order_statuses
    yesterday = (capture_date - 1.day).to_date
    if capture_start > yesterday || capture_end > yesterday
      %w[delivered new confirmed]
    else
      ['delivered']
    end
  end

  def supplier_grouped_orders
    grouped_orders = {}
    report_orders.group_by(&:supplier_profiles).each do |suppliers, supplier_orders|
      suppliers.each do |supplier|
        grouped_orders[supplier] ||= []
        grouped_orders[supplier] += supplier_orders
        print 's-' if verbose
      end
    end
    grouped_orders
  end

  def report_orders
    orders = OrderWithOrderLines.where(status: order_statuses)
    orders = orders.where(delivery_at: [capture_start..capture_end])
    orders = orders.order(:delivery_at)
    orders.includes(:customer_purchase_order, order_lines: { menu_item: { menu_section: :categories } })
  end

  def capture_report_supplier(supplier:)
    print 'rs-' if verbose
    report_supplier = Report::Source.where(
      source: supplier,
      key_date: capture_start.to_date
    ).first_or_create
    report_supplier.report_data.each(&:destroy) # refresh data for that time
    result.report_suppliers << report_supplier
    report_supplier
  end

  def capture_report_data(report_supplier:, supplier_orders:)
    category_grouped_orders_for(supplier: report_supplier.source, orders: supplier_orders).each do |category, category_orders|
      print 'rdg-' if verbose
      capture_report_data_per_category(report_supplier: report_supplier, category: category, orders: category_orders)
    end
  end

  def capture_report_data_per_category(report_supplier:, category:, orders:)
    report_data = report_supplier.report_data.where(category: category).first_or_create
    total_spend = orders.map do |order|
      order_supplier = order.order_suppliers.where(supplier_profile: report_supplier.source).where.not(total: nil).first
      order_supplier.present? ? order_supplier.total : 0
    end.sum
    report_data.update(
      total_spend: total_spend,
      order_ids: orders.map(&:id)
    )
    result.report_data << report_data
  end

  def category_grouped_orders_for(supplier:, orders:)
    orders.group_by do |order|
      major_category = Orders::RetrieveMajorOrderCategory.new(order: order, supplier: supplier).call
      print 'ca-' if verbose
      major_category.try(&:group)
    end
  end

  def remove_old_report_suppliers
    report_suppliers = Report::Source.where(source_type: 'SupplierProfile', key_date: capture_start.to_date)
    report_suppliers = report_suppliers.where.not(id: result.report_suppliers.map(&:id))
    report_suppliers.each(&:destroy)
  end

  # class used to add supplier_order_lines
  class OrderWithOrderLines < ::Order
    attr_accessor :supplier_order_lines
  end

  class Result
    attr_accessor :report_suppliers, :report_data

    def initialize
      @report_suppliers = []
      @report_data = []
    end
  end

end
