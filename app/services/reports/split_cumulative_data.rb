class Reports::SplitCumulativeData

  MIN_THRESHOLD = 5 # at least number of Items

  def initialize(data:, percent: 75)
    @data = data
    @percent = percent
  end

  def call
    [valueable_data, others_data].reject(&:blank?).map(&:to_h).inject(&:merge)
  end

private

  attr_reader :data, :percent

  def sorted_data
    @_sorted_data ||= data.sort_by{|category, v| category == 'Others' ? -1 : -v }
  end

  def valueable_data
    if show_all_data
      sorted_data
    else
      sorted_data.to_a[0..threshold_index]
    end
  end

  def others_data
    others = {}
    return others if show_all_data || threshold_index.blank?

    other_key = 'Others'
    others[other_key] = sorted_data.to_a[(threshold_index + 1)..].map(&:last).sum
    others
  end

  def show_all_data
    (threshold_index == (sorted_data.size - 1)) || (threshold_index == (sorted_data.size - 2))
  end

  def percent_sum
    @_percent_sum ||= data.values.sum * percent / 100
  end

  def threshold_index
    return @_threshold_index if @_threshold_index.present?

    cumulative = 0
    threshold = nil
    sorted_data.each_with_index do |(_, value), idx|
      next if threshold.present?

      cumulative += value
      if cumulative >= percent_sum && idx >= (MIN_THRESHOLD - 1)
        threshold = idx
      end
    end
    @_threshold_index = threshold
  end
end
