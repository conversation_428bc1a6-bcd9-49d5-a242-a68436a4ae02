class OrderLines::GetSupplierItemChanges

  ORDER_LINE_CHANGE_FIELDS = %w[quantity note].freeze

  def initialize(order:, supplier:, since: nil)
    @order = order
    @supplier = supplier
    @since = since.presence || order&.suppliers_notified_at.presence || Time.zone.now
  end

  def call
    (order_line_changes + deleted_order_line_changes)
  end

private

  attr_reader :order, :supplier, :since

  def order_line_changes
    return [] if recently_updated_order_lines.blank?

    changed_order_lines = []
    recently_updated_order_lines.each do |order_line|
      previous_version = previous_version_for(order_line)
      next if previous_version.blank?

      changes = get_changes_between(order_line, previous_version.reify)

      next if changes.blank? && previous_version != 'create'

      changed_order_lines << OpenStruct.new(
        id: order_line.id,
        name: order_line.name,
        location_id: order_line.location_id,
        change_type: previous_version.event == 'create' ? 'created' : 'updated',
        changes: changes
      )
    end
    changed_order_lines
  end

  def previous_version_for(order_line)
    order_line.versions.order(created_at: :desc).where('created_at > ?', since).where(event: %w[update create]).first
  end

  def get_changes_between(order_line, previous_order_line)
    changes = []
    ORDER_LINE_CHANGE_FIELDS.each do |field|
      old_value = previous_order_line.send(field) rescue nil
      new_value = order_line.send(field).presence || nil
      next if (old_value.blank? && new_value.blank?) || old_value == new_value

      changes << OpenStruct.new(
        field: field,
        old_value: old_value,
        new_value: new_value
      )
    end
    changes
  end

  def recently_updated_order_lines
    return @_recently_updated_order_lines if !@_recently_updated_order_lines.nil?

    lister_options = {
      order: order,
      supplier: supplier,
      confirmed_attendees_only: order.is_team_order?,
    }
    order_lines = OrderLines::List.new(options: lister_options).call
    @_recently_updated_order_lines = order_lines.where('updated_at > ?', since)
  end

  def deleted_order_line_changes
    return [] if recently_deleted_order_lines.blank?

    deleted_order_lines = []
    recently_deleted_order_lines.each do |order_line|
      deleted_order_lines << OpenStruct.new(
        id: order_line.id,
        name: order_line.name,
        quantity: order_line.quantity,
        note: order_line.note,
        location_id: order_line.location_id,
        change_type: 'removed',
        changes: []
      )
    end
    deleted_order_lines
  end

  def recently_deleted_order_lines
    return @_recently_deleted_order_lines if !@_recently_deleted_order_lines.nil?

    order_line_versions = PaperTrail::Version.where(event: 'destroy', item_type: 'OrderLine')
    order_line_versions = order_line_versions.where('created_at > ?', since)
    order_line_versions = order_line_versions.where('object LIKE (?)', "%order_id: #{order.id}%")
    order_line_versions = order_line_versions.where('object LIKE (?)', "%supplier_profile_id: #{supplier.id}%")
    @_recently_deleted_order_lines = order_line_versions.map(&:reify)
  end

end
