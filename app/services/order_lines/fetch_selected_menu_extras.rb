class OrderLines::FetchSelectedMenuExtras

  NAME_EXCLUDES = [
    'Choose your',
    'Choose',
    'Add ',
    'Select',
    'Of your choice',
    'Your',
    'Please',
    'from the below options',
    'from the below option',
    'Below option',
    'Options',
    'Amount of',
    'Indicate',
    'Choice'
  ].freeze

  def initialize(order_line:, menu_item: nil)
    @order_line = order_line
    @menu_item = menu_item.presence || order_line.menu_item
  end

  def call
    return {} if order_line.selected_menu_extras.blank?

    selected_menu_extras.group_by{|menu_extra| sanitized_section(menu_extra) }.sort_by{|section, _| section.weight }
  end

private

  attr_reader :order_line, :menu_item

  def selected_menu_extras
    @_selected_menu_extras ||= menu_item.menu_extras.where(id: order_line.selected_menu_extras)
  end

  def sanitized_section(menu_extra)
    case
    when extra_section = menu_extra.menu_extra_section.presence
      OpenStruct.new(
        name: (extra_section.name.present? ? extra_section.name.gsub(exclusion_regex, '').strip : 'extras'),
        weight: extra_section.weight || -1
      )
    else
      OpenStruct.new(
        name: 'extras',
        weight: -1
      )
    end
  end

  def exclusion_regex
    @_exclusion_regex ||= Regexp.new(NAME_EXCLUDES.map(&:downcase).join('|'), 'i')
  end

end
