class OrderLines::<PERSON>move

  def initialize(location:, order_line_params:, order: nil)
    @location = location
    @order = order.presence || location.try(:order)
    @order_line_params = order_line_params
    @result = Result.new
  end

  def call
    fetch_order_line
    if can_remove?
      order_line.quantity = 0
      result.order_line = order_line
      if order_line.destroy!
        sync_with_woolworths if order_line.supplier_profile.try(:woolworths?)
        sync_attendee_status if order_line.attendee_id.present?
        update_status if %w[pending draft quoted].exclude?(order.status)
      else
        result.errors += order_line.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :location, :order_line_params, :order
  attr_accessor :order_line, :result

  def fetch_order_line
    return if location.blank?

    @order_line = OrderLines::Fetch.new(order: order, location: location, order_line_params: order_line_params).call
  end

  def can_remove?
    case
    when location.blank?
      result.errors << 'Cannot remove an order line without a location'
    when order_line.blank? || !order_line.persisted?
      result.errors << 'Order line could not be found'
    when is_active_order? && order.order_lines.count <= 1
      result.errors << 'Cannot remove the last item within a submitted order'
    end
    result.errors.blank?
  end

  def sync_with_woolworths
    return unless order.status == 'draft' && order.order_variant != 'event_order'

    Woolworths::ProcessOrderLine.new(order_line: order_line).call
  end

  def sync_attendee_status
    team_order_attendee = order_line.team_order_attendee
    return if order_line.order.order_lines.where(team_order_attendee: team_order_attendee).present?

    team_order_attendee.update(status: 'invited')
  end

  def update_status
    updated_reference = order.reload.current_version_ref
    order.update(version_ref: updated_reference, status: 'amended')
  end

  def is_active_order?
    %w[pending draft].exclude?(order.status)
  end

  class Result
    attr_accessor :order_line, :errors

    def initialize
      @order_line = nil
      @errors = []
    end

    def success?
      @errors.blank?
    end
  end
end
