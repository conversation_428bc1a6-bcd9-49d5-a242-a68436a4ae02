class OrderLines::CalculateCost

  def initialize(order_line:, menu_item: nil, serving_size: nil, rate_card: nil, customer: nil, company: nil, menu_extras: [], baseline: false)
    @order_line = order_line
    @menu_item = menu_item || order_line.menu_item
    @serving_size = serving_size || order_line.serving_size
    @menu_extras = menu_extras.presence || MenuExtra.where(id: order_line.selected_menu_extras)
    @customer = customer
    @company = company.presence || (customer.present? && customer.company)
    @rate_card = rate_card.presence || fetch_rate_card
    @baseline = baseline
  end

  def call
    case
    when rate_card.present?
      rate_card.cost
    when order_line_price.present? && baseline
      order_line_price
    when order_line_price.present?
      commission_based_cost
    else
      nil
    end
  end

private

  attr_reader :order_line, :menu_item, :serving_size, :menu_extras, :customer, :company, :rate_card, :baseline

  def order_line_price
    @_order_line_price ||= (base_price + menu_extras_price)
  end

  def base_price
    case
    when serving_size.present?
      serving_size.price
    when menu_item.present? && menu_item.promo_price.present?
      menu_item.promo_price
    when menu_item.present?
      menu_item.price
    end
  end

  def menu_extras_price
    return 0 if menu_extras.blank?

    menu_extras.reduce(0) do |sum, menu_extra|
      next sum if menu_extra.price.blank?

      sum + menu_extra.price.to_f
    end
  end

  def commission_based_cost
    case
    when markup_override.present?
      order_line_price * (1 - (markup_override.commission_rate / 100))
    else
      order_line_price * (1 - (supplier.commission_rate / 100))
    end
  end

  def fetch_rate_card
    return nil if company.blank?

    case
    when serving_size.present?
      company.rate_cards.where(serving_size: serving_size).first
    when menu_item.present?
      company.rate_cards.where(menu_item: menu_item).first
    else
      nil
    end
  end

  def supplier
    @_supplier ||= order_line.supplier_profile || order_line.menu_item.supplier_profile
  end

  def markup_override
    @_markup_override ||= Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer, company: company, required_override: :commission_rate).call
  end

end
