class OrderLines::FutureUpdateExists

  def initialize(item:, is_archived: false)
    @item = item
    @is_archived = is_archived
  end

  def call
    existing_update.present?
  end

private
  
  attr_reader :item, :is_archived, :update_exists

  def existing_update
    item_updates.detect do |item_update|
      handler = YAML.load(item_update.handler)

      menu_item_check = handler.menu_item.present? && handler.menu_item == menu_item
      serving_size_check = serving_size.blank? || (handler.serving_size.present? && handler.serving_size == serving_size)
      rate_card_check = rate_card.blank? || (handler.rate_card.present? && handler.rate_card == rate_card)

      menu_item_check && serving_size_check && rate_card_check
    end
  end

  def menu_item
    case
    when item.is_a?(MenuItem)
      item
    when item.is_a?(ServingSize) || item.is_a?(RateCard)
      item.menu_item
    end
  end

  def serving_size
    return nil if item.is_a?(MenuItem)
    case
    when item.is_a?(ServingSize)
      item
    when item.is_a?(RateCard)
      item.serving_size
    end
  end

  def rate_card
    return nil if !item.is_a?(RateCard) || is_archived

    item
  end

  def item_updates
    handler_check = '%ruby/object:MenuItems::UpdateFutureOrderLines%'
    handler_check += 'ruby/object:MenuItem%'
    handler_check += 'ruby/object:ServingSize%' if serving_size.present?
    handler_check += 'ruby/object:RateCard%' if rate_card.present?

    Delayed::Job.where(locked_at: nil, queue: :data_integrity).where('handler ilike ?', handler_check)
  end
end