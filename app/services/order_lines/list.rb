class OrderLines::List

  def initialize(options: {}, includes: [])
    @includes = includes
    @options = [default_options, options].inject(&:merge)
    @order_lines = []
  end

  def call
    @order_lines = base
    filter_by_order if orders.present?
    filter_by_supplier if suppliers.present?
    filter_by_item_gst if options[:gst_split].present?
    filter_by_attendee if options[:for_attendee] != false
    filter_by_confirmed_attendees if options[:confirmed_attendees_only].present?
    filter_by_ordered_attendees if options[:ordered_attendees_only].present?
    filter_by_paid_order_lines if options[:paid_only].present?
    order_lines
  end

private

  attr_reader :order_lines, :options, :includes

  def base
    OrderLine.all.includes(includes)
  end

  def filter_by_order
    @order_lines = order_lines.where(order: orders)
  end

  def orders
    return nil if options[:order].blank? && options[:orders].blank?

    ([options[:order]] + options[:orders]).compact
  end

  def filter_by_supplier
    @order_lines = order_lines.where(supplier_profile: suppliers)
  end

  def filter_by_item_gst
    is_gst_free = options[:gst_split] == 'GST-FREE'
    @order_lines = order_lines.where(is_gst_free: is_gst_free)
  end

  def suppliers
    return nil if options[:supplier].blank? && options[:suppliers].blank?

    ([options[:supplier]] + options[:suppliers]).compact
  end

  def filter_by_attendee
    attendee_id = options[:for_attendee].nil? ? nil : options[:for_attendee].id
    @order_lines = order_lines.where(attendee_id: attendee_id)
  end

  def filter_by_confirmed_attendees
    return if options[:for_attendee] != false && orders.blank?

    confirmed_attendee_ids = orders.map{|order| order.is_team_order? && order.team_order_attendees.where(status: 'ordered') }.reject(&:blank?).flatten.uniq.map(&:id)
    admin_attendee_ids = [nil]
    @order_lines = order_lines.where(attendee_id: (admin_attendee_ids + confirmed_attendee_ids))
  end

  def filter_by_ordered_attendees
    return if options[:for_attendee] != false && orders.blank?

    confirmed_attendee_ids = orders.map{|order| order.team_order_attendees.where(status: 'ordered') }.flatten.uniq.map(&:id)
    admin_attendee_ids = [nil]
    @order_lines = order_lines.where(attendee_id: (admin_attendee_ids + confirmed_attendee_ids))
  end

  def filter_by_paid_order_lines
    @order_lines = order_lines.where(payment_status: 'paid')
  end

  def default_options
    {
      order: nil,
      orders: [],
      supplier: nil,
      suppliers: [],
      gst_split: nil,
      for_attendee: false,
      confirmed_attendees_only: false,
      ordered_attendees_only: false,
      paid_only: false,
    }
  end

end
