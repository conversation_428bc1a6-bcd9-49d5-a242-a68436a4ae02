class OrderLines::<PERSON><PERSON>

  def initialize(location:, order_line_params:, order: nil)
    @location = location
    @order = order.presence || location.order
    @order_line_params = order_line_params
  end

  def call
    order_line = scoped_order_lines.first_or_initialize
    order_line.selected_menu_extras = sanitized_selected_menu_extras if order_line_params[:selected_menu_extra_ids].present?
    order_line
  end

private

  attr_reader :order, :location, :order_line_params

  def scoped_order_lines
    order_lines = order.order_lines
    case
    when order_line_params[:id].present?
      order_lines = order_lines.where(id: order_line_params[:id])
    when find_by_menu_item?
      order_lines = order_lines.where(location_id: location.id) # needs the where to allocate during initialization
      order_lines = order_lines.where(menu_item: order_line_params[:item_id]) # if order_line_params[:item_id].present?
      order_lines = order_lines.where(serving_size_id: order_line_params[:serving_size_id]) # if order_line_params[:serving_size_id].present?
      order_lines = order_lines.where(note: order_line_params[:note]) # if order_line_params[:note].present?
      order_lines = order_lines.where(attendee_id: order_line_params[:attendee_id]) # if order_line_params[:attendee_id].present?
      if order_line_params[:selected_menu_extra_ids].present?
        extras_condition = order_line_params[:selected_menu_extra_ids].map{|id| "selected_menu_extras ilike '%#{id}%'" }.join(' AND ')
        order_lines = order_lines.where(extras_condition).where('length(selected_menu_extras) = ?', sanitized_selected_menu_extras.to_yaml.size)
      else
         order_lines = order_lines.where(selected_menu_extras: nil)
      end
    else
      order_lines = order_lines.none
    end
    order_lines
  end

  def sanitized_selected_menu_extras
    order_line_params[:selected_menu_extra_ids].map(&:to_i)
  end

  def find_by_menu_item?
    %i[item_id serving_size_id note selected_menu_extras attendee_id].any?{|field| order_line_params[field].present? }
  end
end
