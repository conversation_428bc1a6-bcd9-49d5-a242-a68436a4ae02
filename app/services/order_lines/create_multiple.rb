class OrderLines::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(order:, order_params: {}, order_lines_params: [])
    @order_params = order_params
    @order_lines_params = order_lines_params
    @order = order.presence || order_from_params
    @result = Result.new
  end

  def call
    if can_create?
      order_lines_params.each do |order_line_params|
        upsert_order_line(order_line_params)
      end
      result.order = order.reload
      result.location = location.reload
    end
    result
  end

private

  attr_accessor :order, :order_params, :order_lines_params, :result

  def can_create?
    case
    when order.blank?
      result.errors << 'Cannot create order lines without an order'
    when location.blank?
      result.errors << 'Cannot create order lines without a location'
    end
    result.errors.blank?
  end

  def order_from_params
    return nil if order_params.blank? || order_params[:order_id].blank?

    Order.where(id: order_params[:order_id]).first
  end

  def location
    @location ||= Locations::Fetch.new(order: order, location_params: { id: order_params[:location_id] }).call
  end

  def customer
    @_customer ||= order.present? && order.customer_profile
  end

  def upsert_order_line(order_line_params)
    upsert = OrderLines::Upsert.new(order: order, customer: customer, location: location, order_line_params: order_line_params).call
    if upsert.success?
      result.created_order_lines << upsert.order_line
    else
      result.errors += upsert.errors
    end
  end

  class Result
    attr_accessor :created_order_lines, :errors, :order, :location

    def initialize
      @order = nil
      @location = nil
      @created_order_lines = []
      @errors = []
    end

    def success?
      order.present? && location.present? && errors.blank?
    end

    def supplier
      created_order_lines.present? && created_order_lines.first.supplier_profile
    end
  end

end
