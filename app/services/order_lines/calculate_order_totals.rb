# called after adding (multiple), updating or removing order lines from an order
class OrderLines::CalculateOrderTotals

  def initialize(order_line:, profile: nil)
    @order_line = order_line
    @order = order_line.order
    @profile = profile
    @result = Result.new
  end

  def call
    recalculate_supplier_topup if charge_to_minimum?
    result.order_totals = saved_order_totals
    handle_team_order if order.is_team_order? && team_order_attendee.present?
    result
  end

private

  attr_reader :order_line, :order, :profile, :result

  def saved_order_totals
    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
  end

  def handle_team_order
    result.team_order_spends = order_supplier_spends if team_order_attendee.is_team_admin?
    result.order_totals = attendee_totals # override order total to store team order attendee (admin or actual attendee) totals
  end

  def team_order_attendee
    @_team_order_attendee ||= (order_line.attendee_id.present? && order_line.team_order_attendee.presence) || (profile.present? && TeamOrderAttendees::Fetch.new(attendee_code: order.unique_event_id, profile: profile).call)
  end

  def attendee_totals
    @_attendee_totals ||= Orders::CalculateCustomerTotals.new(order: order, attendee: team_order_attendee).call
  end

  def order_supplier_spends
    @_order_supplier_spends ||= Orders::GetSupplierSpends.new(order: order, exclude_surcharge: true).call
  end

  def charge_to_minimum?
    if order.is_team_order?
      order.status != 'pending' && order.cutoff_option.present? && order.cutoff_option == 'charge_to_minimum'
    else
      order.status != 'delivered' && order.charge_to_minimum?
    end
  end

  def recalculate_supplier_topup
    Orders::ChargeToSupplierMinimums.new(order: order, supplier_spends: order_supplier_spends.supplier_spends).call
    order.reload
  end

  class Result
    attr_accessor :order_totals, :team_order_spends

    def initialize
      @order_totals = nil
      @team_order_spends = nil
    end
  end

end
