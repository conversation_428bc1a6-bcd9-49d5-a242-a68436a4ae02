class OrderLines::<PERSON>culate<PERSON><PERSON>

  def initialize(order_line:, menu_item: nil, serving_size: nil, rate_card: nil, markup_override: nil, customer: nil, company: nil, menu_extras: [])
    @order_line = order_line
    @menu_item = menu_item || order_line.menu_item
    @serving_size = serving_size || order_line.serving_size
    @menu_extras = menu_extras.presence || MenuExtra.where(id: order_line.selected_menu_extras)
    @customer = customer
    @company = company.presence || (customer.present? && customer.company)
    @rate_card = rate_card.presence || fetch_rate_card
    @markup_override = markup_override.presence || fetch_markup_override
  end

  def call
    base_line_price + menu_extras_price
  end

private

  attr_reader :order_line, :menu_item, :serving_size, :menu_extras, :customer, :company, :rate_card, :markup_override

  def base_line_price
    case
    when rate_card.present?
      rate_card.price
    when serving_size.present?
      price_with_markup(serving_size.price)
    when menu_item.present? && menu_item.promo_price.present?
      price_with_markup(menu_item.promo_price)
    when menu_item.present?
      price_with_markup(menu_item.price)
    end
  end

  def menu_extras_price
    return 0 if menu_extras.blank?

    menu_extras.reduce(0) do |sum, menu_extra|
      next sum if menu_extra.price.blank?

      sum + price_with_markup(menu_extra.price)
    end
  end

  def fetch_rate_card
    return nil if company.blank?

    case
    when serving_size.present?
      company.rate_cards.where(serving_size: serving_size).first
    when menu_item.present?
      company.rate_cards.where(menu_item: menu_item).first
    else
      nil
    end
  end

  def price_with_markup(price)
    case
    when markup_override.present?
      price.to_f * (1 + (markup_override.markup / 100))
    when supplier.present?
      price.to_f * (1 + (supplier.markup / 100))
    else
      price.to_f
    end
  end

  def fetch_markup_override
    Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer, company: company, required_override: :markup).call
  end

  def supplier
    @_supplier ||= menu_item.supplier_profile
  end

end
