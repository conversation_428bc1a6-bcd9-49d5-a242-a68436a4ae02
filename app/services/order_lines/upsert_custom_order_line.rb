class OrderLines::UpsertCustomOrderLine

  def initialize(location:, order_line_params:)
    @location = location
    @order_line_params = order_line_params
    @result = Result.new
  end

  def call
    if order_line_params[:id].blank? && supplier.blank?
      result.errors << 'Cannot create an order line without a supplier'
    else
      upserter = OrderLines::Upsert.new(location: location, order_line_params: sanitized_order_line_params).call
      if upserter.success?
        result.custom_order_line = upserter.order_line
      else
        result.errors += upserter.errors
      end
    end
    result
  end

private

  attr_accessor :result
  attr_reader :order_line_params, :location

  def sanitized_order_line_params
    {
      id: order_line_params[:id],
      item_id: custom_menu_item.try(:id),
      quantity: order_line_params[:quantity],
      note: order_line_params[:note],
      baseline: order_line_params[:baseline],
      cost: order_line_params[:cost],
      price: order_line_params[:price],
      is_gst_inc: order_line_params[:gst_option] == 'gst_inc',
      is_gst_free: order_line_params[:gst_option] == 'gst_free',
    }
  end

  def supplier
    @_supplier ||= SupplierProfile.where(id: order_line_params[:supplier_id]).first
  end

  def custom_menu_section
    return nil if supplier.blank?
    return @_custom_menu_section if @_custom_menu_section.present?

    menu_section_params = { name: 'custom', supplier_profile: supplier }
    section_upserter = MenuSections::Upsert.new(menu_section_params: menu_section_params).call
    @_custom_menu_section = section_upserter.menu_section
  end

  def custom_menu_item
    return nil if custom_menu_section.blank?
    return @_custom_menu_item if @_custom_menu_item.present?

    menu_item_params = {
      menu_section: custom_menu_section,
      name: order_line_params[:menu_item_description],
      description: order_line_params[:menu_item_description],
      price: order_line_params[:baseline],
      supplier_profile: supplier,
      is_gst_free: order_line_params[:gst_option] == 'gst_free'
    }
    # if order line is present... update menu item instead of creating a new one
    item_upserter = case
    when custom_order_line.present?
      MenuItems::Upsert.new(menu_item: custom_order_line.menu_item, menu_item_params: menu_item_params).call
    else
      MenuItems::Upsert.new(menu_item_params: menu_item_params, forced: true).call
    end
    if item_upserter.success?
      @_custom_menu_item = item_upserter.menu_item
    else
      result.errors += item_upserter.errors
    end
  end

  def custom_order_line
    return nil if order_line_params[:id].blank?

    @_order_line ||= OrderLine.where(id: order_line_params[:id]).first
  end

  class Result
    attr_accessor :custom_order_line, :errors

    def initialize
      @custom_order_line = nil
      @errors = []
    end

    def success?
      @custom_order_line.present? && errors.blank?
    end
  end

end


