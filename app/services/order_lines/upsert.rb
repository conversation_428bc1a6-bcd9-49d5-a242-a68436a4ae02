class OrderLines::Upsert

  def initialize(location:, order: nil, customer: nil, order_line_params: {}, update_item: false)
    @location = location
    @order = order.presence || location.try(:order)
    @customer = customer.presence || order&.customer_profile
    @order_line_params = order_line_params
    @update_item = update_item
    @result = Result.new
  end

  def call
    fetch_order_line
    if order_line.present?
      result.order_line = order_line
      if order_line.new_record? || update_item
        order_line.assign_attributes(sanitized_attributes)
        order_line.price = order_line_params[:price] || calculated_order_line_price
        order_line.cost = order_line_params[:cost] || calculated_order_line_cost
        order_line.baseline = order_line_params[:baseline] || calculated_order_line_baseline
      end
      order_line.note = order_line_params[:note] if !order_line_params[:note].nil?
      order_line.quantity = calculated_order_line_quantity if !order_line_params[:quantity].nil?
      order_line.last_errors = [] if order_line.last_errors.present?
      if order_line.valid? && order_line.save!
        sync_with_woolworths
        mark_attendee_as_pending if order_line.attendee_id.present? && !update_item
        update_status if %w[pending draft quoted].exclude?(order.status) && !update_item
      else
        result.errors = order_line.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :location, :order, :customer, :order_line_params, :update_item
  attr_accessor :order_line, :result

  def fetch_order_line
    if location.blank?
      result.errors << 'Cannot create an order line without a location'
    else
      @order_line = OrderLines::Fetch.new(order: order, location: location, order_line_params: order_line_params).call
    end
  end

  def sanitized_attributes
    attributes = {
      order: order,
      name: order_line_name,
      is_gst_free: order_line_gst_free,
      is_gst_inc: order_line_gst_inc,
      menu_item: menu_item,
      supplier_profile: supplier,
    }
    attributes.merge!(new_order_line_attributes) if order_line.new_record?
    attributes
  end

  def new_order_line_attributes
    {
      serving_size: serving_size,
      selected_menu_extras: selected_extras.map(&:id),
      note: order_line_params[:note],
      attendee_id: order_line_params[:attendee_id],
      category_id: category.try(:id),
    }
  end

  def supplier
    @_supplier ||= menu_item.try(:supplier_profile)
  end

  def menu_item
    @_menu_item ||= case
    when order_line_params[:item_id].present?
      MenuItem.where(id: order_line_params[:item_id]).first
    when order_line_params[:menu_item].present?
      order_line_params[:menu_item]
    else
      order_line.menu_item
    end
  end

  def serving_size
    @_serving_size ||= (menu_item.present? && menu_item.serving_sizes.where(id: order_line_params[:serving_size_id]).first) || order_line.serving_size
  end

  def selected_extras
    return [] if menu_item.blank? && order_line_params[:selected_menu_extra_ids].blank?

    @_selected_extras ||= menu_item.menu_extras.where(id: order_line_params[:selected_menu_extra_ids])
  end

  def order_line_name
    name = menu_item.try(:name) || ''
    name += " - #{serving_size.name}" if serving_size.present?
    name += ' (with extras)' if selected_extras.present?
    name
  end

  def order_line_gst_free
    order_line_params[:is_gst_free].presence || (menu_item.present? && menu_item.is_gst_free)
  end

  def order_line_gst_inc
    order_line_params[:is_gst_inc].presence || order_line.try(:is_gst_inc)
  end

  def calculated_order_line_price
    return nil if menu_item.blank?

    OrderLines::CalculatePrice.new(order_line: order_line, menu_item: menu_item, serving_size: serving_size, rate_card: rate_card, customer: customer, company: company).call
  end

  def calculated_order_line_cost
    return nil if menu_item.blank?

    OrderLines::CalculateCost.new(order_line: order_line, menu_item: menu_item, serving_size: serving_size, rate_card: rate_card, customer: customer, company: company).call
  end

  def calculated_order_line_baseline
    return nil if menu_item.blank?

    OrderLines::CalculateCost.new(order_line: order_line, menu_item: menu_item, serving_size: serving_size, rate_card: rate_card, customer: customer, company: company, baseline: true).call
  end

  def calculated_order_line_quantity
    if order_line.new_record? || order_line_params[:id].present?
      order_line_params[:quantity]
    else
      (order_line.quantity + order_line_params[:quantity].to_i)
    end
  end

  def company
    @_company ||= customer&.company
  end

  def rate_card
    return nil if company.blank?
    return @_rate_card if !@_rate_card.nil?

    @_rate_card = case
    when serving_size.present?
      company.rate_cards.where(serving_size: serving_size).first
    when menu_item.present?
      company.rate_cards.where(menu_item: menu_item).first
    else
      nil
    end
  end

  def category
    menu_categories = menu_item&.menu_section&.categories&.order(weight: :asc)

    categories = case
    when menu_categories.present? && order.is_home_delivery?
      home_categories = menu_categories.select{|category| category.group == 'home-deliveries' }
      home_categories.presence || menu_categories
    when menu_categories.present?
      menu_categories.reject{|category| category.group == 'home-deliveries' }
    when order.is_event_order? && (order_major_category = order.major_category.presence)
      [order_major_category]
    else
      []
    end

    categories.first
  end

  def sync_with_woolworths
    return if supplier.blank? || !supplier.woolworths? # not Woolworths order line

    return if order.woolworths_order.blank? # not connected to Woolworths account

    return if order.status != 'draft' || order.order_variant == 'event_order' #  not a draft or is a custom order

    Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line).call(sync_event: :start)
    Woolworths::ProcessOrderLine.new(order_line: order_line).delay(queue: :instant, attempts: 3).call
  end

  def mark_attendee_as_pending
    order_line.team_order_attendee.update(status: 'pending')
  end

  def update_status
    order_line.update(status: 'amended')
    updated_reference = order.reload.current_version_ref
    order.update(version_ref: updated_reference, status: 'amended')
  end

  class Result
    attr_accessor :order_line, :errors

    def initialize
      @order_line = nil
      @errors = []
    end

    def success?
      order_line.present? && order_line.valid? && errors.blank?
    end
  end

end
