class MenuExtraSections::Upsert

  def initialize(menu_extra_section_params:, menu_extra_section: nil, forced: nil)
    @is_forced = forced.presence || false
    @menu_extra_section_params = menu_extra_section_params.present? ? menu_extra_section_params.to_h.symbolize_keys : {}
    @menu_extra_section = menu_extra_section.presence || fetch_menu_extra_section
    @result = Result.new
  end

  def call
    if menu_extra_section.update(sanitized_params)
      result.menu_extra_section = menu_extra_section
    else
      result.errors += menu_extra_section.errors.full_messages
    end
    result
  end

private

  attr_reader :menu_extra_section, :menu_extra_section_params, :is_forced, :result

  def fetch_menu_extra_section
    retrieval_params = {
      name: sanitized_name_params[:name],
      menu_item: menu_item,
    }
    is_forced ? MenuExtraSection.new(retrieval_params) : MenuExtraSection.where(retrieval_params).first_or_initialize
  end

  def sanitized_params
    [
      default_params,
      menu_extra_section_params,
      sanitized_name_params
    ].inject(&:merge)
  end

  def default_params
    return {} if !menu_extra_section.new_record?

    {
      weight: menu_extra_section_params[:weight].presence || menu_extra_section.weight || max_weight
    }
  end

  def sanitized_name_params
    return {} if menu_extra_section_params[:name].blank?

    {
      name: menu_extra_section_params[:name].strip
    }
  end

  def menu_item
    @_menu_item ||= case
    when menu_extra_section_params[:menu_item].present?
      menu_extra_section_params[:menu_item]
    when menu_item_id = menu_extra_section_params[:menu_item_id].presence
      MenuItem.where(id: menu_item_id).first
    when menu_extra_section.present?
      menu_extra_section.menu_item
    end
  end

  def max_weight
    weight = case
    when menu_item.present?
      menu_item.menu_extra_sections.pluck(:weight).compact.max
    else
      MenuExtraSection.pluck(:weight).compact.max
    end
    (weight.presence || 0) + 1
  end

  class Result
    attr_accessor :menu_extra_section, :errors

    def initialize
      @menu_extra_section = nil
      @errors = []
    end

    def success?
      errors.blank? && menu_extra_section.present? && menu_extra_section.valid? && menu_extra_section.persisted?
    end
  end
end
