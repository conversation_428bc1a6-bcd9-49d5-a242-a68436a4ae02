class MenuExtraSections::<PERSON><PERSON>

  def initialize(menu_extra_section:, menu_item: nil)
    @menu_extra_section = menu_extra_section
    @menu_item = menu_item
    @result = Result.new(template: menu_extra_section)
  end

  def call
    if can_clone?
      extra_creator = MenuExtraSections::Upsert.new(menu_extra_section_params: clone_params, forced: true).call
      if extra_creator.success?
        result.cloned_menu_extra_section = extra_creator.menu_extra_section
        clone_menu_extras if menu_extras.present?
      else
        result.errors += extra_creator.errors
      end
    end
    result
  end

private

  attr_reader :menu_extra_section, :menu_item, :result

  def can_clone?
    if menu_extra_section.archived_at.present?
      result.errors << 'Cannot clone an archived menu extra section'
    end
    result.errors.blank?
  end

  def clone_params
    params = menu_extra_section.attributes.symbolize_keys.except(:id, :name, :weight, :created_at, :updated_at, :menu_item_id)
    params[:name] = menu_item.present? ? menu_extra_section.name : "#{menu_extra_section.name} - CLONED"
    params[:menu_item_id] = menu_item.present? ? menu_item.id : menu_extra_section.menu_item_id
    params
  end

  def menu_extras
    @_menu_extras ||= menu_extra_section.menu_extras.where(archived_at: nil).order(:weight)
  end

  def clone_menu_extras
    menu_extras.each do |menu_extra|
      extra_cloner = MenuExtras::Clone.new(menu_extra: menu_extra, menu_extra_section: result.cloned_menu_extra_section).call
      result.errors += extra_cloner.errors if !extra_cloner.success?
    end
  end

  class Result
    attr_accessor :template, :cloned_menu_extra_section, :errors

    def initialize(template:)
      @template = template
      @cloned_menu_extra_section = nil
      @errors = []
    end

    def success?
      errors.blank? && cloned_menu_extra_section.present? && cloned_menu_extra_section.persisted?
    end
  end
end
