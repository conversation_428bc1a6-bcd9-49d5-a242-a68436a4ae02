class MenuExtraSections::Archive

  def initialize(menu_extra_section:)
    @menu_extra_section = menu_extra_section
    @result = Result.new(menu_extra_section: menu_extra_section)
  end

  def call
    if can_archive?
      menu_extra_section.update(archived_at: Time.zone.now)
      self.delay(queue: :data_integrity).archive_menu_extras
    end
    result
  end

private

  attr_accessor :menu_extra_section, :result

  def can_archive?
    case
    when menu_extra_section.blank?
      result.errors << 'Cannot archive a missing menu extra section'
    when menu_extra_section.archived_at.present?
      result.errors << 'Cannot archvie an already archived menu extra section'
    end
    result.errors.blank?
  end

  def menu_extras
    @_menu_extras ||= menu_extra_section.menu_extras.where(archived_at: nil)
  end

  def archive_menu_extras
    menu_extras.each do |menu_extra|
      MenuExtras::Archive.new(menu_extra: menu_extra).call
    end
  end

  class Result
    attr_accessor :menu_extra_section, :errors

    def initialize(menu_extra_section:)
      @menu_extra_section = menu_extra_section
      @errors = []
    end

    def success?
      errors.blank? && menu_extra_section.reload.archived_at.present?
    end
  end
end
