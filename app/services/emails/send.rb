class Emails::Send

  def initialize(template_name:, recipient:, subject:, cc: nil, sender: nil, email_options: {}, email_variables: {}, attachments: [])
    @template_name = template_name.presence || email_options[:template]
    @recipient = recipient
    @subject = subject
    @cc = cc
    @sender = sender

    @email_options = email_options
    @email_variables = email_variables

    @attachments = attachments
    @result = Result.new
  end

  def call(verbose: false)
    @verbose = verbose
    if can_send?
      puts '********* WE CAN SEND AN EMAIL *********' if verbose
      if create_email_record && send_email
        mark_email_as_sent
      end
    end
    result
  end

private

  attr_reader :template_name, :recipient, :subject, :cc, :sender, :email_options, :email_variables, :attachments, :result, :verbose
  attr_accessor :email

  def can_send?
    case
    when recipient.blank?
      result.errors << 'Cannot send email without recipient(s)'
    when subject.blank?
      result.errors << 'Cannot send email without a valid subject'
    when EmailTemplate::VALID_TEMPLATE_NAMES.exclude?(template_name)
      result.errors << "Cannot send email without a valid template - #{template_name}"
      raise "Unknown email template name '#{template_name}'"
    end
    result.errors.blank?
  end

  def create_email_record
    @email = Email.new(sanitized_email_attributes)
    if email.save
      puts "********* CREATED RECORD #{email.id} *********" if verbose
      result.email = email
    else
      result.errors += email.errors.full_messages
    end
    result.email.present? && result.errors.blank?
  end

  def sanitized_email_attributes
    [
      email_attributes,
      email_options,
      email_details
    ].inject(&:merge)
  end

  def email_attributes
    {
      template_name: template_name,
      recipient: recipient,
      options: email_variables
    }
  end

  def email_details
    details = {
      subject: subject,
      recipient: recipient,
    }
    details[:cc] = cc if cc.present?
    { details: details }
  end

  def send_email
    puts "********* SENDING EMAIL #{email.id} via View Templates *********" if verbose
    TemplateMailer.send_email(template_name: template_name, email_variables: email_variables, recipient: email_recipients, cc: cc, sender: email_sender, subject: subject, email_attachments: email_attachments).deliver
  end

  def mark_email_as_sent
    puts '********* MARKING EMAIL AS SENT *********' if verbose
    email.update(sent_at: Time.zone.now)
  end

  def email_recipients
    recipient.gsub(';', ',')
  end

  def email_sender
    sender.presence || '<EMAIL>'
  end

  def email_attachments
    attachments_hash = {}
    return {} if attachments.blank?

    attachments.each do |attachment|
      case
      when attachment.is_a?(Document)
        attachments_hash["#{attachment.name}.#{attachment.file_extension}"] = open(attachment.url).read
      when attachment[:is_csv]
        attachments_hash["#{attachment[:name]}.csv"] = attachment[:file_url]
      else
        attachments_hash["#{attachment[:name]}.pdf"] = open(attachment[:file_url]).read
      end
    end
    attachments_hash
  end

  class Result
    attr_accessor :email, :errors

    def initialize
      @email = ''
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
