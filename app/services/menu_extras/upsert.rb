class MenuExtras::Upsert

  def initialize(menu_extra_params:, menu_extra: nil, forced: nil)
    @is_forced = forced.presence || false
    @menu_extra_params = menu_extra_params.present? ? menu_extra_params.to_h.symbolize_keys : {}
    @menu_extra = menu_extra.presence || fetch_menu_extra
    @result = Result.new
  end

  def call
    if menu_extra.update(sanitized_params)
      result.menu_extra = menu_extra
    else
      result.errors += menu_extra.errors.full_messages
    end
    result
  end

private

  attr_reader :menu_extra, :menu_extra_params, :is_forced, :result

  def fetch_menu_extra
    retrieval_params = {
      name: sanitized_name_params[:name],
      menu_item: menu_item,
      menu_extra_section: menu_extra_section,
    }
    is_forced ? MenuExtra.new(retrieval_params) : MenuExtra.where(retrieval_params).first_or_initialize
  end

  def sanitized_params
    [
      default_params,
      menu_extra_params,
      sanitized_name_params
    ].inject(&:merge)
  end

  def default_params
    return {} if !menu_extra.new_record?

    {
      weight: menu_extra_params[:weight] || menu_extra.weight || max_weight
    }
  end

  def sanitized_name_params
    return {} if menu_extra_params[:name].blank?

    {
      name: menu_extra_params[:name].strip
    }
  end

  def menu_item
    @_menu_item ||= case
    when menu_extra_params[:menu_item].present?
      menu_extra_params[:menu_item]
    when menu_item_id = menu_extra_params[:menu_item_id].presence
      MenuItem.where(id: menu_item_id).first
    when menu_extra_section.present?
      menu_extra_section.menu_item
    when menu_extra.present?
      menu_extra.menu_item
    end
  end

  def menu_extra_section
    @_menu_extra_section ||= case
    when menu_extra_params[:menu_extra_section].present?
      menu_extra_params[:menu_extra_section]
    when menu_extra_section_id = menu_extra_params[:menu_extra_section_id].presence
      MenuExtraSection.where(id: menu_extra_section_id).first
    when menu_extra.present?
      menu_extra.menu_extra_section
    end
  end

  def max_weight
    weight = case
    when menu_extra_section.present?
      menu_extra_section.menu_extras.pluck(:weight).compact.max
    when menu_item.present?
      menu_item.menu_extras.pluck(:weight).compact.max
    else
      MenuExtra.pluck(:weight).compact.max
    end
    (weight.presence || 0) + 1
  end

  class Result
    attr_accessor :menu_extra, :errors

    def initialize
      @menu_extra = nil
      @errors = []
    end

    def success?
      errors.blank? && menu_extra.present? && menu_extra.valid? && menu_extra.persisted?
    end
  end
end
