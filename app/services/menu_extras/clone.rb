class MenuExtras::<PERSON><PERSON>

  def initialize(menu_extra:, menu_extra_section: nil)
    @menu_extra = menu_extra
    @menu_extra_section = menu_extra_section
    @result = Result.new(template: menu_extra)
  end

  def call
    if can_clone?
      extra_creator = MenuExtras::Upsert.new(menu_extra_params: clone_params, forced: true).call
      if extra_creator.success?
        result.cloned_menu_extra = extra_creator.menu_extra
      else
        result.errors += extra_creator.errors
      end
    end
    result
  end

private

  attr_reader :menu_extra, :menu_extra_section, :result

  def can_clone?
    if menu_extra.archived_at.present?
      result.errors << 'Cannot clone an archived menu extra'
    end
    result.errors.blank?
  end

  def clone_params
    params = menu_extra.attributes.symbolize_keys.except(:id, :name, :weight, :created_at, :updated_at, :menu_extra_section_id, :menu_item_id)
    params[:name] = menu_extra_section.present? ? menu_extra.name : "#{menu_extra.name} - CLONED"
    params[:menu_extra_section_id] = menu_extra_section.present? ? menu_extra_section.id : menu_extra.menu_extra_section_id
    params[:menu_item_id] = menu_extra_section.present? ? menu_extra_section.menu_item_id : menu_extra.menu_item_id
    params
  end

  class Result
    attr_accessor :template, :cloned_menu_extra, :errors

    def initialize(template:)
      @template = template
      @cloned_menu_extra = nil
      @errors = []
    end

    def success?
      errors.blank? && cloned_menu_extra.present? && cloned_menu_extra.persisted?
    end
  end
end
