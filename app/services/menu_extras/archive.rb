class MenuExtras::Archive

  def initialize(menu_extra:)
    @menu_extra = menu_extra
    @result = Result.new(menu_extra: menu_extra)
  end

  def call
    menu_extra.update(archived_at: Time.zone.now) if can_archive?
    result
  end

private

  attr_accessor :menu_extra, :result

  def can_archive?
    case
    when menu_extra.blank?
      result.errors << 'Cannot archive a missing menu extra'
    when menu_extra.archived_at.present?
      result.errors << 'Cannot archvie an already archived menu extra'
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :menu_extra, :errors

    def initialize(menu_extra:)
      @menu_extra = menu_extra
      @errors = []
    end

    def success?
      errors.blank? && menu_extra.reload.archived_at.present?
    end
  end
end
