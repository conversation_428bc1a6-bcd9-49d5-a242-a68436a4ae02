class Notifications::WithPreference < Notifications::Base

private

  attr_accessor :notifying_account, :notification_variation, :template_name

  def notification_preference
    return nil if notifying_account.blank? || template_name.blank?

    @_notification_preference ||= notifying_account.notification_preferences.where(template_name: template_name, variation: notification_variation).first
  end

  def preferred_notification?
    notification_preference.blank? || notification_preference.active?
  end

  def email_recipients(default: '')
    notification_preference.present? && notification_preference.email_recipients.present? ? notification_preference.email_recipients : default
  end

  def email_salutation(default: '')
    notification_preference.present? && notification_preference.salutation.present? ? notification_preference.salutation : default
  end

end
