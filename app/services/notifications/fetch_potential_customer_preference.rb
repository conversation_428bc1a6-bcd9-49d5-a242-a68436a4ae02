class Notifications::FetchPotentialCustomerPreference

  def initialize(customer:, template_name:)
    @customer = customer
    @template_name = template_name
    @result = Result.new
  end

  def call
    result.email_recipients = case email_template.kind
    when 'order', 'team_admin'
      customer_email_recipient
    when 'billing'
      customer.billing_details.present? ? customer_billing_recipients : customer_email_recipient
    end
    result.email_salutation = customer.email_salutation
    result
  end

private

  attr_reader :customer, :template_name, :result

  def email_template
    @_email_template = EmailTemplate.where(name: template_name).first
  end

  def customer_email_recipient
    customer.user.present? ? customer.user.email : yordar_credentials(:yordar, :admin_email)
  end

  def customer_billing_recipients
    recipients = customer.billing_details.email
    recipients += ", #{customer.user.secondary_email}" if customer.user.present? && customer.user.secondary_email.present?
    recipients.gsub(/\r|\n/, '')
  end

  class Result
    attr_accessor :email_recipients, :email_salutation

    def initialize
      @email_recipients = nil
      @email_salutation = nil
    end
  end

end
