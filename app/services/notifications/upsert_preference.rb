class Notifications::UpsertPreference

  def initialize(account:, preference: nil, preference_params: {})
    @account = account
    @preference_params = preference_params
    @preference = preference || fetch_notification_preference
    @result = Result.new
  end

  def call
    if can_upsert? && has_valid_variations?
      if preference.update(preference_params)
        result.preference = preference
      else
        result.errors += preference.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :account, :preference, :preference_params, :result

  def can_upsert?
    case
    when account.blank?
      result.errors << 'Cannot create/update a preference without an account'
    when preference.blank? || preference.account != account
      result.errors << 'You do not have access to this preference'
    when email_template.blank?
      result.errors << 'Cannot upsert a preference with this template name'
    end
    result.errors.blank?
  end

  def has_valid_variations?
    case
    when email_template.variations.blank? && preference_params[:variation].present?
      result.errors << 'Cannot upsert a notification with variation for a template that does not have any variations'
    when email_template.variations.present? && preference_params[:variation].blank?
      result.errors << 'Cannot upsert a notification without variation for a template that has variations'
    when email_template.variations.present? && email_template.variations.exclude?(preference_params[:variation])
      result.errors << 'Cannot upsert a notification with an invalid template variation'
    end
    result.errors.blank?
  end

  def fetch_notification_preference
    account.present? && account.notification_preferences.where(template_name: preference_params[:template_name], variation: preference_params[:variation]).first_or_initialize
  end

  def email_template
    template_name = preference.present? ? preference.template_name : preference_params[:template_name]
    @_email_templates ||= EmailTemplate.where(account_type: account.class.name, name: template_name).first
  end

  class Result
    attr_accessor :preference, :errors

    def initialize
      @preference = preference
      @errors = []
    end

    def success?
      errors.blank? && preference.present? && preference.persisted?
    end
  end
end
