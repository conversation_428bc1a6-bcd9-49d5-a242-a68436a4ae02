class EventTeams::Upsert

  def initialize(team_params:, team_admin:, event_team: nil)
    @team_params = team_params
    @team_admin = team_admin
    @event_team = event_team.presence || fetch_event_team
    @result = Result.new
  end

  def call
    if can_upsert?
      if event_team.update(team_params)
        result.event_team = event_team
      else
        result.errors << event_team.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :team_params, :event_team, :team_admin, :result

  def can_upsert?
    case
    when team_admin.blank?
      result.errors << 'Cannot upsert a team without a team admin'
    when event_team.present? && event_team.customer_profile != team_admin
      result.errors << 'You don\'t have access to this team'
    end
    result.errors.blank?
  end

  def fetch_event_team
    return nil if team_admin.blank? || team_params.blank?

    team_admin.event_teams.where('name ILIKE ?', team_params[:name]).first_or_initialize
  end

  class Result
    attr_accessor :event_team, :errors

    def initialize
      @event_team = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
