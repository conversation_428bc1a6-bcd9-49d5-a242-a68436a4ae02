class Coupons::AttachToO<PERSON>r

  def initialize(coupon_code:, order:, profile: nil)
    @order = order
    @coupon_code = coupon_code.try(:strip)
    @profile = profile
    @result = Result.new(order: order)
  end

  def call
    if is_valid_coupon? && order.update(coupon: coupon)
      calculate_totals_with_coupon
      redeem_coupon if order.status != 'draft'
      result.coupon = coupon
    end
    result
  end

private

  attr_accessor :result
  attr_reader :order, :coupon_code, :profile

  def coupon
    @_coupon = Coupon.where(code: coupon_code).first
  end

  def is_valid_coupon?
    case
    when coupon_code.blank? || coupon.blank?
      result.errors << 'Invalid coupon'
    when coupon.expired?
      result.errors << 'Coupon has expired'
    when !coupon.can_redeem?
      result.errors << 'Coupon has already been used'
    when profile.present? && coupon.orders.where.not(status: %w[draft cancelled skipped paused]).where(customer_profile: profile).present?
      result.errors << 'Coupon can only be used once'
    end
    result.errors.blank?
  end

  def calculate_totals_with_coupon
    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
  end

  def redeem_coupon
    Coupons::Redeem.new(coupon: coupon, order: order).call
  end

  class Result
    attr_accessor :order, :coupon, :errors

    def initialize(order:)
      @order = order
      @coupon = nil
      @errors = []
    end

    def success?
      errors.blank? && coupon.present?
    end
  end

end

