class Coupons::Redeem

  REDEEMABLE_ORDER_FIELDS = %w[customer_subtotal customer_delivery].freeze

  attr_reader :result

  def initialize(coupon:, order: nil)
    @coupon = coupon
    @order = order
    @result = Result.new
  end

  def call
    if can_redeem?
      redeem_coupon
      result.coupon = coupon.reload
      sanitize_other_coupon_attachments
    end
    result
  end

private

  attr_reader :coupon, :order

  def can_redeem?
    case
    when coupon.blank?
      result.errors << 'Cannot redeem a missing coupon'
    when order.blank?
      result.errors << 'Cannot redeem a coupon without an order (amount)'
    when order.coupon != coupon
      result.errors << 'Coupon does not belong to the order'
    when !redeemable?
      result.errors << 'Cannot redeem this coupon now'
    end
    result.errors.blank?
  end

  def redeemable_amount
    REDEEMABLE_ORDER_FIELDS.sum{|field| order.send(field).presence || 0 }
  end

  def redeem_coupon
    calulated_discount = Coupons::CalculateDiscount.new(coupon: coupon, amount: redeemable_amount).call
    coupon.redemptions.create!(order: order)
    result.discount = calulated_discount[:discount]
  end

  def redeemable?
    is_within_redeemable_dates? && coupon.coupon_redemptions_count < coupon.redemption_limit
  end

  def is_within_redeemable_dates?
    coupon.valid_from <= Time.zone.today && (coupon.valid_until.blank? || coupon.valid_until >= Time.zone.today)
  end

  def sanitize_other_coupon_attachments
    if !redeemable? # cannot redeem again
      other_orders = Order.where.not(id: order.id).where(status: 'draft', coupon: coupon)
      other_orders.each do |order|
        order.update(coupon_id: nil)
        Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
      end
    end
  end

  class Result
    attr_accessor :coupon, :discount, :errors

    def initialize
      @coupon = nil
      @discount = nil
      @errors = []
    end

    def success?
      errors.blank? && discount.present?
    end
  end

end
