class Coupons::CalculateDiscount

  def initialize(coupon:, amount:)
    @coupon = coupon
    @amount = amount
  end

  def call
    {
      amount: amount,
      discount: discount,
      total: [0, amount - discount].max,
    }
  end

private

  attr_reader :coupon, :amount

  def discount
    return 0 if coupon.blank? || !is_within_redeemable_dates? || !is_redeemable?

    @_discount ||= coupon.type == 'percentage' ? (amount * coupon.amount / 100) : coupon.amount
  end

  def is_within_redeemable_dates?
    coupon.valid_from <= Time.zone.today && (coupon.valid_until.blank? || coupon.valid_until >= Time.zone.today)
  end

  def is_redeemable?
    coupon.coupon_redemptions_count <= coupon.redemption_limit
  end

end
