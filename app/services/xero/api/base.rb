class Xero::API::Base

  SYNC_THRESHOLD_DATE = '2022-01-01'.freeze

  SIGNATURE = 'x-xero-signature'.freeze

  ADDRESS_TYPE = 'STREET'.freeze
  PHONE_TYPE = 'DEFAULT'.freeze
  MOBILE_TYPE = 'MOBILE'.freeze

  CUSTOMER_INVOICE_TYPE = 'ACCREC'.freeze
  SUPPLIER_INVOICE_TYPE = 'ACCPAY'.freeze

  # App User Id => Xero Account Number
  SUPPLIER_ACCOUNT_CODES = {
    '10045' => '51150',
    '10106' => '51001',
    '10109' => '53232',
    '10040' => '51000',
    '10047' => '53232',
    '10063' => '51000',
    '10052' => '53232',
    '10160' => '51000',
    '10070' => '53232',
    '10037' => '51000',
    '10069' => '52340',
    '10115' => '53232',
    '10072' => '53232',
    '10051' => '53232',
    '10066' => '53232',
    '10065' => '53232',
    '10075' => '53232',
    '10041' => '51000',
    '10046' => '53232',
    '10081' => '53232',
    '10053' => '53232',
    '10126' => '51000',
    '10038' => '51000',
    '10064' => '53232',
    '10103' => '53232',
    '10031' => '51000',
    '10032' => '51000',
    '10054' => '53232',
    '10036' => '51100',
    '10018' => '51000',
    '10039' => '51100',
    '33' => '52340',
  }.freeze

  def xero_client
    return @_xero_client if @_xero_client.present?

    client_id = yordar_credentials(:xero, :client_id)
    client_secret = yordar_credentials(:xero, :client_secret)
    client = Xeroizer::OAuth2Application.new(client_id, client_secret) # setup client
    xero_token = client.authorize_from_client_credentials # get token
    client.authorize_from_access(xero_token.token) # setup client with access token
    @_xero_client = client
  end

end
