class Xero::API::FetchInvoices < Xero::API::Base

  def initialize(invoices: [])
    @invoices = invoices
    @mapped_invoices = []
  end

  def call
    invoices.each do |invoice|
      found_xero_invoice = xero_invoices.detect{|xero_invoice| xero_invoice.invoice_number.sub(/^INV|^BK/, '') == invoice.number }
      next if found_xero_invoice.blank?

      mapped_invoices << MappedInvoice.new(invoice: invoice, xero_invoice: found_xero_invoice)
    end
    mapped_invoices
  end

private

  attr_reader :invoices
  attr_accessor :mapped_invoices

  def xero_invoices
    @_xero_invoices ||= xero_client.Invoice.all(where: number_condition)
  end

  def number_condition
    conditions = invoices.map{|invoice| "InvoiceNumber==\"#{invoice.number}\"" }
    conditions += invoices.map{|invoice| "InvoiceNumber==\"BK#{invoice.number}\"" }
    conditions += invoices.map{|invoice| "InvoiceNumber==\"INV#{invoice.number}\"" }
    conditions.join(' OR ')
  end

  class MappedInvoice
    attr_reader :invoice, :xero_invoice

    def initialize(invoice:, xero_invoice:)
      @invoice = invoice
      @xero_invoice = xero_invoice
    end
  end

end
