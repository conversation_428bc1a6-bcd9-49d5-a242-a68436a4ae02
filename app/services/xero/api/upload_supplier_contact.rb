class Xero::API::UploadSupplierContact < Xero::API::Base

  def initialize(supplier:)
    @supplier = supplier
    @result = Result.new
  end

  def call
    if can_upload? && !has_existing_contact?
      build_new_contact
      add_details
      save_contact
    end
    result
  end

private

  attr_reader :supplier, :result
  attr_accessor :contact

  def can_upload?
    case
    when supplier.blank? || supplier_user.blank?
      result.errors << 'Cannot upload a missing contact'
    end
    result.errors.blank?
  end

  def has_existing_contact?
    existing_contact = find_contact_by_id.presence
    existing_contact ||= find_contact_by_name.presence

    result.contact = @contact = existing_contact
    supplier_user.update(xero_push_fault: false) if existing_contact.present? && supplier_user.xero_push_fault == true
    existing_contact.present?
  end

  def find_contact_by_id
    xero_client.Contact.all(where: { contact_number: supplier_user.id })[0]
  end

  def find_contact_by_name
    formatted_name_condition = format('Name.ToLower()=="%<name>s"', name: supplier.company_name.downcase.strip)
    xero_client.Contact.all(where: formatted_name_condition)[0]
  end

  def build_new_contact
    @contact = xero_client.Contact.build(name: supplier.company_name, contact_number: supplier_user.id)
  end

  def add_details
    contact.bank_account_details = "#{supplier.bsb_number}#{supplier.bank_account_number}" if supplier.bsb_number? && supplier.bank_account_number?
    contact.tax_number = supplier.abn_acn if supplier.abn_acn.present?

    contact.add_address(**address_details) if supplier.company_address.present?
    contact.add_phone(type: PHONE_TYPE, number: supplier.phone) if supplier.phone.present?
    contact.add_phone(type: MOBILE_TYPE, number: supplier.mobile) if supplier.mobile.present?
  end

  def supplier_user
    @_supplier_user ||= supplier.user
  end

  def billing_details
    @_billing_details ||= supplier.billing_details.presence
  end

  def address_details
    [street_address_details, suburb_details].inject(&:merge)
  end

  def street_address_details
    {
      type: ADDRESS_TYPE,
      line1: supplier.company_address,
    }
  end

  def suburb_details
    suburb = supplier.company_address_suburb
    return {} if suburb.blank?

    {
      line2: suburb.name,
      postal_code: suburb.postcode,
      region: suburb.state
    }
  end

  def save_contact
    if contact.save
      result.contact = contact
    else
      result.errors << "Could not save contact for #{supplier.id}"
      supplier_user.update(xero_push_fault: true)
    end
  end

  class Result
    attr_accessor :contact, :errors

    def initialize
      @contact = nil
      @errors = []
    end

    def success?
      errors.blank? && contact.present?
    end
  end

end
