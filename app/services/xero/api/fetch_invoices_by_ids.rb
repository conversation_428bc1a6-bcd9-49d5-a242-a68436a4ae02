class Xero::API::FetchInvoicesByIds < Xero::API::Base

  def initialize(xero_invoices:)
    @xero_invoices = xero_invoices
    @mapped_invoices = []
  end

  def call
    xero_invoices.each do |xero_invoice|
      xero_api_invoice = xero_api_invoices.detect{|api_invoice| api_invoice.invoice_id == xero_invoice.invoice_id }
      yordar_invoice = xero_api_invoice.present? ? yordar_invoices.detect{|invoice| xero_api_invoice.invoice_number.present? && xero_api_invoice.invoice_number.sub(/^INV|^BK/, '') == invoice.number } : nil
      @mapped_invoices << MappedInvoice.new(xero_invoice: xero_invoice, xero_api_invoice: xero_api_invoice, invoice: yordar_invoice)
    end
    mapped_invoices
  end

private

  attr_reader :xero_invoices, :mapped_invoices

  def yordar_invoices
    @_yordar_invoices ||= ::Invoice.where(number: sanitized_invoice_numbers)
  end

  def xero_api_invoices
    @_xero_api_invoices ||= xero_client.Invoice.all(IDs: xero_invoices.map(&:invoice_id).join(','))
  end

  def sanitized_invoice_numbers
    return [] if xero_api_invoices.blank?

    xero_api_invoices.map do |xero_api_invoice|
      next if xero_api_invoice.invoice_number.blank?

      xero_api_invoice.invoice_number.sub(/^INV|^BK/, '')
    end.reject(&:blank?)
  end

  class MappedInvoice
    attr_reader :xero_invoice, :xero_api_invoice, :invoice

    def initialize(xero_invoice:, xero_api_invoice:, invoice:)
      @xero_invoice = xero_invoice
      @xero_api_invoice = xero_api_invoice
      @invoice = invoice
    end
  end

end
