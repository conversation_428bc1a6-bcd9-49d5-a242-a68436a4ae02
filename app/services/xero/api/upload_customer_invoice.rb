class Xero::API::UploadCustomerInvoice < Xero::API::Base

  # set an one second sleep time to avoid exceeding 60 requests per minute
  def initialize(invoice:)
    @invoice = invoice
    @result = Result.new
  end

  def call
    if can_upload? && is_valid_contact? && !xero_invoice_exists?
      push_invoice_to_xero
    end
    result
  end

private

  attr_reader :invoice, :contact, :xero_invoice, :result

  def can_upload?
    case
    when invoice.blank?
      result.errors << 'Cannot upload a missing invoice'
    when invoice.pushed_to_xero?
      result.errors << 'Invoice already uploaded to Xero'
    when invoice_orders.blank?
      result.errors << 'Invoice does not contain any orders'
    when customer.blank?
      result.errors << 'Invoice does not belong to a customer'
    when customer.errors.present?
      result.errors += customer.errors.full_messages
    end
    result.errors.blank?
  end

  def is_valid_contact?
    contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
    if contact_uploader.success?
      result.contact = @contact = contact_uploader.contact
    else
      result.errors << "Failed to save/update customer #{customer.id}"
      result.errors += contact_uploader.errors
    end
    result.errors.blank?
  end

  def xero_invoice_exists?
    begin
      existing_xero_invoice = xero_client.Invoice.all(where: { invoice_number: invoice.number })[0]
      if existing_xero_invoice.present?
        invoice.update(pushed_to_xero: true) # If this invoice is present don't create it again
        result.xero_invoice = existing_xero_invoice
        result.errors << "Invoice ##{invoice.number} - already exists in Xero"
        true
      else
        false
      end
    rescue => exception
      save_exception_errors_for(exception)
      false
    end
  end

  def push_invoice_to_xero
    begin
      @xero_invoice = xero_client.Invoice.build(
        type: CUSTOMER_INVOICE_TYPE,
        contact: contact,
        invoice_number: invoice.number,
        date: invoice.to_at,
        due_date: invoice.due_at,
        reference: customer_reference
      )

      invoice_orders.each do |order|
        add_line_items_for(order: order)
      end

      add_coupon_discounts
      add_surcharges
      set_invoice_path

      save_xero_invoice
    rescue => exception
      save_exception_errors_for(exception)
    end
  end

  def add_line_items_for(order:)
    order_lines_for(order).each do |order_line|
      add_line_item_for(order: order, order_line: order_line)
    end
    add_order_topup_for(order: order)
    add_delivery_fee_for(order: order)
  end

  def add_line_item_for(order:, order_line:)
    # SUPP-1039 always pass unit price with correct GST setting to xero so that GST won't be doubled up in xero generated invoices
    account_code = order_line.is_gst_free ? gst_free_account : non_gst_free_account
    unit_price = order_line.price_exc_gst(gst_country: order.symbolized_country_code)

    line_item = xero_invoice.add_line_item(description: "#{order_line.name} - ##{order.id}", quantity: order_line.quantity, unit_amount: unit_price, account_code: account_code)

    # add tracking category to the line item based on the order line's category name
    line_item.add_tracking(option: (order_line.category.present? ? order_line.category.name : ''), name: 'Sales Revenue')
    line_item.add_tracking(option: order_line.supplier_profile.name, name: 'Suppliers') if order_line.supplier_profile.present?
  end

  def add_order_topup_for(order:)
    return if order.customer_topup.blank? || order.customer_topup <= 0
    return if has_gst_split_invoicing? && order.has_gst_split_pos? && order.cpo_id == invoice_po.id # do not add topup for GST split orders and GST PO Invoice

    xero_invoice.add_line_item(description: "Topup - ##{order.id}", quantity: 1, unit_amount: order.customer_topup, account_code: gst_free_account)
  end

  def add_delivery_fee_for(order:)
    order_totals = totals.order_totals[order]
    return if order_totals.delivery.blank? || order_totals.delivery == 0

    account_code = order_totals.gst.blank? || order_totals.gst == 0 ? yordar_credentials(:xero, :gst_free_delivery_code) : yordar_credentials(:xero, :delivery_code)
    xero_invoice.add_line_item(description: "Delivery fee - ##{order.id}", quantity: 1, unit_amount: order_totals.delivery, account_code: account_code)
  end

  def add_coupon_discounts
    return if totals.discount.blank? || totals.discount.to_f <= 0

    # get the codes applied to each of the orders
    coupon_codes = invoice_orders.map{ |order| order.coupon.present? ? order.coupon.code : '' }.reject(&:blank?)
    xero_invoice.add_line_item(description: "#{'Coupon'.pluralize(coupon_codes.size)} - #{coupon_codes.join(', ')}", quantity: 1, unit_amount: (-1 * totals.discount), account_code: yordar_credentials(:xero, :discount_code))
  end

  def add_surcharges
    return if totals.surcharge.blank? || totals.surcharge.to_f <= 0

    country_code = invoice_orders.sample.symbolized_country_code
    gst_exc_surcharge = (totals.surcharge / (1.0 + + yordar_credentials(:yordar, :gst_percent, country_code))).round(2)
    xero_invoice.add_line_item(description: 'Surcharge', quantity: 1, unit_amount: gst_exc_surcharge, account_code: yordar_credentials(:xero, :surcharge_code))
  end

  def set_invoice_path
    invoice_path = invoice.latest_document(kind: 'tax_invoice')&.url
    xero_invoice.url = invoice_path if invoice_path.present?
  end

  def save_xero_invoice
    if xero_invoice.save
      invoice.update(pushed_to_xero: true)
      result.xero_invoice = xero_invoice
    else
      result.errors << 'We were unable to save the Invoice'
      result.errors += xero_invoice.errors
    end
  end

  def invoice_orders
    @_invoice_orders ||= invoice.invoice_orders.order(delivery_at: :asc, id: :asc)
  end

  def customer
    @_customer ||= invoice_orders.first.customer_profile
  end

  def customer_reference
    return '' if customer.blank?

    @_customer_reference ||= begin
      reference = customer.name
      reference += " [#{accounting_software_reference.upcase}]" if accounting_software_reference.present?
      reference
    end
  end

  def accounting_software_reference
    # do not send software reference if invoice order is paid by card
    if invoice_orders.size == 1 && !invoice_orders.first.credit_card&.pay_on_account?
      nil
    else
      customer.invoice_accounting_software
    end
  end

  def totals
    @_totals ||= Invoices::CalculateTotals.new(invoice: invoice).call
  end

  def order_lines_for(order)
    gst_split = nil
    if has_gst_split_invoicing? && order.gst_free_cpo_id.present?
      gst_split = order.gst_free_cpo_id == invoice_po.id ? 'GST-FREE' : 'GST'
    end

    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
      gst_split: gst_split
    }
    OrderLines::List.new(options: lister_options, includes: %i[supplier_profile category menu_item]).call
  end

  def invoice_po
    return @_invoice_po if defined? @_invoice_po

    @_invoice_po ||= invoice.customer_purchase_order
  end

  def has_gst_split_invoicing?
    return @_has_gst_split_invoicing if defined? @_has_gst_split_invoicing

    @_has_gst_split_invoicing ||= invoice_po.present? && invoice_orders.sample.customer_profile&.has_gst_split_invoicing
  end

  def gst_free_account
    @_gst_free_account ||= yordar_credentials(:xero, :customer_gst_free)
  end

  def non_gst_free_account
    @_non_gst_free_account ||= yordar_credentials(:xero, :customer_non_gst_free)
  end

  def save_exception_errors_for(exception)
    result.errors << "Could not upload Invoice #{invoice.number} (##{invoice.id}) to Xero"
    result.errors << exception.inspect
  end

  class Result
    attr_accessor :contact, :xero_invoice, :errors

    def initialize
      @contact = nil
      @xero_invoice = nil
      @errors = []
    end

    def success?
      errors.blank? && xero_invoice.present?
    end
  end

end
