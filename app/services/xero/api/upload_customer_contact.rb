class Xero::API::UploadCustomerContact < Xero::API::Base

  def initialize(customer:)
    @customer = customer
    @result = Result.new
  end

  def call
    if can_upload?
      build_new_contact if !has_existing_contact?
      if billing_details.present?
        update_billing_email if contact.email_address.blank?
        update_billing_details
      end
      save_contact
    end
    result
  end

private

  attr_reader :customer, :result
  attr_accessor :contact

  def can_upload?
    case
    when customer.blank? || customer_user.blank?
      result.errors << 'Cannot upload a missing contact'
    end
    result.errors.blank?
  end

  def has_existing_contact?
    existing_contact = find_contact_by_id.presence
    existing_contact ||= find_contact_by_name.presence

    if existing_contact.present?
      @contact = existing_contact
      true
    else
      false
    end
  end

  def find_contact_by_id
    xero_client.Contact.all(where: { contact_number: customer_user.id })[0]
  end

  def find_contact_by_name
    formatted_name_condition = format('Name.ToLower()=="%<name>s"', name: customer.customer_or_company_name.downcase.strip)
    xero_client.Contact.all(where: formatted_name_condition)[0]
  end

  def build_new_contact
    @contact = xero_client.Contact.build(
      name: customer.customer_or_company_name,
      contact_number: customer_user.id,
      first_name: customer_user.firstname,
      last_name: customer_user.lastname
    )
  end

  def update_billing_email
    billing_email = billing_details.email
    billing_email = billing_details.email.split(';').first.strip if billing_email.present? && billing_email.include?(';')
    contact.email_address = billing_email
  end

  def update_billing_details
    contact.add_address(**address_details)
    contact.add_phone(type: PHONE_TYPE, number: billing_details.phone) if billing_details.phone.present?
  end

  def customer_user
    @_customer_user ||= customer.user
  end

  def billing_details
    @_billing_details ||= customer.billing_details.presence
  end

  def address_details
    [street_address_details, suburb_details].inject(&:merge)
  end

  def street_address_details
    {
      type: ADDRESS_TYPE,
      line1: billing_details.address,
    }
  end

  def suburb_details
    billing_suburb = billing_details.suburb
    return {} if billing_suburb.blank?

    {
      line2: billing_suburb.name,
      postal_code: billing_suburb.postcode,
      region: billing_suburb.state
    }
  end

  def save_contact
    if contact.save
      result.contact = contact
      customer_user.update(xero_push_fault: false) if customer_user.xero_push_fault == true
    else
      result.errors << "Could not save contact for #{customer.id}"
      customer_user.update(xero_push_fault: true)
    end
  end

  class Result
    attr_accessor :contact, :errors

    def initialize
      @contact = nil
      @errors = []
    end

    def success?
      errors.blank? && contact.present?
    end
  end

end
