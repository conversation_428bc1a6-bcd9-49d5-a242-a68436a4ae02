class Xero::SyncInvoices

  GROUP_THRESHOLD = 20
  WAIT_TIME = 10 # seconds
  STATUS_MAP = {
    'PAID' => 'paid',
    'DELETED' => 'deleted',
    'VOIDED' => 'voided'
  }.freeze

  def initialize(xero_invoices: [], verbose: false)
    @xero_invoices = xero_invoices.presence || pending_xero_invoices
    @verbose = verbose
    @result = Result.new
  end

  def call
    number_of_interations = (xero_invoices.size / GROUP_THRESHOLD) + 1
    puts "Total Xero invoices: #{xero_invoices.size}" if verbose

    xero_invoices.in_groups_of(GROUP_THRESHOLD).each_with_index do |grouped_invoices, idx|
      syncable_xero_invoices = grouped_invoices.reject(&:blank?)

      puts "Interation #{idx + 1} of #{number_of_interations} => #{syncable_xero_invoices.size}" if verbose

      mapping_for(syncable_xero_invoices).each do |mapped_invoice|
        sync_with(mapped_invoice)
      end

      sleep(WAIT_TIME) if !Rails.env.test?
    end
    result
  end

private

  attr_reader :xero_invoices, :verbose, :result

  def sync_with(mapped_invoice)
    xero_api_invoice = mapped_invoice.xero_api_invoice
    xero_invoice = mapped_invoice.xero_invoice
    invoice = mapped_invoice.invoice
    case
    when xero_api_invoice.present? && invoice.present?
      invoice_status = STATUS_MAP[xero_api_invoice.status]
      status_updated = case
      when invoice_status.blank?
        false
      when ['voided', 'deleted'].include?(invoice_status)
        invoice.status != invoice_status && invoice.update(status: invoice_status)
      else
        invoice.payment_status != invoice_status && invoice.update(payment_status: invoice_status)
      end
      if status_updated
        result.synced_invoices << invoice
      end
      xero_invoice.destroy
    when xero_api_invoice.present? && xero_api_invoice.type == Xero::API::Base::CUSTOMER_INVOICE_TYPE
      error_message = "Could not sync Xero Invoice #{xero_api_invoice.invoice_id} with invoice ##{xero_api_invoice.invoice_number}"
      xero_invoice.update(failed_at: Time.zone.now, last_errors: error_message)
      result.errors << error_message
    when xero_api_invoice.blank?
      error_message = "Could not find an API Xero Invoice for #{xero_invoice.id}"
      xero_invoice.update(failed_at: Time.zone.now, last_errors: error_message)
      result.errors << error_message
    else
      xero_invoice.destroy
    end
  end

  def mapping_for(xero_invoices)
    begin
      Xero::API::FetchInvoicesByIds.new(xero_invoices: xero_invoices).call
    rescue
      result.errors << "Could not get mapping for #{xero_invoices.map(&:id).join(' - ')}"
      []
    end
  end

  def pending_xero_invoices
    Xero::Invoice.where(failed_at: nil)
  end

  class Result
    attr_accessor :synced_invoices, :errors

    def initialize
      @synced_invoices = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
