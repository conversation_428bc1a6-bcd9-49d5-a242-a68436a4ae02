class Xero::HandleWebhookEvent

  def initialize(event: {})
    @event = event
    @result = Result.new
  end

  def call
    if can_handle_event?
      create_xero_invoice
    end
    result
  end

private

  attr_reader :event, :result

  def can_handle_event?
    case
    when event.blank?
      result.errors << 'Cannot handle a missing webhook event'
    when event['eventCategory'] != 'INVOICE' || event['eventType'] != 'UPDATE'
      result.errors << "Cannot handle this event - #{event['eventCategory']} - #{event['eventType']}"
    end
    result.errors.blank?
  end

  def create_xero_invoice
    xero_invoice = Xero::Invoice.where(invoice_id: event['resourceId']).first_or_initialize
    can_notify = !xero_invoice.persisted?.dup && yordar_credentials(:xero, :notify_developer)
    if xero_invoice.save
      result.xero_invoice = xero_invoice
      notify_event if can_notify
    else
      result.errors += xero_invoice.errors.full_messages
    end
  end

  def notify_event
    Xero::NotifyWebhookEvent.new(event: event, xero_invoice: result.xero_invoice).call
  end

  class Result
    attr_accessor :xero_invoice, :errors

    def initialize
      @xero_invoice = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
