class Xero::NotifyWebhookEvent

  def initialize(event:, xero_invoice:)
    @event = event
    @xero_invoice = xero_invoice
  end

  def call
    SlackNotifier.send(message, attachments: slack_attachments) if slack_attachments.present?
  end

private

  attr_reader :event, :xero_invoice

  def message
    ':bank: Xero - An Invoice event was pushed to Yordar'
  end

  def slack_attachments
    @_slack_attachments ||= [event_details, xero_invoice_details].reject(&:blank?).map do |text|
      {
        type: 'mrkdwn',
        text: text,
        color: 'warning'
      }
    end
  end

  def event_details
    return nil if event.blank?

    "Event: #{event['eventCategory']} - #{event['eventType']} => #{event['resourceId']}"
  end

  def xero_invoice_details
    return nil if xero_invoice.blank?

    "Xero Invoice: #{xero_invoice.id} - #{xero_invoice.invoice_id} - #{xero_invoice.updated_at.to_s(:full)}"
  end

end
