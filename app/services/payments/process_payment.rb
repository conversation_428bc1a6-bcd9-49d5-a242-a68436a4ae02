class Payments::ProcessPayment

  def initialize(payment:)
    @payment = payment
    @result = Result.new(payment: payment)
  end

  def call
    if can_pay?
      payment.update(payment_attributes)
    end
    result
  end

private

  attr_reader :payment, :result

  def can_pay?
    case
    when payment.response_text.present?
      result.errors << 'Payment is already processed'
    when payment_card.stripe_token.blank? && payment_card.gateway_token.blank?
      result.errors << 'Cannot pay with invalid credit card'
    end
    result.errors.blank?
  end

  def payment_card
    @_payment_card ||= payment.credit_card
  end

  def payment_attributes
    third_party_payment = case
    when payment_card.stripe_token.present?
      pay_via_stripe
    when payment_card.gateway_token.present?
      pay_via_eway
    end
    payment_attributes = { response_text: third_party_payment.response_message }
    if third_party_payment.success?
      payment_attributes[:auth_code] = third_party_payment.auth_code
      payment_attributes[:transaction_number] = third_party_payment.transaction_number
    else
      result.errors += third_party_payment.errors
    end
    payment_attributes
  end

  def pay_via_eway
    Eway::ProcessPayment.new(payment: payment).call
  end

  def pay_via_stripe
    customer = payment.invoice.invoice_orders.first.customer_profile
    Stripe::ProcessPayment.new(customer: customer, payment: payment).call
  end

  class Result
    attr_accessor :payment, :errors

    def initialize(payment:)
      @payment = payment
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
