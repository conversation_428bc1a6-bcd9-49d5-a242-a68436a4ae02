class MenuSections::Archive

  def initialize(menu_section:, is_forced: false)
    @menu_section = menu_section
    @is_forced = is_forced
    @result = Result.new(menu_section: menu_section)
  end

  def call
    return result if !can_archive?

    if menu_section.update(archived_at: Time.zone.now)
      update_supplier_category_group_flags

      if is_forced && unarchived_menu_items.present?
        archive_internal_items
      end
    end
    result
  end

private

  attr_accessor :menu_section, :is_forced, :result

  def can_archive?
    case
    when menu_section.blank?
      result.errors << 'Cannot archive a missing Menu Section'
    when menu_section.archived_at.present?
      result.errors << 'Cannot archvie an already archived Menu Section'
    when !is_forced && unarchived_menu_items.present?
      result.errors << 'Menu Sections can only be archived/removed after archiving all associated menu items'
    end
    result.errors.blank?
  end

  def unarchived_menu_items
    @_unarchived_menu_items ||= menu_section.menu_items.where(archived_at: nil).where(supplier_profile: menu_section.supplier_profile)
  end

  def archive_internal_items
    unarchived_menu_items.each do |menu_item|
      MenuItems::Archive.new(menu_item: menu_item, forced: is_forced).call
    end
  end

  def update_supplier_category_group_flags
    supplier = menu_section.supplier_profile
    return if supplier.blank?

    Suppliers::Cache::CategoryGroups.new(suppliers: [supplier]).delay(queue: :data_integrity).call
  end

  class Result
    attr_reader :menu_section, :errors

    def initialize(menu_section:)
      @menu_section = menu_section
      @errors = []
    end

    def success?
      errors.blank? && menu_section.reload.archived_at.present?
    end
  end

end
