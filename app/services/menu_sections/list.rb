class MenuSections::List

  def initialize(options: {}, includes: [])
    @options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
    @includes = includes
  end

  def call
    @menu_sections = base
    filter_by_suppliers if suppliers.present?
    filter_by_visibility if options[:show_visible].present?
    filter_by_active if options[:show_active].present?
    filter_by_custom_name if options[:ignore_custom].present?
    filter_for_home_deliveries
    filter_by_profile_visibility

    order_menu_section if options[:order_by].present?

    @menu_sections
  end

private

  attr_reader :includes, :options
  attr_accessor :menu_sections

  def base
    MenuSection.all.includes(includes)
  end

  def filter_by_suppliers
    @menu_sections = menu_sections.where(supplier_profile: suppliers)
  end

  def filter_by_visibility
    @menu_sections = menu_sections.where(is_hidden: false)
  end

  def filter_by_active
    @menu_sections = menu_sections.where(archived_at: nil)
  end

  def filter_by_custom_name
    @menu_sections = menu_sections.where.not('menu_sections.name ilike ?', 'custom')
  end

  def filter_for_home_deliveries
    if options[:is_home_delivery]
      @menu_sections = menu_sections.joins(:categories).where(categories: { group: 'home-deliveries' })
    else
      @menu_sections = menu_sections.eager_load(:categories).where("categories.id is NULL or categories.group != 'home-deliveries'")
    end
  end

  def filter_by_profile_visibility
    case
    when options[:profile].present? && suppliers.present? && suppliers.size == 1 && suppliers.first == options[:profile]
      # do nothing as retrieving menu for suppplier
    when options[:profile].present?
      @menu_sections = menu_sections.includes(:companies).where(companies: { id: [nil, options[:profile].try(:company_id)] }) # .distinct
    else
      @menu_sections = menu_sections.includes(:companies).where(companies: { id: nil })
    end
  end

  def suppliers
    (options[:suppliers].presence || [options[:supplier]]).compact
  end

  def order_menu_section
    @menu_sections = menu_sections.order(options[:order_by])
  end

  def default_options
    {
      supplier: nil,
      suppliers: [],
      show_visible: false,
      show_active: false,
      ignore_custom: false,
      is_home_delivery: false,
      profile: nil,
      order_by: nil,
    }
  end

end
