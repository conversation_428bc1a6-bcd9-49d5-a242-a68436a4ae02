class MenuSections::Upsert

  def initialize(menu_section_params:, menu_section: nil, forced: nil)
    @is_forced = forced.presence || false
    @menu_section_params = menu_section_params.present? ? menu_section_params.to_h.symbolize_keys : {}
    @menu_section = menu_section.presence || fetch_menu_section
    @result = Result.new
  end

  def call
    if menu_section.update(sanitized_params)
      result.menu_section = menu_section
      update_supplier_category_group_flags
    else
      result.errors += menu_section.errors.full_messages
    end
    result
  end

private

  attr_reader :menu_section, :menu_section_params, :is_forced, :result

  def fetch_menu_section
    retrieval_params = {
      name: sanitized_name_params[:name],
      group_name: menu_section_params[:group_name],
      supplier_profile: supplier,
    }
    is_forced ? MenuSection.new(retrieval_params) : MenuSection.where(retrieval_params).first_or_initialize
  end

  def sanitized_params
    [
      default_params,
      sanitized_section_params,
      sanitized_name_params
    ].inject(&:merge)
  end

  def default_params
    return {} if !menu_section.new_record?

    {
      weight: menu_section_params[:weight] || menu_section.weight || max_weight
    }
  end

  def sanitized_section_params
    params = menu_section_params
    params[:category_ids] = params[:category_ids].reject(&:blank?) if params[:category_ids].present?
    params
  end

  def sanitized_name_params
    return {} if menu_section_params[:name].blank?

    {
      name: menu_section_params[:name].strip,
    }
  end

  def supplier
    @_supplier ||= case
    when menu_section_params[:supplier_profile].present?
      menu_section_params[:supplier_profile]
    when supplier_profile_id = menu_section_params[:supplier_profile_id].presence
      SupplierProfile.where(id: supplier_profile_id).first
    when menu_section.present?
      menu_section.supplier_profile
    end
  end

  def max_weight
    weight = case
    when supplier.present?
      supplier.menu_sections.where.not(name: 'custom').where(archived_at: nil).pluck(:weight).compact.max
    else
      MenuSection.pluck(:weight).compact.max
    end
    (weight.presence || 0) + 1
  end

  def update_supplier_category_group_flags
    return if supplier.blank?

    Suppliers::Cache::CategoryGroups.new(suppliers: [supplier]).delay(queue: :data_integrity).call
  end

  class Result
    attr_accessor :menu_section, :errors

    def initialize
      @menu_section = nil
      @errors = []
    end

    def success?
      errors.blank? && menu_section.present? && menu_section.valid? && menu_section.persisted?
    end
  end

end
