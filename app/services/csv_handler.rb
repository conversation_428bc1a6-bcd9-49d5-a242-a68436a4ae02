# http://www.lugolabs.com/articles/17-handling-csv-views-in-ruby-on-rails
require 'csv'

module CsvHandler

  class CsvGenerator
    def self.generate
      file = CSV.generate do |csv|
        yield csv
      end
      file.html_safe
    end
  end

  class Handler
    # arguments ate view object (template) and the source for the view object
    # as per https://apidock.com/rails/v6.0.0/ActionView/Template/Handlers/register_template_handler
    def self.call(template, _)
      %(
        CsvHandler::CsvGenerator.generate do |csv|
          #{template.source}
        end
      )
    end

  end

end
