 class Promotions::CalculateDiscount

  def initialize(promotion:, amount:)
    @promotion = promotion
    @amount = amount
  end

  def call
    {
      amount: amount,
      discount: discount,
      total: [0, amount - discount].max,
    }
  end

private

  attr_reader :promotion, :amount

  def discount
    return 0 if promotion.blank?

    @_discount ||= promotion.kind == 'percentage' ? (amount * promotion.amount / 100) : promotion.amount
  end

end
