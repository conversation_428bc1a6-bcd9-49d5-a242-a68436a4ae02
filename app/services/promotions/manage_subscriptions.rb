class Promotions::ManageSubscriptions

  def initialize(promotion:, subscriptions: [])
    @promotion = promotion
    @subscriptions = subscriptions
    @result = Result.new(promotion: promotion)
  end

  def call
    if can_manage?
      subscriptions.each do |subscription|
        case
        when subscription[:id].present? && subscription[:_delete].present?
          remove_subscription_for(subscription: subscription)
        when subscription[:id].present?
          update_subscription_for(subscription: subscription)
        else
          create_subscription_for(subscription: subscription)
        end
      end
    end
    result
  end

private

  attr_reader :promotion, :subscriptions, :result


  def can_manage?
    case
    when promotion.blank?
      result.errors << 'Cannot manage subscriptions without a promotion'
    end
    result.errors.blank?
  end

  def remove_subscription_for(subscription:)
    promotion_subscription = promotion.subscriptions.where(id: subscription[:id]).first
    if !promotion_subscription.destroy
      result.errors << "Could not remove subscription with ID: #{subscription[:id]}" # " - #{permission.errors.full_messages.join(', ')}"
    end
  end

  def update_subscription_for(subscription:)
    promotion_subscription = promotion.subscriptions.where(id: subscription[:id]).first
    if promotion_subscription.update(subscription.except(:id))
      result.subscriptions << promotion_subscription
    else
      result.errors << "Could not update subscription with ID: #{subscription[:id]}" # " - #{permission.errors.full_messages.join(', ')}"
    end
  end

  def create_subscription_for(subscription:)
    subscriber = subscriber_for(subscription: subscription)
    promotion_subscription = promotion.subscriptions.where(subscriber: subscriber).first_or_initialize
    if promotion_subscription.update(subscription.except(:id))
      result.subscriptions << promotion_subscription
    else
      result.errors << "Could not create subscription with subscriber: #{subscription[:subscriber_type]} - #{subscription[:subscriber_id]}" #" - #{permission.errors.full_messages.join(', ')}"
    end
  end

  def subscriber_for(subscription:)
    return nil if subscription[:subscriber_id].blank?

    case subscription[:subscriber_type]
    when 'CustomerProfile'
      CustomerProfile.where(id: subscription[:subscriber_id]).first
    when 'Company'
      Company.where(id: subscription[:subscriber_id]).first
    else
      nil
    end
  end

  class Result
    attr_accessor :subscriptions, :errors
    attr_reader :promotion

    def initialize(promotion:)
      @promotion = promotion
      @subscriptions = []
      @errors = []
    end

    def success?
      @errors.blank?
    end
  end

end