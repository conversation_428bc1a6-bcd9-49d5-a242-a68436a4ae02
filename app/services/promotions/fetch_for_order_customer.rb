class Promotions::FetchForOrderCustomer

  def initialize(order:, customer: nil)
    @order = order
    @customer = customer.presence || order.customer_profile
    @promotion = nil
  end

  def call
    if customer.present? && order.delivery_at.present?
      fetch_customer_promotion
      fetch_company_promotion if promotion.blank?
    end

    promotion
  end

private

  attr_reader :order, :promotion

  def customer
    @_customer ||= order.customer_profile
  end

  def valid_promotions
    @valid_promotions ||= begin
      date = order.delivery_at&.to_date || Date.today
      promotions = Promotion.where(active: true)
      promotions = promotions.where('valid_from <= :date AND (valid_until IS NULL OR valid_until >= :date)', date: date)
      promotions.joins(:subscriptions).where(promotion_subscriptions: { active: true })
    end
  end

  def fetch_customer_promotion
    customer_promotions = valid_promotions.where(promotion_subscriptions: { subscriber: customer }).order(created_at: :desc)
    @promotion = customer_promotions.where(category_restriction: order_category_group).first if order_category_group.present?
    @promotion ||= customer_promotions.where(category_restriction: nil).first
  end

  def fetch_company_promotion
    company = customer.company
    return if company.blank?

    company_promotions = valid_promotions.where(promotion_subscriptions: { subscriber: company }).order(created_at: :desc)
    @promotion = company_promotions.where(category_restriction: order_category_group).first if order_category_group.present?
    @promotion ||= company_promotions.where(category_restriction: nil).first
  end

  def order_category_group
    @_order_category_group ||= Orders::RetrieveMajorOrderCategory.new(order: order).call&.group
  end

end