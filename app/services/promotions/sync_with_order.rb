class Promotions::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(order:, customer: nil)
    @order = order
    @customer = customer.presence || order&.customer_profile
    @result = Result.new(order: order)
  end

  def call
    if can_attach? && order_customer_promotion.present?
      attach_promotion_to_order
      recalculate_totals
    end
    result
  end

private

  attr_reader :order, :customer, :result

  def can_attach?
    case
    when order.blank?
      result.errors << 'Cannot attach promotion to a missing order'
    when customer.blank?
      result.errors << 'Cannot attach promotion without knowing the customer'
    when order.promotion.present? && order.promotion == order_customer_promotion
      result.errors << 'Order is already attached to a promotion'
    end
    result.errors.blank?
  end

  def order_customer_promotion
    @_order_customer_promotion ||= Promotions::FetchForOrderCustomer.new(order: order).call
  end

  def attach_promotion_to_order
    if order.update(promotion: order_customer_promotion)
      result.promotion = order_customer_promotion
    end
  end

  def recalculate_totals
    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
  end

  class Result
    attr_accessor :promotion, :errors

    attr_reader :order

    def initialize(order:)
      @order = order
      @promotion = promotion
      @errors = []
    end

    def success?
      errors.blank? && order&.promotion&.present?
    end
  end

end
