class Docusign::GenerateSupplierAgreement < Docusign::API

  ENVELOPE_STATUSES = %w[sent delivered completed declined voided].freeze

  def initialize(supplier:)
    @supplier = supplier
    @result = Result.new
  end

  def call
    if can_generate?
      begin
        login # within Docusign::API, sets account_id
        send_envelope
        add_document_to_supplier
      rescue DocuSign_eSign::ApiError => exception
        error_message = "Exception when calling EnvelopesApi->create_envelope for supplier with ID #{supplier.id}"
        Raven.capture_exception(exception,
          message: error_message,
          extra: { supplier: supplier.id },
          transaction: 'Docusign::GenerateSupplierAgreement'
        )
        result.errors << error_message
      end
    end
    result
  end

private

  attr_reader :supplier, :result

  def can_generate?
    case
    when !docusign_configured?
      result.errors << 'Cannot access the document server'
    when supplier.blank? || supplier_email.blank?
      result.errors << 'Cannot generate a document wihtout a supplier'
    end
    result.errors.blank?
  end

  def send_envelope
    result.docusign_document = api_instance.create_envelope(account_id, envelope_definition) # within DocuSign::Api
  end

  def envelope_definition
    DocuSign_eSign::EnvelopeDefinition.new(
      emailSubject: "Yordar - Supplier Agreement #{Time.zone.now.year}",
      templateId: yordar_credentials(:docusign, :template_id),
      templateRoles: [yordar_role, supplier_role],
      eventNotification: event_notification,
      status: 'sent'
    )
  end

  def yordar_role
    DocuSign_eSign::TemplateRole.new(
      email: yordar_credentials(:docusign, :yordar_email),
      name: yordar_credentials(:docusign, :yordar_name),
      roleName: yordar_credentials(:docusign, :yordar_role_name),
      tabs: DocuSign_eSign::Tabs.new(
        textTabs: text_tabs
      )
    )
  end

  def text_tabs
    docusign_fields = yordar_credentials(:docusign, :field_list).present? ? yordar_credentials(:docusign, :field_list).split('|') : []
    docusign_fields.map do |field|
      if supplier.respond_to?(field)
        DocuSign_eSign::Text.new(tabLabel: field, value: supplier.public_send(field))
      end
    end.compact
  end

  def supplier_role
    DocuSign_eSign::TemplateRole.new(
      email: supplier_email,
      name: supplier.name,
      roleName: yordar_credentials(:docusign, :supplier_role_name)
    )
  end

  def supplier_email
    supplier_email = supplier.email
    supplier_email.present? ? supplier_email.split(/;|,|\r|\n/).first.strip : nil
  end

  def event_notification
    if yordar_credentials(:docusign, :webhook_url)
      DocuSign_eSign::EventNotification.new(
        logging: true,
        envelopeEvents: ENVELOPE_STATUSES.map do |envelope_status|
          DocuSign_eSign::EnvelopeEvent.new(
            envelopeEventStatusCode: envelope_status
          )
        end,
        url: yordar_credentials(:docusign, :webhook_url)
      )
    else
      DocuSign_eSign::EventNotification.new
    end
  end

  def add_document_to_supplier
    envelope = result.docusign_document
    return if envelope.blank?

    result.agreement_document = supplier.supplier_agreement_documents.create!(docusign_envelope_id: envelope.envelope_id.downcase, status: envelope.status.downcase)
  end

  class Result
    attr_accessor :docusign_document, :agreement_document, :errors

    def initialize
      @docusign_document = nil
      @agreement_document = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
