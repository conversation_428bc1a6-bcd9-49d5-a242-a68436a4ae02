class Docusign::FetchAgreementDocument < Docusign::API

  def initialize(envelope_id:)
    @envelope_id = envelope_id
    @result = Result.new
  end

  def call
    if can_fetch?
      begin
        login # within Docusign::API, sets @account_id
        setup_envelope
      rescue DocuSign_eSign::ApiError => exception
        error_message = "Exception when calling EnvelopesApi->get_document for envelope_id = #{envelope_id}"
        Raven.capture_exception(exception,
          message: error_message,
          extra: { envelope_id: envelope_id },
          transaction: 'Docusign::FetchAgreementDocument'
        )
        result.errors << error_message
      end
    end
    result
  end

private

  attr_reader :envelope_id, :result

  def can_fetch?
    case
    when !docusign_configured?
      result.errors << 'Cannot access the document server'
    when envelope_id.blank?
      result.errors << 'Cannot fetch a document without an id'
    end
    result.errors.blank?
  end

  def setup_envelope
    result.docusign_document = api_instance.get_document(account_id, 'COMBINED', envelope_id) # within Docusign::API
  end

  class Result
    attr_accessor :docusign_document, :errors

    def initialize
      @docusign_document = nil
      @errors = []
    end

    def success?
      errors.blank? && docusign_document.present?
    end
  end

end

