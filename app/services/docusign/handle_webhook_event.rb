class Docusign::HandleWebhookEvent

  def initialize(event:)
    @event = event
    @result = Result.new
  end

  def call
    if can_handle? && update_document_status
      result.document = document
      log_event
    end

    result
  end

private

  attr_reader :event, :result

  def can_handle?
    case
    when event.blank?
      result.errors << 'Missing webhook event'
    when document.blank?
      result.errors << 'Could not find document'
    end
    result.errors.blank?
  end

  def document
    @_document = SupplierAgreementDocument.where(docusign_envelope_id: envelope_id).first
  end

  def update_document_status
    document.update(status: event_status)
  end

  def envelope_status
    event['DocuSignEnvelopeInformation']['EnvelopeStatus']
  end

  def envelope_id
    envelope_status['EnvelopeID'].downcase
  end

  def event_status
    envelope_status['Status'].downcase
  end

  def log_event
    return if event_status != 'completed'

    supplier = document.supplier_profile
    EventLogs::Create.new(event_object: supplier, event: 'supplier-agreement-signed').delay(queue: :notifications).call
  end

  class Result
    attr_accessor :document, :errors

    def initialize
      @document = nil
      @errors = []
    end

    def success?
      errors.blank? && document.present?
    end
  end

end