class Docusign::API

  EXPIRES_IN_SECONDS = 60 * 60

  def docusign_configured?
    yordar_credentials(:docusign, :rest_host).present?
  end

  def login
    login_options = DocuSign_eSign::LoginOptions.new
    login_information = authentication_api.login(login_options)
    account = login_information.login_accounts.find do |login|
      login.is_default = 'true'
    end
    @account_id = account.account_id
    update_api_client(account.base_url)
  end

  def api_instance
    DocuSign_eSign::EnvelopesApi.new(api_client)
  end

private

  attr_reader :account_id

  def authentication_api
    DocuSign_eSign::AuthenticationApi.new(api_client)
  end

  def api_client
    @api_client ||= DocuSign_eSign::ApiClient.new(
      DocuSign_eSign::Configuration.new.tap do |config|
        config.host = yordar_credentials(:docusign, :rest_host)
      end
    ).tap do |api_client|
      api_client.configure_jwt_authorization_flow(
        private_key_filename,
        yordar_credentials(:docusign, :auth_server),
        yordar_credentials(:docusign, :integrator_key),
        yordar_credentials(:docusign, :user_id),
        EXPIRES_IN_SECONDS
      )
    end
  end

  # IMPORTANT: Use the base url from the login account to update the
  # api client which will be used in future api calls
  def update_api_client(base_url)
    base_uri = URI.parse(base_url)
    api_client.config.host = format('%<uri_scheme>s://%<host>s/restapi', uri_scheme: base_uri.scheme, host: base_uri.host)
  end

  def private_key_filename
    tempfile = Tempfile.new('')
    tempfile.write(yordar_credentials(:docusign, :private_key))
    tempfile.close
    tempfile.path
  end

end
