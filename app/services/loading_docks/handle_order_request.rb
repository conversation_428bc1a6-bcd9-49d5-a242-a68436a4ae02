class LoadingDocks::HandleOrderRequest

  def initialize(order:, request_uuid:)
    @order = order
    @request_uuid = request_uuid
    @result = Result.new
  end

  def call
    if can_handle?
      result.order = order
      result.customer = customer
    end
    result
  end

private

  attr_reader :order, :request_uuid, :result

  def can_handle?
    case
    when order.blank? || customer.blank?
      result.errors << 'Could not find order'
    when !valid_url_hash?
      result.errors << 'Invalid Request'
    end
    result.errors.blank?
  end

  def valid_url_hash?
    Digest::MD5.hexdigest(order.id.to_s + customer.id.to_s + yordar_credentials(:random_salt)) == request_uuid
  end

  def customer
    @_customer ||= order.customer_profile
  end

  class Result
    attr_accessor :order, :customer, :errors

    def initialize
      @order = nil
      @customer = nil
      @errors = []
    end

    def success?
      errors.blank? && order.present? && customer.present?
    end
  end

end
