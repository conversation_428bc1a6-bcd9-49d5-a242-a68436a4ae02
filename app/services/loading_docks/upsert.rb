class LoadingDocks::Upsert

  def initialize(loading_dock_params: {}, loading_dock: nil, order: nil)
    @loading_dock = loading_dock
    @loading_dock_params = loading_dock_params
    @order = order
    @result = Result.new
  end

  def call
    if can_upsert?
      if loading_dock.present?
        update_loading_dock
      else
        create_new_loading_dock
      end
      attach_to_order
    end
    result
  end

private

  attr_reader :loading_dock, :loading_dock_params, :order, :result

  def can_upsert?
    case
    when customer.blank?
      result.errors << 'Cannot save loading dock without a customer'
    when loading_dock.present? && loading_dock.customer_profile != customer
      result.errors << 'You do not have access to this loading dock'
    when order.present? && order.customer_profile != customer
      result.errors << 'You do not have access to this order'
    end
    result.errors.blank?
  end

  def update_loading_dock
    if loading_dock.update(sanitized_params)
      result.loading_dock = loading_dock
    else
      result.errors += loading_dock.errors.full_messages
    end
  end

  def create_new_loading_dock
    loading_dock = customer.loading_docks.new(sanitized_params)
    if loading_dock.save
      result.loading_dock = loading_dock
    else
      result.errors += loading_dock.errors.full_messages
    end
  end

  def attach_to_order
    return if order.blank? || result.loading_dock.blank?

    order.update(loading_dock: result.loading_dock)
    notify_suppliers
  end

  def notify_suppliers
    order.supplier_profiles.each do |supplier|
      Suppliers::Emails::SendLoadingDockEmail.new(order: order, supplier: supplier).delay(queue: :notifications).call
    end
  end

  def sanitized_params
    loading_dock_params.to_h.symbolize_keys.except(:id, :customer_profile_id)
  end

  def customer
    return nil if loading_dock_params[:customer_profile_id].blank?

    @customer ||= CustomerProfile.where(id: loading_dock_params[:customer_profile_id]).first
  end

  class Result
    attr_accessor :loading_dock, :errors

    def initialize
      @loading_dock = nil
      @errors = []
    end

    def success?
      errors.blank? && loading_dock.present? && loading_dock.persisted?
    end
  end

end