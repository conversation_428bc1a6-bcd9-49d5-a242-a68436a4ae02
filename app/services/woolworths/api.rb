class Woolworths::API

  BASE_URL = "https://#{yordar_credentials(:woolworths, :host)}/wow".freeze

  # cache keys
  DELIVERY_WINDOWS_CACHE_KEY = 'woolworths-order-:id-delivery-windows'.freeze

  # authorization endpoints
  SIGNUP_ENDPOINT = 'v2/commerce/signup'.freeze
  AUTHENTICATION_ENDPOINT = 'v2/commerce/token'.freeze

  # Order level endpoints
  VALIDATE_ENDPOINT = 'v2/commerce/checkout/payment'.freeze
  CHECKOUT_ENDPOINT = 'v2/commerce/checkout/payment/directdebit'.freeze

  # trolley level endpoints
  TROLLEY_ENDPOINT = 'v2/trolley'.freeze
  EMPTY_TROLLEY_ENDPOINT = 'v2/trolley/clear'.freeze
  TROLLEY_ITEMS_ENDPOINT = 'v2/trolley/items'.freeze

  # delivery level endpoints
  ADDRESS_ENDPOINT = 'v2/addresses'.freeze
  DELIVERY_ADDRESS_ENDPOINT = 'v2/fulfilment'.freeze
  DELIVERY_INSTRUCTIONS_ENPOINT = 'v2/fulfilment/instructions'.freeze
  DELIVERY_WINDOWS_ENDPOINT = 'v2/fulfilment/windows'.freeze
  DELIVERY_WINDOW_ENDPOINT = 'v2/fulfilment/window'.freeze

  # import endpoints
  CATEGORIES_ENDPOINT = 'v3/categories'.freeze
  PRODUCTS_ENDPOINT = 'v2/products'.freeze

end
