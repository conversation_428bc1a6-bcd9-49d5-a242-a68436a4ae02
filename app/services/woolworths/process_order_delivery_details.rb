class Woolworths::ProcessOrderDeliveryDetails
  class NoDeliveryWindowError < StandardError; end

  def initialize(order:)
    @order = order
  end

  # This service class runs after the customer submits the delivery details
  # modal on the Woolworths supplier menu page. At this point, the order has
  # an account assigned to it, as well as all the delivery details. So this
  # class sets up the "fulfilment", which is the delivery address, window, etc
  # on the Woolworths account.
  def call
    return if order.woolworths_account.blank?    

    Woolworths::Order::SyncDeliveryAddress.new(order: order, connection: connection).call

    Woolworths::API::SetDeliveryInstructions.new(order: order, connection: connection).call

    Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: connection).call
  end

private

  attr_reader :order

  def connection
    @_connection ||= Woolworths::API::Connection.new(account: order.woolworths_account, authenticated: true)
  end

end
