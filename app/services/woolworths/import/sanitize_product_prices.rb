# sanitizes the product pricing of always available items to match the ones which are updated from API
class Woolworths::Import::SanitizeProductPrices

  def initialize(supplier:, dry_run: false)
    @supplier = supplier
    @dry_run = dry_run
  end

  def call
    always_available_items.each do |menu_item|
      if dry_run
        puts
        puts "#{menu_item.id} - #{menu_item.name} - #{menu_item.price} - #{menu_item.promo_price || 'nil'}"
      end
      sync_with_simlar_item_for(menu_item)
    end;nil
  end

private

  attr_reader :supplier, :dry_run

  def always_available_items
    supplier.menu_items.where(id: always_available_item_ids)
  end

  def sync_with_simlar_item_for(menu_item)
    split_name = menu_item.name.split(' ')
    name_start = split_name.first(4).join(' ')
    name_end = split_name.last(2).join(' ')

    similar_items = supplier.menu_items.where.not(id: always_available_item_ids)
    similar_items = similar_items.joins(:woolworths_store_availabilities).where.not(woolworths_store_availabilities: { id: nil })
    similar_items = similar_items.where('name ilike ?', "%#{name_start}%#{name_end}%")
    similar_items = similar_items.distinct
    return if similar_items.blank?

    puts "Similar Items: #{similar_items.map(&:name).join(' | ')}" if dry_run

    promo_items = similar_items.select{|item| item.promo_price.present? }
    best_price_item = if promo_items.present?
      promo_items.min_by(&:promo_price)
    else
      similar_items.min_by(&:price)
    end

    puts "Best Item: #{best_price_item.name} - #{best_price_item.price} - #{best_price_item.promo_price}" if dry_run

    menu_item.update(price: best_price_item.price, promo_price: best_price_item.promo_price) if !dry_run
  end

  def always_available_item_ids
    @_always_available_item_ids ||= Rails.configuration.woolworths.category_mappings.always_available_item_ids
  end
end
