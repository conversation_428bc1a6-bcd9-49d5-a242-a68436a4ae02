class Woolworths::Import::RetrieveMappedCategories

  SECTION_CATEGTORIES = {
    'Bakery' => 'Office Bread',
    'Beer, Wine & Spirits' => 'Office Alcohol',
    'Drinks' => 'Office Drinks',
    'Eggs, Dairy & Fridge' => 'Office Milk',
    'Fruit & Veg' => 'Office Fruit',
    'Confectionery & Snacks' => 'Office Snacks & Pantry',
    'Freezer' => 'Office Snacks & Pantry',
    'Health Food' => 'Office Snacks & Pantry',
    'Pantry & International Food' => 'Office Snacks & Pantry',
    'Spreads, Breakfast, Baking & Desserts' => 'Office Snacks & Pantry',
    'Bags, Food Wraps & Cleaning' => 'Cleaning Supplies',
    'Toiletries, Beauty, Health & Wellbeing' => 'Cleaning Supplies',
  }.freeze

  CategoryMapping = Struct.new(:woolworths_category, :menu_section_id, :gst_free)

  def initialize(connection:, supplier:, is_reimport: false, is_verbose: false)
    @connection = connection
    @supplier = supplier
    @is_reimport = is_reimport
    @is_verbose = is_verbose
  end

  def call
    mapped_categories = woolworths_categories.map do |woolworths_category|
      next if woolworths_category.aisle_name.downcase.in?(excluded_aisles)
      next if woolworths_category.category_name.downcase.in?(excluded_categories)
      next if is_reimport && !woolworths_category.category_name.downcase.in?(reimport_categories)

      gst_free = woolworths_category.category_name.downcase.in?(gst_free_categories)

      sanitized_name = woolworths_category.category_name.titleize
      sanitized_aisle_name = woolworths_category.aisle_name.titleize

      menu_section_params = {
        name: sanitized_name,
        group_name: sanitized_aisle_name,
        supplier_profile: supplier,
        # category_ids: category_ids_for(sanitized_aisle_name),
      }
      section_upserter = MenuSections::Upsert.new(menu_section_params: menu_section_params).call
      yordar_menu_section = section_upserter.menu_section
      puts "#{sanitized_aisle_name} - #{sanitized_name} (#{yordar_menu_section.id})" if is_verbose

      CategoryMapping.new(woolworths_category, yordar_menu_section.id, gst_free)
    end

    mapped_categories.compact
  end

private

  attr_reader :connection, :supplier, :is_reimport, :is_verbose

  def woolworths_categories
    Woolworths::Import::GetCategories.new(connection: connection).call
  end

  def excluded_aisles
    @_excluded_aisles ||= Rails.configuration.woolworths.category_mappings.exclusions[:aisles].map(&:downcase)
  end

  def excluded_categories
    @_excluded_categories ||= Rails.configuration.woolworths.category_mappings.exclusions[:categories].map(&:downcase)
  end

  def reimport_categories
    @_reimport_categories ||= Rails.configuration.woolworths.category_mappings.reimport[:categories].map(&:downcase)
  end

  def gst_free_categories
    @_gst_free_categories ||= Rails.configuration.woolworths.category_mappings.gst_free_categories.map(&:downcase)
  end

  def category_ids_for(aisle_name)
    section_category = SECTION_CATEGTORIES[aisle_name]
    return [] if section_category.blank?

    Category.where(name: section_category).map(&:id)
  end

end
