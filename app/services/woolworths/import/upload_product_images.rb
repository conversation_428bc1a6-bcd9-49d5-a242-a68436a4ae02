class Woolworths::Import::UploadProductImages

  def initialize(supplier:)
    @supplier = supplier
  end

  def call
    menu_items.each do |menu_item|
      upload_image_for(menu_item: menu_item)
    end
  end

private

  attr_reader :supplier

  def menu_items
    @_menu_items ||= supplier.menu_items.where.not('image LIKE ?', '%cloudinary%').where.not(image: nil)
  end

  def upload_image_for(menu_item:)
    begin
      menu_item.update!(image: Cloudinary::Uploader.upload(menu_item.image)['secure_url'])
    rescue CloudinaryException
      # Sometimes images can't be found, and a `CloudinaryException: Resource not found - [image URL here]`
      # exception is thrown. Ignore it for now, and automatically try again the next time the importer is run.
    end
  end
end
