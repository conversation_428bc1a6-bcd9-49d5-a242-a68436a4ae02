class Woolworths::Import::AddAvailability

  def initialize(menu_item:, mapped_product:)
    @menu_item = menu_item
    @mapped_product = mapped_product
    @result = Result.new
  end

  def call
    begin
      if can_add?
        store_availability = menu_item.woolworths_store_availabilities.where(store_id: mapped_product.fulfilment_store_id).first_or_initialize
        if store_availability.update(stock_quantity: mapped_product.stock_quantity.to_i)
          result.store_availability = store_availability
        end
      end
    rescue => exception
      result.errors << "Could not add store availability for #{menu_item&.id} - #{exception.message}"
    end
    result
  end

private

  attr_reader :menu_item, :mapped_product, :result

  def can_add?
    case
    when menu_item.blank?
      result.errors << 'Cannot add store availability without an item'
    when mapped_product.fulfilment_store_id.blank?
      result.errors << 'Cannot add store availability without a Store ID'
    when mapped_product.stock_quantity.to_i <= 0
      result.warnings << 'Product does does not have stock left'
    when !mapped_product.is_ranged
      result.warnings << 'Product is not ranged' # don't know what this means
    end
    result.errors.blank? && result.warnings.blank?
  end

  class Result
    attr_accessor :store_availability, :errors, :warnings

    def initialize
      @store_availability = nil
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && warnings.blank? && store_availability.present? && store_availability.persisted?
    end
  end
end
