# process
# loop per fulfilment store OR passed in importable_store_ids (split by '-')
# clear all store availabilities for the store id
# fetch aisles / categories from Woolies (only done once)
#   map and upsert menu sections for these categories
# get product per category mapping
# upsert products within menu section
# create store availabilities for items and store id
# upload any product images
class Woolworths::Import::ImportProducts
  class ImportThresholdError < StandardError; end

  PRODUCTS_THRESHOLD = 15_000

  def initialize(supplier:, importable_store_ids: nil, is_reimport: false, is_verbose: false)
    @supplier = supplier
    @importable_store_ids = importable_store_ids
    @is_reimport = is_reimport
    @is_verbose = is_verbose
    @result = Result.new
  end

  def call
    return result if !can_import?

    fulfilment_store_ids_to_import.each do |fulfilment_store_id|
      puts "Started Woolworths product #{is_reimport ? 're-import' : 'import'} for #{fulfilment_store_id}" if is_verbose
      begin
        ActiveRecord::Base.transaction do
          # remove store availability, cause if an existing menu item isn't available, it won't create an availability for the store.
          # remove_store_availability_for(fulfilment_store_id: fulfilment_store_id) if !is_reimport

          @connection = Woolworths::Import::Connection.new(store_id: fulfilment_store_id, is_reimport: is_reimport).call

          category_mappings.each do |category_mapping|
            # remove_store_availability_for(fulfilment_store_id: fulfilment_store_id, category_mapping: category_mapping) if is_reimport
            import_woolworths_products(fulfilment_store_id: fulfilment_store_id, category_mapping: category_mapping)
            result.add_menu_section(store_id: fulfilment_store_id, category: supplier.menu_sections.where(id: category_mapping.menu_section_id).first)
          end

          number_of_imported_products = result.imported_products[fulfilment_store_id.to_s].size
          puts "  Done store #{fulfilment_store_id} Imported - #{number_of_imported_products} products" if is_verbose
          if Rails.env.production? && number_of_imported_products < PRODUCTS_THRESHOLD
            result.warnings << "We got less than #{PRODUCTS_THRESHOLD} products (#{number_of_imported_products}) for #{fulfilment_store_id}"
          end

          # Kick off a manual garbage collection after each store; we're
          # allocating a lot of objects in this importer.
          GC.start
          puts "  Manually garbage collected. Memory usage: #{GetProcessMem.new.mb.round(0)} MB" if is_verbose

          # Pause after each store, as sometimes we encounter a 500 error after
          # re-authenticating with the API too frequently (which happens at the
          # start of each store's product importing).
          sleep 30.seconds if fulfilment_store_ids_to_import.size > 1 && !Rails.env.test?
        end
      rescue => exception
        result.errors << "Failed to import products for store ID: #{fulfilment_store_id} - #{exception.message}"
      end
    end

    puts " #{result.success? ? 'Successfully' : 'Un-successfully'} completed Woolworths product import. Time: #{result.import_duration} minutes." if is_verbose

    if is_verbose
      puts 'Started Woolworths product image import.'
      product_image_import_start_at = Time.zone.now.dup
    end

    Woolworths::Import::UploadProductImages.new(supplier: supplier).call if !Rails.env.development?

    if is_verbose
      product_image_import_duration = ((Time.zone.now - product_image_import_start_at) / 1.minute).round(2)
      puts "Completed Woolworths product image import. Time: #{product_image_import_duration} minutes."
    end

    puts 'Sanitizing pricing of always available Woolworths products.' if is_verbose
    Woolworths::Import::SanitizeProductPrices.new(supplier: supplier).call

    send_slack_notification if Rails.env.production?

    result
  end

private

  attr_reader :importable_store_ids, :supplier, :is_reimport, :is_verbose, :result
  attr_accessor :connection

  def can_import?
    case
    when supplier.blank?
      result.errors << 'Cannot import products without a supplier'
    when !supplier.woolworths?
      result.errors << 'Cannot import products for a non-Woolworths supplier'
    when fulfilment_store_ids_to_import.blank?
      result.errors << 'Cannot import wihtout valid fulfilment_store_ids'
    end
    result.errors.blank?
  end

  def fulfilment_store_ids_to_import
    @_fulfilment_store_ids_to_import ||= begin
      if importable_store_ids.present?
        [importable_store_ids].flatten(1) & all_store_ids
      else
        all_store_ids
      end
    end
  end

  def all_store_ids
    @_all_store_ids ||= Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys
  end

  def category_mappings
    # Category Mapping for Woolworth Categories are memoized because the categories don't change for the different Fulfilment Stores
    @category_mappings ||= Woolworths::Import::RetrieveMappedCategories.new(connection: connection, supplier: supplier, is_reimport: is_reimport, is_verbose: is_verbose).call
  end

  def import_woolworths_products(fulfilment_store_id:, category_mapping:)
    mapped_products = Woolworths::Import::GetCategoryProducts.new(connection: connection, fulfilment_store_id: fulfilment_store_id, category_mapping: category_mapping).call
    if is_verbose
      puts ''
      puts "#{category_mapping.woolworths_category.name} (#{category_mapping.menu_section_id}) - #{mapped_products.size}"
    end
    mapped_products.each do |mapped_product|
      next if !mapped_product.is_for_delivery
      next if mapped_product.price.blank?

      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: mapped_product).call

      if product_syncer.success?
        result.add_product(store_id: fulfilment_store_id, product: product_syncer.synced_item)
      else
        result.warnings << "#{mapped_product.name} - #{product_syncer.warnings.join('. ')}" if product_syncer.warnings.present?
        result.errors << "Could not import product #{mapped_product.name} - #{product_syncer.errors.join('. ')}" if product_syncer.errors.present?
      end
      print '*' if is_verbose
    end
  end

  def remove_store_availability_for(fulfilment_store_id:, category_mapping: nil)
    always_available_item_ids = Rails.configuration.woolworths.category_mappings.always_available_item_ids

    item_availabilies = Woolworths::StoreAvailability.where(store_id: fulfilment_store_id)
    item_availabilies = item_availabilies.joins(:menu_section).where(menu_sections: { id: category_mapping.menu_section_id }) if category_mapping.present?
    item_availabilies = item_availabilies.joins(:menu_item).where.not(menu_items: { id: always_available_item_ids })
    item_availabilies.destroy_all
  end

  def send_slack_notification
    now = Time.zone.now
    return if now >= Time.zone.parse(yordar_credentials(:yordar, :closure_start_date)) && now <= Time.zone.parse(yordar_credentials(:yordar, :closure_end_date))

    undercounted = []
    attachments = fulfilment_store_ids_to_import.collect do |fulfilment_store_id|
      count = Woolworths::StoreAvailability.where(store_id: fulfilment_store_id).count
      count_warning = count < PRODUCTS_THRESHOLD
      undercounted << fulfilment_store_id if count_warning
      text = "*#{fulfilment_store_id}*: `#{count}`"
      text += ' - *Please check/re-import products for this suburb*' if count_warning
      {
        type: 'mrkdwn',
        text: text,
        color: (count_warning ? 'warning' : 'good')
      }
    end
    if now.to_s(:weekday) == 'Monday' || undercounted.present?
      message = ":corn: *Woolworths Product Importer*: imported products from #{fulfilment_store_ids_to_import.size} Woolworths stores in *#{result.import_duration}* minutes."
      message += " :warning: *#{undercounted.size} #{'store'.pluralize(undercounted.size)}* with products under threshold (#{PRODUCTS_THRESHOLD})" if undercounted.present?
      SlackNotifier.send(message, attachments: attachments)
    end
  end

  class Result
    attr_accessor :imported_menu_sections, :imported_products, :errors, :warnings
    attr_reader :started_at

    def initialize
      @imported_menu_sections = {}
      @imported_products = {}

      @started_at = Time.zone.now
      @errors = []
      @warnings = []
    end

    def import_duration
      ((Time.zone.now - started_at) / 1.minute).round(2)
    end

    def add_menu_section(store_id:, category:)
      @imported_menu_sections[store_id.to_s] ||= []
      @imported_menu_sections[store_id.to_s] << category
    end

    def add_product(store_id:, product:)
      @imported_products[store_id.to_s] ||= []
      @imported_products[store_id.to_s] << product
    end

    def success?
      errors.blank? && warnings.blank?
    end
  end

end
