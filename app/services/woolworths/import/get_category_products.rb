class Woolworths::Import::GetCategoryProducts < Woolworths::API

  CUSTOM_DICTIONARY_ADDITIONS = {
    'coca - cola' => 'Coke',
    'coca-cola' => 'Coke',
  }

  def initialize(connection:, fulfilment_store_id:, category_mapping:)
    @connection = connection
    @fulfilment_store_id = fulfilment_store_id
    @category_mapping = category_mapping
  end

  def call
    products_to_import = []
    page = 1
    while page do
      page, products = get_products_for_page(page: page)

      next if products.blank?

      products.each do |product|
        products_to_import << mapped_product(product: product)
        print '.' if !Rails.env.test?
      end
    end
    products_to_import
  end

private

  attr_reader :connection, :fulfilment_store_id, :category_mapping
  attr_accessor :products_to_import

  def get_products_for_page(page:)
    products_response = @connection.request(method: :get, path: PRODUCTS_ENDPOINT, params: {
      mode: 'online',
      store: fulfilment_store_id,
      aisle: category_mapping.woolworths_category.aisle_name,
      category: category_mapping.woolworths_category.category_name,
      page: page
    })
    if products_response.status == 200
      products = products_response.body[:products]
      next_page = products_response.body[:nextpage] ? CGI.parse(URI.parse(products_response.body[:nextpage]).query)['page'].first.to_i : nil
    else
      products = []
      next_page = nil
    end

    [next_page, products]
  end

  def mapped_product(product:)
    OpenStruct.new(
      sku: product[:article],
      is_for_delivery: product[:is][:fordelivery],
      is_ranged: product[:is][:ranged],
      menu_section_id: category_mapping.menu_section_id,
      name: product[:description].strip,
      description: sanitized_description_for(product: product),
      image: product[:images][:thumbnail],
      price: product[:instoreprice][:pricegst],
      promo_price: (product[:promotions].present? ? product[:promotions][:price] : nil),
      is_gst_free: category_mapping.gst_free,
      fulfilment_store_id: fulfilment_store_id,
      stock_quantity: product[:stockqty]
    )
  end

  def sanitized_description_for(product:)
    description = product[:description].strip
    CUSTOM_DICTIONARY_ADDITIONS.each do |key, value|
      next if description.downcase.exclude?(key)

      description += " - also known as #{value}"
    end
    description
  end
end
