class Woolworths::Import::SyncProduct

  def initialize(supplier:, product_to_import:)
    @supplier = supplier
    @mapped_product = product_to_import
    @result = Result.new
  end

  def call
    if can_sync?
      item_upserter = MenuItems::Upsert.new(menu_item: existing_menu_item, menu_item_params: menu_item_params).call

      if item_upserter.success?
      #   add_availability_for(menu_item: item_upserter.menu_item)
        result.synced_item = item_upserter.menu_item
      end
    end

    result
  end

private

  attr_reader :supplier, :mapped_product, :result

  def can_sync?
    case
    when supplier.blank?
      result.errors << 'Cannot sync product without a supplier'
    when mapped_product.blank?
      result.errors << 'Cannot sync product without a mapping'
    when mapped_product.sku.blank?
      result.errors << 'Cannot sync product without a mapped SKU'
    when mapped_product.menu_section_id.blank?
      result.errors << 'Cannot sync product without a mapped menu section'
    end

    result.errors.blank?
  end

  def existing_menu_item
    @_existing_menu_item ||= supplier.menu_items.where(sku: mapped_product.sku).first
  end

  def menu_item_params
    {
      sku: mapped_product.sku,
      supplier_profile_id: supplier.id,
      menu_section_id: mapped_product.menu_section_id,
      name: mapped_product.name,
      description: mapped_product.description,
      price: mapped_product.price,
      promo_price: promo_price,
      is_gst_free: mapped_product.is_gst_free,
      image: image,
    }
  end

  def promo_price
    return nil if mapped_product.promo_price.blank? || mapped_product.promo_price <= 0

    mapped_product.promo_price
  end

  def image
    if existing_menu_item&.image&.include?('cloudinary')
      existing_menu_item.image
    else
      mapped_product.image
    end
      
  end

  def add_availability_for(menu_item:)
    availability_adder = Woolworths::Import::AddAvailability.new(menu_item: menu_item, mapped_product: mapped_product).call
    if availability_adder.success?
      result.synced_item = menu_item
    else
      result.errors += availability_adder.errors
      result.warnings += availability_adder.warnings
    end
  end

  class Result
    attr_accessor :synced_item, :errors, :warnings

    def initialize
      @synced_item = nil
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && warnings.blank? && synced_item.present? && synced_item.persisted?
    end
  end

end
