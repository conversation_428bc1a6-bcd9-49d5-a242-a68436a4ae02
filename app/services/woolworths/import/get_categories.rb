class Woolworths::Import::GetCategories < Woolworths::API

  def initialize(connection:)
    @connection = connection
  end

  def call
    categories_response = connection.request(method: :get, path: CATEGORIES_ENDPOINT, params: { mode: :online })

    raise 'Could not fetch categories from Woolworths' unless categories_response.status == 200

    categories = []
    categories_response.body[:aisles].each do |aisle|
      aisle[:categories].each do |category|
        categories << WoolworthsCategory.new(aisle_name: aisle[:title].strip, category_name: category[:title].strip)
      end
    end
    categories
  end

private

  attr_reader :connection

  class WoolworthsCategory
    attr_reader :aisle_name, :category_name

    def initialize(aisle_name:, category_name:)
      @aisle_name = aisle_name
      @category_name = category_name
    end

    def name
      "#{aisle_name} - #{category_name}"
    end
  end
end
