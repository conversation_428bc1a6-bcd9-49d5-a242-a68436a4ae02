# The products endpoint returns products scoped to the store that the
# most recently added address resolves to. So we have to add an address,
# re-authenticate, then check that our Woolworths account has been
# linked to the correct fulfilment store.
class Woolworths::Import::Connection

  def initialize(store_id:, is_reimport: false)
    @store_id = store_id
    @is_reimport = is_reimport
  end

  def call
    @connection = Woolworths::API::Connection.new(use_importer_account: true)

    connection.authenticate # authenticate to get primary addres

    if connection.primary_address.except(:street_2) != saved_address_data.except(:postcode)
      Woolworths::API::AddAddress.new({ connection: connection }.merge(saved_address_data)).call
      connection.authenticate # re-authenticate to get the fullfilment store ID again
    end
    raise "Connection is not linked to the correct fulfilment store (ID ##{store_id})" if connection.fulfilment_store_id != store_id.to_s

    set_import_delivery_window

    connection
  end

private

  attr_reader :store_id, :is_reimport, :connection

  def saved_address_data
    @_address_data ||= begin
      fulfilment_store = Rails.configuration.woolworths.fulfilment_stores.mapped_stores[store_id]
      {
        street_1: fulfilment_store[:street_1],
        suburb: fulfilment_store[:suburb],
        postcode: fulfilment_store[:postcode],
      }
    end
  end

  def set_import_delivery_window
    delivery_time = Time.zone.now.beginning_of_day + 2.days + 13.hours # best delivery window is 2 days from today 1pm to 3pm
    if is_reimport
      delivery_time = Time.zone.now.beginning_of_hour + 2.days
    end
    temp_order = ::Order.new(delivery_at: delivery_time)
    available_delivery_windows = Woolworths::Order::GetAvailableDeliveryWindows.new(order: temp_order, connection: connection).call

    best_delivery_window = available_delivery_windows.detect{|window| window.time_range.begin == delivery_time }
    best_delivery_window ||= available_delivery_windows.detect{|window| window.overlaps?(delivery_time) }
    best_delivery_window ||= available_delivery_windows.detect{|window| window.time_range.begin >= delivery_time }

    begin
      Woolworths::API::SetDeliveryWindow.new(order: temp_order, connection: connection, window_id: best_delivery_window.id).call
    rescue
      # do nothing
    end
  end

end
