class Woolworths::Cleanup::RemoveSavedAddresses < Woolworths::API

  def initialize(account: nil, dry_run: false, verbose: false)
    @account = account
    @dry_run = dry_run
    @verbose = verbose
    @result = Result.new
  end

  def call
    available_accounts.each do |account|
      result.accounts << account
      remove_addresses_for(account)
    end
    result
  end

private

  attr_reader :account, :dry_run, :verbose, :result

  def available_accounts
    return [account] if account.present?

    @_available_accounts ||= Woolworths::Account.where(active: true).where.not(id: Woolworths::Order.where(account_in_use: true).pluck(:account_id))
  end

  def remove_addresses_for(account)
    saved_addresses_for(account).each do |saved_address|
      puts "Address: #{saved_address[:text]} - #{saved_address[:isprimary]}" if verbose
      next if saved_address[:isprimary]

      if dry_run
        result.deleted_addresses << saved_address
      else
        remove_saved_address_for(account, saved_address)
      end
    end
  end

  def saved_addresses_for(account)
    if verbose
      puts ''
      puts "Account: #{account.email}"
    end
    begin
      Woolworths::API::ListAddresses.new(account: account, connection: connection_for_account(account)).call
    rescue => exception
      result.errors << exception.message
      []
    end
  end

  def remove_saved_address_for(account, saved_address)
    begin
      Woolworths::API::DeleteAddressById.new(account: account, connection: connection_for_account(account), address_id: saved_address[:id]).call
      result.deleted_addresses << saved_address
    rescue => exception
      result.errors << exception.message
    end
  end

  def connection_for_account(account)
    return @_connections[account] if @_connections.present? && @_connections[account].present?

    @_connections ||= {}
    @_connections[account] = Woolworths::API::Connection.new(account: account, authenticated: true)
  end

  class Result
    attr_accessor :accounts, :deleted_addresses, :errors

    def initialize
      @accounts = []
      @deleted_addresses = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end


