# Run as part of a daily scheduled task
# Sevive Object to detach the limited Woolworths Accounts from any Stale Orders
class Woolworths::Cleanup::DetachStaleAccounts
  StaleOrders = Struct.new(:woolworths_order, :order, :order_updated_at, :order_line_size, :max_order_line_updated_at)

  def initialize(time: Time.zone.now)
    @reference_time = time
    @result = Result.new
  end

  def call(dry_run: false)
    stale_orders.each do |stale_order|
      next if dry_run

      stale_order.woolworths_order.update(account_in_use: false)
      order = stale_order.order
      order.order_lines.each(&:destroy) if order.status == 'draft'
    end
    send_slack_notification if !Rails.env.test?
    result
  end

private

  attr_reader :reference_time, :result

  def attached_woolworths_orders
    @_attached_woolworths_orders = Woolworths::Order.where(account_in_use: true).includes(:order)
  end

  def stale_orders
    @_stale_orders ||= attached_woolworths_orders.collect do |woolworth_order|
      result.attached_woolworths_orders << woolworth_order
      order = woolworth_order.order
      max_order_line_updated_at = order.order_lines.map(&:updated_at).max || reference_time
      two_day_old_orders = order.updated_at <= (reference_time - 2.days) || max_order_line_updated_at <= (reference_time - 2.days)
      one_day_old_orders = order.updated_at <= (reference_time - 1.days) || max_order_line_updated_at <= (reference_time - 1.days)
      stale_order = nil
      if two_day_old_orders || (one_day_old_orders && order.order_lines.blank?)
        stale_order = StaleOrders.new(woolworth_order, order, order.updated_at, order.order_lines.size, max_order_line_updated_at)
        result.stale_orders << stale_order
      end
      stale_order
    end.compact
  end

  def send_slack_notification
    return if stale_orders.blank?

    attachments = stale_orders.collect do |stale_order|
      text = "Order ##{stale_order.order.id} - last updated #{stale_order.order_updated_at.strftime('%d-%m-%Y %H:%M')}"
      text += " - with #{stale_order.order_line_size} order #{'line'.pluralize(stale_order.order_line_size)}" if stale_order.order_line_size > 0
      {
        type: 'mrkdwn',
        text: text,
        color: 'good'
      }
    end
    message = ":globe_with_meridians: *Woolworths Account Dettacher*: detached *#{stale_orders.size}* accounts from #{attached_woolworths_orders.size} Woolworth orders"
    SlackNotifier.send(message, attachments: attachments)
  end

  class Result
    attr_accessor :stale_orders, :attached_woolworths_orders

    def initialize
      @stale_orders = []
      @attached_woolworths_orders = []
    end
  end

end
