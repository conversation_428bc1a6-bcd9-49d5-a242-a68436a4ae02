class Woolworths::Process<PERSON>rder<PERSON>ine

  def initialize(order_line:)
    @order_line = order_line
  end

  def call
    @trolley_product_errors = []
    connection = Woolworths::API::Connection.new(account: order_line.order.woolworths_account, authenticated: true)
    @trolley_product = Woolworths::API::AddProductToTrolley.new(connection: connection, product: trolley_product_data).call
    process_trolley_errors
    sync_order_line_pricing
  rescue Woolworths::API::AddProductToTrolley::ExceededDeliverySaversLimitError
    add_trolley_error(message: 'You can only have a maximum of 2 active Delivery Savers on your account at any one time. These will be used consecutively.')
  rescue Woolworths::API::AddProductToTrolley::CannotProcessProductToTrolley
    add_trolley_error(message: "Could not add/update #{menu_item.name} to the Woolworths trolley.")
  ensure
    sync_trolley_product
  end

private

  attr_reader :order_line, :trolley_product

  def order
    @_order ||= order_line.order
  end

  def menu_item
    @_menu_item ||= order_line.menu_item
  end

  def item_quantity
    return @_item_quantity if @_item_quantity.present?

    all_item_order_lines = order.order_lines.where(menu_item: menu_item)
    @_item_quantity = all_item_order_lines.map(&:quantity).sum
  end

  def trolley_product_data
    {
      menu_item: menu_item,
      quantity: item_quantity,
    }
  end

  def process_trolley_errors
    return if item_quantity == 0

    case
    when trolley_product.blank? || trolley_product.menu_item.blank? || !trolley_product.available
      add_trolley_error(message: 'Item is currently unavailable.')
    when trolley_product.stock_quantity.present?
      add_trolley_error(message: "Only #{trolley_product.stock_quantity} items available.")
    end
  end

  def sync_order_line_pricing
    return if item_quantity == 0 || trolley_product.blank? || !trolley_product.available || trolley_product.instore_price.blank?

    if trolley_product.instore_price.to_d != order_line.cost.to_d # order_line.menu_item.price.to_d
      cost = commission_based_cost(trolley_product.instore_price)
      price = price_with_markup(trolley_product.instore_price)
      order_line.update(cost: cost, price: price)
      notify_price_mismatch if yordar_credentials(:woolworths, :pricing_check)
    end
  end

  def commission_based_cost(price)
    case
    when markup_override&.commission_rate.present?
      price * (1 - (markup_override.commission_rate / 100))
    when supplier&.commission_rate.present?
      price * (1 - (supplier.commission_rate / 100))
    else
      price.to_f
    end
  end

  def price_with_markup(price)
    case
    when markup_override&.markup.present?
      price.to_f * (1 + (markup_override.markup / 100))
    when supplier&.markup.present?
      price.to_f * (1 + (supplier.markup / 100))
    else
      price.to_f
    end
  end

  def markup_override
    @_markup_override ||= begin
      customer = order_line.order.customer_profile
      Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer).call
    end
  end

  def supplier
    @_supplier ||= order_line.supplier_profile
  end

  def notify_price_mismatch
    message = ":warning: Woolworths Product Pricing Mistmatch for order ##{order.id}"
    shown_price = menu_item.promo_price.presence || menu_item.price
    attachments = [
      {
        type: 'mrkdwn',
        text: "#{menu_item.name} (#{menu_item.id}) - $#{shown_price.to_f}#{menu_item.promo_price.present? ? ' (promo)' : ''} vs $#{trolley_product.instore_price.to_f} (instore)",
        color: 'warning'
      }
    ]
    SlackNotifier.send(message, attachments: attachments)
  end

  def add_trolley_error(message:)
    @trolley_product_errors << message
  end

  def sync_trolley_product
    if item_quantity == 0
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line).call(sync_event: :remove)
    else
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line).call(sync_event: :end, errors: @trolley_product_errors)
    end
  end
end
