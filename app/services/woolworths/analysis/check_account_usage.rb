class Woolworths::Analysis::CheckAccountUsage

  def initialize(order_ids: [])
    @order_ids = order_ids
  end

  def call
    woolworths_accounts.each do |account|
      puts ''
      puts "Account: #{account.email}"
      check_usage_for(account)
    end
  end

private

  attr_reader :order_ids

  def woolworths_accounts
    @_woolworths_accounts = Woolworths::Account.all.order(:email)
  end

  def check_usage_for(account)
    worders = Woolworths::Order.where(account: account, account_in_use: true)
    if worders.present?
      worders.each do |worder|
        order = worder.order
        puts "Order ##{order.id}"
        puts "Status #{order.status}"
        puts "Created at: #{order.created_at}"
        puts "Delivery at: #{order.delivery_at}"
        puts "Customer: #{order.customer_profile.try(:name)}"
        puts "Delivery: #{order.delivery_address_arr.join(', ')}"
        puts "Order lines: #{order.order_lines.size}"
        if order_ids.include?(order.id) && worder.update(account_in_use: false)
          puts '*****DETACHED*****'
        end
        puts '-' if worders.size > 1
      end
    else
      puts 'FREE'
    end
  end
end
