# This service object is for purely Slack Notifications
# Service Object which loops though the saved Woolworths Fulfilment stores to check
# if they match against the saved address
class Woolworths::Analysis::CheckFulfilmentStores
  WoolworthsToken = Struct.new(:email, :token)

  def initialize
    @attachments = []
  end

  def call
    clear_addresses
    fulfilment_stores.each do |store_id, address|
      check_connection_for(store_id, address)
    end
    send_notifications if attachments.present?
  end

private

  attr_reader :attachments

  def send_notifications
    message = ":globe_with_meridians: *Woolworths Store Validation*: validated *#{fulfilment_stores.size}* stores"
    SlackNotifier.send(message, attachments: attachments)
  end

  def connection
    @_connection ||= Woolworths::API::Connection.new(use_importer_account: true)
  end

  def fulfilment_stores
    @_stores ||= Rails.configuration.woolworths.fulfilment_stores.mapped_stores
  end

  def check_connection_for(store_id, address)
    connection.authenticate # initial authentication

    if connection.primary_address.except(:street_2) != address.except(:postcode)
      Woolworths::API::AddAddress.new({ connection: connection }.merge(address_data)).call
      sleep(10)
      connection.authenticate # re-authenticate to get the right primary address and fullfilment store ID
      check_fulfilment_address(store_id, address)
    end

    check_fulfilment_store_for(store_id, address)
  end

  def check_fulfilment_address(store_id, address)
    if connection.primary_address.except(:street_2) != address_data.except(:postcode)
      attachments << {
          type: 'mrkdwn',
          text: "#{store_id} -> #{address.values.join(', ')} *ADDRESS-MISMATCH* - FOUND #{connection.primary_address.except(:street_2).join(', ')}",
          color: 'warning'
        }
    end
  end

  def check_fulfilment_store_for(store_id, address)
    if connection.fulfilment_store_id == store_id.to_s
      text = "#{store_id} -> #{address.values.join(', ')} *VALID*"
      color = 'success'
    else
      text = "#{store_id} -> #{address.values.join(', ')} *STORE IN-VALID* - FOUND #{connection.fulfilment_store_id}"
      color = 'warning'
    end
    attachments << {
        type: 'mrkdwn',
        text: text,
        color: color
      }
  end

end
