class Woolworths::Analysis::CheckProductAvailability

  ArchivableMenuSection = Struct.new(:menu_section, :menu_items, :reason)

  def initialize(dry_run: true, verbose: false)
    @dry_run = dry_run
    @verbose = verbose
    @archivable_menu_sections = []
  end

  def call
    woolies_menu_sections.each do |menu_section|
      print 'ms-' if verbose
      check_section_availability_for(menu_section)
    end
    if archivable_menu_sections.present?
      archivable_menu_sections.each do |archivable_menu_section|
        archive(archivable_menu_section)
      end
    else
      puts 'NO sections to archive'
    end
    archivable_menu_sections
  end

private

  attr_reader :dry_run, :verbose, :archivable_menu_sections

  def woolies_menu_sections
    @_woolies_menu_sections ||= MenuSection.where(supplier_profile: woolworths).where.not(name: 'custom').where(archived_at: nil)
  end

  def woolworths
    @_woolworths ||= SupplierProfile.where(id: yordar_credentials(:woolworths, :supplier_profile_id)).first
  end

  def check_section_availability_for(menu_section)
    menu_items = menu_section.menu_items.where(archived_at: nil).includes(:woolworths_store_availabilities)
    if menu_items.blank?
      archivable_menu_sections << ArchivableMenuSection.new(menu_section, menu_items, 'blank menu items')
    else
      check_item_availability_for(menu_section, menu_items)
    end
  end

  def check_item_availability_for(menu_section, menu_items)
    unavailable_menu_items = menu_items.select do |menu_item|
      print 'mi-' if verbose
      menu_item.woolworths_store_availabilities.blank?
    end
    if unavailable_menu_items.size == menu_items.size
      archivable_menu_sections << ArchivableMenuSection.new(menu_section, menu_items, 'All items without any store availabilities')
    end
  end

  def archive(archivable_menu_section)
    menu_section = archivable_menu_section.menu_section
    menu_items = archivable_menu_section.menu_items
    puts ''
    puts "Section: #{menu_section.group_name} - #{menu_section.name} (#{menu_section.id})"
    puts "MenuItems #{menu_items.size}"
    puts "Reason: #{archivable_menu_section.reason}"
    if !dry_run
      puts ''
      print 'Archiving now..'
      MenuSections::Archive.new(menu_section: menu_section, is_forced: true).call
      print '..done'
    end
  end

end
