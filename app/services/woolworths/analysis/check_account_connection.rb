# This service object is for purely Slack Notifications - run as a daily scheduled task
# Service Object which loops though the stored Woolworths Accounts
# It tries to authenticate to retrieve a token.
# If there is an issue while authenticating, it notifies on Slack accordingly.
class Woolworths::Analysis::CheckAccountConnection
  WoolworthsToken = Struct.new(:email, :token)

  def call
    attachments = accounts_with_token.collect do |account|
      next if account.token.present?

      text = "#{account.email} - *authentication failed*"
      {
        type: 'mrkdwn',
        text: text,
        color: 'warning'
      }
    end.compact
    message = ":globe_with_meridians: *Woolworths Account Validation*: validated *#{accounts.size}* accounts"
    if attachments.present?
      message += " - :warning: *#{attachments.size} failed*"
      SlackNotifier.send(message, attachments: attachments)
    end
  end

private

  def accounts
    @_accounts ||= Woolworths::Account.all
  end

  def accounts_with_token
    accounts.collect do |account|
      authenticate(account)
    end
  end

  def authenticate(account)
    connection = Woolworths::API::Connection.new(account: account)
    email = account.email.split('@').first.capitalize
    print '.'
    begin
      connection.authenticate
      WoolworthsToken.new(email, connection.access_token)
    rescue
      WoolworthsToken.new(email, nil)
    end
  end

end
