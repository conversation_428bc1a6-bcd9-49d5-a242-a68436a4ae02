class Woolworths::Order::AttachToOrder
  class NoAccountsAvailableError < StandardError; end

  def initialize(order:)
    @order = order
  end

  def call
    return if order.woolworths_order.present?

    if available_account.present?
      Woolworths::Order.create(
        order: order,
        account: available_account,
        account_in_use: true,
        status: "Connected to woolworths account #{available_account.email}."
      )
    else
      raise NoAccountsAvailableError
    end
  end

private

  attr_accessor :order

  def available_account
    @_available_account ||= Woolworths::GetAvailableAccount.new.call
  end

end
