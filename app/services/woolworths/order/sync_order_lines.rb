class Woolworths::Order::SyncOrderLines

  def initialize(order:, order_lines: [])
    @order = order
    @order_lines = order_lines.presence || fetch_order_lines

    @result = Result.new
  end

  def call
    if can_sync?
      order_lines.each do |order_line|
        order_line.touch # to mark the order line as being synced
        Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line).call(sync_event: :start)
        Woolworths::ProcessOrderLine.new(order_line: order_line).delay(queue: :instant, attempts: 3).call
        result.syncable_order_lines << order_line
      end
    end

    result
  end

private

  attr_reader :order, :order_lines, :result

  def can_sync?
    case
    when order.blank?
      result.errors << 'Order is missing'
    when order.woolworths_order.blank?
      result.errors << 'Cannot sync a non Woolworths order\'s lines'
    when order.status != 'draft'
      result.errors << 'Order is no longer draft'
    when order_lines.blank?
      result.errors << 'Order does not contain any Woolworths items'
    end
    result.errors.blank?
  end

  def fetch_order_lines
    return [] if order.blank?

    lister_options = {
      order: order,
      supplier: SupplierProfile.woolworths
    }
    OrderLines::List.new(options: lister_options).call
  end

  class Result
    attr_accessor :syncable_order_lines, :errors

    def initialize
      @syncable_order_lines = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end