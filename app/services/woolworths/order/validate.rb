class Woolworths::Order::Validate
  DELIVERY_THRESHOLD = 70

  ACCOUNT_NOT_IN_USE_ERROR = 'We seem to have lost connection with Woolworths; Please start a fresh order by clearing cart'.freeze
  ORDER_LINE_SYNC_ERROR = 'It\'s taking some time for your products to be synced with Woolworths; please wait a few seconds and validate again'.freeze
  WINDOW_NOT_SET_ERROR = 'Window has not been set for this checkout'.freeze

  def initialize(order:)
    @order = order
    @woolworths_order = order.woolworths_order
    @result = Result.new(order: order)
  end

  # Steps to validate a woolworths order
  # 1. Check if order lines are synced
  # 2. Check if delivery window is sycned
  # 3. Check if order in Woolworths Account is valid
  # 4. Check for any trolley errors

  def call
    begin
      if order_still_connected? && order_lines_synced? && delivery_address_synced? && delivery_window_synced?
        validate_woolworths_order
      end
    rescue Woolworths::API::SetDeliveryWindow::DeliveryWindowError
      select_next_available_window
    rescue # catch-all rescue
      result.errors[:order] << 'We encountered an error when communicating with Woolworths, please try again'
    end
    result
  end

private

  attr_reader :order, :woolworths_order, :result

  def connection
    @_connection ||= Woolworths::API::Connection.new(account: order.woolworths_account, authenticated: true)
  end

  def order_still_connected?
    return true if order.woolworths_order.present? && order.woolworths_order.account_in_use?

    result.errors[:order] << ACCOUNT_NOT_IN_USE_ERROR
    false
  end

  def order_lines_synced?
    is_synced = Woolworths::CheckOrderSync.new(order: order).call
    if !is_synced
      result.errors[:order] << ORDER_LINE_SYNC_ERROR
    end
    result.errors[:order].blank?
  end

  # check if for some reason the delivery address wasn't set properly
  def delivery_address_synced?
    address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order, woolworths_order: woolworths_order, connection: connection, validate: true).call
    result.errors[:details] += address_syncer.errors if !address_syncer.success?

    address_syncer.success?
  end

  # Sometimes the delivery window is only avaialble for a short amount of time.
  # Hence we need to set it again before validating the order.
  def delivery_window_synced?
    if order.delivery_at.present? && woolworths_order.delivery_window_id.present?
      result.delivery_window_text = Woolworths::API::SetDeliveryWindow.new(order: order, connection: connection).call
    else
      result.errors[:details] << WINDOW_NOT_SET_ERROR
    end
    result.errors[:details].blank?
  end

  def validate_woolworths_order
    order_validator = Woolworths::API::ValidateOrder.new(connection: connection).call
    if order_validator.success?
      update_delivery_fee(order_validator.delivery_fee)
      result.errors[:order] << 'Order Items have Trolley errors!' if trolley_product_errors.present?
    else
      result.errors[:order] += sanitized_validation_errors(order_validator.errors)
    end
  end

  def update_delivery_fee(woolworths_delivery_fee)
    if woolworths_delivery_fee.present? && woolworths_order.delivery_fee != woolworths_delivery_fee
      woolworths_order.update(delivery_fee: woolworths_delivery_fee)
      Orders::CalculateCustomerTotals.new(order: order.reload, save_totals: true).call
    end
  end

  def sanitized_validation_errors(validation_errors)
    sanitized_errors = validation_errors.map do |error_message|
      if error_message == 'MinimumSpend'
       "The subtotal of #{number_to_currency(order.customer_subtotal)} is less than the Delivery minimum spend of #{number_to_currency(DELIVERY_THRESHOLD)}."
      else
        error_message
      end
    end
    case
    when validation_errors.include?('Checkout Order has unavailable items.') && trolley_product_errors.blank?
      sanitized_errors -= ['Checkout Order has unavailable items.']
      sanitized_errors << 'Checkout Order has unavailable items - we are currently trying to re-sync the items.'
      resync_order_lines
    when validation_errors.include?('Checkout Order has unavailable items.') && validation_errors.include?('Checkout has no available product items.')
      sanitized_errors -= ['Checkout Order has unavailable items.']
    end
    sanitized_errors
  end

  def resync_order_lines
    order_lines.each do |order_line|
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line).call(sync_event: :start)
      Woolworths::ProcessOrderLine.new(order_line: order_line).delay(queue: :instant, attempts: 3).call
    end
  end

  def trolley_product_errors
    @_trolley_product_errors ||= order.woolworths_products.where(order_line: order_lines).map(&:trolley_errors).flatten.uniq
  end

  def number_to_currency(amount)
    amount ||= 0
    ActionController::Base.helpers.number_to_currency(amount)
  end

  def select_next_available_window
    begin
      delivery_window_text = Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: connection).call
      if delivery_window_text.present?
        result.delivery_window_text = delivery_window_text
        result.errors[:details] << 'The previously selected delivery window is no-longer available! We\'ve selected the next available one, please review before proceeding'
      end
    rescue
      result.errors[:details] << 'We had some troubles setting your delivery window please try a different date'
    end
  end

  def order_lines
    lister_options = {
      order: order
    }
    OrderLines::List.new(options: lister_options).call
  end

  class Result
    attr_accessor :order, :delivery_window_text, :errors

    def initialize(order:)
      @order = order
      @delivery_window_text = ''
      @errors = {
        order: [],
        details: [],
      }
    end

    def success?
      errors.values.flatten(1).blank?
    end

  end
end
