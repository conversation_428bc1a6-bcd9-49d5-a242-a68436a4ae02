# steps for syncing delivery address
# get address from Woolworths (address matching done in Woolworths::API::GetAddress)
# check if address is present - if not then add new address
# check if address is set to primary - if not then set address to primary
# validation makes the same above check, just returns with errors.

class Woolworths::Order::SyncDeliveryAddress

  DELIVERY_ADDRESS_SYNC_ERROR = 'Delivery Address not synced correctly. Syncing it now!'.freeze

  def initialize(order:, woolworths_order: nil, connection: nil, validate: false)
    @order = order
    @woolworths_order = woolworths_order.presence || order.woolworths_order
    @connection = connection.presence || setup_connection
    @validate = validate

    @result = Result.new
  end

  def call
    case
    when woolworths_address.blank? # could not find address in Woolworths Account
      added_address = Woolworths::API::AddAddress.new({ connection: connection }.merge(address_data)).call
      sync_to_woolworths_as_primary(delivery_address_id: added_address[:id]) if added_address.present?
      result.errors << DELIVERY_ADDRESS_SYNC_ERROR if validate
    when woolworths_order.delivery_address_id.blank? # address not set in Woolworths::Order yet
      sync_to_woolworths_as_primary(delivery_address_id: woolworths_address[:id])
      result.errors << DELIVERY_ADDRESS_SYNC_ERROR if validate
    when (woolworths_order&.delivery_address_id != woolworths_address[:id] || !woolworths_address[:isprimary]) # Mismatch ID or non-primary in Woolworths
      sync_to_woolworths_as_primary(delivery_address_id: woolworths_address[:id])
      result.errors << DELIVERY_ADDRESS_SYNC_ERROR if validate
    else
      result.delivery_address_id = woolworths_address[:id]
    end

    result
  end

private

  attr_reader :order, :woolworths_order, :connection, :validate, :result

  def woolworths_address
    @_woolworths_address ||= Woolworths::API::GetAddress.new({ connection: connection }.merge(address_data)).call
  end

  def sync_to_woolworths_as_primary(delivery_address_id:)
    return if delivery_address_id.blank?

    begin
      Woolworths::API::SetDeliveryAddress.new(connection: connection, address_id: delivery_address_id).call
      woolworths_order.update(delivery_address_id: delivery_address_id)

      result.delivery_address_id = delivery_address_id
    rescue
      result.errors << DELIVERY_ADDRESS_SYNC_ERROR
    end
  end

  def setup_connection
    Woolworths::API::Connection.new(account: order.woolworths_account, authenticated: true)
  end

  def address_data
    street_1 = ''
    if order.delivery_address_level.present?
      street_1 += "#{order.formatted_delivery_address_level}, "
    end
    street_1 += order.delivery_address.strip
    {
      street_1: street_1,
      suburb: order.delivery_suburb.name,
      postcode: order.delivery_suburb.postcode
    }
  end

  class Result
    attr_accessor :delivery_address_id, :errors

    def initialize
      @delivery_address_id = nil
      @errors = []
    end

    def success?
      errors.blank? && delivery_address_id.present?
    end
  end

end
