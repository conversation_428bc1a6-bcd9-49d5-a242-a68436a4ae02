class Woolworths::Order::SyncTrolleyProduct

  def initialize(order:, order_line:)
    @order = order
    @order_line = order_line
  end

  def call(sync_event:, errors: [])
    return if woolworths_order.blank?

    @event = sync_event.to_sym
    case event.to_sym
    when :start
      potential_trolley_products.each do |trolley_product|
        trolley_product.update(synced: false, trolley_errors: [])
      end
    when :remove
      potential_trolley_products.each(&:destroy)
    else # when :end
      potential_trolley_products.each do |trolley_product|
        trolley_product.update(synced: true, trolley_errors: errors)
      end
    end
  end

private

  attr_reader :order, :order_line, :event

  def woolworths_order
    @_woolworths_order = order.woolworths_order
  end

  def potential_trolley_products
    similar_order_lines.map do |order_line|
      woolworths_order.trolley_products.where(
        order_line: order_line
      ).first_or_initialize
    end
  end

  def similar_order_lines
    lines = order.order_lines.where(menu_item: order_line.menu_item)
    lines = [order_line] if lines.blank? && event == :remove
    lines
  end

end
