class Woolworths::Order::FetchSavedDeliveryWindows

  def initialize(order:)
    @order = order
    @result = Result.new
  end

  def call
    if can_fetch?
      result.delivery_windows = grouped_delivery_windows
      result.active_date = active_delivery_date
    end
    result
  end

private
  
  attr_reader :order, :result

  def can_fetch?
    case
    when order.blank?
      false
    when order.id.blank?
      false
    when available_delivery_windows.blank?
      false
    else
      true
    end
  end

  def cache_key
    @_cache_key ||= Woolworths::API::DELIVERY_WINDOWS_CACHE_KEY.sub(':id', order.id.to_s)
  end

  def available_delivery_windows
    @_available_delivery_windows ||= Rails.cache.read(cache_key)
  end

  def grouped_delivery_windows
    @_grouped_delivery_windows ||= available_delivery_windows.group_by(&:date)
  end

  def active_delivery_date
    available_dates = grouped_delivery_windows.keys
    available_dates.detect{|date| date == order.delivery_at.to_date }.presence || # one with the same date as delivery date
      available_dates.detect{|date| date > order.delivery_at.to_date }.presence || # one with date greater than delivery date
        available_dates.select{|date| date < order.delivery_at.to_date }.last # # one with date just before delivery date
  end

  class Result
    attr_accessor :delivery_windows, :active_date

    def initialize
      @delivery_windows = {}
      @active_date = nil
    end
  end

end