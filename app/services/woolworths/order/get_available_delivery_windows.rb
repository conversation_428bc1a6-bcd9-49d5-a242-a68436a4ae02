class Woolworths::Order::GetAvailableDeliveryWindows
  class NoAvailabeDeliveryWindowsError < StandardError; end

  def initialize(order:, connection: nil)
    @order = order
    @connection = connection.presence || new_connection
  end

  def call
    cache_available_delivery_windows
    raise NoAvailabeDeliveryWindowsError if available_delivery_windows_for_delivery_day.blank?

    available_delivery_windows_for_delivery_day
  end

private

  attr_reader :order, :connection

  def all_delivery_windows
    @_all_delivery_windows ||= Woolworths::API::GetDeliveryWindows.new(connection: connection).call
  end

  def all_available_delivery_windows
    return @_all_available_delivery_windows if !@_all_available_delivery_windows.nil?

    available_delivery_slots = []
    all_delivery_windows[:windows].each do |day_window|
      next if !day_window[:available]
      next if day_window[:slots].blank?

      available_slots = day_window[:slots].select{|slot| slot[:available] }
      next if available_slots.blank?

      available_delivery_slots += available_slots.map do |slot|
        starts = Time.zone.parse(slot[:starttime])
        ends = Time.zone.parse(slot[:endtime])
        Woolworths::DeliveryWindow.new(
          id: slot[:windowid],
          date: Date.parse(day_window[:date]),
          time_range: (starts..ends),
          label: "#{starts.strftime('%l%P')} - #{ends.strftime('%l%P')}"
        )
      end
    end
    @_all_available_delivery_windows = available_delivery_slots
  end

  # only get back available windows / slots
  def available_delivery_windows_for_delivery_day
    @_available_delivery_windows_for_delivery_day ||= all_available_delivery_windows.select do |delivery_window|
       delivery_window.date == order.delivery_at.to_date
    end
  end

  def cache_available_delivery_windows
    cache_key = Woolworths::API::DELIVERY_WINDOWS_CACHE_KEY.sub(':id', order.id.to_s)
    Rails.cache.write(cache_key, all_available_delivery_windows, expires_in: 1.hour)
  end

  def new_connection
    Woolworths::API::Connection.new(account: order.woolworths_account, authenticated: true)
  end

end
