class Woolworths::Order::SetBestAvailableDeliveryWindow

  def initialize(order:, connection:)
    @order = order
    @connection = connection
  end

  def call
    if available_delivery_windows.present? && selected_delivery_window.present?
      delivery_window_text = Woolworths::API::SetDeliveryWindow.new(order: order, connection: connection, window_id: selected_delivery_window.id).call
      woolworths_order.update!(delivery_window_id: selected_delivery_window.id)
      @order.update(delivery_at: selected_delivery_window.time_range.last)
      delivery_window_text
    end
  end

private

  attr_reader :order, :connection

  def woolworths_order
    @_woolworths_order ||= order.woolworths_order
  end

  def selected_delivery_window
    @_selected_delivery_window ||= Woolworths::SelectDeliveryWindow.new(delivery_at: order.delivery_at, delivery_windows: available_delivery_windows).call
  end

  def available_delivery_windows
    @_available_delivery_windows ||= Woolworths::Order::GetAvailableDeliveryWindows.new(order: order, connection: connection).call
  end

end
