class Woolworths::CheckOrderSync

  RESYNC_THRESHOLD = 30.seconds

  def initialize(order:)
    @order = order
  end

  def call
    all_order_lines_synced? || resync_stale_order_lines
  end

private

  attr_reader :order

  def order_lines
    @_order_lines = order.order_lines
  end

  def order_line_ids
    @_order_line_ids = order_lines.map(&:id)
  end

  def synced_order_line_ids
    @_synced_order_line_ids ||= order.synced_woolworths_order_lines.where(order: order).compact.map(&:id)
  end

  def all_order_lines_synced?
    Utility::MatchArrays.new(synced_order_line_ids, order_line_ids).call
  end

  def resync_stale_order_lines
    order_lines_to_resync = order_lines.where(id: order_line_ids - synced_order_line_ids).where('updated_at <= ?', Time.now - RESYNC_THRESHOLD)
    order_lines_to_resync.each do |order_line|
      order_line.touch # they are no longer stale
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line).call(sync_event: :start)
      Woolworths::ProcessOrderLine.new(order_line: order_line).delay(queue: :instant, attempts: 3).call
    end
    false # returning false as determining that the Order Items are not synced and need to be re-synced
  end

end
