class Woolworths::SelectDeliveryWindow
  MORNING_THRESHOLD_HOUR = 7.freeze

  class NoDeliveryWindowError < StandardError; end

  def initialize(delivery_at:, delivery_windows:)
    @delivery_at = delivery_at
    @delivery_windows = delivery_windows
  end

  def call
    selected_delivery_window = nil
    if delivery_at.present? && delivery_windows.present?
      selected_delivery_window = first_delivery_window_that_includes_delivery_time
      selected_delivery_window = first_delivery_window_after_delivery_time if selected_delivery_window.blank?
    end
    if selected_delivery_window.present?
      selected_delivery_window
    else
      raise NoDeliveryWindowError
    end
  end

private

  attr_reader :delivery_at, :delivery_windows

  def delivery_date
    delivery_at.to_date
  end

  def first_delivery_window_that_includes_delivery_time
    @_first_delivery_window_that_includes_delivery_time ||= delivery_windows.detect do |delivery_window|
      delivery_window.date == delivery_date && delivery_window.time_range.begin >= delivery_date.to_time.change(hour: MORNING_THRESHOLD_HOUR) && delivery_window.overlaps?(delivery_at)
    end
  end

  def first_delivery_window_after_delivery_time
    @_first_delivery_window_after_delivery_time ||= delivery_windows.detect do |delivery_window|
      delivery_window.date == delivery_date && delivery_window.time_range.begin >= delivery_at
    end
  end

end
