class Woolworths::API::GetProductsInTrolley < Woolworths::API
  class TrolleyProductRetrievalError < StandardError; end

  Trolley = Struct.new(:products, :products_count, :delivery_fee, :total_price)
  TrolleyProduct = Struct.new(:menu_item, :quantity)

  def initialize(connection:)
    @connection = connection
  end

  def call
    trolley_response = @connection.request(method: :get, path: TROLLEY_ENDPOINT)

    raise TrolleyProductRetrievalError unless trolley_response.status == 200

    Trolley.new(
      trolley_response.body[:items].map { |product_data| build_trolley_product(product_data) },
      trolley_response.body[:totalproducts],
      trolley_response.body[:deliveryfee],
      trolley_response.body[:totaltrolleyprice]
    )
  end

private

  attr_reader :connection

  def supplier
    @_supplier ||= SupplierProfile.woolworths
  end

  def build_trolley_product(product_data)
    menu_item = supplier.menu_items.where(sku: product_data[:article]).first
    TrolleyProduct.new(menu_item, product_data[:itemquantityintrolley])
  end
end
