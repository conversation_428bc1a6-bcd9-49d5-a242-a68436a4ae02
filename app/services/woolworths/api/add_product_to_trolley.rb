class Woolworths::API::AddProductToTrolley < Woolworths::API
  class ExceededDeliverySaversLimitError < StandardError; end
  class CannotProcessProductToTrolley < StandardError; end

  TrolleyProduct = Struct.new(:menu_item, :available, :stock_quantity, :instore_price)

  # product is hash with keys menu_item and quantity.
  def initialize(connection:, product:)
    @connection = connection
    @product = product
  end

  def call
    response = connection.request(method: :post, path: TROLLEY_ITEMS_ENDPOINT, body: { items: products_data, replacetrolley: false })

    raise CannotProcessProductToTrolley unless response.status == 200

    raise ExceededDeliverySaversLimitError if response.body.key?(:errors) && response.body[:errors].first[:message].include?('Delivery Savers')

    response_product = response.body[:items].detect{|trolley_item| trolley_item[:article] == product[:menu_item].sku }

    return nil if response_product.blank?
    
    # get max quantity and compare against trolley quantity
    trolley_quantity = response_product[:itemquantityintrolley]
    max_quantity = response_product[:maxquantitylimit]
    stock_quantity = nil
    if max_quantity.present? && (max_quantity < trolley_quantity || trolley_quantity != product[:quantity])
      stock_quantity = max_quantity
    end

    is_available = product[:quantity].present? && (stock_quantity.present? || response_product[:is][:ranged])
    instore_price = response_product.dig(:instoreprice, :pricegst)
    promotion_price = response_product.dig(:promotions, :price)
    trolley_product_price = (promotion_price.presence || instore_price)

    TrolleyProduct.new(product[:menu_item], is_available, stock_quantity, trolley_product_price)
  end

private

  attr_reader :connection, :product

  def products_data
    [
      {
        article: product[:menu_item].sku,
        itemquantityintrolley: product[:quantity]
      }
    ]
  end
end
