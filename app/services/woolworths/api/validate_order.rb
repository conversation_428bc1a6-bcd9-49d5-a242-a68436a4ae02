class Woolworths::API::ValidateOrder < Woolworths::API

  def initialize(connection:)
    @connection = connection
    @result = Result.new
  end

  def call
    response = connection.request(method: :get, path: VALIDATE_ENDPOINT)

    case response.status
    when 200
      retrieve_delivery_fee_from(response)
    when 400
      result.errors += sanitized_errors_from(response)
    else
      result.errors << 'Could not validate the order with Woolworths'
    end
    result
  end

private

  attr_reader :connection, :result

  def retrieve_delivery_fee_from(response)
    delivery_fee = response.body[:deliveryFee].present? ? response.body[:deliveryFee] : nil
    if delivery_fee.present?
      packaging_fee = response.body[:packagingFee].present? && response.body[:packagingFee][:amount].present? ? response.body[:packagingFee][:amount] : nil

      result.delivery_fee = [delivery_fee, packaging_fee].reject(&:blank?).sum.presence
    end
  end

  def sanitized_errors_from(response)
    response.body[:errors].map do |error|
      if error[:Key] == 'MinimumSpend'
        'MinimumSpend'
      else
        error[:Message]
      end
    end
  end

  class Result
    attr_accessor :delivery_fee, :errors

    def initialize
      @delivery_fee = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
