class Woolworths::API::Connection < Woolworths::API
  class NoAccountError < StandardError; end

  class ConnectionError < StandardError
    def initialize(msg = '')
        super(msg)
    end
  end

  Response = Struct.new(:status, :body)

  attr_reader :access_token, :fulfilment_store_id

  def initialize(account: nil, use_importer_account: false, authenticated: false)
    @account = account
    @use_importer_account = use_importer_account
    @authenticated = authenticated

    build_connection
  end

  def authenticate(refresh: false)
    authenticate_path = AUTHENTICATION_ENDPOINT
    authenticate_path += "/#{account.refresh_token}" if refresh && account.present?
    authentication_response = request(method: :post, path: authenticate_path, body: authentication_credentials)

    case
    when refresh && authentication_response.status != 200 # refresh token is invalid
      authenticate(refresh: false) && return
    when authentication_response.status != 200
      raise ConnectionError.new("Woolworths API authentication failure #{authentication_response.body}")
    end

    if account.present?
      account.update(
        access_token: authentication_response.body[:access_token],
        refresh_token: authentication_response.body[:refresh_token],
        token_expires_at: Time.zone.now + authentication_response.body[:expires_in].to_i.seconds
      )
    end

    @access_token = authentication_response.body[:access_token]
    @fulfilment_store_id = authentication_response.body[:fulfilmentstoreid]
    @primary_address = {
      street_1: authentication_response.body[:deliveryaddressstreet1],
      street_2: authentication_response.body[:deliveryaddressstreet2],
      suburb: authentication_response.body[:deliveryaddresssuburb].titleize
    }

    # Build a new connection now that we have an access token.
    build_api_connection
  end

  def request(method:, path:, params: nil, body: nil)
    raise "Unsupported HTTP method: #{method}" unless method.in?(%i[get post delete])

    response = @connection.send(method) do |request|
      request.url(path)
      request.params = params if params

      if method == :post && body
        request.body = body.to_json
      end
    end

    Response.new(response.status, JSON.parse(response.body).deep_symbolize_keys)
  end

  def primary_address
    raise 'Cannot determine primary address without being authenticated' if @connection.blank? || @access_token.blank?

    @primary_address
  end

private

  attr_reader :authenticated, :account

  def build_connection
    if authenticated
      case
      when account.blank?
        raise NoAccountError
      when account.access_token.blank? || account.refresh_token.blank?
        build_api_connection && authenticate
      when account.token_expires_at.present? && account.token_expires_at < Time.zone.now
        build_api_connection && authenticate(refresh: true)
      when account.access_token.present?
        @access_token = account.access_token
        build_api_connection
      else
        build_api_connection && authenticate
      end
    else
      build_api_connection
    end
  end

  def build_api_connection
    if (proxy_server = yordar_credentials(:woolworths, :proxy_server).presence)
      @connection = Faraday.new(url: BASE_URL, proxy: proxy_server) {|faraday| connection_body(faraday) }
    else
      @connection = Faraday.new(url: BASE_URL) {|faraday| connection_body(faraday) }
    end
  end

  def connection_body(faraday)
    faraday.response(:logger, Logger.new(STDOUT), bodies: true) unless yordar_credentials(:disable, :faraday_logging)
    faraday.adapter(Faraday.default_adapter)

    faraday.headers['Content-Type'] = 'application/json'
    faraday.headers['X-Api-Key'] = yordar_credentials(:woolworths, :api_key)
    faraday.headers['Authorization'] = "Bearer #{@access_token}" if @access_token
  end

  def authentication_credentials
    case
    when @use_importer_account
      { user_name: yordar_credentials(:woolworths, :yordar_account_email), password: yordar_credentials(:woolworths, :yordar_account_password) }
    when account.present?
      { user_name: account.email, password: account.password }
    else
      raise NoAccountError
    end
  end
end
