class Woolworths::API::DeleteAddressById < Woolworths::API

  def initialize(account:, address_id:, connection: nil)
    @account = account
    @address_id = address_id
    @connection = connection.presence || new_connection_for_account
  end

  def call
    delete_address_response = connection.request(method: :delete, path: ADDRESS_ENDPOINT + "/#{address_id}")

    if delete_address_response.status == 200
      true
    else
      raise "Could not delete the address for account ##{account.id} with ID #{address_id}"
    end
  end

private

  attr_reader :account, :address_id, :connection

  def new_connection_for_account
    Woolworths::API::Connection.new(account: account, authenticated: true)
  end

end
