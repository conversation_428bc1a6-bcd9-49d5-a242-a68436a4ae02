class Woolworths::API::Add<PERSON>ddress < Woolworths::API

  def initialize(connection:, street_1:, suburb:, postcode:, street_2: nil)
    @connection = connection

    @street_1 = street_1
    @street_2 = street_2
    @suburb = suburb
    @postcode = postcode
  end

  def call
    Woolworths::API::DeleteAddress.new(connection: connection, street_1: street_1, street_2: street_2, suburb: suburb, postcode: postcode).call rescue nil

    add_address_response = connection.request(method: :post, path: ADDRESS_ENDPOINT, body: {
      Street1: street_1,
      Street2: street_2,
      SuburbId: woolworths_suburb_id,
      Postcode: postcode,
      IsForBilling: false
    })

    raise 'Could not add address' unless add_address_response.status == 200

    add_address_response.body
  end

private

  attr_reader :connection, :street_1, :street_2, :suburb, :postcode

  def woolworths_suburb_id
    return @_woolworths_suburb_id if @_woolworths_suburb_id.present?

    suburb_search_response = connection.request(method: :get, path: ADDRESS_ENDPOINT, params: { postcode: postcode })
    raise "Could not search for suburb by postcode: #{postcode}" unless suburb_search_response.status == 200

    woolworths_suburb = suburb_search_response.body[:suburbs].detect { |response_suburb| response_suburb[:text].strip.downcase == suburb.downcase }
    raise "Could not find a suburb with postcode '#{postcode}' and name '#{suburb}'" unless woolworths_suburb

    @_woolworths_suburb_id = woolworths_suburb[:id]
  end
end
