class Woolworths::API::GetAddress < Woolworths::API

  def initialize(connection:, street_1:, suburb:, postcode:, street_2: nil)
    @connection = connection

    @street_1 = street_1
    @street_2 = street_2
    @suburb = suburb
    @postcode = postcode
  end

  def call
    response = connection.request(method: :get, path: ADDRESS_ENDPOINT)
    raise 'Could not retrieve saved addresses' unless response.status == 200

    response.body[:addresses].detect do |address|
      (address[:street1] == street_1) && (address[:street2] == street_2.to_s) && (address[:suburbname].strip.downcase == suburb.downcase) && (address[:postalcode] == postcode.to_s)
    end
  end

private

  attr_reader :connection, :street_1, :street_2, :suburb, :postcode

end
