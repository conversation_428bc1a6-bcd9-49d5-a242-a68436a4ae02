class Woolworths::API::CheckoutOrder < Woolworths::API
  class CheckoutPaymentError < StandardError; end

  def initialize(order:)
    @order = order
  end

  def call
    return if woolworths_account.blank?

    connection = Woolworths::API::Connection.new(account: woolworths_account, authenticated: true)
    checkout_response = connection.request(method: :post, path: CHECKOUT_ENDPOINT, body: { Password: woolworths_account.password })

    update_woolworths_order(checkout_response: checkout_response)
  end

private

  attr_reader :order

  def woolworths_order
     @_woolworths_order ||= order.woolworths_order
  end

  def woolworths_account
    @_woolworths_account ||= woolworths_order.account
  end

  def update_woolworths_order(checkout_response:)
    checkout_successful = checkout_response.status == 200 && checkout_response.body[:IsSuccessful]
    woolworths_order_id = checkout_response.body[:OrderId]
    status = woolworths_order.status.present? ? "#{woolworths_order.status} " : ''
    if checkout_successful
      status += "Confirmed. Woolworths Order #{woolworths_order_id}."
    else
      status = 'Payment with Woolworths errored.'
      capture_error(checkout_response)
      notify_admin
      log_event
    end
    woolworths_order.update(
      woolworths_order_id: woolworths_order_id,
      status: status,
      account_in_use: false # disconnect from account
    )
  end

  def capture_error(checkout_response)
    Raven.capture_exception(CheckoutPaymentError.new,
        message: "Payment error with Woolworths. #{checkout_response.status}",
        extra: { order: order.id, woolworths_account: woolworths_account.email, response: checkout_response.body },
        transaction: 'Woolworths::API::CheckoutOrder'
      )
  end

  def notify_admin
    Admin::Emails::SendFailedWoolworthsCheckoutEmail.new(order: order).delay(queue: :instant).call
  end

  def log_event
    EventLogs::Create.new(event_object: order, event: 'woolworths-checkout-failed', severity: 'error', account: woolworths_account.short_name).delay(queue: :notifications).call
  end
end
