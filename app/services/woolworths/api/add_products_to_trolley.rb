class Woolworths::API::AddProductsToTrolley < Woolworths::API
  class ExceededDeliverySaversLimitError < StandardError; end
  class CannotProcessProductToTrolley < StandardError; end

  TrolleyProduct = Struct.new(:menu_item, :available, :stock_quantity, :instore_price)

  # products is an array of hashes with keys menu_item and quantity.
  def initialize(connection:, products:)
    @connection = connection
    @products = products
  end

  def call
    response = connection.request(method: :post, path: TROLLEY_ITEMS_ENDPOINT, body: { items: products_data, replacetrolley: false })

    raise CannotProcessProductToTrolley unless response.status == 200

    raise ExceededDeliverySaversLimitError if response.body.key?(:errors) && response.body[:errors].first[:message].include?('Delivery Savers')

    # response body returns all items in the trolley
    response.body[:items].map do |response_product|
      added_product = products.detect{|product| product[:menu_item].sku == response_product[:article] }

      # Only return products that were added to the trolley in this execution,
      # not previously.
      next if added_product.blank?

      # get max quantity and compare against trolley quantity
      trolley_quantity = response_product[:itemquantityintrolley]
      max_quantity = response_product[:maxquantitylimit]
      stock_quantity = nil
      if max_quantity.present? && (max_quantity < trolley_quantity || trolley_quantity != added_product[:quantity])
        stock_quantity = max_quantity
      end

      is_available = added_product[:quantity].present? && (stock_quantity.present? || response_product[:is][:ranged])
      instore_price = response_product.dig(:instoreprice, :pricegst)

      TrolleyProduct.new(added_product[:menu_item], is_available, stock_quantity, instore_price)
    end.compact
  end

private

  attr_reader :connection, :products

  def products_data
    products.map do |product|
      {
        article: product[:menu_item].sku,
        itemquantityintrolley: product[:quantity]
      }
    end
  end
end
