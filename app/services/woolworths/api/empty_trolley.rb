class Woolworths::API::EmptyTrolley < Woolworths::API
  class EmptyTrolleyError < StandardError; end

  def initialize(order:, connection: nil)
    @order = order
    @connection = connection.presence || Woolworths::API::Connection.new(account: @order.woolworths_account, authenticated: true)
  end

  def call
    response = connection.request(method: :post, path: EMPTY_TROLLEY_ENDPOINT)
    raise EmptyTrolleyError.new unless response.status == 200
  end

private

  attr_reader :order, :connection

end
