class Woolworths::API::SetDeliveryAddress < Woolworths::API

  def initialize(connection:, address_id:)
    @connection = connection
    @address_id = address_id
  end

  def call
    response = connection.request(method: :post, path: DELIVERY_ADDRESS_ENDPOINT, body: { address: address_id })
    raise 'Could not set delivery address' unless response.status == 200

    true
  end

private

  attr_reader :connection, :address_id

end
