class Woolworths::API::DeleteAddress < Woolworths::API

  def initialize(connection:, street_1:, suburb:, postcode:, street_2: nil)
    @connection = connection

    @street_1 = street_1
    @street_2 = street_2
    @suburb = suburb
    @postcode = postcode
  end

  def call
    if address_in_woolworths.present?
      delete_address_response = connection.request(method: :delete, path: ADDRESS_ENDPOINT + "/#{address_in_woolworths[:id]}")
      raise 'Could not delete the address' unless delete_address_response.status == 200
    end

    true
  end

private

  attr_reader :connection, :street_1, :street_2, :suburb, :postcode

  def address_in_woolworths
    @_address_in_woolworths ||= Woolworths::API::GetAddress.new(connection: connection, street_1: street_1, street_2: street_2, suburb: suburb, postcode: postcode).call
  end
end
