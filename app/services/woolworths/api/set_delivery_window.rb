class Woolworths::API::SetDeliveryWindow < Woolworths::API
  class DeliveryWindowError < StandardError
    def initialize(message = '')
        super(message)
    end
  end

  def initialize(order:, connection: nil, window_id: nil)
    @order = order
    @connection = connection.presence || get_order_connection
    @window_id = window_id.presence || woolworths_order.delivery_window_id
    @delivery_window_text = ''
  end

  def call
    response = connection.request(method: :post, path: DELIVERY_WINDOW_ENDPOINT, body: {
      window: window_id,
      date: window_date
    })
    if response.status == 200
      if response.body[:delivery].present? && response.body[:delivery][:window].present? && response.body[:delivery][:window][:text].present?
        @delivery_window_text = response.body[:delivery][:window][:text]
        
        save_selected_delivery_window
      end
    else
      raise DeliveryWindowError.new('Could not set the delivery window')
    end
    delivery_window_text
  end

private

  attr_reader :order, :connection, :window_id, :delivery_window_text

  def get_order_connection
    Woolworths::API::Connection.new(account: order.woolworths_account, authenticated: true)
  end

  # window_date is the date of the day of the window, not the starttime of the window/slot.
  def window_date
    order.delivery_at.strftime('%Y-%m-%dT%H:%M:00.0000000')
  end

  def woolworths_order
    @_woolworths_order ||= order.woolworths_order
  end

  def save_selected_delivery_window
    woolworths_order&.update(delivery_window_text: delivery_window_text)
  end

end
