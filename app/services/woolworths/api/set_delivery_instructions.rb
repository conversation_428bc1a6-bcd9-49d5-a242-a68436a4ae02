class Woolworths::API::SetDeliveryInstructions < Woolworths::API

  def initialize(order:, connection:)
    @order = order
    @connection = connection
  end

  def call
    return if sanitized_instructions.blank?

    response = connection.request(method: :post, path: DELIVERY_INSTRUCTIONS_ENPOINT, body: { instructions: sanitized_instructions })
    raise 'Could not set delivery instructions' unless response.status == 200

    true
  end

private

  attr_reader :order, :connection

  def sanitized_instructions
    order.delivery_instruction&.gsub(/\n/, '')
  end
end
