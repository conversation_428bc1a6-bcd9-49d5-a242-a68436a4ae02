# Warning.. only use this on test or UAT Woolworths servers as the accounts on live site are created using the live Website.
class Woolworths::API::SignUp < Woolworths::API

  Response = Struct.new(:status, :body)

  def initialize(credentials: {})
    @credentials = credentials
    build_connection
  end

  def call
    request(method: :post, path: SIGNUP_ENDPOINT, body: signup_body)
  end

private

  attr_reader :credentials

  def request(method:, path:, params: nil, body: nil)
    response = @connection.send(method) do |request|
      request.url(path)
      request.params = params if params
      if method == :post && body
        request.body = body.to_json
      end
    end
    Response.new(response.status, JSON.parse(response.body).deep_symbolize_keys)
  end

  def build_connection
    @connection = Faraday.new(url: BASE_URL) {|faraday| connection_body(faraday) }
  end

  def connection_body(faraday)
    faraday.response(:logger, Logger.new(STDOUT), bodies: true) unless yordar_credentials(:disable, :faraday_logging)
    faraday.adapter(Faraday.default_adapter)

    faraday.headers['Content-Type'] = 'application/json'
    faraday.headers['X-Api-Key'] = yordar_credentials(:woolworths, :api_key)
  end

  def signup_body
    {
      'firstname': 'Yordar 1',
      'lastname': 'Tester 2',
      'emailaddress': credentials[:email],
      'password': credentials[:password],
      'dateofbirth': '01/12/1979',
      'mobilephone': '0421000000',
      # 'workphone': '',
      # 'edrnumber': '',
      # 'emailproductsandservices': 'false',
      # 'smsproductsservicesandpromotions': 'false',
      'campaignname': 'testcampaign',
      'isbusinessshopper': 'true',
      # 'deliveryaddress': {
      #     'addressstreet1': '85 William Street',
      #     'addressstreet2': '',
      #     'addresssuburb': 'Darlinghurst',
      #     'addresspostcode': '2010'
      # },
      'businessdetails': {
          'abn': '***********',
          'companyname': 'yordar',
          'businesstype': '',
          'jobtitle': ''
      }
    }.to_json
  end

end
