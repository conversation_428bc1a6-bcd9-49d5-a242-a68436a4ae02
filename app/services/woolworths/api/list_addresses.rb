class Woolworths::API::ListAddresses < Woolworths::API

  def initialize(account:, connection: nil)
    @account = account
    @connection = connection.presence || new_connection_for_account
  end

  def call
    response = connection.request(method: :get, path: ADDRESS_ENDPOINT)
    if response.status == 200
      response.body[:addresses]
    else
      raise "Could not retrieve saved addresses for account with ID #{account.id} - #{response.body[:errorDetail][:Message]}"
    end
  end

private

  attr_reader :account, :connection

  def new_connection_for_account
    Woolworths::API::Connection.new(account: account, authenticated: true)
  end

end
