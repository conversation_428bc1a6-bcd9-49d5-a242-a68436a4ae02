class Stats::ListOrders

  def initialize(starts:, ends:)
    @starts = starts
    @ends = ends
  end

  def call
    return [] if starts.blank? || ends.blank? || ends < starts

    orders = Order.where(status: 'delivered')
    orders = orders.where(delivery_at: [starts..ends])
    orders = orders.includes(:customer_profile, order_lines: :category)
    orders
  end

private

  attr_reader :starts, :ends

end
