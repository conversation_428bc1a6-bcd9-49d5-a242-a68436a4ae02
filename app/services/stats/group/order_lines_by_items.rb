class Stats::Group::OrderLinesByItems
  def initialize(orders:)
    @orders = orders
    @item_grouped_order_lines = []
  end

  def call
    group_by_items
    item_grouped_order_lines
  end

private

  attr_reader :orders
  attr_accessor :item_grouped_order_lines

  def group_by_items
    order_lines = OrderLine.where(order: orders).includes(:serving_size, menu_item: { menu_section: :categories })
    @item_grouped_order_lines = order_lines.group_by do |order_line|
      order_line.serving_size.presence || order_line.menu_item
    end
  end
end
