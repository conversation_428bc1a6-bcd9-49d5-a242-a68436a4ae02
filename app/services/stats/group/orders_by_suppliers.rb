class Stats::Group::OrdersBySuppliers

  def initialize(orders:, verbose: false)
    @orders = orders
    @verbose = verbose
    @supplier_grouped_orders = []
    @multiple_supplier_orders = []
  end

  def call
    group_orders_by_suppliers
    supplier_grouped_orders
  end

private

  attr_reader :orders, :verbose
  attr_accessor :supplier_grouped_orders, :multiple_supplier_orders

  def group_orders_by_suppliers
    puts 'Grouping orders by supplier profiles' if verbose
    suppliers_grouped_orders = orders.group_by do |order|
      suppliers = order.supplier_profiles
      suppliers.size > 1 ? nil : suppliers.first
    end
    suppliers_grouped_orders.each do |supplier, supplier_orders|
      if supplier.present?
        @supplier_grouped_orders << [supplier, supplier_orders] if supplier.present?
      else
        @multiple_supplier_orders += supplier_orders
      end
      print 'g-' if verbose
    end
  end

end

