class Stats::Group::OrdersByCustomer
  def initialize(orders:)
    @orders = orders
    @customer_grouped_orders = []
  end

  def call
    group_orders_by_year
    customer_grouped_orders
  end

private

  attr_reader :orders
  attr_accessor :customer_grouped_orders

  def group_orders_by_year
    orders.group_by{|order| order.delivery_at.year }.each do |year, year_orders|
      group_by_month(year, year_orders)
    end
  end

  def group_by_month(year, year_orders)
    year_orders.group_by{|x| x.delivery_at.month }.each do |month, month_orders|
      group_by_customer(year, month, month_orders)
    end
  end

  def group_by_customer(year, month, month_orders)
    month_orders.group_by(&:customer_profile).each do |customer, customer_orders|
      @customer_grouped_orders << [year, month, customer, customer_orders]
      print 'g-'
    end
  end
end
