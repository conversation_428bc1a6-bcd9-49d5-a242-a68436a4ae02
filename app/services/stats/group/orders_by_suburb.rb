class Stats::Group::OrdersBySuburb

  def initialize(orders:)
    @orders = orders
    @location_grouped_orders = []
  end

  def call
    group_orders_by_year
    location_grouped_orders
  end

private

  attr_reader :orders
  attr_accessor :location_grouped_orders

  def group_orders_by_year
    orders.group_by{|order| order.delivery_at.year }.each do |year, year_orders|
      group_by_month(year, year_orders)
    end
  end

  def group_by_month(year, year_orders)
    year_orders.group_by{|x| x.delivery_at.month }.each do |month, month_orders|
      group_by_delivery_suburb(year, month, month_orders)
    end
  end

  def group_by_delivery_suburb(year, month, month_orders)
    month_orders.group_by(&:delivery_suburb).each do |suburb, suburb_orders|
      @location_grouped_orders << [year, month, suburb, suburb_orders]
      print 'g-'
    end
  end

end

