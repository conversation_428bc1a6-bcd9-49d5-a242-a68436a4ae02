class Stats::Group::OrdersByVariantAndCategory

  def initialize(orders:)
    @orders = orders
    @category_grouped_orders = []
  end

  def call
    group_orders_by_year
    category_grouped_orders
  end

private

  attr_reader :orders
  attr_accessor :category_grouped_orders

  def group_orders_by_year
    orders.group_by{|order| order.delivery_at.year }.each do |year, year_orders|
      group_by_month(year, year_orders)
    end
  end

  def group_by_month(year, year_orders)
    year_orders.group_by{|x| x.delivery_at.strftime('%B') }.each do |month, month_orders|
      group_by_order_variant(year, month, month_orders)
    end
  end

  def group_by_order_variant(year, month, month_orders)
    grouped_orders = month_orders.group_by{|order| order.order_variant == 'event_order' ? 'general' : order.order_variant }
    grouped_orders.each do |variant, variant_orders|
      if %w[team_order recurring_team_order].include?(variant)
        @category_grouped_orders << [year, month, variant, 'catering', variant_orders]
        print 'g-'
      else
        group_by_order_type(year, month, variant_orders)
      end
    end
  end

  def group_by_order_type(year, month, variant_orders)
    grouped_orders = variant_orders.group_by(&:order_type)
    grouped_orders.each do |type, type_orders|
      group_by_order_category(year, month, type, type_orders)
    end
  end

  def group_by_order_category(year, month, type, type_orders)
    catering_orders, pantry_orders = type_orders.partition do |order|
      print 'o-'
      order.ordered_categories.where(group: 'catering-services').present?
    end
    if catering_orders.present?
      @category_grouped_orders << [year, month, type, 'catering', catering_orders]
      print 'g-'
    end
    if pantry_orders.present?
      @category_grouped_orders << [year, month, type, 'pantry', pantry_orders]
      print 'g-'
    end
  end

end

