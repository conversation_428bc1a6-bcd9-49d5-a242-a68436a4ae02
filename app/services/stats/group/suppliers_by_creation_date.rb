class Stats::Group::SuppliersByCreationDate
  def initialize(suppliers:)
    @suppliers = suppliers
    @month_grouped_suppliers = []
  end

  def call
    group_suppliers_by_year
    month_grouped_suppliers
  end

private

  attr_reader :suppliers
  attr_accessor :month_grouped_suppliers

  def group_suppliers_by_year
    suppliers.group_by{|supplier| supplier.created_at.year }.each do |year, year_suppliers|
      group_by_month(year, year_suppliers)
    end
  end

  def group_by_month(year, year_suppliers)
    year_suppliers.group_by{|supplier| supplier.created_at.month }.each do |month, month_suppliers|
      @month_grouped_suppliers << [year, month, month_suppliers]
    end
  end
end
