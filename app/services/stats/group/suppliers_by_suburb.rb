class Stats::Group::SuppliersBySuburb

  def initialize(suppliers:)
    @suppliers = suppliers
    @suburb_grouped_suppliers = []
  end

  def call
    group_suppliers_by_suburb
    sort_by_suburb_postcode
    suburb_grouped_suppliers
  end

private

  attr_reader :suppliers
  attr_accessor :suburb_grouped_suppliers

  def group_suppliers_by_suburb
    suppliers.group_by(&:company_address_suburb).each do |suburb, suburb_suppliers|
      supplier_suburb = suburb.present? ? suburb : Suburb.new(name: 'Unknown', state: '-', postcode: 9999)
      @suburb_grouped_suppliers << [supplier_suburb, suburb_suppliers]
    end
  end

  def sort_by_suburb_postcode
    @suburb_grouped_suppliers = suburb_grouped_suppliers.sort_by do |suburb, _|
      print 'g-'
      suburb.postcode.to_i
    end
  end

end
