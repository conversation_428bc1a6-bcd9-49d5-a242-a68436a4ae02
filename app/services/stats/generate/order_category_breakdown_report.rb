class Stats::Generate::OrderCategoryBreakdownReport < Stats::Generate::Base

  HEADERS = %w[year month type category number_of_orders non_gst_total gst_total customer_total].freeze

  def initialize(report_data:, report_name: 'order_category_revenue', verbose: false, include_order_ids: false)
    @report_data = report_data
    @include_order_ids = include_order_ids
    super(report_name: report_name, verbose: verbose)
  end

  def call
    rows = generate_data
    generate_report_with(rows: rows)

    file_path
  end

private

  attr_reader :report_data, :include_order_ids

  def csv_headers
    headers = HEADERS.dup
    headers << 'order_ids' if include_order_ids
    headers
  end

  def generate_data
    rows = []
    report_data.each do |year, month, type, category, category_orders|
      customer_total, customer_gst_total, customer_non_gst_total = customer_totals_for(category_orders)
      rows << [
        year,
        month,
        type,
        category,
        category_orders.size,
        customer_non_gst_total,
        customer_gst_total,
        customer_total
      ]
      row << category_orders.map(&:id).join('|') if include_order_ids
      print 'd-' if verbose
    end
    rows
  end

  def customer_totals_for(orders)
    totals = orders.sum(&:customer_total)
    gst_totals = orders.sum(&:customer_gst)
    non_gst_totals = totals - gst_totals
    [totals, gst_totals, non_gst_totals]
  end

end
