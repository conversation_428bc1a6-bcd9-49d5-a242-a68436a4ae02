class Stats::Generate::CategorySpendsReport < Stats::Generate::Base

  CATEGORY_COLUMS = %w[group category].freeze
  CUSTOMER_COLUMNS = %w[year month customer company email order_size].freeze
  TOTAL_COLUMNS = %w[non_gst_total gst_total customer_total non_gst_cost cost_gst cost_total].freeze
  HEADERS = (CATEGORY_COLUMS + CUSTOMER_COLUMNS + TOTAL_COLUMNS).freeze

  def initialize(report_data:, report_name: 'category_stats_with_costs', verbose: false, include_order_ids: false)
    @report_data = report_data
    @include_order_ids = include_order_ids
    super(report_name: report_name, verbose: verbose)
  end

  def call
    rows = generate_data
    generate_report_with(rows: rows)

    file_path
  end

private

  attr_reader :report_data, :include_order_ids

  def csv_headers
    headers = HEADERS.dup
    headers << 'order_ids' if include_order_ids
    headers
  end

  def generate_data
    rows = []
    report_data.each do |year, month, customer, customer_orders|
      category_grouped_orders = customer_orders.group_by{|order| major_category_for(order) }

      category_grouped_orders.each do |category, category_orders|
        category = category.presence || Category.new(slug: 'no-category', group: 'no-category')

        customer_total, customer_gst_total, customer_non_gst_total = customer_totals_for(category_orders)
        supplier_total, supplier_gst_total, supplier_non_gst_total = supplier_totals_for(category_orders)

        row = [
          category.group,
          category.slug,
          year,
          Date::MONTHNAMES[month],
          customer.name,
          customer.company.try(:name),
          customer.user.try(:email_recipient),
          category_orders.size,
          customer_non_gst_total,
          customer_gst_total,
          customer_total,
          supplier_non_gst_total,
          supplier_gst_total,
          supplier_total
        ]
        row << (category.slug == 'no-category' ? category_orders.map(&:id).join('|') : '-') if include_order_ids
        rows << row
        print 'd-' if verbose
      end
    end
    rows
  end

  def major_category_for(order)
    Orders::RetrieveMajorOrderCategory.new(order: order).call
  end

  def customer_totals_for(orders)
    totals = orders.sum(&:customer_total)
    gst_totals = orders.sum(&:customer_gst)
    non_gst_totals = totals - gst_totals
    [totals, gst_totals, non_gst_totals]
  end

  def supplier_totals_for(orders)
    supplier_totals = orders.map do |order|
      order.order_suppliers.where.not(total: nil).map do |order_supplier|
        {
          total: order_supplier.total,
          gst: order_supplier.gst,
        }
      end
    end.flatten
    totals = supplier_totals.sum{|supplier| supplier[:total] }
    gst_totals = supplier_totals.sum{|supplier| supplier[:gst] }
    non_gst_totals = totals - gst_totals
    [totals, gst_totals, non_gst_totals]
  end
end
