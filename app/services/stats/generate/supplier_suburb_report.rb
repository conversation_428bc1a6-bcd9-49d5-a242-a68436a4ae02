class Stats::Generate::SupplierSuburbReport < Stats::Generate::Base

  HEADERS = %w[suburb state postcode supplier_name].freeze

  def initialize(report_data:, report_name: 'supplier_suburb_stats', verbose: false)
    @report_data = report_data
    super(report_name: report_name, verbose: verbose)
  end

  def call
    rows = generate_data
    generate_report_with(rows: rows)

    file_path
  end

private

  attr_reader :report_data, :include_order_ids

  def csv_headers
    HEADERS
  end

  def generate_data
    rows = []
    report_data.each do |suburb, suburb_suppliers|
      suburb_suppliers.each do |supplier|
        row = [
          suburb.name,
          suburb.state,
          suburb.postcode,
          supplier.name
        ]
        rows << row

        print 'm-'
      end
    end
    rows
  end
end
