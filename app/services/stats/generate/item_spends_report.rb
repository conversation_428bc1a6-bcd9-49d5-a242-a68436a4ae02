class Stats::Generate::ItemSpendsReport < Stats::Generate::Base

  CATEGORY_COLUMNS = %w[item category_name category_group number_of_items].freeze
  TOTALS_COLUMNS = %w[non_gst_total gst_total customer_total non_gst_cost cost_gst cost_total].freeze
  HEADERS = (CATEGORY_COLUMNS + TOTALS_COLUMNS).freeze

  def initialize(report_data:, report_name: 'item_stats_with_costs', verbose: false)
    @report_data = report_data
    super(report_name: report_name, verbose: verbose)
  end

  def call
    rows = generate_data
    generate_report_with(rows: rows)

    file_path
  end

private

  attr_reader :report_data

  def csv_headers
    HEADERS
  end

  def generate_data
    rows = []

    report_data.each do |grouping, group_order_lines|
      non_gst_lines, gst_lines = group_order_lines.partition(&:is_gst_free)
      if grouping.is_a?(ServingSize)
        grouping_name = "#{grouping.menu_item.name} - #{grouping.name}"
        category = grouping.menu_item.menu_section.categories.order(weight: :asc).first
      else
        grouping_name = grouping.name
        category = grouping.menu_section.categories.order(weight: :asc).first
      end
      # grouping_name += " (#{grouping.class.name}-#{grouping.id})"

      customer_total, customer_gst_total, customer_non_gst_total = customer_totals_for(gst_lines, non_gst_lines)
      supplier_total, supplier_gst_total, supplier_non_gst_total = supplier_totals_for(gst_lines, non_gst_lines)

      row = [
        grouping_name,
        (category&.name || '-'),
        (category&.group || '-'),
        group_order_lines.sum(&:quantity),
        customer_non_gst_total,
        customer_gst_total,
        customer_total,
        supplier_non_gst_total,
        supplier_gst_total,
        supplier_total
      ]
      rows << row
      print 'm-'
    end
    rows
  end

  def customer_totals_for(gst_lines, non_gst_lines)
    gst_totals = gst_lines.sum(&:total_price)
    non_gst_totals = non_gst_lines.sum(&:total_price)
    totals = gst_totals + non_gst_totals
    [totals, gst_totals, non_gst_totals]
  end

  def supplier_totals_for(gst_lines, non_gst_lines)
    gst_totals = gst_lines.sum(&:total_cost)
    non_gst_totals = non_gst_lines.sum(&:total_cost)
    totals = gst_totals + non_gst_totals
    [totals, gst_totals, non_gst_totals]
  end

end
