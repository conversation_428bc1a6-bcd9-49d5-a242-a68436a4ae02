class Stats::Generate::CategoryRevenueReport < Stats::Generate::Base

  HEADERS = %w[supplier category order_size non_gst_cost cost_gst cost_total].freeze

  def initialize(report_data:, report_name: 'category_revenue_with_costs', verbose: false, include_order_ids: false)
    @report_data = report_data
    @include_order_ids = include_order_ids
    super(report_name: report_name, verbose: verbose)
  end

  def call
    rows = generate_data
    generate_report_with(rows: rows)

    file_path
  end

private

  attr_reader :report_data, :include_order_ids

  def csv_headers
    headers = HEADERS.dup
    headers << 'order_ids' if include_order_ids
    headers
  end

  def generate_data
    rows = []
    report_data.each do |supplier, supplier_orders|
      supplier_total, supplier_gst_total, supplier_non_gst_total = supplier_totals_for(supplier: supplier, orders: supplier_orders)
      row = [
        supplier.name,
        major_category_for(supplier),
        supplier_orders.size,
        supplier_non_gst_total,
        supplier_gst_total,
        supplier_total
      ]
      row << supplier_orders.map(&:id).join('|') if include_order_ids
      rows << row
      print 'd-' if verbose
    end
    rows
  end

  def major_category_for(supplier)
    menu_sections = supplier.menu_sections.where(archived_at: nil).includes(:categories)
    group_grouped_categories = menu_sections.map(&:categories).flatten.uniq.group_by(&:group)
    major_category_group = group_grouped_categories.max_by{|_, categories| categories.size }&.first
    print 'c-' if verbose
    if major_category_group.present?
      major_category_group == 'kitchen-supplies' ? 'Pantry' : 'Catering'
    else
      'Unkown'
    end
  end

  def supplier_totals_for(supplier:, orders:)
    puts "#{supplier.name} - #{orders.size}" if verbose
    order_suppliers = OrderSupplier.where(supplier_profile: supplier, order: orders).where.not(total: nil)
    supplier_totals = order_suppliers.map do |order_supplier|
      {
        total: order_supplier.total,
        gst: order_supplier.gst,
      }
    end
    totals = supplier_totals.sum{|supplier_total| supplier_total[:total] }
    gst_totals = supplier_totals.sum{|supplier_total| supplier_total[:gst] }
    non_gst_totals = totals - gst_totals
    print 't-' if verbose
    [totals, gst_totals, non_gst_totals]
  end

end
