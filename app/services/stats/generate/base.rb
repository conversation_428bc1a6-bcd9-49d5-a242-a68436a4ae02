class Stats::Generate::Base

  def initialize(report_name:, verbose: false)
    @report_name = report_name
    @verbose = verbose
  end

private

  attr_reader :report_name, :verbose

  def file_path
    Rails.root.join('tmp/stats', "#{report_name}.csv")
  end

  def generate_report_with(rows:)
    prepare_report_folder

    CSV.open(file_path, 'wb') do |csv|
      csv << csv_headers if csv_headers.present?
      rows.each do |row|
        csv << row
        print 'c-' if verbose
      end
    end
  end

  def prepare_report_folder
    # small sanity check for the folder creation
    dir = File.dirname(file_path)
    unless File.directory?(dir)
      FileUtils.mkdir_p(dir)
    end
  end

end
