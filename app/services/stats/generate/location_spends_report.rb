class Stats::Generate::LocationSpendsReport < Stats::Generate::Base

  REPORT_COLUMNS = %w[year month].freeze
  SUBURB_COLUMNS = %w[suburb postcode state order_size].freeze
  TOTALS_COLUMNS = %w[non_gst_total gst_total customer_total non_gst_cost cost_gst cost_total].freeze
  HEADERS = (REPORT_COLUMNS + SUBURB_COLUMNS + TOTALS_COLUMNS).freeze

  def initialize(report_data:, report_name: 'location_stats_with_costs', verbose: false, include_order_ids: false)
    @report_data = report_data
    @include_order_ids = include_order_ids
    super(report_name: report_name, verbose: verbose)
  end

  def call
    rows = generate_data
    generate_report_with(rows: rows)

    file_path
  end

private

  attr_reader :report_data, :include_order_ids

  def csv_headers
    headers = HEADERS.dup
    headers << 'order_ids' if include_order_ids
    headers
  end

  def generate_data
    rows = []
    report_data.each do |year, month, suburb, suburb_orders|
      customer_total, customer_gst_total, customer_non_gst_total = customer_totals_for(suburb_orders)
      supplier_total, supplier_gst_total, supplier_non_gst_total = supplier_totals_for(suburb_orders)

      row = [
        year,
        Date::MONTHNAMES[month],
        suburb.name,
        suburb.postcode,
        suburb.state,
        suburb_orders.size,
        customer_non_gst_total,
        customer_gst_total,
        customer_total,
        supplier_non_gst_total,
        supplier_gst_total,
        supplier_total
      ]
      row << suburb_orders.map(&:id) if include_order_ids
      rows << row
      print 'd-' if verbose
    end
    rows
  end

  def customer_totals_for(orders)
    totals = orders.sum(&:customer_total)
    gst_totals = orders.sum(&:customer_gst)
    non_gst_totals = totals - gst_totals
    [totals, gst_totals, non_gst_totals]
  end

  def supplier_totals_for(orders)
    supplier_totals = orders.map do |order|
      order.order_suppliers.where.not(total: nil).map do |order_supplier|
        {
          total: order_supplier.total,
          gst: order_supplier.gst,
        }
      end
    end.flatten
    totals = supplier_totals.sum{|supplier| supplier[:total] }
    gst_totals = supplier_totals.sum{|supplier| supplier[:gst] }
    non_gst_totals = totals - gst_totals
    [totals, gst_totals, non_gst_totals]
  end
end
