class Stats::Generate::SupplierCreationReport < Stats::Generate::Base

  HEADERS = %w[year month number_of_suppliers cumulative_count].freeze

  def initialize(report_data:, report_name: 'cumulative_supplier_creation_stats', verbose: false)
    @report_data = report_data
    super(report_name: report_name, verbose: verbose)
  end

  def call
    rows = generate_data
    generate_report_with(rows: rows)

    file_path
  end

private

  attr_reader :report_data, :include_order_ids

  def csv_headers
    HEADERS
  end

  def generate_data
    rows = []

    first_data = report_data.first
    starts = Time.zone.parse("#{first_data[0]}-#{first_data[1]}-1")
    ends = Time.zone.now.end_of_year

    reporting_dates = []
    (starts.year..ends.year).to_a.each do |year|
      Date::MONTHNAMES.each_with_index do |month, idx|
        next if idx == 0

        reporting_dates << [year, idx, month]
      end
    end

    cumulative_count = 0
    reporting_dates.each do |year, month, month_name|
      month_data = report_data.detect{|data_year, data_month, _| data_year == year && data_month == month }
      supplier_count = month_data.present? ? month_data.last.size : 0
      cumulative_count += supplier_count
      row = [
        year,
        month_name,
        supplier_count,
        cumulative_count
      ]
      rows << row
      print 'm-'
    end
    rows
  end
end
