class Stats::Generate::CustomerSpendsReport < Stats::Generate::Base

  CUSTOMER_COLUMNS = %w[year month customer company email order_size].freeze
  TOTAL_COLUMNS = %w[non_gst_total gst_total customer_total non_gst_cost cost_gst cost_total].freeze
  HEADERS = (CUSTOMER_COLUMNS + TOTAL_COLUMNS).freeze

  def initialize(report_data:, report_name: 'customer_spends_with_costs', verbose: false, include_order_ids: false, cumulative: false)
    @report_data = report_data
    @include_order_ids = include_order_ids
    @cumulative = cumulative
    super(report_name: report_name, verbose: verbose)
  end

  def call
    rows = generate_data
    generate_report_with(rows: rows)

    file_path
  end

private

  attr_reader :report_data, :include_order_ids, :cumulative

  def csv_headers
    headers = HEADERS.dup
    headers -= ['year', 'month'] if cumulative
    headers << 'order_ids' if include_order_ids
    headers
  end

  def generate_data
    return generate_cumuative_data if cumulative

    rows = []
    report_data.each do |year, month, customer, customer_orders|
      customer_total, customer_gst_total, customer_non_gst_total = customer_totals_for(customer_orders)
      supplier_total, supplier_gst_total, supplier_non_gst_total = supplier_totals_for(customer_orders)

      row = [
        year,
        Date::MONTHNAMES[month],
        customer.name,
        customer.company.try(:name),
        customer.user.try(:email_recipient),
        customer_orders.size,
        customer_non_gst_total,
        customer_gst_total,
        customer_total,
        supplier_non_gst_total,
        supplier_gst_total,
        supplier_total
      ]
      row << customer_orders.map(&:id).join('|') if include_order_ids
      rows << row
      print 'd-' if verbose
    end
    rows
  end

  def generate_cumuative_data
    rows = []
    report_data.group_by{|report_datum| report_datum[2] }.each do |customer, customer_report_data|
      row = []
      customer_orders = customer_report_data.map{|customer_datum| customer_datum[3] }.flatten
      customer_total, customer_gst_total, customer_non_gst_total = customer_totals_for(customer_orders)
      supplier_total, supplier_gst_total, supplier_non_gst_total = supplier_totals_for(customer_orders)

      row = [
        customer.name,
        customer.company.try(:name),
        customer.user.try(:email_recipient),
        customer_orders.size,
        customer_non_gst_total,
        customer_gst_total,
        customer_total,
        supplier_non_gst_total,
        supplier_gst_total,
        supplier_total
      ]
      row << customer_orders.map(&:id).join('|') if include_order_ids
      rows << row
      print 'd-' if verbose
    end
    rows
  end

  def customer_totals_for(orders)
    totals = orders.sum(&:customer_total)
    gst_totals = orders.sum(&:customer_gst)
    non_gst_totals = totals - gst_totals
    [totals, gst_totals, non_gst_totals]
  end

  def supplier_totals_for(orders)
    supplier_totals = orders.map do |order|
      order.order_suppliers.where.not(total: nil).map do |order_supplier|
        {
          total: order_supplier.total,
          gst: order_supplier.gst,
        }
      end
    end.flatten
    totals = supplier_totals.sum{|supplier| supplier[:total] }
    gst_totals = supplier_totals.sum{|supplier| supplier[:gst] }
    non_gst_totals = totals - gst_totals
    [totals, gst_totals, non_gst_totals]
  end
end
