class TeamOrderAttendees::FetchWithinPackage

  def initialize(team_order_params:, profile: nil)
    @team_order_params = team_order_params
    @profile = profile
  end

  def call
    case
    when has_missing_params? || package_attendee.blank? || has_inactive_event_attendee? || has_missing_package_team_order? || has_different_package?
       nil
    else
      attach_attendee_to_package_order
    end
  end

private

  attr_reader :team_order_params, :profile

  def has_missing_params?
    %i[code event_id].any?{|field| team_order_params[field].blank? }
  end

  def has_missing_package_team_order?
    desitnation_team_order.blank? || !desitnation_team_order.is_package_order?
  end

  def has_inactive_event_attendee?
    !package_attendee.event_attendee.active?
  end

  def has_different_package?
    package_order.blank? || !package_order.is_package_order? || package_order.package_id != desitnation_team_order.package_id
  end

  def package_order
    @_package_order ||= package_attendee.order
  end

  def package_attendee
    @_package_attendee ||= TeamOrderAttendees::Fetch.new(attendee_code: team_order_params[:code], profile: profile).call
  end

  def desitnation_team_order
    @_desitnation_team_order ||= Order.where(unique_event_id: team_order_params[:event_id]).first
  end

  def attach_attendee_to_package_order
    attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: desitnation_team_order, event_attendee: package_attendee.event_attendee, level_id: last_attendee_level_id, notify_attendee: false, anonymous: false).call
    attendee_attacher.success? ? attendee_attacher.team_order_attendee : nil
  end

  # set the team order attendee level to the last selected attendee level
  def last_attendee_level_id
    return nil if desitnation_team_order.attendee_levels.blank? || last_updated_attendee.blank?

    destination_level = desitnation_team_order.attendee_levels.detect do |destination_order_level|
      destination_order_level.name.downcase == last_updated_attendee.level.name.downcase
    end
    destination_level&.id
  end

  def last_updated_attendee
    return @_last_updated_attendee if @_last_updated_attendee.present?

    recent_attendees = TeamOrderAttendee.where(event_attendee: package_attendee.event_attendee)
    recent_attendees = recent_attendees.joins(order: :team_order_detail).where(team_order_details: { package_id: package_order.package_id })
    recent_attendees = recent_attendees.where.not(level: nil)
    @_last_updated_attendee = recent_attendees.order(updated_at: :desc).first
  end

end
