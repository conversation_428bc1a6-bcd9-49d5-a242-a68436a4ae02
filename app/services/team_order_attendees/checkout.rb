class TeamOrderAttendees::Checkout
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  def initialize(attendee_code:, checkout_params: {}, profile: nil)
    @attendee_code = attendee_code
    @checkout_params = checkout_params
    @profile = profile
    @result = Result.new
  end

  def call
    return result if !can_checkout?

    if team_order_attendee.is_team_admin?
      team_order_attendee.status = 'ordered'
    else
      update_team_order_attendee
      notify_attendee
    end

    result.team_order_attendee = team_order_attendee
    calculate_totals
    result
  end

private

  attr_reader :attendee_code, :checkout_params, :profile, :result

  def team_order_attendee
    @_team_order_attendee ||= TeamOrderAttendees::Fetch.new(attendee_code: attendee_code, profile: profile).call
  end

  def can_checkout?
    case
    when team_order.blank?
      result.errors << 'Could not find a matching team order'
      result.is_redirected = true
    when team_order_attendee.status == 'ordered'
      result.warnings << 'Your order is already confirmed'
      update_team_order_attendee if !team_order_attendee.is_team_admin?
      result.team_order_attendee = team_order_attendee
      result.is_redirected = true
    when team_order_attendee.status != 'pending'
      result.errors << 'Your order does not have any selections'
    when over_budget?
      budget_error = 'You have exceeded your budget'
      budget_error += " of #{number_to_currency(team_order.team_order_budget)}" if !team_order.hide_budget
      result.errors << budget_error
    end
    result.errors.blank? && result.warnings.blank?
  end

  def over_budget?
    return if team_order_attendee.is_team_admin? || (profile.present? && profile == team_order.customer_profile)

    attendee_total = Orders::CalculateCustomerTotals.new(order: team_order, attendee: team_order_attendee).call.total
    attendee_total > team_order.team_order_budget
  end

  def update_team_order_attendee
    if team_order_attendee.update(sanitized_attendee_params)
      result.team_order_attendee = team_order_attendee
    end
  end

  def sanitized_attendee_params
    [attendee_status_params, checkout_params].inject(&:merge)
  end

  def attendee_status_params
    return {} if team_order_attendee.status == 'ordered'

    {
      status: 'ordered',
    }
  end

  def team_order
    result.team_order = @_team_order ||= team_order_attendee.present? && team_order_attendee.order
  end

  def notify_attendee
    TeamOrderAttendees::Emails::SendCheckoutEmail.new(team_order_attendee: team_order_attendee).delay(queue: :notifications).call
  end

  def calculate_totals
    Orders::CalculateCustomerTotals.new(order: team_order, save_totals: true).call
  end

  class Result
    attr_accessor :team_order_attendee, :team_order, :is_redirected, :errors, :warnings

    def initialize
      @team_order_attendee = nil
      @team_order = nil
      @is_redirected = nil
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && team_order_attendee.present? && team_order_attendee.status == 'ordered'
    end
  end
end
