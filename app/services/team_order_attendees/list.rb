class TeamOrderAttendees::List

  def initialize(options: {}, includes: [])
    @filter_options = [default_options, options.symbolize_keys].inject(&:merge)
    @includes = includes
  end

  def call
    @attendees = base_attendees
    filter_by_order if filter_options[:order].present? || filter_options[:orders].present?
    add_package_attendees if filter_options[:order].present? && filter_options[:include_package_attendees].present?
    filter_active_attendees if filter_options[:active_only].present?
    sort_attendees if filter_options[:sort_by].present?

    # admin attendee
    add_admin_attendee if filter_options[:order].present? && filter_options[:include_admin].present?

    attendees
  end

private

  attr_reader :filter_options, :includes
  attr_accessor :attendees

  def base_attendees
    @attendees = TeamOrderAttendee.all
  end

  def filter_by_order
    orders = filter_options[:order].presence || filter_options[:orders]
    return if orders.blank?

    @attendees = attendees.where(order: orders)
  end

  def filter_active_attendees
    @attendees = attendees.where.not(status: %w[declined cancelled])
  end

  def sort_attendees
    @attendees = begin
      if filter_options[:sort_by] == 'contact_name'
        attendees.includes(:event_attendee).order('event_attendees.first_name ASC, event_attendees.last_name ASC')
      else
        attendees.order(filter_options[:sort_by])
      end
    end
  end

  def add_package_attendees
    team_order = filter_options[:order]
    return if team_order.blank? || !team_order.is_package_order?

    package_attendees = TeamOrderAttendee.joins(:event_attendee)
    package_attendees = package_attendees.where.not(event_attendees: { id: attendees.select(:event_attendee_id) })
    package_attendees = package_attendees.where(event_attendees: { active: true }) # active contacts only

    package_attendees = package_attendees.where.not(team_order_attendees: { status: 'cancelled' }) # non-cancelled team order attendees only

    package_attendees = package_attendees.joins(order: :team_order_detail)
    package_attendees = package_attendees.where(team_order_details: { package_id: team_order.package_id }) # belonging to package orders

    package_attendees = package_attendees.order('orders.delivery_at DESC')
    listable_package_attendees = package_attendees.group_by(&:event_attendee).map{|_, contact_attendees| contact_attendees.first }.flatten(1).compact

    arel = TeamOrderAttendee.arel_table
    order_attendees_condition = arel[:id].in(attendees.map(&:id))
    package_attendees_condition = arel[:id].in(listable_package_attendees.map(&:id))

    @attendees = TeamOrderAttendee.where(order_attendees_condition.or(package_attendees_condition))
  end

  def add_admin_attendee
    admin_as_attendee = TeamOrderAttendees::Fetch.new(attendee_code: filter_options[:order].unique_event_id, profile: filter_options[:include_admin]).call
    @attendees = attendees.to_a.unshift(admin_as_attendee)
  end

  def default_options
    {
      order: nil,
      orders: [],
      active_only: false,
      sort_by: nil,
      include_admin: nil,
      include_package_attendees: false,
    }
  end

end