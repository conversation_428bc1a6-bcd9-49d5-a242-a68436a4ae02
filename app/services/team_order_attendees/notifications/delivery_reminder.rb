class TeamOrderAttendees::Notifications::DeliveryReminder

  TIME_THRESHOLD = 30.minutes

  def initialize(time: Time.zone.now)
    @time = time
  end

  def call
    deliverable_team_orders.each do |team_order|
      notifiable_attendees_for(team_order).each do |team_order_attendee|
        TeamOrderAttendees::Emails::SendDeliveryReminder.new(team_order: team_order, team_order_attendee: team_order_attendee).call
      end
    end
  end

private

  attr_accessor :sent_notifications
  attr_reader :time

  def deliverable_team_orders
    recent_orders = Order.where(order_variant: %w[team_order recurring_team_order], status: 'confirmed')
    recent_orders = recent_orders.where('delivery_at between ? and ?', time, (time + TIME_THRESHOLD))
    recent_orders
  end

  def notifiable_attendees_for(team_order)
    team_order.team_order_attendees.where(status: 'ordered').where("#{attendee_reminder_attribute} is NULL")
  end

  def attendee_reminder_attribute
    'delivery_30m_reminder'
  end

end
