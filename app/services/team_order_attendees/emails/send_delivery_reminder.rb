class TeamOrderAttendees::Emails::SendDeliveryReminder < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'team-order-attendee-delivery_notification'.freeze

  def initialize(team_order:, team_order_attendee:)
    @team_order = team_order
    @team_order_attendee = team_order_attendee
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
  end

  def call
    return if !can_notify? || !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send team order delivery reminder to attendee - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
      log_errors(exception: exception, message: error_message, sentry: false)
    end
  end

private

  attr_reader :team_order, :team_order_attendee

  def can_notify?
    team_order.present? && team_order_attendee.status == 'ordered' && team_order_attendee.respond_to?(notification_attribute) && team_order_attendee.send(notification_attribute).blank?
  end

  def team_admin
    @_team_admin ||= team_order.present? ? team_order.customer_profile : nil
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: team_order_attendee.email,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      mark_attendee_notified
      Rails.logger.info "Team order delivery reminder sent to attendee - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Yordar: 30 minutes before delivery'
  end

  def email_options
    {
      fk_id: team_order_attendee.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      attendee_name: team_order_attendee.first_name,

      team_order: deep_struct(team_order_data),
      supplier_grouped_order_lines: deep_struct(supplier_grouped_order_lines),

      header_color: :purple
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      delivery_at: team_order.delivery_at.to_s(:full),
      budget: number_to_currency(team_order.team_order_budget),
      hide_budget: team_order.hide_budget,
      delivery_address: team_order.delivery_address_arr.join(',<br/>')
    }
  end

  def supplier_grouped_order_lines
    attendee_order_lines.group_by(&:supplier_profile).map do |supplier, supplier_order_lines|
      hashed_order_lines = supplier_order_lines.map do |order_line|
        {
          name: order_line.name.truncate(50, omission: ' ...'),
          quantity: order_line.quantity,
          image: cloudinary_image(order_line.menu_item.image)
        }
      end
      {
        supplier: {
          name: supplier.company_name,
          image: cloudinary_image(supplier.profile.avatar)
        },
        has_more: false,
        order_lines: hashed_order_lines
      }
    end
  end

  def attendee_order_lines
    lister_options = {
      order: team_order,
      for_attendee: team_order_attendee,
    }
    @_order_lines ||= OrderLines::List.new(options: lister_options).call
  end

  def notification_attribute
    'delivery_30m_reminder'
  end

  def mark_attendee_notified
    team_order_attendee.update(Hash[notification_attribute.to_sym, Time.zone.now])
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{team_order_attendee.id}"
  end

end
