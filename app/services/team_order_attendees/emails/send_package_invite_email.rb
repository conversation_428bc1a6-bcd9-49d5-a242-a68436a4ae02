class TeamOrderAttendees::Emails::SendPackageInviteEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'team-order-attendee-package_invite'.freeze

  def initialize(package_order_attendee:, package_orders: [])
    @package_order_attendee = package_order_attendee
    @package_orders = package_orders.presence || attendee_package_orders
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
  end

  def call
    return if package_order.blank? || !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send package team order ##{package_order.id} invite email to attendee - #{package_order_attendee.uniq_code}, #{package_order_attendee.name}"
      log_errors(exception: exception, message: error_message, sentry: false)
    end
  end

private

  attr_reader :package_order_attendee, :package_orders

  def package_order
    @package_order ||= package_order_attendee.order || (package_orders.present? && package_orders.first)
  end

  def team_admin
    @_team_admin ||= package_order.present? ? package_order.customer_profile : nil
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: package_order_attendee.email,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent package team order ##{package_order.id} invite email to attendee #{package_order_attendee.uniq_code}, #{package_order_attendee.name}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Yordar: You\'ve been invited to multiple team meeting orders'
  end

  def email_options
    {
      fk_id: package_order_attendee.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      attendee: deep_struct(attendee_data),
      attendee_name: package_order_attendee.first_name,
      package_order: deep_struct(package_order_data),
      package_orders: deep_struct(package_orders_data),
      header_color: :purple,
    }
  end

  def attendee_data
    {
      name: package_order_attendee.first_name,
      package_url: url_helper.team_order_attendee_package_url(code: package_order_attendee.uniq_code, host: next_app_host, tld_length: 2), # custom tld length needed
      # unsubscribe_url: url_helper.team_order_attendee_package_unsubscribe_url(code: package_order_attendee.uniq_code, host: app_host),
    }
  end

  def package_order_data
    {
      id: package_order.id,
      name: package_order.name,
      budget: number_to_currency(package_order.team_order_budget, precision: 2),
      hide_budget: package_order.hide_budget,
      delivery_address: package_order.delivery_address_arr.join(',<br>'),
    }
  end

  def package_orders_data
    package_orders.map do |team_order|
      {
        supplier: supplier_data_for(team_order),
        delivery_at: team_order.delivery_at.to_s(:full_verbose)
      }
    end
  end

  def supplier_data_for(team_order)
    supplier = team_order.team_supplier_profiles.first
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def attendee_package_orders
    return [] if package_order.is_recurring_team_order?

    lister_options = { for_attendee: package_order_attendee, active: true }
    TeamOrders::ListPackageOrders.new(team_order: package_order_attendee.order.reload, options: lister_options).call
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{package_order.id}-#{package_order_attendee.id}"
  end

end
