class TeamOrderAttendees::Emails::SendRemovedFromOrderEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'team-order-attendee-removed_notification'.freeze

  def initialize(team_order_attendee:)
    @team_order_attendee = team_order_attendee
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
  end

  def call
    return if team_order.blank? || !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send team order ##{team_order.id} invitation removal email to attendee - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
      log_errors(exception: exception, message: error_message, sentry: false)
    end
  end

private

  attr_reader :team_order_attendee

  def team_order
    @team_order ||= team_order_attendee.order.reload
  end

  def team_admin
    @_team_admin ||= team_order.present? ? team_order.customer_profile : nil
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: team_order_attendee.email,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent team order removed email to attendee - order ##{team_order.id} - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Yordar: You have been removed from a team order'
  end

  def email_options
    {
      fk_id: team_order_attendee.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      attendee_name: team_order_attendee.first_name,
      team_order: deep_struct(team_order_data),

      header_color: :purple,
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      delivery_at: team_order.delivery_at.to_s(:full)
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{team_order_attendee.id}"
  end

end
