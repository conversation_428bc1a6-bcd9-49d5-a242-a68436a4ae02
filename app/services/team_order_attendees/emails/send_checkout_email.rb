class TeamOrderAttendees::Emails::SendCheckoutEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'team-order-attendee-order_checkout'.freeze

  def initialize(team_order_attendee:)
    @team_order_attendee = team_order_attendee
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
  end

  def call
    return if team_order.blank? || !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send order checkout email to attendee - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
      log_errors(exception: exception, message: error_message, sentry: false)
    end
  end

private

  attr_reader :team_order_attendee

  def team_order
    @team_order ||= team_order_attendee.order.reload
  end

  def team_admin
    @_team_admin ||= team_order.present? ? team_order.customer_profile : nil
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: team_order_attendee.email,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent Team Order Attendee Checkout email sent to to attendee - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Yordar: Thank you for placing your order'
  end

  def email_options
    {
      fk_id: team_order_attendee.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      attendee: deep_struct(attendee_data),
      team_order: deep_struct(team_order_data),
      supplier_grouped_order_lines: deep_struct(supplier_grouped_order_lines),

      header_color: :purple
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      delivery_at: team_order.delivery_at.to_s(:full),
      delivery_address: team_order.delivery_address_arr.join(',<br/>'),
      budget: number_to_currency(team_order.team_order_budget, precision: 2),
      hide_budget: team_order.hide_budget
    }
  end

  def attendee_data
    {
      name: team_order_attendee.first_name,
      order_url: url_helper.next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code, host: next_app_host, tld_length: 2)
    }
  end

  def supplier_grouped_order_lines
    attendee_order_lines.group_by(&:supplier_profile).map do |supplier, supplier_order_lines|
      hashed_order_lines = supplier_order_lines.map do |order_line|
        {
          name: order_line.name.truncate(50, omission: ' ...'),
          quantity: order_line.quantity,
          image: cloudinary_image(order_line.menu_item.image)
        }
      end
      {
        supplier: {
          name: supplier.company_name,
          image: cloudinary_image(supplier.profile.avatar)
        },
        has_more: false,
        order_lines: hashed_order_lines
      }
    end
  end

  def attendee_order_lines
    lister_options = {
      order: team_order,
      for_attendee: team_order_attendee,
    }
    @_order_lines ||= OrderLines::List.new(options: lister_options).call
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{team_order_attendee.id}"
  end

end


