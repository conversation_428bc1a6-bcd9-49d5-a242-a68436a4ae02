class TeamOrderAttendees::HandleAnonymous

  def initialize(team_order_attendee:, anonymous_status:, team_admin:)
    @team_order_attendee = team_order_attendee
    @anonymous_status = anonymous_status
    @team_admin = team_admin
    @result = Result.new(team_order_attendee: team_order_attendee)
  end

  def call
    if can_handle?
      team_order_attendee.update(default_attributes)
      case
      when anonymous_status == 'reject'
        detach_attendee
      when %w[auto_approve approve].include?(anonymous_status) && team_order_attendee.status == 'ordered'
        recalculate_order_totals
      end
      handle_within_attached_orders if anonymous_status != 'auto_approve' && team_order.is_package_order?
    end
    result
  end

private

  attr_reader :team_order_attendee, :anonymous_status, :team_admin, :result

  def can_handle?
    case
    when team_order_attendee.blank? || team_admin.blank?
      result.errors << 'Cannot handle a missing team order attendee'
    when !team_order_attendee.anonymous
      result.errors << 'Attendee is not an anonymous attendee'
    when team_order.blank? || team_order.customer_profile != team_admin
      result.errors << 'You do not have access to this attendee'
    end
    result.errors.blank?
  end

  def team_order
    @_team_order ||= team_order_attendee.order
  end

  def default_attributes
    {
      anonymous: false,
    }
  end

  def detach_attendee
    TeamOrderAttendees::Detach.new(team_order_attendee: team_order_attendee, team_admin: team_admin).call
  end

  def recalculate_order_totals
    Orders::CalculateCustomerTotals.new(order: team_order, save_totals: true).call
  end

  def handle_within_attached_orders
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order).call
    package_orders.each do |package_order|
      anonymous_package_attendee = package_order.team_order_attendees.where(event_attendee: team_order_attendee.event_attendee, anonymous: true).first
      next if anonymous_package_attendee.blank?

      handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: anonymous_package_attendee, anonymous_status: anonymous_status, team_admin: team_admin).call

      if !handler.success?
        result.errors += handler.errors
      end
    end
  end

  class Result
    attr_reader :team_order_attendee
    attr_accessor :errors

    def initialize(team_order_attendee:)
      @team_order_attendee = team_order_attendee
      @errors = []
    end

    def success?
      errors.blank? && team_order_attendee.present? && !team_order_attendee.anonymous
    end
  end

end
