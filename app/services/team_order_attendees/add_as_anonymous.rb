class TeamOrderAttendees::AddAsAnonymous

  def initialize(team_order:, attendee_params:, notify_attendee: true)
    @team_order = team_order
    @attendee_params = attendee_params
    @notify_attendee = notify_attendee
    @result = Result.new
  end

  def call
    if fetch_event_attendee && can_attach_attendee?
      if can_attach_to_package_orders?
        attach_attendee_to_package
      else
        attach_attendee_to_team_order
      end
    end
    result
  end

private

  attr_reader :team_order, :attendee_params, :event_attendee, :notify_attendee, :result

  def existing_event_attendee
    @_existing_event_attendee ||= attendee_params[:email].present? && team_admin.event_attendees.where('lower(email) = ?', attendee_params[:email].downcase).first
  end

  def fetch_event_attendee
    case
    when team_order.blank?
      result.errors << 'This event invite link has expired'
    when !team_order.is_package_order? && team_order.status != 'pending'
      result.errors << 'This event invite link has expired'
    when attendee_params[:email] == team_admin.email
      notify_team_admin_registration
      result.warnings << 'You are already registered as an attendee for this event, please check your email for an order invite link'
    when existing_event_attendee.present?
      result.event_attendee = @event_attendee = existing_event_attendee
    else
      attendee_creator = EventAttendees::Upsert.new(event_attendee_params: attendee_params.except(:package_id, :level_id), team_admin: team_admin).call
      if attendee_creator.success?
        result.event_attendee = @event_attendee = attendee_creator.event_attendee
      else
        result.errors += attendee_creator.errors
      end
    end
    result.errors.blank? && result.warnings.blank?
  end

  def existing_team_order_attendee
    @_existing_team_order_attendee ||= team_order.team_order_attendees.where(event_attendee: event_attendee).first
  end

  def can_attach_attendee?
    case
    when existing_team_order_attendee.present?
      result.team_order_attendee = existing_team_order_attendee
      notify_existing_team_order_attendee
      result.warnings << 'You are already registered as an attendee for this event, please check your email for an order invite link or contact your team admin'
    end
    result.errors.blank? && result.warnings.blank?
  end

  def can_attach_to_package_orders?
    team_order.is_package_order? && attendee_params[:package_id].present? && team_order.package_id == attendee_params[:package_id]
  end

  def attach_attendee_to_team_order
    attendee_attachers = TeamOrderAttendees::AddToOrder.new(team_order: @team_order, event_attendee: event_attendee, level_id: attendee_params[:level_id], anonymous: true, notify_attendee: notify_attendee).call
    if attendee_attachers.success?
      result.team_order_attendee = @team_order_attendee = attendee_attachers.team_order_attendee
    else
      result.errors += attendee_attachers.errors
    end
  end

  def attach_attendee_to_package
    package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order).call
    if package_orders.present?
      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params.except(:package_id), notify_attendee: false).call
      if attendee_attacher.success?
        result.team_order_attendee = attendee_attacher.team_order_attendee
        TeamOrderAttendees::Emails::SendPackageInviteEmail.new(package_order_attendee: attendee_attacher.team_order_attendee, package_orders: nil).delay(queue: :notifications).call
      else
        result.errors += attendee_attacher.errors
      end
    end
  end

  def notify_team_admin_registration
    TeamOrders::Emails::SendAdminRegistrationEmail.new(team_order: team_order, team_admin: team_admin).delay(queue: :notifications).call
  end

  def notify_existing_team_order_attendee
    if team_order.is_package_order?
      TeamOrderAttendees::Emails::SendPackageInviteEmail.new(package_order_attendee: existing_team_order_attendee).delay(queue: :notifications).call
    else
      TeamOrderAttendees::Emails::SendInviteEmail.new(team_order_attendee: existing_team_order_attendee).delay(queue: :notifications).call
    end
  end

  def team_admin
    @_team_admin ||= team_order.customer_profile
  end

  class Result
    attr_accessor :event_attendee, :team_order_attendee, :errors, :warnings

    def initialize
      @event_attendee = nil
      @team_order_attendee = nil
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && warnings.blank? && event_attendee.present? && team_order_attendee.present?
    end
  end

end
