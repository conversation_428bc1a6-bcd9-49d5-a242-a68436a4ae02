class TeamOrderAttendees::AddT<PERSON><PERSON><PERSON><PERSON>

  def initialize(team_order:, event_attendee:, level_id: nil, notify_attendee: true, anonymous: false)
    @team_order = team_order
    @event_attendee = event_attendee
    @notify_attendee = notify_attendee
    @level_id = level_id
    @anonymous = anonymous
    @result = Result.new(team_order: team_order)
  end

  def call
    if can_attach?
      if team_order_attendee.update(sanitized_attributes)
        send_invitation_email if notify_attendee
      else
        result.errors += team_order_attendee.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :team_order, :event_attendee, :level_id, :notify_attendee, :anonymous, :result

  def can_attach?
    case
    when event_attendee.blank? || team_order_attendee.blank?
      result.errors << 'Cannot attached a missing contact'
    when team_order_attendee.email == team_order.customer_profile.user.try(&:email)
      result.errors << 'Cannot attach the attendee with same email as the team admin'
    when team_order_attendee.persisted? && team_order_attendee.status != 'cancelled'
      return false # do nothing + do not error
    end
    result.errors.blank?
  end

  def sanitized_attributes
    [default_attributes, level_attributes].inject(&:merge)
  end

  def default_attributes
    {
      status: 'invited',
      uniq_code: SecureRandom.hex(10),
      anonymous: anonymous,
    }
  end

  def level_attributes
    return {} if level_id.blank? || team_order.attendee_levels.blank?
    return {} if team_order.attendee_levels.map(&:id).exclude?(level_id.to_i)

    {
      team_order_level_id: level_id
    }
  end

  def team_order_attendee
    return @_team_order_attendee if @_team_order_attendee.present?

    result.team_order_attendee = @_team_order_attendee = team_order.team_order_attendees.where(event_attendee: event_attendee).first_or_initialize
  end

  def send_invitation_email
    TeamOrderAttendees::Emails::SendInviteEmail.new(team_order_attendee: team_order_attendee).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :team_order, :team_order_attendee, :errors

    def initialize(team_order:)
      @team_order = team_order
      @team_order_attendee = nil
      @errors = []
    end

    def success?
      errors.blank? && team_order_attendee.present? && team_order_attendee.persisted? && team_order_attendee.order == team_order
    end
  end
end
