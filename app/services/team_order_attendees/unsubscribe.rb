class TeamOrderAttendees::Unsubscribe

  def initialize(attendee_code:)
    @attendee_code = attendee_code
    @result = Result.new
  end

  def call
    if can_unsubscribe?
      result.team_order_attendee = team_order_attendee
      team_order_attendee.update(status: 'declined')
    end
    result
  end

private

  attr_reader :attendee_code, :result

  def can_unsubscribe?
    case
    when team_order_attendee.blank? || team_order.blank?
      result.errors << 'Could not find a matching team order'
    when past_cutoff_threshold?
      result.errors << 'Cannot decline your invitation now as its past the cutoff period'
    when team_order_attendee.status == 'ordered' && %w[confirmed delivered].include?(team_order.status)
      result.errors << 'Cannot decline your invitation now as your order has been passed on to the suppliers'
    end
    result.errors.blank?
  end

  def team_order_attendee
    @_team_order_attendee ||= TeamOrderAttendee.where(uniq_code: attendee_code).first
  end

  def team_order
    @_team_order ||= team_order_attendee.present? && team_order_attendee.order
  end

  def past_cutoff_threshold?
    lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call
    lead_time_fetcher.past_lead_time? || (lead_time_fetcher.lead_time - TeamOrderAttendee::REGISTRATION_CUTOFF_THRESHOLD) <= Time.zone.now
  end

  class Result
    attr_accessor :team_order_attendee, :errors

    def initialize
      @team_order_attendee = team_order_attendee
      @errors = []
    end

    def success?
      errors.blank? && team_order_attendee.present? && team_order_attendee.status == 'declined'
    end
  end

end
