class TeamOrderAttendees::Detach

  def initialize(team_order_attendee:, team_admin:, notify_attendee: false)
    @team_order_attendee = team_order_attendee
    @team_admin = team_admin
    @notify_attendee = notify_attendee
    @result = Result.new(team_order_attendee: team_order_attendee)
  end

  def call
    if can_detach?
      if team_order_attendee.update(removal_attributes)
        send_attendee_removed_email if notify_attendee
      else
        result.errors += team_order_attendee.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :team_order_attendee, :team_admin, :notify_attendee, :result

  def can_detach?
    case
    when team_order_attendee.blank?
      result.errors << 'Cannot detach missing attendee'
    when team_admin.blank? || team_order.blank? || team_order.customer_profile != team_admin
      result.errors << 'You do not have access to this team order'
    when team_order_attendee.status == 'cancelled'
      # can detach an already detached attendee
    end
    result.errors.blank?
  end

  def team_order
    @_team_order ||= team_order_attendee.order
  end

  def removal_attributes
    {
      status: 'cancelled',
      uniq_code: SecureRandom.hex(10), # update code
    }
  end

  def send_attendee_removed_email
    TeamOrderAttendees::Emails::SendRemovedFromOrderEmail.new(team_order_attendee: team_order_attendee).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :team_order_attendee, :errors

    def initialize(team_order_attendee:)
      @team_order_attendee = team_order_attendee
      @errors = []
    end

    def success?
      errors.blank? && team_order_attendee.present? && team_order_attendee.status == 'cancelled'
    end
  end
end
