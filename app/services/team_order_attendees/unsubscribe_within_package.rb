class TeamOrderAttendees::UnsubscribeWithinPackage

  def initialize(attendee_code:)
    @attendee_code = attendee_code
    @result = Result.new
  end

  def call
    if can_unsubscribe?
      package_orders.each do |package_order|
        unsubscribe_attendee_from(team_order: package_order)
      end
    end
    result
  end

private

  attr_reader :attendee_code, :result

  def can_unsubscribe?
    case
    when team_order_attendee.blank? || team_order.blank? || package_orders.blank?
      result.errors << 'Could not find a matching team order'
    when team_order_attendee.status == 'ordered' && %w[confirmed delivered].include?(team_order.status)
      result.errors << 'Cannot decline your invitation now as your order has been passed on to the suppliers'
    end
    result.errors.blank?
  end

  def team_order_attendee
    @_team_order_attendee ||= TeamOrderAttendee.where(uniq_code: attendee_code).first
  end

  def team_order
    @_team_order ||= team_order_attendee.present? && team_order_attendee.order
  end

  def package_orders
    lister_options = { for_attendee: team_order_attendee, active_only: true, future_only: true }
    TeamOrders::ListPackageOrders.new(team_order: team_order, options: lister_options).call
  end

  def unsubscribe_attendee_from(team_order:)
    package_order_attendee = team_order.team_order_attendees.where(event_attendee: team_order_attendee.event_attendee).first
    attendee_unsubscriber = TeamOrderAttendees::Unsubscribe.new(attendee_code: package_order_attendee.uniq_code).call
    if attendee_unsubscriber.success?
      result.unsubscribed_attendees << package_order_attendee
      result.package_order = team_order
    else
      result.errors += attendee_unsubscriber.errors
    end
  end

  class Result
    attr_accessor :package_order, :unsubscribed_attendees, :errors

    def initialize
      @package_order = nil
      @unsubscribed_attendees = []
      @errors = []
    end

    def success?
      errors.blank? && unsubscribed_attendees.present?
    end
  end

end
