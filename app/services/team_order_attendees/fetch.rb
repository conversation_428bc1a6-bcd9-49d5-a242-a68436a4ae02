class TeamOrderAttendees::<PERSON><PERSON>

  def initialize(attendee_code:, profile: nil)
    @attendee_code = attendee_code
    @profile = profile
  end

  def call
    team_order_admin_as_attendee.presence || team_order_attendee
  end

private

  attr_reader :attendee_code, :profile

  def team_order_admin_as_attendee
    return nil if attendee_code.blank? || profile.blank?

    team_order = profile.orders.where(order_variant: %w[team_order recurring_team_order]).where(unique_event_id: attendee_code).first
    return nil if team_order.blank?

    event_attendee = EventAttendee.team_admin_as_attendee(team_admin: profile)
    team_order_admin_as_attendee = team_order.team_order_attendees.new(uniq_code: attendee_code, status: 'pending')
    team_order_admin_as_attendee.event_attendee = event_attendee
    team_order_admin_as_attendee
  end

  def team_order_attendee
    profile_attendee = nil
    if profile.present? && profile.team_admin?
      profile_attendee = profile.team_order_attendees.where(uniq_code: attendee_code).first
    end
    profile_attendee.presence || TeamOrderAttendee.where(uniq_code: attendee_code).first
  end
end
