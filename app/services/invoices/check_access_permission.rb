class Invoices::CheckAccessPermission

  def initialize(invoice_id:, customer: nil, is_admin: false)
    @invoice_id = invoice_id
    @customer = customer
    @is_admin = is_admin
    @result = Result.new
  end

  def call
    case
    when invoice_by_uuid.present?
      result.invoice = invoice_by_uuid
    when invoice_by_number.present? && ((customer.present? && is_customer_invoiced_order?) || (customer.blank? && is_admin))
      result.invoice = invoice_by_number
      result.errors << 'Next time use the secure link'
      result.redirect_to = Rails.application.routes.url_helpers.pay_invoice_path(invoice_by_number.uuid)
    when invoice_by_number.present? && customer.present?
      result.invoice = nil
      result.errors << 'You don\'t have access to this invoice'
      result.redirect_to = Rails.application.routes.url_helpers.customer_profile_path
    else # invoice_by_number.present?
      result.invoice = invoice_by_number
      result.errors << 'You need to log in to access invoice'
      result.redirect_to = Rails.application.routes.url_helpers.new_user_session_path
    end
    result
  end

private

  attr_reader :invoice_id, :customer, :is_admin, :result

  def invoice_by_uuid
    @_invoice_by_uuid ||= Invoice.where(uuid: invoice_id).first
  end

  def invoice_by_number
    @_invoice_by_number ||= Invoice.where(number: invoice_id).first
  end

  def is_customer_invoiced_order?
    Order.where(customer_profile: customer, invoice_id: invoice_by_number.id).present?
  end

  class Result
    attr_accessor :invoice, :errors, :redirect_to

    def initialize
      @invoice = nil
      @errors = []
      @redirect_to = nil
    end

    def can_access?
      @errors.blank? && invoice.present?
    end
  end

end
