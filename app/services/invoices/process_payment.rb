class Invoices::ProcessPayment

  def initialize(invoice:, credit_card:)
    @invoice = invoice
    @credit_card = credit_card
    @result = Result.new(invoice: invoice, credit_card: credit_card)
  end

  def call
    if can_process?
      process_pay_on_credit_card_orders if credit_card_orders.present?
      process_pay_on_account_orders if pay_on_account_orders.present?
      send_tax_receipt if (credit_card_orders.present? || pay_on_account_orders.present?) && result.errors.blank?
    end
    result
  end

private

  attr_reader :invoice, :credit_card, :result

  def can_process?
    case
    when invoice.blank?
      result.errors << 'Cannot pay without an invoice'
    when credit_card.blank?
      result.errors << 'Cannot pay without a credit card'
    when invoice.payment_status == 'paid' # invoice.paid?
      result.errors << 'Invoice is already paid for'
    end
    result.errors.blank?
  end

  def invoice_customer
    @_invoice_customer ||= unpaid_invoice_orders.last.customer_profile
  end

  def unpaid_invoice_orders
    @_unpaid_invoice_orders ||= invoice.invoice_orders.where(payment_status: [nil, '', 'unpaid', 'error']).includes(:credit_card)
  end

  def credit_card_orders
    @_credit_card_orders ||= unpaid_invoice_orders - pay_on_account_orders
  end

  def pay_on_account_orders
    @_pay_on_account_orders ||= unpaid_invoice_orders.select do |order|
      order.credit_card.present? && (order.credit_card.pay_on_account? || order.credit_card.auto_pay_invoice)
    end
  end

  def process_pay_on_credit_card_orders
    credit_card_orders.each do |order|
      pay_by_credit_card = Orders::PayByCreditCard.new(order: order, credit_card: credit_card).call
      if pay_by_credit_card.success?
        result.payments << pay_by_credit_card.payment
      else
        result.errors += pay_by_credit_card.errors
      end
    end
  end

  def process_pay_on_account_orders
    orders_payment = Invoices::PayOnAccountOrders.new(invoice: invoice, pay_on_account_orders: pay_on_account_orders, credit_card: credit_card, notify_customer: false).call
    if orders_payment.success?
      result.payments << orders_payment.payment
    else
      result.errors += orders_payment.errors
    end
  end

  def send_tax_receipt
    Customers::Emails::SendInvoiceReceiptEmail.new(customer: invoice_customer, invoice: invoice).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :payments, :errors
    attr_reader :invoice, :credit_card

    def initialize(invoice:, credit_card:)
      @invoice = invoice
      @credit_card = credit_card
      @payments = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
