# generate documents for each invoice order (customer_order_details) # if not already present
# instantly -> single tax invoice pdf - no reports
# weekly -> bulk order tax invoice PDF (containing order links)
# monthly -> bulk order tax invoice PDF (containing order links)
# bulk order invoice => generate a tax invoice spreadsheet if billing needs_invoice_spreadsheet is true

class Invoices::GenerateDocuments

  def initialize(invoice:, customer: nil, notify_customer: false, regenerate: false)
    @invoice = invoice
    @customer = customer.presence || invoice_orders.sample&.customer_profile
    @notify_customer = notify_customer
    @regenerate = regenerate
    @result = Result.new
  end

  def call
    generate_order_documents

    document_types.each do |document_type|
      generate_and_upload_document(document_type: document_type)
    end
    if result.errors.blank? && result.generated_documents.present?
      invoice.update(status: 'confirmed')
      send_invoice_to_customer if notify_customer
    end
    result
  end

private

  attr_reader :invoice, :customer, :notify_customer, :regenerate, :result

  def generate_order_documents
    invoice_orders.each do |order|
      next if order.documents.where(kind: 'customer_order_details').present?

      Documents::Generate::CustomerOrderDetails.new(order: order).call
    end
  end

  def document_types
    documents = ['tax_invoice']
    documents << 'tax_invoice_spreadsheet' if is_multiple_order_invoice? && %w[weekly monthly].include?(billing_frequency) && customer.needs_invoice_spreadsheet
    documents
  end

  def generate_and_upload_document(document_type:)
    begin
      invoice_document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      if invoice_document.present?
        puts "Uploaded #{document_type} . . . . . . . . . . . . . . . . . . #{invoice_document.url}" if !Rails.env.test?
        result.generated_documents << invoice_document
      else
        raise RuntimeError.new(message: 'ERRORED') # to trigger rescue code
      end
    rescue
      error_message = "Failed to generate #{document_type} document for invoice ##{invoice.id}"
      result.errors << error_message
    end
  end

  def send_invoice_to_customer
    email_sender = Customers::Emails::SendOrderInvoiceEmail.new(customer: customer, invoice: invoice, documents: result.generated_documents, is_regenerate: regenerate).call
    if email_sender.success?
      result.customer_notification = email_sender.sent_notification
    else
      result.errors += email_sender.errors
    end
  end

  def is_multiple_order_invoice?
    invoice_orders.size > 1
  end

  def invoice_orders
    @_invoice_order ||= invoice.invoice_orders
  end

  def billing_frequency
    @_billing_frequency ||= customer&.billing_frequency
  end

  class Result
    attr_accessor :generated_documents, :customer_notification, :errors

    def initialize
      @generated_documents = []
      @customer_notification = nil
      @errors = []
    end

    def success?
      errors.blank? && generated_documents.present?
    end
  end
end
