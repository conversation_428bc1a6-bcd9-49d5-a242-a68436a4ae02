class Invoices::GetDates

  def initialize(invoice:)
    @invoice = invoice
    @result = Result.new(invoice: invoice, customer: customer)
  end

  def call
    if has_pay_on_account_orders?
      case customer.billing_frequency
      when 'weekly'
        retrieve_previous_week_dates
      # when 'monthly' && customer.billing_day.present?
      #   retrieve_monthly_dates
      when 'monthly'
        retrieve_previous_month_dates
      end
    end
    result
  end

private

  attr_reader :invoice, :result

  def has_pay_on_account_orders?
    invoice_orders.count > 1 || (first_order.credit_card.present? && first_order.credit_card.pay_on_account?)
  end

  def retrieve_previous_week_dates
    previous_week = (invoice.created_at - 1.week)
    result.from = previous_week.beginning_of_week
    result.to = previous_week.end_of_week
  end

  def retrieve_monthly_dates
    yesterday = invoice.created_at - 1.day
    result.from = yesterday - 1.month.beginning_of_day
    result.to = yesterday.end_of_day
  end

  def retrieve_previous_month_dates
    previous_month = (invoice.created_at - 1.month)
    result.from = previous_month.beginning_of_month
    result.to = previous_month.end_of_month
  end

  def first_order
    @_first_order ||= invoice_orders.first
  end

  def customer
    @_customer = first_order.customer_profile
  end

  def invoice_orders
    @_invoice_orders ||= invoice.invoice_orders.order(delivery_at: :asc)
  end

  class Result
    attr_accessor :from, :to
    attr_reader :customer

    def initialize(invoice:, customer:)
      @customer = customer
      @from = (invoice.created_at - 1.day).beginning_of_day
      @to = (invoice.created_at - 1.day).end_of_day
    end

    def due
      term_days = customer.company&.payment_term_days.presence || Invoice::DEFAULT_PAYMENT_DAYS
      to.beginning_of_day + term_days.days
    end

    def to_h
      {
        from: from,
        to: to,
        due: due,
      }
    end
  end

end
