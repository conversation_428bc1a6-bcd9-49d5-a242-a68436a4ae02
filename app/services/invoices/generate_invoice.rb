# does 5 things
# recalculate totals for each invoicable order
# creates an invoice record
# connects invoice to orders
  # with nominated card
    # processes payment for invoice
    # sends tax receipt (part of payment process)
  # without nominated card
    # generate document(s)
    # notifies customer about generated documents

class Invoices::GenerateInvoice

  def initialize(invoicable_orders:, invoice_dates: {}, purchase_order: nil, notify_customer: false)
    @invoicable_orders = invoicable_orders
    @invoice_dates = invoice_dates.presence || default_invoice_dates
    @notify_customer = notify_customer
    @purchase_order = purchase_order
    @result = Result.new
  end

  def call
    # If the invoice cannot be generated, remove the invoice from the database
    ActiveRecord::Base.transaction do
      recalculate_totals
      begin
        create_invoice
        attach_orders_to_invoice
        case
        when has_attached_credit_card?
          pay_invoice_using(attached_credit_card)
        when has_nominated_card?
          pay_invoice_using(nominated_invoice_credit_card)
        else
          generate_tax_invoice_documents(and_notify_customer: notify_customer)
        end
        result.generated_invoice = invoice
      rescue => exception
        error_message = "Failed to generate invoice for customer orders #{invoice_customer.id} - #{invoicable_orders.map(&:id).join(', ')}"
        Rails.logger.error error_message
        Rails.logger.error exception.inspect
        Rails.logger.error exception.backtrace.join('\n')
        result.errors << error_message
        Raven.capture_exception(exception,
          message: "Failed to generate invoice for cutomer orders #{invoice_customer.id} - #{invoicable_orders.map(&:id).join(', ')}",
          extra: { customer_id: invoice_customer.id, order_ids: invoicable_orders.map(&:id) },
          transaction: 'Invoices::GenerateInvoice'
        )
        raise ActiveRecord::Rollback
      end
    end
    result
  end

private

  attr_reader :invoicable_orders, :invoice, :invoice_dates, :purchase_order, :notify_customer, :result

  def recalculate_totals
    # Recalculate totals before invoicing. This is to make sure the order totals are always up-to-date in invoice.
    invoicable_orders.map do |order|
      Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
    end
  end

  def invoice_customer
    @_invoice_customer ||= invoicable_orders.last.customer_profile
  end

  def create_invoice
    invoice_params = {
      from_at: invoice_dates[:from],
      to_at: invoice_dates[:to],
      customer_purchase_order: purchase_order
    }
    invoice_creator = Invoices::Upsert.new(invoice_params: invoice_params, customer: invoice_customer).call
    if invoice_creator.success?
      @invoice = invoice_creator.invoice
    else
      result.errors += invoice_creator.errors
    end
  end

  def attach_orders_to_invoice
    orders_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: invoicable_orders).call
    if !orders_attacher.success?
      result.errors += invoice_creator.errors
    end
  end

  def default_invoice_dates
    sorted_orders = invoicable_orders.sort_by(&:delivery_at)
    {
      from: sorted_orders.first.delivery_at.beginning_of_day,
      to: sorted_orders.last.delivery_at.end_of_day
    }
  end

  def nominated_invoice_credit_card
    @_nominated_invoice_credit_card ||= invoice_customer.present? && invoice_customer.credit_cards.where(auto_pay_invoice: true).first
  end

  def has_nominated_card?
    nominated_invoice_credit_card.present? && invoicable_orders.any?{|x| x.credit_card.present? && (x.credit_card.pay_on_account || x.credit_card.auto_pay_invoice) }
  end

  def attached_credit_card
    @_attached_credit_card ||= invoicable_orders.first&.credit_card
  end

  def has_attached_credit_card?
    return false if invoicable_orders.size > 1

    attached_credit_card.present? && !attached_credit_card.pay_on_account
  end

  def pay_invoice_using(credit_card)
    invoice_payment = Invoices::ProcessPayment.new(invoice: invoice, credit_card: credit_card).call
    if invoice_payment.success?
      invoice.update(status: 'confirmed')
      result.payments = invoice_payment.payments
    else
      result.errors << "Failed auto payment for invoice id: #{invoice.id} - ##{invoice.number}"
      result.errors += invoice_payment.errors
      generate_tax_invoice_documents(and_notify_customer: false) # at least generate Invoice documents
    end
  end

  def generate_tax_invoice_documents(and_notify_customer:)
    document_generator = Invoices::GenerateDocuments.new(invoice: invoice, customer: invoice_customer, notify_customer: and_notify_customer).call
    if document_generator.success?
      result.generated_documents = document_generator.generated_documents
    else
      result.errors += document_generator.errors
    end
  end

  class Result
    attr_accessor :generated_invoice, :payments, :generated_documents, :errors

    def initialize
      @generated_invoice = nil
      @generated_documents = []
      @payments = []
      @errors = []
    end

    def success?
      errors.blank? && generated_invoice.present?
    end
  end

end

