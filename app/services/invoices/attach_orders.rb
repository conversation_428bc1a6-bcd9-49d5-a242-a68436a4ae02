class Invoices::AttachOrders

  def initialize(invoice:, orders:)
    @invoice = invoice
    @attachable_orders = sanitize_attachable(orders)
    @result = Result.new(invoice: invoice)
  end

  def call
    attachable_orders.each do |order|
      next if order.customer_profile != invoice.customer_profile

      if order.update(order_params_for(order))
        result.attached_orders << order
      else
        result.errors << "Could not attach order ##{order.id}"
      end
    end
    remove_existing_orders
    update_invoice
    result
  end

private

  attr_reader :invoice, :attachable_orders, :result

  def sanitize_attachable(orders)
    return orders if invoice.cpo_id.blank?

    orders.select do |order|
      [order.cpo_id, order.gst_free_cpo_id].compact.include?(invoice.cpo_id)
    end
  end

  def update_invoice
    invoice_status = case invoice.status
    when 'confirmed', 'amended'
      'amended'
    else
      'pending'
    end
    invoice.reload
    if invoice.update(amount_price: invoice_price, status: invoice_status)
      result.invoice = invoice
    end
  end

  def order_params_for(order)
    order_params = { update_with_invoice: true }
    if has_gst_split_invoicing? && order.has_gst_split_pos? && order.gst_free_cpo_id == invoice_po.id
      order_params[:gst_free_invoice] = invoice
    else
      order_params[:invoice] = invoice
    end
    order_params
  end

  def remove_existing_orders
    detachable_invoice_orders = invoice.orders.where.not(id: attachable_orders.map(&:id))
    detachable_invoice_orders.each do |order|
      order.update(update_with_invoice: true, invoice_id: nil)
    end

    detachable_gst_free_invoice_orders = invoice.gst_free_orders.where.not(id: attachable_orders.map(&:id))
    detachable_gst_free_invoice_orders.each do |order|
      order.update(update_with_invoice: true, gst_free_invoice: nil)
    end
  end

  def invoice_price
    Invoices::CalculateTotals.new(invoice: invoice).call.total
  end

  def has_gst_split_invoicing?
    return @_has_gst_split_invoicing if defined? @_has_gst_split_invoicing

    @_has_gst_split_invoicing ||= invoice_po && attachable_orders.sample.customer_profile.has_gst_split_invoicing
  end

  def invoice_po
    return @_invoice_po if defined? @_invoice_po

    @_invoice_po ||= invoice.customer_purchase_order
  end

  class Result
    attr_accessor :invoice, :attached_orders, :errors

    def initialize(invoice:)
      @invoice = invoice
      @attached_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end