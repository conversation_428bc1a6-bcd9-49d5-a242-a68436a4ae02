class Invoices::Update

  def initialize(invoice:, invoice_params:)
    @invoice = invoice
    @invoice_params = invoice_params
    @result = Result.new
  end

  def call
    if can_update?
      if invoice.update(invoice_params)
        result.invoice = invoice
      else
        result.errors += invoice.errors.full_messages
      end
      result.invoice = invoice
    end
    result
  end

private

  attr_reader :invoice, :invoice_params, :result

  def can_update?
    case
    when invoice.blank?
      result.errors << 'Invoice is missing'
    when invoice_params.blank?
      result.errors << 'Cannot update invoice without valid params'
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :invoice, :errors

    def initialize
      @invoice = invoice
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end