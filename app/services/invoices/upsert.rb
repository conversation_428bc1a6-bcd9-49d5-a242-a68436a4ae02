class Invoices::Upsert

  def initialize(invoice_params:, customer: ,invoice: nil)
    @invoice_params = invoice_params
    @invoice = invoice || Invoice.new
    @customer = customer || invoice&.customer_profile
    @result = Result.new
  end

  def call
    if can_upsert?
      if invoice.update(sanitized_params)
        result.invoice = invoice
      else
        result.errors += invoice.errors.full_messages
      end
      result.invoice = invoice
    end
    result
  end

private

  attr_reader :invoice, :invoice_params, :customer, :result

  def can_upsert?
    case
    when invoice_params.blank?
      result.errors << 'Cannot create/update invoice without valid params'
    when invoice.persisted? && customer.present? && invoice.customer_profile != customer
      result.errors << 'You do not have access to this invoice'    
    end
    result.errors.blank?
  end

  def sanitized_params
    [creation_params, date_params, invoice_params.except(:from_at, :to_at)].inject(&:merge)
  end

  def creation_params
    return {} if invoice.persisted?

    invoice_count = Invoice.where('created_at >= ?', Time.zone.now.beginning_of_day).count + 1
    invoice_number = "#{Time.zone.now.to_s(:invoice_number)}#{format('%03d', invoice_count)}"
    {
      number: invoice_number,
      uuid: SecureRandom.uuid,
      customer_profile: customer,
      status: 'draft',
    }
  end

  def date_params
    return {} if invoice_params[:from_at].blank? || invoice_params[:to_at].blank?

    term_days = customer.company&.payment_term_days.presence || Invoice::DEFAULT_PAYMENT_DAYS
    from_at = invoice_params[:from_at].is_a?(String) ? Time.zone.parse(invoice_params[:from_at]) : invoice_params[:from_at]
    to_at = invoice_params[:to_at].is_a?(String) ? Time.zone.parse(invoice_params[:to_at]) : invoice_params[:to_at]
    {
      from_at: from_at.beginning_of_day,
      to_at: to_at.end_of_day,
      due_at: (to_at + term_days.days).end_of_day
    }
  end

  class Result
    attr_accessor :invoice, :errors

    def initialize
      @invoice = invoice
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end