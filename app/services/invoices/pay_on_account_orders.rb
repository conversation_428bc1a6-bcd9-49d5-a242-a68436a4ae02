class Invoices::PayOnAccountOrders

  def initialize(invoice:, pay_on_account_orders:, credit_card:, notify_customer: true)
    @invoice = invoice
    @pay_on_account_orders = pay_on_account_orders
    @credit_card = credit_card
    @notify_customer = notify_customer
    @result = Result.new
  end

  def call
    begin
      create_cumulative_payment
      refund_any_on_hold_charges
      process_payment
    rescue => exception
      error_message = "Failed to process payment or send tax receipt for invoice #{invoice.id} - #{exception.message}"
      result.errors << error_message
    end
    result
  end

private

  attr_reader :invoice, :pay_on_account_orders, :credit_card, :payment, :notify_customer, :result

  def create_cumulative_payment
    pay_on_account_total = pay_on_account_orders.map(&:customer_total).sum
    order = pay_on_account_orders.last
    result.payment = @payment = Payment.create!(
      amount: pay_on_account_total,
      order_id: order.id,
      invoice_id: invoice.id,
      user_id: order.customer_profile.user.id,
      credit_card_id: credit_card.id
    )
  end

  def refund_any_on_hold_charges
    existing_charges = Order::Charge.where(order: pay_on_account_orders, refund_token: nil).distinct
    return if existing_charges.blank?

    existing_charges.each do |order_charge|
      refunder = Stripe::RefundOrderCharge.new(order_charge: order_charge).call
      if refunder.success?
        order_charge.update(refund_token: refunder.refund.id, status: 'refunded')
      else
        result.errors += refunder.errors
      end
    end
  end

  def process_payment
    payment_processor = Payments::ProcessPayment.new(payment: payment).call
    if payment_processor.success?
      @payment = payment_processor.payment
      pay_on_account_orders.each do |order|
        order.update_with_invoice = true
        order_attributes = {
          credit_card: credit_card,
          payment_status: 'paid'
        }
        order.update(order_attributes)
      end
      update_invoice
      send_tax_receipt
    else
      pay_on_account_orders.each do |order|
        order.update_with_invoice = true
        order.update(payment_status: 'error')
        result.errors << "Order Payment error: #{order.id}: #{payment.response_text}"
      end
      result.errors += payment_processor.errors
    end
  end

  def update_invoice
    return if invoice.payment_status == 'paid'

    payment_value = invoice.payment_value.presence || 0
    payment_value += pay_on_account_orders.map(&:customer_total).sum
    payment_status = invoice.amount_price == payment_value ? 'paid' : 'partial'
    invoice.update(
      payment_value: payment_value,
      payment_status: payment_status
    )
  end

  def send_tax_receipt
    return if !notify_customer

    customer = pay_on_account_orders.last.customer_profile
    Customers::Emails::SendInvoiceReceiptEmail.new(customer: customer, invoice: invoice).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :payment, :errors

    def initialize
      @payment = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
