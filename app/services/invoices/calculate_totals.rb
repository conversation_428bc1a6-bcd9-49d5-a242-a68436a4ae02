class Invoices::CalculateTotals

  def initialize(invoice:)
    @invoice = invoice
    @totals = Totals.new(invoice_orders: invoice_orders)
  end

  def call
    totals.fields.each do |field|
      totals.send("#{field}=", sum_of(field))
    end

    totals
  end

private

  attr_reader :invoice, :totals

  def invoice_orders
    @_invoice_orders ||= invoice.invoice_orders
  end

  def sum_of(field)
    values = order_totals.map(&field)
    values.reject(&:blank?).sum
  end

  def order_totals
    @_order_totals ||= begin
      invoice_orders.map do |order|
        if has_gst_split_invoicing? && order.has_gst_split_pos?
          gst_split = order.gst_free_cpo_id == invoice_po.id ? 'GST-FREE' : 'GST'
          order_totals = Orders::CalculateCustomerTotals.new(order: order, gst_split: gst_split).call
        else
          order_totals = Orders::RetrieveTotals.new(order: order).call
        end
        totals.add_order_totals(order: order, totals: order_totals)
        order_totals
      end
    end
  end

  def has_gst_split_invoicing?
    return @_has_gst_split_invoicing if defined? @_has_gst_split_invoicing

    @_has_gst_split_invoicing ||= invoice_po.present? && invoice_orders.sample.customer_profile&.has_gst_split_invoicing
  end

  def invoice_po
    @_invoice_po ||= invoice.customer_purchase_order
  end

  class Totals
    attr_accessor :delivery, :gst, :surcharge, :topup, :discount, :subtotal, :total, :order_totals
    attr_reader :invoice_orders

    def initialize(invoice_orders:)
      @invoice_orders = invoice_orders
      @subtotal = 0
      @delivery = 0
      @discount = 0
      @topup = 0
      @gst = 0
      @surcharge = 0
      @total = 0
      @order_totals = {}
    end

    def gst_total
      gst_total = gst / yordar_credentials(:yordar, :gst_percent, country_code).to_f
      gst_total >= 0 ? gst_total : 0
    end

    def non_gst_total
      non_gst_total = (total - gst_total - gst)
      non_gst_total >= 0 ? non_gst_total : 0
    end

    def order_count
      invoice_orders.size
    end

    def country_code
      @_country_code ||= invoice_orders.sample.symbolized_country_code
    end

    def [](field)
      try(field)
    end

    def add_order_totals(order:, totals:)
      order_totals[order] = totals
    end

    def fields
      # [:subotal, :delivery, :discount, topup, :gst, :surcharge, :total] # same as order totals fields
      (instance_variables - %i[@order_totals @invoice_orders]).map do |field|
        field.to_s.gsub('@', '').to_sym
      end
    end

    def to_h
      fields.map do |field|
        [field, send(field)]
      end.to_h
    end
  end

end
