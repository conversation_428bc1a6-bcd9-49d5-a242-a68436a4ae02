class DeliveryZones::Upsert

  WEEKDAYS = %i[sun mon tue wed thu fri sat].freeze

  def initialize(supplier:, delivery_zone_params: {}, delivery_zone: nil)
    @supplier = supplier
    @delivery_zone_params = delivery_zone_params
    @delivery_zone = delivery_zone || fetch_delivery_zone
    @result = Result.new
  end

  def call
    if can_upsert?
      @delivery_suburb_ids = [delivery_zone.suburb_id, sanitized_params[:suburb_id]].reject(&:blank?).map(&:to_i)
      if delivery_zone.update(sanitized_params)
        result.delivery_zone = delivery_zone
        update_supplier_delivery_details
        update_deliverable_suburbs
      else
        result.errors += delivery_zone.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :supplier, :delivery_zone_params, :delivery_zone, :result

  def is_supplier?
    supplier.present? && supplier.profile.present? && supplier.profile.is_supplier?
  end

  def can_upsert?
    case
    when !is_supplier?
      result.errors << 'Cannot create a delivery zone without a supplier'
    when delivery_zone.supplier_profile != supplier
      result.errors << 'The delivery zone does not belong to you.'
    end
    result.errors.blank?
  end

  def fetch_delivery_zone
    is_supplier? && supplier.delivery_zones.new
  end

  def sanitized_params
    weekday_params = WEEKDAYS.dup
    excepted_parms = weekday_params + %i[operating_hours_start operating_hours_end]
    params = delivery_zone_params.to_h.symbolize_keys.except(*excepted_parms)
    [params, calculated_params].inject(&:merge)
  end

  def calculated_params
    params = {}
    params[:operating_hours_start] = sanitized_time(delivery_zone_params[:operating_hours_start]) if delivery_zone_params[:operating_hours_start].present?
    params[:operating_hours_end] = sanitized_time(delivery_zone_params[:operating_hours_end]) if delivery_zone_params[:operating_hours_end].present?
    params[:operating_wdays] = day_based_operating_workdays if day_based_operating_workdays.present?
    params
  end

  def sanitized_time(time)
    return nil if time.blank?

    Time.zone.parse(time).seconds_since_midnight
  end

  def day_based_operating_workdays
    return nil if WEEKDAYS.none?{|weekday| delivery_zone_params[weekday].present? }

    @_sanitized_operating_week_days ||= WEEKDAYS.map do |weekday|
      delivery_zone_params[weekday]
    end.join
  end

  def update_supplier_delivery_details
    Suppliers::Cache::DeliveryDetails.new(supplier: supplier).delay(queue: :data_integrity).call
  end

  def update_deliverable_suburbs
    DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone, refresh: true).delay(queue: :data_integrity).call
  end

  class Result
    attr_accessor :delivery_zone, :errors

    def initialize
      @delivery_zone = nil
      @errors = []
    end

    def success?
      errors.blank? && delivery_zone.present? && delivery_zone.persisted?
    end
  end

end
