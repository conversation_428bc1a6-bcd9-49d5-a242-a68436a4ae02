class DeliveryZones::Remove

  def initialize(supplier:, delivery_zone:)
    @supplier = supplier
    @delivery_zone = delivery_zone
    @result = Result.new
  end

  def call
    if can_remove? && delivery_zone.destroy
      update_supplier_delivery_details
    end
    result
  end

private

  attr_reader :supplier, :delivery_zone, :result

  def can_remove?
    case
    when supplier.blank?
      result.errors << 'Cannot remove a delivery zone without a supplier'
    when delivery_zone.blank?
      result.errors << 'Cannot remove a missing delivery zone'
    when delivery_zone.supplier_profile != supplier
      result.errors << 'You do not have access to this delivery zone'
    end
    result.errors.blank?
  end

  def update_supplier_delivery_details
    Suppliers::Cache::DeliveryDetails.new(supplier: supplier).delay(queue: :data_integrity).call
  end

  class Result
    attr_accessor :errors

    def initialize
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
