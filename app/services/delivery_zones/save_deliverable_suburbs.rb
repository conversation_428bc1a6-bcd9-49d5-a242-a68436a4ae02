class DeliveryZones::SaveDeliverableSuburbs

  def initialize(delivery_zone:, refresh: false, cached_deliverable_suburbs: [])
    @delivery_zone = delivery_zone
    @refresh = refresh
    @cached_deliverable_suburbs = cached_deliverable_suburbs
    @suburbs = []
  end

  def call
    cleanup_deliverable_suburbs if refresh
    if cached_deliverable_suburbs.present?
      sanitize_cached_suburbs
    else
      gather_deliverable_suburbs
    end
    suburbs
  end

private

  attr_reader :delivery_zone, :refresh, :cached_deliverable_suburbs, :suburbs

  def supplier
    @_supplier ||= delivery_zone.supplier_profile
  end

  def gather_deliverable_suburbs
    suburbs << save_deliverable_suburb(zone_suburb, 0)
    (postcode_suburbs + state_suburbs).each do |suburb|
      distance = distance_for(suburb)
      if suburb.postcode == zone_suburb.postcode || distance < delivery_zone.radius
        suburbs << save_deliverable_suburb(suburb, distance)
      end
    end
  end

  def save_deliverable_suburb(suburb, distance)
    deliverable_suburb = delivery_zone.deliverable_suburbs.where(supplier_profile: supplier, suburb: suburb).first_or_initialize
    deliverable_suburb.update(distance: distance)
    deliverable_suburb
  end

  def sanitize_cached_suburbs
    cached_deliverable_suburbs.each do |deliverable_suburb|
      suburbs << save_deliverable_suburb(deliverable_suburb.suburb, deliverable_suburb.distance)
    end
  end

  def zone_suburb
    @_zone_suburb ||= delivery_zone.suburb
  end

  def state_suburbs
    return [] if delivery_zone.radius <= 0

    @_state_suburbs = Suburb.where.not(latitude: nil).where.not(longitude: nil).where(state: zone_suburb.state).where.not(id: postcode_suburbs.select(:id)).where.not(id: zone_suburb.id)
  end

  def postcode_suburbs
    @_postcode_suburbs = Suburb.where.not(latitude: nil).where.not(longitude: nil).where(postcode: zone_suburb.postcode).where.not(id: zone_suburb.id)
  end

  def distance_for(suburb)
    Haversine.distance(zone_suburb.latitude, zone_suburb.longitude, suburb.latitude, suburb.longitude).to_kilometers
  end

  def cleanup_deliverable_suburbs
    delivery_zone.deliverable_suburbs.destroy_all
  end

end
