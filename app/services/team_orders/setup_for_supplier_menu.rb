class TeamOrders::SetupForSupplierMenu

  def initialize(team_order_params:, is_admin: false, profile: nil, time: Time.zone.now)
    @team_order_params = team_order_params
    @is_admin = is_admin
    @profile = profile
    @time = time
    @result = Result.new
  end

  def call
    setup_team_order
    validate_team_order
    result
  end

private

  attr_reader :team_order_params, :is_admin, :profile, :time
  attr_accessor :result

  def validate_team_order
    case
    when team_order_attendee.blank? || team_order.blank? 
      result.error = :missing_order_attendee
      result.team_order_attendee = nil
    when !team_order_attendee.event_attendee.active?
      result.error = :missing_order_attendee
      result.team_order_attendee = nil
    when %w[paused cancelled].include?(team_order.status)
      result.error = :cancelled_event
    when !ordering_as_team_admin? && !is_admin && time < cutoff_time && (time + TeamOrderAttendee::SOFT_CUTOFF_THRESHOLD) >= cutoff_time
      result.warnings << :cutoff_almost_exceeded
    when !is_admin && cutoff_time < time && (cutoff_time + Order::TEAM_ORDER_GRACE_PERIOD) > time
      result.error = :cutoff_exceeded
    when !is_admin && %w[new amended confirmed].include?(team_order.status)
      result.error = :order_closed
    when team_order.status == 'delivered'
      result.error = :order_closed
    when !is_admin && (cutoff_time + Order::TEAM_ORDER_GRACE_PERIOD) < time
      result.error = :order_closed
    end
    # cumulative warnings
    if !is_admin && !ordering_as_team_admin? && team_order_attendee&.status == 'pending'
      result.warnings << :pending_order_confirmation
    end
    result.error.blank?
  end

  def team_order_attendee
    @_team_order_attendee ||= case
    when team_order_params[:event_id].present?
      TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: profile).call
    else
      TeamOrderAttendees::Fetch.new(attendee_code: team_order_params[:code], profile: profile).call
    end
  end

  def team_order
    @_team_order ||= team_order_attendee&.order
  end

  def cutoff_time
    @_cutoff_time ||= Orders::FetchLeadTime.new(order: team_order).call.lead_time
  end

  def ordering_as_team_admin?
    return false if team_order_attendee.blank?

    team_order_attendee.is_team_admin? || (profile.present? && team_order.present? && team_order.customer_profile == profile)
  end

  def setup_team_order
    result.order = team_order
    result.team_order_attendee = team_order_attendee
    result.supplier = team_order_supplier
  end

  def team_order_supplier
    return nil if team_order.blank?

    team_order.team_supplier_profiles.first
  end

  class Result
    attr_accessor :order, :team_order_attendee, :supplier, :error, :warnings

    def initialize
      @order = nil
      @team_order_attendee = nil
      @supplier = nil
      @error = nil
      @warnings = []
    end

    def success?
      error.blank?
    end

    def is_team_order?
      true
    end
  end

end
