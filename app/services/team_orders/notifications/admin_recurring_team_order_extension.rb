class TeamOrders::Notifications::AdminRecurringTeamOrderExtension

  def initialize(time: Time.zone.now, final_reminder: false)
    @time = time
    @final_reminder = final_reminder
    @notifications = []
  end

  def call
    notifiable_team_orders.each do |recurring_team_order|
      notifications << TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail.new(team_order: recurring_team_order, extension_week: extension_order_lister.extension_week, final_reminder: final_reminder).call
    end
    notifications
  end

private

  attr_reader :time, :final_reminder, :notifications

  def extension_order_lister
    @_extension_order_lister ||= TeamOrders::ListExtendableOrders.new(time: time).call
  end

  def notifiable_team_orders
    @_notifiable_team_orders ||= extension_order_lister.extendable_team_orders
  end

end
