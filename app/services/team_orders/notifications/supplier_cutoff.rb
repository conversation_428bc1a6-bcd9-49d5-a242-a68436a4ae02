class TeamOrders::Notifications::Supplier<PERSON><PERSON>ff < TeamOrders::Notifications::Cutoff

  def initialize(cutoff_time: '4hr', time: Time.zone.now)
    super
  end

  def call
    pending_team_orders.each do |team_order|
      next if !can_notify_suppliers_for(team_order)

      notifiable_suppliers_for(team_order).each do |supplier|
        Suppliers::Emails::SendTeamOrderCutoffEmail.new(team_order: team_order, supplier: supplier, cutoff_time: cutoff_time).call
      end
    end
  end

private

  def pending_team_orders
    pending_orders = Order.where(order_variant: %w[team_order recurring_team_order], status: 'pending')
    pending_orders = pending_orders.where('delivery_at between ? and ?', time, (time + DELIVERY_THRESHOLD))
    pending_orders = pending_orders.joins(:order_suppliers).merge(OrderSupplier.where("#{supplier_reminder_attribute} is NULL"))
    pending_orders.distinct
  end

  def can_notify_suppliers_for(team_order)
    lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call
    lead_time_fetcher.lead_time.between?(start_hours, end_hours)
  end

  def notifiable_suppliers_for(team_order)
    team_order.order_suppliers.where("#{supplier_reminder_attribute} is NULL").map(&:supplier_profile)
  end

  def supplier_reminder_attribute
    "cutoff_#{cutoff_time}_reminder"
  end

end
