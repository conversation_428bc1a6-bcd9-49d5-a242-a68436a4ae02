class TeamOrders::Notifications::Cutoff

  DELIVERY_THRESHOLD = 1.week

  def initialize(cutoff_time:, time: Time.zone.now)
    @cutoff_time = cutoff_time
    @time = time
  end

private

  attr_reader :cutoff_time, :time

  def start_hours
    case cutoff_time
    when '30m'
      time
    when '1hr'
      time + 30.minutes
    when '2hr'
      time + 30.minutes
    when '4hr'
      time + 2.hours
    when 'day', '24hr'
      (time + future_days).beginning_of_day
    end
  end

  def end_hours
    case cutoff_time
    when '30m'
      time + 30.minutes
    when '1hr'
      time + 1.hours
    when '2hr'
      time + 2.hours
    when '4hr'
      time + 4.hours
    when 'day', '24hr'
      (time + future_days).end_of_day
    end
  end

  def future_days
    @_future_days ||= time.wday == 5 ? 3.days : 1.day
  end

end
