class TeamOrders::Notifications::AdminAnonymousAttendees < TeamOrders::Notifications::Cutoff

  def initialize(time: Time.zone.now, cutoff_time: '1hr')
    super
  end

  def call
    pending_team_order_with_anonymous_attendees.each do |team_order|
      if can_notify_team_admin_for(team_order)
        TeamOrders::Emails::SendAdminAnonymousAttendeesEmail.new(team_order: team_order, cutoff_time: cutoff_time).call
      # else # ignore team order
      end
    end
  end

private

  def pending_team_order_with_anonymous_attendees
    pending_orders = Order.where(order_variant: %w[team_order recurring_team_order], status: %w[pending amended])
    pending_orders = pending_orders.where('delivery_at between ? and ?', time, (time + DELIVERY_THRESHOLD))
    pending_orders = pending_orders.joins(:team_order_detail).where("team_order_details.#{admin_reminder_attribute} is NULL")
    pending_orders = pending_orders.joins(:team_order_attendees).where(team_order_attendees: { anonymous: true, status: %w[invited pending ordered] })
    pending_orders.distinct
  end

  def can_notify_team_admin_for(team_order)
    lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call
    lead_time_fetcher.lead_time.between?(start_hours, end_hours)
  end

  def admin_reminder_attribute
    'anonymous_attendees_reminder'
  end

end
