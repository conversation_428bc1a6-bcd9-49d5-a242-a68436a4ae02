class TeamOrders::Notifications::<PERSON><PERSON><PERSON><PERSON><PERSON> < TeamOrders::Notifications::Cutoff

  def initialize(cutoff_time:, time: Time.zone.now)
    super
  end

  def call
    pending_team_orders.each do |team_order|
      next if !can_notify_team_admin_for(team_order)

      TeamOrders::Emails::SendAdminCutOffEmail.new(team_order: team_order, cutoff_time: cutoff_time).call
      log_event_for(team_order: team_order)
    end
  end

private

  def pending_team_orders
    pending_orders = Order.where(order_variant: %w[team_order recurring_team_order], status: 'pending')
    pending_orders = pending_orders.where('delivery_at between ? and ?', time, (time + DELIVERY_THRESHOLD))
    pending_orders = pending_orders.joins(:team_order_detail).merge(TeamOrder::Detail.where("#{admin_reminder_attribute} is NULL"))
    pending_orders.distinct
  end

  def can_notify_team_admin_for(team_order)
    lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call
    lead_time_fetcher.lead_time.between?(start_hours, end_hours)
  end

  def log_event_for(team_order:)
    order_supplier_spends = Orders::GetSupplierSpends.new(order: team_order, exclude_surcharge: true).call
    if order_supplier_spends.is_under?
      EventLogs::Create.new(event_object: team_order, event: 'approaching-cutoff-below-minimum', severity: 'warning', cutoff: cutoff_time, cutoff_option: team_order.cutoff_option, remaining_spend: order_supplier_spends.supplier_spends.first.remaining_spend.round(2)).call
    else
      EventLogs::Create.new(event_object: team_order, event: 'approaching-cutoff', cutoff: cutoff_time).call
    end
  end

  def admin_reminder_attribute
    "cutoff_#{cutoff_time}_reminder"
  end

end
