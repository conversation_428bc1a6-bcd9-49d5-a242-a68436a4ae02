class TeamOrders::ListPackageOrders

  def initialize(team_order:, options: {})
    @team_order = team_order
    @options = [default_options, options].inject(&:merge)
  end

  def call
    if team_order.blank? || !team_order.is_package_order?
      return Order.none
    end

    @package_orders = Order.joins(:team_order_detail)
    filter_linked_orders
    filter_active_orders if options[:active_only].present?
    filter_future_orders if options[:future_only].present? && options[:scoped_time].present?
    filter_by_attendee if options[:for_attendee].present? && !team_order.is_recurring_team_order?
    filter_out_self if options[:exclude_self].present?
    scope_orders if options[:scoped_to].present?
    sort_orders
    package_orders.distinct
  end

private

  attr_reader :team_order, :options, :package_orders

  def filter_linked_orders
    @package_orders = package_orders.where(team_order_details: { package_id: team_order.package_id })
  end

  def filter_active_orders
    @package_orders = package_orders.where(status: %w[pending new amended])
  end

  def filter_future_orders
    @package_orders = package_orders.where('delivery_at >= ?', options[:scoped_time])
  end

  def filter_by_attendee
    @package_orders = package_orders.joins(:team_order_attendees)
    @package_orders = package_orders.where(team_order_attendees: { event_attendee: options[:for_attendee].event_attendee })
    @package_orders = package_orders.where.not(team_order_attendees: { status: %w[declined cancelled] })
  end

  def filter_out_self
    @package_orders = package_orders.where.not(id: team_order.id)
  end

  def scope_orders
    scoped_time = options[:scoped_time].present? && options[:scoped_time].is_a?(String) ? Time.parse(options[:scoped_time]) : options[:scoped_time]
    case options[:scoped_to]
    when 'recent_month'
       @package_orders = package_orders.where(delivery_at: [scoped_time.beginning_of_month..scoped_time.end_of_month])
    when 'recent_fortnight'
       @package_orders = package_orders.where(delivery_at: [scoped_time.beginning_of_week..(scoped_time + 1.week).end_of_week])
    when 'recent_week'
      @package_orders = package_orders.where(delivery_at: [scoped_time.beginning_of_week..scoped_time.end_of_week])
    # else # all # do nothing
    end
  end

  def sort_orders
    @package_orders = case
    when options[:order_by].present? && options[:order_by].is_a?(Hash)
      package_orders.order(**options[:order_by])
    when options[:order_by].present?
      package_orders.order(options[:order_by])
    else
      package_orders.order(delivery_at: :asc, id: :asc)
    end
  end

  def default_options
    {
      active_only: false,
      future_only: false,
      exclude_self: false,
      for_attendee: nil,
      order_by: nil,
      scoped_to: nil,
      scoped_time: Time.zone.now,
    }
  end

end
