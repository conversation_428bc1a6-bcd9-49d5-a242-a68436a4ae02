class TeamOrders::SetupFor<PERSON>ew<PERSON>rder

  def initialize(team_admin:, recurring_team_order: false)
    @team_order = get_default_team_order(recurring_team_order)
    @team_admin = team_admin
  end

  def call
    set_delivery_info
    setup_contact_info
    set_team_order_detail
    set_default_payment
    team_order
  end

private

  attr_accessor :team_order
  attr_reader :team_admin

  def get_default_team_order(recurring_team_order)
    Order.new({
      order_type: 'one-off',
      status: 'draft',
      order_variant: (recurring_team_order ? 'recurring_team_order' : 'team_order'),
    })
  end

  def latest_team_order
    @_latest_team_order ||= team_admin.orders.where(order_variant: %w[team_order recurring_team_order], status: %w[confirmed delivered new pending]).last
  end

  def set_delivery_info
    return if latest_team_order.blank?

    team_order.delivery_suburb ||= latest_team_order.delivery_suburb
    team_order.delivery_address_level ||= latest_team_order.delivery_address_level
    team_order.delivery_address ||= latest_team_order.delivery_address
    team_order.delivery_instruction ||= latest_team_order.delivery_instruction
  end

  def setup_contact_info
    team_order.contact_name ||= team_admin.name
    team_order.company_name ||= case
      when team_admin.company.present?
        team_admin.company.name
      when team_admin.company_name.present?
        team_admin.company_name
      end
    team_order.phone ||= team_admin.contact_phone || team_admin.mobile
  end

  def set_team_order_detail
    team_order.build_team_order_detail
  end

  def set_default_payment
    has_account = team_admin.can_pay_on_account?
    customer_credit_cards = team_admin.credit_cards.where(enabled: true, saved_for_future: true, pay_on_account: false)
    customer_credit_cards = customer_credit_cards.where.not(stripe_token: nil) if Time.zone.now >= Time.zone.parse(yordar_credentials(:stripe, :migration_date))
    customer_credit_cards = customer_credit_cards.order(created_at: :desc)
    team_order.credit_card_id ||= (has_account && pay_on_account_card.id) || customer_credit_cards.pluck(:id).first
  end

  def pay_on_account_card
    CreditCard.where(id: 1, pay_on_account: true).first
  end

end
