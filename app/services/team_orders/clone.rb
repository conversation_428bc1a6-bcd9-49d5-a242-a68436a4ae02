class TeamOrders::<PERSON><PERSON>

  def initialize(team_order:, extension_week: nil)
    @team_order = team_order
    @extension_week = extension_week
    @result = Result.new(order: team_order)
  end

  def call
    clone_team_order
    if cloned_team_order.valid?
      clone_team_order_detail
      clone_team_order_levels if team_order.team_order_levels.present?
      if is_package_order?
        clone_delivery_dates
      else
        clone_attendees
        clone_suppliers
      end
      result.cloned_team_order = cloned_team_order
    else
      result.errors << cloned_team_order.errors.full_messages
    end
    result
  end

private

  attr_accessor :team_order, :cloned_team_order, :extension_week, :result

  def team_admin
    @_team_admin ||= team_order.customer_profile
  end

  def clone_team_order
    @cloned_team_order = team_order.dup
    cloned_team_order.status = 'draft'
    cloned_team_order.invoice = nil
    cloned_team_order.delivery_at = nil
    cloned_team_order.unique_event_id = nil
    result.cloned_team_order = cloned_team_order
  end

  def clone_team_order_detail
    cloned_team_order.build_team_order_detail(
      package_id: (is_package_order? ? team_order.package_id : nil),
      budget: team_order.team_order_budget,
      hide_budget: team_order.hide_budget,
      cutoff_option: team_order.cutoff_option.presence || 'charge_to_minimum',
      attendee_pays: team_order.attendee_pays || false
    )
  end

  def clone_team_order_levels
    cloned_team_order.team_order_detail.levels = team_order.team_order_levels.map do |level|
      TeamOrder::Level.new(name: level.name)
    end
  end

  def clone_attendees
    invited_attendees = team_order.team_order_attendees.where(status: %w[invited pending ordered], anonymous: false)
    invited_attendees.each do |invited_attendee|
      cloned_attendee = TeamOrderAttendee.new(
        event_attendee: invited_attendee.event_attendee,
        status: 'invited'
      )
      cloned_team_order.team_order_attendees << cloned_attendee
    end
  end

  def clone_suppliers
    team_order.order_suppliers.each do |order_supplier|
      cloned_supplier = OrderSupplier.new(
        supplier_profile: order_supplier.supplier_profile,
        selected_menu_sections: order_supplier.selected_menu_sections
      )
      cloned_team_order.order_suppliers << cloned_supplier
    end
  end

  def is_package_order?
    @_is_package_order ||= team_order.is_package_order?
  end

  def clone_delivery_dates
    week_starting = (extension_week.present? && Time.zone.parse(extension_week.to_s).presence) rescue nil
    future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: team_order, week_starting: week_starting).call
    result.cloned_team_order.delivery_dates = future_delivery_orders.map(&:delivery_at)
  end

  class Result
    attr_accessor :cloned_team_order, :errors
    attr_reader :orginial_order

    def initialize(order:)
      @orginial_order = order
      @cloned_team_order = nil
      @errors = []
    end

    def success?
      errors.blank? && cloned_team_order.present? # && cloned_team_order
    end
  end

end
