class TeamOrders::Can<PERSON>

  def initialize(team_order:, team_admin:, notify_attendees: true)
    @team_order = team_order
    @cached_order_status = team_order.status.dup
    @team_admin = team_admin
    @notify_attendees = notify_attendees
    @result = Result.new(team_order: team_order)
  end

  def call
    if can_cancel? && order_cancelled? && notify_attendees
      notify_team_order_attendees
    end
    result
  end

private

  attr_reader :team_order, :cached_order_status, :team_admin, :notify_attendees, :result

  def can_cancel?
    case
    when team_order.customer_profile != team_admin
      result.errors << 'You don\'t have access to this team order'
    when team_order.status == 'cancelled'
      result.errors << 'Order is already cancelled'
    when team_order.status == 'delivered'
      result.errors << 'Cannot cancel a delivered order'
    end
    result.errors.blank?
  end

  def order_cancelled?
    order_canceller = Orders::Cancel.new(order: team_order, mode: 'one-off').call
    if order_canceller.success?
      true
    else
      result.errors += order_canceller.errors
      false
    end
  end

  def notifiable_attendee
    attendee_statuses = %w[pending ordered]
    attendee_statuses << 'invited' if cached_order_status == 'pending'
    team_order.team_order_attendees.where(status: attendee_statuses)
  end

  def notify_team_order_attendees
    notifiable_attendee.each do |team_order_attendee|
      TeamOrderAttendees::Emails::SendOrderCancelledEmail.new(team_order_attendee: team_order_attendee, previous_order_status: cached_order_status).call
    end
  end

  class Result
    attr_accessor :team_order, :errors

    def initialize(team_order:)
      @team_order = team_order
      @errors = []
    end

    def success?
      errors.blank? && team_order.status == 'cancelled'
    end
  end
end
