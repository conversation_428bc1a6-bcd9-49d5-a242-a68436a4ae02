class TeamOrders::AutoConfirm < Orders::<PERSON>Confirm

  def initialize(time: Time.zone.now)
    super
  end

  def call
    auto_confirmable_team_orders.each do |team_order|
      begin
        if can_confirm(team_order)
          confirm_order(team_order)
        end
      rescue => exception
        Rails.logger.error "Failed to automatically confirm team order ##{team_order.id}"
        Rails.logger.error exception.inspect
        Rails.logger.error exception.backtrace.join('\n')
        Raven.capture_exception(exception,
            message: "Failed to automatically confirm team order ##{team_order.id}",
            extra: { team_order: team_order.id },
            transaction: 'TeamOrders::AutoConfirm'
          )
      end
    end
    send_confirmation_check_email if result.auto_confirmed_orders.present?
    result
  end

private

  def auto_confirmable_team_orders
    orders = Order.where(split_order_id: nil)
    orders = orders.where(delivery_at: (time.beginning_of_day..till_date.end_of_day))
    orders = orders.where(status: %w[new amended])
    orders = orders.where(order_variant: %w[team_order recurring_team_order])
    orders
  end

  def can_confirm(team_order)
    lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call
    (lead_time_fetcher.lead_time + Order::TEAM_ORDER_GRACE_PERIOD) <= time
  end

end
