class TeamOrders::<PERSON>tchCutoffDayHour

  def initialize(team_order:, compared_to: Time.zone.now)
    @team_order = team_order
    @compared_to = compared_to
    @result = Result.new(team_order: team_order)
  end

  def call
    result.days_left = total_hours_left < 0 ? 0 : total_hours_left / 24
    result.hours_left = total_hours_left < 0 ? 0 : total_hours_left % 24
    result
  end

private

  attr_reader :team_order, :compared_to, :result

  def cutoff_datetime
    @_cutoff_datetime ||= Orders::FetchLeadTime.new(order: team_order).call.lead_time
  end

  def total_hours_left
    @_total_hours_left ||= ((cutoff_datetime - compared_to) / 1.hour).round
  end

  class Result
    attr_accessor :days_left, :hours_left
    attr_reader :team_order

    def initialize(team_order:)
      @team_order = team_order
      @days_left = nil
      @hours_left = nil
    end

    def has_expired?
      %w[cancelled delivered].include?(team_order.status) || (days_left <= 0 && hours_left <= 0)
    end

    def is_today?
      days_left == 0 && hours_left > 0
    end
  end

end
