class TeamOrders::CreatePackage

  def initialize(team_order_params:, team_admin:)
    @team_order_params = team_order_params
    @team_admin = team_admin
    @result = Result.new
  end

  def call
    result.package_id = package_id
    delivery_dates.each do |delivery_date|
      team_order = create_team_order_for(delivery_date: delivery_date)
      result.package_team_orders << team_order
    end
    notify_admin
    notify_attendees
    log_event
    result
  end

private

  attr_reader :team_order_params, :team_admin, :result

  def extension_package_id
    @_extension_package_id ||= team_order_params[:team_order_detail_attributes].present? && team_order_params[:team_order_detail_attributes][:package_id].presence
  end

  def package_id
    @_package_id ||= extension_package_id || SecureRandom.uuid
  end

  def delivery_dates
    @_delivery_dates ||= team_order_params[:delivery_dates].map do |delivery_date|
      delivery_date.is_a?(String) ? Time.zone.parse(delivery_date).to_date : delivery_date
    end.sort
  end

  def proposed_delivery_at
    team_order_params[:delivery_at].is_a?(String) ? Time.zone.parse(team_order_params[:delivery_at]) : team_order_params[:delivery_at]
  end

  def create_team_order_for(delivery_date:)
    created_order = nil
    order_creator = TeamOrders::Create.new(team_order_params: team_order_params_for(delivery_date: delivery_date), team_admin: team_admin, package_id: package_id).call
    if order_creator.present?
      created_order = order_creator.team_order
    else
      result.errors += order_creator.errors
    end
    created_order
  end

  def team_order_params_for(delivery_date:)
    delivery_at = delivery_date.to_time.in_time_zone.change(hour: proposed_delivery_at.hour, min: proposed_delivery_at.min)
    current_order_params = {
      delivery_at: delivery_at,
      delivery_suppliers: delivery_suppliers_for(delivery_date: delivery_date),
    }
    delivery_team_order_params = team_order_params.merge(current_order_params)
    delivery_team_order_params = delivery_team_order_params.except(:delivery_dates)
    delivery_team_order_params
  end

  def delivery_suppliers_for(delivery_date:)
    return nil if team_order_delivery_suppliers.blank?

    delivery_date_supplier = team_order_delivery_suppliers.detect do |supplier_date, _|
      supplier_delivery_date = supplier_date.is_a?(String) ? Time.zone.parse(supplier_date).to_date : supplier_date
      supplier_delivery_date == delivery_date
    end
    [delivery_date_supplier].to_h
  end

  def team_order_delivery_suppliers
    suppliers = team_order_params[:delivery_suppliers]
    suppliers = suppliers.to_unsafe_h if suppliers.present? && suppliers.is_a?(ActionController::Parameters)
    suppliers
  end

  def notify_admin
    TeamOrders::Emails::SendAdminNewPackageOrderEmail.new(package_orders: result.package_team_orders, is_extension: extension_package_id.present?).delay(queue: :notifications).call
  end

  def notify_attendees
    result.team_order.team_order_attendees.each do |package_order_attendee|
      TeamOrderAttendees::Emails::SendPackageInviteEmail.new(package_order_attendee: package_order_attendee, package_orders: result.package_team_orders).delay(queue: :notifications).call
    end
  end

  def log_event
    package_event = extension_package_id.present? ? 'package-extended' : 'new-package-created'
    EventLogs::Create.new(event_object: result.team_order, event: package_event).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :package_id, :package_team_orders, :errors

    def initialize
      @package_id = nil
      @package_team_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end

    def team_order
      @_team_order ||= package_team_orders.first
    end
  end
end
