# delivery_suppliers is a hash / json with the following structure
# {
#   <delivery_date(yyyy/-mm/dd)>: {
#     <supplier_id>: [<selected_menu_section_ids],
#     <supplier_id2>: [<selected_menu_section_ids2]
#   }
# }

class TeamOrders::AttachSuppliers

  def initialize(team_order:, delivery_suppliers: {})
    @team_order = team_order
    @delivery_suppliers = delivery_suppliers
    @result = Result.new(team_order: team_order)
  end

  def call
    remove_suppliers if existing_team_suppliers.present?
    attach_suppliers
    result
  end

private

  attr_reader :team_order, :delivery_suppliers, :result

  def existing_team_suppliers
    @_existing_team_suppliers ||= team_order.team_supplier_profiles
  end

  def remove_suppliers
    removed_order_suppliers = team_order.order_suppliers.where.not(supplier_profile_id: delivery_suppliers.keys)

    removed_order_suppliers.each do |removed_order_supplier|
      team_supplier = removed_order_supplier.supplier_profile
      remove_supplier_order_lines(team_supplier)
      notify_cancelled_supplier(team_supplier)
      removed_order_supplier.destroy
    end
  end

  def remove_supplier_order_lines(supplier)
    lister_options = {
      order: team_order,
      for_supplier: supplier
    }
    supplier_order_lines = OrderLines::List.new(options: lister_options).call
    if supplier_order_lines.present?
      supplier_order_lines.group_by(&:team_order_attendee).each do |attendee, _|
        attendee.update(status: 'invited') if attendee.present?
      end
      supplier_order_lines.each(&:destroy)
      Orders::CalculateCustomerTotals.new(order: team_order, save_totals: true).call # update totals
    end
  end

  def notify_cancelled_supplier(supplier)
    Suppliers::Emails::SendOrderCancelledEmail.new(mode: 'one-off', supplier: supplier, orders: [team_order]).delay(queue: :notifications).call
  end

  def attach_suppliers
    delivery_suppliers.each do |supplier_id, selected_menu_section_ids|
      supplier = SupplierProfile.where(id: supplier_id).first
      if supplier.present?
        attach_supplier(supplier, selected_menu_section_ids)
      else
        result.errors << 'Cannot attach a missing supplier'
      end
    end
  end

  def attach_supplier(supplier, selected_menu_section_ids)
    order_supplier = team_order.order_suppliers.where(supplier_profile: supplier).first_or_create

    if !selected_menu_section_ids.nil? && selected_menu_section_ids.is_a?(Array) # update menu section config if passed
      menu_sections = supplier.menu_sections.where(id: selected_menu_section_ids)
      order_supplier.update(selected_menu_sections: menu_sections.map(&:id))
    end

    if existing_team_suppliers.exclude?(supplier)
      result.attached_suppliers << order_supplier
      send_supplier_heads_up_email_for(supplier) # notify if new supplier
    end
  end

  def send_supplier_heads_up_email_for(supplier)
    Suppliers::Emails::SendTeamOrderHeadsUpEmail.new(team_order: team_order, supplier: supplier).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :attached_suppliers, :errors
    attr_reader :team_order

    def initialize(team_order:)
      @team_order = team_order
      @attached_suppliers = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
