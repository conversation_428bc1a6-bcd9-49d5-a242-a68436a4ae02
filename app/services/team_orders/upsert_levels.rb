class TeamOrders::UpsertLevels

  def initialize(team_order_detail:, names: [])
    @team_order_detail = team_order_detail
    @names = names.present? ? names.reject(&:blank?) : []
    @result = Result.new
  end

  def call
    if can_upsert?
      create_new_levels
      remove_missing_levels
    end
    result
  end

private

  attr_reader :team_order_detail, :names, :result

  def can_upsert?
    case
    when team_order_detail.blank?
      result.errors << 'Cannot create/update team order levels without a team order detail'
    end
    result.errors.blank?
  end

  def downcased_names
    names.map(&:downcase)
  end

  def create_new_levels
    new_level_names = names.reject{|name| downcased_existing_names.include?(name.downcase) }
    new_level_names.each do |name|
      next if team_order_levels.where('lower(name) ilike ?', name.downcase).present?

      result.levels << team_order_levels.where(name: name).first_or_create
    end
  end

  def remove_missing_levels
    deletable_names = downcased_existing_names - downcased_names
    team_order_levels.where('lower(name) in (?)', deletable_names).each(&:destroy)
  end

  def downcased_existing_names
    @_downcased_existing_names ||= team_order_levels.map{|level| level.name.downcase }
  end

  def team_order_levels
    @_existing_levels ||= team_order_detail.levels
  end

  class Result
    attr_accessor :levels, :errors

    def initialize
      @levels = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
