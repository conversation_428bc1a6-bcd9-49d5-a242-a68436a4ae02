class TeamOrders::UpsertDetail

  def initialize(team_order:, team_order_detail_params:)
    @team_order = team_order
    @team_order_detail_params = team_order_detail_params.presence || {}
    @result = Result.new(team_order: team_order)
  end

  def call
    if team_order_detail.update(sanitized_params)
      upsert_levels if team_order_detail_params[:levels].present?
      result.team_order_detail = team_order_detail
    else
      result.errors += team_order_detail.errors.full_messages
    end
    result
  end

private

  attr_reader :team_order, :team_order_detail_params, :result

  def team_order_detail
    @_team_order_detail ||= team_order.team_order_detail || team_order.build_team_order_detail
  end

  def sanitized_params
    params = default_params
    params = params.merge(team_order_detail_params.except(:levels))
    params
  end

  def default_params
    return {} if !team_order_detail.new_record?

    {
      cutoff_option: 'charge_to_minimum',
    }
  end

  def upsert_levels
    TeamOrders::UpsertLevels.new(team_order_detail: team_order_detail, names: team_order_detail_params[:levels][:names]).call
  end

  class Result
    attr_reader :team_order
    attr_accessor :team_order_detail, :errors

    def initialize(team_order:)
      @team_order = team_order
      @team_order_detail = nil
      @errors = []
    end

    def success?
      errors.blank? && team_order.present? && team_order_detail.present? && team_order_detail.order == team_order
    end
  end
end
