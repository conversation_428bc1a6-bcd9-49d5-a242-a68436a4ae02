class TeamOrders::ListPotentialFutureDeliveryOrders

  WEEKS_THRESHOLD = 5

  DeliveryOrder = Struct.new(:team_order, :delivery_at, :order_suppliers)

  def initialize(team_order:, week_starting: nil)
    @team_order = team_order
    @week_starting = week_starting.presence || next_recurring_week
    @delivery_orders = []
  end

  def call
    return delivery_orders if !team_order.is_package_order?

    previous_week_package_orders.each do |package_order|
      last_week_order = last_week_orders.detect{|order| order.delivery_at.to_s(:weekday) == package_order.delivery_at.to_s(:weekday) }

      potential_order = last_week_order.present? ? last_week_order : package_order
      delivery_date = next_delivery_date_for(package_order.delivery_at)
      package_suppliers = package_order.order_suppliers

      delivery_orders << DeliveryOrder.new(potential_order, delivery_date, package_suppliers)
    end
    delivery_orders.uniq(&:delivery_at)
  end

private

  attr_reader :team_order, :week_starting, :delivery_orders

  def previous_week_package_orders
    week_count = WEEKS_THRESHOLD
    previous_week_orders = []
    while previous_week_orders.blank? && week_count != 0
      previous_delivery_week = (week_starting - week_count.weeks)
      if package_orders_for(previous_delivery_week).count >= last_week_orders.count
        previous_week_orders = package_orders_for(previous_delivery_week)
      end
      week_count -= 1
    end
    previous_week_orders
  end

  def package_orders_for(delivery_week)
    @_week_orders ||= {}
    return @_week_orders[delivery_week] if @_week_orders[delivery_week].present?

    @_week_orders[delivery_week] = package_orders.where(delivery_at: [delivery_week.beginning_of_week..delivery_week.end_of_week]).order(delivery_at: :asc)
  end

  def last_week_orders
    @_last_week_orders ||= package_orders_for(last_delivery_week)
  end

  def next_delivery_date_for(delivery_at)
    required_delivery_day = delivery_at.to_s(:short_weekday).downcase

    current_delivery_at = week_starting
    current_delivery_day = current_delivery_at.to_s(:short_weekday).downcase

    while current_delivery_day != required_delivery_day
      current_delivery_at += 1.day
      current_delivery_day = current_delivery_at.to_s(:short_weekday).downcase
    end
    current_delivery_at.change(hour: delivery_at.hour, min: delivery_at.min, sec: delivery_at.sec)
  end

  def last_delivery_week
    @_last_delivery_week ||= week_starting - 1.week
  end

  def last_package_order
    @_last_package_orders ||= package_orders.last
  end

  def package_orders
    @_package_orders ||= TeamOrders::ListPackageOrders.new(team_order: team_order).call
  end

  def next_recurring_week
    (last_package_order.delivery_at + 1.week).beginning_of_week
  end

end
