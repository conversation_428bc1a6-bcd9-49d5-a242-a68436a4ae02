class TeamOrders::ExtendOrder

  ORDER_FIELDS = %i[name order_variant credit_card_id whodunnit_id].freeze
  DELIVERY_FIELDS = %i[delivery_address delivery_suburb_id delivery_address_level delivery_instruction].freeze
  CONTACT_FIELDS = %i[number_of_people contact_name company_name phone cpo_id department_identity].freeze

  TEAM_ORDER_FIELDS = (ORDER_FIELDS + DELIVERY_FIELDS + CONTACT_FIELDS).freeze
  TEAM_ORDER_DETAIL_FIELDS = %i[budget hide_budget cutoff_option].freeze

  def initialize(team_order:, extension_week:)
    @team_order = team_order
    @extension_week = extension_week
    @result = Result.new(team_order: team_order)
  end

  def call
    potential_future_delivery_orders.each do |potential_package_order|
      package_order = potential_package_order.team_order
      order_creator = TeamOrders::Create.new(team_order_params: package_order_params_for(potential_package_order), team_admin: package_order.customer_profile, package_id: package_order.package_id).call
      if order_creator.present?
        result.extended_orders << order_creator.team_order
      else
        result.errors += order_creator.errors
      end
    end
    result
  end

private

  attr_reader :team_order, :extension_week, :result

  def potential_future_delivery_orders
    @_potential_future_delivery_orders ||= TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: team_order, week_starting: extension_week).call
  end

  def package_id
    @_package_id ||= team_order.package_id
  end

  def package_order_params_for(potential_package_order)
    params = [team_order_params_for(potential_package_order), delivery_date_params_for(potential_package_order)].inject(&:merge)
    params[:team_order_detail_attributes] = team_order_detail_params_for(potential_package_order)
    params[:delivery_suppliers] = delivery_supplier_params_for(potential_package_order)
    params
  end

  def team_order_params_for(potential_package_order)
    potential_package_order.team_order.attributes.symbolize_keys.slice(*TEAM_ORDER_FIELDS)
  end

  def team_order_detail_params_for(potential_package_order)
    potential_package_order.team_order.team_order_detail.attributes.symbolize_keys.slice(*TEAM_ORDER_DETAIL_FIELDS)
  end

  def delivery_date_params_for(potential_package_order)
    {
      delivery_at: potential_package_order.delivery_at
    }
  end

  def delivery_supplier_params_for(potential_package_order)
    supplier_config = potential_package_order.order_suppliers.map do |order_supplier|
      [order_supplier.supplier_profile_id.to_s, order_supplier.selected_menu_sections]
    end
    supplier_params = {}
    supplier_params[potential_package_order.delivery_at.to_s(:date_spreadsheet)] = supplier_config.to_h
    supplier_params
  end

  class Result
    attr_reader :team_order
    attr_accessor :extended_orders, :errors

    def initialize(team_order:)
      @team_order = team_order
      @extended_orders = []
      @errors = []
    end

    def success?
      errors.blank? && extended_orders.present? && extended_orders.all?{|extended_order| extended_order.package_id == team_order.package_id }
    end
  end

end
