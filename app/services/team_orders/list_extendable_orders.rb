class TeamOrders::ListExtendableOrders

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new(time: time)
  end

  def call
    fetch_extendable_orders
    result
  end

private

  attr_reader :time, :result

  def fetch_extendable_orders
    grouped_recurring_team_orders.each do |_, package_orders|
      next if has_all_cancelled_orders?(package_orders)

      package_order = package_orders.last
      if package_order.delivery_at < result.extension_week.beginning_of_week
        result.extendable_team_orders << package_order
      end
    end
  end

  def grouped_recurring_team_orders
    return @_recent_recurring_team_orders if !@_recent_recurring_team_orders.nil?

    recurring_team_orders = Order.where(order_variant: 'recurring_team_order')
    recurring_team_orders = recurring_team_orders.where('delivery_at > ?', time.beginning_of_week)
    recurring_team_orders = recurring_team_orders.order(:delivery_at)
    recurring_team_orders.group_by(&:package_id)
  end

  def has_all_cancelled_orders?(package_orders)
    last_week_orders = package_orders.select do |team_order|
      team_order.delivery_at >= result.extension_week - 1.week
    end
    last_week_order_statuses = last_week_orders.map(&:status).uniq
    last_week_orders.blank? || (last_week_order_statuses.size == 1 && %w[voided cancelled].include?(last_week_order_statuses.first))
  end

  class Result
    attr_accessor :extendable_team_orders
    attr_reader :time, :extension_week

    def initialize(time:)
      @time = time
      @extendable_team_orders = []
      @extension_week = (time + TeamOrder::REMINDER_THRESHOLD).beginning_of_week
    end
  end

end
