class TeamOrders::Submit

  def initialize(team_order:)
    @team_order = team_order
    @result = Result.new(team_order: @team_order)
  end

  def call
    if can_submit?
      remove_unpaid_order_lines if team_order.attendee_pays?
      team_order.update(status: 'new')
      send_supplier_new_team_order_emails
    end
    send_team_order_admin_submission_email if team_order.status == 'new' || (team_order.cutoff_option == 'cancel_order' && team_order.status == 'cancelled')
    result
  end

private

  attr_reader :team_order, :result

  def can_submit?
    case
    when team_order.status != 'pending'
      result.errors << "Cannot submit a #{team_order.status} order"
    when !is_past_cutoff?
      result.errors << 'Order is not past its cutoff time'
    when team_order.is_recurring_team_order? && confirmed_order_lines.blank?
      result.errors << 'Order is skipped as it does not have confirmed order lines'
      team_order.update(status: 'skipped') # skips a recurring team order past cutoff with no order lines
      notify_admin_about_skip
    when confirmed_order_lines.blank?
      result.errors << 'Order does not have any confirmed order lines'
    when can_proceed_with_cutoff_options?
      # check if can proceed
    end
    result.errors.blank?
  end

  def is_past_cutoff?
    lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call
    lead_time_fetcher.past_lead_time?
  end

  def confirmed_order_lines
    approve_anonymous_attendees
    lister_options = {
      order: team_order,
      confirmed_attendees_only: true,
      paid_only: team_order.attendee_pays,
    }
    @_confirmed_order_lines ||= OrderLines::List.new(options: lister_options).call
  end

  def can_proceed_with_cutoff_options?
    return true if team_order.cutoff_option.blank? || !order_supplier_spends.is_under?

    case team_order.cutoff_option
    when 'cancel_order'
      order_canceller = TeamOrders::Cancel.new(team_order: team_order, team_admin: team_order.customer_profile, notify_attendees: false).call
      if order_canceller.success?
        result.errors << 'Order is cancelled as it did not meet supplier minimums'
      else
        result.errors += order_canceller.errors
      end
    when 'charge_to_minimum'
      Orders::ChargeToSupplierMinimums.new(order: team_order, supplier_spends: order_supplier_spends.supplier_spends).call
      Orders::CalculateCustomerTotals.new(order: team_order.reload, save_totals: true).call
    end
  end

  def order_supplier_spends
    @_order_supplier_spends ||= Orders::GetSupplierSpends.new(order: team_order, exclude_surcharge: true).call
  end

  def approve_anonymous_attendees
    anonymous_attendees = team_order.team_order_attendees.where(anonymous: true)
    anonymous_attendees.each do |attendee|
      TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: attendee, anonymous_status: 'auto_approve', team_admin: team_order.customer_profile).call
    end
  end

  def remove_unpaid_order_lines
    unpaid_orderlines = team_order.order_lines.where.not(payment_status: 'paid')
    unpaid_orderlines.each(&:delete)
  end

  def send_team_order_admin_submission_email
    TeamOrders::Emails::SendAdminOrderSubmissionEmail.new(team_order: team_order).delay(queue: :notifications).call
  end

  def send_supplier_new_team_order_emails
    team_order.supplier_profiles.each do |supplier|
      Suppliers::Emails::SendNewTeamOrderEmail.new(supplier: supplier, team_order: team_order).delay(queue: :notifications).call
    end
    team_order.update(suppliers_notified_at: Time.zone.now)
  end

  def notify_admin_about_skip
    return if Rails.env.test?

    message = ":alarm_clock: Recurring Team Order ##{team_order.id} was skipped because of no confirmed order lines"
    SlackNotifier.send(message)
  end

  class Result
    attr_accessor :team_order, :errors

    def initialize(team_order:)
      @team_order = team_order
      @errors = []
    end

    def success?
      errors.blank? && team_order.status == 'new'
    end
  end
end
