class TeamOrders::<PERSON>Extend

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new
  end

  def call
    extendable_recurring_team_orders.each do |team_order|
      extend_team_order(team_order)
    end
    result
  end

private

  attr_reader :time, :result

  def extension_order_lister
    @_extension_order_lister ||= TeamOrders::ListExtendableOrders.new(time: time).call
  end

  def extendable_recurring_team_orders
    @_extendable_recurring_team_orders ||= extension_order_lister.extendable_team_orders
  end

  def extend_team_order(team_order)
    order_extender = TeamOrders::ExtendOrder.new(team_order: team_order, extension_week: extension_order_lister.extension_week).call
    if order_extender.success?
      result.extended_orders += order_extender.extended_orders
    else
      result.errors += order_extender.errors
    end
  end

  class Result
    attr_accessor :extended_orders, :errors

    def initialize
      @extended_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
