class TeamOrders::Create

  PO_FIELDS = %i[po_number cpo_id].freeze
  DELIVERY_FIELDS = %i[delivery_dates delivery_suppliers].freeze
  ESCAPABLE_TEAM_ORDER_FIELDS = %i[supplier_ids attendee_ids team_order_detail_attributes selected_menu_sections].freeze

  def initialize(team_order_params:, team_admin:, package_id: nil)
    @team_order_params = team_order_params
    @team_admin = team_admin
    @package_id = package_id
    @result = Result.new
  end

  def call
    @team_order = Order.new(sanitized_params)
    result.team_order = team_order
    if team_order.save
      create_details
      attach_suppliers
      attach_attendees
      send_team_admin_new_order_email
      log_event
    else
      result.errors += team_order.errors.full_messages
    end
    result
  end

private

  attr_reader :team_order_params, :team_admin, :team_order, :package_id, :result

  def sanitized_params
    [
      default_submission_params,
      purchase_order_params,
      team_order_params.except(*PO_FIELDS, *DELIVERY_FIELDS, *ESCAPABLE_TEAM_ORDER_FIELDS)
    ].inject(&:merge)
  end

  def default_submission_params
    {
      order_type: 'one-off',
      order_variant: 'team_order',
      status: 'pending',
      customer_profile: team_admin,
      credit_card_id: 1,
      unique_event_id: SecureRandom.hex(8),
      uuid: SecureRandom.uuid
    }
  end

  def purchase_order_params
    return {} if customer_purchase_order.blank?

    {
      customer_purchase_order: customer_purchase_order
    }
  end

  def customer_purchase_order
    @_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: team_admin, cpo_id: team_order_params[:cpo_id]).call
  end

  def create_details
    detail_params = team_order_params[:team_order_detail_attributes] || {}
    detail_params = detail_params.merge({ package_id: package_id }) if package_id.present?
    TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: detail_params).call
  end

  def attach_suppliers
    delivery_suppliers = team_order_params[:delivery_suppliers].present? && team_order_params[:delivery_suppliers][team_order.delivery_at.to_s(:date_spreadsheet)]
    return if delivery_suppliers.blank?

    TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: delivery_suppliers).call
  end

  def attach_attendees
    return if team_order_params[:attendee_ids].blank?

    event_attendees = team_admin.event_attendees.where(id: team_order_params[:attendee_ids])
    return if event_attendees.blank?

    event_attendees.each do |event_attendee|
      TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee, notify_attendee: !team_order.is_package_order?).call
    end
  end

  def send_team_admin_new_order_email
    return if team_order.is_package_order?

    TeamOrders::Emails::SendAdminNewOrderEmail.new(team_order: team_order).delay(queue: :notifications).call
  end

  def log_event
    EventLogs::Create.new(event_object: team_order, event: 'new-team-order-created').delay(queue: :notifications).call
  end

  class Result
    attr_accessor :team_order, :errors

    def initialize
      @team_order = nil
      @errors = []
    end

    def success?
      errors.blank? && team_order.present? && team_order.persisted?
    end
  end
end
