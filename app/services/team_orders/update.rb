class TeamOrders::Update

  def initialize(team_order:, team_order_params:, team_admin:)
    @team_order = team_order
    @team_order_params = team_order_params
    @team_admin = team_admin
    @result = Result.new(team_order: team_order)
  end

  def call
    if can_update?
      capture_linked_team_orders
      if team_order.update(sanitized_params)
        update_attendees if !team_order.is_recurring_team_order?
        update_suppliers
        update_details
        recalculate_totals
        update_linked_team_orders if linked_orders.present?
      else
        result.errors += team_order.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :team_order_params, :team_admin, :team_order, :linked_orders, :result

  def can_update?
    case
    when team_order.blank?
      result.errors << 'Cannot update a missing team order'
    when !team_order.is_team_order?
      result.errors << 'This order is not a team order'
    when team_order.customer_profile != team_admin
      result.errors << 'You don\'t have access to this team order'
    # when # team order attendees have already ordered
    end
    result.errors.blank?
  end

  def sanitized_params
    params = {}
    params = params.merge(purchase_order_params)
    params = params.merge(team_order_params.except(:po_number, :cpo_id, :supplier_ids, :attendee_ids, :team_order_detail_attributes, :selected_menu_sections, :delivery_dates, :delivery_suppliers, :mode))
    params
  end

  def purchase_order_params
    return {} if customer_purchase_order.blank?

    {
      customer_purchase_order: customer_purchase_order
    }
  end

  def customer_purchase_order
    @_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: team_admin, cpo_id: team_order_params[:cpo_id]).call
  end

  def update_details
    TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: team_order_params[:team_order_detail_attributes]).call
  end

  def can_notify_attendees?
    @_can_notify_attendees ||= !team_order.is_package_order? || team_order_params[:mode].blank? || %w[linked update-linked].exclude?(team_order_params[:mode])
  end

  def update_attendees
    @newly_attached_attendees = @removed_team_order_attendees = []
    event_attendees = team_admin.event_attendees.where(id: team_order_params[:attendee_ids])
    @removed_team_order_attendees = team_order_attendees.select{|team_order_attendee| event_attendees.exclude?(team_order_attendee.event_attendee) }
    @removed_team_order_attendees.each do |team_order_attendee|
      TeamOrderAttendees::Detach.new(team_order_attendee: team_order_attendee, team_admin: team_admin, notify_attendee: true).call
    end
    new_event_attendees = event_attendees.where.not(id: team_order_attendees.map(&:event_attendee_id))
    new_event_attendees.each do |event_attendee|
      attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee, notify_attendee: can_notify_attendees?).call
      if attendee_attacher.success?
        @newly_attached_attendees << attendee_attacher.team_order_attendee
      end
    end
  end

  def update_suppliers
    delivery_suppliers = team_order_params[:delivery_suppliers].present? && team_order_params[:delivery_suppliers][team_order.delivery_at.to_s(:date_spreadsheet)]
    return if delivery_suppliers.blank?

    TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: delivery_suppliers).call
    team_order.reload
  end

  def team_order_attendees
    @_team_order_attendees ||= team_order.team_order_attendees.includes(:event_attendee)
  end

  def capture_linked_team_orders
    if team_order.is_package_order? && team_order_params[:mode].present? && team_order_params[:mode] == 'linked'
      lister_options = { active_only: true, future_only: true, exclude_self: true }
      @linked_orders = TeamOrders::ListPackageOrders.new(team_order: team_order, options: lister_options).call
    else
      @linked_orders = nil
    end
  end

  def recalculate_totals
    return if @removed_team_order_attendees.blank? && @newly_attached_attendees.blank?

    Orders::CalculateCustomerTotals.new(order: team_order, save_totals: true).call
  end

  def update_linked_team_orders
    linked_orders.each do |linked_order|
      linked_order_params = team_order_params.except(:delivery_suppliers, :delivery_at)
      linked_order_params[:mode] = 'update-linked'
      TeamOrders::Update.new(team_order: linked_order, team_order_params: linked_order_params, team_admin: team_admin).call
    end
    notifiable_package_attendees = @newly_attached_attendees || []
    notifiable_package_orders = linked_orders + [team_order]
    notifiable_package_attendees.each do |package_order_attendee|
      TeamOrderAttendees::Emails::SendPackageInviteEmail.new(package_order_attendee: package_order_attendee, package_orders: notifiable_package_orders).delay(queue: :notifications).call
    end
  end

  class Result
    attr_accessor :team_order, :errors

    def initialize(team_order:)
      @team_order = team_order
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
