class TeamOrders::Emails::SendAdminOrderSubmissionEmail < Notifications::WithPreference
  include Rails.application.routes.url_helpers
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'customer-team_order_submission'.freeze

  def initialize(team_order:)
    @team_order = team_order
    @notifying_account = @team_admin = team_order&.customer_profile
    @template_name = EMAIL_TEMPLATE
  end

  def call
    return if team_order.blank? || !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send team order submission email to customer #{team_admin.id} - ##{team_order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { team_order_id: team_order.id, customer_id: team_admin.id })
    end
  end

private

  attr_reader :team_admin, :team_order

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Team order submission email sent to customer #{team_admin.id} - ##{team_order.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: team_admin.email_recipient)
  end

  def email_subject
    order_status = team_order.status == 'cancelled' ? 'Cancelled' : 'Submitted'
    subject = "Yordar: Team Order #{order_status} - ##{team_order.id}"
    subject += ' - Top-Up Added' if team_order.status != 'cancelled' && supplier_surcharge.present?
    subject
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: team_admin.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,

      team_order: deep_struct(team_order_data),
      order_spend: (order_spend > 0 ? number_to_currency(order_spend, precission: 2) : nil),
      minimum_spend: (minimum_spend > 0 ? number_to_currency(minimum_spend, precission: 2) : nil),
      supplier_surcharge: supplier_surcharge,
      account_managers: deep_struct(account_manager_data_for(team_admin)),

      header_color: :pink,
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      link: team_order_url(team_order.id, host: app_host),
      cutoff_option: team_order.cutoff_option,
      status: team_order.status,
      budget: number_to_currency(team_order.team_order_budget),
      hide_budget: team_order.hide_budget,
      delivery_at: team_order.delivery_at.to_s(:full),
      delivery_address: team_order.delivery_address_arr.join(',<br/>'),
      supplier: supplier_data,
    }
  end

  def supplier_data
    supplier = team_order.team_supplier_profiles.first
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def order_supplier_spends
    @_order_supplier_spends ||= Orders::GetSupplierSpends.new(order: team_order, exclude_surcharge: true).call
  end

  def order_spend
    @_order_spend ||= order_supplier_spends.total_spend.presence || 0
  end

  def minimum_spend
    @_minimum_spend ||= order_supplier_spends.minimum_spend.presence || 0
  end

  def supplier_surcharge
    return nil if team_order.cutoff_option.blank? || team_order.cutoff_option != 'charge_to_minimum' || team_order.status != 'new'

    surcharge = team_order.order_suppliers.map(&:surcharge).compact.sum
    surcharge > 0 ? number_to_currency(surcharge, precission: 2) : nil
  end

  def email_salutation
    super(default: team_admin.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{team_order.version_ref}"
  end

end

