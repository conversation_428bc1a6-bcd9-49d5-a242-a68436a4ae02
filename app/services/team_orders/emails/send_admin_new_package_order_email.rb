class TeamOrders::Emails::SendAdminNewPackageOrderEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'customer-new_team_order_package'.freeze

  def initialize(package_orders:, is_extension: false)
    @package_orders = package_orders
    @team_admin = package_order.customer_profile
    @is_extension = is_extension
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
  end

  def call
    return if !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send new team order package email to customer #{team_admin.id} - ##{package_id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { package_id: package_id, customer_id: team_admin.id })
    end
  end

private

  attr_reader :team_admin, :package_orders, :is_extension

  def package_id
    @_package_id ||= package_order.package_id
  end

  def package_order
    @_package_order ||= package_orders.first
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "New team order package email sent to customer #{team_admin.id} - ##{package_id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: team_admin.email_recipient)
  end

  def email_subject
    order_status = is_extension ? 'extending the' : 'creating a new'
    "Yordar: Thank you for #{order_status} Team Order Package"
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: team_admin.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      is_extension: is_extension,

      package_order: deep_struct(package_order_data),
      package_orders: deep_struct(package_orders_details),
      account_managers: deep_struct(account_manager_data_for(team_admin)),

      header_color: :pink,
    }
  end

  def package_order_data
    attendee_count = [package_order.number_of_people, package_order.team_order_attendees.where(status: 'invited').count].compact.max
    {
      id: package_order.id,
      name: package_order.name,
      delivery_address: package_order.delivery_address_arr.join(',<br>'),
      number_of_attendees: attendee_count,
      budget: number_to_currency(package_order.team_order_budget, precision: 2),
      hide_budget: package_order.hide_budget,
      cutoff_option: package_order.cutoff_option,
      package_link: url_helper.team_order_package_url(package_order, host: app_host),
      package_invite_link: url_helper.new_team_order_package_attendee_invite_url(package_id: package_order.package_id, host: app_host)
    }
  end

  def package_orders_details
    package_orders.map do |team_order|
      team_order_minimum_spend = minimum_spend(team_order)
      {
        order_path: url_helper.team_order_url(team_order, host: app_host),
        # admin_order_path: url_helper.next_app_team_order_attendee_order_url(code: team_order.unique_event_id, host: next_app_host, tld_length: 2),
        delivery_at: team_order.delivery_at.to_s(:full),
        minimum_spend: (team_order_minimum_spend > 0 ? number_to_currency(team_order_minimum_spend, precission: 2) : nil),
        cutoff_datetime: cutoff_datetime_for(team_order).to_s(:full),
        supplier: supplier_data_for(team_order),
      }
    end
  end

  def supplier_data_for(team_order)
    supplier = team_order.team_supplier_profiles.first
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def cutoff_datetime_for(team_order)
    Orders::FetchLeadTime.new(order: team_order).call.lead_time
  end

  def minimum_spend(team_order)
    order_supplier_spends = Orders::GetSupplierSpends.new(order: team_order).call
    @_minimum_spend ||= order_supplier_spends.minimum_spend.presence || 0
  end

  def email_salutation
    super(default: team_admin.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{package_id}-#{package_order.version_ref}"
  end

end

