class TeamOrders::Emails::SendAdminRegistrationEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'team-order-admin-registration'.freeze

  def initialize(team_order:, team_admin:)
    @team_order = team_order
    @team_admin = team_admin
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send new team admin registration email to customer #{team_admin.id} - ##{team_order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { team_order_id: team_order.id, customer_id: team_admin.id })
    end
  end

private

  attr_reader :team_admin, :team_order

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent team admin registration email to #{team_admin.id} - ##{team_order.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    team_admin.email_recipient
  end

  def email_subject
    "Yordar: Your registration to team order -  #{team_order.name} (##{team_order.id})"
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: team_admin.id,
      ref: email_ref,
    }
  end

  def email_variables
    order_data = team_order.is_package_order? ? team_order_package_data : team_order_data
    {
      firstname: team_admin.email_salutation,
      profile_url: url_helper.customer_profile_url(host: app_host),

      team_order: deep_struct(order_data),
      account_managers: deep_struct(account_manager_data_for(team_admin)),

      header_color: :purple,
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      link: url_helper.team_order_url(team_order, host: app_host),

      is_package_order: false,
    }
  end

  def team_order_package_data
    {
      id: team_order.id,
      name: team_order.name,
      link: url_helper.team_order_package_url(team_order, scoped_to: 'recent_week', host: app_host),

      is_package_order: true,
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{team_admin.id}"
  end

end

