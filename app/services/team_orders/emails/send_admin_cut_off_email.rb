class TeamOrders::Emails::SendAdminCutOffEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'team-order-admin-cutoff_notification'.freeze
  REMAINING_TIME_LABEL = {
    '4hr' => '4 hours',
    '2hr' => '2 hours',
    '30m' =>  '30 minutes'
  }.freeze

  def initialize(team_order:, cutoff_time:)
    @team_order = team_order
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
    @notification_variation = @cutoff_time = cutoff_time
  end

  def call
    return if !can_notify? || !preferred_notification?

    begin
      send_email
    rescue => exception
      Rails.logger.error "Failed to send team order #{cutoff_time} cutoff reminder to team admin - #{team_admin.id}, #{team_admin.user.firstname}"
      Rails.logger.error exception.inspect
      Rails.logger.error exception.backtrace.join('\n')
    end
  end

private

  attr_reader :team_order, :cutoff_time

  def can_notify?
    team_order.present? && team_order_detail.present? && team_order_detail.respond_to?(notification_attribute) && team_order_detail.send(notification_attribute).blank?
  end

  def team_order_detail
    @_team_order_detail = team_order.team_order_detail
  end

  def team_admin
    @_team_admin ||= team_order.present? && team_order.customer_profile
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: team_admin.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      mark_admin_notified
      Rails.logger.info "Team order #{cutoff_time} cutoff email sent to team order admin - order ##{team_order.id} - #{team_admin.id}, #{team_admin.user.firstname}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "Yordar: #{remaining_time} remaining until your team order cut-off"
  end

  def email_options
    {
      fk_id: team_admin.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      admin_name: team_admin.user.firstname,
      team_order: deep_struct(team_order_data),

      remaining_time: remaining_time,
      remaining_amount: (remaining_amount > 0 ? number_to_currency(remaining_amount, precission: 2) : nil),

      anonymous_attendee_count: anonymous_attendee_count,
      admin_order_url: url_helper.next_app_team_order_attendee_order_url(code: team_order.unique_event_id, host: next_app_host, tld_length: 2),
      account_managers: deep_struct(account_manager_data_for(team_admin)),

      header_color: :purple,
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      link: url_helper.team_order_url(team_order.id, host: app_host),
      cutoff_option: team_order.cutoff_option,
      delivery_at: team_order.delivery_at.to_s(:full),
      budget: team_order.team_order_budget,
      hide_budget: team_order.hide_budget,
      delivery_address: team_order.delivery_address_arr.join(',<br/>'),
      total: order_total > 0 ? number_to_currency(order_total, precission: 2) : '$0',
      supplier: supplier_data,
    }
  end

  def supplier_data
    supplier = team_order.team_supplier_profiles.first
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def order_supplier_spends
    @_order_supplier_spends ||= Orders::GetSupplierSpends.new(order: team_order).call
  end

  def order_total
    @_order_total ||= order_supplier_spends.total_spend.presence || 0
  end

  def remaining_amount
    @_remaining_amount ||= order_supplier_spends.remaining_spend.presence || 0
  end

  def anonymous_attendee_count
    team_order.team_order_attendees.where(anonymous: true, status: %w[invited pending ordered]).count
  end

  def remaining_time
    REMAINING_TIME_LABEL[cutoff_time]
  end

  def notification_attribute
    "cutoff_#{cutoff_time}_reminder"
  end

  def mark_admin_notified
    team_order.team_order_detail.update(Hash[notification_attribute.to_sym, Time.zone.now])
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{cutoff_time}"
  end

end
