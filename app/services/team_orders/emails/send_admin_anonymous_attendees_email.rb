class TeamOrders::Emails::SendAdminAnonymousAttendeesEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'team-order-admin-anonymous_attendees_notification'.freeze

  def initialize(team_order:, cutoff_time: '1hr')
    @team_order = team_order
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
    @cutoff_time = cutoff_time
  end

  def call
    return if !preferred_notification? || !can_notify?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send anonymous attendees email sent to team order admin of team order ##{team_order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { team_order_id: team_order.id, customer_id: team_admin.id })
    end
  end

private

  attr_reader :team_order, :cutoff_time

  def can_notify?
    team_order.present? && anonymous_attendees.present? && team_order_detail.present? && team_order_detail.respond_to?(notification_attribute) && team_order_detail.send(notification_attribute).blank?
  end

  def team_order_detail
    @_team_order_detail = team_order.team_order_detail
  end

  def team_admin
    @_team_admin ||= team_order.present? && team_order.customer_profile
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      mark_admin_notified
      Rails.logger.info "Anonymous Attendees email sent to team order admin of team order ##{team_order.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: team_admin.email_recipient)
  end

  def email_subject
    "Yordar: #{anonymous_attendees.size} team order attendee(s) pending approval"
  end

  def email_options
    {
      fk_id: team_admin.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      team_order: deep_struct(team_order_data),
      anonymous_attendees: deep_struct(anonymous_attendees_data),

      remaining_time: remaining_time,

      header_color: :purple
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      link: url_helper.team_order_url(team_order.id, host: app_host),
      delivery_at: team_order.delivery_at.to_s(:full),
      budget: number_to_currency(team_order.team_order_budget, precision: 2),
      delivery_address: team_order.delivery_address_arr.join(',<br/>'),
      supplier: supplier_data,
    }
  end

  def supplier_data
    supplier = team_order.team_supplier_profiles.first
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def anonymous_attendees_data
    anonymous_attendees.map do |attendee|
      {
        name: attendee.name,
        email: attendee.email,
        status: attendee.status,
      }
    end
  end

  def anonymous_attendees
    team_order.team_order_attendees.where(anonymous: true, status: %w[invited pending ordered])
  end

  def remaining_time
    case cutoff_time
    when '1hr'
      '1 hour'
    end
  end

  def notification_attribute
    'anonymous_attendees_reminder'
  end

  def mark_admin_notified
    team_order.team_order_detail.update(Hash[notification_attribute.to_sym, Time.zone.now])
  end

  def email_salutation
    super(default: team_admin.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{cutoff_time}"
  end

end
