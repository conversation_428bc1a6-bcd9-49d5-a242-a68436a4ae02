class TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'team-order-admin-team_order_extension'.freeze

  def initialize(team_order:, extension_week:, final_reminder: false)
    @team_order = team_order
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
    @extension_week = extension_week
    @final_reminder = final_reminder
    @notification_variation = final_reminder ? 'final' : 'initial'
  end

  def call
    return if !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send Recurring Team Order Extension email sent to team order admin of team order ##{team_order.id} - week starting #{extension_week}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { team_order_id: team_order.id, customer_id: team_admin.id, extension_week: extension_week })
    end
  end

private

  attr_reader :team_order, :extension_week, :final_reminder

  def team_admin
    @_team_admin ||= team_order.present? && team_order.customer_profile
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Recurring Team Order Extension email sent to team order admin of team order ##{team_order.id} - week starting #{extension_week}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: team_admin.email_recipient)
  end

  def email_subject
    "Yordar: Recurring Team Order Extention Reminder#{final_reminder ? ' - Final Reminder' : ''}"
  end

  def email_cc
    emails = account_managers_data.map do |data|
      data[:email]
    end
    emails << yordar_credentials(:yordar, :orders_email)
    emails.join(';')
  end

  def email_options
    {
      fk_id: team_admin.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      team_order: deep_struct(team_order_data),

      final_reminder: final_reminder,
      extension_week: extension_week.to_s(:date),
      extension_url: url_helper.extend_team_order_url(team_order, host: app_host),
      account_managers: deep_struct(account_managers_data),

      header_color: :purple
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      package_id: team_order.package_id
    }
  end

  def account_managers_data
    @_account_managers_data ||= account_manager_data_for(team_admin)
  end

  def email_salutation
    super(default: team_admin.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{extension_week}"
  end

end
