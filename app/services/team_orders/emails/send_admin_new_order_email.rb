class TeamOrders::Emails::SendAdminNewOrderEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'customer-new_team_order'.freeze

  def initialize(team_order:)
    @team_order = team_order
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
    @team_admin = team_order.customer_profile
  end

  def call
    return if !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send new team order email to customer #{team_admin.id} - ##{team_order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { team_order_id: team_order.id, customer_id: team_admin.id })
    end
  end

private

  attr_reader :team_admin, :team_order

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "New team order email sent to customer #{team_admin.id} - ##{team_order.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: team_admin.email_recipient)
  end

  def email_subject
    "Yordar: Thank you for creating a new Team Order - ##{team_order.id}"
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: team_admin.id,
      ref: email_ref,
    }
  end

  def email_variables
    cutoff_datetime = Orders::FetchLeadTime.new(order: team_order).call.lead_time
    {
      firstname: email_salutation,

      team_order: deep_struct(team_order_data),
      cutoff_datetime: cutoff_datetime,
      minimum_spend: (minimum_spend > 0 ? number_to_currency(minimum_spend, precission: 2) : nil),
      account_managers: deep_struct(account_manager_data_for(team_admin)),

      header_color: :pink,
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      link: url_helper.team_order_url(team_order.id, host: app_host),
      cutoff_option: team_order.cutoff_option,
      status: team_order.status,
      delivery_at: team_order.delivery_at.to_s(:full),
      delivery_address: team_order.delivery_address_arr.join(',<br>'),
      admin_order_link: url_helper.next_app_team_order_attendee_order_url(code: team_order.unique_event_id, host: next_app_host, tld_length: 2),
      invite_link: url_helper.new_team_order_attendee_invite_url(event_id: team_order.unique_event_id, host: app_host),
      number_of_attendees: team_order.team_order_attendees.where(status: 'invited').count,
      budget: number_to_currency(team_order.team_order_budget, precission: 2),
      hide_budget: team_order.hide_budget,
      supplier: supplier_data,
    }
  end

  def supplier_data
    supplier = team_order.team_supplier_profiles.first
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def order_supplier_spends
    @_order_supplier_spends ||= Orders::GetSupplierSpends.new(order: team_order).call
  end

  def minimum_spend
    @_minimum_spend ||= order_supplier_spends.minimum_spend.presence || 0
  end

  def email_salutation
    super(default: team_admin.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{team_order.version_ref}"
  end

end

