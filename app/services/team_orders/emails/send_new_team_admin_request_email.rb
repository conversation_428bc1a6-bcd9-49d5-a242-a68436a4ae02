class TeamOrders::Emails::SendNewTeamAdminRequestEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-team_admin_request'.freeze

  def initialize(customer:)
    @customer = customer
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to sent customer team admin request email to admin #{customer.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "New customer team admin request email sent to admin #{customer.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :admin_email)
  end

  def email_subject
    'Yordar: Customer request to become team admin'
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      customer: deep_struct(customer_data),

      header_color: :black
    }
  end

  def customer_data
    {
      id: customer.id,
      name: customer.name,
      email: customer.user.email,
      company_name: customer.company_name,
      admin_url: customer_admin_link, # edit_url
    }
  end

  def customer_admin_link
    url_helper.customers_admin_url(customer_name: customer.name, host: app_host)
  end

  def email_ref
    EMAIL_TEMPLATE
  end

end

