class MealPlans::<PERSON><PERSON>

  def initialize(customer:, meal_plan_params: {})
    @customer = customer
    @meal_plan_params = meal_plan_params
    @meal_plan = new_meal_plan
    @result = Result.new
  end

  def call
    return result if !can_upsert?

    if meal_plan.update(sanitized_params)
      result.meal_plan = meal_plan
    else
      result.errors += meal_plan.errors.full_messages
    end

    result
  end

private

  attr_reader :customer, :meal_plan_params, :meal_plan, :result

  def can_upsert?
    case
    when customer.blank?
      result.errors << 'Cannot create meal plan without a customer'
    when customer.requires_po && customer_purchase_order.blank?
      result.errors << 'Your orders require a Purchase Order'
    when customer.requires_department_identity && meal_plan_params[:department_identity].blank?
      result.errors << 'Your orders require a Cost Centre ID'
    end
    result.errors.blank?
  end

  def sanitized_params
    [
      default_creation_params,
      purchase_order_params,
      meal_plan_params.except(:uuid, :cpo_id, :gst_free_cpo_id)
    ].inject(&:merge)
  end

  def default_creation_params
    return {} if meal_plan.persisted?

    {
      uuid: SecureRandom.uuid
    }
  end

  def purchase_order_params
    params = {}
    params[:customer_purchase_order] = customer_purchase_order if customer_purchase_order.present?
    params[:gst_free_customer_purchase_order] = gst_free_customer_purchase_order if gst_free_customer_purchase_order.present?
    params
  end

  def customer_purchase_order
    @_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: meal_plan_params[:cpo_id]).call
  end

  def gst_free_customer_purchase_order
    @_gst_free_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: meal_plan_params[:gst_free_cpo_id]).call
  end

  def new_meal_plan
    return nil if customer.blank?

    customer.meal_plans.build
  end

  class Result
    attr_accessor :meal_plan, :errors

    def initialize
      @meal_plan = nil
      @errors = []
    end

    def success?
      errors.blank? && meal_plan.present? && meal_plan.persisted?
    end
  end

end
