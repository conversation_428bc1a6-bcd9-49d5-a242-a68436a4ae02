class MealPlans::AttachToOrder

  ORDER_FIELDS = %i[
    name
    number_of_people
  ].freeze

  DELIVERY_FIELDS = %i[
    delivery_address_level
    delivery_address
    delivery_suburb
    delivery_instruction
  ].freeze

  BILLING_FIELDS = %i[
    cpo_id
    gst_free_cpo_id
    department_identity
    credit_card_id
  ].freeze

  def initialize(meal_plan:, order:, order_params: {})
    @meal_plan = meal_plan
    @order = order
    @order_params = order_params
    @result = Result.new(order: order)
  end

  def call
    return result if !can_attach?

    update_order_with_meal_plan_params

    result
  end

private

  attr_reader :meal_plan, :order, :order_params, :result

  def can_attach?
    case
    when order.blank?
      result.errors << 'Cannot attach meal plan without an order'
    when meal_plan.blank?
      result.errors << 'Cannot attach a missing meal plan'
    when order.customer_profile.present? && order.customer_profile != meal_plan.customer_profile
      result.errors << 'You do not have access to this meal plan'
    end
    result.errors.blank?
  end

  def update_order_with_meal_plan_params
    order_updater = Orders::Update.new(order: order, order_params: sanitized_params).call
    if order_updater.success?
      result.order = order_updater.order
    else
      result.errors += order_updater.errors
    end
  end

  def sanitized_params
    [
      meal_plan_params,
      meal_plan_order_details,
      meal_plan_delivery_details,
      meal_plan_billing_details,
      purchase_order_params,
      order_delivery_details
    ].inject(&:merge)
  end

  def meal_plan_params
    {
      meal_plan: meal_plan
    }
  end

  def meal_plan_order_details
    ORDER_FIELDS.map do |field|
      [field, meal_plan.send(field)]
    end.to_h
  end

  def meal_plan_delivery_details
    DELIVERY_FIELDS.map do |field|
      [field, meal_plan.send(field)]
    end.to_h
  end

  def meal_plan_billing_details
    BILLING_FIELDS.map do |field|
      [field, meal_plan.send(field)]
    end.to_h
  end

  def purchase_order_params
    params = {}
    params[:customer_purchase_order] = customer_purchase_order if customer_purchase_order.present?
    params[:gst_free_customer_purchase_order] = gst_free_customer_purchase_order if gst_free_customer_purchase_order.present?
    params
  end

  def customer_purchase_order
    return nil if meal_plan.cpo_id.blank?

    @_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: order.customer_profile, cpo_id: meal_plan.cpo_id).call
  end

  def gst_free_customer_purchase_order
    return nil if meal_plan.gst_free_cpo_id.blank?

    @_gst_free_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: order.customer_profile, cpo_id: meal_plan.gst_free_cpo_id).call
  end

  def order_delivery_details
    return {} if order_params[:delivery_at].blank?

    {
      delivery_at: order_params[:delivery_at]
    }
  end

  class Result
    attr_accessor :order, :errors

    def initialize(order:)
      @order = order
      @errors = []
    end

    def success?
      errors.blank? && order.meal_plan.present?
    end
  end
end