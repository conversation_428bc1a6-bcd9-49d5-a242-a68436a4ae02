class MealPlans::Emails::SendOrderReminderEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-meal_plan_reminder'.freeze

  def initialize(meal_plan:, dates:, frequency:)
    @meal_plan = meal_plan
    @dates = dates
    @frequency = frequency
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send meal plan reminders email for meal plan #{meal_plan&.id} - customer #{customer&.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { meal_plan_id: meal_plan&.id, customer_id: customer&.id, frequency: frequency })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :meal_plan, :dates, :frequency, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: customer.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Send Order reminder for #{meal_plan.id} for frequency #{frequency} - #{dates[:from_date].to_s(:date_verbose)}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "Yordar - Meal Plan `#{meal_plan.name}` orders reminder #{upcoming_order_text}"
  end

  def upcoming_order_text
    case frequency
    when 'weekly'
      "for upcoming week starting #{dates[:from_date].to_s(:date_verbose)}"
    when 'monthly'
      "for upcoming month of #{dates[:from_date].strftime('%B')}"
    end
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: meal_plan.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: customer.email_salutation,
      frequency: frequency,
      reminder_date: dates[:from_date],
      meal_plan: deep_struct(meal_plan_data),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink
    }
  end

  def meal_plan_data
    {
      id: meal_plan.id,
      name: meal_plan.name,
      customer_name: customer.name,
      link_url: url_helper.customer_meal_plans_url(mealUUID: meal_plan.uuid, date: dates[:from_date].to_s(:date_spreadsheet), host: app_host)
    }
  end

  def customer
    @_customer ||= meal_plan.customer_profile
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{meal_plan.id}-#{dates[:from_date].to_s(:date_spreadsheet)}"
  end

end
