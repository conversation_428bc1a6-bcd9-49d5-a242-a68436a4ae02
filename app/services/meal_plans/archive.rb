class MealPlans::Archive

  def initialize(customer:, meal_plan:, remove_params: {})
    @customer = customer
    @meal_plan = meal_plan
    @remove_params = [default_remove_params, remove_params.to_h.symbolize_keys].inject(&:merge)
    @result = Result.new
  end

  def call
    return result if !can_upsert?

    if meal_plan.update(archived_at: Time.zone.now)
      result.meal_plan = meal_plan
      update_subsequent_orders if has_pending_orders?
    else
      result.errors += meal_plan.errors.full_messages
    end

    result
  end

private

  attr_reader :customer, :meal_plan, :remove_params, :result

  def can_upsert?
    case
    when customer.blank?
      result.errors << 'Cannot archive meal plan without a customer'
    when meal_plan.blank?
      result.errors << 'Cannot archive a missing meal plan'
    when meal_plan&.customer_profile != customer
      result.errors << 'You do not have access to this meal plan'
    when meal_plan.archived?
      result.errors << 'Meal plan is already archived'
    when remove_params[:mode] == 'initial' && has_pending_orders?
      result.errors << 'Your have future order that might be affected by this change'
    end
    result.errors.blank?
  end

  def update_subsequent_orders
    case remove_params[:mode]
    when 'initial'
      # do nothing
    when 'one-off'
      result.pending_orders = []
      # do nothing
    else
      MealPlans::UpdateSubsequentOrders.new(meal_plan: meal_plan, mode: 'cancel', order_ids: remove_params[:order_ids]).delay(queue: :data_integrity).call
      result.pending_orders = []
    end
  end

  def has_pending_orders?
    orders = meal_plan.orders.where('delivery_at > ?', Time.zone.now)
    orders = orders.where(status: %w[new amended confirmed pending]).order(delivery_at: :asc, id: :asc)
    result.pending_orders = orders

    orders.present?
  end

  def default_remove_params
    {
      mode: 'initial',
      order_ids: [],
    }
  end

  class Result
    attr_accessor :meal_plan, :pending_orders, :errors

    def initialize
      @meal_plan = nil
      @pending_orders = []
      @errors = []
    end

    def success?
      errors.blank? && pending_orders.blank? && meal_plan.archived?
    end
  end

end
