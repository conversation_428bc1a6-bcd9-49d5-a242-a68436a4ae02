class MealPlans::UpdateSubsequentOrders

  VALID_UPDATE_MODES = %w[update cancel].freeze

  def initialize(meal_plan:, mode:, order_ids: [], since: Time.zone.now)
    @meal_plan = meal_plan
    @mode = mode
    @order_ids = order_ids
    @since = since
    @result = Result.new
  end

  def call
    if can_update?
      subsequent_orders.each do |order|
        case mode
        when 'update'
          update_order(order)
        when 'cancel'
          cancel_order(order)
        end
      end
    end

    result
  end

private

  attr_reader :meal_plan, :mode, :since, :order_ids, :result

  def can_update?
    case
    when meal_plan.blank?
      result.errors << 'Cannot update without a mean plan'
    when mode.blank? || VALID_UPDATE_MODES.exclude?(mode)
      result.errors << 'Cannot update without a valid mode'
    end

    result.errors.blank?
  end

  def update_order(order)
    order_updator = MealPlans::AttachToOrder.new(meal_plan: meal_plan, order: order, order_params: delivery_params_for(order)).call
    if order_updator.success?
      result.orders << order
    end
  end

  def cancel_order(order)
    order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call
    if order_canceller.success?
      result.orders << order
    end
  end

  def subsequent_orders
    orders = meal_plan.orders.where('delivery_at > ?', since)
    orders = orders.where(id: order_ids) if order_ids.present?
    orders
  end

  def delivery_params_for(order)
    return {} if order.delivery_at.to_s(:time_only) == meal_plan.delivery_time.to_s(:time_only)

    {
      delivery_at: order.delivery_at.change(hour: meal_plan.delivery_time.hour, min: meal_plan.delivery_time.min)
    }
  end

  class Result
    attr_accessor :orders, :errors

    def initialize
      @orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end