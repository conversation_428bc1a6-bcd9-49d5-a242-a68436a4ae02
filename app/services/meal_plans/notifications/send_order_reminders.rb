class MealPlans::Notifications::SendOrderReminders

  CURRENT_ORDER_STATUSES = %w[new amended quoted confirmed delivered].freeze
  FUTURE_ORDER_STATUSES = %w[new amended quoted confirmed delivered cancelled skipped].freeze

  def initialize(time:, frequency:)
    @time = time
    @frequency = frequency
    @result = Result.new
  end

  def call
    frequency_meal_plans.each do |meal_plan|
      if can_notify_for?(meal_plan)
        send_reminder_for(meal_plan)
      end
    end
    result
  end

private

  attr_reader :time, :frequency, :result

  def frequency_meal_plans
    meal_plans = MealPlan.where(archived_at: nil)
    meal_plans = meal_plans.where(reminder_frequency: frequency)
    meal_plans.includes(:orders, :customer_profile)
  end

  def can_notify_for?(meal_plan)
    has_past_orders_for?(meal_plan) && !has_future_orders_for?(meal_plan)
  end

  def send_reminder_for(meal_plan)
    reminder_dates = order_dates(for_future: true)
    email_sender = ::MealPlans::Emails::SendOrderReminderEmail.new(meal_plan: meal_plan, dates: reminder_dates, frequency: frequency).call
    if email_sender.success?
      result.notified_meal_plans << meal_plan
    else
      result.errors += email_sender.errors
    end
  end

  def has_past_orders_for?(meal_plan)
    lister_options = {
      meal_plan: meal_plan,
      statuses: CURRENT_ORDER_STATUSES,
      page: 1,
      limit: nil
    }.merge(order_dates)
     Orders::List.new(options: lister_options).call.present?
  end

  def has_future_orders_for?(meal_plan)
    dates = order_dates(for_future: true)
    lister_options = {
      meal_plan: meal_plan,
      statuses: FUTURE_ORDER_STATUSES,
      page: 1,
      limit: nil
    }.merge(dates)
     Orders::List.new(options: lister_options).call.present?
  end

  def order_dates(for_future: false)
    case frequency
    when 'weekly'
      reminder_week = for_future ? (time + 1.week) : time
      {
        from_date: reminder_week.beginning_of_week,
        to_date: reminder_week.end_of_week,
      }
    when 'monthly'
      reminder_month = for_future ? (time + 1.month) : time
      {
        from_date: reminder_month.beginning_of_month,
        to_date: reminder_month.end_of_month,
      }
    end
  end

  class Result
    attr_accessor :notified_meal_plans, :errors

    def initialize
      @notified_meal_plans = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
