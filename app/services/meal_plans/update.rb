class MealPlans::Update

  def initialize(customer:, meal_plan:, meal_plan_params: {}, update_params: {})
    @customer = customer
    @meal_plan = meal_plan
    @meal_plan_params = meal_plan_params
    @update_params = [default_update_params, update_params.to_h.symbolize_keys].inject(&:merge)
    @result = Result.new
  end

  def call
    return result if !can_upsert?

    if meal_plan.update(sanitized_params)
      result.meal_plan = meal_plan
      update_subsequent_orders if has_pending_orders?
    else
      result.errors += meal_plan.errors.full_messages
    end

    result
  end

private

  attr_reader :customer, :meal_plan, :meal_plan_params, :update_params, :result

  def can_upsert?
    case
    when customer.blank?
      result.errors << 'Cannot update meal plan without a customer'
    when meal_plan.blank?
      result.errors << 'Cannot update a missing meal plan'
    when meal_plan&.customer_profile != customer
      result.errors << 'You do not have access to this meal plan'
    when customer.requires_po && customer_purchase_order.blank?
      result.errors << 'Your orders require a Purchase Order'
    when customer.requires_department_identity && meal_plan_params[:department_identity].blank?
      result.errors << 'Your orders require a Cost Centre ID'
    when update_params[:mode] == 'initial' && has_pending_orders?
      result.errors << 'Your have future order that might be affected by this change'
    end
    result.errors.blank?
  end

  def sanitized_params
    [
      purchase_order_params,
      meal_plan_params.except(:uuid, :cpo_id, :gst_free_cpo_id)
    ].inject(&:merge)
  end

  def purchase_order_params
    params = {}
    params[:customer_purchase_order] = customer_purchase_order if customer_purchase_order.present?
    params[:gst_free_customer_purchase_order] = gst_free_customer_purchase_order if gst_free_customer_purchase_order.present?
    params
  end

  def customer_purchase_order
    @_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: meal_plan_params[:cpo_id]).call
  end

  def gst_free_customer_purchase_order
    @_gst_free_customer_purchase_order ||= Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: meal_plan_params[:gst_free_cpo_id]).call
  end

  def update_subsequent_orders
    case update_params[:mode]
    when 'initial'
      # do nothing
    when 'one-off'
      result.pending_orders = []
      # do nothing
    else
      MealPlans::UpdateSubsequentOrders.new(meal_plan: meal_plan, mode: 'update', order_ids: update_params[:order_ids]).delay(queue: :data_integrity).call
      result.pending_orders = []
    end
  end

  def new_meal_plan
    return nil if customer.blank?

    customer.meal_plans.build
  end

  def has_pending_orders?
    orders = meal_plan.orders.where('delivery_at > ?', Time.zone.now)
    orders = orders.where(status: %w[new amended confirmed pending]).order(delivery_at: :asc, id: :asc)
    result.pending_orders = orders

    orders.present?
  end

  def default_update_params
    {
      mode: 'initial',
      order_ids: [],
    }
  end

  class Result
    attr_accessor :meal_plan, :pending_orders, :errors

    def initialize
      @meal_plan = nil
      @pending_orders = []
      @errors = []
    end

    def success?
      errors.blank? && pending_orders.blank? && meal_plan.present? && meal_plan.persisted?
    end
  end

end
