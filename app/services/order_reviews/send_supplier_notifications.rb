class OrderReviews::SendSupplierNotifications

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new
  end

  def call
    recent_order_reviews.group_by(&:supplier_profile).each do |supplier, supplier_reviews|
      Suppliers::Emails::SendOrderReviewsEmail.new(supplier: supplier, time: time, order_reviews: supplier_reviews).call
      result.sent_notifications << 'sent-notification'
    end
    result
  end

private

  attr_reader :time, :result

  def recent_order_reviews
    reviews = OrderReview.where(created_at: (time.beginning_of_week...time.end_of_week))
    reviews = reviews.joins("LEFT JOIN emails ON (emails.fk_id = order_reviews.supplier_profile_id AND emails.ref = 'supplier-review_notification-#{time.to_s(:year_week)}')").where(emails: { id: nil }) # order review is not already sent
    reviews.includes(:supplier_profile)
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
