class OrderReviews::FetchRatingSummary

  RATING_FIELDS = %w[food_taste presentation delivery_punctuality].freeze # maybe also include field => product_quality

  def initialize(options: {})
    @options = [default_options, options].inject(&:merge)
    @result = Result.new(starts_at: starts_at, ends_at: ends_at, rating_fields: RATING_FIELDS)
  end

  def call
    order_reviews.each do |order_review|
      get_ratings_for(order_review)
      result.order_reviews << order_review
    end
    result
  end

private

  attr_reader :time, :options, :result

  def order_reviews
    return @_order_reviews if @_order_reviews.present?

    reviews = OrderReview.all
    reviews = reviews.where(order_reviews: { created_at: [starts_at..ends_at] })
    reviews = reviews.where(supplier_profile: options[:supplier]) if options[:supplier].present?
    @_order_reviews = reviews.includes(:order)
  end

  def starts_at
    @_starts_at ||= options[:time].beginning_of_week
  end

  def ends_at
    @_ends_at ||= options[:time].end_of_week
  end

  def get_ratings_for(order_review)
    RATING_FIELDS.each do |field|
      rating = order_review.send("#{field}_score").to_i
      next if rating <= 0

      existing_score = result.scores.send(field).presence || 0
      result.scores[field.to_sym] = existing_score + rating

      existing_count = result.counts.send(field).presence || 0
      result.counts[field.to_sym] = existing_count + 1
    end
  end

  def default_options
    {
      supplier: nil,
      time: Time.zone.now
    }
  end

  class Result
    attr_reader :starts_at, :ends_at, :rating_fields
    attr_accessor :scores, :counts, :order_reviews

    def initialize(starts_at:, ends_at:, rating_fields:)
      @starts_at = starts_at
      @ends_at = ends_at
      @rating_fields = rating_fields
      @order_reviews = []

      @scores = OpenStruct.new
      @counts = OpenStruct.new
    end

    def total_score
      rating_fields.map{|field| scores.send(field) }.compact.sum
    end

    def total_count
      rating_fields.map{|field| counts.send(field) }.compact.sum
    end

    def average(score, count)
      count = count > 0 ? count : 1
      (score / count.to_f).round(1)
    end
  end
end
