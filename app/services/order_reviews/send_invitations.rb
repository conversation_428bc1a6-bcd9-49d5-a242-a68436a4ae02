class OrderReviews::SendInvitations

  EMAIL_TEMPLATE = 'customer-order_review_invitation'.freeze

  def initialize(orders: [])
    @orders = orders.presence || reviewable_orders
    @result = Result.new
  end

  def call
    orders.group_by(&:customer_profile).each do |customer, customer_orders|
      next if does_not_prefer_notification_for(customer)

      customer_orders.each do |order|
        email_sender = Customers::Emails::SendOrderReviewEmail.new(order: order, customer: customer).call
        if email_sender.success?
          result.sent_invitations << email_sender.sent_notification
        else
          result.errors << "Error for #{order.id}: #{email_sender.errors.join('. ')}"
        end
      end
    end
    result
  end

private

  attr_reader :orders, :result

  def reviewable_orders
    start_date = yordar_credentials(:yordar, :review_invitation_start_date).presence || Time.zone.now.beginning_of_month
    orders = Order.where('orders.delivery_at > ?', start_date) # since reviewable time
    orders = orders.where(status: 'delivered') # only delivered
    orders = orders.where("(order_type = 'recurrent' and orders.id = template_id) or (order_type = 'one-off')") # only one-off or template orders
    orders = orders.joins("LEFT JOIN emails ON (emails.fk_id = orders.id AND emails.ref = 'customer-order_review_invitation')").where(emails: { id: nil }) # order review is not already sent
    orders = orders.joins(customer_profile: :customer_flags).where(customer_flags: { cancel_review_requests: [nil, false] }) # customer does not have review invitation restrictions
    orders = orders.includes(customer_profile: :notification_preferences)
    orders.distinct
  end

  def does_not_prefer_notification_for(customer)
    review_preference = customer.notification_preferences.where(template_name: EMAIL_TEMPLATE).first
    review_preference.present? && !review_preference.active?
  end

  class Result
    attr_accessor :sent_invitations, :errors

    def initialize
      @sent_invitations = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
