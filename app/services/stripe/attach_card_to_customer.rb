class Stripe::AttachCardToCustomer

  def initialize(customer:, credit_card:)
    @customer = customer
    @credit_card = credit_card
    @result = Result.new
  end

  def call
    case
    when customer.stripe_token.present?
      attach_payment_to_existing_customer
    else
       create_stripe_customer_with_payment
    end
    result
  end

private

  attr_reader :customer, :credit_card, :result

  def create_stripe_customer_with_payment
    customer_creator = Stripe::CreateCustomer.new(customer: customer, payment_token: credit_card.stripe_token).call
    if customer_creator.success?
      result.stripe_customer = customer_creator.stripe_customer
    else
      result.errors += customer_creator.errors
    end
  end

  def attach_payment_to_existing_customer
    result.stripe_customer = Stripe::PaymentMethod.attach(
      credit_card.stripe_token,
      { customer: customer.stripe_token }
    )
  rescue => exception
    error_message = exception.message || ''
    error_message += " - #{exception.json_body&.dig(:error, :decline_code)}" if exception.is_a?(Stripe::CardError)
    result.errors << error_message
  end

  class Result
    attr_accessor :stripe_customer, :errors

    def initialize
      @stripe_customer = nil
      @errors = []
    end

    def success?
      errors.blank? && stripe_customer.present?
    end
  end

end
