class Stripe::NotifyEvent

  EXCLUDE_NOTIFICATION_TYPES = %w[payment_intent.created].freeze

  def initialize(event:)
    @event = event
  end

  def call
    if can_notify?
      notify_via_slack
      notify_via_email
    end
  end

private

  attr_reader :event

  def can_notify?
    event.present? && EXCLUDE_NOTIFICATION_TYPES.exclude?(event.type)
  end

  def notify_via_email
    Admin::Emails::SendFailedStripePaymentEmail.new(event: event).call
  end

  def notify_via_slack
    Admin::Notifications::NotifyStripeEventViaSlack.new(event: event).call
  end

end
