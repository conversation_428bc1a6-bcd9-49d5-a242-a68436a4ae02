class Stripe::CreateOrderCharge

  StripeCharge = Struct.new(:payment_id, :charge_id)

  def initialize(order:)
    @order = order
    @result = Result.new
  end

  def call
    begin
      payment_response = create_payment_intent
      result.response_message = payment_response.status
      if payment_response.present? && payment_response.status == 'requires_capture'
        charge = payment_response.charges.data.first
        result.charge = StripeCharge.new(payment_response.id, charge.id)
      else
        result.errors << "Could not create on-hold charge for order ##{order.id}"
      end
    rescue Stripe::InvalidRequestError => exception
      result.response_message = exception.message
      result.errors << exception.message
    rescue Stripe::CardError => exception
      result.response_message = "#{exception.message} - #{exception.json_body&.dig(:error, :decline_code)}"
      result.errors << exception
    end
    result
  end

private

  attr_reader :order, :on_hold, :result

  def create_payment_intent
    Stripe::PaymentIntent.create(sanitized_options)
  end

  def sanitized_options
    default_charge_options.merge({
      amount: amount_in_cents,
      description: charge_description,
      customer: order.customer_profile.stripe_token,
      payment_method: order.credit_card.stripe_token,
      metadata: {
        order_id: order.id,
      },
    })
  end

  def amount_in_cents
    (order.customer_total * 100).to_i
  end

  def charge_description
    "On-Hold charge for order ##{order.id}"
  end

  def default_charge_options
    {
      currency: 'aud',
      error_on_requires_action: true,
      confirm: true,
      capture_method: 'manual', # required for on-hold charge
    }
  end

  class Result
    attr_accessor :response_message, :charge, :errors

    def initialize
      @response_message = nil
      @charge = nil
      @errors = []
    end

    def success?
      errors.blank? && charge.present?
    end
  end

end

