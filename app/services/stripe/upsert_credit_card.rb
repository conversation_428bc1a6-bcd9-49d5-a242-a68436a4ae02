class Stripe::UpsertCreditCard

  def initialize(credit_card: nil, card_params: {}, customer: nil)
    @credit_card = credit_card.presence || CreditCard.new
    @card_params = card_params
    @customer = customer
    @result = Result.new(customer: customer)
  end

  def call
    save_card_with_token
    result
  end

private

  attr_accessor :result
  attr_reader :credit_card, :customer, :card_params

  def save_card_with_token
    credit_card.assign_attributes(sanitized_card_params)
    if credit_card.save
      if customer.present?
        Stripe::AttachCardToCustomer.new(customer: customer, credit_card: credit_card).call
        customer.credit_cards << credit_card
      end
      result.credit_card = credit_card
    else
      result.credit_card = credit_card
      result.errors += credit_card.errors.full_messages
    end
  end

  def sanitized_card_params
    [
      card_params.to_h.except(:number, :cvv, :last4),
      calculated_params
    ].inject(&:merge)
  end

  def calculated_params
    {
      number: brand_number,
      label: "#{brand_name} (#{brand_number})"
    }
  end

  def brand_number
    case card_params[:brand]
    when 'amex'
      "####-######-##{card_params[:last4]}"
    else
      "####-####-####-#{card_params[:last4]}"
    end
  end

  def brand_name
    case card_params[:brand]
    when 'amex'
      'American Express'
    when 'mastercard'
      'MasterCard'
    else
      card_params[:brand].capitalize
    end
  end

  class Result
    attr_accessor :customer, :credit_card, :errors

    def initialize(customer:)
      @customer = customer
      @credit_card = nil
      @errors = []
    end

    def success?
      errors.blank? && credit_card.present? && credit_card.persisted?
    end
  end
end
