class Stripe::ProcessPayment

  def initialize(customer:, payment:)
    @customer = customer
    @payment = payment
    @result = Result.new
  end

  def call
    begin
      payment_response = create_payment_intent
      result.response_message = payment_response.status
      if payment_response.present? && payment_response.status == 'succeeded'
        charge = payment_response.charges.data.first
        result.auth_code = charge.id
        result.transaction_number = charge.balance_transaction
      end
    rescue Stripe::InvalidRequestError, Stripe::CardError => exception
      result.response_message = exception.message
      result.errors << exception.message
    end
    result
  end

private

  attr_reader :customer, :payment, :result

  def create_payment_intent
    Stripe::PaymentIntent.create({
      amount: payment_amount_in_cents,
      currency: 'aud',
      customer: customer.stripe_token,
      payment_method: payment.credit_card.stripe_token,
      error_on_requires_action: true,
      confirm: true,
      description: "Payment for invoice ##{payment.invoice.number}",
      metadata: {
        order_ids: payment.invoice.order_ids.join(','),
      },
    })
  end

  def payment_amount_in_cents
    (payment.amount * 100).to_i
  end

  class Result
    attr_accessor :response_message, :auth_code, :transaction_number, :errors

    def initialize
      @response_message = nil
      @auth_code = nil
      @transaction_number = nil
      @errors = []
    end

    def success?
      errors.blank? && auth_code.present? && transaction_number.present?
    end
  end

end
