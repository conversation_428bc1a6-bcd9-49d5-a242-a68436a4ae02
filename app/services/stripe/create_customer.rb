class Stripe::<PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(customer:, payment_token:)
    @customer = customer
    @payment_token = payment_token
    @result = Result.new(customer: customer)
  end

  def call
    if can_create_new_stripe_customer?
      result.stripe_customer = stripe_customer = Stripe::Customer.create(customer_create_attributes)
      customer.update(stripe_token: stripe_customer.id)
    end
    result
  end

private

  attr_reader :customer, :payment_token, :result

  def can_create_new_stripe_customer?
    case
    when customer.blank?
      result.errors << 'Cannot create a stripe customer without a Yordar customer'
    when customer.stripe_token.present?
      result.errors << 'Yordar Customer is already a Stripe customer'
    end
    result.errors.blank?
  end

  def customer_create_attributes
    {
      email: customer.user.email,
      name: customer.name,
      description: "#{customer.name} - Stripe Yordar Customer",

      payment_method: payment_token,
    }
  end

  class Result
    attr_accessor :stripe_customer, :errors
    attr_reader :customer

    def initialize(customer:)
      @customer = customer
      @stripe_customer = nil
      @errors = []
    end

    def success?
      errors.blank? && stripe_customer.present? && customer.stripe_token == stripe_customer.id
    end
  end
end
