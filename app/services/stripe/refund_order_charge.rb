class Stripe::RefundOrderCharge

  StripeRefund = Struct.new(:id, :charge_id, :payment_id)

  def initialize(order_charge:)
    @order_charge = order_charge
    @result = Result.new(payment_id: order_charge.stripe_token)
  end

  def call
    begin
      payment_response = cancel_payment_intent
      result.response_message = payment_response.status
      if payment_response.present? && payment_response.status == 'canceled'
        charge = payment_response.charges.data.first
        refund = charge.refunds.data.first
        result.refund = StripeRefund.new(refund.id, charge.id, charge.payment_intent)
      else
        result.errors << "Could not refund charge for order_charge ##{order_charge.id}"
      end
    rescue Stripe::InvalidRequestError => exception
      result.response_message = exception.message
      result.errors << exception.message
    end
    result
  end

private

  attr_reader :order_charge, :result

  def cancel_payment_intent
    Stripe::PaymentIntent.cancel(order_charge.stripe_token)
  end

  class Result
    attr_accessor :response_message, :refund, :errors
    attr_reader :payment_id

    def initialize(payment_id:)
      @payment_id = payment_id
      @response_message = nil
      @refund = nil
      @errors = []
    end

    def success?
      errors.blank? && refund.present? && refund.payment_id == payment_id
    end
  end
end
