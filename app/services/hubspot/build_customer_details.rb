class Hubspot::BuildCustomerDetails < Hubspot::ContactDetails

  def initialize(contact:, refresh: false)
    @contact = contact
    @refresh = refresh
    super(contact: contact)
  end

  def call
    hs_contact.present? && !refresh ? update_details : creation_details
  end

private

  attr_reader :contact, :refresh, :result

  def creation_details
    [
      default_details,
      customer_specific_details,
      update_details
    ].inject(&:merge)
  end

  def update_details
    [
      status_details,
      company_details,
      role_details,
      contact_info_details,
      location_details,
      latest_details
    ].inject(&:merge)
  end

  def customer_specific_details
    {
      custom_lifecyclestage: 'customer',
      contact_type: 'Customer',
    }
  end

  def status_details
    {
      hs_content_membership_status: contact.is_active ? 'active' : 'inactive'
    }
  end

  def latest_details
    last_sign_in = utc_midnight_time(contact.last_sign_in_at)
    date_last_ordered = latest_orders.present? ? utc_midnight_time(latest_orders.last.created_at) : nil
    details = {
      last_sign_in: last_sign_in,
      date_last_ordered: date_last_ordered,
      order_categories: culumative_order_categories.present? ? culumative_order_categories.join(';') : nil,
    }
    details[:lifecyclestage] = 'customer' if date_last_ordered.present?
    details
  end

  def company_details
    return {} if hs_contact.present? && company_name.present? && hs_contact.properties[:company].present? && hs_contact.properties[:company] == company_name

    {
      company: company_name,
    }
  end

  def company_name
    profile.company&.name || profile.company_name
  end

  def role_details
    return {} if hs_contact.present? && job_title.present? && hs_contact.properties[:jobtitle].present? && hs_contact.properties[:jobtitle] == job_title

    {
      jobtitle: job_title,
    }
  end

  def job_title
    profile.company_team_admin? ? 'Company Team Admin' : profile.role
  end


  def phone
    profile_phone = (profile.contact_phone.presence || profile.mobile)
    profile_phone.present? ? profile_phone : nil
  end

  def suburb
    @_suburb ||= profile.billing_details.present? ? profile.billing_details.suburb : contact_suburb
  end

  def latest_orders
    @_latest_orders ||= Order.where(customer_profile: profile).where.not(status: %w[draft on-hold cancelled rejected voided]).order(:created_at).last(10)
  end

  def order_categories
    return [] if latest_orders.blank?

    @_order_categories ||= latest_orders.map do |order|
      catering_categories, other_categories = order.ordered_categories.compact.partition{|category| category.group == 'catering-services' }
      categories = other_categories.present? ? other_categories.map(&:slug) : []
      categories += catering_categories.present? ? ['catering-services'] : []
      categories
    end.flatten(1).uniq.sort
  end

  def culumative_order_categories
    return @_cumulative_order_categories if !@_cumulative_order_categories.nil?

    categories = order_categories
    if hs_contact.present? && hs_contact.properties.present?
      hs_contact_categories = hs_contact.properties[:order_categories]&.split(';')
      categories += hs_contact_categories if hs_contact_categories.present?
    end
    @_cumulative_order_categories = categories.uniq.sort
  end

end
