class Hubspot::BuildSupplierDetails < Hubspot::ContactDetails

  def initialize(contact:, refresh: false)
    @contact = contact
    @refresh = refresh
    super(contact: contact)
  end

  def call
    hs_contact.present? && !refresh ? update_details : creation_details
  end

private

  attr_reader :contact, :refresh, :result

  def creation_details
    [default_details, supplier_specific_details, update_details].inject(&:merge)
  end

  def update_details
    [status_details, contact_info_details, location_details, latest_details].inject(&:merge)
  end

  def supplier_specific_details
    {
      company: profile.company_name,
      custom_lifecyclestage: 'supplier',
      contact_type: 'Supplier',
    }
  end

  def status_details
    is_active = contact.is_active && profile.is_searchable
    {
      hs_content_membership_status: is_active ? 'active' : 'inactive'
    }
  end

  def latest_details
    last_sign_in = utc_midnight_time(contact.last_sign_in_at)
    date_last_ordered = latest_orders.present? ? utc_midnight_time(latest_orders.last.delivery_at) : nil
    details = {
      last_sign_in: last_sign_in,
      date_last_ordered: date_last_ordered,
    }
    details[:lifecyclestage] = 'other' if date_last_ordered.present?
    details
  end

  def latest_orders
    @_latest_orders ||= Order.joins(:order_lines).where.not(status: %w[draft on-hold cancelled rejected]).where(order_lines: { supplier_profile: profile }).order(delivery_at: :asc).last(10)
  end

  def phone
    profile_phone = (profile.phone.presence || profile.mobile)
    profile_phone.present? ? profile_phone : nil
  end

  def suburb
    @_suburb ||= profile.company_address_suburb.presence || contact_suburb
  end

end
