class Hubspot::ContactDetails

  def initialize(contact:)
    @contact = contact
  end

private

  attr_reader :contact

  def profile
    @_profile ||= contact.profile.profileable
  end

  def default_details
    {
      firstname: contact.firstname,
      lastname: contact.lastname,
      lifecyclestage: 'marketingqualifiedlead',
    }
  end

  def contact_info_details
    return {} if hs_contact.present? && phone.present? && hs_contact.properties[:phone] == phone

    {
      phone: phone,
    }
  end

  def location_details
    return {} if hs_contact.present? && suburb.present? && hs_contact.properties[:city] == suburb.name

    {
      zip: suburb.try(:postcode),
      city: suburb.try(:name),
      state: suburb.try(:state),
      country: 'Australia',
      hs_timezone: 'australia_slash_nsw',
    }
  end

  def hs_contact
    return @_hs_contact if !@_hs_contact.nil?
    return false if contact.hs_contact_id.blank?

    hubspot_contact = Hubspot::Contact.find_by_email(contact.email)
    @_hs_contact = hubspot_contact.present? && hubspot_contact
  end

  def utc_midnight_time(time)
    return nil if time.blank?

    Time.utc(time.year, time.month, time.day).midnight.to_i * 1000
  end

  def contact_suburb
    contact.suburb
  end
end
