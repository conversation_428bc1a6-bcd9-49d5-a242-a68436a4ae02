class Hubspot::API::Base

  BASE_URL = 'https://api.hubspot.com/conversations/v3'.freeze
  IDENTITY_PATH = 'visitor-identification/tokens/create'.freeze

  def request(url:, method:, body: nil)
    response = hubspot_connection.send(method) do |request|
      request.url(url)
      request.body = body.to_json if body.present?
    end
    if response.status == 200
      JSON.parse(response.body).deep_symbolize_keys
    else
      {}
    end
  end

  def hubspot_connection
    @_connection ||= Faraday.new(url: BASE_URL) {|faraday| connection_body(faraday) }
  end

  def connection_body(faraday)
    faraday.adapter(Faraday.default_adapter)
    faraday.headers['Content-Type'] = 'application/json'
    # Will not work for Hubspot's App Test account, as it does not have access to conversations, temporarily change to prod credentials for testing
    faraday.headers['Authorization'] = "Bearer #{yordar_credentials(:hubspot, :access_token)}"
  end

end
