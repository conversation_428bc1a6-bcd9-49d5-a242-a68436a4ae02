class Hubspot::API::GetIdentityToken < Hubspot::API::Base

  def initialize(profile:)
    @profile = profile
    @result = Result.new
  end

  def call
    if can_get?
      result.email = contact.email
      result.token = fetch_token
    end
    result
  end

private

  attr_reader :profile, :result

  def can_get?
    case
    when profile.blank?
      result.errors << 'Cannot get token for a missing profile'
    when contact.blank?
      result.errors << 'Cannot get token for a missing contact'
    end
    result.errors.blank?
  end

  def contact
    @_contact ||= profile.user
  end

  def fetch_token
    Rails.cache.fetch(cache_key, expires_in: 12.hours) do
      response = request(url: IDENTITY_PATH, method: :post, body: contact_data)
      response[:token]
    end
  end

  def contact_data
    {
      'email': contact.email,
      'firstName': contact.firstname,
      'lastName': contact.lastname
    }
  end

  def cache_key
    @_cache_key ||= ['hubspot-identity-token', contact.email]
  end

  class Result
    attr_accessor :email, :token, :errors

    def initialize
      @email = nil
      @token = nil
      @errors = []
    end

    def success?
      errors.blank? && token.present?
    end
  end

end
