class Hubspot::Sync<PERSON>ontact

  def initialize(contact:, refresh: false)
    @contact = contact
    @refresh = refresh
    @result = Result.new(contact: contact)
  end

  def call
    begin
      response_hs_contact = Hubspot::Contact.createOrUpdate(contact.email, sync_body)
      result.hs_contact_id = response_hs_contact.vid
      contact.update_column(:hs_contact_id, response_hs_contact.vid) if contact.hs_contact_id.blank? || contact.hs_contact_id.to_s != response_hs_contact.vid.to_s
    rescue => exception
      result.errors << "Sync for User ##{contact.id} failed due to: #{exception}"
      result.errors << exception.message
      Raven.capture_exception(exception,
        message: exception.message,
        extra: { contact_id: contact&.id, profile_id: profile&.id },
        transaction: self.class.name
      )
    end
    result
  end

private

  attr_reader :contact, :refresh, :result

  def sync_body
    if profile.instance_of?(CustomerProfile)
      Hubspot::BuildCustomerDetails.new(contact: contact, refresh: refresh).call
    else
      Hubspot::BuildSupplierDetails.new(contact: contact, refresh: refresh).call
    end
  end

  def profile
    @_profile ||= contact&.profile&.profileable
  end

  class Result
    attr_accessor :hs_contact_id, :errors
    attr_reader :contact

    def initialize(contact:)
      @contact = contact
      @hs_contact_id = nil
      @errors = []
    end

    def success?
      errors.blank? && hs_contact_id.present? && contact.hs_contact_id.to_s == hs_contact_id.to_s
    end
  end

end
