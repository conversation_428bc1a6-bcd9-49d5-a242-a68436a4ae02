class Hubspot::SyncLead

  def initialize(lead:)
    @lead = lead
    @result = Result.new(lead: lead)
  end

  def call
    begin
      sync_with_hubspot
    rescue => exception
      result.errors << exception.message
    end
    result
  end

private

  attr_reader :lead, :result

  def sync_body
    {
      firstname: lead.firstname,
      lastname: lead.lastname,
      custom_lifecyclestage: lead.lead_type,
      lifecyclestage: 'marketingqualifiedlead',

      country: 'Australia',
      hs_timezone: 'australia_slash_nsw',
    }
  end

  def sync_with_hubspot
    begin
      hs_contact = Hubspot::Contact.createOrUpdate(lead.email, sync_body)
      result.lead_id = lead_id = hs_contact.vid
      lead.update_column(:hs_contact_id, lead_id) if lead.hs_contact_id.blank?
    rescue => exception
      result.errors << "Sync for Lead ##{lead.id} failed due to: #{exception}"
    end
  end

  class Result
    attr_accessor :lead_id, :errors
    attr_reader :lead

    def initialize(lead:)
      @lead = lead
      @lead_id = nil
      @errors = []
    end

    def success?
      errors.blank? && lead_id.present? && lead.hs_contact_id.to_s == lead_id.to_s
    end
  end

end
