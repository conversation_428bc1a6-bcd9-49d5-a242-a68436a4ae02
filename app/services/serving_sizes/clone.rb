class ServingSizes::<PERSON><PERSON>

  def initialize(serving_size:, menu_item: nil)
    @serving_size = serving_size
    @menu_item = menu_item
    @result = Result.new(template: serving_size)
  end

  def call
    if can_clone?
      serving_creator = ServingSizes::Upsert.new(serving_size_params: clone_params, forced: true).call
      if serving_creator.success?
        result.cloned_serving_size = serving_creator.serving_size
      else
        result.errors += serving_creator.errors
      end
    end
    result
  end

private

  attr_reader :serving_size, :menu_item, :result

  def can_clone?
    if serving_size.archived_at.present?
      result.errors << 'Cannot clone an archived serving size'
    end
    result.errors.blank?
  end

  def clone_params
    params = serving_size.attributes.symbolize_keys.except(:id, :name, :weight, :created_at, :updated_at, :menu_item_id)
    params[:name] = menu_item.present? ? serving_size.name : "#{serving_size.name} - CLONED"
    params[:menu_item_id] = menu_item.present? ? menu_item.id : serving_size.menu_item_id
    params
  end

  class Result
    attr_accessor :template, :cloned_serving_size, :errors

    def initialize(template:)
      @template = template
      @cloned_serving_size = nil
      @errors = []
    end

    def success?
      errors.blank? && cloned_serving_size.present? && cloned_serving_size.persisted?
    end
  end
end
