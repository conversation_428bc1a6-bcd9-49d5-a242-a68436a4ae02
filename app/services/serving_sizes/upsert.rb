class ServingSizes::Upsert

  ORDERLINE_CHANGE_FIELDS = %w[name price].freeze

  def initialize(serving_size_params:, serving_size: nil, forced: nil)
    @is_forced = forced.presence || false
    @serving_size_params = serving_size_params
    @serving_size = serving_size.presence || fetch_serving_size
    @result = Result.new
  end

  def call
    has_potential_order_line_changes = detect_changes
    if serving_size.update(sanitized_params)
      result.serving_size = serving_size
      update_future_order_lines if has_potential_order_line_changes
      mark_menu_as_updated
    else
      result.errors += serving_size.errors.full_messages
    end
    result
  end

private

  attr_reader :serving_size, :serving_size_params, :is_forced, :result

  def fetch_serving_size
    retrieval_params = {
      name: sanitized_name_params[:name],
      menu_item: menu_item,
    }
    is_forced ? ServingSize.new(retrieval_params) : ServingSize.where(retrieval_params).first_or_initialize
  end

  def detect_changes
    return false if serving_size.new_record?

    serving_size.assign_attributes(sanitized_params)
    ORDERLINE_CHANGE_FIELDS.detect do |field|
      serving_size.changes[field].present? && serving_size.changes[field].first != serving_size.changes[field].last
    end.present?
  end

  def sanitized_params
    [
      default_params,
      serving_size_params,
      sanitized_name_params,
      sanitized_stock_params
    ].inject(&:merge)
  end

  def default_params
    return {} if !serving_size.new_record?

    {
      weight: serving_size_params[:weight] || serving_size.weight || max_weight
    }
  end

  def sanitized_name_params
    return {} if serving_size_params[:name].blank?

    {
      name: serving_size_params[:name].strip
    }
  end

  def sanitized_stock_params
    stock_quantity = serving_size_params[:stock_quantity].present? ? serving_size_params[:stock_quantity].to_i : nil
    {
      stock_quantity: stock_quantity
    }
  end

  def menu_item
    @_menu_item ||= case
    when serving_size_params[:menu_item].present?
      serving_size_params[:menu_item]
    when menu_item_id = serving_size_params[:menu_item_id].presence
      MenuItem.where(id: menu_item_id).first
    when serving_size.present?
      serving_size.menu_item
    end
  end

  def max_weight
    weight = case
    when menu_item.present?
      menu_item.serving_sizes.pluck(:weight).compact.max
    else
      ServingSize.pluck(:weight).compact.max
    end
    (weight.presence || 0) + 1
  end

  def update_future_order_lines
    has_existing_update = OrderLines::FutureUpdateExists.new(item: serving_size).call
    return if has_existing_update

    MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size).delay(queue: :data_integrity).call
  end

  def mark_menu_as_updated
    supplier = serving_size.supplier_profile
    return if supplier.blank?

    supplier.supplier_flags&.update(menu_last_updated_on: Time.zone.now)
  end

  class Result
    attr_accessor :serving_size, :errors

    def initialize
      @serving_size = nil
      @errors = []
    end

    def success?
      errors.blank? && serving_size.present? && serving_size.valid? && serving_size.persisted?
    end
  end
end
