class ServingSizes::Archive

  def initialize(serving_size:)
    @serving_size = serving_size
    @result = Result.new
  end

  def call
    if can_archive? && serving_size.update(archived_at: Time.zone.now)
      mark_menu_as_updated
      result.serving_size = serving_size
    end
    result
  end

private

  attr_accessor :serving_size, :result

  def can_archive?
    case
    when serving_size.blank?
      result.errors << 'Cannot archive a missing serving size'
    when serving_size.archived_at.present?
      result.errors << 'Cannot archive an already archived serving size'
    end
    result.errors.blank?
  end

  def mark_menu_as_updated
    supplier = serving_size.supplier_profile
    return if supplier.blank?

    supplier.supplier_flags&.update(menu_last_updated_on: Time.zone.now)
  end

  class Result
    attr_accessor :serving_size, :errors

    def initialize
      @serving_size = nil
      @errors = []
    end

    def success?
      errors.blank? && serving_size&.archived_at.present?
    end
  end
end
