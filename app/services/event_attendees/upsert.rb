class EventAttendees::Upsert

  def initialize(event_attendee_params:, team_admin:, event_attendee: nil)
    @event_attendee_params = event_attendee_params
    @team_admin = team_admin
    @event_attendee = event_attendee.presence || fetch_event_attendee
    @result = Result.new(event_attendee: @event_attendee)
  end

  def call
    if event_attendee.update(sanitized_params)
      upsert_teams
      result.event_attendee = event_attendee
    else
      result.errors += event_attendee.errors.full_messages
    end
    result
  end

private

  attr_reader :event_attendee, :event_attendee_params, :team_admin, :result

  def fetch_event_attendee
    team_admin.event_attendees.where(email: event_attendee_params[:email]).first_or_initialize
  end

  def sanitized_params
    [
      default_params,
      event_attendee_params.except(:teams)
    ].inject(&:merge)
  end

  def default_params
    {
      active: true
    }
  end

  def upsert_teams
    return if event_attendee_params[:teams].blank?

    EventAttendees::UpsertTeams.new(event_attendee: event_attendee, team_params: event_attendee_params[:teams], team_admin: team_admin).call
  end

  class Result
    attr_accessor :event_attendee, :errors

    def initialize(event_attendee:)
      @event_attendee = event_attendee
      @errors = []
    end

    def success?
      errors.blank? && event_attendee.present? && event_attendee.persisted?
    end
  end

end
