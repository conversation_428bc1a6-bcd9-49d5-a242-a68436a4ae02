class EventAttendees::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(team_admin:, attendee_params:)
    @team_admin = team_admin
    @attendee_params = attendee_params
    @result = Result.new
  end

  def call
    if can_create_attendee?
      attendee_creator = EventAttendees::Upsert.new(event_attendee_params: attendee_params, team_admin: team_admin).call
      if attendee_creator.success?
        result.event_attendee = @event_attendee = attendee_creator.event_attendee
      else
        result.errors += attendee_creator.errors
      end
    end
    result
  end

private

  attr_reader :team_admin, :attendee_params, :event_attendee, :result

  def can_create_attendee?
    case
    when team_admin.event_attendees.where(email: attendee_params[:email]).present?
      result.errors << 'You are already registered as a contact'
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :event_attendee, :team_order_attendee, :errors

    def initialize
      @event_attendee = nil
      @errors = []
    end

    def success?
      errors.blank? && event_attendee.present?
    end
  end

end
