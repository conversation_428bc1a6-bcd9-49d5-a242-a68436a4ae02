class EventAttendees::Add<PERSON><PERSON><PERSON><PERSON>ToTeam

  def initialize(event_attendee:, event_team:)
    @event_team = event_team
    @event_attendee = event_attendee
    @result = Result.new(event_team: @event_team, event_attendee: @event_attendee)
  end

  def call
    if can_add?
      event_attendee.event_teams << event_team
    end
    result
  end

private

  attr_reader :event_team, :event_attendee, :result

  def can_add?
    case
    when event_team.blank?
      result.errors << 'Cannot add to missing team'
    when event_attendee.blank?
      result.errors << 'Cannot add a missing attendee'
    when event_attendee.team_admin != event_team.customer_profile
      result.errors << 'Cannot add an attendee not belonging to the same team admin as the team'
    when event_attendee.event_teams.include?(event_team)
      result.errors << 'Attendee already part of the team'
    end
    result.errors.blank?
  end

  class Result
    attr_reader :event_team, :event_attendee
    attr_accessor :errors

    def initialize(event_team:, event_attendee:)
      @event_team = event_team
      @event_attendee = event_attendee
      @errors = []
    end

    def success?
      errors.blank? && event_attendee.event_teams.include?(event_team)
    end
  end
end
