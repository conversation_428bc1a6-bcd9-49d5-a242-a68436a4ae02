class EventAttendees::Remove

  def initialize(event_attendee:, team_admin:)
    @event_attendee = event_attendee
    @team_admin = team_admin
    @result = Result.new(event_attendee: event_attendee)
  end

  def call
    if can_remove?
      event_attendee.update(active: false)
    end
    result
  end

private

  attr_reader :event_attendee, :team_admin, :result

  def can_remove?
    case
    when event_attendee.blank? || team_admin.blank?
      result.errors << 'Could not find a matching contact'
    when event_attendee.team_admin != team_admin
      result.errors << 'This contact does not belong to you'
    when !event_attendee.active?
      result.errors << 'Cannot remove an already removed contact'
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :event_attendee, :errors

    def initialize(event_attendee:)
      @event_attendee = event_attendee
      @errors = []
    end

    def success?
      errors.blank? && event_attendee.present? && !event_attendee.active?
    end
  end

end
