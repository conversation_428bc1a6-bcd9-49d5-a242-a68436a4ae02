class EventAttendees::UpsertTeams

  def initialize(event_attendee:, team_params:, team_admin: nil)
    @event_attendee = event_attendee
    @team_admin = team_admin.presence || event_attendee.team_admin
    @team_params = team_params
    @result = Result.new(event_attendee: event_attendee)
  end

  def call
    event_attendee.event_teams = existing_event_teams + new_teams
    event_attendee.save
    result
  end

private

  attr_reader :event_attendee, :team_params, :team_admin, :result

  def sanitized_team_params
    @_sanitized_team_params ||= team_params.present? ? team_params.reject(&:blank?) : []
  end

  def existing_event_teams
    @_existing_teams ||= team_admin.event_teams.where(id: sanitized_team_params.map(&:to_i))
  end

  def new_teams
    new_teams = []
    possible_new_teams = sanitized_team_params.map(&:to_s) - existing_event_teams.map{|team| team.id.to_s }
    return new_teams if possible_new_teams.blank?

    possible_new_teams.each do |possible_new_team_name|
      team_upserter = EventTeams::Upsert.new(team_admin: team_admin, team_params: { name: possible_new_team_name }).call
      if team_upserter.success?
        new_teams << team_upserter.event_team
      else
        result.errors += team_upserter.errors
      end
    end
    new_teams
  end

  class Result
    attr_accessor :event_attendee, :errors

    def initialize(event_attendee:)
      @event_attendee = event_attendee
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
