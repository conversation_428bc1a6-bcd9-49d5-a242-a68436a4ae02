class EventAttendees::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(csv_data:, team_admin:)
    @csv_data = csv_data
    @team_admin = team_admin
    @result = Result.new
  end

  def call
    if can_create?
      csv_data.each do |csv_row|
        create_attendee_for(csv_row)
      end
    end
    result
  end

private

  attr_reader :csv_data, :team_admin, :result

  def can_create?
    case
    when csv_data.blank?
      result.errors << 'CSV data missing'
    end
    result.errors.blank?
  end

  def create_attendee_for(csv_row)
    event_attendee_params = {
      first_name: csv_row['first_name'].try(:strip),
      last_name: csv_row['last_name'].try(:strip),
      email: csv_row['email'].try(:strip),
      teams: csv_row['teams'].present? ? csv_row['teams'].split('/').map(&:strip) : [],
    }
    attendee_creator = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params, team_admin: team_admin).call
    if attendee_creator.success?
      result.event_attendees << attendee_creator.event_attendee
    else
      result.errors += attendee_creator.errors
    end
  end

  class Result
    attr_accessor :event_attendees, :errors

    def initialize
      @event_attendees = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
