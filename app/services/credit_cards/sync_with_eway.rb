require 'active_merchant/billing/rails'

class CreditCards::SyncWithEway

  def initialize(card_params: {}, customer: nil)
    @card_params = card_params
    @customer = customer
    set_merchant_mode
    @result = Result.new
  end

  def call
    if eway_card.present? && eway_card.valid?
      result.display_number = eway_card.display_number
      result.brand = brand_name
      fetch_token
    else
      result.errors += eway_card.errors.full_messages
    end
    result
  end

private

  attr_reader :customer, :card_params, :result

  def eway_card
    return @_eway_card if @_eway_card.present?

    if (name = card_params[:name].presence)
      first_name = name.split(' ').first
      last_name = name.gsub(first_name, '')
    end
    @_eway_card = ActiveMerchant::Billing::CreditCard.new(
      first_name: first_name,
      last_name: last_name,
      number: card_params[:number],
      month: card_params[:expiry_month],
      year: card_params[:expiry_year],
      verification_value: card_params[:cvv]
    )
  end

  def fetch_token
    response = eway_gateway.store(eway_card, options_for_token)
    if response.success? # retrieved the token
      result.token = response.params['CreateCustomerResult']
    else
      result.errors << response.message
    end
  end

  def eway_gateway
    ActiveMerchant::Billing::Base.gateway(:eway_managed).new(login: yordar_credentials(:eway, :login), username: yordar_credentials(:eway, :username), password: yordar_credentials(:eway, :password))
  end

  def set_merchant_mode
    if !Rails.env.production?
      ActiveMerchant::Billing::Base.mode = :test
    end
  end

  def options_for_token
    billing_details = {
      title: '',
      name: card_params[:name] || (customer.present? && customer.name),
      country: 'au',
    }
    if (customer_billing_details = customer.present? && customer.billing_details.presence)
      billing_details[:address1] = customer_billing_details.address
      if (suburb = customer_billing_details.suburb.presence)
        billing_details[:city] = suburb.name
        billing_details[:zip] = suburb.postcode
        billing_details[:state] = suburb.state
      end
    end
    email = customer.present? ? customer.user.email : ''
    {
      billing_address: billing_details,
      email: email,
    }
  end

  def brand_name
    brand = eway_card.brand
    return 'Unknown' if brand.blank?

    case brand
    when 'master'
      'MasterCard'
    else
      brand.gsub(/_/, ' ').titleize
    end
  end

  class Result
    attr_accessor :errors, :token, :display_number, :brand

    def initialize
      @token = nil
      @display_number = nil
      @brand = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
