class CreditCards::FetchSurcharge

  # stripe constants
  STRIPE_FEE = 0.30
  STRIPE_DOMESTIC_PERCENT = 1.75
  STRIPE_INTERNATIONAL_PERCENT = 2.9

  # eway constants
  EWAY_FEE = 0.25
  EWAY_PERCENT = 1.5
  EWAY_DINERS_PERCENT = 3.7

  def initialize(credit_card:)
    @credit_card = credit_card
    @result = Result.new
  end

  def call
    if credit_card.present? && !credit_card.pay_on_account?
      retrieve_surcharge_percent
      retrieve_surcharge_fee
    end
    result
  end

private

  attr_reader :credit_card, :result

  def retrieve_surcharge_percent
    result.percent = credit_card.is_stripe_card? ? stripe_percent : eway_percent
  end

  def stripe_percent
    case
    when credit_card.country_code.present? && credit_card.country_code.downcase != 'au' # international
      STRIPE_INTERNATIONAL_PERCENT
    else
      STRIPE_DOMESTIC_PERCENT
    end
  end

  def eway_percent
    case
    when credit_card.brand.present? && credit_card.brand == 'diners'
     EWAY_DINERS_PERCENT
    else
      EWAY_PERCENT
    end
  end

  def retrieve_surcharge_fee
    result.fee = credit_card.is_stripe_card? ? STRIPE_FEE : EWAY_FEE
  end

  class Result
    attr_accessor :percent, :fee

    def initialize
      @percent = 0
      @fee = 0
    end
  end

end
