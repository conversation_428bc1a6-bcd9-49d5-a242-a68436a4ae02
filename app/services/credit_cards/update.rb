class CreditCards::Update

  CARD_DETAILS = %i[number name expiry_month expiry_year cvv].freeze

  def initialize(credit_card:, customer:, card_params: {})
    @credit_card = credit_card
    @card_params = card_params
    @customer = customer
    @result = Result.new(customer: customer)
  end

  def call
    if can_update_card?
      if has_card_details?
        update_card_with_token if eway_card.present? && eway_card.try(:token).present?
      else
        update_card
      end
    end
    result
  end

private

  attr_reader :credit_card, :customer, :card_params, :result

  def can_update_card?
    case
    when credit_card.blank?
      result.errors << 'Cannot update a missing card'
    when customer.blank? || credit_card.customer_profiles.exclude?(customer)
      result.errors << 'You don\'t have permissions to change this card'
    end
    result.errors.blank?
  end

  def has_card_details?
    CARD_DETAILS.any?{|field| card_params[field].present? }
  end

  def eway_card
    return nil if result.errors.present?
    return @_eway_card if @_eway_card.present?

    eway_syncer = CreditCards::SyncWithEway.new(card_params: card_params, customer: customer).call
    result.errors += eway_syncer.errors if eway_syncer.errors.present?
    @_eway_card = eway_syncer
  end

  def eway_card_params
    display_number = eway_card.display_number.gsub('X', '#')
    {
      number: eway_card.display_number,
      label: "#{eway_card.brand} (#{display_number})",
      gateway_token: eway_card.token,
      cvv: nil, # do not save cvv
    }
  end

  def update_card_with_token
    sanitized_card_params = [card_params, eway_card_params].inject(&:merge)
    credit_card.update(sanitized_card_params)
    if !credit_card.save
      result.errors += credit_card.errors.full_messages
    end
    result.card = credit_card
  end

  def update_card
    sanitized_card_params = card_params.except(*CARD_DETAILS)
    card_updated = credit_card.update(sanitized_card_params)
    if card_updated
      revoke_older_nomination if is_nominated_card?
    else
      result.errors += credit_card.errors.full_messages
    end
    result.card = credit_card
  end

  def is_nominated_card?
    card_params.keys.map(&:to_sym).include?(:auto_pay_invoice) && card_params[:auto_pay_invoice]
  end

  def revoke_older_nomination
    other_nominated_cards = customer.credit_cards.where.not(id: credit_card.id).where(auto_pay_invoice: true)
    other_nominated_cards.each do |card|
      card.update(auto_pay_invoice: false)
    end
  end

  class Result
    attr_accessor :customer, :card, :errors

    def initialize(customer:)
      @customer = customer
      @card = nil
      @errors = []
    end

    def success?
      errors.blank? && card.present? && card.persisted?
    end
  end
end
