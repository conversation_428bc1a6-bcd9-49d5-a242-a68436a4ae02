class CreditCards::<PERSON><PERSON>

  def initialize(card_params: {}, customer: nil)
    @card_params = card_params
    @customer = customer
    @result = Result.new(customer: customer)
  end

  def call
    if eway_card.present? && eway_card.try(:token).present?
      build_new_card_with_token
    end
    result
  end

private

  attr_accessor :result
  attr_reader :customer, :card_params

  def eway_card
    return nil if result.errors.present?
    return @_eway_card if @_eway_card.present?

    eway_syncer = CreditCards::SyncWithEway.new(card_params: card_params, customer: customer).call
    result.errors += eway_syncer.errors if eway_syncer.errors.present?
    @_eway_card = eway_syncer
  end

  def build_new_card_with_token
    card = CreditCard.new(sanitized_card_params)
    card.number = eway_card.display_number
    card.label = "#{eway_card.brand} (#{eway_card.display_number.gsub('X', '#')})"
    card.gateway_token = eway_card.token
    if card.save
      customer.credit_cards << card if customer.present?
      result.card = card
    else
      result.card = card
      result.errors += card.errors.full_messages
    end
  end

  def sanitized_card_params
    card_params.except(:number, :cvv)
  end

  class Result
    attr_accessor :customer, :card, :errors

    def initialize(customer:)
      @customer = customer
      @card = nil
      @errors = []
    end

    def success?
      errors.blank? && card.present? && card.persisted?
    end
  end
end
