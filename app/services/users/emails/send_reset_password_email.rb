# Dispatches the reset-password email to the user
class Users::Emails::SendResetPasswordEmail < Notifications::Base

  EMAIL_TEMPLATE = 'user-lost_password'.freeze

  def initialize(user:, token:)
    @user = user
    @reset_token = token
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send reset password email to user #{user.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { user_id: user.id })
    end
  end

private

  attr_reader :user, :reset_token

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: user.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Reset password email sent to user #{user.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: Your password reset request'
  end

  def email_options
    {
      fk_id: user.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: user.firstname,
      reset_password_url: reset_password_url,
      header_color: profile_specific_header_color
    }
  end

  def profile_specific_header_color
    case
    when user.profile.present? && user.profile.profileable_type == 'CustomerProfile'
      :pink
    when user.profile.present? && user.profile.profileable_type == 'SupplierProfile'
      :cream
    else # admin
      :default
    end
  end

  def reset_password_url
    url_helper.reset_password_change_url(reset_password_token: reset_token, host: app_host)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{user.id}"
  end

end

