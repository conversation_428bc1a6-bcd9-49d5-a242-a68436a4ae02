# Dispatches the account confirmation email to the user
class Users::Emails::SendAccountConfirmationEmail < Notifications::Base

  EMAIL_TEMPLATE = 'user-confirm_account'.freeze

  def initialize(user:)
    @user = user
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send account confirmation email to user #{user.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { user_id: user.id })
    end
  end

private

  attr_reader :user, :reset_token

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Account confirmation email sent to user #{user.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: Confirm your email address'
  end

  def email_recipient
    user.unconfirmed_email || user.email_recipient
  end

  def email_options
    {
      fk_id: user.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: user.firstname,
      activation_url: account_activation_url,
      header_color: profile_specific_header_color,
    }
  end

  def profile_specific_header_color
    case
    when user.profile.present? && user.profile.profileable_type == 'CustomerProfile'
      :pink
    when user.profile.present? && user.profile.profileable_type == 'SupplierProfile'
      :cream
    else # admin
      :default
    end
  end

  def account_activation_url
    url_helper.user_confirmation_url(confirmation_token: user.confirmation_token, host: app_host)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{user.id}"
  end

end

