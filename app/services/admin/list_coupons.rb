class Admin::ListCoupons

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @coupons = base_coupons
    filter_by_query  if filter_options[:query].present?
    filter_by_coupon if filter_options[:coupon].present?
    order_coupons if filter_options[:order_by].present?
    paginate_coupons

    coupons.includes(includes).distinct
  end

private
  
  attr_reader :includes, :filter_options, :coupons

  def base_coupons
    Coupon.all
  end

  def filter_by_query
    coupon_arel = Coupon.arel_table
    code_condition = coupon_arel[:code].matches("#{filter_options[:query]}%")
    description_condition = coupon_arel[:description].matches("%#{filter_options[:query]}%")

    order_arel = Order.arel_table
    order_id_condition = order_arel[:id].in(filter_options[:query])

    customer_arel = CustomerProfile.arel_table
    customer_name_condition = customer_arel[:customer_name].matches("%#{filter_options[:query]}%")
    company_name_condition = customer_arel[:company_name].matches("%#{filter_options[:query]}%")

    company_arel = Company.arel_table
    company_condition = company_arel[:name].matches("%#{filter_options[:query]}%")

    query_condition = code_condition.or(description_condition).or(order_id_condition).or(customer_name_condition).or(company_name_condition).or(company_condition)
    @coupons = coupons.left_outer_joins(orders: { customer_profile: :company }).where(query_condition)
  end

  def filter_by_coupon
    if filter_options[:coupon].is_a?(Coupon)
      @coupons = coupons.where(id: filter_options[:coupon].id)
    else
      @coupons = Coupon.none
    end
  end

  def order_coupons
    @coupons = coupons.order(filter_options[:order_by])
  end

  def paginate_coupons
    @coupons = coupons.page(filter_options[:page]).per(filter_options[:limit])
  end

  def default_options
    {
      query: nil,
      coupon: nil,
      page: 1,
      limit: 20,      
      order_by: { created_at: :desc, id: :desc }
    }
  end

end