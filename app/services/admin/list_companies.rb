class Admin::ListCompanies

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @companies = base_companies
    filter_by_query  if filter_options[:query].present?
    filter_by_access
    filter_by_company if filter_options[:company].present?
    sort_companies if filter_options[:order_by].present?
    paginate_companies

    companies.includes(includes).distinct
  end

private
  
  attr_reader :includes, :filter_options, :companies

  def base_companies
    Company.all
  end

  def filter_by_query
    company_arel = Company.arel_table
    name_condition = company_arel[:name].matches("%#{filter_options[:query]}%")

    customer_arel = CustomerProfile.arel_table
    customer_name_condition = customer_arel[:customer_name].matches("%#{filter_options[:query]}%")

    @companies = companies.left_outer_joins(:customer_profiles).where(name_condition.or(customer_name_condition))
  end

  def filter_by_access
    case
    when filter_user.blank? # if no user is passed return none
      @companies = Company.none
    when (filter_user.admin? || filter_user.super_admin?) && !filter_options[:favourites_only]
      # do not fitler - admins get access to all companies
    when customer_ids.present?
      @companies = companies.joins(:customer_profiles).where(customer_profiles: { id: customer_ids })
    else # defaults to none
      @companies = Company.none
    end
  end

  def filter_by_company
    if filter_options[:company].is_a?(Company)
      @companies = companies.where(id: filter_options[:company].id)
    else
      @companies = Company.none
    end
  end

  def sort_companies
    @companies = companies.order(filter_options[:order_by])
  end

  def paginate_companies
    @companies = companies.page(filter_options[:page]).per(filter_options[:limit])
  end

  def customer_ids
    return [] if filter_user.blank?

    lister_options = { for_user: filter_user, favourites_only: filter_options[:favourites_only], limit: nil }
    customers = Admin::ListCustomers.new(options: lister_options).call.map(&:id)
  end

  def filter_user
    @_filter_user ||= filter_options[:for_user]
  end

  def default_options
    {
      for_user: nil,
      query: nil,
      company: nil,
      favourites_only: false,
      page: 1,
      limit: 20,      
      order_by: { updated_at: :desc, id: :desc }
    }
  end

end