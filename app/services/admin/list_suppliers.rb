class Admin::ListSuppliers

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @suppliers = base_suppliers
    filter_by_query if filter_options[:query].present?
    filter_by_access
    order_suppliers if filter_options[:order_by].present?
    paginate_suppliers

    suppliers.includes(includes)
  end

private

  attr_reader :includes, :filter_options, :suppliers

  def base_suppliers
    SupplierProfile.all
  end

  def filter_by_query
    supplier_arel = SupplierProfile.arel_table
    name_condition = supplier_arel[:company_name].matches("%#{filter_options[:query]}%")
    supplier_email_condition = supplier_arel[:email].matches("%#{filter_options[:query]}%")

    user_arel = User.arel_table
    user_email_condition = user_arel[:email].matches("%#{filter_options[:query]}%")

    query_condition = name_condition.or(supplier_email_condition).or(user_email_condition)
    @suppliers = suppliers.joins(:user).where(query_condition)
  end

  def order_suppliers
    @suppliers = suppliers.order(filter_options[:order_by])
  end

  def paginate_suppliers
    @suppliers = suppliers.page(filter_options[:page]).per(filter_options[:limit])
  end

  def filter_by_access
    user = filter_options[:for_user]

    case
    when user.blank? # if no user is passed return none
      @suppliers = SupplierProfile.none
    when user.admin? || user.super_admin?
      # do not fitler - admins get access to all suppliers
    when user.allow_all_supplier_access
      @suppliers = suppliers.joins(:user).where(users: { is_active: true })
    when user.can_access_suppliers && user.supplier_profiles.present?
      @suppliers = suppliers.where(id: user.supplier_profile_ids)
    else # defaults to none
      @suppliers = SupplierProfile.none
    end
  end

  def default_options
    {
      for_user: nil,
      query: nil,
      page: 1,
      limit: 20,
      order_by: { created_at: :desc, id: :desc }
    }
  end

end