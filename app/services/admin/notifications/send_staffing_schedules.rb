class Admin::Notifications::SendStaffingSchedules

  def initialize(time:)
    @time = time
    @result = Result.new
  end

  def call
    pantry_manager_spends.each do |pantry_manager_spend|
      pantry_manager = pantry_manager_spend.manager
      orders = pantry_manager_spend.orders
      next if pantry_manager.blank? || orders.blank?

      send_email_for(pantry_manager: pantry_manager, orders: orders)
    end
    result
  end

private

  attr_reader :time, :result

  def send_email_for(pantry_manager:, orders:)
    email_sender = Admin::Emails::SendStaffingScheduleEmail.new(pantry_manager: pantry_manager, orders: orders, week: notification_week).call
    if email_sender.success?
      result.notified_pantry_managers << pantry_manager
    else
      result.errors += email_sender.errors
    end
  end

  def pantry_manager_spends
    spend_options = {
      active_orders_only: true
    }.merge(notification_week)
    Admin::Reports::FetchPantryManagerSpends.new(options: spend_options).call
  end

  def notification_week
    next_week = time + 1.week
    {
      from_date: next_week.beginning_of_week,
      to_date: next_week.end_of_week
    }
  end

  class Result
    attr_accessor :notified_pantry_managers, :errors

    def initialize
      @notified_pantry_managers = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end