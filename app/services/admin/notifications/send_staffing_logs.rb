class Admin::Notifications::SendStaffingLogs

  def initialize(time:, type: 'all')
    @time = time
    @type = type
    @result = Result.new
  end

  def call
    if pantry_manager_spends.present?
      notify_accounts_team
      pantry_manager_spends.each do |pantry_manager_spend|
        notify_pantry_manager_for(pantry_manager_spend)
      end
    end
    result
  end

private

  attr_reader :time, :type, :result

  def notify_accounts_team
    return if ['all', 'accounts'].exclude?(type)

    email_sender = Admin::Emails::SendStaffingLogAccountsEmail.new(staffing_spends: pantry_manager_spends, fortnight: notification_fortnight).call
    if email_sender.success?
      result.notified_accounts_team = true
    else
      result.errors += email_sender.errors
    end
  end

  def notify_pantry_manager_for(pantry_manager_spend)
    return if ['all', 'pantry-managers'].exclude?(type)

    pantry_manager = pantry_manager_spend.manager
    email_sender = Admin::Emails::SendStaffingLogEmail.new(pantry_manager: pantry_manager, orders: pantry_manager_spend.orders, fortnight: notification_fortnight).call
    if email_sender.success?
      result.notified_pantry_managers << pantry_manager
    else
      result.errors += email_sender.errors
    end
  end

  def pantry_manager_spends
    @_pantry_manager_spends ||= begin
      spend_options = {
        active_orders_only: true
      }.merge(notification_fortnight)
      spends = Admin::Reports::FetchPantryManagerSpends.new(options: spend_options).call
      spends.select do |spend|
        spend.manager.present? && spend.orders.present?
      end
    end
  end

  def notification_fortnight
    last_week = time - 1.week
    {
      from_date: last_week.beginning_of_week,
      to_date: time.end_of_week
    }
  end

  class Result
    attr_accessor :notified_accounts_team, :notified_pantry_managers, :errors

    def initialize
      @notified_accounts_team = false
      @notified_pantry_managers = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end