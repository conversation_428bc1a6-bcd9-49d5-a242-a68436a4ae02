class Admin::Notifications::BroadcastCount

  NOTIFICATIONS_COUNT_CHANNEL = 'my_notifications'.freeze

  def initialize(user: nil)
    @user = user
    @result = Result.new
  end

  def call
    connected_users.each do |conection_user|
      next if conection_user.blank? || (user.present? && conection_user.id != user.id)

      unviewed_notifications = unviewed_notifications_for(conection_user: conection_user)
      ActionCable.server.broadcast("#{NOTIFICATIONS_COUNT_CHANNEL}_#{conection_user.id}", { count: unviewed_notifications.count })
      result.notifed_users << conection_user
    end
    result
  end

private

  attr_reader :user, :result

  def identifier_grouped_connections
   
    ActionCable.server.connections.group_by(&:connection_identifier)
  end

  def connected_users
    if Rails.env.development?
      ActionCable.server.connections.group_by(&:connection_identifier).map do |_, user_connections|
        user_connections.first.current_user
      end.compact
    else
      channels = Redis.new.pubsub('channels').select{|channel| channel.include?(NOTIFICATIONS_COUNT_CHANNEL) }.compact
      connection_user_ids = channels.map{|channel| channel[/\d+/] }.compact
      User.where(id: connection_user_ids).distinct
    end
  end

  def unviewed_notifications_for(conection_user:)
    has_favourite_customers = conection_user&.favourite_customers&.present?
    lister_options = { for_user: conection_user, favourites_only: has_favourite_customers, unviewed_only: true, limit: 1000 }
    Admin::ListNotifications.new(options: lister_options).call
  end

  class Result
    attr_accessor :notifed_users, :errors

    def initialize
      @notifed_users = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end