class Admin::Notifications::NotifyStripeEventViaSlack
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  def initialize(event:)
    @event = event
  end

  def call
    SlackNotifier.send(message, attachments: slack_attachments)
  end

private

  attr_reader :event

  def event_object
    @_event_object ||= event.data.object
  end

  def message
    case event.type
    when 'payment_intent.payment_failed'
      ':warning: STRIPE - Payment intent failed'
    when 'charge.failed'
      ':warning: STRIPE - A Charge failed'
    else
      ':warning: STRIPE - An event was triggered'
    end
  end

  def amount
    @_amount ||= event_object.amount.present? ? number_to_currency((event_object.amount / 100.0), precision: 2) : 'N/A'
  end

  def slack_attachments
    attachments = []
    case event.type
    when 'payment_intent.payment_failed'
      attachments << "Payment Intent ID: [#{event_object.id}](#{payment_url})"
      attachments << "Status: #{event_object.status}"

    when 'charge.failed'
      attachments << "Charge ID: [#{event_object.id}](#{payment_url})"
    else
      attachments << "Type: #{event.type}"
      attachments << "ID: #{event_object.id}"
    end

    attachments << "Amount: #{amount} #{event_object.currency}"
    attachments << "Description: #{event_object.description}"

    # add source
    if event_object.respond_to?(:source) && event_object.source.present?
      event_source = event_object.source.presence
      attachments << "Source: #{event_source.id} - #{event_source&.customer} last4- #{event_source&.last4}"
    end

    # add failure code
    if event_object.respond_to?(:failure_code) && event_object.failure_code && event_object.status == 'failed'
      attachments << "Failure: #{event_object.failure_code} - #{event_object.failure_message}"
    end

    attachments.map do |text|
      {
        type: 'mrkdwn',
        text: text,
        color: 'warning'
      }
    end
  end

  def payment_url
    "#{stripe_base_url}/payments/#{event_object.id}"
  end

  def stripe_base_url
    base_url = 'https://dashboard.stripe.com'
    base_url += '/test' if !Rails.env.production?
    base_url
  end

end
