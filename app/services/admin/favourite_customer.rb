class Admin::FavouriteCustomer

  def initialize(user:, customer:)
    @user = user
    @customer = customer
    @result = Result.new(customer: customer)
  end

  def call
    if can_favourite?
      existing_favourite_customers.include?(customer) ? unfavourite_customer : favourite_customer
    end
    result
  end

private
  
  attr_reader :user, :customer, :result

  def can_favourite?
    case
    when user.blank?
      result.errors << 'Cannot favourite customer without a user'
    when customer.blank?
      result.errors << 'Cannot favourite without a customer'
    when !can_access_customer?
      result.errors << 'You do not have access to this customer'
    end
    result.errors.blank?
  end

  def existing_favourite_customers
    @_existing_favourite_customers ||= user.favourite_customer_profiles
  end

  def unfavourite_customer
    new_favourites = existing_favourite_customers - [customer]
    if user.update(favourite_customer_profiles: new_favourites.uniq)
      result.favourite_mode = 'un-favourited'
    end
  end

  def favourite_customer
    new_favourites = existing_favourite_customers + [customer]
    if user.update(favourite_customer_profiles: new_favourites.uniq)
      result.favourite_mode = 'favourited'
    end
  end

  def can_access_customer?
    return true if user.super_admin? || user.admin?

    lister_options = { for_user: user, customer: customer }
    adminable_customer = ::Admin::ListCustomers.new(options: lister_options).call.first
    adminable_customer.present? && adminable_customer == customer
  end

  class Result
    attr_reader :customer
    attr_accessor :favourite_mode, :errors

    def initialize(customer:)
      @customer = customer
      @favourite_mode = nil
      @errors = []
    end

    def success?
      errors.blank? && customer.present? && favourite_mode.present?
    end
  end

end