class Admin::ListPromotions

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @promotions = base_promotions
    filter_by_promotion if filter_options[:promotion].present?
    order_promotions if filter_options[:order_by].present?
    paginate_promotions

    promotions.includes(includes).distinct
  end

private
  
  attr_reader :includes, :filter_options, :promotions

  def base_promotions
    Promotion.all
  end

  def filter_by_promotion
    if filter_options[:promotion].is_a?(Promotion)
      @promotions = promotions.where(id: filter_options[:promotion].id)
    else
      @promotions = Promotion.none
    end
  end

  def order_promotions
    @promotions = promotions.order(filter_options[:order_by])
  end

  def paginate_promotions
    @promotions = promotions.page(filter_options[:page]).per(filter_options[:limit])
  end

  def default_options
    {
      promotion: nil,
      page: 1,
      limit: 20,      
      order_by: { created_at: :desc, id: :desc }
    }
  end

end