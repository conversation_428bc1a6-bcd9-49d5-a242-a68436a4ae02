class Admin::DeprecateUser

  def initialize(user:, admin:)
    @user = user
    @admin = admin
    @result = Result.new(user: user)
  end

  def call
    if can_deprecate? && user.update(deprecate_user_attributes) && user.confirm
      result.user = user
    end
    result
  end

private

  attr_reader :user, :admin, :result

  def deprecate_user_attributes
    {
      email: user.email.sub('@', '@deprecated-'), # needs confirmation
      lastname: "#{user.lastname || ''}-deprecated",
      is_active: false,
    }
  end

  def can_deprecate?
    case
    when user.blank?
      result.errors << 'Cannot deprecate a missing user'
    when admin.blank? || !admin.super_admin?
      result.errors << 'Only super admins can deprecate users'
    when user.email.include?('deprecated') && user.lastname.include?('deprecated') && !user.is_active
      result.errors << 'User is already deprecated'
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :user, :errors

    def initialize(user:)
      @user = user
      @errors = []
    end

    def success?
      errors.blank? && user.present? && !user.is_active?
    end
  end

end
