class Admin::ListAdmins

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @admins = base_users
    filter_active if filter_options[:is_active].present?
    filter_by_kind
    filter_by_query if filter_options[:query].present?
    order_admins if filter_options[:order_by].present?
    paginate_admins if filter_options[:page].present? && filter_options[:limit].present?

    admins.includes(includes)
  end

private
  
  attr_reader :includes, :filter_options, :admins

  def base_users
    User.all
  end

  def filter_active
    @admins = admins.where(is_active: true)
  end

  def filter_by_kind
    @admins = case filter_options[:kind]
    when 'yordar_admin'
      admins.where('super_admin = :truthy OR admin = :truthy OR can_access_suppliers = :truthy', truthy: true)
    when 'team_admin'
      admins.joins(:customer_profile).where.not(customer_profiles: { id: nil }).where(customer_profiles: { team_admin: true })
    when 'company_team_admin'
      admins.joins(customer_profile: :admin_access_permissions).where.not(customer_profiles: { id: nil }).where(customer_profiles: { company_team_admin: true }).where(access_permissions: { active: true, scope: [nil, '', 'full_access', filter_options[:kind]] }).distinct
    when 'account_manager', 'pantry_manager'
      admins.joins(customer_profile: :admin_access_permissions).where.not(customer_profiles: { id: nil }).where(customer_profiles: { company_team_admin: true }).where(access_permissions: { active: true, scope: filter_options[:kind] }).distinct    
    else
      User.none
    end
  end

  def filter_by_query
    user_arel = User.arel_table
    full_name = Arel::Nodes::NamedFunction.new('concat', [
        user_arel[:firstname], 
        Arel::Nodes.build_quoted(' '), 
        user_arel[:lastname]
      ]
    )
    name_condition = full_name.matches("%#{filter_options[:query]}%")
    email_condition = user_arel[:email].matches("%#{filter_options[:query]}%")
    @admins = admins.where(name_condition.or(email_condition))
  end

  def order_admins
    @admins = admins.order(filter_options[:order_by])
  end

  def paginate_admins
    @admins = admins.page(filter_options[:page]).per(filter_options[:limit])
  end

  def default_options
    {
      is_active: true,
      kind: nil,
      query: '',
      order_by: 'firstname asc, lastname asc',
      page: 1,
      limit: 20,
    }
  end

end