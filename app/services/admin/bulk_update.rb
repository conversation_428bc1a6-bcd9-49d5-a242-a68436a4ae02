# used in Rails Admins bulk update action
class Admin::BulkUpdate

  def initialize(model_name:, bulk_ids:, update_params: {})
    @model_name = model_name
    @bulk_ids = bulk_ids
    @update_params = update_params.is_a?(Hash) ? ActionController::Parameters.new(update_params) : update_params
  end

  def call
    return if update_model.blank?

    items.each do |item|
      item_params = update_params[item.id.to_s].permit(update_model::BULK_UPDATE_FIELDS)
      item.update(item_params)
    end
  end

private

  attr_reader :model_name, :bulk_ids, :update_params

  def update_model
    @_model ||= model_name.classify.constantize rescue nil
  end

  def items
    @_items = update_model.where(id: bulk_ids)
  end

end
