class Admin::Cleanup::ScrambleUniqueIdentifiers < Admin::Cleanup::Base

  def call
    cleanup_customers
    cleanup_suppliers
    cleanup_invoices
    cleanup_team_order_attendees
    cleanup_team_order_packages
    cleanup_oauth_clients
  end

private

  def cleanup_customers
    customers = CustomerProfile.where.not(uuid: nil)
    puts "Customers: #{customers.size}"
    scramble_data_for(collection: customers, field: :uuid, type: 'c-')
  end

  def cleanup_suppliers
    suppliers = SupplierProfile.where.not(uuid: nil)
    puts "Suppliers: #{suppliers.size}"
    scramble_data_for(collection: suppliers, field: :uuid, type: 's-')
  end

  def cleanup_invoices
    invoices = Invoice.where.not(uuid: nil)
    puts "Invoices: #{invoices.size}"
    scramble_data_for(collection: invoices, field: :uuid, type: 'i-')
  end

  def cleanup_team_order_attendees
    attendees = TeamOrderAttendee.where.not(uniq_code: nil)
    puts "Attendees: #{attendees.size}"
    scramble_data_for(collection: attendees, field: :uniq_code, type: 'a-')
  end

  def cleanup_team_order_packages
    team_order_details = TeamOrder::Detail.where.not(package_id: nil)
    puts "Packages: #{team_order_details.size}"
    scramble_data_for(collection: team_order_details, field: :package_id, type: 't-')
  end

  def cleanup_oauth_clients
    clients = Doorkeeper::Application.all
    puts "Clients: #{clients.size}"
    scramble_data_for(collection: clients, field: :uid, type: 'oai-')
    scramble_data_for(collection: clients, field: :secret, type: 'oas-')
  end

  def scramble_data_for(collection:, field:, type:)
    collection.each do |record|
      record.update_column(field, SecureRandom.uuid)
      print type if verbose
    end
  end

end
