class Admin::Cleanup::ScrambleStripeData < Admin::Cleanup::Base

  def call
    cleanup_credit_cards
    cleanup_customer_profiles
    cleanup_order_charges
  end

private

  def cleanup_credit_cards
    credit_cards = CreditCard.all
    puts "CreditCards: #{credit_cards.size}"
    credit_cards.each_with_index do |credit_card, cidx|
      credit_card.update_columns(
        name: "Credit Card #{cidx + 1}",
        label: "Card (####-####-####-#{1000 + cidx})",
        number: "####-####-####-#{1000 + cidx}",
        brand: 'visa',
        expiry_year: (Time.zone.now + 2.years).year,
        expiry_month: 6,
        stripe_token: (credit_card.stripe_token.present? ? "pmx_#{SecureRandom.hex(10)}" : nil)
      )
      print 'c-' if verbose
    end
  end

  def cleanup_customer_profiles
    customers = CustomerProfile.where.not(stripe_token: nil)
    puts "Customers: #{customers.size}"
    customers.each do |customer|
      customer.update_columns(
        stripe_token: "cusx_#{SecureRandom.hex(10)}"
      )
      print 'cus-' if verbose
    end
  end

  def cleanup_order_charges
    order_charges = Order::Charge.all
    puts "OrderCharges: #{order_charges.size}" if verbose
    order_charges.each do |order_charge|
      order_charge.update_columns(
        stripe_token: "pix_#{SecureRandom.hex(10)}",
        refund_token: "rex_#{SecureRandom.hex(10)}"
      )
      print 'oc-' if verbose
    end
  end

end
