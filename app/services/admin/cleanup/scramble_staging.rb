class Admin::Cleanup::ScrambleStaging < Admin::Cleanup::Base

  def call
    scramble_emails
    scramble_stripe_data
    scramble_unique_identifiers
    cleanup_delayed_jobs
  end

private

  def scramble_emails
    puts 'Blanking out emails now!' if verbose
    Admin::Cleanup::ScrambleEmails.new(verbose: verbose).call
  end

  def scramble_stripe_data
    puts 'Cleaning up Stripe Data' if verbose
    Admin::Cleanup::ScrambleStripeData.new(verbose: verbose).call
  end

  def scramble_unique_identifiers
    puts 'Cleaning up Unique Identifiers' if verbose
    Admin::Cleanup::ScrambleUniqueIdentifiers.new(verbose: verbose).call
  end

  def cleanup_delayed_jobs
    puts 'Cleaning up Delayed Jobs' if verbose
    Admin::Cleanup::RemoveDelayedJobs.new(verbose: verbose).call
  end

end
