class Admin::Cleanup::ScrambleEmails < Admin::Cleanup::Base

  TEST_EMAIL = '<EMAIL>'.freeze

  def call
    scramble_user_and_lead_emails
    scramble_billing_detail_emails
    scramble_supplier_emails
    scramble_company_emails
    scramble_event_attendee_emails
    scramble_order_contact_emails
    scramble_order_lead_emails
    scramble_notification_preferences
    scramble_woolworths_accounts
  end

private

  def scramble_user_and_lead_emails
    users = User.where.not(email: [nil, '']).where.not('email like ? or email like ? or email like ?', '%@yordar.com.au', '@empireone.com.au', '@woollymammoth.co').includes(:leads)
    puts "Users: #{users.size}"
    users.each do |user|
      email = TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5))
      begin
        user.update_column(:email, email)
        if user.secondary_email.present?
          user.update_column(:secondary_email, nil)
        end
        if user.leads.present?
          user.leads.each do |l|
            l.update_column(:email, email)
          end
        end
      rescue
        email = TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5))
        user.update_column(:email, email)
        if user.leads.present?
          user.leads.each do |l|
            l.update_column(:email, email)
          end
        end
      end
      print '-u' if verbose
    end
  end

  def scramble_billing_detail_emails
    billing_details = BillingDetails.all
    puts "Billing Details: #{billing_details.size}"
    billing_details.each do |billing_detail|
      billing_detail.update_column(:email, TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5)))
      print '-b' if verbose
    end
  end

  def scramble_supplier_emails
    supplier_profiles = SupplierProfile.where.not(email: [nil, ''])
    puts "Suppliers: #{supplier_profiles.size}"
    supplier_profiles.each do |supplier|
      supplier.update_column(:email, TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5)))
      print '-s' if verbose
    end
  end

  def scramble_company_emails
    companies = Company.where.not(email: [nil, ''])
    puts "Companies: #{companies.size}"
    companies.each do |company|
      company.update_column(:email, TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5)))
      print '-c' if verbose
    end
  end

  def scramble_event_attendee_emails
    event_attendees = EventAttendee.where.not(email: [nil, ''])
    puts "Attendees: #{event_attendees.size}"
    event_attendees.each do |event_attendee|
      event_attendee.update_column(:email, TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5)))
      print '-a' if verbose
    end
  end

  def scramble_order_contact_emails
    orders = Order.where.not(contact_email: [nil, ''])
    puts "Order: #{orders.size}"
    orders.each do |order|
      order.update_column(:contact_email, TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5)))
      print '-o' if verbose
    end
  end

  def scramble_order_lead_emails
    leads = Lead.where.not(email: [nil, ''])
    puts "Leads: #{leads.size}"
    leads.each do |lead|
      lead.update_column(:email, TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5)))
      print '-l' if verbose
    end
  end

  def scramble_notification_preferences
    preferences = Notification::Preference.where.not(email_recipients: [nil, ''])
    puts "Preferences: #{preferences.size}"
    preferences.each do |preference|
      preference.update_column(:email_recipients, TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5)))
      print '-p' if verbose
    end
  end

  def scramble_woolworths_accounts
    accounts = Woolworths::Account.all
    puts "Woolworths Accounts: #{accounts.size}"
    accounts.each do |account|
      account.update_columns(
        email: TEST_EMAIL.gsub(/-regex-/, SecureRandom.uuid.first(5)),
        password: SecureRandom.hex(7)
      )
      print '-wa' if verbose
    end
  end

end
