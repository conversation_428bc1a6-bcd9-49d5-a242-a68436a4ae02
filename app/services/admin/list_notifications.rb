class Admin::ListNotifications

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @notifications = base_notifications
    filter_by_scopable_access
    filter_by_event_type
    filter_by_views if filter_options[:unviewed_only].present?
    filter_by_severity if filter_options[:severity].present?
    sort_notifications if filter_options[:order_by].present?
    paginate_notifications if filter_options[:page].present? && filter_options[:limit].present?

    notifications.distinct.includes(includes)
  end

private

  attr_reader :includes, :filter_options, :notifications

  def base_notifications
    EventLog.all
  end

  def filter_by_event_type
    case
    when filter_options[:event_type].present?
      @notifications = notifications.where(event: filter_options[:event_type])
    when filter_user.present? && filter_user.super_admin? && filter_options[:show_all_events].present?
      # do nothing
    else
      @notifications = EventLogs::FilterByUser.new(user: filter_user, existing_logs: notifications).call
    end
  end

  def filter_by_views
    if filter_user
      viewed_notification_ids = filter_user.event_log_views.select(:event_log_id)
      @notifications = notifications.left_outer_joins(:views).where.not(id: viewed_notification_ids)
    else
      @notifications = EventLog.none
    end
  end

  def filter_by_severity
    @notifications = notifications.where(severity: filter_options[:severity])
  end

  def filter_by_scopable_access
    return if filter_user.present? && (filter_user.admin? || filter_user.super_admin? || filter_user.can_access_suppliers?) && !filter_options[:favourites_only] && filter_options[:query].blank?

    @notifications = case
    when filter_user.blank? # if no user is passed return none
      EventLog.none
    when (filter_user.admin? || filter_user.super_admin?) && filter_options[:favourites_only] && customer_ids.blank?
      # do nothing
      notifications
    when (filter_user.admin? || filter_user.super_admin?) && filter_options[:favourites_only] && customer_ids.present?
      log_arel = EventLog.arel_table
      customer_type_scope = log_arel[:scopable_type].eq('CustomerProfile')
      customer_id_scope = log_arel[:scopable_id].in(customer_ids)
      customer_scoped = customer_type_scope.and(customer_id_scope)
      un_scoped = log_arel[:scopable_type].eq(nil)

      notifications.where(customer_scoped.or(un_scoped))
    when customer_ids.present?
      notifications.where(scopable_type: 'CustomerProfile', scopable_id: customer_ids)
    else # defaults to none
      EventLog.none
    end
  end

  def sort_notifications
    @notifications = notifications.order(filter_options[:order_by])
  end

  def paginate_notifications
    @notifications = notifications.page(filter_options[:page]).per(filter_options[:limit])
  end

  def filter_user
    @_filter_user ||= filter_options[:for_user]
  end

  def customer_ids
    return [] if filter_user.blank?

    @_customer_ids ||= begin
      lister_options = { for_user: filter_user, query: filter_options[:query], favourites_only: filter_options[:favourites_only], limit: nil }
      Admin::ListCustomers.new(options: lister_options).call.map(&:id)
    end
  end

  def default_options
    {
      for_user: nil,
      event_type: nil,
      query: nil,
      favourites_only: false,
      show_all_events: false,
      unviewed_only: false,
      page: 1,
      limit: 20,
      order_by: { created_at: :desc, id: :desc }
    }
  end

end
