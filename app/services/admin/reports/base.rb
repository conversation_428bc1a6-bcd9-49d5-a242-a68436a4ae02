class Admin::Reports::Base

  def initialize(options: {}, kind: nil)
    @options = sanitized_options(options)
    @report_kind = kind
    @report_data = []
  end

private

  attr_reader :options, :report_kind, :report_data

  def sanitized_options(input_options)
    sanitized = input_options.to_h
    if sanitized[:company_id].blank?
      sanitized = sanitized.except(:company_id)
    elsif sanitized[:company_id] == 'default'
      sanitized[:company_id] = nil
    end
    sanitized
  end

  def from
    @_from ||= options[:from].present? && DateTime.parse(options[:from])
  end

  def to
    @_to ||= options[:to].present? ? DateTime.parse(options[:to]) : from.end_of_day
  end

  def order_name
    @_order_name ||= options[:order_name].present? ? options[:order_name] : nil
  end

  def gst_free_hash(order_hash_gst)
    order_hash = {}
    order_hash_gst.each do |key, value|
      # group returns false as 0 in mysql and f in postgres...
      if key.last == 0 || key.last == 'f'
        value *= (1 + yordar_credentials(:yordar, :gst_percent, :au).to_f) # TO-DO change for NZ orders
      end
      order_hash[key[0...-1]] = (order_hash[key[0...-1]] || 0) + value.to_f
    end
    order_hash
  end

  def render_to_csv
    return '' if report_data.blank?

    CSV.generate do |csv|
      csv << headers
      report_data.each do |f|
        csv << f
      end
    end
  end

end