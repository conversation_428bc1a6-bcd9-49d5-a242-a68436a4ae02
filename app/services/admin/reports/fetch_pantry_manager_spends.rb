class Admin::Reports::FetchPantryManagerSpends

  VALID_STAFFING_ORDER_STATUSES = %w[new amended confirmed pending delivered cancelled skipped].freeze
  VALID_ACTIVE_ORDER_STATUSES = %w[new amended confirmed pending delivered].freeze

  def initialize(options: {})
    @report_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
    @spends = []
  end

  def call
    pantry_managers.each do |pantry_manager|
      pantry_orders = manager_grouped_pantry_orders[pantry_manager] || []
      spends << PantryManager.new(manager: pantry_manager, orders: pantry_orders)
    end
    if unasissgned_pantry_orders.present?
      spends << PantryManager.new(manager: nil, orders: unasissgned_pantry_orders)
    end
    spends
  end

private

  attr_reader :report_options, :spends

  def pantry_managers
    @_pantry_managers ||= begin
      lister_options = {
        page: 1,
        limit: nil,
        kind: 'pantry_manager'
      }
      ::Admin::ListAdmins.new(options: lister_options).call.map(&:customer_profile)
    end
  end

  def manager_grouped_pantry_orders
    @_manager_grouped_pantry_orders ||= pantry_orders.group_by(&:pantry_manager)
  end

  def pantry_orders
    lister_options = {
      from_date: report_options[:from_date],
      to_date: report_options[:to_date],
      supplier_ids: [yordar_credentials(:yordar, :staffing_supplier_id)],
      statuses: report_options[:active_orders_only] ? VALID_ACTIVE_ORDER_STATUSES : VALID_STAFFING_ORDER_STATUSES,
      order_by: { delivery_at: :asc, id: :asc }
    }
    Orders::List.new(options: lister_options, includes: %i[customer_profile pantry_manager order_lines]).call
  end

  def unasissgned_pantry_orders
    @_unasissgned_pantry_orders ||= (manager_grouped_pantry_orders[nil] || []).reject{|order| VALID_ACTIVE_ORDER_STATUSES.exclude?(order.status) }
  end

  def default_options
    {
      from_date: Time.zone.now.beginning_of_week,
      to_date: Time.zone.now.end_of_week,
      active_orders_only: false, 
    }
  end

  class PantryManager
    attr_accessor :manager, :orders

    def initialize(manager:, orders:)
      @manager = manager
      @orders = orders
    end

    def customer_grouped_active_orders
      orders.reject do |order|
        VALID_ACTIVE_ORDER_STATUSES.exclude?(order.status)
      end.group_by(&:customer_profile)
    end

    def customer_spends
      @_customer_spends ||= begin
        customer_grouped_active_orders.map do |customer, customer_orders|
          CustomerSpend.new(customer: customer, orders: customer_orders)
        end
      end
    end

    def hours
      @_hours ||= customer_spends.sum(&:hours)
    end
  end

  class CustomerSpend
    attr_reader :customer, :orders

    def initialize(customer:, orders:)
      @customer = customer
      @orders = orders
    end

    def item_spends
      @_item_spends ||= begin
        staffing_order_lines.group_by(&:menu_item).map do |item, item_lines|
          ItemSpend.new(item: item, quantity: item_lines.sum(&:quantity))
        end
      end
    end

    def staffing_order_lines
      orders.map do |order|
        order.order_lines.select{|order_line| order_line.supplier_profile_id == yordar_credentials(:yordar, :staffing_supplier_id) }
      end.flatten(1)
    end

    def hours
      @_hours ||= item_spends.sum(&:hours)
    end
  end

  class OrderSpend
    attr_reader :order

    def initialize(order:)
      @order = order
    end

    def item_spends
      @_item_spends ||= begin
        staffing_order_lines.group_by(&:menu_item).map do |item, item_lines|
          ItemSpend.new(item: item, quantity: item_lines.sum(&:quantity))
        end
      end
    end

    def staffing_order_lines
      order.order_lines.select{|order_line| order_line.supplier_profile_id == yordar_credentials(:yordar, :staffing_supplier_id) }
    end

    def hours
      @_hours ||= item_spends.sum(&:hours)
    end
  end

  class ItemSpend
    HOURS_MAP = {
      'weekly': 37.5,
      'full time': 37.5,
      'half hour': 0.5,
      '30 min': 0.5,
    }.freeze
    attr_reader :item, :quantity

    def initialize(item:, quantity:)
      @item = item
      @quantity = quantity
    end

    def item_hours
      HOURS_MAP.map do |key, hours|
        item.name.match(/#{key}/i).present? ? hours : nil
      end.compact.first || 1
    end

    def hours
      @_hours ||= item_hours * quantity
    end
  end

end