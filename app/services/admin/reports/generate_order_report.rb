class Admin::Reports::GenerateOrderReport < Admin::Reports::Base

  def call
    return if from.blank?

    generate_data
    render_to_csv
  end

private

  def orders
    @_orders ||= begin
      orders = Order.where(status: %w[new amended confirmed delivered])
      orders = orders.where(delivery_at: from.beginning_of_day.utc..to.end_of_day.utc)
      orders = orders.joins(order_lines: :supplier_profile, customer_profile: :user)

      if order_name.present?
        orders = orders.where('orders.name LIKE ?', "%#{order_name}%")
      end

      if options[:customer_profile_id].present?
        orders = orders.where(customer_profile_id: options[:customer_profile_id])
      end

      if options[:supplier_profile_id].present?
        orders = orders.where(supplier_profiles: { id: options[:supplier_profile_id] })
      end

      if options.key?(:company_id)
        orders = orders.where(customer_profiles: { company_id:  options[:company_id] })
      end

      orders
    end
  end

  def with_filtered_data?
    options[:customer_profile_id].present? || options[:supplier_profile_id].present? || options[:company_id].present?
  end

  def generate_data
    @report_data = case
    when report_kind == 'product'
      generate_product_data
    when with_filtered_data?
      generate_filtered_data
    else
      generate_order_based_data
    end
  end

  def headers
    case
    when report_kind == 'product'
      ['Supplier', 'Item name', 'Qty', 'Item price $', 'Item cost $', 'Total price $', 'Total cost $', 'GST free']
    when with_filtered_data?
      ['Delivery Date', 'Customer', 'Order #', 'Order name', 'Number of people', 'Inv. no.', 'Supplier', 'Item name', 'Qty', 'Item price $', 'Item cost $', 'Total price $', 'Total cost $', 'GST free']
    else
      ['Delivery date', 'Customer', 'Order #', 'Order name', 'Number of people', 'Order delivery fee', 'Order surcharge', 'Inv. no.', 'Supplier', 'Total price $', 'Total cost $']
    end
  end

  def generate_product_data
    grouped_orders = orders.group('order_lines.name', 'order_lines.price', 'order_lines.cost', 'order_lines.is_gst_free', 'supplier_profiles.company_name')

    order_hash = grouped_orders.sum('order_lines.quantity').sort_by do |_, quantity|
      -quantity
    end

    get_csv_hash(order_hash)
  end

  def generate_filtered_data
    grouped_orders = orders.joins('LEFT JOIN invoices ON invoices.id = orders.invoice_id')
    grouped_orders = grouped_orders.group('orders.delivery_at', "CONCAT(users.firstname, ' ', users.lastname)", 'orders.number_of_people', 'orders.id', 'orders.name', 'invoices.number', 'invoices.created_at')
    grouped_orders = grouped_orders.group('supplier_profiles.id', 'supplier_profiles.company_name')
    grouped_orders = grouped_orders.group('order_lines.id', 'order_lines.name', 'order_lines.quantity', 'order_lines.price', 'order_lines.cost', 'order_lines.is_gst_free')

    order_hash = grouped_orders.count

    get_csv_hash(order_hash)
  end

  def generate_order_based_data
    grouped_orders = orders.joins('LEFT JOIN invoices ON invoices.id = orders.invoice_id')
    grouped_orders = grouped_orders.group('orders.delivery_at', "CONCAT(users.firstname, ' ', users.lastname)", 'orders.number_of_people', 'orders.id', 'orders.name', 'orders.customer_delivery', 'orders.customer_surcharge', 'invoices.number', 'invoices.created_at')
    grouped_orders = grouped_orders.group('supplier_profiles.id', 'supplier_profiles.company_name')
    grouped_orders = grouped_orders.group('order_lines.is_gst_free')

    order_hash_price_gst = grouped_orders.sum('order_lines.price * order_lines.quantity')
    order_hash_price = gst_free_hash(order_hash_price_gst)

    # get the grouped orderlines' cost (add the non_gst_free grouped orderlines costs with gst_free grouped orderlines costs)
    order_hash_cost_gst = grouped_orders.sum('order_lines.cost * order_lines.quantity')
    order_hash_cost = gst_free_hash(order_hash_cost_gst)

    # merge prices and cost into an array
    # order_hash result format: ['delivery_at', 'customer', 'order_id', 'order_name', 'invoice_number', 'invoice_date', 'supplier_id', 'supplier_name'] => ['grouped_orderlines_price', 'grouped_orderlines.cost']
    order_hash = {}
    order_hash_price.each{|key, _| order_hash[key] = [order_hash_price[key]] << order_hash_cost[key]}

    get_csv_hash(order_hash)
  end

  # based on the hash data, filled up the columns separately
  def get_csv_hash(order_hash)
    rows = []
    order_hash.each do |key, value|
      case
      when report_kind == 'product'
        template_row = [key[4]] + [key[0]] + [value] + [key[1]] + [key[2]] + [number_with_precision(key[1], precision: 2).to_f * value] + [number_with_precision(key[2], precision: 2).to_f * value] + [key[3] == 1 || key[3] == 't' ? 'Yes' : 'No']
      when with_filtered_data?
        template_row = [key[0].to_s(:date)] + [key[1]] + [key[3]] + [key[4]] + [key[2]] + [key[5] || 'Not Invoiced'] + [key[8]] + [key[10]] + [key[11]] + [number_with_precision(key[12], precision: 2)] + [number_with_precision(key[13], precision: 2)] + [number_with_precision(key[12], precision: 2).to_f * key[11]] + [number_with_precision(key[13], precision: 2).to_f * key[11]] + [key[14] == 1 || key[14] == 't' ? 'Yes' : 'No']
      else
        template_row = [key[0].to_s(:date)] + [key[1]] + [key[3]] + [key[4]] + [key[2]] + [number_with_precision(key[5], precision: 2)] + [number_with_precision(key[6], precision: 2)] + [key[7] || 'Not Invoiced'] + [key[10]] + [number_with_precision(value[0], precision: 2)] + [number_with_precision(value[1], precision: 2)]
      end
      rows << template_row
    end
    rows
  end

end
