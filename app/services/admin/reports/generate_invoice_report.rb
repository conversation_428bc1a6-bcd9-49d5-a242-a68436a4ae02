class Admin::Reports::GenerateInvoiceReport < Admin::Reports::Base

  def call
    return if from.blank?

    generate_data
    render_to_csv
  end

private

  def orders
    @_orders ||= begin
      orders = Order.joins(:invoice)
      orders = orders.where(invoices: { created_at: from.beginning_of_day.utc..to.end_of_day.utc })
      orders = orders.joins(order_lines: :supplier_profile, customer_profile: :user)

      if order_name.present?
        orders = orders.where('orders.name LIKE ?', "%#{order_name}%")
      end

      if options[:customer_profile_id].present?
        orders = orders.where(customer_profile_id: options[:customer_profile_id])
      end

      if options[:supplier_profile_id].present?
        orders = orders.where(supplier_profiles: { id: options[:supplier_profile_id] })
      end

      if options.key?(:company_id)
        orders = orders.where(customer_profiles: { company_id: options[:company_id] })
      end

      orders
    end
  end

  def with_filtered_data?
    options[:customer_profile_id].present? || options[:supplier_profile_id].present? || options[:company_id].present?
  end

  def generate_data
    @report_data = case
    when with_filtered_data?
      generate_filtered_data
    else
      generate_invoice_data
    end
  end

  def headers
    case
    when with_filtered_data?
      ['Inv. no.', 'Inv. date', 'Customer', 'Order #', 'Order name', 'Number of people', 'Supplier', 'Item name', 'Qty', 'Item price $', 'Item cost $', 'Total price $', 'Total cost $', 'GST free']
    else
      ['Inv. no.', 'Inv. date', 'Customer', 'Order #', 'Order name', 'Number of people', 'Order delivery fee', 'Order surcharge', 'Supplier', 'Total price $', 'Total cost $']
    end
  end

  def generate_filtered_data
    grouped_orders = orders.group('orders.delivery_at', 'CONCAT(users.firstname, \' \', users.lastname)', 'orders.number_of_people', 'orders.id', 'orders.name', 'invoices.number', 'invoices.created_at')
    grouped_orders = grouped_orders.group('supplier_profiles.id', 'supplier_profiles.company_name')
    grouped_orders = grouped_orders.group('order_lines.id', 'order_lines.name', 'order_lines.quantity', 'order_lines.price', 'order_lines.cost', 'order_lines.is_gst_free')

    invoice_hash = grouped_orders.count

    get_csv_hash(invoice_hash)
  end

  def generate_invoice_data
    # since for invoice report, the data we are fetching is quite similar to the order one, we just reuse the order scope
    grouped_orders = orders.group('orders.delivery_at', 'CONCAT(users.firstname, \' \', users.lastname)', 'orders.number_of_people', 'orders.id', 'orders.name', 'orders.customer_delivery', 'orders.customer_surcharge', 'invoices.number', 'invoices.created_at')
    grouped_orders = grouped_orders.group('supplier_profiles.id', 'supplier_profiles.company_name')
    grouped_orders = grouped_orders.group('order_lines.is_gst_free')

    invoice_hash_price_gst = grouped_orders.sum('order_lines.price * order_lines.quantity')
    invoice_hash_price = gst_free_hash(invoice_hash_price_gst)

    invoice_hash_cost_gst = grouped_orders.sum('order_lines.cost * order_lines.quantity')
    invoice_hash_cost = gst_free_hash(invoice_hash_cost_gst)

    invoice_hash = {}
    invoice_hash_price.each{|key, _| invoice_hash[key] = [invoice_hash_price[key]] << invoice_hash_cost[key]}

    get_csv_hash(invoice_hash)
  end

  # based on the hash data, filled up the columns separately
  def get_csv_hash(invoice_hash)
    rows = []
    invoice_hash.each do |key, value|
      if with_filtered_data?
        template_row = [key[5]] + [key[6].to_s(:date)] + [key[1]] + [key[3]] + [key[4]] + [key[2]] + [key[10]] + [key[8]] + [key[11]] + [number_with_precision(key[12], precision: 2)] + [number_with_precision(key[13], precision: 2)] + [number_with_precision(key[12], precision: 2).to_f * key[11]] + [number_with_precision(key[13], precision: 2).to_f * key[11]] + [key[14] == 1 || key[14] == 't' ? 'Yes' : 'No']
      else
        template_row = [key[7]] + [key[8].to_s(:date)] + [key[1]] + [key[3]] + [key[4]] + [key[2]] + [number_with_precision(key[5], precision: 2)] + [number_with_precision(key[6], precision: 2)] + [key[10]] + [number_with_precision(value[0], precision: 2)] + [number_with_precision(value[1], precision: 2)]
      end
      rows << template_row
    end
    rows
  end

end

