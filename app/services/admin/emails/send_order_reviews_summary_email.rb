class Admin::Emails::SendOrderReviewsSummaryEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-order_review_summary'.freeze

  def initialize(time:, reviews: [])
    @time = time
    @reviews = reviews
  end

  def call
    return if email_already_sent? || recent_order_reviews.blank?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send Order Reviews summary sent to Yordar Admin #{email_recipients} - #{unique_identifier}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { identifier: unique_identifier })
    end
  end

private

  attr_reader :time, :reviews

  def email_already_sent?
    Email.where(fk_id: unique_identifier, ref: email_ref).present?
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Order Reviews summary sent to Yordar Admin #{email_recipients} - #{unique_identifier}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :orders_email)
  end

  def email_subject
    'YORDAR: Weekly order review summary'
  end

  def email_options
    {
      fk_id: unique_identifier,
      ref: email_ref,
    }
  end

  def email_variables
    {
      week_start: time.beginning_of_week.to_s(:date_verbose),
      week_end: time.end_of_week.to_s(:date_verbose),
      supplier_reviews: deep_struct(suppliers_review_data)
    }
  end

  def recent_order_reviews
    return reviews if reviews.present?

    @_recent_order_reviews ||= OrderReview.where(created_at: (time.beginning_of_week...time.end_of_week)).includes(:order, :supplier_profile)
  end

  def suppliers_review_data
    recent_order_reviews.group_by(&:supplier_profile).map do |supplier, supplier_order_reviews|
      {
        id: supplier.id,
        name: supplier.name,
        image: cloudinary_image(supplier.profile.avatar),
        reviews: order_reviews_data(supplier_order_reviews)
      }
    end
  end

  def order_reviews_data(supplier_order_reviews)
    comment_sorted_reviews = supplier_order_reviews.sort_by{|review| [(review.comment.present? ? 99 : 1), review.created_at] } # reviews with comments go at the bottom
    comment_sorted_reviews.map do |order_review|
      {
        order_number: order_review.order.id,
        order_date: order_review.order.delivery_at.to_s(:date_verbose),
        review_date: order_review.created_at.to_s(:date_verbose),

        food_taste: order_review.food_taste_score,
        presentation: order_review.presentation_score,
        delivery_punctuality: order_review.delivery_punctuality_score,
        comment: order_review.comment,
      }
    end
  end

  def unique_identifier
    time.to_s(:year_week).to_i
  end

  def email_ref
    EMAIL_TEMPLATE
  end
end

