class Admin::Emails::SendMenuItemAmendedEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'yordar-menu_item_amended'.freeze

  def initialize(menu_item:, serving_size: nil, updated_orders: [])
    @menu_item = menu_item
    @serving_size = serving_size
    @updated_orders = updated_orders
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send menu item amended orders email to admin - #{menu_item.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { menu_item_id: menu_item.id, serving_size_id: serving_size.try(:id), updated_orders: updated_orders })
    end
  end

private

  attr_reader :menu_item, :serving_size, :updated_orders

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Menu Item amended orders email sent to admin - #{menu_item.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_subject
    "YORDAR: #{amended_item_name} from #{supplier.name} was recently updated"
  end

  def email_options
    {
      fk_id: 0,
      ref: email_ref,
    }
  end

  def email_variables
    {
      item: deep_struct(item_data),
      supplier: deep_struct(supplier_data),
      updated_orders: updated_orders,
    }
  end

  def item_data
    price_item = serving_size.present? ? serving_size : menu_item
    new_price = price_item.price
    new_cost = new_price.present? ? (new_price * (1 - (supplier.commission_rate / 100))) : nil
    {
      name: amended_item_name,
      image: cloudinary_image(menu_item.image),
      new_price: number_to_currency(new_price, precision: 2) || 'N/A',
      new_cost: number_to_currency(new_cost, precision: 2) || 'N/A'
    }
  end

  def supplier_data
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def amended_item_name
    return @_amended_item_name if @_amended_item_name.present?

    item_name = menu_item.name
    item_name += " - #{serving_size.name}" if serving_size.present?
    @_amended_item_name = item_name
  end

  def supplier
    @_supplier ||= menu_item.supplier_profile
  end

  def email_ref
    EMAIL_TEMPLATE
  end

end


