class Admin::Emails::SendRateCardAmendedEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'yordar-rate_card_amended'.freeze

  def initialize(rate_card:, updated_orders: [])
    @rate_card = rate_card
    @updated_orders = updated_orders
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send rate card amended orders email to admin - #{rate_card.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { rate_card_id: rate_card.id, updated_orders: updated_orders })
    end
  end

private

  attr_reader :rate_card, :updated_orders

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Rate card amended orders email sent to admin - #{rate_card.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_subject
    "YORDAR: Rate card ##{rate_card.id} from `#{supplier.name}` for `#{rate_card.company.name}` was recently updated"
  end

  def email_options
    {
      fk_id: 0,
      ref: email_ref,
    }
  end

  def email_variables
    {
      rate_card: deep_struct(rate_card_data),
      item: deep_struct(item_data),
      supplier: deep_struct(supplier_data),
      updated_orders: updated_orders,
    }
  end

  def rate_card_data
    new_price = rate_card.persisted? ? rate_card.price : nil
    new_cost = rate_card.persisted? ? rate_card.cost : nil
    {
      id: rate_card.id,
      company_name: rate_card.company.name,
      new_price: number_to_currency(new_price, precision: 2) || 'N/A',
      new_cost: number_to_currency(new_cost, precision: 2) || 'N/A'
    }
  end

  def item_data
    menu_item = rate_card.menu_item
    item_name = menu_item.name
    item_name += " - #{rate_card.serving_size.name}" if rate_card.serving_size.present?
    {
      name: item_name,
      image: cloudinary_image(menu_item.image)
    }
  end

  def supplier_data
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar),
    }
  end

  def supplier
    @_supplier ||= rate_card.menu_item.supplier_profile
  end

  def email_ref
    EMAIL_TEMPLATE
  end

end

