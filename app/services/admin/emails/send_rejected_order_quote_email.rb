class Admin::Emails::SendRejectedOrderQuoteEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'admin-rejected_order_quote'.freeze

  def initialize(order:)
    @order = order
    @attachments = []
  end

  def call
    begin
      attach_quote_document
      send_email
    rescue => exception
      error_message = 'Failed to send rejected order quote email to admin'
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order: order.id, version: latest_quote_document&.version })
    end
  end

private

  attr_reader :order, :attachments

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info 'Sent rejected order quote email to admin'
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :admin_email)
  end

  def email_subject
    "YORDAR: Quote Order ##{order.id} was rejected"
  end

  def email_cc
    yordar_credentials(:yordar, :developer_email)
  end

  def email_options
    {
      fk_id: 1,
      ref: email_ref,
    }
  end

  def email_variables
    {
      order: deep_struct(order_data),
      customer: deep_struct(customer_data)
    }
  end

  def order_data
    {
      id: order.id,
      name: order.name,
      delivery_at: order.delivery_at.to_s(:full_verbose),
      link: url_helper.order_show_url(order, host: app_host),
      quote_pdf_url: latest_quote_document&.url,
    }
  end

  def customer_data
    customer = order.customer_profile
    {
      name: customer.name,
      company: (customer.company&.name.presence || customer.company_name),
      email: (order.contact_email.presence || customer.user.email),
      phone: order.phone,
    }
  end

  def attach_quote_document
    return if latest_quote_document.blank?

    @attachments << latest_quote_document
  end

  def latest_quote_document
    @_latest_quote_document ||= order.documents.where(kind: 'customer_order_quote').order(version: :desc).first
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{Time.zone.now.to_s(:date_spreadsheet)}"
  end

end
