class Admin::Emails::SendRakeTaskErrorsEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-rake_task_errors'.freeze

  def initialize(errors:)
    @errors = errors
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = 'Failed to send daily rake task errors email to admin'
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { errors: errors })
    end
  end

private

  attr_reader :errors

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info 'Sent daily rake task errors email to admin'
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :developer_email)
  end

  def email_subject
    'YORDAR: Rake task errors'
  end

  def email_options
    {
      fk_id: 1,
      ref: email_ref,
    }
  end

  def email_variables
    {
      errors: deep_struct(errors),
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{Time.zone.now.to_s(:date_spreadsheet)}"
  end

end
