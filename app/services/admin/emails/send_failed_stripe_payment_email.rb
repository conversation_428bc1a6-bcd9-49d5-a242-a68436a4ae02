class Admin::Emails::SendFailedStripePaymentEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'yordar-failed_stripe_payment'.freeze

  def initialize(event:)
    @event = event
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = 'Failed to send failed Stripe payment notification to admin'
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { event: event })
    end
  end

private

  attr_reader :event

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info 'Sent failed Stripe payment notification to admin'
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_subject
    error_date = Time.zone.now.to_s(:date)
    "YORDAR: Stripe Payment Failure - #{error_date}"
  end

  def email_cc
    yordar_credentials(:yordar, :developer_email)
  end

  def email_options
    {
      fk_id: 1,
      ref: email_ref,
    }
  end

  def email_variables
    {
      event: deep_struct(event_data),
      payment_url: payment_url,
      stripe_account_url: stripe_account_url,
    }
  end

  def event_data
    failure_code = event_object.respond_to?(:failure_code) && event_object.failure_code
    {
      payment_id: event_object.id,
      currency: event_object.currency.upcase,
      amount: number_to_currency((event_object.amount / 100.0), precision: 2),
      description: event_object.description,
      failure_code: failure_code,
      failure_message: (failure_code && event_object.failure_message),
    }
  end

  def stripe_account_url
    "#{stripe_base_url}/dashboard"
  end

  def payment_url
    "#{stripe_base_url}/payments/#{event_object.id}"
  end

  def stripe_base_url
    base_url = 'https://dashboard.stripe.com'
    base_url += '/test' if !Rails.env.production?
    base_url
  end

  def event_object
    @_event_object ||= event.data.object
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{event.id}"
  end

end
