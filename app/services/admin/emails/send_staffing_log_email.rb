class Admin::Emails::SendStaffingLogEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-staffing_log'.freeze

  def initialize(pantry_manager:, orders:, fortnight:)
    @pantry_manager = pantry_manager
    @orders = orders
    @fortnight = fortnight
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send pantry manager log to #{pantry_manager&.name} - (#{fortnight[:from_date].to_s(:date_verbose)} to #{fortnight[:to_date].to_s(:date_verbose)})"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { pantry_manager: pantry_manager&.name, fortnight: fortnight })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :pantry_manager, :orders, :fortnight, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Staffing Log sent to #{pantry_manager.name} - (#{fortnight[:from_date].to_s(:date_verbose)} to #{fortnight[:to_date].to_s(:date_verbose)})"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipient
    pantry_manager.email_recipient
  end

  def email_subject
    "YORDAR: Your scheduling log for the fortnight ending #{fortnight[:to_date].to_s(:date_verbose)}"
  end

  def email_options
    {
      fk_id: pantry_manager.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: pantry_manager.email_salutation,
      from_date: fortnight[:from_date].to_s(:date_verbose),
      to_date: fortnight[:to_date].to_s(:date_verbose),
      total_hours: total_hours,

      schedule: deep_struct(schedule_data)
    }
  end

  def total_hours
    schedule_data.sum do |data|
      data[:total_hours]
    end
  end

  def schedule_data
    @_schedule_data ||= begin
      date_grouped_orders.map do |date, date_orders|
        orders_data = date_orders.map{|order| orders_data_for(order) }
        total_hours = orders_data.sum{|order_data| order_data[:hours] }
        is_allocated_for_week  = total_hours == Admin::Reports::FetchPantryManagerSpends::ItemSpend::HOURS_MAP[:weekly]
        {
          date: date.to_s(:full_date),
          orders: orders_data,
          total_hours: total_hours,
          is_allocated_for_week: is_allocated_for_week
        }
      end
    end
  end

  def orders_data_for(order)
    customer = order.customer_profile
    order_spend = Admin::Reports::FetchPantryManagerSpends::OrderSpend.new(order: order)
    {
      customer_name: customer.name,
      company_name: customer.company&.name || customer.company_name,
      hours: order_spend.hours
    }
  end

  def date_grouped_orders
    orders.group_by do |order|
      order.delivery_at.to_date
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{pantry_manager.id}-#{fortnight[:from_date].to_s(:date_spreadsheet)}"
  end

end

