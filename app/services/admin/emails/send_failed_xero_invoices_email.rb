class Admin::Emails::SendFailedXeroInvoicesEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-failed_xero_invoices'.freeze

  def initialize(time: Time.zone.now, invoices: [])
    @time = time
    @invoices = invoices
  end

  def call
    return if failed_invoices.blank?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send failed xero invoices email to admin on #{time}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { errors_datetime: time })
    end
  end

private

  attr_reader :time, :invoices

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      cc: email_cc,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Failed xero invoices email sent to admin on #{time}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_cc
    yordar_credentials(:yordar, :developer_email)
  end

  def email_subject
    'YORDAR: Invoices failed to send to XERO'
  end

  def email_options
    {
      fk_id: 0,
      ref: email_ref,
    }
  end

  def email_variables
    {
      sync_issues: deep_struct(sync_issues),
      contact_issues: deep_struct(contact_issues),
    }
  end

  def failed_invoices
    return invoices if invoices.present?
    return @_failed_invoices if @_failed_invoices.present?

    failed_invoices = Invoice.where(pushed_to_xero: false)
    failed_invoices = failed_invoices.where('invoices.created_at BETWEEN :starts_at AND :ends_at', starts_at: (time - 2.days).beginning_of_day, ends_at: time.beginning_of_day)
    @_failed_invoices = failed_invoices.order(:created_at)
  end

  def customers_with_contact_issues
    CustomerProfile.joins(:user).where(users: { xero_push_fault: true })
  end

  def invoices_for_customers_with_contact_issues
    @_invoices_for_customers_with_contact_issues ||= failed_invoices.joins(orders: :customer_profile).where(orders: { customer_profile_id: customers_with_contact_issues.map(&:id) }).distinct
  end

  def contact_issues
    invoices_for_customers_with_contact_issues.map do |invoice|
      info_for(invoice)
    end
  end

  def sync_issues
    other_issue_invoices = failed_invoices - invoices_for_customers_with_contact_issues
    other_issue_invoices.map do |invoice|
      info_for(invoice)
    end
  end

  def info_for(invoice)
    customer = invoice.invoice_orders.first.customer_profile
    {
      invoice_number: invoice.number,
      invoice_date: invoice.created_at.to_s(:date),
      invoice_url: invoice.latest_document&.url,
      customer_name: customer.customer_or_company_name
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{time.to_s(:date)}"
  end

end
