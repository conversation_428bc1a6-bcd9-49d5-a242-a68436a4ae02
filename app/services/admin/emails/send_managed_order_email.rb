class Admin::Emails::SendManagedOrderEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'yordar-managed_order'.freeze

  def initialize(order:, account_manager:)
    @order = order
    @account_manager = account_manager
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send order email to Account manager #{account_manager&.name} - ##{order&.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { account_manager: account_manager&.name, order: order&.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :order, :account_manager, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: account_manager.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent email to Account Manager #{account_manager.name} for order ##{order.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: Managed Order ##{order.id} was submitted by #{customer_data[:name]}"
  end

  def email_options
    {
      fk_id: 1,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: account_manager.email_salutation,

      order: deep_struct(order_data),
      supplier_grouped_order_lines: deep_struct(supplier_grouped_order_lines),
      totals: deep_struct(totals),

      customer: deep_struct(customer_data)
    }
  end

  def order_data
    {
      id: order.id,
      name: order.name,
      delivery_at: order.delivery_at.to_s(:full_verbose),
      delivery_address: order.delivery_address_arr.join(', '),
      link: url_helper.order_show_url(order, host: app_host),
    }
  end

  def customer_data
    customer = order.customer_profile
    {
      name: customer.name,
      company: (customer.company&.name.presence || customer.company_name),
    }
  end

  def supplier_grouped_order_lines
    order_lines.group_by(&:supplier_profile).map do |supplier, supplier_order_lines|
      hashed_order_lines = supplier_order_lines.map do |order_line|
        {
          name: order_line.name.truncate(50, omission: ' ...'),
          quantity: order_line.quantity,
          price: number_to_currency(order_line.price),
          image: cloudinary_image(order_line.menu_item.image)
        }
      end
      {
        supplier: {
          name: supplier.company_name,
          image: cloudinary_image(supplier.profile.avatar),
        },
        has_more: false,
        order_lines: hashed_order_lines
      }
    end
  end

  def totals
    {
      customer_subtotal: number_to_currency(order.customer_subtotal),
      order_discount: sanitized_currency_for(order.discount),
      customer_delivery: number_to_currency(order.customer_delivery),
      customer_gst: number_to_currency(order.customer_gst),
      customer_surcharge: sanitized_currency_for(order.customer_surcharge),
      customer_topup: sanitized_currency_for(order.customer_topup),
      customer_total: number_to_currency(order.customer_total),
    }
  end

  def order_lines
    @_order_lines = begin
      lister_options = {
        order: order,
        confirmed_attendees_only: order.is_team_order?,
      }
      OrderLines::List.new(options: lister_options, includes: %i[supplier_profile menu_item]).call
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-##{order.id}-#{account_manager.id}-#{Time.zone.now.to_s(:date_spreadsheet)}"
  end

end

