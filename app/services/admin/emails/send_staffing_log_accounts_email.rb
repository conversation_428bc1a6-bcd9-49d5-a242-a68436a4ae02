class Admin::Emails::SendStaffingLogAccountsEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-staffing_log_accounts'.freeze

  def initialize(staffing_spends:, fortnight:)
    @staffing_spends = staffing_spends
    @fortnight = fortnight
    @attachments = []
    @result = Result.new
  end

  def call
    begin
      generate_document
      send_email
    rescue => exception
      error_message = "Failed to send staffing log to accounts team - (#{fortnight[:from_date].to_s(:date_verbose)} to #{fortnight[:to_date].to_s(:date_verbose)})"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { fortnight: fortnight })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :staffing_spends, :fortnight, :attachments, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipient,
      cc: email_cc,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Staffing Log sent to accounts team - (#{fortnight[:from_date].to_s(:date_verbose)} to #{fortnight[:to_date].to_s(:date_verbose)})"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipient
    [
      yordar_credentials(:yordar, :accounts_email),
      yordar_credentials(:yordar, :elias_email)
    ].join(';')
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_subject
    "YORDAR: Staffing log for the fortnight ending #{fortnight[:to_date].to_s(:date_verbose)}"
  end

  def email_options
    {
      fk_id: 1,
      ref: email_ref,
    }
  end

  def email_variables
    {
      from_date: fortnight[:from_date].to_s(:date_verbose),
      to_date: fortnight[:to_date].to_s(:date_verbose),
      total_hours: total_hours,
      document_url: @spend_document&.url,

      schedule: deep_struct(schedule_data)
    }
  end

  def generate_document
    @spend_document = Documents::Generate::StaffingSpend.new(staffing_spends: staffing_spends, with_orders: true).call
    attachments << @spend_document if @spend_document.present?
  end

  def total_hours
    schedule_data.sum do |data|
      data[:hours]
    end
  end

  def schedule_data
    @_schedule_data ||= begin
      staffing_spends.map do |staffing_spend|
        {
          pantry_manager: staffing_spend.manager.name,
          hours: staffing_spend.hours
        }
      end
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{fortnight[:from_date].to_s(:date_spreadsheet)}"
  end

end

