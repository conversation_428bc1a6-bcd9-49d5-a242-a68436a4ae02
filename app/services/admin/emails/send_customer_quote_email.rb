class Admin::Emails::SendCustomerQuoteEmail < Notifications::Base

  EMAIL_TEMPLATE = 'admin-customer_quote'.freeze

  def initialize(quote:)
    @quote = quote
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send event customer quote email to Yordar admin #{quote.kind} - ##{quote.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { quote_id: quote.id, customer_id: quote_customer&.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :quote, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Customer Quote Submission email sent to Yordar admin #{quote.id} - ##{quote.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipient
    case quote.kind
    when 'catering'
      yordar_credentials(:yordar, :orders_email)
    when 'snacks'
      yordar_credentials(:yordar, :sales_admin)
    when 'event'
      yordar_credentials(:yordar, :events_admin)
    end
  end

  def email_subject
    subject = "YORDAR: A #{quote.kind.humanize} quote was submitted"
    subject += " by #{quote.customer_profile.name}" if quote_customer.present?
    subject
  end

  def email_options
    {
      fk_id: quote.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      quote: deep_struct(quote_data),
      form_data: quote.form_data.except('type'),

      header_color: header_color
    }
  end

  def quote_data
    {
      kind: quote.kind,
      customer_name: quote_customer&.name,
    }
  end

  def quote_customer
    @_quote_customer ||= quote.customer_profile
  end

  def header_color
    case quote.kind
    when 'event' then :pink
    when 'snacks' then :yellow
    when 'catering' then :purple
    else :pink
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{quote.id}"
  end

end

