class Admin::Emails::SendFailedWoolworthsCheckoutEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'admin-woolworths_checkout_failed'.freeze

  def initialize(order:)
    @order = order
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send failed Woolworths Order Checkout notification to admin - #{order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order_id: order.id })
    end
  end

private

  attr_reader :order

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info 'Sent failed Woolworths Order Checkout notification to admin'
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :orders_email)
  end

  def email_subject
    "YORDAR: FAILED Woolworths Checkout - ##{order.id}"
  end

  def email_cc
    yordar_credentials(:yordar, :developer_email)
  end

  def email_options
    {
      fk_id: order.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      order: deep_struct(order_data),
    }
  end

  def order_data
    woolworths_order = order.woolworths_order
    {
      id: order.id,
      name: order.name,
      customer_name: order.customer_profile.name,
      delivery_at: order.delivery_at.to_s(:full_verbose),
      total: number_to_currency(order.customer_total),
      order_line_count: order.order_lines.count,
      address: order.delivery_address_arr.join(', '),
      account_name: woolworths_order.account.short_name,
      delivery_window_text: woolworths_order.delivery_window_text,
      link: url_helper.order_show_url(order, host: app_host),
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}"
  end

end
