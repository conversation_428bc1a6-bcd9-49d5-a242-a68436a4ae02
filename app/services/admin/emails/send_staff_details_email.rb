class Admin::Emails::SendStaffDetailsEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-staff_details'.freeze

  def initialize(customer:)
    @customer = customer
    @result = Result.new
  end

  def call
    begin
      send_email if can_send_email?
    rescue => exception
      error_message = "Failed to send customer staff details to accounts team ##{customer&.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer&.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :result

  def can_send_email?
    case
    when Email.where(email_options).present?
      result.errors << 'Accounts team already notified'
    end
    result.errors.blank?
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Customer Staff Details email sent to accounts team - ##{customer.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipient
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_subject
    "YORDAR: Staff On-Boarding Details - #{customer.name}"
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      customer_name: customer.name,

      personal: staff_details.personal,
      emergency_contact: staff_details.emergency_contact,
      bank: staff_details.bank,
      tax: deep_struct(staff_details.tax),
      documents: staff_details.documents
    }
  end

  def staff_details
    @_staff_details ||= customer.staff_details
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}-#{staff_details.current_version_ref}"
  end

end

