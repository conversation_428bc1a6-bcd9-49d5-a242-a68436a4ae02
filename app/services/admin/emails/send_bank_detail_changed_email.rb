class Admin::Emails::SendBankDetailChangedEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-bank_details_changed'.freeze

  def initialize(supplier:, previous_details: {})
    @supplier = supplier
    @previous_details = previous_details
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send supplier bank details changed email to Yordar admin #{supplier.name} - (#{supplier.id})"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier&.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :previous_details, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Supplier Bank details changed email sent to Yordar admin #{supplier.name} - (#{supplier.id})"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipient
    [
      yordar_credentials(:yordar, :accounts_email),
      yordar_credentials(:yordar, :elias_email)
    ].join(';')
  end

  def email_subject
    "YORDAR: Supplier #{supplier.name} have changed their Bank Details"
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      supplier: deep_struct(supplier_data),
      previous_details: deep_struct(previous_details)
    }
  end

  def supplier_data
    {
      name: supplier.name,
      bsb_number: supplier.bsb_number,
      bank_account_number: supplier.bank_account_number,
      admin_url: url_helper.suppliers_admin_url(company_name: supplier.company_name, host: app_host)
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{supplier.id}-#{Time.zone.now.to_s(:date_spreadsheet)}"
  end

end

