class Admin::ListOrders

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @orders = base_orders
    filter_by_query  if filter_options[:query].present?
    filter_by_status if filter_options[:for_statuses].present?
    filter_by_custom_orders if filter_options[:custom_orders_only].present?
    filter_by_delivery_date
    filter_by_order if filter_options[:order].present?
    filter_by_access    
    sort_orders
    paginate_orders if filter_options[:page].present? && filter_options[:limit].present?

    orders.includes(includes).distinct
  end

private
  
  attr_reader :includes, :filter_options, :orders

  def base_orders
    Order.all
  end

  def filter_by_query
    order_arel = Order.arel_table
    id_condition = order_arel[:id].eq(filter_options[:query])
    name_condition = order_arel[:name].matches("#{filter_options[:query]}%")

    woolworths_order_arel = Woolworths::Order.arel_table
    woolworths_id_condition = woolworths_order_arel[:woolworths_order_id].matches("#{filter_options[:query]}%")

    customer_arel = CustomerProfile.arel_table
    customer_name_condition = customer_arel[:customer_name].matches("%#{filter_options[:query]}%")
    company_name_condition = customer_arel[:company_name].matches("%#{filter_options[:query]}%")

    company_arel = Company.arel_table
    company_condition = company_arel[:name].matches("%#{filter_options[:query]}%")

    query_condition = id_condition.or(name_condition).or(customer_name_condition).or(company_name_condition).or(company_condition).or(woolworths_id_condition)
    @orders = orders.joins(:customer_profile).left_outer_joins(:woolworths_order, customer_profile: :company).where(query_condition)
  end

  def filter_by_status
    order_statuses = filter_options[:for_statuses] || []
    order_statuses << 'draft' if filter_options[:custom_orders_only].present?
    @orders = orders.where(status: order_statuses)
  end

  def filter_by_custom_orders
    @orders = orders.where(order_variant: 'event_order')
  end

  def filter_by_delivery_date
    case
    when filter_options[:order].present?
      # do nothing
    when filter_options[:from_date].present? && filter_options[:to_date].present?
      starts = filter_options[:from_date].is_a?(String) ? Time.zone.parse(filter_options[:from_date]).beginning_of_day : filter_options[:from_date] 
      ends = filter_options[:to_date].is_a?(String) ? Time.zone.parse(filter_options[:to_date]).end_of_day : filter_options[:to_date]
      @orders = orders.where(delivery_at: [starts..ends])
    when filter_options[:show_past].present?
      @orders = orders.where('delivery_at < ?', Time.zone.now.beginning_of_day)
    else
      @orders = orders.where('delivery_at >= ?', Time.zone.now.beginning_of_day)
    end
  end

  def filter_by_access
    case
    when filter_user.blank? # if no user is passed return none
      @orders = Order.none
    when (filter_user.admin? || filter_user.super_admin?) && !filter_options[:favourites_only]
      # do not fitler - admins get access to all orders
    when filter_options[:customer_id].present? && customer_ids.include?(filter_options[:customer_id])
      @orders = orders.joins(:customer_profile).where(customer_profiles: { id: filter_options[:customer_id] })
    when customer_ids.present?
      @orders = orders.joins(:customer_profile).where(customer_profiles: { id: customer_ids })
    else # defaults to none
      @orders = Order.none
    end
  end

  def filter_user
    @_filter_user ||= filter_options[:for_user]
  end

  def filter_by_order
    if filter_options[:order].is_a?(Order)
      @orders = orders.where(id: filter_options[:order].id)
    else
      @orders = Order.none
    end
  end

  def sort_orders
    order_by = case
    when filter_options[:from_date].present? && filter_options[:to_date].present?
      { delivery_at: :asc, id: :asc }
    when filter_options[:show_past]
      { delivery_at: :desc, id: :asc }
    else
      { delivery_at: :asc, id: :desc }
    end

    @orders = orders.order(order_by)
  end

  def paginate_orders
    @orders = orders.page(filter_options[:page]).per(filter_options[:limit])
  end


  def customer_ids
    return [] if filter_user.blank?

    @_customer_ids ||= begin
      lister_options = { for_user: filter_user, favourites_only: filter_options[:favourites_only], limit: nil }
      Admin::ListCustomers.new(options: lister_options).call.map(&:id)
    end
  end

  def default_options
    {
      for_user: nil,
      customer_id: nil,
      for_statuses: %w[quoted pending new amended confirmed delivered paused skipped cancelled],
      query: nil,
      from_date: nil,
      to_date: nil,
      show_past: false,
      order: nil,
      favourites_only: false,
      custom_orders_only: false,
      page: 1,
      limit: 20,
      order_by: nil,
    }
  end

end