class Orders::GetMinimumSpends

  def initialize(order: [])
    @order = order
    @minimum_spends = []
  end

  def call
    orders.each do |order|
      minimum_spends << Orders::GetSupplierSpends.new(order: order, exclude_surcharge: false).call
    end
    minimum_spends
  end

private

  attr_accessor :minimum_spends
  attr_reader :order

  def orders
    if order.status == 'draft' && order.is_recurrent?
      Order.where(recurrent_id: order.recurrent_id).order(:created_at)
    else
      [order]
    end
  end

end
