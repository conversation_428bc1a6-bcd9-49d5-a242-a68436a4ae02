class Orders::ChargeToSupplierMinimums

  def initialize(order:, supplier_spends: nil)
    @order = order
    @supplier_spends = supplier_spends.presence || fetch_supplier_spends
  end

  def call
    supplier_spends.each do |supplier_spend|
      charge_to_minimum_for(supplier_spend) if charge_to_minimum?
    end
    remove_unwanted_order_suppliers if !order.is_team_order?
  end

private

  attr_reader :order, :supplier_spends

  def charge_to_minimum?
    if order.is_team_order?
      order.cutoff_option.present? && order.cutoff_option == 'charge_to_minimum'
    else
      order.charge_to_minimum?
    end
  end

  def remove_unwanted_order_suppliers
    order.order_suppliers.where.not(supplier_profile: supplier_spends.map(&:supplier)).each(&:destroy)
  end

  def fetch_supplier_spends
    order_spends = Orders::GetSupplierSpends.new(order: order, exclude_surcharge: true).call
    order_spends.supplier_spends
  end

  def charge_to_minimum_for(supplier_spend)
    order_supplier = order.order_suppliers.where(supplier_profile: supplier_spend.supplier).first_or_initialize
    if supplier_spend.is_under?
      order_supplier.update(surcharge: supplier_spend.remaining_spend)
    else
      order_supplier.update(surcharge: nil)
    end
  end

end
