class Orders::ValidateOrderLines

  ValidationError = Struct.new(:name, :message)

  def initialize(order:)
    @order = order
    @result = Result.new(order: @order)
  end

  def call
    check_order_line_errors
    result
  end

private

  attr_reader :order, :result

  def check_order_line_errors
    order_lines_with_errors = order_lines.select{|order_line| order_line.last_errors.present? }
    order_lines_with_errors.each do |order_line|
      if order_line.last_errors.any?{|error| error.include?('Item is no longer available') || error.include?('Supplier is no longer available') }
        result.errors << ValidationError.new(order_line.name, order_line.last_errors.join('. '))
      else
        result.warnings << ValidationError.new(order_line.name, order_line.last_errors.join('. '))
      end
    end
  end

  def order_lines
    @_order_lines ||= begin
      lister_options = {
        order: order
      }
      OrderLines::List.new(options: lister_options).call
    end
  end

  class Result
    attr_accessor :errors, :warnings
    attr_reader :order

    def initialize(order:)
      @order = order
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && warnings.blank?
    end
  end

end