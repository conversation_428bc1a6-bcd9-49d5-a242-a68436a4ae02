class Orders::RenewOrders

  DUPLICATION_COUNT = 6

  def initialize(time: Time.zone.now, verbose: !Rails.env.test?)
    @time = time
    @verbose = verbose
    @result = Result.new
  end

  def call
    DUPLICATION_COUNT.times.each do |_|
      renewable_orders = orders_needing_reviewing
      break if renewable_orders.blank?

      renewable_orders.each do |order|
        renew_order(order)
      end
    end # Duplication loop
    result
  end

private

  attr_reader :time, :verbose, :result

  def orders_needing_reviewing
    orders = Order.where.not(recurrent_id: nil)
    orders = orders.where.not(template_id: nil)
    orders = orders.where.not(renewed_from_id: nil)
    orders = orders.where(renewed_to_id: nil)
    orders = orders.where("status IN ('confirmed', 'amended', 'paused', 'skipped') OR (status in ('cancelled', 'skipped') AND order_type = 'one-off')") # we allow one-off cancelled order or skipped order to be renewed
    orders = orders.where('delivery_at <= ?', time + 5.weeks)
    orders
  end

  def renew_order(order)
    begin
      order_renewer = Orders::Recurring::Renew.new(order: order).call
      if order_renewer.success?
        renewed_order = order_renewer.renewed_order
        puts ".. #{order.id} renewed to create #{renewed_order.id}, for delivery at #{renewed_order.delivery_at}" if verbose
        result.renewed_orders << renewed_order
      else
        result.errors << order_renewer.errors.join('. ')
      end
    rescue => exception
      puts "Failed to renew order #{order.id}. #{exception.inspect}" if verbose
      result.errors << {
        error: exception,
        backtrace: exception.backtrace,
        description: 'Failed to renew order',
        data: {
          'order': order.id
        }
      }
    end
  end

  class Result
    attr_accessor :renewed_orders, :errors

    def initialize
      @renewed_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
