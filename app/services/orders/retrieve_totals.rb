class Orders::RetrieveTotals

  def initialize(order:, order_lines: nil, profile: nil, baseline: false, recalculate: false)
    @order = order
    @order_lines = order_lines
    @profile = profile
    @baseline = baseline
    @recalculate = recalculate
  end

  def call
    totals.is_a?(Hash) ? OpenStruct.new(totals) : totals
  end

private

  attr_reader :order, :order_lines, :profile, :baseline, :recalculate

  def order_order_lines
    supplier = profile.present? && profile.instance_of?(SupplierProfile) ? profile : nil
    lister_options = {
      order: order,
      supplier: supplier,
      confirmed_attendees_only: order.is_team_order?,
    }
    OrderLines::List.new(options: lister_options).call
  end

  def totals
    @_totals ||= case
    when profile.present? && profile.instance_of?(SupplierProfile)
      Orders::CalculateSupplierTotals.new(order: order, supplier: profile, order_lines: order_order_lines, save_totals: recalculate, baseline: baseline).call
    when order_lines.present? || recalculate
      Orders::CalculateCustomerTotals.new(order: order, order_lines: order_lines, save_totals: recalculate).call
    else
      {
        order_line_count: order_order_lines.count,
        subtotal: order.customer_subtotal,
        discount: order.discount,
        delivery: order.customer_delivery,
        gst: order.customer_gst,
        topup: order.customer_topup,
        surcharge: order.customer_surcharge,
        total: order.customer_total,
      }
    end
  end
end
