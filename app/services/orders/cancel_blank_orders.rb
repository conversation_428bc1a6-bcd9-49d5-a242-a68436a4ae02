class Orders::CancelBlankOrders

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new
  end

  def call
    blank_orders.each do |order|
      result.possible_orders << order
      if order.update_column(:status, 'cancelled')
        result.cancelled_orders << order
      end
    end
    result
  end

private

  attr_reader :time, :result

  def blank_orders
    orders = Order.where(order_variant: 'general')
    orders = orders.where.not(status: %w[draft cancelled]) # not already cancelled
    orders = orders.includes(:order_lines).where(order_lines: { id: nil }) # no order lines
    orders = orders.where('orders.delivery_at <= ?', (time - 1.day).end_of_day) # day old orders
    orders
  end

  class Result
    attr_accessor :possible_orders, :cancelled_orders

    def initialize
      @possible_orders = []
      @cancelled_orders = []
    end
  end

end
