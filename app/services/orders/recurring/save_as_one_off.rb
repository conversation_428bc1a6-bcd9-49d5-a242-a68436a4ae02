class Orders::Recurring::SaveAsOneOff

  def initialize(order:)
    @order = order
  end

  def call
    return if !order.is_recurrent?

    update_template_of_subsequent_orders if order.id == order.template_id && subsequent_orders.present?
    order.update(order_type: 'one-off')
  end

private

  attr_reader :order

  def subsequent_orders
    @_subsequent_orders = Order.where(template_id: order.template_id, order_type: 'recurrent').where.not(id: order.id).order(:id)
  end

  def update_template_of_subsequent_orders
    new_template = subsequent_orders.first
    subsequent_orders.update_all(template_id: new_template.id)
  end

end
