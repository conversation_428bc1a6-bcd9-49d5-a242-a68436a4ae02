class Orders::Recurring::SyncOrderLines

  def initialize(order:, template_order: nil, customer: nil)
    @order = order
    @template_order = template_order.presence || fetch_template_order
    @customer = customer.presence || @template_order.customer_profile
  end

  def call
    return if !order.is_recurrent? || order.id == order.template_id || template_order.blank?

    remove_existing_order_lines

    template_order_lines.group_by(&:location).each do |location, location_order_lines|
      new_location = create_new_location(location: location)
      next if new_location.blank?

      add_location_order_lines_to_order(location: new_location, order_lines: location_order_lines)
    end

    tag_order_lines_as_amended
  end

private

  attr_reader :order, :template_order, :customer

  def fetch_template_order
    @_template_order ||= Order.where(id: order.template_id).first
  end

  def remove_existing_order_lines
    order_lines_to_remove = order.order_lines

    locations_to_remove = order_lines_to_remove.map(&:location).select do |location|
      location.order == order # only belonging to current (subsequent recurring) order
    end

    order_lines_to_remove.destroy_all
    locations_to_remove.each(&:destroy)
  end

  def template_order_lines
    lister_options = {
      order: template_order
    }
    @_template_order_lines = OrderLines::List.new(options: lister_options, includes: %i[menu_item serving_size]).call
  end

  def mismatched_order_lines
    @_mismatched_order_lines ||= Orders::FetchMismatchedOrderLines.new(order: template_order, order_lines: template_order_lines).call
  end

  def create_new_location(location:)
    return nil if location.blank?

    location_upserter = Locations::Upsert.new(order: order, location_params: { details: location.details }).call
    if location_upserter.success?
      location_upserter.location
    else
      result.errors += location_upserter.errors
      nil
    end
  end

  def add_location_order_lines_to_order(location:, order_lines:)
    order_params = { order_id: order.id, location_id: location.id }
    order_lines_params = order_lines.map do |order_line|
      found_mismatched_order_line = mismatched_order_lines.present? && mismatched_order_lines.detect{|mismatched_order_line| mismatched_order_line.id == order_line.id }
      order_line = found_mismatched_order_line.presence || order_line
      {
        item_id: order_line.menu_item_id,
        serving_size_id: order_line.serving_size_id,
        selected_menu_extra_ids: order_line.selected_menu_extras,
        note: order_line.note,
        quantity: order_line.quantity,
      }
    end
    OrderLines::CreateMultiple.new(order: order, order_params: order_params, order_lines_params: order_lines_params).call
  end

  def tag_order_lines_as_amended
    order.reload.order_lines.update_all(status: 'amended')
  end
end

