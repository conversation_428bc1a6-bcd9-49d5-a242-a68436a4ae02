class Orders::Recurring::UpdateSubsequentOrders

  def initialize(order:, order_params: {}, delivery_difference: nil)
    @order = order
    @order_params = order_params.to_h.symbolize_keys.except(:delivery_at)
    @delivery_difference = delivery_difference
  end

  def call
    # return if order.status != 'amended'
    recurrent_orders = all_subsequent_orders.select{|subsequent_order| subsequent_order.order_type == 'recurrent' }
    order.update(template_id: order.id)

    subsequent_recurrent_orders = recurrent_orders.reject{|recurrent_order| recurrent_order.id == order.id }
    subsequent_recurrent_orders.each do |subsequent_order|
      update_order_details_for(subsequent_order: subsequent_order)
      self.delay(queue: :data_integrity).update_order_lines_for(subsequent_order: subsequent_order)
    end
  end

private

  attr_reader :order, :order_params, :delivery_difference

  def update_order_details_for(subsequent_order:)
    Orders::Update.new(order: subsequent_order, order_params: sanitized_order_params_for(subsequent_order), profile: customer).call
  end

  def update_order_lines_for(subsequent_order:)
    sync_order_lines_for(subsequent_order: subsequent_order)
    sync_order_suppliers(subsequent_order: subsequent_order)
    recalculate_totals_for(subsequent_order: subsequent_order)
    generate_documents_for(subsequent_order: subsequent_order)
  end

  def sync_order_lines_for(subsequent_order:)
    Orders::Recurring::SyncOrderLines.new(order: subsequent_order, template_order: order, customer: customer).call
  end

  def sync_order_suppliers(subsequent_order:)
    Orders::Recurring::SyncOrderSuppliers.new(order: subsequent_order, template_order: order).call
  end

  def recalculate_totals_for(subsequent_order:)
    subsequent_order.reload
    Orders::CalculateCustomerTotals.new(order: subsequent_order, save_totals: true).call
    subsequent_order.supplier_profiles.each do |supplier|
      Orders::CalculateSupplierTotals.new(order: subsequent_order, supplier: supplier, save_totals: true).call
    end
  end

  def generate_documents_for(subsequent_order:)
    subsequent_order.reload
    subsequent_order.supplier_profiles.each do |supplier|
      Documents::Generate::SupplierOrderDetails.new(order: subsequent_order, supplier: supplier).call
      Documents::Generate::SupplierOrderDetails.new(order: subsequent_order, supplier: supplier, variation: 'delivery_docket').call
      Documents::Generate::SupplierOrderDetails.new(order: subsequent_order, supplier: supplier, variation: 'json').call if supplier.uses_flex_catering
    end
  end

  def sanitized_order_params_for(subsequent_order)
    [
      order_params,
      default_params,
      delivery_params_for(subsequent_order)
    ].inject(&:merge)
  end

  def default_params
    {
      status: 'amended',
      template_id: order.template_id,
    }
  end

  def delivery_params_for(subsequent_order)
    return {} if delivery_difference.blank?

    {
      delivery_at: subsequent_order.delivery_at + delivery_difference
    }
  end

  def all_subsequent_orders
    @_all_subsequent_orders ||= Order.where(template_id: order.template_id).where('delivery_at >= ?', order.delivery_at).order(:delivery_at)
  end

  def customer
    @_customer ||= order.customer_profile
  end
end
