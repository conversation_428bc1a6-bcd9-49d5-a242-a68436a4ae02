class Orders::Recurring::SyncOrderSuppliers

  def initialize(order:, template_order: nil)
    @order = order
    @template_order = template_order.presence || fetch_template_order
  end

  def call
    return if !order.is_recurrent? || order.id == order.template_id || template_order.blank?

    order.order_suppliers.delete_all # remove_all_order_suppliers
    template_order_suppliers.each do |template_order_supplier|
      new_order_supplier = order.order_suppliers.where(supplier_profile: template_order_supplier.supplier_profile).first_or_initialize
      new_order_supplier.update(delivery_fee_override: template_order_supplier.delivery_fee_override)
    end
  end

private

  attr_reader :order, :template_order

  def fetch_template_order
    @_template_order ||= Order.where(id: order.template_id).first
  end

  def template_order_suppliers
    @_template_order_suppliers = template_order.order_suppliers.where(supplier_profile: template_order.supplier_profiles)
  end

end