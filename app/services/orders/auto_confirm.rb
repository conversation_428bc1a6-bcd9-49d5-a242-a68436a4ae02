class Orders::AutoConfirm

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new(time: time)
  end

  def call
    auto_confirmable_orders.each do |order|
      begin
        confirm_order(order)
      rescue => exception
        Rails.logger.error "Failed to automatically confirm order ##{order.id}"
        Rails.logger.error exception.inspect
        Rails.logger.error exception.backtrace.join('\n')
        Raven.capture_exception(exception,
            message: "Failed to automatically confirm order ##{order.id}",
            extra: { order_id: order.id },
            transaction: 'Orders::AutoConfirm'
          )
      end
    end
    if result.auto_confirmed_orders.present?
      send_confirmation_check_email
      log_event
    end
    result
  end

private

  attr_reader :time, :result

  def till_date
    if time.friday?
      time + 3.days # Auto-confirm orders to be delivered tomorrow, Sunday and Monday
    else
      time + 1.day # Auto-confirm orders for today and tomorrow
    end
  end

  def auto_confirmable_orders
    orders = Order.where(split_order_id: nil)
    orders = orders.where(delivery_at: (time.beginning_of_day..till_date.end_of_day))
    orders = orders.where(status: %w[new amended])
    orders = orders.where.not(order_variant: %w[team_order recurring_team_order])
    orders
  end

  def confirm_order(order)
    order_confirmer = Orders::Confirm.new(order: order, auto_confirmation: true).call
    if order_confirmer.success?
      result.auto_confirmed_orders << order
    else
      result.errors += order_confirmer.errors.map{|error| "Order ##{order.id}: #{error}" }
    end
  end

  def send_confirmation_check_email
    Orders::Emails::SendConfirmationCheckEmail.new(auto_confirmed_orders: result.auto_confirmed_orders).delay(queue: :notifications).call
  end

  def log_event
    EventLogs::Create.new(event: 'orders-auto-confirmed', orders: result.auto_confirmed_orders.map(&:id)).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :time, :auto_confirmed_orders, :errors

    def initialize(time:)
      @time = time
      @auto_confirmed_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
