class Orders::RetrieveMajorOrderCategory

  def initialize(order:, supplier: nil, save_category: false)
    @order = order
    @supplier = supplier
    @save_category = save_category
  end

  def call
    if save_category.blank? && supplier.blank? && (saved_major_category = order.major_category.presence)
      saved_major_category
    else
      major_category = major_order_line_category || custom_order_category || supplier_category
      save_category_to_order(category: major_category) if major_category.present? && save_category

      major_category
    end
  end

private

  attr_reader :order, :supplier, :save_category

  def major_order_line_category
    return nil if category_grouped_order_lines.blank?

    category_grouped_order_lines.max_by do |category, category_order_lines|
      category.present? ? (category_grouped_order_lines.size * category_order_lines.sum(&:quantity)) : 0
    end.first
  end

  def custom_order_category
    return nil if !order.is_event_order?

    case
    when order.major_category.present?
      order.major_category
    when order.order_category.present?
      Category.generic_category_for(group_name: order.order_category)
    end
  end

  def supplier_category
    suppliers = supplier.present? ? [supplier] : order.supplier_profiles
    supplier_categories = suppliers.map do |supplier|
      categories = []
      categories << Category.generic_category_for(group_name: 'catering-services') if supplier.has_catering_services
      categories << Category.generic_category_for(group_name: 'kitchen-supplies') if supplier.has_kitchen_supplies
      categories
    end.flatten(1).uniq

    supplier_categories.min_by do |category|
      [category.group_weight, category.weight]
    end
  end

  def save_category_to_order(category:)
    update_attributes = { major_category: category }
    update_attributes[:update_with_invoice] = true if order.invoice_id.present?
    order.update(update_attributes)
  end

  def order_lines
    @_order_lines ||= begin
      lister_options = {
        order: order,
        confirmed_attendees_only: order.is_team_order?,
        supplier: supplier,
      }
      @order_lines = OrderLines::List.new(options: lister_options, includes: [:category, { menu_item: { menu_section: :categories } }]).call
    end
  end

  def category_grouped_order_lines
    return {} if order_lines.blank?

    @_category_grouped_order_lines ||= begin
      order_lines.group_by do |order_line|
        case
        when order_line_category = order_line.category.presence
          order_line_category
        when menu_section_categories = order_line.menu_item&.menu_section&.categories&.order(group_weight: :asc, weight: :asc, id: :asc).presence
          menu_section_categories.first
        end
      end
    end
  end

end