class Orders::GetSanitizedAddress

  LOOP_THRESHOLD = 3

  def initialize(order:)
    @order = order
    @address = Address.new
  end

  def call
    if delivery_address.present?
      if has_level_info?(delivery_address)
        street_address = delivery_address
        loop_count = 1
        while has_level_info?(street_address) && loop_count <= LOOP_THRESHOLD
          level, street_address = sanitized_address_for(street_address)
          loop_count += 1
        end
        address.level = level
        address.street_address = street_address
      else
        address.level = delivery_level
        address.street_address = delivery_address
      end
      address.suburb = order.delivery_suburb
      address.instructions = order.delivery_instruction
    end
    address
  end

private

  attr_reader :order, :address

  def has_level_info?(street_address)
    street_address.present? && street_address.downcase.match?(/\blevel\b/i)
  end

  def delivery_level
    @_delivery_level ||= order.delivery_address_level.present? ? order.formatted_delivery_address_level.strip : nil
  end

  def delivery_address
    return nil if order.delivery_address.blank?
    return @_order_delivery_address if @_order_delivery_address.present?

    address = order.delivery_address
    address = address.gsub(/\blvl\b/i, 'level') # replace lvl with level
    address = address.gsub(order.delivery_suburb.label, '') if order.delivery_suburb.present? # remove suburb info
    address = address.strip.gsub(/,$/, '')
    @_order_delivery_address = address.strip
  end

  def sanitized_address_for(address)
    partitioned = address.partition(/level .*[,|\/|-]/i)
    partitioned = partitioned.map{|lvl| lvl.gsub(/,|\/|-/, '') }.map(&:strip).reject(&:blank?)

    level = partitioned.size > 1 ? partitioned[0..partitioned.size - 2].join(', ').gsub(/level level/i, 'level') : ''
    street_address = partitioned[-1]

    level = sanitized_level_info(level)
    [level, street_address]
  end

  def sanitized_level_info(level)
    case
    when delivery_level.present? && delivery_level.gsub(/\blvl\b/i, 'level').downcase.exclude?('level')
      "#{delivery_level} #{level}"
    when delivery_level.present?
      delivery_level
    else
      level
    end
  end

  class Address
    attr_accessor :level, :street_address, :suburb, :instructions

    def initialize
      @level = nil
      @street_address = nil
      @suburb = nil
      @instructions = nil
    end

    def label
      [level, street_address].reject(&:blank?).join(', ')
    end
  end
end

