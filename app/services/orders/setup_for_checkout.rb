class Orders::SetupFor<PERSON>heckout

  def initialize(order:, profile:, cookies: {})
    @order = order
    @profile = profile
    @cookies = cookies

    @result = Result.new
  end

  def call
    switch_recurrent if order.is_recurrent?
    validate_woolworths_order if order.is_woolworths_order?

    set_customer_details
    set_contact_details
    set_delivery_details
    set_promotions
    set_payment_details
    result.order = order

    result
  end

private

  attr_accessor :order
  attr_reader :profile, :cookies, :result

  def switch_recurrent
    @order = Order.where(id: order.recurrent_id).first if order.recurrent_id != order.id

    result.order = order
    result.recurrent_orders = Order.where(recurrent_id: order.recurrent_id).order(:id) if order.status == 'draft'
  end

  def latest_order
    @_latest_order ||= profile.orders.where(status: %w[confirmed delivered new]).last
  end

  def set_customer_details
    order.customer_profile ||= profile
  end

  def set_contact_details
    order.contact_name ||= profile.name
    order.company_name ||= case
      when profile.company.present?
        profile.company.name
      when profile.company_name.present?
        profile.company_name
      end
    order.phone ||= profile.contact_phone || profile.mobile
  end

  def set_delivery_details
    order.delivery_suburb ||= latest_order.present? ? latest_order.delivery_suburb : nil
    if cookies.present? && cookies[:yordar_suburb_id].present?
      order.delivery_suburb ||= Suburb.where(id: cookies[:yordar_suburb_id]).first
      order.delivery_address ||= cookies[:yordar_street_address] if cookies[:yordar_street_address].present? && order.delivery_suburb_id == cookies[:yordar_suburb_id].to_i
    end
    order.delivery_instruction ||= latest_order.present? ? latest_order.delivery_instruction : nil
    order.delivery_type = 'loading_dock' if profile&.requires_loading_dock_code
  end

  def set_promotions
    Promotions::SyncWithOrder.new(order: order).call
  end

  def set_payment_details
    has_account = profile.can_pay_on_account?
    customer_credit_cards = profile.credit_cards.where(enabled: true, saved_for_future: true, pay_on_account: false)
    customer_credit_cards = customer_credit_cards.where.not(stripe_token: nil) if Time.zone.now >= Time.zone.parse(yordar_credentials(:stripe, :migration_date))
    customer_credit_cards = customer_credit_cards.order(created_at: :desc)
    order.credit_card_id ||= (has_account && pay_on_account_card.id) || customer_credit_cards.pluck(:id).first
  end

  def pay_on_account_card
    CreditCard.where(id: 1, pay_on_account: true).first
  end

  def validate_woolworths_order
    order_validator = Woolworths::Order::Validate.new(order: order).call
    if !order_validator.success?
      order_validator.errors.each do |key, errors|
        result.errors[key] ||= []
        result.errors[key] += errors
      end
    end
  end

  class Result
    attr_accessor :order, :recurrent_orders, :errors

    def initialize
      @order = nil
      @recurrent_orders = []
      @errors = {}
    end

    def success?
      errors.values.flatten(1).blank? && order.present?
    end
  end

end
