class Orders::CalculateDiscount
  def initialize(order:, discountable_amount:, recalculate: false)
    @order = order
    @discountable_amount = discountable_amount
    @recalculate = recalculate
  end

  def call
    discount = case
    when !recalculate && order.discount.present? && order.discount != 0
      order.discount
    when order.coupon.present?
      coupon_discount
    when order.promotion.present?
      promotion_discount
    else
      0
    end
    discount.round(2)
  end

private

  attr_reader :order, :discountable_amount, :recalculate

  def coupon_discount
    discount_calculation = Coupons::CalculateDiscount.new(coupon: order.coupon, amount: discountable_amount).call
    discount_calculation.present? ? discount_calculation[:discount] : 0.0
  end

  def promotion_discount
    discount_calculation = Promotions::CalculateDiscount.new(promotion: order.promotion, amount: discountable_amount).call
    discount_calculation.present? ? discount_calculation[:discount] : 0.0
  end

end
