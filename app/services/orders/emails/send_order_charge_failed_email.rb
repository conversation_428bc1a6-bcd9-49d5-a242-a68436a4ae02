class Orders::Emails::SendOrderChargeFailedEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'admin-order_charge_failed'.freeze

  def initialize(order:, card_error:, customer: nil)
    @order = order
    @card_error = card_error
    @customer = customer.presence || order&.customer_profile
  end

  def call
    return if order.blank? || card_error.blank?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send order charge failed email to Yordar Admin #{email_recipients}"
      log_errors(exception: exception, message: error_message, sentry: true)
    end
  end

private

  attr_reader :order, :card_error, :customer

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Failed Order Charge email sent to Yordar Admin #{email_recipients}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :orders_email)
  end

  def email_cc
    [yordar_credentials(:yordar, :accounts_email), yordar_credentials(:yordar, :developer_email)].join(', ')
  end

  def email_subject
    subject = "YORDAR: FAILED On Hold Charge for order ##{order.id}"
    subject += ' - FRAUD DETECTED' if is_fraudulent?
    subject
  end

  def email_options
    {
      fk_id: 0,
      ref: email_ref,
    }
  end

  def email_variables
    {
      order: deep_struct(orders_data),      
      error: deep_struct(error_data),
      customer: deep_struct(customer_data)
    }
  end

  def orders_data
    {
      id: order.id,
      name: order.name,
      status: order.status,
      delivery_datetime: order.delivery_at.to_s(:full),
      card_in_use: order.credit_card&.last4,
      total: number_to_currency(order.customer_total),
      supplier_names: order.supplier_profiles.map(&:name)
    }
  end

  def customer_data
    {
      name: customer.customer_name,
      email: customer.email,
      phones: [order.phone, customer.contact_phone, customer.mobile].reject(&:blank?).uniq,
      pending_orders_count: customer.orders.where(status: %w[new amended confirmed]).where.not(id: order.id).count
    }
  end

  def error_data
    {
      message: card_error.message,
      code: card_error.code.titleize,
      decline_code: card_error.decline_code.titleize,
      is_fraudulent: is_fraudulent?,
    }
  end

  def is_fraudulent?
    card_error.decline_code == 'fraudulent'
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}"
  end
end

