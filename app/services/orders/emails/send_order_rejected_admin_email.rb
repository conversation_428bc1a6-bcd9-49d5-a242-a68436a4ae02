class Orders::Emails::SendOrderRejectedAdminEmail < Notifications::Base

  EMAIL_TEMPLATE = 'admin-rejected_order'.freeze

  def initialize(order:, supplier:)
    @order = order
    @supplier = supplier
    @attachments = []
  end

  def call
    begin
      generate_document
      send_email
    rescue => exception
      error_message = "Failed to send order rejection email to supplier #{supplier.id} - ##{order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order_id: order.id, supplier_id: supplier.id })
    end
  end

private

  attr_reader :supplier, :order, :attachments

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Order rejected email sent to supplier #{supplier.id} - ##{order.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_subject
    "YORDAR: An order was rejected by Supplier - ##{order.id} (Ver.#{@details_document&.version})"
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      order: deep_struct(order_data),
      supplier: deep_struct(supplier_data),
    }
  end

  def order_data
    {
      id: order.id,
      date: order.delivery_at.to_s(:full),
      customer_name: customer.name,
      pdf_url: @details_document&.url,
    }
  end

  def supplier_data
    {
      name: supplier.company_name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def customer
    @_customer ||= order.customer_profile
  end

  def generate_document
    @details_document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: "#{email_ref}-#{supplier.id}").call
    if @details_document.present?
      attachments << @details_document
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}"
  end

  def order_details_report_name(order, ref)
    md5 = Digest::MD5.hexdigest("#{order.id}/#{ref}" + yordar_credentials(:secret_token))
    "order_details/#{md5}"
  end

end
