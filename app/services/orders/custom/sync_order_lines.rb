class Orders::Custom::SyncOrderLines

  def initialize(location:, order_lines_params: [])
    @location = location
    @order_lines_params = order_lines_params
    @result = Result.new(location: location)
  end

  def call
    result.order_lines = sync_passed_in_order_lines
    delete_unwanted_order_lines
    result
  end

private

  attr_accessor :location, :synced_order_lines, :result
  attr_reader :order_lines_params

  def sync_passed_in_order_lines
    @synced_order_lines = []
    order_lines_params.each do |order_line_params|
      custom_upserter = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call
      if custom_upserter.success?
        synced_order_lines << custom_upserter.custom_order_line
      else
        result.errors += custom_upserter.errors
      end
    end
    @synced_order_lines
  end

  def delete_unwanted_order_lines
    order_lines_to_delete = location.order_lines.where.not(id: synced_order_lines.map(&:id))
    order_lines_to_delete.each do |order_line|
      OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call
    end
  end

  class Result
    attr_accessor :location, :order_lines, :errors

    def initialize(location:)
      @location = location
      @order_lines = []
      @errors = []
    end

    def success?
      errors.blank? && location.order_lines.size == order_lines.size
    end
  end

end

