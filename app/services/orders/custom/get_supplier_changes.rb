class Orders::Custom::GetSupplierChanges

  CHANGE_FIELDS = %w[delivery_at delivery_note].freeze

  def initialize(order:, supplier:, since: nil)
    @order = order
    @supplier = supplier
    @since = since.presence || order.suppliers_notified_at.presence || Time.zone.now
    @changes = []
  end

  def call
    if order.is_event_order? && previous_order_supplier.present?
      determine_order_changes
    end
    changes
  end

private

  attr_reader :order, :supplier, :since, :changes

  def order_supplier
    @_order_supplier ||= CustomOrderSupplier.where(order: order, supplier_profile: supplier).first
  end

  def previous_version
    @_previous_version ||= order_supplier.present? && order_supplier.versions.order(created_at: :desc).where('created_at > ?', since).first
  end

  def previous_order_supplier
    @_previous_order_supplier ||= previous_version.present? && previous_version.reify
  end

  def determine_order_changes
    CHANGE_FIELDS.each do |field|
      old_value = previous_order_supplier.send(field)
      new_value = order_supplier.send(field)
      next if (old_value.blank? && new_value.blank?) || old_value == new_value

      changes << OpenStruct.new(
        field: field,
        label: label_for(field),
        old_value: saniized_value_for(field, old_value),
        new_value: saniized_value_for(field, new_value)
      )
    end
  end

  def saniized_value_for(field, value)
    return nil if value.blank?

    case field
    when 'delivery_at'
      value.to_s(:full)
    else
      value
    end
  end

  def label_for(field)
    case field
    when 'delivery_at'
      'Delivery date'
    else
      field.humanize
    end
  end

end
