class Orders::Custom::SyncSuppliers

  def initialize(order:, suppliers_params: {})
    @order = order
    @suppliers_params = suppliers_params
    @result = Result.new
  end

  def call
    sync_passed_in_suppliers
    delete_unwanted_suppliers
    result
  end

private

  attr_accessor :order, :custom_order_suppliers, :result
  attr_reader :suppliers_params

  def sync_passed_in_suppliers
    @custom_order_suppliers = []
    suppliers_params.each do |supplier_id, supplier_data|
      custom_order_supplier = CustomOrderSupplier.where(order_id: order.id, supplier_profile_id: supplier_id).first_or_initialize
      if custom_order_supplier.update(
        delivery_at: (supplier_data[:delivery_at].present? ? Time.zone.parse(supplier_data[:delivery_at]) : nil),
        delivery_fee: supplier_data[:delivery_fee],
        delivery_note: supplier_data[:delivery_note]
      )
        custom_order_suppliers << custom_order_supplier
      else
        result.errors += custom_order_supplier.errors.full_messages
      end
    end
    result.suppliers = custom_order_suppliers
  end

  def delete_unwanted_suppliers
    custom_order_supplier_to_delete = order.custom_order_suppliers.where.not(id: custom_order_suppliers.map(&:id))
    custom_order_supplier_to_delete.each(&:destroy)
  end

  class Result
    attr_accessor :suppliers, :errors

    def initialize
      @suppliers = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end

