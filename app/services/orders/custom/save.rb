class Orders::Custom::Save

  def initialize(order_params: {})
    @order_params = order_params
    @order = order_from_params
    @result = Result.new(order: order)
  end

  def call
    if order.update(sanitized_attributes)
      result.order = order.reload
    else
      result.errors += order.errors.full_messages
    end
    result
  end

private

  attr_accessor :result
  attr_reader :order, :order_params # , :customer

  def order_from_params
    order = Order.where(id: (order_params[:id] || order_params[:order_id])).first_or_initialize
    order.update(default_attributes) if order.new_record?
    order
  end

  def customer
    @_customer = CustomerProfile.where(id: order_params[:customer_profile_id] || order.customer_profile_id).first
  end

  def default_attributes
    {
      status: 'draft',
      order_type: 'one-off',
      order_variant: 'event_order',
      major_category: Category.generic_category_for(group_name: 'catering-services'),
      customer_total: 0,
      customer_surcharge: 0,
      whodunnit_id: order_params[:whodunnit_id],
      uuid: SecureRandom.uuid
    }
  end

  def order_attributes
    {
      name: order_params[:event_name] || order.name,
      number_of_people: order_params[:number_of_people],
      customer_profile: customer,
      department_identity: order_params[:department_identity],
      major_category_id: order_params[:major_category_id].presence || order.major_category_id,
      meal_plan_id: order_params[:meal_plan_id],
    }
  end

  def delivery_attributes
    {
      delivery_at: (order_params[:delivery_at].present? ? Time.zone.parse(order_params[:delivery_at]) : nil),
      delivery_address_level: order_params[:delivery_level],
      delivery_address: order_params[:delivery_address],
      delivery_suburb_id: order_params[:suburb_id],
      delivery_instruction: order_params[:delivery_instruction],
    }
  end

  def contact_attributes
    {
      contact_name: order_params[:contact_name],
      contact_email: order_params[:contact_email],
      phone: order_params[:contact_phone],
      company_name: order_params[:company_name],
    }
  end

  def order_totals_attributes
    {
      customer_delivery: order_params[:delivery],
      commission: order_params[:commission],
      credit_card_id: order_params[:credit_card_id],
      invoice_individually: order_params[:invoice_individually],
    }
  end

  def purchase_order_attributes
    {
      customer_purchase_order: Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: order_params[:cpo_id].presence || order_params[:po_number]).call,
    }
  end

  def sanitized_attributes
    [
      order_attributes,
      delivery_attributes,
      contact_attributes,
      order_totals_attributes,
      purchase_order_attributes
    ].inject(&:merge)
  end

  class Result
    attr_accessor :order, :errors

    def initialize(order:)
      @order = order
      @errors = []
    end

    def success?
      order.present? && order.persisted? && errors.blank?
    end
  end

end

