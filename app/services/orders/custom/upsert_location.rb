class Orders::Custom::UpsertLocation

  def initialize(order:, location_params: {})
    @order = order
    @location_params = location_params
    @result = Result.new
  end

  def call
    @location = Locations::Fetch.new(order: order, location_params: sanitized_location_params).call
    location.update(details: location_params[:location_name]) if location_params[:location_name].present?
    if location.valid?
      result.location = location
      sync_order_lines
    else
      result.errors += location.errors.full_messages
    end
    result
  end

private

  attr_accessor :location, :result
  attr_reader :order, :location_params

  def sync_order_lines
    order_lines_params = location_params[:order_lines] || []
    order_lines_syncer = Orders::Custom::SyncOrderLines.new(location: location, order_lines_params: order_lines_params).call
    if order_lines_syncer.success?
      result.order_lines = order_lines_syncer.order_lines
    else
      result.errors += order_lines_syncer.errors
    end
    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
  end

  def sanitized_location_params
    {
      id:  location_params[:location_id],
      details: location_params[:location_name]
    }
  end

  class Result
    attr_accessor :location, :order_lines, :errors

    def initialize
      @location = nil
      @order_lines = []
      @errors = []
    end

    def success?
      location.present? && location.persisted? && errors.blank?
    end
  end

end

