class Orders::Custom::Upsert

  COMMISSION_THRESHOLD = 20
  ORDER_FIELDS = %i[event_name number_of_people credit_card_id].freeze
  DELIVERY_FIELDS = %i[delivery_at delivery_address suburb_id delivery_instruction].freeze
  CONTACT_FIELDS = %i[contact_name contact_phone].freeze
  REQUIRED_FIELDS = (ORDER_FIELDS + DELIVERY_FIELDS + CONTACT_FIELDS).freeze

  def initialize(order_params: {})
    @order_params = order_params
    @result = Result.new
  end

  def call
    ActiveRecord::Base.transaction do
      if has_reqiured_fields? && has_valid_delivery_date? && can_save_order?
        update_location if location_params.present?
        sync_custom_suppliers
        attach_coupon if order_params[:coupon_code].present?
        sync_promotions_for_order
        result.action = order_params[:custom_action]
        confirm_order if order_params[:custom_action] == 'confirm'
        save_quote if order_params[:custom_action] == 'save_quote'
        send_quote if order_params[:custom_action] == 'send_quote'
        log_event(event: 'custom-order-saved-as-draft') if order.status == 'draft'
        check_order_margin
        result.order = order
      end
    end # transaction
    result
  end

private

  attr_accessor :order, :result
  attr_reader :order_params

  def has_reqiured_fields?
    if %w[confirm save_quote send_quote].include?(order_params[:custom_action])
      REQUIRED_FIELDS.each do |field|
        if order_params[field].blank?
          field_name = I18n.t("admin.custom_order_fields.#{field}")
          result.errors << "Missing field => #{field_name}"
        end
      end
    end
    result.errors.blank?
  end

  def has_valid_delivery_date?
    return true if %w[confirm save_quote send_quote].exclude?(order_params[:custom_action])

    week_start = Time.zone.now.beginning_of_week
    month_start = Time.zone.now.beginning_of_month

    allowed_delivery_at = week_start
    if customer&.billing_frequency == 'monthly' && month_start > week_start
      allowed_delivery_at = month_start
    end

    delivery_at = order_params[:delivery_at].is_a?(String) ? Time.zone.parse(order_params[:delivery_at]) : order_params[:delivery_at]
    if delivery_at <= allowed_delivery_at
      result.errors << 'Delivery date needs to be set in future for the order to be picked up for invoicing'
    end
    result.errors.blank?
  end

  def can_save_order?
    save_order = Orders::Custom::Save.new(order_params: order_params).call
    if save_order.success?
      @order = save_order.order.reload
    else
      result.errors += save_order.errors
    end
    save_order.success?
  end

  def update_location
    location_upserter = Orders::Custom::UpsertLocation.new(order: order, location_params: location_params).call
    if !location_upserter.success?
      result.errors += location_upserter.errors
    end
  end

  def location_params
    order_params.slice(:location_id, :location_name, :order_lines)
  end

  def sync_custom_suppliers
    suppliers_params = order_params[:suppliers] || {}
    Orders::Custom::SyncSuppliers.new(order: order, suppliers_params: suppliers_params).call
    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
  end

  def attach_coupon
    coupon_attacher = Coupons::AttachToOrder.new(coupon_code: order_params[:coupon_code], order: order, profile: customer).call
    if coupon_attacher.success?
      result.order = coupon_attacher.order
    else
      result.errors += coupon_attacher.errors
    end
  end

  def sync_promotions_for_order
    Promotions::SyncWithOrder.new(order: order).call
  end

  def confirm_order
    order_status = %w[draft quoted].include?(order.status) ? 'new' : 'amended'
    order_line_status = %w[draft quoted].include?(order.status) ? 'accepted' : 'amended'

    order.update(status: order_status)
    order.order_lines.update_all(status: order_line_status)
    case order_status
    when 'new'
      send_new_order_emails_to_suppliers
      redeem_coupon if order.coupon.present?
      log_event(event: 'new-custom-order-submitted')
    when 'amended'
      Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order, notify_now: true).delay(queue: :notifications, run_at: 1.minutes.from_now).call
    end
    update_customer_last_ordered_at
  end

  def redeem_coupon
    Coupons::Redeem.new(coupon: order.coupon, order: order).call
  end

  def send_new_order_emails_to_suppliers
    suppliers_to_notify = order.supplier_profiles
    suppliers_to_notify.each do |supplier|
      Suppliers::Emails::SendNewOrderEmail.new(order: order, supplier: supplier).delay(queue: :notifications).call
    end
    order.update(suppliers_notified_at: Time.zone.now)
  end

  def save_quote
    document_reference = "#{Customers::Emails::SendEventOrderQuoteEmail::EMAIL_TEMPLATE}-#{customer.id}-#{order.version_ref}"
    Documents::Generate::CustomerOrderDetails.new(order: order, reference: document_reference, variation: 'quote').call
    order.update(status: 'quoted')
    log_event(event: 'new-custom-order-quoted', only_once: true)
  end

  def send_quote
    return if order_params[:quote_emails].blank?

    order.update(status: 'quoted')
    quote_sender = Customers::Emails::SendEventOrderQuoteEmail.new(customer: customer, order: order, quote_emails: order_params[:quote_emails], quote_message: order_params[:quote_message]).call
    if quote_sender.success?
      log_event(event: 'new-custom-order-quoted', only_once: true)
    else
      result.errors += quote_sender.errors
    end
  end

  def update_customer_last_ordered_at
    return if !Rails.env.production? && !Rails.env.test?

    Hubspot::SyncContact.new(contact: customer.user).delay(attempts: 1).call
  end

  def log_event(event:, only_once: false)
    return if only_once && EventLog.where(loggable: order, event: event).present?

    EventLogs::Create.new(event_object: order, event: event)
  end

  def check_order_margin
    return if order.commission.blank?

    markdown = 0.0
    yordar_commission = (1 - (1 - (markdown / 100)) / (1 + (order.commission.to_f / 100))).round(2)
    return if yordar_commission >= COMMISSION_THRESHOLD

    EventLogs::Create.new(event_object: order, event: 'order-below-margin-threshold', severity: 'warning', commission: yordar_commission)
  end

  def customer
    @_customer ||= order&.customer_profile || CustomerProfile.where(id: order_params[:customer_profile_id]).first
  end

  class Result
    attr_accessor :order, :action, :errors

    def initialize
      @order = nil
      @action = nil
      @errors = []
    end

    def success?
      order.present? && order.persisted? && errors.blank?
    end
  end

end

