class Orders::CalculateSupplierTotals

  def initialize(order:, order_lines: [], supplier: nil, baseline: false, save_totals: false)
    @order = order
    @supplier = supplier
    @order_lines = order_lines.presence || fetch_order_lines
    @baseline = baseline
    @save_totals = save_totals
    @totals = Totals.new(order_line_count: @order_lines.size)
  end

  def call
    if order_lines.present?
      gst_free_order_lines, gst_order_lines = order_lines.partition(&:is_gst_free)
      gst_free_subtotal = gst_free_order_lines.map{|order_line| total_cost_for(order_line) }.sum
      gst_subtotal = gst_order_lines.map{|order_line| total_cost_for(order_line) }.sum
      gst_subtotal_with_gst = gst_order_lines.map{|order_line| (total_cost_for(order_line) * (1.0 + gst)).round(2) }.sum
    else
      gst_free_subtotal = gst_subtotal = gst_subtotal_with_gst = 0
    end

    totals.subtotal = gst_free_subtotal + gst_subtotal

    totals.delivery = Orders::CalculateDelivery.new(order: order, order_lines: order_lines, profile: supplier).call
    delivery_with_gst = gst_subtotal == 0 ? totals.delivery : (totals.delivery * (1.0 + gst)).round(2)

    order_gst = totals.delivery.present? && totals.delivery > 0 ? delivery_with_gst - totals.delivery : 0 # extract GST for delivery
    order_gst += gst_subtotal_with_gst.present? && gst_subtotal_with_gst > 0 ? gst_subtotal_with_gst - (totals.subtotal - gst_free_subtotal) : 0 # extract GST from subtotal (minus the gst free subtotal)
    totals.gst = order_gst.round(2)

    totals.total = gst_subtotal_with_gst
    totals.total += gst_free_subtotal
    totals.total += delivery_with_gst
    totals.total = 0 if totals.total < 0

    if (order.is_team_order? && order.cutoff_option == 'charge_to_minimum') || (!order.is_team_order? && order.charge_to_minimum)
      order_suppliers = order.order_suppliers.where(supplier_profile_id: order_lines.map(&:supplier_profile_id))
      supplier_surcharge = order_suppliers.map do |order_supplier|
        surcharge = order_supplier.surcharge
        surcharge.present? && surcharge > 0 ? surcharge * (1 - (order_supplier.supplier_profile.commission_rate / 100)) : nil # surcharge - yordar commission
      end.compact.sum.presence || 0

      totals.topup = supplier_surcharge
      totals.total += totals.topup
    end

    update_totals if save_totals && !baseline

    totals
  end

private

  attr_accessor :order, :order_lines, :baseline, :save_totals, :totals
  attr_reader :supplier

  def gst
    @_gst ||= yordar_credentials(:yordar, :gst_percent, country_code).to_f || 0
  end

  def fetch_order_lines
    lister_options = {
      order: order,
      supplier: supplier,
      confirmed_attendees_only: order.is_team_order?,
      paid_only: order.is_team_order? && order.attendee_pays,
    }
    OrderLines::List.new(options: lister_options).call
  end

  def total_cost_for(order_line)
    cost = baseline ? order_line.baseline : order_line.cost_exc_gst(gst_country: country_code)
    cost ||= 0

    quantity = order_line.quantity.presence || 0
    (cost * quantity).round(2)
  end

  def update_totals
    order_supplier = order.order_suppliers.where(supplier_profile: supplier).first_or_create
    order_supplier.update(
      subtotal: totals.subtotal,
      delivery: totals.delivery,
      gst: totals.gst,
      total: totals.total
    )
  end

  def country_code
    @country_code ||= order.symbolized_country_code || :au
  end

  class Totals
    attr_accessor :delivery, :topup, :gst, :subtotal, :total
    attr_reader :order_line_count

    def initialize(order_line_count:)
      @order_line_count = order_line_count
      @subtotal = 0
      @delivery = 0
      @topup = 0
      @gst = 0
      @total = 0
    end

    def [](field)
      try(field).presence || 0
    end

    def to_h
      instance_variables.map do |field|
        field_name = field.to_s.gsub('@', '')
        [field_name, send(field_name)]
      end.to_h
    end
  end

end
