class Orders::List

  DELIVERY_DURATIONS = {
    'week' => 1.week,
    'fortnight' => 2.weeks,
    'month' => 1.month,
    '3-months' => 3.months,
    '6-months' => 6.months,
    'year' => 1.year,
    'all' => 99.years,
  }.freeze

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options].inject(&:merge)
  end

  def call
    @orders = base
    filter_by_customer if filter_options[:for_customer].present?
    filter_by_pantry_managers if filter_options[:for_pantry_managers].present?
    filter_non_drafts if filter_options[:non_draft].present?
    filter_voided if filter_options[:non_voided].present?
    filter_by_name if filter_options[:name].present?
    filter_by_query if filter_options[:query].present?
    filter_by_order_type if filter_options[:order_type].present?
    filter_by_order_variant if filter_options[:order_variant].present?
    filter_by_order_statuses if filter_options[:statuses].present?
    filter_by_delivery_type if filter_options[:delivery_type].present?
    filter_by_meal_plan if filter_options[:meal_plan].present? || filter_options[:meal_plan_id]
    filter_by_exclusion if filter_options[:excluded_order_ids].present?
    filter_by_delivery_time if filter_options[:for_duration].present? || filter_options[:for_date].present? || (filter_options[:from_date].present? && filter_options[:to_date].present?)
    filter_out_cancelled_recurrents if filter_options[:ignore_cancelled_recurrent]
    filter_by_suppliers if filter_options[:supplier_ids].present?
    filter_by_quotes if filter_options[:only_quotes].present?
    sort_orders
    filter_by_pagination if filter_options[:with_pagination].present? && !(filter_options[:from_date].present? && filter_options[:to_date].present?)
    @orders = orders.includes(includes) if includes.present?

    orders
  end

private

  attr_reader :includes, :filter_options, :orders

  def base
    Order.all
  end

  def filter_by_customer
    @orders = orders.where(customer_profile: filter_options[:for_customer])
  end

  def filter_by_pantry_managers
    @orders = orders.where.not(pantry_manager_id: nil).where(pantry_manager: filter_options[:for_pantry_managers])
  end

  def filter_non_drafts
    @orders = orders.where.not(orders: { status: 'draft' })
  end

  def filter_voided
    @orders = orders.where.not(orders: { status: 'voided' })
  end

  def filter_by_name
    @orders = orders.where('name ilike ?', "%#{filter_options[:name]}%")
  end

  def filter_by_query
    @orders = orders.where('CAST(id AS TEXT) ilike :starts_with OR CAST(id AS TEXT) ilike :ends_with OR name ilike :query', starts_with: "#{filter_options[:query]}%", ends_with: "%#{filter_options[:query]}", query: "%#{filter_options[:query]}%")
  end

  def filter_by_order_type
    if filter_options[:order_type] == 'team-order'
      filter_options[:order_type] = 'one-off'
      filter_options[:order_variant] = 'team_order'
    end
    @orders = orders.where(order_type: filter_options[:order_type])
  end

  def filter_by_order_variant
    filter_options[:order_variant] = %w[team_order recurring_team_order] if filter_options[:order_variant] == 'team_order'
    @orders = orders.where(order_variant: filter_options[:order_variant])
  end

  def filter_by_order_statuses
    @orders = orders.where(status: filter_options[:statuses])
  end

  def filter_by_delivery_type
    @orders = orders.where(delivery_type: filter_options[:delivery_type])
  end

  def filter_by_meal_plan
    meal_plan_id = filter_options[:meal_plan_id].presence || filter_options[:meal_plan]&.id
    @orders = orders.where(meal_plan_id: meal_plan_id)
  end

  def filter_by_exclusion
    @orders = orders.where.not(id: filter_options[:excluded_order_ids])
  end

  def sort_orders
    @orders = case
    when filter_options[:order_by].present?
      orders.order(filter_options[:order_by])
    when filter_options[:show_past].present?
      orders.order(delivery_at: :desc, id: :desc)
    else
      orders.order(delivery_at: :asc, id: :asc)
    end
  end

  def filter_by_delivery_time
    beginning_of_today = Time.zone.now.beginning_of_day
    delivery_duration = DELIVERY_DURATIONS[filter_options[:for_duration]]
    case
    when filter_options[:from_date].present? && filter_options[:to_date].present?
      starts_on = filter_options[:from_date].presence || Date.today
      starts_on = starts_on.is_a?(String) ? Date.parse(starts_on) : starts_on
      ends_on = filter_options[:to_date].presence || Date.today
      ends_on = ends_on.is_a?(String) ? Date.parse(ends_on) : ends_on
      @orders = orders.where(delivery_at: [starts_on.beginning_of_day...ends_on.end_of_day])
    when filter_options[:for_date].present?
      filter_date = filter_options[:for_date].is_a?(String) ? Time.zone.parse(filter_options[:for_date]) : filter_options[:for_date]
      @orders = orders.where(delivery_at: [filter_date.beginning_of_day...filter_date.end_of_day])
    when filter_options[:include_past].present?
      arel = Order.arel_table
      one_off_condition = arel[:order_type].eq_any([nil, 'one-off']).and(arel[:delivery_at].gteq(beginning_of_today)) # show all upcoming one-offs
      recurrent_condition = arel[:order_type].eq('recurrent').and(arel[:delivery_at].gteq(beginning_of_today)).and(arel[:delivery_at].lt(beginning_of_today + delivery_duration)) # restrict upcoming recurrents to days
      upcoming_status_condition = arel[:status].not_eq('delivered')
      upcoming_orders_condition = upcoming_status_condition.and(one_off_condition.or(recurrent_condition)) # same as upcoming logic in arel

      past_orders_condition = arel[:delivery_at].gteq(beginning_of_today - delivery_duration).and(arel[:delivery_at].lt(beginning_of_today)) # same as show past logic but in arel

      @orders = orders.where(upcoming_orders_condition.or(past_orders_condition))
    when filter_options[:show_past].present?
      @orders = orders.where(delivery_at: [(beginning_of_today - delivery_duration)...beginning_of_today])
    else # upcoming
      arel = Order.arel_table
      one_off_condition = arel[:order_type].eq_any([nil, 'one-off']).and(arel[:delivery_at].gteq(beginning_of_today)) # show all upcoming one-offs
      recurrent_condition = arel[:order_type].eq('recurrent').and(arel[:delivery_at].gteq(beginning_of_today)).and(arel[:delivery_at].lt(beginning_of_today + delivery_duration)) # restrict upcoming recurrents to days
      @orders = orders.where(one_off_condition.or(recurrent_condition))
    end
  end

  def filter_by_pagination
    page = filter_options[:with_pagination][:page]
    limit = filter_options[:with_pagination][:limit]
    @orders = orders.page(page).per(limit)
  end

  def filter_out_cancelled_recurrents
    @orders = orders.where.not('order_type = :type AND orders.status = :status AND orders.template_id <> orders.id', type: 'recurrent', status: 'cancelled')
  end

  def filter_by_suppliers
    order_line_supplier_orders = orders.joins(:order_lines).where(order_lines: { supplier_profile_id: [filter_options[:supplier_ids]] })
    order_supplier_orders = orders.joins(:order_suppliers).where(order_suppliers: { supplier_profile_id: [filter_options[:supplier_ids]] })
    @orders = orders.where(orders: { id: (order_line_supplier_orders.select(:id) + order_supplier_orders.select(:id)) })
  end

  def filter_by_quotes
    @orders = orders.where(status: 'quoted')
  end

  def default_options
    {
      for_customer: nil,
      for_pantry_managers: [],
      statuses: [],
      delivery_type: nil,
      non_draft: true,
      non_voided: true,
      name: nil,
      query: nil,
      order_type: nil,
      order_variant: nil,
      excluded_order_ids: [],
      meal_plan: nil,
      meal_plan_id: nil,
      for_duration: nil,
      show_past: false,
      include_past: false,
      order_by: nil,
      with_pagination: {},
      ignore_cancelled_recurrent: false,
      supplier_ids: nil,
      for_date: nil,
      from_date: nil,
      to_date: nil,
    }
  end

end
