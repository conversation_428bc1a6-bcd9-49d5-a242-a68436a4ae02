class Orders::CalculateTopup

  def initialize(order:, order_lines: [])
    @order = order
    @order_lines = order_lines
  end

  def call
    if is_charge_to_minimum?
      supplier_minimum_surcharge
    else
      0
    end
  end

private

  attr_reader :order, :order_lines

  def is_charge_to_minimum?
    (order.is_team_order? && order.cutoff_option == 'charge_to_minimum') || (!order.is_team_order? && order.charge_to_minimum)
  end

  def order_suppliers
    return @_order_suppliers if @_order_suppliers.present?

    suppliers = order.order_suppliers
    suppliers = order_suppliers.where(supplier_profile: order_lines.map(&:supplier_profile).uniq) if order_lines.present?
    @_order_suppliers = suppliers
  end

  def supplier_minimum_surcharge
    if order_suppliers.present?
      order_suppliers.map(&:surcharge).compact.sum
    else
      0
    end
  end

end
