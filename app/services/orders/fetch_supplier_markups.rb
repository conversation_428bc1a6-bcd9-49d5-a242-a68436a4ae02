class Orders::FetchSupplierMarkups

  MARKUP_ROUNDING_THRESHOLD = 0.25
  SupplierMarkup = Struct.new(:supplier, :markup)

  def initialize(order: , order_lines: nil)
    @order = order
    @order_lines = order_lines.presence || get_order_lines
    @supplier_markups = []
  end

  def call
    order_lines.group_by(&:supplier_profile).each do |supplier, supplier_order_lines|
      if order.is_event_order?
        supplier_markups << SupplierMarkup.new(supplier, order.commission)
      else
        supplier_markup = markup_for(supplier: supplier)
        next if supplier_markup.blank?

        supplier_markups << SupplierMarkup.new(supplier, supplier_markup)
      end
    end

    return supplier_markups.compact
  end

private

  attr_reader :order, :order_lines, :supplier_markups

  def markup_for(supplier:)
    supplier_markup = supplier.markup.round(2)
    supplier_commission = supplier.commission_rate

    markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer, required_override: :markup).call
    if markup_override.present?
      supplier_markup = markup_override.markup
      # reverts back to supplier commission if override commission is missing
      supplier_commission = markup_override.commission_rate if markup_override.commission_rate.present?
    end
    # do not return markup if commission is 0 - as per <PERSON>'s requirements
    return nil if supplier_commission.blank? || supplier_commission == 0

    # compare supplier markup against order line markup (with a +/- MARKUP_ROUNDING_THRESHOLD variation for rounding errors)
    order_line = order_lines.detect{|order_line| order_line.supplier_profile == supplier }
    return nil if order_line.blank?

    order_line_markup = (((order_line.price.to_f / order_line.baseline.to_f) * 100) - 100.0).round(2)
    has_same_markup = supplier_markup == order_line_markup
    markup_in_low_range = order_line_markup < supplier_markup && (order_line_markup + MARKUP_ROUNDING_THRESHOLD) >= supplier_markup
    markup_in_high_range = order_line_markup > supplier_markup && (order_line_markup - MARKUP_ROUNDING_THRESHOLD) <= supplier_markup
    has_same_markup || markup_in_low_range || markup_in_high_range ? supplier_markup : order_line_markup    
  end

  def customer
    @_customer ||= order.customer_profile
  end

  def get_order_lines
    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    OrderLines::List.new(options: lister_options, includes: [:supplier_profile]).call
  end

end