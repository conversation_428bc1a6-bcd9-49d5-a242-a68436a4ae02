class Orders::Amend

  def initialize(order:, order_params:, mode: nil, customer: nil)
    @order = order
    @order_params = order_params
    @mode = mode
    @customer = order&.customer_profile || customer
    @delivery_difference = calculate_delivery_difference
    @result = Result.new(order: order)
  end

  def call
    if has_changes?
      result.has_changes = true
      order_updater = Orders::Update.new(order: order, order_params: sanitized_params, profile: customer).call
      if order_updater.success?
        update_recurring_orders if order.is_recurrent? && mode.present?
        notify_for_order
        self.delay(queue: :notifications).log_event
        save_major_category
      else
        result.errors += order_updater.errors
      end
    elsif order.status == 'amended'
      notify_for_order
      update_recurring_orders if order.is_recurrent?
    end
    update_customer_quote if order.customer_quote_id.present?
    result
  end

private

  attr_reader :order, :order_params, :mode, :customer, :result

  def update_recurring_orders
    case mode
    when 'one-off'
      Orders::Recurring::SaveAsOneOff.new(order: order).call
    when 'subsequent'
      Orders::Recurring::UpdateSubsequentOrders.new(order: order, order_params: sanitized_params, delivery_difference: @delivery_difference).call
    end
  end

  def update_customer_quote
    customer_quote = order.customer_quote
    return if customer_quote.blank? || order.customer_quote.status == 'accepted'

    order.customer_quote.update(status: 'accepted')
  end

  def calculate_delivery_difference
    return nil if !order.is_recurrent?

    original_delivery_at = order.delivery_at.dup
    new_delivery_at = order_params[:delivery_at].present? && order_params[:delivery_at].is_a?(String) ? Time.zone.parse(order_params[:delivery_at]) : order_params[:delivery_at]
    if new_delivery_at.present? && new_delivery_at != original_delivery_at
      new_delivery_at - original_delivery_at
    else
      nil
    end
  end

  def sanitized_params
    return @_sanitized_params if @_sanitized_params.present?

    params = default_amended_params
    params = params.merge(purchase_order_params)
    params = params.merge(order_params.to_h.except(:cpo_id, :gst_free_cpo_id, :po_number))
    @_sanitized_params = params.symbolize_keys
  end

  def default_amended_params
    {
      status: 'amended',
    }
  end

  def purchase_order_params
    {
      customer_purchase_order: Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: order_params[:cpo_id]).call,
      gst_free_customer_purchase_order: Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: order_params[:gst_free_cpo_id]).call,
    }
  end

  def has_changes?
    order.assign_attributes(sanitized_params.except(:status))
    order_lines_amended = order.order_lines.where(status: 'amended').present?
    order.changed? || woolworths_order_changed? || order_lines_amended
  end

  def woolworths_order_changed?
    return false if sanitized_params[:associated_woolworths_order_attributes].blank?
    return false if order.woolworths_order.blank?

    woolworths_order = order.woolworths_order
    woolworths_order.assign_attributes(sanitized_params[:associated_woolworths_order_attributes])
    woolworths_order.changed?
  end

  def notify_for_order
    notify_supplier
    notify_account_manager
  end

  def notify_supplier
    Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order, notify_now: true).delay(queue: :notifications).call
  end

  def notify_account_manager
    Orders::Notifications::NotifyAccountManagers.new(order: order).delay(queue: :notifications).call
  end

  def log_event
    event_info = {}
    lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order).call
    if !lead_time_fetcher.can_process?
      event_info[:after_cutoff] = true
    end

    supplier_spends_fetcher = Orders::GetSupplierSpends.new(order: order, exclude_surcharge: true).call
    if supplier_spends_fetcher.is_under?
      event_info[:under_supplier_minimum] = true
      event_info[:supplier_spends] = supplier_spends_fetcher.supplier_spends.select(&:is_under?).map do |supplier_spend|
        {
          name: supplier_spend.supplier.name,
          minimum: supplier_spend.minimum_spend,
          remaining: supplier_spend.remaining_spend
        }
      end
    end
    if event_info.present?
      EventLogs::Create.new(event_object: order, event: 'order-amended', severity: 'warning', **event_info).call
    end
  end

  def save_major_category
    Orders::RetrieveMajorOrderCategory.new(order: order, save_category: true).delay(queue: :data_integrity).call
  end

  class Result
    attr_accessor :order, :has_changes, :errors

    def initialize(order:)
      @order = order
      @has_changes = false
      @errors = []
    end

    def success?
      errors.blank? && (!has_changes || order.reload.status == 'amended')
    end
  end

end
