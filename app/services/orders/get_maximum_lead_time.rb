class Orders::GetMaximumLeadTime

  SupplierLeadTime = Struct.new(:supplier, :formatted_lead_time, :minimum_delivery_at)

  def initialize(order: nil, new_delivery_at: nil, time: nil, supplier_ids: [])
    @order = order
    @time = time || Time.zone.now
    @supplier_ids = supplier_ids
    @result = Result.new(delivery_at: get_delivery_at_with(new_delivery_at))
  end

  def call
    if suppliers.blank?
      result.minimum_delivery_at = time
    elsif max_supplier_lead_time.present?
      result.formatted_lead_time = max_supplier_lead_time.formatted_lead_time
      result.minimum_delivery_at = max_supplier_lead_time.minimum_delivery_at
    end
    result
  end

private

  attr_reader :order, :time, :supplier_ids, :result

  def get_delivery_at_with(new_delivery_at)
    case
    when new_delivery_at.present? && new_delivery_at.is_a?(String)
      Time.zone.parse(new_delivery_at)
    when new_delivery_at.present?
      new_delivery_at
    when order.blank?
      time
    else
      order.delivery_at
    end
  end

  def max_supplier_lead_time
    @_max_supplier_lead_time ||= supplier_lead_times.max{|slt1, slt2| slt1.minimum_delivery_at <=> slt2.minimum_delivery_at }
  end

  def supplier_lead_times
    suppliers.map do |supplier|
      case supplier.lead_mode
      when 'by_hour'
        get_lead_time_by_hour_for(supplier)
      when 'by_day_before'
        get_lead_time_by_day_for(supplier)
      else
        unkown_lead_time_for(supplier)
      end
    end
  end

  def get_lead_time_by_hour_for(supplier)
    minimums = minimums_for(supplier)
    return unkown_lead_time_for(supplier) if minimums.blank?

    max_lead_time = minimums.max{|m1, m2| m1.lead_time <=> m2.lead_time }
    max_minimum_delivery_at = time + max_lead_time.lead_time.hours
    SupplierLeadTime.new(supplier, max_lead_time.formatted_lead_time, max_minimum_delivery_at)
  end

  def get_lead_time_by_day_for(supplier)
    minimums = minimums_for(supplier)
    return unkown_lead_time_for(supplier) if minimums.blank?

    min_lead_time = minimums.min{|m1, m2| m1.lead_time_day_in_numbers <=> m2.lead_time_day_in_numbers }
    lead_time_hours = min_lead_time.lead_time_day_in_numbers / 100
    lead_time_mins = min_lead_time.lead_time_day_in_numbers - (lead_time_hours * 100)
    lead_time_for_time = time.beginning_of_day + lead_time_hours.hours + lead_time_mins.minutes
    minimum_delivery_at = time.beginning_of_day + 1.day
    minimum_delivery_at += 1.day if lead_time_for_time < time

    SupplierLeadTime.new(supplier, min_lead_time.formatted_lead_time, minimum_delivery_at)
  end

  def suppliers
    @_suppliers ||= supplier_ids.present? ? SupplierProfile.where(id: supplier_ids) : order&.supplier_profiles
  end

  def supplier_grouped_order_lines
    return {} if order.blank?

    @_supplier_grouped_order_lines ||= order.order_lines.group_by(&:supplier_profile)
  end

  def minimums_for(supplier)
    return @_supplier_minimums[supplier] if @_supplier_minimums.present? && @_supplier_minimums[supplier].present?

    supplier_order_lines = supplier_grouped_order_lines[supplier]
    order_line_categories = supplier_order_lines.present? && supplier_order_lines.map(&:category).compact.uniq
    base_minimums = Minimum.where(supplier_profile: supplier).includes(:category)
    minimums = base_minimums.select{|minimum| order_line_categories.map(&:id).include?(minimum.category_id) } if order_line_categories.present?
    minimums = base_minimums.select{|minimum| order_line_categories.map(&:group).include?(minimum.category.group) } if minimums.blank? && order_line_categories.present?
    minimums = base_minimums if minimums.blank?
    @_supplier_minimums ||= {}
    @_supplier_minimums[supplier] = minimums
  end

  def unkown_lead_time_for(supplier)
    SupplierLeadTime.new(supplier, nil, time)
  end

  class Result
    attr_accessor :formatted_lead_time, :minimum_delivery_at
    attr_reader :delivery_at

    def initialize(delivery_at:)
      @delivery_at = delivery_at
      @formatted_lead_time = nil
      @minimum_delivery_at = nil
    end

    def can_process?
      delivery_at.blank? || minimum_delivery_at < delivery_at
    end
  end

end
