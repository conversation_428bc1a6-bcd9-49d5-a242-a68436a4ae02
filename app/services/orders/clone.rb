class Orders::Clone

  ORDER_FIELDS = %i[
    name
    credit_card_id
    number_of_people
    charge_to_minimum
    invoice_individually
  ].freeze

  CONTACT_FIELDS = %i[
    contact_name
    company_name
    contact_email
    phone
    cpo_id
    department_identity
  ].freeze

  DELIVERY_FIELDS = %i[
    delivery_address_level
    delivery_address
    delivery_suburb_id
    delivery_instruction
    delivery_type
  ].freeze

  def initialize(order:)
    @order = order
    @result = Result.new(order: order)
  end

  def call
    clone_order
    set_defaults
    if cloned_order.save
      if order_lines.present?
        create_order_lines
        check_item_availability
        sync_supplier_overrides
      end
      result.cloned_order = cloned_order.reload
      calculate_totals
      void_original_order if is_saved_woolworths_order?
    else
      result.errors << cloned_order.errors.full_messages
    end
    result
  end

private

  attr_accessor :order, :cloned_order, :result

  def clone_order
    @cloned_order = order_customer.orders.new do |customer_order|
      clonable_fields.each do |field|
        customer_order.send("#{field}=", order.send(field))
      end
    end
    result.cloned_order = cloned_order
  end

  def clonable_fields
    fields = (ORDER_FIELDS + CONTACT_FIELDS + DELIVERY_FIELDS)
    fields << 'delivery_at' if is_saved_woolworths_order? && order.delivery_at > Time.zone.now
    fields
  end

  def set_defaults
    cloned_order.status = 'draft'
    cloned_order.order_type = 'one-off'
    cloned_order.order_variant = 'general'
    cloned_order.uuid = SecureRandom.uuid
  end

  def create_order_lines
    location_grouped_order_lines = order_lines.group_by(&:location)
    location_grouped_order_lines.each do |orignial_location, location_order_lines|
      location = duplicate_location(orignial_location)
      order_params = { order_id: cloned_order.id, location_id: location.id }
      order_lines_params = location_order_lines.map do |order_line|
        {
          item_id: order_line.menu_item_id,
          serving_size_id: order_line.serving_size_id,
          selected_menu_extra_ids: order_line.selected_menu_extras,
          note: order_line.note,
          quantity: order_line.quantity,
        }
      end
      multiple_creator = OrderLines::CreateMultiple.new(order: cloned_order, order_params: order_params, order_lines_params: order_lines_params).call
      if !multiple_creator.success?
        result.errors += multiple_creator.errors
      end
    end
  end

  def check_item_availability
    item_availability = Orders::CheckItemAvailability.new(order: cloned_order).call
    return if item_availability.success?

    result.warnings += item_availability.errors
  end

  def sync_supplier_overrides
    overriden_order_suppliers = order.order_suppliers.where(supplier_profile: order.supplier_profiles).where.not(delivery_fee_override: nil)
    return if overriden_order_suppliers.blank?

    overriden_order_suppliers.each do |order_supplier|
      new_order_supplier = cloned_order.order_suppliers.where(supplier_profile: order_supplier.supplier_profile).first_or_create
      new_order_supplier.update(delivery_fee_override: order_supplier.delivery_fee_override)
    end
  end

  def duplicate_location(orignial_location)
    location = orignial_location.dup
    location.order = cloned_order
    location.save
    location
  end

  def calculate_totals
    Orders::CalculateCustomerTotals.new(order: cloned_order, save_totals: true).call
    cloned_order.supplier_profiles.each do |supplier|
      Orders::CalculateSupplierTotals.new(order: cloned_order, supplier: supplier, save_totals: true).delay(queue: :data_integrity).call
    end
  end

  def void_original_order
    order.update(status: 'voided')
  end

  def order_customer
    @_order_customer ||= order.customer_profile
  end

  def order_lines
    @_order_lines = begin
      lister_options = {
        order: order,
      }
      OrderLines::List.new(options: lister_options, includes: [:location]).call
    end
  end

  def is_saved_woolworths_order?
    order.status == 'saved' && order.woolworths_order.present?
  end

  class Result
    attr_accessor :cloned_order, :warnings, :errors
    attr_reader :orginial_order

    def initialize(order:)
      @orginial_order = order
      @cloned_order = nil
      @warnings = []
      @errors = []
    end

    def success?
      errors.blank? && cloned_order.present? && cloned_order.persisted?
    end
  end

end
