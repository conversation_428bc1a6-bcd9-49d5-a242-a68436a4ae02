class Orders::ProcessCardPayments

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new
  end

  def call
    card_orders.each do |order|
      card_payer = Orders::PayByCreditCard.new(order: order).call
      if card_payer.success?
        result.processed_payments << card_payer.payment
      else
        result.errors += card_payer.errors
      end
    end
    result
  end

private

  attr_reader :time, :result

  def card_orders
    return @_card_orders if @_card_orders.present?

    card_orders = Order.where(status: 'delivered') # delivered orders only
    card_orders = card_orders.where(payment_status: [nil, '', 'unpaid', 'error']) # unpaid order

    # order connected to actual credit cards and not the pay on account cards
    arel = CreditCard.arel_table
    valid_token_condition = arel[:gateway_token].not_eq_any([nil, '']).or(arel[:stripe_token].not_eq_any([nil, '']))

    card_orders = card_orders.joins(:credit_card)
    card_orders = card_orders.where(credit_cards: { pay_on_account: false, auto_pay_invoice: false })
    card_orders = card_orders.where(valid_token_condition) # credit card has a valid gateway token eWay or stripe

    card_orders = card_orders.joins(:invoice).where(invoices: { created_at: [invoice_times[:starts]...invoice_times[:ends]] })
    card_orders = card_orders.where.not(invoices: { payment_status: 'paid' }) # with attached unpaid invoice
    @_card_orders = card_orders.distinct
  end

  def invoice_times
    ends = time.end_of_day
    starts = ends - 1.week
    { starts: starts, ends: ends }
  end

  class Result
    attr_accessor :processed_payments, :errors

    def initialize
      @processed_payments = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
