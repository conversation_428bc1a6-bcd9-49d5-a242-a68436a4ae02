class Orders::CalculateCustomerTotals

  def initialize(order:, order_lines: [], attendee: nil, gst_split: nil, save_totals: false)
    @order = order
    @attendee = attendee
    @gst_split = gst_split
    @order_lines = fetch_order_lines(scoped_order_lines: order_lines)
    @totals = Totals.new(order_line_count: @order_lines.size)
    @save_totals = save_totals
  end

  def call
    if order_lines.present?
      gst_free_order_lines, gst_order_lines = order_lines.partition(&:is_gst_free)
      gst_free_subtotal = gst_free_order_lines.map{|order_line| order_line.total_price(gst_country: country_code).round(2) }.sum
      gst_subtotal = gst_order_lines.map{|order_line| order_line.total_price(gst_country: country_code).round(2) }.sum
      gst_subtotal_with_gst = gst_order_lines.map{|order_line| (order_line.total_price(gst_country: country_code) * (1.0 + gst)).round(2) }.sum
    else
      gst_free_subtotal = gst_subtotal = gst_subtotal_with_gst = 0
    end

    totals.subtotal = gst_free_subtotal + gst_subtotal

    # do not calculate topup for attendee or GST only items
    if attendee.present? || gst_split == 'GST'
      totals.topup = 0.0
    else
      totals.topup = Orders::CalculateTopup.new(order: order).call
    end

    # do not calculate delivery for attendee or GST-FREE only items
    if attendee.present? || gst_split == 'GST-FREE'
      totals.delivery = 0.0
    elsif gst_split == 'GST'
      all_order_lines = fetch_order_lines(with_gst_split: false)
      totals.delivery = Orders::CalculateDelivery.new(order: order, order_lines: all_order_lines, profile: order.customer_profile, recalculate: save_totals).call
    else
      totals.delivery = Orders::CalculateDelivery.new(order: order, order_lines: order_lines, profile: order.customer_profile, recalculate: save_totals).call
    end
    delivery_with_gst = gst_subtotal == 0 ? totals.delivery : (totals.delivery * (1.0 + gst)).round(2)

    order_gst = totals.delivery.present? && totals.delivery > 0 ? delivery_with_gst - totals.delivery : 0 # extract GST for delivery
    order_gst += gst_subtotal_with_gst.present? && gst_subtotal_with_gst > 0 ? gst_subtotal_with_gst - (totals.subtotal - gst_free_subtotal) : 0 # extract GST from subtotal (minus the gst free subtotal)
    totals.gst = order_gst.round(2)

    discount_gst = 0
    if order_lines.present?
      discountable_amount = totals.delivery + totals.subtotal
      totals.discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount, recalculate: (save_totals || gst_split.present?)).call
      totals.discount = totals.delivery + totals.subtotal if totals.discount >= discountable_amount

      discount_gst = gst_split == 'GST-FREE' ? 0 : (totals.discount * 0.1).round(2)
      if totals.gst > 0
        totals.gst -= discount_gst
        if totals.gst == 0.01 # fix rounding errors with discount gst calculations
          discount_gst += 0.01
          totals.gst = 0
        end
      end
    end

    # totals.gst = (gst_subtotal_with_gst + delivery_with_gst) - ( gst_subtotal + totals.delivery ) - discount_gst # as per Xero

    totals.total = gst_subtotal_with_gst
    totals.total += gst_free_subtotal
    totals.total += totals.topup
    totals.total += delivery_with_gst
    totals.total -= totals.discount
    totals.total -= discount_gst

    totals.total = 0.0 if totals.total < 0
    totals.gst = 0.0 if totals.gst < 0

    if attendee.present? || gst_split == 'GST'
      totals.surcharge = 0.0
    else
      # calculate credit card surcharge and update total
      totals.surcharge = Orders::CalculateSurcharge.new(order: order, total: totals.total).call
      totals.total += totals.surcharge
    end

    update_totals if save_totals && attendee.blank? && gst_split.blank?

    totals
  end

private

  attr_accessor :order, :order_lines, :totals
  attr_reader :save_totals, :attendee, :gst_split

  def update_totals
    order.update_columns(
      customer_subtotal: totals.subtotal,
      customer_delivery: totals.delivery,
      discount: totals.discount,
      customer_gst: totals.gst,
      customer_topup: totals.topup,
      customer_surcharge: totals.surcharge,
      customer_total: totals.total
    )
  end

  def gst
    @_gst ||= yordar_credentials(:yordar, :gst_percent, country_code).to_f || 0
  end

  def country_code
    @_country_code = order.symbolized_country_code || :au
  end

  def fetch_order_lines(scoped_order_lines: nil, with_gst_split: true)
    lister_options = {
      order: order,
      for_attendee: order.is_team_order? && attendee.present? ? attendee : false,
      confirmed_attendees_only: order.is_team_order? && attendee.blank?,
      paid_only: order.is_team_order? && attendee.blank? && order.attendee_pays,
      gst_split: with_gst_split && gst_split,
    }
    fetched_order_lines = OrderLines::List.new(options: lister_options).call
    if scoped_order_lines.present?
      fetched_order_lines = fetched_order_lines.where(id: scoped_order_lines.map(&:id))
    end
    fetched_order_lines
  end

  class Totals
    attr_accessor :delivery, :gst, :surcharge, :topup, :discount, :subtotal, :total
    attr_reader :order_line_count

    def initialize(order_line_count:)
      @order_line_count = order_line_count
      @subtotal = 0
      @delivery = 0
      @discount = 0
      @topup = 0
      @gst = 0
      @surcharge = 0
      @total = 0
    end

    def [](field)
      try(field)
    end

    def to_h
      instance_variables.map do |field|
        field_name = field.to_s.gsub('@', '')
        [field_name, send(field_name)]
      end.to_h
    end
  end

end
