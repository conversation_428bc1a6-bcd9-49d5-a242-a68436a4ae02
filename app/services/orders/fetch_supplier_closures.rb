class Orders::FetchSupplierClosures

  def initialize(order: nil, new_delivery_at:, suburb_id: nil, supplier_ids: [])
    @order = order
    @new_delivery_at = new_delivery_at.present? && new_delivery_at.is_a?(String) ? Time.zone.parse(new_delivery_at) : new_delivery_at
    @suburb_id = suburb_id
    @supplier_ids = supplier_ids
    @result = Result.new
  end

  def call
    return result if order&.delivery_at.present? && new_delivery_at == order.delivery_at

    orders.each do |order|
      order_delivery_at = delivery_at_for(order)
      suppliers_for(order).each do |supplier|
        next if result.closures.detect{|closure| closure.supplier == supplier }.present?

        supplier_closures = supplier_closures_for(supplier: supplier, delivery_at: order_delivery_at)
        if supplier_closures.closure_dates.include?(order_delivery_at.to_date) # check if there is any overlap
          supplier_closure = supplier_closures.closures.detect do |closure|
            closure.days_in_between.include?(order_delivery_at.to_date)
          end
          next if supplier_closure.blank?

          starts_on = supplier_closure.starts_on || order_delivery_at
          ends_on = supplier_closure.ends_on || order_delivery_at

          result.closures << Closure.new(supplier: supplier, close_from: starts_on, close_to: ends_on)
        else
          check_working_hours_for(supplier: supplier, delivery_at: order_delivery_at)
        end
      end
    end
    result
  end

private

  attr_reader :order, :new_delivery_at, :suburb_id, :supplier_ids, :result

  def is_processing_recurrent_order?
    return false if order.blank?

    order.is_recurrent? && order.status == 'draft'
  end

  def delivery_at_for(delivery_order)
    case
    when is_processing_recurrent_order? && delivery_order != order
      Orders::GetNextRecurringDeliveryAt.new(order: delivery_order, new_delivery_at: new_delivery_at).call
    else
      new_delivery_at
    end
  end

  def orders
    case
    when order.blank?
      [Order.new(status: 'draft', delivery_at: new_delivery_at)]
    when is_processing_recurrent_order?
      Order.where(recurrent_id: order.recurrent_id)
    else
      [order]
    end
  end

  def suppliers_for(order)
    case
    when supplier_ids.present?
      SupplierProfile.where(id: supplier_ids)
    when order.persisted?
      order.supplier_profiles
    else
      []
    end
  end

  def supplier_closures_for(supplier:, delivery_at:)    
    Suppliers::ListClosureDates.new(supplier: supplier, from_date: delivery_at, until_date: delivery_at + 2.months, delivery_zone: delivery_zone_for(supplier, delivery_at)).call
  end

  def check_working_hours_for(supplier:, delivery_at:)
    delivery_zone = delivery_zone_for(supplier, delivery_at)
    return if delivery_zone.blank?

    operating_starts_at = Time.at(delivery_zone.operating_hours_start).utc
    operating_ends_at = Time.at(delivery_zone.operating_hours_end).utc

    starts_at_on_delivery_date = delivery_at.change(hour: operating_starts_at.hour, min: operating_starts_at.min)
    ends_at_on_delivery_date = delivery_at.change(hour: operating_ends_at.hour, min: operating_ends_at.min)

    if !delivery_at.between?(starts_at_on_delivery_date, ends_at_on_delivery_date)
      result.closures << Closure.new(supplier: supplier, operating_hours: [starts_at_on_delivery_date, ends_at_on_delivery_date])
    end
  end

  def delivery_zone_for(supplier, delivery_at)
    return nil if suburb_id.blank? || supplier.blank?

    delivery_suburb = Suburb.where(id: suburb_id).first
    return nil if delivery_suburb.blank?

    Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: delivery_suburb, delivery_date: delivery_at).call
  end

  class Result
    attr_accessor :closures

    def initialize
      @closures = []
    end

    def has_closures?
      closures.any?{|closure| closure.close_from.present? && closure.close_to.present? }
    end

    def outside_operating_hours?
      closures.any?{|closure| closure.operating_hours.present? }
    end
  end

  class Closure
    attr_reader :supplier, :close_from, :close_to, :operating_hours

    def initialize(supplier:, close_from: nil, close_to: nil, operating_hours: [])
      @supplier = supplier
      @close_from = close_from&.beginning_of_day
      @close_to = close_to&.end_of_day
      @operating_hours = operating_hours
    end
  end

end
