class Orders::FetchSupplierAvailability

  def initialize(order:, suppliers: [], suburb: nil)
    @order = order
    @suppliers = suppliers.presence || retrieve_order_suppliers
    @suburb = suburb.presence || order.delivery_suburb
    @result = Result.new(suburb: @suburb)
  end

  def call
    suppliers.each do |supplier|
      fetch_delivery_zone_for(supplier)
    end
    result
  end

private

  attr_reader :order, :suppliers, :suburb, :result

  def retrieve_order_suppliers
    order.is_team_order? ? order.team_suppliers_profiles : order.supplier_profiles
  end

  def fetch_delivery_zone_for(supplier)
    supplier_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb, delivery_date: order.delivery_at).call
    result.deliverable_suppliers[supplier] = supplier_zone
  end

  class Result
    attr_accessor :suburb, :deliverable_suppliers

    def initialize(suburb:)
      @suburb = suburb
      @deliverable_suppliers = {}
    end

    def available?
      deliverable_suppliers.present? && deliverable_suppliers.all?{|_, supplier_zone| supplier_zone.present? }
    end
  end

end
