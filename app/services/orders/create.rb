class Orders::<PERSON><PERSON>

  def initialize(order_params:, suburb: nil, customer: nil, cookies: nil, whodunnit: nil)
    @order = Order.new
    @order_params = order_params
    @suburb = suburb
    @customer = customer
    @cookies = cookies
    @whodunnit = whodunnit
    @result = Result.new
  end

  def call
    if order.update(sanitized_attributes)
      attach_meal_plan_to_order if order_params[:mealUUID].present?
      attach_order_to_customer_quote if order_params[:quoteUUID].present?
      process_woolworths_delivery_details if order_params[:is_woolworths_order]
      result.order = order
    end
    result
  end

private

  attr_accessor :order, :order_params, :suburb, :customer, :cookies, :whodunnit, :result

  def sanitized_attributes
    [
      default_attributes,
      variant_attributes,
      whodunnit_attributes,
      cookie_attributes,
      delivery_suburb_attributes,
      order_params.to_h.except(:is_home_delivery, :is_woolworths_order, :mealUUID, :quoteUUID).symbolize_keys
    ].inject(&:merge)
  end

  def default_attributes
    {
      status: 'draft',
      order_type: 'one-off',
      customer_profile: customer,
      uuid: SecureRandom.uuid
    }
  end

  def variant_attributes
    return {} if order_params[:is_home_delivery].blank?

    {
      order_variant: 'home_delivery'
    }
  end

  def whodunnit_attributes
    return {} if whodunnit.blank?

    {
      whodunnit_id: whodunnit.id
    }
  end

  def cookie_attributes
    return {} if cookies.blank?

    {
      delivery_address: cookies[:yordar_street_address],
      delivery_suburb_id: cookies[:yordar_suburb_id],
    }
  end

  def delivery_suburb_attributes
    return {} if suburb.blank?

    {
      delivery_suburb_id: suburb.id,
    }
  end

  def delivery_detail_errors
    errors = []
    errors << 'Please select a valid street address' if order.delivery_address.blank?
    errors << 'We are unable to process the order for this suburb' if order.delivery_suburb.blank?
    errors << 'Please select a valid delivery date-time' if order.delivery_at.blank?
    errors
  end

  def attach_meal_plan_to_order
    return if order_params[:mealUUID].blank? || order.meal_plan_id.present?

    meal_plan = customer.meal_plans.where(archived_at: nil, uuid: order_params[:mealUUID]).first
    return if meal_plan.blank?

    MealPlans::AttachToOrder.new(order: order, meal_plan: meal_plan, order_params: order_params).call
  end

  def attach_order_to_customer_quote
    quote = customer.quotes.where(uuid: order_params[:quoteUUID]).first
    return if quote.blank?

    Customers::Quotes::AttachToOrder.new(order: order, quote: quote, order_params: order_params).call
  end

  def process_woolworths_delivery_details
    if (delivery_errors = delivery_detail_errors.presence)
      result.errors += delivery_errors
      return
    end

    begin
      Woolworths::Order::AttachToOrder.new(order: order).call
      Woolworths::API::EmptyTrolley.new(order: order).call
    rescue Woolworths::Order::AttachToOrder::NoAccountsAvailableError, Woolworths::API::Connection::ConnectionError, Woolworths::API::Connection::NoAccountError, Woolworths::API::EmptyTrolley::EmptyTrolleyError
      result.errors << 'The Woolworths monkeys are busy right now, please try again in a few minutes'
    else
      Woolworths::ProcessOrderDeliveryDetails.new(order: order).delay(queue: :instant, attempts: 2).call
    end
  end

  class Result
    attr_accessor :order, :errors

    def initialize
      @order = nil
      @errors = []
    end

    def success?
      errors.blank? && order.present?
    end
  end
end
