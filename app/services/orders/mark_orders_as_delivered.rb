class Orders::<PERSON><PERSON><PERSON><PERSON>AsDelivered

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new
  end

  def call
    day_old_confirmable_orders.each do |order|
      if order.order_lines.present?
        if order.update(status: 'delivered')
          result.delivered_orders << order
        else
          result.errors << "Could not mark order ##{order.id} as delivered #{order.errors.full_messages.join(', ')}"
        end
      else
        order.update(status: 'draft')
      end
    end
    result
  end

private

  attr_reader :time, :result

  def day_old_confirmable_orders
    orders = Order.where(status: %w[confirmed amended]) # amended/confirmed orders only
    orders = orders.where(split_order_id: nil) # is not a split order
    orders = orders.where('delivery_at <= ?', (time - 1.day).end_of_day) # day old orders
    orders.includes(:order_lines)
  end

  class Result
    attr_accessor :delivered_orders, :errors

    def initialize
      @delivered_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
