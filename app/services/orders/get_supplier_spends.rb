class Orders::GetSupplierSpends

  def initialize(order:, exclude_surcharge: false)
    @order = order
    @exclude_surcharge = exclude_surcharge
    @result = Result.new(order: order)
  end

  def call
    suppliers.each do |supplier|
      total_spend = total_spend_for(supplier)
      minimum_spend = minimum_spend_for(supplier)
      result.supplier_spends << SupplierSpend.new(supplier: supplier, total_spend: total_spend, minimum_spend: minimum_spend)
    end
    result
  end

private

  attr_reader :order, :exclude_surcharge, :result

  def suppliers
    @_suppliers = order.is_team_order? ? order.team_supplier_profiles : order.supplier_profiles
  end

  def supplier_grouped_minimums
    @_supplier_grouped_minimums ||= Minimum.where(supplier_profile: suppliers).includes(:category).group_by{|minimum| minimum.supplier_profile_id.to_s }
  end

  def supplier_grouped_order_lines
    return @_supplier_grouped_order_lines if @_supplier_grouped_order_lines.present?

    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    order_lines = OrderLines::List.new(options: lister_options, includes: [:category]).call
    @_supplier_grouped_order_lines = order_lines.group_by{|order_line| order_line.supplier_profile_id.to_s }
  end

  def total_spend_for(supplier)
    total_spend = order_line_spend_for(supplier)
    total_spend += surcharge_for(supplier) if !exclude_surcharge
    total_spend
  end

  def surcharge_for(supplier)
    order_supplier = order.order_suppliers.where(supplier_profile: supplier).first
    order_supplier.present? && order_supplier.surcharge.present? ? order_supplier.surcharge : 0
  end

  def order_line_spend_for(supplier)
    return 0 if supplier_grouped_order_lines[supplier.id.to_s].blank?

    supplier_grouped_order_lines[supplier.id.to_s].map do |order_line|
      order_line.total_price(gst_country: country_code).round(2)
    end.sum
  end

  def gst
    @_gst ||= yordar_credentials(:yordar, :gst_percent, country_code).to_f || 0
  end

  def country_code
    @_country_code ||= order.symbolized_country_code || :au
  end

  def minimums_for(supplier)
    supplier_order_line_categories = supplier_grouped_order_lines[supplier.id.to_s].present? ? supplier_grouped_order_lines[supplier.id.to_s].map(&:category).compact.uniq : []
    supplier_minimums = supplier_grouped_minimums[supplier.id.to_s].present? ? supplier_grouped_minimums[supplier.id.to_s] : []
    category_supplier_minimums = supplier_minimums.select{|minimum| supplier_order_line_categories.map(&:id).include?(minimum.category_id) } if supplier_order_line_categories.present?
    category_group_supplier_minimums = supplier_minimums.select{|minimum| supplier_order_line_categories.map(&:group).include?(minimum.category.group) } if supplier_order_line_categories.present? && category_supplier_minimums.blank?
    category_supplier_minimums.presence || category_group_supplier_minimums.presence || supplier_minimums
  end

  def minimum_spend_for(supplier)
    minimums_for(supplier).map(&:spend_price).max || 0
  end

  class Result
    attr_reader :order
    attr_accessor :supplier_spends

    def initialize(order:)
      @order = order
      @supplier_spends = []
    end

    def minimum_spend
      supplier_spends.map(&:minimum_spend).sum
    end

    def total_spend
      supplier_spends.map(&:total_spend).sum
    end

    def remaining_spend
      remaining = minimum_spend - total_spend
      remaining > 0 ? remaining : nil
    end

    def is_under?
      supplier_spends.any?(&:is_under?)
    end
  end

end
