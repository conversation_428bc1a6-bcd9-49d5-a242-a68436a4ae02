class Orders::ConvertToRecurrent

  SORTED_DAY_NAMES = Date::DAYNAMES.map{|day| day.downcase.slice(0, 3) }
  FREQUENCY_PATTERN_MAP = {
    weekly: '1.week',
    fortnightly: '2.weeks',
    monthly: '1.month',
    bi_fortnightly: '4.weeks',
  }.freeze

  def initialize(order:, recurrent_params: {})
    @order = order
    @recurrent_params = recurrent_params
    @result = Result.new(order: order, days: recurrent_days)
  end

  def call
    if can_convert?
      if order.update(sanitized_order_params)
        result.order = order.reload
      else
        result.errors += order.errors.full_messages
      end
      create_recurrents if recurrent_days.present?
    end
    result
  end

private

  attr_reader :recurrent_params
  attr_accessor :order, :result

  def can_convert?
    case
    when order.blank?
      result.errors << 'Cannot convert an order that does not exist'
    when recurrent_pattern.blank?
      result.errors << 'Cannot convert an order without repeat frequency'
    when recurrent_days.blank?
      result.errors << 'Cannot convert an order without repeat days'
    end
    result.errors.blank?
  end

  def recurrent_pattern
    return nil if recurrent_params[:frequency].blank?

    FREQUENCY_PATTERN_MAP[recurrent_params[:frequency].to_sym]
  end

  def recurrent_days
    return @_recurrent_days if !@_recurrent_days.nil?

    days = case
    when recurrent_params[:recurrent_days].present?
      recurrent_params[:recurrent_days].map(&:to_s)
    else
      recurrent_params[:days].select{|_, selected| selected.present? && [true, 'true'].include?(selected) }.keys.map(&:to_s) || []
    end
    days = days.reject{|day| SORTED_DAY_NAMES.exclude?(day) } # remove unwanted day names
    @_recurrent_days = days.sort_by{|day| SORTED_DAY_NAMES.index(day) } # sort day names
  end

  def sanitized_order_params
    {
      pattern: recurrent_pattern,
      recurrent_id: order.id,
      template_id: order.id,
      order_type: 'recurrent',
      skip: recurrent_params[:skip].present? && recurrent_params[:skip],
      recurring_order_params: { days: recurrent_days },
      name: recurrent_days.first,
    }
  end

  def create_recurrents
    recurrent_days[1..].each do |day|
      recurrent_order = order.dup
      recurrent_attributes = { name: day, template_id: recurrent_order.id, uuid: SecureRandom.uuid }
      if recurrent_order.update(recurrent_attributes)
        if recurrent_params[:copy_all].present?
          clone_order_lines(recurrent_order)
          clone_empty_locations(recurrent_order)
        end
        result.recurrent_orders << recurrent_order
      end
    end
  end

  def location_grouped_order_lines
    @_location_grouped_order_lines ||= order.order_lines.group_by(&:location)
  end

  def clone_order_lines(recurrent_order)
    location_grouped_order_lines.each do |location, order_lines|
      dup_location = location.dup
      dup_location.order = recurrent_order
      dup_location.save!
      order_lines.each do |order_line|
        dup_order_line = order_line.dup
        dup_order_line.location = dup_location
        dup_order_line.order = recurrent_order
        dup_order_line.save
      end
    end
  end

  def clone_empty_locations(recurrent_order)
    empty_locations = Location.where(order: order).includes(:order_lines).where(order_lines: { id: nil })
    return if empty_locations.blank?

    empty_locations.each do |location|
      dup_location = location.dup
      dup_location.order = recurrent_order
      dup_location.save!
    end
  end

  class Result
    attr_accessor :order, :recurrent_orders, :errors, :recurrent_days

    def initialize(order:, days:)
      @order = order
      @recurrent_days = days
      @recurrent_orders = []
      @errors = []
    end

    def success?
      errors.blank? && order.present? && order.order_type == 'recurrent' && (recurrent_days.size - 1) == recurrent_orders.size
    end
  end

end
