class Orders::HealthCheck

  THRESHOLD = 20

  def initialize(time: Time.zone.now)
    @time = time
    @notified_orders = {}
  end

  def call
    notify(notifiable_orders)
    notified_orders
  end

private

  attr_reader :time, :notified_orders

  def notifiable_orders
    orders = {}
    (order_documents + order_supplier_documents).each do |document|
      order = document.documentable_type == 'OrderSupplier' ? document.documentable.order : document.documentable
      if orders[order].blank? || orders[order] < document.version
        orders[order] = document.version
      end
    end
    orders
  end

  def future_orders
    orders = Order.where(status: %w[pending new amended confirmed])
    orders = orders.where(delivery_at: [time..time + 2.days])
    orders
  end

  def order_documents
    documents = Document.joins(:order).where(orders: { id: future_orders })
    documents = documents.where(kind: 'supplier_order_details')
    documents = documents.where('version >= ?', THRESHOLD)
    documents
  end

  def order_supplier_documents
    supplier_documents = Document.joins(order_supplier: :order).where(order_suppliers: { order_id: future_orders })
    supplier_documents = supplier_documents.where(kind: 'supplier_order_details')
    supplier_documents = supplier_documents.where('version >= ?', THRESHOLD)
    supplier_documents
  end

  def notify(notifiable_orders)
    message = ':face_with_head_bandage: Order health check warning: found order(s) with pdf version > 20'
    attachments = []
    notifiable_orders.each do |order, version|
      notified_orders[order] = version
      attachments << {
        type: 'mrkdwn',
        text: "Order *##{order.id}*, pdf version: #{version}",
        color: 'warning',
      }
    end
    SlackNotifier.send(message, attachments: attachments)
  end

end
