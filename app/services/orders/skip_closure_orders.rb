class Orders::SkipClosureOrders

  def initialize(options: {})
    @skip_options = options
    @closure_orders = fetch_closure_orders
    @result = Result.new
  end

  def call(dry_run: false)
    closure_orders.each do |order|
      order_updated = case
      when dry_run
        order.assign_attributes(status: 'skipped')
        order.valid?
      else
        order.update(status: 'skipped')
      end
      if order_updated
        result.skipped_orders << order
      else
        result.errors << "Error order ##{order.id} #{order.errors.full_messages.join(', ')}"
      end
    end
    result
  end

private

  attr_reader :skip_options, :closure_orders, :result

  def fetch_closure_orders
    Orders::ListSupplierClosureOrders.new(options: skip_options, includes: [:customer_profile]).call
  end

  class Result
    attr_accessor :skipped_orders, :errors

    def initialize
      @skipped_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
