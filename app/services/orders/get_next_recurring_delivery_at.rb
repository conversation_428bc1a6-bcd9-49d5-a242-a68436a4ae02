class Orders::GetNextRecurringDeliveryAt

  def initialize(order:, new_delivery_at:)
    @order = order
    @new_delivery_at = new_delivery_at
  end

  def call
    if order_delivery_day == new_delivery_day
      new_delivery_at
    else
      next_delivery_at
    end
  end

private

  attr_reader :order, :new_delivery_at

  def order_delivery_day
    @_order_delivery_day ||= order.delivery_at.present? && order.status != 'draft' ? order.delivery_at.to_s(:short_weekday).downcase : order.name.downcase
  end

  def new_delivery_day
    new_delivery_at.to_s(:short_weekday).downcase
  end

  def next_delivery_at
    delivery_at = new_delivery_at
    delivery_day = new_delivery_day
    while delivery_day != order_delivery_day
      delivery_at += 1.day
      delivery_day = delivery_at.to_s(:short_weekday).downcase
    end
    delivery_at
  end
end
