class Orders::CalculateDelivery
  def initialize(order:, profile: nil, order_lines: [], recalculate: false, calculate_default: false)
    @order = order
    @profile = profile
    @order_lines = order_lines.presence || fetch_order_lines
    @recalculate = is_supplier? || recalculate || calculate_default
    @calculate_default = calculate_default
  end

  def call
    delivery = case
    when !recalculate && order.customer_delivery.present? && order.customer_delivery != 0
      order.customer_delivery
    when order.no_delivery_charge && !calculate_default
      0.0
    when order.order_variant == 'event_order'
      custom_order_delivery_charges
    else
      supplier_delivery_charges
    end
    delivery.round(2)
  end

private

  attr_reader :order, :profile, :order_lines, :recalculate, :calculate_default

  def supplier_grouped_order_lines
    @_suppler_grouped_order_lines ||= order_lines.group_by(&:supplier_profile)
  end

  def custom_order_delivery_charges
    delivery = 0.0
    supplier_grouped_order_lines.each do |supplier, supplier_order_lines|
      delivery += custom_order_delivery_for(supplier, supplier_order_lines)
    end
    delivery
  end

  def custom_order_delivery_for(supplier, supplier_order_lines)
    custom_order_supplier = supplier.custom_order_suppliers.where(order: order).first
    delivery = case
    when custom_order_supplier.present?
      custom_order_supplier.delivery_fee
    else
      supplier_delivery_charges_for(supplier, supplier_order_lines)
    end
    delivery.presence || 0.0
  end

  def supplier_delivery_charges
    delivery = 0.0
    supplier_grouped_order_lines.each do |supplier, supplier_order_lines|
      delivery += supplier_delivery_charges_for(supplier, supplier_order_lines)
    end
    delivery
  end

  def supplier_delivery_charges_for(supplier, supplier_order_lines)
    zone_based_supplier_fee = zone_delivery_fee_for(supplier)
    case
    when supplier.woolworths? && order.woolworths_order.present? && order.woolworths_order.delivery_fee.present?
      order.woolworths_order.delivery_fee
    when !calculate_default && (order_delivery_override = order_delivery_override_for(supplier: supplier).presence)
      order_delivery_override
    when !calculate_default && (delivery_override = delivery_override_for(supplier: supplier).presence)
      delivery_override
    when zone_based_supplier_fee > 0
      zone_based_supplier_fee
    else
      minimum_based_delivery_fee_for(supplier, supplier_order_lines)
    end
  end

  def zone_delivery_fee_for(supplier)
    supplier_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: order.delivery_suburb, delivery_date: order.delivery_at).call
    supplier_zone.present? && supplier_zone.delivery_fee.present? ? supplier_zone.delivery_fee : 0.0
  end

  def minimum_based_delivery_fee_for(supplier, supplier_order_lines)
    supplier_minimum_spend = supplier_minimum_spend_for(supplier, supplier_order_lines)
    supplier_total = supplier_order_lines.map(&:total_price).sum

    # add minimum delivery fee if supplier total is less than minimum spend
    if supplier_minimum_spend.present? && supplier_total < supplier_minimum_spend && supplier.minimum_delivery_fee.present?
      supplier.minimum_delivery_fee
    else
      0.0
    end
  end

  def supplier_minimum_spend_for(supplier, supplier_order_lines)
    supplier_order_line_categories = supplier_order_lines.map(&:category).compact.uniq

    supplier_minimums = supplier.minimums
    category_supplier_minimums = supplier_minimums.select{|minimum| supplier_order_line_categories.map(&:id).include?(minimum.category_id) } if supplier_order_line_categories.present?
    category_group_supplier_minimums = supplier_minimums.select{|minimum| minimum.category.present? && supplier_order_line_categories.map(&:group).include?(minimum.category.group) } if supplier_order_line_categories.present? && category_supplier_minimums.blank?

    sanitized_supplier_minimums = (category_supplier_minimums.presence || category_group_supplier_minimums.presence || supplier_minimums).max_by(&:spend_price)
    sanitized_supplier_minimums&.spend_price
  end

  def fetch_order_lines
    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
      paid_only: order.is_team_order? && order.attendee_pays,
    }
    lister_options[:supplier] = profile if is_supplier?
    OrderLines::List.new(options: lister_options).call
  end

  def order_delivery_override_for(supplier:)
    order.order_suppliers.where(supplier_profile: supplier).first&.delivery_fee_override
  end

  def delivery_override_for(supplier:)
    delivery_override = DeliveryOverrides::FetchOverride.new(customer: order.customer_profile, supplier: supplier).call
    return nil if delivery_override.blank? || profile.nil?

    is_customer? ? delivery_override.customer_override : delivery_override.supplier_override
  end

  def is_customer?
    profile.present? && profile.is_a?(CustomerProfile)
  end

  def is_supplier?
    profile.present? && profile.is_a?(SupplierProfile)
  end

end
