class Orders::ListForSuppliers

  DELIVERY_DURATIONS = {
    'week' => 1.week,
    'fortnight' => 2.weeks,
    'month' => 1.month,
    '6-months' => 6.months,
    'year' => 1.year,
    'all' => 99.years,
  }.freeze

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options].inject(&:merge)
    @result = Result.new(lister_options: @filter_options)
  end

  def call
    @orders = base
    filter_by_supplier if filter_options[:for_supplier].present?
    filter_by_name if filter_options[:name].present?
    if filter_options[:date].present?
      filter_by_delivery_range
      filter_by_status
      filter_by_delivery_date
    else
      filter_by_delivery_range if filter_options[:for_duration].present?
      filter_by_status if filter_options[:list_type].present?
    end
    filter_by_pagination if filter_options[:with_pagination].present?
    order_list
    retrieve_more if filter_options[:for_duration] == 'week' && orders.blank?
    result.orders = orders
    result
  end

private

  attr_reader :includes, :filter_options, :orders, :result

  def base
    Order.all
  end

  def filter_by_supplier
    @orders = orders.joins(:supplier_profiles).where(supplier_profiles: { id: filter_options[:for_supplier].id })
  end

  def filter_by_name
    @orders = orders.where('name ilike ?', "%#{filter_options[:name]}%")
  end

  def order_list
    @orders = case
    when filter_options[:order_by].present?
      orders.order(filter_options[:order_by])
    when filter_options[:list_type].present? && filter_options[:list_type] == 'past'
      orders.order(delivery_at: :desc)
    else
      orders.order(delivery_at: :asc)
    end
    @orders = orders.distinct
  end

  def filter_by_delivery_range
    now = Time.zone.now
    delivery_duration = DELIVERY_DURATIONS[filter_options[:for_duration]]
    case
    when filter_options[:list_type] == 'pending'
      @orders = orders.where(delivery_at: [(now - 1.month)..(now + 6.months)])
    when filter_options[:list_type] == 'upcoming'
      @orders = orders.where(delivery_at: [now.beginning_of_day..(now + delivery_duration)])
    when filter_options[:list_type] == 'past'
      @orders = orders.where(delivery_at: [(now - delivery_duration)..now])
    when filter_options[:for_duration] == 'all' || filter_options[:date].present?
      # do nothing
    else
      @orders = orders.where(delivery_at: [now..(now + delivery_duration)])
    end
  end

  def filter_by_status
    case
    when filter_options[:date].present?
      @orders = orders.where.not(status: %w[draft paused skipped cancelled])
    when filter_options[:list_type] == 'pending'
      @orders = orders.where(status: %w[pending new amended confirmed])
      @orders = orders.joins(:order_lines) if filter_options[:for_supplier].blank?
      @orders = orders.where.not(order_lines: { status: %w[rejected accepted] })
    when filter_options[:list_type] == 'upcoming'
      @orders = orders.where(status: %w[new amended confirmed])
      @orders = orders.joins(:order_lines) if filter_options[:for_supplier].blank?
      @orders = orders.where(order_lines: { status: 'accepted' })
    when filter_options[:list_type] == 'past'
      @orders = orders.where(status: %w[amended confirmed delivered])
    else
      @orders = Order.none
    end
  end

  def filter_by_delivery_date
    date = filter_options[:date].is_a?(String) ? Time.zone.parse(filter_options[:date]) : filter_options[:date]
    @orders = orders.where(delivery_at: [date.beginning_of_day...date.end_of_day])
  end

  def filter_by_pagination
    page = filter_options[:with_pagination][:page]
    limit = filter_options[:with_pagination][:limit]
    @orders = orders.page(page).per(limit)
  end

  def retrieve_more
    lister_options = filter_options.merge({ for_duration: 'month' })
    order_lister = Orders::ListForSuppliers.new(options: lister_options, includes: includes).call
    @orders = order_lister.orders
    result.lister_options = @filter_options = lister_options
  end

  def default_options
    {
      for_supplier: nil,
      name: nil,
      list_type: nil,
      for_duration: 'all',
      order_by: nil,
      with_pagination: {},
      date: nil,
    }
  end

  class Result
    attr_accessor :lister_options, :orders

    def initialize(lister_options:)
      @lister_options = lister_options
      @orders = []
    end
  end

end
