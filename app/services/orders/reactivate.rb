class Orders::Reactivate

  def initialize(order:, mode:)
    @order = order
    @reactivate_mode = mode
    @result = Result.new(order: order)
  end

  def call
    case
    when order.status != 'paused'
      result.errors << 'Cannot Reactivate un-paused order'
    when order.order_type != 'recurrent'
      result.errors << 'Cannot reactivate one-off orders'
    when reactivate_mode == 'one-off'
      update_recurrent_chain if order.id == order.template_id
      order.update(order_type: 'one-off')
      Orders::Confirm.new(order: order, auto_confirmation: true).call
      result.reactivated_orders = [order]
    else # reactivate_mode == subsequent
      subsequent_orders.each do |subsequent_order|
        subsequent_order.update(template_id: order.id)
      end
      orders_to_be_reactivated = subsequent_orders.select{|order| order.status == 'paused'}
      orders_to_be_reactivated.each do |subsequent_order|
        Orders::Confirm.new(order: subsequent_order, auto_confirmation: true).call
      end
      result.reactivated_orders = orders_to_be_reactivated
    end
    send_supplier_order_reactivation_emails(orders: result.reactivated_orders) if result.reactivated_orders.present?
    result
  end

private

  attr_reader :order, :reactivate_mode
  attr_accessor :result

  class Result
    attr_accessor :order, :reactivated_orders, :errors

    def initialize(order:)
      @order = order
      @reactivated_orders = []
      @errors = []
    end

    def success?
      errors.blank? && order.reload.status == 'confirmed' && reactivated_orders.present?
    end
  end

  def subsequent_orders
    @_subsequent_orders ||= Order.where(template_id: order.template_id).where('delivery_at >= ?', order.delivery_at)
  end

  def update_recurrent_chain
    orders_chain = subsequent_orders.reject{|subsequent_order| subsequent_order.id == order.id }.sort_by(&:id)
    if orders_chain.present?
      new_template = orders_chain.first
      orders_chain.each do |order|
        order.update(template_id: new_template.id)
      end
    end
  end

  def send_supplier_order_reactivation_emails(orders:)
    suppliers_to_notify = orders.map(&:supplier_profiles).flatten.uniq
    suppliers_to_notify.each do |supplier|
      Suppliers::Emails::SendOrderReactivatedEmail.new(mode: reactivate_mode, supplier: supplier, orders: orders.sort_by(&:id)).delay(queue: :notifications).call
    end
  end
end
