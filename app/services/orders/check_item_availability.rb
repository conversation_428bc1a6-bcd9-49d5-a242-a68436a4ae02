class Orders::CheckItemAvailability

  def initialize(order:, order_lines: [])
    @order = order
    @order_lines = order_lines.presence || fetch_order_lines
    @result = Result.new
  end

  def call
    order_lines.each do |order_line|
      check_availability_for(order_line)
      result.order_lines << order_line
    end
    result
  end

private

  attr_reader :order, :order_lines, :result

  def fetch_order_lines
    lister_options = {
      order: order,
    }
    OrderLines::List.new(options: lister_options, includes: %i[supplier_profile menu_item serving_size]).call
  end

  def check_availability_for(order_line)
    error_message = case
    when is_unavailable?(order_line.supplier_profile)
      'The Supplier is no longer available'
    when is_unavailable?(order_line.menu_item) || is_unavailable?(order_line.serving_size)
      'The Item is no longer available'
    when order_line.selected_menu_extras.present? && is_unavailable?(MenuExtra.where(id: order_line.selected_menu_extras))
      'Selected item extras are no longer available'
    end
    if error_message.present? && order_line.update(last_errors: [error_message])
      result.errors << "#{order_line.name} - #{error_message}"
    end
  end

  def is_unavailable?(item)
    return false if item.blank?

    case
    when item.is_a?(SupplierProfile)
      !item.is_searchable?
    when item.is_a?(MenuItem)
      item.archived_at.present? || item.is_hidden?
    when item.is_a?(ServingSize)
      item.archived_at.present?
    when item.is_a?(ActiveRecord::Relation) && item.sample.is_a?(MenuExtra)
      item.any?(&:archived_at)
    else
      false
    end
  end

  def searchable_suppliers
    @_searchable_suppliers ||= order_lines.group_by(&:supplier_profile).keys.select(&:is_searchable?)
  end

  class Result
    attr_accessor :order_lines, :errors

    def initialize
      @order_lines = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end