class Orders::OnHoldCharges::Upsert

  def initialize(order:)
    @order = order
    @result = Result.new(order: order)
  end

  def call
    if can_charge?
      refund_old_charges if existing_active_charges.present?
      create_new_charge
    end
    result
  end

private

  attr_reader :order, :result

  def can_charge?
    case
    when order.blank?
      result.errors << 'Cannot charge without an order'
    when order_card.blank? || order_card.pay_on_account?
      result.errors << 'Cannot charge without a valid credit card'
    when order_card.stripe_token.blank?
      result.errors << 'Cannot charge on a non-stripe card'
    end
    result.errors.blank?
  end

  def order_card
    @_order_card ||= order.credit_card
  end

  def existing_active_charges
    @_existing_active_charges ||= order.on_hold_charges.where(refund_token: nil)
  end

  def create_new_charge
    new_stripe_charge = create_new_stripe_charge
    if new_stripe_charge.present?
      order_charge = order.on_hold_charges.where(stripe_token: new_stripe_charge.payment_id).first_or_initialize
      if order_charge.present? && order_charge.update(amount: order.customer_total, status: 'charged')
        result.charge = order_charge
        notify_via_slack(order_charge)
      end
    end
  end

  def refund_old_charges
    charge_refunder = Orders::OnHoldCharges::Refund.new(order: order, order_charges: existing_active_charges).call
    if !charge_refunder.success?
      result.errors += charge_refunder.errors
    end
  end

  def create_new_stripe_charge
    charge_creator = Stripe::CreateOrderCharge.new(order: order).call
    case
    when charge_creator.success?
      charge_creator.charge
    when card_error = charge_creator.errors.detect{|error| error.is_a?(Stripe::CardError) }.presence
      result.errors << 'On-Hold Charge could not be created due to Card Error on Stripe'
      Orders::OnHoldCharges::HandleCardError.new(order: order, card_error: card_error).call
      nil # retun nil to stop creating an order on_hold_charge
    else
      result.errors += charge_creator.errors
      notify_error_via_slack(charge_creator.errors)
      nil # retun nil to stop creating an order on_hold_charge
    end
  end

  def notify_via_slack(order_charge)
    return if yordar_credentials(:stripe, :slack_notification).blank?

    message = ":credit_card: An on-hold charge was created for order ##{order.id} (via stripe)"
    attachments = []
    attachments << {
      type: 'mrkdwn',
      text: "charge id: #{order_charge.id}",
      color: 'good'
    }
    attachments << {
      type: 'mrkdwn',
      text: "payment token: #{order_charge.stripe_token}",
      color: 'good'
    }
    SlackNotifier.send(message, attachments: attachments)
  end

  def notify_error_via_slack(errors)
    return if yordar_credentials(:stripe, :slack_notification).blank?

    message = ":warning: :credit_card: Could not create on-hold charge for order ##{order.id} (via stripe)"
    attachments = []
    attachments << {
      type: 'mrkdwn',
      text: "error message: #{errors.join('. ')}",
      color: 'warning'
    }
    SlackNotifier.send(message, attachments: attachments)
  end

  class Result
    attr_accessor :charge, :errors
    attr_reader :order

    def initialize(order:)
      @order = order
      @charge = nil
      @errors = []
    end

    def success?
      errors.blank? && charge.present?
    end
  end
end
