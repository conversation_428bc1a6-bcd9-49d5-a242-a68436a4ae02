class Orders::OnHoldCharges::Refund

  def initialize(order:, order_charges: [])
    @order = order
    @order_charges = order_charges
    @result = Result.new
  end

  def call
    refundable_order_charges.each do |order_charge|
      stripe_refund = refund_with_stripe(order_charge)
      if stripe_refund.present?
        order_charge.update(refund_token: stripe_refund.id, status: 'refunded')
        notify_via_slack(order_charge)
      end
    end
    result
  end

private

  attr_reader :order, :order_charges, :result

  def refundable_order_charges
    refundable_charges = order_charges.presence || order.on_hold_charges
    refundable_charges = refundable_charges.where(refund_token: nil)
    refundable_charges
  end

  def refund_with_stripe(order_charge)
    charge_refunder = Stripe::RefundOrderCharge.new(order_charge: order_charge).call
    if charge_refunder.success?
      charge_refunder.refund
    else
      result.errors += charge_refunder.errors
      nil
    end
  end

  def notify_via_slack(order_charge)
    return if yordar_credentials(:stripe, :slack_notification) != 'true'

    message = ":heavy_dollar_sign: An on-hold charge has been refunded/canceled for order ##{order.id}"

    attachments = []
    attachments << {
      type: 'mrkdwn',
      text: "charge id: #{order_charge.id}",
      color: 'good'
    }
    attachments << {
      type: 'mrkdwn',
      text: "payment token: #{order_charge.stripe_token}",
      color: 'good'
    }
    attachments << {
      type: 'mrkdwn',
      text: "refund token: #{order_charge.refund_token}",
      color: 'good'
    }
    SlackNotifier.send(message, attachments: attachments)
  end

  class Result
    attr_accessor :refunds, :errors

    def initialize
      @refunds = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
