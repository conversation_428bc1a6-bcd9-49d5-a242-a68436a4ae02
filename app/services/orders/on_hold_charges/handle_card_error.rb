class Orders::OnHoldCharges::HandleCardError

  def initialize(order:, card_error:)
    @order = order
    @card_error = card_error
    @result = Result.new(order: order)
  end

  def call
    if can_handle?
      save_failed_order_charge
      cancel_order if decline_code == 'fraudulent' && !has_existing_delivered_orders?
      notify_admin
      log_event
    end
    result
  end

private

  attr_reader :order, :card_error, :result

  def can_handle?
    case
    when order.blank?
      result.errors << 'Cannot handle without an order'
    when card_error.blank? || !card_error.is_a?(Stripe::CardError)
      result.errors << 'Cannot handle without a valid card error'
    end
    result.errors.blank?
  end

  def save_failed_order_charge
    order_charge = order.on_hold_charges.new
    if order_charge.update(amount: order.customer_total, status: 'failed')
      result.on_hold_charge = order_charge
    end
  end

  def has_existing_delivered_orders?
    existing_orders = order_customer.orders.where(status: 'delivered')
    existing_orders = existing_orders.where('delivery_at < ?', order.delivery_at)
    existing_orders.present?
  end

  def cancel_order
    order_canceller = Orders::Cancel.new(order: order, mode: 'one-off').call
    if order_canceller.success?
      result.order = order_canceller.order
    else
      result.errors += order_canceller.errors
    end
  end

  def notify_admin
    Orders::Emails::SendOrderChargeFailedEmail.new(order: order, customer: order_customer, card_error: card_error_for_email).call
  end

  def log_event
    event_severity = decline_code == 'fraudulent' ? 'error' : 'warning'
    EventLogs::Create.new(event_object: order, event: 'on-hold-charge-failed', severity: event_severity, message: card_error.message, decline_code: decline_code).call
  end

  def card_error_for_email
    OpenStruct.new(
      message: card_error.message,
      code: card_error.code,
      decline_code: decline_code
    )
  end

  def decline_code
    return nil if card_error&.json_body.blank?

    @_decline_code ||= card_error.json_body.dig(:error, :decline_code)
  end

  def order_customer
    @_order_customer ||= order.customer_profile
  end

  class Result
    attr_accessor :order, :on_hold_charge, :errors

    def initialize(order:)
      @order = order
      @on_hold_charge = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end