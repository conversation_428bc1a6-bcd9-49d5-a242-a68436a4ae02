class Orders::MarkSupplierAsNotified

  def initialize(order:, supplier:, notified_at: Time.zone.now)
    @order = order
    @supplier = supplier
    @notified_at = notified_at
  end

  def call
    orders.each do |order|
      mark_supplier_order_lines_for(order)
      mark_supplier_as_notified_for(order)
    end
  end

private

  attr_reader :order, :supplier, :notified_at

  def orders
    if order.is_recurrent?
      Order.where(template_id: order.template_id).where('delivery_at >= ?', order.delivery_at).order(:delivery_at)
    else
      [order]
    end
  end

  def mark_supplier_order_lines_for(order)
    lister_options = {
      order: order,
      supplier: supplier
    }
    order_lines = OrderLines::List.new(options: lister_options).call
    order_lines.each do |order_line|
      order_line.update(status: 'notified')
    end
  end

  def mark_supplier_as_notified_for(order)
    order.update_column(:suppliers_notified_at, notified_at)
  end

end
