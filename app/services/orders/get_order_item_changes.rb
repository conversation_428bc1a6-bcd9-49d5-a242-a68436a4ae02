class Orders::GetOrderItemChanges

  LOCATION_CHANGE_FIELDS = %w[details note].freeze

  def initialize(order:, supplier:, since: nil)
    @order = order
    @supplier = supplier
    @since = since.presence || order&.suppliers_notified_at || Time.zone.now
    @changes = []
  end

  def call
    @changed_locations = recently_updated_locations.to_a
    changes = order_line_location_changes
    changes += remaining_location_changes
    changes.sort_by(&:weight)
  end

private

  attr_reader :order, :supplier, :since, :changes

  def order_line_location_changes
    changes = []
    location_grouped_order_line_changes.map do |location_id, location_order_line_changes|
      change_type = nil
      location_changes = []
      location = Location.where(id: location_id).first

      case
      when location.blank?
        location = get_deleted_location_with(id: location_id)
        next if location.blank?

        change_type = 'removed'
      when recently_updated_locations.map(&:id).include?(location.id)
        previous_version = previous_version_for(location)
        if previous_version.present?
          if previous_version.event == 'create'
            change_type = 'created'
          else
            change_type = 'changed'
            location_changes = get_changes_for(location, previous_version.reify)
          end
        end
        @changed_locations.reject!{|changed_location| changed_location.id == location.id }
      end
      changes << OpenStruct.new(
        id: location.id,
        details: location.details,
        weight: location.created_at,
        change_type: change_type,
        changes: location_changes,
        order_line_changes: location_order_line_changes
      )
    end
    changes
  end

  def remaining_location_changes
    return [] if @changed_locations.blank?

    @changed_locations.map do |location|
      previous_version = previous_version_for(location)
      next if previous_version.blank?

      if previous_version.present?
        if previous_version.event == 'create'
          change_type = 'created'
          location_changes = []
        else
          change_type = 'changed'
          location_changes = get_changes_for(location, previous_version.reify)
        end
      end
      OpenStruct.new(
        id: location.id,
        details: location.details,
        weight: location.created_at,
        change_type: change_type,
        changes: location_changes,
        order_line_changes: []
      )
    end.compact
  end

  def previous_version_for(location)
    location.versions.order(:id).where('created_at > ?', since).where(event: %w[update create]).first
  end

  def get_changes_for(location, previous_location)
    changes = []
    LOCATION_CHANGE_FIELDS.each do |field|
      old_value = previous_location.send(field)
      new_value = location.send(field)
      next if old_value == new_value

      changes << OpenStruct.new(
        field: field,
        old_value: old_value,
        new_value: new_value
      )
    end
    changes
  end

  def location_grouped_order_line_changes
    order_line_changes.group_by(&:location_id)
  end

  def order_line_changes
    OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier, since: since).call
  end

  def recently_updated_locations
    @_recently_updated_locations = order.locations.where('locations.updated_at >= ?', since)
  end

  def get_deleted_location_with(id:)
    location_versions = PaperTrail::Version.where(event: 'destroy', item_type: 'Location')
    location_versions = location_versions.where('created_at > ?', since)
    location_versions = location_versions.where('object LIKE (?)', "%id: #{id}%")
    location_version = location_versions.first
    location_version.present? ? location_version.reify : nil
  end

end
