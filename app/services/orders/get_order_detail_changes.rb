class Orders::GetOrderDetailChanges

  CUSTOMER_FIELDS = %w[contact_name phone company_name department_identity].freeze
  DELIVERY_FIELDS = %w[delivery_at delivery_address_level delivery_address delivery_instruction delivery_type].freeze
  CHANGE_FIELDS = (CUSTOMER_FIELDS + DELIVERY_FIELDS).freeze

  def initialize(order:, since: nil)
    @order = order
    @since = since.presence || order&.suppliers_notified_at
    @changes = []
  end

  def call
    if previous_order.present?
      determine_order_changes
    end

    changes
  end

private

  attr_reader :order, :since, :changes

  def previous_order
    @_previous_order ||= previous_version.present? && previous_version.reify
  end

  def previous_version
    @_previous_version ||= order.present? && order.versions.order(created_at: :desc).where('created_at > ?', since).where(event: 'update').first
  end

  def determine_order_changes
    CHANGE_FIELDS.each do |field|
      sanitised_value = sanitized_value_for(field)
      next if sanitised_value.blank?

      changes << OpenStruct.new(
        field: field,
        label: I18n.t("order.column_label.#{field}"),
        value: sanitised_value
      )
    end
  end

  def sanitized_value_for(field)
    previous_value = previous_order.send(field)
    current_value = order.send(field)

    return nil if (previous_value.present? || current_value.present?) && previous_value == current_value

    case
    when field == 'delivery_at' && previous_value.present?
      "from #{previous_value.to_s(:full)} to #{current_value.to_s(:full)}"
    when field == 'delivery_at' # && previous_value.blank?
      "from N/A to #{current_value.to_s(:full)}"
    else
      current_value
    end
  end

end
