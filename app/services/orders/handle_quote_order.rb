class Orders::HandleQuoteOrder

  VALID_MODES = %w[edit approve reject].freeze

  def initialize(handle_params: {}, forced: false)
    @handle_params = handle_params
    @forced = forced
    @result = Result.new(order: order)
  end

  def call
    if can_handle?
      case
      when mode == 'edit'
        result.redirect_to = :edit_page
      when mode == 'approve' && order.credit_card_id.present? && forced
        approve_order
      when mode == 'approve'
        result.redirect_to = :approve_page
      when mode == 'reject' && forced
        reject_order
      when mode == 'reject'
        notify_admin_about_rejection
        result.warnings << 'We\'ve notified <PERSON><PERSON><PERSON> Admin about the quote being rejected'
        result.redirect_to = :show_page
      end
    end
    result
  end

private

  attr_reader :handle_params, :forced, :result

  def can_handle?
    case
    when !valid_url_hash? || order.blank? || customer.blank? || order.customer_profile != customer
      result.errors << 'Invalid Request'
    when VALID_MODES.exclude?(mode)
      result.errors << "We don't know what you mean to do with the order. action => #{mode}"
    when order.status != 'quoted'
      action = mode == 'approve' ? 'approved' : 'rejected'
      result.warnings << "This order can no longer be #{action}, as it's not a quoted order"
      result.redirect_to = :show_page
    end
    result.errors.blank? && result.warnings.blank?
  end

  def valid_url_hash?
    Digest::MD5.hexdigest(handle_params[:order_id].to_s + handle_params[:profile_id].to_s + yordar_credentials(:random_salt)) == handle_params[:hashed_value]
  end

  def order
    @_order ||= Order.where(id: handle_params[:order_id]).first
  end

  def mode
    @_mode ||= handle_params[:mode]
  end

  def customer
    @_customer ||= CustomerProfile.where(id: handle_params[:profile_id]).first
  end

  def approve_order
    order_approver = Orders::Submit.new(order: order, customer: customer).call
    if order_approver.success?
      result.order = order_approver.order
      result.redirect_to = :show_page
    else
      result.errors += order_approver.errors
    end
  end

  def reject_order
    result.redirect_to = :show_page
    order_rejector = Orders::Cancel.new(order: order, mode: 'one-off').call
    if order_rejector.success?
      result.order == order_rejector.order
    else
      result.errors += order_rejector.errors
    end
  end

  def notify_admin_about_rejection
    Admin::Emails::SendRejectedOrderQuoteEmail.new(order: order).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :order, :errors, :warnings, :redirect_to

    def initialize(order:)
      @order = order
      @errors = []
      @warnings = []
      @redirect_to = nil
    end

    def success?
      errors.blank?
    end
  end

end
