class Orders::ListSentEmails

  SUPPLIER_EMAILS = [
    'supplier-new_order-<order-id>',
    'supplier-order_changed-<order-version-ref>',
    'supplier-order_cancelled-<order-id>',
    'supplier-order_reactivated-<order-id>',

    'supplier-team_order_created-<order-id>-<supplier-id>',
    'supplier-new_team_order-<order-id>',
    'supplier-team_order_cutoff-<order-id>-<supplier-id>',
    'supplier-team_order_cutoff-4hr-<order-id>-<supplier-id>',
    'supplier-team_order_cutoff-day-<order-id>-<supplier-id>'
  ].freeze

  CUSTOMER_EMAILS = [
    'customer-event_order_quote-<customer-id>-<order-version-ref>',
    'customer-new_order-<order-version-ref>', # old
    'customer-new_order-<order-id>-<order-version-ref>',

    'customer-order_confirmed-<order-version-ref>', # old
    'customer-order_confirmed-<order-id>-<order-version-ref>',

    'customer-order_review_invitation<fk-order-id>',
    'customer-new_team_order-<order-id>-<order-version-ref>',
    'customer-new_team_order_package-<package-id>-<order-version-ref>',
    'customer-team_order_submission-<order-id>-<order-version-ref>',
    'team-order-admin-anonymous_attendees_notification-<order-id>-<cutoff-time>',
    'team-order-admin-cutoff_notification-<order-id>-<cutoff-time>'
  ].freeze

  ADMIN_EMAILS = [
    'admin-rejected_order-<order-id>'
  ].freeze

  REGEX_PLACEHOLDERS = ['<package-id>', '<supplier-id>', '<customer-id>', '<cutoff-time>'].freeze

  def initialize(order:)
    @order = order
    @sent_emails = []
  end

  def call
    all_email_references.each do |reference|
      config = get_config_for(reference)
      @sent_emails += get_emails_for(config)
    end
    sent_emails
  end

private

  attr_reader :order
  attr_accessor :sent_emails

  def all_email_references
    SUPPLIER_EMAILS + CUSTOMER_EMAILS + ADMIN_EMAILS
  end

  def get_config_for(reference)
    email_config = EmailConfig.new

    if reference.include?('<fk-order-id>')
      email_config.fk_id = order.id
      reference = reference.gsub('<fk-order-id>', '')
    end
    reference = reference.gsub('<order-id>', order.id.to_s) if reference.include?('<order-id>')
    reference = reference.gsub('<order-version-ref>', order.version_ref) if order.version_ref.present? && reference.include?('<order-version-ref>')

    REGEX_PLACEHOLDERS.each do |placeholder|
      reference = reference.gsub(placeholder, '%') if reference.include?(placeholder)
    end

    email_config.reference = reference
    email_config
  end

  def get_emails_for(config)
    emails = Email.where('ref ilike ?', config.reference)
    emails = emails.where(fk_id: config.fk_id) if config.fk_id.present?
    emails.present? ? emails.to_a : []
  end

  class EmailConfig
    attr_accessor :reference, :fk_id

    def initialize
      @reference = nil
      @fk_id = nil
    end

    def inspect
      { reference: reference, fk_id: fk_id }
    end
  end

end
