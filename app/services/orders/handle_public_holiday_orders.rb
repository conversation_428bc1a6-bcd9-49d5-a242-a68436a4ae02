class Orders::HandlePublicHolidayOrders

  def initialize(holiday:)
    @holiday = holiday
    @result = Result.new
  end

  def call
    return result if !can_handle?

    holiday_orders.group_by(&:customer_profile).each do |customer, customer_orders|
      customer_handled_orders = []
      customer_orders.each do |order|
        order_handled = case
        when order.skip?
          skip_order(order)
        else
          push_order(order)
        end
        customer_handled_orders << order if order_handled
      end
      notify_customer(customer: customer, handled_orders: customer_handled_orders)
    end
    notify_suppliers
    log_event
    result
  end

private

  attr_reader :holiday, :result

  def can_handle?
    holiday.push_to.present? && holiday.effective_from.present? && holiday.effective_to.present? && holiday_orders.present? 
  end

  def holiday_orders
    orders = Order.where.not(status: %w[draft pending skipped paused rejected cancelled])
    orders = orders.where(delivery_at: [holiday.effective_from..holiday.effective_to])
    orders = orders.joins(:delivery_suburb).where(suburbs: { state: holiday.state }) if holiday.state.present?
    orders
  end

  def skip_order(order)
    update_attributes = { status: 'skipped' }
    update_attributes[:order_type] = 'one-off' if order.is_recurrent?
    if order.update(update_attributes)
      result.skipped_orders << order
      true
    else
      result.errors << "Error order ##{order.id} - #{order.errors.full_messages.join(', ')}"
      false
    end
  end

  def push_order(order)
    new_delivery_at = holiday.push_to.change(hour: order.delivery_at.hour, min: order.delivery_at.min, sec: order.delivery_at.sec)
    update_attributes = {
      old_delivery_at: order.delivery_at,
      delivery_at: new_delivery_at,
    }
    update_attributes[:order_type] = 'one-off' if order.is_recurrent?
    if order.update(update_attributes)
      result.pushed_orders << order
      true
    else
      result.errors << "Error order ##{order.id} - #{order.errors.full_messages.join(', ')}"
      false
    end
  end

  def notify_customer(customer:, handled_orders:)
    Customers::Emails::SendPublicHolidayOrdersEmail.new(customer: customer, holiday: holiday, handled_orders: handled_orders).delay(queue: :notifications).call
  end

  def notify_suppliers
    supplier_grouped_orders.each do |supplier, supplier_orders|
      Suppliers::Emails::SendPublicHolidayOrdersEmail.new(supplier: supplier, holiday: holiday, handled_orders: supplier_orders).delay(queue: :notifications).call
    end
  end

  def supplier_grouped_orders
    supplier_grouped_orders = {}
    supplier_profiles_grouped_orders = result.handled_orders.group_by(&:supplier_profiles)
    supplier_profiles_grouped_orders.each do |supplier_profiles, supplier_orders|
      supplier_profiles.each do |supplier|
        supplier_grouped_orders[supplier] ||= []
        supplier_grouped_orders[supplier] += supplier_orders
      end
    end
    supplier_grouped_orders
  end

  def log_event
    EventLogs::Create.new(event_object: holiday, event: 'upcoming-public-holiday', pushed_orders: result.pushed_orders.map(&:id).sort, skipped_orders: result.skipped_orders.map(&:id).sort).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :pushed_orders, :skipped_orders, :errors

    def initialize
      @pushed_orders = []
      @skipped_orders = []
      @errors = []
    end

    def success?
      errors.blank? && handled_orders.present?
    end

    def handled_orders
      pushed_orders + skipped_orders
    end
  end

end
