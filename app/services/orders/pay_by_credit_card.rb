class Orders::PayByCreditCard

  def initialize(order:, credit_card: nil)
    @order = order
    @credit_card = credit_card.presence || order.try(:credit_card)
    @result = Result.new(order: order)
  end

  def call
    if can_pay?
      begin
        attach_credit_card if credit_card != order.credit_card
        refund_any_on_hold_charges
        create_order_payment
        process_payment
      rescue
        error_message = "Failed to process payment or send tax receipt for order #{order.id}"
        result.errors << error_message
      end
    end
    result
  end

private

  attr_reader :order, :credit_card, :payment, :result

  def can_pay?
    case
    when order.blank?
      result.errors << 'Cannot pay without an order'
    when credit_card.blank?
      result.errors << 'Cannot pay without a credit card'
    when credit_card.gateway_token.blank? && credit_card.stripe_token.blank?
      result.errors << 'Cannot pay without a valid credit card'
    when order.payment_status == 'paid'
      result.errors << 'Order is already paid for'
    end
    result.errors.blank?
  end

  def attach_credit_card
    order.update_with_invoice = true
    order.update(credit_card: credit_card)
    # update totals with surcharge
    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
  end

  def refund_any_on_hold_charges
    existing_charges = order.on_hold_charges.where(status: 'charged').where(refund_token: nil).where.not(stripe_token: nil)
    return if existing_charges.blank?

    existing_charges.each do |order_charge|
      refunder = Stripe::RefundOrderCharge.new(order_charge: order_charge).call
      if refunder.success?
        order_charge.update(refund_token: refunder.refund.id, status: 'refunded')
      else
        result.errors += refunder.errors
      end
    end
  end

  def create_order_payment
    result.payment = @payment = Payment.create!(
      amount: order.customer_total,
      order_id: order.id,
      invoice_id: order.invoice_id,
      user_id: order.customer_profile.user.id,
      credit_card_id: credit_card.id
    )
  end

  def process_payment
    payment_processor = Payments::ProcessPayment.new(payment: payment).call
    order.update_with_invoice = true
    if payment_processor.success?
      @payment = payment_processor.payment
      order.update(payment_status: 'paid')
      update_invoice
      send_tax_receipt
    else
      order.update(payment_status: 'error')
      result.errors << "Order Payment error: #{order.id}"
      result.errors << "#{order.id}: #{payment.response_text}"
      result.errors += payment_processor.errors
    end
  end

  def invoice
    @_invoice ||= order.invoice || payment.invoice
  end

  def update_invoice
    return if invoice.blank? || invoice.payment_status == 'paid'

    payment_value = invoice.payment_value.presence || 0
    payment_value += order.customer_total
    payment_status = invoice.amount_price == payment_value ? 'paid' : 'partial'
    invoice.update(
      payment_value: payment_value,
      payment_status: payment_status
    )
  end

  def send_tax_receipt
    return if invoice.blank?

    Customers::Emails::SendInvoiceReceiptEmail.new(customer: order.customer_profile, invoice: invoice).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :order, :payment, :errors

    def initialize(order:)
      @order = order
      @payment = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
