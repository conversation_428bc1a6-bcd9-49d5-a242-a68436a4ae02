class Orders::SetupForSupplierMenu

  def initialize(session_order:, suburb: nil, cookies: nil)
    @session_order = session_order
    @suburb = suburb
    @cookies = cookies
    @result = Result.new
  end

  def call
    setup_order
    result
  end

private

  attr_reader :session_order, :suburb, :cookies, :order, :result

  def setup_order
    if session_order.present? && !session_order.is_team_order?
      @order = session_order
      override_delivery_suburb_from_cookie
    else
      @order = Order.new(status: 'draft', order_type: 'one-off')
      set_delivery_detail_from_cookie
    end

    result.order = order
    result.recurrent_orders = Order.where(recurrent_id: order.recurrent_id).order(:id) if order.order_type == 'recurrent' && order.status == 'draft'
  end

  def cookie_suburb
    cookies.present? && cookies[:yordar_suburb_id].present? && Suburb.where(id: cookies[:yordar_suburb_id]).first
  end

  def override_delivery_suburb_from_cookie
    return if suburb.blank? && cookie_suburb.blank?

    return if order.status != 'draft'

    return if order.woolworths_order.present? && order.delivery_suburb_id.present? # Woolworths order

    order.update(delivery_suburb_id: (suburb&.id || cookie_suburb&.id))
  end

  def set_delivery_detail_from_cookie
    return if cookie_suburb.blank?

    order.delivery_suburb ||= cookie_suburb
    order.delivery_address ||= cookies[:yordar_street_address] if cookies[:yordar_street_address].present?
  end

  class Result
    attr_accessor :order, :recurrent_orders, :errors, :warnings

    def initialize
      @order = nil
      @recurrent_orders = []
      @errors = []
      @warnings = []
    end

    def is_team_order?
      false
    end

    def success?
      errors.blank?
    end
  end

end
