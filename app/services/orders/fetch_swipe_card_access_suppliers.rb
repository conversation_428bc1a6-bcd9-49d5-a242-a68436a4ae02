class Orders::FetchSwipeCardAccessSuppliers
  SUPPLIER_DELIVERY_DAY_FROM = 1
  SUPPLIER_DELIVERY_DAY_TO = 5
  SUPPLIER_DELIVERY_HOUR_FROM = 9
  SUPPLIER_DELIVERY_HOUR_TO = 17

  def initialize(order:, delivery_at: nil)
    @order = order
    @delivery_at = delivery_at.presence || order&.delivery_at
  end

  def call
    return [] if order.blank? || delivery_at.blank?

    is_outside_normal_working_hours? ? swipe_card_order_suppliers : []
  end

private

  attr_reader :order, :delivery_at

  def is_outside_normal_working_hours?
    delivery_at.wday < SUPPLIER_DELIVERY_DAY_FROM ||
      delivery_at.wday > SUPPLIER_DELIVERY_DAY_TO ||
      delivery_at.hour < SUPPLIER_DELIVERY_HOUR_FROM ||
      delivery_at.hour > SUPPLIER_DELIVERY_HOUR_TO
  end

  def swipe_card_order_suppliers
    order.supplier_profiles.joins(:supplier_flags).where(supplier_flags: { needs_swipe_card_access: true })
  end

end
