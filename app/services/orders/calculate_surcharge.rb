class Orders::CalculateSurcharge

  def initialize(order:, total:, attendee: nil)
    @order = order
    @total = total
    @attendee = attendee
  end

  def call
    if total.blank? || total == 0 || order.blank?
      0
    else
      credit_card_surcharge
    end
  end

private

  attr_reader :order, :total, :attendee

  def chargeable_card
    @_chargeable_card ||= case
    when attendee.present?
      attendee.credit_cards.last
    else
      order.credit_card
    end
  end

  def credit_card_surcharge
    card_surcharge = CreditCards::FetchSurcharge.new(credit_card: chargeable_card).call
    surcharge_percent = card_surcharge.percent / 100
    surcharge_fee = card_surcharge.fee
    chargeable_amount = total # passed in order total

    if chargeable_card.present? && chargeable_card.is_stripe_card?
      (chargeable_amount * surcharge_percent + surcharge_fee) / (1 - surcharge_percent)
    else
      (chargeable_amount * surcharge_percent) + surcharge_fee
    end
  end

end
