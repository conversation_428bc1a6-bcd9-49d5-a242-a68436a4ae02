class Orders::FetchMismatchedOrderLines

  def initialize(order:, order_lines: [])
    @order = order
    @order_lines = order_lines.presence || order.order_lines
  end

  def call
    return [] if order_lines.blank?

    mismatched_order_lines = []
    order_lines.group_by(&:menu_item).each do |menu_item, item_order_lines|
      serving_sizes = menu_item.serving_sizes.where(archived_at: nil).order(:weight)
      item_order_lines.each do |order_line|
        mismatched_order_lines << check_mismatch_for(order_line, menu_item, serving_sizes)
      end
    end
    mismatched_order_lines.compact
  end

private

  attr_reader :order, :order_lines

  def check_mismatch_for(order_line, menu_item, serving_sizes)
    case
    when has_missing_serving_size_for(order_line, serving_sizes)
      mismatched_order_line = order_line.dup
      mismatched_order_line.id = order_line.id
      mismatched_order_line.serving_size_id = serving_sizes.detect(&:is_default).try(:id).presence || serving_sizes.first.id
      mismatched_order_line
    when menu_item.archived_at.blank? && has_archived_serving_size_for(order_line, serving_sizes)
      mismatched_order_line = order_line.dup
      mismatched_order_line.id = order_line.id
      mismatched_order_line.serving_size_id = nil
      mismatched_order_line
    else
      nil
    end
  end

  def has_missing_serving_size_for(order_line, serving_sizes)
    order_line.serving_size_id.blank? && serving_sizes.present?
  end

  def has_archived_serving_size_for(order_line, serving_sizes)
    order_line.serving_size_id.present? && serving_sizes.blank?
  end
end
