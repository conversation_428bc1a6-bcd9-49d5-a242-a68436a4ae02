class Orders::FetchLeadTime

  def initialize(order:)
    @order = order
    @result = Result.new(order: @order)
  end

  def call
    result.lead_time = lead_time_on_business_day
    result
  end

private

  attr_reader :order, :result

  def lead_time_on_business_day
    return delivery_at if suppliers.blank?

    business_day_for([lead_time_by_day, supplier_lead_time].compact.min)
  end

  def supplier_minimums
    @_supplier_minimums ||= suppliers.map{|supplier| minimums_for(supplier) }.flatten(1)
  end

  def minimums_for(supplier)
    return @_supplier_minimums[supplier] if @_supplier_minimums.present? && @_supplier_minimums[supplier].present?

    base_minimums = Minimum.where(supplier_profile: supplier).includes(:category)
    if order.is_team_order?
      minimums = base_minimums.joins(:category).merge(Category.where(group: 'catering-services')) if order.is_team_order?
    else
      supplier_order_lines = supplier_grouped_order_lines[supplier]
      order_line_categories = supplier_order_lines.map(&:category).compact.uniq
      minimums = base_minimums.select{|minimum| order_line_categories.map(&:id).include?(minimum.category_id) }
      minimums = base_minimums.select{|minimum| order_line_categories.map(&:group).include?(minimum.category.group) } if minimums.blank?
    end
    minimums = base_minimums if minimums.blank?
    @_supplier_minimums ||= {}
    @_supplier_minimums[supplier] = minimums
  end

  def supplier_grouped_order_lines
    @_supplier_grouped_order_lines ||= begin
      lister_options = {
        order: order
      }
      order_lines = OrderLines::List.new(options: lister_options, includes: %i[supplier_profile category]).call
      order_lines.group_by(&:supplier_profile)
    end
  end

  def lead_time_by_day
    min_lead_time_day_before = supplier_minimums.reject{|minimum| ['', nil].include?(minimum.lead_time_day_before) }.map(&:lead_time_day_before).min
    if min_lead_time_day_before.present? && min_lead_time_day_before.include?(':')
      hour, minute = min_lead_time_day_before.split(':')
      delivery_at.change(hour: hour.to_i, min: minute.to_i) - 1.days
    else
      nil
    end
  end

  def supplier_lead_time
    max_lead_time = supplier_minimums.reject{|minimum| minimum.lead_time.blank? }.map(&:lead_time).max
    max_lead_time = 0 if max_lead_time.blank?
    delivery_at - max_lead_time.to_i.hours
  end

  def business_day_for(date)
    business_day = date
    while is_public_holiday?(business_day) || business_day.saturday? || business_day.sunday?
      business_day -= 1.day
    end
    business_day
  end

  def is_public_holiday?(date)
    Holiday.where(on_date: (date.beginning_of_day..date.end_of_day), state: [nil, delivery_state]).where.not(push_to: nil).present?
  end

  def suppliers
    @_suppliers ||= order.is_team_order? ? order.team_supplier_profiles : order.supplier_profiles
  end

  def delivery_at
    @_delivery_at ||= order.delivery_at
  end

  def delivery_state
    @_delivery_state ||= order.delivery_suburb.present? ? order.delivery_suburb.state : nil
  end

  class Result
    attr_reader :order
    attr_accessor :lead_time

    def initialize(order:)
      @order = order
      @lead_time = nil
    end

    def lead_time_hours
      (order.delivery_at - lead_time) / 1.hours
    end

    def past_lead_time?
      lead_time <= Time.zone.now
    end
  end
end
