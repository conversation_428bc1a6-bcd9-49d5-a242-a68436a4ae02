class Orders::Update

  ORDER_SUPPLIER_FIELDS = %i[delivery_fee_override].freeze
  TOTAL_DEPENDENT_FIELDS = %i[no_delivery_charge charge_to_minimum credit_card_id order_supplier].freeze
  AMENDABLE_FIELDS = %i[order_supplier].freeze

  def initialize(order:, order_params: {}, suburb: nil, profile: nil)
    @order = order
    @order_params = order_params
    @suburb = suburb
    @profile = order&.customer_profile || profile
    @result = Result.new(order: order)
  end

  def call
    order.update(sanitized_params)
    if order.valid?
      result.order = order.reload
      update_order_supplier if order_params[:order_supplier].present?
      process_woolworths_delivery_details if order_params[:is_woolworths_order] # start of a new Woolworths Order
      set_and_validate_delivery_windwow if order_params[:associated_woolworths_order_attributes].present? && order_params[:associated_woolworths_order_attributes][:delivery_window_id].present?
      charge_to_minimum if order_params[:charge_to_minimum].present?
      attach_coupon if order_params[:coupon_code].present?
      recalculate_totals
      update_order_status
      sync_dear_order if %w[new amended].include?(order.status)
    else
      result.errors += order.errors.full_messages
    end

    result
  end

private

  attr_accessor :result
  attr_reader :order, :order_params, :suburb, :profile

  def sanitized_params
    [sanitized_order_params, suburb_params].inject(&:merge)
  end

  def sanitized_order_params
    order_params.to_h.symbolize_keys.except(:coupon_code, :is_woolworths_order, :order_supplier)
  end

  def suburb_params
    return {} if suburb.blank?

    {
      delivery_suburb_id: suburb.id
    }
  end

  def update_order_supplier
    order_supplier_params = order_params[:order_supplier]
    order_supplier = order.order_suppliers.where(supplier_profile_id: order_supplier_params[:supplier_profile_id]).first_or_initialize
    if order_supplier_params[:id].present? && order_supplier.id != order_supplier_params[:id]
      result.errors << 'Anomalous order supplier update'
      return

    end
    order_supplier.update(order_supplier_params.slice(*ORDER_SUPPLIER_FIELDS))
  end

  def delivery_detail_errors
    errors = []
    errors << 'Please select a valid street address' if order.delivery_address.blank?
    errors << 'We are unable to process the order for this suburb' if order.delivery_suburb.blank?
    errors << 'Please select a valid delivery date-time' if order.delivery_at.blank?
    errors
  end

  def process_woolworths_delivery_details
    return if order.woolworths_order.present?

    if (delivery_errors = delivery_detail_errors.presence)
      result.errors += delivery_errors
      return
    end

    begin
      Woolworths::Order::AttachToOrder.new(order: order).call
      Woolworths::API::EmptyTrolley.new(order: order).call
    rescue Woolworths::Order::AttachToOrder::NoAccountsAvailableError, Woolworths::API::Connection::ConnectionError, Woolworths::API::Connection::NoAccountError, Woolworths::API::EmptyTrolley::EmptyTrolleyError
      result.errors << 'The Woolworths monkeys are busy right now, please try again in a few minutes'
    else
      Woolworths::ProcessOrderDeliveryDetails.new(order: order).delay(queue: :instant, attempts: 2).call
      Woolworths::Order::SyncOrderLines.new(order: order).call
    end
  end

  def set_and_validate_delivery_windwow
    begin
      Woolworths::API::SetDeliveryWindow.new(order: order).call
    rescue Woolworths::API::Connection::ConnectionError, Woolworths::API::Connection::NoAccountError
      result.errors << 'We seem to have lost connection with Woolworths. Please clear cart and start again!'
    rescue Woolworths::API::SetDeliveryWindow::DeliveryWindowError
      result.errors << 'The Window is no longer available. Please select another one!'
    end
  end

  def charge_to_minimum
    Orders::ChargeToSupplierMinimums.new(order: order).call
  end

  def attach_coupon
    coupon_attacher = Coupons::AttachToOrder.new(coupon_code: order_params[:coupon_code], order: order, profile: profile).call
    if coupon_attacher.success?
      result.order = coupon_attacher.order
    else
      result.errors += coupon_attacher.errors
    end
  end

  def recalculate_totals
    updated_fields = order_params.keys.map(&:to_sym)
    return if (updated_fields & TOTAL_DEPENDENT_FIELDS).blank? || updated_fields.include?(:coupon_code)

    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
  end

  def update_order_status
    updated_fields = order_params.keys.map(&:to_sym)
    return if (updated_fields & AMENDABLE_FIELDS).blank? || %w[new confirmed skipped paused cancelled].exclude?(order.status)

    updated_reference = order.reload.current_version_ref
    order.update(version_ref: updated_reference, status: 'amended')
  end

  def sync_dear_order
    return if !order.is_dear_order?

    dear_sale_suppliers = order.dear_sales.map(&:supplier_profile)
    dear_suppliers = (order.dear_suppliers + dear_sale_suppliers).uniq
    return if dear_suppliers.blank?

    dear_suppliers.each do |supplier|
      Dear::SyncOrder.new(order: order, supplier: supplier).delay(queue: :data_integrity).call
    end
  end

  class Result
    attr_accessor :order, :errors

    def initialize(order:)
      @order = order
      @errors = []
    end

    def success?
      errors.blank? && order.present? && order.persisted?
    end
  end

end
