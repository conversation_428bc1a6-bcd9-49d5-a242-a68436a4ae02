class Orders::CheckIntegrity

  def initialize(starts:, ends:)
    @starts = starts
    @ends = ends
    @result = Result.new
  end

  def call
    ranged_orders.each do |order|
      check_integrity_for(order)
    end
    result
  end

private

  attr_reader :starts, :ends, :result

  def ranged_orders
    orders = Order.where(status: %w[new amended comfirmed pending])
    orders = orders.where(delivery_at: [starts..ends])
    orders
  end

  def check_integrity_for(order)
    result.pending_orders << order
  end

  class Result
    attr_accessor :pending_orders, :errors

    def initialize
      @pending_orders = []
      @errors = []
    end

    def success?
      errors.blank? && pending_orders.blank?
    end
  end

end
