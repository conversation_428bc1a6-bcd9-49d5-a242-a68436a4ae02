class Orders::ListSupplierClosureOrders
  
  def initialize(options: {}, includes: [])
    @lister_options = [default_options, options.deep_symbolize_keys].inject(&:merge)
    @includes = includes
  end

  def call
    @closure_orders = base
    filter_by_status
    filter_by_supplier_closures
    filter_one_offs if lister_options[:exclude_one_offs].present?
    sort_orders if lister_options[:order_by].present?

    @closure_orders.includes(includes).distinct
  end

private

  attr_reader :closure_orders, :lister_options, :includes

  def base
    Order.all
  end

  def filter_by_status
    # @closure_orders = closure_orders.where.not(status: %w[draft pending skipped rejected cancelled paused delivered voided])
    @closure_orders = closure_orders.where(status: %w[quoted new amended confirmed])
  end

  def filter_by_supplier_closures
    @closure_orders = closure_orders.joins(:supplier_profiles).where('orders.delivery_at BETWEEN supplier_profiles.close_from AND supplier_profiles.close_to')
  end

  def filter_one_offs
     @closure_orders = closure_orders.where.not(order_type: 'one-off')
  end

  def sort_orders
    @closure_orders = closure_orders.order(lister_options[:order_by])
  end

  def default_options
    {
      order_by: :delivery_at,
      exclude_one_offs: true
    }
  end

end