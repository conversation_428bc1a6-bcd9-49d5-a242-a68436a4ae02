class Orders::Notifications::SendOrderAdjustedSupplierNotifications

  def initialize(order:, verbose: false)
    @order = order
    @since = order.suppliers_notified_at.dup
    @verbose = verbose
    @result = Result.new
  end

  def call
    begin
      if can_notify?
        (deleted_supplier_ids + adjusted_supplier_ids).uniq.each do |supplier_id|
          supplier = SupplierProfile.find supplier_id
          supplier_item_changes = item_changes_for(supplier)
          next if supplier_item_changes.blank?

          print 's-' if verbose
          email_sender = Suppliers::Emails::SendOrderAdjustedEmail.new(supplier: supplier, order: order, item_changes: supplier_item_changes).call
          if email_sender.success?
            result.sent_notifications << email_sender.sent_notification
          else
            result.errors += email_sender.errors
          end
        end
        notify_error if result.errors.present?
      end
    rescue => exception
      result.errors << "There were issues sending order adjusted emails for order ##{order.id} - #{exception.message}"
      notify_error(exception)
    end
    result
  end

private

  attr_reader :order, :since, :verbose, :result

  def can_notify?
    case
    when order.status != 'delivered'
      result.errors << 'Order needs to be adjusted post delivery'
    end
    result.errors.blank?
  end

  def email_ref
    "#{Suppliers::Emails::SendOrderAdjustedEmail::EMAIL_TEMPLATE}-#{order.version_ref}"
  end

  def item_changes_for(supplier)
    Orders::GetOrderItemChanges.new(order: order, supplier: supplier, since: since).call
  end

  def deleted_supplier_ids
    deleted_order_lines = []
    versions = PaperTrail::Version.where(event: 'destroy', item_type: 'OrderLine')
    versions = versions.where('created_at > ?', since)
    versions = versions.where('object LIKE (?)', "%order_id: #{order.id}%")
    versions.map do |order_line_version|
      deleted_order_lines << order_line_version.reify
    end
    deleted_order_lines.map(&:supplier_profile_id).uniq
  end

  def adjusted_supplier_ids
    order.order_lines.where(status: 'amended').joins(:supplier_profile).joins("LEFT JOIN emails ON emails.fk_id = supplier_profiles.id AND emails.ref = '#{email_ref}'").where('emails.id is ?', nil).pluck(:supplier_profile_id).uniq
  end

  def notify_error(exception)
    exception ||= RuntimeError.new('Error Sending Order Adjusted Emails to order suppliers')
    Raven.capture_exception(exception,
      extra: { errors: result.errors },
      transaction: 'Orders::Notifications::SendOrderAdjustedSupplierNotifications'
    )
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
