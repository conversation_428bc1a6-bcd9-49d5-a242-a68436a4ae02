class Orders::Notifications::SendOrderAmendedNotifications

  def initialize(time: Time.zone.now, delayed: false, verbose: false)
    @time = time
    @delayed = delayed
    @verbose = verbose
    @result = Result.new
  end

  def call
    (recently_updated_orders + recently_amended_orders_with_removed_order_lines).compact.uniq.each do |order|
      print 'o-' if verbose
      result.notifiable_orders << order
      notify_suppliers_of(order)
    end
    result
  end

private

  attr_reader :time, :delayed, :verbose, :result

  def recently_amended_orders
    order_arel = Order.arel_table
    one_off_condition = order_arel[:order_type].eq('one-off')
    recurrent_condition = order_arel[:order_type].eq('recurrent')
    template_id_condition = order_arel[:template_id].eq(order_arel[:id])
    recurrent_template_condition = order_arel.grouping(recurrent_condition.and(template_id_condition))
    template_order_condition = one_off_condition.or(recurrent_template_condition)

    orders = Order.where(status: 'amended')
    orders = orders.where('delivery_at >= ?', time.beginning_of_day - 1.day)
    # orders = orders.where("(orders.order_type = 'one-off' OR (orders.order_type != 'one-off' AND orders.template_id = orders.id))")
    orders.where(template_order_condition)    
  end

  def recently_updated_orders
    order_arel = Order.arel_table
    order_updated_condition = order_arel[:updated_at].gt(order_arel[:suppliers_notified_at])
    order_line_arel = OrderLine.arel_table
    order_line_status_condition = order_line_arel[:status].eq('amended')
    order_line_updated_condition = order_line_arel[:updated_at].gt(order_arel[:suppliers_notified_at])
    order_line_amended_condition = order_line_arel.grouping(order_line_status_condition.and(order_line_updated_condition))
    update_condition = order_updated_condition.or(order_line_amended_condition)

    recently_amended_orders.left_outer_joins(:order_lines).where(update_condition)
  end

  def recently_amended_orders_with_removed_order_lines
    order_arel = Order.arel_table
    versions_arel = PaperTrail::Version.arel_table

    version_destroy_condition = versions_arel[:event].eq('destroy')
    version_order_line_condition = versions_arel[:item_type].eq('OrderLine')
    version_creation_condition = versions_arel[:created_at].gt(order_arel[:suppliers_notified_at])
    order_line_destroyed_condition = version_destroy_condition.and(version_order_line_condition).and(version_creation_condition)
  
    recently_amended_orders.joins("LEFT OUTER JOIN versions ON (versions.object ilike ('%order_id: ' || orders.id || '%') )").where(order_line_destroyed_condition)
  end

  def notify_suppliers_of(order)
    if delayed
      Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).delay(queue: :notifications).call
    else
      notification_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call
      if notification_sender.success?
        result.notified_orders << order
        result.sent_notifications += notification_sender.sent_notifications
      else
        result.errors += notification_sender.errors
      end
    end
  end

  class Result
    attr_accessor :notifiable_orders, :notified_orders, :sent_notifications, :errors

    def initialize
      @notifiable_orders = []
      @notified_orders = []
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
