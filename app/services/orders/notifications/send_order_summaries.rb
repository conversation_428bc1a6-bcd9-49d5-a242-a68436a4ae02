class Orders::Notifications::SendOrderSummaries

  def initialize(time: Time.zone.now, summary_type: 'daily')
    @time = time
    @summary_type = summary_type
    @result = Result.new
  end

  def call
    order_delivery_days.each do |summary_day|
      send_supplier_notifications(summary_day)
      send_customer_notifications(summary_day) if summary_type == 'daily'
    end
    result
  end

private

  attr_reader :time, :summary_type, :result

  def order_delivery_days
    return [] if time.saturday? || time.sunday? # no emails on weekends

    case summary_type
    when 'morning'
      [time]
    when 'daily'
      daily_delivery_days
    when 'reminder'
      reminder_delivery_days
    end
  end

  def daily_delivery_days
    if time.friday?
      (1..3).map do |num|
        time + num.days
      end
    else
      [time + 1.day]
    end
  end

  def reminder_delivery_days
    if time.friday?
      (2..4).map do |num|
        time + num.days
      end
    else
      [time + 2.days]
    end
  end

  def send_supplier_notifications(summary_day)
    notifications_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: summary_day, summary_type: summary_type).call
    if notifications_sender.success?
      result.supplier_notifications += notifications_sender.sent_notifications
    else
      result.errors += notifications_sender.errors
    end
  end

  def send_customer_notifications(summary_day)
    notifications_sender = Customers::Notifications::SendOrderSummariesForDay.new(summary_day: summary_day).call
    if notifications_sender.success?
      result.customer_notifications += notifications_sender.sent_notifications
    else
      result.errors += notifications_sender.errors
    end
  end

  class Result
    attr_accessor :supplier_notifications, :customer_notifications, :errors

    def initialize
      @supplier_notifications = []
      @customer_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
