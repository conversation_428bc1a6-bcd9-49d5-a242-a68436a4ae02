class Orders::Notifications::RequestLoadingDockCode

  def initialize(date: Date.today)
    @date = date
    @result = Result.new
  end

  def call
    loading_dock_orders.group_by(&:customer_profile).each do |customer, customer_orders|
      send_email_to(customer: customer, orders: customer_orders)
    end
    result
  end

private

  attr_reader :date, :result

  def send_email_to(customer:, orders:)
    Customers::Emails::SendLoadingDockRequestEmail.new(customer: customer, orders: orders, date: date).delay(queue: :notfication).call

    result.notified_customers[customer] ||= []
    result.notified_customers[customer] += orders
  end

  def loading_dock_orders
    lister_options = {
      from_date: date,
      to_date: date + 3.days,
      statuses: %w[new amended confirmed pending],
      delivery_type: 'loading_dock',
      order_by: { delivery_at: :asc, id: :asc }
    }
    orders = Orders::List.new(options: lister_options, includes: [:customer_profile]).call
    orders.where(loading_dock: nil)
  end

  class Result
    attr_accessor :notified_customers, :errors

    def initialize
      @notified_customers = {}
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
