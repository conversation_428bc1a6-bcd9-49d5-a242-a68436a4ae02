class Orders::Notifications::NotifyAccountManagers

  def initialize(order:)
    @order = order
    @result = Result.new(order: order)
  end

  def call
    if can_notify?
      account_managers.each do |account_manager|
        send_email_to(account_manager)
      end
    end
    result
  end

private

  attr_reader :order, :result

  def can_notify?
    case
    when order.blank?
      result.errors << 'Cannot notify without an order'
    when account_managers.blank?
      result.warnings << 'Order customer does not have any account managers'
    when order.order_type != 'one-off'
      result.warnings << 'Order is not a one-off order'
    when !is_pantry_order?
      result.warnings << 'Order is not pantry order'
    end
    result.errors.blank? && result.warnings.blank?
  end

  def account_managers
    @_account_managers = order.customer_profile.access_permissions_as_customer.where(active: true, scope: 'account_manager').map(&:admin)
  end

  def send_email_to(account_manager)
    email_sender = Admin::Emails::SendManagedOrderEmail.new(order: order, account_manager: account_manager).call
    if email_sender.success?
      result.notifications << email_sender.sent_notification
    else
      result.errors += email_sender.errors
    end
  end

  def is_pantry_order?
    order_major_category = Orders::RetrieveMajorOrderCategory.new(order: order).call
    order_major_category.present? && order_major_category.group == 'kitchen-supplies'
  end

  class Result
    attr_accessor :order, :notifications, :errors, :warnings

    def initialize(order:)
      @order = order
      @notifications = []
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && warnings.blank? && notifications.present?
    end
  end

end