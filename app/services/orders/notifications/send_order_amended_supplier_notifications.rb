class Orders::Notifications::SendOrderAmendedSupplierNotifications

  CUSTOMER_FIELDS = %i[contact_name phone company_name department_identity].freeze
  DELIVERY_FIELDS = %i[delivery_at delivery_address delivery_address_level delivery_instruction delivery_type].freeze
  ORDER_CHANGE_FIELDS = (CUSTOMER_FIELDS + DELIVERY_FIELDS).freeze

  def initialize(order:, notify_now: false, verbose: false)
    @order = order
    @since = order.suppliers_notified_at.dup
    @notify_now = notify_now
    @verbose = verbose
    @result = Result.new
  end

  def call
    begin
      if can_notify?
        (deleted_supplier_ids + amended_supplier_ids).uniq.each do |supplier_id|
          supplier = SupplierProfile.find supplier_id
          supplier_item_changes = item_changes_for(supplier)
          next if order_detail_changes.blank? && supplier_item_changes.blank?

          print 's-' if verbose
          email_sender = Suppliers::Emails::SendOrderAmendedEmail.new(supplier: supplier, order: order, detail_changes: order_detail_changes, item_changes: supplier_item_changes).call
          if email_sender.success?
            result.sent_notifications << email_sender.sent_notification
          else
            result.errors += email_sender.errors
          end
        end
        notify_error if result.errors.present?
      end
    rescue => exception
      result.errors << "There were issues sending order amended emails for order ##{order.id} - #{exception.message}"
      notify_error(exception)
    end
    result
  end

private

  attr_reader :order, :since, :notify_now, :verbose, :result

  def can_notify?
    case
    when order.status != 'amended'
      result.errors << 'Order needs to be amended'
    when order.is_recurrent? && (order.template_id.blank? || order.template_id != order.id)
      result.errors << 'Order needs to be a one-off or template order'
    when !notify_now && !order.is_team_order? && last_updated_at > (Time.zone.now - 20.minutes) # YOR-609 send order change email for one order after update, skipping 20 minutes wait
      result.errors << 'Cannot notify now'
    end
    result.errors.blank?
  end

  def last_updated_at
    [order.updated_at, order.order_lines.where(status: 'amended').maximum(:updated_at)].compact.max
  end

  def email_ref
    "#{Suppliers::Emails::SendOrderAmendedEmail::EMAIL_TEMPLATE}-#{order.version_ref}"
  end

  def order_detail_changes
    @_order_detail_changes ||= Orders::GetOrderDetailChanges.new(order: order, since: since).call
  end

  def item_changes_for(supplier)
    Orders::GetOrderItemChanges.new(order: order, supplier: supplier, since: since).call
  end

  def deleted_supplier_ids
    versions = PaperTrail::Version.where(event: 'destroy', item_type: 'OrderLine')
    versions = versions.where('created_at > ?', since)
    versions = versions.where('object LIKE (?)', "%order_id: #{order.id}%")
    return [] if versions.blank?

    versions.map do |order_line_version|
      order_line_version.reify&.supplier_profile_id
    end.compact.uniq
  end

  def amended_supplier_ids
    if has_order_changes?
      order.supplier_profiles.pluck(:id).uniq
    else
      order.order_lines.where(status: 'amended').joins(:supplier_profile).joins("LEFT JOIN emails ON emails.fk_id = supplier_profiles.id AND emails.ref = '#{email_ref}'").where('emails.id is ?', nil).pluck(:supplier_profile_id).uniq
    end
  end

  def has_order_changes?
    latest_version = order.versions.order(:id).where('created_at > ?', since).where(event: 'update').first
    return false if latest_version.blank?

    version_order = latest_version.reify
    ORDER_CHANGE_FIELDS.any?{|field| version_order.send(field) != order.send(field) }
  end

  def notify_error(exception)
    exception ||= RuntimeError.new('Error Sending Order Amended Emails to order suppliers')
    Raven.capture_exception(exception,
      extra: { errors: result.errors  },
      transaction: 'Orders::Notifications::SendOrderAmendedSupplierNotifications'
    )
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
