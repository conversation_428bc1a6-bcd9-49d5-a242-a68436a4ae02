class Documents::Generate::RecipientGeneratedInvoice < Documents::Generate::Base

  DOCUMENT_KIND = 'recipient_generated_invoice'.freeze

  def initialize(invoice:)
    @invoice = invoice
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :invoice

  def document_generator
    @_document_generator ||= Invoices::RecipientGeneratedInvoice.new(invoice: invoice)
  end

  def setup_for_generation
    @documentable = invoice
    @document_kind = DOCUMENT_KIND
    @document_name = 'Recipient Generated Invoice'
  end

end
