class Documents::Generate::SupplierOrderSummary < Documents::Generate::Base

  DOCUMENT_KIND = 'supplier_order_summary'.freeze

  def initialize(supplier:, order_lines:, summary_day:, summary_type:)
    @supplier = supplier
    @order_lines = order_lines
    @summary_day = summary_day
    @summary_type = summary_type
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :supplier, :order_lines, :summary_day, :summary_type

  def document_generator
    @_document_generator ||= Suppliers::OrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type)
  end

  def setup_for_generation
    @documentable = supplier
    @document_kind = DOCUMENT_KIND
  end

  def document_name
    case summary_type
    when 'reminder'
      "supplier-orders-reminder-#{summary_day.to_s(:date_compact)}-#{supplier.id}"
    else # daily or morning
      "supplier-orders-summary-#{summary_day.to_s(:date_compact)}-#{supplier.id}-#{summary_type}"
    end
  end

end
