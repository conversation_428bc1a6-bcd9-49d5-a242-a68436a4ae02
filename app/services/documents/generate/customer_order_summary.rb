class Documents::Generate::CustomerOrderSummary < Documents::Generate::Base

  DOCUMENT_KIND = 'customer_daily_summary'.freeze

  def initialize(customer:, orders:, summary_day:)
    @customer = customer
    @orders = orders
    @summary_day = summary_day
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :customer, :orders, :summary_day

  def document_generator
    @_document_generator ||= Customers::OrderSummary.new(customer: customer, orders: orders, summary_day: summary_day)
  end

  def setup_for_generation
    @documentable = customer
    @document_kind = DOCUMENT_KIND
    @document_name = "order-summary-#{summary_day.to_s(:date_compact)}"
  end

end
