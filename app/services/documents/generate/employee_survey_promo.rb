class Documents::Generate::EmployeeSurveyPromo < Documents::Generate::Base

  DOCUMENT_KIND = 'employee_survey_promo'.freeze

  def initialize(employee_survey:)
    @employee_survey = employee_survey
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :employee_survey

  def document_generator
    @_document_generator ||= Customers::EmployeeSurveyPromo.new(employee_survey: employee_survey)
  end

  def setup_for_generation
    @documentable = employee_survey
    @document_kind = DOCUMENT_KIND
    @document_name = "Employee Survey Promo - #{employee_survey.category_group_name}"
  end

end