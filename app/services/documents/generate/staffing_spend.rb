class Documents::Generate::StaffingSpend < Documents::Generate::Base

  DOCUMENT_KIND = 'staffing_spend'.freeze

  def initialize(staffing_spends:, with_orders: false)
    @staffing_spends = staffing_spends
    @with_orders = with_orders

    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :staffing_spends, :with_orders

  def document_generator
    @_document_generator ||= Admins::StaffingSpend.new(staffing_spends: staffing_spends, with_orders: with_orders)
  end

  def setup_for_generation
    @documentable = SupplierProfile.where(id: yordar_credentials(:yordar, :staffing_supplier_id)).first
    @document_kind = DOCUMENT_KIND
  end

  def document_name
    "staffing-log-#{Time.zone.now.to_s(:filename)}"
  end

end
