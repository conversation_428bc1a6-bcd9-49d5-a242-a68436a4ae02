class Documents::Generate::CustomerOrderDetails < Documents::Generate::Base

  ORDER_DOCUMENT_KIND = 'customer_order_details'.freeze
  QUOTE_DOCUMENT_KIND = 'customer_order_quote'.freeze
  TEAM_ORDER_DOCUMENT_KIND = 'customer_team_order_details'.freeze

  def initialize(order:, reference: nil, variation: 'normal')
    @order = order
    @reference = reference.presence || order.version_ref
    @variation = variation
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :order, :reference, :variation

  def document_generator
    @_document_generator ||= case 
    when variation == 'team_order_manifest' && order.is_team_order?
      Customers::TeamOrderDetails.new(team_order: order, reference: reference, version: document_version)
    else
      Customers::OrderDetails.new(order: order, reference: reference, version: document_version, is_quote: variation == 'quote')
    end

  end

  def setup_for_generation
    @documentable = order
    @document_kind = case 
    when variation == 'team_order_manifest' && order.is_team_order?
      TEAM_ORDER_DOCUMENT_KIND
    when variation == 'quote'
      QUOTE_DOCUMENT_KIND
    else
      ORDER_DOCUMENT_KIND
    end
  end

  def document_name
    case
    when variation == 'team_order_manifest' && order.is_team_order?
      "team-order-details-#{order.id}-v#{document_version}"
    when variation == 'quote'
      "order-quote-#{order.id}-v#{document_version}"
    else
      "order-details-#{order.id}-v#{document_version}"
    end
  end

end
