class Documents::Generate::Base

  RAW_FILE_FORMATS = ['.csv', '.json'].freeze

private

  attr_reader :documentable, :document_name, :document_kind, :document_url, :document

  # generate the report
  def generate_report
    document_generator.generate
  end

  # upload document to cloudinary
  def upload_report(dry_run: false)
    # render file
    document_generator.render_file
    if dry_run
      @document_url = document_generator.file_path&.to_s
    else
      upload_file_to_cloudinary
    end
  end

  def upload_file_to_cloudinary
    # retrieve file path
    local_file_path = document_generator.file_path

    # upload to cloudinary
    # http://res.cloudinary.com/yordar-<env>/image/upload/v<version-hex>/<report-path>
    upload_options = {
      public_id: document_generator.report_path(ext: ''), # report path without the extension
      use_filename: true,
      unique_filename: false,
    }
    upload_options[:resource_type] = 'raw' if RAW_FILE_FORMATS.any?{|format| local_file_path.to_s.include?(format) }
    cloudinary_response = Cloudinary::Uploader.upload(local_file_path, **upload_options)

    uploaded_file_url = cloudinary_response.present? ? cloudinary_response['secure_url'] : local_file_path

    @document_url = uploaded_file_url
  end

  # create a document record attached to documentable
  def attach_document(dry_run: false)    
    document_params = {
      name: document_name,
      kind: document_kind,
      version: document_version,
      url: document_url
    }
    if dry_run
      @document = OpenStruct.new(document_params)
    else
      document_upserter = Documents::Upsert.new(documentable: documentable, document_params: document_params).call
      if document_upserter.success?
        @document = document_upserter.document
      end
    end
  end

  def document_version
    return 1 if documentable.blank?
    return @version_override if @version_override.present?

    @_document_version ||= ::Documents::GetNextVersion.new(documentable: documentable, kind: document_kind).call
  end

end
