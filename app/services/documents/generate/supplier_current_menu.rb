class Documents::Generate::SupplierCurrentMenu < Documents::Generate::Base

  def initialize(supplier:, reference:)
    @supplier = supplier
    @reference = reference
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :supplier, :reference

  def document_generator
    @_document_generator ||= Suppliers::CurrentMenu.new(supplier: supplier, reference: reference)
  end

  def setup_for_generation
    @documentable = supplier
    @document_kind = 'supplier_current_menu'
    @document_name = "supplier-#{supplier.id}-menu-v#{document_version}"
  end

end
