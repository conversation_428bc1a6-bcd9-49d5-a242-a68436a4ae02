class Documents::Generate::TeamOrderAveryLabels < Documents::Generate::Base

  def initialize(team_order:, supplier:, reference:)
    @team_order = team_order
    @supplier = supplier
    @reference = reference
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :team_order, :supplier, :reference

  def document_generator
    @_document_generator ||= Suppliers::TeamOrderAveryLabels.new(team_order: team_order, supplier: supplier, reference: reference)
  end

  def setup_for_generation
    @documentable = order_supplier.presence || team_order
    @document_kind = 'team_order_avery_labels'
    @document_name = "team-order-#{team_order.id}-avery-labels-v#{document_version}"
  end

  def order_supplier
    @_order_supplier ||= team_order.order_suppliers.where(supplier_profile: supplier).first
  end

end
