class Documents::Generate::SupplierOrderDetails < Documents::Generate::Base

  def initialize(order:, supplier:, reference: nil, variation: 'normal', version_override: nil)
    @order = order
    @supplier = supplier
    @reference = reference.presence || SecureRandom.hex(7)
    @variation = variation
    @version_override = version_override
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :order, :supplier, :reference, :variation

  def document_generator
    @_document_generator ||= case
    when order.is_team_order? && variation != 'heads_up'
      Suppliers::TeamOrderDetails.new(team_order: order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: variation, version: document_version)
    when variation == 'json'
      Suppliers::OrderJsonDetails.new(order: order, supplier: supplier, reference: "#{reference}-#{supplier.id}", version: document_version)
    else
      Suppliers::OrderDetails.new(order: order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: variation, version: document_version)
    end
  end

  def setup_for_generation
    @documentable = order_supplier.presence || order
    @document_kind = case variation
    when 'heads_up'
      'supplier_heads_up_order_details'
    when 'delivery_docket'
      'supplier_order_delivery_details'
    when 'json'
      'supplier_json_order_details'
    else
      'supplier_order_details'
    end
  end

  def order_supplier
    @_order_supplier ||= order.order_suppliers.where(supplier_profile: supplier).first
  end

  def document_name
    order.is_team_order? ? team_order_document_name : order_document_name
  end

  def team_order_document_name
    case variation
    when 'heads_up'
      "supplier-heads-up-team-order-details-#{order.id}-v#{document_version}"
    when 'delivery_docket'
      "supplier-team-order-delivery-details-#{order.id}-v#{document_version}"
    else
      "supplier-team-order-details-#{order.id}-v#{document_version}"
    end
  end

  def order_document_name
    case variation
    when 'json'
      "supplier-json-order-details-#{order.id}-#{supplier.id}-v#{document_version}"
    when 'heads_up'
      "supplier-heads-up-order-details-#{order.id}-#{supplier.id}-v#{document_version}"
    when 'delivery_docket'
      "supplier-order-delivery-details-#{order.id}-#{supplier.id}-v#{document_version}"
    else
      "supplier-order-details-#{order.id}-#{supplier.id}-v#{document_version}"
    end
  end

end
