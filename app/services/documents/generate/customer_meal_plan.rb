class Documents::Generate::CustomerMealPlan < Documents::Generate::Base

  DOCUMENT_KIND = 'customer_meal_plan'.freeze

  def initialize(meal_plan:, orders:, variation: nil)
    @meal_plan = meal_plan
    @orders = orders
    @variation = variation.presence || 'normal'

    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :meal_plan, :orders, :variation

  def document_generator
    @_document_generator ||= Customers::MealPlanDetails.new(meal_plan: meal_plan, orders: orders, variation: variation)
  end

  def setup_for_generation
    @documentable = meal_plan.presence || orders.sample.customer_profile
    @document_kind = DOCUMENT_KIND
  end

  def document_name
    "meal-plan-#{documentable.id}-#{Time.zone.now.to_s(:date_compact)}"
  end

end
