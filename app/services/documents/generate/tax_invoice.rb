class Documents::Generate::TaxInvoice < Documents::Generate::Base

  def initialize(invoice:, document_type:)
    @invoice = invoice
    @document_type = document_type
    setup_for_generation
  end

  def call(dry_run: false)
    generate_report
    upload_report(dry_run: dry_run)
    attach_document(dry_run: dry_run)
  end

private

  attr_accessor :invoice, :document_type, :options

  def document_generator
    @_document_generator ||= begin
      case document_type
      when 'tax_invoice_receipt'
        Invoices::TaxInvoiceReceipt.new(invoice: invoice)
      when 'tax_invoice_spreadsheet'
        Invoices::TaxInvoiceSpreadsheet.new(invoice: invoice)
      else
        Invoices::TaxInvoice.new(invoice: invoice)
      end
    end
  end

  def setup_for_generation
    @documentable = invoice
    @document_kind = document_type
  end

  def document_name
    case document_type
    when 'tax_invoice_receipt'
      "Tax-Invoice-Receipt-#{invoice.number}"
    when 'tax_invoice', 'tax_invoice_spreadsheet'
      "Invoice-#{invoice.number}"
    end
  end

end
