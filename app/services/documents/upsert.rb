class Documents::Upsert

  def initialize(documentable:, document_params: {})
    @documentable = documentable
    @document_params = document_params
    @result = Result.new
  end

  def call
    if can_upsert_document?
      result.document = document = documentable.documents.where(
        kind: document_params[:kind],
        version: document_params[:version]
      ).first_or_initialize
      if !document.update(document_params)
        result.errors += document.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :documentable, :document_params, :result

  def can_upsert_document?
    case
    when documentable.blank?
      result.errors << 'Cannot create a document without a documentable'
    when document_params[:kind].blank?
      result.errors << 'Cannot create a document without a document kind'
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :document, :errors

    def initialize
      @document = nil
      @errors = []
    end

    def success?
      errors.blank? && document.present? && document.persisted?
    end
  end

end
