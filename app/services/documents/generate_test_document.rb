class Documents::GenerateTestDocument

  DEPRECATED_DOCUMENT_KINDS = %w[supplier_order_manifest invoice_location_report invoice_detailed_report].freeze

  def initialize(kind:, variation: nil, dry_run: true)
    @kind = kind
    @variation = variation
    @dry_run = dry_run
    @result = Result.new(kinds: document_kinds)
  end

  def call
    return if !Rails.env.development?

    document_kinds.each do |document_kind|
      generated_document = call_method_for(document_kind)
      if generated_document.present?
        result.generated_kinds << document_kind
        result.generated_documents << generated_document
      else
        result.errored_kinds << document_kind
      end
    end

    puts "Generated #{result.generated_kinds.size} / #{result.kinds.size} documents"

    result
  end

private

  attr_reader :kind, :variation, :dry_run, :result

  def document_kinds
    @_document_kinds ||= case kind
    when 'all'
      puts 'Generating all documents'
      Document::VALID_KINDS - DEPRECATED_DOCUMENT_KINDS
    else
      [kind]
    end
  end

  def call_method_for(document_kind)
    document_method = "generate_#{document_kind.underscore}"
    if respond_to?(document_method, true)
      send(document_method)
    else
      puts "Could not find method for kind - #{document_kind} (method name: #{document_method})"
    end
  end

  def customer
    @_customer ||= CustomerProfile.all.sample
  end

  def supplier
    @_supplier ||= SupplierProfile.where(is_searchable: true).all.sample
  end

  def user
    [customer, supplier].sample.user
  end

  def order(status: 'delivered', variant: 'general', type: 'one-off')
    @_order ||= Order.where(status: status, order_variant: variant, order_type: type).last(100).sample
  end

  def order_customer
    @_order_customer ||= order.customer_profile
  end

  def order_supplier
    @_order_supplier ||= order.supplier_profiles.sample
  end

  def invoice
    @_invoice ||= variation.present? ? Invoice.where(number: variation).first : Invoice.where(pushed_to_xero: true).last(100).sample
  end

  def invoice_customer
    @_invoice_customer ||= invoice.invoice_orders.sample.customer_profile
  end

  def document_reference
    SecureRandom.hex(7)
  end

  def generate_customer_meal_plan
    meal_plan = variation.present? ? MealPlan.find(variation) : MealPlan.joins(:orders).sample
    meal_plan_orders = meal_plan.orders.where.not(delivery_at: nil).sample(3)
    puts "generate_customer_meal_plan #{meal_plan.name} - #{meal_plan.customer_profile.name}"
    Documents::Generate::CustomerMealPlan.new(meal_plan: meal_plan, orders: meal_plan_orders).call(dry_run: dry_run)
  end

  def generate_customer_order_details
    customer_order = variation.present? ? Order.find(variation) : order
    customer = customer_order.customer_profile
    puts "generate_customer_order_details #{customer_order.id} - #{customer.name}"
    Documents::Generate::CustomerOrderDetails.new(order: customer_order, reference: document_reference).call(dry_run: dry_run)
  end

  def generate_customer_order_quote
    customer_order = variation.present? ? Order.find(variation) : order
    customer = customer_order.customer_profile
    puts "generate_customer_order_quote #{customer_order.id} - #{customer.name}"
    Documents::Generate::CustomerOrderDetails.new(order: customer_order, reference: document_reference, variation: 'quote').call(dry_run: dry_run)
  end

  def generate_customer_team_order_details
    team_order = variation.present? ? Order.find(variation) : order(variant: %w[team_order recurring_team_order].sample)
    puts "generate_customer_team_order_details #{team_order.id} - #{team_order.customer_profile.name}"
    Documents::Generate::CustomerOrderDetails.new(order: team_order, reference: document_reference, variation: 'team_order_manifest').call(dry_run: dry_run)
  end

  def generate_customer_daily_summary
    customer_order = variation.present? ? Order.find(variation) : order
    customer = customer_order.customer_profile
    customer_orders = [customer_order]
    summary_day = customer_order.delivery_at
    puts "generate_customer_daily_summary #{customer_orders.map(&:id)} - #{customer.name}"
    Documents::Generate::CustomerOrderSummary.new(customer: customer, orders: customer_orders, summary_day: summary_day).call(dry_run: dry_run)
  end

  def generate_supplier_order_details
    details_order = variation.present? ? Order.find(variation) : order
    supplier = details_order.supplier_profiles.sample
    puts "generate_supplier_order_details #{details_order.id} - #{supplier.name}"
    Documents::Generate::SupplierOrderDetails.new(order: details_order, supplier: supplier, reference: document_reference).call(dry_run: dry_run)
  end

  def generate_supplier_order_delivery_details
    details_order = variation.present? ? Order.find(variation) : order
    supplier = details_order.supplier_profiles.sample
    puts "generate_supplier_order_delivery_details #{details_order.id} - #{supplier.name}"
    Documents::Generate::SupplierOrderDetails.new(order: details_order, supplier: supplier, reference: document_reference, variation: 'delivery_docket').call(dry_run: dry_run)
  end

  def generate_supplier_heads_up_order_details
    details_order = variation.present? ? Order.find(variation) : order
    supplier = details_order.supplier_profiles.sample
    puts "generate_supplier_heads_up_order_details #{details_order.id} - #{supplier.name}"
    Documents::Generate::SupplierOrderDetails.new(order: details_order, supplier: supplier, reference: document_reference, variation: 'heads_up').call(dry_run: dry_run)
  end

  def generate_supplier_json_order_details
    details_order = variation.present? ? Order.find(variation) : order
    supplier = details_order.supplier_profiles.sample
    puts "generate_supplier_json_order_details #{details_order.id} - #{supplier.name}"
    Documents::Generate::SupplierOrderDetails.new(order: details_order, supplier: supplier, reference: document_reference, variation: 'json').call(dry_run: dry_run)
  end

  def generate_team_order_avery_labels
    team_order = variation.present? ? Order.find(variation) : order(variant: 'team_order')
    supplier = team_order.supplier_profiles.sample
    puts "generate_team_order_avery_labels #{team_order.id} - #{supplier.name}"
    Documents::Generate::TeamOrderAveryLabels.new(team_order: team_order, supplier: supplier, reference: document_reference).call(dry_run: dry_run)
  end

  def generate_supplier_current_menu
    excluded_supplier_ids = [yordar_credentials(:woolworths, :supplier_profile_id), yordar_credentials(:woolworths, :staffing_supplier_id)]
    supplier = variation.present? ? SupplierProfile.where(id: variation).first : SupplierProfile.where(is_searchable: true).where.not(id: excluded_supplier_ids).sample
    puts "generate_supplier_current_menu #{supplier.name} - (#{supplier.id})"
    Documents::Generate::SupplierCurrentMenu.new(supplier: supplier, reference: document_reference).call(dry_run: dry_run)
  end

  def generate_supplier_order_summary
    supplier = order_supplier
    order_lines = order.order_lines
    summary_day = order.delivery_at
    summary_type = variation.present? ? variation : %w[daily morning reminder].sample
    puts "generate_supplier_order_summmary #{order.id} - #{supplier.name} - #{summary_type}"
    Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type).call(dry_run: dry_run)
  end

  def generate_tax_invoice_receipt
    puts "generate_tax_invoice_receipt ##{invoice.number} (id: #{invoice.id}) - #{invoice_customer.name}"
    Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: 'tax_invoice_receipt').call(dry_run: dry_run)
  end

  def generate_tax_invoice
    puts "generate_tax_invoice ##{invoice.number} (id: #{invoice.id}) - #{invoice_customer.name}"
    Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: 'tax_invoice').call(dry_run: dry_run)
  end

  def generate_tax_invoice_spreadsheet
    puts "generate_tax_invoice_spreadsheet ##{invoice.number} (id: #{invoice.id}) - #{invoice_customer.name}"
    Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: 'tax_invoice_spreadsheet').call(dry_run: dry_run)
  end

  def generate_recipient_generated_invoice
    supplier_invoice = variation.present? ? SupplierInvoice.find(variation) : SupplierInvoice.last(200).sample
    invoice_supplier = supplier_invoice.supplier_profile
    puts "generate_recipient_generated_invoice Supplier Invoice ##{supplier_invoice.number} - #{invoice_supplier.name}"
    Documents::Generate::RecipientGeneratedInvoice.new(invoice: supplier_invoice).call(dry_run: dry_run)
  end

  def generate_employee_survey_promo
    employee_survey = variation.present? ? EmployeeSurvey.find(variation) : EmployeeSurvey.last(200).sample
    puts "generate_employee_survey_promo #{employee_survey.category_group_name} Employee Survey ##{employee_survey.id} for #{employee_survey.customer_profile.name}"
    Documents::Generate::EmployeeSurveyPromo.new(employee_survey: employee_survey).call(dry_run: dry_run)
  end

  def generate_staffing_spend
    fortnight_options = {
      from_date: (Time.zone.now - 1.week).beginning_of_week,
      to_date: Time.zone.now.end_of_week,
      active_orders_only: true
    }
    staffing_spends = Admin::Reports::FetchPantryManagerSpends.new(options: fortnight_options).call
    staffing_spends = staffing_spends.select do |spend|
      spend.manager.present? && spend.orders.present?
    end
    puts "generate_staffing_spend #{staffing_spends.size}"
    Documents::Generate::StaffingSpend.new(staffing_spends: staffing_spends, with_orders: [true, false].sample).call(dry_run: dry_run)
  end

  class Result
    attr_accessor :generated_kinds, :errored_kinds, :generated_documents
    attr_reader :kinds

    def initialize(kinds:)
      @kinds = kinds
      @generated_kinds = []
      @errored_kinds = []
      @generated_documents = []
    end

    def success?
      errored_kinds.blank? && generated_kinds.size == kinds.size
    end
  end

end