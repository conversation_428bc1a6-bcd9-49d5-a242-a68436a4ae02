class Documents::GetNextVersion

  RECURRING_ORDER_DOCUMENT_KINDS = %w[
    supplier_order_details
    supplier_order_delivery_details
    supplier_json_order_details
  ].freeze

  def initialize(documentable:, kind:)
    @documentable = documentable
    @kind = kind
    @version = 1
  end

  def call
    if is_valid_document?
      case
      when RECURRING_ORDER_DOCUMENT_KINDS.include?(kind) && is_changing_recurring_order? && latest_document.blank?
        @version = 2
      when latest_document.present? && latest_document.version.present?
        @version = latest_document.version + 1
      end
    end
    version
  end

private

  attr_reader :documentable, :kind, :version

  def is_valid_document?
    documentable.present? && kind.present?
  end

  def latest_document
    documentable.documents.where(kind: kind).order(version: :asc).last
  end

  def is_changing_recurring_order?
    order = documentable.is_a?(OrderSupplier) && documentable.order
    order.present? && order.recurrent_id.present? && order.status != 'new'
  end

end
