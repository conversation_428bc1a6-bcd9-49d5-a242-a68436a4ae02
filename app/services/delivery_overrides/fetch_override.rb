class DeliveryOverrides::FetchOverride

  def initialize(customer:, supplier:)
    @customer = customer
    @supplier = supplier
  end

  def call
    return nil if customer.blank? || supplier.blank? || supplier_scoped_overrides.blank?

    supplier_scoped_overrides.max_by do |override|
      weight_for(override)
    end
  end

private

  attr_reader :customer, :supplier

  def customer_overrides
    @_customer_overrides ||= customer.delivery_overrides.where(active: true)
  end

  # remove overrides which do not match the passed in supplier
  def supplier_scoped_overrides
    customer_overrides.reject do |override|
      case override.supplier_kind
      when 'specific'
        override.supplier_profile != supplier
      when 'catering'
        !is_catering_supplier?
      when 'pantry'
        !is_pantry_supplier?
      end
    end
  end

  # specific over category over all
  def weight_for(override)
    case override.supplier_kind
    when 'specific'
      100
    when 'catering', 'pantry'
      50
    else # 'all'
      0
    end
  end

  def is_catering_supplier?
    @_is_catering_supplier ||= supplier.has_catering_services
  end

  def is_pantry_supplier?
    @_is_pantry_supplier ||= supplier.has_kitchen_supplies
  end
end