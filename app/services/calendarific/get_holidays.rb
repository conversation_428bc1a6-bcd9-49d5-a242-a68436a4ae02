class Calendarific::GetHolidays

  BASE_URL = "https://calendarific.com/api/v2/holidays?api_key=#{yordar_credentials(:calendarific, :api_key)}&country=AU&type=local,national".freeze

  def self.call(year: Time.zone.now.year)
    puts "Fetching holidays for year #{year} from API"

    api_url = BASE_URL + "&year=#{year}"
    response = Faraday.get(api_url)
    if response.status != 200
      raise StandardError.new 'Error communicating with the Calendarific API'
    end

    parsed_response = JSON.parse(response.body, object_class: OpenStruct)
    parsed_response.response.holidays
  end

end
