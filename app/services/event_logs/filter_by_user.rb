class EventLogs::FilterByUser

  EVENT_BASED_ACCESS = {
    super_admin: %w[
      woolworths-checkout-failed
      order-canceled-permanently
      order-rejected
      order-below-margin-threshold
      new-customer-registration
      new-supplier-registration
      supplier-agreement-signed
      upcoming-public-holiday
      monthly-calendar-event
      new-amazon-order
      new-quote-submitted
      magin-updated
      on-hold-charge-failed
    ],
    yordar_admin: %w[
      woolworths-checkout-failed
      order-canceled-permanently
      order-rejected
      upcoming-public-holiday
      monthly-calendar-event
      approaching-cutoff-below-minimum
      new-order-submitted
      order-amended
      orders-auto-confirmed
      new-amazon-order
      new-quote-submitted
      pending-orders
    ],
    account_manager: %w[
      woolworths-checkout-failed
      order-canceled-permanently
      order-rejected
      order-below-margin-threshold
      upcoming-public-holiday
      monthly-calendar-event
      new-order-quoted
      new-order-submitted
      new-amazon-order
      order-amended
      new-custom-order-quoted
      new-custom-order-submitted
      new-team-order-created
      new-package-created
      package-extended
      approaching-cutoff-below-minimum
      order-canceled
      new-quote-submitted
      pending-orders
      new-customer-registration
      new-supplier-registration
      budget-spend-50
      budget-spend-75
      budget-spend-90
      budget-spend-100
    ],
    pantry_manager: %w[
      new-order-submitted
      order-amended
      woolworths-checkout-failed
      order-rejected
      budget-spend-50
      budget-spend-75
      budget-spend-90
      budget-spend-100
    ],
    customer_team_admin: %w[
      new-order-submitted
      order-amended
      order-canceled
      order-canceled-permanently
      invoice-overdue
    ],
    supplier_admin: %w[
      new-supplier-registration
      supplier-agreement-signed
      searchable-updated
      margin-updated
    ]
  }.freeze

  def initialize(user:, existing_logs:)
    @user = user
    @event_logs = existing_logs
  end

  def call
    @event_logs = case
    when event_logs.blank? || user_kind.blank?
      EventLog.none
    else
      event_logs.where(event: EVENT_BASED_ACCESS[user_kind])
    end

    event_logs
  end

private

  attr_reader :user, :event_logs

  def base_event_logs
    user.present? && user_kind.present? ? EventLog.all : EventLog.none
  end

  def user_kind
    return nil if user.blank?

    @_user_kind ||= case
    when user.super_admin?
      :super_admin
    when user.admin? && customer.present? && customer.active_admin_access_permissions.where(scope: 'account_manager').present?
      :account_manager
    when user.admin?
      :yordar_admin
    when user.can_access_suppliers?
      :supplier_admin
    when customer.present? && customer.active_admin_access_permissions.where(scope: 'pantry_manager').present?
      :pantry_manager
    when customer.present? && customer.active_admin_access_permissions.where(scope: [nil, 'full_access', 'company_team_admin']).present?
      :customer_team_admin
    else
      nil
    end
  end

  def customer
    return nil if user.blank?

    @_customer ||= begin
      profileable = user&.profile&.profileable
      profileable.present? && profileable.is_a?(CustomerProfile) ? profileable : nil
    end
  end

end