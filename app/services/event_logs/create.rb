class EventLogs::<PERSON><PERSON>

  def initialize(event:, event_object: nil, severity: 'info', **event_info)
    @event = event
    @event_object = event_object
    @severity = severity
    @event_info = event_info
    @result = Result.new
  end

  def call
    event_log = base_event_log
    if event_log.valid? && event_log.save
      result.event_log = event_log
      broadcast_event
    else
      result.errors += event_log.errors.full_messages
    end
    result
  end

private

  attr_reader :event, :event_object, :severity, :event_info, :result

  def broadcast_event
    Admin::Notifications::BroadcastCount.new.call
  end

  def base_event_log
    if event_object.present? && (event_object.is_a?(Order) || event_object.is_a?(Holiday))
      EventLog.where(sanitized_params).first_or_initialize
    else
      EventLog.new(sanitized_params)
    end
  end

  def sanitized_params
    [loggable_params, scopable_params, info_params].inject(&:merge)
  end

  def loggable_params
    {
      loggable: event_object,
      event: event,
      severity: severity,
    }
  end

  def scopable_params
    return {} if event_object.blank?

    scopable_object = case
    when event_object.is_a?(Order) || event_object.is_a?(CustomerBudget)
      event_object.customer_profile
    when event_object.is_a?(Invoice)
      event_object.orders.sample&.customer_profile
    else
      nil
    end
    scopable_object.present? ? { scopable: scopable_object } : {}
  end

  def info_params
    object_info = case
    when event_object.present? && event_object.is_a?(Order)
      {
        type: (event_object.order_variant == 'recurring_team_order' ? 'team_order' : event_object.order_variant),
        is_recurrent: event_object.is_recurrent? || event_object.is_recurring_team_order?,
      }
    else
      {}
    end.merge(event_info)
    object_info.present? ? { info: object_info } : {}
  end

  class Result
    attr_accessor :event_log, :errors

    def initialize
      @event_log = nil
      @errors = []
    end

    def success?
      errors.blank? && event_log.present? && event_log.persisted?
    end
  end
end