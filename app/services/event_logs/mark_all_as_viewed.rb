class EventLogs::<PERSON><PERSON><PERSON><PERSON><PERSON>iewed

  def initialize(user:, lister_options: {}, include_high_severity: false)
    @user = user
    @lister_options = [default_options, lister_options.to_h.symbolize_keys].inject(&:merge)
    @include_high_severity = include_high_severity
    @result = Result.new
  end

  def call
    viewable_notifications.each do |notification|
      view_marker = EventLogs::MarkAsViewed.new(event_log: notification, user: user, in_bulk: true).call
      if view_marker.success?
        result.viewed_notifications << notification 
      end
    end
    result.pending_notifications += pending_notifications

    broadcast_count if result.viewed_notifications.present?
    
    result
  end

private
  attr_reader :user, :lister_options, :include_high_severity, :result

  def unviewed_notifications
    @_unviewed_notifications ||= Admin::ListNotifications.new(options: lister_options).call
  end

  def viewable_notifications
    return unviewed_notifications if include_high_severity || lister_options[:severity].present?

    @_viewable_notifications ||= unviewed_notifications.where.not(severity: %w[warning error])
  end

  def pending_notifications
    @_pending_notifications ||= unviewed_notifications.where.not(id: viewable_notifications.map(&:id))
  end

  def broadcast_count
    Admin::Notifications::BroadcastCount.new(user: user).call
  end

  def default_options
    {
      for_user: user,
      limit: nil,
      unviewed_only: true,
    }
  end

  class Result
    attr_accessor :viewed_notifications, :pending_notifications

    def initialize 
      @viewed_notifications = []
      @pending_notifications = []
    end

    def success?
      pending_notifications.blank?
    end
  end

end