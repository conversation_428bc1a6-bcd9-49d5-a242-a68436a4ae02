class EventLogs::Mark<PERSON><PERSON><PERSON>

  def initialize(event_log:, user:)
    @event_log = event_log
    @user = user
    @result = Result.new
  end

  def call
    if can_mark?
      if update_event_log_assignment
        result.event_log = event_log
        mark_as_viewed
      else
        result.errors += event_log.errors.full_messages
      end
    end

    result
  end

private

  attr_reader :event_log, :user, :result

  def can_mark?
    case
    when event_log.blank?
      result.errors << 'Cannot mark as assigned without an event log'
    when user.blank?
      result.errors << 'Cannot mark as assigned without user'
    when already_assigned? && event_log.assigned_to != user
      result.errors << 'Event log is aready assigned by someone'
    when EventLog::ASSIGNABLE_EVENTS.exclude?(event_log.event)
      result.errors << 'This event is not assignable'
    end
    result.errors.blank?
  end

  def update_event_log_assignment
    if already_assigned?
      event_log.update(assigned_to: nil)
    else
      event_log.update(assigned_to: user)
    end
  end

  def mark_as_viewed
    EventLogs::MarkAsViewed.new(event_log: event_log, user: user).call
  end

  def already_assigned?
    @_already_assigned ||= event_log.assigned_to.present?
  end

  class Result
    attr_accessor :event_log, :errors

    def initialize
      @event_log = nil
      @errors = []
    end

    def success?
      errors.blank? && event_log.present?
    end
  end

end