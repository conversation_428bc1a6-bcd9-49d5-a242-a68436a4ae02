class EventLogs::<PERSON><PERSON><PERSON>iewed

  def initialize(event_log:, user:, in_bulk: false)
    @event_log = event_log
    @user = user
    @in_bulk = in_bulk
    @result = Result.new
  end

  def call
    if can_mark?
      event_log_view = user.event_log_views.where(event_log: event_log,).first_or_initialize
      if event_log_view.valid? && event_log_view.update(in_bulk: in_bulk)
        result.event_log_view = event_log_view
        broadcast_event if !in_bulk
      else
        result.errors += event_log_view.errors.full_messages
      end
    end

    result
  end

private

  attr_reader :event_log, :user, :in_bulk, :result

  def can_mark?
    case
    when event_log.blank?
      result.errors << 'Cannot mark as viewed without an event log'
    when user.blank?
      result.errors << 'Cannot mark as viewed without user'
    end
    result.errors.blank?
  end

  def broadcast_event
    Admin::Notifications::BroadcastCount.new(user: user).call
  end

  class Result
    attr_accessor :event_log_view, :errors

    def initialize
      @event_log_view = nil
      @errors = []
    end

    def success?
      errors.blank? && event_log_view.present? && event_log_view.persisted?
    end
  end

end