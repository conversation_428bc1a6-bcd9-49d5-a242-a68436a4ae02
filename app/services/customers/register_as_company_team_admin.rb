class Customers::RegisterAsCompanyTeamAdmin

  def initialize(company_team_admin:, customer:)
    @company_team_admin = company_team_admin
    @customer = customer
    @result = Result.new
  end

  def call
    if can_register?
      make_company_team_admin
      update_admin_permissions
      update_company
      update_billing_details
    end

    result
  end

private

  attr_reader :company_team_admin, :customer, :result

  def can_register?
    case
    when company_team_admin.blank?
      result.errors << 'Cannot register without a potential company team admin (customer)'
    when customer.blank? || !customer.is_a?(CustomerProfile) || !customer.user&.is_active
      result.errors << 'Cannot register without an active customer'
    end
    result.errors.blank?
  end

  def make_company_team_admin
    return if company_team_admin.company_team_admin?

    company_team_admin.update(company_team_admin: true)
  end

  def update_admin_permissions
    access_permission = company_team_admin.active_admin_access_permissions.where(customer_profile: customer).first_or_initialize
    if access_permission.update(scope: 'company_team_admin')
      result.company_team_admin = company_team_admin.reload
      result.customer = customer
    else
      result.errors << 'Could not add adminable company_team_admin under the company_team_admin'
    end
  end

  def update_company
    return if company_team_admin.company.present?

    admin_company = customer.company
    return if admin_company.blank?

    updated_company_company_team_admins = (admin_company.customer_profiles + [company_team_admin]).uniq
    admin_company.update(customer_profiles: updated_company_company_team_admins)
  end

  def update_billing_details
    customer_billing_details = customer.billing_details
    return if customer_billing_details.blank?

    billing_details_params = {
      name: customer_billing_details.name,
      email: customer_billing_details.email,
      phone: customer_billing_details.phone,
      address: customer_billing_details.address,
      suburb: customer_billing_details.suburb
    }
    billing_creator = Customers::BillingDetails::Upsert.new(customer: company_team_admin, detail_params: billing_details_params).call
    if !billing_creator.success?
      result.errors += billing_creator.errors
    end
  end  

  class Result
    attr_accessor :company_team_admin, :customer, :errors

    def initialize
      @company_team_admin = nil
      @customer = nil
      @errors = []
    end

    def success?
      errors.blank? && company_team_admin.present? && customer.present? && company_team_admin.active_adminable_customer_profiles.include?(customer)
    end
  end

end