# deprecated.. but kept for legacy purposes to check
class Customers::<PERSON><PERSON><PERSON>onthlyReport

  def initialize(customer:, start_date:, end_date:, company_wide: false, verbose: false)
    @customer = customer
    @start_date = start_date
    @end_date = end_date
    @company_wide = company_wide
    @verbose = verbose
  end

  def call(as_data: true)
    if as_data
      generate_csv_as_data
    else
      generate_csv_as_file
    end
  end

private

  attr_reader :customer, :start_date, :end_date, :company_wide, :verbose

  def generate_csv_as_file
    report_csv = Rails.root.join('tmp', 'customer_monthly_report.csv')
    CSV.open(report_csv, 'wb') do |csv|
      name_in_csv = company_wide && customer.company.present? ? customer.company.name : customer.name
      csv << [name_in_csv]
      csv << []
      monthly_orders_rows.each do |row|
        csv << row
        print 'c-' if verbose
      end
    end
    report_csv
  end

  def generate_csv_as_data
    CSV.generate do |csv|
      name_in_csv = company_wide && customer.company.present? ? customer.company.name : customer.name
      csv << [name_in_csv]
      csv << []
      monthly_orders_rows.each do |row|
        csv << row
      end
    end
  end

  def monthly_orders_rows
    monthly_orders_rows = []
    months.each do |start_of_month|
      monthly_orders_rows += order_line_rows_for(start_of_month)
    end
    monthly_orders_rows
  end

  def months
    current_month = start_date.beginning_of_month
    months = [current_month]
    while current_month != end_date.beginning_of_month
      current_month += 1.month
      months << current_month
    end
    months
  end

  def month_grouped_orders
    return @_month_grouped_orders if @_month_grouped_orders.present?

    orders = Order.where(status: 'delivered')
    orders = orders.where(updated_at: [start_date.beginning_of_month..end_date.end_of_month])
    if company_wide && customer.company.present?
      orders = orders.joins(:customer_profile).where(customer_profiles: { company_id: customer.company_id })
    else
      orders = orders.where(customer_profile: customer)
    end
    orders.group_by do |order|
      order.updated_at.beginning_of_month.to_s
    end
  end

  def order_lines_for(month_start)
    orders = month_grouped_orders[month_start.to_s]
    return [] if orders.blank?

    lister_options = {
      orders: orders,
      confirmed_attendees_only: orders.detect(&:is_team_order?),
    }
    OrderLines::List.new(options: lister_options, includes: %i[order menu_item]).call
  end

  def order_line_rows_for(month_start)
    grand_total = 0
    grand_avg_spend_total = 0

    # rows for period (month start to month end)
    order_line_rows = []
    order_line_rows << ["Period #{month_start.to_s(:date)} to #{month_start.end_of_month.to_s(:date)}"]
    order_line_rows << []

    monthly_order_lines = order_lines_for(month_start)

    if monthly_order_lines.blank?
      order_line_rows << ['No orders recorded for this period']
      order_line_rows << []
      order_line_rows << []
      return order_line_rows
    end

    # header for data within period
    order_line_rows << ['LOCATION', 'CATEGORY', 'MENU ITEM', 'NUMBER OF MONTHLY ITEMS ORDERED', 'AVERAGE SPEND PER ORDER', 'TOTAL SPEND', '% OF TOTAL EXPENDITURE']

    # group by address
    address_grouped_order_lines = monthly_order_lines.group_by {|order_line| order_line.order.delivery_address_arr.join(', ') }

    address_grouped_order_lines.each do |address, address_order_lines|
      order_line_rows << [address]

      menu_item_grouped_order_lines = address_order_lines.group_by(&:menu_item)

      location_total_spend, location_average_spend = get_location_spends(menu_item_grouped_order_lines)
      grand_total += location_total_spend
      grand_avg_spend_total += location_average_spend

      order_line_rows += category_spend_rows(menu_item_grouped_order_lines, location_total_spend)

      order_line_rows << Array.new(4, '') + ["Location: #{number_to_currency(location_average_spend)}", number_to_currency(location_total_spend)]
    end

    order_line_rows << []
    order_line_rows << Array.new(3, '') + ['Monthly Totals:', "$#{grand_avg_spend_total}", "$#{grand_total}"]
    order_line_rows << []
    order_line_rows << []

    order_line_rows
  end

  # get location spend and average spend per menu item
  def get_location_spends(menu_item_grouped_order_lines)
    # need to loop through menu item to determine each items total price
    item_spends = menu_item_grouped_order_lines.map do |_, menu_item_order_lines|
      qty = menu_item_order_lines.sum(&:quantity)
      price = qty * menu_item_order_lines.first.price
      average = price / (qty == 0 ? 1 : qty)
      { price: price, average: average }
    end
    location_total_spend = item_spends.map{|spend| spend[:price] }.sum
    location_average_spend = item_spends.map{|spend| spend[:average] }.sum
    [location_total_spend, location_average_spend]
  end

  # group by category or menu section and show order line counts and price and percent of total
  def category_spend_rows(menu_item_grouped_order_lines, location_total_spend)
    category_rows = []
    category_or_section_grouped_order_lines = category_grouped_order_lines(menu_item_grouped_order_lines)
    category_or_section_grouped_order_lines.each do |category_or_section_name, category_order_lines|
      category_rows << Array.new(1, '') + [category_or_section_name]

      category_total_price = 0
      category_total_quantity = 0

      name_grouped_order_lines = category_order_lines.group_by(&:name).sort_by{|order_line_name, _| order_line_name }
      name_grouped_order_lines.map do |order_line_name, named_order_lines|
        total_quantity = named_order_lines.sum(&:quantity)
        total_spend = total_quantity * named_order_lines.first.price

        category_total_quantity += total_quantity
        category_total_price += total_spend
        location_spend_percent = (total_spend / (location_total_spend == 0 ? 1 : location_total_spend)) * 100

        category_rows << Array.new(2, '') + [order_line_name, total_quantity, '', number_to_currency(total_spend), number_to_percentage(location_spend_percent)]
      end
      category_total_average = category_total_price / (category_total_quantity == 0 ? 1 : category_total_quantity)
      category_rows << Array.new(4, '') + ["Category: #{number_to_currency(category_total_average)}"]
    end
    category_rows
  end

  # get group the menu items by category or menu section
  def category_grouped_order_lines(menu_item_grouped_order_lines)
    category_order_lines = {}
    menu_item_grouped_order_lines.each do |menu_item, order_lines|
      menu_item_categories = menu_item.menu_section.categories
      category_or_section_name = menu_item_categories.present? ? menu_item_categories.first.name : menu_item.menu_section.name

      category_order_lines[category_or_section_name] ||= []
      category_order_lines[category_or_section_name] += order_lines
    end
    category_order_lines.sort_by{|category_or_section_name, _| category_or_section_name }
  end

end
