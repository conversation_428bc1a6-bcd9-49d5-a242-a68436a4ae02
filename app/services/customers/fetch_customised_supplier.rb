class Customers::FetchCustomisedSupplier

  def initialize(customer:, supplier_id: nil)
    @customer = customer
    @supplier_id = supplier_id
    @result = Result.new
  end

  def call
    if can_fetch?
      fetch_favourites
      if customer.company_id.present?
        fetch_rate_cards
      end
    end
    result
  end

private

  attr_reader :customer, :supplier_id, :result

  def can_fetch?
    customer.present? && customer.is_a?(CustomerProfile)
  end

  def fetch_favourites
    favourite_menu_items = customer.favourite_menu_items
    favourite_menu_items = favourite_menu_items.joins(:menu_item).where(menu_items: { supplier_profile_id: supplier_id }) if supplier_id.present?
    result.favourite_menu_item_ids = favourite_menu_items.pluck(:menu_item_id)
  end

  def fetch_rate_cards
    supplier = supplier_id.present? ? SupplierProfile.where(id: supplier_id).first : nil
    lister_options = {
      supplier: supplier,
      active_only: true,
    }
    result.rate_cards = RateCards::List.new(customer: customer, options: lister_options).call
  end

  class Result
    attr_accessor :favourite_menu_item_ids, :rate_cards

    def initialize
      @favourite_menu_item_ids = []
      @rate_cards = []
    end
  end

end
