class Customers::FavouriteSupplier

  def initialize(customer:, supplier:, kind: nil)
    @customer = customer
    @supplier = supplier
    @kind = kind
    @result = Result.new(supplier: supplier)
  end

  def call
    if can_favourite?
      existing_favourite_suppliers.include?(supplier) ? unfavourite_supplier : favourite_supplier
    end
    result
  end

private
  
  attr_reader :customer, :supplier, :kind, :result

  def can_favourite?
    case
    when customer.blank?
      result.errors << 'Cannot favourite supplier without a customer'
    when supplier.blank?
      result.errors << 'Cannot favourite without a supplier'
    when !can_access_supplier?
      result.errors << 'You do not have access to this supplier'
    end
    result.errors.blank?
  end

  def existing_favourite_suppliers
    @_existing_favourite_suppliers ||= is_team_order? ? customer.favourite_team_suppliers : customer.favourite_suppliers
  end

  def update_favourites_with(new_favourites)
    case
    when is_team_order?
      customer.update(favourite_team_suppliers: new_favourites)
    else
      customer.update(favourite_suppliers: new_favourites)
    end
  end

  def unfavourite_supplier
    new_favourites = existing_favourite_suppliers - [supplier]    
    if update_favourites_with(new_favourites)
      result.favourite_mode = 'un-favourited'
    end
  end

  def favourite_supplier
    new_favourites = existing_favourite_suppliers + [supplier]
    if update_favourites_with(new_favourites.uniq)
      result.favourite_mode = 'favourited'
    end
  end

  def can_access_supplier?
    supplier.customer_profiles.blank? || supplier.customer_profiles.include?(customer)
  end

  def is_team_order?
    kind == 'team_order'
  end

  class Result
    attr_reader :supplier
    attr_accessor :favourite_mode, :errors

    def initialize(supplier:)
      @supplier = supplier
      @favourite_mode = nil
      @errors = []
    end

    def success?
      errors.blank? && supplier.present? && favourite_mode.present?
    end
  end

end