class Customers::RegisterUnderCompanyTeamAdmin

  def initialize(customer:, company_team_admin:)
    @customer = customer
    @company_team_admin = company_team_admin
    @result = Result.new(customer: customer, company_team_admin: company_team_admin)
  end

  def call
    if can_register?
      update_admin_permissions
      update_company
      update_billing_details
    end

    result
  end

private

  attr_reader :customer, :company_team_admin, :result

  def can_register?
    case
    when customer.blank?
      result.errors << 'Cannot register without a customer'
    when company_team_admin.blank? || !company_team_admin.respond_to?(:company_team_admin) || !company_team_admin.company_team_admin?
      result.errors << 'Cannot register without an active Company team Admin'
    end
    result.errors.blank?
  end

  def update_admin_permissions
    access_permission = company_team_admin.active_admin_access_permissions.where(customer_profile: customer).first_or_initialize
    if access_permission.update(scope: 'company_team_admin')
      result.company_team_admin = company_team_admin.reload
      result.customer = customer
    else
      result.errors << 'Could not add customer under the company team admin'
    end
  end

  def update_company
    return if customer.company.present?

    admin_company = company_team_admin.company
    return if admin_company.blank?

    updated_company_customers = (admin_company.customer_profiles + [customer]).uniq
    admin_company.update(customer_profiles: updated_company_customers)
  end

  def update_billing_details
    admin_billing_details = company_team_admin.billing_details
    return if admin_billing_details.blank?

    billing_details_params = {
      name: admin_billing_details.name,
      email: admin_billing_details.email,
      phone: admin_billing_details.phone,
      address: admin_billing_details.address,
      suburb: admin_billing_details.suburb
    }
    billing_creator = Customers::BillingDetails::Upsert.new(customer: customer, detail_params: billing_details_params).call
    if !billing_creator.success?
      result.errors += billing_creator.errors
    end
  end  

  class Result
    attr_accessor :customer, :company_team_admin, :errors

    def initialize(customer:, company_team_admin:)
      @customer = nil
      @company_team_admin = nil
      @errors = []
    end

    def success?
      errors.blank? && customer.present? && company_team_admin.present? && company_team_admin.active_adminable_customer_profiles.include?(customer)
    end
  end

end