class Customers::SaveStaffDetails

  def initialize(customer:, detail_params: {})
    @customer = customer
    @detail_params = detail_params
    @result = Result.new
  end

  def call
    return result if !can_save?

    if staff_details.update(detail_params)
      result.staff_details = staff_details
      notify_accounts_team if staff_details.complete?
    else
      result.errors = staff_details.errors.full_messages
    end
    result
  end

private

  attr_reader :customer, :detail_params, :result

  def can_save?
    case
    when customer.blank? || !customer.is_a?(CustomerProfile)
      result.errors << 'Cannot save details without a customer'
    when detail_params.blank?
      result.errors << 'Cannot save without details'
    end
    result.errors.blank?
  end

  def notify_accounts_team
    email_sender = Admin::Emails::SendStaffDetailsEmail.new(customer: customer).call
    if email_sender.success?
      result.accounts_team_notified = true
    else
      result.errors += email_sender.errors
    end
  end

  def staff_details
    @_staff_details ||= customer.staff_details.presence || customer.build_staff_details
  end

  class Result
    attr_accessor :staff_details, :accounts_team_notified, :errors

    def initialize
      @staff_details = nil
      @accounts_team_notified = false
      @errors = []
    end

    def success?
      errors.blank? && staff_details.present? && staff_details.persisted?
    end
  end

end