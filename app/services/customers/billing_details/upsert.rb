class Customers::BillingDetails::Upsert

  def initialize(customer:, detail_params: {})
    @customer = customer
    @detail_params = detail_params
    @result = Result.new
  end

  def call
    if can_upsert_billing_details?
      if customer_billing_details.update(detail_params)
        result.billing_details = customer_billing_details
      else
        result.errors += customer_billing_details.errors.full_messages
      end
    end
    result
  end

private

  attr_reader :customer, :detail_params
  attr_accessor :result

  def customer_billing_details
    @_customer_billing_details ||= customer.billing_details || customer.build_billing_details
  end

  def can_upsert_billing_details?
    case
    when customer.blank?
      result.errors << 'Customer is missing'
    when customer_billing_details.new_record? && detail_params.blank?
      result.errors << 'Cannot create new billing detail without data'
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :billing_details, :errors

    def initialize
      @billing_details = nil
      @errors = []
    end

    def success?
      errors.blank? && billing_details.present? && billing_details.persisted?
    end
  end

end
