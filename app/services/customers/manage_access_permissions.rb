class Customers::ManageAccessPermissions

  def initialize(customer:, access_permissions: [])
    @customer = customer
    @access_permissions = access_permissions
    @result = Result.new(customer: customer)
  end

  def call
    if can_manage?
      access_permissions.each do |access_permission|
        case
        when access_permission[:id].present? && access_permission[:_delete].present?
          remove_access_for(access_permission: access_permission)
        when access_permission[:id].present?
          update_access_for(access_permission: access_permission)
        else
          create_access_for(access_permission: access_permission)
        end
      end
      update_company_team_admin_flag
    end
    result
  end

private

  attr_reader :customer, :access_permissions, :result

  def can_manage?
    case
    when customer.blank?
      result.errors << 'Cannot manage without a customer'
    when access_permissions.detect{|permission| permission[:customer_profile_id] == customer.id }.present?
      result.errors << 'Permissions contains admin to self'
    end
    result.errors.blank?
  end

  def remove_access_for(access_permission:)
    permission = customer.admin_access_permissions.where(id: access_permission[:id]).first
    if !permission.destroy
      result.errors << "Could not remove access_permission with ID: #{access_permission[:id]}" # " - #{permission.errors.full_messages.join(', ')}"
    end
  end

  def update_access_for(access_permission:)
    permission = customer.admin_access_permissions.where(id: access_permission[:id]).first
    if permission.update(access_permission.except(:id))
      result.access_permissions << permission
    else
      result.errors << "Could not update access_permission with ID: #{access_permission[:id]}" # " - #{permission.errors.full_messages.join(', ')}"
    end
  end

  def create_access_for(access_permission:)
    permission = customer.admin_access_permissions.where(customer_profile_id: access_permission[:customer_profile_id]).first_or_initialize
    if permission.update(access_permission.except(:id))
      result.access_permissions << permission
    else
      result.errors << "Could not create access_permission with Customer ID: #{access_permission[:customer_profile_id]}" # " - #{permission.errors.full_messages.join(', ')}"
    end
  end

  def update_company_team_admin_flag
    is_company_team_admin = customer.reload.admin_access_permissions.where(active: true).present?
    customer.update(company_team_admin: is_company_team_admin)
  end

  def existing_permissions
    @_existing_permissions = customer.admin_access_permissions
  end

  class Result
    attr_accessor :access_permissions, :errors
    attr_reader :customer

    def initialize(customer:)
      @customer = customer
      @access_permissions = []
      @errors = []
    end

    def success?
      @errors.blank?
    end
  end

end