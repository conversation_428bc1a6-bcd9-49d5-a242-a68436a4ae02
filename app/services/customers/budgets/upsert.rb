class Customers::Budgets::Upsert

  def initialize(customer:, budget: nil, budget_params: {})
    @customer = customer
    @budget_params = budget_params
    @budget = budget.presence || fetch_customer_budget
    @result = Result.new
  end

  def call
    if can_upsert_budget?
      save_current_budget
    end
    result
  end

private

  attr_reader :customer, :budget, :budget_params
  attr_accessor :result

  def can_upsert_budget?
    case
    when customer.blank?
      result.errors << 'Customer is missing'
    when budget_params.blank? || budget_params[:starts_on].blank?
      result.errors << 'Cannot create/update budget without data'
    end
    result.errors.blank?
  end

  def save_current_budget
    can_manage_future_budgets = budget.new_record? # cache before saving
    if budget.update(sanitized_params)
      result.budget = budget
      manage_future_budgets if can_manage_future_budgets && Rails.env.test?
    else
      result.errors += budget.errors.full_messages
    end
  end

  def manage_future_budgets
    Customers::Budgets::ManageFutureBudgets.new(customer: customer, budget_params: budget_params, current_budget: budget).call
  end

  def sanitized_params
    [
      budget_params,
      sanitized_date_params
    ].inject(&:merge)
  end

  def sanitized_date_params
    {
      starts_on: budget_from,
      ends_on: budget_to,
    }
  end

  def budget_from
    return nil if budget_params.blank?

    @_budget_from ||= begin
      starts_on = budget_params[:starts_on].is_a?(String) ? Date.parse(budget_params[:starts_on]) : budget_params[:starts_on]
      starts_on&.beginning_of_month
    end
  end

  def budget_to
    return nil if budget_params.blank?

    @_budget_to ||= begin
      ends_on = budget_params[:ends_on].is_a?(String) ? Date.parse(budget_params[:ends_on]) : budget_params[:ends_on]
      ends_on&.end_of_month
    end
  end

  def fetch_customer_budget
    customer.present? && budget_from.present? && customer.budgets.where(starts_on: budget_from, customer_purchase_order_id: budget_params[:customer_purchase_order_id]).first_or_initialize
  end

  class Result
    attr_accessor :budget, :errors

    def initialize
      @budget = nil
      @errors = []
    end

    def success?
      errors.blank? && budget.present? && budget.persisted?
    end
  end

end
