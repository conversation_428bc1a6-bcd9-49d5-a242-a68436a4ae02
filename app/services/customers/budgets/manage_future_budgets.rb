class Customers::Budgets::ManageFutureBudgets

  def initialize(customer:, budget_params:, current_budget: nil)
    @customer = customer
    @budget_params = budget_params
    @current_budget = current_budget
    @result = Result.new
  end

  def call
    if budget_to.blank?
      purge(future_budgets) if future_budgets.present?
    elsif budgets_in_range.present?
      purge(budgets_in_range)
    end
    result
  end

private

  attr_reader :customer, :current_budget, :budget_params, :result

  def purge(purgable_budgets)
    purgable_budgets.each do |budget|
      duped_budget = budget.dup
      if budget.destroy
        result.purged_budgets << duped_budget
      else
        result.errors += budget.errors.full_messages
      end
    end
  end

  def future_budgets
    @future_budgets ||= begin
      budgets = customer.budgets.where('starts_on > ?', budget_from)
      budgets.where(customer_purchase_order_id: budget_params[:customer_purchase_order_id])
    end
  end

  def budgets_in_range
    @_budgets_in_range ||= begin
      budgets = customer.budgets.where(starts_on: [budget_from..budget_to])
      budgets = budgets.where(customer_purchase_order_id: budget_params[:customer_purchase_order_id])
      budgets = budgets.where.not(id: current_budget.id) if current_budget.present?
      budgets
    end
  end

  def budget_from
    @_budget_from ||= begin
      starts_on = budget_params[:starts_on].is_a?(String) ? Date.parse(budget_params[:starts_on]) : budget_params[:starts_on]
      starts_on&.beginning_of_month
    end
  end

  def budget_to
    @_budget_to ||= begin
      ends_on = budget_params[:ends_on].is_a?(String) ? Date.parse(budget_params[:ends_on]) : budget_params[:ends_on]
      ends_on&.end_of_month
    end
  end

  class Result
    attr_accessor :purged_budgets, :errors

    def initialize
      @purged_budgets = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end