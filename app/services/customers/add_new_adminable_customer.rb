class Customers::AddNewAdminableCustomer

  def initialize(customer_params:, company_team_admin:)
    @customer_params = customer_params
    @company_team_admin = company_team_admin
    @result = Result.new
  end

  def call
    if can_register?
      register_customer
    end
    result
  end

private

  attr_reader :customer_params, :company_team_admin, :result

  def can_register?
    case
    when company_team_admin.blank? || !company_team_admin.company_team_admin?
      result.errors << 'Cannot register customer without an admin'
    when customer_params[:firstname].blank? || customer_params[:lastname].blank?
      result.errors << 'Need both first and last name to register'
    end
    result.errors.blank?
  end

  def register_customer
    customer_registration = Customers::Register.new(user_params: customer_user_params, registration_params: customer_registration_params, company_team_admin: company_team_admin).call
    if customer_registration.success?
      result.customer = customer_registration.customer
    else
      result.errors += customer_registration.errors
    end
  end

  def customer_user_params
    customer_email = "#{customer_params[:firstname].parameterize}.#{customer_params[:lastname].parameterize}+#{SecureRandom.hex(2)}@yordar.com.au"
    {
      email: customer_email,
      password: SecureRandom.hex(7),
      suburb_id: company_team_admin.user&.suburb_id,
    }.merge(customer_params.to_h.symbolize_keys)
  end

  def customer_registration_params
    {
      role: 'Other',
      contact_phone: company_team_admin.contact_phone
    }
  end

  class Result
    attr_accessor :customer, :errors

    def initialize
      @customer = nil
      @company_team_admin = nil
      @errors = []
    end

    def success?
      errors.blank? && customer.present? && customer.persisted?
    end
  end

end
