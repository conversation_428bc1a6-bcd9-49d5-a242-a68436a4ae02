class Customers::Emails::SendCustomerInvitationLinkEmail < Notifications::Base
  include Rails.application.routes.url_helpers

  EMAIL_TEMPLATE = 'customer-company_customer_invitation_link'.freeze

  def initialize(customer:)
    @customer = customer
    @result = Result.new
  end

  def call
    return result if email_already_sent?

    begin
      send_email
    rescue => exception
      error_message = 'Failed to send customer invitation link email.'
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :result

  def email_already_sent?
    if (existing_email = Email.where(email_options).presence)
      result.errors << 'Email already sent!'
    end
    existing_email.present?
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: customer.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Customer user invitation link email sent to #{customer.name}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: Your custom user invitation link'
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: customer.email_salutation,
      invite_url: invitation_link,
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink
    }
  end

  def invitation_link
    new_company_customer_registration_url(company_team_admin_code: customer.uuid, host: app_host)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}"
  end

end

