class Customers::Emails::SendFinalClosureEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-final_closure'.freeze

  def initialize(customer:, orders:)
    @customer = customer
    @orders = orders
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send Final Closure email to customer #{customer.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :orders, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: customer.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Final Closure email sent to customer #{customer.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: Holiday period skipped orders - #{Time.zone.now.year}"
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref
    }
  end

  def email_variables
    final_last_delivery_at = orders_data.map{|x| x.last_delivery_at.present? ? Time.zone.parse(x.last_delivery_at) : nil }.compact.max
    final_first_delivery_at = orders_data.map{|x| x.first_delivery_at.present? ? Time.zone.parse(x.first_delivery_at) : nil }.compact.min
    {
      firstname: customer.email_salutation,

      orders: orders_data,
      final_last_delivery_at: final_last_delivery_at.present? ? final_last_delivery_at.to_s(:full) : 'N/A',
      final_first_delivery_at: final_first_delivery_at.present? ? final_first_delivery_at.to_s(:full) : 'N/A',
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink,
    }
  end

  def orders_data
    return @_orders_data if @_orders_data.present?

    sanitised_orders = []
    recurrent_orders, one_off_orders = orders.partition(&:is_recurrent?)

    recurrent_orders.group_by(&:template_id).each do |template_id, template_orders|
      related_orders = Order.where(template_id: template_id)

      last_delivery_order = related_orders.joins(:supplier_profiles).where.not(delivery_at: nil).where('orders.delivery_at < supplier_profiles.close_from').order(:delivery_at).last
      first_delivery_order = related_orders.joins(:supplier_profiles).where.not(delivery_at: nil).where('orders.delivery_at > supplier_profiles.close_to').order(:delivery_at).first

      sanitised_orders << {
        name: template_orders.first.name.truncate(25, omission: '...'),
        last_delivery_at: (last_delivery_order.present? ? last_delivery_order.delivery_at.to_s(:full) : nil),
        first_delivery_at: (first_delivery_order.present? ? first_delivery_order.delivery_at.to_s(:full) : nil)
      }
    end

    one_off_orders.each do |order|
      sanitised_orders << {
        name: order.name
      }
    end
    @_orders_data = deep_struct(sanitised_orders)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}"
  end

end
