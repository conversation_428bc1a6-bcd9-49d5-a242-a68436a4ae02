class Customers::Emails::SendOrderQuoteEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper
  include MenuItemHelper

  EMAIL_TEMPLATE = 'customer-order_quote'.freeze

  def initialize(order:, customer:, document: nil, quote_emails: nil, quote_message: nil)
    @order = order
    @customer = customer
    @document = document
    @attachments = []
    @quote_emails = quote_emails
    @quote_message = quote_message
    @result = Result.new
  end

  def call
    begin
      generate_and_attach_document
      send_email
    rescue => exception
      error_message = "Failed to send order ##{order.id} quote email to #{email_recipients}"
      log_errors(exception: exception, message: error_message, sentry: true)
      result.errors << error_message
    end
    result
  end

private

  attr_reader :order, :customer, :attachments, :quote_emails, :quote_message, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Order ##{order.id} quote email sent to #{email_recipients}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    quote_emails.presence || customer.email_recipient
  end

  def email_subject
    subject = "YORDAR: Your quote for order ##{order.id}"
    subject += " (Ver.#{@details_document.version})" if @details_document.present? && @details_document.version > 1
    subject
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,

      quote_message: quote_message,

      order: deep_struct(orders_data),
      supplier_grouped_order_lines: deep_struct(supplier_grouped_order_lines),
      totals: deep_struct(totals),

      customer: deep_struct(customer_data),
      account_managers: deep_struct(account_manager_data_for(customer)),
      
      is_customer_email: is_customer_email?,

      header_color: :pink,
    }
  end

  def orders_data
    {
      id: order.id,
      name: order.name,
      link: url_helper.order_show_url(order, host: app_host),
      date: order.delivery_at.to_s(:full),
      delivery_address: order.delivery_address_arr.join(', '),
      customer_total: number_to_currency(order.customer_total),
      quote_pdf_url: @details_document&.url,
      edit_url: order_manage_urls(mode: 'edit'),
      confirm_url: order_manage_urls(mode: 'approve'),
      reject_url: order_manage_urls(mode: 'reject'),
    }
  end

  def customer_data
    customer_email = case
    when customer.user.present?
      customer.user.email
    else
      yordar_credentials(:yordar, :admin_email)
    end
    {
      name: customer.name,
      email: customer_email
    }
  end

  def generate_and_attach_document
    @details_document = case
    when @document.present?
      @document
    when is_customer_email?
      Documents::Generate::CustomerOrderDetails.new(order: order, reference: email_ref, variation: 'quote').call
    else
      order.documents.where(kind: 'customer_order_quote').order(version: :desc).first
    end
    if @details_document.present?
      attachments << @details_document
    end
  end

  def is_customer_email?
    quote_emails.blank?
  end

  def email_salutation
    is_customer_email? ? customer.email_salutation : 'there'
  end

  def order_manage_urls(mode:)
    return nil if !is_customer_email?

    path = url_helper.order_approve_or_reject_url(order_id: order.id, profile_type: 'customer', profile_id: customer.id, mode: mode, hashed_value: md5_hash, host: app_host)
    url_shortner = Shortener::ShortenedUrl.generate(path)
    url_helper.shortened_url(url_shortner.unique_key, host: app_host)
  end

  def md5_hash
    @_md5_hash ||= Digest::MD5.hexdigest(order.id.to_s + customer.id.to_s + yordar_credentials(:random_salt))
  end

  def supplier_grouped_order_lines
    order_lines.group_by(&:supplier_profile).map do |supplier, supplier_order_lines|
      hashed_order_lines = supplier_order_lines.map do |order_line|
        menu_item = order_line.menu_item
        {
          name: order_line.name.strip.truncate(50, omission: ' ...'),
          quantity: order_line.quantity,
          price: number_to_currency(order_line.price),
          image: cloudinary_image(menu_item.image),
          description: menu_item.description,
          dietary_preferences: dietary_preferences_for(menu_item: menu_item)
        }
      end
      {
        supplier: {
          name: supplier.company_name.strip,
          image: cloudinary_image(supplier.profile.avatar),
        },
        has_more: false,
        order_lines: hashed_order_lines
      }
    end
  end

  def totals
    {
      customer_subtotal: number_to_currency(order.customer_subtotal),
      order_discount: sanitized_currency_for(order.discount),
      customer_delivery: number_to_currency(order.customer_delivery),
      customer_gst: number_to_currency(order.customer_gst),
      customer_surcharge: sanitized_currency_for(order.customer_surcharge),
      customer_topup: sanitized_currency_for(order.customer_topup),
      customer_total: number_to_currency(order.customer_total),
    }
  end

  def dietary_preferences_for(menu_item:)
    dietary_preferences(menu_item: menu_item).map do |field, preference|
      [preference[:letter], field]
    end
  end

  def order_lines
    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    OrderLines::List.new(options: lister_options, includes: %i[supplier_profile menu_item]).call
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}-#{Time.zone.now.to_s(:date)}"
  end

end
