class Customers::Emails::SendPublicHolidayOrdersEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'customer-public_holiday_orders'.freeze

  def initialize(customer:, holiday:, handled_orders:)
    @customer = customer
    @holiday = holiday
    @handled_orders = handled_orders
    @result = Result.new
  end

  def call
    return result if !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send public holiday order email to customer #{customer.id} - ##{holiday.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id, holiday_id: holiday.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :holiday, :handled_orders, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Public holiday order email sent to customer #{customer.id} - ##{holiday.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    if customer.billing_details.present?
      recipients = customer.billing_details.email
      recipients += ", #{customer.user.secondary_email}" if customer.user.present? && customer.user.secondary_email.present?
    else
      recipients = customer.email_recipient
    end
    super(default: recipients.gsub(/\r|\n/, ''))
  end

  def email_subject
    'YORDAR: Upcoming public holiday notice'
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      profile_url: url_helper.customer_profile_url(host: app_host),

      holiday: deep_struct(holiday_data),
      orders: deep_struct(orders_data),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink,
    }
  end

  def holiday_data
    {
      name: holiday.name,
      date: holiday.on_date.to_s(:full_date)
    }
  end

  def orders_data
    handled_orders.map do |order|
      is_skipped = order.status == 'skipped' && order.old_delivery_at.blank?
      status = is_skipped ? 'Skipped' : 'Pushed'
      old_delivery_at = case
        when is_skipped
          order.delivery_at.to_s(:full)
        when order.old_delivery_at.present?
          order.old_delivery_at.to_s(:full)
        else
          '-'
        end
      new_delivery_at = is_skipped ? '-' : order.delivery_at.to_s(:full)
      order_url = order.is_team_order? ? url_helper.team_order_url(order, host: app_host) : url_helper.order_show_url(order, host: app_host)
      {
        id: order.id,
        name: order.name.truncate(25, omission: '...'),
        link: order_url,
        status: status,
        old_delivery_at: old_delivery_at,
        new_delivery_at: new_delivery_at,
      }
    end
  end

  def email_salutation
    super(default: customer.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}"
  end

end

