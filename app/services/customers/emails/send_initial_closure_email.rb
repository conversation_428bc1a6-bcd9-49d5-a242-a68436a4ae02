class Customers::Emails::SendInitialClosureEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-initial_closure'.freeze

  def initialize(customer:, orders:)
    @customer = customer
    @orders = orders
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send Initial Closure email to customer #{customer.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :orders, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: customer.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Initial Closure email sent to customer #{customer.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: Holiday admin closure dates - #{Time.zone.now.year}"
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref
    }
  end

  def email_variables
    yordar_last_day = Time.zone.parse(yordar_credentials(:yordar, :closure_start_date)) - 1.day
    yordar_first_day = Time.zone.parse(yordar_credentials(:yordar, :closure_end_date)) + 1.day
    notification_threshold_day = yordar_last_day - 1.week
    {
      firstname: customer.email_salutation,
      yordar_last_day: yordar_last_day.strftime("%A the #{yordar_last_day.day.ordinalize} of %B %Y"), # 'Friday the 20th of December 2019',
      yordar_new_year_first_day: yordar_first_day.strftime("%A the #{yordar_first_day.day.ordinalize} of %B %Y"), # 'Monday the 6th of January 2020',
      notification_threshold_day: notification_threshold_day.strftime("#{notification_threshold_day.day.ordinalize} of %B %Y"), # '6th of December 2019'

      orders: deep_struct(orders_data),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink,
    }
  end

  def orders_data
    sanitised_orders = []
    recurrent_orders, one_off_orders = orders.partition(&:is_recurrent?)

    recurrent_orders.group_by(&:template_id).each do |template_id, template_orders|
      related_orders = Order.where(template_id: template_id)

      last_delivery_order = related_orders.joins(:supplier_profiles).where('orders.delivery_at < supplier_profiles.close_from').order(:delivery_at).last
      last_delivery_at = last_delivery_order.delivery_at.strftime('%a %d %b %Y<br/>%I:%M %p') if last_delivery_order.present?

      first_delivery_order = related_orders.joins(:supplier_profiles).where('orders.delivery_at > supplier_profiles.close_to').order(:delivery_at).first
      first_delivery_at = first_delivery_order.delivery_at.strftime('%a %d %b %Y<br/>%I:%M %p') if first_delivery_order.present?

      sanitised_orders << {
        name: template_orders.first.name,
        last_delivery_at: last_delivery_at,
        first_delivery_at: first_delivery_at
      }
    end

    one_off_orders.each do |order|
      sanitised_orders << {
        name: order.name,
        last_delivery_at: 'N/A',
        first_delivery_at: 'N/A'
      }
    end
    sanitised_orders
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}"
  end

end
