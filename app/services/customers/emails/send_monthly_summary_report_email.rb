# deprecated.. but kept for legacy purposes to check
class Customers::Emails::SendMonthlySummaryReportEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'customer-monthly_summary_report'.freeze

  def initialize(customer:, start_date:, end_date:, report_csv: nil)
    @notifying_account = @customer = customer
    @template_name = EMAIL_TEMPLATE
    @start_date = start_date
    @end_date = end_date
    @report_csv = report_csv.presence || generate_monthly_report
  end

  def call
    return if !preferred_notification? || report_csv.blank?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send monthly summary report email to customer #{customer.id} - #{end_date}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id, start_date: start_date, end_date: end_date })
    end
  end

private

  attr_reader :customer, :start_date, :end_date, :report_csv

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables,
      attachments: report_attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Monthly summary report sent for #{customer.id} - #{end_date}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: customer.user.email_recipient)
  end

  def email_subject
    "Yordar: #{company_name} Monthly Summary Report"
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref
    }
  end

  def company_name
    @_company_name ||= customer.company&.name || customer.company_name
  end

  def email_variables
    {
      customer_name: email_salutation,
      company_name: company_name,
      account_managers: deep_struct(account_manager_data_for(customer)),
    }
  end

  def report_attachments
    [
      {
        name: "Monthly Summary Report - #{start_date.to_s(:date)} to #{end_date.to_s(:date)}",
        file_url: report_csv,
        is_csv: true
      }
    ]
  end

  def generate_monthly_report
    Customers::CreateMonthlyReport.new(customer: customer, start_date: start_date, end_date: end_date).call
  end

  def email_salutation
    super(default: customer.customer_name)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{end_date}"
  end

end

