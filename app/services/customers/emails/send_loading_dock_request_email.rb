class Customers::Emails::SendLoadingDockRequestEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'customer-loading_dock_request'.freeze

  def initialize(customer:, orders:, date: Date.today)
    @customer = customer
    @orders = orders
    @date = date
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send order loading dock request email to customer #{customer.id} - #{orders.map(&:id).join(',')}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order_ids: orders.map(&:id), customer_id: customer.id })
      result.errors << error_message
    end

    result
  end

private

  attr_reader :customer, :orders, :date, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Loading Dock request email sent to customer #{customer.id} - #{orders.map(&:id).join(',')}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    customer.email_recipient
  end

  def email_subject
    "YORDAR: Loading Dock Info Request orders #{orders.map(&:id).join(', ')}"
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: customer.email_salutation,
      profile_url: url_helper.customer_profile_url(host: app_host),
      account_managers: deep_struct(account_manager_data_for(customer)),

      orders: deep_struct(orders_data),

      header_color: :pink,
    }
  end

  def orders_data
    orders.map do |order|
      {
        id: order.id,
        name: order.name,
        link: order_view_url_for(order),
        loading_dock_link: loading_dock_request_url_for(order),
        delivery_address: order.delivery_address_arr.join('<br />'),
        delivery_time: order.delivery_at.to_s(:full),
        remaining_days: (order.delivery_at.to_date - date).to_i,
        total: number_to_currency(order.customer_total, precission: 2),
        suppliers: suppliers_for(order),
      }
    end
  end

  def order_view_url_for(order)
    order.is_team_order? ? url_helper.team_order_url(order, host: app_host) : url_helper.order_show_url(order, host: app_host)
  end

  def loading_dock_request_url_for(order)
    request_uuid = Digest::MD5.hexdigest(order.id.to_s + customer.id.to_s + yordar_credentials(:random_salt))
    url_helper.loading_dock_request_url(uuid: order.uuid, request_uuid: request_uuid, host: next_app_host)
  end

  def suppliers_for(order)
    order.supplier_profiles.map do |supplier|
      {
        name: supplier.name,
        image: cloudinary_image(supplier.profile.avatar),
      }
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}-#{date.to_s(:date_spreadsheet)}"
  end

end
