class Customers::Emails::SendOrderConfirmationEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper
  include MenuItemHelper

  EMAIL_TEMPLATE = 'customer-order_confirmed'.freeze

  def initialize(customer:, order:)
    @notifying_account = @customer = customer
    @template_name = EMAIL_TEMPLATE
    @order = order
    @result = Result.new
  end

  def call
    return result if !can_send_email? || !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send order confirmation email to customer #{customer.id} - ##{order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order_id: order.id, customer_id: customer.id })
      result.errors << error_message
    end

    result
  end

private

  attr_reader :customer, :order, :result

  def can_send_email?
    if Email.where(email_options).present?
      result.errors << 'Email already sent!'
    end

    result.errors.blank?
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Order confirmation email sent to customer #{customer.id} - ##{order.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: customer.email_recipient)
  end

  def email_subject
    "YORDAR: Your order ##{order.id} was confirmed"
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: email_salutation,
      profile_url: url_helper.customer_profile_url(host: app_host),

      order: deep_struct(orders_data),
      supplier_grouped_order_lines: deep_struct(supplier_grouped_order_lines),
      totals: deep_struct(totals),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink,
    }
  end

  def orders_data
    order_url = order.is_team_order? ? url_helper.team_order_url(order, host: app_host) : url_helper.order_show_url(order, host: app_host)
    order_type = order.is_recurrent? ? 'recurring' : 'one off'
    order_day = order.template_delivery_at.blank? ? '' : order.template_delivery_at.to_s(:weekday)
    {
      id: order.id,
      name: order.name,
      link: order_url,
      delivery_address: order.delivery_address_arr.join(', '),
      type: order_type,
      day: order_day,
      recurrent_type: order.recurrent_type,
      date: order.delivery_at.to_s(:full),
    }
  end

  def supplier_grouped_order_lines
    order_lines.group_by(&:supplier_profile).map do |supplier, supplier_order_lines|
      hashed_order_lines = supplier_order_lines.first(5).map do |order_line|
        menu_item = order_line.menu_item
        {
          name: order_line.name.strip.truncate(50, omission: ' ...'),
          quantity: order_line.quantity,
          price: number_to_currency(order_line.price),
          image: cloudinary_image(menu_item.image),
          description: menu_item.description,
          dietary_preferences: dietary_preferences_for(menu_item: menu_item)
        }
      end
      {
        supplier: {
          name: supplier.company_name.strip,
          image: cloudinary_image(supplier.profile.avatar),
        },
        has_more: supplier_order_lines.size > 5,
        order_lines: hashed_order_lines
      }
    end
  end

  def totals
    {
      customer_subtotal: number_to_currency(order.customer_subtotal),
      order_discount: sanitized_currency_for(order.discount),
      customer_delivery: number_to_currency(order.customer_delivery),
      customer_gst: number_to_currency(order.customer_gst),
      customer_surcharge: sanitized_currency_for(order.customer_surcharge),
      customer_topup: sanitized_currency_for(order.customer_topup),
      customer_total: number_to_currency(order.customer_total),
    }
  end

  def dietary_preferences_for(menu_item:)
    dietary_preferences(menu_item: menu_item).map do |field, preference|
      [preference[:letter], field]
    end
  end

  def order_lines
    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    OrderLines::List.new(options: lister_options, includes: %i[supplier_profile menu_item]).call
  end

  def email_salutation
    super(default: customer.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}-#{order.version_ref}"
  end

end

