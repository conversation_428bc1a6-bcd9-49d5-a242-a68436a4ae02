class Customers::Emails::SendNewOrderEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'customer-new_order'.freeze

  def initialize(customer:, order:)
    @notifying_account = @customer = customer
    @template_name = EMAIL_TEMPLATE
    @order = order
  end

  def call
    return if !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send new order email to customer #{customer&.id} - ##{order&.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order_id: order&.id, customer_id: customer&.id })
    end
  end

private

  attr_reader :customer, :order

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "New order email sent to customer #{customer.id} - ##{order.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: customer.email_recipient)
  end

  def email_subject
    subject = "YORDAR: Your new order ##{order.id} was received"
    subject += ' - Under Supplier Minimum(s)' if order_spends.is_under?
    subject
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      profile_url: url_helper.customer_profile_url(host: app_host),

      order: deep_struct(orders_data),
      supplier_grouped_order_lines: deep_struct(supplier_grouped_order_lines),
      totals: deep_struct(totals),
      order_spend: deep_struct(order_spend_data),

      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink,
    }
  end

  def orders_data
    order_type = order.is_recurrent? ? 'recurring' : 'one off'
    order_day = order.template_delivery_at.blank? ? '' : order.template_delivery_at.to_s(:weekday)
    {
      id: order.id,
      name: order.name,
      link: url_helper.order_show_url(order, host: app_host),
      delivery_address: order.delivery_address_arr.join(', '),
      type: order_type,
      day: order_day,
      recurrent_type: order.recurrent_type,
      date: order.delivery_at.to_s(:full),
      loading_dock_url: loading_dock_url
    }
  end

  def supplier_grouped_order_lines
    order_lines.group_by(&:supplier_profile).map do |supplier, supplier_order_lines|
      hashed_order_lines = supplier_order_lines.first(5).map do |order_line|
        {
          name: order_line.name.truncate(50, omission: ' ...'),
          quantity: order_line.quantity,
          price: number_to_currency(order_line.price),
          image: cloudinary_image(order_line.menu_item.image)
        }
      end
      {
        supplier: {
          name: supplier.company_name,
          image: cloudinary_image(supplier.profile.avatar),
        },
        has_more: supplier_order_lines.size > 5,
        order_lines: hashed_order_lines
      }
    end
  end

  def totals
    {
      customer_subtotal: number_to_currency(order.customer_subtotal),
      order_discount: sanitized_currency_for(order.discount),
      customer_delivery: number_to_currency(order.customer_delivery),
      customer_gst: number_to_currency(order.customer_gst),
      customer_surcharge: sanitized_currency_for(order.customer_surcharge),
      customer_topup: sanitized_currency_for(order.customer_topup),
      customer_total: number_to_currency(order.customer_total),
    }
  end

  def order_spend_data
    if order_spends.is_under?
      {
        is_under: true,
        total_spend: number_to_currency(order_spends.total_spend),
        minimum_spend: number_to_currency(order_spends.minimum_spend),
        remaining_spend: number_to_currency(order_spends.remaining_spend),
        supplier_spends: spend_data_from(order_spends.supplier_spends)
      }
    else
      {
        is_under: false
      }
    end
  end

  def spend_data_from(supplier_spends)
    supplier_spends.select(&:is_under?).map do |supplier_spend|
      {
        supplier_name: supplier_spend.supplier.name,
        total_spend: number_to_currency(supplier_spend.total_spend),
        minimum_spend: number_to_currency(supplier_spend.minimum_spend),
        remaining_spend: number_to_currency(supplier_spend.remaining_spend),
        is_under: supplier_spend.is_under?,
      }
    end
  end

  def order_spends
    @_order_spends ||= Orders::GetSupplierSpends.new(order: order, exclude_surcharge: true).call
  end

  def order_lines
    return @_order_lines if !@_order_lines.nil?

    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    @_order_lines = OrderLines::List.new(options: lister_options, includes: %i[supplier_profile menu_item]).call
  end

  def loading_dock_url
    return nil if order.delivery_type != 'loading_dock' || order.loading_dock.present?

    request_uuid = Digest::MD5.hexdigest(order.id.to_s + customer.id.to_s + yordar_credentials(:random_salt))
    url_helper.loading_dock_request_url(uuid: order.uuid, request_uuid: request_uuid, host: next_app_host)
  end

  def email_salutation
    super(default: customer.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}-#{order.version_ref}"
  end

end
