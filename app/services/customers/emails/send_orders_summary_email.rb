class Customers::Emails::SendOrdersSummaryEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'customer-order_summary'.freeze

  def initialize(customer:, summary_day:, customer_orders:)
    @notifying_account = @customer = customer
    @summary_day = summary_day
    @template_name = EMAIL_TEMPLATE
    @customer_orders = customer_orders
    @attachments = []
    @result = Result.new
  end

  def call
    return result if email_already_sent? || !preferred_notification?

    begin
      generate_document
      send_email
    rescue => exception
      error_message = "Failed to send orders summary email to customer #{customer.id} - #{summary_day}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id, summary_day: summary_day })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :summary_day, :customer_orders, :attachments, :result

  def email_already_sent?
    Email.where(fk_id: customer.id, ref: email_ref).present?
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Sent orders summary email to customer #{customer.id} - #{summary_day}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    if customer.billing_details.present?
      recipients = customer.billing_details.email
      recipients += ", #{customer.user.secondary_email}" if customer.user.present? && customer.user.secondary_email.present?
    else
      recipients = customer.email_recipient
    end
    super(default: recipients.gsub(/\r|\n/, ''))
  end

  def email_salutation
    super(default: customer.email_salutation)
  end

  def email_subject
    'YORDAR: Your daily order summary'
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      company_name: customer.customer_or_company_name,
      summary_day: summary_day.to_s(:weekday),

      summary_pdf_url: @summary_document&.url,
      profile_url: url_helper.customer_profile_url(host: app_host),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink
    }
  end

  def generate_document
    @summary_document = Documents::Generate::CustomerOrderSummary.new(customer: customer, orders: customer_orders, summary_day: summary_day).call
    if @summary_document.present?
      attachments << @summary_document
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}-#{summary_day.to_s(:date_compact)}"
  end

end
