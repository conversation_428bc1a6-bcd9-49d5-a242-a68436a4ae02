class Customers::Emails::SendCustomerInviteEmail < Notifications::Base
  include Rails.application.routes.url_helpers

  EMAIL_TEMPLATE = 'customer-company_customer_invitation'.freeze

  def initialize(customer:, invite_params: {})
    @customer = customer
    @invite_params = invite_params
    @result = Result.new
  end

  def call
    return result if invite_already_sent?

    begin
      send_email
    rescue => exception
      error_message = 'Failed to send customer invite email.'
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id, invite_params: invite_params })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :invite_params, :result

  def invite_already_sent?
    if (existing_email = Email.where(email_options).presence)
      result.errors << 'Invite already sent!'
    end
    existing_email.present?
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: invite_params[:email],
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Customer invite email sent to #{invite_params[:first_name]} #{invite_params[:last_name]}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: You\'ve been invited to register under #{company_name}"
  end

  def email_cc
    cc_emails = yordar_credentials(:yordar, :orders_email)
    cc_emails += ";#{customer.email}"
    cc_emails
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: invite_params[:first_name],
      admin: deep_struct(admin_data),
      invite_url: invitation_link,

      header_color: :pink
    }
  end

  def invitation_link
    new_company_customer_registration_url(
      company_team_admin_code: customer.uuid,
      host: app_host,
      firstname: invite_params[:first_name],
      lastname: invite_params[:last_name],
      email: invite_params[:email]
    )
  end

  def admin_data
    {
      name: customer.name,
      company_name: company_name,
    }
  end

  def company_name
    customer.company&.name || customer.company_name || customer.name
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}-#{invite_params[:email]}"
  end

end

