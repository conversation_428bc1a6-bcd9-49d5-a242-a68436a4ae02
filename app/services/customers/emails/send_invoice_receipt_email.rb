class Customers::Emails::SendInvoiceReceiptEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'customer-invoice_tax_receipt'.freeze

  def initialize(customer:, invoice:)
    @notifying_account = @customer = customer
    @template_name = EMAIL_TEMPLATE
    @invoice = invoice
    @attachments = []
    @result = Result.new
  end

  def call
    return result if !preferred_notification?

    begin
      generate_document
      send_email
    rescue => exception
      error_message = "Failed to send invoice tax receipt to customer #{customer.id} - ##{invoice.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { invoice_id: invoice.id, customer_id: customer.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :invoice, :attachments, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Invoice tax receipt sent to customer #{customer.id} - ##{invoice.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    if customer.billing_details.present?
      recipients = customer.billing_details.email
      recipients += ", #{customer.user.secondary_email}" if customer.user.present? && customer.user.secondary_email.present?
    else
      recipients = customer.email_recipient
    end
    super(default: recipients.gsub(/\r|\n/, ''))
  end

  def email_subject
    "YORDAR: Your tax receipt - Invoice ##{invoice.number}"
  end

  def email_cc
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      invoice: deep_struct(invoice_data),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink
    }
  end

  def invoice_data
    {
      id: invoice.id,
      number: invoice.number,
      pdf_url: @invoice_document.url,
    }
  end

  def generate_document
    generate_order_documents

    @invoice_document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: 'tax_invoice_receipt').call
    if @invoice_document.present?
      attachments << @invoice_document
    else
      raise RuntimeError.new(message: 'ERRORED') # to trigger rescue code
    end
  end

  def generate_order_documents
    invoice_orders.each do |order|
      next if order.documents.where(kind: 'customer_order_details').present?

      Documents::Generate::CustomerOrderDetails.new(order: order).call
    end
  end

  def invoice_orders
    @_invoice_orders ||= invoice.invoice_orders
  end

  def email_salutation
    super(default: customer.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{invoice.id}"
  end

end

