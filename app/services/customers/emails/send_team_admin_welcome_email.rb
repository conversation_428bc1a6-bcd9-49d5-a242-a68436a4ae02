class Customers::Emails::SendTeamAdminWelcomeEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-team_admin_welcome'.freeze

  def initialize(customer:)
    @customer = customer
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send team admin welcome email to customer #{customer.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id })
    end
  end

private

  attr_reader :customer

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: customer.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent team admin welcome email to customer #{customer.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Welcome to Yordar Team Ordering!'
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: customer.email_salutation,
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :purple
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}"
  end

end
