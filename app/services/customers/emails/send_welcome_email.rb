class Customers::Emails::SendWelcomeEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-welcome'.freeze

  def initialize(customer:)
    @customer = customer
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send welcome email to customer #{customer.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id })
    end
  end

private

  attr_reader :customer

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: customer.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Welcome email sent to customer #{customer.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Welcome to Yordar'
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: customer.email_salutation,
      confirmation_url: confirmation_url,

      header_color: :pink,
    }
  end

  def confirmation_url
    Rails.application.routes.url_helpers.user_confirmation_url(confirmation_token: customer.user.confirmation_token, host: app_host)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}"
  end

end

