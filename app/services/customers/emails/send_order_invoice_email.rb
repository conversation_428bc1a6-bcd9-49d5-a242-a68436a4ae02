class Customers::Emails::SendOrderInvoiceEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'customer-order_invoice'.freeze

  def initialize(customer:, invoice:, documents: [], is_regenerate: false)
    @notifying_account = @customer = customer
    @template_name = EMAIL_TEMPLATE
    @invoice = invoice
    @attachments = documents.presence || invoice.documents
    @is_regenerate = is_regenerate
    @result = Result.new
  end

  def call
    return result if !can_send_email? || !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send new order invoice email to customer #{customer.id} - ##{invoice.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { invoice_id: invoice.id, customer_id: customer.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :invoice, :attachments, :is_regenerate, :result

  def can_send_email?
    case
    when is_regenerate
      # do nothing
    when Email.where(email_options).present?
      result.errors << "Order Invoice email already sent for #{customer.id} - ##{invoice.id}"
    end
    result.errors.blank?
  end

  def invoice_orders
    @_invoice_orders ||= invoice.invoice_orders
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "New order invoice email sent to customer #{customer.id} - ##{invoice.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    # Replacing special character from self.billing_details.email and self.email_recipient.
    if customer.billing_details.present?
      recipients = customer.billing_details.email
      recipients += ", #{customer.user.secondary_email}" if customer.user.present? && customer.user.secondary_email.present?
    else
      recipients = customer.email_recipient
    end
    super(default: recipients.gsub(/\r|\n/, ''))
  end

  def email_subject
    "YORDAR: Your invoice - ##{invoice.number}"
  end

  def email_cc
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: email_salutation,

      invoice: deep_struct(invoice_data),
      account_managers: deep_struct(account_manager_data_for(customer)),
      accounting_software: customer.invoice_accounting_software,

      header_color: :pink,
    }
  end

  def invoice_data
    invoice_on_credit_card = invoice_orders.count == 1 && !invoice_orders.first.credit_card.pay_on_account?
    {
      number: invoice.number,
      paid_by_credit_card: invoice_on_credit_card,
      pdf_url: tax_invoice_pdf&.url,
      payment_url: url_helper.pay_invoice_url(invoice, host: app_host),
      with_invoice_spreadsheet: invoice_orders.count > 1 && customer.needs_invoice_spreadsheet
    }
  end

  def tax_invoice_pdf
    return nil if attachments.blank?

    attachments.detect{|document| document.kind == 'tax_invoice' }
  end

  def email_salutation
    super(default: customer.email_salutation)
  end

  def email_ref
    "customer-invoice-#{invoice.id}"
  end

end

