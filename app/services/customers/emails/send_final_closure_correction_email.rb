class Customers::Emails::SendFinalClosureCorrectionEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-final_closure_correction'.freeze

  def initialize(customer:)
    @customer = customer
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send Final Closure Correction email to customer #{customer.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { customer_id: customer.id })
    end
  end

private

  attr_reader :customer

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: customer.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Final Closure Correction email sent to customer #{customer.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Yordar - Important Update'
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref
    }
  end

  def email_variables
    {
      firstname: customer.email_salutation,
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}"
  end

end
