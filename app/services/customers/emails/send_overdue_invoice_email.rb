class Customers::Emails::SendOverdueInvoiceEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'customer-overdue_invoice'.freeze

  def initialize(customer:, invoices:, kind:)
    @customer = customer
    @invoices = invoices
    @kind = kind
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send Overdue Invoice email to customer #{customer.id} - #{kind}"
      log_errors(exception: exception, message: error_message, sentry: false, error_objects: { customer_id: customer.id, kind: kind, invoice_ids: invoices.map(&:id) })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :invoices, :kind, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      sender: sender_email_address,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "#{kind} Overdue Invoice email sent to customer #{customer.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    recipients = []
    if customer.billing_details.present?
      recipients << customer.billing_details.email
      if kind == :final
        recipients << customer.email_recipient
        recipients << customer.user.secondary_email if customer.user.present? && customer.user.secondary_email.present?
      end
    else
      recipients << customer.email_recipient
      recipients << customer.user.secondary_email if kind == :final && customer.user.present? && customer.user.secondary_email.present?
    end
    recipients.map{|email| email.gsub(/\n|\r/, '') }.uniq.join(', ')
  end

  def email_subject
    case kind
    when :second
      'Yordar - Overdue Invoices - Second Reminder'
    when :third
      'Yordar - Overdue Invoices - Third Reminder'
    when :final
      'Yordar - Overdue Invoices - Final Notice'
    else # :heads_up
      'Yordar - You have Overdue Invoices'
    end
  end

  def sender_email_address
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref
    }
  end

  def email_variables
    {
      company_name: customer_company_name,
      kind: kind,
      due_at: invoices.sample.due_at.to_s(:date),
      invoices: deep_struct(invoices_data),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink,
    }
  end

  def invoices_data
    invoices.map do |invoice|
      totals = Invoices::CalculateTotals.new(invoice: invoice).call
      latest_document = invoice.latest_document(kind: 'tax_invoice')
      {
        id: invoice.id,
        number: invoice.number,
        total: number_to_currency(totals.total),
        pdf_url: latest_document&.url,
        payment_url: url_helper.pay_invoice_url(invoice, host: app_host),
      }
    end
  end

  def customer_company_name
    company_name = customer.company.present? ? customer.company.name : customer.company_name
    company_name = nil if company_name.length > 25
    company_name
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}-#{kind}"
  end

end
