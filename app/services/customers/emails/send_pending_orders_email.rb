class Customers::Emails::SendPendingOrdersEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'customer-pending_orders'.freeze

  def initialize(customer:, pending_orders:, delivery_on:)
    @customer = customer
    @pending_orders = pending_orders
    @delivery_on = delivery_on
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send pending orders email to customer: #{customer.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { pending_orders: pending_orders.map(&:id) })
    end
  end

private

  attr_reader :customer, :pending_orders, :delivery_on

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent pending orders email to customer: #{customer.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: Pending Quote Order(s) Reminder'
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: 1,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: customer.email_salutation,
      delivery_date: delivery_on.to_s(:full_date),
      pending_orders: deep_struct(pending_orders_data),
      profile_url: url_helper.customer_profile_url(host: app_host),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink
    }
  end

  def pending_orders_data
    pending_orders.map do |order|
      {
        id: order.id,
        total: number_to_currency(order.customer_total, precission: 2),
        link: url_helper.order_show_url(order, host: app_host),
        delivery_time: (order.delivery_at.present? ? order.delivery_at.to_s(:time_only) : 'unknown'),
        suppliers: suppliers_for(order),
      }
    end
  end

  def suppliers_for(order)
    order.supplier_profiles.map do |supplier|
      {
        name: supplier.name,
        image: cloudinary_image(supplier.profile.avatar),
      }
    end
  end

  def email_recipients
    customer.email_recipient
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{Time.zone.now.to_s(:date_spreadsheet)}"
  end

end
