class Customers::Emails::SendOrderReviewEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'customer-order_review_invitation'.freeze
  ORDER_LINES_THRESHOLD = 3

  def initialize(customer:, order:)
    @notifying_account = @customer = customer
    @template_name = EMAIL_TEMPLATE
    @order = order
    @result = Result.new
  end

  def call
    if !preferred_notification?
      result.errors << "The customer ##{customer.id} does not prefer getting order review emails"
      return result
    end
    begin
      send_email
    rescue => exception
      error_message = "Failed to send order review email to customer #{customer.id} - ##{order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order_id: order.id, customer_id: customer.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :order, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Order review email sent to customer #{customer.id} - ##{order.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: customer.email_recipient)
  end

  def email_subject
    "Yordar: About your recent order - ##{order.id}"
  end

  def email_options
    {
      fk_id: order.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      order: deep_struct(order_data),
      suppliers: deep_struct(suppliers_data),
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink
    }
  end

  def order_data
    order_url = order.is_team_order? ? url_helper.team_order_url(order, host: app_host) : url_helper.order_show_url(order, host: app_host)
    {
      id: order.id,
      name: order.name.strip,
      link: order_url,
      created_on: order.created_at.to_s(:date_verbose),
      delivery_on: order.delivery_at.to_s(:date_verbose),
    }
  end

  def suppliers_data
    order.supplier_profiles.map do |supplier|
      order_lines_data = order_lines_data_for(supplier)
      next if order_lines_data.blank?

      {
        name: supplier.name,
        image: cloudinary_image(supplier.profile.avatar),
        order_lines: order_lines_data.first(ORDER_LINES_THRESHOLD),
        has_more: order_lines_data.size > ORDER_LINES_THRESHOLD,
        review_url: review_link_for(supplier)
      }
    end
  end

  def order_lines_data_for(supplier)
    lister_options = {
      order: order,
      supplier: supplier,
      confirmed_attendees_only: order.is_team_order?,
    }
    order_lines = OrderLines::List.new(options: lister_options).call
    order_lines.map do |order_line|
      {
        name: order_line.name,
        quantity: order_line.quantity
      }
    end
  end

  def review_link_for(supplier)
    url_helper.new_order_review_url(order_id: order.id, supplier_profile_id: supplier.id, host: app_host)
  end

  def email_salutation
    super(default: customer.email_salutation)
  end

  def email_ref
    EMAIL_TEMPLATE
  end

end

