class Customers::Emails::SendEventOrderQuoteEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-event_order_quote'.freeze

  def initialize(customer:, order:, quote_emails: '', quote_message: '')
    @customer = customer
    @order = order
    @quote_emails = quote_emails
    @quote_message = quote_message
    @attachments = []
    @result = Result.new
  end

  def call
    return result if quote_emails.blank?

    begin
      generate_document
      send_email
    rescue => exception
      error_message = "Failed to send event order quote email to customer #{customer.id} - ##{order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order_id: order.id, customer_id: customer.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :customer, :order, :quote_emails, :quote_message, :attachments, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: quote_emails,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Event order quote email sent to quoted emails ##{order.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: Quote for your event order'
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: customer.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: customer.email_salutation,
      order: deep_struct(order_data),
      quote_pdf_url: @details_document&.url,
      quote_message: quote_message,
      account_managers: deep_struct(account_manager_data_for(customer)),

      header_color: :pink
    }
  end

  def generate_document
    @details_document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: email_ref, variation: 'quote').call
    if @details_document.present?
      attachments << @details_document
    end
  end

  def order_data
    {
      name: order.name,
      type: order.is_recurrent? ? 'recurring' : 'one off'
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{customer.id}-#{order.version_ref}"
  end

end

