class Customers::Emails::SendCustomerQuoteEmail < Notifications::Base

  EMAIL_TEMPLATE = 'customer-customer_quote'.freeze

  def initialize(quote:)
    @quote = quote
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send event customer quote email to Customer #{quote.kind} - ##{quote.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { quote_id: quote.id, customer_id: quote_customer&.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :quote, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Customer Quote Submission email sent to Yordar admin #{quote.id} - ##{quote.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipient
    quote.form_data['email']
  end

  def email_subject
    "YORDAR: Thank you for submitting a #{quote.kind.humanize} quote"
  end

  def email_options
    {
      fk_id: quote.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      customer_name: quote.form_data['fullName'],
      quote: deep_struct(quote_data),
      form_data: quote.form_data.except('type'),

      header_color: header_color
    }
  end

  def quote_data
    {
      kind: quote.kind,
    }
  end

  def quote_customer
    @_quote_customer ||= quote.customer_profile
  end

  def header_color
    case quote.kind
    when 'event' then :pink
    when 'snacks' then :yellow
    when 'catering' then :purple
    else :pink
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{quote.id}"
  end

end

