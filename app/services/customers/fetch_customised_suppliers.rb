class Customers::FetchCustomisedSuppliers

  def initialize(customer:, scoped_supplier_ids: [])
    @customer = customer
    @scoped_supplier_ids = scoped_supplier_ids.present? ? scoped_supplier_ids.map(&:to_i) : []
    @result = Result.new
  end

  def call
    if can_fetch?
      fetch_favourites
      if customer.company_id.present?
        fetch_rate_cards
        fetch_custom_menu_suppliers
      end
    end
    result
  end

private

  attr_reader :customer, :scoped_supplier_ids, :result

  def can_fetch?
    customer.present? && customer.is_a?(CustomerProfile)
  end

  def fetch_favourites
    supplier_ids = customer.favourite_supplier_ids
    supplier_ids = (supplier_ids & scoped_supplier_ids) if scoped_supplier_ids.present?
    result.favourite_supplier_ids = supplier_ids.uniq
  end

  def fetch_rate_cards
    menu_items_with_rate_cards = MenuItem.where(archived_at: nil).joins(:rate_cards)
    menu_items_with_rate_cards = menu_items_with_rate_cards.where(rate_cards: { company_id: customer.company_id })
    menu_items_with_rate_cards = menu_items_with_rate_cards.where(supplier_profile_id: scoped_supplier_ids) if scoped_supplier_ids.present?
    result.rate_card_supplier_ids = menu_items_with_rate_cards.select(:supplier_profile_id).distinct.map(&:supplier_profile_id)
  end

  def fetch_custom_menu_suppliers
    restricted_menu_sections = MenuSection.where(archived_at: nil).joins(:companies)
    restricted_menu_sections = restricted_menu_sections.where(companies: { id: customer.company_id })
    restricted_menu_sections = restricted_menu_sections.where(supplier_profile_id: scoped_supplier_ids) if scoped_supplier_ids.present?
    result.custom_menu_supplier_ids = restricted_menu_sections.select(:supplier_profile_id).distinct.map(&:supplier_profile_id)
  end

  class Result
    attr_accessor :favourite_supplier_ids, :rate_card_supplier_ids, :custom_menu_supplier_ids

    def initialize
      @favourite_supplier_ids = []
      @rate_card_supplier_ids = []
      @custom_menu_supplier_ids = []
    end
  end

end
