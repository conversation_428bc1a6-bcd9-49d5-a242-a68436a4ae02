class Customers::ListOrderSupplierIds

  def initialize(customer:, show_past: false, save_cache: false)
    @customer = customer
    @show_past = show_past
    @save_cache = save_cache
  end

  def call
    supplier_ids = order_supplier_ids.flatten.map(&:id).uniq
    save_ids_in_cache(supplier_ids) if save_cache
    supplier_ids
  end

private

  attr_reader :customer, :show_past, :save_cache

  def order_supplier_ids
    orders.map do |order|
      order.supplier_profiles.select(:id) + order.team_supplier_profiles.select(:id)
    end
  end

  def orders
    lister_options = {
      for_customer: customer,
      for_duration: show_past ? '3-months' : 'all',
      show_past: show_past,
    }
    Orders::List.new(options: lister_options, includes: %i[team_supplier_profiles supplier_profiles]).call
  end

  def save_ids_in_cache(supplier_ids)
    key_name = show_past ? 'past-supplier-ids' : 'upcoming-supplier-ids'
    cache_key = [customer.cache_key, key_name]
    # puts cache_key
    Rails.cache.write(cache_key, supplier_ids)
  end

end
