class Customers::ManageDeliveryOverrides

  def initialize(customer:, delivery_overrides: [])
    @customer = customer
    @delivery_overrides = delivery_overrides
    @result = Result.new(customer: customer)
  end

  def call
    if can_manage?
      delivery_overrides.each do |delivery_override|
        case
        when delivery_override[:id].present? && delivery_override[:_delete].present?
          remove_override_for(delivery_override: delivery_override)
        when delivery_override[:id].present?
          update_override_for(delivery_override: delivery_override)
        else
          create_override_for(delivery_override: delivery_override)
        end
      end
    end
    result
  end

private

  attr_reader :customer, :delivery_overrides, :result

  def can_manage?
    case
    when customer.blank?
      result.errors << 'Cannot manage without a customer'
    end
    result.errors.blank?
  end

  def remove_override_for(delivery_override:)
    override = customer.delivery_overrides.where(id: delivery_override[:id]).first
    if !override.destroy
      result.errors << "Could not remove delivery_override with ID: #{delivery_override[:id]}" # " - #{override.errors.full_messages.join(', ')}"
    end
  end

  def update_override_for(delivery_override:)
    override = customer.delivery_overrides.where(id: delivery_override[:id]).first
    if override.update(delivery_override.except(:id))
      result.delivery_overrides << override
    else
      result.errors << "Could not update delivery_override with ID: #{delivery_override[:id]}" # " - #{override.errors.full_messages.join(', ')}"
    end
  end

  def create_override_for(delivery_override:)
    override = customer.delivery_overrides.where(supplier_kind: delivery_override[:supplier_kind], supplier_profile_id: delivery_override[:supplier_profile_id]).first_or_initialize
    if override.update(delivery_override.except(:id))
      result.delivery_overrides << override
    else
      result.errors << "Could not create delivery_override with kind: #{delivery_override[:supplier_kind]}" # " - #{override.errors.full_messages.join(', ')}"
    end
  end

  class Result
    attr_accessor :delivery_overrides, :errors
    attr_reader :customer

    def initialize(customer:)
      @customer = customer
      @delivery_overrides = []
      @errors = []
    end

    def success?
      @errors.blank?
    end
  end

end