class Customers::FetchPurchaseOrder

  def initialize(customer:, cpo_id:)
    @customer = customer
    @cpo_id = cpo_id
  end

  def call
    if customer.blank? || cpo_id.blank?
      nil
    else
      customer_purchase_order
    end
  end

private

  attr_reader :customer, :cpo_id

  def customer_purchase_order
    purchase_order = customer.customer_purchase_orders.where(id: cpo_id).first rescue nil
    purchase_order = customer.customer_purchase_orders.where(customer_profile: customer, po_number: cpo_id).first_or_create if purchase_order.blank?
    purchase_order.update(active: true) if !purchase_order.active? # activates fetched purchase order if in-active
    purchase_order
  end

end
