class Customers::Register

  def initialize(user_params:, registration_params: {}, company_team_admin: nil, adminable_customer: nil)
    @user_params = user_params.present? ? user_params.to_h.symbolize_keys : {}
    @registration_params = registration_params.present? ? registration_params.to_h.symbolize_keys : {}
    @company_team_admin = company_team_admin
    @adminable_customer = adminable_customer
    @result = Result.new
  end

  def call
    if can_register?
      CustomerProfile.transaction do
        result.user = build_user
        user.just_registered = true
        if user.save!
          result.customer = create_customer
        else
          result.errors << 'We had some issues registering you. Please try again!'
        end
      rescue ActiveRecord::RecordInvalid
        result.errors += user.errors.full_messages if user.present?
        result.errors += customer.errors.full_messages if customer.present?
        raise ActiveRecord::Rollback
      end
    end
    result
  end

private

  attr_reader :user_params, :registration_params, :company_team_admin, :adminable_customer, :user, :customer, :result

  def can_register?
    case
    when %i[email password].any?{|field| user_params[field].blank? }
      result.errors << 'Cannot register without a valid email and password'
    when User.where(email: user_params[:email]).present?
      result.errors << 'An account already exists with these details'
    end
    result.errors.blank?
  end

  def build_user
    @user = User.new(sanitzed_user_params)
  end

  def sanitzed_user_params
    params = user_params.transform_values do |value|
      value.is_a?(String) ? value.strip : value
    end
    params[:business] = 'yr'
    params
  end

  def create_customer
    @customer = CustomerProfile.new(sanitized_customer_params)
    if customer.save!
      profile = user.build_profile(profileable: customer)
      if profile.save!
        register_under_company_team_admin if company_team_admin.present?
        register_as_company_team_admin if adminable_customer.present?
        sync_with_hubspot
        send_welcome_email
        log_event
      end
      create_customer_flags
    end
    customer
  end

  def register_under_company_team_admin
    return if company_team_admin.blank? || !company_team_admin.company_team_admin?

    company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: customer, company_team_admin: company_team_admin).call
    if !company_registration.success?
      message = 'Failed to register customer under company team admin'
      Raven.capture_exception(RuntimeError.new(message),
        message: message,
        extra: { customer_id: customer.id, company_team_admin_id: company_team_admin.id, errors: company_registration.errors },
        transaction: 'Customers::Register'
      )
    end
  end

  def register_as_company_team_admin
    return if adminable_customer.blank?

    company_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: customer, customer: adminable_customer).call
    if !company_registration.success?
      message = 'Failed to register customer as a company team admin'
      Raven.capture_exception(RuntimeError.new(message),
        message: message,
        extra: { admin_id: customer.id, adminable_customer_id: adminable_customer.id, errors: company_registration.errors },
        transaction: 'Customers::Register'
      )
    end
  end

  def create_customer_flags
    customer.create_customer_flags
  end

  def sanitized_customer_params
    [
      default_customer_params,
      sanitzed_registration_params
    ].inject(&:merge)
  end

  def default_customer_params
    {
      uuid: SecureRandom.uuid,
      customer_name: user.name,
    }
  end

  def sanitzed_registration_params
    registration_params.transform_values do |value|
      value.is_a?(String) ? value.strip : value
    end
  end

  def sync_with_hubspot
    Hubspot::SyncContact.new(contact: user.reload, refresh: true).delay.call
  end

  def send_welcome_email
    return if yordar_credentials(:disable, :welcome_email).present?

    Customers::Emails::SendWelcomeEmail.new(customer: customer.reload).delay(queue: :notifications).call
  end

  def log_event
    EventLogs::Create.new(event_object: customer, event: 'new-customer-registration', role: customer.role).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :user, :customer, :errors

    def initialize
      @user = nil
      @customer = nil
      @errors = []
    end

    def success?
      errors.blank? && user.present? && customer.present? && customer.user == user
    end
  end

end
