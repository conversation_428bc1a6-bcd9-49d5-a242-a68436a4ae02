class Customers::Notifications::SendClosureEmails

  def initialize(version:, closure_orders: [])
    @version = version
    @closure_orders = closure_orders.presence || fetch_closure_orders
    @result = Result.new
  end

  def call
    closure_orders.group_by(&:customer_profile).each do |customer, customer_orders|
      email_sender = case version
      when 'initial'
        Customers::Emails::SendInitialClosureEmail.new(customer: customer, orders: customer_orders).call
      else # 'final'
        Customers::Emails::SendFinalClosureEmail.new(customer: customer, orders: customer_orders).call
      end
      if email_sender.success?
        result.sent_notifications << email_sender.sent_notification
      else
        result.errors += email_sender.errors
      end
    end
    result
  end

private

  attr_reader :version, :closure_orders, :result

  def fetch_closure_orders
    Orders::ListSupplierClosureOrders.new(includes: [:customer_profile]).call
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
