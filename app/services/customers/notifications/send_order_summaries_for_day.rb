class Customers::Notifications::SendOrderSummariesForDay

  def initialize(summary_day: Time.zone.now)
    @summary_day = summary_day
    @result = Result.new
  end

  def call
    delivery_day_orders.group_by(&:customer_profile).each do |customer, customer_orders|
      email_sender = Customers::Emails::SendOrdersSummaryEmail.new(customer: customer, summary_day: summary_day, customer_orders: customer_orders).call
      if email_sender.success?
        result.sent_notifications << email_sender.sent_notification
      else
        result.errors += email_sender.errors
      end
    end
    result
  end

private

  attr_reader :summary_day, :result

  def delivery_day_orders
    email_ref = "'customer-order_summary-%-#{summary_day.to_s(:date_compact)}'"

    orders = Order.where(status: 'confirmed')
    orders = orders.where(delivery_at: (summary_day.beginning_of_day..summary_day.end_of_day))
    orders = orders.joins(customer_profile: %i[billing_details user]).where(billing_details: { order_summaries: true }).where(users: { business: 'yr' })
    orders = orders.joins('LEFT JOIN emails ON emails.fk_id = customer_profiles.id AND emails.ref ilike ' << email_ref).where(emails: { id: nil })
    orders.order(:delivery_at)
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
