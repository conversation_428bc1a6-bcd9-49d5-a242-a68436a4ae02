class Customers::Notifications::NotifyBudgetSpends

  NOTIFIABLE_PERCENTS = [100, 90, 75, 50].freeze

  def initialize(since: Time.zone.now, verbose: false)
    @since = since
    @is_verbose = verbose
    @notifications = []
  end

  def call
    current_budgets.group_by(&:customer_profile).each do |customer, customer_budgets|
      if is_verbose
        puts ''
        puts "#{customer.name} - #{customer_budgets.size}"
      end
      customer_budgets.each do |budget|
        puts "    #{budget.value} => #{budget.po_number || budget.frequency}" if is_verbose    
        create_notification_for(customer: customer, budget: budget)
      end
    end
    notifications
  end

private

  attr_reader :since, :is_verbose, :notifications

  def create_notification_for(customer:, budget:)
    spend, customer_orders = spend_for(customer: customer, budget: budget)
    spend_percentage = (spend.to_f / budget.value) * 100
    notifiable_percentage = NOTIFIABLE_PERCENTS.detect{|percentage| spend_percentage >= percentage }
    return if notifiable_percentage.blank?

    event_type = "budget-spend-#{notifiable_percentage}"
    existing_logs = EventLog.where(loggable: budget, event: event_type)
    existing_logs = existing_logs.where(created_at: since.beginning_of_month...since.end_of_month)

    severity = case notifiable_percentage
    when 100
      'error'
    when 90
      'warning'
    else
      'info'
    end

    if existing_logs.blank?
      puts "      Total Spend #{customer_orders.count}: #{spend.round(2)} --- #{notifiable_percentage}% (#{spend_percentage.round(2)}%)" if is_verbose
      event_info = {
        number_of_orders: customer_orders.count,
        budget: budget.value.round(2),
        spend: spend.round(2),
        spend_percentage: spend_percentage.round(2),
        po_number: budget.po_number,
        remaining: (budget.value - spend).round(2)
      }
      event_creator = EventLogs::Create.new(event_object: budget, event: event_type, severity: severity,  **event_info).call
      if event_creator.success?
        @notifications << event_creator.event_log
      end
    end
  end

  def spend_for(customer:, budget:)
    customer_orders = customer.orders.where(delivery_at: since.beginning_of_month..since.end_of_month)
    customer_orders = customer_orders.where(status: %w[new amended pending confirmed delivered])
    if (budget_po = budget.customer_purchase_order.presence)
      customer_orders = customer_orders.where(customer_purchase_order: budget_po)
    end
    estimated_spend = customer_orders.sum(&:customer_total)
    return [estimated_spend, customer_orders]
  end

  def current_budgets
    budget_arel = CustomerBudget.arel_table

    starts_before_check = budget_arel[:starts_on].lteq(since.end_of_month)
    ends_after_check = budget_arel[:ends_on].gteq(since.beginning_of_month)

    has_end = budget_arel[:ends_on].not_eq(nil)
    no_end = budget_arel[:ends_on].eq(nil)

    ranged_condition = has_end.and(starts_before_check.and(ends_after_check))
    long_condition = no_end.and(starts_before_check)

    budgets = CustomerBudget.where(frequency: 'monthly')
    budgets.where(ranged_condition.or(long_condition))
  end

end