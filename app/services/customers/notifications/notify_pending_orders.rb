class Customers::Notifications::NotifyPendingOrders

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new
  end

  def call
    if can_notify?
      customer_grouped_pending_orders.each do |customer, customer_pending_orders|
        Customers::Emails::SendPendingOrdersEmail.new(customer: customer, pending_orders: customer_pending_orders, delivery_on: delivery_on).call
        result.pending_orders += customer_pending_orders
      end
    end
    result
  end

private

  attr_reader :time, :result

  def can_notify?
    case
    when time.on_weekend?
      result.errors << 'Cannot notify on a weekend'
    when customer_grouped_pending_orders.blank?
      result.errors << 'No Pending Orders'
    end
    result.errors.blank?
  end

  def customer_grouped_pending_orders
    return @pending_orders if !@pending_orders.nil?

    orders = Order.where(status: 'quoted')
    orders = orders.where(delivery_at: delivery_on..delivery_end)
    orders = orders.where.not(customer_profile_id: nil)
    orders = orders.where.not(delivery_at: nil)

    @pending_orders = orders.group_by(&:customer_profile)
  end

  def delivery_on
    (time + 1.day).beginning_of_day
  end

  def delivery_end
    days_in_future = time.wday == 5 ? 3 : 1
    (time + days_in_future.days).end_of_day
  end

  class Result
    attr_accessor :pending_orders, :errors

    def initialize
      @pending_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
