class MenuItems::<PERSON><PERSON>

  def initialize(menu_item:, deep: true)
    @menu_item = menu_item
    @deep_clone = deep
    @result = Result.new(template: menu_item)
  end

  def call
    if clone_item && deep_clone
      clone_serving_sizes if serving_sizes.present?
      clone_menu_extra_sections if menu_extra_sections.present?
      result.cloned_item.reload
    end
    result
  end

private

  attr_reader :menu_item, :deep_clone, :result

  def clone_item
    item_creator = MenuItems::Upsert.new(menu_item_params: clone_params, forced: true).call
    if item_creator.success?
      result.cloned_item = item_creator.menu_item
    else
      result.errors += item_creator.errors
    end
    result.errors.blank?
  end

  def clone_params
    params = menu_item.attributes.symbolize_keys.except(:id, :archived_at, :created_at, :updated_at, :name, :weight)
    params[:name] = "#{menu_item.name} - CLONED"
    params
  end

  def serving_sizes
    @_serving_sizes ||= menu_item.serving_sizes.where(archived_at: nil).order(:weight)
  end

  def clone_serving_sizes
    serving_sizes.each do |serving_size|
      serving_cloner = ServingSizes::Clone.new(serving_size: serving_size, menu_item: result.cloned_item).call
      result.errors += serving_cloner.errors if !serving_cloner.success?
    end
  end

  def menu_extra_sections
    @_menu_extra_sections ||= menu_item.menu_extra_sections.where(archived_at: nil).order(:weight)
  end

  def clone_menu_extra_sections
    menu_extra_sections.each do |menu_extra_section|
      extra_section_cloner = MenuExtraSections::Clone.new(menu_extra_section: menu_extra_section, menu_item: result.cloned_item).call
      result.errors += extra_section_cloner.errors if !extra_section_cloner.success?
    end
  end

  class Result
    attr_accessor :template, :cloned_item, :errors

    def initialize(template:)
      @template = template
      @cloned_item = nil
      @errors = []
    end

    def success?
      errors.blank? && cloned_item.present? && cloned_item.persisted?
    end
  end
end
