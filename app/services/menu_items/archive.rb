class MenuItems::Archive

  def initialize(menu_item:, forced: nil)
    @menu_item = menu_item
    @is_forced_archive = forced.presence || false
    @result = Result.new(menu_item: menu_item)
  end

  def call
    if can_archive? && menu_item.update(archived_at: Time.zone.now)
      mark_menu_as_updated
      self.delay(queue: :data_integrity).archive_associated
    end
    result
  end

private

  attr_accessor :menu_item, :is_forced_archive, :result

  def can_archive?
    case
    when menu_item.blank?
      result.errors << 'Cannot archive a missing menu item'
    when menu_item.archived_at.present?
      result.errors << 'Cannot archvie an already archived menu item'
    when !is_forced_archive && menu_item_in_use?
      result.warnings << 'The item is still in use!'
      result.warnings << 'Please be aware that if the supplier is removing the item that they will still fulfil the order that is in use.'
    end
    result.errors.blank? && result.warnings.blank?
  end

  def menu_item_in_use?
    menu_item.rate_cards.present? ||
      menu_item.order_lines.joins(:order).where(orders: { status: %w[pending quoted new amended confirmed] }).present?
  end

  def serving_sizes
    @_serving_sizes ||= menu_item.serving_sizes.where(archived_at: nil)
  end

  def menu_extra_sections
    @_menu_extra_sections ||= menu_item.menu_extra_sections.where(archived_at: nil)
  end

  def archive_associated
    serving_sizes.each do |serving_size|
      ServingSizes::Archive.new(serving_size: serving_size).call
    end
    menu_extra_sections.each do |menu_extra_section|
      MenuExtraSections::Archive.new(menu_extra_section: menu_extra_section).call
    end
  end

  def mark_menu_as_updated
    supplier = menu_item.supplier_profile
    return if supplier.blank?

    supplier.supplier_flags&.update(menu_last_updated_on: Time.zone.now)
  end

  class Result
    attr_accessor :menu_item, :errors, :warnings

    def initialize(menu_item:)
      @menu_item = menu_item
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank? && warnings.blank? && menu_item.reload.archived_at.present?
    end
  end
end
