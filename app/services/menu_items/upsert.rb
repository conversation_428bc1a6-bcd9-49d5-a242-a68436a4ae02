class MenuItems::Upsert

  ORDERLINE_CHANGE_FIELDS = %w[name price].freeze

  def initialize(menu_item_params:, menu_item: nil, forced: nil)
    @is_forced = forced.presence || false
    @menu_item_params = menu_item_params.present? ? menu_item_params.to_h.symbolize_keys : {}
    @menu_item = menu_item.presence || fetch_menu_item
    @result = Result.new
  end

  def call
    has_potential_order_line_changes = detect_changes
    if menu_item.update(sanitized_params)
      result.menu_item = menu_item
      update_future_order_lines if has_potential_order_line_changes
      mark_menu_as_updated if supplier.present?
    else
      result.errors += menu_item.errors.full_messages
    end
    result
  end

private

  attr_reader :menu_item, :menu_item_params, :is_forced, :result

  def fetch_menu_item
    return MenuItem.new if is_forced

    retrieval_params = case
    when menu_item_params[:sku].present?
      {
        sku: menu_item_params[:sku]
      }
    else
      {
        name: sanitized_name_params[:name],
        supplier_profile: supplier,
      }
    end
    MenuItem.where(retrieval_params).first_or_initialize
  end

  def detect_changes
    return false if menu_item.new_record?

    menu_item.assign_attributes(sanitized_params)
    ORDERLINE_CHANGE_FIELDS.detect do |field|
      menu_item.changes[field].present? && menu_item.changes[field].first != menu_item.changes[field].last
    end.present?
  end

  def sanitized_params
    [
      default_params,
      menu_item_params,
      sanitized_name_params,
      sanitized_stock_params
    ].inject(&:merge)
  end

  def default_params
    return {} if !menu_item.new_record?

    {
      weight: menu_item_params[:weight] || menu_item.weight || max_weight
    }
  end

  def sanitized_name_params
    return {} if menu_item_params[:name].blank?

    {
      name: menu_item_params[:name].strip
    }
  end

  def sanitized_stock_params
    stock_quantity = menu_item_params[:stock_quantity].present? ? menu_item_params[:stock_quantity].to_i : nil
    {
      stock_quantity: stock_quantity
    }
  end

  def menu_section
    @_menu_section ||= case
    when menu_item_params[:menu_section].present?
      menu_item_params[:menu_section]
    when menu_section_id = menu_item_params[:menu_section_id].presence
      MenuSection.where(id: menu_section_id).first
    when menu_item.present?
      menu_item.menu_section
    end
  end

  def supplier
    @_supplier ||= case
    when menu_item_params[:supplier_profile].present?
      menu_item_params[:supplier_profile]
    when supplier_profile_id = menu_item_params[:supplier_profile_id].presence
      SupplierProfile.where(id: supplier_profile_id).first
    when menu_section.present?
      menu_section.supplier_profile
    when menu_item.present?
      menu_item.supplier_profile
    end
  end

  def max_weight
    weight = case
    when supplier.present?
      supplier.menu_items.pluck(:weight).compact.max
    else
      MenuItem.pluck(:weight).compact.max
    end
    (weight.presence || 0) + 1
  end

  def update_future_order_lines
    return if supplier&.woolworths?

    has_existing_update = OrderLines::FutureUpdateExists.new(item: menu_item).call
    return if has_existing_update

    MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).delay(queue: :data_integrity).call
  end

  def mark_menu_as_updated
    supplier.supplier_flags&.update(menu_last_updated_on: Time.zone.now)
  end

  class Result
    attr_accessor :menu_item, :errors

    def initialize
      @menu_item = nil
      @errors = []
    end

    def success?
      errors.blank? && menu_item.present? && menu_item.valid? && menu_item.persisted?
    end
  end
end
