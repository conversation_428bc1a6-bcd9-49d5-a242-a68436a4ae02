class MenuItems::ListUsageInOrders

  def initialize(menu_item:, since: Time.zone.now)
    @menu_item = menu_item
    @since = since
  end

  def call
    future_order_lines.group_by(&:order)
  end

private

  attr_reader :menu_item, :since

  def future_order_lines
    order_lines = OrderLine.joins(:order)
    order_lines = order_lines.where(menu_item: menu_item)
    order_lines = order_lines.where(orders: { status: %w[pending quoted new amended confirmed] })
    order_lines = order_lines.where('orders.delivery_at >= ?', since)
    order_lines = order_lines.order('orders.delivery_at ASC, order_lines.updated_at ASC')
    order_lines
  end

end