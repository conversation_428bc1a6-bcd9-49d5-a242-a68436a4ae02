class MenuItems::UpdateFutureOrderLines
  include ActionView::Helpers::NumberHelper

  OrderUpdate = Struct.new(:id, :name, :customer_name, :old_total, :new_total)

  def initialize(menu_item:, serving_size: nil, rate_card: nil)
    @menu_item = menu_item
    @serving_size = serving_size
    @rate_card = rate_card
    @updated_orders = []
  end

  def call
    return if menu_item.blank? || menu_item.supplier_profile.woolworths?

    future_order_lines.group_by(&:order).each do |order, order_lines|
      customer = order.customer_profile
      order_lines.group_by(&:location).each do |location, location_order_lines|
        location_order_lines.each do |order_line|
          OrderLines::Upsert.new(order: order, customer: customer, location: location, order_line_params: { id: order_line.id }, update_item: true).call
        end
      end
      update_totals_for(order: order)
    end
    notify_admin if updated_orders.present?
  end

private

  attr_reader :menu_item, :serving_size, :rate_card, :updated_orders

  def future_order_lines
    order_lines = OrderLine.where(menu_item: menu_item).includes(:location, order: :customer_profile)
    order_lines = order_lines.joins(:order)
    order_lines = order_lines.where(orders: { status: %w[draft new amended pending confirmed paused] })
    order_lines = order_lines.where('orders.delivery_at > ?', Time.zone.now.beginning_of_week)
    order_lines = order_lines.where(serving_size: serving_size) if serving_size.present?
    if rate_card.present?
      order_lines = order_lines.joins(order: :customer_profile).where(customer_profiles: { company_id: rate_card.company_id }).readonly(false)
    end
    order_lines.includes(:order, :location)
  end

  def update_totals_for(order:)
    old_total = number_to_currency(order.customer_total.dup, precision: 2)
    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
    order.supplier_profiles.each do |supplier|
      Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, save_totals: true).call
    end
    new_total = number_to_currency(order.customer_total.dup, precision: 2)
    updated_orders << OrderUpdate.new(order.id, order.name, order.customer_profile.name, old_total, new_total)
  end

  def notify_admin
    if rate_card.present?
      Admin::Emails::SendRateCardAmendedEmail.new(rate_card: rate_card, updated_orders: updated_orders).delay(queue: :notifications).call
    else
      Admin::Emails::SendMenuItemAmendedEmail.new(menu_item: menu_item, serving_size: serving_size, updated_orders: updated_orders).delay(queue: :notifications).call
    end
  end

end
