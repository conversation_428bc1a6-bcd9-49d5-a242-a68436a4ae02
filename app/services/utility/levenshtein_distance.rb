class Utility::LevenshteinDistance

  def initialize(str1:, str2:)
    @str1 = str1
    @str2 = str2
  end

  def call
    return str2_length if str1_length.zero?
    return str1_length if str2_length.zero?

    (1..str2_length).each do |j|
      (1..str1_length).each do |i|
        cost = str1[i - 1] != str2[j - 1] ? 1 : 0

        distance_matrix[i][j] = [
          distance_matrix[i - 1][j] + 1,
          distance_matrix[i][j - 1] + 1,
          distance_matrix[i - 1][j - 1] + cost
        ].min
      end
    end

    distance_matrix[str1_length][str2_length]
  end

private

  attr_reader :str1, :str2

  def distance_matrix
    @_distance_matrix ||= begin
      matrix = Array.new(str1_length + 1) { Array.new(str2_length + 1) }

      (0..str1_length).each { |i| matrix[i][0] = i }
      (0..str2_length).each { |j| matrix[0][j] = j }

      matrix
    end
  end

  def str1_length
    str1.length
  end

  def str2_length
    str2.length
  end

end
