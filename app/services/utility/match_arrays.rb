class Utility::MatchArrays
  def initialize(*arrays)
    @arrays = arrays
  end

  def call
    return false if arrays.map(&:size).uniq.size != 1

    (arrays.size - 1).times do |i|
      arr_1 = arrays[i]
      arr_2 = arrays[i + 1]
      return false if !match_2_arrays(arr_1, arr_2)
    end
    true
  end

private

  attr_reader :arrays

  def match_2_arrays(arr_1, arr_2)
    return false if arr_1.nil? || arr_2.nil?

    ((arr_1 | arr_2) - (arr_1 & arr_2)).blank?
  end

end
