class Leads::<PERSON>reate

  def initialize(lead_params: {})
    @lead_params = lead_params
    @result = Result.new
  end

  def call
    if can_create_lead?
      result.user = user
      if lead.new_record?
        if lead.update(lead_params)
          Hubspot::SyncLead.new(lead: lead).delay.call
        else
          result.errors += lead.errors.full_messages
        end
      end
      result.lead = lead
    end
    result
  end

private

  attr_reader :lead_params, :result

  def can_create_lead?
    case
    when user.persisted?
      result.errors << 'A user with that email already exists!'
    when !user.valid_attributes?(:email)
      result.errors += user.errors.full_messages
    end
    result.errors.blank?
  end

  def user
    @user ||= User.where(email: lead_params[:email]).first_or_initialize
  end

  def lead
    @lead ||= Lead.where(email: lead_params[:email]).first_or_initialize
  end

  class Result
    attr_accessor :user, :lead, :errors

    def initialize
      @user = nil
      @lead = nil
      @errors = []
    end

    def success?
      @user.present? && @lead.present? && errors.blank?
    end
  end

end


