class Dear::AttachSaleToOrder

  def initialize(sale:, order:, supplier:)
    @sale = sale
    @order = order
    @supplier = supplier
    @result = Result.new
  end

  def call
    if can_attach?
      upsert_dear_sale
    end
    result
  end

private

  attr_reader :sale, :order, :supplier, :result

  def can_attach?
    case
    when sale.blank?
      result.errors << 'Cannot attach to a missing sale'
    when order.blank?
      result.errors << 'Cannot attach to a missing order'
    when supplier.blank?
      result.errors << 'Cannot attach sale with a missing supplier'
    end
    result.errors.blank?
  end

  def dear_sale
    @_dear_sale ||= order.dear_sales.where(supplier_profile: supplier).first_or_initialize
  end

  def upsert_dear_sale
    if dear_sale.update(sanitized_params)
      result.dear_sale = dear_sale
    else
      result.errors += dear_sale.errors.full_messages
    end
  end

  def sanitized_params
    {
      sale_id: sale[:ID],
      location: sale[:Location],
    }
  end

  class Result
    attr_accessor :dear_sale, :errors

    def initialize
      @dear_sale = nil
      @errors = []
    end

    def success?
      errors.blank? && dear_sale.present?
    end
  end
end
