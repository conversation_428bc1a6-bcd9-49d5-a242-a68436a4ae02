class Dear::SyncProductImage

  def initialize(menu_item:, attachments: [], refresh: false)
    @menu_item = menu_item
    @attachments = attachments
    @refresh = refresh
    @result = Result.new(menu_item: menu_item)
  end

  def call
    if can_sync_image?
      upload_attachment
    end
    result
  end

private

  attr_reader :menu_item, :attachments, :refresh, :result

  def can_sync_image?
    case
    when menu_item.blank?
      result.errors << 'Cannot sync image to a missing menu item'
    when menu_item.image.present? && menu_item.image.include?('cloudinary') && !refresh
      result.errors << 'Item already has an uploaded image'
    when potential_attachment.blank?
      result.errors << 'Cannot sync without an image'
    end
    result.errors.blank?
  end

  def potential_attachment
    return nil if attachments.blank?
    return @_potential_attachment if @_potential_attachment.present?

    potential_attachments = attachments.select{|attachment| attachment.type.include?('image') }
    attachment = potential_attachments.detect(&:default)
    attachment ||= potential_attachments.first
    @_potential_attachment = attachment
  end

  def upload_attachment
    begin
      cloudinary_image_url = Cloudinary::Uploader.upload(potential_attachment.url)['secure_url']
      if menu_item.update!(image: cloudinary_image_url)
        result.image = cloudinary_image_url
      end
    rescue CloudinaryException
      result.errors << "Image upload to cloudinary failed for menu item - ##{menu_item.id} => #{potential_attachment.url}"
      # Sometimes images can't be found, and a `CloudinaryException: Resource not found - [image URL here]`
      # exception is thrown. Ignore it for now, and automatically try again the next time the importer is run.
    end
  end

  class Result
    attr_accessor :menu_item, :image, :errors

    def initialize(menu_item:)
      @menu_item = menu_item
      @image = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
