class Dear::SyncSupplierOrders

  def initialize(supplier:, time: Time.zone.now, verbose: false)
    @supplier = supplier
    @verbose = verbose
    @time = time
    @result = Result.new
  end

  def call
    if can_sync?
      syncable_orders.each do |order|
        sync_with_dear(order)
      end
    end
    result
  end

private

  attr_reader :supplier, :time, :verbose, :result

  def can_sync?
    case
    when supplier.blank?
      result.errors << 'Cannot sync without a supplier'
    when supplier.dear_account.blank? || !supplier.dear_account.active?
      result.errors << 'Supplier does not have an active Dear Account'
    end
    result.errors.blank?
  end

  def syncable_orders
    (orders_through_order_lines + orders_through_dear_sales).uniq
  end

  def sync_with_dear(order)
    puts "Syncing Order #{order.id}" if verbose
    order_syncer = Dear::SyncOrder.new(order: order, supplier: supplier).call
    if order_syncer.success?
      result.synced_orders << order
    else
      errors = ["Could not sync order #{order.id}"]
      errors += result.errors
      result.errors << errors
    end
  end

  def base_orders
    return @_base_orders if !@_base_orders.nil?

    orders = Order.where('delivery_at > ?', time.beginning_of_day)
    orders = orders.where.not(status: 'delivered')
    @_base_orders = orders
  end

  def orders_through_order_lines
    base_orders.joins(:order_lines).where(order_lines: { supplier_profile: supplier })
  end

  def orders_through_dear_sales
    base_orders.joins(:dear_sales).where(dear_sales: { supplier_profile: supplier })
  end

  class Result
    attr_accessor :synced_orders, :errors

    def initialize
      @synced_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
