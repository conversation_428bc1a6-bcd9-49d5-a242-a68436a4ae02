class Dear::API::FetchProducts < Dear::API

  PRODUCT_LIMIT = 100

  def initialize(supplier:, verbose: false)
    super(supplier: supplier)
    @verbose = verbose
    @result = Result.new
  end

  def call
    if can_fetch?
      page = 1
      while can_get_products_for?(page)
        get_product_for(page: page)
        page += 1
      end
    end
    result
  end

private

  attr_reader :verbose, :result

  def can_fetch?
    result.errors += can_make_requests? # from Dear::API
    result.errors.blank?
  end

  def get_product_for(page:)
    puts "Getting data from API for page #{page}" if verbose
    product_getter = dear_request(
      method: :get,
      url: PRODUCT_ENDPOINT,
      params: {
        page: page,
        limit: PRODUCT_LIMIT,
        IncludeDeprecated: true,
        IncludeAttachments: true,
      }
    )
    if product_getter.success?
      products_data = product_getter.data
      result.total_products = products_data[:Total] if products_data[:Total] > result.total_products
      result.products += products_data[:Products]
    else
      result.errors += product_getter.errors
    end
  end

  def can_get_products_for?(page)
    page == 1 || result.total_products > (page - 1) * PRODUCT_LIMIT
  end

  class Result
    attr_accessor :products, :total_products, :errors

    def initialize
      @products = []
      @total_products = 0
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
