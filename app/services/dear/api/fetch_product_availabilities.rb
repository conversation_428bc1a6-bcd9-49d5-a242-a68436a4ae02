class Dear::API::FetchProductAvailabilities < Dear::API
  ITEM_LIMIT = 100

  def initialize(supplier:, verbose: false)
    super(supplier: supplier)
    @verbose = verbose
    @result = Result.new
  end

  def call
    page = 1
    while can_get_availabilities_for?(page)
      get_availabilities_for(page: page)
      page += 1
    end
    result
  end

private

  attr_reader :verbose, :result

  def get_availabilities_for(page:)
    puts "Getting availability data from API for page #{page}" if verbose
    availability_getter = dear_request(
      method: :get,
      url: AVAILABILITY_ENDPOINT,
      params: {
        page: page,
        limit: ITEM_LIMIT,
      }
    )
    result.total_availabilities = availability_getter[:Total] if availability_getter[:Total] > result.total_availabilities
    result.availabilities += availability_getter[:ProductAvailabilityList]
  end

  def can_get_availabilities_for?(page)
    page == 1 || result.total_availabilities > (page - 1) * ITEM_LIMIT
  end

  class Result
    attr_accessor :availabilities, :total_availabilities, :errors

    def initialize
      @total_availabilities = []
      @total_availabilities = 0
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
