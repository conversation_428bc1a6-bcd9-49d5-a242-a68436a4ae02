class Dear::API::UpsertSale < Dear::API

  def initialize(supplier:, order:)
    @order = order
    super(supplier: supplier)
    @result = Result.new
  end

  def call
    if can_upsert?
      create_or_update_sale
    end
    result
  end

private

  attr_reader :sale, :order, :result

  def can_upsert?
    case
    when supplier.blank?
      result.errors << 'Cannot upsert without a supplier'
    when !is_dear_supplier?
      result.errors << 'Suppier does not have an active Dear Account'
    when order.blank?
      result.errors << 'Cannot upsert without an order'
    end
    result.errors.blank?
  end

  def dear_sale
    @_dear_sales ||= order.dear_sales.where(supplier_profile: supplier).first
  end

  def create_or_update_sale
    if dear_sale.blank?
      request_method = :post
      request_body = sale_body
    else
      request_method = :put
      request_body = [
        update_body,
        sale_body
      ].inject(&:merge)
    end

    sale_upserter = dear_request(
      method: request_method,
      url: SALE_ENDPOINT,
      body: request_body
    )
    if sale_upserter.success?
      attach_sale_to_order(sale_upserter.data)
    else
      result.errors += sale_upserter.errors
    end
  end

  def attach_sale_to_order(sale)
    sale_attacher = Dear::AttachSaleToOrder.new(sale: sale, order: order, supplier: supplier).call
    if sale_attacher.success?
      result.dear_sale = sale_attacher.dear_sale
    else
      result.errors += sale_attacher.errors
    end
  end

  def update_body
    {
      id: dear_sale.sale_id,
      location: dear_sale.location
    }
  end

  def sale_body
    {
      customer_id: dear_account.customer_id,
      external_ID: order.id,
      customer_reference: "#{order.name} | Order #{order.id}",
      contact: order.contact_name,
      email: order.contact_email,
      phone: order.phone,
      ship_by: order.delivery_at.strftime('%FT%T%:z'),
      shipping_address: [
        address_details,
        customer_details,
        {
          ship_to_other: false
        }
      ].inject(&:merge),
      shipping_notes: order.delivery_instruction,
      tax_rule: SALE_TAX_RULE,
      skip_quote: true,
    }
  end

  def address_details
    if order.delivery_address_level.present?
      line1 = order.formatted_delivery_address_level
      line2 = order.delivery_address
    else
      line1 = order.delivery_address
      line2 = ''
    end
    delivery_suburb = order.delivery_suburb
    {
      line1: line1,
      line2: line2,
      city: delivery_suburb.name,
      state: delivery_suburb.state,
      postcode: delivery_suburb.postcode,
      country: 'AUS',
    }
  end

  def customer_details
    customer = order.customer_profile
    {
      company: customer.customer_or_company_name,
      contact: customer.email_salutation,
    }
  end

  class Result
    attr_accessor :dear_sale, :errors

    def initialize
      @dear_sale = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
