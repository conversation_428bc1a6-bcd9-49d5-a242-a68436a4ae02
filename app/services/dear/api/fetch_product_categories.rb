class Dear::API::FetchProductCategories < Dear::API

  ITEM_LIMIT = 100

  def initialize(supplier:, verbose: false)
    super(supplier: supplier)
    @verbose = verbose
    @result = Result.new
  end

  def call
    if can_fetch?
      page = 1
      while can_get_categories_for?(page)
        get_categories_for(page: page)
        page += 1
      end
    end
    result
  end

private

  attr_reader :verbose, :result

  def can_fetch?
    result.errors += can_make_requests? # from Dear::API
    result.errors.blank?
  end

  def get_categories_for(page:)
    puts "Getting category data from API for page #{page}" if verbose
    categories_getter = dear_request(
      method: :get,
      url: CATEGORY_ENDPOINT,
      params: {
        page: page,
        limit: ITEM_LIMIT,
      }
    )
    if categories_getter.success?
      categories_data = categories_getter.data
      result.total_categories = categories_data[:Total] if categories_data[:Total] > result.total_categories
      result.categories += categories_data[:CategoryList]
    else
      result.errors += categories_getter.errors
    end
  end

  def can_get_categories_for?(page)
    page == 1 || result.total_categories > (page - 1) * ITEM_LIMIT
  end

  class Result
    attr_accessor :categories, :total_categories, :errors

    def initialize
      @categories = []
      @total_categories = 0
      @errors = []
    end

    def categories_hash
      return {} if categories.blank?

      categories.map do |category|
        [category[:ID], category[:Name]]
      end.to_h
    end

    def success?
      errors.blank?
    end
  end

end
