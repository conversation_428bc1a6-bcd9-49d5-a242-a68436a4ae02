class Dear::API::UpsertSaleOrder < Dear::API

  def initialize(supplier:, order:, dear_sale: nil)
    @order = order
    super(supplier: supplier)
    @dear_sale = dear_sale.presence || fetch_dear_sale
    @result = Result.new
  end

  def call
    if can_upsert?
      upsert_sale_order
    end
    result
  end

private

  attr_reader :order, :dear_sale, :result

  def can_upsert?
    case
    when supplier.blank?
      result.errors << 'Cannot upsert without a supplier'
    when !is_dear_supplier?
      result.errors << 'Suppier does not have an active Dear Account'
    when order.blank?
      result.errors << 'Cannot upsert without an order'
    when dear_sale.blank?
      result.errors << 'Need a Dear Sale before upserting Sale Order'
    end
    result.errors.blank?
  end

  def fetch_dear_sale
    order.present? && order.dear_sales.where(supplier_profile: supplier).first
  end

  def upsert_sale_order
    sale_order_upserter = dear_request(
      method: :post,
      url: SALE_ORDER_ENDPOINT,
      body: sale_order_body
    )
    if sale_order_upserter.success?
      result.sale_order_synced = true
    else
      result.errors += sale_order_upserter.errors
    end
  end

  def sale_order_body
    [
      sale_data,
      order_data
    ].inject(&:merge)
  end

  def sale_data
    {
      sale_id: dear_sale.sale_id,
      memo: '',

    }
  end

  def order_data
    {
      status: 'DRAFT',
      lines: lines_data,
      additional_charges: additional_charges_data,
    }
  end

  def lines_data
    order_lines.map do |order_line|
      order_line_data_for(order_line)
    end
  end

  def order_line_data_for(order_line)
    order_line_total_cost = (order_line.cost * order_line.quantity)
    order_line_tax = order_line.is_gst_free ? 0 : (order_line_total_cost * gst).round(2)
    {
      sku: order_line.serving_size.present? ? order_line.serving_size.sku : order_line.menu_item.sku,
      name: order_line.name,
      quantity: order_line.quantity,
      price: order_line.cost,
      tax: order_line_tax,
      tax_rule: SALE_TAX_RULE,
      total: order_line_total_cost,
    }
  end

  def order_lines
    return @_order_lines if !@_order_lines.nil?

    lister_options = {
      order: order,
      supplier: supplier,
    }
    @_order_lines = OrderLines::List.new(options: lister_options, includes: %i[menu_item serving_size]).call
  end

  def additional_charges_data
    order_supplier = order.order_suppliers.where(supplier_profile: supplier).first
    if order_supplier.present? && order_supplier.delivery.present?
      delivery_fee = order_supplier.delivery
    else
      delivery_fee = Orders::CalculateDelivery.new(order: order, order_lines: order_lines, profile: supplier).call
    end
    return [] if delivery_fee&.to_d == 0.0.to_d

    delivery_tax = (delivery_fee * gst).round(2)
    [
      {
        description: 'Delivery Fee',
        price: delivery_fee,
        quantity: 1,
        tax: delivery_tax,
        total: delivery_fee,
        tax_rule: SALE_TAX_RULE
      }
    ]
  end

  def gst
    country_code = order.symbolized_country_code || :au
    @_gst ||= yordar_credentials(:yordar, :gst_percent, country_code).to_f || 0
  end

  class Result
    attr_accessor :sale_order_synced, :errors

    def initialize
      @sale_order_synced = false
      @errors = []
    end

    def success?
      errors.blank? && sale_order_synced
    end
  end

end
