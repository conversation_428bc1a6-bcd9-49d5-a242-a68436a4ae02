class Dear::SyncSupplierCategories

  def initialize(supplier:, verbose: false)
    @supplier = supplier
    @verbose = verbose
    @result = Result.new
  end

  def call
    if can_map_categories?
      existing_categories.each do |category|
        map_category(category)
      end
    end
    result
  end

private

  attr_reader :supplier, :verbose, :mapped_categories, :result

  def can_map_categories?
    case
    when supplier.blank?
      result.errors << 'Cannot sync categories for a missing supplier'
    when dear_account.blank? || !dear_account.active?
      result.errors << 'Cannot sync categories for an missing/in-active Dear supplier'
    when existing_categories.blank?
      result.errors << 'No need to sync categories'
    when api_categories.blank?
      result.errors << 'Cannot sync missing categories from API'
    end
    result.errors.blank?
  end

  def dear_account
    @_dear_account ||= supplier.dear_account
  end

  def existing_categories
    @_existing_categories ||= dear_account.categories.dup
  end

  def api_categories
    return @_api_categories if !@_api_categories.nil?

    categories = []
    categories_fetcher = Dear::API::FetchProductCategories.new(supplier: supplier, verbose: verbose).call
    if categories_fetcher.success?
      categories = categories_fetcher.categories_hash
    end
    @_api_categories = categories
  end

  def map_category(category)
    api_category_name = api_categories[category.category_id]
    if api_category_name.present?
      category.update(name: api_category_name) if category.name != api_category_name
    else
      category.destroy
    end
  end

  class Result
    attr_accessor :mapped_categories, :errors

    def initialize
      @mapped_categories = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
