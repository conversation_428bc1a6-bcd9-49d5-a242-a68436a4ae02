class Dear::SyncProduct

  def initialize(api_product:, menu_section:, supplier: nil, verbose: false)
    @api_product = api_product
    @menu_section = menu_section
    @supplier = supplier.presence || menu_section&.supplier_profile
    @verbose = verbose
    @result = Result.new
  end

  def call
    if can_sync_product?
      if product_is_active?
        save_menu_item
      else
        archive_menu_item
      end
    end
    result
  end

private

  attr_reader :api_product, :menu_section, :supplier, :verbose, :result

  def can_sync_product?
    case
    when api_product.blank?
      result.errors << 'Cannot sync product without an api product'
    when menu_section.blank?
      result.errors << 'Cannot sync product without a menu section'
    when mapped_product.blank? || mapped_product.sku.blank?
      result.errors << 'Cannot sync product wihtout the correct mapping'
    end
    result.errors.blank?
  end

  def product_is_active?
    api_product[:Status] == 'Active' && mapped_product.price&.to_d != 0.0.to_d
  end

  def mapped_product
    @_mapped_product ||= Dear::MapProduct.new(api_product: api_product, supplier: supplier).call
  end

  def save_menu_item
    item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call
    if item_creator.success?
      menu_item = item_creator.menu_item
      puts "Menu Item - #{menu_item.name} - #{menu_item.id}" if verbose
      result.menu_item = menu_item
      save_attachment_for(menu_item) if mapped_product.attachments.present?
    else
      result.errors += item_creator.errors
    end
  end

  def menu_item_params
    [
      default_item_attributes,
      sanitized_item_params,
      sanitized_dietary_params
    ].inject(&:merge)
  end

  def default_item_attributes
    {
      menu_section_id: menu_section.id,
      supplier_profile_id: supplier.id,
      archived_at: nil
    }
  end

  def sanitized_item_params
    {
      sku: mapped_product.sku,
      name: mapped_product.name,
      description: mapped_product.description,
      price: mapped_product.price,
    }
  end

  def sanitized_dietary_params
    dietary_params = {}
    Dear::MapProduct::DIETARY_FIELDS.each do |_, field|
      dietary_params[field] = mapped_product.send(field)
    end
    dietary_params
  end

  def archive_menu_item
    menu_item = MenuItem.where(sku: api_product[:SKU]).first
    if menu_item.present? && menu_item.archived_at.nil?
      item_archiver = MenuItems::Archive.new(menu_item: menu_item).call
      if item_archiver.success?
        result.menu_item = item_archiver.menu_item
      else
        result.errors += item_archiver.errors
      end
    end
  end

  def save_attachment_for(menu_item)
    Dear::SyncProductImage.new(menu_item: menu_item, attachments: mapped_product.attachments).delay.call
  end

  class Result
    attr_accessor :menu_item, :errors

    def initialize
      @menu_item = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
