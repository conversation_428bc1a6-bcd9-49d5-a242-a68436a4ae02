class Dear::<PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(supplier:, order:)
    @supplier = supplier
    @order = order
    @result = Result.new(order: order)
  end

  def call
    if can_sync_order?
      sync_dear_sale
      sync_dear_sale_order
    end
    result
  end

private

  attr_reader :supplier, :order, :dear_sale, :result

  def can_sync_order?
    case
    when supplier.blank?
      result.errors << 'Cannot sync order for a missing supplier'
    when supplier.dear_account.blank? || !supplier.dear_account.active?
      result.errors << 'Supplier does not have an active Dear Account'
    when order.blank?
      result.errors << 'Cannot sync a missing order'
    when order_lines.blank?
      result.errors << 'Order does not contain items from supplier'
    end
    result.errors.blank?
  end

  def sync_dear_sale
    sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier).call
    if sale_upserter.success?
      result.dear_sale = @dear_sale = sale_upserter.dear_sale
    else
      result.errors += sale_upserter.errors
    end
  end

  def sync_dear_sale_order
    return if dear_sale.blank?

    sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier, dear_sale: dear_sale).call
    if !sale_order_upserter.success?
      result.errors += sale_order_upserter.errors
    end
  end

  def order_lines
    return @_order_lines if !@_order_lines.nil?

    lister_options = {
      order: order,
      supplier: supplier
    }
    @_order_lines = OrderLines::List.new(options: lister_options).call
  end

  class Result
    attr_accessor :order, :dear_sale, :errors

    def initialize(order:)
      @order = order
      @dear_sale = nil
      @errors = []
    end

    def success?
      errors.blank? && dear_sale.present?
    end
  end

end
