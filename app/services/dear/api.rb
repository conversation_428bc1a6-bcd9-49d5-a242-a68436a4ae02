class Dear::API

  BASE_URL = 'https://inventory.dearsystems.com/ExternalApi/v2'.freeze

  # endpoints
  PRODUCT_ENDPOINT = 'Product'.freeze
  CATEGORY_ENDPOINT = 'ref/category'.freeze
  AVAILABILITY_ENDPOINT = 'ref/productavailability'.freeze
  SALE_ENDPOINT = 'sale'.freeze
  SALE_ORDER_ENDPOINT = 'sale/order'.freeze

  # Dear constants
  SALE_TAX_RULE = 'GST on Income'.freeze

  def initialize(supplier:)
    @supplier = supplier
  end

private

  attr_reader :supplier

  def can_make_requests?
    errors = []
    case
    when supplier.blank?
      errors << 'Supplier is missing'
    when !is_dear_supplier?
      errors << 'Suppier does not have an active Dear Account'
    end
    errors
  end

  def dear_account
    @_dear_account ||= supplier.dear_account
  end

  def is_dear_supplier?
    dear_account.present? && dear_account.active?
  end

  def dear_request(method:, url:, params: nil, body: nil)
    response = Response.new
    api_request = dear_connection.send(method) do |request|
      request.url(url)
      request.params = params if params.present?
      request.body = camelized_json(body) if body.present?
    end
    response.status = api_request.status
    if api_request.status == 200
      response.data = JSON.parse(api_request.body).deep_symbolize_keys
    else
      errors = JSON.parse(api_request.body)
      errors = errors.map(&:deep_symbolize_keys)
      response.errors = errors
    end
    response
  end

  def camelized_json(data)
    data.deep_transform_keys(&:to_s).deep_transform_keys(&:camelize).to_json
  end

  def dear_connection
    @_connection ||= Faraday.new(url: BASE_URL) {|faraday| connection_body(faraday) }
  end

  def connection_body(faraday)
    faraday.adapter(Faraday.default_adapter)
    faraday.headers['Content-Type'] = 'application/json'
    faraday.headers['api-auth-accountid'] = dear_account.account_id
    faraday.headers['api-auth-applicationkey'] = dear_account.api_key
  end

  class Response
    attr_accessor :status, :data, :errors

    def initialize
      @status = nil
      @data = nil
      @errors = []
    end

    def success?
      errors.blank? && status == 200
    end
  end

end
