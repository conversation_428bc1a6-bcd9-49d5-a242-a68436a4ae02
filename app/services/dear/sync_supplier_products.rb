class Dear::SyncSupplierProducts

  def initialize(supplier:, refresh: false, verbose: false)
    @supplier = supplier
    @refresh = refresh
    @verbose = verbose
    @result = Result.new
  end

  def call
    if can_sync_products?
      clean_up_menu if refresh
      sync_dear_categories if dear_account.categories.present?
      category_grouped_api_products.each do |category_name, category_products|
        sync_category(category_name: category_name, products: category_products)
      end
    else
      result.errors << 'Does not have an active Dear Account'
    end
    result
  end

private

  attr_reader :supplier, :refresh, :verbose, :result

  def can_sync_products?
    case
    when supplier.blank?
      result.errors << 'Cannot sync a missing supplier'
    when dear_account.blank? || !dear_account.active?
      result.errors << 'Cannot sync with a missing/in-active Dear account'
    end
    result.errors.blank?
  end

  def dear_account
    @_dear_account ||= supplier.dear_account
  end

  def clean_up_menu
    Suppliers::ArchiveMenu.new(supplier: supplier).call
  end

  def sync_dear_categories
    Dear::SyncSupplierCategories.new(supplier: supplier, verbose: verbose).call
  end

  def category_grouped_api_products
    products = []
    products_fetcher = Dear::API::FetchProducts.new(supplier: supplier, verbose: verbose).call
    if products_fetcher.success?
       products = products_fetcher.products
    else
      result.errors += products_fetcher.errors
    end
    result.all_products = products
    products.group_by{|product| product[:Category] }
  end

  def sync_category(category_name:, products:)
    menu_section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier, verbose: verbose).call
    if menu_section_syncer.success?
      menu_section = menu_section_syncer.menu_section
      result.menu_sections << menu_section
      products.each do |api_product|
        map_and_sync_product(api_product: api_product, menu_section: menu_section)
      end
    else
      result.errors += menu_section_syncer.errors
    end
  end

  def map_and_sync_product(api_product:, menu_section:)
    product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section, supplier: supplier, verbose: verbose).call
    if product_syncer.success?
      result.menu_items << product_syncer.menu_item
    else
      result.unsynced_products << api_product
    end
  end

  class Result
    attr_accessor :all_products, :menu_sections, :menu_items, :unsynced_products, :errors

    def initialize
      @all_products = []
      @menu_sections = []
      @menu_items = []
      @unsynced_products = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
