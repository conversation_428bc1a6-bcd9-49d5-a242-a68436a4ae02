class Dear::SyncMenuSection

  def initialize(category_name:, supplier:, verbose: false)
    @category_name = category_name
    @supplier = supplier
    @verbose = verbose
    @result = Result.new
  end

  def call
    if can_sync_menu_section?
      save_menu_section
    end
    result
  end

private

  attr_reader :category_name, :supplier, :verbose, :result

  def can_sync_menu_section?
    case
    when category_name.blank?
      result.errors << 'Cannot sync a menu section without a name'
    when supplier.blank?
      result.errors << 'Cannot sync without a supplier'
    when dear_account.blank? || !dear_account.active?
      result.errors << 'Cannot sync into non-dear supplier'
    when category_name.downcase == 'custom'
      result.errors << 'Cannot sync a custom menu section' # protected menu section name
    when dear_categogies.present? && dear_category.blank?
      result.errors << "Not a valid/syncable category name - #{category_name}"
    end
    result.errors.blank?
  end

  def save_menu_section
    section_upserter = MenuSections::Upsert.new(menu_section_params: menu_section_params).call
    if section_upserter.success?
      if verbose
        puts ''
        puts "Menu Secton - #{section_upserter.menu_section.name}"
      end
      result.menu_section = section_upserter.menu_section
    else
      result.error += section_upserter.errors
    end
  end

  def dear_account
    @_dear_account ||= supplier.dear_account
  end

  def dear_categogies
    @_dear_categogies ||= dear_account.categories
  end

  def dear_category
    @_dear_category ||= dear_categogies.detect{|category| category.name == category_name }
  end

  def menu_section_params
    section_name = (dear_category.present? && dear_category.override_name.presence) || category_name
    {
      name: section_name,
      group_name: nil,
      supplier_profile_id: supplier.id,
      archived_at: nil,
    }
  end

  class Result
    attr_accessor :menu_section, :errors

    def initialize
      @menu_section = nil
      @errors = []
    end

    def success?
      @errors.blank? && @menu_section.present?
    end
  end

end
