class MenuFetcher
  def initialize(menu_client:, week_of:)
    @menu_client = menu_client
    @menus = menu_client.weekly_menus
    @week_of = week_of
  end

  def any_menus?
    !@menus.empty?
  end

  def current_menu
    @current_menu ||= @menus.where(week_of: beginning_of_week..end_of_week).first
  end

  def next_menu
    @menus.where(week_of: (beginning_of_week + 1.week)..(end_of_week + 1.week)).first
  end

  def previous_menu
    @menus.where(week_of: (beginning_of_week - 1.week)..(end_of_week - 1.week)).first
  end

  private

  def beginning_of_week
    @week_of.beginning_of_week
  end

  def end_of_week
    @week_of.end_of_week
  end
end
