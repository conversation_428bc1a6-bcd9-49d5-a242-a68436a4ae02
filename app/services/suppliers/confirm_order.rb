class Suppliers::ConfirmOrder

  def initialize(supplier:, order:)
    @supplier = supplier
    @order = order
    @result = Result.new(order: order)
  end

  def call
    if can_confirm?
      accept_supplier_order_lines
      confirm_order
    end
    result
  end

private

  attr_reader :supplier, :order, :result

  def can_confirm?
    case
    when order.blank?
      result.errors << 'Cannot confirm a missing order'
    when supplier.blank? || supplier_order_lines.blank?
      result.errors << 'You don\'t have access to this order'
    end
    result.errors.blank?
  end

  def supplier_order_lines
    return @_supplier_order_lines if @_supplier_order_lines.present?

    lister_options = {
      order: order,
      supplier: supplier,
      confirmed_attendees_only: order.is_team_order?,
      paid_only: order.is_team_order? && order.attendee_pays,
    }
    @_supplier_order_lines ||= OrderLines::List.new(options: lister_options).call
  end

  def accept_supplier_order_lines
    supplier_order_lines.each do |order_line|
      order_line.update(status: 'accepted')
    end
  end

  def confirm_order
    order_confirmer = Orders::Confirm.new(order: order).call
    case
    when order_confirmer.success?
      result.order = order_confirmer.order
    when order_confirmer.errors.any?{|error| error.scan(/order line\(s\) have not been accepted/).present? }
      result.warnings << 'Order has not been processed as being confirmed because of pending order lines from other supplier(s) within the order'
    else
      result.errors += order_confirmer.errors
    end
  end

  class Result
    attr_accessor :order, :errors, :warnings

    def initialize(order:)
      @order = order
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank?
    end
  end
end
