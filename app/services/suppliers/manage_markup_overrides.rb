class Suppliers::ManageMarkupOverrides

  def initialize(supplier:, markup_overrides: [])
    @supplier = supplier
    @markup_overrides = markup_overrides
    @result = Result.new(supplier: supplier)
  end

  def call
    if can_manage?
      markup_overrides.each do |markup_override|
        case
        when markup_override[:id].present? && markup_override[:_delete].present?
          remove_override_for(markup_override: markup_override)
        when markup_override[:id].present?
          update_override_for(markup_override: markup_override)
        else
          create_override_for(markup_override: markup_override)
        end
      end
    end
    result
  end

private

  attr_reader :supplier, :markup_overrides, :result

  def can_manage?
    case
    when supplier.blank?
      result.errors << 'Cannot manage without a supplier'
    end
    result.errors.blank?
  end

  def remove_override_for(markup_override:)
    override = supplier.markup_overrides.where(id: markup_override[:id]).first
    if !override.destroy
      result.errors << "Could not remove markup_override with ID: #{markup_override[:id]}" # " - #{override.errors.full_messages.join(', ')}"
    end
  end

  def update_override_for(markup_override:)
    override = supplier.markup_overrides.where(id: markup_override[:id]).first
    if override.update(markup_override.except(:id))
      result.markup_overrides << override
    else
      result.errors << "Could not update markup_override with ID: #{markup_override[:id]}" # " - #{override.errors.full_messages.join(', ')}"
    end
  end

  def create_override_for(markup_override:)
    override = supplier.markup_overrides.where(overridable_type: markup_override[:overridable_type], overridable_id: markup_override[:overridable_id]).first_or_initialize
    if override.update(markup_override.except(:id, :overridable_type, :overridable_id))
      result.markup_overrides << override
    else
      result.errors << override.errors.full_messages.join(', ')
    end
  end

  class Result
    attr_accessor :markup_overrides, :errors
    attr_reader :supplier

    def initialize(supplier:)
      @supplier = supplier
      @markup_overrides = []
      @errors = []
    end

    def success?
      @errors.blank?
    end
  end

end