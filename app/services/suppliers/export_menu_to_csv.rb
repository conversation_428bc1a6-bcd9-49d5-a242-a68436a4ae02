class Suppliers::ExportMenuToCsv

  SECTION_FIELDS = %w[name].freeze
  ITEM_FIELDS = %w[name description price is_gst_free minimum_quantity sub_quantity is_vegan is_vegetarian is_gluten_free is_visible team_order_only team_order].freeze
  SERVING_SIZE_FIELDS = %w[name price].freeze
  EXTRAS_FIELDS = %w[name price].freeze

  def initialize(supplier:, csv_options: {})
    @supplier = supplier
    @csv_options = csv_options
    @result = Result.new(supplier: supplier)
  end

  def call
    begin
      result.csv_data = generate_csv
    rescue => exception
      error_message = "Failed to generate the csv data for menu of supplier #{supplier.id}"
      Raven.capture_exception(exception,
        message: error_message,
        extra: { supplier_id: supplier.id },
        transaction: 'Suppliers::ExportMenuToCsv'
      )
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :csv_options, :result

  def generate_csv
    CSV.generate(csv_options) do |csv|
      header_row(csv)
      menu_rows(csv)
    end
  end

  def header_row(csv)
    # headers
    header = %w[menu_section_id menu_item_id serving_size_id menu_extra_id]
    header += SECTION_FIELDS.map{|field| "menu_section_#{field}" }
    header += ITEM_FIELDS.map{|field| "menu_item_#{field}" }
    header += SERVING_SIZE_FIELDS.map{|field| "serving_size_#{field}" }
    header += EXTRAS_FIELDS.map{|field| "menu_extra_#{field}" }
    header += ordered_categories.pluck(:name)
    csv << header
    csv
  end

  def menu_rows(csv)
    sorted_menu_items.each do |menu_item|
      row = []
      row += [menu_item.menu_section.try(:id), menu_item.id]

      menu_section_data, categories_data = menu_section_data_for(menu_item)
      menu_item_data = menu_item_data_for(menu_item)

      serving_sizes = menu_item.serving_sizes.where(archived_at: nil).order(:weight)
      menu_extra_sections = menu_item.menu_extra_sections.where(archived_at: nil).order(:weight)
      menu_extras = menu_extra_sections.map{|menu_extra_section| menu_extra_section.menu_extras.where(archived_at: nil).order(:weight) }.flatten(1)

      case
      when serving_sizes.blank? && menu_extras.blank?
        csv << row + [''] + [''] + menu_section_data + menu_item_data + SERVING_SIZE_FIELDS.map{|_| '' } + EXTRAS_FIELDS.map{|_| '' } + categories_data
      when serving_sizes.present? # create new row for each serving sizes
        serving_sizes.order(:weight).each do |serving_size|
          csv << row + [serving_size.id] + [''] + menu_section_data + menu_item_data + SERVING_SIZE_FIELDS.map{|field| serving_size.send(field) } + EXTRAS_FIELDS.map{|_| '' } + categories_data
        end
      when menu_extras.present? # create new row for each menu extra
        menu_extras.each do |menu_extra|
          csv << row + [''] + [menu_extra.id] + menu_section_data + menu_item_data + SERVING_SIZE_FIELDS.map{|_| '' } + EXTRAS_FIELDS.map{|field| menu_extra.send(field) } + categories_data
        end
      end
    end
    csv
  end

  def ordered_categories
    @_ordered_categories ||= Category.where(show_in_menu: true).order(:id)
  end

  def menu_section_data_for(menu_item)
    menu_section_data = []
    categories_data = []
    if menu_item.menu_section.blank?
      menu_section_data << ''
      ordered_categories.count.times do
        categories_data << false
      end
    else
      menu_section_data += menu_item.menu_section.attributes.values_at(*SECTION_FIELDS)
      ordered_categories.each do |category|
        categories_data << menu_item.menu_section.categories.include?(category)
      end
    end
    [menu_section_data, categories_data]
  end

  def menu_item_data_for(menu_item)
    ITEM_FIELDS.map do |field|
      menu_item.send(field)
    end
  end

  def sorted_menu_items
    menu_items = supplier.menu_items
    menu_items = menu_items.where(menu_items: { archived_at: nil })
    menu_items = menu_items.joins(:menu_section)
    menu_items = menu_items.order('menu_sections.id, menu_sections.weight, menu_sections.created_at, menu_items.weight')
    menu_items
  end

  class Result
    attr_accessor :csv_data, :errors
    attr_reader :supplier

    def initialize(supplier:)
      @supplier = supplier
      @csv_data = nil
      @errors = []
    end

    def success?
      errors.blank? && csv_data.present?
    end
  end
end
