class Suppliers::ListForDeliverableSuburb

  def initialize(suburb: nil, order_date: nil, scoped_suppliers: nil)
    @suburb = suburb
    @order_date = order_date
    @suppliers = scoped_suppliers.presence || SupplierProfile.all
  end

  def call
    filter_by_deliverable_suburbs
    filter_by_order_date if order_date.present?
    suppliers
  end

private

  attr_reader :suppliers, :order_date, :suburb

  def filter_by_deliverable_suburbs
    @suppliers = suppliers.joins(:deliverable_suburbs).where(deliverable_suburbs: { suburb: suburb })
  end

  def filter_by_order_date
    zone_arel = DeliveryZone.arel_table
    wday = Time.zone.parse(order_date.to_s).wday
    required_operating_days = 7.times.map{|num| num == wday ? '1' : '_' }.join('')
    operating_date_condition = zone_arel[:operating_wdays].matches(required_operating_days)
    @suppliers = suppliers.joins(:delivery_zones).where(operating_date_condition)
  end

end


