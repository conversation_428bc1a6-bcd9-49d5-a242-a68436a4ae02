class Suppliers::Update

  def initialize(supplier:, supplier_params: {}, supplier_flag_params: {})
    @supplier = supplier
    @supplier_params = supplier_params
    @supplier_flag_params = supplier_flag_params
    @result = Result.new
  end

  def call
    if can_update?
      update_supplier
      update_supplier_flags
    end

    result
  end

private

  attr_reader :supplier, :supplier_params, :supplier_flag_params, :result

  def can_update?
    case
    when supplier.blank?
      result.errors << 'Cannot update without a supplier'
    when supplier_params.blank? && supplier_flag_params.blank?
      result.errors << 'Cannot update without update params'
    end
    result.errors.blank?
  end

  def update_supplier
    return if supplier_params.blank?

    if supplier.update(supplier_params)
      result.supplier = supplier
      if changed_supplier_fields.include?(:is_searchable)
        notify_activation_change
        mark_menu_as_updated
      end
      reset_minimums if changed_supplier_fields.include?(:lead_mode)
      update_future_orders if (%i[markup commission_rate] & changed_supplier_fields).present?
      notify_bank_details_changed if (%i[bsb_number bank_account_number] & changed_supplier_fields).present?
      result.updated_fields = changed_supplier_fields
    else
      result.errors += supplier.errors.full_messages
    end
  end

  def update_supplier_flags
    return if supplier_flag_params.blank?

    if (supplier_flags = supplier.supplier_flags.presence)
      if supplier_flags.update(supplier_flag_params)
        result.supplier = supplier
      else
        result.errors += supplier_flag_params.errors.full_messages
      end
    else
      result.errors << 'Supplier does not have any supplier flags'
    end
  end

  def notify_activation_change
    EventLogs::Create.new(event_object: supplier, event: 'searchable-updated', is_searchable: supplier.is_searchable).call
  end

  def mark_menu_as_updated
    supplier.supplier_flags.update(menu_last_updated_on: Time.zone.now)
  end

  def reset_minimums
    supplier.minimums.destroy_all
  end

  def update_future_orders
    EventLogs::Create.new(event_object: supplier, event: 'margin-updated', markup: supplier.markup&.round(2).to_s, commission_rate: supplier.commission_rate&.round(2).to_s).delay(queue: :notifications).call

    update_handler = "%object:Suppliers::UpdateFutureOrders%value_before_type_cast: #{supplier.id}%method_name: :call%"
    return if Delayed::Job.where('handler ilike ?', update_handler).where(locked_at: nil, failed_at: nil).present?

    Suppliers::UpdateFutureOrders.new(supplier: supplier).delay(queue: :data_integrity).call
  end

  def notify_bank_details_changed
    previous_bank_details = {
      bsb_number: supplier.previous_changes[:bsb_number]&.first || 'no-changes',
      bank_account_number: supplier.previous_changes[:bank_account_number]&.first || 'no-changes'
    }
    Admin::Emails::SendBankDetailChangedEmail.new(supplier: supplier, previous_details: previous_bank_details).delay(queue: :notifications).call
  end

  def changed_supplier_fields
    @_changed_supplier_fields = supplier.previous_changes.keys.map(&:to_sym)
  end

  class Result
    attr_accessor :supplier, :updated_fields, :errors

    def initialize
      @supplier = nil
      @updated_fields = []
      @errors = []
    end

    def success?
      errors.blank? && supplier.present?
    end
  end

end