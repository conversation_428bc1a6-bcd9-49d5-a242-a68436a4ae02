class Suppliers::FetchPotentialDeliveryZone

  def initialize(supplier:, suburb:, delivery_date: nil)
    @supplier = supplier
    @delivery_date = delivery_date
    @suburb = suburb
  end

  def call
    case
    when delivery_date.present? && has_operational_suburb_delivery_zone?
      suburb_delivery_zone
    when delivery_date.present? && available_deliverable_suburbs.present?
      nearest_deliverable_zone_for(available_deliverable_suburbs)
    when suburb_delivery_zone.present?
      suburb_delivery_zone
    else
      nearest_deliverable_zone_for(all_deliverable_suburbs)
    end
  end

private

  attr_reader :supplier, :suburb, :delivery_date

  def suburb_delivery_zone
    @_suburb_delivery_zone ||= supplier.delivery_zones.where(suburb: suburb).order(radius: :asc, delivery_fee: :asc).first
  end

  def has_operational_suburb_delivery_zone?
    return false if suburb_delivery_zone.blank? || suburb_delivery_zone.operating_wdays.blank?

    suburb_delivery_zone.operating_wdays.match?(/#{required_operating_days}/)
  end

  def all_deliverable_suburbs
    @_all_deliverable_suburbs = supplier.deliverable_suburbs.where(suburb: suburb).includes(:delivery_zone).order('distance DESC')
  end

  def required_operating_days(delimiter = '.')
    return nil if delivery_date.blank?

    wday = Time.zone.parse(delivery_date.to_s).wday
    @_required_operating_days = 7.times.map{|num| num == wday ? '1' : delimiter }.join('')
  end

  def available_deliverable_suburbs
    return @_available_deliverable_suburbs if @_available_deliverable_suburbs.present?

    zone_arel = DeliveryZone.arel_table
    operating_date_condition = zone_arel[:operating_wdays].matches(required_operating_days('_'))
    @_available_deliverable_suburbs = all_deliverable_suburbs.joins(:delivery_zone).where(operating_date_condition)
  end

  def nearest_deliverable_zone_for(deliverable_suburbs)
    min_distance = deliverable_suburbs.map(&:distance).min
    nearest_deliverable_suburbs = deliverable_suburbs.select do |deliverable_suburb|
      deliverable_suburb.distance == min_distance
    end
    nearest_deliverable_suburbs.map(&:delivery_zone).sort_by do |delivery_zone|
      [delivery_zone.radius, delivery_zone.delivery_fee]
    end.first
  end

end
