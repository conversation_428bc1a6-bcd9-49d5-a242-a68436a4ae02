class Suppliers::Notifications::SendFinalClosureCorrectionEmails

  def initialize(closure_orders: [])
    @closure_orders = closure_orders.presence || fetch_closure_orders
    @result = Result.new
  end

  def call
    supplier_grouped_closure_orders.each do |supplier, supplier_orders|
      email_sender = Suppliers::Emails::SendFinalClosureCorrectionEmail.new(supplier: supplier, orders: supplier_orders).call
      if email_sender.success?
        result.sent_notifications << email_sender.sent_notification
      else
        result.errors += email_sender.errors
      end
    end
    result
  end

private

  attr_reader :closure_orders, :result

  def fetch_closure_orders
    orders = Order.where(status: 'skipped')
    orders = orders.joins(:supplier_profiles).where('orders.delivery_at BETWEEN supplier_profiles.close_from AND supplier_profiles.close_to')
    orders = orders.where.not(order_type: 'one-off')
    orders.order(:delivery_at).includes(:supplier_profiles)
  end

  def supplier_grouped_closure_orders
    grouped_orders = {}
    closure_orders.group_by(&:supplier_profiles).each do |supplier_profiles, supplier_orders|
      supplier_profiles.each do |supplier|
        grouped_orders[supplier] ||= []
        grouped_orders[supplier] += supplier_orders
      end
    end
    grouped_orders
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
