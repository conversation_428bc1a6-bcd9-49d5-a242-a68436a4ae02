# to be run once every hour as it detects if the order lead time is within the current hour
class Suppliers::Notifications::SendRecurringOrderReminders
  DELIVERY_THRESHOLD = 5.days.freeze

  def initialize(time: nil, verbose: false)
    @time = time.presence || Time.zone.now
    @verbose = verbose
    @result = Result.new
  end

  def call
    pending_orders.each do |order|
      next if !can_notify_suppliers_for(order)

      puts "Notifying suppliers of order ##{order.id}" if verbose
      notifiable_suppliers_for(order).each do |supplier|
        Suppliers::Emails::SendRecurringOrderReminderEmail.new(order: order, supplier: supplier).call
        result.notified_suppliers << supplier
      end
      result.notified_orders << order
    end
    result
  end

private

  attr_reader :time, :result, :verbose

  def pending_orders
    pending_orders = Order.where(order_type: 'recurrent', status: %w[new confirmed amended])
    pending_orders = pending_orders.where('delivery_at between ? and ?', hour_start, (time + DELIVERY_THRESHOLD))
    pending_orders = pending_orders.joins(supplier_profiles: :supplier_flags).where(supplier_flags: { needs_recurring_reminder: true })
    pending_orders.distinct
  end

  def can_notify_suppliers_for(order)
    lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call
    lead_time_fetcher.lead_time.between?(hour_start, hour_end)
  end

  def hour_start
    time.beginning_of_hour
  end

  def hour_end
    time.end_of_hour
  end

  def notifiable_suppliers_for(order)
    order.supplier_profiles.joins(:supplier_flags).where(supplier_flags: { needs_recurring_reminder: true })
  end

  class Result
    attr_accessor :notified_orders, :notified_suppliers

    def initialize
      @notified_orders = []
      @notified_suppliers = []
    end
  end

end