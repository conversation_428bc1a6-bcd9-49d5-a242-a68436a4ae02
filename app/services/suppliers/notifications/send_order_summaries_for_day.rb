class Suppliers::Notifications::SendOrderSummariesForDay

  def initialize(summary_day: Time.zone.now, summary_type: 'daily')
    @summary_day = summary_day
    @summary_type = summary_type
    @result = Result.new
  end

  def call
    order_lines_for(day: summary_day).group_by(&:supplier_profile).each do |supplier, supplier_order_lines|
      begin
        generate_and_send_summary_pdf_for(supplier: supplier, supplier_order_lines: supplier_order_lines)
      rescue => exception
        error_message = "Could not send Order Summary Notifications to supplier #{supplier.name} - #{summary_day}"
        Raven.capture_exception(exception,
          message: error_message,
          extra: { supplier_id: supplier.id, summary_day: summary_day },
          transaction: 'Suppliers::Notifications::SendOrderSummariesForDay'
        )
        result.errors << error_message
      end
    end
    result
  end

private

  attr_reader :summary_day, :summary_type, :result

  def order_lines_for(day:, supplier: nil)
    order_lines = OrderLine.where(status: order_line_statuses)
    order_lines = order_lines.joins(:order).where(orders: { status: order_statuses, delivery_at: (day.beginning_of_day..day.end_of_day) })
    order_lines = order_lines.where(orders: { pattern: %w[2.weeks 1.month 4.weeks] }) if summary_type == 'reminder'
    order_lines = order_lines.where(supplier_profile: supplier) if supplier.present?
    order_lines = order_lines.where(orders: { split_order_id: nil })
    order_lines.order('orders.delivery_at asc, order_lines.created_at asc')
  end

  def generate_and_send_summary_pdf_for(supplier:, supplier_order_lines:)
    case
    when summary_type == 'reminder'
      email_sender = Suppliers::Emails::SendOrdersReminderEmail.new(supplier: supplier, order_lines: supplier_order_lines, summary_day: summary_day).call
    when summary_type == 'morning' && summary_day.wday == 5 && supplier.needs_multi_day_summary
      monday = summary_day + 3.days
      monday_order_lines = order_lines_for(day: monday, supplier: supplier)
      email_sender = Suppliers::Emails::SendOrdersSummaryEmail.new(supplier: supplier, order_lines: (supplier_order_lines + monday_order_lines), summary_day: summary_day, summary_type: summary_type).call
    else
      email_sender = Suppliers::Emails::SendOrdersSummaryEmail.new(supplier: supplier, order_lines: supplier_order_lines, summary_day: summary_day, summary_type: summary_type).call
    end
    if email_sender.success?
      result.sent_notifications << email_sender.sent_notification
    else
      result.errors += email_sender.errors
    end
  end

  def order_statuses
    return @_order_statuses if @_order_statuses.present?

    statuses = ['confirmed']
    statuses += %w[new amended] if %w[morning reminder].include?(summary_type)
    @_order_statuses = statuses
  end

  def order_line_statuses
    return @_order_line_statuses if @_order_line_statuses.present?

    statuses = ['accepted']
    statuses += %w[pending amended notified] if %w[morning reminder].include?(summary_type)
    @_order_line_statusess = statuses
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
