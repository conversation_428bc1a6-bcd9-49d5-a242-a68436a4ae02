class Suppliers::Notifications::SendMenuReminders

	def initialize(time: Time.zone.now)
		@time = time
		@notified_suppliers = []
	end

	def call
		notifiable_suppliers.each do |supplier|
			send_reminder_to(supplier) if can_notify?(supplier)
		end
		notified_suppliers
	end

private

	attr_reader :time, :notified_suppliers

	def can_notify?(supplier)
		last_updated_on = supplier.menu_last_updated_on
		return if last_updated_on.to_date == time.to_date

		case supplier.menu_reminder_frequency
		when 'monthly'
			last_updated_on.day == time.day
		when '3.months'
			last_updated_on.day == time.day && (last_updated_on.month % 3) == (time.month % 3)
		when '6.months'
			 last_updated_on.day == time.day && (last_updated_on.month % 6) == (time.month % 6)
		else
			false
		end
	end

	def send_reminder_to(supplier)
		Suppliers::Emails::SendMenuReminderEmail.new(supplier: supplier, time: time).call
		@notified_suppliers << supplier
	end

	def notifiable_suppliers
		suppliers = SupplierProfile.where(is_searchable: true).joins(:supplier_flags)
		suppliers = suppliers.where.not(supplier_flags: { menu_reminder_frequency: [nil, ''] })
		suppliers = suppliers.where.not(supplier_flags: { menu_last_updated_on: nil })
		suppliers.order('supplier_flags.menu_last_updated_on ASC')
	end

end