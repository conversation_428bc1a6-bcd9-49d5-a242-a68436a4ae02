class Suppliers::Notifications::SendInitialClosureEmails

  def initialize
    @result = Result.new
  end

  def call
    searchable_suppliers.each do |supplier|
      email_sender = Suppliers::Emails::SendInitialClosureEmail.new(supplier: supplier).call
      if email_sender.success?
        result.sent_notifications << email_sender.sent_notification
      else
        result.errors += email_sender.errors
      end
    end
    result
  end

private

  attr_reader :result

  def searchable_suppliers
    SupplierProfile.where(is_searchable: true)
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
