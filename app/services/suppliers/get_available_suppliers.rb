class Suppliers::GetAvailableSuppliers

  def initialize(supplier_ids:, delivery_date:, suburb:)
    @supplier_ids = supplier_ids
    @delivery_date = delivery_date
    @suburb = suburb
    @result = Result.new(scoped_suppliers: scoped_suppliers)
  end

  def call
    result.available_suppliers = suppliers_within_cutoff & suppliers_available_operating_day
    result
  end

private

  attr_reader :supplier_ids, :delivery_date, :suburb, :result

  def scoped_suppliers
    @_scoped_suppliers ||= SupplierProfile.where(id: supplier_ids)
  end

  def suppliers_within_cutoff
    cutoff_suppliers = []
    scoped_suppliers.each do |supplier|
      cutoff_hours_fetcher = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_date, suburb: suburb).call
      cutoff_suppliers << supplier if cutoff_hours_fetcher.hours_remaining.present? && cutoff_hours_fetcher.hours_remaining >= 0
    end
    cutoff_suppliers
  end

  def suppliers_available_operating_day
    Suppliers::ListForDeliverableSuburb.new(suburb: suburb, order_date: delivery_date, scoped_suppliers: scoped_suppliers).call.distinct
  end

  class Result
    attr_reader :scoped_suppliers
    attr_accessor :available_suppliers

    def initialize(scoped_suppliers:)
      @scoped_suppliers = scoped_suppliers
      @available_suppliers = scoped_suppliers
    end

    def unavailable_suppliers
      scoped_suppliers - available_suppliers
    end
  end

end
