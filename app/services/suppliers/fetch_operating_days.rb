class Suppliers::FetchOperatingDays

  WEEKDAYS = %w[<PERSON> Mon Tue Wed Thur Fri Sat].freeze

  def initialize(supplier:, delivery_zone: nil, calculate: false)
    @supplier = supplier
    @delivery_zone = delivery_zone
    @calculate = calculate
  end

  def call
    case
    when supplier.present? && supplier.operating_days.present? && !calculate
      supplier.operating_days
    when delivery_zone.present?
      delivery_zone_based_operating_days
    else
      nil
    end
  end

private

  attr_reader :supplier, :delivery_zone, :calculate

  def operating_weekdays
    @_operating_weekdays ||= delivery_zone.operating_wdays.present? ? delivery_zone.operating_wdays : '0000000'
  end

  def operating_days
    @_operating_days = operating_weekdays.split(//)
  end

  def delivery_zone_based_operating_days
    working_days = []
    if operating_weekdays.to_s == '1111111'
      working_days = '7 days a week'
    else
      last_days = ''
      operating_days.each_with_index do |day, idx|
        if day != '1'
          working_days << last_days if last_days != ''
          last_days = ''
          next
        end
        if (operating_days[idx + 1].present? && operating_days[idx + 1] != '0') || ((idx - 1) != -1 && operating_days[idx - 1].present? && operating_days[idx - 1] != '0')
          if last_days == ''
            last_days = WEEKDAYS[idx]
          else
            last_days = "#{last_days.split('-').first}-#{WEEKDAYS[idx]}"
          end
          working_days << last_days if idx == 6 && last_days != ''
        else
          working_days << last_days if last_days != ''
          working_days << WEEKDAYS[idx]
          last_days = ''
        end
      end
      working_days = working_days.size == 1 ? working_days.first : working_days
    end
    working_days.is_a?(Array) ? working_days.join(', ') : working_days
  end
end
