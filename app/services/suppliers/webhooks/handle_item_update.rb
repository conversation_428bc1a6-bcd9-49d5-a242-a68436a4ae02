class Suppliers::Webhooks::HandleItemUpdate

  NAME_CHANGE_THRESHOLD = 30
  NAME_MISMATCH_THRESHOLD = 75
  UPDATABLE_FIELDS = %i[stock_quantity].freeze

  def initialize(supplier:, item_params: {})
    @supplier = supplier
    @item_params = item_params.present? ? item_params.to_h.symbolize_keys : nil

    @result = Result.new
  end

  def call
    if can_handle_update? && item.update(sanitized_item_params)
      result.item = item
    end
    notify_developer if yordar_credentials(:api_item_update, :notify_developer)
    result
  end

private

  attr_reader :supplier, :item_params, :result

  def can_handle_update?
    case
    when supplier.blank?
      result.errors << 'Supplier is missing'
    when item_params.blank?
      result.errors << 'Cannot update without data'
    when item.blank?
      result.errors << 'Could not find Item using SKU/Name'
    end
    result.errors.blank?
  end

  def item
    @_item ||= Suppliers::Webhooks::FetchItem.new(supplier: supplier, webhook_params: item_params).call
  end

  def sanitized_item_params
    [
      item_params.slice(*UPDATABLE_FIELDS),
      name_params
    ].inject(&:merge)
  end

  def name_params
    return {} if name_mismatch_percentage == 0 || name_mismatch_percentage > NAME_CHANGE_THRESHOLD

    {
      name: item_params[:name]
    }
  end

  def notify_developer
    slack_notifier = Suppliers::Webhooks::NotifyViaSlack.new(message: ':hook: *Yordar External Webhook*: An item was marked to be updated')

    if supplier.present? && item_params.present?
      slack_notifier.add_attachment(
        text: "Update Params for *#{supplier.name}* => #{item_params.to_h.inspect}",
        kind: 'info'
      )
      if item.blank?
         slack_notifier.add_attachment(
           text: "Could not find object with SKU: #{item_params[:sku]} or Name: #{item_params[:name]}",
           kind: 'error'
         )
      else
        slack_notifier.add_attachment(
          text: "Found #{item.class.name} => ID: #{item.id}",
          kind: 'info'
        )
        if item.archived_at.present?
          slack_notifier.add_attachment(
            text: 'The Item is Archived on Yordar\'s end',
            kind: 'warning'
          )
        end
        if item.sku&.to_s != item_params[:sku]&.to_s
          slack_notifier.add_attachment(
            text: "SKU Mismatch => Stored: '#{item.sku}' vs Passed: '#{item_params[:sku]}'",
            kind: 'warning'
          )
        end
        if name_mismatch_percentage >= NAME_MISMATCH_THRESHOLD
          slack_notifier.add_attachment(
            text: "Name Mismatch => Stored: '#{item.name}' vs Passed: '#{item_params[:name]}'",
            kind: 'warning'
          )
        end
        if item_params[:stock_quantity].present?
          slack_notifier.add_attachment(
            text: "Stock Quantity: #{item_params[:stock_quantity]}",
            kind: 'info'
          )
        end
      end
    elsif supplier.present?
      slack_notifier.add_attachment(
        text: "No params passed for #{supplier.name}'s' item",
        kind: 'error'
      )
    end
    slack_notifier.notify
  end

  def name_mismatch_percentage
    return 0 if item_params[:name].blank? || item.name == item_params[:name]

    @_name_mismatch_percentage ||= begin
      sanitized_item_name = item.name.downcase.strip
      sanitized_api_name = item_params[:name].downcase.strip

      distance = Utility::LevenshteinDistance.new(str1: sanitized_item_name, str2: sanitized_api_name).call
      max_length = [sanitized_item_name.size, sanitized_api_name.size].max

      (distance / max_length.to_f * 100).to_i
    end
  end

  class Result
    attr_accessor :item, :errors

    def initialize
      @item = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end


