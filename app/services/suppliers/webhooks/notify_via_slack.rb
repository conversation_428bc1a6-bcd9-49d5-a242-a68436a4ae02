class Suppliers::Webhooks::NotifyViaSlack

  def initialize(message:)
    @message = message
    @attachments = []
  end

  def add_attachment(text:, kind:)
    @attachments << {
      type: 'mrkdwn',
      text: text,
      color: kind,
    }
  end

  def notify
    return if attachments.blank?

    SlackNotifier.send(message, attachments: attachments)
  end

private

  attr_reader :message, :attachments

end