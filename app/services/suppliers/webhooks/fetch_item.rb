class Suppliers::Webhooks::FetchItem

  ITEM_ASSOCIATIONS = %i[menu_items serving_sizes].freeze

  def initialize(supplier:, webhook_params:)
    @supplier = supplier
    @params = webhook_params
  end

  def call
    if params.present?
      ITEM_ASSOCIATIONS.each do |association|
        items = supplier.send(association)
        @item = find_by_sku_within(items).presence || find_by_name_within(items)
        break if @item.present?
      end
    end
    @item
  end

private

  attr_reader :supplier, :params

  def find_by_sku_within(items)
    return nil if params[:sku].blank?

    items.where(sku: params[:sku]).first
  end

  def find_by_name_within(items)
    return nil if params[:name].blank?

    items.where(name: params[:name].strip).first
  end

end