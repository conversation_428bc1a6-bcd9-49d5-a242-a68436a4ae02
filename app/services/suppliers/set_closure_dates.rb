class Suppliers::SetClosureDates

  def initialize(close_from: nil, close_to: nil, suppliers: [], verbose: false)
    @close_from = close_from.present? ? close_from.beginning_of_day : Time.zone.parse(yordar_credentials(:yordar, :closure_start_date)).beginning_of_day
    @close_to = close_to.present? ? close_to.end_of_day : Time.zone.parse(yordar_credentials(:yordar, :closure_end_date)).end_of_day
    @suppliers = suppliers.presence || searchable_suppliers
    @verbose = verbose
    @result = Result.new
  end

  def call
    suppliers.each do |supplier|
      if supplier.update(
        close_from: close_from,
        close_to: close_to
      )
        result.closure_suppliers << supplier
        print 's-' if verbose
      else
        result.errors << "Could not set closure dates for supplier ##{supplier.id} => #{supplier.errors.full_messages.join(', ')}"
      end
    end
    result
  end

private

  attr_reader :close_from, :close_to, :suppliers, :verbose, :result

  def searchable_suppliers
    SupplierProfile.where(is_searchable: true)
  end

  class Result
    attr_accessor :closure_suppliers, :errors

    def initialize
      @closure_suppliers = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
