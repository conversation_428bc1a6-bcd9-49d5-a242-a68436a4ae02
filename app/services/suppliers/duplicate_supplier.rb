# feature used in Rails Admin
class Suppliers::DuplicateSupplier

  def initialize(supplier:)
    @supplier = supplier
    @result = Result.new(supplier: supplier)
  end

  def call
    begin
      duplicate_supplier_record
      duplicate_menu
      duplicate_delivery_zones
    rescue => exception
      error_message = "Unable to duplicate supplier #{supplier.id} #{exception.message}"
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :duplicate_supplier, :result

  def duplicate_supplier_record
    @duplicate_supplier = supplier.dup
    duplicate_supplier.company_name = "Copy - #{duplicate_supplier.company_name}"
    duplicate_supplier.email = nil # remove email from the profile (user is also not duped as part of this)
    duplicate_supplier.is_searchable = false
    if duplicate_supplier.save
      result.duplicate_supplier = duplicate_supplier
      duplicate_user
    end
  end

  def duplicate_user
    user = supplier.user.dup

    email_hex = SecureRandom.hex(2)
    user.email = "copy#{email_hex}_#{user.email.split('@').first}@yordar.com.au"
    user.firstname = "Copy - #{user.firstname}"
    user.lastname = "Copy - #{user.lastname}"
    user.admin = false

    password_hex = SecureRandom.hex(10) # give it a random password
    user.password = password_hex
    user.password_confirmation = password_hex

    user.reset_password_token = nil
    user.reset_password_sent_at = nil
    if user.save!
      # attach duplicated supplier to user
      profile = user.build_profile(profileable: duplicate_supplier)
      profile.save
    end
  end

  def duplicate_menu
    Suppliers::CloneSupplierMenu.new(old_supplier: supplier, new_supplier: duplicate_supplier).call
  end

  def duplicate_delivery_zones
    delivery_zones = supplier.delivery_zones
    delivery_zones.each do |delivery_zone|
      duplicate_zone = delivery_zone.dup
      duplicate_zone.supplier_profile_id = duplicate_supplier.id
      duplicate_zone.save
    end
  end

  class Result
    attr_accessor :supplier, :duplicate_supplier, :errors

    def initialize(supplier:)
      @supplier = supplier
      @duplicate_supplier = nil
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
