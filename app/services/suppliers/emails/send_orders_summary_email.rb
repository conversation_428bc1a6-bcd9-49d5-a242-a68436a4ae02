class Suppliers::Emails::SendOrdersSummaryEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'supplier-order_summary'.freeze
  ORDERS_THRESHOLD = 20

  def initialize(supplier:, order_lines:, summary_day:, summary_type:)
    @notifying_account = @supplier = supplier
    @order_lines = order_lines
    @summary_day = summary_day
    @summary_type = summary_type
    @attachments = []
    @template_name = EMAIL_TEMPLATE
    @notification_variation = summary_type
    @result = Result.new
  end

  def call
    return result if !preferred_notification?

    begin
      generate_documents
      send_email
    rescue => exception
      error_message = "Failed to send orders summary email to supplier #{supplier.id} - #{summary_day} - #{summary_type}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, summary_day: summary_day, summary_type: summary_type })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :order_lines, :summary_day, :summary_type, :summary_documents, :attachments, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Sent orders summary email to supplier #{supplier.id} - #{summary_day} - #{summary_type}"
      result.sent_notification = email_sender.email
    else
      result.errors = email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: supplier.email_recipient)
  end

  def email_subject
    case summary_type
    when 'morning'
      'YORDAR: Your daily morning order summary'
    else
      'YORDAR: Your daily order summary'
    end
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      company_name: supplier.company_name,

      order_dates: deep_struct(date_grouped_orders_data),
      summary_documents: deep_struct(summary_documents.compact),

      header_color: :cream,
    }
  end

  def generate_documents
    @summary_documents = []
    date_grouped_order_lines.each do |date, dated_order_lines|
      document = Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: dated_order_lines, summary_day: date, summary_type: summary_type).call
      next if document.blank?

      attachments << document
      @summary_documents << {
        date: date.to_s(:full_date),
        day: date.to_s(:weekday),
        pdf_url: document.url,
      }
    end
  end

  def date_grouped_orders_data
    date_grouped_order_lines.map do |date, dated_order_lines|
      document = summary_documents.detect{|summary_document| summary_document[:date] == date.to_s(:full_date) }
      summary_url = (document.present? ? document[:pdf_url] : nil)

      order_grouped_order_lines = dated_order_lines.group_by(&:order)
      sorted_orders = order_grouped_order_lines.sort_by{|order, _| [order.customer_profile.customer_or_company_name.downcase, order.delivery_at] }

      remaining_orders = (sorted_orders.size - ORDERS_THRESHOLD)
      remaining_orders = 0 if remaining_orders < 0
      {
        date: date.to_s(:full_date),
        summary_url: summary_url,
        total_orders: sorted_orders.size,
        remaining_orders: remaining_orders,
        orders: orders_data_from(sorted_orders.first(ORDERS_THRESHOLD))
      }
    end
  end

  def orders_data_from(sorted_orders)
    sorted_orders.map do |order, _|
      {
        id: order.id,
        name: order.name,
        is_recurrent: order.is_recurrent?,
        customer_name: order.customer_profile.customer_or_company_name,
        delivery_time: order.delivery_at.to_s(:time_only),
      }
    end
  end

  def date_grouped_order_lines
    @_date_grouped_orders ||= order_lines.group_by{|order_line| order_line.order.delivery_at.to_date }.sort_by{|date, _| date }
  end

  def email_salutation
    super(default: supplier.email_salutation)
  end

  def email_ref
    reference = "supplier-orders_summary-#{summary_day.to_s(:date_compact)}"
    reference += "-#{summary_type}"
    reference
  end

end
