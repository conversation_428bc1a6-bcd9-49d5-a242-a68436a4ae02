class Suppliers::Emails::SendTeamOrderCutoffEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'supplier-team_order_cutoff'.freeze

  def initialize(team_order:, supplier:, cutoff_time:)
    @team_order = team_order
    @supplier = supplier
    @cutoff_time = cutoff_time
    @attachments = []
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send team order #{cutoff_time} cutoff email to supplier #{supplier.id} - ##{team_order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { team_order_id: team_order.id, supplier_id: supplier.id, cutoff_time: cutoff_time })
    end
  end

private

  attr_reader :team_order, :supplier, :cutoff_time, :attachments

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      mark_supplier_notified
      Rails.logger.info "Team order #{cutoff_time} cutoff email sent to supplier #{supplier.id} - ##{team_order.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    case cutoff_time
    when '4hr'
      "YORDAR: The team order ##{team_order.id} is now close to cut-off - 4hrs remaining"
    else # 'day'
      "YORDAR: The team order ##{team_order.id} will be ready soon"
    end
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      team_order: deep_struct(team_order_data),
      cutoff_time: cutoff_time,
      cutoff_datetime: cutoff_datetime.present? && cutoff_datetime.to_s(:full_verbose),
      header_color: :purple
    }
  end

  def team_order_data
    customer_name = team_order.company_name.presence || team_order.customer_profile.company_name.presence || team_order.customer_profile.name
    {
      id: team_order.id,
      customer_name: customer_name,
      number_of_invitees: invited_team_order_attendees.count,
      number_of_confirmed_orders: invited_team_order_attendees.select{|attendee| attendee.status == 'ordered' }.size,
      total: team_order_supplier.total.present? ? number_to_currency(team_order_supplier.total) : 'tbd',
      delivery_at: team_order.delivery_at.to_s(:full_verbose),
      delivery_address: team_order.delivery_address_arr.join('<br>'),
      view_url: url_helper.supplier_order_show_url(team_order, host: app_host),
    }
  end

  def cutoff_datetime
    @_cutoff_datetime ||= Orders::FetchLeadTime.new(order: team_order).call.lead_time
  end

  def team_order_supplier
    @_team_order_supplier ||= team_order.order_suppliers.where(supplier_profile: supplier).first
  end

  def invited_team_order_attendees
    @_invited_team_order_attendees ||= team_order.team_order_attendees.where.not(status: %w[cancelled declined])
  end

  def mark_supplier_notified
    reminder_attribute = "cutoff_#{cutoff_time}_reminder"
    team_order_supplier.update(Hash[reminder_attribute.to_sym, Time.zone.now])
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{cutoff_time}-#{team_order.id}-#{supplier.id}"
  end
end
