class Suppliers::Emails::SendPublicHolidayOrdersEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-order_skipped'.freeze

  def initialize(supplier:, holiday:, handled_orders: [])
    @supplier = supplier
    @holiday = holiday
    @handled_orders = handled_orders
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send handled public holiday notification email to supplier #{supplier.id} - #{holiday.name}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, holiday_id: holiday.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :holiday, :handled_orders, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent handled public holiday notification email to supplier #{supplier.id} - #{holiday.name}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: The following orders have been skipped/moved for upcoming public holiday'
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,

      holiday: deep_struct(holiday_data),
      handled_orders: deep_struct(handled_orders_data),

      header_color: :cream
    }
  end

  def holiday_data
    {
      id: holiday.id,
      name: holiday.name,
      date:  holiday.on_date.to_s(:full_date)
    }
  end

  def handled_orders_data
    handled_orders.map do |order|
      {
        id: order.id,
        link: 'order_link',
        name: order.name.truncate(30, omission: '...'),
        customer_name: order.customer_profile.customer_or_company_name.truncate(20, omission: '...'),
        delivery_at: order.delivery_at.to_s(:full_date),
      }
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{holiday.on_date}-#{supplier.id}"
  end

end

