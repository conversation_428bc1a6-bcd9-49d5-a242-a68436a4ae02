class Suppliers::Emails::SendNewTeamOrderEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-new_team_order'.freeze

  def initialize(supplier:, team_order:)
    @supplier = supplier
    @team_order = team_order
    @attachments = []
  end

  def call
    begin
      generate_documents
      send_email
    rescue => exception
      error_message = "Failed to send new team order email to supplier #{supplier.id}- ##{team_order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { team_order_id: team_order.id, supplier_id: supplier.id })
    end
  end

private

  attr_reader :supplier, :team_order, :attachments

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "New team order email sent to supplier #{supplier.id} - ##{team_order.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: You have a new team order - ##{team_order.id} (Ver.#{@details_document&.version})"
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      has_commission: supplier.commission_rate > 0.0,

      team_order: deep_struct(team_order_data),

      header_color: :cream
    }
  end

  def team_order_data
    customer_name = team_order.customer_profile&.customer_or_company_name || ''
    {
      id: team_order.id,
      date: team_order.delivery_at.to_s(:full),
      customer_name: customer_name,
      has_attendee_levels: team_order.team_order_levels.present?,

      pdf_url: @details_document&.url,
      avery_labels_url: @avery_document&.url,
      flex_url: @flex_document&.url,
      view_url: url_helper.supplier_order_show_url(team_order, host: app_host),
      confirm_url: order_mangement_urls(mode: 'accepted'),
      reject_url: order_mangement_urls(mode: 'rejected'),
    }
  end

  def order_mangement_urls(mode:)
    path = Rails.application.routes.url_helpers.order_confirm_or_reject_path(order_id: team_order.id, profile_type: 'supplier', profile_id: supplier.id, mode: mode, hashed_value: md5_hash)
    url_shortner = Shortener::ShortenedUrl.generate(path)
    url_helper.shortened_url(url_shortner.unique_key, host: app_host)
  end

  def md5_hash
    @_md5_hash ||= Digest::MD5.hexdigest(team_order.id.to_s + supplier.id.to_s + yordar_credentials(:random_salt))
  end

  def generate_documents
    generate_team_order_detail
    generate_team_order_delivery_detail
    generate_avery_label_csv
    generate_flex_json if supplier.uses_flex_catering
  end

  def generate_team_order_detail
    @details_document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: "team-order-#{team_order.id}").call
    if @details_document.present?
      attachments << @details_document
    end
  end

  def generate_team_order_delivery_detail
    delivery_details_document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: "team-order-#{team_order.id}", variation: 'delivery_docket').call
    if delivery_details_document.present?
      attachments << delivery_details_document
    end
  end

  def generate_avery_label_csv
    @avery_document = Documents::Generate::TeamOrderAveryLabels.new(team_order: team_order, supplier: supplier, reference: "team-order-#{team_order.id}").call
    if @avery_document.present?
      attachments << @avery_document
    end
  end

  def generate_flex_json
    @flex_document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: "team-order-#{team_order.id}", variation: 'json').call
    if @flex_document.present?
      attachments << @flex_document
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}"
  end

end

