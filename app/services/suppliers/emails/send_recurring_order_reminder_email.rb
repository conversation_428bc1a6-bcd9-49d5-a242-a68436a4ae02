class Suppliers::Emails::SendRecurringOrderReminderEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-recurring_order_reminder'.freeze

  def initialize(order:, supplier:)
    @order = order
    @supplier = supplier
  end

  def call
    return if order.blank? || supplier.blank?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send recurring order reminder email to supplier #{supplier.id} - #{order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, order_id: order.id })
    end
  end

private

  attr_reader :order, :supplier

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments_data
    ).call
    if email_sender.success?
      Rails.logger.info "Sent recurring order reminder email to supplier #{supplier.id} - #{order.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: You have an upcoming recurring order - ##{order.id}"
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      order: deep_struct(order_data),
      has_commission: supplier.commission_rate > 0.0,

      header_color: :cream,
    }
  end

  def order_data
    {
      id: order.id,
      customer_name: order.customer_profile.customer_or_company_name,
      delivery_at: order.delivery_at.to_s(:full_verbose),
      view_url: url_helper.supplier_order_show_url(order, host: app_host),
      pdf_url: details_document&.url,
    }
  end

  def details_document
    order_supplier = order.order_suppliers.where(supplier_profile: supplier).first
    return nil if order_supplier.blank?

    @_details_document ||= order_supplier.documents.where(kind: 'supplier_order_details').max_by(&:version)
  end

  def attachments_data
    [details_document].compact
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}-#{supplier.id}"
  end

end