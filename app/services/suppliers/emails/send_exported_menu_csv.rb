class Suppliers::Emails::SendExportedMenuCsv < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-supplier_menu_export'.freeze

  def initialize(supplier:, email: nil, csv_data: nil)
    @supplier = supplier
    @email = email
    @csv_data = csv_data
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send supplier exported menu for supplier #{supplier.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id })
    end
  end

private

  attr_reader :supplier, :email

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Sent exported menu csv to supplier #{supplier.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    email.presence || yordar_credentials(:yordar, :orders_email) # maybe it should be supplier.email_recipient
  end

  def email_subject
    "YORDAR: #{supplier.company_name} - Supplier Menu Export"
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      supplier_name: supplier.company_name,
    }
  end

  def attachments
    if @csv_data.present?
      csv_data = @csv_data
    else
      csv_generator = Suppliers::ExportMenuToCsv.new(supplier: supplier).call
      if csv_generator.success?
        csv_data = csv_generator.csv_data
      end
    end
    if csv_data.blank?
      []
    else
      [{ name: "#{supplier.company_name} menu - #{Time.zone.now.to_s(:date)}", file_url: csv_data, is_csv: true }]
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{supplier.id}-#{Time.zone.now.to_s(:date_spreadsheet)}"
  end

end

