class Suppliers::Emails::SendTeamOrderHeadsUpEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-team_order_created'.freeze

  def initialize(team_order:, supplier:)
    @team_order = team_order
    @supplier = supplier
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send team order heads up email to supplier #{supplier.id} - ##{team_order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { team_order_id: team_order.id, supplier_id: supplier.id })
    end
  end

private

  attr_reader :team_order, :supplier

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "New team order heads up email sent to supplier #{supplier.id} - ##{team_order.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: Just a heads up for an upcoming team order'
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      team_order: deep_struct(team_order_data),
      header_color: :purple,
    }
  end

  def team_order_data
    customer_name = team_order.company_name.presence || team_order.customer_profile.company_name.presence || team_order.customer_profile.name
    number_of_invitees = [team_order.number_of_people, team_order.team_order_attendees.where(status: 'invited').count].compact.max
    {
      id: team_order.id,
      customer_name: customer_name,
      is_recurring: team_order.is_recurring_team_order?,
      number_of_invitees: number_of_invitees,
      delivery_at: team_order.delivery_at.to_s(:full_verbose),
      delivery_address: team_order.delivery_address_arr.join('<br>'),
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{supplier.id}"
  end
end
