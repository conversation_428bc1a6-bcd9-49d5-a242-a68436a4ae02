class Suppliers::Emails::SendFinalClosureCorrectionEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-final_closure_correction'.freeze
  OrderData = Struct.new(:name, :company_name, :delivery_address)

  def initialize(supplier:, orders:)
    @supplier = supplier
    @orders = orders
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send final closure correction email to supplier #{supplier.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :orders, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Final closure correction email sent to supplier #{supplier.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: Holiday period skipped orders - Update'
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,

      supplier_close_from: supplier.close_from.to_s(:date_verbose),
      supplier_close_to: supplier.close_to.to_s(:date_verbose),

      orders: sanitised_orders,
    }
  end

  def template_only_orders
    template_orders = []
    recurrent_orders, one_off_orders = orders.partition(&:is_recurrent?)
    recurrent_orders.group_by(&:template_id).each do |template_id, _|
      template_orders << Order.where(id: template_id).first
    end
    template_orders += one_off_orders
    template_orders
  end

  def sanitised_orders
    sanitised_orders = []
    template_only_orders.group_by(&:customer_profile).each do |customer_profile, customer_orders|
      customer_orders.each do |order|
        sanitised_orders << OrderData.new(order.name, customer_profile.customer_or_company_name, order.delivery_address_arr.join(', '))
      end
    end
    sanitised_orders
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{supplier.id}"
  end

end

