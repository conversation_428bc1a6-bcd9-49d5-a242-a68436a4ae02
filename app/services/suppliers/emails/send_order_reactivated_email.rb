class Suppliers::Emails::SendOrderReactivatedEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-order_reactivated'.freeze

  def initialize(supplier:, orders:, mode:)
    @supplier = supplier
    @orders = orders
    @reactivate_mode = mode
    @order = orders.min_by(&:delivery_at)
    @attachments = []
  end

  def call
    begin
      generate_document
      send_email
    rescue => exception
      error_message = "Failed to send order reactivation email to supplier #{supplier.id} - #{orders.map(&:id).join(', ')}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, order_ids: orders.map(&:id) })
    end
  end

private

  attr_reader :supplier, :order, :orders, :reactivate_mode, :attachments

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Order reactivation email sent to supplier #{supplier.id} - #{orders.map(&:id).join(', ')}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: An order was #{reactivate_order_status} - ##{order.id} (Ver.#{@details_document&.version})"
  end

  def email_cc
    yordar_credentials(:yordar, :admin_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      reactivate_mode: reactivate_mode,
      firstname: supplier.email_salutation,

      profile_url: Rails.application.routes.url_helpers.supplier_profile_url(host: 'app.yordar.com.au', protocol: 'https'),

      order: deep_struct(order_data),

      header_color: :cream
    }
  end

  def order_data
    delivery_date_format = reactivate_mode == 'one-off' ? '%A, %d-%b-%Y, %I:%M%P' : '%d-%b-%Y, %I:%M%P'
    {
      id: order.id,
      status: reactivate_order_status,
      customer_name: (order.customer_profile&.customer_or_company_name || ''),
      date: ((order.delivery_at.present? && order.delivery_at.strftime(delivery_date_format)) || ''),
      days: orders.map{|o| o.delivery_at.strftime('%As') }.uniq.join(', '),
      pdf_url: @details_document&.url,
      # delivery_address: order.delivery_address_arr.join(', ')
    }
  end

  def reactivate_order_status
    @_reactivate_order_status ||= reactivate_mode == 'one-off' ? 're-activated as a one-off' : 're-activated'
  end

  def generate_document
    @details_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: email_ref).call
    if @details_document.present?
      attachments << @details_document
    end
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}"
  end

end

