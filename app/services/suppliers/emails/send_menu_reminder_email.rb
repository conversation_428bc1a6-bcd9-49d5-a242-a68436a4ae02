class Suppliers::Emails::SendMenuReminderEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-menu_reminder'.freeze

  def initialize(supplier:, time: Time.zone.now)
    @supplier = supplier
    @time = time
    @attachments = []
  end

  def call
    begin
      generate_documents
      send_email
    rescue => exception
      error_message = "Failed to send menu reminder email to supplier #{supplier.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, time: time })
    end
  end

private

  attr_reader :supplier, :time, :attachments

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      cc: email_cc,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Menu Reminder email sent to supplier #{supplier.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    supplier.email_recipient
  end

  def email_cc
    supplier_admin_email
  end

  def email_subject
    "YORDAR: Your #{reminder_frequency} Menu Reminder - #{supplier.name} - #{time.to_s(:date_verbose)}"
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    supplier_menu_url = url_helper.next_app_supplier_show_url(supplier.slug, host: next_app_host)
    {
      first_name: supplier.email_salutation,

      supplier_admin_email: supplier_admin_email,
      frequency: reminder_frequency,
      menu_url: url_helper.new_user_session_url(user_return_to: supplier_menu_url, host: app_host),
      menu_file_url: @menu_file&.url,

      header_color: :cream,
    }
  end

  def generate_documents
    @menu_file = Documents::Generate::SupplierCurrentMenu.new(supplier: supplier, reference: email_ref).call
    attachments << @menu_file
  end

  def reminder_frequency
    supplier.menu_reminder_frequency.humanize.gsub('.', ' ')
  end

  def supplier_admin_email
    yordar_credentials(:yordar, :supplier_email)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{supplier.id}-#{time.to_s(:filename)}"
  end

end
