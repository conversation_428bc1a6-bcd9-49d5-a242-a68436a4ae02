class Suppliers::Emails::SendOrderReviewsEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-review_notification'.freeze

  def initialize(supplier:, time:, order_reviews: [])
    @supplier = supplier
    @time = time
    @order_reviews = order_reviews
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send order reviews notification to supplier #{supplier.id} - #{week_number}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, week_number: week_number })
    end
  end

private

  attr_reader :supplier, :time, :order_reviews

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Order reviews notification sent to supplier #{supplier.id} - #{week_number}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: You have order review(s)'
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      ratings: deep_struct(ratings_data),
      header_color: :cream,
    }
  end

  def ratings_data
    {
      count: order_reviews.count,
      week_starting: time.beginning_of_week.to_s(:date_verbose),
      url: url_helper.supplier_ratings_url(date: time.to_date, host: app_host)
    }
  end

  def week_number
    time.to_s(:year_week)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{week_number}"
  end

end

