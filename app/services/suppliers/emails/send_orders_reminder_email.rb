class Suppliers::Emails::SendOrdersReminderEmail < Notifications::WithPreference

  EMAIL_TEMPLATE = 'supplier-orders_reminder'.freeze

  def initialize(supplier:, order_lines:, summary_day:)
    @notifying_account = @supplier = supplier
    @order_lines = order_lines
    @summary_day = summary_day
    @attachments = []
    @template_name = EMAIL_TEMPLATE
    @result = Result.new
  end

  def call
    return if !preferred_notification?

    begin
      generate_document
      send_email
    rescue => exception
      error_message = "Failed to send orders reminder email to supplier #{supplier.id} - #{summary_day}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, summary_day: summary_day })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :order_lines, :summary_day, :attachments, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Send orders reminder email to supplier #{supplier.id} - #{summary_day}"
      result.sent_notification = email_sender.email
    else
      result.errors = email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    super(default: supplier.email_recipient)
  end

  def email_subject
    'YORDAR: Your fortnightly/monthly order reminder'
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: email_salutation,
      company_name: supplier.company_name,
      summary: deep_struct(summary_data),
      header_color: :cream,
    }
  end

  def summary_data
    {
      day: summary_day.to_s(:full_date),
      pdf_url: @summary_document&.url,
    }
  end

  def generate_document
    @summary_document = Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: 'reminder').call
    if @summary_document.present?
      attachments << @summary_document
    end
  end

  def email_salutation
    super(default: supplier.email_salutation)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{summary_day.to_s(:date_compact)}"
  end

end
