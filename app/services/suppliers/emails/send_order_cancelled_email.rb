class Suppliers::Emails::SendOrderCancelledEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-order_cancelled'.freeze

  def initialize(supplier:, orders:, mode:)
    @supplier = supplier
    @orders = orders
    @cancel_mode = mode
    @result = Result.new
  end

  def call
    return result if !can_send_email?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send order cancellation email to supplier #{supplier.id} - ##{orders.map(&:id).join(', ')}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, order_ids: orders.map(&:id) })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :orders, :cancel_mode, :result

  def can_send_email?
    case
    when supplier.blank?
      result.errors << 'Cannot notify without a supplier'
    when orders.blank?
      result.errors << 'Cannot notify without (cancelled) orders'
    end
    result.errors.blank?
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      mark_supplier_as_notified
      Rails.logger.info "Order cancellation email sent to supplier #{supplier.id} - ##{orders.map(&:id).join(', ')}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: An order has been #{order_status} - ##{order.id}"
  end

  def email_cc
    yordar_credentials(:yordar, :orders_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      cancel_mode: cancel_mode,
      profile_url: url_helper.supplier_profile_url(host: app_host),

      order: deep_struct(order_data),

      header_color: :cream,
    }
  end

  def order_data
    if cancel_mode != 'one-off'
      order_days = orders.map{|o| o.delivery_at.strftime('%As') }.uniq.join(', ')
      delivery_date_format = '%d-%b-%Y, %I:%M%P'
    else
      order_days = nil
      delivery_date_format = '%A, %d-%b-%Y, %I:%M%P'
    end

    {
      id: order.id,
      type: order_type,
      status: order_status,
      customer_name: (order.customer_profile&.customer_or_company_name || ''),
      date: ((order.delivery_at.present? && order.delivery_at.strftime(delivery_date_format)) || ''),
      days: order_days,
      pdf_url: details_document&.url,
      # delivery_address: order.delivery_address_arr.join(', ')
    }
  end

  def order_type
    case
    when order.is_team_order?
      'team'
    when order.recurrent_id.present?
      'standing'
    else
      'one off'
    end
  end

  def order_status
    case
    when cancel_mode == 'one-off'
      'cancelled'
    when cancel_mode == 'on-hold'
      'put on hold'
    else # cancel_mode == related or subsequent
      'cancelled permanently'
    end
  end

  def order
    @_order = orders.min_by(&:delivery_at)
  end

  def details_document
    order_supplier = order.order_suppliers.where(supplier_profile: supplier).first
    return nil if order_supplier.blank?

    order_supplier.documents.where(kind: 'supplier_order_details').order(version: :desc).first
  end

  def mark_supplier_as_notified
    Orders::MarkSupplierAsNotified.new(order: order, supplier: supplier).call
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}"
  end

end
