class Suppliers::Emails::SendPurchaseOrderSummaryEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-purchase_order_summary'.freeze

  def initialize(supplier:, invoice:, rgi_document:)
    @supplier = supplier
    @invoice = invoice
    @rgi_document = rgi_document
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send purchase orders summary email to supplier #{supplier.id} - #{summary_period}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, summary_period: summary_period })
    end
  end

private

  attr_reader :supplier, :invoice, :rgi_document

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments_data
    ).call
    if email_sender.success?
      Rails.logger.info "Sent purchase orders summary email to supplier #{supplier.id} - #{summary_period}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'YORDAR: Recipient Generated Invoice'
  end

  def email_cc
    yordar_credentials(:yordar, :accounts_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      pdf_url: rgi_document&.url,
      delivery_times: deep_struct(delivery_times_data),
      header_color: :cream,
    }
  end

  def delivery_times_data
    date_format = supplier.billing_frequency == 'weekly' ? :full_date : :date_verbose
    {
      starts: invoice.from_at.to_s(date_format),
      ends: invoice.to_at.to_s(date_format),
      due: invoice.due_at.to_s(:date_verbose),
    }
  end

  def attachments_data
    [rgi_document].compact
  end

  def summary_period
    "#{invoice.from_at.to_s(:date)}-#{invoice.to_at.to_s(:date)}"
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{summary_period}"
  end
end
