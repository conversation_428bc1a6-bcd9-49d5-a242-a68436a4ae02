class Suppliers::Emails::SendNewOrderEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-new_order'.freeze

  def initialize(supplier:, order:)
    @supplier = supplier
    @order = order
    @attachments = []
  end

  def call
    begin
      generate_documents
      send_email
    rescue => exception
      error_message = "Failed to send new order email to supplier #{supplier.id} - ##{order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, order_id: order.id })
    end
  end

private

  attr_reader :supplier, :order, :attachments

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      mark_supplier_as_notified
      Rails.logger.info "New order email sent to supplier #{supplier.id} - ##{order.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: You have a new order - ##{order.id} (Ver.#{@details_document&.version})"
  end

  def email_cc
    yordar_credentials(:yordar, :order_check_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: supplier.email_salutation,
      has_commission: supplier.commission_rate > 0.0,

      order: deep_struct(order_data),

      header_color: :cream,
    }
  end

  def order_data
    order_day = order.template_delivery_at&.to_s(:weekday)
    if order.is_recurrent?
      pattern_text = {
        '1.week' => 'week',
        '2.weeks' => 'every second week',
        '1.month' => 'month',
        '4.weeks' => 'every 4th week'
      }
      order_day = pattern_text[order.pattern]
    end
    customer_name = order.customer_profile&.customer_or_company_name || ''
    {
      id: order.id,
      date: order.delivery_at.to_s(:full),
      day: order_day,
      customer_name: customer_name,
      is_recurring: order.is_recurrent?,
      recurrent_type: order.recurrent_type,
      is_contactless_delivery: supplier.provides_contactless_delivery && order.is_contactless_delivery?,
      pdf_url: @details_document&.url,
      flex_url: @flex_document&.url,

      view_url: url_helper.supplier_order_show_url(order, host: app_host),
      confirm_url: order_manage_urls(mode: 'accepted'),
      reject_url: order_manage_urls(mode: 'rejected'),
    }
  end

  def order_manage_urls(mode:)
    path = url_helper.order_confirm_or_reject_url(order_id: order.id, profile_type: 'supplier', profile_id: supplier.id, mode: mode, hashed_value: md5_hash, host: app_host)
    url_shortner = Shortener::ShortenedUrl.generate(path)
    url_helper.shortened_url(url_shortner.unique_key, host: app_host)
  end

  def md5_hash
    @_md5_hash ||= Digest::MD5.hexdigest(order.id.to_s + supplier.id.to_s + yordar_credentials(:random_salt))
  end

  def generate_documents
    generate_order_detail_document
    generate_delivery_detail_document
    generate_flex_json if supplier.uses_flex_catering
  end

  def generate_order_detail_document
    @details_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: email_ref).call
    if @details_document.present?
      attachments << @details_document
    end
  end

  def generate_delivery_detail_document
    delivery_details_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: email_ref, variation: 'delivery_docket').call
    if delivery_details_document.present?
      attachments << delivery_details_document
    end
  end

  def generate_flex_json
    @flex_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: email_ref, variation: 'json').call
    if @flex_document.present?
      attachments << @flex_document
    end
  end

  def mark_supplier_as_notified
    Orders::MarkSupplierAsNotified.new(order: order, supplier: supplier).call
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}"
  end

end

