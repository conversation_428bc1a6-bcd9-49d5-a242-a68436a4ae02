class Suppliers::Emails::SendContactlessDeliveryInfo < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-contactless_delivery_info'.freeze

  def initialize(supplier:)
    @supplier = supplier
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send contactless delivery info email to supplier #{supplier.id}"
      log_errors(exception: exception, message: error_message, sentry: false)
    end
  end

private

  attr_reader :supplier

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      cc: email_cc,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Contactless delivery info email sent to supplier #{supplier.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Go Contactless with Yordar'
  end

  def email_cc
    yordar_credentials(:yordar, :admin_email)
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      confirm_url: settings_urls(mode: 'true'),
      # reject_url: settings_urls(mode: 'false'),
      profile_settings_url: url_helper.supplier_account_url(host: app_host),

      header_color: :cream
    }
  end

  def settings_urls(mode:)
    url_helper.supplier_anonymous_settings_url(id: supplier.uuid, field: 'contactless_delivery', value: mode, host: app_host)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{supplier.id}"
  end
end
