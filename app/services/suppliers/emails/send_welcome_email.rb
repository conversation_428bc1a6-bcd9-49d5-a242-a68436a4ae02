class Suppliers::Emails::SendWelcomeEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-welcome'.freeze

  def initialize(supplier:)
    @supplier = supplier
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send welcome email to supplier #{supplier.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id })
    end
  end

private

  attr_reader :supplier

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Welcome email sent to supplier #{supplier.id}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Welcome to Yordar!'
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      company_name: supplier.company_name,
      activation_url: activation_url,

      header_color: :cream
    }
  end

  def activation_url
    url_helper.user_confirmation_url(confirmation_token: supplier.user.confirmation_token, host: app_host)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{supplier.id}"
  end

end

