class Suppliers::Emails::SendInitialClosureEmail < Notifications::Base

  EMAIL_TEMPLATE = 'supplier-initial_closure'.freeze

  def initialize(supplier:)
    @supplier = supplier
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send initial closure email to supplier #{supplier.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :supplier, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: supplier.email_recipient,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Initial closure email sent to supplier #{supplier.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "YORDAR: Holiday closure dates - #{Time.zone.now.year}"
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      firstname: supplier.email_salutation,
      yordar: deep_struct(dates_data),

      header_color: :cream,
    }
  end

  def dates_data
    last_day = Time.zone.parse(yordar_credentials(:yordar, :closure_start_date)) - 1.day
    first_day = Time.zone.parse(yordar_credentials(:yordar, :closure_end_date)) + 1.day
    notification_threshold_day = last_day - 2.weeks
    {
      last_day: last_day.strftime("%A the #{last_day.day.ordinalize} of %B %Y"), # 'Friday the 20th of December 2019',
      new_year_first_day: first_day.strftime("%A the #{first_day.day.ordinalize} of %B %Y"), # 'Monday the 6th of January 2020',
      notification_threshold_day: notification_threshold_day.strftime("#{notification_threshold_day.day.ordinalize} of %B %Y"), # '6th of December 2019'
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{supplier.id}"
  end

end

