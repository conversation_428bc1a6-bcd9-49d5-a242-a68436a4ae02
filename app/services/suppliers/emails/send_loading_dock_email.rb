class Suppliers::Emails::SendLoadingDockEmail < Notifications::Base
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'supplier-loading_dock'.freeze

  def initialize(order:, supplier:)
    @order = order
    @supplier = supplier
    @attachments = []
    @result = Result.new
  end

  def call
    return if loading_dock.blank?

    begin
      attach_loading_dock_file if loading_dock.file_url.present?
      send_email
    rescue => exception
      error_message = "Failed to send order loading dock email to supplier #{supplier.id} - ##{order.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { order_id: order.id, supplier_id: supplier.id })
      result.errors << error_message
    end

    result
  end

private

  attr_reader :supplier, :order, :attachments, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables,
      attachments: attachments
    ).call
    if email_sender.success?
      Rails.logger.info "Loading Dock email sent to supplier #{supplier.id} - ##{order.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    supplier.email_recipient
  end

  def email_subject
    "YORDAR: Loading Dock Code for ##{order.id}"
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      first_name: supplier.email_salutation,
      order: deep_struct(order_data),
      loading_dock: deep_struct(loading_dock_data),

      header_color: :cream,
    }
  end

  def order_data
    customer_name = order.customer_profile&.customer_or_company_name || ''
    {
      id: order.id,
      date: order.delivery_at.to_s(:full),
      customer_name: customer_name,
    }
  end

  def loading_dock_data
    {
      show_url: url_helper.loading_dock_url(uuid: order.uuid, host: next_app_host),
      file_url: loading_dock.file_url,
      code: loading_dock.code
    }
  end

  def attach_loading_dock_file
    loading_dock_file = Document.new(name: "order-loading-dock-#{order.id}", url: loading_dock.file_url)
    attachments << loading_dock_file
  end

  def loading_dock
    @_loading_dock ||= order.loading_dock
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{order.id}-#{order.version_ref}"
  end

end
