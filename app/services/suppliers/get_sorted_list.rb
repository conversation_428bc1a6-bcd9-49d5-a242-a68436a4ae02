class Suppliers::GetSortedList

  def initialize(session_profile:, suppliers: [], filter_options: {}, selected_supplier: nil)
    @session_profile = session_profile
    @suppliers = suppliers
    @filter_options = filter_options
    @selected_supplier = selected_supplier
    @result = Result.new(suppliers: suppliers)
  end

  def call
    return result if suppliers.blank?

    if can_sort_by_rating?
      sort_by_rating
    else
      retrieve_rate_card_suppliers
      retrieve_markup_override_suppliers
      retrieve_custom_menu_suppliers
      fetch_favourite_suppliers
      sort_by_data
    end
    result
  end

private

  attr_reader :session_profile, :suppliers, :filter_options, :selected_supplier, :result

  def can_sort_by_rating?
    filter_options[:other].present? && filter_options[:other].include?('by_rating')
  end

  def sort_by_rating
    result.sorted_suppliers = suppliers.sort_by do |supplier|
      [-supplier.rating, -supplier.rating_count]
    end
  end

  def sort_by_data
    result.sorted_suppliers = suppliers.shuffle.sort_by do |supplier|
      supplier_minimum_spend = minimum_spend_for(supplier)
      [
        result.custom_menu_supplier_ids.include?(supplier.id) ? 0 : 1,
        supplier.is_featured ? 0 : 1,
        result.favourite_supplier_ids.include?(supplier.id) ? 0 : 1,
        result.rate_card_supplier_ids.include?(supplier.id) ? 0 : 1,
        result.markup_override_supplier_ids.include?(supplier.id) ? 0 : 1,
        supplier.is_new? ? 0 : 1,
        supplier_minimum_spend.present? ? supplier_minimum_spend : 1
      ]
    end
    if selected_supplier.present? && suppliers.include?(selected_supplier)
      result.sorted_suppliers.unshift(selected_supplier)
      result.sorted_suppliers = result.sorted_suppliers.uniq
    end
  end

  def is_customer?
    session_profile.present? && session_profile.profile.present? && session_profile.profile.profileable_type == 'CustomerProfile'
  end

  def customer_company
    return nil if !is_customer?

    @_customer_company ||= session_profile.company
  end

  def retrieve_rate_card_suppliers
    return if customer_company.blank?

    result.rate_card_supplier_ids = MenuItem.where(supplier_profile: suppliers.to_a, archived_at: nil).joins(:rate_cards).where(rate_cards: { company_id: customer_company.id }).select(:supplier_profile_id).map(&:supplier_profile_id)
  end

  def retrieve_markup_override_suppliers
    return if customer_company.blank? && session_profile.blank?

    # only include available and discounted markup overrides
    supplier_arel = SupplierProfile.arel_table
    override_arel = Supplier::MarkupOverride.arel_table
    markup_present = override_arel[:markup].not_eq(nil)
    discounted_markup_condition = override_arel[:markup].lteq(supplier_arel[:markup])

    markup_overrides = Supplier::MarkupOverride.where(supplier_profile: suppliers.to_a, active: true)
    markup_overrides = markup_overrides.where(overridable: [session_profile, customer_company].compact)
    markup_overrides = markup_overrides.joins(:supplier_profile).where(markup_present.and(discounted_markup_condition))

    result.markup_override_supplier_ids = markup_overrides.select(:supplier_profile_id).map(&:supplier_profile_id)
  end

  def retrieve_custom_menu_suppliers
    return if customer_company.blank?

    result.custom_menu_supplier_ids = MenuSection.where(supplier_profile: suppliers.to_a, archived_at: nil).joins(:companies).where(companies: { id: customer_company.id }).select(:supplier_profile_id).map(&:supplier_profile_id)
  end

  def fetch_favourite_suppliers
    return nil if !is_customer?

    if is_team_order?
      result.favourite_supplier_ids = session_profile.favourite_team_supplier_ids
    else
      result.favourite_supplier_ids = session_profile.favourite_supplier_ids
    end
  end

  def is_team_order?
    filter_options.present? && filter_options[:team_suppliers].present?
  end

  def minimum_spend_for(supplier)
    return nil if !is_team_order? || filter_options[:minimums].blank?

    filter_options[:minimums][supplier]&.minimum_spend || rand(999...9999)
  end

  class Result
    attr_accessor :sorted_suppliers, :rate_card_supplier_ids, :markup_override_supplier_ids, :custom_menu_supplier_ids, :favourite_supplier_ids

    def initialize(suppliers:)
      @sorted_suppliers = suppliers
      @rate_card_supplier_ids = []
      @markup_override_supplier_ids = []
      @custom_menu_supplier_ids = []
      @favourite_supplier_ids = []
    end
  end

end
