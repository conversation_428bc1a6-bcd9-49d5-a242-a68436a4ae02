class Suppliers::ListClosureDates

  DAY_NAMES = Date::DAYNAMES.map{|day| day.downcase.slice(0, 3) }

  def initialize(supplier:, from_date: nil, until_date: nil, delivery_zone: nil, constrained: false)
    @supplier = supplier
    @from_date = from_date.present? ? from_date.beginning_of_day : Time.zone.now.beginning_of_day
    @until_date = until_date.present? ? until_date.end_of_day : (Time.zone.today + 6.months).end_of_day
    @delivery_zone = delivery_zone
    @constrained = constrained
    @result = Result.new
  end

  def call
    retrieve_supplier_saved_closure_dates
    retrieve_associated_closure_dates
    retrieve_non_operating_days if delivery_zone.present?
    result
  end

private

  attr_reader :supplier, :from_date, :until_date, :delivery_zone, :constrained, :result

  def retrieve_supplier_saved_closure_dates
    if supplier.close_from.present? && supplier.close_to.present? && supplier.close_from <= until_date && supplier.close_to >= from_date
      result.closures << ClosurePeriod.new(
        reason: 'Christmas Closure Period',
        starts_on: supplier.close_from.to_date,
        ends_on: supplier.close_to.to_date,
        days_in_between: days_in_between(starts: supplier.close_from, ends: supplier.close_to)
      )
    else
      []
    end
  end

  def associated_closure_dates
    @_associated_closure_dates ||= supplier.closure_dates.where('starts_at <= :until OR ends_at >= :from', from: from_date, until: until_date)
  end

  def retrieve_associated_closure_dates
    associated_closure_dates.each do |supplier_closure|
      closure_days = days_in_between(starts: supplier_closure.starts_at, ends: supplier_closure.ends_at)

      next if closure_days.blank?

      result.closures << ClosurePeriod.new(
        reason: supplier_closure.reason,
        starts_on: supplier_closure.starts_at.to_date,
        ends_on: supplier_closure.ends_at.to_date,
        days_in_between: closure_days
      )
    end
  end

  def retrieve_non_operating_days
    return if delivery_zone.blank? || delivery_zone.operating_wdays.blank?

    non_operating_days = delivery_zone.operating_wdays.split('').map.each_with_index{|day, idx| day == '0' ? DAY_NAMES[idx] : nil }.compact
    return if non_operating_days.blank?

    starts = from_date
    ends = until_date
    non_operating_dates = []
    while starts <= ends
      if non_operating_days.include?(starts.to_s(:short_weekday).downcase)
        non_operating_dates << starts.to_date
      end
      starts += 1.day
    end
    if non_operating_dates.present?
      result.closures << ClosurePeriod.new(
        reason: 'Non Operational Days',
        days_in_between: non_operating_dates
      )
    end
  end

  def days_in_between(starts:, ends:)
    return [] if starts > until_date && ends > until_date

    if constrained
      starts = from_date if starts <= from_date
      starts = until_date if starts >= until_date
      ends = until_date if ends >= until_date
    end
    days = []
    while starts <= ends
      days << starts.to_date
      starts += 1.day
    end
    days
  end

  class ClosurePeriod
    attr_reader :reason, :starts_on, :ends_on, :days_in_between

    def initialize(reason:, starts_on: nil, ends_on: nil, days_in_between: [])
      @reason = reason
      @starts_on = starts_on
      @ends_on = ends_on
      @days_in_between = days_in_between
    end
  end

  class Result
    attr_accessor :closures

    def initialize
      @closures = []
    end

    def closure_dates
      return [] if closures.blank?

      closures.map(&:days_in_between).flatten(1).sort
    end
  end

end
