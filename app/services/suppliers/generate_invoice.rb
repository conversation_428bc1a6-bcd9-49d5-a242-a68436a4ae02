# does 5 things
# recalculate totals for each invoicable order
# creates an invoice record
# connects invoice to orders via order suppliers
# generates pdf
# notifies suppliers about generated pdfs

class Suppliers::GenerateInvoice

  def initialize(supplier:, invoicable_orders:, invoice_dates: {}, notify_supplier: false)
    @supplier = supplier
    @invoicable_orders = invoicable_orders
    @invoice_dates = get_invoice_dates(invoice_dates)
    @notify_supplier = notify_supplier
    @result = Result.new
  end

  def call
    # If the invoice cannot be generated, remove the invoice from the database
    ActiveRecord::Base.transaction do
      recalculate_totals
      begin
        create_invoice
        attach_invoice_to_orders
        generate_rgi_document
        send_invoice_to_supplier if notify_supplier
        result.generated_invoice = supplier_invoice
      rescue => exception
        error_message = "Failed to generate invoice for supplier #{supplier.id} - #{invoicable_orders.map(&:id).join(', ')}"
        Rails.logger.error error_message
        Rails.logger.error exception.inspect
        Rails.logger.error exception.backtrace.join('\n')
        result.errors << error_message
        Raven.capture_exception(exception,
          message: error_message,
          extra: { supplier_id: supplier.id, order_ids: invoicable_orders.map(&:id) },
          transaction: 'Suppliers::GenerateInvoice'
        )
        raise ActiveRecord::Rollback
      end
    end
    result
  end

private

  attr_reader :supplier, :invoicable_orders, :invoice_dates, :notify_supplier, :supplier_invoice, :result

  def recalculate_totals
    # Recalculate totals before invoicing. This is to make sure the order totals are always up-to-date in invoice.
    invoicable_orders.map do |order|
      Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, save_totals: true).call
    end
  end

  def create_invoice
    invoicable_amount = all_order_suppliers.map(&:total).compact.sum
    @supplier_invoice = supplier.supplier_invoices.new
    supplier_invoice.update(
      number: invoice_number,
      uuid: SecureRandom.uuid,
      from_at: invoice_dates[:from],
      to_at: invoice_dates[:to],
      due_at: invoice_dates[:due],
      amount: invoicable_amount
    )
  end

  def invoice_number
    @_invoice_number ||= begin
      invoice_count = SupplierInvoice.where('created_at >= ?', Time.zone.now.beginning_of_day).count + 1
      "RGI-#{Time.zone.now.to_s(:invoice_number)}#{format('%03d', invoice_count)}"
    end
  end

  def attach_invoice_to_orders
    all_order_suppliers.each do |order_supplier|
      order_supplier.update(supplier_invoice: supplier_invoice)
    end
  end

  def generate_rgi_document
    @generated_document = Documents::Generate::RecipientGeneratedInvoice.new(invoice: supplier_invoice).call
    return if @generated_document.blank?

    result.generated_pdf = @generated_document
  end

  def send_invoice_to_supplier
    return if @generated_document.blank?

    Suppliers::Emails::SendPurchaseOrderSummaryEmail.new(supplier: supplier, invoice: supplier_invoice, rgi_document: @generated_document).call
  end

  def get_invoice_dates(dates)
    if dates.present?
      {
        from: dates[:from],
        to: dates[:to],
        due: dates[:to] + term_days.days
      }
    else
      yesterday = (Time.zone.now - 1.day).beginning_of_day
      {
        from: yesterday.beginning_of_day,
        to: yesterday.end_of_day,
        due: yesterday + term_days.days
      }
    end
  end

  def term_days
    @_term_days ||= supplier.payment_term_days
  end

  def all_order_suppliers
    @_all_order_suppliers ||= OrderSupplier.where(order: invoicable_orders, supplier_profile: supplier)
  end

  class Result
    attr_accessor :generated_invoice, :generated_pdf, :errors

    def initialize
      @generated_invoice = nil
      @generated_pdf = nil
      @errors = []
    end

    def success?
      errors.blank? && generated_invoice.present?
    end
  end

end

