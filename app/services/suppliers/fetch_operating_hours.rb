class Suppliers::FetchOperatingHours

  def initialize(supplier:, delivery_zone:, calculate: false)
    @supplier = supplier
    @delivery_zone = delivery_zone
    @calculate = calculate
  end

  def call
    case
    when supplier.present? && supplier.operating_hours.present? && !calculate
      supplier.operating_hours
    when delivery_zone.present?
      delivery_zone_based_operating_hours
    else
      nil
    end
  end

private

  attr_reader :supplier, :delivery_zone, :calculate

  def delivery_zone_based_operating_hours
    delivery_hrs = []
    operating_hours = [delivery_zone.operating_hours_start, delivery_zone.operating_hours_end]

    operating_hours.each do |operating_hour_in_seconds|
      if operating_hour_in_seconds.present?
        delivery_hrs << printable_time(seconds: operating_hour_in_seconds)
      end
    end
    delivery_hrs.join(' - ')
  end

  def printable_time(seconds:)
    t = Time.gm(2000, 1, 1) + seconds # date doesn't matter but has to be valid
    t.to_s(:time_only) # '%I:%M %p'
  end

end
