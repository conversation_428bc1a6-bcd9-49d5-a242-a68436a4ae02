class Suppliers::SetConfigAnonymously

  ANONYMOUS_CONFIGS = %w[provides_contactless_delivery].freeze

  def initialize(uuid:, field:, value: nil)
    @uuid = uuid
    @field = field.present? ? "provides_#{field}" : nil
    @value = value.nil? ? true : value
    @result = Result.new
  end

  def call
    result.supplier = supplier
    if can_set_config?
      supplier.supplier_flags.update(Hash[field.to_sym, value])
      result.field = field
    end
    result
  end

private

  attr_reader :uuid, :field, :value, :result

  def supplier
    @_supplier ||= SupplierProfile.where(uuid: uuid).first
  end

  def can_set_config?
    case
    when supplier.blank?
      result.errors << 'Could not find supplier'
    when supplier.supplier_flags.blank?
      result.errors << 'Could not find supplier with flags'
    when field.blank? || ANONYMOUS_CONFIGS.exclude?(field.to_s)
      result.errors << 'Cannot set this config'
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :supplier, :field, :errors

    def initialize
      @supplier = supplier
      @field = field
      @errors = []
    end

    def success?
      errors.blank? && supplier.present?
    end
  end
end
