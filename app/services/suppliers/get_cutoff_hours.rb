class Suppliers::GetCutoffHours

  def initialize(supplier:, delivery_at:, suburb:)
    @supplier = supplier
    @suburb = suburb
    @delivery_at = sanitize_delivery_at_for(delivery_at)
    @result = Result.new(delivery_at: @delivery_at)
  end

  def call
    return nil if delivery_at.blank?

    result.cutoff_hours = calculate_cutoff_hours
    result
  end

private

  attr_accessor :supplier, :delivery_at, :suburb, :result

  def sanitize_delivery_at_for(input_delivery_at)
    case
    when input_delivery_at.present? && input_delivery_at.is_a?(String)
      Time.zone.parse(input_delivery_at)
    else
      input_delivery_at
    end
  end

  def state
    @_state ||= suburb.present? ? suburb.state : nil
  end

  def calculate_cutoff_hours
    minimum_lead_time = [cutoff_time_by_day, cutoff_time_by_lead_time, delivery_at].compact.min
    cutoff_time_on_business_day = non_holiday_business_day(minimum_lead_time)
    ((delivery_at - cutoff_time_on_business_day) / 1.hours).to_s
  end

  # e.g. 22.0, meaning 22 hours lead time
  def cutoff_time_by_lead_time
    max_lead_time = supplier.minimums.where.not(lead_time: nil).maximum(:lead_time)
    max_lead_time.present? ? delivery_at - max_lead_time.to_i.hours : nil
  end

  # e.g. 18:00, meaning 18:00 in previous day
  def cutoff_time_by_day
    time_by_day = nil
    min_lead_time_day_before = supplier.minimums.where.not(lead_time_day_before: [nil, '']).minimum(:lead_time_day_before).to_s
    if min_lead_time_day_before.present? && min_lead_time_day_before.include?(':')
      hour, minute = min_lead_time_day_before.split(':')
      time_by_day = delivery_at.change(hour: hour.to_i, min: minute.to_i) - 1.days
    end
    time_by_day
  end

  def non_holiday_business_day(date)
    business_day = date
    while is_public_holiday?(business_day) || business_day.saturday? || business_day.sunday?
      business_day -= 1.days
    end
    business_day
  end

  def is_public_holiday?(date)
    Holiday.where(on_date: (date.beginning_of_day..date.end_of_day), state: [nil, state]).where.not(push_to: nil).present?
  end

  class Result
    attr_accessor :cutoff_hours, :delivery_at

    def initialize(delivery_at:)
      @delivery_at = delivery_at
      @cutoff_hours = nil
    end

    def hours_remaining
      return nil if cutoff_hours.blank?

      ((delivery_at - Time.zone.now - cutoff_hours.to_i.hours) / 1.hours).round(1)
    end

    def hours_remaining_message
      return nil if hours_remaining.blank? || hours_remaining < 0

      if hours_remaining <= 24
        @hours_remaining_message = ApplicationController.helpers.distance_of_time_in_words(hours_remaining.hours)
      else
        @hours_remaining_message = "until #{(delivery_at - cutoff_hours.to_f.hours).to_s(:full)}"
      end
    end
  end
end
