class Suppliers::Cache::DeliveryDetails

  def initialize(supplier:, verbose: false)
    @supplier = supplier
    @verbose = verbose
    @result = Result.new
  end

  def call
    if supplier_delivery_zones.present?
      check_delivery_fees
      check_operating_days
      check_operating_hours
    end
    result
  end

private

  attr_reader :supplier, :verbose, :result

  def supplier_delivery_zones
    @_supplier_delivery_zones ||= supplier.delivery_zones
  end

  def check_delivery_fees
    delivery_zone_fees = supplier_delivery_zones.map(&:delivery_fee)
    if delivery_zone_fees.uniq.size == 1
      sigular_delivery_fee = delivery_zone_fees.first
      supplier.update(delivery_fee: sigular_delivery_fee)
      print 'df-' if verbose
      result.has_same_delivery_fee = true
    else
      supplier.update(delivery_fee: nil)
    end
  end

  def check_operating_days
    delivery_zone_operating_days = supplier_delivery_zones.map(&:operating_wdays)
    if delivery_zone_operating_days.uniq.size == 1
      calculated_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: supplier.delivery_zones.first, calculate: true).call
      supplier.update(operating_days: calculated_operating_days)
      print 'od-' if verbose
      result.has_same_operating_days = true
    else
      supplier.update(operating_days: nil)
    end
  end

  def check_operating_hours
    operating_hours_start = supplier_delivery_zones.map(&:operating_hours_start)
    operating_hours_end = supplier_delivery_zones.map(&:operating_hours_end)
    if operating_hours_start.uniq.size == 1 && operating_hours_end.uniq.size == 1
      calculated_operating_hours = Suppliers::FetchOperatingHours.new(supplier: supplier, delivery_zone: supplier.delivery_zones.first, calculate: true).call
      supplier.update(operating_hours: calculated_operating_hours)
      print 'oh-' if verbose
      result.has_same_operating_hours = true
    else
      supplier.update(operating_hours: nil)
    end
  end

  class Result
    attr_accessor :has_same_delivery_fee, :has_same_operating_days, :has_same_operating_hours
    attr_reader :supplier

    def initialize
      @has_same_delivery_fee = false
      @has_same_operating_days = false
      @has_same_operating_hours = false
    end
  end

end
