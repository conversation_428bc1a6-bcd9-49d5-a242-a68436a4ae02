class Suppliers::Cache::CategoryGroups

  CACHEABLE_CATEGORY_GROUPS = %w[catering-services kitchen-supplies].freeze

  def initialize(suppliers: [], verbose: false)
    @suppliers = suppliers.presence || searchable_suppliers

    @verbose = verbose
  end

  def call
    suppliers.each do |supplier|
      set_flag_for(supplier: supplier)
    end
  end

private

  attr_reader :suppliers, :verbose, :result

  def set_flag_for(supplier:)
    supplier_menu_sections = supplier_grouped_menu_sections[supplier].presence || []
    supplier_category_groups = supplier_menu_sections.map(&:categories).flatten(1).map(&:group).uniq
    if verbose
      puts ''
      puts "#{supplier.name} => #{supplier_category_groups}"
    end
    updated_attributes = {}
    CACHEABLE_CATEGORY_GROUPS.each do |category_group|
      supplier_flag = "has_#{category_group.underscore}"
      has_category_group = supplier_category_groups.present? && supplier_category_groups.include?(category_group)
      puts "#{supplier_flag} => #{has_category_group}" if verbose

      updated_attributes[supplier_flag] = has_category_group
    end

    supplier.supplier_flags&.update(updated_attributes)
  end

  def searchable_suppliers
    SupplierProfile.where(is_searchable: true)
  end

  def supplier_grouped_menu_sections
    @_grouped_menu_sections ||= begin
      menu_sections = MenuSection.where(supplier_profile: suppliers)
      menu_sections = menu_sections.where(archived_at: nil)
      menu_sections = menu_sections.where(is_hidden: false)
      menu_sections = menu_sections.includes(:supplier_profile, :categories)
      menu_sections.group_by(&:supplier_profile)
    end
  end

end
