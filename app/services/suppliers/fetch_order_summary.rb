class Suppliers::FetchOrderSummary

  def initialize(supplier:, summary_day:, regenerate: false)
    @supplier = supplier
    @summary_day = summary_day.present? && summary_day.is_a?(String) ? Time.zone.parse(summary_day) : summary_day
    @regenerate = regenerate
    @result = Result.new
  end

  def call
    if can_fetch?
      if !regenerate && existing_document.present?
        result.document = existing_document
      else
        generate_document
      end
    end
    result
  end

private

  attr_reader :supplier, :summary_day, :regenerate, :result

  def can_fetch?
    case
    when supplier.blank?
      result.errors << 'Cannot generate summary for a missing supplier'
    when summary_day.blank?
      result.errors << 'Cannot generate summary for a missing day'
    when order_lines.blank?
      result.errors << 'Supplier does not have any orders on the summary day'
    end
    result.errors.blank?
  end

  def existing_document
    @_existing_document ||= begin
      documents = supplier.documents.where(kind: 'supplier_order_summary')
      documents = documents.where(name: "supplier-orders-summary-#{summary_day.to_s(:date_compact)}-#{supplier.id}-#{summary_type}")
      documents.order(:version).last
    end
  end

  def generate_document
    result.document = Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type).call
  end

  def summary_type
    @_summary_type ||= summary_day >= Time.zone.now.end_of_day ? 'morning' : 'daily'
  end

  def order_lines
    @_order_lines ||= begin
      order_lines = OrderLine.where(status: order_line_statuses)
      order_lines = order_lines.joins(:order).where(orders: { status: order_statuses, delivery_at: (summary_day.beginning_of_day..summary_day.end_of_day) })
      order_lines = order_lines.where(supplier_profile: supplier)
      order_lines = order_lines.where(orders: { split_order_id: nil })
      order_lines.order('orders.delivery_at asc, order_lines.created_at asc')
    end
  end

  def order_statuses
    @_order_statuses ||= begin
      statuses = %w[delivered confirmed]
      statuses += %w[new amended] if summary_type == 'morning'
      statuses
    end
  end

  def order_line_statuses
    @_order_line_statuses ||= begin
      statuses = ['accepted']
      statuses += %w[pending amended notified] if summary_type == 'morning'
      statuses
    end
  end

  class Result
    attr_accessor :document, :errors

    def initialize
      @document = nil
      @errors = []
    end

    def success?
      errors.blank? && document.present?
    end
  end

end
