class Suppliers::Reject<PERSON>rder

  def initialize(supplier:, order:)
    @supplier = supplier
    @order = order
    @result = Result.new(order: order)
  end

  def call
    if can_reject?
      reject_supplier_order_lines
      notify_admin
      log_event
      check_other_supplier_cancellation
    end
    result
  end

private

  attr_reader :supplier, :order, :result

  def can_reject?
    case
    when order.blank?
      result.errors << 'Cannot reject a missing order'
    when supplier.blank? || supplier_order_lines.blank?
      result.errors << 'You don\'t have access to this order'
    end
    result.errors.blank?
  end

  def supplier_order_lines
    return @_supplier_order_lines if @_supplier_order_lines.present?

    lister_options = {
      order: order,
      supplier: supplier,
      confirmed_attendees_only: order.is_team_order?,
      paid_only: order.is_team_order? && order.attendee_pays,
    }
    @_supplier_order_lines ||= OrderLines::List.new(options: lister_options).call
  end

  def reject_supplier_order_lines
    supplier_order_lines.each do |order_line|
      order_line.update(status: 'rejected')
    end
  end

  def notify_admin
    Orders::Emails::SendOrderRejectedAdminEmail.new(order: order, supplier: supplier).delay(queue: :notifications).call
  end

  def log_event
    EventLogs::Create.new(event_object: order, event: 'order-rejected', severity: 'warning', supplier: supplier.name).delay(queue: :notifications).call
  end

  def check_other_supplier_cancellation
    other_supplier_order_lines = order.order_lines.where.not(supplier_profile: supplier).where.not(status: 'rejected')
    if other_supplier_order_lines.present?
      result.warnings << 'Cannot cancel order because of pending order lines from other supplier(s) within the order'
    end
  end

  class Result
    attr_accessor :order, :errors, :warnings

    def initialize(order:)
      @order = order
      @errors = []
      @warnings = []
    end

    def success?
      errors.blank?
    end
  end
end
