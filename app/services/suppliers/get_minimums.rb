class Suppliers::GetMinimums

  SupplierMinimum = Struct.new(:minimum_spend, :lead_time)

  def initialize(suppliers: [], categories: [], category_group: nil)
    @suppliers = suppliers
    @category_group = sanitized_category_group(category_group)
    @categories = categories.present? && categories.first.is_a?(String) ? Category.where(slug: categories) : categories
    @supplier_mapped_minimums = {}
  end

  def call
    return supplier_mapped_minimums if suppliers.blank?

    suppliers.each do |supplier|
      next if supplier_grouped_minimums[supplier].blank?

      supplier_mapped_minimums[supplier] = minimum_details_for(supplier)
    end
    supplier_mapped_minimums
  end

private

  attr_reader :suppliers, :categories, :category_group
  attr_accessor :supplier_mapped_minimums

  def supplier_grouped_minimums
    @_supplier_grouped_minimums ||= all_minimums.group_by(&:supplier_profile)
  end

  def all_minimums
    return @_all_minimums if !@_all_minimums.nil?

    base_minimums = Minimum.where(supplier_profile: suppliers)
    minimums = case
    when categories.present?
      category_minimums = base_minimums.where(category: categories)
      category_minimums = base_minimums.includes(:category).where(categories: { group: categories.map(&:group) }) if category_minimums.blank?
      category_minimums
    when category_group.present?
      base_minimums.includes(:category).where(categories: { group: category_group })
    end
    @_all_minimums = minimums || base_minimums
  end

  def minimum_details_for(supplier)
    SupplierMinimum.new(
      maximum_spend_for(supplier),
      maximum_lead_time_for(supplier)
    )
  end

  def maximum_spend_for(supplier)
    minimums = supplier_grouped_minimums[supplier].presence || []
    minimum = minimums.select{|x| x.spend_price.present? }.max_by(&:spend_price)
    minimum&.spend_price
  end

  def maximum_lead_time_for(supplier)
    minimums = supplier_grouped_minimums[supplier].presence || []
    minimum = case
    when supplier.lead_by_hour?
      minimums.select{|x| x.lead_time.present? }.max_by(&:lead_time)
    else
      minimums.select{|x| x.lead_time_day_before.present? }.min_by(&:lead_time_day_before)
    end
    return 'Unknown' if minimum.blank?

    case
    when supplier.lead_by_hour? && minimum.lead_time.present?
      "#{minimum.lead_time.to_i} hrs"
    when supplier.lead_by_hour?
      'Unknown'
    when minimum.lead_time_day_before.present?
      "#{Time.zone.parse(minimum.lead_time_day_before).to_s(:time_only).strip} (day prior)"
    else
      'Unknown'
    end
  end

  def sanitized_category_group(group)
    case group
    when 'office-snacks', 'kitchen-supplies'
      'kitchen-supplies'
    when 'office-catering', 'catering-services'
      'catering-services'
    else
      group
    end
  end

end
