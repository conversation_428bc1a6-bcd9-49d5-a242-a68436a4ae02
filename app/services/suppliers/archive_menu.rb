class Suppliers::ArchiveMenu

  def initialize(supplier:)
    @supplier = supplier
    @result = Result.new(supplier: @supplier)
  end

  def call
    if can_archive? && archive_menu_sections
      supplier.update(is_searchable: false)
    end
    result
  end

private

  attr_reader :supplier, :result

  def can_archive?
    case
    when supplier.blank?
      result.errors << 'Cannot archive menu of a missing supplier'
    when unarchived_menu_sections.blank?
      result.errors << 'The supplier does not have any active menu items'
    end
    result.errors.blank?
  end

  def unarchived_menu_sections
    return @_unarchived_menu_sections if @_unarchived_menu_sections.present?

    menu_sections = MenuSection.where(supplier_profile: supplier)
    menu_sections = menu_sections.where(archived_at: nil) # only archive active menu sections
    menu_sections = menu_sections.where.not(name: 'custom') # do not archive custom menu section
    menu_sections = menu_sections.includes(:companies).where(companies: { id: nil }) # do not archive menu sections created for specific companies
    @_unarchived_menu_sections = menu_sections
  end

  def archive_menu_sections
    unarchived_menu_sections.each do |menu_section|
      section_archiver = MenuSections::Archive.new(menu_section: menu_section, is_forced: true).call
      if section_archiver.success?
        result.archived_menu_sections << menu_section
      else
        result.errors += section_archiver.errors
      end
    end
    result.errors.blank?
  end

  class Result
    attr_accessor :archived_menu_sections, :errors
    attr_reader :supplier

    def initialize(supplier:)
      @supplier = supplier
      @archived_menu_sections = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
