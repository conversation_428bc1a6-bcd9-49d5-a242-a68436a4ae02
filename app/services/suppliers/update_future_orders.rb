class Suppliers::UpdateFutureOrders
  include ActionView::Helpers::NumberHelper

  OrderUpdate = Struct.new(:id, :name, :customer_name, :old_total, :new_total)

  def initialize(supplier:)
    @supplier = supplier
    @result = Result.new(supplier: supplier)
  end

  def call
    if can_update?
      future_orders.each do |future_order|
        begin
          update_order_lines_for(order: future_order)
          update_totals_for(order: future_order)
        rescue
          result.errors << "Could not update order #{future_order.id}"
        end
      end
    end
    result
  end

private

  attr_reader :supplier, :result

  def can_update?
    case
    when supplier.blank?
      result.errors << 'Cannot update without a supplier'
    when future_orders.blank?
      result.errors << 'No future orders to be updated'
    end
    result.errors.blank?
  end

  def update_order_lines_for(order:)
    customer = order.customer_profile
    order_lines = OrderLines::List.new(options: { order: order, supplier: supplier }).call

    order_lines.group_by(&:location).each do |location, location_order_lines|
      location_order_lines.each do |order_line|
        OrderLines::Upsert.new(order: order, customer: customer, location: location, order_line_params: { id: order_line.id }, update_item: true).call
      end
    end
  end

  def update_totals_for(order:)
    old_total = number_to_currency(order.customer_total.dup, precision: 2)
    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
    order.supplier_profiles.each do |supplier|
      Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, save_totals: true).call
    end
    new_total = number_to_currency(order.customer_total.dup, precision: 2)
    result.updated_orders << OrderUpdate.new(order.id, order.name, order.customer_profile.name, old_total, new_total)
  end

  def future_orders
    @_future_orders ||= begin
      orders = Order.where(status: %w[new amended confirmed paused])
      orders = orders.where('delivery_at > ?', Time.zone.now.beginning_of_day)
      orders = orders.joins(:order_lines).where(order_lines: { supplier_profile: supplier })
      orders.includes(order_lines: :location).distinct
    end
  end

  class Result
    attr_accessor :updated_orders, :errors
    attr_reader :supplier

    def initialize(supplier:)
      @supplier = supplier
      @updated_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end