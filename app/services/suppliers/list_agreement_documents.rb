class Suppliers::ListAgreementDocuments

  def initialize(supplier:, options: {})
    @supplier = supplier
    @options = [default_options, options].inject(&:merge)
  end

  def call
    documents = base
    documents = documents.where(supplier_profile: supplier)
    documents = documents.where(status: options[:status]) if options[:status]
    documents = documents.order(created_at: :desc).first(1) if options[:latest]
    documents
  end

private

  attr_reader :supplier, :options

  def base
    SupplierAgreementDocument.all
  end

  def default_options
    {
      latest: false,
      status: nil,
    }
  end

end
