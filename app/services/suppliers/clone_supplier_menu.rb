class Suppliers::CloneSupplierMenu

  def initialize(old_supplier:, new_supplier:, verbose: false)
    @old_supplier = old_supplier
    @new_supplier = new_supplier
    @verbose = verbose
  end

  def call
    archive_new_supplier_menu

    menu_sections.each do |menu_section|
      puts "Menu Section:  #{menu_section.name}" if verbose
      cloned_section = clone_menu_section_for(menu_section)

      if (category_menu_sections = category_menu_sections_for(menu_section).presence)
        clone_section_categories_for(cloned_section, category_menu_sections)
      end

      if (menu_items = menu_item_for(menu_section))
        clone_menu_items_for(cloned_section, menu_items)
      end
    end
  end

private

  attr_reader :old_supplier, :new_supplier, :verbose

  def archive_new_supplier_menu
    Suppliers::ArchiveMenu.new(supplier: new_supplier).call
  end

  def clone_menu_section_for(menu_section)
    cloned_section = menu_section.dup
    cloned_section.supplier_profile_id = new_supplier.id
    cloned_section.save
    cloned_section
  end

  def clone_section_categories_for(cloned_section, category_menu_sections)
    category_menu_sections.each do |category_menu_section|
      puts "Category: #{category_menu_section.category.name}" if verbose
      duplicate_catetgory = category_menu_section.dup
      duplicate_catetgory.menu_section_id = cloned_section.id
      duplicate_catetgory.save
    end
  end

  def menu_item_for(menu_section)
    @_menu_items ||= {}
    @_menu_items[menu_section] ||= begin
      item_lister_options = {
        menu_section: menu_section,
        supplier: old_supplier,
        show_active: true,
      }
      MenuItems::List.new(options: item_lister_options, includes: %i[serving_sizes menu_extras]).call
    end
  end

  def clone_menu_items_for(cloned_section, menu_items)
    menu_items.each do |menu_item|
      puts "Menu Item:  #{menu_item.name}" if verbose
      cloned_item = menu_item.dup
      cloned_item.supplier_profile_id = new_supplier.id
      cloned_item.menu_section_id = cloned_section.id

      next if !cloned_item.save

      # clone serving sizes
      if (serving_sizes = serving_size_for(menu_item).presence)
        clone_serving_sizes_for(cloned_item, serving_sizes)
      end
      # clone menu extra sections
      if (menu_extra_sections = menu_extra_sections_for(menu_item).presence)
        clone_menu_extra_sections_for(cloned_item, menu_extra_sections)
      end
    end
  end

  def clone_serving_sizes_for(cloned_item, serving_sizes)
    serving_sizes.each do |serving_size|
      puts "Serving Size:  #{serving_size.name}" if verbose
      cloned_serving = serving_size.dup
      cloned_serving.menu_item_id = cloned_item.id
      cloned_serving.save
    end
  end

  def clone_menu_extra_sections_for(cloned_item, menu_extra_sections)
    menu_extra_sections.each do |menu_extra_section|
      puts "Menu Extra Secton:  #{menu_extra_section.name}" if verbose
      cloned_extra_section = menu_extra_section.dup
      cloned_extra_section.menu_item_id = cloned_item.id
      next if !cloned_extra_section.save

      # clone underlying menu extras
      if (menu_extras = menu_extras_for(menu_extra_section).presence)
        clone_menu_extras_for(cloned_extra_section, menu_extras)
      end
    end
  end

  def clone_menu_extras_for(cloned_extra_section, menu_extras)
    menu_extras.each do |menu_extra|
      puts "Menu Extra:  #{menu_extra.name}" if verbose
      cloned_extra = menu_extra.dup
      cloned_extra.menu_extra_section_id = cloned_extra_section.id
      cloned_extra.menu_item_id = cloned_extra_section.menu_item_id
      cloned_extra.save
    end
  end

  def menu_sections
    @_menu_sections ||= begin
      section_lister_options = {
        supplier: old_supplier,
        show_active: true,
        ignore_custom: true,
      }
      MenuSections::List.new(options: section_lister_options, includes: { menu_items: :serving_sizes }).call
    end
  end

  def category_menu_sections_for(menu_section)
    @_category_menu_sections ||= {}
    @_category_menu_sections[menu_section] ||= CategoryMenuSection.where(menu_section: menu_section)
  end

  def serving_size_for(menu_item)
    @_serving_sizes ||= {}
    @_serving_sizes[menu_item] ||= menu_item.serving_sizes.where(archived_at: nil)
  end

  def menu_extra_sections_for(menu_item)
    @_menu_extra_sections ||= {}
    @_menu_extra_sections[menu_item] ||= menu_item.menu_extra_sections.where(archived_at: nil).joins(:menu_extras).where.not(menu_extras: { id: nil }).distinct
  end

  def menu_extras_for(section)
    @_menu_extras ||= {}
    @_menu_extras[section] = section.menu_extras.where(archived_at: nil)
  end

end
