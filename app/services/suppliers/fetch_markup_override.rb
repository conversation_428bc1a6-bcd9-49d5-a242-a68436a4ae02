class Suppliers::FetchMarkupOverride

  VALID_OVERRIDE_FIELDS = %i[markup commission_rate].freeze

  def initialize(supplier:, company: nil, customer: nil, required_override: nil)
    @supplier = supplier
    @customer = customer
    @company = company.presence || customer&.company
    @required_override = required_override
  end

  def call
    return nil if supplier.blank? || (company.blank? && customer.blank?)

    customer_override || company_override
  end

private

  attr_reader :supplier, :customer, :company, :required_override

  def supplier_markup_overrides
    @_supplier_markup_overrides ||= begin
      overrides = supplier.markup_overrides.where(active: true)
      overrides = case
      when required_override == :markup
        overrides.where.not(markup: nil)
      when required_override == :commission_rate
        overrides.where.not(commission_rate: nil)
      when required_override.present?
        Supplier::MarkupOverride.none
      else
        overrides
      end
      overrides
    end
  end

  def customer_override
    return nil if customer.blank?

    supplier_markup_overrides.where(overridable: customer).first
  end

  def company_override
    return nil if company.blank?

    supplier_markup_overrides.where(overridable: company).first
  end

end