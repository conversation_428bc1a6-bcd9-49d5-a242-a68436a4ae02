class Eway::ProcessPayment

  def initialize(payment:)
    @payment = payment
    @result = Result.new
  end

  def call
    payment_response = eway_payment_gateway.purchase(payment_amount_in_cents, payment.credit_card.gateway_token, payment_options)
    if payment_response.params['success'] || result.response_message == '08,Honour With Identification'
      result.auth_code = payment_response.params['auth_code']
      result.transaction_number = payment_response.params['transaction_number']
    else
      result.errors << payment_response.inspect
    end
    result.response_message = payment_response.params['message']
    result
  end

private

  attr_reader :payment, :result

  def eway_payment_gateway
    @_payment_gateway ||= ActiveMerchant::Billing::Base.gateway(:eway_managed).new(
      login: yordar_credentials(:eway, :login),
      username: yordar_credentials(:eway, :username),
      password: yordar_credentials(:eway, :password)
    )
  end

  def payment_options
    {
      invoice: payment.invoice.number,
      description: payment.order.name,
    }
  end

  def payment_amount_in_cents
    (payment.amount * 100).to_i
  end

  class Result
    attr_accessor :response_message, :auth_code, :transaction_number, :errors

    def initialize
      @response_message = nil
      @auth_code = nil
      @transaction_number = nil
      @errors = []
    end

    def success?
      errors.blank? && auth_code.present? && transaction_number.present?
    end
  end

end
