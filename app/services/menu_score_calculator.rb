class MenuScoreCalculator
  def initialize(reviews)
    @reviews = reviews
  end

  def overall_rating
    [taste_rating, presentation_rating, quantity_rating].inject(&:+) / 3
  end

  def taste_rating
    @taste_rating ||= @reviews.map(&:taste).inject(&:+) / review_count
  end

  def presentation_rating
    @presentation_rating ||= @reviews.map(&:presentation).inject(&:+) / review_count
  end

  def quantity_rating
    @quantity_rating ||= @reviews.map(&:quantity).inject(&:+) / review_count
  end

  def see_menu_again_rating
    yes_count = @reviews.map(&:see_again).count(true)

    (yes_count * 100) / review_count
  end

  private

  def review_count
    @review_count ||= @reviews.count
  end
end
