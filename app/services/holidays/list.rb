class Holidays::List

  def initialize(options: {})
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @holidays = base_holidays
    filter_within_date_range if filter_options[:from_date].present? && filter_options[:to_date].present?
    filter_by_query if filter_options[:query].present?
    sort_holidays if filter_options[:order_by].present?
    paginate_holidays if filter_options[:page].present? && filter_options[:limit].present?

    holidays
  end

private
  attr_reader :filter_options, :holidays

  def base_holidays
    Holiday.all
  end

  def filter_within_date_range
    from_date = filter_options[:from_date].is_a?(String) ? Time.zone.parse(filter_options[:from_date]) : filter_options[:from_date]
    to_date = filter_options[:to_date].is_a?(String) ? Time.zone.parse(filter_options[:to_date]) : filter_options[:to_date]
    @holidays = holidays.where(on_date: from_date..to_date)
  end

  def filter_by_query
    holiday_arel = Holiday.arel_table
    name_condition = holiday_arel[:name].matches("%#{filter_options[:query]}%")
    state_condition = holiday_arel[:state].eq("#{filter_options[:query]}")    

    query_condition = name_condition.or(state_condition)
    @holidays = holidays.where(query_condition)

  end

  def sort_holidays
    @holidays = holidays.order(filter_options[:order_by])
  end

  def paginate_holidays
    @holidays = holidays.page(filter_options[:page]).per(filter_options[:limit])
  end

  def default_options
    {
      from_date: nil,
      to_date: nil,
      query: nil,
      order_by: 'on_date ASC, name ASC',
      page: 1,
      limit: 20,
    }
  end

end