class Holidays::LogCalendarEvents

  def initialize(month:)
    @month = month
    @result = Result.new
  end

  def call
    name_and_date_group_holidays.each do |_, holidays|
      log_events_for(holidays: holidays)
    end
    result
  end

private

  attr_reader :month, :result

  def name_and_date_group_holidays
    month_holidays.group_by do |holiday|
      [holiday.on_date, holiday.name]
    end
  end

  def month_holidays
    Holiday.where(on_date: (month.beginning_of_month..month.end_of_month))
  end

  def log_events_for(holidays:)
    if holidays.size == 1
      log_event_for(holiday: holidays.first)
    else
      log_event_for(holiday: holidays.sort_by(&:id).first, states: holidays.map(&:state).sort)
    end
  end

  def log_event_for(holiday:, **event_info)
    event_creator = EventLogs::Create.new(event_object: holiday, event: 'monthly-calendar-event', **event_info).call
    if event_creator.success?
      result.holidays << holiday
      result.events << event_creator.event_log
    else
      result.errors += event_creator.errors
    end
  end

  class Result
    attr_accessor :holidays, :events, :errors

    def initialize
      @holidays = []
      @events = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end