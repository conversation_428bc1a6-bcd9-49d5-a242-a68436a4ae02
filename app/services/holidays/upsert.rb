class Holidays::Upsert

  def initialize(holiday_params:, holiday: nil)
    @holiday_params = holiday_params
    @holiday = holiday.presence || Holiday.new
    @result = Result.new
  end

  def call
    if holiday.update(holiday_params)
      result.holiday = holiday
    else
      result.errors += holiday.errors.full_messages
    end

    result
  end

private

  attr_reader :holiday_params, :holiday, :result

  
  class Result
    attr_accessor :holiday, :errors

    def initialize
      @holiday = nil
      @errors = []
    end

    def success?
      errors.blank? && holiday.present? && holiday.persisted?
    end
  end


end