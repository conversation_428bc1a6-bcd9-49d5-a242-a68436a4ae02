class Holidays::MapFromAPI

  ApiHoliday = Struct.new(:name, :on_date, :state)

  def initialize(year: Time.zone.now.year)
    @year = year
  end

  def call
    mapped_holidays
  end

private

  attr_reader :year

  def api_holidays
    Calendarific::GetHolidays.call(year: year)
  end

  def mapped_holidays
    puts "Mapping holidays for year #{year} from API"
    holidays = []
    api_holidays.each do |holiday|
      states = []
      if holiday.states.instance_of?(String)
        states = ['All']
      else
        states = holiday.states.map{|state| state.abbrev.present? ? state.abbrev.upcase : 'UNKNOWN' }
      end
      states.each do |state|
        holidays << ApiHoliday.new(holiday[:name], holiday.date.iso.to_time.in_time_zone.beginning_of_day, state == 'All' ? nil : state)
      end
    end
    holidays
  end

end
