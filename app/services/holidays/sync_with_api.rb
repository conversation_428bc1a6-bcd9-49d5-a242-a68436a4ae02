class Holidays::SyncWith<PERSON>I

  VALID_STATES = %w[ACT NSW QLD SA VIC WA}].freeze # NT TAS
  IRRELEVANT_HOLIDAYS = [
    'Holy Saturday',
    'Queensland Day',
    'Royal National Agricultural Show Day Queensland',
    'New South Wales Bank Holiday',
    'Christmas Eve',
    'New Year\'s Eve'
  ].freeze

  def initialize(year: nil, dry_run: false)
    @year = year || Time.zone.now.year
    @holidays = api_mapped_holidays
    @dry_run = dry_run
  end

  def call
    puts "Syncing holidays for year #{year}" if !Rails.env.test?
    imported_holidays = []
    filtered_holidays.each do |holiday|
      attributes = attributes_for(holiday)
      if dry_run
        imported_holidays << Holiday.where(attributes).first_or_initialize
      else
        imported_holidays << Holiday.where(attributes).first_or_create
      end
    end
    notify_via_slack(imported_holidays) if !Rails.env.test? && !dry_run
    imported_holidays
  end

private

  attr_reader :year, :dry_run
  attr_accessor :holidays

  def api_mapped_holidays
    Holidays::MapFromAPI.new(year: year).call
  end

  def filtered_holidays
    filter_by_states
    filter_irrelevant_holidays
    filter_out_weekends
    @holidays
  end

  def filter_by_states
    @holidays = holidays.select do |holiday|
      holiday.state.blank? || VALID_STATES.map(&:downcase).include?(holiday.state.downcase)
    end
  end

  def filter_irrelevant_holidays
    @holidays = holidays.reject do |holiday|
      IRRELEVANT_HOLIDAYS.include?(holiday.name)
    end
  end

  def filter_out_weekends
    @holidays = holidays.reject do |holiday|
      holiday.on_date.saturday? || holiday.on_date.sunday?
    end
  end

  def attributes_for(holiday)
    {
      name: sanitized_name_for(holiday),
      on_date: holiday.on_date.beginning_of_day,
      push_to: get_next_available_date(holiday),
      effective_from: holiday.on_date.beginning_of_day,
      effective_to: holiday.on_date.end_of_day,
      state: holiday.state,
    }
  end

  def sanitized_name_for(holiday)
    case
    when holiday.name == 'Proclamation Day'
      'Boxing Day'
    when holiday.state.present? && holiday.state != 'NT' && holiday.name == 'May Day'
      'Labour Day'
    else
      holiday.name.gsub(/ Observed| Additional Public Holiday|Friday before the /, '')
    end
  end

  def state_grouped_holidays
    @_state_grouped_holidays ||= holidays.group_by(&:state)
  end

  def get_next_available_date(holiday)
    state_holidays = holiday.state.present? ? state_grouped_holidays[nil] + state_grouped_holidays[holiday.state] : holidays
    if holiday.on_date.wday == 5 # Friday Holidaysholiday.name == 'Good Friday'
      next_day = holiday.on_date.yesterday
    else
      next_day = holiday.on_date.tomorrow
      while next_day.saturday? || next_day.sunday? || state_holidays.any?{|state_holiday| state_holiday.on_date == next_day} || next_day == next_day.beginning_of_year
        next_day = next_day.tomorrow
      end
    end
    next_day
  end

  def notify_via_slack(holidays)
    message = ":beach_with_umbrella: Synced #{holidays.size} holidays for year #{year}"
    attachments = holidays.collect do |holiday|
      text = "*#{holiday.name} (#{holiday.state || 'ALL'})* - #{holiday.on_date.to_s(:date)} pushed to #{holiday.push_to.to_s(:date)}"
      {
        type: 'mrkdwn',
        text: text,
        color: 'good'
      }
    end
    SlackNotifier.send(message, attachments: attachments)
  end

end
