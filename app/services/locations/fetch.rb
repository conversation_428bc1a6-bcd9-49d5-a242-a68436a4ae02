class Locations::Fetch

  def initialize(order:, location_params: {})
    @order = order
    @location_params = location_params
  end

  def call
    return nil if location_params[:id].blank? && order.blank?

    location = fetch_location
    return nil if location.blank?

    location.save! if location.new_record? && location.valid?
    location
  end

private

  attr_reader :order, :location_params, :location

  def fetch_location
    if location_params[:id].present?
      Location.where(id: location_params[:id]).first
    else
      Location.where(order: order, details: (location_params[:details] || 'Your Office')).first_or_initialize
    end
  end

end
