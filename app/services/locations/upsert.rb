class Locations::Upsert

  def initialize(order:, location: nil, location_params: {})
    @order = order
    @location_params = location_params
    get_order_location_for(location, location_params)
    @result = Result.new(location: location)
  end

  def call
    if order.present? && location.present?
      if location.update(location_params)
        result.location = location.reload
      else
        result.errors += location.errors.full_messages
      end
    else
      result.errors << 'Cannot add/update location without an order/'
    end
    result
  end

private

  attr_reader :order, :location_params
  attr_accessor :result, :location

  def get_order_location_for(location_arg, location_params)
    @location = begin
      if location_arg.present?
        Location.where(order: order, id: location_arg.id).first
      else
        Locations::Fetch.new(order: order, location_params: location_params).call
      end
    end
  end

  class Result
    attr_accessor :location, :errors

    def initialize(location:)
      @location = location
      @errors = []
    end

    def success?
      location.present? && location.valid? && errors.blank?
    end
  end
end
