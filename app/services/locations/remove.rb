class Locations::Remove

  def initialize(order:, location:)
    @order = order
    get_location(location)
    @result = Result.new
  end

  def call
    case
    when order.blank?
      result.errors << 'Cannot remove location without an order'
    when location.blank?
      result.errors << 'Cannot remove non-existant location'
    else
      remove_location_order_lines if location.order_lines.present?
      location.destroy
    end
    result
  end

private

  attr_reader :order
  attr_accessor :location, :result

  def get_location(input_location)
    @location = input_location.present? && Location.where(order: order, id: input_location.id).first
  end

  def remove_location_order_lines
    location.order_lines.each do |order_line|
      OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call
    end
  end

  class Result
    attr_accessor :errors

    def initialize
      @errors = []
    end

    def success?
      @errors.blank?
    end
  end

end
