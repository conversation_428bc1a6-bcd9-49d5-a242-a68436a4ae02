class Suburbs::Upsert

  def initialize(suburb_params:, suburb: nil)
    @suburb_params = suburb_params
    @suburb = suburb.presence || Suburb.new
    @result = Result.new
  end

  def call
    if suburb.update(suburb_params)
      save_deliverable_suburbs
      result.suburb = suburb
    else
      result.errors += suburb.errors.full_messages
    end

    result
  end

private

  attr_reader :suburb_params, :suburb, :result

  def save_deliverable_suburbs
    Suburbs::SaveDeliverableSuburbs.new(suburb: suburb).delay(queue: :data_integrity).call
  end

  class Result
    attr_accessor :suburb, :errors

    def initialize
      @suburb = nil
      @errors = []
    end

    def success?
      errors.blank? && suburb.present? && suburb.persisted?
    end
  end


end