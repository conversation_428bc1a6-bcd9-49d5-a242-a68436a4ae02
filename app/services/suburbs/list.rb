class Suburbs::List

  def initialize(options: {}, includes: [])
    @options = [default_options, options.to_h.deep_symbolize_keys].inject(&:merge)
    @includes = includes
  end

  def call
    @suburbs = base
    filter_by_postcode if options[:postcode].present?
    filter_by_name if options[:name].present?
    filter_by_term if options[:term].present?
    filter_by_country_code if options[:country_code].present?
    order_suburbs if options[:order_by].present?
    limit_suburbs if options[:limit].present?
    filter_best_matched if options[:best_matched_to].present?
    suburbs
  end

private

  attr_reader :options, :includes, :suburbs

  def base
    base_suburbs = Suburb.all
    base_suburbs = base_suburbs.includes(includes) if includes.present?
    base_suburbs
  end

  def filter_by_postcode
    @suburbs = suburbs.where('postcode ilike :str', str: "%#{options[:postcode]}%")
  end

  def filter_by_name
    @suburbs = suburbs.where('name ilike :str', str: "%#{options[:name]}%")
  end

  def filter_by_term
    @suburbs = suburbs.where('name ilike :str OR postcode ilike :str ', str: "%#{options[:term]}%")
  end

  def filter_by_country_code
    @suburbs = suburbs.where('country_code ilike :str ', str: options[:country_code])
  end

  def order_suburbs
    @suburbs = suburbs.order(options[:order_by])
  end

  def limit_suburbs
    @suburbs = suburbs.limit(options[:limit])
  end

  def filter_best_matched
    best_suburb = suburbs.detect{|suburb| suburb.name == options[:best_matched_to][:name] && suburb.postcode == options[:best_matched_to][:postcode]} # check if name and postcode match
    best_suburb = suburbs.detect{|suburb| suburb.name == options[:best_matched_to][:name] } if best_suburb.blank? # check if name matches
    best_suburb = suburbs.first if best_suburb.blank? # default to first
    @suburbs = [best_suburb]
  end

  def default_options
    {
      postcode: nil,
      name: nil,
      term: nil,
      country_code: 'AU',
      order_by: :id,
      limit: nil,
      best_matched_to: {}
    }
  end

end
