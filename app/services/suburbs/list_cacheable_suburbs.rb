class Suburbs::ListCacheableSuburbs

  MAJOR_SUBURBS = %w[Sydney Melbourne Adelaide Brisbane Perth].freeze
  DISTANCE_THRESHOLD = 5 # kms

  def initialize
    @cacheable_suburbs = []
  end

  def call
    MAJOR_SUBURBS.each do |major_suburb_name|
      @cacheable_suburbs += major_suburbs = Suburb.where(name: major_suburb_name)
      @cacheable_suburbs += postcode_suburbs(major_suburbs)
      @cacheable_suburbs += ranged_suburbs(major_suburbs)
    end
    unique_suburbs
  end

private

  attr_reader :cacheable_suburbs

  def postcode_suburbs(suburbs)
    Suburb.where(postcode: suburbs.map(&:postcode))
  end

  def state_suburbs(state)
    return @_state_suburbs[state] if @_state_suburbs.present? && @_state_suburbs[state].present?

    @_state_suburbs ||= {}
    @_state_suburbs[state] = Suburb.where(state: state)
  end

  def ranged_suburbs(suburbs)
    state_suburbs(suburbs.first.state).select do |state_suburb|
      suburbs.any? do |suburb|
        distance = Haversine.distance(state_suburb.latitude, state_suburb.longitude, suburb.latitude, suburb.longitude).to_kilometers
        distance < DISTANCE_THRESHOLD
      end
    end
  end

  def unique_suburbs
    cacheable_suburbs.uniq do |suburb|
      [suburb.name, suburb.state]
    end
  end
end

