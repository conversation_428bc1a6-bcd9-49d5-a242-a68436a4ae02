class Suburbs::FetchForSearch

  SUBURB_FIELDS_MAP = {
    suburb: :yordar_suburb,
    state: :yordar_state,
    postcode: :yordar_postcode,
  }.freeze

  def initialize(suburb_params:, suburb_cookies: {}, host: nil)
    @suburb_params = suburb_params
    @suburb_cookies = suburb_cookies
    @host = host
  end

  def call
    @suburb = case
    when find_by_name_params?
      Suburb.where(name_conditions).first
    when suburb_id.present?
      Suburb.where(id: suburb_id).first
    end
    set_suburb_cookies
    suburb
  end

private

  attr_reader :suburb_params, :suburb_cookies, :host
  attr_accessor :suburb

  def find_by_name_params?
    SUBURB_FIELDS_MAP.keys.detect{|field| suburb_params[field].present? }.present?
  end

  def suburb_id
    @_suburb_id ||= suburb_params[:suburb_id] || suburb_cookies[:yordar_suburb_id]
  end

  def name_conditions
    conditions = []
    options = {}
    if suburb_params[:suburb].present?
      conditions << 'name ilike :name'
      options[:name] = suburb_params[:suburb].gsub(/-|_/, ' ')
    end
    if suburb_params[:state].present?
      conditions << 'state ilike :state'
      options[:state] = suburb_params[:state].gsub(/-|_/, ' ')
    end
    if suburb_params[:postcode].present?
      conditions << 'postcode = :postcode'
      options[:postcode] = suburb_params[:postcode]
    end
    [conditions.join(' AND '), options]
  end

  def set_suburb_cookies
    SUBURB_FIELDS_MAP.each do |field, cookie_name|
      if suburb.present? && suburb_params[field].present?
        suburb_cookies[cookie_name] = { value: suburb_params[field], domain: cookie_domain(host: host) }
      else
        suburb_cookies.delete(cookie_name)
      end
    end
    if suburb.present?
      # added wildcard domain so cookies are shared on all yordar applications (including server-side requests)
      suburb_cookies[:yordar_street_address] = { value: suburb_params[:street_address], domain: cookie_domain(host: host) } if (find_by_name_params? && suburb_params[:street_address].present?) || suburb_changed?
      suburb_cookies[:yordar_suburb_id] = { value: suburb.id, domain: cookie_domain(host: host) }
      suburb_cookies[:yordar_suburb_label] = { value: suburb.label, domain: cookie_domain(host: host) }
      suburb_cookies[:yordar_suburb] ||= { value: suburb.name, domain: cookie_domain(host: host) } if suburb.present?
      suburb_cookies[:yordar_postcode] ||= { value: suburb.postcode, domain: cookie_domain(host: host) }
      suburb_cookies[:yordar_state] ||= { value: suburb.state, domain: cookie_domain(host: host) }
    else
      suburb_cookies.delete(:yordar_suburb_id)
      suburb_cookies.delete(:yordar_suburb_label)
      suburb_cookies.delete(:yordar_street_address)
    end
  end

  def suburb_changed?
    suburb_cookies[:yordar_suburb_id].blank? || suburb_cookies[:yordar_suburb_id].to_i != suburb.id
  end

end
