class Suburbs::SaveDeliverableSuburbs
  
  def initialize(suburb:)
    @suburb = suburb
  end

  def call
    delivery_zones_in_range.each do |delivery_zone|
      save_deliverable_suburbs_for(delivery_zone)
    end
  end

private
  attr_reader :suburb

  def delivery_zones_in_range
    DeliverableSuburb.where(suburb: suburbs_in_range).includes(:delivery_zone).map(&:delivery_zone).uniq
  end

  def suburbs_in_range
    state_suburb_in_range = state_suburbs.select do |state_suburb|
      distance_for(state_suburb) < max_radius
    end

    state_suburb_in_range + postcode_suburbs
  end

  def state_suburbs
    @_state_suburbs = Suburb.where.not(latitude: nil).where.not(longitude: nil).where(state: suburb.state).where.not(id: postcode_suburbs.select(:id)).where.not(id: suburb.id)
  end

  def postcode_suburbs
    @_postcode_suburbs = Suburb.where.not(latitude: nil).where.not(longitude: nil).where(postcode: suburb.postcode)
  end

  def distance_for(state_suburb)
    Haversine.distance(suburb.latitude, suburb.longitude, state_suburb.latitude, state_suburb.longitude).to_kilometers
  end

  def max_radius
    DeliveryZone.all.select(:radius).map(&:radius).max
  end

  def save_deliverable_suburbs_for(delivery_zone)
    DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone, refresh: true).call
  end

end