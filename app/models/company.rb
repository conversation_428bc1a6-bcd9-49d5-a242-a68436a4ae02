# == Schema Information
#
# Table name: companies
#
#  id                     :integer          not null, primary key
#  name                   :string(255)
#  address                :text
#  contact_name           :string(255)
#  contact_phone          :string(255)
#  email                  :string(255)
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  can_pay_on_account     :boolean
#  suburb_id              :integer
#  old_id                 :integer
#  payment_term_days      :integer          default(21)
#  invoice_by_po          :boolean          default(FALSE)
#  requires_po            :boolean
#  can_pay_by_credit_card :boolean          default(TRUE)
#  abn_acn                :string
#

class Company < ActiveRecord::Base
	require 'csv'

	DEFAULT_PAYMENT_TERM_DAYS = 21
	VALID_PAYMENT_TERM_DAYS = [7, 14, 21, 30, 45, 60].freeze

	validates :name, presence: true
	validates_format_of :email, with: /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\z/i, allow_blank: true
	validates :payment_term_days, inclusion: { in: VALID_PAYMENT_TERM_DAYS }
	validates :accounting_software, inclusion: { in: CustomerFlags::VALID_ACCOUNTING_SOFTWARES }, allow_blank: true
	validate :at_least_one_payment_option

	has_many :customer_profiles
	has_many :rate_cards
	has_and_belongs_to_many :menu_sections

	has_many :supplier_markup_overrides, class_name: 'Supplier::MarkupOverride', as: :overridable
	has_many :promotion_subscriptions, as: :subscriber

	# when can pay on account is set to false, update all associated customers' billing frequency preference
	after_save :update_customers_billing_frequency

	# when trying to overwrite customer_profile_ids for this company, we need to reassign the deleted company's billing frequency
	# otherwise: customer_profile_ids = [] will update and NOT trigger customer profile call backs
	def customer_profile_ids=(ids)
		updatable_customer_ids = customer_profile_ids - ids.map(&:to_i)
		CustomerProfile.where(id: updatable_customer_ids).each do |customer|
			next if customer.billing_details.blank?

			customer.billing_details.update(frequency: 'instantly')
		end
		super
	end

private

	def at_least_one_payment_option
		if !can_pay_on_account && !can_pay_by_credit_card
			errors.add(:payment_option, 'at least one payment option should be available')
		end
	end

	def update_customers_billing_frequency
		if saved_change_to_can_pay_on_account? && !can_pay_on_account
			customer_profiles.each do |customer|
				next if customer.billing_details.blank?

				customer.billing_details.update(frequency: 'instantly')
			end
		end
	end

end
