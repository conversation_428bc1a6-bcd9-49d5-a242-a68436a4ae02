require 'active_merchant/billing/rails'
# == Schema Information
#
# Table name: credit_cards
#
#  id                :integer          not null, primary key
#  number            :string(255)
#  expiry_month      :integer
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  expiry_year       :integer
#  gateway_token     :string(255)
#  name              :string(255)
#  cvv               :string
#  label             :string(255)
#  enabled           :boolean          default(TRUE)
#  event_attendee_id :integer
#  saved_for_future  :boolean          default(TRUE)
#  pay_on_account    :boolean          default(FALSE)
#  auto_pay_invoice  :boolean          default(FALSE)
#  stripe_token      :string
#  brand             :string
#  country_code      :string
#

class CreditCard < ActiveRecord::Base

	validates :name, presence: true

	has_and_belongs_to_many :customer_profiles
	belongs_to :event_attendee

	def self.default_scope
		where(enabled: true)
	end

	def expiry_label
		"#{expiry_month}/#{expiry_year}"
	end

	# Check associated orders have any payment errors
	def has_failed_orders?
		Order.where(credit_card_id: id, payment_status: 'error').count > 0
	end

	# Check associated orders have any pending payments or errors
	def has_pending_orders?
		Order.where(credit_card_id: id).where(status: %w[new amended confirmed pending quoted delivered]).where.not(payment_status: 'paid').count > 0
	end

	def fetched_surcharge
		@_fetched_surcharge ||= CreditCards::FetchSurcharge.new(credit_card: self).call
	end

	def surcharge_percent
		fetched_surcharge.percent
	end

	def surcharge_fee
		fetched_surcharge.fee
	end

	def invoice_only_card?
		!pay_on_account? && customer_profiles.blank?
	end

	def is_stripe_card?
		stripe_token.present?
	end

	def expired?
		expiry_date = Date.parse("#{expiry_year}-#{expiry_month}-01").end_of_month
		Date.today.end_of_month > expiry_date
	end

	def last4
		label&.scan(/\d/)&.join
	end

	def brand_label
		case brand
		when 'mastercard'
			'MasterCard'
		when 'amex'
			'American Express'
		else
			brand.humanize
		end
	end

end
