# == Schema Information
#
# Table name: weekly_menu_clients
#
#  id                  :integer          not null, primary key
#  title               :string
#  navigation_title    :string
#  slug                :string
#  header_image        :string
#  logo                :string
#  header_content      :text
#  abbreviations       :text
#  monday              :boolean          default(FALSE)
#  tuesday             :boolean          default(FALSE)
#  wednesday           :boolean          default(FALSE)
#  thursday            :boolean          default(FALSE)
#  friday              :boolean          default(FALSE)
#  saturday            :boolean          default(FALSE)
#  sunday              :boolean          default(FALSE)
#  customer_profile_id :integer
#

class WeeklyMenuClient < ActiveRecord::Base

  mount_uploader :header_image, WeeklyMenuClientHeaderImageUploader
  mount_uploader :logo, WeeklyMenuClientLogoUploader

  validates :title, presence: true
  validates :navigation_title, presence: true
  validates :slug, presence: true
  validates :header_content, presence: true
  validates :abbreviations, presence: true

  belongs_to :customer_profile, required: true
  has_many :weekly_menus, -> { order(week_of: :desc) }

end
