# not an active_record model, just used to store supplier spends for a team order
class SupplierSpend

  attr_reader :supplier, :total_spend, :minimum_spend

  def initialize(supplier:, total_spend:, minimum_spend:)
    @supplier = supplier
    @total_spend = total_spend
    @minimum_spend = minimum_spend
  end

  def is_under?
    total_spend < minimum_spend
  end

  def remaining_spend
    remaining = minimum_spend - total_spend
    remaining > 0 ? remaining : nil
  end
end
