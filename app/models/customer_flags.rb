class CustomerFlags < ApplicationRecord

  VALID_ORDER_VIEWS = %w[list calendar].freeze
  VALID_DEPARTMENT_ID_FORMATS = %w[free-text digits 4-digits].freeze
  VALID_ACCOUNTING_SOFTWARES = %w[ariba corrigo coupa].freeze

  validates :customer_profile, presence: true
  validates :default_orders_view, inclusion: { in: VALID_ORDER_VIEWS }
  validates :requires_department_identity, inclusion: { in: VALID_DEPARTMENT_ID_FORMATS }, allow_blank: true
  validates :accounting_software, inclusion: { in: (VALID_ACCOUNTING_SOFTWARES + ['none']) }, allow_blank: true

  belongs_to :customer_profile

end
