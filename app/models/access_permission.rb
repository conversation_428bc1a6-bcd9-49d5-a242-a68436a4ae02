# == Schema Information
#
# Table name: access_permissions
#
#  id                  :bigint           not null, primary key
#  admin_id            :bigint
#  customer_profile_id :bigint
#  active              :boolean          default(TRUE)
#  scope               :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class AccessPermission < ApplicationRecord

  VALID_SCOPES = %w[full_access company_team_admin account_manager pantry_manager].freeze

  validates :admin, presence: true
  validates :customer_profile, presence: true, uniqueness: { scope: :admin, message: 'Can only have one Access Permission for this customer' }
  validates :scope, inclusion: { in: VALID_SCOPES }, allow_nil: true

  belongs_to :admin, class_name: 'CustomerProfile', foreign_key: :admin_id
  belongs_to :customer_profile

end
