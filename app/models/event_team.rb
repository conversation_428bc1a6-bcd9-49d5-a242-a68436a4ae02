# == Schema Information
#
# Table name: event_teams
#
#  id                  :integer          not null, primary key
#  customer_profile_id :integer
#  name                :string
#  active              :boolean          default(TRUE)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class EventTeam < ActiveRecord::Base
  belongs_to :customer_profile
  has_many :event_attendee_teams
  has_and_belongs_to_many :event_attendees

  validates :name, uniqueness: { scope: :customer_profile_id, case_sensitive: false }, presence: true
end
