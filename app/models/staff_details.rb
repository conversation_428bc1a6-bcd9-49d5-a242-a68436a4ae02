class StaffDetails < ApplicationRecord

  VERSION_REF_FIELDS = %i[personal emergency_contact bank tax].freeze

  validates :customer_profile, presence: true

  belongs_to :customer_profile

  def complete?
    personal.present? && personal.values.all?(&:present?) &&
      emergency_contact.present? && emergency_contact.values.all?(&:present?) &&
      bank.present? && bank.values.all?(&:present?) &&
      tax.present? && (tax['tfn'].present? || tax['abn'].present?) &&
      documents.present? && documents.values.all?(&:present?)
  end

  # used to determine if accounts team need to be notified again
  def current_version_ref
    reference = ''
    reference = VERSION_REF_FIELDS.map do |field|
      value = send(field)
      next if value.blank?

      value.to_json
    end.reject(&:blank?).join('')
    Digest::MD5.hexdigest(reference)
  end

end
