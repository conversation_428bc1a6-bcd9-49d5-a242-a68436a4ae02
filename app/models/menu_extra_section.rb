# == Schema Information
#
# Table name: menu_extra_sections
#
#  id           :bigint           not null, primary key
#  menu_item_id :bigint
#  name         :string
#  weight       :integer
#  min_limit    :integer
#  max_limit    :integer
#  archived_at  :datetime
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
class MenuExtraSection < ApplicationRecord

  validates :name, presence: true
  validates :menu_item, presence: true

  belongs_to :menu_item, touch: true
  has_many :menu_extras

end
