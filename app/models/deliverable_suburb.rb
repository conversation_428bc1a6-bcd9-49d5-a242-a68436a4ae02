# == Schema Information
#
# Table name: deliverable_suburbs
#
#  id                  :bigint           not null, primary key
#  delivery_zone_id    :bigint
#  supplier_profile_id :bigint
#  suburb_id           :bigint
#  distance            :decimal(, )
#
class DeliverableSuburb < ApplicationRecord

  validates :delivery_zone, presence: true
  validates :supplier_profile, presence: true
  validates :suburb, presence: true

  belongs_to :delivery_zone
  belongs_to :supplier_profile
  belongs_to :suburb

end
