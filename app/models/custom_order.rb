# This model is solely created to show a
# scoped view of Order in Rails_Admin
# (Delete this model when we trash rails_admin, clean init/rails_admin.rb and app_ability file )
#
#
class CustomOrder < Order

  # scope to show the upcoming delivery only
  default_scope { where(order_variant: 'event_order', order_type: 'one-off') }

  def self.model_name
    # to make sure the correct model is picked up by
    # rails_admin for edit and update
    Order.model_name
  end

end
