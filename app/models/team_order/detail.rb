# == Schema Information
#
# Table name: team_order_details
#
#  id                           :integer          not null, primary key
#  order_id                     :integer
#  attendee_pays                :boolean          default(FALSE)
#  cutoff_2hr_reminder          :datetime
#  cutoff_30m_reminder          :datetime
#  cutoff_option                :string
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  anonymous_attendees_reminder :datetime
#  budget                       :decimal(10, 2)
#  hide_budget                  :boolean          default(FALSE)
#  package_id                   :string
#
class TeamOrder::Detail < ActiveRecord::Base

  VALID_CUTOFF_OPTIONS = %w[charge_to_minimum cancel_order].freeze

  validates :cutoff_option, inclusion: { in: VALID_CUTOFF_OPTIONS, allow_blank: true }

  belongs_to :order
  has_many :levels, class_name: 'TeamOrder::Level', foreign_key: :team_order_detail_id, dependent: :destroy

  def is_package_order?
    package_id.present?
  end

end
