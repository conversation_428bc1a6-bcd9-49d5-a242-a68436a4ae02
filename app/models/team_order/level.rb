# == Schema Information
#
# Table name: team_order_levels
#
#  id                   :bigint           not null, primary key
#  team_order_detail_id :bigint
#  name                 :string
#  is_default           :boolean          default(FALSE)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
class TeamOrder::Level < ApplicationRecord

  validates :team_order_detail, presence: true
  validates :name, uniqueness: { scope: :team_order_detail_id, case_sensitive: false }, presence: true

  belongs_to :team_order_detail, class_name: 'TeamOrder::Detail', foreign_key: :team_order_detail_id

end
