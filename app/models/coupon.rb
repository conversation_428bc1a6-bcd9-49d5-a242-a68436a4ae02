# == Schema Information
#
# Table name: coupons
#
#  id                       :integer          not null, primary key
#  code                     :string           not null
#  description              :string
#  valid_from               :date             not null
#  valid_until              :date
#  redemption_limit         :integer          default(1), not null
#  coupon_redemptions_count :integer          default(0), not null
#  amount                   :integer          default(0), not null
#  type                     :string           not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#

class Coupon < ActiveRecord::Base
	# https://apidock.com/rails/ActiveRecord/Base/inheritance_column/class
	self.inheritance_column = :_type_disabled

	VALID_TYPES = %w[amount percentage].freeze

	validates :code, presence: true
	validates :valid_from, presence: true
	validates :redemption_limit, presence: true
	validates :amount, presence: true
	validates :type, presence: true, inclusion: { in: VALID_TYPES }

	has_many :orders
	has_many :redemptions, class_name: 'CouponRedemption'

	after_initialize :default_values
	before_save :strip_code, if: -> { code.present? }

	def calculated_discount(amount)
		Coupons::CalculateDiscount.new(coupon: self, amount: amount).call
	end

	def discount(amount)
		calculated_discount(amount)[:discount].to_d
	end

	def total(amount)
		calculated_discount(amount)[:total].to_d
	end

	def expired?
		valid_until.present? ? valid_until < Time.zone.today : false
	end

	def can_redeem?
		!expired? && coupon_redemptions_count < redemption_limit
	end

	def discount_note
		if type == 'amount'
			"$#{amount} OFF"
		else
			"#{amount}% OFF"
		end
	end

	def redeemed?
		coupon_redemptions_count != 0
	end

	# used as coupon name in rails admin
	def name
		code
	end

private

	# callback methods
	def default_values
		if new_record?
			self.code ||= "YOR-#{SecureRandom.hex(6)[0, 6].upcase}"
			self.valid_from ||= Time.zone.now
			self.redemption_limit ||= 1
			self.type ||= 'amount'
		end
	end

	def strip_code
		self.code = self.code.strip
	end

end
