# == Schema Information
#
# Table name: minimums
#
#  id                   :integer          not null, primary key
#  spend_price          :decimal(10, 2)   default(0.0)
#  category_id          :integer
#  supplier_profile_id  :integer
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  lead_time            :decimal(10, 2)   default(0.0)
#  lead_time_day_before :string(255)      default("")
#
class Minimum < ActiveRecord::Base

	validates :category_id, presence: true
	validates :spend_price, presence: true, numericality: { greater_than_or_equal_to: 0 }

	# these two fields should be either/or
	validates :lead_time, numericality: { greater_than_or_equal_to: 0 }, if: -> { lead_time_day_before.blank? }
	validates :lead_time_day_before, presence: true, if: -> { lead_time.blank? }
	validates_associated :category, :supplier_profile

	belongs_to :category
	belongs_to :supplier_profile

  def lead_time_day_in_numbers
    return nil if lead_time_day_before.blank?

    lead_time_day_before.remove(':').to_i
  end

  def formatted_lead_time
    case
    when lead_time_day_before.present?
      "#{Time.zone.parse(lead_time_day_before).to_s(:time_only).strip} day before"
    else
      "#{lead_time.to_i} hrs"
    end
  end

end
