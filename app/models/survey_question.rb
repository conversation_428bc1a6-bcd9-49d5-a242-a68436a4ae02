# == Schema Information
#
# Table name: survey_questions
#
#  id                 :bigint           not null, primary key
#  employee_survey_id :bigint
#  label              :string
#  input_type         :string
#  options            :text             default([]), is an Array
#  active             :boolean          default(TRUE)
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  position           :integer
#
class SurveyQuestion < ApplicationRecord

  VALID_INPUT_TYPES = %w[text ratings toggle single-select multi-select].freeze

  validates :employee_survey, presence: true
  validates :label, presence: true
  validates :input_type, presence: true, inclusion: { in: VALID_INPUT_TYPES }
  validates :position, presence: true

  belongs_to :employee_survey
  has_many :survey_answers, dependent: :destroy

end
