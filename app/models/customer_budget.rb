# == Schema Information
#
# Table name: customer_budgets
#
#  id                         :bigint           not null, primary key
#  customer_profile_id        :bigint
#  value                      :decimal(10, 2)
#  frequency                  :string
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  starts_on                  :date
#  ends_on                    :date
#  customer_purchase_order_id :integer
#
class CustomerBudget < ApplicationRecord

  VALID_BUDGET_FREQUENCIES = %w[weekly monthly yearly].freeze

  validates :customer_profile, presence: true
  validates :value, presence: true
  validates :starts_on, presence: true
  validates :frequency, presence: true, inclusion: { in: VALID_BUDGET_FREQUENCIES }

  validate :valid_dates

  belongs_to :customer_profile
  belongs_to :customer_purchase_order

  delegate :po_number, to: :customer_purchase_order, allow_nil: true

private

  def valid_dates
    if starts_on.present? && ends_on.present? && ends_on < starts_on
      errors.add(:base, 'Must end after it starts')
    end
  end

end
