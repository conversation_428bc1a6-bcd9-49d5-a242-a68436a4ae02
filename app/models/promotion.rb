class Promotion < ApplicationRecord

  VALID_KINDS = %w[amount percentage].freeze

  validates :name, presence: true
  validates :amount, presence: true
  validates :kind, presence: true, inclusion: { in: VALID_KINDS }
  validates :category_restriction, inclusion: { in: Category::VALID_CATEGORY_GROUPS }, allow_blank: true
  validates :valid_from, presence: true
  validate :valid_dates, if: -> { valid_until.present? }

  has_many :subscriptions, class_name: 'PromotionSubscription', foreign_key: :promotion_id
  has_many :active_subscriptions, -> { where(active: true ) }, class_name: 'PromotionSubscription', foreign_key: :promotion_id
  has_many :orders

  def discount_note
    if kind == 'amount'
      "$#{amount} OFF"
    else
      "#{amount}% OFF"
    end
  end

private

  def valid_dates
    if valid_until <= valid_from
      errors.add(:valid_until, 'Must end after it starts')
    end
  end

end
