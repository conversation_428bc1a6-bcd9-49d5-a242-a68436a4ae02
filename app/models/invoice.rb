# == Schema Information
#
# Table name: invoices
#
#  id             :integer          not null, primary key
#  number         :string(255)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  amount_price   :decimal(10, 2)
#  payment_status :string(255)      default("unpaid")
#  payment_value  :decimal(10, 2)   default(0.0)
#  pushed_to_xero :boolean          default(FALSE)
#  uuid           :string
#  from_at        :datetime
#  to_at          :datetime
#  due_at         :datetime
#  do_not_notify  :boolean          default(FALSE)
#

class Invoice < ActiveRecord::Base

	DEFAULT_PAYMENT_DAYS = 7
	VALID_STATUSES = %w[draft pending amended confirmed voided deleted].freeze
	VALID_PAYMENT_STATUSES = %w[unpaid partial paid].freeze

	validates :status, inclusion: { in: VALID_STATUSES }, allow_blank: true
	validates :payment_status, inclusion: { in: VALID_PAYMENT_STATUSES }
	validates :number, presence: true
	validates :customer_profile, presence: true
	validates :from_at, presence: true
	validates :to_at, presence: true
	validates :due_at, presence: true
	validates :uuid, presence: true

	has_many :orders, foreign_key: :invoice_id, inverse_of: :invoice
	has_many :gst_free_orders, class_name: 'Order', foreign_key: :gst_free_invoice_id, inverse_of: :gst_free_invoice
	has_many :order_lines, through: :orders
	has_one :payment
	belongs_to :customer_profile
	belongs_to :customer_purchase_order, foreign_key: :cpo_id

	has_many :documents, as: :documentable

	delegate :po_number, to: :customer_purchase_order, allow_nil: true

	def invoice_orders
		Order.where(id: (order_ids + gst_free_order_ids))
	end

  # used for secure links to the invoice page
	def to_param
		uuid
	end

	# used for pdf generating and xero invoice pushing
	# invoice_to: based on frequency, get end of last week/month/yesterday
	# invoice_from: based on frequency, get beginning of last week/month/yesterday
	def invoice_to_from_date
		first_order = orders.first
		if orders.count > 1 || (first_order.credit_card.present? && first_order.credit_card.pay_on_account?)
			case first_order.customer_profile.billing_frequency
			when 'weekly'
				invoice_to = (created_at - 1.week).end_of_week
				invoice_from = (created_at - 1.week).beginning_of_week
			when 'monthly'
				invoice_to = (created_at - 1.month).end_of_month
				invoice_from = (created_at - 1.month).beginning_of_month
			end
		end

		# invoice with credit card payment or 'instant' as billing frequency
		if invoice_to.blank? || invoice_from.blank?
			invoice_to = (created_at - 1.day).end_of_day
			invoice_from = (created_at - 1.day).beginning_of_day
		end

		{ from_date: invoice_from, to_date: invoice_to }
	end

	def latest_document(kind: nil)
		document_kind = kind
		document_kind ||= paid? ? 'tax_invoice_receipt' : 'tax_invoice'
		latest_documents = documents.where(kind: document_kind)
		latest_documents = latest_documents.order(version: :desc)
		document = latest_documents.first
		document = latest_document(kind: 'tax_invoice') if paid? && document_kind == 'tax_invoice_receipt' && document.blank?
		document
	end

	def paid?
		payment_status == 'paid'
	end

	def overdue?
		!paid? && due_at.present? && due_at.end_of_day < Time.zone.now.beginning_of_day
	end
end
