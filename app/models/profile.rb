# == Schema Information
#
# Table name: profiles
#
#  id               :integer          not null, primary key
#  user_id          :integer
#  profileable_id   :integer
#  profileable_type :string(255)
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  avatar           :string(255)
#

class Profile < ActiveRecord::Base
	# TODO: issues with session handling and avatar model object related to upload. need to fix this later
	# mount_uploader :avatar, Uploader

	validates_presence_of :user_id
	validates_uniqueness_of :user_id

	belongs_to :user, foreign_key: :user_id, inverse_of: :profile
	belongs_to :profileable, polymorphic: true # supplier or customer..

	accepts_nested_attributes_for :profileable, :user

	# short-hand to find the profile type
	# used in supplier protected page area
	#
	def is_customer?
		profileable_type == 'CustomerProfile'
	end

	def is_supplier?
		profileable_type == 'SupplierProfile'
	end

end
