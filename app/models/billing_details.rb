# == Schema Information
#
# Table name: billing_details
#
#  id                     :bigint           not null, primary key
#  customer_profile_id    :bigint
#  name                   :string
#  email                  :string
#  address                :string
#  suburb_id              :bigint
#  phone                  :string
#  frequency              :string           default("instantly")
#  order_summaries        :boolean          default(FALSE)
#  summary_report         :boolean          default(FALSE)
#  invoice_order_grouping :string           default("address_po")
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  billing_day            :integer
#  hide_delivery_pricing  :boolean          default(FALSE)
#  invoice_spreadsheet    :boolean          default(FALSE)
#
class BillingDetails < ApplicationRecord

  VALID_FREQUENCIES = %w[instantly weekly monthly].freeze
  VALID_INVOICE_ORDER_GROUPINGS = %w[address address_po purchase_order delivery_date].freeze

  validates :customer_profile, :name, :email, :address, :suburb, :phone, presence: true
  validates :frequency, inclusion: { in: VALID_FREQUENCIES }, presence: true
  validates :invoice_order_grouping, inclusion: { in: VALID_INVOICE_ORDER_GROUPINGS }, presence: true

  belongs_to :customer_profile
  belongs_to :suburb

end
