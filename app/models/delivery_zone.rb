# == Schema Information
#
# Table name: delivery_zones
#
#  id                    :integer          not null, primary key
#  suburb_id             :integer
#  radius                :integer
#  delivery_fee          :decimal(10, 2)
#  supplier_profile_id   :integer
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  operating_wdays       :string(255)
#  operating_hours_start :integer
#  operating_hours_end   :integer
#
class DeliveryZone < ActiveRecord::Base

	validates_associated :suburb, :supplier_profile

	validates :suburb_id, presence: true
	validates :radius, :delivery_fee, numericality: { greater_than_or_equal_to: 0, message: 'must be >= 0' }
	validates :operating_hours_start, :operating_hours_end, numericality: { greater_than_or_equal_to: 0, message: 'invalid time. HH:MM' }, allow_blank: true
	validates :operating_hours_start, :operating_hours_end, presence: { message: 'must have both' }, if: :at_least_one_time_present?
	validates :operating_wdays, presence: true
	validate :valid_operating_wdays

	belongs_to :suburb
	belongs_to :supplier_profile
	has_many :deliverable_suburbs, dependent: :destroy

private

	# validation
	def valid_operating_wdays
		if operating_wdays == '0000000' # seven 0's
			errors.add(:base, 'Must have at least one day')
		end
	end

	def at_least_one_time_present?
		operating_hours_start.present? || operating_hours_end.present?
	end

end
