# == Schema Information
#
# Table name: pages
#
#  id            :integer          not null, primary key
#  name          :string(255)
#  slug          :string(255)
#  keywords      :string(255)
#  teaser        :text
#  body          :text
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  nav_name      :string(255)
#  tracking_code :string(255)
#

# DEPRECATED - Remove when possible
class Page < ActiveRecord::Base

end
