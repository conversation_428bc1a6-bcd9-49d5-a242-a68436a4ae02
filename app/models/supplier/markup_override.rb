# == Schema Information
#
# Table name: supplier_markup_overrides
#
#  id                  :bigint           not null, primary key
#  supplier_profile_id :bigint
#  markup              :decimal(5, 3)    default(0.0)
#  commission_rate     :decimal(5, 3)    default(0.0)
#  active              :boolean          default(TRUE)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  overridable_type    :string
#  overridable_id      :integer
#
class Supplier::MarkupOverride < ApplicationRecord

  validates :supplier_profile, presence: true
  validates :overridable, presence: true

  validate :unique_supplier_overridable
  validate :at_least_one_override

  belongs_to :supplier_profile
  belongs_to :overridable, polymorphic: true

  def yordar_commission
    supplier_commission = commission_rate || supplier_profile.commission_rate
    supplier_markup = markup || supplier_profile.markup
    1 - (1 - (supplier_commission / 100)) / (1 + (supplier_markup / 100))
  end

private

  def at_least_one_override
    if markup.blank? && commission_rate.blank?
      errors.add :base, 'Must have at least one override value'
    end
  end

  def unique_supplier_overridable
    if supplier_profile.present? && overridable.present? && Supplier::MarkupOverride.where(supplier_profile: supplier_profile, overridable: overridable).where.not(id: id).present?
      errors.add :overridable, 'Markup override already exists'
    end
  end

end
