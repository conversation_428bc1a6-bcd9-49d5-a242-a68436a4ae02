class Ability
  include CanCan::Ability

  def initialize(user)
    cannot :manage, :all

    # abort user.inspect

    # Nishat says: if you get this error, it means the cookie has something it doesn't like
    # kill that cookie
    #
    #
    # CanCan::AccessDenied in RailsAdmin::MainController#dashboard
    # You are not authorized to access this page.
    # cancancan (1.15.0) lib/cancan/ability.rb:216:in `authorize!'
    # rails_admin (1.1.0) lib/rails_admin/extensions/cancancan/authorization_adapter.rb:11:in `initialize'

    if user.present?
      if user.can_access_customers?
        can :access, :rails_admin
        can :read, :dashboard
        if user.allow_all_customer_access?
          can :read, CustomerProfile
          if user.can_manage_custom_orders?
            can :manage, CustomOrder, order_variant: 'event_order'
            can :custom_order, CustomerProfile
          end
        else
          user.customer_profiles.map(&:id).each do |c_id|
            can :read, CustomerProfile, id: c_id
            if user.can_manage_custom_orders?
              can :manage, CustomOrder, customer_profile_id: c_id, order_variant: 'event_order'
              can :custom_order, CustomerProfile
            end
          end
        end
        can %i[update sign_in_as favourite_customer], [CustomerProfile]
      end

      if user.can_access_suppliers?
        can :access, :rails_admin
        can :read, :dashboard
        if user.allow_all_supplier_access?
          can %i[read update], SupplierProfile
        else
          user.supplier_profiles.map(&:id).each do |s_id|
            can :read, SupplierProfile, id: s_id
          end
        end
        # can [:update, :destroy, :sign_in_as], [SupplierProfile]
        can :sign_in_as, SupplierProfile
      end

      if user.can_access_orders?
        can :access, :rails_admin
        can :read, :dashboard
        can :read, [CustomOrder]
        can %i[create update], [CustomOrder]
      end

      if user.admin?
        can :access, :rails_admin
        can :read, :dashboard
        can :read, :accounting

        can :read, :all
        cannot :read, [Doorkeeper::Application, Dear::Account]
        can %i[create update destroy], [User, Company, Category]
        can %i[update sign_in_as], [CustomerProfile, SupplierProfile]
        can %i[custom_order favourite_customer], [CustomerProfile]
        can :manage, CustomOrder
      end

      if user.super_admin?
        can :manage, :all
      end
    end

    # Define abilities for the passed in user here. For example:
    #
    #   user ||= User.new # guest user (not logged in)
    #   if user.admin?
    #     can :manage, :all
    #   else
    #     can :read, :all
    #   end
    #
    # The first argument to `can` is the action you are giving the user
    # permission to do.
    # If you pass :manage it will apply to every action. Other common actions
    # here are :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on.
    # If you pass :all it will apply to every resource. Otherwise pass a Ruby
    # class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the
    # objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, :published => true
    #
    # See the wiki for details:
    # https://github.com/CanCanCommunity/cancancan/wiki/Defining-Abilities
  end
end
