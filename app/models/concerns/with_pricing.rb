module WithPricing
  extend ActiveSupport::Concern

  def markup_price(gst_country: nil, override: nil, promotional: false)
    baseline_price = (promotional && promotional_price.presence) || price
    return nil if baseline_price.blank?

    markedup_price = case
    when override.present?
      baseline_price.to_f * (1 + (override.markup / 100))
    when supplier = supplier_profile.presence
      baseline_price.to_f * (1 + (supplier.markup / 100))
    else
      baseline_price.to_f
    end

    if is_gst_free? || gst_country.blank?
      markedup_price
    else
      markedup_price.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, gst_country.downcase.to_sym).to_f)
    end
  end

  def cost(override: nil)
    baseline_price = promotional_price.presence || price
    return nil if baseline_price.blank?

    case
    when override.present?
      baseline_price.to_f * (1 - (override.commission_rate / 100))
    when supplier = supplier_profile.presence
      baseline_price.to_f * (1 - (supplier.commission_rate / 100))
    else
      baseline_price.to_f
    end
  end

  def promotional_price
    respond_to?(:promo_price) && promo_price
  end

end