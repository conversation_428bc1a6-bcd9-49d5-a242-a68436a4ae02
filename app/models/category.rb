# == Schema Information
#
# Table name: categories
#
#  id               :integer          not null, primary key
#  name             :string(255)
#  slug             :string(255)
#  group            :string(255)
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  show_in_menu     :boolean
#  show_in_homepage :boolean
#  group_weight     :integer
#  weight           :integer
#

class Category < ActiveRecord::Base
	include PgSearch::Model
	pg_search_scope :search_by_keywords,
									against: %i[keywords teaser],
									using: { tsearch: { any_word: true, dictionary: 'english' } }

	VALID_CATEGORY_GROUPS = %w[catering-services kitchen-supplies gifting special-events home-deliveries work-from-home].freeze
	SUPPLIER_CATEGORIES = %w[catering-services kitchen-supplies gifting home-deliveries work-from-home].freeze
	MEAL_PLAN_CATEGORY_SLUGS = %w[buffets individually-boxed-meals share-meals].freeze

	GROUP_WEIGHTS = {
		'special-events' => 1,
		'catering-services' => 2,
		'home-deliveries' => 2,
		'kitchen-supplies' => 3,
		'gifting' => 4,
		'work-from-home' => 5,
	}.freeze

	validates :name, presence: true
	validates :group, inclusion: { in: VALID_CATEGORY_GROUPS }, presence: true

	has_many :category_menu_sections
	has_many :menu_sections, through: :category_menu_sections
	has_many :orders, foreign_key: :major_category_id

	before_save :assign_group_weight, if: -> { changes['group'].present? }
	before_save :make_slug, if: -> { changes['name'].present? }

	# scopes
	scope :generic_category_for, ->(group_name:) { where(group: group_name, is_generic: true).first }

private

	# callback methods
	def assign_group_weight
		self.group_weight = GROUP_WEIGHTS[group] || 99
	end

	def make_slug
		self.slug = name.downcase.gsub(/[^a-z1-9]+/, '-').chomp('-')
	end
end
