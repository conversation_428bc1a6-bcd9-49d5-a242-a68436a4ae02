# == Schema Information
#
# Table name: delivery_overrides
#
#  id                  :bigint           not null, primary key
#  customer_profile_id :bigint
#  supplier_kind       :string
#  supplier_profile_id :bigint
#  customer_override   :decimal(10, 2)
#  supplier_override   :decimal(10, 2)
#  active              :boolean          default(TRUE)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class DeliveryOverride < ApplicationRecord

  VALID_KINDS = %w[all catering pantry specific].freeze

  validates :customer_profile, presence: true
  validates :supplier_kind, presence: true, inclusion: { in: VALID_KINDS }
  validates :supplier_profile, presence: true, if: -> { supplier_kind == 'specific' }
  validate :at_least_one_override

  belongs_to :customer_profile
  belongs_to :supplier_profile

private

  def at_least_one_override
    if customer_override.blank? && supplier_override.blank?
      errors.add :base, 'Must have at least one override value'
    end
  end

end
