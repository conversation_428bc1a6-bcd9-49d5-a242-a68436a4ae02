# == Schema Information
#
# Table name: supplier_closures
#
#  id                  :bigint           not null, primary key
#  supplier_profile_id :bigint
#  starts_at           :datetime
#  ends_at             :datetime
#  reason              :string
#  description         :text
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class SupplierClosure < ApplicationRecord

  validates :supplier_profile_id, presence: true
  validates :starts_at, presence: true
  validates :ends_at, presence: true
  validates :reason, presence: true

  validate :valid_datetimes

  belongs_to :supplier_profile

private

  # validation
  def valid_datetimes
    if starts_at.present? && ends_at.present? && ends_at < starts_at
      errors.add(:base, 'Must end after it starts')
    end
  end

end
