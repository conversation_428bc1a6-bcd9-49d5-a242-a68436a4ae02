# == Schema Information
#
# Table name: supplier_flags
#
#  id                               :bigint           not null, primary key
#  supplier_profile_id              :bigint
#  is_new                           :boolean          default(FALSE)
#  is_featured                      :boolean          default(FALSE)
#  needs_swipe_card_access          :boolean          default(FALSE)
#  supplies_in_working_hours        :boolean          default(FALSE)
#  provides_multi_service_point     :boolean          default(FALSE)
#  provides_contactless_delivery    :boolean          default(FALSE)
#  can_manage_menu_dashboard        :boolean          default(FALSE)
#  has_gluten_free_items            :boolean          default(FALSE)
#  has_vegetarian_items             :boolean          default(FALSE)
#  has_vegan_items                  :boolean          default(FALSE)
#  has_dairy_free_items             :boolean          default(FALSE)
#  is_socially_responsible          :boolean          default(FALSE)
#  is_eco_friendly                  :boolean          default(FALSE)
#  is_environmentally_accredited    :boolean          default(FALSE)
#  is_registered_charity            :boolean          default(FALSE)
#  is_female_owned                  :boolean          default(FALSE)
#  is_rainforest_alliance_certified :boolean          default(FALSE)
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  needs_multi_day_summary          :boolean          default(FALSE)
#  has_skus                         :boolean          default(FALSE)
#  is_indigenous_owned              :boolean          default(FALSE)
#  is_event_caterer                 :boolean          default(FALSE)
#  uses_flex_catering               :boolean          default(FALSE)
#  billing_frequency                :string           default("weekly")
#  payment_term_days                :integer          default(45)
#  has_catering_services            :boolean          default(FALSE)
#  has_kitchen_supplies             :boolean          default(FALSE)
#  is_new_expires_at                :datetime
#
class SupplierFlags < ApplicationRecord
  self.table_name = 'supplier_flags'

  VALID_BILLING_FREQUENCIES = %w[do_not_generate weekly fortnightly monthly].freeze
  VALID_TERM_DAYS = [7, 15, 21, 30, 45].freeze
  VALID_REMINDER_FREQUENCIES = %w[monthly 3.months 6.months].freeze

  validates :billing_frequency, inclusion: { in: VALID_BILLING_FREQUENCIES }
  validates :payment_term_days, inclusion: { in: VALID_TERM_DAYS }
  validates :menu_reminder_frequency, inclusion: { in: VALID_REMINDER_FREQUENCIES }, allow_blank: true

  belongs_to :supplier_profile, touch: true
end
