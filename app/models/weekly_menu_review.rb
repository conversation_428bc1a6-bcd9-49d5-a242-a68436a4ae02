# == Schema Information
#
# Table name: weekly_menu_reviews
#
#  id             :integer          not null, primary key
#  weekly_menu_id :integer
#  taste          :integer
#  presentation   :integer
#  quantity       :integer
#  see_again      :boolean
#  comments       :text
#  day            :integer
#  created_at     :datetime
#  updated_at     :datetime
#  first_name     :string
#  last_name      :string
#

class WeeklyMenuReview < ActiveRecord::Base

  DAYS_OF_WEEK = %i[monday tuesday wednesday thursday friday saturday sunday].freeze

  enum day: DAYS_OF_WEEK

  validates :first_name, presence: true
  validates :last_name, presence: true
  validates :day, presence: true
  validates :taste, presence: true
  validates :presentation, presence: true
  validates :quantity, presence: true
  validates :see_again, inclusion: { in: [true, false] }

  belongs_to :weekly_menu

  def name
    "#{first_name} #{last_name}"
  end
end
