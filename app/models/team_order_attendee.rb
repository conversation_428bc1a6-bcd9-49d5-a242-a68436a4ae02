# == Schema Information
#
# Table name: team_order_attendees
#
#  id                    :integer          not null, primary key
#  order_id              :integer
#  event_attendee_id     :integer
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  uniq_code             :string(255)
#  status                :string(255)      default("invited")
#  cutoff_4hr_reminder   :datetime
#  cutoff_2hr_reminder   :datetime
#  cutoff_30m_reminder   :datetime
#  delivery_30m_reminder :datetime
#  anonymous             :boolean          default(FALSE)
#  team_order_level_id   :bigint
#  cutoff_24hr_reminder  :datetime
#

class TeamOrderAttendee < ActiveRecord::Base

  REGISTRATION_CUTOFF_THRESHOLD = 1.hour
  SOFT_CUTOFF_THRESHOLD = 30.minutes
  VALID_ATTENDEE_STATUSES = %w[invited pending ordered declined cancelled].freeze

  validates :uniq_code, presence: true, uniqueness: true
  validates :status, inclusion: { in: VALID_ATTENDEE_STATUSES }

  belongs_to :order
  belongs_to :event_attendee
  belongs_to :level, class_name: 'TeamOrder::Level', foreign_key: :team_order_level_id

  delegate :first_name, to: :event_attendee
  delegate :last_name, to: :event_attendee
  delegate :name, to: :event_attendee
  delegate :email, to: :event_attendee

  def is_team_admin?
    uniq_code.present? && order.present? && uniq_code == order.unique_event_id
  end

end
