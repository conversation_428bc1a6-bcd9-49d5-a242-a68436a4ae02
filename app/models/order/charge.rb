# == Schema Information
#
# Table name: order_charges
#
#  id           :integer          not null, primary key
#  order_id     :integer
#  amount       :decimal(, )
#  stripe_token :string
#  refund_token :string
#  expires_at   :datetime
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
class Order::Charge < ApplicationRecord
  self.table_name = 'order_charges'

  VALID_STATUSES = %w[charged refunded failed]

  validates :status, inclusion: { in: VALID_STATUSES }, presence: true

  belongs_to :order

  def refunded?
    status == 'refunded' && refund_token.present?
  end

end
