# This model is solely created to show a
# scoped view of Users in Rails_Admin
# (Delete this model when we trash rails_admin, clean init/rails_admin.rb and app_ability file )
#
class EventSupplier < SupplierProfile
  self.table_name = 'supplier_profiles'

  default_scope { joins(:supplier_flags).where(supplier_flags: { is_event_caterer: true }) }

  rails_admin do
    weight 33
    label 'Event Suppliers'
    visible do
      user = bindings[:controller].current_user
      user.super_admin? || user.admin? || user.allow_all_supplier_access?
    end

    list do
      items_per_page 100
      sort_by :company_name

      field :company_name do
        label 'Name'
      end
      field :email
      field :is_searchable
    end
  end
end
