# == Schema Information
#
# Table name: serving_sizes
#
#  id                       :integer          not null, primary key
#  name                     :string(255)
#  price                    :decimal(10, 2)
#  menu_item_id             :integer
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  weight                   :integer
#  archived_at              :datetime
#  is_default               :boolean          default(FALSE)
#  available_for_team_order :boolean          default(TRUE)
#  sku                      :string
#  stock_quantity           :integer
#

class ServingSize < ActiveRecord::Base
	include WithPricing

	validates :menu_item_id, presence: true
	validates :name, presence: true
	validates :price, presence: true
	validates :price, numericality: { greater_than_or_equal_to: 0 }, on: :update

	belongs_to :menu_item, touch: true
	has_one :supplier_profile, through: :menu_item
	has_many :rate_cards

	attr_accessor :over_budget

	def is_gst_free?
		menu_item.present? && menu_item.is_gst_free?
	end

end
