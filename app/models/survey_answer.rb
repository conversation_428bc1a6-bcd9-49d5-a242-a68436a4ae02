# == Schema Information
#
# Table name: survey_answers
#
#  id                            :bigint           not null, primary key
#  employee_survey_submission_id :bigint
#  survey_question_id            :bigint
#  value                         :string
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  question_label                :string
#
class SurveyAnswer < ApplicationRecord

  validates :employee_survey_submission, presence: true
  validates :survey_question, presence: true
  validates :value, presence: true

  belongs_to :employee_survey_submission
  belongs_to :survey_question

end
