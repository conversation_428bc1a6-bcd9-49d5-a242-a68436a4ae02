# == Schema Information
#
# Table name: rate_cards
#
#  id              :integer          not null, primary key
#  serving_size_id :integer
#  menu_item_id    :integer
#  company_id      :integer
#  price           :decimal(10, 2)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  cost            :decimal(10, 2)
#

class RateCard < ActiveRecord::Base

	# used when bulk updating from admin # currently deprecated
	BULK_UPDATE_FIELDS = %w[price cost].freeze

	validates :company_id, presence: true
	validates :menu_item_id, presence: true
	validates :price, presence: true
	validates :cost, presence: true

	belongs_to :company
	belongs_to :menu_item, touch: true
	belongs_to :serving_size, touch: true
	has_one :supplier_profile, through: :menu_item

	def price_inc_gst(gst_country: nil)
		country_code = gst_country&.downcase&.to_sym || :au
		if menu_item.is_gst_free?
			price
		else
			price.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)
		end
	end

	def margin_rate
		return 0 if price.blank? || cost.blank?

		((price - cost) / price) * 100
	end

	def active_orders
		return @_active_orders if !@_active_orders.nil?

		orders = Order.joins(:order_lines)
		orders = orders.where(order_lines: { menu_item_id: menu_item_id, serving_size_id: serving_size_id })
		orders = orders.where(orders: { status: %w[pending new paused amended confirmed] })
		orders = orders.joins(:customer_profile)
		@_active_orders = orders.where(customer_profiles: { company_id: company_id }).distinct
	end

	def in_active_use?
		active_orders.present?
	end

end
