# == Schema Information
#
# Table name: supplier_agreement_documents
#
#  id                   :integer          not null, primary key
#  supplier_profile_id  :integer          not null
#  status               :string           not null
#  docusign_envelope_id :string           not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  status_changes       :jsonb            not null
#

class SupplierAgreementDocument < ActiveRecord::Base

  KNOWN_STATUSES = %w[sent delivered completed voided].freeze

  validates :status, presence: true
  validates :supplier_profile, presence: true
  validates :docusign_envelope_id, presence: true, uniqueness: true

  belongs_to :supplier_profile

  before_save :update_status_changes, if: -> { changes['status'].present? }

private

  def update_status_changes
    status_changes << {
      old_status: status_was,
      new_status: status,
      changed_at: Time.zone.now.utc
    }
  end
end
