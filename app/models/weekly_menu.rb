# == Schema Information
#
# Table name: weekly_menus
#
#  id                    :integer          not null, primary key
#  weekly_menu_client_id :integer
#  week_of               :datetime
#  monday                :text
#  tuesday               :text
#  wednesday             :text
#  thursday              :text
#  friday                :text
#  saturday              :text
#  sunday                :text
#  created_at            :datetime
#  updated_at            :datetime
#  monday_supplier_id    :integer
#  tuesday_supplier_id   :integer
#  wednesday_supplier_id :integer
#  thursday_supplier_id  :integer
#  friday_supplier_id    :integer
#  saturday_supplier_id  :integer
#  sunday_supplier_id    :integer
#

class WeeklyMenu < ActiveRecord::Base

  validates_presence_of :week_of

  has_many :weekly_menu_reviews
  belongs_to :weekly_menu_client, required: true
  belongs_to :monday_supplier, class_name: 'SupplierProfile', foreign_key: :monday_supplier_id
  belongs_to :tuesday_supplier, class_name: 'SupplierProfile', foreign_key: :tuesday_supplier_id
  belongs_to :wednesday_supplier, class_name: 'SupplierProfile', foreign_key: :wednesday_supplier_id
  belongs_to :thursday_supplier, class_name: 'SupplierProfile', foreign_key: :thursday_supplier_id
  belongs_to :friday_supplier, class_name: 'SupplierProfile', foreign_key: :friday_supplier_id
  belongs_to :saturday_supplier, class_name: 'SupplierProfile', foreign_key: :saturday_supplier_id
  belongs_to :sunday_supplier, class_name: 'SupplierProfile', foreign_key: :sunday_supplier_id

end
