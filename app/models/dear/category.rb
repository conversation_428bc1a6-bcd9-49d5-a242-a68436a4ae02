# == Schema Information
#
# Table name: dear_categories
#
#  id              :bigint           not null, primary key
#  dear_account_id :bigint
#  category_id     :string
#  name            :string
#  override_name   :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
class Dear::Category < ApplicationRecord

  validates :dear_account_id, presence: true
  validates :category_id, presence: true
  validates :name, presence: true

  belongs_to :dear_account, class_name: 'Dear::Account', foreign_key: :dear_account_id
  has_one :supplier_profile, through: :dear_account

end
