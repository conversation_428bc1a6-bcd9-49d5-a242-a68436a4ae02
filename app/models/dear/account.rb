# == Schema Information
#
# Table name: dear_accounts
#
#  id                  :bigint           not null, primary key
#  supplier_profile_id :bigint
#  account_id          :string           not null
#  api_key             :string           not null
#  customer_id         :string           not null
#  active              :boolean          default(TRUE)
#  price_tier          :string
#  dietary_attribute   :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class Dear::Account < ApplicationRecord

  VALID_PRICE_TIERS = (1..10).map{|num| "PriceTier#{num}"}
  VALID_ADDITIONAL_ATTRIBUTES = (1..10).map{|num| "AdditionalAttribute#{num}"}

  validates :account_id, presence: true
  validates :api_key, presence: true
  validates :customer_id, presence: true

  validates :price_tier, inclusion: { in: VALID_PRICE_TIERS }, allow_blank: true
  validates :dietary_attribute, inclusion: { in: VALID_ADDITIONAL_ATTRIBUTES }, allow_blank: true

  belongs_to :supplier_profile
  has_many :categories, class_name: 'Dear::Category', foreign_key: :dear_account_id

  # used for rails admin accociation
  def title
    return '' if supplier_profile.blank? || account_id.blank?

    "#{supplier_profile.name} - #{account_id.split('-').first}..."
  end

end
