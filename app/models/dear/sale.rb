# == Schema Information
#
# Table name: dear_sales
#
#  id                  :bigint           not null, primary key
#  order_id            :bigint
#  supplier_profile_id :bigint
#  sale_id             :string
#  location            :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class Dear::Sale < ApplicationRecord

  validates :sale_id, presence: true
  validates :location, presence: true

  belongs_to :order
  belongs_to :supplier_profile

end
