# == Schema Information
#
# Table name: leads
#
#  id           :integer          not null, primary key
#  lead_type    :string
#  progress     :string
#  email        :string
#  user_id      :integer
#  created_at   :datetime
#  updated_at   :datetime
#  firstname    :string
#  lastname     :string
#  company_name :string
#  phone        :string
#  hs_id        :integer
#

class Lead < ActiveRecord::Base

  VALID_LEAD_TYPES = %w[started_registration submitted_50_staff_form started_supplier_registration contact_us_lead].freeze

	validates :lead_type, inclusion: { in: VALID_LEAD_TYPES }

  belongs_to :user, required: false

end
