# == Schema Information
#
# Table name: suburbs
#
#  id           :integer          not null, primary key
#  postcode     :string(255)
#  name         :string(255)
#  state        :string(255)
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  latitude     :decimal(15, 10)
#  longitude    :decimal(15, 10)
#  country_code :string
#

class Suburb < ActiveRecord::Base

	VALID_COUNTRY_CODES = %w[AU NZ].freeze

	validates :name, presence: true
	validates :postcode, presence: true, numericality: { only_integer: true, greater_than: 1, less_than_or_equal_to: 9999 }
	validates :country_code, presence: true, inclusion: { in: VALID_COUNTRY_CODES }

	has_many :delivery_zones
	has_many :deliverable_suburbs

	# Concatenates name, state and post code to return a uniform label to be used throughout
	def label
		"#{name}, #{state} #{postcode}"
	end

	# Concatenates name, state
	def suburb_state
		"#{name}, #{state}"
	end

	# ensure that a few methods are included in the json result (here, label)
	def as_json(options = {})
	  super.as_json(options).merge({ label: label })
	end

	# if we have long-lat against the zone
	def has_coordinates?
		latitude.present? && longitude.present?
	end

	def symbolized_country_code
		country_code&.downcase&.to_sym
	end

end
