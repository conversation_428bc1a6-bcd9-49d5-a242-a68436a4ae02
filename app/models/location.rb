# == Schema Information
#
# Table name: locations
#
#  id         :integer          not null, primary key
#  details    :text
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  note       :string(255)
#  order_id   :integer
#

class Location < ActiveRecord::Base

	has_paper_trail only: %i[details note]

	has_many :order_lines
	belongs_to :order

	# Return location's details and note if they exist as a string
	def label
		[details, note].reject(&:blank?).join(' - ')
	end

end
