# == Schema Information
#
# Table name: supplier_invoices
#
#  id                  :bigint           not null, primary key
#  supplier_profile_id :bigint
#  number              :string
#  from_at             :datetime
#  to_at               :datetime
#  due_at              :datetime
#  amount              :decimal(10, 2)   default(0.0)
#  pushed_to_xero      :boolean          default(FALSE)
#  payment_status      :string           default("unpaid")
#  uuid                :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class SupplierInvoice < ApplicationRecord

  DEFAULT_PAYMENT_DAYS = 45
  VALID_PAYMENT_STATUSES = %w[unpaid partial paid voided deleted].freeze

  validates :number, presence: true
  validates :uuid, presence: true
  validates :payment_status, inclusion: { in: VALID_PAYMENT_STATUSES }

  belongs_to :supplier_profile
  has_many :documents, as: :documentable
  has_many :order_suppliers
  has_many :orders, through: :order_suppliers

end
