# == Schema Information
#
# Table name: notification_preferences
#
#  id               :integer          not null, primary key
#  account_id       :integer
#  account_type     :string
#  email_recipients :string
#  salutation       :string
#  active           :boolean          default(TRUE)
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  variation        :string
#  template_name    :string
#
class Notification::Preference < ApplicationRecord

  validates :account, presence: true
  validates :template_name, presence: true

  belongs_to :account, polymorphic: true

end
