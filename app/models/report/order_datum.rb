# == Schema Information
#
# Table name: report_order_data
#
#  id              :bigint           not null, primary key
#  report_datum_id :bigint
#  data_kind       :string
#  kind            :string
#  total_spend     :decimal(10, 2)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
class Report::OrderDatum < ApplicationRecord

  VALID_DATA_KINDS = %w[Category Supplier Ethical].freeze
  validates :data_kind, inclusion: { in: VALID_DATA_KINDS }, presence: true

  belongs_to :report_datum, class_name: 'Report::Datum'

end
