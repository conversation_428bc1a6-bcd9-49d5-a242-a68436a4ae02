# == Schema Information
#
# Table name: report_data
#
#  id                         :integer          not null, primary key
#  report_source_id           :integer
#  customer_purchase_order_id :integer
#  total_spend                :decimal(10, 2)
#  order_ids                  :integer          default([]), is an Array
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  category                   :string
#
class Report::Datum < ApplicationRecord
  belongs_to :report_source, class_name: 'Report::Source'
  belongs_to :customer_purchase_order
  has_many :order_data, class_name: 'Report::OrderDatum', foreign_key: :report_datum_id, dependent: :destroy
end
