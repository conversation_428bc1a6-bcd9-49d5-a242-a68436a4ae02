# == Schema Information
#
# Table name: report_sources
#
#  id          :integer          not null, primary key
#  source_id   :integer
#  key_date    :date
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  source_type :string
#
class Report::Source < ApplicationRecord

  belongs_to :source, polymorphic: true
  has_many :report_data, class_name: 'Report::Datum', foreign_key: :report_source_id, dependent: :destroy

end
