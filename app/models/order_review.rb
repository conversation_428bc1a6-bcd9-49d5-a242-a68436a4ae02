# == Schema Information
# Schema version: 20180306085702
#
# Table name: order_reviews
#
#  id                         :integer          not null, primary key
#  supplier_profile_id        :integer          not null
#  order_id                   :integer          not null
#  product_quality_score      :integer
#  delivery_punctuality_score :integer
#  food_taste_score           :integer
#  presentation_score         :integer
#  comment                    :text
#  created_at                 :datetime
#  updated_at                 :datetime

class OrderReview < ActiveRecord::Base

  SCORE_FIELDS = %i[
    food_taste_score
    presentation_score
    delivery_punctuality_score
  ].freeze

  validates :supplier_profile, presence: true, uniqueness: { scope: :order }
  validates :order, presence: true

  belongs_to :supplier_profile
  belongs_to :order

  scope :week, lambda {|time|
    where(
      'order_reviews.created_at >= :start and order_reviews.created_at <= :end',
      start: time.beginning_of_week,
      end: time.end_of_week
    )
  }

  after_create :update_supplier_rating

private

  def update_supplier_rating
    score = supplier_profile.rating_score + rating_score
    count = supplier_profile.rating_count + rating_count
    supplier_profile.update_columns rating_score: score, rating_count: count
  end

  def rating_score
    SCORE_FIELDS.sum do |name|
      public_send(name).to_i
    end
  end

  def rating_count
    SCORE_FIELDS.sum do |name|
      public_send(name).to_i > 0 ? 1 : 0
    end
  end
end
