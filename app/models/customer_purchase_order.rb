# == Schema Information
#
# Table name: customer_purchase_orders
#
#  id                  :integer          not null, primary key
#  po_number           :string
#  weekly_budget       :decimal(, )
#  created_at          :datetime
#  updated_at          :datetime
#  description         :text
#  customer_profile_id :integer
#  active              :boolean          default(TRUE)
#

class CustomerPurchaseOrder < ActiveRecord::Base

	validates :po_number, presence: true, uniqueness: { scope: :customer_profile_id, message: 'should have one po number' }
	validates :customer_profile_id, presence: true

	has_many :orders, foreign_key: :cpo_id
	has_many :invoices, foreign_key: :cpo_id
	belongs_to :customer_profile
  has_many :report_data, class_name: 'Report::Datum', foreign_key: :customer_purchase_order_id, dependent: :destroy

	def info
		info_text = po_number
		info_text += " - #{description}" if description.present?
		info_text
	end

end
