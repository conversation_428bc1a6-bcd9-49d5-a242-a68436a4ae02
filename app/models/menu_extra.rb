# == Schema Information
#
# Table name: menu_extras
#
#  id                    :integer          not null, primary key
#  created_at            :datetime
#  updated_at            :datetime
#  name                  :string
#  price                 :string
#  menu_item_id          :integer
#  weight                :integer
#  archived_at           :datetime
#  menu_extra_section_id :integer
#  sku                   :string
#

class MenuExtra < ActiveRecord::Base
	include WithPricing

	validates_presence_of :name, :price
	validates :price, numericality: { greater_than_or_equal_to: 0 }, on: :update
	validates :menu_extra_section, presence: true

	belongs_to :menu_extra_section, touch: true
	belongs_to :menu_item
	has_one :supplier_profile, through: :menu_item

	def is_gst_free?
		menu_item.present? && menu_item.is_gst_free?
	end

end
