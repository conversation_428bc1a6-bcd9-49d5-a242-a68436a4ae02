# == Schema Information
#
# Table name: documents
#
#  id                :bigint           not null, primary key
#  documentable_id   :integer
#  documentable_type :string
#  kind              :string
#  version           :integer
#  url               :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  name              :string
#
class Document < ApplicationRecord

  VALID_KINDS = %w[
    supplier_heads_up_order_details
    supplier_order_details
    supplier_order_delivery_details
    supplier_json_order_details
    team_order_avery_labels
    supplier_current_menu
    supplier_order_manifest
    customer_meal_plan
    customer_order_details
    customer_team_order_details
    customer_order_quote
    customer_daily_summary
    supplier_order_summary
    tax_invoice_receipt
    tax_invoice
    invoice_location_report
    invoice_detailed_report
    tax_invoice_spreadsheet
    recipient_generated_invoice
    employee_survey_promo
    staffing_spend
  ].freeze

  validates :documentable, presence: true
  validates :url, presence: true
  validates :kind, presence: true, inclusion: { in: VALID_KINDS }, uniqueness: { scope: %i[documentable version] }

  belongs_to :documentable, polymorphic: true
  belongs_to :order, -> { where(documents: { documentable_type: 'Order' }) }, foreign_key: :documentable_id
  belongs_to :order_supplier, -> { where(documents: { documentable_type: 'OrderSupplier' }) }, foreign_key: :documentable_id

  def file_extension
    case kind
    when 'team_order_avery_labels'
      'csv'
    when 'supplier_json_order_details'
      'json'
    else
      url.split('.').last
    end
  end

  def fallback_name
    return name if name.present?

    case
    when kind == 'tax_invoice'
      doc_name = "invoice-#{documentable.number}"
      doc_name += "-v#{version}" if version > 1
      doc_name
    when %w[invoice_location_report invoice_detailed_report].include?(kind)
      "invoice-#{documentable.number}-#{kind.gsub('invoice_', '').gsub('_', '-')}-v#{version}"
    when kind == 'tax_invoice_receipt'
      "invoice-tax-receipt-#{documentable.number}"
    else
      "#{kind.gsub('_', '-')}-v#{version}"
    end
  end

  def kind_based_name
    "#{kind.humanize.titleize} (v#{version})"
  end

end
