# == Schema Information
#
# Table name: supplier_profiles
#
#  id                        :integer          not null, primary key
#  company_name              :string(255)
#  company_address           :string(255)
#  company_address_suburb_id :string(255)
#  email                     :text
#  phone                     :string(255)
#  mobile                    :string(255)
#  price_indication          :string(255)
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  abn_acn                   :string(255)
#  commission_rate           :decimal(5, 3)    default(10.0)
#  description               :text
#  slug                      :string(255)
#  is_searchable             :boolean          default(TRUE)
#  avatar_url                :string(255)
#  close_from                :datetime
#  close_to                  :datetime
#  needs_freight             :boolean
#  team_supplier             :boolean          default(FALSE)
#  lead_mode                 :string(255)      default("by_hour")
#  bank_account_number       :string
#  bsb_number                :string
#  markup                    :decimal(5, 3)    default(0.0)
#  rating_score              :integer          default(0), not null
#  rating_count              :integer          default(0), not null
#  send_supplier_agreement   :boolean          default(FALSE), not null
#  minimum_delivery_fee      :decimal(10, 2)   default(0.0)
#  liquor_license_no         :string
#  uuid                      :string
#  operating_days            :string
#  operating_hours           :string
#  delivery_fee              :decimal(10, 2)
#

class SupplierProfile < ActiveRecord::Base

	include ImportHelper
	include CloudinaryHelper

	include PgSearch::Model
	pg_search_scope :search_by_name,
									against: %i[company_name],
									using: { tsearch: { dictionary: 'english', prefix: true } }

	VALID_LEAD_MODES = %w[by_hour by_day_before].freeze
	BULK_UPDATE_FIELDS = %w[commission_rate markup].freeze # used when bulk updating from admin

	SUPPLIER_IS_FLAGS = %i[is_new_expires_at is_featured is_event_caterer admin_only].freeze
	SUPPLIER_SUSTAINABLE_FLAGS = %i[is_socially_responsible is_indigenous_owned is_environmentally_accredited is_registered_charity is_female_owned is_lgbtqi_owned is_rainforest_alliance_certified is_eco_friendly]
	SUPPLIER_PROVIDES_FLAGS = %i[provides_multi_service_point provides_contactless_delivery supplies_in_working_hours].freeze
	SUPPLIER_HAS_FLAGS = %i[has_catering_services has_kitchen_supplies has_skus].freeze
	SUPPLIER_DIETARY_FLAGS = %i[ has_gluten_free_items has_vegetarian_items has_vegan_items has_dairy_free_items has_egg_free_items has_halal_items has_kosher_items has_nut_free_items]
	SUPPLIER_USES_FLAGS = %i[uses_flex_catering].freeze
	SUPPLIER_NEED_FLAGS = %i[needs_swipe_card_access needs_multi_day_summary needs_recurring_reminder].freeze
	SUPPLIER_CAN_FLAGS = %i[can_manage_menu_dashboard].freeze
	SUPPLIER_BILLING_FIELDS = %i[billing_frequency payment_term_days].freeze
	SUPPLIER_MENU_FIELDS = %i[menu_reminder_frequency menu_last_updated_on]
	SUPPLIER_FLAG_FIELDS = (
		SUPPLIER_IS_FLAGS +
		SUPPLIER_SUSTAINABLE_FLAGS +
		SUPPLIER_PROVIDES_FLAGS +
		SUPPLIER_HAS_FLAGS +
		SUPPLIER_DIETARY_FLAGS +
		SUPPLIER_USES_FLAGS +
		SUPPLIER_NEED_FLAGS +
		SUPPLIER_CAN_FLAGS +
		SUPPLIER_BILLING_FIELDS +
		SUPPLIER_MENU_FIELDS
	).freeze

	# Validations
	validates :company_name, presence: true
	validates :commission_rate, presence: true
	validates_format_of :email, with: /.+@.+/, allow_blank: true, message: 'is not a valid email'
	validates :lead_mode, inclusion: { in: VALID_LEAD_MODES }, allow_blank: false
	validate :can_deactivate?

	# Associations
	# acts as profile
	has_one :profile, as: :profileable, dependent: :destroy
	accepts_nested_attributes_for :profile
	# because we want to use the user
	has_one :user, through: :profile
	accepts_nested_attributes_for :user

	has_one :supplier_flags, class_name: 'SupplierFlags'
	has_one :dear_account, class_name: 'Dear::Account'

	has_many :supplier_invoices
	has_many :minimums
	has_many :order_suppliers
	accepts_nested_attributes_for :order_suppliers

	has_many :custom_order_suppliers

	belongs_to :company_address_suburb, class_name: 'Suburb'

	has_many :delivery_zones
	has_many :suburbs, through: :delivery_zones
	has_many :deliverable_suburbs

	has_many :menu_sections
	has_many :categories, through: :menu_sections, source: :categories
	has_many :menu_items
	has_many :serving_sizes, through: :menu_items, source: :serving_sizes
	has_many :markup_overrides, class_name: 'Supplier::MarkupOverride'

	has_many :order_lines
	has_many :order_reviews
	has_many :supplier_agreement_documents

	has_many :documents, as: :documentable

	has_and_belongs_to_many :customer_profiles

	has_many :report_sources, class_name: 'Report::Source', as: :source
	has_many :orders, -> { distinct }, through: :order_lines

	has_many :closure_dates, class_name: 'SupplierClosure'
	has_many :oauth_applications, class_name: 'Doorkeeper::Application', as: :owner

	has_many :notification_preferences, class_name: 'Notification::Preference', as: :account

	# Scopes
	scope :search_by_menu_item_keywords, lambda {|keywords|
		joins(menu_sections: :menu_items)
			 .distinct
			 .merge(MenuItem.search_by_keywords(keywords).where(archived_at: nil))
			 .reorder(nil)
	}

	scope :search_by_keywords, lambda {|keywords|
		search_by_name(keywords)
		  .union(search_by_menu_item_keywords(keywords))
	}

	# if there ever is a requirement to remove the active_record_union gem the below scope can be used
	# scope :search_by_keywords_non_union, ->(keywords) {
	# 	name_search = search_by_name(keywords)
	# 	item_search = search_by_menu_item_keywords(keywords)
	# 	where('id in (?) OR id in (?)', name_search.select(:id), item_search.select(:id))
	# }

	scope :search_by_team_order_keywords, lambda {|keywords|
		search_by_name(keywords)
		  .union(search_by_team_order_menu_item_keywords(keywords))
	}

	scope :search_by_team_order_menu_item_keywords, lambda {|keywords|
		joins(menu_sections: :menu_items)
			 .distinct
			 .merge(MenuItem.search_by_keywords(keywords).where(archived_at: nil).where('team_order = :truthy OR team_order_only = :truthy', truthy: true))
			 .reorder(nil)
	}

	scope :visible_to, lambda {|customer_profile_id|
		# Returns SupplierProfile records where:
		# Either there are no associated CustomerProfile records (for the SupplierProfile)
		# Or there are associated CustomerProfile records and the input
		# `customer_profile_id` is part of the list
		arel_table = CustomerProfilesSupplierProfile.arel_table
		criteria = arel_table[:customer_profile_id].eq(customer_profile_id).to_sql
		sql = <<-SQL
			NOT exists(
							SELECT 1
							FROM customer_profiles_supplier_profiles
							WHERE customer_profiles_supplier_profiles.supplier_profile_id = supplier_profiles.id
					) OR exists(
							SELECT 1
							FROM customer_profiles_supplier_profiles
							WHERE customer_profiles_supplier_profiles.supplier_profile_id = supplier_profiles.id AND
									#{criteria}
					)
		SQL
		where(sql)
	}

	# Model Callbacks
	before_save :set_defaults
	before_save :make_slug, if: -> { changes['company_name'].present? }

	# before_validation :check_valid_closure_dates # suppliers' closure dates need to be beyond yordar closure period

	# Class methods
	def self.woolworths
		find_by!(id: yordar_credentials(:woolworths, :supplier_profile_id))
	end

	# Instance methods
	delegate(*SUPPLIER_FLAG_FIELDS, to: :supplier_flags)

	def lead_by_hour?
		lead_mode == 'by_hour'
	end

	def woolworths?
		id == yordar_credentials(:woolworths, :supplier_profile_id).to_i
	end

	# Major supplier determines which menu template is rendered for the supplier.
	# Major suppliers have a hierarchy of categories, the products of which are
	# loaded dynamically; and minor suppliers have one flat level of categories,
	# and all products are displayed upon page load.
	def is_major_supplier?
		woolworths?
	end

	# Returns the company_name, and fall-back to user name
	def name
		case
		when company_name.present?
			company_name
		when user.present?
			user.name
		else
			nil
		end
	end

	# returns first name of the supplier
	#
	def email_salutation
		user&.firstname || 'there'
	end

	def email_recipient
		recipient = case
		when email.present?
			email
		when user.present?
			user.email_recipient
		else
			yordar_credentials(:yordar, :admin_email)
		end
		recipient.gsub(/\r|\n/, '')
	end

	def yordar_commission
		1 - (1 - (commission_rate / 100)) / (1 + (markup / 100))
	end

	def current_agreement
		supplier_agreement_documents.last
	end

	def visible_to(customer_profile_id)
		SupplierProfile.visible_to(customer_profile_id).exists?(id: id)
	end

 	def rating
 		rating_count > 0 ? (rating_score / rating_count.to_f).round(1) : 0
 	end

 	def is_new?
 		(supplier_flags&.is_new_expires_at.present? && supplier_flags.is_new_expires_at > Time.zone.now) || created_at > 2.months.ago
 	end

 	def country_of_origin
 		user&.country_of_origin || 'AU'
 	end

	# Import menu data from a .csv file.
	# The format of the csv file should be the same as the exported .csv file (generated by the method ' Suppliers::ExportMenuToCsv')
	# requires 'ImportHelper'
	# NOTE: if the same id for menu_section/menu_item/serving_size has been defined multiple times, only the last record will be taken as update
	def import_menu_from_csv(csv_arrays)
		start = Time.now.utc # timestamp of start of importing. Will be used for deleting after import.
		import_msg = nil
		msg = nil

		# get first and last category name
		cats = Category.where(show_in_menu: true).order(:id)
		first_category = cats.first.name
		last_category = cats.last.name
		transaction do
			# clear all weights
			menu_sections.update_all(weight: nil)
			menu_items.update_all(weight: nil)
			menu_items.map{|mi| mi.serving_sizes.update_all(weight: nil) }

			# store and remove the first row (heads) from the csv file
			head = csv_arrays.shift
			# go through each row, create/update models, and check for error message
			csv_arrays.each do |r|
				if !is_data_empty?(r)
					row_no = csv_arrays.index(r) + 2

					# YOR-707 - after adding menu extra's, lets do a check here to see
					# if we have either an extra or serving size then import those
					ss_id = r[head.index('ServingSize.id')]
					ss_name = r[head.index('ServingSize.name')]
					ss_price = r[head.index('ServingSize.price')]
					if (ss_id.present? || ss_name.present?) && ss_price.blank? # If they have a serving size it must have a price
						ss_price = 0
					end
					ss_data = [ss_id, ss_name, ss_price]

					me_id = r[head.index('MenuExtra.id')]
					me_name = r[head.index('MenuExtra.name')]
					me_price = r[head.index('MenuExtra.price')]
					if (me_id.present? || me_name.present?) && me_price.blank? # If they have a menu extra it must have a price
						me_price = 0
					end
					me_data = [me_id, me_name, me_price]

					case
					when !is_data_empty?(ss_data)
						import_msg = import_serving_size(r, head, id, row_no, first_category, last_category)[1]
					when !is_data_empty?(me_data)
						import_msg = import_menu_extra(r, head, id, row_no, first_category, last_category)[1]
					else
						import_msg = import_menu_item(r, head, id, row_no, first_category, last_category)[1]
					end
				end
				# stop the loop if error msssage is not nil
				break if import_msg.present?
			end

			# if no error message
			# delete records which has not been created/updated during the import
			# msg = import_msg || delete_after_import(start, id)
			# archive menu item rather than delete
			msg = import_msg || archive_after_import(start, id)

			# perform a database rollback if error message presents
			if msg.present?
				puts "Menu import had an error #{msg}"
				raise ActiveRecord::Rollback
			else
				msg = 'Your menu has been successfully imported.'
			end
		end
		# return complete/error message
		msg
	end

private

	def can_deactivate?
		if !is_searchable && orders.where(status: %w[pending quoted new amended confirmed]).where('delivery_at > ?', Time.zone.now.beginning_of_week).present?
			errors.add(:is_searchable, 'cannot be set to false as the supplier still has pending future orders')
		end
	end

	# callback methods
	def set_defaults
		self.close_from ||= yordar_credentials(:yordar, :closure_start_date)
		self.close_to ||= yordar_credentials(:yordar, :closure_end_date)
		self.markup ||= 0
		true
	end

	def check_valid_closure_dates
		yordar_closure_start = Time.zone.parse(yordar_credentials(:yordar, :closure_start_date))
		yordar_closure_end = Time.zone.parse(yordar_credentials(:yordar, :closure_end_date))
		if close_from.present? && close_to.present? && ((close_from.to_date > yordar_closure_start.to_date && close_from.to_date < yordar_closure_end.to_date) || close_to.to_date < yordar_closure_end.to_date)
			errors.add(:base, "Closure dates need to be beyond Yordar closure dates of #{yordar_closure_start.to_date} and #{yordar_closure_end.to_date}")
		end
	end

	def make_slug
		self.slug = company_name.downcase.gsub(/[^a-z1-9]+/, '-').chomp('-')
	end

end
