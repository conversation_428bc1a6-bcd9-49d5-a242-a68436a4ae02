# == Schema Information
#
# Table name: payments
#
#  id                 :integer          not null, primary key
#  invoice_id         :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  credit_card_id     :integer
#  response_text      :text
#  auth_code          :string(255)
#  transaction_number :string(255)
#  user_id            :integer
#  amount             :decimal(10, 2)
#  order_id           :integer
#  attendee_id        :integer
#

class Payment < ActiveRecord::Base

	validates :amount, presence: true
	validates :order_id, presence: true
	validates :credit_card_id, presence: true

	validates :user_id, presence: true, if: -> { attendee_id.blank? }, on: :create
	validates :invoice_id, presence: true, if: -> { user_id.present? }, on: :create
	validates :attendee_id, presence: true, if: -> { user_id.blank? }, on: :create

	belongs_to :invoice
	belongs_to :order
	belongs_to :credit_card
	belongs_to :user, class_name: 'User'

	# only used in team_orders_controller attendee_pay
	# TODO - will need to be moved to a service object when we re-implement attendee payment
	def attendee_process
		success = false	# default response for process fnc
		# check if the payment was already processed
		# we do not process same payment twice!
		if response_text.blank?
			credit_card = CreditCard.find(credit_card_id)
			# extract the gateway_token from order's credit_card
			gateway_token = credit_card.gateway_token
			# gateway requires payment request to be made in cents (int value)
			amount_in_cents = amount * 100
			# :invoice will be picked up as invoice_reference in eWay
			options = { invoice: order.id, description: (order.name if order.name.present?) }
			# do the purchase
			gateway = ActiveMerchant::Billing::Base.gateway(:eway_managed).new(
					login: yordar_credentials(:eway, :login),
					username: yordar_credentials(:eway, :username),
					password: yordar_credentials(:eway, :password)
	    	)
			response = gateway.purchase amount_in_cents.to_i, gateway_token, options

			# save the payment response
			self.response_text = response.params['message']

			# check if this was a successful payment request
			success = response.params['success']
			if success
				# 'message'=>'00, Transaction Approved (Sandbox)', 'success'=>true, 'auth_code'=>'885111', 'transaction_number'=>'10187510'
				self.auth_code = response.params['auth_code']
				self.transaction_number = response.params['transaction_number']
			end
			# save the payment object
			save!
		end
		# response
		success
	end

	def self.tax_receipt_report_name(id, number)
		salt = yordar_credentials(:secret_key_base)
		md5 = Digest::MD5.hexdigest("#{id}#{number}" + salt)
		"tax_invoice/#{md5}"
	end
end
