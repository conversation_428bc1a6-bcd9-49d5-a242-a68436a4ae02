# == Schema Information
#
# Table name: employee_surveys
#
#  id                  :bigint           not null, primary key
#  customer_profile_id :bigint
#  category_group      :string
#  active              :boolean          default(TRUE)
#  uuid                :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  name                :string
#  description         :text
#
class EmployeeSurvey < ApplicationRecord

  VALID_CATEGORY_GROUPS = %w[catering-services kitchen-supplies].freeze

  validates :name, presence: true
  validates :customer_profile, presence: true
  validates :category_group, presence: true, inclusion: { in: VALID_CATEGORY_GROUPS }, uniqueness: { scope: :customer_profile_id }
  validates :uuid, presence: true

  belongs_to :customer_profile
  has_many :survey_questions, dependent: :destroy
  has_many :submissions, class_name: 'EmployeeSurveySubmission', dependent: :destroy
  has_many :documents, as: :documentable

  def category_group_name
    case category_group
    when 'catering-services'
      'Catering'
    when 'kitchen-supplies'
      'Pantry'
    end
  end

end
