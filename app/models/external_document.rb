# == Schema Information
#
# Table name: external_documents
#
#  id              :integer          not null, primary key
#  doc_type        :string(255)
#  url             :string(255)
#  status          :text
#  rejected_reason :string(255)
#  order_id        :integer
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  ref_number      :string(255)
#

class ExternalDocument < ActiveRecord::Base

	VALID_DOC_TYPES = %w[pod supplier_invoice ffc_invoice].freeze
	VALID_DOC_STATUSES = %w[pending in-progress approved rejected].freeze

	validates_presence_of :doc_type, :order
	validates :doc_type, inclusion: { in: VALID_DOC_TYPES }
	validates :status, inclusion: { in: VALID_DOC_STATUSES }

	# Update the status of the proof of delivery docket based on whether one has been provided (Category Solutions only)
	before_validation :update_status, if: -> { url.blank? || saved_change_to_url? }

	belongs_to :order

	def update_status
		# pending - If we don't have a document then it is pending
		# in-progress - IfUploaded a new document so it is awaiting approval
		self.status = url.blank? ? 'pending' : 'in-progress'
	end

	# Used by Rails Admin to show a pretty name for approving/rejecting a document
	def name
		type = case doc_type
		when 'pod'
			'Proof of Delivery'
		when 'supplier_invoice'
			'Supplier Invoice'
		when 'ffc_invoice'
			'Freight Forwarding Company Invoice'
		else
			'Document'
		end
		"#{type} for Order ##{order_id}"
	end
end
