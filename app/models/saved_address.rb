# == Schema Information
#
# Table name: saved_addresses
#
#  id                  :bigint           not null, primary key
#  customer_profile_id :bigint
#  level               :string
#  street_address      :string
#  suburb_id           :integer
#  instructions        :text
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class SavedAddress < ApplicationRecord

  validates :street_address, presence: true
  validates :suburb_id, presence: true
  validates :instructions, presence: true

  belongs_to :customer_profile
  belongs_to :suburb

  def address_arr
    address = []
    address << formatted_level
    address << street_address
    address << suburb.label
    address.reject(&:blank?)
  end

  def formatted_level
    saved_level = level&.strip
    return '' if saved_level.blank?

    formatted = saved_level.match(/level|^l/i).present? ? '' : 'Level '
    formatted += saved_level
    formatted
  end

end
