# == Schema Information
#
# Table name: custom_order_suppliers
#
#  id                  :integer          not null, primary key
#  order_id            :integer
#  supplier_profile_id :integer
#  delivery_at         :datetime
#  delivery_fee        :decimal(10, 2)
#  delivery_note       :text
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#

# Do we really need this
class CustomOrderSupplier < ActiveRecord::Base

	has_paper_trail only: %i[delivery_at delivery_note]

	validates :order_id, presence: true
	validates :supplier_profile_id, presence: true

	belongs_to :order
	belongs_to :supplier_profile

end
