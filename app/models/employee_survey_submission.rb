# == Schema Information
#
# Table name: employee_survey_submissions
#
#  id                 :bigint           not null, primary key
#  employee_survey_id :bigint
#  name               :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  overall_rating     :integer
#
class EmployeeSurveySubmission < ApplicationRecord

  validates :employee_survey, presence: true

  belongs_to :employee_survey
  has_many :survey_answers, dependent: :destroy

end
