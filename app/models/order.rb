# == Schema Information
#
# Table name: orders
#
#  id                         :integer          not null, primary key
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  customer_profile_id        :integer
#  invoice_id                 :integer
#  status                     :text
#  pattern                    :string(255)
#  recurring_order_params     :text
#  contact_name               :string(255)
#  company_name               :string(255)
#  po_number                  :string(255)
#  department_identity        :string(255)
#  contact_email              :string(255)
#  name                       :string(255)
#  delivery_at                :datetime
#  renewed_from_id            :integer
#  template_id                :integer
#  renewed_to_id              :integer
#  recurrent_id               :integer
#  skip                       :boolean          default(TRUE)
#  version_ref                :string(255)
#  delivery_address           :string(255)
#  delivery_suburb_id         :integer
#  credit_card_id             :integer
#  phone                      :string(255)
#  payment_status             :string(255)      default("unpaid")
#  delivery_instruction       :text
#  customer_subtotal          :decimal(10, 2)
#  customer_gst               :decimal(10, 2)
#  customer_total             :decimal(10, 2)
#  customer_delivery          :decimal(10, 2)
#  customer_surcharge         :decimal(10, 2)
#  pushed_to_xero             :boolean          default(FALSE)
#  order_type                 :string(255)
#  old_delivery_at            :datetime
#  suppliers_notified_at      :datetime
#  pdf_version_num            :integer
#  freight_forwarding_rate_id :integer
#  split_order_id             :integer
#  document_urls              :text
#  estimated_delivery         :datetime
#  ffc_bill_pushed_to_xero    :boolean          default(FALSE)
#  rejected_reason            :string(255)
#  order_variant              :string(255)      default("general")
#  commission                 :decimal(10, 2)
#  no_delivery_charge         :boolean          default(FALSE)
#  cpo_id                     :integer
#  discount                   :decimal(10, 2)   default(0.0)
#  coupon_id                  :integer
#  number_of_people           :integer
#  pushed_as_rgi_to_xero      :boolean
#  attendee_pays              :boolean          default(FALSE)
#  payment_id                 :integer
#  unique_event_id            :string
#  order_category             :string
#  delivery_address_level     :string
#  delivery_type              :string           default("normal")
#  whodunnit_id               :integer
#  charge_to_minimum          :boolean          default(FALSE)
#  customer_topup             :decimal(10, 2)
#  invoice_individually       :boolean          default(FALSE)
#

class Order < ActiveRecord::Base

	has_paper_trail only: %i[delivery_at delivery_address delivery_address_level delivery_instruction delivery_type department_identity company_name contact_name phone status pattern template_id renewed_from_id renewed_to_id payment_status credit_card_id]

	attr_accessor :delivery_at_date, :delivery_at_time, :delivery_dates, :delivery_suppliers, :attendee, :update_with_invoice

  # constants
  TEAM_ORDER_GRACE_PERIOD = 1.hour
	VALID_ORDER_STATUSES = %w[draft pending saved quoted cancelled skipped paused new amended confirmed delivered voided].freeze # awaiting_ffc_confirmation
  VALID_PATTERNS = %w[1.week 2.weeks 4.weeks 1.month].freeze
  VALID_PAYMENT_STATUSES = %w[unpaid paid error].freeze
  VALID_ORDER_TYPES = %w[one-off recurrent].freeze
  VALID_ORDER_VARIANTS = %w[general team_order recurring_team_order event_order home_delivery].freeze
  VALID_DELIVERY_TYPES = %w[normal contactless loading_dock].freeze
  VERSION_REF_FIELDS = %i[contact_name company_name department_identity delivery_at delivery_address_level delivery_address delivery_suburb_id].freeze

  # Validations
	validates :status, inclusion: { in: VALID_ORDER_STATUSES }
	validates :pattern, inclusion: { in: VALID_PATTERNS }, allow_blank: true
	validates :payment_status, inclusion: { in: VALID_PAYMENT_STATUSES }, allow_blank: true
	validates :order_type, inclusion: { in: VALID_ORDER_TYPES }, allow_blank: true
  validates :order_variant, inclusion: { in: VALID_ORDER_VARIANTS }, allow_blank: true
	validates :delivery_type, inclusion: { in: VALID_DELIVERY_TYPES }, allow_blank: true
  validates :delivery_at, presence: true, if: -> { %w[draft cancelled].exclude?(status) }
  validates :uuid, presence: true
  validates :major_category, presence: true, if: :is_event_order?
  validate :check_update_with_invoice

  # Conditional validation based on order status -
  # see http://rubyquicktips.com/post/*********/conditional-validation-using-with-options-to-improve
  # and http://stackoverflow.com/questions/13174435/rails-nested-with-option-if-used-in-validation
  #
  with_options if: -> { status != 'draft' } do |order|
    order.validates_presence_of :name
    # order.validates_presence_of :number_of_people, unless: 'is_team_order? || is_event_order?'
    order.validates_presence_of :delivery_suburb_id
    order.validates_presence_of :credit_card_id, unless: -> { is_team_order? || %w[quoted cancelled].include?(status) }
    order.validates_presence_of :customer_profile_id
    order.validates_presence_of :delivery_address
    # might need more..
  end

  with_options if: -> { template_id.present? && recurrent_id.present? } do |order|
    order.validates_presence_of :pattern
  end

  with_options if: -> { status == 'confirmed' } do |order|
    order.validate do
    # accepted (and notified) order_lines status means supplier has accepted them
    # notified is set to orderline when customer made change to order_line,
    # and supplier is informed of this change (without informing other suppliers)
    #
      pending_order_lines_count = order_lines.where.not(status: %w[accepted notified]).count
      if pending_order_lines_count > 0
        errors.add(:base, "#{pending_order_lines_count} order line(s) have not been accepted")
      end
    end
  end

  ##
  ## Associations and Relationships
  ##
  belongs_to :customer_profile
  belongs_to :pantry_manager, class_name: 'CustomerProfile', foreign_key: :pantry_manager_id
  belongs_to :delivery_suburb, class_name: 'Suburb', foreign_key: :delivery_suburb_id
  belongs_to :invoice, foreign_key: :invoice_id, inverse_of: :orders
  belongs_to :gst_free_invoice, class_name: 'Invoice', foreign_key: :gst_free_invoice_id, inverse_of: :orders
  belongs_to :credit_card
  belongs_to :customer_purchase_order, foreign_key: :cpo_id
  belongs_to :gst_free_customer_purchase_order, class_name: 'CustomerPurchaseOrder', foreign_key: :gst_free_cpo_id
  belongs_to :coupon
  belongs_to :promotion
  belongs_to :major_category, class_name: 'Category', foreign_key: :major_category_id
  belongs_to :loading_dock
  # belongs_to :freight_forwarding_rate

  has_many :order_lines, dependent: :destroy
  has_many :ordered_menu_items, through: :order_lines, source: :menu_item
  has_many :ordered_categories, through: :order_lines, source: :category
  has_many :locations, -> { distinct }, through: :order_lines
  has_many :supplier_profiles, -> { distinct }, through: :order_lines

  has_many :order_suppliers, dependent: :destroy
  has_many :custom_order_suppliers, dependent: :destroy
  has_many :documents, as: :documentable
  belongs_to :meal_plan
  belongs_to :customer_quote

	# Category Solutions external documents such as PODs and invoices
	has_many :external_documents
  has_many :on_hold_charges, class_name: 'Order::Charge'

  # team order related
	has_one :team_order_detail, class_name: 'TeamOrder::Detail', dependent: :destroy
  has_many :team_order_levels, class_name: 'TeamOrder::Level', through: :team_order_detail, source: :levels
	accepts_nested_attributes_for :team_order_detail
  has_many :team_order_attendees, dependent: :destroy
  has_many :event_attendees, through: :team_order_attendees
  has_many :team_supplier_profiles, through: :order_suppliers, source: :supplier_profile

  # Woolworths related
  has_one :associated_woolworths_order, class_name: 'Woolworths::Order'
  accepts_nested_attributes_for :associated_woolworths_order
  has_one :woolworths_order, class_name: 'Woolworths::Order'
  has_one :woolworths_account, -> { where(woolworths_orders: { account_in_use: true }) }, through: :woolworths_order, source: :account
  has_one :attached_woolworths_account, through: :woolworths_order, source: :account
  has_many :woolworths_products, through: :woolworths_order, source: :trolley_products
  has_many :synced_woolworths_products, through: :woolworths_order, source: :snyced_trolley_products
  has_many :synced_woolworths_order_lines, through: :synced_woolworths_products, source: :order_line

  has_many :dear_sales, class_name: 'Dear::Sale'

  # TODO: - remove document_urls field along with Serializing
	# Everytime a document is generated, e.g. delivery docket, order details, we are saving it into this hash
  SERIALIZED_FIELDS = [:document_urls].freeze
  SERIALIZED_FIELDS.each do |field|
    define_method "#{field}=" do |value|
      sanitized_value = (value.present? ? value.to_yaml : nil)
      write_attribute(field, sanitized_value)
    end
    define_method field do
      field_data = read_attribute(field)
      field_data.present? ? YAML.safe_load(field_data) : nil
    end
  end

  # Model callbacks
  before_destroy :log_order_deletion

  # Save version which contains md5 result, so that we know if this order has been amended in some fields
  before_save :assign_version_ref, if: -> { status != 'draft' }

  # Executed after each order object being saved
  after_save do
    # Set the renewal template and from if we have a pattern for renewal
    if pattern.present? && (
      template_id.blank? || renewed_from_id.blank? || recurrent_id.blank?
    )
      self.template_id = id if template_id.nil?
      self.renewed_from_id = id if renewed_from_id.nil?
      self.recurrent_id = id if recurrent_id.nil?
      ##
      ## WARNING - CALL TO RECUSRIVE .save METHOD (will trigger before save!!)
      save if changes
    end

    is_splitable_order = status == 'new' && !is_team_order? && !is_event_order?
    customer_is_cs = customer_profile.present? && customer_profile.user.present? && customer_profile.user.business == 'cs'

    if saved_change_to_status? && is_splitable_order && split_order_id.blank? && customer_is_cs
      split_order
    end
  end

  after_save :create_documents, if: -> { saved_change_to_status? && status == 'delivered' && !is_yordar? }

  # association method delegation
  delegate :budget, to: :team_order_detail, allow_nil: true, prefix: 'team_order'
  delegate :hide_budget, to: :team_order_detail, allow_nil: true
  delegate :cutoff_option, to: :team_order_detail, allow_nil: true
  delegate :attendee_pays, to: :team_order_detail, allow_nil: true
  delegate :package_id, to: :team_order_detail, allow_nil: true
  delegate :is_package_order?, to: :team_order_detail, allow_nil: true

  delegate :po_number, to: :customer_purchase_order, allow_nil: true
  delegate :po_number, to: :gst_free_customer_purchase_order, allow_nil: true, prefix: 'gst_free'

  delegate :symbolized_country_code, to: :delivery_suburb, allow_nil: true

	# lock down the delivered status, once delivered, the order status can not be changed.
	# If renewal from template order, status change is allowed as it's not persisted in db yet.
	def status=(new_status)
		super if new_record? || status != 'delivered' || new_status == 'voided'
	end

  # boolean methods
	def is_team_order?
		%w[team_order recurring_team_order].include?(order_variant)
	end

  def is_recurring_team_order?
    order_variant == 'recurring_team_order'
  end

	def is_event_order?
		order_variant == 'event_order'
	end

	def is_home_delivery?
		order_variant == 'home_delivery'
	end

  # Returns whether the current order is a recurrent order or not
  def is_recurrent?
    order_type == 'recurrent'
  end

	def is_contactless_delivery?
		delivery_type.present? && delivery_type == 'contactless'
	end

  # Helper to get the business attribute off the user (whether they are Yordar or Category Solutions)
  def is_yordar?
    true
  end

  def has_gst_split_pos?
    cpo_id.present? && gst_free_cpo_id.present?
  end

  def is_woolworths_order?
    @_is_woolworths_order ||= woolworths_order.present? || has_woolworths_items?
  end

  def has_woolworths_items?
    @_has_woolworths_items ||= order_lines.any? { |order_line| order_line.supplier_profile.woolworths? }
  end

  def is_dear_order?
    @_is_dear_order ||= dear_sales.present? || dear_suppliers.present?
  end

  def dear_suppliers
    @_dear_suppliers ||= supplier_profiles.joins(:dear_account).where.not(dear_accounts: { id: nil }).where.not(dear_accounts: { active: false }).distinct
  end

  def attendee_levels
    @_attendee_levels ||= is_team_order? && team_order_levels.presence
  end

  # name override for Woolworths Orders
  def woolworths_order_name
    order_name = name || ''
    return order_name if woolworths_order.blank?

    if (woolworths_order_id = woolworths_order.woolworths_order_id.presence)
      order_name = order_name.split(' (Woolworths ID').first if order_name.include?('Woolworths ID')
      order_name += " - Woolworths ID: ##{woolworths_order_id}"
    end
    if (account = attached_woolworths_account.presence)
      order_name += " - #{account.short_name}"
    end
    order_name
  end

  def recurrent_type
    case pattern
    when '1.week'
      'weekly'
    when '2.weeks'
      'fortnightly'
    when '1.month'
      'monthly'
    when '4.weeks'
      'every 4 weeks'
    else
      nil
    end
  end

	def original_creator
    return nil if whodunnit_id.blank?

		User.where(id: whodunnit_id).first
	end

  def current_version_ref
    reference = ''
    reference = VERSION_REF_FIELDS.map do |field|
      send(field)
    end.join('')
    order_lines.each do |order_line|
      reference += OrderLine::ORDER_VERSION_REF_FIELDS.map do |field|
        order_line.send(field)
      end.join('')
    end
    order_suppliers.where.not(delivery_fee_override: nil).each do |order_supplier|
      reference += OrderSupplier::ORDER_VERSION_REF_FIELDS.map do |field|
        order_supplier.send(field).to_s
      end.join('')
    end
    Digest::MD5.hexdigest(reference)
  end

	# find the delivery_at date of the first order in the series
	# used in order change recurring order 'email' template
	def template_delivery_at
		order_day = nil
		if is_recurrent?
			parent_template = Order.where(id: template_id).first
			order_day = parent_template.present? ? parent_template.delivery_at : delivery_at
		end
		order_day
	end

	def address_label
    label = delivery_address_level.present? ? "#{formatted_delivery_address_level}, " : ''
    label += delivery_address
    label += delivery_suburb.present? ? " - #{delivery_suburb.label}" : ''
    label
	end

	# YOR-645 this is used for grouping orders by location and po in monthly tax invoice
	def address_po_label
    label = address_label
    label += " | PO ##{po_number}" if po_number.present?
    label
	end

  def delivery_date
    delivery_at.present? ? delivery_at.to_date : nil
  end

	# create an order details pdf path
	def order_details_report_name(order, ref)
		md5 = Digest::MD5.hexdigest("#{order.id}/#{ref}" + yordar_credentials(:secret_token))
		"order_details/#{md5}"
	end

	def delivery_docket_report_name(order, ref)
		md5 = Digest::MD5.hexdigest("#{order.id}/#{ref}" + yordar_credentials(:secret_token))
		"delivery_docket/#{md5}"
	end

	def delivery_address_arr
		address_arr = []
		address_arr << formatted_delivery_address_level
		address_arr << delivery_address
		address_arr << delivery_suburb&.label
		address_arr.reject(&:blank?)
	end

  def formatted_delivery_address_level
    return '' if delivery_address_level.blank?

    address_level = delivery_address_level.strip
    return '' if address_level.blank?

    level = address_level.match(/level|^l|unit/i).present? ? '' : 'Level '
    level += address_level
    level
  end

  # ******* DEPRECATED CODE ********
  # Used in Deprecated Orders controller method for Category Solutions order#reject
  # Sets the order lines belonging to the given supplier to 'confirmed', and updates the status of the order
  def set_supplier_order_lines_status!(supplier_profile_id, status)
    case status
    when 'accepted'
      # update all the renewed orders' order lines' statuses related to a given supplier to 'accepted'
      if order_type == 'one-off' || order_type != 'recurrent'
        ols = order_lines.where(supplier_profile_id: supplier_profile_id)
      else
        # ols = OrderLine.includes(:order).where("((orders.order_type != 'one-off' AND orders.template_id = :template_id)) AND order_lines.supplier_profile_id = :sup_id", id: id, template_id: template_id, sup_id: supplier_profile_id)
        # SUPP-1129 confirming recurring order not working. Should find related orders by template_id, not the order's id. Order lines of all related orders will be accepted.
        # ols = OrderLine.includes(:order).where(orders: { order_type: 'recurrent', template_id: id}, supplier_profile_id: supplier_profile_id)
        ols = OrderLine.includes(:order).where(orders: { order_type: 'recurrent', template_id: template_id }, supplier_profile_id: supplier_profile_id)
      end

      # need to use update_attributes instead of update_all because mysql doesn't allow ambiguous update 'status'
      # and postgres doesn't allow query in update_all, i.e.("order_lines.status = 'accepted'")
      ols.map{|ol| ol.update(status: 'accepted') }
      # if the statuses of all the order lines of any renewed orders are 'accepted' then update the orders statuses to 'confirmed'
      ols.includes(:order).group_by(&:order).map{|order, order_lines| order.update(status: 'confirmed') if order_lines.count == order.order_lines.where(supplier_profile_id: supplier_profile_id).count}
    when 'rejected'
      updated = OrderLine.where(supplier_profile_id: supplier_profile_id, order_id: id).update_all status: status
      if updated
        ids = order_lines.map{|ol| ol.supplier_profile.id }
        suppliers = SupplierProfile.where(id: ids)
        logger.info "Sending order reject emails to suppliers #{ids}"
        suppliers.each do |supplier|
          Orders::Emails::SendOrderRejectedAdminEmail.new(order: self, supplier: supplier).delay(queue: :notifications).call
        end
      end
    end
  end

  # Update order status / orderlines status for FFC in order list
  def update_ffc_order_lines_status!(status)
    case status
    when 'confirmed'
      update(status: status)
    when 'rejected'
      order_lines.update_all(status: status)
    end
  end

private

 def check_update_with_invoice
   if invoice_id.present? && update_with_invoice.blank?
     logger.info "Order #{id} cannot be saved as invoice has been issued."
     errors.add(:invoice_id, 'Cannot be saved as invoice has been issued')
   end
 end

  # callback methods
  # logging out any order deletions since this should not occur!
  def log_order_deletion
    Rails.logger.error "Order destroy initiated on Order ID: #{id} with orderlines: #{OrderLine.where(order_id: id).pluck(:id)} - Order inspect: #{Order.find(id).inspect}"
  end

  def create_documents
    %w[pod supplier_invoice].each do |d|
      ExternalDocument.create!(order_id: id, doc_type: d)
    end
    if order_lines.first.supplier_profile.needs_freight?
      ExternalDocument.create!(order_id: id, doc_type: 'ffc_invoice')
    end
  end

  def assign_version_ref
    self.version_ref = current_version_ref
  end

  # Split orders by supplier and freight type for Category Solutions users
  def split_order
    # split_order_id will contain the id of the order which this was split from,  or nil if it was not split
    # note: all new orders placed by CS users will have this set
    self.split_order_id = id
    first_order = true # Keep some orderlines in the order
    # Split order based on supplier profile and freight type
    order_lines.group_by(&:supplier_profile).each do |sp, supplier_profile_ols|
      supplier_profile_ols.group_by{ |ol| ol.menu_item.freight_type }.each do |freight_type, freight_type_ols|
        if first_order
          first_order = false
          next
        else
          # Move the orderlines into a different order (we don't need to recreate them)
          dup_order = self.dup
          dup_order.split_order_id = id # Keep a reference to the original order id
          dup_order.delivery_at = delivery_at # dup does not copy datetimes correctly. TODO: Is this really necessary?
          dup_order.save
          # Copy orderlines before setting the rate
          freight_type_ols.each { |ol2| ol2.update_column(:order_id, dup_order.id) }
          dup_order.set_freight_forwarding_rate(freight_type, sp)
          dup_order.save
          # Re-calculate the totals for each of the suborders
          Orders::CalculateCustomerTotals.new(order: dup_order, save_totals: true).call
        end
      end
    end
    set_freight_forwarding_rate
    save
    # Now we have split this order, recalculate the totals
    Orders::CalculateCustomerTotals.new(order: self, save_totals: true).call
  end

  # Used by split_order
  def set_freight_forwarding_rate(freight_type = nil, supplier = nil, order_delivery_suburb_postcode = nil)
    ol = order_lines.first
    supplier ||= ol.supplier_profile
    freight_type ||= ol.menu_item.freight_type
    if supplier.needs_freight?
      rate = FreightForwardingRate.find_cheapest_match(freight_type, self, supplier, order_delivery_suburb_postcode)
      self.freight_forwarding_rate = rate
    end
  end

end
