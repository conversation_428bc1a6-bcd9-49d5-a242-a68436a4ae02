# == Schema Information
#
# Table name: woolworths_accounts
#
#  id               :integer          not null, primary key
#  email            :string           not null
#  password         :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  access_token     :string
#  refresh_token    :string
#  token_expires_at :datetime
#
class Woolworths::Account < ActiveRecord::Base
  has_many :woolworths_orders, class_name: 'Woolworths::Order', foreign_key: :account_id, dependent: :restrict_with_exception
  has_many :orders, through: :woolworths_orders

  validates :email, presence: true, uniqueness: { case_sensitive: false, allow_blank: true }, format: { with: Devise::email_regexp, allow_blank: true }
  validates :password, presence: true

  def available?
    woolworths_orders.blank? || woolworths_orders.where(account_in_use: false).present?
  end

  def short_name
    if (num = email.scan(/\d+/).first.presence)
      "W#{num}"
    else
      email.split('@').first
    end
  end

end
