# == Schema Information
#
# Table name: woolworths_trolley_products
#
#  id                  :integer          not null, primary key
#  woolworths_order_id :integer
#  order_line_id       :integer
#  synced              :boolean          default(FALSE), not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  trolley_errors      :string           default([]), not null, is an Array
#
class Woolworths::TrolleyProduct < ActiveRecord::Base

  belongs_to :woolworths_order, class_name: 'Woolworths::Order'
  belongs_to :order_line

end
