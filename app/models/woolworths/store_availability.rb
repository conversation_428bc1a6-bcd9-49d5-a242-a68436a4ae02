# == Schema Information
#
# Table name: woolworths_store_availabilities
#
#  id             :integer          not null, primary key
#  menu_item_id   :integer          not null
#  store_id       :integer          not null
#  stock_quantity :integer          not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#
class Woolworths::StoreAvailability < ActiveRecord::Base

  validates :menu_item, presence: true, uniqueness: { scope: :store_id, message: 'can only have one availability record per Woolworths store' }
  validates :store_id, presence: true, inclusion: { in: Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys, message: 'is not a recognized Woolworths store ID' }
  validates :stock_quantity, presence: true, numericality: { only_integer: true, greater_than: 0 }

  belongs_to :menu_item
  has_one :menu_section, through: :menu_item
end
