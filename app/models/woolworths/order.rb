# == Schema Information
#
# Table name: woolworths_orders
#
#  id                  :integer          not null, primary key
#  order_id            :integer
#  account_id          :integer
#  account_in_use      :boolean          default(FALSE), not null
#  status              :string
#  delivery_window_id  :integer
#  delivery_address_id :integer
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  woolworths_order_id :string
#  delivery_fee        :decimal(, )
#
class Woolworths::Order < ActiveRecord::Base
  belongs_to :order, class_name: '::Order'
  belongs_to :account, class_name: 'Woolworths::Account'

  has_many :trolley_products, class_name: 'Woolworths::TrolleyProduct', foreign_key: :woolworths_order_id, dependent: :destroy
  has_many :snyced_trolley_products, -> { where(synced: true) }, class_name: 'Woolworths::TrolleyProduct', foreign_key: :woolworths_order_id, dependent: :destroy

  has_many :order_lines, through: :trolley_products, source: :order_line

  validates :order, presence: true
  # validates :woolworths_account, presence: true

end
