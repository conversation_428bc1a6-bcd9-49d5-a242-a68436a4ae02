# == Schema Information
#
# Table name: order_suppliers
#
#  id                     :integer          not null, primary key
#  order_id               :integer
#  supplier_profile_id    :integer
#  status                 :string(255)
#  surcharge              :decimal(, )
#  selected_menu_sections :integer          default([]), is an Array
#  cutoff_4hr_reminder    :datetime
#  subtotal               :decimal(10, 2)
#  delivery               :decimal(10, 2)
#  gst                    :decimal(10, 2)
#  total                  :decimal(10, 2)
#  cutoff_day_reminder    :datetime
#  supplier_invoice_id    :integer
#

class OrderSupplier < ActiveRecord::Base

	ORDER_VERSION_REF_FIELDS = %i[delivery_fee_override].freeze

	belongs_to :order
	belongs_to :supplier_profile
  belongs_to :supplier_invoice, foreign_key: :supplier_invoice_id

  has_many :documents, as: :documentable

end
