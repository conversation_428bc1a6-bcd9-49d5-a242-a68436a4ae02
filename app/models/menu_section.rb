# == Schema Information
#
# Table name: menu_sections
#
#  id                  :integer          not null, primary key
#  name                :string(255)
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  supplier_profile_id :integer
#  weight              :integer
#  archived_at         :datetime
#  group_name          :string
#  is_hidden           :boolean          default(FALSE)
#

class MenuSection < ActiveRecord::Base

	has_many :category_menu_sections
	has_many :categories, through: :category_menu_sections

	has_many :menu_items, dependent: :destroy
	belongs_to :supplier_profile
	has_and_belongs_to_many :companies

	# Favourite menu_section stub
	def self.favourite_menu_section(supplier:)
		new(id: -1, name: 'Favourites', group_name: 'My Favourites', supplier_profile: supplier)
	end

	# Search menu_section stub
	def self.search_menu_section(supplier:, search_keywords:)
		new(id: -2, name: "Searched for: #{search_keywords}", supplier_profile: supplier)
	end

	def self.recent_orders_menu_section(supplier:)
		new(id: -3, name: 'Order Again', group_name: 'Recent Orders', supplier_profile: supplier)
	end

	def is_favourite_section?
		id == -1 && name == 'Favourites'
	end

end
