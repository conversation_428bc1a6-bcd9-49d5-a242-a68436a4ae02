# == Schema Information
#
# Table name: order_lines
#
#  id                   :integer          not null, primary key
#  name                 :string(255)
#  price                :decimal(10, 2)
#  location_id          :integer
#  quantity             :integer
#  note                 :text
#  order_id             :integer
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  supplier_profile_id  :integer
#  status               :string(255)      default("pending")
#  cost                 :decimal(10, 2)
#  category_id          :integer
#  is_gst_free          :boolean          default(FALSE)
#  menu_item_id         :integer
#  serving_size_id      :integer
#  attendee_id          :integer
#  team_admin_id        :integer
#  is_gst_inc           :boolean          default(FALSE)
#  selected_menu_extras :text
#  sent_as_rgi_to_xero  :boolean
#  payment_id           :integer
#  payment_status       :string
#  baseline             :decimal(10, 2)
#  last_errors          :jsonb
#

class OrderLine < ActiveRecord::Base

	has_paper_trail only: %i[quantity note name price]

	VALID_ORDER_LINE_STATUSES = %w[pending accepted rejected amended notified].freeze
	VALID_ORDER_LINE_PAYMENT_STATUSES = %w[unpaid paid error].freeze
	ORDER_VERSION_REF_FIELDS = %i[id quantity note].freeze

	validates :quantity, presence: true
	validates :location_id, presence: true
	validates :name, presence: true, on: :update
	validates :price, presence: true, on: :update
	validates :cost, presence: true, on: :update
	validates :status, inclusion: { in: VALID_ORDER_LINE_STATUSES }	# these status are set by the respective suppliers, add more..?
	validates :payment_status, inclusion: { in: VALID_ORDER_LINE_PAYMENT_STATUSES }, allow_blank: true

	belongs_to :order
	belongs_to :location
	belongs_to :supplier_profile
	belongs_to :category
	belongs_to :menu_item
	has_one :menu_section, through: :menu_item
	belongs_to :serving_size
	belongs_to :team_order_attendee, foreign_key: :attendee_id
	has_one :trolley_product, class_name: 'Woolworths::TrolleyProduct'
	belongs_to :payment # can make explicit that we only want payment against normal orders

	attr_accessor :company_id, :address_label # used for reporting purposes

	SERIALIZED_FIELDS = [:selected_menu_extras].freeze
	SERIALIZED_FIELDS.each do |field|
	  define_method "#{field}=" do |value|
	  	sanitized_value = (value.present? ? value.to_yaml : nil)
	   	write_attribute(field, sanitized_value)
	  end
	  define_method field do
	    field_data = read_attribute(field)
	    field_data.present? ? YAML.safe_load(field_data) : nil
	  end
	end

	def price_inc_gst(gst_country: nil)
		return 0 if price.blank?
		return price if is_gst_free || is_gst_inc

		country_code =  gst_country&.downcase&.to_sym || order&.symbolized_country_code || :au
		price.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)
	end

	def total_price(gst_country: nil)
		(price_exc_gst(gst_country: gst_country) * quantity).round(2)
	end

	def total_cost(gst_country: nil)
		(cost_exc_gst(gst_country: gst_country) * quantity).round(2)
	end

	def price_exc_gst(gst_country: nil)
		return 0 if price.blank?
		return price if !is_gst_inc

		country_code = gst_country&.downcase&.to_sym || order&.symbolized_country_code || :au
		price / (1 + yordar_credentials(:yordar, :gst_percent, country_code))
	end

	def cost_exc_gst(gst_country: nil)
		return 0 if cost.blank?
		return cost if !is_gst_inc

		country_code = gst_country&.downcase&.to_sym || order&.symbolized_country_code || :au
		cost / (1 + yordar_credentials(:yordar, :gst_percent, country_code))
	end

	# user in for team order attendee pay
	def mark_as_paid(payment_id)
		update_columns(payment_status: 'paid', payment_id: payment_id)
	end

	def mark_as_payment_error(payment_id)
		update_columns(payment_status: 'error', payment_id: payment_id)
	end

end
