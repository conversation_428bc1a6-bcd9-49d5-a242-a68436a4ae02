class EventLog < ApplicationRecord

  VALID_ORDER_EVENTS = %w[
    new-order-quoted
    new-order-submitted
    woolworths-checkout-failed
    new-amazon-order
    order-amended
    order-rejected
    order-canceled
    order-canceled-permanently
    on-hold-charge-failed
  ].freeze

  VALID_TEAM_ORDER_EVENTS = %w[
    new-team-order-created
    new-package-created
    package-extended
    approaching-cutoff
    approaching-cutoff-below-minimum
  ].freeze

  VALID_CUSTOM_ORDER_EVENTS = %w[
    custom-order-saved-as-draft
    new-custom-order-quoted
    new-custom-order-submitted
    order-below-margin-threshold
  ].freeze

  VALID_SUPPLIER_EVENTS = %w[
    new-supplier-registration
    supplier-agreement-signed
    searchable-updated
    margin-updated
  ].freeze

  VALID_CUSTOMER_EVENTS = %w[
    new-customer-registration
    new-quote-submitted
    invoice-overdue
    company-team-admin-request
  ].freeze

  VALID_BUDGET_EVENTS = %w[
    budget-spend-50
    budget-spend-75
    budget-spend-90
    budget-spend-100
  ].freeze

  VALID_ADMIN_EVENTS = %w[
    pending-orders
    orders-auto-confirmed
    upcoming-public-holiday
    monthly-calendar-event
  ].freeze

  ASSIGNABLE_EVENTS = %w[
    new-quote-submitted
    new-customer-registration
    company-team-admin-request
    new-amazon-order
  ].freeze

  VALID_EVENTS = (VALID_ORDER_EVENTS + VALID_TEAM_ORDER_EVENTS + VALID_CUSTOM_ORDER_EVENTS + VALID_SUPPLIER_EVENTS + VALID_CUSTOMER_EVENTS + VALID_BUDGET_EVENTS + VALID_ADMIN_EVENTS).freeze
  VALID_SEVERITIES = %w[info warning error].freeze

  validates :event, presence: true, inclusion: { in: VALID_EVENTS }
  validates :severity, presence: true, inclusion: { in: VALID_SEVERITIES }

  belongs_to :loggable, polymorphic: true
  belongs_to :scopable, polymorphic: true
  belongs_to :assigned_to, class_name: 'User', foreign_key: :assigned_user_id
  has_many :views, class_name: 'EventLog::View'

  def itemized_info
    deep_struct(info)
  end

end
