# == Schema Information
#
# Table name: customer_profile_menu_items
#
#  id                  :integer          not null, primary key
#  customer_profile_id :integer          not null
#  menu_item_id        :integer          not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class CustomerProfileMenuItem < ActiveRecord::Base

  validates :customer_profile, presence: true
  validates :menu_item, presence: true
  validates :menu_item, uniqueness: { scope: :customer_profile }

  belongs_to :customer_profile
  belongs_to :menu_item
end
