class MyMailer < Devise::Mailer
  helper :application # gives access to all helpers defined within `application_helper`.
  include Devise::Controllers::UrlHelpers # Optional. eg. `confirmation_url`

	# Override devise's sending confirmation_instructions function. Don't call anything so we avoid sending devise email
	# In user model call back, we send email with our own template instead
  def confirmation_instructions(record)
    Users::Emails::SendAccountConfirmationEmail.new(user: record).call
  end

end
