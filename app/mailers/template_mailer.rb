require 'open-uri'

class TemplateMailer < ActionMailer::Base
	helper EmailsHelper

	layout 'email'

	def send_email(template_name:, email_variables:, recipient:, cc:, sender:, subject:, email_attachments:)
		email_attachments.each do |filename, file|
			attachments[filename] = file
		end
  	cc = nil if Rails.env.staging?
  	@header_color = Email::HEADER_COLOR_MAP[email_variables[:header_color]]

  	view_template = Email.view_template(template_name)
  	@account_managers = email_variables[:account_managers]
  	@email_subject = subject

		mail(to: recipient, cc: cc, from: sender, subject: subject) do |format|
			format.html do
				render view_template, locals: { **email_variables }
			end
		end
	end

end
