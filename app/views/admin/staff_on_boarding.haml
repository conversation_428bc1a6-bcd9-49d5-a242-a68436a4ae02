:ruby
  on_borading_config = {
    employeeHandbook: yordar_credentials(:yordar, :employee_handbook_url),
    healthAndSafety: yordar_credentials(:yordar, :heath_and_safety_handbook_url),
    googleApiKey: yordar_credentials(:google, :api_key),
    countryCode: request_country_code,
    cloudinary: {
      uploadUrl: Cloudinary.config.upload_url,
      uploadPreset: Cloudinary.config.upload_preset
    }
  }

%div{ data: { view_staff_on_boarding: on_borading_config.to_json } }