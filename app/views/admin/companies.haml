:ruby
  has_favourite_customers = current_user&.favourite_customers&.present?
  has_favourite_companies = has_favourite_customers && current_user&.favourite_customers.any?{|customer| customer.customer_profile.company.present? }

- content_for :header_title, 'Companies'

%div{ data: { view_company_admin: { canManageCompanies: is_yordar_admin?, hasFavourites: has_favourite_companies, companyName: params[:company_name] }.to_json } }