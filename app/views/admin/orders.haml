:ruby
  has_favourite_customers = current_user&.favourite_customers&.present?

- content_for :header_title, params[:custom_orders_only].present? ? 'Custom Orders' : 'Customer Orders'

- content_for :header_elements do
  - if is_admin? && params[:custom_orders_only].present?
    %a.ml-1-2{ href: rails_admin.index_path(model_name: 'custom_order'), style: 'text-decoration: underline;' }
      View in Old Admin →

%div{ data: { view_order_admin: { hasFavourites: has_favourite_customers, customOrdersOnly: params[:custom_orders_only].present? }.to_json } }