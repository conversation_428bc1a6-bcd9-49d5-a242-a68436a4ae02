:ruby
  supplier = session_profile

- content_for :header_title, 'Account'
- content_for :webpack, 'supplier'

%div{ data: { view_supplier_account_settings: true } }
  %fieldset.supplier-profile-fieldset
    = form_for supplier, method: 'PUT', url: supplier_profile_update_path(supplier), html: { class: 'update_supplier_profile' } do |f|

      = render 'supplier_profiles/company_settings', supplier: supplier, f: f
      = render 'supplier_profiles/supplier_agreement', supplier: supplier
      = render 'supplier_profiles/account_settings', supplier: supplier, f: f      

  .form-footer.light-gray-bg.clearfix.medium-text-right
    %a.button.small.cancel{ href: supplier_profile_path } Cancel
    .supplier-account-submit-btn.button.small{ data: { next_url: supplier_account_path } } Save
