- content_for :header_title, 'Orders'
- content_for :webpack, 'supplier'

%div{ data: { view_supplier_orders: true, view_supplier_order: true, view_order_slideout: true } }
  = render 'supplier_profiles/orders', orders: @orders, list_type: @lister_options[:list_type]

#modal-order-show.reveal.modal.modal-drawer{ data: { reveal: '' } }

= render 'layouts/popup', container_id: 'confirm-order-rejection'
