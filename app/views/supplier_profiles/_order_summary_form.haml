:ruby  
  datepicker_config = {
    options: {
      dateFormat: 'dd-mm-yy',
      firstDay: 1,
    },
  }

%span{ data: { view_supplier_order_summary: true } } 
  %a.summary-document-handle
    Get Summary Documents

#modal-summary.reveal.modal{ data: { reveal: '' } }
  .form-header
    %h2.modal-title.text-center Supplier Summary
  .form-content.has-small-gutter
    .small-12.columns
      %label Summary Date
      = text_field_tag :start_date, Time.zone.now.to_s(:date) , class: 'datepicker summary-date form-input', data: { view_datepicker_input: datepicker_config.to_json }
      %small.form-error.summary-errors
        This is an error

    .small-12.columns.text-center.document-handler.hidden
      %a.button.document-link{ href: 'javascript:void(0)', target: '_blank' }
        View Document
      %br
      %small
        file: 
        %span.filename abc
        %br
        %span.version vNNN

  .form-footer.light-gray-bgx.medium-text-right
    %button.yordar-cancel.button.small.gray-btn{ aria: { label: 'Close modal' }, data: { close: '' } } CLOSE
    %button.generate-summary.button.small{ type: 'submit', data: { url: api_supplier_fetch_order_summary_path(session_profile), regenerate: false } }
      Generate
