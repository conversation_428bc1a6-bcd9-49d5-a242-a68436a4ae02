:ruby
  suburb_label = supplier.company_address_suburb.present? ? supplier.company_address_suburb.label : ''
  autocomplete_config = {
    path: api_suburbs_path,
    target_name: 'supplier_profile[company_address_suburb_id]',
    countryCode: request_country_code
  }

%fieldset.supplier-profile-fieldset
  = f.fields_for :user do |u|
    .dashboard-container.dashboard-container__form
      %p.dashboard-container__title Account Details
      .row
        .small-12.medium-6.columns
          %label First Name
          = u.text_field :firstname, class: 'form-input'
        .small-12.medium-6.columns
          %label Last Name
          = u.text_field :lastname, class: 'form-input'
      .row
        .small-12.medium-6.columns
          %label
            Contact Email
            %small (used for login)
          = u.email_field :email, class: 'form-input'
          - if u.object.unconfirmed_email.present?
            %span.checkout-hint
              Note: Email change to 
              %u= u.object.unconfirmed_email
              needs confirmation. Please check your email.
        .small-12.medium-6.columns
          %label
            Orders Email
            %span.whats-this{data: {tooltip: true, 'tooltip-class': 'whats-this__tooltip'}, class: "bottom tooltip-dash", tabindex: "2", title: "To add multiple emails, separate them with a semi-colon. Example <EMAIL>; <EMAIL>"} Add Multiple Emails
          = f.text_area :email, type: 'email', rows: 2, class: 'form-input'
      .row
        .small-12.medium-6.columns
          %label Contact No.
          = f.text_field :phone, class: 'form-input'
        .small-12.medium-6.columns
          %label Mobile
          = f.text_field :mobile, class: 'form-input'
      .row
        .small-12.medium-6.columns
          %label Street Address
          = f.text_field :company_address, class: 'form-input'
        .small-12.medium-6.columns
          %label Postcode
          = text_field_tag :suburb, suburb_label, { placeholder: 'Enter your delivery postcode', class: 'form-input', autocomplete: 'off', data: { view_autocomplete_input: autocomplete_config.to_json } }
          = f.hidden_field :company_address_suburb_id, value: supplier.company_address_suburb_id
      .row
        .small-12.columns
          = link_to 'Change Password', change_password_path, class: "button outline"
