:ruby
  supplier = session_profile

  supplier_hash = {
    id: supplier.id,
    name: supplier.name,
    slug: supplier.slug,
    is_searchable: supplier.is_searchable,
    menu_reminder_frequency: supplier.menu_reminder_frequency,
    menu_last_updated_on: supplier.menu_last_updated_on,
    is_woolworths: supplier.woolworths?,
    has_skus: supplier.has_skus,
    image: supplier.profile.avatar,
    team_supplier: supplier.team_supplier?,
    preview_url: next_app_supplier_show_url(supplier.slug),
    team_preview_url: supplier.team_supplier? ? next_app_supplier_show_url(supplier.slug, team_order_menu: true) : nil
  }

  if is_admin?
    restricted_customers = supplier.customer_profiles
    supplier_hash[:restricted_customers] = restricted_customers.present? ? restricted_customers.map(&:name).sort : []
    supplier_hash[:markup] = number_to_percentage(supplier.markup, precision: 1)
    supplier_hash[:commission] = number_to_percentage(supplier.commission_rate, precision: 1)
  end

  image_urls = {
    upload: Cloudinary.config.upload_url,
    upload_preset: Cloudinary.config.upload_preset,
  }

  categories = Category.where(show_in_menu: true, group: %w[catering-services kitchen-supplies]).order(:name)
  section_categories = categories.group_by(&:group).map do |group, categories|
    {
      group: group,
      categories: categories.map do |category|
        {
          id: category.id,
          name: category.name,
        }
      end
    }.merge(category_group_info(group))
  end

  menu_item_preferences = {
    dietary: menu_item_dietary_options.map{|field, label| { field: field, label: label} },
    misc: menu_item_misc_options.map{|field, label| { field: field, label: label} },
  }

- content_for :header_title, 'Menu'
- content_for :webpack, 'supplier'

%div{ data: { view_supplier_menu: { imageUrls: image_urls, supplier: supplier_hash, sectionCategories: section_categories, itemPreferences: menu_item_preferences, canManageMenu: (is_admin? || supplier.can_manage_menu_dashboard), isAdmin: is_admin?, showArchived: is_admin? && params[:show_archived].present?, countryCode: request_country_code }.to_json }  }
