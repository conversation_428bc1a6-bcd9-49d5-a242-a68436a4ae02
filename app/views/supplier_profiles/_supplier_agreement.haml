:ruby
  supplier ||= session_profile
  lister_options = {
    latest: true,
  }
  document_lister = Suppliers::ListAgreementDocuments.new(supplier: supplier, options: lister_options).call
  current_agreement = document_lister.last
  can_manage_agreements = current_user.present? && (current_user.admin? || current_user.allow_all_supplier_access?)

  if !can_manage_agreements
    return
  end

.dashboard-container.dashboard-container__form
  %p.dashboard-container__title
    Supplier Agreement
    - if can_manage_agreements
      %a.ml-2.button.button-tiny.new-supplier_agreement{ href: send_supplier_agreement_path }
        Send New Agreement

  - if current_agreement.present?
    .row
      .small-12.medium-7.columns.supplier-profile-fields
        %label Document
        %ul.no-bullet.status-changes
          - current_agreement.status_changes.each do |status_change|
            %li
              = status_change['new_status']
              \:
              = status_change['changed_at'].in_time_zone.to_s(:full)
      .small-12.medium-5.columns.supplier-profile-agreement
        %label Download
        = link_to 'Download PDF', fetch_supplier_agreement_path, class: 'icon-pdf supplier-profile-agreement-link'
  - else
    .row
      .small-12.columns
        %h5 No supplier agreement yet.


