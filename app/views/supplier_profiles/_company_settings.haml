.dashboard-container.dashboard-container__form
  %p.dashboard-container__title Company Details
  .row
    .medium-7.columns.supplier-profile-fields
      .row
        .small-12.medium-6.columns
          %label Company Name
          = f.text_field :company_name, class: 'form-input'
        .small-12.medium-6.columns
          %label ABN / ACN
          = f.text_field :abn_acn, class: 'form-input'
      .row
        .small-6.columns
          %label Liquor License No.
          = f.text_field :liquor_license_no, class: 'form-input'
      .row
        .small-12.medium-6.columns
          %label BSB Number
          = f.text_field :bsb_number, class: 'form-input'
        .small-12.medium-6.columns
          %label Bank Account Number
          = f.text_field :bank_account_number, class: 'form-input'
      .row
        .small-12.columns
          %label Description
          = f.text_area :description, rows: '4', class: 'form-input'
          
    .medium-5.columns.supplier-profile-image
      %div
        %p Display Image
        %p.help-text Upload an image that is  at least 230px wide
        %div.supplier-avatar-container
          - if supplier.profile.avatar.present?
            = cl_image_tag(supplier.profile.avatar)

      - if is_admin?
        = f.fields_for :profile do |p|
          %label.button.small.avatar-upload-button{ for: 'imageUpload', data: { button_text: 'Upload File' } }
            Upload File
          = file_field_tag :avatar_uploader, id: 'imageUpload', class: 'show-for-sr', data: { upload_url: Cloudinary.config.upload_url, upload_preset: Cloudinary.config.upload_preset, target_name: 'supplier_profile[profile_attributes][avatar]' }
          = p.hidden_field :avatar
