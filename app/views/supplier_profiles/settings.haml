:ruby
  supplier = session_profile
  admin_options = is_admin? && %i[
    is_new_expires_at
    is_featured
    admin_only
    can_manage_menu_dashboard
    needs_multi_day_summary
    needs_recurring_reminder
    has_skus
    is_event_caterer
    uses_flex_catering
    billing_frequency
    payment_term_days
    menu_reminder_frequency
    menu_last_updated_on
  ]
  supplier_delivery_options = %i[
    needs_swipe_card_access
    supplies_in_working_hours
    provides_contactless_delivery
    provides_multi_service_point
  ]
  supplier_taggable_options = SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS.dup

  tag_object = supplier.supplier_flags
  update_url = api_supplier_path(supplier)
  translation_key = 'supplier_profiles.tags'

- content_for :header_title, 'Tag Settings'
- content_for :container_class, 'billing-form'
- content_for :webpack, 'supplier'

.customer-area-container{ data: { view_user_flags: true } }
  .dashboard-container
    .notification-pref
      %ul.list-unstyled
        - if admin_options.present?
          %li
            %p.notification-pref__title
              Admin Option
              - admin_options.each do |field|
                %li.row.notification-pref__row
                  = render 'partials/tag_form', object: tag_object, field: field, update_url: update_url, translation_key: translation_key

        %li
          %p.notification-pref__title
            Delivery Option
            - supplier_delivery_options.each do |field|
              %li.row.notification-pref__row
                = render 'partials/tag_form', object: tag_object, field: field, update_url: update_url, translation_key: translation_key

        %li
          %p.notification-pref__title
            Tagged as
            - supplier_taggable_options.each do |field|
              %li.row.notification-pref__row
                = render 'partials/tag_form', object: tag_object, field: field, update_url: update_url, translation_key: translation_key


