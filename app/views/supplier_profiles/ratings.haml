- content_for :header_title, 'Ratings'

- if @ratings.order_reviews.blank?
  .no-upcoming
    %h2 No New Reviews
    %img{ src: '/assets/illustrations/reporting.svg', width: '400px' }
- else
  .rating-container
    %p.rating-date
      = "#{@ratings.starts_at.to_s(:date_verbose)} - #{@ratings.ends_at.to_s(:date_verbose)}"
    .row
      .small-8.columns.rating-headline.no-gutter
        = @ratings.order_reviews.size
        new reviews this week
      .small-4.columns.rating-headline.no-gutter
        Average this week:
        .star
        = @ratings.average(@ratings.total_score, @ratings.total_count)

    .row
      - @ratings.rating_fields.each do |field|
        .small-12.columns.rating-sub-headline.no-gutter{ class: "medium-#{12/@ratings.rating_fields.size}" }
          = "#{field.humanize.capitalize}:"
          .star
          = @ratings.average(@ratings.scores.send(field), @ratings.counts.send(field))

    %div
      %h3
        Detailed ratings and comments
    - @ratings.order_reviews.each do |order_review|
      = render 'order_reviews/review', order_review: order_review, rating_fields: @ratings.rating_fields
