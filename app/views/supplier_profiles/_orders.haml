:ruby
  filter_days = [
    ['week', '7 days'],
    ['fortnight', '14 days'],
    ['month', '1 month'],
    ['6-months', '6 months'],
    ['year', '1 year'],
    ['all', 'all']
  ].to_h
  selected_day_filter = @lister_options[:for_duration]

  list_url = supplier_profile_path(list_type: list_type)
  case list_type
  when 'pending'
    list_class = 'order-requests'
    title = 'Orders Pending Approval'
    next_link_options = { title: 'Upcoming Orders', path: supplier_profile_path(list_type: 'upcoming') }
  when 'upcoming'
    list_class = 'upcoming-deliveries'
    title = 'Upcoming Deliveries'
    next_link_options = { title: 'Past Orders', path: supplier_profile_path(list_type: 'past') }
  when 'past'
    list_class = 'upcoming-deliveries'
    title = 'Past Orders'
  end


.order-list{ class: list_class, data: { url: list_url } }
  - if @orders.blank?
    .no-upcoming
      %h3
        No
        = title
        - if selected_day_filter != 'all'
          in the
          = list_type == 'past' ? 'previous' : 'next'
          %select.filter-by-order-days.form-input{ style: 'width: 150px;' }
            - filter_days.each do |key, label|
              %option{ value: key, selected: selected_day_filter == key }
                = label

      %img{src: '/assets/illustrations/bill.svg', width: '400px'}
      - if next_link_options.present?
        = link_to "View #{next_link_options[:title]}", next_link_options[:path], class: 'button'
  - else
    .supplier-table
      .supplier-table__header.row
        .small-12.medium-6.columns.no-gutter
          %h6.supplier-table__header--heading
            = title
            %span.orders-count= "(#{orders.total_count})"

        .small-12.medium-3.columns.no-gutter
          .supplier-table__header--heading
            = render 'supplier_profiles/order_summary_form'

        .small-12.medium-3.columns.no-gutter
          .small-12.day-filter.float-right
            %select.filter-by-order-days.form-input
              - filter_days.each do |key, label|
                %option{ value: key, selected: selected_day_filter == key }
                  = "Show #{label}"

      .row.supplier-table-list__headings
        .supplier-table-list__heading.small-1.columns.no-gutter Order
        .supplier-table-list__heading.small-2.columns.order-company Company
        .supplier-table-list__heading.small-3.columns Date
        .supplier-table-list__heading.small-3.columns.order-address Address
        - if list_type == 'pending'
          .supplier-table-list__heading.small-2.columns
        - else
          .supplier-table-list__heading.small-1.columns
          .supplier-table-list__heading.small-2.columns Status

      .row.supplier-table-list
        - orders.each do |order|
          = render 'supplier_profiles/order', order: order, list_type: list_type

    - if orders.respond_to?(:total_pages)
      = paginate orders
