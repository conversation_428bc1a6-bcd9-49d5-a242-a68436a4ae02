:ruby
  today = Time.zone.today
  order_delivery_at = order.delivery_at
  formatted_delivery_date = order_delivery_at.strftime("%A #{order_delivery_at.day.ordinalize} %B")
  formatted_delivery_date += order_delivery_at.strftime(' %Y') if today.year != order_delivery_at.year
  formatted_delivery_date += " at #{order.delivery_at.to_s(:time_only)}"
  company_name = order.customer_profile.billing_details.present? ? order.customer_profile.billing_details.name : order.customer_profile.company_name
  readable_company_name = company_name.gsub(/ pty ltd/i, '').truncate(30)
%li.order-item.small-12.columns.no-gutter{ data: { order_id: order.id } }
  %ul.row.supplier-table-list__row
    %li.small-12.medium-1.columns.no-gutter
      = link_to "##{order.id}", supplier_order_show_path(order), data: { modal_view: 'true' }, class: "supplier-table-list__order"
    %li.small-12.medium-2.columns.no-gutter-small.order-company
      %p= readable_company_name
    %li.small-12.order-date.medium-3.columns.no-gutter-small
      - if order_delivery_at.present?
        - if today.cweek == order_delivery_at.to_date.cweek && today.year == order_delivery_at.year && order_delivery_at > Time.zone.now.beginning_of_day
          %span{ title:  formatted_delivery_date, data: { view_tooltip_el: true } }
            = order_delivery_at.strftime('This %A at %I:%M%P')
        - else
          = formatted_delivery_date
    %li.small-12.order-address.medium-3.columns.no-gutter-small
      - order.delivery_address_arr.each do |add|
        %p= add
    - if list_type == 'rejected'
      %li.small-12.medium-1.columns.no-gutter-small
      %li.small-12.supplier-table-list__status.medium-2.columns.no-gutter-small{ class: 'rejected' }
        %span
          Rejected
    - elsif list_type == 'pending' && order.status != 'pending'
      %li.small-12.medium-3.columns.no-gutter.supplier-order-btns
        .row
          .small-12.columns.no-gutter
            %button.button.supplier-order-btn.order-confirm-btn{ data: { url: api_order_confirm_order_path(order), order_id: order.id } }
              Confirm
            %button.button.supplier-order-btn.border-red.order-reject-btn{ data: { url: api_order_reject_order_path(order), order_id: order.id } }
              Reject
    - else
      %li.small-12.medium-1.columns.no-gutter-small
      %li.small-12.supplier-table-list__status.medium-2.columns.no-gutter-small{ class: order.status }
        - if order.status == 'delivered'
          %span Delivered
        - elsif order.status == 'pending'
          %span{ title: 'The customer is still building the order', data: { view_tooltip_el: true } }
            In Progress
            %span.icon-info-circle
        - else
          %span Confirmed
