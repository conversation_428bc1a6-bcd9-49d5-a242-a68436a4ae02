:javascript
  var SIGNED_IN_AS_ADMIN = "#{session[:sign_in_as_admin].present?}"
!!! Strict
%html.no-js{ lang: 'en' }
  %head
    %meta{ charset: 'utf-8'}/
    %meta{ content: 'ie=edge', 'http-equiv' => 'x-ua-compatible' }/
    %meta{ content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no', name: 'viewport' }/
    %meta{ content: yield(:keywords), name: 'keywords' }/
    %meta{ content: yield(:title), name: 'title' }/
    %meta{ content: yield(:description), name: 'description' }/
    = stylesheet_link_tag 'application', media: 'all'
    = csrf_meta_tags
    = favicon_link_tag asset_path('favicon/favicon.ico')
    - if Rails.env.production?
      = render 'layouts/logrocket'
      = render 'layouts/google_tag_manager'
  %body
    = render 'layouts/flash_messages'

    = render 'layouts/admin_banner'

    = render 'layouts/popup'

    %main.team-order-package-page
      = yield

    .overlay.hidden

    = javascript_include_tag 'https://js.stripe.com/v3/'
    - if content_for?(:webpack)
      = javascript_pack_tag yield(:webpack)
    - else
      = javascript_include_tag 'team_order'
    = javascript_include_tag "https://maps.googleapis.com/maps/api/js?key=#{yordar_credentials(:google, :api_key)}&libraries=places"
    = yield :javascript_includes
    - if Rails.env.production?
      = render 'layouts/hubspot'
