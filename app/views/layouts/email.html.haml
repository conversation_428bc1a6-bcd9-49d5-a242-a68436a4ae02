!!!
%html.no-js{lang: 'en'}
  %head
    %meta{content: 'text/html; charset=UTF-8', 'http-equiv' => 'Content-Type'}/
    %link{href: 'https://fonts.googleapis.com', rel: 'preconnect'}/
    %link{crossorigin: '', href: 'https://fonts.gstatic.com', rel: 'preconnect'}/
    %link{href: 'https://fonts.googleapis.com/css2?family=Mukta:wght@200;300;400;500;600;700;800&display=swap', rel: 'stylesheet'}
    :css
      * {
          font-family: #{Email::FONT_FAMILY};
          font-size: #{Email::FONT_SIZE};
          color: #{Email::FONT_COLOR}
        }
  %body

    %table{border: '0', cellpadding: '0', cellspacing: '0', width: '100%'}
      %tbody
        %tr
          %td{bgcolor: '#fdfdfd'}
            %table{ align: 'center', border: '0', cellpadding: '0', cellspacing: '0', style: 'width: 100%;max-width: 600px;', width: '100%' }
              %tbody
                %tr
                  %td
                    = render 'layouts/emails/spacer', height: 30

                    // header
                    %header
                      = render 'layouts/emails/header_logo'

                    = render 'layouts/emails/spacer', height: 8

                    = render 'layouts/emails/content_border'

                    %table{border: '0', cellpadding: '0', cellspacing: '0', style: 'table-layout: fixed', width: '100%'}
                      %tbody
                        %tr
                          %td{ bgcolor: '#ffffff', height: '100%', style: 'padding: 24px 24px 24px 24px; line-height: 22px; text-align: inherit; background-color: #ffffff; border: 1px solid #ededed; border-top: none; border-bottom-left-radius: 8px;border-bottom-right-radius: 8px;', valign: 'top' }

                            = yield

                    = render 'layouts/emails/spacer', height: @account_managers.present? ? 10 : 50

                    - if @account_managers.present?
                      - @account_managers.each do |account_manager|
                        = render 'layouts/emails/signature', account_manager: account_manager

                    = render 'layouts/emails/spacer', height: 1, color: Email::HR_COLOR

                    = render 'layouts/emails/footer_logo'
                    = render 'layouts/emails/footer_info'

                    = render 'layouts/emails/spacer', height: 60
                  
