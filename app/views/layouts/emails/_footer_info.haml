:ruby
  phone ||= yordar_credentials(:yordar, :office, :phone)

%table{border: '0', cellpadding: '0', cellspacing: '0', style: 'table-layout: fixed;', width: '100%'}
  %tbody
    %tr
      %td{height: '100%', valign: 'top'}
        %div{style: 'line-height: 8px;'}

          %div{style: 'font-family: inherit;text-align: inherit;'}
            %br

          %div{style: 'font-family: inherit;text-align: center;'}
            %span{style: 'font-family: inherit;font-size: 10px;color: #a9a9a9;'} &#xA9; Yordar

          %div{style: 'font-family: inherit;text-align: center;'}
            %br

          %div{style: 'font-family: inherit;text-align: center;'}
            %span{style: 'font-family: inherit;font-size: 10px;color: #a9a9a9;'}
              Phone:
              %a{href: "tel:#{phone.gsub(/\s/, '')}", style: 'font-family: inherit;font-size: 10px;color: #a9a9a9;'}
                = phone
                    
          %div{style: 'font-family: inherit;text-align: center;'}
            %br
            
          %div{style: 'font-family: inherit;text-align: center;'}
            %span{style: 'font-family: inherit;font-size: 10px;color: #a9a9a9;'}
              Email:
              %a{ href: "mailto:#{orders_email}", style: 'font-family: inherit;font-size: 10px;color: #a9a9a9;'}
                = orders_email

          %div{style: 'font-family: inherit;text-align: center;'}
            %br
            
          %div{style: 'font-family: inherit;text-align: center;'}
            %span{style: 'font-family: inherit;font-size: 10px;color: #a9a9a9;'}
              Suite 1B, Level 16, 56 Pitt Street, Sydney NSW 2000


                
