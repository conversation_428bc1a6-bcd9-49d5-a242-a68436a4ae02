- no_script ||= false
- if no_script
  / Google Tag Manager (noscript)
  %noscript
    %iframe{ height: '0', src: 'https://www.googletagmanager.com/ns.html?id=GTM-57BH9Z5', style: 'display:none;visibility:hidden', width: '0' }
  / End Google Tag Manager (noscript)
- else
  / Google Tag Manager
  :javascript
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-57BH9Z5');
  / End Google Tag Manager
