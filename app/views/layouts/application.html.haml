!!!
%html.no-js{lang: 'en'}
  %head
    %meta{ content: 'text/html; charset=UTF-8', 'http-equiv' => 'Content-Type' }/
    %meta{ charset: 'utf-8' }/
    %meta{ content: 'ie=edge', 'http-equiv' => 'x-ua-compatible' }/
    %meta{ content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no', name: 'viewport' }/
    %meta{ content: yield(:keywords), name: 'keywords' }/
    %meta{ content: yield(:title), name: 'title' }/
    %meta{ content: yield(:description), name: 'description' }/
    - if content_for?(:no_index)
      %meta{name: 'robots', content: 'noindex'}

    - if content_for?(:canonical_url)
      %link{ href: yield(:canonical_url), rel: 'canonical' }/

    %title
      = yield(:title)
      | YORDAR

    = stylesheet_link_tag 'application', media: 'all'

    = csrf_meta_tags
    = favicon_link_tag asset_path('favicon/favicon.ico')

    - if @suppliers
      = render 'shared/ratings', suppliers: @suppliers
    - elsif @supplier
      = render 'shared/ratings', suppliers: [@supplier]

    - if Rails.env.production?
      = render 'layouts/logrocket'
      = render 'layouts/google_tag_manager'

  %body{ class: yield(:body_clazz) }

    = render 'layouts/google_tag_manager', no_script: true

    = render 'layouts/flash_messages'

    / HEADER
    - if @supplier_order.present? && @supplier_order.order.present? && @supplier_order.order.is_team_order?
      = render 'layouts/header/team_order_header'
    - else
      = render 'layouts/header/normal_header'
    / END HEADER

    = render 'layouts/popup'

    - if content_for?(:webpack)
      #stripe-container{ data: { publishable_key: yordar_credentials(:stripe, :publishable_key) }}

    / MAIN
    %main{ class: yield(:main_clazz) }
      = yield

    / FOOTER
    - if request.original_url.exclude?('s_profile')
      = render 'layouts/footer'

    .overlay.hidden

    - if content_for?(:webpack)
      = javascript_pack_tag yield(:webpack)
    - else
      = javascript_include_tag 'application'
    = javascript_include_tag "https://maps.googleapis.com/maps/api/js?key=#{yordar_credentials(:google, :api_key)}&libraries=places"
    = javascript_include_tag 'https://js.stripe.com/v3/'
    = yield :javascript_includes
    - if Rails.env.production?
      = render 'layouts/hubspot'

