:ruby
  customer ||= session_profile
  user = customer.user
  title = content_for?(:header_title) ? yield(:header_title) : ''
  different_domain_country = request_country_code&.downcase != session_profile.country_of_origin&.downcase
  is_masquerading = can_access_admin_portal?

  order = session_order
  ol_count = order.present? ? order.order_lines.count : 0
  is_order_edit = order.present? && order.status != 'draft'
  button_link = is_order_edit ? next_app_order_edit_url(order) : order_checkout_url

.customer-header.dash
  .customer-header__info
    .between-flex
      - if content_for(:back_button)
        = yield(:back_button)

      - if title
        %h3.customer-header__title
          = title
    - if content_for(:header_elements)
      = yield(:header_elements)

  - if different_domain_country
    %span.error-msg
      You are registered in
      %strong= session_profile.country_of_origin&.upcase
      and seem to be on the wrong country domain (#{request_country_code.upcase}).

  
  #dash-hamburger.hamburger{ data: { view_hamburger: { kind: 'dash' }.to_json } }
    %span
    %span
    %span
    %span
    %span
    %span

  .cart-container
    %a.dash-cart{ href: button_link }
      %span.dash-cart-count{ class: ol_count == 0 ? 'hidden' : '' }
        = ol_count
      %a.clear-cart{ href: clear_cart_path, class: (order.blank? ? 'hidden' : ''), title: 'clear cart', data: { is_order_edit: is_order_edit.to_s } }
        x

  .auth-dropdown
    %ul.dropdown.menu.no-reset.no-float{ data: { dropdown_menu: '', alignment: 'right' } }
      %li.is-dropdown-submenu-parentxx.no-reset
        %a.personalised__name.personalised__name--header.personalised__name--header--dash{ href: 'javascript:void(0)', class: is_masquerading ? 'masqueraded' : '', style: 'display: flex; align-items: center; padding: 0; margin: 0 1rem;' }
          %span.circle-icon.dash{ style: 'background-color: #dc2454; color: white; height: 32px; width: 32px;' }
            = "#{user.firstname[0]}#{user.lastname[0]}"
          %span.dash-name
            = customer.name
        %ul.menu.customer-header__dropdown.no-reset
          - if is_masquerading
            %li= link_to 'Admin Dash', sign_out_as_path
          %li= link_to 'Account Details', customer_account_and_billing_path
          %li= link_to 'Logout', destroy_user_session_path, method: :delete
