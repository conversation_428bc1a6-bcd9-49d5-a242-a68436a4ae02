%script#hs-script-loader{ async: '', defer: 'defer', src: '//js.hs-scripts.com/21281170.js', type: 'text/javascript' }
:javascript
  window.hsConversationsSettings = {
    loadImmediately: false,
  }
  async function fetchHubSpotToken() {
    const response = await fetch('/api/hubspot.json');
    const data = await response.json();
    return data;
  }

  if (typeof window !== 'undefined') {
    async function onConversationsAPIReady() {
      const hubSpotAuthInfo = await fetchHubSpotToken();
      window.hsConversationsSettings = {
        ...(hubSpotAuthInfo.email && { identificationEmail: hubSpotAuthInfo.email }),
        ...(hubSpotAuthInfo.token && { identificationToken: hubSpotAuthInfo.token }),
      };
      window.HubSpotConversations.widget.load();
    }
    if (window.HubSpotConversations) {
      onConversationsAPIReady();
    } else {
      window.hsConversationsOnReady = [onConversationsAPIReady];
    }
  }