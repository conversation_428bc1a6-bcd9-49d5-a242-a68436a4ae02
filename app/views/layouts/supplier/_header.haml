:ruby
  title = content_for?(:header_title) ? yield(:header_title) : ''
  different_domain_country = request_country_code&.downcase != session_profile.country_of_origin&.downcase

.between-flex.mb-1.mx-1
  %h3.customer-header__title
    = title
    
  - if different_domain_country
    %span.error-msg
      You are registered in
      %strong= session_profile.country_of_origin&.upcase
      and seem to be on the wrong country domain (#{request_country_code.upcase}).
  .between-flex
    - if !is_admin?
      .personalised__tag.circle-icon.supplier-icon-header
        - if session_profile.profile.avatar
          = resize_image_tag(session_profile.profile.avatar, "Supplier Avatar", 200, 200, 'fill')
        - else
          = supplier_name_helper(:initials, session_profile.company_name)
    #dash-hamburger.hamburger{ data: { view_hamburger: { kind: 'dash' }.to_json } }
      %span
      %span
      %span
      %span
      %span
      %span
    - if is_admin? 
      %div
      .no-reset
        %ul.dropdown.menu.no-reset{ data: { dropdown_menu: '', alignment: 'right' } }
          %li.is-dropdown-submenu-parent.no-reset
            %a.personalised__name.personalised__name--header{ href: 'javascript:void(0)', class: is_admin? ? 'masquerade' : ''  }
              = supplier_name_helper(:full)
            %ul.menu.customer-header__dropdown.no-reset
              %li= link_to 'Admin Dash', sign_out_as_path
              %li= link_to 'Logout', destroy_user_session_path, method: :delete
    - else
      %span.supplier-name-dash= supplier_name_helper(:full)
      = link_to "Logout", destroy_user_session_path, method: :delete, class: "supplier-logout"
    