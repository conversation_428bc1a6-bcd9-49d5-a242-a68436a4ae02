:ruby
  supplier_order_paths = [supplier_profile_path]
  if @order.present?
    supplier_order_paths << supplier_order_show_path(@order)
  end
  settings_paths = [supplier_account_path, supplier_settings_path, supplier_notification_preferences_path]
.customer-sticky-sidebar{ data: { view_sidebar_nav: true } }
  %aside.customer-area-sidebar
    %div
      .customer-area-sidebar__image
        = link_to prismic_root_url do
          = image_tag 'logo.svg'
          - if Rails.env.staging?
            %span STAGING
      %p.customer-area-sidebar__title Supplier Account
      %ul.customer-area-sidebar__ul.vertical
        %li.customer-area-sidebar__li.nested-list{ class: is_active_path(supplier_profile_path) }
          = link_to 'Orders', supplier_profile_path, class: 'customer-sidebar-link'
          %ul.nested.vertical.menu{ class: should_be_hidden(*supplier_order_paths) }
            %li{ class: (params[:list_type].blank? || params[:list_type] == 'pending' ? is_active_path(supplier_profile_path) : '') }
              = link_to 'Pending Orders', supplier_profile_path(list_type: 'pending')
            %li{ class: (params[:list_type].present? && params[:list_type] == 'upcoming' ? is_active_path(supplier_profile_path) : '') }
              = link_to 'Upcoming Orders', supplier_profile_path(list_type: 'upcoming')
            %li{ class: (params[:list_type].present? && params[:list_type] == 'past' ? is_active_path(supplier_profile_path) : '') }
              = link_to 'Past Orders', supplier_profile_path(list_type: 'past')

        %li.customer-area-sidebar__li{ class: is_active_path(supplier_menu_path) }
          = link_to 'Menu', supplier_menu_path

        %li.customer-area-sidebar__li{ class: is_active_path(supplier_minimums_path) }
          = link_to 'Categories', supplier_minimums_path

        %li.customer-area-sidebar__li{ class: is_active_path(supplier_delivery_zones_path) }
          = link_to 'Delivery Zones', supplier_delivery_zones_path

        %li.customer-area-sidebar__li{ class: is_active_path(supplier_closure_dates_path) }
          = link_to 'Closure Dates', supplier_closure_dates_path

        %li.customer-area-sidebar__li{ class: is_active_path(supplier_ratings_path) }
          = link_to 'Reviews', supplier_ratings_path

        - if session_profile.billing_frequency != 'do_not_generate'
          %li.customer-area-sidebar__li{ class: is_active_path(supplier_invoices_path) }
            = link_to 'Payments', supplier_invoices_path

        %li.customer-area-sidebar__li{ class: is_active_path(supplier_reports_path) }
          = link_to 'Sales Reports', supplier_reports_path

        %li.customer-area-sidebar__li.nested-list{ class: is_active_path(*settings_paths) }
          = link_to 'Settings', supplier_account_path, class: 'customer-sidebar-link customer-sidebar-link__nested'
          %ul.nested.vertical.menu{ class: should_be_hidden(*settings_paths) }
            %li{ class: is_active_path(supplier_account_path) }
              = link_to 'Account & Billing', supplier_account_path
            %li{ class: is_active_path(supplier_settings_path) }
              = link_to 'Tags', supplier_settings_path
            %li{ class: is_active_path(supplier_notification_preferences_path) }
              = link_to 'Notification Preferences', supplier_notification_preferences_path
    %div
      .personalised
        .personalised__tag.circle-icon
          - if session_profile.profile.avatar
            = resize_image_tag(session_profile.profile.avatar, 'Supplier Avatar', 200, 200, 'fill')
          - else
            = supplier_name_helper(:initials, session_profile.company_name)
        %div
          %span.personalised__name.sidebar= supplier_name_helper(:full)
