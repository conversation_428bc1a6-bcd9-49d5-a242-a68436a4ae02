%li.parent
  %span.drop-title Our Services
  .submenu
    %span Catering Services
    %ul.mobile-list
      - catering_options.each do |option|
        %li
          = link_to option[:name], prismic_page_url(page: option[:slug]), class: option[:class]

    %span Snacks & Pantry Services
    %ul.mobile-list
      - kitchen_options.each do |option|
        %li
          = link_to option[:name], prismic_page_url(page: option[:slug]), class: option[:class]

%li.parent.mobile-only-links
  %a.button.black-btn.outline-button.full-width{ href: next_app_customer_quotes_url, style: 'width: 100%;' }
    Get A Quote

%li.parent.mobile-only-links
  %span.drop-title Your Account
  .submenu
    %ul.mobile-list
      - auth_options.each do |option|
        %li
          %a.user-icon{ href: option[:path] } #{option[:to]}
      %li
        %a.shopcart-icon{ href: order_checkout_url } Cart
