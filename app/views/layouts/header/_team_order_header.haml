:ruby
  ol_count = 0
  if session_order.present?
    order_total = session_order.customer_total || 0.00
    ol_count = session_order.order_lines.count
  end
  team_order = @supplier_order.present? && @supplier_order.order
  attendee = @supplier_order.present? && @supplier_order.team_order_attendee

%header.header
  .header-wrap{style: 'max-width: inherit'}
    = link_to 'Yordar', prismic_root_url, class: 'logo team-menu'
    .attendee-info
      - if attendee.present?
        %p{style: 'white-space: nowrap;'}
          Hi, #{attendee.first_name.try(:capitalize)}!

      - if team_order.present? && team_order.is_package_order? && attendee.present? && !attendee.is_team_admin?
        = link_to 'Back', team_order_attendee_package_order_path(code: attendee.uniq_code)
      - else
        %a#team-checkout-btn.shopcart-icon.team-order-cart.cart-checkout-btn.no-reset
          %span.cart Cart
          %span.shopcart-item-counter.no-reset{ class: (ol_count == 0 ? 'hidden' : '') } 0

:javascript
  var ON_MENU = 'false';
  var ALLOW_CHECKOUT = "#{session_order.try(:order_lines).present?}";
