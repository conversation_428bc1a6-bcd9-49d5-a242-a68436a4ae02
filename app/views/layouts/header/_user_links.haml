.secondary-links
  %ul.dropdown.menu{ data: { dropdown_menu: '' } }
    %li
      %a.quote-button{ href: next_app_customer_quotes_url }
        Get A Quote

    - if current_user.blank?
      %li
        = link_to 'Login', new_user_session_path
      - if ['/supplier_registration', '/supplier_partnership'].exclude?(request.path)
        %li
          = link_to 'Register', new_user_registration_path

    - else
      %li.is-dropdown-submenu-parent
        = link_to session_profile&.user&.firstname, is_admin? ? admin_path : customer_profile_path, class: 'masquerade'
        %ul.menu
          - auth_options.each do |option|
            %li= link_to option[:to], option[:path]
          %li= link_to 'Logout', destroy_user_session_path, method: :delete
