:ruby
  order = session_order
  order_total = 0.00
  ol_count = 0
  if order.present?
    order_total = order.customer_total || 0.00
    ol_count = order.order_lines.count
  end
  no_wrap = [new_user_registration_path, new_user_session_path, order_step1_path].none?{|path| current_page?(path) }

  is_order_edit = order.present? && order.status != 'draft'
  button_link = is_order_edit ? next_app_order_edit_url(order) : order_checkout_url

%header.header
  .header-wrap{ style: no_wrap ? 'max-width: inherit' : '' }
    = link_to 'Yordar', prismic_root_url, {class: ['logo', Rails.env.staging? && 'logo__staging']}

    %nav#main-navigation{ role: 'navigation' }
      %ul.mobile-dropdown-grid
        = render 'layouts/header/dropdowns'

    .auth-and-cart
      = render 'layouts/header/user_links', order_total: order_total, ol_count: ol_count

      %a.shopcart-icon.cart-checkout-btn{ href: button_link }
        %span Cart
        %span.shopcart-item-counter{ class: ol_count == 0 ? 'hidden' : '' }
          = ol_count

        %a.shopcart-clear{ href: clear_cart_path, class: (order.blank? ? 'hidden' : ''), title: 'clear cart', data: { is_order_edit: is_order_edit.to_s } }
          %sup.cancel-icon
            X

      %div
        / Hamburger
        #menu-toggle.hamburger
          %span
          %span
          %span
          %span
          %span
          %span

:javascript
  var ON_MENU = 'false';
  var ALLOW_CHECKOUT = "#{session_order.try(:order_lines).present?}";
