<script src="https://cdn.lr-ingest.io/LogRocket.min.js" crossorigin="anonymous"></script>
<script>
  window.LogRocket && window.LogRocket.init('ojq7n8/yordar', {
    rootHostname: 'yordar.com.au',
  });
</script>
<% if user_signed_in? %>
  <script>
    current_user_id = "<%= current_user.id %>";
    current_user_email = "<%= current_user.email %>";
    current_user_name = "<%= current_user.name %>";
    window.LogRocket.identify(current_user_id, {
      name: current_user_name,
      email: current_user_email,
    });
  </script>
<% end %>
<% if request.path.include?('admin/') %>
  <script>
    LogRocket.getSessionURL(function(sessionURL) {
      document.cookie = "logRocketSession=" + sessionURL;
    });
  </script>
<% else %>
  <% content_for :javascript_includes do %>
    <script>
      LogRocket.getSessionURL(function(sessionURL) {
        document.cookie = "logRocketSession=" + sessionURL;
      });
    </script>
  <% end %>
<% end %>
