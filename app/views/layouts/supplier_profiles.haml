:javascript
  var SIGNED_IN_AS_ADMIN = "#{session[:sign_in_as_admin].present?}"
!!! Strict
%html.no-js{ lang: 'en' }
  %head
    %meta{ charset: 'utf-8'}/
    %meta{ content: 'ie=edge', 'http-equiv' => 'x-ua-compatible' }/
    %meta{ content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no', name: 'viewport' }/
    %meta{ content: yield(:keywords), name: 'keywords' }/
    %meta{ content: yield(:title), name: 'title' }/
    %meta{ content: yield(:description), name: 'description' }/
    = stylesheet_link_tag 'application', media: 'all'
    = csrf_meta_tags
    = favicon_link_tag asset_path('favicon/favicon.ico')
    - if Rails.env.production?
      = render 'layouts/logrocket'
      = render 'layouts/google_tag_manager'
  %body
    
    = render 'layouts/flash_messages'

    = render 'layouts/popup'

    %main.customer-area.supplier-dashboard-container
      = render 'layouts/supplier/sidebar', supplier: session_profile
      .customer-form{ class: (content_for?(:container_class) ? yield(:container_class) : '') }
        = render 'layouts/supplier/header'

        .customer-area-container
          = yield

        - if Rails.env.production?
          .intercom-separator.light-gray-bg

    .overlay.hidden

    - if content_for?(:webpack)
      = javascript_pack_tag yield(:webpack)
    - else
      = javascript_include_tag 'supplier_profile'
    = javascript_include_tag "https://maps.googleapis.com/maps/api/js?key=#{yordar_credentials(:google, :api_key)}&libraries=places"
    = yield :javascript_includes
    - if Rails.env.production?
      = render 'layouts/hubspot'
