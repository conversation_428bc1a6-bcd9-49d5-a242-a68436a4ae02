:ruby  
  return if current_user.blank?
  return if session_profile.blank?

  current_user_profile = current_user.profile&.profileable
  signed_in_to_customer = session_profile.is_a?(CustomerProfile)

  signed_in_as_admin = current_user.admin?
  signed_in_as_access_admin = current_user.can_access_suppliers?

  signed_in_as_customer = current_user_profile.present? && current_user_profile.is_a?(CustomerProfile)
  signed_in_with_access_permissions = signed_in_as_customer && current_user_profile.company_team_admin? && signed_in_to_customer && current_user_profile.admin_access_permissions.where(active: true, customer_profile: session_profile).present?

- if signed_in_as_admin || signed_in_as_access_admin || signed_in_with_access_permissions
  :ruby
    logged_in_text = signed_in_with_access_permissions ? 'Admin, ' : ''
    logged_in_text = "You're logged in as #{session_profile.user.name}. "
  .sign-out-admin-banner
    = logged_in_text
    = link_to 'Click here when you\'re finished', signed_in_with_access_permissions ? sign_out_as_customer_path: sign_out_as_path

    - if signed_in_to_customer && session_profile.admin_notes.present?
      %span{ data: { view_admin_banner: true } }
        %span#customer-admin-notes.admin_top_bar_info.float-right Click here to view customer notes
        %p#customer-notes-text.text-right.hidden
          %small
            = session_profile.admin_notes
