:ruby
  title = content_for?(:header_title) ? yield(:header_title) : ''
  is_masquerading_as_admin = session[:original_admin_id].present?
  
.customer-header
  .customer-header__info
    .between-flex
      - if content_for(:back_button)
        = yield(:back_button)

      - if title
        %h3.customer-header__title
          = title
    - if content_for(:header_elements)
      = yield(:header_elements)

  .customer-header__auth
    .personalised__tag.circle-icon= customer_name_helper(:initials, current_user.name)
    #dash-hamburger.hamburger{ data: { view_hamburger: { kind: 'dash' }.to_json } }
      %span
      %span
      %span
      %span
      %span
      %span
    %div
      .no-reset
        %ul.dropdown.menu.no-reset{ data: { dropdown_menu: '', alignment: 'right' } }
          %li.is-dropdown-submenu-parent.no-reset
            %a.personalised__name.personalised__name--header{ class: (is_masquerading_as_admin ? 'masquerade' : ''), href: 'javascript:void(0)' }
              = customer_name_helper(:full, current_user.name)
            %ul.menu.customer-header__dropdown.no-reset
              - if is_masquerading_as_admin
                %li= link_to 'Back to My Admin', sign_out_as_admin_path
              %li= link_to 'Logout', destroy_user_session_path, method: :delete
