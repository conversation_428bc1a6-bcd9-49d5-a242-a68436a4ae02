<!--
This is the custom order form for creating custom order for cutomer by admin.
1. Initiated from customer list page to create a new custom order for the selected customer
2. Initiated from custom orders list page to update the existing order

Actions:
  a. create order
  b. update order,
  c. send quote to customer
  d. confirm the order
-->
<%
  # for populating exiting order lines
  if @object.instance_of? CustomOrder
    # update exsiting order
    order = @object
    customer_profile = order.customer_profile
    if order.persisted?
      order_lines = order.order_lines.order(:id)
      loc = order_lines.present? ? order.order_lines.first.location : Location.where(order: order).first
    else
      order_lines = order.order_lines
      loc = order_lines.present? ? order_lines.first.location.dup : nil
    end
  else
    # create new custom order for customer
    customer_profile = @object
    loc = nil
    order = Order.new(status: 'draft', order_variant: 'event_order')
    order_lines = OrderLine.none
  end

  purchase_orders = customer_profile.customer_purchase_orders.where(active: true).distinct
  requires_purchase_order = customer_profile.company.try(&:requires_po)

  customer_credit_cards = customer_profile.credit_cards.where(enabled: true, saved_for_future: true).order(created_at: :desc)
  customer_credit_cards = customer_credit_cards.where(pay_on_account: false)
  customer_credit_cards = customer_credit_cards.where.not(stripe_token: nil)
  customer_credit_cards = customer_credit_cards.reject(&:expired?)

  order_categories = ['catering-services', 'kitchen-supplies'].map{|group_name| Category.generic_category_for(group_name: group_name) }

  meal_plans = customer_profile.meal_plans.where(archived_at: nil)
  if order.meal_plan_id.present? && meal_plans.map(&:id).exclude?(order.meal_plan_id)
    meal_plans = meal_plans.to_a.unshift(order.meal_plan)
  end
%>

<style>
  .ui-autocomplete-loading {
    background: white url('/assets/loading_16x16.gif') right center no-repeat;
  }
  .required-mark {
    color: red;
  }
</style>

<%= form_tag('', {id: 'custom_order_form'}) do %>
  <%= hidden_field_tag 'customer_profile_id', customer_profile.id %>
  <%= hidden_field_tag 'order_id', order.try(:id) %>
  <%= hidden_field_tag 'whodunnit_id', current_user.id %>
  <fieldset style='margin-top:30px'>
    <legend>
      <i class='icon-chevron-down'></i>
      <%- if order.present? && order.persisted? %>
      Custom Order <%= "##{order.id} - Status: #{order.status.capitalize} - Customer: #{customer_profile.name} (#{customer_profile.id})" %>
      <%- else %>
        Custom Order (New) - <%= "Customer: #{customer_profile.name} (#{customer_profile.id})" %>
      <% end %>
    </legend>
    <div class='form-group'>
      <label for='event_name'>Event name<span class='required-mark'>*</span></label>
      <div class='form-inline'>
        <%= text_field_tag 'event_name', order.try(:name), class: 'form-control', required: true, display_name: 'Event name' %>
      </div>
    </div>
    <div class='form-group'>
    	<label for='event_name'>Number of people<span class='required-mark'>*</span></label>
    	<div class='form-inline'>
    		<%= number_field_tag 'number_of_people', order.try(:number_of_people), class: 'form-control', required: true, display_name: 'Number of people' %>
    	</div>
    </div>

    <% if meal_plans.present? %>
      <div class='form-group'>
        <label for="total">Meal Plan</label>
        <div class='form-inline'>
          <%= select_tag 'meal_plan_id', options_from_collection_for_select(meal_plans, 'id', 'name', order.meal_plan_id), { include_blank: true, class: 'form-control' } %>
        </div>
      </div>
    <% end %>
  </fieldset>

  <fieldset style='margin-top:30px'>
    <legend><i class='icon-chevron-down'></i>  Delivery Details</legend>
    <div class='form-group'>
      <label for='delivery_at'>Delivery date<span class='required-mark'>*</span></label>
      <div class='form-inline' style='position: relative;'>
        <%= text_field_tag 'delivery_at', order.delivery_at&.to_s(:datetime), class: 'form-control datepicker order_delivery_at', required: true, display_name: 'Delivery date' %>
      </div>
    </div>
    <div class='form-group'>
      <label for='delivery_address'>Delivery Level</label>
      <div class='form-inline'>
        <%= text_field_tag 'delivery_address_level', order.try(:delivery_address_level), class: 'form-control', display_name: 'Delivery address level' %>
        <small style='display:block;'>
          Enter delivery level data as number value, eg.  <strong>41</strong> instead of <strong>level 41</strong> order <strong>L41</strong>
        </small>
      </div>
    </div>
    <div class='form-group'>
      <label for='delivery_address'>Delivery address<span class='required-mark'>*</span></label>
      <div class='form-inline'>
        <%= text_field_tag 'delivery_address', order.try(:delivery_address), class: 'form-control', required: true, display_name: 'Delivery address' %>
      </div>
    </div>
    <div class='form-group'>
      <label for='delivery_suburb'>Delivery suburb<span class='required-mark'>*</span></label>
      <div class='form-inline'>
        <%= hidden_field_tag 'suburb_id', order.try(:delivery_suburb_id) %>
        <% suburb = Suburb.find(order.try(:delivery_suburb_id)) if order.try(:delivery_suburb_id) %>
        <%= text_field_tag 'delivery_suburb', suburb.try(:label), class: 'form-control', autocomplete: 'new-password', required: true, display_name: 'Delivery suburb' %>
        <small style='display:block;'>
          Type at least 2 characters and select a suburb from the dropdown
        </small>
      </div>
    </div>
    <div class='form-group'>
      <label for='delivery_instruction'>Delivery instructions<span class='required-mark'>*</span></label>
      <div class='form-inline'>
        <%= text_area_tag 'delivery_instruction', order.try(:delivery_instruction), class: 'form-control', required: true, display_name: "Delivery instruction" %>
      </div>
    </div>
    <div class='form-group'>
      <label for="po_number">
        PO number
        <% if requires_purchase_order %>
        <span class='required-mark'>*</span>
        <% end %>
      </label>
      <div class='form-inline'>
        <% selected_cpo_id = order.present? ? order.cpo_id : '' %>
        <%= select_tag :cpo_id, options_from_collection_for_select(purchase_orders.order(:po_number), :id, :po_number, selected_cpo_id), { class: 'form-control', include_blank: 'Select PO number', required: requires_purchase_order, display_name: 'Purchase Order' } %>
        <%= text_field_tag :po_number, nil, class: 'form-control new_po_number hide', placeholder: 'Enter new PO number', required: requires_purchase_order, display_name: 'New Purchase Order Number' %>
        &nbsp;
        <a href='javascript:void(0)' class='button new_po_number_button'>New PO number</a>
      </div>
    </div>
    <div class='form-group'>
      <label for="department_identity">Cost Centre ID</label>
      <div class='form-inline'>
        <%= text_field_tag 'department_identity', order.try(:department_identity), class: 'form-control' %>
      </div>
    </div>
  </fielset>

  <fieldset style='margin-top:30px'>
    <legend><i class='icon-chevron-down'></i>  Contact Details</legend>
    <div class='form-group'>
      <label for="contact_name">Contact name<span class='required-mark'>*</span></label>
      <div class='form-inline'>
        <% value =  order.try(:contact_name) ?  order.try(:contact_name) : customer_profile.customer_name %>
        <%= text_field_tag 'contact_name', value, class: 'form-control', required: true, display_name: "Contact Name" %>
      </div>
    </div>
    <div class='form-group'>
      <label for="contact_name">Contact email</label>
      <div class='form-inline'>
        <%= text_field_tag 'contact_email', order.try(:contact_email), class: 'form-control' %>
      </div>
    </div>
    <div class='form-group'>
      <label for="contact_phone">Contact phone<span class='required-mark'>*</span></label>
      <div class='form-inline'>
        <% value =  order.try(:phone) ?  order.try(:phone) : customer_profile.contact_phone %>
        <%= text_field_tag 'contact_phone', value, class: 'form-control', required: true, display_name: "Contact Phone" %>
      </div>
    </div>
    <div class='form-group'>
      <label for="company_name">Company Name</label>
      <div class='form-inline'>
        <% value =  order.try(:company_name) ?  order.try(:company_name) : customer_profile.company_name %>
        <%= text_field_tag 'company_name', value, class: 'form-control' %>
      </div>
    </div>
  </fielset>

  <fieldset style='margin-top:30px'>
    <legend><i class='icon-chevron-down'></i>  Order Details</legend>
    <div class='form-group'>
      <label for="location">Order Category<span class='required-mark'>*</span></label>
      <div class='form-inline'>
        <%= select_tag :major_category_id, options_from_collection_for_select(order_categories, :id, :name, order&.major_category_id), { class: 'form-control', include_blank: "Select Order Category", required: 'required', display_name: 'Order Category' } %>
      </div>
      <br/>
      <label for='location'>Location<span class='required-mark'>*</span></label>
      <div class='form-inline'>
        <%= hidden_field_tag 'location_id', loc.try(:id) %>
        <%= text_field_tag 'location_name', loc.try(:details), class: 'form-control', required: 'required', display_name: "Location" %>
      </div>
      <br/>
      <table class='table table-striped' id='order_lines' max_id=0 >
        <thead>
          <tr>
            <th>Supplier<span class='required-mark'>*</span></th>
            <th>Item Name<span class='required-mark'>*</span></th>
            <th>Note / Item Description</th>
            <th>Quantity<span class='required-mark'>*</span></th>
            <th>Price<span class='required-mark'>*</span></th>
            <th>Price Includes Supplier Commission?</th>
            <th>Supplier Cost<span class='required-mark'>*</span></th>
            <th>Customer Price<span class='required-mark'>*</span></th>
            <th>GST<span class='required-mark'>*</span></th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
        </tbody>
      </table>
      <a class='btn btn-default' href="#" role='button' id='add_new_order_line'>Add New Order Line</a>
    </div>
  </fieldset>

  <fieldset style='margin-top:30px'>
    <legend><i class='icon-chevron-down'></i>  Suppliers Details</legend>
      <div class="supplier_details"></div>
  </fieldset>

  <fieldset style='margin-top:30px;margin-bottom:30px;''>
    <legend><i class='icon-chevron-down'></i>  Totals</legend>
    <div class='columns small-12 medium-6'>
      <div class='form-group'>
        <label for="markup">
            CUSTOMER MARKUP
            <span class='required-mark'>*</span>
            <small>(Must be minimum 25%)</small>
        </label>
        <div class='form-inline'>
          <div class="input-group">
            <%= number_field_tag 'commission', order.present? ? order.commission : 0, class: 'form-control', id: 'markup', required: true, onkeyup: "calculate_total(); calculate_yordar_commission()", display_name: "Commission" %>
            <div class="input-group-addon">%</div>
          </div>
          <strong id='yordar-commission' class='hidden' style='margin-left: 1rem;'>
            Yordar Commission: <span id='yordar-commission-value'></span>%
            <small id='yordar-commission-minimum'>(Must be minimum 20%)</small>
          </strong>
        </div>
      </div>
      <div class='form-group'>
        <label for="delivery">Delivery<span class='required-mark'>*</span></label>
        <div class='form-inline'>
          <div class="input-group">
            <div class="input-group-addon">$</div>
            <%= number_field_tag 'delivery', order.present? ? order.customer_delivery : 0, class: 'form-control', required: true, onkeyup: "calculate_total()", display_name: "Delivery", readonly: true %>
          </div>
        </div>
      </div>
      <div class='form-group'>
        <label for="total">Total<span class='required-mark'>*</span></label>
        <div class='form-inline'>
          <div class="input-group">
            <div class="input-group-addon">$</div>
            <%= number_field_tag 'total', 0, class: 'form-control', required: true, readonly: true, display_name: "Total" %>
          </div>
        </div>
      </div>
    <%- if order.present? && order.persisted? && order.customer_total.present? %>
      <div class='columns small-12 medium-6'>
        <h4 style='border-bottom: 1px solid #ccc'>Customer Totals as Saved in Database</h4>
        <p>
          <label>Subtotal: <%= number_to_currency(order.customer_subtotal) %></label>
        </p>
        <%- if order.customer_delivery.present? && order.customer_delivery != 0.0 %>
          <p>
            <label>Delivery: <%= number_to_currency(order.customer_delivery) %></label>
          </p>
        <% end %>
        <p>
          <label>GST: <%= number_to_currency(order.customer_gst) %></label>
        </p>
        <%- if order.customer_surcharge.present? && order.customer_surcharge != 0.0 %>
          <p>
            <label>CC Surcharge: <%= number_to_currency(order.customer_surcharge) %></label>
          </p>
        <% end %>
        <%- if order.customer_topup.present? && order.customer_topup != 0.0 %>
          <p>
            <label>Topup: <%= number_to_currency(order.customer_topup) %></label>
          </p>
        <% end %>
        <%- if order.discount.present? && order.discount != 0.0 %>
          <p>
            <label>
              Discount: <%= number_to_currency(order.discount) %></label>
            <% if (coupon = order.coupon.presence) %>
              <small>(Attached to coupon with code <strong>`<%= coupon.code %>`</strong> - <%= coupon.discount_note %>)</small>
            <% end %>
            <% if (promotion = order.promotion.presence) %>
              <small>(Attached to promotion <strong>`<%= promotion.name %>`</strong> - <%= promotion.discount_note %>)</small>
            <% end %>
          </p>
        <% end %>
        <p>
          <label>Total: <%= number_to_currency(order.customer_total) %></label>
        </p>
      </div>
    <%- end %>
  </fieldset>

  <fieldset style="margin-top:30px;margin-bottom:30px;">
    <legend><i class='icon-chevron-down'></i>  Payment Details</legend>
    <%- if customer_profile.can_pay_on_account? || customer_credit_cards.present? %>
      <div class='form-group'>
        <label for="total">Credit card<span class='required-mark'>*</span></label>
        <div class="saved-payments small-12 medium-8 large-5 columns form-inline" data-default-cc-id=<%= order.try(:credit_card_id)%>>
          <select id="credit_card_id" name="credit_card_id" class="select-credit-card form-control" placeholder="please select">
            <% if customer_profile.can_pay_on_account? %>
              <option <%= "selected" if order.try(:credit_card_id) == 1 %> value="1" data-charge=0>Invoice this bill to my company</option>
            <% end %>
            <% customer_credit_cards.each do |credit_card| %>
              <option value="<%=credit_card.id %>" data-percent=<%= credit_card.surcharge_percent %> data-fee=<%= credit_card.surcharge_fee %> <%= "selected" if credit_card.id == order.try(:credit_card_id) %>><%= credit_card.label %></option>
            <% end %>
          </select>
        </div>
      <div>
    <%- else %>
      <h5>Customer cannot `Pay on Account` and does not have a stored credit card</h4>
      <p>Either set the customer within a company with <strong>Pay on Account</strong> previleges and/or add a card by logging in as the customer.</p>
    <% end %>

    <% if customer_profile.can_pay_on_account? %>
      <div class='form-group' style="margin-top:30px;">
        <div class="small-12 medium-8 large-5 columns form-inline">
          <%= hidden_field_tag :invoice_individually, 0 %>
          <label>
            <%= check_box_tag :invoice_individually, 1, order.present? && order.invoice_individually? %>
            Invoice Individually
          </label>
        </div>
      <div>
    <% end %>

    <div class='form-group' style="margin-top:30px;">
      <div class="small-12 medium-8 large-5 columns form-inline">
        <label>Coupon Code</label>
        <div class='form-inline'>
          <%= text_field_tag 'coupon_code', '', class: 'form-control', display_name: 'Coupon code' %>
        </div>
      </div>
      <% if (coupon = order.coupon.presence) %>
        <p>Attached Coupon: <strong><%= coupon.code %> (<%= coupon.discount_note %>)</strong></p>
      <% end %>
    <div>
  </fieldset>
  
  <% if order.blank? || order.status != 'confirmed' %>
    <fieldset style='margin-top:30px;margin-bottom:30px;'>
      <legend><i class='icon-chevron-down'></i>  Quote Details</legend>
      <div class='form-group'>
        <label>
          Quote emails
          <small>(Separate multiple emails with semicolon ';')</small>
        </label>
        <div class='small-12 medium-8 large-6 columns form-inline'>
          <%= text_area_tag 'quote_emails', '', class: 'form-control', placeholder: 'Email address(es) to send the quote to', display_name: 'Quote Emails', rows: 5, cols: 50 %>
          <a class='customer-quote-email' data-customer-email='<%= customer_profile.email %>'>Click to add customer email <em>`<%= customer_profile.email %>`</em></a>
        </div>
        <div class='small-12 medium-8 large-6 columns form-inline' style='margin-top: 1rem;'>
          <%= text_area_tag 'quote_message', '', class: 'form-control', placeholder: 'Optional message to go along with the quote', display_name: 'Quote Emails', rows: 3, cols: 50 %>
        </div>
        <div class='small-12 medium-8 large-6 columns form-inline' style='margin-top: 1rem;'>
          <a class='btn btn-default submit-btn' href='javascript:void(0)' role='button' data-action='send_quote' data-submit-text='Sending Quote...' data-button-text='Send Quote'>Send Quote</a>
        </div>
      </div>
    </fieldset>
  <% end %>

  <%- document_kinds = %w[customer_order_quote customer_order_details customer_team_order_details] %>
  <% if order.present? && (order_documents = order.documents.where(kind: document_kinds).order(version: :desc).presence) %>
    <p><strong>Order Documents</strong></p>
    <ul>
      <% order_documents.each do |document| %>
        <li>
          <a href='<%= document.url %>' target='_blank'>
              <%= "#{document.kind_based_name} - #{document.file_extension.upcase}" %>
          </a>
      <% end %>
    </ul>
  <% end %>

  <% if order.present? && order.status != 'draft' && customer_profile.user.present? %>
    <a href='<%= Rails.application.routes.url_helpers.sign_in_as_customer_path(customer_profile.user, redirect_path: Rails.application.routes.url_helpers.order_show_path(order)) %>'>View Order in Customer Dash</a>
  <% end %>

  <fieldset style='margin-top:30px;margin-bottom:30px;'>
    <a class='btn btn-default submit-btn' href='javascript:void(0)' role='button' data-action='confirm' data-submit-text='Confirming...' data-button-text='Confirm'>Confirm</a>

    <a class='btn btn-default' href='/admin/customer_profile' role='button' id='cancel'>Cancel</a>
    <% if order.present? && ['new', 'amended', 'confirmed'].include?(order.status) %>
      <a class='btn btn-default' href='javascript:void(0)' role='button' id='set-order-to-cancelled' data-next-url='<%= rails_admin.index_path('custom_order') %>' onclick='cancel_order(<%= order.id %>)'>Set Order to Cancelled</a>
    <% end %>
    <% if order.blank? || order.status == 'draft' %>
        <a class='btn btn-default submit-btn' href='javascript:void(0)' role='button' data-action='save' data-submit-text='Saving...'data-button-text='Save'>Save</a>
    <% end %>
    <% if %w[quoted draft].include?(order&.status) %>
      <a class='btn btn-default submit-btn' href='javascript:void(0)' role='button' data-action='save_quote' data-submit-text='Saving as Quote...' data-button-text='Save as Quote'>Save as Quote</a>
    <% end %>
    <% if order.present? && order.status == 'draft' && order.persisted? %>
      <a class='btn btn-default' href='javascript:void(0)' role='button' id='delete' data-next-url='<%= rails_admin.index_path('custom_order') %>' onclick='delete_order(<%= order.id %>)'>Delete</a>
    <% end %>
    <!-- <button type='submit' class='btn btn-default'>Submit</button> -->
  </fieldset>
<% end %>

<!-- Modal for post action message/alert -->
<div class='modal fade' id='myModal' role='dialog'>
  <div class='modal-dialog'>

    <!-- Modal content-->
    <div class='modal-content'>
      <div class='modal-header'>
        <button type='button' class='close' data-dismiss='modal'>&times;</button>
        <h4 class='modal-title'>Custom Order</h4>
      </div>
      <div class='modal-body'>
        <p>Message body</p>
      </div>
      <div class='modal-footer'>
        <button type='button' class='btn btn-default' data-dismiss='modal'>Close</button>
      </div>
    </div>

  </div>
</div>

<script>
  $(document).ready(function () {

    $('a.new_po_number_button').on('click', function(e){
      e.preventDefault();
      var $link = $(event.currentTarget);
      var $newPO = $('input.new_po_number')
      var $selectPO = $('select#cpo_id')
      if ($newPO.hasClass('hide')) {
        // Add new po number
        $newPO.removeClass('hide');
        $link.text('Select existing PO');
        $selectPO.addClass('hide');
      } else {
        $newPO.addClass('hide');
        $link.text('New PO Number');
        $selectPO.removeClass('hide');
      }
      // empty the selected po from the dropdown      
      $selectPO.val('');
    })

    $('select#cpo_id').on('change', function() {
      if ($(this).val() != "") {
        // If an existing po is selected, empty and hide the new po field
        $('input#po_number').val('');
        $('div.new_po_number').addClass('hide');
      }
    })

    // order delivery date is not showing during editing. Register the datetimepick in the following way to make it work.
    // Disable minDate option as it prevents past saved delivery date from showing
    $("input.datepicker").each(function(){
      $(this).datetimepicker({
        format: 'YYYY-MM-DD LT',
        sideBySide: true,
        stepping: 15
        // minDate:new Date()
      });
    });

    // $("input.datepicker.order_delivery_date").datetimepicker({
    //   format: "DD/MM/YYYY LT",
    //   format: 'YYYY-MM-DD LT',
    //   sideBySide: true,
    //   stepping: 15,
    //   minDate:new Date()
    // });

    // autocomple for selecting supplier
    $(document).on('keydown.autocomplete', '.tags', function() {
      $(this).autocomplete({
        // source: "/s_profile/search.json",
        source: function(request, response){
          $.get("/s_profile/search.json", {term: request.term}, function(data){
            response($.map(data, function(item) {
              return {
                label: item.company_name + " - " + item.user_name,
                value: item.company_name + " - " + item.user_name,
                id: item.id
              }
            }))
          });
        },
        minLength: 2,
        select: function( event, ui ) {
          // update supplier id stored in hidden field
          row_count = $(event.target).attr('row_count');
          $('tr[row_count=' + row_count + '] input.supplier_id').val(ui.item.id);
        }
      });
    });

    // autocomple for selecting delivery suburb
    $(document).on('keydown.autocomplete', '#delivery_suburb', function() {
      $(this).autocomplete({
        source: function(request, response){
          $.get("/api/suburbs.json", {term: request.term}, function(suburbs){
            response($.map(suburbs, function(item) {
              return {
                label: item.label,
                value: item.label,
                id: item.id
              }
            }))
          });
        },
        minLength: 2,
        select: function( event, ui ) {
          $('#suburb_id').val(ui.item.id);
        }
      });
    });

    // populate order lines
    <% order_lines.each_with_index do |order_line, idx| %>
      <%- supplier = order_line.supplier_profile %>
      var row_count = <%= idx + 1 %>;
      var orderLine = {
        row_count: row_count,
        id: '<%= order_line.id %>',
        supplier_id: <%= order_line.supplier_profile_id %>,
        supplier_name: '<%= "#{supplier.company_name} - #{supplier.user.name}" %>',
        description: '<%= order_line.name %>',
        note: '<%= order_line.note&.gsub(/\r\n?/, ' ') %>',
        quantity: <%= order_line.quantity %>,
        baseline: <%= order_line.baseline %>,
        cost: <%= order_line.cost %>,
        inc_markdown: <%= order_line.cost != order_line.baseline %>,
        price: <%= order_line.price %>,
        gst_option: '<%= order_line.is_gst_inc ? 'gst_inc' : (order_line.is_gst_free ? 'gst_free' : 'gst_exc' ) %>',
      };

      var html = order_line_form_html(orderLine);
      $('table#order_lines').append(html);
      $('table#order_lines').attr('max_id', row_count);
    <% end %>

    // initial calculation of the total after the page is loaded
    calculate_total();
    calculate_yordar_commission();

    // supplier details
    populate_suppliers();
  });

  // Add new order line
  $('#add_new_order_line').on('click', function(event){
    event.preventDefault();
    var max_id = $('table#order_lines').attr('max_id');
    var next_id = parseInt(max_id) + 1;
    var newOrderLine = {
      row_count: next_id,
      id: '',
      supplier_id: '',
      supplier_name: '',
      description: '',
      note: '',
      quantity: '',
      baseline: '',
      cost: '',
      inc_markdown: 'inc_markdown',
      price: '',
      gst_option: '',
    };
    var html = order_line_form_html(newOrderLine);
    $('table#order_lines').append(html);
    $('table#order_lines').attr('max_id', next_id);
  });

  function order_line_form_html (orderLine){
    var default_gst_selected = '';
    var gst_inc_selected = '';    
    var gst_free_selected = '';
    var gst_exc_selected = '';

    if (orderLine.gst_option == "gst_inc")
      gst_inc_selected = 'selected';
    else if (orderLine.gst_option == "gst_free")
      gst_free_selected = 'selected';
    else if (orderLine.gst_option == "gst_exc")
      gst_exc_selected = 'selected';
    else
      default_gst_selected = 'selected';

    var html =
      '<tr row_count=' + orderLine.row_count + '>' +
        '<td>' +
          '<input type="hidden" class="form-control order_line_id" row_count=' + orderLine.row_count + ' name="order_lines[' + orderLine.row_count + '][order_line_id]" value=' + orderLine.id + '>' +
          '<input type="hidden" class="form-control supplier_id" name="order_lines[' + orderLine.row_count + '][supplier_id]" required="required" display_name="Suppler id" value=' + orderLine.supplier_id + '>' +
          '<input type="text" class="form-control tags supplier" row_count=' + orderLine.row_count + ' name="order_lines[' + orderLine.row_count + '][supplier]" required="required" display_name="Suppler" onblur="populate_suppliers();" value="' + orderLine.supplier_name + '">' +
        '</td>' +
        '<td>' +
          '<input type="text" class="form-control menu_item_description" name="order_lines[' + orderLine.row_count + '][menu_item_description]" required="required" display_name="Menu item name" value="' + orderLine.description + '">' +
        '</td>' +
        '<td>' +
          '<textarea rows="1" cols="5" class="form-control note" name="order_lines[' + orderLine.row_count + '][note]" display_name="Note / Item description">' + orderLine.note + '</textarea>' +
        '</td>' +
        '<td style="width: 2rem;">' +
          '<input type="number" class="form-control quantity" row_count=' + orderLine.row_count + ' name="order_lines[' + orderLine.row_count + '][quantity]" required="required" display_name="Quantity" onkeyup="calculate_total()" value=' + orderLine.quantity + '>' +
        '</td>' +
        '<td>' +
          '<div class="input-group">' +
            '<div class="input-group-addon">$</div>' +
            '<input type="number" class="form-control baseline" row_count=' + orderLine.row_count + ' name="order_lines[' + orderLine.row_count + '][baseline]" required="required" onkeyup="calculate_total()" display_name="Baseline" value=' + orderLine.baseline + '>' +
          '</div>' +
        '</td>' +
        '<td style="width: 2rem;">' +
          '<div class="input-group">' +
            '<select name="order_lines[' + orderLine.row_count + '][includes_commission]" row_count=' + orderLine.row_count + ' class="form-control includes_commission" onchange=calculate_total()>' +
            '<option ' + (orderLine.inc_markdown ? 'selected' : '') + ' value="inc_markdown">YES - Includes Supplier Commission</option>' +
            '<option ' + (orderLine.inc_markdown ? '' : 'selected') + ' value="exc_markdown">NO - Does Not include Supplier Commission</option>' +
          '</select>' +
          '</div>' +
        '</td>' +
        '<td>' +
          '<div class="input-group">' +
            '<div class="input-group-addon">$</div>' +
            '<input type="number" class="form-control cost" row_count=' + orderLine.row_count + ' name="order_lines[' + orderLine.row_count + '][cost]" required="required" onkeyup="calculate_total()" display_name="Cost" readonly value=' + orderLine.cost + '>' +
          '</div>' +
        '</td>' +
        '<td>' +
          '<div class="input-group">' +
            '<div class="input-group-addon">$</div>' +
            '<input type="number" class="form-control price" row_count=' + orderLine.row_count + ' name="order_lines[' + orderLine.row_count + '][price]" required="required" onkeyup="calculate_total()" display_name="Price" readonly value=' + orderLine.price + '>' +
          '</div>' +
        '</td>' +
        '<td>' +
          '<div class="input-group">' +
            '<select name="order_lines[' + orderLine.row_count + '][gst_option]" row_count=' + orderLine.row_count + ' class="form-control gst_options" onchange="calculate_total()" required="required" display_name="Order Line GST Option">' +
            '<option ' + default_gst_selected + ' value=""></option>' +
            '<option ' + gst_exc_selected + ' value="gst_exc">GST Exc</option>' +
            '<option ' + gst_inc_selected + ' value="gst_inc">Inc GST</option>' +
            '<option ' + gst_free_selected + ' value="gst_free">GST Free</option>' +
          '</select>' +
          '</div>' +
        '</td>' +
        '<td>' +
          '<ul class="inline list-inline">' +
            '<li title="Delete" rel="tooltip" class="icon ">' +
              '<a class="delete" href="#" onclick="delete_order_line(' + orderLine.row_count + ')" >' +
                '<i class="icon-remove icon-large"></i>' +
              '</a>' +
            '</li>' +
            '<li title="Clone" rel="tooltip" class="icon ">' +
              '<a class="clone" href="#" onclick="clone_order_line(' + orderLine.row_count + ')" >' +
                '<i class="icon-download-alt icon-large"></i>' +
              '</a>' +
            '</li>' +

          '</ul>' +
        '</td>' +
      '</tr>';
      return html;
  }

  function delete_order_line (row_count){
    event.preventDefault();
    $('tr[row_count=' + row_count + ']').remove();
    populate_suppliers();
  }

  function clone_order_line (row_count) {
    event.preventDefault();
    var max_id = $('table#order_lines').attr('max_id');
    var next_id = parseInt(max_id) + 1;
    $('#add_new_order_line').click();
    $('tr[row_count=' + next_id + '] td input.supplier_id').val( $('tr[row_count=' + row_count + '] td input.supplier_id').val() );
    $('tr[row_count=' + next_id + '] td input.supplier').val( $('tr[row_count=' + row_count + '] td input.supplier').val() );
    $('tr[row_count=' + next_id + '] td input.menu_item_description').val( $('tr[row_count=' + row_count + '] td input.menu_item_description').val() );
    $('tr[row_count=' + next_id + '] td input.quantity').val( $('tr[row_count=' + row_count + '] td input.quantity').val() );
    $('tr[row_count=' + next_id + '] td input.baseline').val( $('tr[row_count=' + row_count + '] td input.baseline').val() );
    $('tr[row_count=' + next_id + '] td select.includes_commission').val( $('tr[row_count=' + row_count + '] td select.includes_commission').val() );
    $('tr[row_count=' + next_id + '] td input.cost').val( $('tr[row_count=' + row_count + '] td input.cost').val() );
    $('tr[row_count=' + next_id + '] td input.price').val ($('tr[row_count=' + row_count + '] td input.price').val() );
    $('tr[row_count=' + next_id + '] td select.gst_options').val ('');
    calculate_total();
  }

  function calculate_total(){
    var total = 0;
    var markup = parseFloat($('input#markup').val()) || 0;

    // calculate cost and price based on baseline
    $('input.baseline').each(function () {
      var baseline = parseFloat($(this).val());
      var row_count = $(this).attr('row_count');
      var supplier_id = $(`input[name="order_lines[${row_count}][supplier_id]"]`).val();
      var markdown = $(`tr[row_count=${row_count}]`).attr('supplier_commission');
      var markdownText = $(`#suppliers_${supplier_id}_supplier_commission`).text();
      var markdown = markdownText && parseFloat(markdownText);
      var baselineIncludesMarkdown = $(`select[name="order_lines[${row_count}][includes_commission]"]`).val() == 'inc_markdown';

      var cost = baseline;
      if (baselineIncludesMarkdown && markdown) {
        cost = baseline * (1 - (markdown / 100)); // remove markdown if price includes it
      }
      const price = cost * (1 + markup / 100);

      $(`input.cost[row_count=${row_count}]`).val(cost.toFixed(2));
      $(`input.price[row_count=${row_count}]`).val(price.toFixed(2));
    })

    // calculate order total
    $('input.price').each(function() {
      var value = $(this).val();
      row_count = $(this).attr('row_count');
      if(!isNaN(value) && value.length != 0) {
        quantity = $('input.quantity[row_count=' + row_count + ']').val();
        total += parseFloat(value) * quantity;
      }
    });
    calculate_delivery_fee();
    delivery_fee = $('input#delivery').val();
    if(!isNaN(delivery_fee) && delivery_fee.length != 0) {
      total = total + parseFloat(delivery_fee);
    }
    $('input#total').val(total);
  }

  function calculate_yordar_commission (event) {
    const markdown = 0.0;
    var markup = parseFloat($('input#markup').val());
    var $commisionText = $('#yordar-commission');
    var $commisionValue = $('#yordar-commission-value');
    if (markup) {      
      var calculatedCommission = 1 - (1 - (markdown / 100)) / (1 + (markup / 100))
      var yordarCommission = (calculatedCommission * 100).toFixed(2)
      $commisionText.removeClass('hidden');
      $commisionValue.text(yordarCommission);

      var $commisionMinimum = $('#yordar-commission-minimum');
      if (yordarCommission >= 20.0) {
        $commisionMinimum.addClass('hidden');
      } else {
        $commisionMinimum.removeClass('hidden');
      }
    } else {
      $commisionText.addClass('hidden')
    }
  }

  function calculate_delivery_fee(){
    total_delivery_fee = 0;
    $('.supplier_delivery_fee').each(function(){
      supplier_deliery_fee = $(this).val();
      total_delivery_fee += parseFloat(supplier_deliery_fee);
    });
    $('input#delivery').val(total_delivery_fee);
    return total_delivery_fee;
  }

  $('form#custom_order_form a.customer-quote-email').on('click', function(event) {
    var customerEmail = $(event.currentTarget).data('customer-email');
    var $textarea = $('form#custom_order_form textarea[name="quote_emails"]');
    var quoteEmails = $textarea.val();
    if (!quoteEmails.includes(customerEmail)) {
      var newQuoteEmails = `${customerEmail};${quoteEmails}`
      $textarea.val(newQuoteEmails);
    }
  });

  $('form#custom_order_form a.submit-btn').on('click', function(event){
    var $button = $(event.currentTarget);
    var submitText = $button.data('submit-text');
    var action = $button.data('action');
    $button.attr('disabled', true);    
    $button.text(submitText);
    submit_form(action, $button);
  })

  function validate(custom_action) {
    var is_valid = true;
    var error = "";
    var required_fields = $('input[required=required]:visible, textarea[required=required]:visible, select[required=required]:visible');
    $.each(required_fields, function( idx, item ){
      var $field = $(item)
      var value = $field.val();
      if( value.length == 0 ) {
        if ( $field.hasClass('supplier_id') )
          error = "Invalid supplier";
        else
          error = $field.attr('display_name') + ' is required';
        $("#myModal div.modal-body p").text(error);
        $("#myModal").modal();
        is_valid = false;
        // break the $.each loop

        var scrollTop = $field.offset().top - 50 - 40// top-header + input + label 
        window.scrollTo({top: scrollTop, behavior: 'smooth'});

        return false;
      }
    });
    
    // check for suburb ID
    var suburbId = $('input[name="suburb_id"]').val();
    if (is_valid && !suburbId) {
      error = 'Type a suburb name for Delivery Suburb and <strong>select one from the drop down</strong><br/>Please <strong>DO-NOT copy and paste delivery suburb info</strong>'
      $("#myModal div.modal-body p").html(error);
      $("#myModal").modal();

      var scrollTop = $('#delivery_suburb').offset().top - 50 - 40// top-header + input + label 
      window.scrollTo({top: scrollTop, behavior: 'smooth'});

      is_valid = false;
    }

    if (custom_action == 'send_quote') {
      var quoteEmails = $('form#custom_order_form textarea[name="quote_emails"]').val();
      if (!quoteEmails) {
        alert('Need at least 1 email to send a quote to')
        is_valid = false
      }
    }

    if ( !is_valid )
      return false;

    // warning on 0 valued commission or delivery
    if ( $('#markup').val() == 0 || $('#delivery').val() == 0 ) {
      if (confirm("Commission or delivery has $0 value. Are you sure to save?") == false )
        return false;
    }

    if (confirm("Have you double-checked that you've set the supplier commission correctly for each menu item?") == false)
      return false;
    
    return true;
  }

  function submit_form(custom_action, $button) {
    var is_valid = validate(custom_action);
    var buttonText = $button.data('button-text');
    event.preventDefault();
    if (is_valid) {
      var url = '<%= Rails.application.routes.url_helpers.api_custom_orders_path %>?custom_action=' + custom_action;
      var submitRequest = $.ajax({
        type: "POST",
        url: url,
        dataType: "JSON",
        data: $('form#custom_order_form').serialize(),
      });

      submitRequest.fail(function (response) {
        if (response.responseJSON && response.responseJSON.errors && response.responseJSON.errors.length) {
          alert(response.responseJSON.errors.join("\n"));  
        } else {
          alert(`Something went wrong when trying to ${custom_action.replace(/_/g, ' ')}!`)
        }
        $button.text(buttonText)
        $button.attr('disabled', false)
      });

      submitRequest.done(function (response) {
        if (response.message) {
          alert(response.message)
        }
        $button.text('Reloading Page...')
        window.location = response.redirect_to
      });
    } else {
      $button.text(buttonText)
      $button.attr('disabled', false)
    }
  }

  function delete_order(order_id) {
    if (confirm("Are you sure you want to delete the custom order?") == true) {
      var eventOrderDeletePath = '<%= Rails.application.routes.url_helpers.api_custom_order_path(id: '_order_id') %>';
      var url = eventOrderDeletePath.replace('_order_id', order_id);
      var deleteRequest = $.ajax({
        type: "DELETE",
        dataType: "json",
        url: url,
      })

      deleteRequest.done(function () {
        alert('Order Removed/Deleted!');
        var data_next_url = $('#delete').attr("data-next-url");
        window.location = data_next_url
      })

      deleteRequest.fail(function (response) {
        if (response.responseJSON && response.responseJSON.errors && response.responseJSON.errors.length) {
          alert(response.responseJSON.errors.join("\n"));  
        } else {
          alert(`Something went wrong when trying to delete/remove the order!`)
        }
      })
    }
  }

  function cancel_order(order_id) {
    if (confirm("Are you sure you want to cancel the custom order?") == true) {
      var eventOrderCancelPath = '<%= Rails.application.routes.url_helpers.api_custom_order_path(id: '_order_id') %>';
      var url = eventOrderCancelPath.replace('_order_id', order_id);
      var cancelRequest = $.ajax({
        type: "DELETE",
        data: { cancel_mode: "one-off" },
        dataType: "JSON",
        url: url,
      })
      cancelRequest.done(function () {
        alert('Order has been marked as Cancelled');
        var data_next_url = $('#set-order-to-cancelled').attr("data-next-url");
        window.location = data_next_url
      })

      cancelRequest.fail(function (response) {
        if (response.responseJSON && response.responseJSON.errors && response.responseJSON.errors.length) {
          alert(response.responseJSON.errors.join("\n"));  
        } else {
          alert(`Something went wrong when trying to cancel the order!`)
        }
      })
    }
  }

  function populate_suppliers() {
    var supplier_ids = new Array();
    $('.supplier_details').empty();
    // initialise supplier forms
    $('.supplier_id').each(function(idx){
      var supplier_id = $(this).val();
      if ( !supplier_ids.includes(supplier_id) ) {
        supplier_ids.push($(this).val());
        var supplier_name = $(this).next().val();
        var supplierHtml = init_supplier_form(supplier_id, supplier_name, idx);
        $('.supplier_details').append(supplierHtml);        
      }
    });
    // populate supplier details
    var order_id = $('#order_id').val();
    $.getJSON( {
      url: '<%= Rails.application.routes.url_helpers.suppliers_api_custom_orders_path %>',
      data: {
        order_id: order_id,
        supplier_ids: supplier_ids
      }
    }, function( data ) {
      $.each(data, function(idx, custom_order_supplier){
        supplier_profile_id = custom_order_supplier["supplier_profile_id"];
        $("#suppliers_" + supplier_profile_id + "_delivery_fee").val(custom_order_supplier["delivery_fee"]);
        $("#suppliers_" + supplier_profile_id + "_delivery_note").val(custom_order_supplier["delivery_note"]);
        $("#suppliers_" + supplier_profile_id + "_supplier_commission").text(custom_order_supplier["commission_rate"]);
      });
      // calculate delivery fee(total as well)
      calculate_total();
    });
  }

  function init_supplier_form(supplier_id, supplier_name, idx){
    var html =
      `<div class="form-group" supplier_id="${supplier_id}" ${idx > 0 ? 'style="margin-top: 4rem;"' : ''}>` +
        '<h4>' +
          supplier_name +
          '<small style="margin-left: 1rem;">' +
            '(supplier commission: <span id="suppliers_' + supplier_id + '_supplier_commission"></span>%)' +
          '</small>' +
        '</h4>' +
        '<label for="delivery_at">Delivery fee</label>' +
        '<div class="form-inline">' +
          '<input type="number" name="suppliers[' + supplier_id + '][delivery_fee]" id="suppliers_' + supplier_id + '_delivery_fee" class="form-control supplier_delivery_fee" required="required" display_name="Delivery fee" onchange="calculate_total();">' +
        '</div>' +
        '<label for="delivery_at">Delivery note</label>' +
        '<div class="form-inline">' +
          '<input type="text" name="suppliers[' + supplier_id + '][delivery_note]" id="suppliers_' + supplier_id + '_delivery_note" class="form-control" display_name="Delivery note">' +
        '</div>' +
      '</div>';

      return html;
  }

  // $('#add_new_order_line').click();

</script>

<%= render 'layouts/logrocket' %>
