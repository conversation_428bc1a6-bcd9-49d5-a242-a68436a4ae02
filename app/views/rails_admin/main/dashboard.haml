:ruby
  case
  when current_user.admin? || current_user.allow_all_customer_access? || current_user.allow_all_supplier_access?
    recent_orders = Order.order(updated_at: :desc).includes(:customer_profile, :supplier_profiles).first(100)
  else
    recent_orders = Order.none
  end
  favourite_customers = current_user.favourite_customer_profiles

  recent_order_customers = []
  recent_order_suppliers = []

  if current_user.admin? || current_user.allow_all_customer_access?
    recent_order_customers = recent_orders.map(&:customer_profile).compact.uniq.first(10)
    if current_user&.admin?
      joey_messup = CustomerProfile.where(id: 11349).first
      recent_order_customers.unshift(joey_messup) if recent_order_customers.exclude?(joey_messup)
    end
  end  
  if current_user.admin? || current_user.allow_all_supplier_access?
    recent_order_suppliers = recent_orders.map(&:supplier_profiles).flatten.compact.uniq.first(10)
  end
  
:css
  .medium-6 { width: 49%; }
  .medium-3 { width: 33.33%; }
  .columns { padding: 0 1rem; float: left;}

.clearfix

  - if favourite_customers.present?
    = render 'rails_admin/main/profile_list', title: 'Favourite Customers', model_name: 'customer_profile', profiles: favourite_customers

  - if current_user.admin?
    = render 'rails_admin/main/profile_list', title: 'Recent Customers', model_name: 'customer_profile', profiles: recent_order_customers

    = render 'rails_admin/main/profile_list', title: 'Recent Suppliers', model_name: 'supplier_profile', profiles: recent_order_suppliers

  - else
    - if current_user.allow_all_customer_access?
      = render 'rails_admin/main/profile_list', title: 'Recent Customers', model_name: 'customer_profile', profiles: recent_order_customers
    
    - if current_user.allow_all_supplier_access?
      = render 'rails_admin/main/profile_list', title: 'Recent Suppliers', model_name: 'supplier_profile', profiles: recent_order_suppliers
    - elsif current_user.can_access_suppliers?
      = render 'rails_admin/main/profile_list', title: 'Suppliers', model_name: 'supplier_profile', profiles: current_user.supplier_profiles
