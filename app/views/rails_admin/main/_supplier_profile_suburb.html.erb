<%= hidden_field_tag "supplier_profile[company_address_suburb_id]", profile.company_address_suburb_id %>
<%= text_field_tag :suburb, (profile.company_address_suburb.label if profile.company_address_suburb.present?), { placeholder: 'Suburb or Postcode', class: 'form-control postcode-autocomplete', required: true, data: { autocomplete_path: main_app.api_suburbs_path, id_field: 'supplier_profile_company_address_suburb_id' } } %>

<script>
$(document).ready(function () {
  $(".postcode-autocomplete").autocomplete({
    messages : {
      noResults : '',
      results : function(){}
    },
    source: function( request, response ) {
      url = $(".postcode-autocomplete").attr('data-autocomplete-path') + "?term=" + request.term;
      $.getJSON(url, function(suburbs) {
        // set the result to the response object
        response(suburbs);
      });
    },
    minLength: 1,
    select: function( event, ui ) {			
      var id_field = $(this).attr("data-id-field");
      
      if (typeof id_field !== 'undefined' && id_field.length > 0) {				
        $("#"+id_field).val(ui.item.id);
      } else {
        $(this).parents('form').find('#suburb_id').val(ui.item.id);
      }
    }
    
  });
});
</script>
