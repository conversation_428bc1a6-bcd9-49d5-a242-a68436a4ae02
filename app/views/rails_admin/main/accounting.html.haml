:ruby
  suppliers = SupplierProfile.where.not(company_name: [nil, '']).order('company_name ASC')
  companies = Company.where.not(name: [nil, '']).order('name ASC')
  customers = CustomerProfile.where.not(customer_name: [nil, '']).order('customer_name ASC')

%fieldset{ style: 'margin-top:30px' }
  %legend
    %i.icon-chevron-down
    Reports
  .control-group{ style: 'display: block;' }
    = form_tag '', class: 'form-horizontal denser admin-report-form' do
      #report_date_fields.control-group.datetime_type.report_date_fields
        = label_tag 'Filter by Date', nil, class: 'control-label'
        .controls
          = text_field_tag :from, params[:from], placeholder: 'YYYY-MM-DD', class: 'required datepicker'
          = text_field_tag :to, params[:to], placeholder: 'YYYY-MM-DD', class: 'datepicker'
          %p.help-block Required. pick a date range
          
      #report_date_fields.control-group.datetime_type.report_date_fields
        = label_tag 'Filter by supplier', nil, class: 'control-label'
        .controls
          = select_tag 'supplier_profile_id', options_from_collection_for_select(suppliers, 'id', 'name', params[:supplier_profile_id]), include_blank: true
          %p.help-block Optional.

      #report_date_fields.control-group.datetime_type.report_date_fields
        = label_tag 'Filter by company', nil, class: 'control-label'
        .controls
          = select_tag 'company_id', '<option value=\'default\'> - customers with no company -</option>\n' + options_from_collection_for_select(companies, 'id', 'name', params[:company_id]), include_blank: true, id: 'order-company'
          %p.help-block Optional.

      #report_date_fields.control-group.datetime_type.report_date_fields
        = label_tag 'Filter by customer', nil, class: 'control-label'
        .controls
          = select_tag 'customer_profile_id', options_from_collection_for_select(customers, 'id', 'customer_name', params[:customer_profile_id]), include_blank: true
          %p.help-block Optional.

      #report_date_fields.control-group.datetime_type.report_date_fields
        = label_tag 'Filter by order name', nil, class: 'control-label'
        .controls
          = text_field_tag :order_name, nil
          %p.help-block Optional.

      .form-actions
        %button.btn.btn-primary.generate-admin-report{ name: 'csv', type: 'submit', data: { url: main_app.accounting_report_path(kind: 'invoice') } }
          Invoice Report

        %button.btn.btn-primary.generate-admin-report{ name: 'csv', type: 'submit', data: { url: main_app.accounting_report_path(kind: 'order') } }
          Order Report

        %button.btn.btn-primary.generate-admin-report{ name: 'csv', type: 'submit', data: { url: main_app.accounting_report_path(kind: 'product') } }
          Product Report
      %br/
      %br/

:javascript
  $(function() {
  
    $( '.datepicker' ).datetimepicker({
      format: 'YYYY-MM-DD'
    });

    $(document).on('click', '.generate-admin-report', function (event) {
      event.preventDefault();
      var $button = $(event.currentTarget);
      var $form = $button.parents('form.admin-report-form');
      $form.attr('action', $button.data('url'));
      $form.submit();
    })
    
  });