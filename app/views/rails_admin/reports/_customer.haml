:ruby
  companies = Company.order('name ASC').all
  customers = CustomerProfile.where.not(customer_name: nil).order(customer_name: :asc)
  detail_options = [
    ['Orders data', 'orders'],
    ['Products data', 'products'],
    ['Supplier data', 'suppliers']
  ]

= form_tag main_app.api_reports_path, method: :get do
  #report_date_fields.control-group.datetime_type.report_date_fields
    = hidden_field_tag :source_type, 'CustomerProfile'
    = hidden_field_tag :report_type, 'monthly'
    = hidden_field_tag :is_admin_reports, true
    = label_tag 'Search for order delivered at', nil, class: 'control-label'
    .controls
      = text_field_tag :start_date, nil, placeholder: 'dd-mm-yyyy', class: 'required datepicker'

      = text_field_tag :end_date, nil, placeholder: 'dd-mm-yyyy', class: 'datepicker'

      %p.help-block Required. pick a date range

  #report_date_fields.control-group.datetime_type.report_date_fields
    = label_tag 'Filter by customer', nil, class: 'control-label'
    .controls
      = select_tag :customer_id, options_from_collection_for_select(customers, :id, :customer_name), include_blank: true
      %p.help-block Optional.


  #report_date_fields.control-group.datetime_type.report_date_fields
    = label_tag 'Company Wide Data for customer', nil, class: 'control-label'
    .controls
      = check_box_tag :company_wide
      %p.help-block Optional.. only when filtering by customer

  #report_date_fields.control-group.datetime_type.report_date_fields
    = label_tag 'Type of data to retrieve', nil, class: 'control-label'
    .controls
      = select_tag :detail_type, options_for_select(detail_options)
      %p.help-block Required

  .form-actions
    %button.btn.btn-primary{ name: 'format', type: 'submit', value: 'csv' }
      %i.icon-white.icon-ok
      Export to csv

    %a.btn.btn-primary.generate-graph{ data: { container_id: 'customer-order-spend-container' } }
      %i.icon-white.icon-ok
      Generate graph


#customer-order-spend-container.spend-chart

