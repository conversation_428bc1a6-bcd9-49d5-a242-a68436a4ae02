:ruby
  companies = Company.order('name ASC').all
  suppliers = SupplierProfile.where.not(company_name: nil).order(company_name: :asc)
  detail_options = [
    ['Orders data', 'orders'],
    ['Products data', 'products']
  ]

= form_tag main_app.api_reports_path, method: :get do
  #report_date_fields.control-group.datetime_type.report_date_fields
    = hidden_field_tag :source_type, 'SupplierProfile'
    = hidden_field_tag :report_type, 'monthly'
    = hidden_field_tag :is_admin_reports, true
    = label_tag 'Search for order delivered at', nil, class: 'control-label'
    .controls
      = text_field_tag :start_date, nil, placeholder: 'dd-mm-yyyy', class: 'required datepicker'

      = text_field_tag :end_date, nil, placeholder: 'dd-mm-yyyy', class: 'datepicker'

      %p.help-block Required. pick a date range

  #report_date_fields.control-group.datetime_type.report_date_fields
    = label_tag 'Filter by supplier', nil, class: 'control-label'
    .controls
      = select_tag :supplier_id, options_from_collection_for_select(suppliers, :id, :company_name), include_blank: true
      %p.help-block Optional.

  #report_date_fields.control-group.datetime_type.report_date_fields
    = label_tag 'Type of data to retrieve', nil, class: 'control-label'
    .controls
      = select_tag :detail_type, options_for_select(detail_options)
      %p.help-block Required

  .form-actions
    %button.btn.btn-primary{ name: 'format', type: 'submit', value: 'csv' }
      %i.icon-white.icon-ok
      Export to csv

    %a.btn.btn-primary.generate-graph{ data: { container_id: 'supplier-order-spend-container' } }
      %i.icon-white.icon-ok
      Generate graph

#supplier-order-spend-container.spend-chart
