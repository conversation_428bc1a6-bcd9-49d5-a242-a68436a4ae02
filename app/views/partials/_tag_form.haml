:ruby
  translation_key += ".#{field}"
  options = tag_options(field)
  case
  when field == :requires_department_identity
    allow_blank = 'Not Required'
  when field == :accounting_software && object.is_a?(CustomerFlags) && (company_accounting_software = object.customer_profile.company&.accounting_software.presence)
    allow_blank = "Company Setting - #{company_accounting_software.capitalize}"
    options << ['No Accounting Software', 'none']
  when field == :menu_reminder_frequency
    allow_blank = 'Do Not Remind'
  else 
    allow_blank = true
  end

= form_for object, url: update_url, html: { class: 'tag-form', data: { method: 'PUT' } } do |f|
  .row
    .small-12.medium-8.columns.no-gutter
      %p.notification-pref__heading
        = I18n.t("#{translation_key}.label", default: translation_key)
        %span.notification-pref__loading.hidden
          %span.sk-three-bounce
            %span.sk-child.sk-bounce1
            %span.sk-child.sk-bounce2
            %span.sk-child.sk-bounce3

      %p.notification-pref__description
        = I18n.t("#{translation_key}.description", default: 'description')

    .small-12.medium-4.columns.text-right
      - case
      - when %i[is_new_expires_at menu_last_updated_on].include?(field)
        .float-right
          = f.text_field field, value: f.object.send(field).present? ? f.object.send(field).to_s(:date) : nil, class: 'datepicker form-input user-tag', style: 'width: 200px;', data: { view_datepicker_input: true }
          - if field == :is_new_expires_at && f.object.send(field).present?
            %span.checkout-hint
              = f.object.send(field) < Time.zone.now ? 'No Longer New' : 'Tagged as New'
          - if field == :menu_last_updated_on
            %span.checkout-hint
              = f.object.supplier_profile.is_searchable? ? 'Is Searchable' : 'Currently Not Searchable'
      - when options.present?
        = f.select field, options, { include_blank: allow_blank }, { class: 'form-input user-tag', style: 'width: 150px;' }
      - else
        %label{ for: field }
          .section-toggle
            = f.check_box field, id: field, class: 'user-tag'
            %span.section-toggle__switch
