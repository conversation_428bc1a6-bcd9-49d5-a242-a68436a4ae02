:ruby
  auto_open ||= false
  selected_suburb = cookies[:yordar_street_address].present? ? cookies[:yordar_street_address] + ', ' + cookies[:yordar_suburb_label] : cookies[:yordar_suburb_label]

  search_url = next_app_supplier_search_url(category_group: '_category_group_', state: '_state_', suburb: '_suburb_')
  
  google_location_config = {
    search_url: search_url,
    suburb_id: cookies[:yordar_suburb_id].presence || '',
    suburb: cookies[:yordar_suburb].presence || '',
    state: cookies[:yordar_state].presence || '',
    postcode: cookies[:yordar_postcode].presence || '',
    street_address: cookies[:yordar_street_address].presence || '',
    category_group: params[:category_group] || 'office-catering',
    is_next_app: true,
    countryCode: request_country_code,
  }

#enter-address-modal.reveal{ role: 'dialog', data: { close_on_click: (auto_open ? 'false' : nil), close_on_esc: (auto_open ? 'false' : nil), reveal: '' }, aria: { hidden: true }  }
  %h3.no-address-heading Please enter an address or suburb before continuing

  .suppliers-cta-form
    = text_field_tag :location_search, selected_suburb, { placeholder: 'Enter street address, suburb or city', data: { view_google_location_search: google_location_config.to_json } }

    = submit_tag 'Find Suppliers', class: 'button small search-submit'
    #postcode-search-spinner.button.modal-spinner{ style: 'display:none;' }
      .sk-three-bounce
        .sk-child.sk-bounce1
        .sk-child.sk-bounce2
        .sk-child.sk-bounce3
