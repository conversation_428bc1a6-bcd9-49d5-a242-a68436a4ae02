:ruby
  hide_out_of_budget_items = current_order_budget.present? && session_profile.blank?
  modal_class = hide_out_of_budget_items ? 'hide-out-of-budget-items' : ''
  has_image = menu_item.image?

.reveal.menu-item-reveal.menu-item-modal{ id: "menu-item-modal-#{menu_item.id}", role: 'dialog', class: modal_class, data: { reveal: true, menu_item_id: menu_item.id, is_gst_free: menu_item.is_gst_free }, aria: { labelledby: 'modalTitle', hidden: true } }
  %h2.menu-item-modal-heading
    = menu_item.name

    %a.close-reveal.menu-item-modal-close{ data: { reveal_id: "menu-item-modal-#{menu_item.id}" } }
      x

  .menu-item-modal-content
    %p.menu-item-modal-description
      = "#{menu_item.description}." if menu_item.description.present?
      - if !team_order_pricing?
        - if menu_item.minimum_quantity.present? && menu_item.minimum_quantity > 0
          %span Min Qty: #{menu_item.minimum_quantity}
        - if menu_item.sub_quantity.present? && menu_item.sub_quantity > 0
          %span Sub Qty: #{menu_item.sub_quantity}.
        - if letter_icons.present?
          =raw letter_icons
    - if has_image
      - image_tooltip_url = Cloudinary::Utils.cloudinary_url(menu_item.ci_image, width: 260, height: 260, crop: 'fill')
      .menu-item-modal-image{data: { tooltip: image_tooltip_url } }
        = resize_image_tag(menu_item.ci_image, menu_item.name, 600, 600, 'fill')

    = render 'menu_items/selection_list', menu_item: menu_item, serving_sizes: serving_sizes, item_rate_cards: item_rate_cards

    = render 'menu_items/extras_list', menu_item: menu_item, menu_extras: menu_extras if menu_extras.present?

  .modal-footer
    %a.add-to-cart.button{ aria: { label: 'Close' } }
      Add to Cart
