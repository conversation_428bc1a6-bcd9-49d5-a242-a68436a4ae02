:ruby
  maximum_quantity ||= nil
.item-name
  = name
  - if !is_within_budget
    %small
      %sup *
      over budget

%span.item-price{ class: discount_price.present? ? 'discount-price' : '' }
  - if show_pricing?
    %strong
      = number_to_currency(discount_price.presence || price, precision: 2)
      - if team_order_pricing?
        %small (inc gst)


.qty-input.value-counter.clearfix
  %button{ type: 'button', class: 'minus-btn toggle-quantity', disabled: cannot_select_menu_item?, data: { toggle: 'decrease' } }
    = "-"

  %input.count-input.menu-item-qty{ type: 'number', disabled: cannot_select_menu_item?, value: (!cannot_select_menu_item? && set_default_value ? 1 : ''), max: (maximum_quantity if maximum_quantity.present?) , data: { price: discount_price || price, with_defaults: set_default_value.to_s } }/

  %button{ type: 'button', class: 'plus-btn toggle-quantity', disabled: cannot_select_menu_item?, data: { toggle: 'increase' } }
    +

  -if maximum_quantity.present?
    %span.item-quantity-warning
      Max qty: #{maximum_quantity}

%input{ type: 'text', class: 'menu-item-modal-note', placeholder: 'Add note (extra sauce, no onions, etc)' }/


