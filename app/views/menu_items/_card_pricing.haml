:ruby
  serving_prices = []
  markup_override = @supplier_menu.markup_override
  gst_country = team_order_pricing? ? request_country_code : nil
  
  if serving_sizes.present? && serving_sizes.size > 1
    serving_prices = serving_sizes.map do |serving_size|
      discount_pricing = item_rate_cards.detect{|rc| rc.serving_size_id == serving_size.id }
      if discount_pricing
        serving_price = team_order_pricing? ? discount_pricing.price_inc_gst(gst_country: gst_country) : discount_pricing.price
      else
        serving_price = serving_size.markup_price(gst_country: gst_country, override: markup_override)
      end
      serving_price
    end.uniq.sort
  end

.item-footer
  - if serving_sizes.present?
    - if serving_sizes.size > 1
      - if serving_prices.size == 1
        %span.item-price
          = number_to_currency(serving_prices.first, precision: 2)
          - if team_order_pricing?
            %small (inc gst)
      - else
        %span
          = "#{number_to_currency(serving_prices[0], precision: 2)} - #{number_to_currency(serving_prices[-1], precision: 2)}"
      - if menu_item.minimum_quantity.present? && !team_order_pricing?
        %span
          Min Qty: #{menu_item.minimum_quantity}
    - else
      :ruby
        serving_size = serving_sizes.first
        discount_pricing = item_rate_cards.detect{|rc| rc.serving_size_id == serving_size.id }
      %span.item-price{ class: discount_pricing.present? ? 'strike' : '' }
        = number_to_currency(serving_size.markup_price(gst_country: gst_country, override: markup_override), precision: 2)
        - if team_order_pricing?
          %small (inc gst)
      - if discount_pricing
        - discount_price = team_order_pricing? ? discount_pricing.price_inc_gst(gst_country: gst_country) : discount_pricing.price
        %span.item-price.discount
          Your Price: #{number_to_currency(discount_price, precision: 2, unit: '')}
      %span.serving-options= serving_size.name
  - else
    :ruby
      item_price = menu_item.markup_price(gst_country: gst_country, override: markup_override)
      has_promotion = false
      case
      when item_rate_card = item_rate_cards.first.presence
        discount_price = team_order_pricing? ? item_rate_card.price_inc_gst(gst_country: gst_country) : item_rate_card.price
      when menu_item.promo_price.present?
        discount_price = menu_item.markup_price(gst_country: gst_country, override: markup_override, promotional: true)
        has_promotion = true
      else
        nil
      end
    %span.item-price{ class: discount_price.present? ? 'strike' : '' }
      = number_to_currency(item_price, precision: 2)
      - if team_order_pricing?
        %small (inc gst)
    - if discount_price.present?      
      - if has_promotion
        %span.item-price.special-pricing
          %span.special-label SPECIAL
          #{number_to_currency(discount_price, precision: 2)}
      - else
        %span.item-price.discount
          Your Price: #{number_to_currency(discount_price, precision: 2)}

    - if menu_item.minimum_quantity.present? && !team_order_pricing?
      %span
        Min Qty: #{menu_item.minimum_quantity}

