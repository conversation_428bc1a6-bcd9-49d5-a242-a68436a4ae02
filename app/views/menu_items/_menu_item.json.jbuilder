item_rate_cards ||= []
is_favourite ||= false
markup_override ||= @supplier_menu&.markup_override
gst_country = team_order_pricing? ? request_country_code : nil
hide_over_budget ||= false
baseline_pricing = @with_baseline_pricing.present?

json.extract! menu_item, :id, :name, :description, :minimum_quantity
json.image_id menu_item.image_id

if is_favourite
  json.is_favourite is_favourite
end

json.cache! ['menu-item-json', menu_item.cache_key, item_rate_cards, markup_override&.cache_key, baseline_pricing, gst_country, team_order_pricing?, current_order_budget, hide_over_budget], expires_in: 1.hour do
  display_prices = []
  serving_sizes = menu_item.serving_sizes.select{|serving_size| serving_size.archived_at.blank? }
  if team_order_pricing?
    serving_sizes = serving_sizes.select{|serving_size| serving_size.available_for_team_order? }
    if current_order_budget.present?
      serving_sizes = serving_sizes.each do |serving_size|
        serving_discount_price = item_rate_cards.detect{|rc| rc.serving_size_id == serving_size.id }&.price
        if baseline_pricing
          serving_price = serving_size.price
        else
          serving_price = serving_size.markup_price(gst_country: gst_country, override: markup_override)
        end
        serving_size.over_budget = (serving_discount_price || serving_price).round(2) > current_order_budget.round(2)
      end
    end
    if hide_over_budget
      serving_sizes = serving_sizes.reject(&:over_budget)
    elsif current_order_budget.present?
      json.over_budget serving_sizes.all?(&:over_budget)
    end
  end
  serving_sizes = serving_sizes.sort_by(&:weight)
  item_has_promotion = false

  json.gst_inc_pricing team_order_pricing?
  json.hide_pricing team_order_pricing? && !show_pricing?

  if serving_sizes.present?
    json.serving_sizes serving_sizes.each do |serving_size|
      if baseline_pricing
        serving_price = number_with_precision(serving_size.price, precision: 2)
      else
        serving_markup_price = serving_size.markup_price(gst_country: gst_country, override: markup_override)
        serving_price = number_with_precision(serving_markup_price, precision: 2)
      end
      json.extract! serving_size, :id, :name, :over_budget
      json.price serving_price
      display_prices << serving_price

      if item_rate_cards.present?
        discount_pricing = item_rate_cards.detect{|rc| rc.serving_size_id == serving_size.id }
        if discount_pricing.present?
          discount_price = number_with_precision(discount_pricing.price, precision: 2)
          json.discount discount_price
        else
          json.discount nil
        end
      end
    end
  else
    if baseline_pricing
      item_price = number_with_precision(menu_item.price, precision: 2)
    else
      item_markup_price = menu_item.markup_price(gst_country: gst_country, override: markup_override)
      item_price = number_with_precision(item_markup_price, precision: 2)
    end
    json.price item_price
    display_prices << item_price

    case
    when item_rate_cards.present?
      discount_pricing = item_rate_cards.first
      if discount_pricing.present?
        discount_price = number_with_precision(discount_pricing.price, precision: 2)
        json.discount discount_price
      else
        json.discount nil
      end
    when menu_item.promo_price.present?
      if baseline_pricing
        discount_price = menu_item.promo_price
      else
        discount_price = menu_item.markup_price(gst_country: gst_country, override: markup_override, promotional: true)
      end
      json.discount number_with_precision(discount_price, precision: 2)
      item_has_promotion = true
    end

    if team_order_pricing? && current_order_budget.present? && !hide_over_budget
      json.over_budget (discount_pricing || item_markup_price).round(2) > current_order_budget.round(2)
    end
  end

  display_prices = display_prices.reject(&:blank?).sort_by(&:to_f).uniq
  if display_prices.size > 1
    json.display_price "#{number_to_currency(display_prices.first, precision: 2)} - #{number_to_currency(display_prices.last, precision: 2)}"
  else
    json.display_price number_to_currency(display_prices.first, precision: 2)
  end

  json.has_promotion item_has_promotion

  menu_extra_sections = menu_item.menu_extra_sections.select{|menu_extra_section| menu_extra_section.archived_at.blank? }.sort_by(&:weight)
  if menu_extra_sections.present?
    json.menu_extra_sections menu_extra_sections.map do |menu_extra_section|
      json.partial! 'menu_extra_sections/menu_extra_section', menu_extra_section: menu_extra_section
    end
  end

  dietaries = dietary_preferences(menu_item: menu_item)
  if dietaries.present?
    json.dietaries dietaries.values.map{|preference| preference[:letter] }
  end
end
