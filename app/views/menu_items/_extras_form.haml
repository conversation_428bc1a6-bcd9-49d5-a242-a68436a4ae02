:ruby
  markup_override = @supplier_menu.markup_override
  gst_country = team_order_pricing? ? request_country_code : nil

%li.menu-item-modal-list-item
  %span.cart-item-name{ style: 'font-size:90%;' }
    = menu_extra.name
  %span.cart-item-price
    - if show_pricing?
      %strong
        = number_to_currency(menu_extra.markup_price(gst_country: gst_country, override: markup_override), precision: 2)

  .extras-input.value-counter.clearfix
    %input.count-input.menu-extra-checbox{ type: 'checkbox', value: '', disabled: cannot_select_menu_item?, data: { menu_extra_id: menu_extra.id, prev_val: '0' } }
