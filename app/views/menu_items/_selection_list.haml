:ruby
  markup_override = @supplier_menu.markup_override
  gst_country = team_order_pricing? ? request_country_code : nil
  
%ul.menu-item-modal-list
  - if serving_sizes.present?
    - serving_sizes.each do |serving_size|
      :ruby
        serving_price = serving_size.markup_price(gst_country: gst_country, override: markup_override)
        servings_rate_card = item_rate_cards.detect{|rc| rc.serving_size_id == serving_size.id }
        if servings_rate_card.present?
          serving_discount_price = team_order_pricing? ? servings_rate_card.price_inc_gst(gst_country: gst_country) : servings_rate_card.price
        else
          serving_discount_price = nil
        end
        is_within_budget = current_order_budget.blank? || ( (serving_discount_price || serving_price).round(2) <= current_order_budget.round(2) )
      %li.menu-item-modal-list-item.serving-size-item{ class: (!is_within_budget ? 'out-of-budget' : ''), data: { serving_size_id: serving_size.id } }
        = render 'menu_items/selection_form', name: serving_size.name, set_default_value: serving_sizes.size == 1, price: serving_price, discount_price: serving_discount_price, is_within_budget: is_within_budget
  - else
    :ruby
      item_price = menu_item.markup_price(gst_country: gst_country, override: markup_override)
      if item_rate_card = item_rate_cards.present? && item_rate_cards.first.presence
        item_discount_price = team_order_pricing? ? item_rate_cards.first.price_inc_gst(gst_country: gst_country) : item_rate_cards.first.price
      elsif menu_item.promo_price
        item_discount_price = menu_item.markup_price(gst_country: gst_country, override: markup_override, promotional: true)
      else
        item_discount_price = nil
      end
      is_within_budget = current_order_budget.blank? || ( (item_discount_price || item_price).round(2) <= current_order_budget.round(2) )
    %li.menu-item-modal-list-item.serving-size-item{ class: (!is_within_budget ? 'out-of-budget' : ''), data: { serving_size_id: nil } }
      = render 'menu_items/selection_form', name: menu_item.name, set_default_value: true, price: item_price, discount_price: item_discount_price, maximum_quantity: maximum_available_quantity_for(menu_item), is_within_budget: is_within_budget
