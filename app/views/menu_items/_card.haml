:ruby
  letter_icons = item_letter_icons(menu_item: menu_item)
  has_image = menu_item.image?
  item_rate_cards ||= []
  markup_override = @supplier_menu.markup_override
  do_not_cache_card = @supplier.try(:woolworths?) || params[:is_preview].present?
  cache_key = [
    can_favourite, 
    is_favourite, 
    cannot_select_menu_item?, 
    team_order_pricing?, 
    current_order_budget, 
    show_pricing?, 
    item_rate_cards.map(&:id), 
    menu_item.cache_key, 
    markup_override&.cache_key,
  ]
  add_margin = current_user.blank? && letter_icons.blank?
  icon_class = is_favourite ? 'hr-icon' : 'he-icon'

- cache_unless(do_not_cache_card, cache_key) do
  :ruby
    serving_sizes = menu_item.serving_sizes.where(archived_at: nil).order(:weight)
    serving_sizes = serving_sizes.where(available_for_team_order: true) if team_order_pricing?
    menu_extras = menu_item.menu_extras.where(archived_at: nil).order(:weight)
    if team_order_pricing? && current_order_budget.present?
      is_within_budget = pricing_within_budget(budget: current_order_budget, menu_item: menu_item, serving_sizes: serving_sizes, rate_cards: item_rate_cards, markup_override: markup_override)
    else
      is_within_budget = true
    end

  .menu-item{ id: "menu-item-#{menu_item.id}", class: (!is_within_budget ? 'out-of-budget' : ''), data: { menu_item_id: menu_item.id, reveal_id: "menu-item-modal-#{menu_item.id}" } }
    - if !is_within_budget
      %p.out-of-budget__text Over Budget
    .item-heading
      %h4.item-title
        = menu_item.name.truncate(40)
      - if can_favourite
        %i.favourite{ class: icon_class, data: { url: api_favourite_menu_item_path(menu_item) } }

    .item-details
      .description-detail{ class: (has_image ? 'with-image' : '') }
        %p.item-description{class: add_margin ? 'no-icons-margin' : '' }
          = menu_item.description.strip.truncate( has_image ? 70 : 130) if menu_item.description.present?

        .icons-and-fav
          .menu-icons
            - if letter_icons.present?
              =raw letter_icons

      - if has_image
        - image_tooltip_url = Cloudinary::Utils.cloudinary_url(menu_item.ci_image, width: 260, height: 260, crop: 'fill')
        .menu-item-image-container
          .item-preview.hide-for-small-only
            = resize_image_tag(menu_item.ci_image, menu_item.name, 110, 110, 'fill')

    - if show_pricing?
      = render 'menu_items/card_pricing', menu_item: menu_item, serving_sizes: serving_sizes, item_rate_cards: item_rate_cards

  = render 'menu_items/modal', menu_item: menu_item, serving_sizes: serving_sizes, menu_extras: menu_extras, item_rate_cards: item_rate_cards, letter_icons: letter_icons
