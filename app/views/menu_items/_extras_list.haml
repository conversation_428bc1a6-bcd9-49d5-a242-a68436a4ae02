:ruby
  grouped_extras = menu_extras.group_by(&:menu_extra_section).sort_by{|extra_section, _| extra_section.present? && extra_section.weight ? extra_section.weight : -1 }

- if team_order_pricing?
  %small (pricing inc gst)

- grouped_extras.each_with_index do |(extra_section, section_extras), idx|
  :ruby
    limits = [
      extra_section.min_limit.present? && "at least #{extra_section.min_limit}",
      extra_section.max_limit.present? && "max #{extra_section.max_limit}"
    ].reject(&:blank?).join(', ')
  %p
    - if extra_section&.name.present?
      %strong
        = grouped_extras.size > 1 ? "#{idx + 1}. " : ' '
        #{extra_section.name}
    - if extra_section.min_limit.present? || extra_section.max_limit.present?
      %small
        (#{limits})

  %ul.menu-item-modal-list.menu-extras-list{ data: { limit: extra_section&.max_limit  } }
    - section_extras.each do |menu_extra|
      = render 'menu_items/extras_form', menu_extra: menu_extra


