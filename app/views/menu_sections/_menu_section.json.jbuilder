favourite_menu_items ||= []
grouped_rate_cards ||= {}
hide_over_budget ||= false

json.extract! menu_section, :id, :name, :group_name
json.position menu_section.weight

with_rate_cards, without_rate_cards = menu_items.partition{|item| grouped_rate_cards.keys.include?(item.id) }
sorted_menu_items = with_rate_cards + without_rate_cards
json.menu_items sorted_menu_items.each do |menu_item|
  json.partial! 'menu_items/menu_item', menu_item: menu_item, is_favourite: favourite_menu_items.include?(menu_item), item_rate_cards: grouped_rate_cards[menu_item.id] || [], hide_over_budget: hide_over_budget
end

if params[:for_cache].present?
  json.visible_to menu_section.company_ids.presence || nil
else
  json.categories menu_section.categories.map(&:slug)
end
