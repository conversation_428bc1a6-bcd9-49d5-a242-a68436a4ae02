%nav.menu-section-group-navigation.clearfix.menu-navigation
  %ul.menu-section-list.clearfix
    - grouped_menu_sections.keys.each_with_index do |group_name, gidx|
      - group_name ||= "Group #{gidx}"
      - if group_name.present?
        %li.menu-section-item{ id: "scroll-menu-section-group-#{group_name}" }
          %a.menu-section-link.menu-section-group-link{ href: 'javascript:void(0);', class: (gidx == 0 ? 'menu-section-group-link-selected' : ''), data: { reveal_class: "menu-section-group-#{group_name.try(:parameterize)}", nav_id: "group-#{gidx}" } }
            = group_name

    %li.more-button-wrapper
      .more-button.hidden
        %a.menu-section-link.more-button-link{ href: 'javascript:void(0);', data: { toggle: 'menu-navigation-section-group-dropdown' } }
          %span More
          = render 'partials/chevron'

        .dropdown-pane.drop.bottom.list-dropdown.text-left{ id: 'menu-navigation-section-group-dropdown', data: { close_on_click: 'true', dropdown: ''} }
          %ul.dropdown-pane__list

- grouped_menu_sections.each_with_index do |(group_name, menu_sections), index|
  - group_name ||= "Group #{index}"
  - if group_name.present?
    = render 'menu_sections/navigation', group_name: group_name, menu_sections: menu_sections, selected_menu_section: menu_sections.first, hidden_by_default: index > 1
