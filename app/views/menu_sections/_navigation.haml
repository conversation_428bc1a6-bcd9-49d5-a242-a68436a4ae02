:ruby
  grouped_nav = defined?(group_name)
  hidden_by_default ||= false
  nav_class = grouped_nav ? "menu-section-group-#{group_name.try(:parameterize)} second-level-navigation" : ''
  nav_class += hidden_by_default ? ' hidden' : ''
%nav.clearfix.menu-navigation{ class: nav_class }
  .medium-9.columns.menu-section-list-wrapper
    %ul.menu-section-list.clearfix
      - menu_sections.each do |menu_section|
        %li.menu-section-item
          - if grouped_nav
            %a.menu-section-link.menu-section-item-link{ href: api_menu_section_path(menu_section, supplier_profile_id: @supplier.id), class: (menu_section == selected_menu_section ? 'menu-section-link-selected' : '') }
              %span= menu_section.name
          - else
            %a.menu-section-link{ href: "#menu-section-#{menu_section.id}", class: (menu_section == selected_menu_section ? 'menu-section-link-selected' : '') }
              %span= menu_section.name

      %li.more-button-wrapper
        .more-button.hidden
          - toggle_class = 'menu-section-dropdown'
          - toggle_class = "menu-section-#{group_name.try(:parameterize)}-dropdown" if grouped_nav
          %a.menu-section-link.more-button-link{ href: 'javascript:void(0);', data: { toggle: toggle_class } }
            %span More
            = render 'partials/chevron'

          .dropdown-pane.drop.bottom.list-dropdown.text-left{ id: toggle_class, data: { close_on_click: 'true', dropdown: ''} }
            %ul.dropdown-pane__list

  .medium-3.columns
    - if grouped_nav
      .menu-items-search
        %input.search-input.menu-items-search__search-input{ type: 'search', placeholder: 'Search menu items', data: { url: api_menu_section_path(-2, supplier_profile_id: @supplier.id) } }/
    - else
      .menu-items-search
        %input.search-input.menu-items-search__search-input{ name: '', placeholder: 'Search menu items', type: 'search'}/
