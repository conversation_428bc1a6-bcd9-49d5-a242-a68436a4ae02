- grouped_menu_items.each do |menu_section, menu_items|
  .menu-section.small-12.columns.no-gutter
    %h3.section-heading{ id: "menu-section-#{menu_section.id}" }
      = menu_section.name
    .menu-item-list
      - menu_items.each do |menu_item|
        = render 'menu_items/card', menu_item: menu_item, can_favourite: can_favourite, is_favourite: menu_section.is_favourite_section? || favourite_menu_items.include?(menu_item), item_rate_cards: (@supplier_menu.grouped_rate_cards.present? && @supplier_menu.grouped_rate_cards[menu_item.id].presence) || []
