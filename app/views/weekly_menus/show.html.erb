<% content_for :title, @menu_client.navigation_title %>

<!-- MenuClient -->
<section class="banner-section has-gutter has-custom-bg-image"
  style="background-image: url('<%= "#{menu_client_header_image_url(@menu_client)}" %>');">
  <div class="row v-align-row">
    <div class="medium-7 large-7 medium-centered columns v-align-cell">
      <div class="banner-cta-block custom-menu-banner text-center">
        <%= image_tag @menu_client.logo.url, class: "MenuClient__logo" %>

        <h1 class="block-heading"><%= @menu_client.title %>'s Weekly Menu</h1>

        <div class="MenuClient__heading-content">
          <%= raw @menu_client.header_content %>
        </div>
      </div>
    </div>
  </div>
</section>


<%- if @menu_fetcher.any_menus? %>
  <%= render partial: "weekly_menus/menu_header", locals: { menu_fetcher: @menu_fetcher, menu_client: @menu_client } %>
  <%= render partial: "weekly_menus/menu", locals: { menu_fetcher: @menu_fetcher } %>
<% else %>
  <div class="Menu__abbreviations">
    <span>
      No menus have been setup for <%= @menu_client.title %> yet. Please check back soon!
    </span>
  </div>
<% end %>
