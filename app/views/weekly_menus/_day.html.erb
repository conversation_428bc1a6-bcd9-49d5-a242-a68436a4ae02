<%- if menu.weekly_menu_client.send(day) %>
  <section class="has-gutter text-center <%= alt_class %>">
    <div class="row v-align-row">
      <h2><%= day.to_s.capitalize %></h2>

      <%= raw menu.send(day) %>

      <p>Supplied by: <%= menu.send("#{day}_supplier".to_sym).try(:name) %></p>

      <hr>

      <% reviews = WeeklyMenuReview.where(weekly_menu_id: menu.id).send(day) %>
      <div class="menu-rating">
        <% if reviews.count > 0 %>
          <% calc = MenuScoreCalculator.new(reviews) %>
          <i class='star selected'></i>
          <span><%= calc.overall_rating.to_f %></span>
          <span>from <%= reviews.count %> <%= "Rating".pluralize(reviews.count) %></span>
        <% else %>
          No reviews
        <% end %>
      </div>

      <% if can_rate_this_meal?(menu.week_of, day) %>
        <%= link_to "Rate This Meal",
        new_weekly_menu_review_path(weekly_menu_id: menu.id, day: day),
        class: "button" %>
      <% else %>
        This meal has not been served yet, and cannot be rated.
      <% end %>
    </div>
  </section>
<% end %>