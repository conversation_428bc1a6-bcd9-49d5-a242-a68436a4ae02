:ruby
  template_name ||= preference.template_name
  email_template ||= EmailTemplate.where(name: template_name).first
  form_url = preference.new_record? ? api_notification_preferences_path : api_notification_preference_path(preference)
  form_method = preference.new_record? ? 'POST' : 'PUT'

  has_overrides = preference.active? && email_template.can_override? && (preference.email_recipients.present? || preference.salutation.present?)
  pottential_preference =  Notifications::FetchPotentialCustomerPreference.new(customer: preference.account, template_name: template_name).call

  translation_key = "notification_preference.#{template_name}"
  translation_key += ".#{preference.variation}" if preference.variation.present?

  active_checkbox_key = "preference-#{template_name}-active"
  active_checkbox_key += "-#{preference.variation}" if preference.variation.present?

= form_for preference, url: form_url, html: { class: 'notification-preference', data: { method: form_method, preference_id: preference.id, template: template_name } } do |f|
  = f.hidden_field :template_name
  - if preference.variation.present?
    = f.hidden_field :variation
  .row
    .small-12.medium-8.columns.no-gutter
      %p.notification-pref__heading
        = I18n.t("#{translation_key}.label", default: translation_key)
        %span.notification-pref__loading.hidden
          %span.sk-three-bounce
            %span.sk-child.sk-bounce1
            %span.sk-child.sk-bounce2
            %span.sk-child.sk-bounce3

      %p.notification-pref__description
        = I18n.t("#{translation_key}.description", default: 'description')

    .small-12.medium-4.columns.text-right
      %label{ for: active_checkbox_key }
        .section-toggle
          = f.check_box :active, { id: active_checkbox_key }
          %span.section-toggle__switch
      - if email_template.can_override?
        %a.toggle-overrides{ class: (has_overrides || !preference.active? ? 'hidden' : '') }
          show overrides

  - if email_template.can_override?
    .row.preference-overrides{ class: (has_overrides ? '' : 'hidden') }
      .small-12.medium-6.columns.no-gutter
        %label Email Recipient(s)
        = f.text_field :email_recipients, class: 'form-input', placeholder: pottential_preference.email_recipients

      .small-12.medium-6.columns
        %label Salutation
        = f.text_field :salutation, class: 'form-input', placeholder: pottential_preference.email_salutation

