:ruby
  webpack_name = session_profile.present? && session_profile.class.name == 'CustomerProfile' ? 'customer' : 'supplier'

- content_for :header_title, 'Notification Preferences'
- content_for :webpack, webpack_name

.dashboard-container
  .notification-pref{ data: { view_notification_preferences: true } }
    - templates_with_preferences.each do |heading, templates|
      %ul.list-unstyled
        %li
          %p.notification-pref__title
            = heading
        - templates.each do |email_template|
          - template_name = email_template.name
          - if variations = email_template.variations.presence
            - variations.each do |variation|
              - preference = @notification_preferences.detect{|preference| preference.template_name == template_name && preference.variation == variation }
              - preference ||= session_profile.notification_preferences.new(template_name: template_name, variation: variation)
              %li.row.notification-pref__row
                = render 'notification_preferences/notification_preference', preference: preference, email_template: email_template
          - else
            - preference = @notification_preferences.detect{|preference| preference.template_name == template_name }
            - preference ||= session_profile.notification_preferences.new(template_name: template_name)
            %li.row.notification-pref__row
              = render 'notification_preferences/notification_preference', preference: preference, email_template: email_template
