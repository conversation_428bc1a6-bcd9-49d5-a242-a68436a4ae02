:ruby
  is_account_manager ||= false
  suburb_label = billing_details.suburb ? billing_details.suburb.label : ''
  invoice_email_override = session_profile&.notification_preferences.find_by(template_name: 'customer-order_invoice')&.email_recipients

%fieldset.dashboard-container.dashboard-container__form#customer-billing-details
  %p.dashboard-container__title Billing Details
  .row
    .small-12.medium-6.columns
      %label Company Billing Name
      = text_field_tag 'billing_details[name]', billing_details.name, required: !is_account_manager, placeholder: 'Company name', label: false, class: 'form-input'
      %span.form-error Please enter a company name

    .small-12.medium-6.columns
      %label Phone
      = text_field_tag 'billing_details[phone]', billing_details.phone, required: !is_account_manager, type: 'text', placeholder: 'Phone', label: false, class: 'form-input'
      %span.form-error Please enter a valid billing phone number

  .add-new-address-box
    .row
      .small-12.medium-6.columns
        %label Billing Address
        = text_area_tag 'billing_details[address]', billing_details.address, required: !is_account_manager, placeholder: 'Address', class: 'form-input', rows: 3
        %span.form-error Please enter a valid billing street address

      .small-12.medium-6.columns
        %label Billing suburb
        = text_field_tag 'postcode', suburb_label, placeholder: 'Enter your delivery postcode', autocomplete: 'off', required: !is_account_manager, class: ' text-input form-input', data: { view_autocomplete_input: autocomplete_config.merge({ target_name: 'billing_details[suburb_id]' }).to_json }
        %span.form-error Please enter a valid billing suburb

        = hidden_field_tag 'billing_details[suburb_id]', billing_details.suburb ? billing_details.suburb.id : ''

    .row
      .small-12.columns
        %label
          Email(s)
          %span.tool{ tabindex: 1, data: { tip: 'Multiple recipients can be separated by a ";"' } }
            ℹ️
        = text_field_tag 'billing_details[email]', billing_details.email, required: !is_account_manager, placeholder: 'Email Address(es)', class: 'form-input'
        %span.form-error Please enter billing email adddresses

    - if invoice_email_override.present?
      .row
      .small-12.columns
        %p{style: 'font-size: 14px; border: 1px solid #1f9e86; padding: 12px'}
          Note: Due to your overwrite in
          %a{href: customer_notification_preferences_path, style: "font-weight: bold"}Notification Preferences
          your invoices will only be sent to 
          %span{style: 'font-weight: bold'} #{invoice_email_override}
