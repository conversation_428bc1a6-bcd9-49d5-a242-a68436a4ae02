:ruby
  customer = session_profile
  admin_options = is_admin? && [
    :team_admin,
    :company_team_admin,
  ]

  customer_requirement_options = is_admin? && [
    :cancel_review_requests,
    :has_gst_split_invoicing,
    :requires_department_identity,
    :requires_supplier_markup,
    :requires_loading_dock_code,
    :default_orders_view,
    :accounting_software
  ]

  update_url = api_customer_path(customer, format: :json)
  translation_key = "customer_profiles.settings"

- content_for :header_title, 'Customer Settings'
- content_for :container_class, 'billing-formx'
- content_for :webpack, 'customer'

.customer-area-container{ data: { view_user_flags: true } }
  .dashboard-container
    .notification-pref
      %ul.list-unstyled
        - if admin_options.present?
          %li
            %p.notification-pref__title
              Customer Admin Settings
              - admin_options.each do |field|
                %li.row.notification-pref__row
                  = render 'partials/tag_form', object: customer, field: field, update_url: update_url, translation_key: translation_key

        - if customer_requirement_options.present?
          %li
            %p.notification-pref__title
              Customer Specific Settings
              - customer_requirement_options.each do |field|
                %li.row.notification-pref__row
                  = render 'partials/tag_form', object: customer.customer_flags, field: field, update_url: update_url, translation_key: translation_key