:ruby
  if recent_order.present?
    delivery_at = recent_order.delivery_at
    formatted_delivery_at = delivery_at.strftime("%l:%M%P %a #{delivery_at.day.ordinalize} %b")
    recent_order_path = recent_order.is_team_order? ? team_order_path(recent_order) : order_show_path(recent_order)
  end
  place_order_link = cookie_suburb.present? ? next_app_supplier_show_url(supplier&.slug) : supplier_search_url

.supplier-card{ class: "supplier-card__#{category}" }
  .supplier-image
    = resize_image_tag(supplier.profile.avatar, supplier.company_name, 800, 440, 'fill')
  .supplier-details
    .supplier-title
      %h3.supplier-name= supplier.company_name
    - if recent_order.present? && recent_order.delivery_at < Time.zone.now
      %a.my-supplier-delivery.my-supplier-delivery--none{ href: recent_order_path  }
        Last Delivery: #{formatted_delivery_at}
    - elsif recent_order.present?
      %a.my-supplier-delivery.my-supplier-delivery--next{ href: recent_order_path }
        Next Delivery: #{formatted_delivery_at}
    - else
      %p.my-supplier-delivery.my-supplier-delivery--none No Recent Orders
    .my-supplier-buttons
      %a.button.black-btn{ href: place_order_link }
        Place Order

      %a.button{ style: 'background: white; color: black; border: 1px solid black;', href: customer_profile_path(supplier_ids: [supplier.id], supplier_name: supplier.company_name, show_past: (recent_order.blank? || recent_order.delivery_at < Time.zone.now) ? true : nil) }
        View Orders
