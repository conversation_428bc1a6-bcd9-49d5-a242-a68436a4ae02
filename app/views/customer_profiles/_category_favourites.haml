:ruby
  category_group = category === 'catering' ? 'office-catering' : 'office-snacks'
  supplier_search_url = cookie_suburb.present? ? next_app_supplier_search_url(category_group: category_group, state: cookie_suburb&.state, suburb: cookie_suburb&.name&.gsub(/\s/, '-')) : "#supplier-search-#{category_group}"

.favourite-suppliers-options
  %h3.my-suppliers-category= category.titleize 
  %input.favourites-filter{ type: 'text', placeholder: 'Search', id: "search-box-#{category}", data: { category: category } }
  %a.favourite-suppliers-show-all{ href: supplier_search_url }
    View All Suppliers
  - if has_multiple_category_favourites
    = link_to 'Catering Suppliers', customer_my_suppliers_path, {class: (category == 'catering' ? 'button catering' : 'button hollow catering')}
    = link_to 'Snacks Suppliers', customer_my_suppliers_path(category: 'snacks'), {class: (category == 'snacks' ? 'button snacks' : 'button hollow snacks')}

- if suppliers.present?
  .favourite-suppliers
    - sorted_supplier_orders_for(suppliers, @recent_orders).each do |supplier, recent_order|
      = render 'customer_profiles/supplier_card', supplier: supplier, recent_order: recent_order, category: category, supplier_search_url: supplier_search_url
  %h3.no-results-message.hidden.catering No Suppliers Found
