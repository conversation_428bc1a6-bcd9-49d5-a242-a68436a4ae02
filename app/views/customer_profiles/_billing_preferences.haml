:ruby
  frequencies = ['instantly']
  frequencies += %w[weekly monthly] if customer.can_pay_on_account?
  frequencies << billing_details.frequency if billing_details.frequency.present?
  customer_billing_frequency_options = frequencies.uniq.map do |frequency|
    [I18n.t("order.billing_frequency.#{frequency}"), frequency]
  end

  customer_billing_day_options = (1..31).to_a.map do |num|
    ["#{num.ordinalize} of the month", num]
  end

  company_flags = {
    can_pay_on_account: 'can pay on Account',
    can_pay_by_credit_card: 'can pay by Credit-Card', 
    requires_po: 'requires PO number',
    invoice_by_po: 'invoiced by PO'
  }

  can_manage_billing_preferences = current_user.present? && (current_user.admin? || current_user.super_admin?)
  can_manage_super_billing_preferences = current_user.present? && current_user.super_admin?
  invoice_order_grouping_options = BillingDetails::VALID_INVOICE_ORDER_GROUPINGS.map{|x| [x.titleize, x] }

%fieldset.dashboard-container.dashboard-container__form
  %p.dashboard-container__title Billing Preferences

  .row
    .small-12.medium-6.columns
      %label Billing Frequency
      = select_tag 'billing_details[frequency]', options_for_select(customer_billing_frequency_options, billing_details.frequency), { class: 'form-input' }
      - if customer.can_pay_on_account?
        %small.checkout-hint
          Note: Customer can Pay on Account

    - if billing_details.frequency == 'monthly' && current_user.present? && current_user.super_admin?
      .small-12.medium-6.columns
        %label
          Billing Day (Not in Use yet!)
          %small
            (defaults to 1st of the month)
        = select_tag 'billing_details[billing_day]', options_for_select(customer_billing_day_options, billing_details.billing_day), { include_blank: true, class: 'form-input' }

  - if can_manage_billing_preferences
    .row
      .small-12.columns
        %strong Yordar Admin Only Settings
      .small-12.medium-6.columns
        %label Invoice Order Grouping
        = select_tag 'billing_details[invoice_order_grouping]', options_for_select(invoice_order_grouping_options, billing_details.invoice_order_grouping), { class: 'form-input' }

      - if company = customer.company.presence
        .small-12.medium-6.columns
          %label
            Company settings (#{company.name})
          %ul.bullet-list
            - company_flags.each do |field, label|
              - next if !company.send(field)
              %li.float-left.mr-2
                %strong
                  = label
    .row
      - CustomerProfile::BILLING_NEEDS_FIELDS.each do |field|
        .small-12.medium-6.columns
          %label
            .toggle-checkbox
              = hidden_field_tag "billing_details[#{field}]", false
              = check_box_tag "billing_details[#{field}]", true, billing_details.send(field)
              %span.toggle-checkbox__switch
            %span.ml-1-4{ style: 'vertical-align: bottom;', title: I18n.t("customer_profiles.billing_details.#{field}.description"), data: { view_tooltip_el: true } }
              = I18n.t("customer_profiles.billing_details.#{field}.label")
              %span.icon-info-circle

      .small-12.medium-6.columns
        %label
          .toggle-checkbox
            = hidden_field_tag 'billing_details[hide_delivery_pricing]', false
            = check_box_tag 'billing_details[hide_delivery_pricing]', true, billing_details.hide_delivery_pricing?
            %span.toggle-checkbox__switch
          %span.ml-1-4{ style: 'vertical-align: bottom;' }
            Hide Delivery Details in Bulk Order Invoices
