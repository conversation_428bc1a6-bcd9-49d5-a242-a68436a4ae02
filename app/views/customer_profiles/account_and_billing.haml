:ruby
  customer = session_profile
  billing_details = customer.billing_details.presence  
  billing_details ||= customer.build_billing_details(
    name: customer.company_name,
    phone: customer.contact_phone
  )
  autocomplete_config = {
    path: api_suburbs_path,
    countryCode: request_country_code
  }
  is_account_manager = is_account_manager?(user: customer.user)

- content_for :header_title, 'Account Details'

= form_for customer, method: 'PUT', url: customer_profile_update_path(customer), html: { class: 'update_customer_profile' }, data: { abide: '', view_customer_settings: 'true' } do |f|
  
  = render 'customer_profiles/account_details', customer: customer, autocomplete_config: autocomplete_config, f: f, is_account_manager: is_account_manager
  = render 'customer_profiles/billing_details', customer: customer, billing_details: billing_details, autocomplete_config: autocomplete_config, is_account_manager: is_account_manager
  = render 'customer_profiles/billing_preferences', customer: customer, billing_details: billing_details

  .form-footer.clearfix
    = button_tag 'Save', class: 'button small float-right'
    %a.button.small.cancel.float-right{ href: customer_profile_path }
      Cancel

