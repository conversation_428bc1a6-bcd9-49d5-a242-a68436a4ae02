- content_for :header_title, 'Credit Cards'
- content_for :container_class, 'dashboard-table'

%div{ data: { view_customer_payment_options: true } }

  %fieldset{ style: 'box-shadow: none; border: none;' }
    = render 'credit_cards/list'
  .row
    .columns.small-12
      %a.add-new-credit-card-btn.button Add New Card

  .row.new-credit-card-container.credit-card-form-container.hidden{ data: { view_stripe_card_form: true } }
    .small-12.medium-8.columns
      .add-new-credit-card-box.group
        = render 'credit_cards/stripe_form', show_buttons: true, default_save_for_future: true, context: 'payment-options'
