:ruby
  supplier_search_url = cookie_suburb.present? ? next_app_supplier_search_url(category_group: 'office-catering', state: cookie_suburb.state, suburb: cookie_suburb.name.gsub(/\s/, '-')) : '#supplier-search-office-catering'

- content_for :header_title, 'My Suppliers'

- if @shown_suppliers.blank?
  .no-favourites
    = image_tag("illustrations/search.svg")
    %p You don't currently have any favourited suppliers. You can favourite a supplier by clicking the heart on a supplier card.  
    = link_to 'Go To Suppliers', supplier_search_url, class: 'button black-btn'

- else
  %p.my-suppliers-info-message Your Favourited Suppliers. You can remove and add favourites from the suppliers listing page.
  %div{ data: { view_customer_fav_suppliers: 'true' } }
    = render 'customer_profiles/category_favourites', suppliers: @shown_suppliers[:suppliers], category: @shown_suppliers[:category], has_multiple_category_favourites: @has_multiple_category_favourites
