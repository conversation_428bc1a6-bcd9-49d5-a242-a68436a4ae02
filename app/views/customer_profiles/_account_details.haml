:ruby
  user ||= customer.user
  is_account_manager ||= false

%fieldset.dashboard-container.dashboard-container__form
  %p.dashboard-container__title Contact Details
  = f.fields_for :user do |uf|
    .row
      .small-12.medium-6.columns
        %label First Name
        = uf.text_field :firstname, required: true, class: 'form-input'
        %span.form-error Please enter a first name

      .small-12.medium-6.columns
        %label Last Name
        = uf.text_field :lastname, required: true, class: 'form-input'
        %span.form-error Please enter a last name

    .row
      .small-12.medium-6.columns
        %label Email
        = uf.email_field :email, required: true, class: 'form-input'
        %span.form-error Please enter a valid email address
        - if user.unconfirmed_email.present?
          %span.checkout-hint
            Note: Email change to 
            %u= user.unconfirmed_email
            needs confirmation. Please check your email.

      .small-12.medium-6.columns
        %label
          Secondary Email
          %span.tool{ tabindex: 1, data: { tip: 'Receives a copy of the emails sent to the default email address. Multiple recipients can be separated by a ";"' } }
            ℹ️
        = uf.text_field :secondary_email, class: 'form-input'
    .row
      .small-12.medium-6.columns
        %label Suburb or postcode
        = text_field_tag 'postcode', uf.object.try(:suburb).try(:label), class: 'user-suburb text-input form-input', autocomplete: 'off', data: { view_autocomplete_input: autocomplete_config.merge({ target_name: 'customer_profile[user_attributes][suburb_id]' }).to_json }

        = uf.hidden_field :suburb_id, class: 'user-suburb-id'

  .row
    .small-12.medium-6.columns
      %label Company
      = f.text_field :company_name, class: 'form-input'
    .small-12.medium-6.columns
      %label Role
      = f.select :role, CustomerProfile::VALID_ROLES, { include_blank: 'Please select' }, { class: 'form-input', required: true }
      %span.form-error Please select a role
      
  .row
    .small-12.medium-6.columns
      %label Contact No.
      = f.text_field :contact_phone, required: true, class: 'form-input'
      %span.form-error Please enter a contact phone number

    .small-12.medium-6.columns
      %label Mobile
      = f.text_field :mobile, class: 'form-input'
  .row
    .small-12.medium-6.columns
      %label Reset Password
      = link_to 'Change your password', change_password_path, class: 'password-link button small'

  - if is_account_manager
    .row
      .medium-5.columns.supplier-profile-image
        %div
          %p Display Image
          %p.help-text Upload an image that is  at least 230px wide
          %div.supplier-avatar-container
            - if (avatar = customer.profile.avatar.presence)
              = cl_image_tag(avatar)

        - if is_admin?
          = f.fields_for :profile do |p|
            %label.button.small.avatar-upload-button{ for: 'imageUpload', data: { button_text: 'Upload File' } }
              Upload File
            = file_field_tag :avatar_uploader, id: 'imageUpload', class: 'show-for-sr', data: { upload_url: Cloudinary.config.upload_url, upload_preset: Cloudinary.config.upload_preset, target_name: 'customer_profile[profile_attributes][avatar]' }
            = p.hidden_field :avatar
