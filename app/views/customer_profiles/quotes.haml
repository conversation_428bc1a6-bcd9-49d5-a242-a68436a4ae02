:ruby
  customer = session_profile  
  suburb = cookie_suburb || customer.user.suburb
  catering_supplier_url = suburb.present? ? next_app_supplier_search_url(category_group: 'office-catering', state: suburb.state, suburb: suburb.name.gsub(/\s/, '-')) : '#supplier-search-office-catering'
  snack_supplier_url = suburb.present? ? next_app_supplier_search_url(category_group: 'office-snacks', state: suburb.state, suburb: suburb.name.gsub(/\s/, '-')) : '#supplier-search-office-snacks'

  external_order_urls = {
    catering: catering_supplier_url,
    pantry: snack_supplier_url,
    quote: next_app_customer_quotes_url,
    share: next_app_customer_specific_quotes_url(session_profile.uuid)
  }

- content_for :header_title, 'Quotes'

%div{ data: { view_customer_quotes: { externalOrderUrls: external_order_urls, isAdmin: is_admin? }.to_json } }

