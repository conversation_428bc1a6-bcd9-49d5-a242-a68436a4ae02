json.can_process !@supplier_closure_fetcher.has_closures?
json.outside_operating_hours @supplier_closure_fetcher.outside_operating_hours?
json.supplier_closure_dates @supplier_closure_fetcher.closures.each do |closure|
  json.extract! closure.supplier, :id, :name
  if (operating_hours = closure.operating_hours.presence)
    json.outside_operating_hours true
    json.operating_hours closure.operating_hours.map{|hour| hour.to_s(:time_only) }.join(' to ')
  else
    json.outside_operating_hours false
    json.close_from closure.close_from.to_s(:date_verbose)
    json.close_to closure.close_to.to_s(:date_verbose)
  end
end

