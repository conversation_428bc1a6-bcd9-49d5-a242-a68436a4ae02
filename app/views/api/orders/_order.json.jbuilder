json.extract! order, :id, :name, :status, :no_delivery_charge, :delivery_address_level, :delivery_address, :delivery_instruction

json.delivery_at order.delivery_at.present? ? order.delivery_at.strftime('%Y-%m-%d %I:%M %P') : nil
woolworths_order = order.woolworths_order.presence
if woolworths_order.present?
  json.woolworths_orrder_id woolworths_order.id
  json.delivery_window_id woolworths_order.delivery_window_id
  json.delivery_window_text woolworths_order.delivery_window_text
  json.delivery_window_date order.delivery_at.to_s(:date)
end

json.delivery_suburb do
  suburb = order.delivery_suburb
  json.name suburb&.name
  json.state suburb&.state
  json.postcode suburb&.postcode
end

json.totals do
  json.order_line_count totals.order_line_count
  json.subtotal number_to_currency(totals.subtotal)
  json.discount number_to_currency(totals.discount)
  json.delivery number_to_currency(totals.delivery)
  json.topup totals.topup.present? && totals.topup > 0.0 ? number_to_currency(totals.topup) : nil
  json.surcharge number_to_currency(totals.surcharge)
  json.gst number_to_currency(totals.gst)
  json.total number_to_currency(totals.total)
  json.total_number totals.total

  if defined?(team_order_spends) && team_order_spends.present?
    total_spend = team_order_spends.total_spend || 0
    remaining_spend = team_order_spends.remaining_spend || 0
    json.total_spend total_spend > 0 ? number_to_currency(total_spend, precision: 2) : '$0'
    json.remaining_spend remaining_spend > 0 ? number_to_currency(remaining_spend, precision: 2) : '$0'
  end

end