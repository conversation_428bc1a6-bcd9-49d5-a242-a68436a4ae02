include_empty_locations ||= false
attendee ||= nil

lister_options = {
  order: order,
  for_attendee: attendee,
}
order_lines = OrderLines::List.new(options: lister_options, includes: %i[location supplier_profile]).call.order(:id)

if attendee.present?
  totals = Orders::CalculateCustomerTotals.new(order: order, order_lines: order_lines, attendee: attendee).call
  team_order_spends = Orders::GetSupplierSpends.new(order: order, exclude_surcharge: true).call if attendee.is_team_admin?
else
  totals = Orders::RetrieveTotals.new(order: order, order_lines: order_lines, profile: session_profile).call
  team_order_spends = nil
end

json.id order.id
json.name order.name
json.coupon_code attendee.blank? && order.coupon_id.present? ? order.coupon&.code : nil

if is_yordar_admin?
  # order supplier info for delivery fee overrides
  json.orderSuppliers do
    order.supplier_profiles.order(company_name: :asc).map do |supplier|
      json.set! supplier.id do
        default_delivery_fee = Orders::CalculateDelivery.new(order: order, profile: supplier, calculate_default: true).call
        order_supplier = order.order_suppliers.where(supplier_profile: supplier).first_or_initialize
        json.extract! order_supplier, :id, :delivery_fee_override, :supplier_profile_id
        json.supplier_name supplier.name
        json.default_delivery_fee default_delivery_fee
      end
    end
  end
end

json.totals do
  json.order_line_count totals.order_line_count
  json.subtotal number_to_currency(totals.subtotal)
  json.discount number_to_currency(totals.discount)
  json.delivery number_to_currency(totals.delivery)
  json.topup totals.topup.present? && totals.topup > 0.0 ? number_to_currency(totals.topup) : nil
  json.surcharge number_to_currency(totals.surcharge)
  json.gst number_to_currency(totals.gst)
  json.total number_to_currency(totals.total)
  json.total_number totals.total
  if team_order_spends.present?
    total_spend = team_order_spends.total_spend || 0
    remaining_spend = team_order_spends.remaining_spend || 0
    json.total_spend total_spend > 0 ? number_to_currency(total_spend, precision: 2) : '$0'
    json.remaining_spend remaining_spend > 0 ? number_to_currency(remaining_spend, precision: 2) : '$0'
  end
end

json.partial! 'api/order_lines/location_grouped_order_lines', order_lines: order_lines, order: order, include_empty_locations: include_empty_locations
