json.order do
  json.partial! 'api/orders/order', order: order, totals: totals
end

json.totals do 
  json.order_line_count totals.order_line_count
  json.subtotal number_to_currency(totals.subtotal)
  json.discount number_to_currency(totals.discount)
  json.delivery number_to_currency(totals.delivery)
  json.topup totals.topup.present? && totals.topup > 0.0 ? number_to_currency(totals.topup) : nil
  json.surcharge number_to_currency(totals.surcharge)
  json.gst number_to_currency(totals.gst)
  json.total number_to_currency(totals.total)
  json.total_number totals.total
end

json.is_woolworths_order order.reload.woolworths_order.present?
