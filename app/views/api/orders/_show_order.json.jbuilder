order ||= supplier_order.order

totals = Orders::RetrieveTotals.new(order: order, profile: session_profile).call
json.order do
  json.partial! 'api/orders/order', order: order, totals: totals
end

recurrent_orders = supplier_order.recurrent_orders
if recurrent_orders
  json.recurrent_orders recurrent_orders.each do |recurrent_order|
    json.extract! recurrent_order, :id, :name
    json.current recurrent_order == order
  end
end

order_lines = order.order_lines.includes(:location, :supplier_profile).order(:id)
json.order_lines order_lines.each do |order_line|
  json.partial! 'api/order_lines/order_line', order_line: order_line, order: order
end

order_line_locations = order.order_lines.map(&:location).uniq
empty_locations = order.new_record? ? [] : Location.where(order: order).where.not(id: order_line_locations.map(&:id))
locations = order_line_locations + empty_locations
if locations.present?
  json.locations do
    locations.each do |location|
      json.set! location.id do
        json.extract! location, :details
      end
    end
  end
  json.set! :activeLocation, locations.first.id.to_s
else
  json.set! :locations, {}
end

suppliers = order.order_lines.map(&:supplier_profile).uniq
if suppliers.present?
  json.suppliers do
    suppliers.each do |supplier|
      json.set! supplier.id do
        json.extract! supplier, :name
        json.extract! supplier, :slug
      end
    end
  end
else
  json.set! :suppliers, {}
end
json.is_woolworths_order order.woolworths_order.present?
