json.is_under @suppliers_min_spends.any?(&:is_under?)
json.is_single_order @suppliers_min_spends.size == 1
json.suppliers_min_spends @suppliers_min_spends.each do |order_spend|
  json.order_id order_spend.order.id
  json.order_name order_spend.order.name
  json.is_under order_spend.is_under?
  json.supplier_spends order_spend.supplier_spends.each do |supplier_spend|
    json.supplier_id supplier_spend.supplier.id
    json.supplier_name supplier_spend.supplier.name
    json.is_under supplier_spend.is_under?
    json.minimum_spend number_to_currency(supplier_spend.minimum_spend, precision: 0)
    json.total_spend number_to_currency(supplier_spend.total_spend)
    json.remaining_spend number_to_currency(supplier_spend.remaining_spend)
    json.menu_link next_app_supplier_show_url(supplier_spend.supplier&.slug)
  end
end
