delivery_windows_fetcher = Woolworths::Order::FetchSavedDeliveryWindows.new(order: order).call

json.delivery_window_dates delivery_windows_fetcher.delivery_windows.map do |date, date_windows|
  json.date date.to_s(:date)
  json.weekday date.to_s(:weekday)
  json.active date == delivery_windows_fetcher.active_date
  json.delivery_windows date_windows.map do |delivery_window|
    json.id delivery_window.id
    json.label delivery_window.label.gsub(/\s\s/,' ').strip
    json.delivery_at delivery_window.time_range.end.to_s(:datetime)
  end
end
