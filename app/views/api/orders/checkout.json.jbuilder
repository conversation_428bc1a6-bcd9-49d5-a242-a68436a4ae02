json.checkoutErrors @checkout_setup.errors

order = @checkout_setup.order
json.extract! order, :id, :name, :status, :skip, :phone

json.numberOfPeople order.number_of_people
json.poNumber order.po_number
json.cpoId order.cpo_id
json.departmentIdentity order.department_identity
json.deliveryInstruction order.delivery_instruction
json.deliveryAddressLevel order.delivery_address_level
json.deliveryAddress order.delivery_address
json.deliverySuburbId order.delivery_suburb_id
json.deliveryType order.delivery_type
json.contactName order.contact_name
json.companyName order.company_name
json.invoiceIndividually order.invoice_individually
json.creditCardId order.credit_card_id
if (promotion = order.promotion.presence)
  json.promotion do
    json.extract! promotion, :name, :discount_note
  end
end
if (meal_plan = order.meal_plan.presence)
  json.mealPlan do
    json.extract! meal_plan, :name, :kind, :uuid
  end
end
if (delivery_at = order.delivery_at.presence)
  json.deliveryAtDisplay delivery_at.strftime('%-I:%M %p %A, %b %-d, %Y') # 4:30 AM Friday, Apr 5, 2024
  json.deliveryAt delivery_at.strftime('%Y-%m-%d %I:%M %P')
  json.deliveryDate do
    json.year delivery_at.year
    json.month delivery_at.month
    json.day delivery_at.day
  end
  json.deliveryTime do
    json.hours delivery_at.hour
    json.minutes delivery_at.min
    json.display delivery_at.strftime('%-I:%M %p')
  end
else
  json.deliveryAtDisplay nil
  json.deliveryAt nil
  json.deliveryDate nil
  json.deliveryTime nil
end

json.deliverySuburbLabel order.delivery_suburb.present? ? order.delivery_suburb.label : nil
json.isCateringOrder order.order_lines.joins(:category).where(categories: { group: 'catering-services' }).present?
json.isRecurrent order.is_recurrent?

woolworths_order = order.woolworths_order.presence
json.isWoolworthsOrder woolworths_order.present?

if woolworths_order.present?
  json.woolworthsOrderId woolworths_order.id
  json.deliveryWindowId woolworths_order.delivery_window_id
  json.deliveryWindowText woolworths_order.delivery_window_text
end

orders_to_render = order.is_recurrent? ? @checkout_setup.recurrent_orders : [@checkout_setup.order]
order_suppliers = []
json.set! :orders do
  orders_to_render.map do |order_to_render|
    json.set! order_to_render.id do
      json.partial! 'api/orders/checkout_order', order: order_to_render
    end
    order_suppliers += order_to_render.supplier_profiles
  end
end

json.order_suppliers do
  order_suppliers.uniq.map do |supplier|
    json.set! supplier.id do
      json.extract! supplier, :id, :name, :provides_contactless_delivery
      json.menu_link next_app_supplier_show_url(supplier&.slug)
      json.image_id  supplier.profile.avatar
    end
  end
end