can_process_as_admin = params[:order_delivery_at].blank? ? is_admin? : false

json.can_process_lead_time @lead_time_fetcher.can_process? || can_process_as_admin

json.formatted_lead_time @lead_time_fetcher.formatted_lead_time
json.minimum_delivery_at @lead_time_fetcher.minimum_delivery_at
json.formatted_minimum_delivery_at @lead_time_fetcher.minimum_delivery_at.strftime('%d %b %Y, %I:%M%p')

json.is_closed @supplier_closure_fetcher.has_closures?
json.outside_operating_hours @supplier_closure_fetcher.outside_operating_hours?
json.supplier_closure_dates @supplier_closure_fetcher.closures.each do |closure|
  json.extract! closure.supplier, :id, :name
  if (operating_hours = closure.operating_hours.presence)
    json.outside_operating_hours true
    json.operating_hours closure.operating_hours.map{|hour| hour.to_s(:time_only) }.join(' to ')
  else
    json.outside_operating_hours false
    json.close_from closure.close_from.to_s(:date_verbose)
    json.close_to closure.close_to.to_s(:date_verbose)
  end
end
