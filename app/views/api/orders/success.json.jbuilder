order ||= @submitted_order
quote_mode ||= nil
delivery_suburb = order.delivery_suburb
redirect_url ||= ''

json.extract! order, :id, :name, :status, :number_of_people, :po_number, :cpo_id, :department_identity, :delivery_instruction, :delivery_address_level, :delivery_address, :delivery_suburb_id, :delivery_type, :contact_name, :phone, :company_name, :invoice_individually, :credit_card_id
json.is_recurrent order.is_recurrent?
json.order_link order_show_url(order)
json.delivery_at order.delivery_at&.to_s(:full)
json.delivery_address_arr order.delivery_address_arr
geocoder_query = "#{order.delivery_address} #{delivery_suburb&.postcode}"
json.geo_coordinates Geocoder.search(geocoder_query).first.try(:coordinates)
major_order_category = Orders::RetrieveMajorOrderCategory.new(order: order).call
json.supplier_index_page_url next_app_supplier_search_url(category_group: next_app_category_group(major_order_category&.group), state: delivery_suburb&.state, suburb: delivery_suburb&.name&.gsub(/\s/, '-'))

json.customer_name order.customer_profile.name
json.quote_mode quote_mode

if (woolworths_order = order.woolworths_order.presence)
  json.woolworths_order_id woolworths_order.woolworths_order_id
  json.woolworths_account woolworths_order.account.short_name
end
json.redirect_url redirect_url
