json.extract! order, :id, :status, :order_variant, :order_type, :payment_status
json.name order_display_name(order)

json.customer_total number_to_currency(order.customer_total)
delivery_date = order.delivery_at.try(:to_date)
if delivery_date.present?
  if Date.today.cweek == delivery_date.cweek && Date.today.year == delivery_date.year
    formatted_delivery_date = relative_weekday(delivery_date, format: 'short') + order.delivery_at.strftime(' at %l:%M%P')
  else
    formatted_delivery_date = order.delivery_at.strftime("%a #{delivery_date.day.ordinalize} %b at %l:%M%P")
  end
end
json.delivery_at formatted_delivery_date
json.delivery_at_raw order.delivery_at
json.delivery_time order.delivery_at.to_s(:time_only)
json.delivery_day order.delivery_at.to_s(:weekday)
json.delivery_week order.delivery_at.to_date.cweek
json.delivery_date order.delivery_at.to_s(:date_spreadsheet)

suppliers = order.is_team_order? ? order.team_supplier_profiles : order.supplier_profiles
json.is_team_order order.is_team_order?
json.is_woolworths_order order.woolworths_order.present?
json.suppliers suppliers.each do |supplier|
  json.extract! supplier, :id, :name
  json.image cl_image_path(supplier.profile.avatar, width: 100, height: 100, crop: 'fill', raw_transformation: "q_auto,fl_lossy,f_auto")
end

if order.is_team_order?
  json.partial! 'api/orders/list/team_order_links', team_order: order
else
  json.partial! 'api/orders/list/links', order: order, suppliers: suppliers
end
