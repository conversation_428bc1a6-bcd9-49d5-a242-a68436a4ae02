minimum_category = minimum.category

json.id minimum.id
json.spend_price minimum.persisted? ? minimum.spend_price : nil
json.lead_time minimum.persisted? ? minimum.lead_time : nil
json.lead_time_day_before minimum.persisted? ? minimum.lead_time_day_before : nil

json.category_id minimum_category.id
json.category_name minimum_category.name
if @category_grouped_sections.present?
  number_of_menu_sections = @category_grouped_sections[minimum_category]&.size
end
json.number_of_menu_sections number_of_menu_sections.presence || nil
