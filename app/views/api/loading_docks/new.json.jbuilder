order = @loading_dock_request_handler.order
customer = @loading_dock_request_handler.customer
loading_dock = order.loading_dock || customer.loading_docks.new

json.loading_dock do
  json.partial! 'api/loading_docks/loading_dock', loading_dock: loading_dock
end

json.customer do
  json.partial! 'api/loading_docks/customer', customer: customer, include_loading_docks: true
end

json.order do
  json.partial! 'api/loading_docks/order', order: order
end