customer ||= @customer
order ||= @order

billing_details = customer.billing_details
if billing_details.blank?
  billing_details = BillingDetails.new(
    customer_profile_id: customer.id,
    name:  order.company_name,
    phone: order.phone,
    email: customer.user.email,
    suburb_id: cookies[:yordar_suburb_id]
  )
end

json.requires_billing_details !billing_details.persisted?
json.billing_details do
  json.extract! billing_details, :email, :name, :address, :phone, :suburb_id
  if (billing_suburb = billing_details.suburb.presence)
    json.suburb do
      json.extract! billing_suburb, :id, :name, :label, :state, :postcode
    end
  end
end

json.requires_loading_dock_code customer.requires_loading_dock_code
