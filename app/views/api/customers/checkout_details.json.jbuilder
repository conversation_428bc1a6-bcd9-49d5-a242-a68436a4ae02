json.name @customer.name
json.email @customer.user.email
json.billing_preference @customer.billing_frequency || 'instantly'

json.partial! 'api/customers/credit_card_details', customer: @customer, order: @order
json.partial! 'api/customers/purchase_order_details', customer: @customer, order: @order
json.partial! 'api/customers/saved_addresses', customer: @customer, suburb: @order&.delivery_suburb
json.partial! 'api/customers/billing_details', customer: @customer, order: @order
if @order.present? && @order.supplier_profiles.where(id: yordar_credentials(:yordar, :staffing_supplier_id)).present?
  json.partial! 'api/customers/pantry_manager_details'
end
if @order.present? && @order.meal_plan_id.blank?
  json.partial! 'api/customers/meal_plan_details', customer: @customer
end
if @order&.woolworths_order.present?
  json.partial! 'api/orders/woolworths_delivery_details', order: @order
end
