hide_po_number = order&.is_home_delivery? || (customer.present? && customer.company.present? && customer.company.id == 10_741) # <PERSON>ton Dickson

purchase_orders = customer.customer_purchase_orders.where(active: true).distinct
if (order_purchase_order = order&.customer_purchase_order.presence)
  purchase_orders = purchase_orders.to_a.unshift(order_purchase_order) if purchase_orders.exclude?(order_purchase_order)
end

json.has_gst_split_invoicing customer.has_gst_split_invoicing
if customer.has_gst_split_invoicing
  json.has_gst_split_items order.present? && order.order_lines.map(&:is_gst_free).uniq.size == 2
end

json.hide_po_number hide_po_number
json.requires_purchase_order !hide_po_number && customer.requires_po
json.required_department_identity_format !hide_po_number && customer.requires_department_identity.presence
json.purchase_orders purchase_orders do |purchase_order|
  json.extract! purchase_order, :id, :po_number, :description
  json.inactive !purchase_order.active? && (order&.delivery_at.blank? || order&.delivery_at > Time.zone.now)
end
