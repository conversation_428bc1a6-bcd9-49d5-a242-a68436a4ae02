meal_plans = customer.meal_plans.where(archived_at: nil)

return if meal_plans.blank?

json.meal_plans meal_plans.each do |meal_plan|
  json.partial! 'api/meal_plans/meal_plan', meal_plan: meal_plan
  json.po_number meal_plan.po_number
  json.gst_free_po_number meal_plan.gst_free_po_number

  selected_card = meal_plan.credit_card
  json.payment_option selected_card.pay_on_account? ? 'Pay On Account' : "[#{selected_card.brand_label}] #{selected_card.name} ending in #{selected_card.last4} (expires #{selected_card.expiry_label})"
end
