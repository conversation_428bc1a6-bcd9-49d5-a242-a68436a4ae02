# Credit Card(s) Details
customer_credit_cards = customer.credit_cards.where(enabled: true, saved_for_future: true).order(created_at: :desc)
customer_credit_cards = customer_credit_cards.where(pay_on_account: false)
customer_credit_cards = customer_credit_cards.where.not(stripe_token: nil)
customer_credit_cards = customer_credit_cards.reject(&:expired?)

can_pay_by_credit_card = customer.can_pay_by_credit_card?
customer_credit_cards = CreditCard.none if !can_pay_by_credit_card

if order.present? && order.credit_card_id.present? && !order.credit_card.pay_on_account? && customer_credit_cards.map(&:id).exclude?(order.credit_card_id)
  customer_credit_cards = customer_credit_cards.to_a.unshift(order.credit_card)
end

json.can_pay_on_account customer.can_pay_on_account?
json.has_nominated_card customer_credit_cards.reject(&:expired?).detect(&:auto_pay_invoice).present?
json.can_pay_by_credit_card can_pay_by_credit_card

json.saved_credit_cards customer_credit_cards.map do |credit_card|
  json.partial! 'api/credit_cards/credit_card', credit_card: credit_card
end
