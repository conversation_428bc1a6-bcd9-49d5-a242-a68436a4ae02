json.extract! supplier_closure, :id, :reason, :description

json.number_of_days ((supplier_closure.ends_at - supplier_closure.starts_at) / (60 * 60 * 24) + 1).to_i

json.starts_at supplier_closure.starts_at.to_s(:date_spreadsheet)
json.formatted_starts_at supplier_closure.starts_at.strftime("#{supplier_closure.starts_at.day.ordinalize} %B, %Y")

json.ends_at supplier_closure.ends_at.to_s(:date_spreadsheet)
json.formatted_ends_at supplier_closure.ends_at.strftime("#{supplier_closure.ends_at.day.ordinalize} %B, %Y")

json.is_expired supplier_closure.ends_at < Time.zone.now
