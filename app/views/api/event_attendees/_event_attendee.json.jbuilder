json.extract! event_attendee, :id, :first_name, :last_name, :name, :email, :team_admin_id

if params[:wants_html]
  context = params[:context].presence || ''
  case context
  when 'attendee_selection'
    json.attendee_html (render 'event_attendees/event_attendee', event_attendee: event_attendee, as_potential_team_order_attendee: true, is_invited: true)
  when 'team_contact_list'
    json.attendee_html (render 'event_attendees/event_attendee', event_attendee: event_attendee, as_team_contact_attendee: true, is_selected: params[:attendee_action].present? && params[:attendee_action] == 'add', row_index: params[:row_id])
  else
    json.attendee_html (render 'event_attendees/event_attendee', event_attendee: event_attendee)
  end
end
