order ||= order_line.order
json.extract! order_line, :id, :menu_item_id, :name, :quantity, :note, :is_gst_free

order_line_price = order.is_team_order? ? order_line.price_inc_gst(gst_country: order.symbolized_country_code) : order_line.price
line_total = order_line.price.nil? ? 0 : order_line_price * order_line.quantity
json.line_total number_to_currency(line_total, precision: 2)

order_line_errors = order_line.last_errors || []
if trolley_product = order_line.trolley_product.presence
  order_line_errors << 'Item is being Synced!' if (trolley_product.updated_at < Time.zone.now - 10.seconds) && !trolley_product.synced?
  order_line_errors += trolley_product.trolley_errors if trolley_product.trolley_errors.present?
end
json.errors order_line_errors
json.image order_line.menu_item.image

json.location_id order_line.location_id
json.supplier_id order_line.supplier_profile_id
json.order_id order_line.order_id

grouped_extras = order_line.selected_menu_extras.present? ? OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call : []
selected_extras = grouped_extras.map do |section, extras|
  "#{section.name.presence || 'extras'}: #{extras.map(&:name).map(&:strip).join(',')}"
end
json.selected_extras selected_extras


order_line_value = case
when session_profile&.is_a?(CustomerProfile) || order.is_team_order?
  order_line_price
else 
  order_line.baseline || order_line.cost
end
json.price number_with_precision(order_line_value, precision: 2)
json.order_line_value order_line_value
json.is_reduced_price order_line.supplier_profile_id == yordar_credentials(:woolworths, :supplier_profile_id) ? order_line.cost < order_line.baseline : false

if params[:want_htmls]
  json.html render('orders/docket/order_line', order_line: order_line, grouped_extras: grouped_extras, order: order)
end
