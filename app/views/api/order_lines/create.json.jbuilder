order = @order_lines_creator.order

json.order_lines @order_lines_creator.created_order_lines.each do |order_line|
  json.partial! 'api/order_lines/order_line', order_line: order_line, order: order
end

json.order do
  json.partial! 'api/orders/order', order: order, totals: @totals, team_order_spends: @team_order_spends
end

location = @order_lines_creator.location
json.location do
  json.partial! 'api/locations/location', location: @order_lines_creator.location, order: order
end

supplier = @order_lines_creator.supplier
json.supplier do
  json.extract! supplier, :id, :name, :slug
end

if params[:want_htmls]
  json.location_html (render 'orders/docket/location', location: location, order_lines: [], order: order)
  json.supplier_html (render 'orders/docket/supplier', supplier: supplier, order_lines: [], order: order)
end


