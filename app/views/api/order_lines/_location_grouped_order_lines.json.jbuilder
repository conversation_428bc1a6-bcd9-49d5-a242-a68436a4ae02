location_grouped_order_lines = order_lines.group_by(&:location)
empty_locations = include_empty_locations ? Location.where(order: order).includes(:order_lines).where(order_lines: { id: nil }) : []
if empty_locations.present?
  location_grouped_order_lines = location_grouped_order_lines.to_a + empty_locations.map{|location| [location, []] }
end

json.set! :locations do
  location_grouped_order_lines.map do |location, location_order_lines|
    json.set! location.id do
      json.id location.id  
      json.name location.details

      supplier_grouped_order_lines = location_order_lines.group_by(&:supplier_profile)
      json.set! :suppliers do
        supplier_grouped_order_lines.map do |supplier, supplier_order_lines|
          json.set! supplier.id do
            json.extract! supplier, :id, :name, :slug

            json.set! :order_lines do
              supplier_order_lines.map do |order_line|
                json.set! order_line.id do
                  json.partial! 'api/order_lines/order_line', order_line: order_line, order: order
                end # set! order_line.id
              end # order_lines map
            end # set! :order_lines
          end # set! supplier.id
        end # suppliers map
      end # set! :suppliers
    end # set! location.id
  end # locations map
end # set! locations