selected_supplier_profiles = @selected_order_suppliers.map(&:supplier_profile)
json.suppliers @suppliers.each do |supplier|
  json.extract! supplier, :id, :name, :rating
  json.selected selected_supplier_profiles.include?(supplier)
end
if params[:wants_html]
  json.suppliers_html (render partial: 'team_orders/suppliers_list', locals: { suppliers: @suppliers, suppliers_minimums: @suppliers_minimums, budget: (params[:budget].presence || nil), selected_order_suppliers: @selected_order_suppliers, favourite_team_supplier_ids: @favourite_team_supplier_ids })
end
