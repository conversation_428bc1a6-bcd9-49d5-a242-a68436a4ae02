json.extract! credit_card, :id, :label, :name, :brand, :brand_label, :last4, :expiry_month, :expiry_year, :auto_pay_invoice, :surcharge_fee, :surcharge_percent, :saved_for_future
json.expired credit_card.expired?
json.is_old credit_card.stripe_token.blank?

if params[:wants_html].present?
  case params[:wants_html]
  when 'payment-options'
    json.html (render 'credit_cards/credit_card', credit_card: credit_card)
  end
end

