json.extract! quote, :id, :kind, :status, :uuid
quote.form_data.except('type').each do |field, value|
  formatted_value = case
  when field == 'date'
    Time.zone.parse(value).strftime('%d/%m/%Y')
  when field == 'time'
    Time.zone.parse(value).strftime('%I:%M%p')
  when value.is_a?(Array)
    value.join(', ')
  else
    value
  end
  json.set! field.titleize, formatted_value
end


if (quote_date = quote.form_data['date'].presence) && (quote_time = quote.form_data['time'].presence)
  date = Time.zone.parse(quote_date)
  time = Time.zone.parse(quote_time)
  json.order_date "#{date.to_s(:date_spreadsheet)}T#{time.strftime('%H:%M')}"
end

json.created_at quote.created_at.to_s(:date_verbose)
json.day_month quote.created_at.to_s(:day_month)