json.extract! order, :id, :name, :delivery_address_arr, :cpo_id, :po_number

json.delivery_date order.delivery_at.strftime("#{order.delivery_at.day.ordinalize} %B, %Y")

if order.cpo_id.present?
  json.po_active order.customer_purchase_order&.active || false
else
  json.po_active false
end

json.suppliers order.supplier_profiles.uniq.each do |supplier|
  json.extract! supplier, :id, :name
  json.image supplier.profile.avatar.presence || ''
end
