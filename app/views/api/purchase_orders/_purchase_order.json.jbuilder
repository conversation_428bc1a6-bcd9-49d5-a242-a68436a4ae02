json.extract! purchase_order, :id, :po_number, :active, :description, :created_at

orders = purchase_order.orders.select{|order| %w[pending quoted paused new amended confirmed delivered].include?(order.status) && order.delivery_at.present? }
json.number_of_orders orders.size

if latest_order = orders.max_by(&:delivery_at).presence
  json.latest_order do 
    json.extract! latest_order, :id, :name
    json.delivery_at latest_order.delivery_at.strftime("#{latest_order.delivery_at.day.ordinalize} %B, %Y @ %l:%M %P")
  end
end

