json.extract! delivery_zone, :id, :suburb_id, :radius, :operating_wdays

weekdays = Date::DAYNAMES.map(&:first)
display_days = weekdays.map.with_index do |weekday, widx|
  {
    weekday: weekday,
    active: delivery_zone.operating_wdays.present? && delivery_zone.operating_wdays[widx] == '1'
  }
end

json.delivery_fee delivery_zone.delivery_fee&.round(2)
json.operating_days display_days.rotate(1)
json.operating_hours operating_hours_for(delivery_zone)
json.operating_hours_start delivery_zone.operating_hours_start.present? ? Time.at(delivery_zone.operating_hours_start).utc.strftime('%T.%L') : nil
json.operating_hours_end delivery_zone.operating_hours_end.present? ? Time.at(delivery_zone.operating_hours_end).utc.strftime('%T.%L') : nil

json.suburb_label delivery_zone.suburb.label

threshold = 15
if is_admin?
  deliverable_suburbs = delivery_zone.deliverable_suburbs.includes(:suburb).order(distance: :asc)
  deliverable_suburb_data = deliverable_suburbs.limit(threshold).map do |deliverable_suburb|
    suburb = deliverable_suburb.suburb
    distance = deliverable_suburb.distance
    {
      name: "#{suburb.name} #{suburb.postcode}",
      distance: "#{distance > 0.0 ? distance.round(2) : 0}km"
    }
  end
else
  deliverable_suburbs = DeliverableSuburb.none
  deliverable_suburb_data = []
end

json.deliverable_suburbs deliverable_suburb_data
json.deliverable_suburb_count deliverable_suburbs.count
json.recently_updated delivery_zone.updated_at > (Time.zone.now - 1.minutes)
