json.extract! employee_survey, :id, :name, :category_group, :active, :uuid
json.updated_at employee_survey.updated_at.to_s(:full_date)
json.category_group_name employee_survey.category_group_name

survey_questions = employee_survey.survey_questions.where(active: true).order(:position)

json.questions survey_questions.each do |survey_question|
  json.partial! 'api/survey_questions/survey_question', survey_question: survey_question
end
