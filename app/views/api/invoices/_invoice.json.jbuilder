invoice_emails = (@invoice_emails || []).select{|email| email.ref.include?("-#{invoice.id}") }
invoice_documents = (@invoice_documents || []).select{|document| document.documentable_id == invoice.id }
newly_generated_documents ||= []

json.extract! invoice, :id, :number, :uuid, :do_not_notify, :po_number

payment_status = invoice.payment_status == 'unpaid' && invoice.do_not_notify ? 'amended' : invoice.payment_status
json.payment_status payment_status.upcase
json.status invoice.payment_status != 'paid' ? invoice.status : nil
json.from_at invoice.from_at.to_s(:date)
json.to_at invoice.to_at.to_s(:day_month)
json.due_at invoice.due_at.to_s(:date_verbose)
json.invoice_date invoice.to_at.to_s(:short_week_full_date)

orders = invoice.invoice_orders
totals = Invoices::CalculateTotals.new(invoice: invoice).call
is_due = !invoice.do_not_notify && invoice.payment_status == 'unpaid' && invoice.due_at >= (Time.zone.now.beginning_of_day + 1.days)
is_overdue = !invoice.do_not_notify && invoice.payment_status == 'unpaid' && invoice.due_at <= (Time.zone.now.beginning_of_day - 2.days) && invoice.due_at > Time.zone.parse(Xero::API::Base::SYNC_THRESHOLD_DATE)
failed_payment = !invoice.paid? && orders.any?{|order| order.payment_status == 'error' }

json.number_of_orders totals.order_count
json.order_ids orders.map(&:id).sort
json.order_names orders.map(&:name).uniq.sort
json.amount (!invoice.do_not_notify || is_admin?) && number_to_currency(totals.total, precision: 2)

json.document_url (!invoice.do_not_notify || is_admin?) ? invoice.latest_document&.url : nil

invoice_needs_payment = !invoice.do_not_notify && invoice.payment_status == 'unpaid' && invoice.due_at > Time.zone.parse(Xero::API::Base::SYNC_THRESHOLD_DATE)
json.pay_url invoice_needs_payment && pay_invoice_path(invoice)

json.is_due is_due
json.is_overdue is_overdue
json.failed_payment failed_payment
json.due_distance is_overdue && distance_of_time_in_words(invoice.due_at.to_time - Time.zone.now.beginning_of_day)
json.can_manage is_invoice_admin? && invoice.payment_status == 'unpaid' && invoice.due_at > Time.zone.parse(Xero::API::Base::SYNC_THRESHOLD_DATE)

json.documents invoice_documents.group_by(&:version).each do |version, version_documents|
  json.version version
  json.documents version_documents.each do |document|
    json.partial! 'api/invoices/document', document: document, is_newly_generated: newly_generated_documents.include?(document)
  end
end

json.emails invoice_emails do |email|
  json.extract! email, :recipient
  json.sent_at email.sent_at.to_s(:full_verbose)
end
