is_favourite ||= false
markup_override ||= @markup_override

json.id menu_item.id
json.image_id menu_item.image_id
json.name menu_item.name.strip
json.description menu_item.description
json.minimum_quantity 1

# return a range of menu items serving sizes with prices lowest to highest in a string like so 'lowest price - highest price'
discount_price = nil
item_has_promotion = false
serving_sizes = menu_item.serving_sizes.where(archived_at: nil)
if serving_sizes.present?
  prices = serving_sizes.map{|serving| serving.markup_price(override: markup_override) }.sort
  if prices.uniq.length == 1
    display_price = number_to_currency(prices.first, precision: 2)
  else
    display_price = "#{number_to_currency(prices.min, precision: 2)} - #{number_to_currency(prices.max, precision: 2)}"
  end
else
  if menu_item.promo_price.present?
    discount_price = menu_item.markup_price(promotional: true, override: markup_override).round(2)
    item_has_promotion = true
  end
  display_price = number_to_currency(menu_item.markup_price(override: markup_override), precision: 2)
end

json.price number_with_precision(menu_item.markup_price(override: markup_override), precision: 2)
json.display_price display_price
json.discount discount_price
json.has_promotion item_has_promotion
json.is_favourite is_favourite
