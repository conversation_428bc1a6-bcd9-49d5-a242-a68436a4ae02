if @item_orders.present?
  json.array! @item_orders.each do |order, order_lines|
    customer = order.customer_profile
    json.extract! order, :id, :name
    json.customer_name customer.name
    json.company_name customer.company&.name || customer.company_name
    json.delivery_at order.delivery_at.strftime("%a, #{order.delivery_at.day.ordinalize} %b %Y at %l:%M%P")
    json.quantity order_lines.sum(&:quantity)
    json.url supplier_order_show_path(order)
  end
else
  json.array! ['orders-not-found']
end