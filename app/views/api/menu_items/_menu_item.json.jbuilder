json.extract! menu_item, :id, :name, :description, :sku, :stock_quantity, :weight, :minimum_quantity, :sub_quantity, :is_gst_free, :archived_at
json.price number_with_precision(menu_item.price, precision: 2)
json.promo_price number_with_precision(menu_item.promo_price, precision: 2)

serving_sizes = menu_item.serving_sizes.where(archived_at: nil).order(:weight)
menu_extra_sections = menu_item.menu_extra_sections.where(archived_at: nil).order(:weight)
rate_cards = is_admin? ? menu_item.rate_cards.includes(:company).order('companies.name ASC') : []

menu_section = menu_item.menu_section
if menu_section.present?
  json.menu_section_id menu_section.id
  json.menu_section_name menu_section.name
end

json.serving_sizes serving_sizes.each do |serving_size|
  json.partial! 'api/serving_sizes/serving_size', serving_size: serving_size
end

json.menu_extra_sections menu_extra_sections.each do |menu_extra_section|
  json.partial! 'api/menu_extra_sections/menu_extra_section', menu_extra_section: menu_extra_section
end

json.rate_cards rate_cards.each do |rate_card|
  json.partial! 'api/rate_cards/rate_card', rate_card: rate_card
end

json.dietary_preferences dietary_preferences(menu_item: menu_item, for_admin: true).map{|field, data| data.merge({field: field } )}

menu_item_dietary_options.each do |flag, _|
  json.set! flag, menu_item.send(flag.to_sym)
end

menu_item_misc_options.each do |flag, _|
  json.set! flag, menu_item.send(flag.to_sym)
end

if menu_item.image?
  json.image Cloudinary::Utils.cloudinary_url(menu_item.ci_image)
  json.logo Cloudinary::Utils.cloudinary_url(menu_item.image_id, width: 600, height: 600, crop: :fill, quality: 'auto', fetch_format: :auto)
end
