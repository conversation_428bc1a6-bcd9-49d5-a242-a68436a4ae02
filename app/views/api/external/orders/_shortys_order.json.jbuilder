json.key_format! camelize: :lower

json.order_id order.id
customer = order.customer_profile
json.company_name customer.company&.name || customer.company_name
json.contact_name customer.name

json.status order.status
json.contact_number order.phone
json.po_number order.po_number
json.delivery_date order.delivery_at.to_s(:date_spreadsheet)
json.delivery_time order.delivery_at.strftime('%H:%M')
json.delivery_level order.delivery_address_level
json.street_address order.delivery_address
delivery_suburb = order.delivery_suburb
json.city delivery_suburb.name
json.state delivery_suburb.state
json.postcode delivery_suburb.postcode
json.delivery_instruction order.delivery_instruction
lister_options = {
  order: order,
  for_supplier: supplier,
  confirmed_attendees_only: order.is_team_order?
}
order_lines = OrderLines::List.new(options: lister_options).call
order_lines = order_lines.order(:id)

json.items order_lines.each do |order_line|
  menu_item = order_line.menu_item
  serving_size = order_line.serving_size_id.present? && order_line.serving_size
  item_sku = (serving_size.present? ? serving_size.sku : menu_item.sku) rescue nil
  json.sku item_sku
  json.name order_line.name
  json.quantity order_line.quantity
end
