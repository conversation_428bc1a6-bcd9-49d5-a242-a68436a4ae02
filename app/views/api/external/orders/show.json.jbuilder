supplier = @session_resource_profile.class.name == 'SupplierProfile' && @session_resource_profile
order = @order
if supplier.present?
  case
  when supplier.name == 'Shorty\'s Liquor'
    json.partial! 'api/external/orders/shortys_order', order: order, supplier: supplier
  when supplier.uses_flex_catering
    json.partial! 'api/external/orders/flex_order', order: order, supplier: supplier
  else
    json.extract! order, :id, :name
    json.delivery_at order.delivery_at.strftime('%Y-%m-%d %H:%M')
  end
end
