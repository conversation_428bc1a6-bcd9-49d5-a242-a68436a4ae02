json.key_format! camelize: :lower
customer = order.customer_profile
version ||= nil

json.extract! order, :id, :name, :status, :delivery_instruction, :number_of_people
json.delivery_at order.delivery_at.strftime('%Y-%m-%d %H:%M:%S')
json.company_name order.company_name || customer.company&.name || customer.company_name
json.contact_name order.contact_name
json.contact_phone order.phone
json.contactless_delivery order.is_contactless_delivery? && supplier.provides_contactless_delivery

if order.delivery_address_level.present?
  line1 = order.formatted_delivery_address_level
  line2 = order.delivery_address
else
  line1 = order.delivery_address
  line2 = ''
end
delivery_suburb = order.delivery_suburb
address_country = order.delivery_suburb.country_code.upcase
json.delivery_address do 
  json.line1 line1
  json.line2 line2
  json.city delivery_suburb.name
  json.state delivery_suburb.state
  json.postcode delivery_suburb.postcode
  json.country address_country == 'AU' ? 'AUS' : address_country
end

order_supplier = order.order_suppliers.where(supplier_profile: supplier).first
baseline_totals = Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, baseline: true).call
country_code = order.symbolized_country_code

if baseline_totals.delivery.present? && baseline_totals.delivery > 0
  delivery_with_gst = baseline_totals.delivery.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)
else
  delivery_with_gst = 0
end
json.delivery delivery_with_gst.round(2)
json.gst baseline_totals.gst.round(2)
json.topup baseline_totals.topup.present? && baseline_totals.topup > 0 ? baseline_totals.topup.round(2): nil
json.total baseline_totals.total.round(2)

if order_supplier.total != baseline_totals.total
  json.discount (order_supplier.total - baseline_totals.total).round(2)
  json.total_with_discount order_supplier.total.round(2)
else
  json.discount 
  json.total_with_discount baseline_totals.total.round(2)
end

# order lines
lister_options = {
  order: order,
  supplier: supplier,
  confirmed_attendees_only: order.is_team_order?,
}
order_lines = OrderLines::List.new(options: lister_options, includes: [:location, :menu_item, :serving_size]).call
sorted_order_lines = order_lines.sort_by{|order_line| [order_line.location.id, order_line.id] }

json.items sorted_order_lines.map do |order_line|
  # sku
  location = order_line.location
  menu_item = order_line.menu_item
  serving_size = order_line.serving_size_id.present? && order_line.serving_size
  item_sku = serving_size.present? ? serving_size.sku : menu_item.sku
  json.sku item_sku

  json.name order_line.name
  json.is_gst_free order_line.is_gst_free
  json.note order_line.note
  json.location location&.details || 'Your Office'
  json.quantity order_line.quantity

  # pricing
  baseline_value = order_line.baseline
  if order_line.is_gst_free? || order_line.is_gst_inc?
    baseline_value_with_gst = baseline_value
  else
    baseline_value_with_gst = baseline_value.round(2) * (1 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)
  end
  json.price baseline_value_with_gst.round(2)

  # menu extras
  if order_line.selected_menu_extras.present?
    grouped_extra = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call
    json.menu_extras grouped_extra.map do |section, menu_extras|
      json.section section
      json.extras menu_extras.map(&:name).map(&:strip).join(', ')
    end
  else
    json.menu_extras []  
  end
end

if version.present?
  json.version version
end