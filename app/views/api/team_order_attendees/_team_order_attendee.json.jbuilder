json.extract! team_order_attendee, :id, :first_name, :last_name, :name, :email, :status

if params[:wants_html].present? || params[:context].present?
  team_order = team_order_attendee.order
  if %w[pending ordered].include?(team_order_attendee.status)
    lister_options = {
      order: team_order,
      for_attendee: team_order_attendee,
    }
    attendee_order_lines = OrderLines::List.new(options: lister_options).call
  else
    attendee_order_lines = []
  end
  if params[:context].present? && params[:context] == 'view-order'
    json.order_html (render 'team_order_attendees/modal_order_show', team_order: team_order, team_order_attendee: team_order_attendee, order_lines: attendee_order_lines)
  else
    json.attendee_html (render 'team_orders/team_order_attendee', team_order: team_order, team_order_attendee: team_order_attendee, can_edit: true, order_lines: attendee_order_lines)
  end
end
