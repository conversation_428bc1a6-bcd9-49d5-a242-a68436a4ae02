report_label_heading = @report_retriever.options.report_type == 'monthly' ? 'Month' : 'Week Starting'

header = [report_label_heading]
header << 'Order Date'
header += ['First Name', 'Last Name', 'Email Address'] # attendee data
header += ['Item', 'Extras/Add Ons', 'Price'] # Item data
csv << header

@report_retriever.data.each_with_index do |report_result, _idx|
  report_label = report_result.key_date[:label]

  team_orders = Order.where(id: report_result.order_ids)
  team_orders = team_orders.where(order_variant: %w[team_order recurring_team_order])
  team_orders = team_orders.where(status: 'delivered')
  team_orders = team_orders.order(delivery_at: :asc)

  team_orders.each do |team_order|
    lister_options = {
      order: team_order,
      confirmed_attendees_only: true,
    }
    order_lines = OrderLines::List.new(options: lister_options, includes: %i[team_order_attendee]).call.where.not(attendee_id: nil)

    grouped_order_lines = order_lines.group_by(&:team_order_attendee)
    sorted_grouped_order_lines = grouped_order_lines.sort_by{|attendee, _| attendee.present? ? attendee.name : '' }
    country_code = team_order.symbolized_country_code || :au

    sorted_grouped_order_lines.each do |attendee, attendee_order_lines|
      next if attendee.blank?

      attendee_order_lines.each do |order_line|
        order_line_row = []
        order_line_row << report_label
        order_line_row << team_order.delivery_at.to_s(:date_spreadsheet)
        order_line_row << attendee.first_name
        order_line_row << attendee.last_name
        order_line_row << attendee.email
        order_line_row << order_line.name
        if order_line.selected_menu_extras.present?
          grouped_extra = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call
          order_line_extras = grouped_extra.map do |section, menu_extras|
            "#{section.name}: #{menu_extras.map(&:name).map(&:strip).join(', ')}"
          end.join(' | ')
          order_line_row << order_line_extras
        else
          order_line_row << ''
        end
        order_line_row << order_line.total_price(gst_country: country_code)
        csv << order_line_row
      end # attendee order line
    end # attendee grouped order lines
  end # report order
end # report_result

