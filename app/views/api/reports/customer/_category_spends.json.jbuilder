catering_report_data, snacks_report_data = report_data.partition{|data| data.category == 'catering-services' }

catering_category_data = Report::OrderDatum.where(data_kind: 'Category', report_datum: catering_report_data)
catering_spends = {}
catering_category_data.group_by(&:kind).each do |kind, category_data|
  catering_spends[kind] = category_data.sum(&:total_spend)
end

snacks_category_data = Report::OrderDatum.where(data_kind: 'Category', report_datum: snacks_report_data)
snacks_spends = {}
snacks_category_data.group_by(&:kind).each do |kind, category_data|
  snacks_spends[kind] = category_data.sum(&:total_spend)
end


if catering_spends.present?  
  cumulative_catering_spends = Reports::SplitCumulativeData.new(data: catering_spends.to_h).call
  
  json.catering cumulative_catering_spends.map do |category, category_spend|
    json.label category
    json.value category_spend
  end
end
if snacks_spends.present?
  cumulative_snacks_spends = Reports::SplitCumulativeData.new(data: snacks_spends.to_h).call

  json.snacks cumulative_snacks_spends.map do |category, category_spend|
    json.label category
    json.value category_spend
  end
end