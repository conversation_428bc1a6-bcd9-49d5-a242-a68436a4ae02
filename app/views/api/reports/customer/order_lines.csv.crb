report_label_heading = @report_retriever.options.report_type == 'monthly' ? 'Month' : 'Week Starting'

header = [report_label_heading]
header += ['Order #', 'Order Name'] # order data
header << 'Customer' if @report_retriever.options.company_wide || @report_retriever.options.customer_id.blank?
header << 'Delivery At' # delivery data
header += ['Item name', 'Quantity', 'Item Price', 'Total Price'] # Order Line data
csv << header

@report_retriever.data.each do |report_result|
  report_label = report_result.key_date[:label]
  orders = Order.where(id: report_result.order_ids).order(:delivery_at)

  orders = orders.includes(:customer_profile, :delivery_suburb, :customer_purchase_order)
  orders.each do |order|
    order_row = []
    order_row << report_label
    order_row << order.id
    order_row << order.name
    order_row << order.customer_profile.name if @report_retriever.options.company_wide || @report_retriever.options.customer_id.blank?
    order_row << order.delivery_at.to_s(:datetime)

    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    order_lines = OrderLines::List.new(options: lister_options).call
    order_lines.each do |order_line|
      order_line_row = order_row.dup
      order_line_row << order_line.name
      order_line_row << order_line.quantity
      order_line_row << number_to_currency(order_line.price, precision: 2)
      order_line_row << number_to_currency(order_line.price * order_line.quantity, precision: 2)
      csv << order_line_row
    end
  end
end

