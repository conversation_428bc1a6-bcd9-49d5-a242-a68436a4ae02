supplier_spend_data =  Report::OrderDatum.where(data_kind: 'Supplier', report_datum: report_data)
supplier_spends = {}
supplier_spend_data.group_by(&:kind).each do |supplier, supplier_data|
  supplier_spends[supplier] = supplier_data.sum(&:total_spend)
end

ethical_spend_data =  Report::OrderDatum.where(data_kind: 'Ethical', report_datum: report_data)
ethical_spends = {}
ethical_spend_data.group_by(&:kind).each do |kind, ethical_data|
  ethical_spends[kind] = ethical_data.sum(&:total_spend)
end

if supplier_spends.present?
  cumulative_supplier_spends = Reports::SplitCumulativeData.new(data: supplier_spends.to_h).call
  json.supplier_spend cumulative_supplier_spends.each do |supplier, spends|
    json.label supplier
    json.value spends
  end
end

if ethical_spends.present?
  json.ethical_spend ethical_spends.each do |kind, spends|
    json.label kind
    json.value spends
  end
end


