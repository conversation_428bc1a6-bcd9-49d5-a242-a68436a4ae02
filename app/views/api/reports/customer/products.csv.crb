report_label_heading = @report_retriever.options.report_type == 'monthly' ? 'Month' : 'Week Starting'

header = [report_label_heading]
header << 'Supplier'
header += ['Item Name', 'Serving size'] # items name
header += ['Price', 'GST Free?'] # pricing data
header += ['Quantity', 'Total Spend'] # totals
csv << header

@report_retriever.data.each_with_index do |report_result, idx|
  report_label = report_result.key_date[:label]
  orders = Order.where(id: report_result.order_ids).order(:delivery_at)
  if orders.present?
    lister_options = {
      orders: orders,
      confirmed_attendees_only: true,
    }
    all_order_lines = OrderLines::List.new(options: lister_options, includes: [:supplier_profile, :menu_item, :serving_size]).call
  else
    all_order_lines = []
  end
  item_grouped_order_lines = all_order_lines.group_by do |order_line|
    OpenStruct.new(menu_item: order_line.menu_item, serving_size: order_line.serving_size, price: order_line.price, gst_free: order_line.is_gst_free?)
  end
  sorted_grouped_order_lines = item_grouped_order_lines.sort_by do |grouping, order_lines|
    -order_lines.map(&:quantity).sum
  end
  sorted_grouped_order_lines.each do |grouping, order_lines|
    order_line_row = []
    order_line_row << report_label
    order_line_row << grouping.menu_item.supplier_profile.name
    order_line_row << grouping.menu_item.name
    order_line_row << grouping.serving_size.try(:name)
    order_line_row << number_to_currency(grouping.price, precision: 2)
    order_line_row << (grouping.gst_free ? 'yes' : 'no')
    quantity = order_lines.map(&:quantity).sum
    order_line_row << quantity
    order_line_row << number_to_currency(quantity * grouping.price, precision: 2)
    csv << order_line_row
  end
end

