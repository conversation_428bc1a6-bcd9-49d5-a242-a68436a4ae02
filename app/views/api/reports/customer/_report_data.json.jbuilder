json.array! report_results.map do |report_result|
  json.label report_result.key_date[:label]
  json.total_spend report_result.total_spend
  catering_data, pantry_data = report_result.report_data.partition{|data| data.category == 'catering-services' }
  json.catering_spend catering_data.map(&:total_spend).sum
  json.snacks_spend pantry_data.map(&:total_spend).sum
  json.budget report_result&.budget
end
