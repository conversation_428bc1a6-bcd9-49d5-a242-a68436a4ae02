report_label_heading = @report_retriever.options.report_type == 'monthly' ? 'Month' : 'Week Starting'
has_supplier_costs = @report_retriever.options.with_supplier_costs

header = [report_label_heading]
header += ['Order #', 'Order Name', 'Order Status'] # order data
header << 'Customer' if @report_retriever.options.company_wide || @report_retriever.options.customer_id.blank?
header += ['Delivery At', 'Delivery Address', 'Delivery Suburb'] # delivery data
header << 'PO number'
header += ['Subtotal', 'Delivery Fee', 'GST', 'Total'] # customer totals
header << 'Supplier Total' if has_supplier_costs
csv << header

@report_retriever.data.each do |report_result|
  report_label = report_result.key_date[:label]
  orders = Order.where(id: report_result.order_ids)
  orders = orders.includes(:supplier_profiles, :order_suppliers) if has_supplier_costs
  orders = orders.order(:delivery_at)

  orders = orders.includes(:customer_profile, :delivery_suburb, :customer_purchase_order)
  orders.each do |order|
    order_row = []
    order_row << report_label
    order_row << order.id
    order_row << order.name
    order_row << order.status
    order_row << order.customer_profile.name if @report_retriever.options.company_wide || @report_retriever.options.customer_id.blank?
    order_row << order.delivery_at.to_s(:datetime)

    street_address = [order.formatted_delivery_address_level, order.delivery_address].reject(&:blank?).join(', ')
    order_row << street_address
    order_row << order.delivery_suburb.label

    order_row << order.po_number.presence || '-'
    order_row << number_to_currency(order.customer_subtotal, precision: 2)
    order_row << number_to_currency(order.customer_delivery, precision: 2)
    order_row << number_to_currency(order.customer_gst, precision: 2)
    order_row << number_to_currency(order.customer_total, precision: 2)
    if has_supplier_costs
      order_line_supplier_ids = order.supplier_profile_ids
      order_suppliers = order.order_suppliers.select do |order_supplier|
        order_supplier.total.present? &&
          order_line_supplier_ids.include?(order_supplier.supplier_profile_id)
      end
      order_row << number_to_currency(order_suppliers.sum(&:total), precision: 2)
    end
    csv << order_row
  end
end

