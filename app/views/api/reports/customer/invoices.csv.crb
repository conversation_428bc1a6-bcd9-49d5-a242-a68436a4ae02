report_label_heading = @report_retriever.options.report_type == 'monthly' ? 'Month' : 'Week Starting'

header = [report_label_heading]
header += ['Invoice #', 'Creation Date'] # Invoice data
header << 'PO number' # if any
header += ['Non-GST total', 'GST total', 'Total', 'GST', 'Grand Total (inc gst)']
csv << header

@report_retriever.data.each_with_index do |report_result, _idx|
  report_label = report_result.key_date[:label]

  invoices = report_result.report_data

  invoices.each do |invoice|
    invoice_row = []
    invoice_row << report_label
    invoice_row << invoice.number
    invoice_row << invoice.to_at.to_s(:date_spreadsheet)
    invoice_orders = invoice.invoice_orders

    purchase_orders = [invoice.customer_purchase_order].compact.presence || invoice_orders.map(&:customer_purchase_order).compact.uniq
    if purchase_orders.present? && purchase_orders.size == 1
      invoice_row << purchase_orders.first.po_number
    else
      invoice_row << ''
    end

    country_code = invoice_orders.sample.symbolized_country_code
    gst = invoice_orders.map(&:customer_gst).sum
    gst_total = gst / yordar_credentials(:yordar, :gst_percent, country_code).to_f
    grand_total = invoice_orders.map(&:customer_total).sum
    non_gst_total = grand_total - gst_total - gst

    invoice_row << non_gst_total
    invoice_row << gst_total
    invoice_row << non_gst_total + gst_total
    invoice_row << gst
    invoice_row << grand_total
    csv << invoice_row
  end

end

