json.report_type @report_retriever.options.report_type
json.detail_type @report_retriever.options.detail_type
json.source_type @report_retriever.options.source_type
json.company_wide @report_retriever.options.company_wide
case
when @report_retriever.options.source_types.present?
  json.source_types @report_retriever.options.source_types
  json.report_data do
    json.partial! 'api/reports/supplier/report_data', report_results: @report_retriever.data, supplier_id: @report_retriever.options.supplier_id, report_options: @report_retriever.options
  end
when @report_retriever.options.react_data
  json.report_data do
    json.partial! 'api/reports/customer/report_data', report_results: @report_retriever.data
  end

  report_data = @report_retriever.data.map(&:report_data).flatten(1)
  json.category_spend do
    json.partial! 'api/reports/customer/category_spends', report_data: report_data
  end
  json.partial! 'api/reports/customer/supplier_spends', report_data: report_data
else
  json.report_data @report_retriever.data
end
