supplier_id ||= nil
json.array! report_results.map do |report_result|
  report_data = report_result.report_data
  json.label report_result.key_date[:label]

  json.total_spend report_result.total_spend
  customer_data, supplier_data = report_data.partition{|data| data.report_source.source_type == 'CustomerProfile' }

  json.supplier_cost supplier_data.map(&:total_spend).sum
  case
  when supplier_id.present? && (supplier = SupplierProfile.where(id: supplier_id).first.presence)
    supplier_spend_data = Report::OrderDatum.where(data_kind: 'Supplier', report_datum: customer_data, kind: supplier.name)
    json.customer_spend supplier_spend_data.sum(&:total_spend)
  when report_options.exclude_staffing
    supplier_spend_data = Report::OrderDatum.where(data_kind: 'Supplier', report_datum: customer_data).where.not(kind: 'Yordar Staffing')
    json.customer_spend supplier_spend_data.sum(&:total_spend)
  else
    json.customer_spend customer_data.map(&:total_spend).sum
  end
end
