report_label_heading = @report_retriever.options.report_type == 'monthly' ? 'Month' : 'Week Starting'

header = [report_label_heading]
header << 'Supplier'
header += ['Order #', 'Order Name', 'Order Status'] # order data
header += ['Delivery At', 'Delivery Address', 'Delivery Suburb'] # delivery data
header += ['Subtotal', 'Delivery Fee', 'GST', 'Total'] # customer totals
csv << header

@report_retriever.data.each do |report_result|
  report_label = report_result.key_date[:label]

  supplier_grouped_report_data = report_result.report_data.group_by{|data| data.report_source.source }

  supplier_grouped_report_data.each do |supplier, supplier_report_data|
    supplier_order_ids = supplier_report_data.map(&:order_ids).sum
    orders = Order.where(id: supplier_order_ids).order(:delivery_at)
    orders.each do |order|
      order_row = []
      order_row << report_label
      order_row << supplier.name
      order_row << order.id
      order_row << order.name
      order_row << order.status
      order_row << order.delivery_at.to_s(:datetime)

      street_address = [order.formatted_delivery_address_level, order.delivery_address].reject(&:blank?).join(', ')
      order_row << street_address
      order_row << order.delivery_suburb.label

      order_supplier = order.order_suppliers.where(supplier_profile: supplier).where.not(total: nil).first
      if order_supplier.present?
        order_row << number_to_currency(order_supplier.subtotal, precision: 2)
        order_row << number_to_currency(order_supplier.delivery, precision: 2)
        order_row << number_to_currency(order_supplier.gst, precision: 2)
        order_row << number_to_currency(order_supplier.total, precision: 2)
      end
      csv << order_row
    end
  end
end

