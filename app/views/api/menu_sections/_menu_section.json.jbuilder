json.extract! menu_section, :id, :name, :group_name, :weight, :is_hidden, :archived_at
json.category_ids menu_section.categories.map(&:id)
json.company_ids is_admin? ? menu_section.company_ids : []

json.restricted_companies menu_section.companies.map do |company|
  json.extract! company, :id, :name
end

section_items = menu_section.menu_items.where(supplier_profile_id: menu_section.supplier_profile_id).order(:weight)
menu_items = section_items.where(archived_at: nil)

if @show_archived
  archived_items = section_items.where.not(archived_at: nil).joins(order_lines: :order)
  archived_items = archived_items.where('orders.delivery_at > ?', Time.zone.now - 1.day)
  archived_items = archived_items.where(orders: { status: %w[new pending amended confirmed paused]}).distinct
else
  archived_items = []
end

all_items = archived_items.present? ? menu_items + archived_items : menu_items
json.menu_items all_items.each do |menu_item|
  json.partial! 'api/menu_items/menu_item', menu_item: menu_item
end
