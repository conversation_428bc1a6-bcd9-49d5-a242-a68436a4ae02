active_questions = @employee_survey.survey_questions.where(active: true)
all_survey_answers = @employee_survey_submissions.map(&:survey_answers).flatten(1)
answer_question_ids = all_survey_answers.map(&:survey_question_id).flatten(1).uniq
answer_only_question_ids = answer_question_ids - active_questions.map(&:id)

survey_questions = (active_questions + SurveyQuestion.where(id: answer_only_question_ids)).reject(&:blank?)

header = ['Name', 'Overall Score']
survey_questions.each do |question|
  header << question.label
end
header <<  'Submission On'
csv << header

@employee_survey_submissions.each do |submission|
  row = []
  row << (submission.name&.strip.presence || 'Anonymous')
  row << submission.overall_rating
  survey_answers = all_survey_answers.select{|survey_answer| survey_answer.employee_survey_submission_id == submission.id }

  survey_questions.each do |question|
    survey_answer = survey_answers.detect{|answer| answer.survey_question_id == question.id }
    row << (survey_answer&.value || '-')
  end

  row << submission.created_at.to_s(:datetime)

  csv << row
end
