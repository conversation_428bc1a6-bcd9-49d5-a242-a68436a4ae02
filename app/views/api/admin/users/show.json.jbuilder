json.extract! @user, :id, :firstname, :lastname, :email, :unconfirmed_email, :reset_password_sent_at, :sign_in_count

json.is_confirmed @user.confirmed?
json.confirmation_sent_at @user.confirmation_sent_at&.to_s(:full_verbose)
json.last_sign_in_at @user.last_sign_in_at&.to_s(:full_verbose)
json.confirmation_link user_confirmation_url(confirmation_token: @user.confirmation_token, host: yordar_credentials(:default_host))
