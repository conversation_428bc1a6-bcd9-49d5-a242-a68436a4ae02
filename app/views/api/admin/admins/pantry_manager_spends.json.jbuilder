json.array! @pantry_manager_spends.each do |manager_spend|
  pantry_manager = manager_spend.manager
  json.pantry_manager pantry_manager&.name || 'Un-Assigned'
  json.hours manager_spend.hours
  json.is_unassigned pantry_manager.blank?

  json.spends manager_spend.customer_spends.each do |customer_spend|
    json.customer customer_spend.customer.customer_or_company_name
    json.hours customer_spend.hours
  end

  json.orders manager_spend.orders.each do |order|
    json.partial! 'api/admin/orders/order', order: order
    json.pantry_manager do
      json.name pantry_manager&.name || 'Un-Assigned'
      avatar = pantry_manager&.profile&.avatar
      json.img avatar.present? ? cl_image_path(avatar, width: 100, height: 100, crop: 'fill', quality: 'auto,fl_lossy,f_auto') : Email::DEFAULT_AVATAR
      json.is_unassigned pantry_manager.blank?
    end
    order_hours = Admin::Reports::FetchPantryManagerSpends::OrderSpend.new(order: order).hours
    json.hours order_hours
    json.is_allocated_for_week order_hours == Admin::Reports::FetchPantryManagerSpends::ItemSpend::HOURS_MAP[:weekly]
  end
end