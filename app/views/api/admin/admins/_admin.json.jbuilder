customer = admin.profile&.profileable
is_customer = customer&.is_a?(CustomerProfile)

json.extract! admin, :id, :name, :email
case 
when kind == 'yordar_admin'
  json.partial! "api/admin/admins/yordar_admin", admin: admin
when kind == 'team_admin' && is_customer
  json.partial! "api/admin/admins/team_admin", customer: customer
when %w[account_manager pantry_manager company_team_admin].include?(kind) && is_customer
  json.partial! "api/admin/admins/access_permissions", customer: customer
end

if is_customer && is_yordar_admin?(user: current_user) && session[:original_admin_id].blank? && customer.company_team_admin?
  json.sign_in_path sign_in_as_admin_path(admin)
elsif is_customer
  json.sign_in_path sign_in_as_customer_path(admin)
else
  json.sign_in_path nil
end