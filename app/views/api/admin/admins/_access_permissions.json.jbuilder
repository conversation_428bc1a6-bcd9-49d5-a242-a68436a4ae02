json.customer do
  json.extract! customer, :id, :name
end
access_permissions = customer.admin_access_permissions.where(active: true).includes(:customer_profile).order('customer_profiles.customer_name ASC')
json.customers access_permissions.each do |access_permission|
  accessible_customer = access_permission.customer_profile
  json.extract! accessible_customer, :name, :email
  json.scope access_permission.scope
end
attached_company = customer.company
json.company_name attached_company&.name || customer.company_name
json.has_attached_company attached_company.present?