json.extract! supplier, :id, :name, :email, :is_searchable, :markup, :commission_rate, :company_address, :team_supplier, :close_from, :close_to

json.contact_numbers [supplier.phone, supplier.mobile].reject(&:blank?)
json.suburb supplier.company_address_suburb&.label
json.login_email supplier.user&.email
json.img supplier.profile&.avatar ? cl_image_path(supplier.profile.avatar, width: 100, height: 100, crop: 'fill') : nil
json.is_new supplier.created_at >= 2.week.ago

supplier_customers = supplier.customer_profiles.order(customer_name: :asc)
json.customer_profile_ids supplier_customers.map(&:id)
json.customers supplier_customers.each do |customer|
  json.extract! customer, :id, :email
  json.name customer.customer_name
end

if (supplier_user = supplier.user.presence)
  json.sign_in_path sign_in_as_supplier_path(supplier_user)
  json.menu_path sign_in_as_supplier_path(supplier_user, redirect_path: supplier_menu_path)
  json.closures_path sign_in_as_supplier_path(supplier_user, redirect_path: supplier_closure_dates_path)
else
  json.sign_in_path nil
end

if is_admin?
  json.restrictions_count supplier.customer_profile_ids.count
  json.overrides_count supplier.markup_overrides.where(active: true).count
end
