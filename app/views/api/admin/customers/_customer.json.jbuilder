favourite_customer_ids = @favourite_customer_ids || current_user&.favourite_customer_profile_ids
customer_user = customer.user

json.extract! customer, :id, :name, :email, :role, :team_admin, :company_team_admin
if current_user&.super_admin?
  json.admin customer_user.admin?
end
if is_admin?
  json.uuid customer.uuid
end
json.contact_numbers [customer.contact_phone, customer.mobile].reject(&:blank?)
user_suburb = customer.user&.suburb
json.suburb user_suburb&.label
json.is_own_account customer_user == current_user

attached_company = customer.company
json.company_name attached_company&.name || customer.company_name
json.has_attached_company attached_company.present?
json.is_favourite favourite_customer_ids.include?(customer.id)
json.is_new customer.created_at >= 1.week.ago

if customer_user.present?
  json.sign_in_path sign_in_as_customer_path(customer.user)
  if is_yordar_admin?(user: current_user) && customer.company_team_admin?
    json.admin_sign_in_path sign_in_as_admin_path(customer.user)
  end

  json.new_catering_order sign_in_as_customer_path(customer_user, redirect_path: next_app_supplier_search_url(category_group: 'office-catering', state: '_state_', suburb: '_suburb_'))
  json.new_pantry_order sign_in_as_customer_path(customer_user, redirect_path: next_app_supplier_search_url(category_group: 'office-snacks', state: '_state_', suburb: '_suburb_'))
  json.new_team_order customer.team_admin? ? sign_in_as_customer_path(customer_user, redirect_path: customer_new_team_orders_path) : nil
  json.new_custom_order is_admin? ? rails_admin.custom_order_path(model_name: 'customer_profile', id: customer.id) : nil
else
  json.sign_in_path nil
  json.new_catering_order nil
  json.new_pantry_order nil
  json.new_team_order nil
  json.new_custom_order nil
end