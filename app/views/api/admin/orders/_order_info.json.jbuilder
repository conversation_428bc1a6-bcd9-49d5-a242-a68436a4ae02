order ||= @order
order_invoice = order.invoice
order_card = order.credit_card
customer = order.customer_profile
billing_frequency = customer.billing_frequency || 'instantly'
if order.delivery_at.present?
  billing_at = billing_frequency == 'monthly' ? (order.delivery_at.end_of_month + 1.day) : (order.delivery_at.end_of_week + 1.day)
else
  billing_at = nil
end

json.extract! order, :po_number, :invoice_individually
json.billing_frequency billing_frequency
json.billing_date billing_at&.to_s(:date_verbose)
  
# show name with Woolworths Order ID
if woolworths_order = order.woolworths_order.presence
  json.name order.woolworths_order_name
end

if order.is_recurrent? && is_yordar_admin?
  pattern_text = {
    '1.week' => 'Weekly',
    '2.weeks' => 'Fortnightly',
    '1.month' => 'Monthly',
    '4.weeks' => 'Every 4 weeks'
  }[order.pattern]
  json.recurring_details do
    json.extract! order, :template_id, :recurrent_id, :renewed_from_id, :renewed_to_id
    json.pattern pattern_text
  end
end

if order_invoice.present?
  json.invoice do
    json.number order_invoice.number
    json.document_url order_invoice.latest_document&.url
    json.date order_invoice.to_at.to_s(:date_verbose)
  end
end

if order_card.present? && !order_card.pay_on_account?
  json.order_card do
    json.extract! order_card, :last4, :pay_on_account
  end
end

coordinates = Geocoder.search("#{order.delivery_address} #{order.delivery_suburb&.postcode}").first.try(:coordinates)
json.map_url coordinates.present? ? "#{yordar_credentials(:google, :maps_api_url)}?zoom=18&scale=2&size=300x120&markers=#{coordinates[0]},#{coordinates[1]}&key=#{yordar_credentials(:google, :maps_api_key)}" : nil

json.suppliers order.supplier_profiles.each do |supplier|
  json.extract! supplier, :id, :name, :phone, :mobile, :email
  json.image supplier.profile.avatar.present? ? cl_image_path(supplier.profile.avatar, width: 100, height: 100, crop: 'fill') : nil
end