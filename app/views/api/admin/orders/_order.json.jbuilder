customer = order.customer_profile
json.extract! order, :id, :name, :status, :order_type, :order_variant

json.is_recurrent order.is_recurrent?
json.customer_name customer&.name
customer_user = customer&.user
if customer_user.present?
  json.customer_sign_in_path sign_in_as_customer_path(customer_user)

  if order.is_team_order?
    show_path = sign_in_as_customer_path(customer_user, redirect_path: team_order_path(order))
  else
    show_path = sign_in_as_customer_path(customer_user, redirect_path: order_show_path(order))
  end

  edit_path = case
  when is_admin? && order.is_event_order?
    rails_admin.custom_order_path(model_name: 'custom_order', id: order.id)
  when order.is_team_order?
    sign_in_as_customer_path(customer_user, redirect_path: edit_team_order_path(order))
  else
    sign_in_as_customer_path(customer_user, redirect_path: next_app_order_edit_url(order))
  end

  json.order_view_path show_path
  json.order_edit_path edit_path
else
  json.customer_sign_in_path nil
  json.order_view_path nil
  json.order_edit_path nil
end

if is_yordar_admin? && params[:custom_orders_only].present? && order.is_event_order? && order.commission.present?
  yordar_commission = (1 - (1 - (0.0 / 100)) / (1 + (order.commission / 100))) * 100
  json.yordar_commission number_to_percentage(yordar_commission, precision: 2)
  json.commission_below_recommendation yordar_commission < 20
end

# delivery date
json.delivery_at_raw order.delivery_at
json.delivery_at order.delivery_at&.to_s(:full_verbose)
json.delivery_date order.delivery_at&.to_s(:full_date)
json.delivery_time order.delivery_at&.to_s(:time_only)
json.delivery_day order.delivery_at&.to_s(:weekday)
json.delivery_instruction order.delivery_instruction
suppliers = order.order_lines.map(&:supplier_profile).uniq
if suppliers.present?
  json.suppliers do
    json.array! suppliers do |supplier|
      json.name supplier.name
      avatar = supplier.profile&.avatar
      json.img avatar.present? ? cl_image_path(avatar, width: 100, height: 100, crop: 'fill', quality: 'auto,fl_lossy,f_auto') : nil
    end
  end
else
  json.set! :suppliers, []
end

json.address order.delivery_address_arr.join(', ')

json.totals do
  json.subtotal order.customer_subtotal.present? ? number_to_currency(order.customer_subtotal) : nil
  json.gst order.customer_gst.present? ? number_to_currency(order.customer_gst) : nil
  json.total order.customer_total.present? ? number_to_currency(order.customer_total) : nil
end
