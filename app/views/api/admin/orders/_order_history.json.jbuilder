json.array! order.versions.each do |version|
  json.whodunnit version.whodunnit.present? ? "#{User.find(version.whodunnit).name} #{version.whodunnit}" : 'Not Known'
  json.event version.event
  json.created_at version.created_at.to_s(:full_verbose)
  json.changes version.changeset.each do |field, changes|
    json.field field.titleize
    json.old_value changes.first
    json.new_value changes.last
  end
end

