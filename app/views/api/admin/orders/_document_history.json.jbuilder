order_documents = order.documents.order(:version)
supplier_documents = Document.where(documentable: order.order_suppliers).order(:version)
all_documents = order_documents + supplier_documents

json.array! all_documents.group_by(&:documentable).each do |documentable, documents|
  supplier = documentable.is_a?(OrderSupplier) ? documentable.supplier_profile : nil
  json.supplier supplier.present? ? supplier.name : nil
  json.documents documents.each do |document|
    json.name (document.name || document.fallback_name)&.titleize
    json.extract! document, :url, :version
  end
end