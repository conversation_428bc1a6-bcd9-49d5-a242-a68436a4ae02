skipped_orders = Order.where(id: notification.info['skipped_orders'])
pushed_orders = Order.where(id: notification.info['pushed_orders'])

json.skipped_orders skipped_orders.map do |order|
  json.partial! 'api/admin/notifications/show/simple_order_details', order: order
end

json.pushed_orders pushed_orders.map do |order|
  json.partial! 'api/admin/notifications/show/simple_order_details', order: order
end