customer = order.customer_profile

json.extract! order, :id, :name, :order_variant, :status
json.address order.delivery_address_arr.join(', ')
json.customer_name customer&.name
json.delivery_at order.delivery_at.to_s(:full_verbose)

customer_user = customer&.user
if customer_user.present?
  json.order_view_path sign_in_as_customer_path(customer_user, redirect_path: order_show_path(order))
  if is_admin? && order.order_variant == 'event_order'
    edit_path = rails_admin.custom_order_path(model_name: 'custom_order', id: order.id)
  else
    edit_path = sign_in_as_customer_path(customer_user, redirect_path: next_app_order_edit_url(order))
  end
  json.order_edit_path edit_path
else
  json.customer_sign_in_path nil
  json.order_view_path nil
  json.order_edit_path nil
end

json.suppliers order.supplier_profiles.each do |supplier|
  json.extract! supplier, :id, :name, :phone, :mobile, :email
  json.image supplier.profile.avatar.present? ? cl_image_path(supplier.profile.avatar, width: 100, height: 100, crop: 'fill') : nil
end