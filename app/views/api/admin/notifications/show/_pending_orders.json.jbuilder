json.delivery_on notification.info['delivery_on']

custom_orders = Order.where(id: notification.info['custom_orders'])
normal_orders = Order.where(id: notification.info['normal_orders'])

json.custom_orders custom_orders.map do |order|
  json.partial! 'api/admin/notifications/show/simple_order_details', order: order
end

json.normal_orders normal_orders.map do |order|
  json.partial! 'api/admin/notifications/show/simple_order_details', order: order
end