customer_quote = notification.loggable
customer = notification.scopable

json.customer_name customer&.name
json.extract! customer_quote, :id
json.kind customer_quote.kind.titleize

customer_quote.form_data.except('type').each do |field, value|
  formatted_value = case
  when field == 'date'
    DateTime.parse(value).strftime('%I:%M%p on %d/%m/%Y')
  when value.is_a?(Array)
    value.join(', ')
  else
    value
  end
  json.set! field.titleize, formatted_value
end

