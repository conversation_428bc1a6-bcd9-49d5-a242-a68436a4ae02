holiday = notification.loggable

message = case notification.event
when 'upcoming-public-holiday'
  pushed_order_ids = notification.info['pushed_orders']
  skipped_order_ids = notification.info['skipped_orders']
  affected_orders_count = pushed_order_ids.size + skipped_order_ids.size

  "Handled orders for upcoming public holiday <b>#{holiday.name}</b> on #{holiday.on_date.to_s(:full_date)} - <b>#{affected_orders_count}x orders affected</b>"
when 'monthly-calendar-event'
  msg = "<b>#{holiday.name}</b> is coming up on #{holiday.on_date.to_s(:full_date)}"
  if (info_states = notification.info['states'].presence)
    msg += " - in <b>#{info_states.join(', ')}</b>"
  elsif holiday.state.present?
    msg += " - in <b>#{holiday.state}</b>"
  end
  msg += ' (orders will be affected)' if holiday.push_to.present?
  msg
end

json.extract! holiday, :name
json.date holiday.on_date.to_s(:full_date)
json.affected_orders_count affected_orders_count
json.message message