customer_budget = notification.loggable
return if customer_budget.blank?

customer = customer_budget.customer_profile
budget_info = notification.info
po_number = budget_info['po_number']

message = "<b>#{customer.name}</b> has an reached <b>#{budget_info['spend_percentage']}%</b> of their"
message += po_number.present? ? ' budget' : ' monthly budget'
message += " of $#{budget_info['budget']}"
message += " (for #{po_number})" if po_number.present?

json.message message
json.info budget_info