supplier = notification.loggable
notification_info = notification.info

json.extract! supplier, :name

message = case notification.event
when 'new-supplier-registration'
  if notification_info['category_group'].present?
    supplier_catogory = notification_info['category_group'] == 'catering-services' ? 'Catering' : 'Snacks and Pantry'
    msg = "<b>#{supplier.name}</b> has registered as a new <b>#{supplier_catogory}</b> supplier"
    msg += ', also tagged itself as a <b>Team Supplier</b>' if notification_info['is_team_supplier'].present?
    msg
  else
    "<b>#{supplier.name}</b> has registered as a new supplier"
  end
when 'supplier-agreement-signed'
  "<b>#{supplier.name}</b> signed the supplier agreement"
when 'searchable-updated'
  "<b>#{supplier.name}</b> is now set as <b>#{notification_info['is_searchable'] ? 'Searchable (Live)' : 'Not-Searchable (inactive)'}</b>"
when 'margin-updated'
  "<b>#{supplier.name}</b> has had their margin updated. Markup: <b>#{notification_info['markup']}%</b>, Commission Rate: <b>#{notification_info['commission_rate']}%</b>"
end

json.message message