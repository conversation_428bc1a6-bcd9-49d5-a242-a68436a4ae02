is_viewed ||= false

json.extract! notification, :id, :event, :loggable_type, :severity
json.event_at notification.created_at

json.viewed is_viewed
assignable_event = EventLog::ASSIGNABLE_EVENTS.include?(notification.event)
is_assignable = assignable_event && (notification.assigned_to.blank? || notification.assigned_to == current_user)

json.is_assignable is_assignable
if assignable_event && notification.assigned_to.present?
  json.assigned_to do
    json.initials customer_name_helper(:initials, notification.assigned_to.name)
    json.name notification.assigned_to.name
  end
end

if (notification_template = notification_template_for(notification).presence)
  json.partial! "api/admin/notifications/#{notification_template}", notification: notification
end
