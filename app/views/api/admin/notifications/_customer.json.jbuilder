customer = notification.loggable
info = notification.info

message = case notification.event
when 'new-customer-registration'
  "<b>#{customer.name}</b> has registered as a new customer, with role of <b>#{info['role']}</b>"
when 'company-team-admin-request'
  "<b>#{customer.name}</b> has requested to become a company team admin.#{info['message'].present? ? " <b>Message:</b> #{info['message']}" : ''}"
end

json.message message