json.extract! company, :id, :name, :can_pay_on_account, :invoice_by_po, :requires_po, :can_pay_by_credit_card, :payment_term_days, :accounting_software

# reloaded the customer to ignore the customer profiles join done to determine access
company.reload if is_admin?
company_customers = company.customer_profiles.order(customer_name: :asc)
json.customer_profile_ids company_customers.map(&:id)
json.customers company_customers.each do |customer|
  json.extract! customer, :id, :email
  json.name customer.customer_name
end