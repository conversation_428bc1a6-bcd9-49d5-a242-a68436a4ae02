json.extract! coupon, :id, :code, :description, :valid_from, :valid_until, :redemption_limit, :coupon_redemptions_count, :amount, :type, :order_ids

json.expired coupon.expired?
json.can_redeem coupon.can_redeem?
json.redeemed "#{coupon.coupon_redemptions_count}/#{coupon.redemption_limit}"
json.formatted_valid_from coupon.valid_from&.to_s(:date_verbose)
json.formatted_valid_until coupon.valid_until&.to_s(:date_verbose)
