<% content_for :title, "Leave a review for #{day.capitalize}'s menu" %>

<section class="has-gutter medium-gray-bg">
  <div class="row">
    <div class="small-12 columns">
      <div class="card">
        <div class="card__container">
          <h2>Rate <%= day.capitalize%>'s Menu:</h2>

          <%= form_for WeeklyMenuReview.new, url: weekly_menu_reviews_path, html: { onsubmit: "return validateForm();" } do |f|%>
            <%= hidden_field_tag :weekly_menu_id, weekly_menu.id %>
            <%= hidden_field_tag :day, day %>

            <div class="row">
              <div class="small-12 medium-4 columns">
                <%= f.label :first_name, "First name:" %>
              </div>
              <div class="small-12 medium-8 large-5 columns">
                <%= f.text_field :first_name %>
              </div>
            </div>

            <div class="row">
              <div class="small-12 medium-4 columns">
                <%= f.label :last_name, "Last name:" %>
              </div>
              <div class="small-12 medium-8 large-5 columns">
                <%= f.text_field :last_name %>
              </div>
            </div>

            <%=
              render partial: "star_ratings", locals: {
                title: "Food taste:",
                target: "#weekly_menu_review_taste"
              }
            %>
            <%= f.hidden_field :taste %>

            <%=
              render partial: "star_ratings", locals: {
                title: "Presentation:",
                target: "#weekly_menu_review_presentation"
              }
            %>
            <%= f.hidden_field :presentation %>

            <%=
              render partial: "star_ratings", locals: {
                title: "Food quantity and variety:",
                target: "#weekly_menu_review_quantity"
              }
            %>
            <%= f.hidden_field :quantity %>

            <%=
              render partial: "thumb_rating", locals: {
                title: "Would you like to see this menu again?",
                target: "#weekly_menu_review_see_again"
              }
            %>
            <%= f.hidden_field :see_again %>

            <div class="row">
              <div class="small-12 medium-4 columns">
                <%= f.label :comments, "Additional comments (will only be shown to company admin, supplier and Yordar staff):" %>
              </div>
              <div class="small-12 medium-8 large-5 columns">
                <%= f.text_area :comments %>
              </div>
            </div>

            <%= f.submit "Submit", class: 'button submit', data: { disable_with: "Please wait.." } %>

          <% end %>
        </div>
      </div>
    </div>
  </div>
</section>

<% content_for :javascript_includes do %>
  <%= javascript_include_tag 'pages/weekly-menu-review-new' %>
<% end %>
