%ul.list-unstyled.review-order-lines.hidden
  - @order_lines.each do |order_line|
    %li.delivery-details-suppliers--customer.mb-1-2
      - if order_line_image = order_line.menu_item&.ci_image.presence
        = cl_image_tag(order_line_image, width: 400, height: 400, crop: 'fill', quality: "auto,fl_lossy,f_auto", alt: order_line.name, class: "circle-icon")
      - else
        .circle-icon
          = order_line.name.first
      %strong.mr-1
        = "#{order_line.quantity}x"
      %span
        = order_line.name
