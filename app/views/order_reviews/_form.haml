:ruby
  rating_fields = [
    {
      label: 'Food taste',
      field: :food_taste_score,
    },
    {
      label: 'Presentation',
      field: :presentation_score,
    },
    {
      label: 'Delivery punctuality',
      field: :delivery_punctuality_score
    },
  ].map{|rating| OpenStruct.new(rating) }

- if @errors.present?
  %p.form-error.show
    %strong Error:
    - @errors.each do |error|
      = error

= form_for @order_review, html: { class: 'new-order-review' } do |f|
  = f.hidden_field :supplier_profile_id, value: @supplier.id
  = f.hidden_field :order_id, value: @order.id

  - rating_fields.each do |rating|
    = render 'order_reviews/star_ratings', rating: rating, f: f

  .row
    .small-12.medium-4.columns
      = f.label :comment, 'Give feedback to the supplier (optional):'
    .small-12.medium-8.large-5.columns
      = f.text_area :comment, rows: 3

  %a.button.submit-review
    Submit
