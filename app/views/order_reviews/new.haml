:ruby
  review_submitted = @order_review.valid? && @order_review.persisted?
- content_for :webpack, 'front_end'

%section.has-gutter.medium-gray-bgx{ data: { view_new_order_review: true } }
  .row
    .small-12.columns
      .card
        .card__container

          - if review_submitted
            %h2 Thank you for Reviewing
          - else
            %h2 Please rate your order:

          %p
            #{@order.name} 
            %strong
              = "(##{@order.id})"
            delivered on
            = @order.delivery_at.to_s(:date_verbose)

          %p.delivery-details-suppliers--customer
            %span.circle-icon
              - if @supplier.profile.avatar
                = resize_image_tag(@supplier.profile.avatar, 'Supplier Avatar', 200, 200, 'fill')
              - else
                = supplier_name_helper(:initials, @supplier.company_name)
            %strong
              = @supplier.name

            %a.view-review-order-lines.ml-2
              see order
            
          = render 'order_reviews/order_lines'

          - if !review_submitted
            = render 'order_reviews/form'
            
