:ruby
  rating_fields ||= OrderReviews::FetchRatingSummary::RATING_FIELDS
.row
  - rating_fields.each do |field|
    - count = order_review.send("#{field}_score")
    .small-12.columns.rating-review.no-gutter{ class: "medium-#{12/rating_fields.size}" }
      = "#{field.humanize}:"
      - count.times do
        .star.selected
      - (5 - count).times do
        .star

%p.comment
  = order_review.comment
%p
  %small
    - review_order = order_review.order
    Order # #{review_order.id} on #{review_order.created_at.to_s(:date_verbose)}
%hr
