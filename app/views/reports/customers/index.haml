:ruby
  customer = session_profile
  customer_pos = customer.customer_purchase_orders.where(active: true)
  customer_pos = customer_pos.joins(:orders).where('orders.delivery_at > ?', Time.zone.now - 1.year).distinct
  po_options = customer_pos.map do |cpo|
    [cpo.po_number, cpo.id]
  end

- content_for :header_title, 'Spend Reports'
  
- if params[:jquery].blank?
  %div{ data: { view_customer_reports: { isAdmin: is_admin?, isTeamAdmin: customer.team_admin?, customerID: customer.id, poOptions: po_options }.to_json } } 

- else
  %fieldset.report-fields.customer-reports-field{ data: { view_customer_reports_jq: true } }

    = render 'reports/customers/filters'

    = render 'reports/customers/exports'

    %br/

    %h4.fieldset-title.report-label.hide Order Spends

    #order-spend-container.spend-chart{ style: 'min-width: 310px; height: 400px; margin: 0 auto' }
  
