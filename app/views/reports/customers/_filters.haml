:ruby
  today = Time.zone.today
  report_type_options = ['Monthly', 'Weekly'].map{|option| [option, option.downcase] }
  customer_pos = session_profile.customer_purchase_orders
  customer_pos = customer_pos.joins(:orders).where('orders.delivery_at > ?', Time.zone.now - 1.year).distinct
  po_options = customer_pos.map do |cpo|
    [cpo.po_number, cpo.id]
  end
  po_options << ['Not connected to any PO', 'no-po']

  category_options = { 'Catering': 'catering-services' , 'Kitchen/Pantry': 'kitchen-supplies' }.to_a

  datepicker_config = {
    options: {
      dateFormat: 'dd-mm-yy',
      showWeek: true,
      firstDay: 1,
    },
    is_reports: true,
  }

= form_tag api_reports_path, method: :get, class: 'filter-form' do
  .row
    .small-12.medium-3.columns
      %label.report-date-lable Start Day
      = text_field_tag :start_date, (today - 5.months).beginning_of_month.to_s(:date) , class: 'datepicker budget', data: { view_datepicker_input: datepicker_config.to_json }
    .small-12.medium-3.columns
      %label.report-date-lable End Day
      = text_field_tag :end_date, today.end_of_month.to_s(:date), class: 'datepicker budget', data: { view_datepicker_input: datepicker_config.to_json }

    .small-12.medium-3.columns.no-gutter
      %label.report-date-lable Report Period
      = select_tag :report_type, options_for_select(report_type_options)
      = hidden_field_tag :customer_id, session_profile.id
      = hidden_field_tag :source_type, 'CustomerProfile'

    .small-12.medium-3.columns
      = button_tag 'Generate', class: 'button hidden', data: { next_url: customer_profile_path }

  .row
    .small-12.medium-3.columns
      %label.report-date-lable Filter by PO
      = select_tag :purchase_order_id, options_for_select(po_options), include_blank: 'All'

    .small-12.medium-3.columns
      %label.report-date-lable Filter by order category
      = select_tag :category_group, options_for_select(category_options), include_blank: 'All categories'

