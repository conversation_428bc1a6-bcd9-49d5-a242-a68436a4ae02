:ruby
  detail_options = [
    ['Orders data', 'orders'],
    ['Products data', 'products'],
  ]
  if is_admin?
    detail_options << ['Order Lines data', 'order_lines']
    detail_options << ['Invoice data', 'invoices']
  end
.row
  .small-12.columns
    %a.export-toggle.uppercase{ href: 'javascript:void(0)' }
      Export data

.export-panel.hidden
  %hr/
  = form_tag api_reports_path(format: 'csv'), method: :get, class: 'export-form' do
    .row
      .small-12.columns
        %h5
          Export data
          %a.export-toggle.uppercase.medium-float-right.hidden{ href: 'javascript:void(0)' }
            close
      .small-12.medium-3.columns
        %label.report-date-lable What details to include?
        = select_tag :detail_type, options_for_select(detail_options)

      - if is_admin? && session_profile.company.present?
        .small-12.medium-3.columns
          %label
            &nbsp;
          %label{ for: 'company_wide', style: 'padding-top: 0.5rem' }
            = check_box_tag :company_wide
            gather company wide data

    .row
      .small-12.columns
        = button_tag 'Export', class: 'button export-report-csv'
