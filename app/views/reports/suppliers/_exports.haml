:ruby
  detail_options = [
    ['Orders data', 'orders'],
    ['Products data', 'products']
  ]
.row
  .small-12.columns
    %a.export-toggle.uppercase{ href: 'javascript:void(0)' }
      Export data

.export-panel.hidden
  %hr/
  = form_tag api_reports_path(format: 'csv'), method: :get, class: 'export-form' do
    .row
      .small-12.columns
        %h5
          Export data
          %a.export-toggle.uppercase.medium-float-right.hidden{ href: 'javascript:void(0)' }
            close
      .small-12.medium-3.columns
        %label.report-date-lable What details to include?
        = select_tag :detail_type, options_for_select(detail_options)

    .row
      .small-12.columns
        = button_tag 'Export', class: 'button export-report-csv'
