:ruby
  today = Time.zone.today
  report_type_options = ['Monthly', 'Weekly'].map{|option| [option, option.downcase] }

  datepicker_config = {
    options: {
      dateFormat: 'dd-mm-yy',
      showWeek: true,
      firstDay: 1,
    },
    is_reports: true,
  }

= form_tag api_reports_path, method: :get, class: 'filter-form' do
  .row
    .small-12.medium-3.columns
      %label.report-date-lable Start Day
      = text_field_tag :start_date, (today - 5.months).beginning_of_month.to_s(:date) , class: 'datepicker budget form-input', data: { view_datepicker_input: datepicker_config.to_json }
    .small-12.medium-3.columns
      %label.report-date-lable End Day
      = text_field_tag :end_date, (today - 1.week).end_of_week.to_s(:date), class: 'datepicker budget form-input', data: { view_datepicker_input: datepicker_config.to_json }

    .small-12.medium-3.columns.no-gutter
      %label.report-date-lable Report Period
      = select_tag :report_type, options_for_select(report_type_options), {class: 'form-input'}
      = hidden_field_tag :supplier_id, session_profile.id
      = hidden_field_tag :source_type, 'SupplierProfile'

    .small-12.medium-3.columns
      = button_tag 'Generate', class: 'button hidden', data: { next_url: customer_profile_path }
