:ruby
  team_order ||= @team_order
  formatted_date = team_order.delivery_at.strftime("%l:%M%P on %A the #{team_order.delivery_at.day.ordinalize} of %b %Y")
  current_status = team_order_current_status(team_order)
  can_edit = %w[pending cutoff_approaching].include?(current_status) || (is_admin? && %w[pending new amended confirmed].include?(team_order.status))
  if team_order.is_recurring_team_order?
    lister_options = {
      scoped_to: 'recent_fortnight',
      scoped_time: team_order.delivery_at,
    }
  else
    lister_options = {}
  end
  package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order, options: lister_options).call
  package_orders = package_orders.order(delivery_at: :asc, id: :asc)

- content_for :back_button do
  - if package_orders.present?
    = link_to team_order_package_path(team_order) do
      %span.customer-header-back-link
  - else
    = link_to customer_team_orders_path do
      %span.customer-header-back-link

- content_for :header_title do
  %span Team Order - ##{team_order.id}

- content_for :header_elements do
  .between-flex
    - if package_orders.present?
      %ul.dropdown.menu.linked-orders{ data: { dropdown_menu: '' } }
        %li.is-dropdown-submenu-parent
          %span Linked Orders
          %ul.menu.customer-header__dropdown.customer-header__dropdown--linked
            %li
              = link_to 'Show All', team_order_package_path(team_order)
            - package_orders.each do |package_order|
              %li
                - order_name = relative_weekday(package_order.delivery_at.to_date, format: 'numeric')
                - if package_order == team_order
                  = link_to order_name, 'javascript:void(0)', class: 'show-all-linked'
                - else
                  = link_to order_name, team_order_path(package_order)

.order-show{ data: { view_team_order_show: true } }
  %h2= team_order.name
  .order-show-header
    .order-show__options
      %p.date= formatted_date
      - if team_order.status != 'delivered'
        %a.button.hollow{ href: edit_team_order_path(team_order) } Edit

    .order-show__options
      = render 'orders/customer_documents', order: team_order

  .order-show-body
    = render 'team_orders/show/attendees_panel', team_order: team_order, can_edit: can_edit

    = render 'team_orders/show/order_details', team_order: team_order, current_status: current_status
