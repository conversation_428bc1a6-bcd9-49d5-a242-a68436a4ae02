:ruby
  is_package_order ||= false
  errors ||= []

- content_for :header_title, 'Team Order'
.no-upcoming
  - if errors.present?
    %h2
      Team Order
      = 'Package' if is_package_order
    - errors.each do |error|
      %p= error
  - else
    %h2
      No Team Order
      = 'Package' if is_package_order
    %p
      The team order
      = 'package' if is_package_order
      does not exist OR you don't have access to this team order.

    %img{src: '/assets/no-upcoming.png', width: '350px'}

  %a.button{ href: customer_team_orders_path }
    Go to Team Orders
