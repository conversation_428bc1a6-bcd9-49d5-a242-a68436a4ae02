:ruby
  suppliers ||= []
  suppliers_minimums ||= {}
  budget ||= nil
  selected_order_suppliers ||= []
  favourite_team_supplier_ids ||= []

- suppliers.to_a.each do |supplier|
  - selected_order_supplier = selected_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile_id == supplier.id }
  = render 'team_orders/supplier', supplier: supplier, supplier_minimums: suppliers_minimums[supplier], budget: budget, selected_order_supplier: selected_order_supplier, is_favourite: favourite_team_supplier_ids.include?(supplier.id)

- if selected_order_suppliers.present?
  - selected_order_suppliers.each do |team_order_supplier|
    = hidden_field_tag 'order[supplier_ids][]', team_order_supplier.supplier_profile_id
- else
  = hidden_field_tag 'order[supplier_ids][]', ''
