:ruby
  supplier_minimums ||= nil
  budget ||= 10
  selected_order_supplier ||= nil
  selected_menu_sections = selected_order_supplier.present? ? selected_order_supplier.selected_menu_sections : nil
  for_modal ||= params[:for_modal].present?

%li.team-supplier.team-order-list__row.row{ data: { supplier_id: supplier.id, minimum_spend: supplier_minimums.present? ? supplier_minimums.minimum_spend : 0, is_favourite: is_favourite } }

  .small-12.columns.no-gutter{ class: for_modal ? 'medium-5' : 'medium-4' }
    .small-12.columns.no-gutter.team-supplier-image-container{ class: for_modal ? 'medium-4' : 'medium-2' }
      = cl_image_tag(supplier.profile.avatar, width: 600, height: 600, crop: 'fill', quality: "auto,fl_lossy,f_auto", alt: supplier.company_name)
    .team-order-list__cell.team-supplier-name.small-12.columns.no-gutter{ class: for_modal ? 'medium-8' : 'medium-10' }
      %span.mobile-tag Supplier Name: 
      = supplier.company_name.truncate(40)
      - if supplier.is_new?
        %span.team-supplier-new new
      - if @rate_card_supplier_ids.include?(supplier.id) || @markup_override_supplier_ids.include?(supplier.id)
        %span.team-supplier-custom custom pricing
      - if @custom_menu_supplier_ids.include?(supplier.id)
        %span.team-supplier-custom custom menu

  .small-12.columns.no-gutter{ class: for_modal ? 'medium-4' : 'medium-6' }

    .team-order-list__cell.small-12.columns.no-gutter{ class: for_modal ? 'medium-6' : 'medium-3' }
      %span.mobile-tag Min Order:
      = supplier_minimums.present? ? format_price(supplier_minimums.minimum_spend) : '-'

    - if !for_modal
      .team-order-list__cell.small-12.medium-2.columns.no-gutter
        %span.mobile-tag Rating: 
        %span.team-supplier-rating{class: supplier.rating >= 3 ? 'team-supplier-rating--approval' : '' }= supplier.rating > 0 ? supplier.rating : '-'
    .team-order-list__cell.small-12.columns.no-gutter{ class: for_modal ? 'medium-6' : 'medium-4' }
      %span.mobile-tag Lead Time: 
      = supplier_minimums.present? ? supplier_minimums.lead_time : '-'

    - if !for_modal
      - best_for = supplier_minimums.present? ? supplier_minimums.minimum_spend/budget.to_i : 1
      .team-order-list__cell.small-12.medium-3.columns.no-gutter
        %span.mobile-tag Best For: 
        %span.best-for-teams<>
          = best_for.to_i
        + teams

  .small-12.columns.no-gutter.team-order-list__supplier-btns{ class: for_modal ? 'medium-3' : 'medium-2' }
    - if !for_modal
      .favourite-supplier
        - add_or_remove = "#{is_favourite ? 'Remove from' : 'Add to'} favourites"
        %label.favourite-team-supplier{ for: "favourite-supplier-#{supplier.id}", title: add_or_remove }
          = check_box_tag "favourite-supplier-#{supplier.id}", true, false, { data: { supplier_id: supplier.id, url: api_favourite_supplier_path(supplier, kind: 'team_order') }, class: is_favourite ? 'remove-fav-supp': 'add-fav-supp' }
          %div

    %div
      %a.button.tiny.gray-btn.supplier-menu{ style: 'margin: 0', data: { url: retrieve_supplier_menu_api_team_orders_path(slug: supplier.slug, selected_menu_sections: selected_menu_sections), open: 'modal-supplier-menu.reveal' } }
        Menu

    %div
      - if selected_menu_sections.present?
        - selected_menu_sections.each do |menu_section_id|
          = hidden_field_tag "order[selected_menu_sections][#{supplier.id}][]", menu_section_id
      %button.small-6.columns.choose-supplier-btn.team-order-list__btn.circle-icon.float-right{ type: 'button', class: selected_order_supplier.present? ? 'selected-supplier' : '' }
