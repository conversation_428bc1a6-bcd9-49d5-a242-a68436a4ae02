:ruby
  customer ||= session_profile

  customer_credit_cards = customer.credit_cards.where(enabled: true, saved_for_future: true).order(created_at: :desc)
  customer_credit_cards = customer_credit_cards.where(pay_on_account: false)
  customer_credit_cards = customer_credit_cards.where.not(stripe_token: nil) if Time.zone.now >= Time.zone.parse(yordar_credentials(:stripe, :migration_date))

  can_pay_on_account = customer.can_pay_on_account?
  can_pay_by_credit_card = customer.can_pay_by_credit_card?

  customer_credit_cards = CreditCard.none if !can_pay_by_credit_card

  if order.credit_card_id.present? && !order.credit_card.pay_on_account? && customer_credit_cards.map(&:id).exclude?(order.credit_card_id)
    customer_credit_cards = customer_credit_cards.to_a.unshift(order.credit_card)
  end

  pay_on_account = can_pay_on_account && (order.credit_card_id.blank? || order.credit_card.pay_on_account? || order.credit_card.auto_pay_invoice)

  pay_on_account_class = case
  when pay_on_account
    'active'
  when !can_pay_on_account
    'disabled'
  end
  pay_by_card_class = case
  when !pay_on_account
    'active'
  when !can_pay_by_credit_card
    'disabled'
  end
.row{ data: { view_team_order_payment_form: true } }
  .small-12.columns.no-gutter
    .payment-options
      %div.payment-options__toggle{ class: pay_on_account_class, data: { type: 'invoice' }}
        %p
          = can_pay_on_account ? 'Pay on Account' : 'Pay on Account (not approved)'

      %div.payment-options__toggle{ class: pay_by_card_class, data: { type: 'card' } }
        %p
          = can_pay_by_credit_card ? 'Pay by Credit Card' : 'Pay by Credit Card (not approved)'

  .small-12.columns.payment-options__panel.payment-options__panel--invoice{ class: ('hidden' if !pay_on_account) }
    = render 'team_orders/forms/payments/pay_on_account', pay_on_account: pay_on_account, order: order, customer_credit_cards: customer_credit_cards

  .small-12.columns.payment-options__panel.payment-options__panel--card{class: ('hidden' if pay_on_account) }
    = render 'team_orders/forms/payments/card_payment', pay_on_account: pay_on_account, order: order, customer_credit_cards: customer_credit_cards
