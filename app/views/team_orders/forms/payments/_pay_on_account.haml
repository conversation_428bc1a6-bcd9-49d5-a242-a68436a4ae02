:ruby
  customer ||= session_profile
  billing_frequency = customer.billing_frequency
  has_nominated_card = customer_credit_cards.reject(&:expired?).detect(&:auto_pay_invoice).present?

.payment-options__panel--content
  - if billing_frequency == 'instantly'
    %h5 Individual Billing
    %p This order will be invoiced individually on delivery. You've currently set your account to be billed per order.
  - else
    %h5 Choose Billing Method
    %p
      You've currently set your account to be billed #{billing_frequency}. You can choose to either add this to your #{billing_frequency} billing or invoice it separately as an individual order.
    %select.form-input{ name: 'invoice_order_individually' }
      %option{ value: 'false', selected: !order.invoice_individually }
        = "Add to my #{billing_frequency} billing"
      %option{ value: 'true', selected: order.invoice_individually }
        = "Invoice separately from #{billing_frequency} billing"

  - if has_nominated_card
    %p
      %em
        %sup *
        The invoice will automatically be paid using your nominated card.
