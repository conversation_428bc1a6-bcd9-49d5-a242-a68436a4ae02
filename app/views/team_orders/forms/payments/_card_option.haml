:ruby
  display_card = "#{credit_card.brand_label} (ending in #{credit_card.last4})"

- if credit_card.expired?
  %option{ value: credit_card.id, disabled: 'disabled', data: { brand: credit_card.brand }}
    #{display_card} expired
- elsif credit_card.present?
  %option{ value: credit_card.id, selected: credit_card == selected_card, data: { brand: credit_card.brand, percent: credit_card.surcharge_percent, fee: credit_card.surcharge_fee, is_old: credit_card.stripe_token.blank?.to_s } }
    = display_card
