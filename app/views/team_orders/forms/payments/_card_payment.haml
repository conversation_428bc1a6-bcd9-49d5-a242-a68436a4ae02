:ruby
  active_cards = customer_credit_cards.reject(&:expired?).reject(&:auto_pay_invoice)
  has_valid_cards = active_cards.present?

  stripe_cards, eway_cards = customer_credit_cards.reject(&:auto_pay_invoice).partition{|credit_card| credit_card.stripe_token.present? }

  selected_card = !pay_on_account && (order.credit_card || stripe_cards.first || eway_cards.first)
  select_class = !has_valid_cards ? 'hidden' : ''
  select_class += selected_card.present? ? selected_card.brand : ''

.payment-options__panel--content
  .payment-options__panel--card-heading
    %h5 Select Credit Card
    .add-new-credit-card-btn.button.small
      = has_valid_cards ? 'Add New Card' : 'Saved Cards'
  %p This order will be individually invoiced.
  %select#credit-card-select.form-input{ placeholder: 'please select', class: select_class, disabled: !has_valid_cards }
    - if eway_cards.present?
      %optgroup{ label: 'New cards'}
        - stripe_cards.each do |credit_card|
          = render 'team_orders/forms/payments/card_option', credit_card: credit_card, selected_card: selected_card

      %optgroup{ label: 'Old cards'}
        - eway_cards.each do |credit_card|
          = render 'team_orders/forms/payments/card_option', credit_card: credit_card, selected_card: selected_card

    - elsif stripe_cards.present?
      - stripe_cards.each do |credit_card|
        = render 'team_orders/forms/payments/card_option', credit_card: credit_card, selected_card: selected_card

  #credit-card-charge-note.card-charge-note{ class: (!has_valid_cards || pay_on_account ? 'hidden' : '') }
    %span.card-type= selected_card.present? ? selected_card.label.split(' (').first.strip : ''
    credit card payments will incur a
    %span.card-percent= selected_card.present? && selected_card.surcharge_percent.present? ? number_to_percentage(selected_card.surcharge_percent, precision: 1) : ''
    %span.card-fee= selected_card.present? && selected_card.surcharge_fee.present? ? "+ #{(selected_card.surcharge_fee*100).to_i}c" : ''
    surcharge.
    %strong.card-deprecation{ class: (selected_card.blank? || selected_card.stripe_token.present? ? 'hidden' : '') }
      %br
      This card is saved in our older system and we advise not to use it.
      %br/
      Please re-enter your card details or add a new card.

  .new-credit-card-container{ class: ( has_valid_cards ? 'hidden' : ''), data: { view_stripe_card_form: true } }
    = render 'credit_cards/stripe_form', show_charge_notes: true, has_order: true
