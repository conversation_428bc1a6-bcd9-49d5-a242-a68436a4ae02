:ruby
  item_rate_cards ||= []
  markup_override ||= nil
  letter_icons = item_letter_icons(menu_item: menu_item)
  serving_sizes = menu_item.serving_sizes.where(archived_at: nil)
  serving_sizes = serving_sizes.where(available_for_team_order: true)
  gst_country = request_country_code

  if serving_sizes.present?
    all_serving_prices_equal = serving_sizes.map do |serving_size|
      discount_pricing = item_rate_cards.detect{|rc| rc.serving_size_id == serving_size.id }
      discount_pricing.present? ? discount_pricing.price_inc_gst(gst_country: gst_country).to_f : serving_size.markup_price(gst_country: gst_country, override: markup_override).to_f
    end.uniq.length == 1
    if all_serving_prices_equal
      sample_serving_size = serving_sizes.sample
      discount_pricing = item_rate_cards.detect{|rc| rc.serving_size_id == sample_serving_size.id }
      equal_serving_pricing = discount_pricing.present? ? discount_pricing.price_inc_gst(gst_country: gst_country) : sample_serving_size.markup_price(gst_country: gst_country, override: markup_override)
    end
  end
  has_image = menu_item.image?
  is_within_budget = pricing_within_budget(budget: budget, menu_item: menu_item, serving_sizes: serving_sizes, rate_cards: item_rate_cards, markup_override: markup_override)
.team-menu-item
  %div
    - if has_image
      = cl_image_tag(menu_item.ci_image, width: 400, height: 400, crop: 'fill', quality: "auto,fl_lossy,f_auto", alt: menu_item.name, class: "circle-icon")
    - else
      .circle-icon
        = menu_item.name.first
  %div.team-menu-item__details
    %p.team-menu-item__name
      = menu_item.name.truncate(40)
      - if letter_icons.present?
        %span
          =raw letter_icons
      - if !is_within_budget
        %small
          %sup *
          over budget

    %p.team-menu-item__description
      = menu_item.description.strip.truncate(100) if menu_item.description.present?

    .team-menu-item__footer
      - if serving_sizes.present?
        - if all_serving_prices_equal
          %p.item-price
            = number_to_currency(equal_serving_pricing, precision: 2)
            %small (inc gst)
        - if serving_sizes.size > 1
          %p.serving-options View options
          .serving-sizes
            %p Options:
            - serving_sizes.each do |serving_size|
              - discount_pricing = item_rate_cards.detect{|rc| rc.serving_size_id == serving_size.id }
              - serving_price = discount_pricing.present? ? discount_pricing.price_inc_gst(gst_country: gst_country) : serving_size.markup_price(gst_country: gst_country, override: markup_override)
              .serving
                %p= serving_size.name
                %p
                  = number_to_currency(serving_price, precision: 2)
                  %small (inc gst)

      - else
        %p.item-price
          :ruby            
            discount_price = case
            when item_rate_cards = item_rate_cards.detect{|rc| rc.menu_item_id == menu_item.id }.presence
              item_rate_cards.price_inc_gst(gst_country: gst_country)
            when menu_item.promo_price.present?
              menu_item.markup_price(gst_country: request_country_code, override: markup_override, promotional: true)
            else
              nil
            end
            item_price = discount_price || menu_item.markup_price(gst_country: request_country_code, override: markup_override)
          = number_to_currency(item_price, precision: 2)
          %small (inc gst)
