- return if params[:delivery_dates].blank? || params[:delivery_dates].size == 1

:ruby
  current_date = params[:current_date]
  delivery_dates = params[:delivery_dates].present? ? JSON.parse(params[:delivery_dates]) : []

  delivery_suppliers = params[:delivery_suppliers].present? ? JSON.parse(params[:delivery_suppliers]) : {}
  selected_dates = delivery_suppliers.present? ? delivery_suppliers.select{|delivery_date, day_suppliers| day_suppliers.present? }.keys : []
  selected_dates = selected_dates - [current_date]

  inactive_dates = delivery_dates.select do |delivery_date|
    availability_fetcher = Suppliers::GetAvailableSuppliers.new(supplier_ids: [@supplier.id], delivery_date: Time.zone.parse(delivery_date), suburb: @suburb).call
    availability_fetcher.unavailable_suppliers.include?(@supplier)
  end

.row.team-supplier-banner__details
  %p.team-supplier-banner__title.small-12.columns.no-gutter
    Add supplier to one or more days
  - if selected_dates.present?
    %p.team-supplier-banner__day-chosen-info
      Days with '-' means a supplier is already selected. Check to overwrite it.
  - if inactive_dates.present?
    %p.team-supplier-banner__day-chosen-info
      Days with 'x' means a supplier is not available on that day/date.
  %ul.menu-dates.small-12.columns.no-gutter
    - delivery_dates.sort.each do |delivery_date|
      = render 'team_orders/forms/menu_date', current_date: current_date, delivery_date: delivery_date, delivery_dates: delivery_dates, selected_dates: selected_dates, inactive_dates: inactive_dates
