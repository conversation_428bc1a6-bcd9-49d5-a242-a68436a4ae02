:ruby
  in_panel ||= false
  team_order_attendees = team_order.team_order_attendees.reject{|attendee| attendee.status == 'cancelled' }
  team_order_event_attendees = team_order_attendees.map(&:event_attendee)
  event_attendees ||= session_profile.event_attendees.where(active: true)
  event_attendees = event_attendees.sort_by{|attendee| (team_order_event_attendees.include?(attendee) ? '11' : '99') + attendee.name.downcase }
  team_admin_as_event_attendee = EventAttendee::team_admin_as_attendee(team_admin: session_profile)
  event_attendees.unshift(team_admin_as_event_attendee)
  teams = session_profile.event_teams.where(active: true)

.team-order-list.team-order-show__contact-list{ data: { view_customer_contact_list: true, view_customer_contact_filtering: true, view_customer_contact_team: true, view_team_order_attendee_form: true } }
  .team-order-panel.attendees-selection
    %h6.team-order-header__heading.team-order-header__heading--edit
      Select Attendees
      (
      %span.invited-attendee-count<>
        = team_order_event_attendees.size
      )

    .row
      .small-12.columns
        .small-12.medium-3.columns.no-gutter
          .dropdown.dropdown-filter-wrapper.dropdown-bubble.team-order-filter.team-order-filter--teams
            %span.dropdown-filter-button
              Teams
            .filter-content.dropdown-content.hidden
              %ul
                %li
                  %label.drop-text.filter-by-teams<
                    .section-toggle
                      = check_box_tag 'All teams', 'all-teams', true, class: 'checkbox-content team-filter--everyone', id: 'all-teams', data: { label: 'all-teams' }
                      %span.section-toggle__switch
                    Everyone

                - teams.each do |team|
                  = render 'event_teams/event_team', team: team, unique_class: 'filter-by-teams'

        .small-12.medium-6.columns.no-gutter-small
          %input.team-order-filter.team-order-filter--search{ placeholder: 'Search', type: 'search', name: 'filter-contacts' }/
        .small-12.medium-3.columns.no-gutter
          %span.button.small.team-order-header__add{ data: { open: 'modal-add-new-contact' } }
            Add Contact


    .form-content
      = hidden_field_tag 'order[attendee_ids][]',''
      %div{ style: 'clear:both;' }
      .team-order-list
        .row.show-for-medium.team-order-list__headings
          %span.team-order-list__heading.small-10.columns.no-gutter Name

          .small-2.columns.no-gutter.text-right
            %label
              SELECT ALL
              .toggle-checkbox
                = check_box_tag 'invite-all', 'invited-all', false, class: 'invite-all-attendees'
                %span.toggle-checkbox__switch

        %ul.team-contacts.row
          - event_attendees.each do |event_attendee|
            :ruby
              team_order_attendee = team_order_event_attendees.include?(event_attendee) && team_order_attendees.detect{|attendee| attendee.event_attendee == event_attendee }
              is_invited = team_order_attendee.present? || event_attendee.is_team_admin?
              has_ordered = team_order_attendee.present? && %w[pending ordered].include?(team_order_attendee.status)
            = render 'event_attendees/event_attendee', event_attendee: event_attendee, as_potential_team_order_attendee: true, is_invited: is_invited, has_ordered: has_ordered, compact: true

