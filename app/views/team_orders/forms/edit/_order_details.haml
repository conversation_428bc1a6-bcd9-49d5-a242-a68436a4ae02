:ruby
  team_order_supplier ||= team_order.order_suppliers.first
  total_spend = @team_order_spends.present? && @team_order_spends.total_spend || 0

  customer ||= team_order.customer_profile
  customer_purchase_orders = customer.present? ? customer.customer_purchase_orders : []
  cutoff_options = TeamOrder::Detail::VALID_CUTOFF_OPTIONS.map{|option| [option.titleize, option] }

  if team_order.is_package_order?
    lister_options = { active_only: true, future_only: true, exclude_self: true }
    future_package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order, options: lister_options).call
  else
    future_package_orders = nil
  end

  team_order_suppliers = {}
  supplier, selected_menu_sections = nil
  team_order.order_suppliers.each do |order_supplier|
    supplier = order_supplier.supplier_profile
    selected_menu_sections = order_supplier.selected_menu_sections
    team_order_suppliers[supplier.id] = selected_menu_sections
  end
  delivery_date = team_order.delivery_at.to_s(:date_spreadsheet)
  delivery_suppliers = {
    delivery_date => team_order_suppliers
  }

  google_location_config = {
    as_street_address: true,
    restricted_suburb_id: team_order.delivery_suburb_id,
    restricted_suburb_label: team_order.delivery_suburb.label,
    countryCode: request_country_code
  }

  cutoff_remaining_url = fetch_supplier_cutoff_hours_remaining_api_team_orders_path

  team_order_levels = team_order.team_order_detail&.levels

.order-show__details.no-sticky{ data: { view_team_order_supplier_form: true, view_team_order_supplier_menu: true, view_team_order_payment_slideout: true } }
  .between-flex
    %p Edit Order Details

    %a.change-payment-details{ data: { modal_view: 'true' } } Change Payment Details

  .supplier-day.active.hidden{ data: { date: delivery_date } }
  .supplier-selection{ data: { supplier_cutoff_hours_remaining_url: cutoff_remaining_url } }
    - team_order.order_suppliers.each do |order_supplier|
      - team_supplier = order_supplier.supplier_profile
      .team-order-supplier-banner.team-supplier{ data: { supplier_id: team_supplier.id } }
        %div
          = cl_image_tag(team_supplier.profile.avatar, width: 600, height: 600, crop: 'fill', quality: "auto,fl_lossy,f_auto", alt: team_supplier.company_name)
          %span.supplier-name
            = team_supplier.company_name

          // used for supplier selection
          %button.small-6.columns.choose-supplier-btn.team-order-list__btn.circle-icon.float-right.hidden{ type: 'button', class: 'selected-supplier' }

        %div
          - if is_admin?
            %a.change-supplier{ data: { url: refresh_suppliers_list_api_team_orders_path(team_order_id: team_order.id), open: 'modal-supplier-list.reveal' } }
              Edit Supplier

          %a.supplier-menu{ data: { url: retrieve_supplier_menu_api_team_orders_path(slug: supplier.slug, selected_menu_sections: selected_menu_sections), open: 'modal-supplier-menu.reveal' } }
            Edit Menu

  .team-order-details
    %p.team-order-details__info
      %strong Event Name
      = f.hidden_field :credit_card_id, value: team_order.credit_card_id
      = f.hidden_field :invoice_individually, value: team_order.invoice_individually
      = f.hidden_field :whodunnit_id, value: current_user.id

      = hidden_field_tag 'order[delivery_dates]', delivery_suppliers.keys.to_json
      = hidden_field_tag 'order[delivery_suppliers]', delivery_suppliers.to_json
      = hidden_field_tag 'suburb_id', team_order.delivery_suburb_id

      - if team_order.is_package_order?
        = f.hidden_field :mode, value: 'one-off'

      = f.text_field :name, placeholder: 'Enter the name for your order', class: 'form-input', required: true
      %span.form-error
        An event name is required

  .team-order-details
    %p.team-order-details__label Date
    %p.team-order-details__info
      = f.text_field :delivery_at, placeholder: 'Select a Date and Time', class: 'datepicker form-input', data: { edit: true }, autocomplete: 'no', value: team_order.delivery_at&.to_s(:datetime), disabled: !is_admin?
      - if is_admin?
        %small Please only change times, and let the supplier know about the updates.
      - else
        %small DateTime is fixed, as this requires supplier communication.


  .team-order-details
    %p.team-order-details__label Delivery Address
    %p.team-order-details__info
      Saved: #{team_order.delivery_address_arr.join(', ')}

    %p.team-order-details__label Level
    %p.team-order-details__info
      = f.text_field :delivery_address_level

    %p.team-order-details__label Street Address
    %p.team-order-details__info
      = f.text_field :delivery_address, autocomplete: 'no', class: 'form-input validate', data: { view_google_location_input: google_location_config.to_json }, required: 'true'

    %p.team-order-details__info
      = f.text_field :suburb, value: team_order.delivery_suburb.label, disabled: true
      = f.hidden_field :delivery_suburb_id, read_only: true

  .team-order-details
    %p.team-order-details__label Delivery Instructions
    %p.team-order-details__info
      = f.text_area :delivery_instruction, placeholder: 'Provide instructions for the delivery personnel', class: 'form-input', rows: 3, required: true

  = f.fields_for :team_order_detail do |d|
    .team-order-details
      %p.team-order-details__label Budget
      %p.team-order-details__info
        = d.text_field :budget, placeholder: 'Set a per head spending amount', class: 'form-input', required: true

        %label{ for: 'order_team_order_detail_attributes_hide_budget'}
          = d.check_box :hide_budget
          Hide budget from attendees

    = d.fields_for :levels do |l|
      .team-order-details
        %p.team-order-details__label
          Attendee Levels
          - if team_order_levels.blank?
            %small (optional)
        %p.team-order-details__info
          = l.select :names, options_from_collection_for_select(team_order_levels, :name, :name, team_order_levels.map(&:name)), {}, { multiple: true, include_blank: true, style: 'width: 100%;' }

    .team-order-details
      %p.team-order-details__label
        Cutoff Option
        %span.whats-this.bottom.tooltip-dash{ tabindex: "2", title: 'If your team order doesn\'t reach the suppliers minimum order value by the cutoff time, choose whether to be charged the remaining value or cancel the order. You can add more items to the order as an admin, if you choose to be charged the remaining value.', data: { tooltip: true, tooltip_class: 'whats-this__tooltip' }} What's This?
      %p.team-order-details__info
        = d.select :cutoff_option, options_for_select(cutoff_options, team_order.cutoff_option), { include_blank: false }

  .team-order-details
    %p.team-order-details__info
      %strong #PO number
      = f.hidden_field :po_number
      = f.select :cpo_id, options_from_collection_for_select(customer_purchase_orders, :id, :info, team_order.cpo_id), { include_blank: true }, { style: 'width: 100%;'}

  .team-order-details
    %a.button.small.gray-btn.team-order-edit-btn{ href: team_order_path(team_order) }
      Cancel
    %a.button.small.place-order-btn.team-order-edit-btn
      = future_package_orders.present? ? 'Update Day' : 'Update'

    - if future_package_orders.present?
      %a.button.small.team-order-edit-btn.hollow{  href: 'javascript:void(0)', data: { open: 'package-update-modal' } }
        Update All Linked
