:ruby
  existing_supplier_ids = team_order.team_supplier_profiles.pluck(:id)

#package-update-modal.customer-form.reveal.modal{ data: { reveal: '' } }
  .package-update-modal__container
    %h3.modal-title
      Package Update
    %p.modal-subtitle The following future linked orders will have your changes applied to them also:
    %ul.list-unstyled
      - package_orders.each do |package_order|
        %li
          = link_to "##{package_order.id}:", team_order_path(package_order), target: '_blank'
          = package_order.delivery_at.to_s(:weekday)
          %span -
          %span= package_order.delivery_at.strftime("#{package_order.delivery_at.day.ordinalize} %b %Y")
          - if package_order.team_supplier_profiles.pluck(:id) == existing_supplier_ids
            %p.package-update-menu-warning= "- Any menu changes to #{package_order.team_supplier_profiles.first.name} will also apply to this day"

  .form-footer.light-gray-bg.medium-text-right
    %button.yordar-cancel.button.small.gray-btn{ aria: { label: 'Close modal' }, data: { close: '' } }
      Cancel
    %button.button.small.place-order-btn.float-right{ type: ' button', data: { mode: 'linked' } }
      Confirm
