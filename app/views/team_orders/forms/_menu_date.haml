:ruby
  is_selected_date = selected_dates.present? && selected_dates.map(&:to_date).include?(delivery_date.to_date)
  is_inactive_date = inactive_dates.present? && inactive_dates.map(&:to_date).include?(delivery_date.to_date)
  is_current_date = current_date.present? && delivery_date.to_date == current_date.to_date && !is_inactive_date

  tick_class = case
  when is_inactive_date
    'delivery-day-tick__day-inactive'
  when is_selected_date && !is_current_date
    'delivery-day-tick__day-chosen'
  else
    ''
  end

%li.menu-date-selector{ class: is_selected_date ? 'active' : '' }
  %label.day-selector__label
    = delivery_date.to_date.strftime('%a %d %b')
    %input.delivery-day{ type: 'checkbox', value: delivery_date.to_date, checked: is_current_date, disabled: is_current_date || is_inactive_date, }
    %span.delivery-day-tick{ class: tick_class }
