:ruby
  selected_menu_sections ||= []
  supplier_minimums ||= nil
  budget = params[:budget].presence || nil
  if supplier_minimums.present? && budget.present?
    best_for = (supplier_minimums.minimum_spend / budget.to_i).to_i
    best_for = 1 if best_for <= 0
  else
    best_for = 10
  end
  markup_override = supplier_menu.markup_override

.slider-container
  %div
    .row.team-supplier-banner
      .small-12.team-supplier-banner__image
        = cl_image_tag(supplier.profile.avatar, crop: 'fill', quality: "auto,fl_lossy,f_auto", alt: supplier.company_name)
      .team-supplier-banner__details.row
        %p.team-supplier-banner__title.small-12.columns
          %strong.supplier-name
            = supplier.name
          %span{ data: { close: '' } } X
        .team-supplier-banner__info.small-12.columns
          %div
            %p.team-supplier-rating{class: supplier.rating >= 3 ? 'team-supplier-rating--approval' : '' }= supplier.rating > 0 ? supplier.rating : '-'
            %p Min Order: #{supplier_minimums.present? ? format_price(supplier_minimums.minimum_spend) : '-'}
            %p.best-for-teams<>
              Best for teams of #{best_for}+
          %div
            - if supplier_menu.section_grouped_menu_items.present?
              %a.preview-menu.button.small{ href: next_app_supplier_show_url(supplier.slug, team_order_menu: true, budget: budget), target: '_blank' }
                Preview Menu

    = render 'team_orders/forms/menu_date_pickers'

    .form-content.team-supplier-menu{ data: { supplier_id: supplier.id } }
      - supplier_menu.section_grouped_menu_items.each do |menu_section, menu_items|
        .team-menu-section
          .team-menu-section__heading
            %label.section-toggle
              = check_box_tag "order[selected_menu_sections][#{supplier.id}][]", menu_section.id, selected_menu_sections.blank? || selected_menu_sections.map(&:to_i).include?(menu_section.id), class: 'menu-section-selection'
              %span.section-toggle__switch
            %span= menu_section.name
          .team-menu-section__items
            - menu_items.each do |menu_item|
              - item_rate_cards = supplier_menu.grouped_rate_cards.present? && supplier_menu.grouped_rate_cards[menu_item.id]
              = render 'team_orders/forms/menu_item', menu_item: menu_item, budget: budget, item_rate_cards: item_rate_cards, markup_override: markup_override

  %div
    .row.slider-footer
      %button.small-12.columns.button.small.select-supplier{ data: { closex: true } }
        Select supplier
