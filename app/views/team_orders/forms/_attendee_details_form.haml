:ruby
  cutoff_options = TeamOrder::Detail::VALID_CUTOFF_OPTIONS.map{|option| [option.titleize, option] }
  cutoff_text = 'If your team order doesn\'t reach the suppliers minimum order value by the cutoff time, choose whether to be charged the remaining value or cancel the order. You can add more items to the order as an admin, if you choose to be charged the remaining value.'

  team_order_levels = team_order.team_order_detail&.levels
  has_levels = team_order_levels.present?

.row
  = f.fields_for :team_order_detail do |d|
    .small-12.medium-6.columns.team-order-event-details__input.budget
      %label
        Budget per head
        %small (inc gst)
      = d.text_field :budget, placeholder: 'Set a per head spending amount', type: 'number', class: 'form-input', required: true

      %label{ for: 'order_team_order_detail_attributes_hide_budget'}
        = d.check_box :hide_budget
        Hide budget from attendees

  .small-12.medium-6.columns.team-order-event-details__input
    %label
      Cutoff Option
      %span.whats-this{ class: 'bottom tooltip-dash', tabindex: '2', title: cutoff_text, data: { tooltip: true, 'tooltip-class': 'whats-this__tooltip' } } What's This?

    = f.fields_for :team_order_detail do |d|
      = d.select :cutoff_option, options_for_select(cutoff_options, team_order.cutoff_option), { include_blank: false }, { class: 'form-input' }
.row
  .small-12.medium-6.columns.team-order-event-details__input
    %label Estimated # of Attendees
    = f.text_field :number_of_people, placeholder: 'Approx number of attendees', type: 'number', class: 'form-input estimated-number-attendees', required: true
  .small-12.medium-6.columns.team-order-event-details__input
    %label
      Attendee invites
    %small
      All invitation for team orders are via magic link.
      %br
      Attendees will be able to add themselves to the order, which can be done through the magic link provided after the order is placed.

.row
  = f.fields_for :team_order_detail do |d|
    = d.fields_for :levels do |l|
      .small-12.medium-6.columns.team-order-event-details__input
        %a.team-order-levels-handle{ href: 'javascript:void(0)', class: (has_levels ? 'hidden' : '') }
          Add Attendee Levels
        .team-order-levels{ class: (has_levels ? '' : 'hidden') }
          %label
            Attendee Levels
            - if !has_levels
              %small (optional)
          = l.select :names, options_from_collection_for_select(team_order_levels, :name, :name, team_order_levels&.map(&:name)), {}, { multiple: true, include_blank: true, style: 'width: 100%;' }
