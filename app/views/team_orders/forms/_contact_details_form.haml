:ruby
  customer ||= session_profile
  customer_purchase_orders = customer.present? ? customer.customer_purchase_orders : []
.row
  .small-12.medium-6.columns.team-order-event-details__input
    %label Contact
    = f.text_field :contact_name, placeholder: '<PERSON>', class: 'form-input'

  .small-12.medium-6.columns.team-order-event-details__input
    %label Company Name
    = f.text_field :company_name, placeholder: '<PERSON><PERSON><PERSON>', class: 'form-input'
.row
  .small-12.medium-6.columns.team-order-event-details__input
    %label Phone
    = f.text_field :phone, class: 'form-input'
.row
  .small-12.medium-6.columns.team-order-event-details__input
    %label PO Number
    = f.hidden_field :po_number
    = f.select :cpo_id, options_from_collection_for_select(customer_purchase_orders, :id, :info, f.object.cpo_id), { include_blank: true }, { style: 'width: 100%;'}

  .small-12.medium-6.columns.team-order-event-details__input
    %label Cost Centre ID
    = f.text_field :department_identity, placeholder: 'Enter a department ID', class: 'form-input'
