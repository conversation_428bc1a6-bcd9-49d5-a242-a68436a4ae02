:ruby
  delivery_address = team_order.delivery_address.present? ? (team_order.delivery_address + ', ') : ''
  delivery_address += delivery_suburb.present? ? delivery_suburb.label : ''
  delivery_at = team_order.delivery_at.present? ? team_order.delivery_at.to_s(:datetime) : nil
  is_multi_day = true
  delivery_dates = team_order.delivery_dates.presence || []
  if delivery_dates.present?
    delivery_at = delivery_dates.first.to_s(:datetime)
  end
  delivery_suppliers = team_order.delivery_suppliers.presence || {}
  weekdays = Date::DAYNAMES.map{|day| day.first(3).upcase }
  google_location_config = {
    set_suburb: 'input[name="order[delivery_suburb_id]"]',
    set_street_address: 'input[name="order[delivery_address]"]',
    countryCode: request_country_code
  }

.team-order-panel.order-details
  .dashboard-container
    .team-order-header
      %h6.team-order-header__heading.team-order-header__heading--event-details  Event Details
    .team-order-event-details
      = f.hidden_field :credit_card_id, value: team_order.credit_card_id
      = f.hidden_field :invoice_individually, value: team_order.invoice_individually
      = f.hidden_field :whodunnit_id, value: current_user.id
      = f.hidden_field :order_variant, value: 'recurring_team_order'
      - if team_order.package_id.present?
        = f.fields_for :team_order_detail do |d|
          = d.hidden_field :package_id, value: team_order.package_id

      .team-order-type-selection.hidden
        %button.active{ data: { order_type: 'open' } } Recurring Team Order

      .row
        %label.open-team-day-label
          Select the days for your order
        .small-12.columns.between-flex.open-team-days
          - weekdays.each_with_index do |weekday, widx|
            %label.drop-text{ style: 'flex-grow: 1' }<
              = check_box_tag 'weekday', widx, delivery_dates.map(&:wday).include?(widx), class: 'checkbox-content', id: "weekday-#{widx}"
              %span.open-team-day-selector
                %span.open-team-day-selector__day
                  = weekday
                %span.open-team-day-selector__toggle
      .row
        .small-12.medium-6.columns.team-order-event-details__input
          %label Event Name
          = f.text_field :name, placeholder: 'Enter the name for your order', class: 'form-input', required: true
          %span.form-error
            An event name is required

        .small-12.medium-6.columns.team-order-event-details__input
          %label
            Starting Week
          = f.text_field :delivery_at, placeholder: 'Select a Starting Week and Time', class: 'datepicker form-input', data: { edit: true }, autocomplete: 'no', required: true, value: delivery_at

          = hidden_field_tag 'order[delivery_dates]', delivery_dates.map{|date| date.to_s(:datetime) }.to_json
          = hidden_field_tag 'order[delivery_suppliers]', delivery_suppliers.to_json

          %span.form-error
            Delivery date is required

      .row
        .small-12.medium-6.columns.team-order-event-details__input
          - delivery_address = team_order.delivery_address.present? ? (team_order.delivery_address + ', ') : ''
          - delivery_address += delivery_suburb.present? ? delivery_suburb.label : ''
          %label Delivery Address

          = text_field_tag 'postcode',                                                                  |
            delivery_address,                                                                           |
            placeholder: 'Enter a delivery address',                                                    |
            autocomplete: 'no',                                                                         |
            class: 'text-input form-input',                                                             |
            required: true,                                                                             |
            data: { view_google_location_input: google_location_config.to_json },                       |
            disabled: team_order.order_suppliers.present?                                               |

          = f.hidden_field :delivery_suburb_id, value: delivery_suburb.blank? ? '' : delivery_suburb.id
          = hidden_field_tag :suburb_id, delivery_suburb.blank? ? '' : delivery_suburb.id
          = f.hidden_field :delivery_address, value: team_order.delivery_address

        .small-12.medium-6.columns.team-order-event-details__input
          %label Delivery Level
          = f.text_field :delivery_address_level, placeholder: 'Enter a Delivery Level', class: 'form-input'

      .row
        .small-12.columns.team-order-event-details__input
          %label Delivery Instructions
          = f.text_area :delivery_instruction, placeholder: 'Provide instructions for the delivery personnel', class: 'form-input', rows: 3, required: true

      = render 'team_orders/forms/attendee_details_form', f: f, team_order: team_order

      = render 'team_orders/forms/contact_details_form', f: f

  .dashboard-container__footer
    .row
      .small-6.columns
        %a.button.small.gray-btn{ href: customer_team_orders_path }
          Cancel
      .small-6.columns.text-right
        %a.button.small.team-order-step-btn{ data: { current_step: 'step-1', step: 'step-3' } }
          Select Suppliers
