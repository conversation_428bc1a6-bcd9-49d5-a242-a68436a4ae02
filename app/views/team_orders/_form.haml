:ruby
  delivery_suburb = team_order.delivery_suburb
  form_url = team_order.new_record? ? team_orders_path : team_order_path(team_order)
  form_method = team_order.new_record? ? 'POST' : 'PUT'

  event_attendees = @event_attendees.presence || session_profile.event_attendees.where(active: true)

= form_for team_order, method: form_method , url: form_url, html: { class: 'team-order-form', data: { abide: '' } } do |f|

  - if team_order.is_recurring_team_order?
    = render 'team_orders/forms/recurring_details_form', f: f, team_order: team_order, delivery_suburb: delivery_suburb

  - else
    = render 'team_orders/forms/details_form', f: f, team_order: team_order, delivery_suburb: delivery_suburb

    = render 'team_orders/attendees_selection', team_order: team_order, event_attendees: event_attendees

  = render 'team_orders/supplier_selection', team_order: team_order, delivery_suburb: delivery_suburb

= render 'team_orders/payment_details', team_order: team_order

= render 'event_attendees/new_modal', team_order: team_order, event_attendees: event_attendees, context: 'attendee_selection'

#modal-supplier-menu.modal-drawer.modal-drawer--no-padding.reveal.modal{ data: { reveal: '' } }
  .form-header
    %h2.modal-title Supplier Menu

