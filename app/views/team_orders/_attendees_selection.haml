:ruby
  team_order_event_attendees = team_order.team_order_attendees.reject{|attendee| attendee.status == 'cancelled' }.map(&:event_attendee)
  event_attendees ||= session_profile.event_attendees.where(active: true)
  event_attendees = event_attendees.sort_by{|attendee| attendee.name.downcase }
  team_admin_as_event_attendee = EventAttendee::team_admin_as_attendee(team_admin: session_profile)
  event_attendees.unshift(team_admin_as_event_attendee)
  teams = session_profile.event_teams.where(active: true)

.team-order-panel.attendees-selection.hidden{ data: { view_customer_contact_creation: true, view_customer_contact_filtering: true, view_team_order_attendee_form: true } }
  .dashboard-container
    .team-order-header.row
      .small-12.medium-5.columns
        %h6.team-order-header__heading
          Select Attendees
          (
          %span.invited-attendee-count<>
            = team_order_event_attendees.size
          )
      .small-12.medium-7.columns.no-gutter
        .small-12.medium-3.columns.no-gutter
          .dropdown.dropdown-filter-wrapper.dropdown-bubble{ class:"team-order-filter team-order-filter--teams"}
            %span.dropdown-filter-button
              Teams
            .filter-content.dropdown-content.hidden
              %ul
                %li
                  %label.drop-text.filter-by-teams<
                    .section-toggle
                      = check_box_tag "All teams", "all-teams", true, class: 'checkbox-content team-filter--everyone', id: "all-teams", data: { label: "all-teams" }
                      %span.section-toggle__switch
                    Everyone

                - teams.each do |team|
                  = render "event_teams/event_team", team: team, unique_class: "filter-by-teams"
                  
        .small-12.medium-6.columns.no-gutter-small
          %input.team-order-filter.team-order-filter--search{ placeholder: 'Search', type: 'search', name: 'filter-contacts' }/
        .small-12.medium-3.columns.no-gutter
          %span.button.small.team-order-header__add{ type: 'button', data: { open: 'modal-add-new-contact' } }
            Add Contact


    .form-content
      = hidden_field_tag 'order[attendee_ids][]',''
      %div{ style: 'clear:both;' }
      .team-order-list
        .row.show-for-medium.team-order-list__headings
          %span.team-order-list__heading.small-4.columns.no-gutter Name
          %span.team-order-list__heading.small-4.columns.no-gutter Email
          %span.team-order-list__heading.small-2.columns.no-gutter Team
          .small-2.columns.no-gutter.text-right
            %label
              SELECT ALL
              .toggle-checkbox
                = check_box_tag 'invite-all', 'invited-all', false, class: 'invite-all-attendees'
                %span.toggle-checkbox__switch

        %ul.team-contacts.team-order-list__table.row
          - event_attendees.each do |event_attendee|
            = render 'event_attendees/event_attendee', event_attendee: event_attendee, as_potential_team_order_attendee: true, is_invited: (team_order_event_attendees.include?(event_attendee) || event_attendee.is_team_admin?)

  .dashboard-container__footer
    .row
      .small-6.columns
        %a.button.small.gray-btn.team-order-step-btn{ data: { step: 'step-1' } }
          Enter Event Details
      .small-6.columns
        %a.button.small.team-order-step-btn.float-right{ data: { current_step: 'step-2', step: 'step-3' } }
          Choose Supplier

