:ruby
  team_order ||= @team_order
  can_edit = team_order.status == 'pending' || (is_admin? && %w[new amended confirmed].include?(team_order.status))
  formatted_date = team_order.delivery_at.strftime("%l:%M%P on %A the #{team_order.delivery_at.day.ordinalize} of %b %Y")

  if team_order.is_recurring_team_order?
    lister_options = {
      scoped_to: 'recent_fortnight',
      scoped_time: team_order.delivery_at,
    }
  else
    lister_options = {}
  end
  package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order, options: lister_options).call

  event_attendees = session_profile.event_attendees.where(active: true)

  if team_order.is_package_order?
    lister_options = { active_only: true, future_only: true, exclude_self: true }
    future_package_orders = TeamOrders::ListPackageOrders.new(team_order: team_order, options: lister_options).call
  else
    future_package_orders = nil
  end

- content_for :back_button do
  = link_to team_order_path(team_order) do
    %span.customer-header-back-link

- content_for :header_title do
  - if team_order.is_package_order?
    %span= "Edit #{team_order.delivery_at.to_s(:full_date)}"
  - else
    %span= "Edit #{team_order.name.titleize.truncate(30)}"

- content_for :header_elements do
  .between-flex
    - if package_orders.present?
      %ul.dropdown.menu.linked-orders{ data: { dropdown_menu: ''} }
        %li.is-dropdown-submenu-parent
          %span
            - if team_order.is_recurring_team_order?
              Recent
            Linked Orders
          %ul.menu.customer-header__dropdown.customer-header__dropdown--linked
            - package_orders.each do |package_order|
              %li
                - order_name = relative_weekday(package_order.delivery_at.to_date, format: 'numeric')
                - if package_order == team_order
                  = link_to order_name, 'javascript:void(0)', class: 'show-all-linked'
                - else
                  = link_to order_name, edit_team_order_path(package_order)

%div{ data: { view_team_order_edit_form: true, view_team_order_form: true } }
  = form_for team_order, method: 'PUT' , url: team_order_path(team_order), html: { class: 'team-order-form team-order-show team-order-edit-form', data: { abide: '' } } do |f|

    .order-edit
      %h2= "#{team_order.name}"
      .order-show-header
        .order-show__options
          %p.date= formatted_date
      .order-show-body
        - if !team_order.is_recurring_team_order?
          = render 'team_orders/forms/edit/attendees_selection', team_order: team_order, event_attendees: event_attendees

        = render 'team_orders/forms/edit/order_details', team_order: team_order, f: f

  = render 'team_orders/forms/payments/modal_payment_form', team_order: team_order

  = render 'event_attendees/new_modal', team_order: team_order, event_attendees: event_attendees, context: 'attendee_selection'

  - if future_package_orders.present?
    = render 'team_orders/forms/edit/package_update_modal', team_order: team_order, package_orders: future_package_orders

  #modal-supplier-menu.modal-drawer.modal-drawer--no-padding.reveal.modal{ data: { reveal: '' } }
    .form-header
      %h2.modal-title Supplier Menu

  #modal-supplier-list.modal-drawer.modal-drawer--no-padding.reveal.modal{ data: { reveal: '' } }
    .modal-content
      .row.team-order-list__headings.show-for-medium
        %span.team-order-list__heading.small-5.columns.no-gutter
          NAME
        .team-order-list__heading.small-4.columns.no-gutter
          %span.small-6.columns.no-gutter
            MIN ORDER
          %span.small-6.columns.no-gutter
            LEAD TIME
        .team-order-list__heading.small-3.columns.no-gutter
      %ul.team-suppliers-list.row
    .intercom-separator
