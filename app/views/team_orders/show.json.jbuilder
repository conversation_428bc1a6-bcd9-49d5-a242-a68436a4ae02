json.extract! @team_order, :id, :name, :delivery_at, :delivery_address_arr, :delivery_instruction, :po_number
json.budget @team_order.team_order_budget

team_order_supplier = @team_order.order_suppliers.first
team_supplier = team_order_supplier && team_order_supplier.supplier_profile
team_order_attendees = @team_order.team_order_attendees.where.not(status: 'cancelled')

if params[:wants_html].present?
  json.html (render 'team_orders/show/order_details', team_order: @team_order, team_order_supplier: team_order_supplier, team_order_attendees: team_order_attendees, for_modal: true)
else
  if team_supplier.present?
    json.supplier do
      json.name team_supplier.company_name
      json.menu_link next_app_supplier_show_url(team_supplier.slug, team_order_menu: true, budget: @team_order.team_order_budget, selected_menu_sections: team_order_supplier.selected_menu_sections)
    end
  end

  json.team_order_attendees team_order_attendees.each do |team_order_attendee|
    json.name team_order_attendee.name
    json.status team_order_attendee.status
    json.order_link next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code)
  end
end

