:ruby
  team_order_spends ||= @team_order_spends
  
  delivery_fee = team_order.customer_delivery.present? && team_order.customer_delivery > 0 ? team_order.customer_delivery : nil
  topup = team_order.customer_topup.present? && team_order.customer_topup > 0 ? team_order.customer_topup : nil  
  discount = team_order.discount.present? && team_order.discount > 0 ? team_order.discount : nil

  anonymous_totals = nil
  anonymous_attendees = team_order.team_order_attendees.where.not(status: 'cancelled').where(anonymous: true).present?
  if anonymous_attendees.present?
    lister_options = {
      order: team_order,
      ordered_attendees_only: true
    }
    full_order_order_lines = OrderLines::List.new(options: lister_options).call
    anonymous_totals = Orders::CalculateCustomerTotals.new(order: team_order, order_lines: full_order_order_lines).call
  end


%h6.order-show-detail-title.totals Order Totals
%p
  Spend:
  = number_to_currency(team_order_spends.total_spend || 0, precision: 2)
  - if team_order_spends.is_under?
    out of
    = number_to_currency(team_order_spends.minimum_spend, precision: 2)
    = "(#{number_to_currency(team_order_spends.remaining_spend)} remaining)"

%div.content.content--no-border
  - if delivery_fee.present?
    %p= "Delivery Fee: #{number_to_currency(delivery_fee, precision: 2)}"

  %p= "GST: #{number_to_currency(team_order.customer_gst.presence || 0, precision: 2)}"

  - if topup.present?
    %p= "Topup: #{number_to_currency(topup, precision: 2)}"

  - if discount.present?
    %p.team-order-details__info
      = "Discount #{number_to_currency(-1 * discount, precision: 2)}"

%p
  = "Total: #{number_to_currency(team_order.customer_total.presence || 0, precision: 2)}"
  %small.team-order-details__see-more.collapsible-totals See more
  - if anonymous_totals.present?
    (#{number_to_currency(anonymous_totals.total)} from anon attendees)
- if team_order.status == 'pending'
  - if team_order_spends.is_under?
    %p.min-spend-notice
      - if team_order.cutoff_option == 'cancel_order'
        If remaining spend isn't met the order will be cancelled.
      - elsif team_order.cutoff_option == 'charge_to_minimum'
        The order will be topped up to the minimum spend.
  - else
    %p.min-spend-notice.success
      Min Spend Met