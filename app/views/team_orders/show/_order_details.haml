:ruby
  for_modal ||= false
  team_order_supplier ||= team_order.order_suppliers.first
  current_status ||= team_order_current_status(team_order)
  team_supplier = team_order_supplier.present? && team_order_supplier.supplier_profile

  team_order_cutoff = TeamOrders::FetchCutoffDayHour.new(team_order: team_order).call
  expiry_options = team_order_expiry_options(team_order_cutoff: team_order_cutoff)
  lead_time_fetcher = is_admin? && %w[cancelled delivered].exclude?(team_order.status) ? Orders::FetchLeadTime.new(order: team_order).call : nil

.order-show__details{class: ('full-height' if for_modal)}
  - if team_supplier.present?
    .team-order-supplier-banner
      %div
        = cl_image_tag(team_supplier.profile.avatar, width: 600, height: 600, crop: 'fill', quality: "auto,fl_lossy,f_auto", alt: team_supplier.company_name)
        %span
          = team_supplier.company_name
      .between-flex
        %a.hollow{ href: next_app_supplier_show_url(team_supplier.slug, team_order_menu: true, budget: team_order.team_order_budget, selected_menu_sections: team_order_supplier.selected_menu_sections), target: '_blank' }
          Menu
        - if for_modal
          %a{ href: team_order_path(team_order), target: '_blank' } View Order
  .order-show-details-section
    %h6.order-show-detail-title.date Date
    %p
      %span= team_order.delivery_at.to_s(:time_only)
      %span -
      %span= team_order.delivery_at.strftime("%A #{team_order.delivery_at.day.ordinalize} of %b")
  - if lead_time_fetcher
    .order-show-details-section
      %h6.order-show-detail-title.cutoff Cutoff
      %p Cutoff Time: #{lead_time_fetcher.lead_time.strftime("%a #{lead_time_fetcher.lead_time.day.ordinalize} %b, %Y at %l:%M%P")}
      - if !team_order_cutoff.has_expired?
        %p= "(#{expiry_options[:text]})"
  
  .order-show-details-section
    %h6.order-show-detail-title.delivery Address
    %p=raw team_order.delivery_address_arr.join(" ")

  .order-show-details-section
    %h6.order-show-detail-title.instructions Delivery Instructions
    %p= team_order.delivery_instruction

  .order-show-details-section
    %h6.order-show-detail-title.status Order Status
    %p.status-icon{ class: team_order.status }
      = I18n.t("team_order.status.#{current_status}")

  .order-show-details-section
    = render 'team_orders/show/order_totals', team_order: team_order
      
  - if po_number = team_order.po_number.presence
    .order-show-details-section
      %h6.order-show-detail-title.po PO number
      %p
        = po_number

  .order-show-details-section
    %h6.order-show-detail-title.attendees Number of Attendees
    .between-flex
      = render 'team_orders/show/attendee_counts', team_order: team_order, show_package_counts: team_order.is_package_order? && team_order.status == 'pending'

  - if team_order_levels = team_order.team_order_levels.presence
    .order-show-details-section
      %h6.order-show-detail-title Attendee Levels
      %p= team_order_levels.map(&:name).join(', ')

  .order-show-details-section
    %h6.order-show-detail-title.magic Magic Link
    %p.attendee-invite-link
      - if team_order.is_package_order?
        = new_team_order_package_attendee_invite_url(package_id: team_order.package_id)
      - else
        = new_team_order_attendee_invite_url(event_id: team_order.unique_event_id)

    %p.invite-link-info--copied.hidden Link Copied!
    %p
      Forgot to invite some people? Send this link to whoever needs it. Just remember to approve them from this page.
    

