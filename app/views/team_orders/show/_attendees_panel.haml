:ruby
  can_edit ||= false

  attendee_lister_options = {
    order: team_order,
    sort_by: 'contact_name',
    include_admin: session_profile,
    include_package_attendees: team_order.status == 'pending' && team_order.is_package_order?,
  }
  team_order_attendees = TeamOrderAttendees::List.new(options: attendee_lister_options).call

  lister_options = {
    order: team_order,
    confirmed_attendees_only: team_order.status != 'pending',
  }
  order_lines = OrderLines::List.new(options: lister_options, includes: [:team_order_attendee]).call
  attendee_grouped_order_lines = order_lines.group_by(&:team_order_attendee)

.team-order-list.team-order-show__contact-list
  - if team_order_attendees.blank?
    .team-order-list__magic-setup
      %div
        %h2 You've set this up as a magic link order
        %p Send this link to your staff. After they've added their information they will appear in this panel. You can always find this link in the order details panel to the right.
        %p.no-attendees-magic-link.attendee-invite-link
          - if team_order.is_package_order?
            = new_team_order_package_attendee_invite_url(package_id: team_order.package_id)
          - else
            = new_team_order_attendee_invite_url(event_id: team_order.unique_event_id)
        %p If you wish to start ordering as admin for this event, click the button below.
        %a.button.hollow{ href: next_app_team_order_attendee_order_url(code: team_order.unique_event_id) }
          Order As Admin
        %p.invite-link-info--copied.hidden Link Copied!
      = image_tag('illustrations/ordering.png', width: '100%', class: 'magic-link-illustration')
    
  - else
    .orderlines-title
      %p Order Attendees
      %button.button.team-order-attendees__show-orders Show Orders
    .team-order-attendees__table-header.orderlines-title
      %p.team-order-attendees__table-header--name Name
      %p
        = "Attendee Budget: #{number_to_currency(team_order.team_order_budget)}#{' (hidden from attendees)' if team_order.hide_budget}"

    %ul.team-contacts.team-contacts--active.row
      - team_order_attendees.each_with_index do |team_order_attendee, index|
        - order_line_attendee = team_order_attendee.is_team_admin? ? nil : team_order_attendee
        = render 'team_orders/team_order_attendee', team_order: team_order, team_order_attendee: team_order_attendee, order_lines: attendee_grouped_order_lines[order_line_attendee], can_edit: can_edit, sort_id: index
