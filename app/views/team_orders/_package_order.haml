:ruby
  team_supplier = package_order.team_supplier_profiles.first

  total_spend = Orders::GetSupplierSpends.new(order: package_order).call.total_spend
  team_order_cutoff = TeamOrders::FetchCutoffDayHour.new(team_order: package_order).call
  expiry_options = team_order_expiry_options(team_order_cutoff: team_order_cutoff)

  current_status = team_order_current_status(package_order)
  can_edit = current_status == 'pending' || (is_admin? && %w[pending new amended confirmed].include?(package_order.status))

  attendee_counts_config = package_order.status == 'pending' ? { title: 'Currently engaged attendees only', style: 'cursor: help;', data: { view_tooltip_el: true } } : {}

.row.show-package__row.show-package__row--grey
  .small-12.medium-7.columns.no-gutter
    %span.team-order-details__label
      =# "#{relative_weekday(package_order.delivery_at.to_date, format: 'short')} - #{package_order.delivery_at.strftime('%I:%M %P')}"
      = package_order.delivery_at.strftime('%a, %b %d - %I:%M %P')
    %span.budget= "Budget: #{number_to_currency(package_order.team_order_budget)}"
  .small-12.medium-5.columns.between-flex.package-separator{ **attendee_counts_config }
    = render 'team_orders/show/attendee_counts', team_order: package_order

.row.show-package__row--supplier.show-package__row
  .small-12.medium-4.columns.no-gutter
    = cl_image_tag(team_supplier.profile.avatar, width: 40, height: 40, crop: 'fill', quality: 'auto,fl_lossy,f_auto', alt: team_supplier.company_name)
    %span.show-package__supplier-name= team_supplier.name
  - if team_order_cutoff.has_expired?
    .small-12.medium-3.columns.status-icon{ class: package_order.status }
      = package_order.status.capitalize
  - else
    .small-12.medium-3.columns.icon.icon-alarm{style: ('color: grey' if expiry_options[:grey]) }
      = expiry_options[:text]
  .small-12.medium-5.columns.between-flex.package-separator
    %p.icon.icon-cost
      = "Total Order: #{number_to_currency(total_spend, precision: 2)}"
    %div
      = link_to 'View', team_order_path(package_order), class: 'button small'
      - if can_edit
        = link_to 'Edit', edit_team_order_path(package_order), class: 'button small hollow'
