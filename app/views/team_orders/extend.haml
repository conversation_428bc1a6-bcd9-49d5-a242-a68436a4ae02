- content_for :header_title, 'Extend Team Order'

:ruby
  team_order = @team_order
  delivery_suburb = team_order.delivery_suburb
  form_class = 'team-order-form team-order-extend-form'
  form_class += ' recurring-team-order-extend-form' if team_order.is_recurring_team_order?

%div{ data: { view_team_order_form: true  } }
  = form_for team_order, method: 'POST' , url: team_orders_path, html: { class: form_class, data: { abide: '' } } do |f|

    - if team_order.is_recurring_team_order?
      = render 'team_orders/forms/recurring_details_form', f: f, team_order: team_order, delivery_suburb: delivery_suburb
    - else
      = render 'team_orders/forms/details_form', f: f, team_order: team_order, delivery_suburb: delivery_suburb

    = render 'team_orders/supplier_selection', team_order: team_order, delivery_suburb: delivery_suburb, is_extension: true

  #modal-supplier-menu.modal-drawer.modal-drawer--no-padding.reveal.modal{ data: { reveal: '' } }
    .form-header
      %h2.modal-title Supplier Menu
