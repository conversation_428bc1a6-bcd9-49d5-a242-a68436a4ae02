:ruby
  button_text = team_order.new_record? ? 'Place Order' : 'Update Order'
.team-order-panel.payment-details.hidden

  .dashboard-container
    .team-order-header
      %h6.team-order-header__heading.team-order-header__heading--event-details Payment Options

    = render 'team_orders/forms/payments/payment_form', order: team_order

  .dashboard-container__footer
    .row
      .small-6.columns
        %a.button.small.gray-btn.team-order-step-btn{ data: { step: 'step-3' } }
          Select Supplier
      .small-6.columns.text-right
        %button.team-order-submit-btn.button.small.place-order-btn{ type: 'submit', data: { credit_card_button: button_text } }
          = button_text
