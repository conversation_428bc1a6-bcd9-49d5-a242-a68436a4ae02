:ruby
  order_lines ||= []
  can_edit ||= false
  sort_id ||= 0
  colors = ['#ff628d66', '#2bffc666', '#24bbff66', '#ffc62666']
  team_order ||= team_order_attendee.order

  if can_edit && !team_order_attendee.is_team_admin? && team_order_attendee.order_id != team_order.id
    team_order_attendee = team_order.team_order_attendees.where(status: 'invited', event_attendee: team_order_attendee.event_attendee, uniq_code: team_order_attendee.uniq_code).first_or_initialize
  end

  order_link_text = case team_order_attendee.status
  when 'ordered'
    'Edit Order'
  when 'pending'
    'Confirm Order'
  else
    'Place Order'
  end
  order_link_text = 'Order as admin' if team_order_attendee.is_team_admin?
  attendee_package_link = team_order_attendee_package_url(code: team_order_attendee.uniq_code) if !team_order_attendee.is_team_admin? && team_order.is_package_order?

  if can_edit
    if team_order_attendee.is_team_admin?
      attendee_order_link = next_app_team_order_attendee_order_url(code: team_order.unique_event_id)
      cancel_link = ''
    elsif team_order_attendee.persisted?
      attendee_order_link = next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code)
      cancel_link = api_team_order_attendee_path(team_order_attendee)
    else
      attendee_order_link = team_order_attendee_registered_package_order_path(code: team_order_attendee.uniq_code, event_id: team_order.unique_event_id)
      cancel_link = team_order_attendee_detach_registered_package_order_path(code: team_order_attendee.uniq_code, event_id: team_order.unique_event_id)
    end
  end

  attendee_status = team_order_attendee.status
  attendee_status = 'registered' if attendee_status == 'invited' && team_order.is_package_order?
 
%li.team-contact{ data: { attendee_id: team_order_attendee.id, order_status: team_order_attendee.status, sort_id: sort_id, name: team_order_attendee.name } }
  .orderline
    .order-show-table-row.team
      .team-order-attendee-name{ title: team_order_attendee.email, data: { tooltip: true, tooltip_class: 'whats-this__tooltip' } }
        %span.circle-icon.small{ style: "background: #{colors[sort_id%4]};" }
          = customer_name_helper(:initials, team_order_attendee.name)
        %span.team-order-list__cell.team-order-list__cell--show-page
          = team_order_attendee.name
          - if team_order_attendee.level.present?
            %small
              = " - #{team_order_attendee.level.name} "
          - if team_order_attendee.anonymous?
            %small
              (anon)
          - if attendee_package_link.present?
            %a.team-admin-attendee-order{ href: attendee_package_link }
              (package)

      %div
        - if !team_order_attendee.is_team_admin?
          %p.team-order-attendee-status{class: attendee_status }
            = attendee_status

      %div
        - if can_edit
          .between-flex
            - if team_order_attendee.is_team_admin?
              %a.button.tiny.rounded{ href: attendee_order_link }
                Order as Admin
            - else
              %a.team-admin-attendee-order{ href: attendee_order_link }
                = order_link_text
              %a.remove-attendee{ href: cancel_link }

    - if order_lines.present?
      .team-attendee-order-lines.hidden
        - order_lines.each do |order_line|
          %p.team-attendee-order-line
            \-
            %span.team-attendee-order-line__quantity
              = "#{order_line.quantity}x"
            = order_line.name


