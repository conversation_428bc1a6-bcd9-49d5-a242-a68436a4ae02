:ruby
  first_delivery = @package_orders.first.try(:delivery_at)
  last_delivery = @package_orders.last.try(:delivery_at)
  all_package_orders = Order.joins(:team_order_detail).where(team_order_details: { package_id: @team_order.package_id })
  has_previous_orders = (params[:direction].blank? || params[:direction] != 'next') && all_package_orders.where('delivery_at < ?', first_delivery).present?
  has_next_orders = (params[:direction].blank? || params[:direction] != 'previous') && all_package_orders.where('delivery_at > ?', last_delivery).present?

  weekly_orders = @package_orders.group_by{|order| order.delivery_at.to_date.cweek }

  if @package_order.is_recurring_team_order?
    scoped_to = 'recent_week'
    previous_delivery_time = has_previous_orders && (first_delivery - 1.week)
    next_delivery_time = has_next_orders && (last_delivery + 1.week)
    button_frequency = 'week'
  else
    scoped_to = 'recent_month'
    previous_delivery_time = has_previous_orders && (first_delivery - 1.month)
    next_delivery_time = has_next_orders && (last_delivery + 1.month)
    button_frequency = 'month'
  end

- if has_previous_orders
  .row.show-package__row
    %a.button.load-more{ href: team_order_package_path(@package_order, scoped_to: scoped_to, scoped_time: previous_delivery_time), data: { direction: 'previous' } }
      load previous #{button_frequency}

- weekly_orders.each do |week_number, week_orders|

  .row.show-package__row.show-package__row--grey
    Week starting #{week_orders.first.delivery_at.beginning_of_week(:sunday).to_s(:date)}

  - week_orders.each do |package_order|
    = render 'team_orders/package_order', package_order: package_order

- if has_next_orders
  .row.show-package__row
    %a.button.load-more{ href: team_order_package_path(@package_order, scoped_to: scoped_to, scoped_time: next_delivery_time), data: { direction: 'next' } }
      load next #{button_frequency}
