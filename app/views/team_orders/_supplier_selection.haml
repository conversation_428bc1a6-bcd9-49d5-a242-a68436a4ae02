:ruby
  is_extension ||= false
  category_group = 'catering-services'
  category_options = grouped_category_options_for_team_orders[category_group.to_sym]
  other_options = grouped_other_options_for_team_orders[category_group.to_sym]
  if team_order.persisted?
    refresh_options = { team_order_id: team_order.id }
  else
    selected_suppliers = team_order.order_suppliers.map do |order_supplier|
      {
        supplier_profile_id: order_supplier.supplier_profile_id,
        selected_menu_sections: order_supplier.selected_menu_sections
      }
    end
    refresh_options = { selected_suppliers: selected_suppliers }
  end
  supplier_refresh_url = refresh_suppliers_list_api_team_orders_path(refresh_options)
  suppliers_availability_url = fetch_suppliers_availability_api_team_orders_path
  cutoff_remaining_url = fetch_supplier_cutoff_hours_remaining_api_team_orders_path

.team-order-panel.supplier-selection.hidden{ data: { refresh_url: supplier_refresh_url, suppliers_availability_url: suppliers_availability_url, supplier_cutoff_hours_remaining_url: cutoff_remaining_url, view_team_order_supplier_form: true, view_team_order_supplier_filtering: true, view_team_order_supplier_menu: true } }
  .dashboard-container
    .team-order-header.row
      .small-12.medium-5.columns.no-gutter
        %h6.team-order-header__heading
          Select Caterer
          %span.multi-supplier-selection-header.hidden
            for each delivery day
      .small-12.medium-7.columns
        .small-12.medium-4.columns.no-gutter-small.team-order-dropdown
          %input.team-order-filter.team-order-filter--search.keyword-search-filter{ placeholder: 'Search', type: 'search', name: 'filter-team-suppliers' }/
        .small-12.medium-4.columns.no-gutter-small.team-order-dropdown
          = render 'suppliers/dropdown_checkboxes', category_group: category_group, header: category_options[:button_text], sections: category_options[:sections], hidden: false, kind: 'category', for_team_order_listing: true, unique_class: 'category'
        .small-12.medium-4.columns.no-gutter-small.team-order-dropdown
          = render 'suppliers/dropdown_checkboxes', category_group: category_group, header: other_options[:button_text], sections: other_options[:sections], hidden: false, kind: 'other', for_team_order_listing: true, unique_class: 'other'

    %ul.multi-day-supplier-selector.hidden

    .form-content.has-small-gutter
      .team-order-list
        .row.show-for-medium.team-order-list__headings
          %span.team-order-list__heading.small-4.columns.no-gutter NAME
          .small-12.medium-6.columns.no-gutter
            %span.team-order-list__heading.small-3.columns.no-gutter MIN ORDER
            %span.team-order-list__heading.small-2.columns.no-gutter RATING
            %span.team-order-list__heading.small-4.columns.no-gutter LEAD TIME
            %span.team-order-list__heading.small-3.columns.no-gutter.best-for-header{ title: "<span class='text-center'>Based on budget of:<br />$#{team_order.team_order_budget} (per head)</span>", data: { view_tooltip_el: { allow_html: true }.to_json } }
              BEST FOR
              %sup
                *
          .small-12.columns.no-gutte.medium-2
            &nbsp;
        %ul.team-suppliers-list.team-order-list__table.row
          -# this is where suppliers list goes

  .dashboard-container__footer
    .row
      - if team_order.is_package_order? || team_order.is_recurring_team_order?
        .small-6.columns
          .small-12.medium-4.no-gutter.columns
            %a.button.small.gray-btn.team-order-step-btn{ data: { step: 'step-1' } }
              Edit Order Details

          .hide-for-small-only.medium-8.columns
            %strong.unavailble-suppliers-disclaimer.text-center.hidden
              * Greyed out suppliers are not available or need more notice

        - if is_extension
          .small-6.columns.text-right
            %button.team-order-submit-btn.button.small.extend-order-btn{ data: { credit_card_button: 'Extend Order' } }
              Extend Order
        - else
          .small-6.columns.text-right
            %a.button.small.team-order-step-btn{ data: { current_step: 'step-3', step: 'step-4' } }
              Payment Options
      - else
        .small-6.columns
          %a.button.small.gray-btn.team-order-step-btn.team-supplier-back-btn{ data: { step: 'step-1' } }
            Enter Event Details
        .small-6.columns.text-right
          %a.button.small.team-order-step-btn{ data: { current_step: 'step-3', step: 'step-4' } }
            Payment Options
