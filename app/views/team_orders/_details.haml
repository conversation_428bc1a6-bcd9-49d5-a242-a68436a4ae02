:ruby
  team_order_supplier = team_order.order_suppliers.first
  team_supplier = team_order_supplier.supplier_profile
  selected_menu_sections = team_order_supplier.selected_menu_sections
.row
  .medium-12.large-12.columns
    .row
      .medium-6.large-6.columns
        .form-fieldset
          %h4.fieldset-title Team Order Details
          / <div class='form-header light-gray-bg clearfix'>
          / <h3>Team order details</h3>
        .form-fieldset
          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Event Name
            .small-12.medium-7.large-5.columns
              = team_order.name
          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Event Date
            .small-12.medium-7.large-5.columns
              = team_order.delivery_at.to_s(:full)

          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Supplier
            .small-12.medium-7.large-5.columns
              = team_supplier.company_name

          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Budget
            .small-12.medium-7.large-5.columns
              #{number_to_currency(team_order.team_order_budget)} per head

          - if team_order.unique_event_id.present?
            .row
              .small-12.large-5.columns
                %label.large-text-right.middle Invite Link
              .small-12.medium-7.large-5.columns
                %a.attendee-link{ href: new_team_order_attendee_invite_path(event_id: team_order.unique_event_id), target: '_blank' }
                  Attendee invite
                  %small{ style: 'display:block; margin-top: -0.5rem' }
                    (click to copy)

          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Menu
            .small-12.medium-7.large-5.columns
              %a{ href: next_app_supplier_show_url(supplier.slug, team_order_menu: true, budget: team_order.team_order_budget, selected_menu_sections: selected_menu_sections), target: '_blank' }
                Preview

          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Add to order
            .small-12.medium-7.large-5.columns
              = link_to 'Order as admin', next_app_team_order_attendee_order_url(code: team_order.unique_event_id), target: '_blank'

      .medium-6.large-6.columns
        .form-fieldset
          %h4.fieldset-title Delivery Details
          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Delivery Address
            .small-12.medium-7.large-5.columns
              =raw team_order.delivery_address_arr.join('<br>')

          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Delivery Instructions
            .small-12.medium-7.large-5.columns
              = team_order.delivery_instruction

          - if team_order.cutoff_option.present?
            .row
              .small-12.large-5.columns
                %label.large-text-right.middle Cutoff Option
              .small-12.medium-7.large-5.columns
                = team_order.cutoff_option.titleize

          .row
            .small-12.large-5.columns
              %label.large-text-right.middle Contact
            .small-12.medium-7.large-5.columns
              = team_order.contact_name
              = team_order.contact_email
              = team_order.phone
              = team_order.company_name

          - if team_order.po_number.present?
            .row
              .small-12.large-5.columns
                %label.large-text-right.middle PO Number
              .small-12.medium-7.large-5.columns
                = team_order.po_number

          - if team_order.department_identity.present?
            .row
              .small-12.large-5.columns
                %label.large-text-right.middle Cost Centre ID
              .small-12.medium-7.large-5.columns
                = team_order.department_identity
