:ruby
  team_order_attendees = team_order.team_order_attendees.where.not(status: 'cancelled')
.row
  .medium-12.large-12.columns
    .form-fieldset
      %h4.fieldset-title Event Attendees
      .table-order
        %table.table-order__table
          %thead
            %tr
              %td Attendee
              %td Email
              %td Status
              %td Edit
          %tbody.attendee-contact-table
            - team_order_attendees.each do |team_order_attendee|
              %tr
                %td= team_order_attendee.name
                %td= team_order_attendee.email
                %td= team_order_attendee.status
                %td= link_to 'edit', next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code)
