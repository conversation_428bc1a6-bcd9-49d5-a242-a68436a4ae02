if @with_baseline_pricing.present?
	extra_price = number_with_precision(menu_extra.price, precision: 2)
else
	markup_override ||= @supplier_menu&.markup_override
	gst_country = team_order_pricing? ? request_country_code : nil
	markup_price = menu_extra.markup_price(gst_country: gst_country, override: markup_override)
	extra_price = number_with_precision(markup_price, precision: 2)
end
json.extract! menu_extra, :id, :name
json.price extra_price
