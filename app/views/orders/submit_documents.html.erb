<div id='doc-upload'>
    <h2>Document Upload</h2>

    <% if @success %>
        <div class='flash alert success'>
            <a class='close' data-dismiss='alert'>x</a>
            Your documents have been submitted awaiting approval from an administrator.
        </div>
    <% end %>

    <div class='row' id='document-upload-content'>
        <p class='order-details'>
            Order #<%= @order.id %><br />
            Delivery date: <%= @order.delivery_at.to_s(:full) %><br />
            <%= @order.customer_profile.blank? ? '' : "Customer: #{@order.customer_profile&.customer_or_company_name}" %>
        </p>

        <%= form_for(@document, url: request.original_url) do |f| %>
            <% if @allow_pod_upload %>
                <div id='pod-upload'>
                    <%= cl_image_upload_tag(:pod) %>
                    <%= button_tag 'Browse POD', type: 'button', class: 'yordar-ok browse-btn button primary' %>
                    <span class='selected-file'></span>
                </div>
            <% end %>
            <div id='invoice-upload'>
                <%= cl_image_upload_tag(:invoice) %>
                <%= button_tag 'Browse Invoice', type: 'button', class: 'yordar-ok browse-btn button primary' %>
                <span class='selected-file'></span>
            </div>
            <div id='ref-number'>
                <%= f.label :ref_number, 'Ref number', id: 'ref-number-label' %>
                <%= f.text_field :ref_number %>
            </div>
            <%= f.submit 'Upload', class: 'yordar-ok button primary upload-btn' %>
            <div class='upload-spinner'></div>
      <% end %>
    </div>
</div>

<% content_for :javascript_includes do %>
    <% javascript_include_tag 'submit-documents' %>
<% end %>
