:ruby
  lister_options = {
    order: order,
    confirmed_attendees_only: order.is_team_order?
  }
  order_lines = OrderLines::List.new(options: lister_options, includes: [:location, :supplier_profile]).call
  location_grouped_order_lines = order_lines.group_by(&:location)

.customer-order__orders{ class: "order-#{order.id}", data: { view_checkout_slider: true } }
  - location_grouped_order_lines.each_with_index do |(location, location_order_lines), location_index|
    %h6.customer-order__heading.with-chevron.between-flex.collapsible= location.details
    %div.content{class: (location_index == 0 ? 'first-location' : '') }
      - location_order_lines.group_by(&:supplier_profile).each do |supplier, supplier_order_lines|
        .customer-order__supplier-info
          .customer-order__supplier-banner
            = resize_image_tag(supplier.profile.avatar, supplier.company_name, 40, 40, "fill")
            %a.customer-order__supplier-name{ href: next_app_supplier_show_url(supplier&.slug) }
              = supplier.name
          - supplier_order_lines.each do |order_line|
            = render 'orders/modal/order_line', order_line: order_line
            