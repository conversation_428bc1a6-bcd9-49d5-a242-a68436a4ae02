<%
#This page is used by Category Solutions suppliers and freight forwarders to reject an order from an email/link
%>
<div id='reject-order-container'>
    <h2>Reject order</h2>

    <div class='row' id='reject-order'>
        <p class='order-details'>
            Order #<%= @order.id %><br />
            Delivery date: <%= @order.delivery_at.to_s(:full) %><br />
            <% @profile_type == 'ffc' ? "Pickup date: #{@order.delivery_at - (@order.freight_forwarding_rate.travel_time || 0).days}<br/>" : ''%>
            <%= @order.customer_profile.blank? ? '' : "Customer: #{@order.customer_profile&.customer_or_company_name}" %>
        </p>

        <%= form_for(@order, url: "/reject_order/#{@order.id}/#{@profile_type}/#{@profile_id}") do |f| %>
            <label for='order-rejection-reason'>Reason for rejection *</label>
            <input type='text' name='rejected_reason' id='order-rejection-reason' required='required' />
            <label for='order-estimated-delivery'>Estimated delivery date *</label>
            <input type='text' class='datepicker' name='estimated_delivery' id='order-estimated-delivery' required='required' />
            <%= f.submit 'Reject', class: 'yordar-ok button primary' %>
      <% end %>
    </div>
</div>

<% content_for :javascript_includes do %>
    <% javascript_include_tag 'reject-order' %>
<% end %>
