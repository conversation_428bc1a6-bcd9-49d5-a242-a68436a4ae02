:ruby
  attendee_name = attendee.present? ? attendee.name : "#{order.customer_profile.name} (admin)"

%tbody.table-order-location.table-order__details
  - if attendee_index != 0
    %tr.table-gap
  %tr.table-order__subtitle-row
    %th.table-order__subtitle{ colspan: table_cols }
      = "Ordered By: #{attendee_name}"

  %tr.table-order__headers-row
    %th Item
    %th.table-order__item--numeric
      %span{ class: (is_print ? 'hide' : '') }
        Unit Price
    %th.table-order__item--centered Qty
    %th.table-order__item--numeric
      %span{ class: (is_print ? 'hide' : '') }
        Line Total

  - attendee_order_lines.sort_by(&:id).each do |order_line|
    = render 'orders/summary/order_line', order_line: order_line, is_customer: is_customer, is_print: is_print, order: order
