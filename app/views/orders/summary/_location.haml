:ruby
  location_class = "location-#{location.id}"
  location_class += ' hidden' if is_summary_page && location_index != 0
  supplier_grouped_order_lines = location_order_lines.group_by {|ol| ol.supplier_profile }

%tbody.table-order-location-all{ class: (is_summary_page ? 'hidden' : '') }
  - if location_index != 0
    %tr.table-gap
  %tr.table-order__title-row
    %th.table-order__title{ colspan: 6 }
      = location.details

%tbody.table-order-location.table-order__details{ class: location_class, data: { location_id: location.id } }
  - supplier_grouped_order_lines.each do |supplier, supplier_order_lines|
    %tr.table-order__subtitle-row
      %th.table-order__subtitle{ colspan: table_cols }
        = supplier.company_name.downcase

    - if !is_summary_page
      %tr.table-order__headers-row
        %th Item
        %th.table-order__item--numeric
          %span{ class: (is_print ? 'hide' : '') }
            Unit Price
        %th.table-order__item--centered Qty
        %th.table-order__item--numeric
          %span{ class: (is_print ? 'hide' : '') }
            Line Total
        - if is_edit_page
          %th.table-order__item--narrow

    - supplier_order_lines.sort_by(&:id).each do |order_line|
      = render 'orders/summary/order_line', order_line: order_line, is_customer: is_customer, can_update: is_summary_page || is_edit_page, is_print: is_print, order: order
