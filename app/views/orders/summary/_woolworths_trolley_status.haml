:ruby
  supplier = SupplierProfile.woolworths
  order ||= session_order
  errors ||= @woolworths_errors || []

  available_delivery_windows = Rails.cache.read(Woolworths::API::DELIVERY_WINDOWS_CACHE_KEY.sub(':id', order.id.to_s))
  grouped_delivery_windows = available_delivery_windows.present? ? available_delivery_windows.group_by(&:date) : {}
  active_delivery_date = grouped_delivery_windows.keys.detect{|date| date == order.delivery_at.to_date }.presence || grouped_delivery_windows.keys.detect{|date| date > order.delivery_at.to_date }.presence || grouped_delivery_windows.keys.select{|date| date < order.delivery_at.to_date }.last

  delivery_window_errors = ['We could not find an appropriate delivery window', 'We had some troubles setting your delivery window please try a different date.', 'Your selected window affects some items in your order.', 'Your selected window affects items in your order. Please update to continue.']

  has_lost_connection =  errors.any?{|error| error == Woolworths::Order::Validate::ACCOUNT_NOT_IN_USE_ERROR }

  has_delivery_window_errors = errors.detect { |error| delivery_window_errors.any?{|delivery_error| error.include?(delivery_error) } }
  has_no_checkout_window = errors.detect { |error| error.include?(Woolworths::Order::Validate::WINDOW_NOT_SET_ERROR) }
  if has_no_checkout_window
    errors = errors - [Woolworths::Order::Validate::WINDOW_NOT_SET_ERROR]
  end

%td.woolworths-order-validation-errors{ colspan: 7 }

  - if errors.present?
    %h3.woolworths-heading There's a problem with your order
    .woolworths-info
      %ul
        - errors.each do |error|
          %li= error

        - if has_lost_connection
          %li
            %a.button.outline-button.tiny{ href: clear_cart_path }
              Clear Cart


  - if @woolworths_delivery_window.present? || has_delivery_window_errors.present? || has_no_checkout_window.present?
    %h3.woolworths-heading.woolworths-order-delivery-window-heading Woolworths delivery windows

    - if @woolworths_delivery_window.present?      
      .woolworths-info
        Based on the delivery date and time that you selected, we have selected the following Woolworths delivery window for your order:
        = succeed '.' do
          %span.delivery-window= @woolworths_delivery_window

    - if has_no_checkout_window.present?
      .woolworths-info
        We could not find an appropriate delivery window. Please select a different date/slot.
  
    .woolworths-info
      .woolworths-order-delivery-window-wrapper
        - if grouped_delivery_windows.present?
          .woolworths-order-delivery-window-text
            %em You can select a different delivery window below:
            %ul.delivery-window-days.clearfix
              - grouped_delivery_windows.each do |date, _|
                - is_active = active_delivery_date == date
                %li.delivery-window-day{ id: "#delivery-window-#{date.to_time.to_i}", class: (is_active ? 'active' : ''), style: "width: calc(100%/#{grouped_delivery_windows.size});" }
                  =raw date.strftime('%A<br>%d-%m-%Y')
            .delivery-window-day-panels
              - grouped_delivery_windows.each do |date, delivery_windows|
                - is_active = active_delivery_date == date
                .delivery-window-day-panel{ id: "delivery-window-#{date.to_time.to_i}", class: (is_active ? 'active' : '') }
                  - delivery_windows.each do |delivery_window|
                    - delivery_window_selected = order.woolworths_order.delivery_window_id == delivery_window.id
                    %button.woolworths-delivery-window.button.outline-button.small{ class: (delivery_window_selected ? 'selected' : ''), data: { woolworths_order_id: order.woolworths_order.try(&:id), delivery_window_id: delivery_window.id, delivery_at: delivery_window.time_range.end } }
                      = delivery_window.label           
              
        - else
          %br/
          No delivery windows are available.

          %button.woolworths-delivery-window.button.outline-button.small{ data: { woolworths_order_id: order.woolworths_order.try(&:id), delivery_window_id: nil, delivery_at: order.delivery_at, reload: 'true', button_text: 'Fetch delivery windows' } }
            Fetch delivery windows
