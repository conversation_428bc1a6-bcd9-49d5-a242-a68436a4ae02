:ruby
  totals = Orders::RetrieveTotals.new(order: order, profile: session_profile).call
  combined_discount = is_customer ? orders.map(&:discount).compact.reduce(:+).to_f : 0
  combined_surcharge = orders.map(&:customer_surcharge).compact.reduce(:+).to_f
  table_gap = table_cols - 3
  table_gap += 1 if !(is_summary_page || is_edit_page)
  total_cols = table_cols - table_gap - 1
  multiple_orders = orders.count > 1
  delivery_overrides = []
  if is_admin?
    delivery_overrides = order.supplier_profiles.map do |supplier|
      delivery_override = DeliveryOverrides::FetchOverride.new(customer: order.customer_profile, supplier: supplier).call
      next if delivery_override.blank? || delivery_override.customer_override.blank?

      "#{supplier.name} (#{number_to_currency(delivery_override.customer_override)})"
    end.compact
  end

%tfoot.table-order__footer{ class: is_print ? 'hide' : '' }
  %tr.table-gap.large

  - if is_summary_page && (@woolworths_errors || @woolworths_delivery_window)
    %tr
      = render 'orders/summary/woolworths_trolley_status', order: order

    %tr.table-gap.large

  %tr
    %td.coupon-wrapper{ class: (is_print ? 'hide' : ''), colspan: '2', rowspan: '9', valign: 'top' }
      - if !order.is_recurrent? && is_customer && (is_summary_page || is_edit_page)
        %p Have a coupon?
        %input.coupon-input{ name: 'coupon', placeholder: 'Coupon code', type: 'text' }/
        %a.button.outline-button.small.apply-coupon{ href: 'javascript:void(0)' }
          Apply Coupon
        %span.coupon-message

      - if is_edit_page
        %a.add-products.button.outline-button.samll{ href: add_more_order_products_path(order) }
          Add More Products

    %td Subtotal
    %td.right{colspan: total_cols}
      %span.order-subtotal
        = number_to_currency(totals[:subtotal], unit: '$ ')

  - hide_discount = !is_customer || combined_discount <= 0
  %tr.discount_wrapper{ class: (hide_discount ? 'hidden' : '') }
    %td
      Discount
    %td.right{ colspan: total_cols }
      %span.order-discount
        = number_to_currency(-1 * totals[:discount], unit: '$ ')

  %tr
    %td
      - if order.is_woolworths_order?
        %span{ title: 'Is re-calculated on checkout based on selected fulfilment store and delivery window. May include delivery packaging charges.', data: { view_tooltip_el: { tooltipClass: 'whats-this__tooltip' }.to_json } }
          Delivery
          %sup
            *
      - elsif delivery_overrides.present?
        %span{ title: "Delivery calculated with #{'override'.pluralize(delivery_overrides.size)} for:<br /> #{delivery_overrides.join('<br />')}.", data: { view_tooltip_el: { allow_html: true, tooltipClass: 'summary-tooptip' }.to_json } }
          Delivery
          %sup
            (with #{'override'.pluralize(delivery_overrides.size)})
      - else
        Delivery

    %td.right{ colspan: total_cols }
      %span.order-delivery
        = number_to_currency(totals[:delivery], unit: '$ ')

  %tr
    %td
      GST
      - if totals[:gst] < 0 && totals[:discount] > 0
        %small
          (inc discounted gst)
    %td.right{ colspan: total_cols }
      %span.order-gst
        = number_to_currency(totals[:gst], unit: '$ ')

  %tr.topup-wrapper{ class: totals[:topup].present? && totals[:topup] > 0 ? '' : 'hidden' }
    %td Top Up
    %td.right{ colspan: total_cols }
      %span.order-topup
        = number_to_currency(totals[:topup], unit: '$ ') if totals[:topup].present?

  - if is_customer
    %tr.surcharge-wrapper{ class: combined_surcharge > 0 ? '' : 'hidden' }
      %td Surcharge
      %td.right{ colspan: total_cols }
        %span.order-surcharge
          = number_to_currency(combined_surcharge, unit: '$ ')

  %tr.table-gap
  - if locations.size > 1
    %tr.location-total-wrapper{ class: (is_summary_page ? '' : 'hidden') }
      %td Level Total
      %td.right{ colspan: total_cols }
        %span.location-total

  %tr.order-total-row{ class: !multiple_orders ? 'total-bold' : ''}
    %td
      = multiple_orders ? 'Daily Total' : 'Total'
    %td.right{ colspan: total_cols }
      %span.order-total{ data: {} }
        = number_to_currency(totals[:total], unit: '$ ')

  - if multiple_orders
    %tr.table-gap
    %tr.total-bold
      %td Weekly Total
      %td.right{ colspan: total_cols }
        %span.weekly-total
