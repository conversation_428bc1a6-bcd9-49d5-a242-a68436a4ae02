:ruby
  display_price = is_customer ? order_line.price : order_line.cost
  display_price = 0.0 if display_price.nil?
  linetotal = display_price * order_line.quantity
  can_update ||= false
  grouped_extras = order_line.selected_menu_extras.present? ? OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call : []

%tr.table-order__item-row.order-line-info.order-line-item{ id: "order-line-#{order_line.id}", data: { order_line_id: order_line.id } }
  %td.table-order__item
    = '*' if order_line.is_gst_free
    = order_line.name

    - if grouped_extras.present?
      %br
      %span.table-order__item-notes
        =raw grouped_extras.map{ |section, extras| "#{section.name}: #{extras.map(&:name).join(',')}" }.join('<br/>')

    %span.table-order__item-notes
      - if can_update
        %input.order-line-note{ name: 'note', placeholder: 'Add item note', type: 'text', value: order_line.note, data: { prev_value: order_line.note } }/
      - else
        = order_line.note

    - if order_line.last_errors.present?
      %br
      %span.error-msg
        =raw order_line.last_errors.join('<br>')

  %td.table-order__item.table-order__item--numeric{ class: (is_print ? 'hide' : '') }
    = number_to_currency(display_price, precision: 2, unit: '$ ')
  %td
    .qty-input.value-counter.clearfix.table-order__item--centered
      %button.minus-btn.toggle-quantity{ type: 'button', disabled: !can_update, data: { toggle: 'decrease' } } -

      %input.count-input.table-order__item.table-order__item--numeric.item-qty{ min: '0', name: "quantity", type: 'number', value: order_line.quantity, disabled: !can_update, onwheel: 'blur();', data: { prev_value: order_line.quantity } }/

      %button.plus-btn.toggle-quantity{ type: 'button', disabled: !can_update, data: { toggle: 'increase' } } +

  %td.table-order__item.table-order__item--numeric.table-order__item--bold.price-field{ class: (is_print ? 'hide' : '') }
    %span.order-line-total= number_to_currency(linetotal, precision: 2)

  - if can_update
    %td.table-order__item--narrow
      %a.delete-icon{ href: 'javascript:void(0)' } delete item
