:ruby
  is_recurrent  = orders.first.is_recurrent?
  is_customer   = session_profile.present? && session_profile.profile.is_customer?
  is_print      = params[:print] == 'true'
  is_summary_page ||= false
  is_edit_page    ||= false
  table_cols    = 5
  table_cols += 1 if is_recurrent

- if is_recurrent
  .day-orders
    - orders.each_with_index do |order, order_index|
      %div.table-order__order-button.button.small{ class: (order_index == 0 ? 'table-order__active-order-button' : ''), id: (is_summary_page ? order.id : '') }
        = order.delivery_at.nil? ? order.name : order.delivery_at.to_s(:weekday)

- orders.each_with_index do |order, order_index|
  %div.table-order{ class: (order_index != 0 ? 'hidden' : ''), id: "order-#{order.id}", is_customer: is_customer, session_profile_id: session_profile.id, data: { order_id: order.id } }
    %table.table-order__table
      :ruby
        lister_options = {
          order: order,
          supplier: (session_profile.profile.is_customer? ? nil : session_profile),
          confirmed_attendees_only: order.is_team_order?,
        }
        order_lines = OrderLines::List.new(options: lister_options).call.order(:id)
        location_grouped_order_lines = order_lines.group_by(&:location)
        locations = location_grouped_order_lines.keys

      - if order.is_team_order?
        - location = locations.first
        %tbody.table-order-location-all
          %tr.table-gap
          %tr.table-order__title-row
            %th.table-order__title{ colspan: table_cols }
              = location.details
              - if location.note.present?
                %br/
                %span.table-order__item-notes= location.note
        - attendee_grouped_order_lines = order_lines.includes(:team_order_attendee, :location).group_by(&:team_order_attendee)
        - attendee_grouped_order_lines.each_with_index do |(attendee, attendee_order_lines), attendee_index|
          = render 'orders/summary/attendee', attendee: attendee, attendee_order_lines: attendee_order_lines, is_customer: is_customer, table_cols: table_cols, is_print: is_print, order: order, attendee_index: attendee_index
      - else
        - if is_summary_page || is_edit_page
          - if locations.size > 1
            - location_options = locations.sort_by(&:id).map{|location| [location.details, location.id] }
            - is_edit_page ? location_options.unshift(['All', 'all']) : location_options << ['All', 'all']
            = select_tag "location-#{order.id}", options_for_select(location_options), class: 'table-order__location-select'
          - else
            %h3
              = locations.first.try(:details)
              %h3
        - location_grouped_order_lines.sort_by{|loc, ol| loc.id }.each_with_index do |(location, location_order_lines), location_index|
          = render 'orders/summary/location', location: location, location_order_lines: location_order_lines, location_index: location_index, is_customer: is_customer, table_cols: table_cols, is_edit_page: is_edit_page, is_print: is_print, order: order, is_summary_page: is_summary_page

      = render 'orders/summary/footer', order: order, orders: orders, is_customer: is_customer, is_print: is_print, table_cols: table_cols, is_edit_page: is_edit_page, locations: locations, is_summary_page: is_summary_page
