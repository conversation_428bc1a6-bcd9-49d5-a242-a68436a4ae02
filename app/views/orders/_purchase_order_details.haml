:ruby
  customer ||= order.customer_profile

- if customer.has_gst_split_invoicing && (gst_free_po_number = order.gst_free_po_number.presence)
  - if (gst_po_number = order.po_number.presence)
    %p 
      %strong PO number (for GST items):
      #{gst_po_number}
  %p 
    %strong PO number (for GST free items):
    #{gst_free_po_number}
- elsif (po_number = order.po_number.presence)
  %p 
    %strong PO number:
    #{po_number}