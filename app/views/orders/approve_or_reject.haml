- content_for :webpack, 'front_end'

.customer-area.has-large-gutter
  .auth-container
    .auth-card
      .auth-card__illustration
        = image_tag 'illustrations/search.svg'
        %h4.auth-card__title Quote Order Approval
      .authorization-module
        %h4 Please verify you are not a bot!
        = form_tag request.path, method: :post, data: { view_email_link_captcha: true } do
          = recaptcha_tags callback: "emailLinkRecaptchaSuccess"