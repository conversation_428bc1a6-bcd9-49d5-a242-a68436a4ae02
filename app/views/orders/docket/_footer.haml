:ruby
  is_new_order = order.status == 'draft'
  button_link = is_new_order ? order_checkout_url : next_app_order_edit_url(order)
  button_text = is_new_order ? 'Go to Checkout' : 'Update'
  
  totals = docket_totals(order: order)
.cart-footer
  %table.cart-summary-table
    %tbody
      %tr
        %td Subtotal
        %td
          %span#docket-subtotal
            = number_to_currency(totals.subtotal)

      - if current_user.present? && current_user.admin?
        %tr
          %td Remove delivery charges?
          %td
            .switch.small.float-right
              %input.switch-input#remove-delivery-fee-switch{ type: 'checkbox', name: '', checked: order.no_delivery_charge }
              %label.switch-paddle{ for: 'remove-delivery-fee-switch' }
                %span.show-for-sr
                  Remove delivery charges?

      %tr
        %td Delivery
        %td
          %span#docket-delivery
            = number_to_currency(totals.delivery)

      %tr
        %td GST
        %td
          %span#docket-gst
            = number_to_currency(totals.gst)

      - if is_admin?
        %tr.custom-order-event-item{ class: ( 'hidden' if !order.is_event_order? ) }
          %td Comission (%)
          %td
            %input.docket-commission-percentage#docket-comussion-percentage{ type: 'number', value: order.commission, step: 'any' }/
        %tr.custom-order-event-item{ class: ( 'hidden' if !order.is_event_order? ) }
          %td COMISSION
          %td
            %span#docket-comission
              $0.00

    %tr.topup-wrapper{ class: totals.topup.present? && totals.topup != 0 ? '' : 'hidden'}
      %td TOPUP
      %td
        %span.docket-topup
          = number_to_currency(totals.topup)

    %tr.surcharge-wrapper{ class: totals.surcharge.present? && totals.surcharge != 0 ? '' : 'hidden'}
      %td SURCHARGE
      %td
        %span.docket-surcharge
          = number_to_currency(totals.surcharge)

    %tfoot
      %tr
        %td TOTAL
        %td
          %span.docket-total{ data: { total: totals.total } }
            = number_to_currency(totals.total)

  %button.docket-checkout-btn.button.large.expanded#docket-check-out-button{ data: { url: button_link } }
    = button_text


