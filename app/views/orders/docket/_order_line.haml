:ruby
  grouped_extras ||= order_line.selected_menu_extras.present? ? OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call : []

  is_team_admin = order.is_team_order? && session_profile.present? && session_profile.id == order.customer_profile_id
  show_pricing =  !order.is_team_order? || is_team_admin || (order.is_team_order? && !order.hide_budget)

%li.cart-list-item{ data: { order_line_id: order_line.id } }
  .item-counter.docket-qty-menu-item
    %input{type: 'text', value: order_line.quantity, data: { prev_value: order_line.quantity } }

  %p.item-title
    = '* ' if order_line.is_gst_free
    = order_line.name
    - if grouped_extras.present?
      %br
      %span.docket-menu-item-note{ style: 'color: #5D5D5D' }
        =raw grouped_extras.map{ |section, extras| "#{section.name.presence || 'extras'}: #{extras.map(&:name).map(&:strip).join(',')}" }.join('<br/>')

  %span.item-summ
    - if show_pricing
      - order_line_price = order.is_team_order? ? order_line.price_inc_gst(gst_country: order.symbolized_country_code) : order_line.price
      = number_to_currency(order_line_price, precision: 2)
      = hidden_field_tag '', order_line_price

  %span.item-remove
    %a.delete-icon{ href: 'javascript:void(0)' }
      delete item

  %p.docket-menu-item-note
    = order_line.note
