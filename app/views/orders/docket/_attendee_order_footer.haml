:ruby
  is_ordering_on_behalf_as_team_admin = session_profile.present? && order.customer_profile == session_profile
  budget = (team_order_attendee.is_team_admin? || is_ordering_on_behalf_as_team_admin) ? nil : order.team_order_budget
  show_totals = budget.blank? || !order.hide_budget

  totals = docket_totals(order: order, attendee: team_order_attendee)

.cart-footer
  - if show_totals
    %table.cart-summary-table
      %tbody
        %tr
          %td Subtotal
          %td
            %span#docket-subtotal
              = number_to_currency(totals.subtotal)

        %tr
          %td GST
          %td
            %span#docket-gst
              = number_to_currency(totals.gst)

      %tfoot
        %tr
          %td TOTAL
          %td
            %span.docket-total{ data: { total: totals.total } }
              = number_to_currency(totals.total)
  - else
    // used for checking team order budget
    %span.docket-total{ data: { total: totals.total, hide_total: 'true' } }

  - if order.attendee_levels.present? && !team_order_attendee.is_team_admin?
    %label Delivery Level
    = select_tag 'team_order_level_id', options_from_collection_for_select(order.attendee_levels, :id, :name, team_order_attendee.team_order_level_id), { prompt: 'Select a level'}
    
  %button.docket-checkout-btn.button.large.expanded#team-order-docket-check-out-button{ data: { url: api_team_order_attendee_checkout_path(team_order_attendee_id: team_order_attendee.uniq_code), budget: budget, hide_budget: (budget.present? && order.hide_budget.present?).to_s, team_order: 'true' } }
    Confirm
  %button.docket-close.button.large
    Back to Menu


