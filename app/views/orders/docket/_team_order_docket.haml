:ruby
  team_order ||= order
  attendee_id = team_order_attendee.is_team_admin? ? nil : team_order_attendee.id
  attendee_order_lines = team_order.order_lines.where(attendee_id: attendee_id).order(:id)
  team_order_spends = Orders::GetSupplierSpends.new(order: team_order, exclude_surcharge: true).call if team_order_attendee.is_team_admin?
  team_admin = team_order.customer_profile

.docket-wrapper.team-order{ data: { team_order_attendee_id: team_order_attendee.id, order_id: team_order.id, company_id: team_admin.company_id } }
  %ul.cart-accordion.accordion{ data: { accordion: 'true', allow_all_closed: 'true' } }
    .team-docket-header
      .off-canvas-header
        %h3
          - if team_order.is_package_order?
            %span{ title: team_order.name }
              = team_order.name.titleize.truncate(20)
            %span.float-right
              = team_order.delivery_at.to_s(:full_date)
          - else
            = team_order.name

      .cart-switch.clearfix
        %h6.float-left TEAM ORDER
        %h6.float-right
          - if team_order_spends.present? # ordering as team admin

            - total_spend = team_order_spends.total_spend || 0
            - remaining_spend = team_order_spends.remaining_spend || 0
            Total:
            %span.team-order-total-spend
              = total_spend > 0 ? number_to_currency(total_spend, precision: 2) : '$0'
            | Remaining:
            %span.team-order-remaining-spend
              = remaining_spend > 0 ? number_to_currency(remaining_spend, precision: 2) : '$0'

          - elsif team_order.team_order_budget.present? && !team_order.hide_budget
            Your budget: #{number_to_currency(order.team_order_budget)}

    = render 'orders/docket/order', order: team_order, order_lines: attendee_order_lines

  = render 'orders/docket/attendee_order_footer', order: team_order, team_order_attendee: team_order_attendee

  - if team_order_attendee.is_team_admin?
    = link_to 'Back to Order', team_order_path(team_order), { class: 'add-another-supplier' }
  - elsif team_order.is_package_order?
    = link_to 'Back to Package', team_order_attendee_package_url(code: team_order_attendee.uniq_code), { class: 'add-another-supplier' }

