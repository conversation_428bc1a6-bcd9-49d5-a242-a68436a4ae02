.recurring-form-wrapper
  .recurring-box
    %input#recurring-switch{ type: 'checkbox' }/
    %label{for: 'recurring-switch'}
      Set as recurring order?

  .recurring-settings.hidden
    %h2 Recurring Order
    %p Repeat this order weekly, fortnightly or monthly
    = form_tag make_recurrent_api_orders_path do
      .row
        .small-12.columns
          %label.middle{ for: 'frequency-tabs' } Frequency
          %select.frequency-tabs.text-center{ name: 'repeat[frequency]'}
            %option{ value: 'weekly' }
              Weekly
            %option{ value: 'fortnightly' }
              Fortnightly
            %option{ value: 'monthly' }
              Monthly

      .row
        .small-12.columns
          %label.middle.frequency-label Select Days

        .small-12.columns
          .day-switch.button-group
            - Date::DAYNAMES.each_with_index do |day, index|
              - day_string = day.slice(0, 3)
              .checkbox-weekday
                = check_box 'repeat', "[days][#{day_string.downcase}]" , { class: 'checkbox-weekday__input' }, 'true', 'false'
                %label.checkbox-weekday__label
                  = day_string

      .row
        .copy-order-settings
          = check_box_tag 'repeat[copy_all]', true, checked: true
          = label_tag 'repeat[copy_all]', 'Copy order to all selected days?'
        .order-holiday-settings
          %label On Public Holidays
          %ul.radio-option-list.no-bullet
            %li
              = radio_button_tag 'repeat[skip]', true , checked: true
              = label_tag 'repeat_skip_true', 'Skip the delivery'
            %li
              = radio_button_tag 'repeat[skip]', false
              = label_tag 'repeat_skip_false', 'Deliver on next business day'

        = submit_tag 'Save', class: 'submit button expanded', id: 'order-type-recurring-change'
