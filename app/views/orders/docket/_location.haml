%li.accordion-item.order-docket-levels.location-level{ data: { accordion_item: true, accordion_item_id: "docket-location-id-#{location.id}", location_id: location.id } }

  %a.accordion-title.order-docket-levels__title{ class: (order.is_team_order? ? 'hidden' : '') }
    %span.order-docket-location-title
      = location.details
    %span.delete-docket-level.order-docket-levels__title-action.float-right
      Remove
    %span.order-docket-levels__title-action.float-right.edit-location{ id: "docket-location-edit-id-#{location.id}", data: { id: location.id } }
      Edit

  .item-meta.clearfix.edit-location-form.hidden{ id: "docket-location-edit-id-#{location.id}" }
    .row
      .large-12.columns
        %input.location-details.edit-location-details{type: 'text', id: "docker-location-edit-value#{location.id}", value: location.details }/

    .row
      .small-12.columns.text-right
        %button.edit-location-cancel-btn.button.small.gray-btn{ href: 'javascript:void(0)' }
          Cancel
        %button.edit-location-btn.button.small.margin-left-half{ id: "docket-location-edit-value-#{location.id}", data: { id: location.id } }
          Update

  .suppliers-list.accordion-content{ data: { tab_content: true } }
    - supplier_grouped_order_lines = order_lines.group_by(&:supplier_profile)
    - supplier_grouped_order_lines.each do |supplier, supplier_order_lines|
      = render 'orders/docket/supplier', supplier: supplier, order_lines: supplier_order_lines, order: order

