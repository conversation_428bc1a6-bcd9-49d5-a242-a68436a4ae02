:ruby
  location_grouped_order_lines = order_lines.group_by(&:location)
  empty_locations = order.new_record? ? [] : Location.where(order: order).where.not(id: location_grouped_order_lines.keys.map(&:id))

- location_grouped_order_lines.each do |location, order_lines|
  = render 'orders/docket/location', location: location, order_lines: order_lines, order: order

- empty_locations.each do |location|
  = render 'orders/docket/location', location: location, order_lines: [], order: order

.location-break

= render 'orders/docket/add_location' unless order.is_team_order?
