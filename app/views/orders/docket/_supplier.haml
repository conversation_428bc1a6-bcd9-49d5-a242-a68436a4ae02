.accordion-content.supplier-level{ style: 'display: block;', data: { tab_content: true, supplier_id: supplier.id } }
  .cart-list-head.clearfix
    %span.cart-list-title
      = supplier.company_name

    - if !order.is_team_order?
      %a.arrow-link.float-right{ href: next_app_supplier_show_url(supplier&.slug, **request.query_parameters) }
        View Menu
    = hidden_field_tag "supplier-id-#{supplier.id}", '0.0', class: 'supplier-delivery-fee', name: "supplier-id-#{supplier.id}"


  %ul.cart-list.no-bullet
    - order_lines.each do |order_line|
      = render 'orders/docket/order_line', order_line: order_line, order: order
