:ruby
  order_lines = order.order_lines.order(:id)

.docket-wrapper{ data: { order_id: order.id, company_id: is_customer? && session_profile.company_id, supplier_id: @supplier.id, is_home_delivery: order.new_record? && is_home_delivery? } }
  - if order.status == 'draft'
    - if recurrent_orders.present?
      .recurrent-order-container
        = render 'orders/docket/recurring_order', order: order, recurrent_orders: recurrent_orders
    - elsif !@supplier.woolworths? && !is_home_delivery?
      = render 'orders/docket/recurring_order_form'
  - else
    %h3.px-1 You are editing an order!

  %ul.cart-accordion.accordion{ data: { accordion: 'true', allow_all_closed: 'true' } }
    = render 'orders/docket/order', order: order, order_lines: order_lines

  = render 'orders/docket/footer', order: order