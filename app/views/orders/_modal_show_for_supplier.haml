:ruby
  supplier = session_profile
  coordinates = Geocoder.search("#{order.delivery_address} #{order.delivery_suburb.postcode}").first.try(:coordinates)
  lister_options = {
    order: order,
    supplier: supplier,
    confirmed_attendees_only: order.is_team_order?
  }
  supplier_order_lines = OrderLines::List.new(options: lister_options, includes: [:location, :supplier_profile]).call
  supplier_order_lines = supplier_order_lines.order(:id)
  location_grouped_order_lines = supplier_order_lines.group_by(&:location)

  status = order.status
  status = 'rejected' if supplier_order_lines.present? && supplier_order_lines.all?{|order_line| order_line.status == 'rejected' }
  is_pending_approval = %w[new amended confirmed].include?(order.status) && supplier_order_lines.where.not(order_lines: { status: %w[rejected accepted] }).present?

.order-show__details.full-height
  = render 'orders/delivery_details', order: order, for_modal: true, status: status, map_size: '450x180'

- if is_pending_approval
  %div.supplier-modal-optns
    %button.button.primary-btn.order-confirm-btn{style: "margin-right: 20px", data: { url: api_order_confirm_order_path(order), order_id: order.id } }
      Confirm
    %button.button.hollow.border-red.order-reject-btn{ data: { url: api_order_reject_order_path(order), order_id: order.id } }
      Reject

- if Rails.env.production?
  .intercom-separator
