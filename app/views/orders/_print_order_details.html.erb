<%
	instructions = order.delivery_instruction
	if session_profile.profile.is_supplier?
		# custom order has supplier specific settings
		custom_order_supplier = CustomOrderSupplier.where(order: order, supplier_profile: session_profile).first
		instructions = custom_order_supplier.delivery_note if custom_order_supplier.present?
	end
%>
<img src="/assets/logo.svg" />
<section class="display-order-details">
	<div class="delivery-details group">
		<h3><%= order.name %> - #<%= order.id%> (<%= order.customer_profile.customer_or_company_name%>)</h3>

		<div class="form-sec-order-details group left">
			<div class="order-info">
			<span class="name" >Delivery date & time: </span>
			<span class="value" ><%= order.delivery_at.to_s(:full) %></span>
			</div>
			<div class="order-info">
			<span class="name" >Delivery address: </span>
			<span class="value" ><%= order.delivery_address_arr.join(', ') %></span>
			</div>
			<div class="order-info">
			<span class="name" >Contact name: </span>
			<span class="value" ><%= order.contact_name %></span>
			</div>
			<div class="order-info">
			<span class="name" >Contact email: </span>
			<span class="value" ><%= order.contact_email %></span>
			</div>
		</div>
		<div class="form-sec-order-details group right">
			<div class="order-info">
			<span class="name" >Contact phone: </span>
			<span class="value" ><%= order.phone %></span>
			</div>
			<div class="order-info">
			<span class="name" >Company name: </span>
			<span class="value" ><%= order.company_name%></span>
			</div>
			<div class="order-info">
			<span class="name" >Po Number: </span>
			<span class="value" ><%= order.po_number%></span>
			</div>
			<div class="order-info">
			<span class="name" >Cost Centre ID: </span>
			<span class="value" ><%= order.department_identity %></span>
			</div>

	</div>

		<div class="delivery-instruction bottom">
			<div class="order-info">
			<span class="name">Delivery Instructions:</span>
			<span class="value"><%= instructions %></span>
			</div>
		</div>
</section>
