:ruby
  order = @order
  lister_options = {
    order: order,
    supplier: session_profile,
    confirmed_attendees_only: order.is_team_order?
  }
  supplier_order_lines = OrderLines::List.new(options: lister_options, includes: [:location, :supplier_profile]).call
  supplier_order_lines = supplier_order_lines.order(:id)

  order_status = order.status
  order_status = 'rejected' if supplier_order_lines.present? && supplier_order_lines.all?{|order_line| order_line.status == 'rejected' }
  is_pending_approval = %w[new amended confirmed].include?(order.status) && supplier_order_lines.where.not(order_lines: { status: %w[rejected accepted] }).present?

  formatted_date = order.delivery_at.strftime("%l:%M%P on %A the #{order.delivery_at.day.ordinalize} of %b %Y")  

- content_for :header_title, "Order: ##{order.id}"
- content_for :back_button do
  = link_to customer_profile_path(show_past: order.delivery_at < Time.zone.now ? true : nil) do
    %span.customer-header-back-link
- content_for :webpack, 'supplier'

- if @print_friendly
  = render 'orders/print_order_details', order: order

.order-show{ data: { view_supplier_order: true } }
  %h2
    = order.name
  .order-show-header
    .order-show__options
      %p.date= formatted_date
      = link_to 'Print', 'Print', id: 'print-link' , onclick: 'window.print();return false;',class: 'button hollow'

    .order-show__options
      - if is_pending_approval
        %div
          %button.button.order-confirm-btn{ data: { url: api_order_confirm_order_path(order), order_id: order.id, reload: 'true' } }
            Confirm
          %button.button.hollow.order-reject-btn{ data: { url: api_order_reject_order_path(order), order_id: order.id, reload: 'true' } }
            Reject

      = render 'orders/supplier_documents', order: order        

  .order-show-body
    = render 'orders/order_lines_table', order: order, status: order_status, order_lines: supplier_order_lines
    
    = render 'orders/delivery_details', order: order, status: order_status

