:ruby
  order ||= @order
  formatted_date = order.delivery_at.strftime("%l:%M%P on %A the #{order.delivery_at.day.ordinalize} of %b %Y")
  lister_options = {
    order: order,
    confirmed_attendees_only: order.is_team_order?
  }
  order_lines = OrderLines::List.new(options: lister_options, includes: [:location, :supplier_profile]).call

- content_for :header_title, "Order: ##{order.id}"
- content_for :container_class, 'dashboard-table'

- content_for :back_button do
  - if (meal_plan = order.meal_plan.presence)
    = link_to customer_meal_plans_path(mealUUID: meal_plan.uuid, date: order.delivery_at&.to_s(:date_spreadsheet)) do
      %span.customer-header-back-link
  - else
    = link_to customer_profile_path(show_past: order.delivery_at < Time.zone.now ? true : nil) do
      %span.customer-header-back-link

.order-show
  %h2= order_display_name(order)

  .order-show-header
    .order-show__options
      %p.date
        = formatted_date

      - if order.status == 'saved'
        %a.button.hollow{ href: api_order_clone_path(order) } Place Order
      - else
        = render 'orders/edit_button', order: order        

    .order-show__options
      = render 'orders/customer_documents', order: order

  .order-show-body
    = render 'orders/order_lines_table', order: order, order_lines: order_lines.order(:id)
    
    = render 'orders/delivery_details', order: order

