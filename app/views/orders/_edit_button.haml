:ruby
  can_edit = can_edit?(order: order)

  return '' if !can_edit

  lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order).call

- if lead_time_fetcher.can_process? || is_admin?
  %a.button.hollow{ href: next_app_order_edit_url(order) } Edit
- else
  %span.button.gray-btn{ href: 'javascript:void(0)', title: 'Unfortunately this order has passed it\'s lead time. Please contact support if this is an issue.', data: { view_tooltip_el: true } } Edit