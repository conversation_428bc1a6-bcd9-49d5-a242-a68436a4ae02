:ruby
  document_kinds = %w[
    supplier_order_details
    supplier_heads_up_order_details
    supplier_order_delivery_details
    supplier_json_order_details
    team_order_avery_labels
  ]
  order_documents = order.documents.where(kind: document_kinds).order(version: :desc)
  order_suppliers = order.order_suppliers.where(supplier_profile: session_profile)
  supplier_documents = order_suppliers.present? ? Document.where(kind: document_kinds, documentable: order_suppliers).order(version: :desc) : []
  template_documents = []

  # get template order documents for a recurrent order with no documents of its own
  if supplier_documents.blank? && order.is_recurrent?
    template_order_supplier = OrderSupplier.where(order_id: order.template_id, supplier_profile: session_profile)
    template_documents = order_suppliers.present? ? Document.where(kind: document_kinds, documentable: template_order_supplier).order(version: :desc) : []
  end
  
  documents = order_documents + supplier_documents

  return if documents.blank? && template_documents.blank?

%p
  %a.button.hollow.tiny{ data: { open: 'modal-order-documents' } }
    View Documents

#modal-order-documents.reveal.modal.modal-drawer{ data: { reveal: '' } }  

  - if documents.present?
    %h3.modal-title Order Documents
    %hr
    .modal-content
      - documents.group_by(&:version).each do |version, versioned_documents|
        %p
          Version: #{version}
        %ul
          - versioned_documents.sort_by{|document| Document::VALID_KINDS.index(document.kind) }.each do |document|
            %li.bullet-list
              = link_to document.url, target: '_blank' do
                = "#{document.kind_based_name} - #{document.file_extension.upcase}"

  - if template_documents.present?
    %h3.modal-title Template Order Documents
    %hr
    .modal-content
      - template_documents.group_by(&:version).each do |version, versioned_documents|
        %p
          Version: #{version}
        %ul
          - versioned_documents.sort_by{|document| Document::VALID_KINDS.index(document.kind) }.each do |document|
            %li.bullet-list
              = link_to document.url, target: '_blank' do
                = "#{document.kind_based_name} - #{document.file_extension.upcase}"