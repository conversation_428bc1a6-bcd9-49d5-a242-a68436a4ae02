:ruby
  is_quotes_page ||= false
  customer ||= session_profile
  catering_supplier_url = cookie_suburb.present? ? next_app_supplier_search_url(category_group: 'office-catering', state: cookie_suburb.state, suburb: cookie_suburb.name.gsub(/\s/, '-')) : '#supplier-search-office-catering'
  snack_supplier_url = cookie_suburb.present? ? next_app_supplier_search_url(category_group: 'office-snacks', state: cookie_suburb.state, suburb: cookie_suburb.name.gsub(/\s/, '-')) : '#supplier-search-office-snacks'

  external_order_urls = {
    catering: catering_supplier_url,
    pantry: snack_supplier_url,
    quote: next_app_customer_quotes_url,
  }

  customer_details = {
    name: customer.name,
    id: customer.id,
  }

  url_params = params.permit(:query, :order_type, :order_date, :show_past, :only_quotes, :supplier_name, supplier_ids: [])

%div{ data: { view_team_order_show: true, view_customer_orders: { customer: customer_details, externalOrderUrls: external_order_urls, domain: cookie_domain(host: request&.host), isAdmin: session[:sign_in_as_admin].present?, urlParams: url_params, defaultOrdersView: is_quotes_page ? 'list' : customer.default_orders_view, onlyQuotes: is_quotes_page }.to_json, view_order_slideout: true } }

#modal-order-show.reveal.modal.modal-drawer{ data: { reveal: '' } }
