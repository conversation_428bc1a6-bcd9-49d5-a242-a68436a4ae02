- suppliers.each do |supplier|
  - if is_admin?
    .delivery-details-suppliers--admin
      %strong= supplier.name
      - if supplier.phone.present?
        %p 
          Phone: 
          %a{ href: "tel:#{supplier.phone}" }
            = supplier.phone
      - if supplier.mobile.present?
        %p 
          Mobile:
          %a{ href: "tel:#{supplier.mobile}" }
            = supplier.mobile
      - if supplier.email.present?
        %p
          Email: 
          %a{ href: "mailto:#{supplier.email}" }
            = supplier.email
  - else
    .delivery-details-suppliers--customer{style: 'padding-block: 0.125rem'}
      .circle-icon
        - if supplier.profile.avatar
          = resize_image_tag(supplier.profile.avatar, "Supplier Avatar", 200, 200, 'fill')
        - else
          = supplier_name_helper(:initials, supplier.name)
      = link_to supplier.name, next_app_supplier_show_url(supplier&.slug)
      

