:ruby
  order = session_order
  supplier = order.supplier_profiles.last
  date = order.delivery_at

  can_validate_path = order.order_lines.any?{|order_line| order_line.last_errors.present? }
  can_check_minimums = !order.has_woolworths_items?
  can_charge_to_minimums = can_check_minimums && !order.is_recurrent?

  validate_order_path = can_validate_path ? api_order_validate_path(order) : ''
  minimum_spend_path = can_check_minimums ? api_order_check_minimum_spend_path(order) : ''
  minimum_charge_path = can_charge_to_minimums ? api_order_path(order) : ''

  button_text = 'Checkout'
  button_text = 'Validate' if @woolworths_errors.present?
  button_next_link = order_checkout_url

- content_for :webpack, 'front_end'

.summary-body.has-gutter
  .row.summary-page
    .small-12.columns
      .checkout-module{ data: { view_order_summary: true, view_multi_level_order_summary: true } }
        .checkout-form
          %h1.form-title.text-center Order Review
          %p.form-description.text-center
            - if order.is_recurrent?
              Recurring Order
            - else
              This is a one off order,
              = link_to 'switch to recurring?', next_app_supplier_show_url(supplier&.slug), class: 'recurring-switch-link'
          #order-lines-table
            = render 'orders/summary/order_lines_table', orders: @orders, is_summary_page: true

          .summary-btns.order_lines_table__cta.text-right.has-small-gutter
            - if supplier.present?
              %a.add-product.menu-return-btn.button.small{ href: next_app_supplier_show_url(supplier&.slug) }
                ← Back to Supplier Menu
            - else
              %span{ title: 'for flex purposes' }

            = button_tag button_text, class: 'checkout-btn button small uppercase', data: { validate_order_url: validate_order_path, min_spend_url: minimum_spend_path, min_charge_url: minimum_charge_path , next_url: button_next_link, button_text: button_text }

