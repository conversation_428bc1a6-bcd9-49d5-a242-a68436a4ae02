:ruby
  is_edit_page ||= false

  customer = session_profile
  show_billing_details = !is_edit_page && (customer.billing_details.blank? || !customer.billing_details.valid?)

  checkout_panels = {
    'order-details' => 'Order Details',
    'billing-details' => 'Billing Details',
    'payment-details' => 'Payment Details',
  }
  checkout_panels = checkout_panels.except('billing-details') if !show_billing_details
  checkout_panels = checkout_panels.map{|key, value| { key: key, label: value }}

  order_submit_path = api_order_submit_path(order, format: :json)

  if is_edit_page
    order_fetch_path = edit_api_order_path(order, format: :json)
    order_submit_path = api_order_amend_path(order, format: :json) if order.status != 'quoted'
    customer_fetch_path = customer_checkout_details_api_customers_path(order_id: order.id, format: :json) # passed in order
  else
    order_fetch_path = checkout_api_orders_path(format: :json)
    customer_fetch_path = customer_checkout_details_api_customers_path(format: :json) # from session
  end

  suburb = order.delivery_suburb || cookie_suburb
  supplier_search_url = suburb.present? ? next_app_supplier_search_url(category_group: 'office-catering', state: suburb.state, suburb: suburb.name.gsub(/\s/, '-')) : '#supplier-search-office-catering'

  google_config = {  
    api_key: yordar_credentials(:google, :api_key),
    map_key: yordar_credentials(:google, :maps_api_key),
    map_url: yordar_credentials(:google, :maps_api_url),
  }

.checkout-module{ class: (is_edit_page ? '' : 'details-and-success'), data: { view_order_checkout: { panels: checkout_panels, supplierIndexUrl: supplier_search_url, googleConfig: google_config, isAdmin: is_admin?, isEditPage: is_edit_page, showBillingDetails: show_billing_details, stripeKey: yordar_credentials(:stripe, :publishable_key), countryCode: request_country_code, domain: cookie_domain(host: request&.host), isWoolworths: order.is_woolworths_order?, orderID: order.id, finaliseQuote: order.status == 'quoted' && params[:finalise_quote].present? }.to_json } }
