:ruby
  order = @checkout_order.order
  orders = order.is_recurrent? ? @checkout_order.recurrent_orders : [order]  
.medium-12.large-3.columns.customer-order{data: { view_checkout_slider: true } }
  %h5.uppercase.customer-order__heading.main Your Order
  %div
    - if order.is_recurrent?
      - orders.each_with_index do |recurrent_order, order_index|
        %div.table-order__order-button.button.small{ id: recurrent_order.id, class: (order_index == 0 ? 'table-order__active-order-button' : '') }
          = recurrent_order.delivery_at.nil? ? recurrent_order.name : recurrent_order.delivery_at.to_s(:weekday)

  - orders.each_with_index do |recurrent_order, order_index|
    %div.customer-order__orders{ class: "order-#{recurrent_order.id}" + (order_index != 0 ? ' hidden' : '') }
      - location_grouped_order_lines = recurrent_order.order_lines.includes(:location, :supplier_profile).order(:id).group_by(&:location)
      - location_grouped_order_lines.each_with_index do |(location, location_order_lines), location_index|
        %h6.customer-order__heading.with-chevron.between-flex.collapsible= location.details
        %div.content{class: (location_index == 0 ? 'first-location' : '') }
          - location_order_lines.group_by(&:supplier_profile).each do |supplier, supplier_order_lines|
            .customer-order__supplier-info
              .customer-order__supplier-banner
                = resize_image_tag(supplier.profile.avatar, supplier.company_name, 40, 40, "fill")
                %span.customer-order__supplier-name
                  = supplier.name
              - supplier_order_lines.each do |order_line|
                .order-show-table-row
                  %p.customer-order__ol-quantity
                    = "#{order_line.quantity}x"
                  %p.customer-order__ol-name
                    = order_line.name
                  %p.customer-order__ol-price
                    = number_to_currency(order_line.price)
      .customer-order__totals

        %p.between-flex
          %span
            Total
            %small
              (exc GST)
          %span
            %strong= number_to_currency(recurrent_order.customer_subtotal)

        - if recurrent_order.customer_delivery.present? && recurrent_order.customer_delivery > 0
          %p.between-flex
            Delivery
            %span
              %strong= number_to_currency(recurrent_order.customer_delivery)

        - if recurrent_order.discount.to_f > 0
          %p.between-flex
            Discount
            %span
              %strong= number_to_currency(-1 * recurrent_order.discount)

        %p.between-flex
          %span
            GST
            - if recurrent_order.customer_gst.to_f < 0 && recurrent_order.discount.to_f > 0
              %small
                (inc discounted gst)
          %span
            %strong= number_to_currency(recurrent_order.customer_gst)

        - if recurrent_order.customer_topup.to_f > 0
          %p.between-flex
            Topup
            %span
              %strong= number_to_currency(recurrent_order.customer_topup)

        - if recurrent_order.customer_surcharge.to_f > 0
          %p.between-flex
            Surcharge
            %span
              %strong= number_to_currency(recurrent_order.customer_surcharge)

      %p.uppercase.between-flex
        %strong Total
        %span
          %strong= number_to_currency(recurrent_order.customer_total)
