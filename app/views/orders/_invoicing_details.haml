:ruby
  is_customer ||= (session_profile.present? && session_profile.is_a?(CustomerProfile))
  order_invoice = order.invoice
  order_card = order.credit_card_id == 1 ? nil : order.credit_card
  customer = order.customer_profile
  billing_frequency = customer.billing_frequency || 'instantly'
  pay_on_account_by_card = order_card&.pay_on_account?
  billing_date = case billing_frequency
  when 'monthly'
    order.delivery_at.end_of_month + 1.day
  when 'weekly'
    order.delivery_at.end_of_week + 1.day
  else # instantly
    order.delivery_at + 1.day
  end

  return if !is_customer || %w[new amended confirmed delivered].exclude?(order.status)

- if order_card.present?
  - if order_invoice.present?
    %p
      %strong Invoiced:
      = link_to order_invoice.latest_document&.url, target: '_blank' do
        = "##{order_invoice.number}"
      (on #{order_invoice.to_at.to_s(:date_verbose)})
      - if pay_on_account_by_card && billing_frequency != 'instantly' && !order.invoice_individually
        %small
          = " (as part of #{billing_frequency} billing)"
    %p
      %strong Paid with:
      Card ending in ...#{order_card.last4}

  - else
    %p
      %strong Attached to:
      Card ending in ..#{order_card.last4}.
    - if !pay_on_account_by_card || billing_frequency == 'instantly' || order.invoice_individually
      %p
        This order will be invoiced individually on delivery.
    - else
      %p
        Will be invoiced as part of #{billing_frequency} billing on #{billing_date}.
- else
  - if order_invoice.present?
    %p
      %strong Invoiced:
      = link_to order_invoice.latest_document&.url, target: '_blank' do
        = "##{order_invoice.number}"
      (on #{order_invoice.to_at.to_s(:date_verbose)})
      - if billing_frequency != 'instantly' && order.invoice_individually
        %small
          = " (invoiced individually outside #{billing_frequency} billing)"
      - else
        %small
          = " (invoiced as part of #{billing_frequency} billing)"

  - else
    - if order.invoice_individually || billing_frequency == 'instantly'
      %p
        This order will be invoiced individually on delivery.
    - else
      %p
        Will be invoiced as part of #{billing_frequency} billing on #{billing_date}.