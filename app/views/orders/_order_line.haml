:ruby
  grouped_extras = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call
  extras_string = grouped_extras.map{ |section, extras| "#{section.name.capitalize}: #{extras.map(&:name).join(', ')}" }.join(', ')

  order_line_value = session_profile.is_a?(CustomerProfile) ? order_line.price : (order_line.baseline || order_line.cost)

.orderline
  .order-show-table-row
    .orderline-image-name
      .orderline-image
        - if order_line.menu_item.image?
          = cl_image_tag(order_line.menu_item.ci_image, width: 50, height: 50, crop: 'fill', quality: 'auto,fl_lossy,f_auto', alt: order_line.name, class: "circle-icon")
          %span.orderline-count= order_line.quantity
        - else
          .circle-icon
            = order_line.name.first
          %span.orderline-count
            = order_line.quantity
      %div
        %p.orderline-name= order_line.name
        - if order_line.note.present?
          %p.orderline-note.grey{ title: order_line.note }
            Note: #{order_line.note}
    .extras
      - if grouped_extras.present?
        %span.grey{ title: extras_string }
          = extras_string
    %p
      = "#{order_line.quantity} x #{number_to_currency(order_line_value)}"
    %p.customer-order__ol-price
      %strong
        = number_to_currency(order_line_value * order_line.quantity)
