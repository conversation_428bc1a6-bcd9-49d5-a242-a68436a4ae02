:ruby
  discounted_totals = totals = Orders::RetrieveTotals.new(order: order, profile: session_profile, baseline: false).call

  if session_profile.is_a?(SupplierProfile)
    baseline_totals = Orders::RetrieveTotals.new(order: order, profile: session_profile, baseline: true).call
    totals = baseline_totals.subtotal != 0 ? baseline_totals : discounted_totals
  end
  with_discount_totals = discounted_totals.total != totals.total
  discount = totals.respond_to?(:discount) ? totals.discount : 0
  surcharge = totals.respond_to?(:surcharge) ? totals.surcharge : 0

.customer-order__totals
  %p.between-flex.grey
    Subtotal
    %span= number_to_currency(totals.subtotal)

  - if totals.delivery.present? && totals.delivery > 0
    %p.between-flex.grey
      Delivery
      %span= number_to_currency(totals.delivery)

  - if discount.to_f > 0
    %p.between-flex.grey
      - if (promotion = order.promotion.presence)
        = "#{promotion.name} (#{promotion.discount_note})"
      - else
        Discount
      %span= number_to_currency(-1 * discount)

  %p.between-flex.grey
    GST
    - if totals.gst.to_f < 0 && discount.to_f > 0
      %small
        (inc discounted gst)
    %span= number_to_currency(totals.gst)

  - if totals.topup.to_f > 0
    %p.between-flex.grey
      Topup
      %span= number_to_currency(totals.topup)

  - if surcharge.to_f > 0
    %p.between-flex.grey
      Surcharge
      %span= number_to_currency(surcharge)
  %p.between-flex{ class: (with_discount_totals ? 'grey' : '') }
    Total
    %span= number_to_currency(totals.total)

  - if with_discount_totals
    %p.between-flex
      %span
        Total
        %small
          (with discount)
      %span= number_to_currency(discounted_totals.total)
