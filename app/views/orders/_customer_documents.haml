:ruby
  document_kinds = %w[customer_order_quote customer_order_details customer_team_order_details]
  documents = order.documents.where(kind: document_kinds).order(version: :desc)

  return if documents.blank? && !order.is_team_order?

%p
  %a.button.hollow.tiny{ data: { open: 'modal-order-documents' } }
    View Documents

#modal-order-documents.reveal.modal.modal-drawer{ data: { reveal: '' } }  
  %h3.modal-title Order Documents
  %hr
  .modal-content
    - documents.group_by(&:version).each do |version, versioned_documents|
      %p
        Version: #{version}
      %ul.bullet-list
        - versioned_documents.sort_by{|document| Document::VALID_KINDS.index(document.kind) }.each do |document|
          %li
            = link_to document.url, target: '_blank' do
              = "#{document.kind_based_name} - #{document.file_extension.upcase}"

    - if order.is_team_order?

      %p.manifest-document-handle{ data: { view_customer_team_order_manifest: { url: api_team_order_generate_order_manifest_path(order) }.to_json } } 
        %a.button.generate-manifest
          Generate Team Order Manifest