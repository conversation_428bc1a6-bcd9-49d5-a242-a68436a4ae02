:ruby
  status ||= order.status
  location_grouped_order_lines = order_lines.group_by(&:location)

  is_customer = session_profile.present? && session_profile.is_a?(CustomerProfile)
  show_totals = is_customer || order.status != 'pending'

.order-show__orderlines
  %div
    .orderlines-title
      - if !is_customer && status == 'pending'
        %p.status-icon.orderlines-title__sub.with-tooltip{ class: status, title: 'The customer is still building the order' }
          In Progress
          %span.icon-info-circle
      - else
        %p.status-icon.orderlines-title__sub{ class: status }
          = status.titleize
      %p.address.orderlines-title__sub
        = order.delivery_address_arr.join(', ')
    .customer-order__orders{ class: "order-#{order.id}", data: { view_checkout_slider: true } }
      - location_grouped_order_lines.each do |location, location_order_lines|
        %h6.customer-order__heading
          = location.details
        %div
          - location_order_lines.group_by(&:supplier_profile).each do |supplier, supplier_order_lines|
            %p.supplier-name= supplier.name
            - supplier_order_lines.each do |order_line|
              = render 'orders/order_line', order_line: order_line
  - if show_totals 
    = render 'orders/totals', order: order
