:ruby
  errors ||= []

.banner-section.size-large
  .row.v-align-row
    .medium-10.large-8.medium-centered.columns.v-align-cell
      .banner-cta-block.text-center.medium-gray-bg
        %h2 Yordar - Order
        - if errors.present?
          - errors.each do |error|
            %p= error
          %a.button.tiny{ href: current_user.present? ? customer_profile_path : prismic_root_url }
            Back to Homepage
        -else
          %p The order does not exist OR you don't have access to this order.
