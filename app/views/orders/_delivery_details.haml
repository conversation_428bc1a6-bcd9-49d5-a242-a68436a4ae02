:ruby
  status ||= order.status
  map_size ||= '300x120'
  for_modal ||= false
  is_customer = session_profile.is_a?(CustomerProfile)
  view_order_link = is_customer ? order_show_path(order) : supplier_order_show_path(order)
  show_totals = for_modal && (is_customer || order.status != 'pending')
  coordinates = Geocoder.search("#{order.delivery_address} #{order.delivery_suburb.postcode}").first.try(:coordinates)
  request_uuid = is_customer && order.delivery_type == 'loading_dock' ? Digest::MD5.hexdigest(order.id.to_s + session_profile.id.to_s + yordar_credentials(:random_salt)) : nil

.order-show__details{ class: for_modal ? 'full-height' : '' }
  %h3.delivery-details-title 
    Delivery Details
    - if for_modal
      %a.button{ href: view_order_link, target: '_blank' }
        View Full Order
  .order-show-details-section
    %h6.order-show-detail-title.order Order
    - if for_modal
      %p
        %strong Name:
        %span= order_display_name(order)
    %p
      %strong Status:
      - if !is_customer && status == 'pending' 
        %span.status-icon.with-tooltip{ class: status, title: 'The customer is still building the order'  }
          In Progress
          %span.icon-info-circle
      - else
        %span.status-icon{ class: status }
          = status.capitalize
    %p
      %strong Order:
      %span= "##{order.id}"
    %p 
      %strong Delivery: 
      #{order.delivery_at.strftime("%A #{order.delivery_at.day.ordinalize} %b %Y at %l:%M%P") if order.delivery_at.present?}

    - if is_admin? && %w[delivered cancelled paused].exclude?(order.status)
      %p 
        %strong
          Lead Time
          = succeed ':' do
            %small (admin only)
        = Orders::FetchLeadTime.new(order: order).call.lead_time&.to_s(:full_verbose)
        
    %p 
      %strong Type:
      - if order.is_recurrent?
        Recurring Order (#{order.recurrent_type})
      - else
        One-Off Order

    - if is_customer
      - if (pantry_manager = order.pantry_manager.presence)
        %p 
          %strong Pantry Manager:
          = pantry_manager.name
      = render 'orders/purchase_order_details', order: order
      = render 'orders/invoicing_details', order: order

  .order-show-details-section
    %h6.order-show-detail-title.delivery Delivery To
    %p
      = order.contact_name || order.customer_profile.name
    - order.delivery_address_arr.each do |line|
      %p= line
    - if coordinates.present?
      .order-map.order-map--small
        = image_tag "https://maps.googleapis.com/maps/api/staticmap?zoom=18&scale=2&size=#{map_size}&markers=#{coordinates[0]},#{coordinates[1]}&key=#{yordar_credentials(:google, :maps_api_key)}", alt: "Map"

  .order-show-details-section
    %h3.order-show-detail-title.instructions Instructions
    %p
      = order.delivery_instruction
      - if order.is_contactless_delivery?
        %br
        %strong This is a contactless delivery order.

      - if order.delivery_type == 'loading_dock'
        %br/
        - if order.loading_dock.present?        
          %a.button.tiny{ href: loading_dock_url(uuid: order.uuid) }
            View Loading Dock Code
          - if is_customer && order.status != 'delivered'
            %a{ href: loading_dock_request_url(uuid: order.uuid, request_uuid: request_uuid) }
              edit
        - elsif is_customer
          %a.button.tiny{ href: loading_dock_request_url(uuid: order.uuid, request_uuid: request_uuid) }
            Enter Loading Dock Code
        - else
          %small (Awaiting Loading Dock Code)

  - if is_customer && !for_modal
    .order-show-details-section
      %h6.order-show-detail-title.supplier Suppliers
      = render 'orders/order_suppliers', suppliers: order.supplier_profiles

  - if for_modal
    .mt-1
      = render 'orders/modal/order_lines', order: order

  - if show_totals
    .order-show-details-section
      = render 'orders/totals', order: order

  - if for_modal && is_customer && order.status == 'quoted'
    %a.button{ href: next_app_order_edit_url(order, finaliseQuote: true), style: 'width: 100%' }
      Finalise Quote
