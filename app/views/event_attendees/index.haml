:ruby
  teams = session_profile.event_teams.where(active: true)
  no_contacts = @event_attendees.blank?

- content_for :header_title, 'Contact List'
- content_for :webpack, :true

.no-team-contacts{ class: (no_contacts ? '' : 'hidden') }
  %h2 No Contacts
  = image_tag("illustrations/together.png", width: "350px")
  %p.no-team-contacts__text Food's better shared. 
  %p Currently you don't have any team contacts. Let's change that!
  %button.button.black-btn{ type: 'button', data: { open: 'modal-add-new-contact' } } Add contacts

.dashboard-container.contact-list{ class: ('hidden' if no_contacts), data: { view_customer_contact_list: true, view_customer_contact_filtering: true, view_customer_contact_team: true } }
  .team-order-header.row
    .small-12.medium-5.columns
      %h6.team-order-header__heading
        Contacts

    .small-12.medium-7.columns.no-gutter
      .small-12.medium-3.columns.no-gutter
        .dropdown.dropdown-filter-wrapper.dropdown-bubble.team-order-filter.team-order-filter--teams
          %button.dropdown-filter-button
            Teams
          .filter-content.dropdown-content.hidden
            %ul
              %li
                = form_for EventTeam.new, url: api_event_teams_path, class: 'add-new-team__form' do |f|
                  = f.text_field :name, placeholder: 'Add a team', class: 'add-new-team__input'
                  %button.add-new-team__submit{type: 'submit'}
              - teams.each do |team|
                = render 'event_teams/event_team', team: team, unique_class: 'add-edit-teams'
      .small-12.medium-6.columns.no-gutter-small
        %input.team-order-filter.team-order-filter--search{ placeholder: 'Search', type: 'search', name: 'filter-contacts' }/
      .small-12.medium-3.columns.no-gutter
        %button.button.small.team-order-header__add{ type: 'button', data: { open: 'modal-add-new-contact' } }
          Add Contacts

  .form-content
    .clearfix
    .team-order-list
      .row.show-for-medium.team-order-list__headings
        %span.team-order-list__heading.small-4.columns.no-gutter Name
        %span.team-order-list__heading.small-4.columns.no-gutter Email
        %span.team-order-list__heading.small-1.columns.no-gutter Team
        %span.team-order-list__heading.team-order-list__heading--highlight.small-3.columns.no-gutter
          Remove Contact

      %ul.team-contacts.row
        - @event_attendees.each_with_index do |event_attendee, eidx|
          = render 'event_attendees/event_attendee', event_attendee: event_attendee, row_index: eidx, context: 'contact_list'

= render 'event_attendees/new_modal'
