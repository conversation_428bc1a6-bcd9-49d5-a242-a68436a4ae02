.customer-area.has-large-gutter
  .auth-container
    .auth-card
      .auth-card__illustration
        - if @team_admin.blank?
          = image_tag 'illustrations/search.svg'
          %h4.auth-card__title That's weird?
        - else
          = image_tag 'illustrations/people.svg'
          %h4.auth-card__title Join the Team!
          %p Hey! Lucky you! #{@team_admin.name} wants to add you to their contact list. Looks like some tasty days await!
      .authorization-module
        - if @team_admin.blank?
          %h3.login-heading
            Hmmm
          %p
            We couldn't find a matching team admin in our system. Please contact your team admin to get further information.
        - else
          = form_for EventAttendee.new, method: 'POST', url: event_attendees_path(team_admin_id: @team_admin.uuid), html: { data: { abide: '' }, class: 'authorization-form', novalidate: '' } do |f|
            %h3.login-heading
              Enter Your Details
            .row
              .small-6.columns
                %label.input-label First Name
                = f.text_field :first_name, placeholder: '<PERSON>', class: 'validate input', required: true
                %span.form-error Please enter your first name
              .small-6.columns
                %label.input-label Last Name
                = f.text_field :last_name, placeholder: 'Doe', class: 'validate input', required: true
                %span.form-error Please enter your last name
            .row
              .small-12.columns
                %label.input-label Email
                = f.text_field :email, placeholder: '<EMAIL>', class: 'validate input', required: true, pattern: 'email'
                %span.form-error Please enter a valid email
            .row
              .small-12.columns
                %button.button.button-large.submit-new-attendee{ type: 'submit' } Add to #{@team_admin.name.split[0]}'s contacts

