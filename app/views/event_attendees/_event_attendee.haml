:ruby
  as_potential_team_order_attendee ||= false
  as_team_contact_attendee ||= false
  is_invited ||= false
  has_ordered ||= false
  is_selected ||= false
  row_index ||= nil
  compact ||= false
  attendee_teams = event_attendee.event_teams.where(active: true).order(:name)
  row_class = case
  when compact && has_ordered
    'medium-7'
  when compact
    'medium-11'
  else
    'medium-4'
  end

%li.small-12.columns.no-gutter.team-contact{ data: { attendee_id: event_attendee.id, list_order_id: row_index, team_ids: attendee_teams.map(&:id).to_json } }
  %ul.team-order-list__row.row
    %li.small-12.columns.no-gutter{ class: row_class }
      .row
        %span.small-6.columns.circle-icon{ style: "background: #{random_icon_colour}" }
          = customer_name_helper(:initials, event_attendee.name)
        %span.team-order-list__cell.small-6.columns.no-gutter
          = event_attendee.name

    - if has_ordered
      %li.small-12.columns.no-gutter.medium-4.team-order-list__cell
        %span.team-order-attendee-status.ordered
          Ordered

    - if !compact
      %li.team-order-list__cell.team-order-list__cell--greyed.small-12.medium-4.columns.no-gutter
        = event_attendee.email
      %li.team-order-list__cell.team-order-list__cell--greyed.small-12.medium-3.columns.no-gutter.team-order-list__cell--teams
        = attendee_teams.map(&:name).join(', ')

    %li.small-12.medium-1.columns.no-gutter
      - if as_potential_team_order_attendee
        %button.team-order-list__btn.circle-icon{ type: 'button', class: (is_invited ? 'uninvite-btn' : 'invite-btn'), disabled: event_attendee.is_team_admin? }
        = hidden_field_tag 'order[attendee_ids][]', is_invited ? event_attendee.id  : '', id: "order-attendee-id-#{event_attendee.id}"
      - elsif as_team_contact_attendee
        %a.team-order-list__btn.circle-icon{class: (is_selected ? 'contact-selected' : '') + ' edit-contact',  href: api_event_attendee_path(event_attendee), method: 'delete', data: { edit_team_path: api_event_attendee_team_update_path(event_attendee) }  }
          = is_selected ? '-' : '+'
      - else
        %a.team-order-list__btn.circle-icon.delete-contact{ href: api_event_attendee_path(event_attendee), method: 'delete', data: { edit_team_path: api_event_attendee_team_update_path(event_attendee) }  }
          X

