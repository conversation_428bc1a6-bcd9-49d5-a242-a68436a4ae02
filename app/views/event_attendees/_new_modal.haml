:ruby
  existing_team_options = session_profile.event_teams.order(:name).map{|team| [team.name, team.id] }
  team_order ||= nil
  context ||= ''
#modal-add-new-contact.reveal.modal.modal-drawer{ data: { reveal: '', close_on_click: 'false', view_customer_contact_creation: true } }
  .form-header
    .row.add-contact-banner
      %h6.small-11.columns.modal-title Add Contact
      %span.small-1.columns.close-modal-drawer{ data: { close: '' } } X
    .add-contact-tabs.row
      .small-4.columns.change-panel.active{ data: { panel: 'single-contact-panel' } }
        %span Single Contact
      .small-4.columns.change-panel{ data: { panel: 'bulk-upload-panel' } }
        %span Bulk Upload
      .small-4.columns.change-panel{ data: { panel: 'invite-link-panel' } }
        %span Invite Link
  .add-contact-panel.single-contact-panel
    .form-content.has-small-gutter
      = form_for EventAttendee.new, url: api_event_attendees_path, html: { class: 'new-team-contact-form', data: { abide: '', context: context } } do |f|
        .form-fieldset
          .row
            .small-12.medium-6.columns
              %label.team-order-label First Name
            .small-12.medium-6.columns
              %label.team-order-label Last Name
          .row
            .small-12.medium-6.columns
              = f.text_field :first_name, required: true, placeholder: 'Jane'
              %span.form-error
                First Name is required
            .small-12.medium-6.columns
              = f.text_field :last_name, required: true, placeholder: 'Doe'
              %span.form-error
                Last Name is required
          .row
            .small-12.columns
              %label.team-order-label Email address
            .small-12.columns
              = f.text_field :email, required: true, type: 'email', placeholder: '<EMAIL>'
              %span.form-error
                Email is required

          .row
            .small-12.columns
              %label.team-order-label Add to/create a team
            .small-12.columns
              = select_tag 'event_attendee[teams]', options_for_select(existing_team_options), { multiple: true, class: 'select choose-teams-select', style: 'width: 100%;' }

          .row.add-contact-footer
            .small-3.columns
              %a.button.columns.small.gray-btn{ data: { close: '' } }
                Cancel
            .small-9.columns
              %button.button.small.float-right.ml-1.add-contact{ data: { add_more: 'true', button_text: 'Save & Add More' } }
                Save & Add More
              %button.button.small.float-right.add-contact{ data: { button_text: 'Add Contact' } }
                Add Contact


  .add-contact-panel.bulk-upload-panel.hidden
    = form_tag api_event_attendees_create_from_csv_path, class: 'new-team-contact-csv-form', authenticity_token: true, multipart: true, data: { abide: '', context: context } do
      .drop-zone
        .drop-zone__prompt
          = image_tag('icons/box.svg')
          %span Drop a CSV or click here to upload
        = file_field_tag :contact_csv, style: 'padding-top:10px;', class: 'drop-zone__input',  reqiured: true
      .bulk-upload-info
        = link_to 'Download an example CSV', customer_contacts_path(format: 'csv'), class: 'bulk-upload-info__example'
        %p.bulk-upload-info__note
          Note: CSV must have 4 columns only. First name, Last name, Email and Teams.
          To add multiple teams for a contact, separate teams with a forward slash (ex. Tech/HR/Marketing)
      .row.add-contact-footer
        %a.small-4.button.columns.small.gray-btn{ data: { close: '' } }
          Cancel
        %button.upload-contacts-from-csv-btn.small-4.columns.button.small.float-right{ type: 'submit' }
          Upload Contacts
  .add-contact-panel.invite-link-panel.hidden
    - if team_order.present? && team_order.persisted?
      - invite_link = team_order.is_package_order? ? new_team_order_package_attendee_invite_url(package_id: team_order.package_id) : new_team_order_attendee_invite_url(event_id: team_order.unique_event_id)
      .invite-link-panel-for-contact-list
        %p.invite-link-info
          This is a magic link that you can send to your co-workers so they can add themselves into this
          = team_order.is_package_order? ? 'package.' : 'event.'

        %p Drop it in a group email, a slack channel or wherever you'd like and let everyone enter their own details to save you the work! You only need to approve these entries before the order closes.
        %a.button.small.team-order-header__add.attendee-invite-link{ href: invite_link, target: '_blank' }
          Copy Team Order
          - if team_order.is_package_order?
            Package
          Invite Link
        %p.invite-link-info--copied.hidden Link Copied!
    - elsif context == 'attendee_selection'
      .invite-link-panel-for-team-order
        %p.invite-link-info Once you've placed this order, you can send a magic link to your team where they can enter their details and order without being added as a contact.
        %p This link is accessible on the Team Order view page once the order has been placed, where you can also approve each person who adds themself in this manner.
        %p.invite-link-info--italic
          Alternatively, if you'd just like everyone to add themselves into your contact list, navigate to the team order
          = link_to "contact list", customer_contacts_path, class: "invite-link-info--link"
          page, where this same panel will provide a magic link.
        %p.invite-link-info--copied.hidden Link Copied!
    - else
      .invite-link-panel-for-contact-list
        %p.invite-link-info This is a magic link that you can send to your co-workers so they can add themselves into your contact list.
        %p Drop it in a group email, a slack channel or wherever you'd like and let everyone enter their own details to save you the work!
        %a.button.small.team-order-header__add.attendee-invite-link{ href: new_event_attendee_invite_path(team_admin_id: session_profile.uuid), target: '_blank' }
          Copy Invite Link
        %p.invite-link-info--copied.hidden Link Copied!
