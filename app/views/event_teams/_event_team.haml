%li.event-team{ data:  { team_id: team.id, url: api_event_team_path(team) } }
  - if unique_class == 'add-edit-teams'
    %label.drop-text{class: unique_class}<
      .section-toggle
        = check_box_tag team.name, team.id, false, class: 'checkbox-content team-filter', id: "event-team-#{team.id}", data: { label: team.name, team_id: team.id }
        %span.section-toggle__switch

    %span.team-name
      = team.name

    %a.edit-team-btn{ href: api_event_team_path(team) }
      = image_tag 'icons/pencil'
    %a.delete-team-btn{ href: api_event_team_path(team), method: 'delete' }
      = image_tag 'icons/bin-black'
  - else
    %label.drop-text{class: unique_class}<
      .section-toggle
        = check_box_tag team.name, team.id, false, class: 'checkbox-content team-filter', id: "event-team-#{team.id}", data: { label: team.name, team_id: team.id }
        %span.section-toggle__switch
      %span.team-name
        = team.name
