:ruby
  can_pay_invoice = @invoice.present? && !@invoice.paid? && @invoice.invoice_orders.where(payment_status: [nil, '', 'unpaid', 'error']).present?

- content_for :webpack, 'front_end'

.customer-area.has-large-gutter.medium-gray-bg{ data: { view_invoice_payment: true } }
  .small-10.small-centered.columns
    .auth-container
      .auth-card
        .auth-card__illustration
          = image_tag 'illustrations/bill.svg'
          %h4.auth-card__title Thanks!
          %p We hope you enjoyed your experience! Thanks for using Yordar.
        .authorization-module
          - if can_pay_invoice
            %h3.auth-card__title Pay Invoice

            %p
              Download Invoice -
              = link_to @invoice.latest_document&.url do
                = "Invoice ##{@invoice.number}"

            .row
              .small-12.columns.no-gutter
                .new-credit-card-container.credit-card-form-container{ style: 'margin: 1rem 0', data: { view_stripe_card_form: true } }
                  = render 'credit_cards/stripe_form', credit_card: CreditCard.new(saved_for_future: false), show_charge_notes: true, default_save_for_future: current_user.blank?, customer_email: @invoice.invoice_orders.first.customer_profile.user.email

              .show-for-large.large-4.columns.no-gutter.card-container

            .form-footer

              = form_for :payment, url: invoice_payment_path, html: { id: 'invoice-payment-form' } do |f|
                = f.hidden_field :invoice_uuid, value: @invoice.uuid
                = f.hidden_field :credit_card_id

              .row
                - unless notice.blank?
                  %fieldset.error-notice
                    %span= notice

                .small-12.medium-8.large-9.columns.no-gutter
                  %button#pay-invoice-btn.button.small{ type: 'submit', data: { credit_card_button: 'Pay Invoice' } } Pay Invoice
                
          - elsif @invoice.present? && @invoice.paid?
            %p
              It seems this invoice is already paid for.
              %br
              Download Invoice -
              = link_to @invoice.latest_document&.url do
                = "Invoice ##{@invoice.number}"

          - else
            %p Sorry, we could'nt find the invoice you were looking for.
