:ruby
  stripe_cards, eway_cards = @credit_cards.partition{|credit_card| credit_card.stripe_token.present? }

.table-scroll.credit-card.list
  %table.credit-cards-table.customer-area-table
    %thead
      %tr
        %td Name
        %td Card Number
        %td.exp Expiry
        %td Actions
    %tbody#credit-cards-listing
      - if eway_cards.present?
        %tr.new-cards
          %th{ colspan: 4 }
            %strong  New cards

        = render partial: 'credit_cards/credit_card', collection: stripe_cards, as: :credit_card

        %tr
          %th{ colspan: 4 }

        %tr
          %th.old-cards{ colspan: 4 }
            %strong
              Old cards -
              %span{ style: 'color: #c71e2a' }
                You won't be able to use these cards from #{Time.zone.parse(yordar_credentials(:stripe, :migration_date)).to_s(:date_verbose)}
        = render partial: 'credit_cards/credit_card', collection: eway_cards, as: :credit_card


      - elsif stripe_cards.present?
        = render partial: 'credit_cards/credit_card', collection: stripe_cards, as: :credit_card
