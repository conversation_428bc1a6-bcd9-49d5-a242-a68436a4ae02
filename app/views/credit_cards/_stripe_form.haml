
:ruby
  show_buttons ||= false
  show_charge_notes ||= false
  default_save_for_future ||= false
  has_order ||= false
  context ||= ''

  credit_card ||= CreditCard.new

  form_url = credit_card.new_record? ? api_stripe_credit_cards_path : api_stripe_credit_card_path(credit_card)
  form_method = credit_card.new_record? ? 'POST' : 'PUT'
  form_class = 'credit-card-form stripe-card-form'
  form_class += credit_card.new_record? ? ' new-credit-card' : ' edit-credit-card'

  customer_email ||= session_profile.present? ? session_profile.user.email : ''

= form_for credit_card, url: form_url, html: { class: form_class, data: { form_method: form_method, context: context }, novalidate: '' } do |c|
  .row
    .small-12.medium-12.columns.no-gutter
      %label.uppercase Card number
      .card-number.stripe-element.form-input{ id: "card-number-#{credit_card.id}"}

  .row
    .small-12.medium-6.columns.no-gutter
      %label.uppercase Name on card
      = c.text_field :name, required: true, class: 'form-input'
      = hidden_field_tag :customer_email, customer_email

    .small-12.medium-4.columns.no-gutter-small
      %label.uppercase Expiry Dates
      .card-expiry.stripe-element.form-input{ id: "card-expiry-#{credit_card.id}"}

    .small-6.medium-2.columns.no-gutter
      %label.uppercase CVV
      .card-cvc.stripe-element.form-input{ id: "card-cvc-#{credit_card.id}"}

  - if default_save_for_future && !credit_card.saved_for_future?
    = c.hidden_field :saved_for_future, value: false
  - else
    .row
      .small-12.medium-6.columns.no-gutter
        %label.uppercase
          = c.check_box :saved_for_future, disabled: default_save_for_future
          Save card for future use

  - if show_buttons
    .buttons
      %a.button.small.secondary.submit-card Save
      %a.button.small.gray-btn.cancel Cancel

  - if show_charge_notes
    .small-12.columns.no-gutter
      #new-credit-card-charge-note.small-12.columns.hidden.card-charge-note
        %span.card-type
        credit card payments will incur a
        %span.card-charge
        +
        %span.card-fee
        surcharge.
