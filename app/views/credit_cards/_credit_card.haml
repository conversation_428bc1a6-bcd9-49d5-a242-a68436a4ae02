:ruby
  failed_card = credit_card.has_failed_orders?
  tooltip = failed_card ? 'We were unable to charge this credit card for an order. Please update your details and allow 24 hours for reprocessing.' : ''
  is_expired_nominated_card = credit_card.expired? && credit_card.auto_pay_invoice?
  can_nominate = is_admin? && (is_expired_nominated_card || !credit_card.expired?)

%tr.credit-card-item.group{ class: (failed_card ? 'failed_credit_card' : ''), id: "credit-card-item-#{credit_card.id}", title: tooltip, data: { credit_card_id: credit_card.id, url: api_credit_card_path(credit_card), nominated: credit_card.auto_pay_invoice.to_json, view_tooltip_el: failed_card } }
  %div.display-cc.hide
    %td.col.name
      - if failed_card
        %span &#9888
      = credit_card.name
    %td.col.wider= credit_card.label
    %td.col.exp= credit_card.expiry_label
    %td
      %a.edit-credit-card{ href: 'javascript:void(0)' } Update
      - if !failed_card
        |
        %a.hide-credit-card{ href: 'javascript:void(0)' } Hide
        - if credit_card.has_pending_orders?
          %small{ title: 'There are unpaid orders on this credit card' } (Cannot Delete)
        - else
          |
          %a.delete-credit-card{ href: 'javascript:void(0)' } Delete
      - if can_nominate
        - nominate_text = credit_card.auto_pay_invoice? ? 'Card is NOMINATED to automatically pay bulk order invoices. Click to un-nominate.' : 'Nominate card to automatically pay bulk order invoices.'
        |
        %a.nominate-credit-card{ href: 'javascript:void(0)', title: nominate_text, data: { view_tooltip_el: true } }
          = credit_card.auto_pay_invoice? ? 'NOMINATED' : 'Nominate'

%tr.edit-credit-card-form-container.credit-card-form-container.hidden{ data: { credit_card_id: credit_card.id, view_stripe_card_form: true } }
  %td{ colspan: 4 }
    .edit-cc
      = render 'credit_cards/stripe_form', credit_card: credit_card, show_buttons: true, default_save_for_future: true, context: 'payment-options'
