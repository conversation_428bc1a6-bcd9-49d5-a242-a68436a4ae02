- menu_sections = @supplier_menu.section_grouped_menu_items.to_h.keys
- if @suburb.present?
  - content_for :title, "#{@supplier.company_name} in #{@suburb.name}#{category_for_title(@supplier, menu_sections)}"
- else
  - content_for :title, "#{@supplier.company_name}#{category_for_title(@supplier, menu_sections)}"

- content_for :description, @supplier.description

- content_for :no_index, @supplier.woolworths?

- content_for :keywords, "#{@supplier.company_name},#{@supplier.company_address},corporate catering,office catering,catering companies,catering services,office catering services,office fruit delivery,corporate fruit delivery,office fruit,fruit box delivery,office fruit box delivery,corporate fruit delivery,office milk delivery,office milk supplier,corporate milk delivery,corporate milk supplier,milk delivery for the office,Office alcohol delivery,office alcohol supplier,corporate alcohol supplier,Sydney,Melbourne,Brisbane,Perth"
- content_for :canonical_url, next_app_supplier_show_url(@supplier&.slug)

:ruby
  order = @supplier_order.order
  heading_block_class = case
                        when @supplier.woolworths?
                          'woolworths-heading-block'
                        when @supplier.liquor_license_no.present?
                          'liquor-license-warning'
                        end

.heading-block{ class: heading_block_class }
.supplier-show-container
  .supplier-show-wrapper
    .supplier-details-container

      = render 'suppliers/header'

      = render 'suppliers/menu', menu_sections: menu_sections

    .docket-container.columns
      .docket
        - if order.present?
          - if order.is_team_order?
            = render 'orders/docket/team_order_docket', order: order, team_order_attendee: @supplier_order.team_order_attendee
          - else
            = render 'orders/docket/docket', order: order, recurrent_orders: @supplier_order.recurrent_orders
.docket-open
  %button View Cart

- if order.present? && order.is_team_order? && (@supplier_order.error.present? || @supplier_order.warnings.present?)
  = render 'suppliers/team_order_errors_modal', error: @supplier_order.error.presence || @supplier_order.warnings.first, team_order_attendee: @supplier_order.team_order_attendee

- content_for :javascript_includes do
  = javascript_include_tag 'pages/supplier-show'
