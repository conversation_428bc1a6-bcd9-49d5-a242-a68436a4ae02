:ruby
  supplier_minimum = Suppliers::GetMinimums.new(suppliers: [@supplier], category_group: params[:category_group]).call[@supplier]
  supplier_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: @supplier, suburb: @suburb).call
  if supplier_zone.present?
    delivery_fee = supplier_zone.delivery_fee == 0 ? 'Free Delivery' : "#{number_to_currency(supplier_zone.delivery_fee, precision: 0)} Delivery Fee"
    operating_days = operating_days_data(supplier: @supplier, delivery_zone: supplier_zone)
    operating_hours = operating_hours(supplier: @supplier, delivery_zone: supplier_zone)
  end
  operating_days ||= nil
  operating_hours ||= nil
  delivery_fee ||= nil
  min_order = supplier_minimum.present? ?  'Minimum order ' + format_price(supplier_minimum.minimum_spend) : 'No Minimum Order'


.supplier-banner
  .supplier-banner-image
    - if @supplier.profile.avatar.present?
      = cl_image_tag(@supplier.profile.avatar, alt: @supplier.company_name)
    - else
      = image_tag 'supplier-menu-image-placeholder.jpg', alt: @supplier.company_name

  .supplier-banner-details
    %h1
      = @supplier.company_name

    - if operating_days.present? || operating_hours.present?
      %p.supplier-banner-details-info
        = [operating_days, operating_hours].reject(&:blank?).join(' | ')
    %p.supplier-banner-details-info= "#{delivery_fee} | #{min_order}" if delivery_fee
    %p.supplier-banner-details-info= "Lead Time: #{supplier_minimum.lead_time}" if supplier_minimum.present?
    %p.supplier-banner-details-info= "#{@supplier.rating if @supplier.rating > 0} | #{normalised_rating_count(@supplier.rating_count)} #{'Rating'.pluralize(@supplier.rating_count)}"
        

    .icon-display
      - available_features_for(supplier: @supplier).each do |feature|
        = image_tag("icons/#{feature[:icon]}")

- if @supplier.liquor_license_no.present?
  %p.department-description.warning-description
    =raw liquor_license_info(@supplier).join('<br>')
