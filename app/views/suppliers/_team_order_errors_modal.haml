#team-order-error-modal.reveal{ role: 'dialog', data: { close_on_click: 'false', close_on_esc: 'false', reveal: '' }, aria: { hidden: 'true' } }

  %h2 Yordar - Team Order #{error}...

  %h3= I18n.t("team_order.errors.#{error}")

  - if team_order_attendee.present? && team_order_attendee.status == 'pending'
    %p We can see you have added items to your cart. Please confirm your items to be included in the final order.

  %div
    - if [:cutoff_almost_exceeded, :pending_order_confirmation].include?(error)
      %a.button.small{ data: { close: true }, aria: { label: 'Close modal' } }
        Place order
    - elsif error == :cutoff_exceeded
      %a.button.small{ href: prismic_root_url }
        OK
      %a.button.small.gray-btn{ data: { close: true }, aria: { label: 'Close modal' } }
        View Menu
      %a#open-intercom.button.small.float-right{ href: 'javascript:void(0);', data: { close: true }, aria: { label: 'Close modal' } }
        Contact Yordar
    - else
      %a.button.small{ href: prismic_root_url }
        OK
      %a.button.small.gray-btn{ data: { close: true }, aria: { label: 'Close modal' } }
        View Menu

- content_for :javascript_includes do
  :javascript
    $(document).ready(function () {
      $("#team-order-error-modal").foundation("open");
    })
