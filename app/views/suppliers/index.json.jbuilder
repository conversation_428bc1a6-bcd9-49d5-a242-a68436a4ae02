can_favourite = session_profile.present? && session_profile.profile.is_customer?

json.array! @suppliers.each do |supplier|
  has_custom_menu = @custom_menu_supplier_ids.include?(supplier.id)
  has_custom_pricing = @rate_card_supplier_ids.include?(supplier.id) || @markup_override_supplier_ids.include?(supplier.id)
  is_favourite = @favourite_supplier_ids.include?(supplier.id)

  json.partial! 'suppliers/supplier', supplier: supplier, supplier_minimums: @supplier_minimums[supplier], has_custom_menu: has_custom_menu, has_custom_pricing: has_custom_pricing, can_favourite: can_favourite, is_favourite: is_favourite
end
