:ruby
  for_team_order_listing ||= false;
.dropdown.dropdown-filter-wrapper.dropdown-bubble{ class: for_team_order_listing ? "team-order-filter team-order-filter--#{unique_class}" : (hidden ? 'hidden' : ''), data: { category_group: category_group, kind: kind } }
  %button.dropdown-filter-button{class: for_team_order_listing ? '' : 'dropbtn'}
    = header
  .filter-content.dropdown-content.hidden
    %ul
      - sections.each do |section_name, options|
        %li.drop-text-header= section_name
        - options.each do |option|
          %li
            %label.drop-text<
              = check_box_tag option.name, option.value, false, class: 'checkbox-content', id: "#{section_name.downcase}-#{option.value}", disabled: option[:is_fixed], data: { label: option.label }
              %span.checkbox-content-tick
              = option.label
              - if option.is_new
                %span.new-filter-tag NEW
