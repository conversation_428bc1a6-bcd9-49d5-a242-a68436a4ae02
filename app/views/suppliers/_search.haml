:ruby
  disable_search = cookies[:yordar_suburb_id].present? && session_order.present? && session_order.order_lines.present?
  with_button = current_page?(root_path) || current_page?(page_path(:home)) || modal
  selected_suburb = cookies[:yordar_street_address].present? ? cookies[:yordar_street_address] + ', ' + cookies[:yordar_suburb_label] : cookies[:yordar_suburb_label]


.suppliers-cta-form
  = text_field_tag :location_search, selected_suburb, { placeholder: 'Enter street address, suburb or city', class: 'text-input', disabled: disable_search }

  - if with_button
    = submit_tag 'Find Suppliers', class: (modal ? 'button small search-submit' : 'button large search-submit')
    #postcode-search-spinner{ class: (modal ? 'button modal-spinner' : 'button large'), style: 'display:none;' }
      .sk-three-bounce
        .sk-child.sk-bounce1
        .sk-child.sk-bounce2
        .sk-child.sk-bounce3
  - else
    #postcode-search-spinner.sk-three-bounce.sk-three-bounce-top-bar{ style: 'display:none;', data: { adjust_on_screen_size: true } }
      - spinner_style = 'width:12px;height:12px;background-color:#241C15;'
      .sk-child.sk-bounce1{ style: spinner_style }
      .sk-child.sk-bounce2{ style: spinner_style }
      .sk-child.sk-bounce3{ style: spinner_style }
