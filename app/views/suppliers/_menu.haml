- menu_sections ||= @supplier_menu.section_grouped_menu_items.to_h.keys
- if @supplier.is_major_supplier? || (!Rails.env.production? && params[:as_major].present?)
  = render 'menu_sections/grouped_navigation', grouped_menu_sections: menu_sections.group_by(&:group_name)
  = image_tag 'loader.gif', class: 'menu-section-items-loading-spinner hidden', alt: 'Loading...'
  .menu-section-wrapper.clearfix{ data: { major_supplier: true } }
    -# just a wrapper

- else
  = render 'menu_sections/navigation', menu_sections: menu_sections, selected_menu_section: menu_sections.first
  .menu-section-wrapper.clearfix
    = render 'menu_sections/list', grouped_menu_items: @supplier_menu.section_grouped_menu_items, can_favourite: session_profile && session_profile.profile.is_customer? && @is_team_order.blank?, favourite_menu_items: (@supplier_menu.favourite_menu_items || [])
