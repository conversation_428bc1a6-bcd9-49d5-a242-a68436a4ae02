:ruby
  menu_item = order_line.menu_item
  letter_icons = item_letter_icons(menu_item: menu_item)
  has_image = menu_item.image?
  grouped_extras = order_line.selected_menu_extras.present? ? OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call : []
  has_multiple_quantities = order_line.quantity > 1
  gst_country = order_line.order.symbolized_country_code || :au
  price = order_line.price_inc_gst(gst_country: gst_country) * order_line.quantity

.team-menu-item
  %div
    - if has_image
      = cl_image_tag(menu_item.ci_image, width: 400, height: 400, crop: 'fill', quality: "auto,fl_lossy,f_auto", alt: menu_item.name, class: "circle-icon")
    - else
      .circle-icon
        = order_line.name.first
  %div.team-menu-item__details
    %p.team-menu-item__name
      #{has_multiple_quantities ? "#{order_line.quantity}x " : ''} #{order_line.name.truncate(40)}
      - if letter_icons.present?
        %span
          =raw letter_icons

    %p.team-menu-item__description
      = menu_item.description.strip.truncate(100) if menu_item.description.present?

    %p.team-menu-item__description
      =raw grouped_extras.map{ |section, extras| "#{section.name}: #{extras.map(&:name).join(',')}" }.join('<br/>')

    %p.team-menu-item__price
      - if show_pricing
        = number_to_currency(price, precision: 2)
