:ruby
  team_order_supplier ||= team_order.order_suppliers.first
  team_supplier = team_order_supplier.present? && team_order_supplier.supplier_profile
  attendee_order_total = Orders::CalculateCustomerTotals.new(order: team_order, attendee: team_order_attendee).call.total

  show_pricing = (session_profile.present? && session_profile == team_order.customer_profile) || !team_order.hide_budget

.slider-container
  %div
    .team-supplier-banner
      .small-12.team-supplier-banner__image
        = cl_image_tag(team_supplier.profile.avatar, crop: 'fill', quality: "auto,fl_lossy,f_auto", alt: team_supplier.company_name)
      .team-supplier-banner__details.row
        %p.team-supplier-banner__title{style: 'margin: 0'}
          Your Meal For #{relative_weekday(team_order.delivery_at.to_date)}
          %span{ data: { close: '' } } X
        %p.team-supplier-banner__supplier-tag= team_supplier.name

    - if team_order_attendee.level.present?
      .attendee-items-info
        %span.attendee-items-info__item-no
          Delivery Level:
          = team_order_attendee.level.name
          

    .attendee-items-info
      %span.attendee-items-info__item-no #{order_lines.size} Items Selected
      - if show_pricing
        %span Total: #{number_to_currency(attendee_order_total)} || Budget: #{number_to_currency(team_order.team_order_budget)}

    .team-menu-section__items.team-menu-section__items--attendee
      .team-menu-items
      - order_lines.each do |order_line|
        = render "team_order_attendees/attendee_order_line", order_line: order_line, show_pricing: show_pricing

  - if %w[cancelled delivered].exclude?(team_order.status)
    %div
      %a.button.small.black-btn.slider-footer{ href: next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code) }
        Edit Order
