- content_for :webpack, 'front_end'
.customer-area.has-large-gutter
  .auth-container
    .auth-card
      .auth-card__illustration
        = image_tag 'illustrations/login-man.svg'
        %h4.auth-card__title Welcome Back
        %p Great to see you again! There's lots of new and delicious things happening on Yordar. Jump in!
      .authorization-module
        %div
          = form_for resource, as: resource_name, url: session_path(resource_name), html: { class: 'authorization-form' } do |f|
            .form-header{ style: 'min-height:3rem;' }
              %h3.login-heading Login
            %div
              %label.input-label Email Address
              = f.email_field :email, placeholder: '<EMAIL>', class: 'validate input', required: true
            %div
              %label.input-label Password
              = f.password_field :password, placeholder: 'Enter your Password', class: 'validate password input', required: true
            %button#continue-button.button.button-large{type: 'submit', value: 'Continue'} Login
            %div{style: 'display: flex; justify-content: space-between;'}
              %div
                %p.forgot-password
                  = link_to 'Forgot password?', new_password_path(resource_name)
              %div
                %p.forgot-password
                  = link_to 'Don\'t have an account?', new_user_registration_path
