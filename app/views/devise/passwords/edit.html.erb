
<!-- 
	when user has reset the password using 'Forgot Password', they land here to reset password 
-->

<div class='has-gutter medium-gray-bg'>
	<div class='row'>
		<div class='medium-5 large-4 small-centered coulmns'>
			<div class='authorization-module'>
				<div class='login-register-form group'>
					<div class='login-form reset-password-form group'>
						<div class='text-center'>
							<h4>CHANGE PASSWORD</h4>
							<p>Please change your password below.</p>
							<%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f|  %>
								
								<%= f.hidden_field :reset_password_token %>
								
								<div class='field-container'>
									<%= f.password_field :password, id: 'new_user_password', placeholder: 'Enter a new password', class: 'validate password text-center', required: true, autocomplete: 'off' %>
								</div>
								
								<div class='field-container'>
									<%= f.password_field :password_confirmation, id: 'new_user_password_confirmation', placeholder: 'Confirm your password', class: 'validate password text-center', required: true, autocomplete: 'off' %>
								</div>
								
								<button type='submit' value='Change' id='continue-button' class='button' name='commit'>Change</button>
							<% end %>

							<div class='register-form group'>
								<h4>Yordar</h4>
								<p>Welcome back, let's set a new password!</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div> <!-- End Login-Register Form -->
<% content_for :javascript_includes do %>
	<%= javascript_include_tag 'login_register' %>
<% end %>
