<div class='customer-area has-large-gutter'>
		<div class='auth-container'>
			<div class='auth-card'>
				<div class='auth-card__illustration'>
					<%= image_tag 'forgot-password.svg'%>
					<h4 class='auth-card-title'>Reset. Start Fresh</h4>
					<p>Life's busy. Passwords are easy to forget. We understand. Enter your email and let's get you a new password!</p>
				</div>
				<div class='authorization-module'>
					<div class='login-register-form group'>
						
						<div class='login-form reset-password-form group'>
							
							<div>
								<%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post, class: 'authorization-form' }) do |f|  %>
								
								<div class='margin-top-2'>
									<h3 class='form-title'>Forgot your password?</h3>
								</div>
								
								<%= f.hidden_field :template, value: 'devise/passwords/new' %>
								
								<div class='field-container'>
									<label>Email Address</label>
									<%= f.email_field :email, placeholder: 'Enter your email here', class: 'validate input', required: true, autofocus: true %>
									
									<% if resource.present? and resource.errors[:email].present? %>
										<% if resource.errors[:email] == ['not found'] %>
											<label generated='true' class='error'>Sorry, couldn't find you in system.</label>
										<% end %>
									<% end %>
								</div>
								<button class='button button-large' type='submit' value='Reset' id='reset-button' name='commit'>Reset</button>
								<div>
									<p class='text-center'>
										<%= link_to 'Don\'t have an account?', new_user_registration_path %>
									</p>
								</div>
								<% end %>
							</div>
						</div>
					</div>
					<% content_for :javascript_includes do %>
						<%= javascript_include_tag 'login_register' %>
					<% end %>
				</div>
			</div>
		</div>
	</div>
