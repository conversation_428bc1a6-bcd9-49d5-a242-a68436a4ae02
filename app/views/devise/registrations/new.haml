:ruby
  is_company_registration = @company_team_admin.present?
  is_staff_registration = @is_staff_registration.present?
  new_registration_errors = @email.present? && @invalid_email_errors
  existing_registration_error = new_registration_errors.present? && new_registration_errors.include?('A user with that email already exists!')
  roles = CustomerProfile::CUSTOMER_ROLES
  roles = CustomerProfile::STAFF_ROLES if is_staff_registration
  is_home_delivery = session_order.present? && session_order.is_home_delivery?
  autocomplete_config = {
    path: api_suburbs_path,
    target_name: 'user[suburb_id]',
    countryCode: request_country_code
  }
  company_name = case
  when is_company_registration
    @company_team_admin.company&.name || @company_team_admin.company_name
  when is_staff_registration
    'Yordar'
  else
    nil
  end

- content_for :webpack, 'front_end'

.customer-area.has-large-gutter{ data: { view_customer_registration: true } }
  .auth-container
    .auth-card
      .auth-card__illustration
        - if is_company_registration
          = image_tag "illustrations/together.png"
        - else
          = image_tag "illustrations/search.svg"
        %p.auth-card__step Step 1/2

        %h4.auth-card__title
          - if is_company_registration
            Join #{company_name}
          - else
            Let's start with the basics
        %p
          - if is_company_registration
            You've been invited to join #{company_name}. After completing this form you will be associated under your team admin's account
          - else
            Find delicious food. Nourish your Team. Enrich your culture. You can do it all with the Yordar platform
      .authorization-module
        = form_for resource, as: resource_name, url: new_user_registration_path, html: { class: 'authorization-form', data: { abide: ''}, novalidate: '' } do |f|
          - if @company_team_admin.present?
            = hidden_field_tag :company_team_admin_code, @company_team_admin.uuid
          - if @adminable_customer.present?
            = hidden_field_tag :adminable_customer_code, @adminable_customer.uuid
          #register-step-1
            %h3.form-title
              - if is_company_registration
                Join Yordar - under
                %strong= company_name
              - elsif is_staff_registration
                Join Yordar (as staff)
              - else
                Join Yordar
            %div{ style: 'position:relative;' }
              .row
                .large-6.columns
                  %label.input-label First name
                  =f.text_field :firstname, placeholder: 'Joe', class: 'validate input with-label', required: true
                  %span.form-error Please enter your first name
                .large-6.columns
                  %label.input-label Last name
                  =f.text_field :lastname, placeholder: 'Bloggs', class: 'validate input with-label', required: true
                  %span.form-error Please enter your last name
              .row
                .large-12.columns
                  %label.input-label Email Address
                  =f.email_field :email, placeholder: 'Enter Your Email Address', class: 'validate input with-label', required: true, pattern: "email"
                  %span.form-error Please enter a valid email address
              %label.step-1-errors.error{ class: new_registration_errors.present? ? '' : 'hidden' }
                = @invalid_email_errors.join('. ') if new_registration_errors.present?

              %label.password-reset{ class: (existing_registration_error ? '' : 'hidden') }
                %a{ href: new_password_path(resource_name) }
                  reset password?

            %span#continue-with-email.button.button-large{ data: { url: api_leads_path } }
              = is_home_delivery ? 'Continue' : 'Complete your profile'

            - if !is_company_registration && !is_staff_registration
              %p.forgot-password.text-center
                = link_to 'Sign Up As Supplier', new_supplier_registration_path
            %p.forgot-password.text-center
              = link_to 'Already have an account?', new_user_session_path

          #register-step-2.hidden
            %h3.form-title
              - if is_company_registration
                Join Yordar - under
                %strong= company_name
              - elsif is_staff_registration
                Join Yordar (as staff)
              - else
                Join Yordar
            -if !is_home_delivery
              .row
                .large-6.columns
                  %label.input-label
                    Company
                    %sup *
                  = text_field_tag :company_name, company_name, placeholder: 'Awesome Inc.', class: 'required validate input with-label', readonly: is_company_registration && company_name.present?
                  %span.form-error Please enter your company name
                .large-6.columns
                  %label.input-label
                    Your Role
                    %sup *
                  = select_tag :role, options_for_select(roles), include_blank: 'Choose a role that applies to you', class: 'required'
                  %span.form-error Please enter your role
                
            .row
              - if !is_home_delivery
                .large-6.columns
                  %label.input-label
                    Suburb
                    %sup *
                  = text_field_tag 'postcode', '', placeholder: 'eg. Darlinghurst', class: 'required validate input with-label', autocomplete: 'off', data: { view_autocomplete_input: autocomplete_config.merge({ target_name: 'user[suburb_id]' }).to_json }
                  = f.hidden_field :suburb_id, { class: 'user-suburb-id' }
                  %span.form-error Please choose a suburb

              .large-6.columns
                %label.input-label Contact number
                = telephone_field_tag 'contact_phone', '', placeholder: 'eg. +61 123456789', class: 'input with-label', autocomplete: 'off'

            .row
              .large-6.columns
                %label.input-label
                  Password
                  %sup *
                = f.password_field :password, id: 'new_user_password', placeholder: 'Password', class: 'required validate password input with-label'
                %span.form-error Please enter a password
              
              .large-6.columns
                %label.input-label
                  Password Confirmation
                  %sup *
                = f.password_field :password_confirmation, id: 'new_user_password_confirmation', placeholder: 'Re-Type Password', class: 'required validate password input with-label'
                %span.form-error.confirmation-error Passwords should match

              %label.step-2-errors.error{ class: 'hidden' }

            .row
              .large-12.columns
                = recaptcha_tags callback: "recaptchaSuccess"

            %button#sign-up-button.button.button-large{type: 'submit', disabled: 'disabled'} Join Yordar
