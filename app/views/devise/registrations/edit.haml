:ruby
  # https://github.com/plataformatec/devise/wiki/How-To:-Allow-users-to-edit-their-password

%fieldset.dashboard-container
  .row
    .medium-8.large-6.small-centered.columns.pt-2
      .form-header.margin-bottom-2
        %h3.form-title.text-center Change password

      = form_for @user, as: :user, url: { action: 'change_password' }, method: 'PUT' do |f|
        .field-container
          %label Current Password
          = f.password_field :current_password, class: 'validate form-input', required: true, autocomplete: 'off'
          - if @user.errors[:current_password].present?
            %span.form-error.show{generated: 'true'}
              Current password #{@user.errors[:current_password].first.capitalize}
        .field-container
          %label New Password
          = f.password_field :password, placeholder: 'must be at-least 6 characters long', class: 'validate form-input password', required: true, autocomplete: 'off'
          - if @user.errors[:password].present?
            %span.form-error.show{ generated: 'true' }
              #{@user.errors[:password].first.capitalize}
        .field-container
          %label Confirm New Password
          = f.password_field :password_confirmation, placeholder: 'must be same as the new password', class: 'validate form-input password', required: true, autocomplete: 'off'
          - if @user.errors[:password_confirmation].present?
            %span.form-error.show{ generated: 'true' }
              #{@user.errors[:password_confirmation].first.capitalize}
        %button.button{ name: 'commit', type: 'submit' }
          Change
