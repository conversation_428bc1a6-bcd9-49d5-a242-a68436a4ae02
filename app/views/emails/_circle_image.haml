:ruby
  image ||= ''
  name ||= 'Supplier'
  dimensions ||= 36
  
- if image.present?
  %img{ src: image, style: "height: #{dimensions}px; width: #{dimensions}px; border-radius: 50%; margin-right: 10px;" }
- else
  %div{ style: "height: #{dimensions}px; width: #{dimensions}px; text-align: center; line-height: #{dimensions}px; color: #FFFFFF; border-radius: 50%; margin-right: 10px; background-color: #241c15;" }
    = name.scan(/[a-zA-Z]/).first
