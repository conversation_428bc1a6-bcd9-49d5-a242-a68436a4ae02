%p
  Hi Admin,

%p
  The on-hold charge for Order
  %strong ##{order.id}
  (#{order.total}) has
  = succeed '.' do
    %strong
      FAILED

- if error.is_fraudulent
  %p
    We've detected
    = succeed '.' do
      %strong{ style: "color: #{Email::ERROR_COLOR};" } fraudulent activity on the card
    %br
    %strong Last 4 digits of card:
    #{order.card_in_use}
- else
  %p
    %strong Error message:
    #{error.message}
    %br
    %strong Code:
    #{error.code}
    %br
    %strong Decline code:
    %span{ style: "color: #{Email::ERROR_COLOR};" }
      #{error.decline_code}
    %br
    %strong Last 4 digits of card:
    #{order.card_in_use}
    
- if order.status == 'cancelled'
  %p
    Because the order met a specific criteria, we've
    = succeed '.' do
      %strong cancelled the order
    %br
    #{'Supplier'.pluralize(order.supplier_names.size)}
    %em
      #{order.supplier_names.join(', ')}
    have been notified about the cancellation.
- else
  %p
    %strong N.B.:
    The order is still active (status: 
    = succeed ')' do
      %em
        #{order.status}
    and set to be delivered @ #{order.delivery_datetime}.

%p
  %strong Customer:
  #{customer.name}
  %br
  %strong Email:
  #{customer.email}
  - if customer.phones.present?
    %br
    %strong Phone:
    #{customer.phones.join(', ')}

- if customer.pending_orders_count > 0
  %p
    The customer has
    %strong #{customer.pending_orders_count} pending orders
    , which also need to be looked at.

%p{ style: 'text-align: center' }
  = render 'emails/button', text: 'View Order', link: order.link, kind: :bordered, is_last: true

%p{ style: 'text-align: center' }
  %em{ style: 'font-size: 12px;' }
    Need to be logged in as `#{customer.name}` to view order.