%p
  Dear Admin,

%p
  This is to notify you that order
  %strong
    = order.name
    %small
      = "(##{order.id})"
  was rejected by the customer.

%p
  Please contact
  %strong
    = customer.name
  - if customer.company.present?
    from
    %strong
      = customer.company
  @
  %br/
  Email:
  %a{ href: "mailto:#{customer.email}?subject=#{@email_subject}", style: email_link_style }
    = customer.email
  - if customer.phone.present?
    %br/
    Phone: 
    %a{ href: "tel:#{customer.phone}", style: email_link_style }
      = customer.phone


%p{ style: 'text-align: center' }
  - if order.quote_pdf_url
    = render 'emails/button', text: 'View PDF', link: order.quote_pdf_url, kind: :bordered, displacement: 40
  = render 'emails/button', text: 'View Order', link: order.link, kind: :bordered, is_last: true

%p
  %em{ style: 'font-size: 12px;' }
    = succeed '!' do
      N.B. Need to be logged in as 
      %strong<
        = customer.name
    to view order.
