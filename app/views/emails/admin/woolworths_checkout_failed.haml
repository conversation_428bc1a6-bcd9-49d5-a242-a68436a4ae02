%p Hi Admin,

%p
  A Woolworths order ##{order.id} for #{order.customer_name}, has
  %strong FAILED CHECKOUT ON WOOLWORTHS.

%p
  Please check the account #{order.account_name} to see if the order is still present and can be checked out.

%p
  %strong Order Details
%table{ width: '100%', style: 'padding-bottom: 10px;' }
  %tr
    %td{ style: 'border-bottom: 1px solid #ededed; width: 50%;' }
      Name
    %td{ style: 'border-bottom: 1px solid #ededed' }
      = order.name
  %tr
    %td{ style: 'border-bottom: 1px solid #ededed; width: 50%;' }
      Delivery Address
    %td{ style: 'border-bottom: 1px solid #ededed' }
      = order.address

  %tr
    %td{ style: 'border-bottom: 1px solid #ededed; width: 50%;' }
      Delivery Datetime
    %td{ style: 'border-bottom: 1px solid #ededed' }
      = order.delivery_at

  %tr
    %td{ style: 'border-bottom: 1px solid #ededed; width: 50%;' }
      Selected Delivery Window
    %td{ style: 'border-bottom: 1px solid #ededed' }
      = order.delivery_window_text

  %tr
    %td{ style: 'border-bottom: 1px solid #ededed; width: 50%;' }
      Totals
    %td{ style: 'border-bottom: 1px solid #ededed' }
      #{order.total} with
      %strong #{order.order_line_count} items


%p{ style: 'margin-top: 20px; margin-bottom: 0; text-align: center;' }
  = render 'emails/button', text: 'View Order', link: order.link, kind: :primary, is_last: true

%p{ style: 'text-align: center' }
  %em{ style: 'font-size: 12px;' }
    Need to be logged in as `#{order.customer_name}` to view order.