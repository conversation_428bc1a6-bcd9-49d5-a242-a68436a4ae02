%p
  Hi admin,
%p
  A customer named
  %strong
    = customer.name
  with email
  %strong
    = customer.email
  from
  %em
    = "`#{customer.company_name}`"
  would like to become a
  = succeed '.' do
    %strong<
      Company Team Admin

- if message.present?
  %p
    = succeed '.' do
      %strong= message.sub(/\.$/,'')
%p
  = render 'emails/button', text: 'View Customer In Admin', link: customer.admin_url, kind: :secondary, is_last: true
