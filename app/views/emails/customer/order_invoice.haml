:ruby
  accounts_email = yordar_credentials(:yordar, :accounts_email)
  
%p
  Hi #{first_name},
%p
  Attached is your invoice from Yordar
  = succeed '.' do
    %a{ href: invoice.pdf_url, target: '_blank', style: email_link_style }<
      for your recent orders

- if invoice.with_invoice_spreadsheet
  %p For bulk order invoices, you will find attached some supplementary invoice reports.

- if invoice.paid_by_credit_card
  %p
    This invoice will be automatically charged within the next 24 hours. If you have any questions about making payment, payment terms or the attached invoices, you can reach out to our team at
    %a{ href: "mailto:#{accounts_email}?subject=#{@email_subject}", style: email_link_style }
      = accounts_email      
    for more information.
- else
  %p
    Please ensure your payment is finalised within your designated account terms. If you have any questions about making payment, payment terms or the attached invoices, you can reach out to our team at
    %a{ href: "mailto:#{accounts_email}?subject=#{@email_subject}", style: email_link_style }
      = accounts_email
    for more information.

  - if accounting_software.present?
    %p Our team will upload the invoice to #{accounting_software.upcase} shortly.

%p Invoices that remain outstanding beyond designated payment terms may attract a late payment fee.

%p
  %strong EFT:
  If you are paying via EFT, please use your company name or invoice number as reference.

%p
  or
  = render 'emails/button', text: 'Pay By Credit Card', link: invoice.payment_url, kind: :primary, is_last: true

%p Thank you for ordering with Yordar, we appreciate your business!
