%p
  Hi #{firstname},

- if quote_message.present?
  %p
    = succeed '.' do
      %strong= quote_message.sub(/\.$/,'')

%p
  Please find attached the quote for your #{order.type} order named
  %strong
    `#{order.name}`

%p
  = render 'emails/button', text: 'View Quote PDF', link: quote_pdf_url, kind: :primary, is_last: true

%p
  Please email us at
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    = orders_email
  to confirm and accept the quote.

%p Once the quote is confirmed, you will receive another email with the delivery details.
