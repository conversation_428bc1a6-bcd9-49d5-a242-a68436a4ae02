%p Hi #{first_name},

%p
  Just a quick reminder to place orders
  - if frequency == 'weekly'
    for the upcoming week 
    %strong
      starting #{reminder_date.to_s(:date_verbose)}
  - elsif frequency == 'monthly'
    for the upcoming month of
    %strong
      #{reminder_date.strftime('%B')}
  in your Meal Plan named
  = succeed '.' do
    %strong #{meal_plan.name}

%p
  If your account manager has already been assigned to handle your orders, you can simply ignore this reminder — everything’s taken care of!

%p{ style: 'text-align: center;' }
  = render 'emails/button', text: 'View Meal Plan', link: meal_plan.link_url, kind: :primary, is_last: true

%p{ style: 'text-align: center' }
  %em{ style: 'font-size: 12px;' }
    Need to be logged in as `#{meal_plan.customer_name}` to view meal plan.