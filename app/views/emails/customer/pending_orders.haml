%p
  Hi #{firstname},
%p
  Just a reminder you have pending quote orders set to be delivered tomorrow:
  %br
  = succeed '.' do
    %strong<
      = delivery_date
  %br
  If you wish to proceed please login to your account and approve the order(s)

%table{ width: '100%' }
  %tbody
    - pending_orders.each_with_index do |order, idx|
      %tr
        %td{ style: 'width: 50%; border-bottom: 2px solid #ededed;' + (idx == 0 ? 'border-top: 1px solid #ededed;' : '') }
          %table{ width: '100%' }
            %tr
              %td
                %a{ href: order.link, style: email_link_style('font-size: 16px') }
                  Order ##{order.id}
            %tr
              %td
                Delivery on:
                = order.delivery_time
            %tr
              %td
                Order Total:
                = order.total

        %td{ style: 'border-bottom: 2px solid #ededed;' + (idx == 0 ? 'border-top: 1px solid #ededed;' : '') }
          %table{ width: '100%' }
            %tbody
              - order.suppliers.each do |supplier|
                %tr
                  %td{ style: 'width: 40px' }
                    = render 'emails/circle_image', name: supplier.name, image: supplier.image
                  %td{ style: 'text-align: left' }
                    = supplier.name

%p
  %a{ href: profile_url, style: email_link_style } 
    Log in to your account
  to edit/approve/cancel the order(s).

%p
  You can also contact
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    = orders_email
  for more information.
