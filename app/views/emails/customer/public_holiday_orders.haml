%p
  Hi #{firstname},
%p
  Given the
  %strong
    = holiday.date
  is a public holiday in your state (yay!), we have requested the supplier to either push or skip the following orders as per your recommendations.

%table{ width: '100%' }
  %tr
    %td{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;text-align: center;' }
      Order #
    %td{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;text-align: center;' }
      Order Name
    %td{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;text-align: center;' }
      Status
    %td{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;text-align: center;' }
      Old delivery at
    %td{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;text-align: center;' }
      New delivery at
  - orders.each do |order|
    %tr
      %td{ style: 'padding-right: 20px; position: relative;' }
        %a{ href: order.link, style: email_link_style }
          = order.id
      %td{ style: 'padding-right: 20px; position: relative;' }
        = order.name
      %td{ style: 'padding-right: 20px; position: relative; text-align: right' }
        = order.status
      %td{ style: "padding-right: 20px; position: relative; text-align: #{order.old_delivery_at == '-' ? 'center' : 'right'}" }
        = order.old_delivery_at
      %td{ style: "padding-right: 20px; position: relative; text-align: #{order.new_delivery_at == '-' ? 'center' : 'right'}" }
        = order.new_delivery_at
%p
  If you prefered to postpone the delivery instead, please visit your
  %a{ href: profile_url, style: email_link_style } Yordar Profile
  to copy the order to a suitable date.
