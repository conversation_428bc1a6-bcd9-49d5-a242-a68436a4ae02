%p
  Dear #{firstname},
%p
  Hope you are well. Not long now till the Holidays!!!

%p
  This email is to notify that your orders have been put on hold for the Holiday period with last delivery on 
  %strong
    = final_last_delivery_at
  and first delivery starting on
  = succeed '.' do
    %strong<
      = final_first_delivery_at

%table{ width: '100%' }
  %tr
    %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative; text-align: left' }
      Order name
    %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;' }
      Last delivery date
    %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;' }
      First delivery date
      
  - orders.each do |order|
    %tr
      %td{ style: 'padding-right: 20px; position: relative;' }
        = order.name
      %td{ style: 'padding-right: 20px; position: relative; text-align: center' }
        = order.last_delivery_at.present? ? order.last_delivery_at : 'N/A'
      %td{ style: 'padding-right: 20px; position: relative; text-align: center' }
        = order.first_delivery_at.present? ? order.first_delivery_at : 'N/A'
%p
  If you would like to make any changes to the above, contact
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    = orders_email
  as soon as possible.
