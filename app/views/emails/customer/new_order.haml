%p
  Hi #{firstname},

%p
  This is an email confirming your
  %strong
    = order.type == 'recurring' ? "#{order.recurrent_type} #{order.type}" : order.type
  '#{order.name}' order 
  (
  %a{ href: order.link, style: email_link_style }<>
    = order.id
  )
  - if order.type == 'recurring'
    that recurs every #{order.day} to be processed for first
  - else
    to be processed for
  delivery on
  %strong= order.date
  was received.
%p
  The order will be delivered to
  = succeed '.' do
    %strong<
      = order.delivery_address

= render 'emails/order_lines_list', supplier_grouped_order_lines: supplier_grouped_order_lines, totals: totals, order: order

- if order.loading_dock_url.present?
  %p
    This order requires a Loading Dock Code, please send through the code at you convinience before the order delivery date.
  %p{ style: 'text-align: center;' }
    = render 'emails/button', text: 'Enter Loading Dock Code', link: order.loading_dock_url, kind: :primary, is_last: true

- if order_spend.is_under
  %p 
    %strong N.B.
    The order is currently below the minimum order value for the following #{'supplier'.pluralize(order_spend.supplier_spends.size)}:
    %ul{ style: 'list-style: none;' }
      - order_spend.supplier_spends.each do |supplier_spend|
        %li
          #{supplier_spend.supplier_name}: #{supplier_spend.minimum_spend} (remaining: #{supplier_spend.remaining_spend})
    We'll try our best to work with the #{'supplier'.pluralize(order_spend.supplier_spends.size)} to get this order fulfilled. But note that there is always a chance that
    %strong{ style: "color: #{Email::ERROR_COLOR};" } the order could be rejected.

%p
  If there are any mistakes please email us at
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    = orders_email
  or go to
  %a{ href: order.link, style: email_link_style }
    order page
  to edit and update the details.
%p
  %strong N.B.
  If the order is no longer required please
  %a{ href: profile_url, style: email_link_style }
    login
  to cancel the order or
  = succeed '.' do
    %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }<
      email us
  %br/
  %em{ style: 'font-size: 12px;' }
    Please refer to our 
    = succeed '.' do
      %a{ href: 'https://www.yordar.com.au/p/terms', style: email_link_style }<
        cancellation policy

%p{ style: 'text-align: center;' }
  = render 'emails/button', text: 'View Order', link: order.link, kind: :bordered, is_last: true
