%p
  Hi #{firstname},
%p
  Thanks for #{is_extension ? 'extending the' : 'creating a new'} Team Order Package with <PERSON><PERSON><PERSON>.

= render 'emails/team_order_details', team_order: package_order, team_orders: package_orders

%p
  You can invite more attendees to the package by sharing the magic link
  %a{ href: package_order.package_invite_link, style: email_link_style }
    = package_order.package_invite_link

- if package_order.cutoff_option.present?
  - if package_order.cutoff_option == 'charge_to_minimum'
    %p
      You've chosen to
      = succeed ',' do
        %strong<
          top up your order(s)
      %br/
      If you'd like to automatically cancel your order(s) instead, you can do that by
      = succeed '.' do
        %a{ href: package_order.package_link, title: 'Package url', style: email_link_style }<
          editing your preference here
    %p
  - else
    %p
      You've chosen to
      = succeed ',' do
        %strong<
          cancel your order(s)
      %br/
      If you'd like to automatically top up your order(s) instead, you can do that by
      = succeed '.' do
        %a{ href: package_order.package_link, title: 'Package url', style: email_link_style }<
          editing your order preference here

%p{ style: 'text-align: center' }
  = render 'emails/button', text: 'View Package', link: package_order.package_link, kind: :bordered, is_last: true
