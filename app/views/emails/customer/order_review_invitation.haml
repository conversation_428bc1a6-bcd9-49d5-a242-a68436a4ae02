%p
  Hi #{firstname},
%p
  Thank you for placing your recent order with us!

%p
  Your opinion is very important to us. We would love for you to rate and review your recent purchase(s) while it's still fresh in your mind.

%table{ width: '50%' }
  %tbody
    %tr
      %td{ colspan: 2 }
        = order.name
        %a{ href: order.link, style: email_link_style }
          = "(##{order.id})"
    / %tr
    /   %td
    /     Ordered On:
    /   %td
    /     = order.created_on
    %tr
      %td
        Delivered On:
      %td
        = order.delivery_on

%p
  Here is what you ordered:

%table{ width: '100%' }
  %tbody
    - suppliers.each do |supplier|
      %tr
        %td{ style: 'border-bottom: 2px solid #ededed;' }
          %table{ width: '100%' }
            %tr
              %td{ style: 'width: 40px;' }
                = render 'emails/circle_image', image: supplier.image, name: supplier.name
              %td
                %strong
                  = supplier.name
            %tr
              %td{ colspan: 2 }
                %ul
                  - supplier.order_lines.each do |order_line|
                    %li
                      = order_line.name
                      x #{order_line.quantity}
                  - if supplier.has_more
                    %li{ style: 'list-style: none' }
                      %a{ href: order.link, style: email_link_style }
                        See all

        %td{ style: 'border-bottom: 2px solid #ededed;' }
          %p
            = render 'emails/button', text: 'Review', link: supplier.review_url, kind: :primary, is_last: true
