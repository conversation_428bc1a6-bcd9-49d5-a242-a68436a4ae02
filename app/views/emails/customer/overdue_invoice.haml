%p
  - if company_name.present?
    Hi #{company_name} Accounts Team,
  - else
    Hi Accounts Team,

- if kind == :heads_up
  %p
    This is a reminder that you have invoices which are
    %strong
      3 days
    past their due date for payment
    %br
    Due at 
    = succeed '.' do
      %strong
        = due_at

- if kind == :second
  %p
    This is a second reminder to let you know that you have invoices which are
    %strong
      7 days
    past their due date for payment.
    %br
    Due at 
    = succeed '.' do
      %strong
        = due_at

- if kind == :third
  %p
    Your account is well overdue and has invoices which are
    %strong
      14 days 
    past their due date for payment.
    %br
    Due at 
    = succeed '.' do
      %strong
        = due_at

- if kind == :final
  %p
    Unfortunately, after multiple attempts to contact you, you still have invoices which are
    %strong
      21 days
    past their due date for payment.
    %br
    Due at 
    = succeed '.' do
      %strong
        = due_at
  
  %p
    You will receive a call from our accounts team shortly as this will need to be settled within the next 7 days as a matter of urgency.

- if invoices.present?
  %p
    The following invoices are overdue:
  %table{ width: '100%' }
    %thead
      %tr{style: 'text-align: left;'}
        %th{ width: '50%' }
          Invoice number
        %th{ width: '25%' }
          Total
        %th{ width: '25%' }
          &nbsp;
    %tbody
      %tr
        %td{ style: 'height: 2px; border-top: 1px solid #ededed;', colspan: 3 }

      - invoices.each do |invoice|
        %tr
          %td{width: '50%'}
            %strong= "##{invoice.number}"
            - if invoice.pdf_url.present?
              %a{ href: invoice.pdf_url, style: email_link_style }
                (pdf)

          %td{ width: '25%' }
            = invoice.total

          %td{ width: '25%' }
            %a{ href: invoice.payment_url, style: email_link_style }
              Pay now

      %tr
        %td{ style: 'height: 2px; border-top: 1px solid #ededed;', colspan: 3 }

%p
  If the above #{invoices.size > 1 ? 'invoices have' : 'invoice has'} been paid within the last 48 hours please disregard this email.

%p
  If you would like to discuss anything regarding these invoices, you can contact our Accounts team by replying to this email or mailing them at
  = succeed '.' do
    %a{ href: "mailto:#{yordar_credentials(:yordar, :accounts_email)}?subject=#{@email_subject}", style: email_link_style }
      = yordar_credentials(:yordar, :accounts_email)
  
