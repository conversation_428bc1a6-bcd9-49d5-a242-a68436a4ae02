%p
  Hi #{first_name},
%p
  Your
  %strong
    = order.type
  `#{order.name}` order
  (
  %a{ href: order.link, style: email_link_style }<>
    = "##{order.id}"
  )
  - if order.type == "recurring"
    that recurs every #{order.day} was confirmed and will be processed for first
  - else
    was confirmed and will be processed for delivery on
  %strong
    = order.date
  \.

%p
  The order will be delivered to
  %strong
    = order.delivery_address
  \.

= render 'emails/order_lines_list', supplier_grouped_order_lines: supplier_grouped_order_lines, totals: totals, order: order

%p
  If needed, you can request for changes to be made by visiting your
  %a{ href: profile_url, style: email_link_style } Yordar Profile
  or contacting
  = succeed '.' do
    %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }<
      customer service

%p{ style: 'text-align: center;' }
  = render 'emails/button', text: 'View Order', link: order.link, kind: :bordered, is_last: true
