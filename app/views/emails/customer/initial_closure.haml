%p
  Dear #{firstname},
%p
  Hope you are well.

%p This is an email to notify you of the upcoming Yordar Admin closure dates for the Christmas and New Year Holiday period.
%p
  Yordar admin will be closing
  %strong
    = yordar_last_day
  and reopening on
  = succeed '.' do
    %strong<
      = yordar_new_year_first_day
  - if Time.zone.now.year == 2023
    Due to 1st Jan being a
    = succeed ',' do
      %strong public holiday
    any orders on this day will be pushed to next day or skipped according to your order config.

- if orders.count > 0
  %p Additionally, the following orders will be on hold beyond our closure dates due to your supplier closing for longer than our closure dates:
  %table{ width: '100%' }
    %tr
      %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;text-align: left' }
        Order name
      %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;' }
        Last delivery
      %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;' }
        First delivery
    - orders.each do |order|
      %tr
        %td{ style: 'padding-right: 20px; position: relative;' }
          = order.name
        %td{ style: 'padding-right: 20px; position: relative; text-align: center' }
          =raw order.last_delivery_at
        %td{ style: 'padding-right: 20px; position: relative; text-align: center' }
          =raw order.first_delivery_at
%p
  Any orders that are required during this period must be notified to
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    = orders_email
  prior to the
  = succeed '.' do
    %strong<
      = notification_threshold_day
%p
  If your office is reopening after #{yordar_new_year_first_day} please also notify us of when you would like first deliveries in January prior to the
  = succeed '.' do
    %strong<
      = notification_threshold_day
%p We would also like to take this opportunity to thank you for your support over the year and would like to wish you a happy and safe holiday period.
