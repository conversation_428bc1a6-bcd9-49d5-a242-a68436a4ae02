%p
  Hi #{firstname},

- if quote_message.present?
  %p
    = succeed '.' do
      %strong= quote_message.sub(/\.$/,'')
    
%p
  Find attached
  %a{ href: order.quote_pdf_url, style: email_link_style }
    the quote
  for your
  %em
    `#{order.name}`
  order
  - if is_customer_email
    %a{ href: order.link, style: email_link_style }
      = " (##{order.id})"
  - else
    = " (##{order.id})"

%p The order is set to be processed with the following details:
%ul
  %li
    %strong Delivery On:
    = order.date
  %li
    %strong Delivered To:
    = order.delivery_address
  %li
    %strong Order Total:
    = order.customer_total

= render 'emails/order_lines_list', supplier_grouped_order_lines: supplier_grouped_order_lines, totals: totals, order: order

- if is_customer_email
  %p{ style: 'margin-top: 20px; margin-bottom: 0; text-align: center;' }
    = render 'emails/button', text: 'View PDF', link: order.quote_pdf_url, kind: :bordered, is_last: true

  %p{ style: 'text-align: center;' }
    = render 'emails/button', text: 'Edit', link: order.edit_url, kind: :bordered, displacement: 16
    = render 'emails/button', text: 'Confirm', link: order.confirm_url, kind: :primary, displacement: 16
    = render 'emails/button', text: 'Reject', link: order.reject_url, kind: :secondary, is_last: true    

  %p
    If you need any changes please email us at
    = succeed '.' do
      %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }<
        = orders_email
- else
  %p{ style: 'margin-top: 20px; margin-bottom: 0; text-align: center;' }
    = render 'emails/button', text: 'View Quote', link: order.quote_pdf_url, kind: :primary, is_last: true
  %p
    Contact #{customer.name} @
    %a{ href: 'mailto:#{customer.email}?subject=#{@email_subject}', style: email_link_style }
      = customer.email
    to confirm or decline this quote.
    %br
    If you need any help, email us at
    = succeed '.' do
      %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }<
        = orders_email
  
