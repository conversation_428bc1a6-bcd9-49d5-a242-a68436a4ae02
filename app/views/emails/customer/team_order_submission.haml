%p
  Hi #{firstname},
%p
  The Team Order you created,
  %strong
    = "`#{team_order.name}`"
  , has now reached cut-off.

- if (team_order.cutoff_option.present? && minimum_spend.present?)
  - if supplier_surcharge.present?
    %p
      Since the order spend of #{order_spend} did not meet the supplier minimum of #{minimum_spend}, as per your selection we've
      %strong
        automatically topped up your order with the remaining value of #{supplier_surcharge}.
    %p
      If you wish to add any further items to the order or make any last minute changes, contact our ordering team ASAP at
      %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
        = orders_email

  - elsif team_order.cutoff_option == 'cancel_order' && team_order.status == 'cancelled'
    %p
      Since the order spend of #{order_spend} did not meet the supplier minimum of #{minimum_spend}, as per your selection we've
      = succeed '.' do
        %strong<
          automatically cancelled your order
    %p
      If you wish to re-instate the order and/or add any further items to the order, you can contact our ordering team ASAP at
      %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
        = orders_email
  - else
    %p
      If you wish to make any last minute changes, contact our ordering team ASAP at
      %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
        = orders_email

= render 'emails/team_order_details', team_order: team_order, team_orders: [team_order]
  
- if team_order.status != 'cancelled'
  %p We hope you and your team enjoy your food!

%p{ style: 'text-align: center;' }
  = render 'emails/button', text: 'View Order', link: team_order.link, kind: :bordered, is_last: true
