%p
  Hi #{firstname},

%p
  Thanks for creating a new Team Order with <PERSON><PERSON><PERSON>.

= render 'emails/team_order_details', team_order: team_order, team_orders: [team_order]

%p
  You can invite more attendees to the order by sharing this magic link
  %br/
  %a{ href: team_order.invite_link, style: email_link_style }
    = team_order.invite_link

- if minimum_spend.present?
  %p
    The order requires a spend of
    %strong= minimum_spend
    to meet the supplier minimums and you have until
    %strong
      = cutoff_datetime.in_time_zone.to_s(:full)
    , when the order reaches its cut-off time.

- if team_order.cutoff_option.present?
  - if team_order.cutoff_option == 'charge_to_minimum'
    %p
      You've chosen to
      %strong top up your order,
      If you'd like to automatically cancel your order instead, you can do that by
      = succeed '.' do
        %a{ href: team_order.link, title: 'Order url', style: email_link_style }<
          editing your preference here
    %p
  - else
    %p
      You've chosen to
      %strong
        cancel your order
      ,
      %br
      If you'd like to automatically top up your order instead, you can do that by
      = succeed '.' do
        %a{ href: team_order.link, title: 'Order url', style: email_link_style }<
          editing your preference here

%p{ style: 'text-align: center' }
  = render 'emails/button', text: 'View Order', link: team_order.link, kind: :bordered, displacement: 40
  = render 'emails/button', text: 'Order As Team Admin', link: team_order.admin_order_link, kind: :primary, is_last: true
