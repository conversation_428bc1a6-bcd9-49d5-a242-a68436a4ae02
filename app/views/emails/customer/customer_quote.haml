:ruby
  customer_fields = %w[company phone officeAddress staffSize extraContactInformation]
  event_fields = %w[location date time occasion budget budgetType estimatedAttendees]
  requirement_fields = %w[categories allergies dietaries notListedAllergyOrDietary eventRequirements]
  style_fields = %w[eventStyles serviceStyles individuallyPacked cutlery]
  extra_fields = %w[currentProductList needsMerchandisingEquipment]


%p Hi #{customer_name},

%p
  Thank you for sumbitting a quote with <PERSON><PERSON><PERSON>. We've received your 
  %strong #{quote.kind} quote
  submission with the following details:

%h1{ style: 'font-size: 18px;' } Contact Details

%table{ width: '100%', style: 'padding-bottom: 10px;' }
  - customer_fields.each do |field|
    - if form_data[field].present?
      %tr
        %td{ style: 'border-bottom: 1px solid #ededed; width: 50%;' }
          = field.underscore.humanize.titleize
        %td{ style: 'border-bottom: 1px solid #ededed' }
          - if form_data[field].is_a?(Array)
            = form_data[field].join(', ')
          - else
            = form_data[field]

%h1{ style: 'font-size: 18px;' } Order / Event Details

%table{ width: '100%', style: 'padding-bottom: 10px;' }
  - (event_fields + requirement_fields + style_fields + extra_fields).each do |field|
    - if form_data[field].present?
      %tr
        %td{ style: 'border-bottom: 1px solid #ededed; width: 50%;' }
          = field.underscore.humanize.titleize
        %td{ style: 'border-bottom: 1px solid #ededed' }
          - if field == 'date'
            = DateTime.parse(form_data[field]).strftime('%d/%m/%Y')
          - elsif form_data[field].is_a?(Array)
            = form_data[field].join(', ')
          - else
            = form_data[field]

%p
  Our team will be in touch shortly to help proceed with your quote. Thanks!
