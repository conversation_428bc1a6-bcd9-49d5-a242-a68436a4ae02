%p
  Hi #{first_name},
%p
  You have the following upcomming orders which require a loading dock code.

%table{ width: '100%' }
  %tbody
    - orders.each_with_index do |order, idx|
      %tr
        %td{ style: 'width: 50%; border-bottom: 2px solid #ededed;' + (idx == 0 ? 'border-top: 1px solid #ededed;' : '') }
          %table{ width: '100%' }
            %tr
              %td
                %a{ href: order.link, style: email_link_style('font-size: 16px') }
                  Order ##{order.id}

            %tr
              %td
                %strong= order.delivery_time
                - if order.remaining_days.present? && order.remaining_days != 0
                  %br
                  %small (#{order.remaining_days} days remaining)
            %tr
              %td
                =raw order.delivery_address

            %tr
              %td
                - order.suppliers.each do |supplier|
                  %table{ width: '100%' }
                    %tr
                      %td{ style: 'width: 40px' }
                        = render 'emails/circle_image', name: supplier.name, image: supplier.image
                      %td{ style: 'text-align: left' }
                        = supplier.name
            %tr
              %td
                %strong Order Total:
                = order.total              

        %td{ style: 'border-bottom: 2px solid #ededed;text-align: center;' + (idx == 0 ? 'border-top: 1px solid #ededed;' : '') }
          = render 'emails/button', text: 'Enter Loading Dock Code', link: order.loading_dock_link, kind: :primary, is_last: true

%p
  If needed, you can request for changes to be made by visiting your
  %a{ href: profile_url, style: email_link_style } Yordar Profile
  or contacting
  = succeed '.' do
    %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }<
      customer service
