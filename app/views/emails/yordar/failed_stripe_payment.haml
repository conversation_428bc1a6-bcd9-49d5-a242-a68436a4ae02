%p
  Dear Accounts Admin,

%p 
  A payment transaction 
  %strong
    FAILED
  on 
  = succeed '.' do
    %strong<
      Stripe

%table{ width: '100%' }
  %tr
    %td{ colspan: 2 }
      %strong{ style: 'font-size: 16px' }
        Details 

  %tr
    %td
      Payment ID
    %td
      %a{ href: payment_url, style: email_link_style }
        = event.payment_id

  %tr
    %td
      Reference
    %td
      = event.description

  %tr
    %td
      Purchase / Payment
    %td
      = "#{event.currency} #{event.amount}"

  - if event.failure_code.present?
    %tr
      %td
        Failure Code
      %td
        = event.failure_code

    %tr
      %td
        Failure Message
      %td
        = event.failure_message


%p{ style: 'text-align: center;' }
  = render 'emails/button', text: 'Go to Stripe', link: stripe_account_url, kind: :bordered, disposition: 40
  = render 'emails/button', text: 'Failed Stripe Payment', link: payment_url, kind: :bordered, is_last: true
  %br/
  %em{ style: 'font-size: 12px' }
    Requires access to Yordar's Stripe Account.
