%p
  Dear Accounts Admin,

%p
  In the last 24 hours, the following invoices 
  %strong
    failed
  to push to
  = succeed '.' do
    %strong<
      Xero

- if sync_issues.present?
  %table{ width: '100%' }
    %tr
      %td{ colspan: 4, style: 'border-bottom: 2px dotted #ededed;' }
        %strong{ style: 'font-size: 16px' }
          Xero sync issue
        %br/
        Attempted to push invoices but failed due to an error with the Xero API.
    %tr
      %td{ style: 'border-bottom: 2px dotted #ededed;' }
        %strong Invoice number
      %td{ style: 'width: 40%; border-bottom: 2px dotted #ededed;' }
        %strong Customer Name
      %td{ style: 'width: 20%; border-bottom: 2px dotted #ededed;' }
        %strong Invoice Date
      %td{ style: 'border-bottom: 2px dotted #ededed;' }
        &nbsp;
    - sync_issues.each do |issue|
      %tr
        %td{ style: 'border-bottom: 1px dotted #ededed;' }
          = issue.invoice_number
        %td{ style: 'width: 40%; border-bottom: 1px dotted #ededed;' }
          = issue.customer_name
        %td{ style: 'border-bottom: 1px dotted #ededed;' }
          = issue.invoice_date
        %td{ style: 'width: 20%; border-bottom: 1px dotted #ededed;' }
          = render 'emails/button', text: 'View PDF', link: issue.invoice_url, kind: :bordered, is_last: true

- if sync_issues.present? && contact_issues.present?
  = render 'layouts/emails/spacer', height: 20

- if contact_issues.present?
  %table{ width: '100%' }
    %tr
      %td{ colspan: 4, style: 'border-bottom: 2px dotted #ededed;' }
        %strong{ style: 'font-size: 16px' }
          Contact creation issue
        %br/
        Contacts (Customers) could not be created in Xero. This may be because they have been archived.        
    %tr
      %td{ style: 'border-bottom: 2px dotted #ededed;' }
        %strong Invoice number
      %td{ style: 'width: 40%; border-bottom: 2px dotted #ededed;' }
        %strong Customer Name
      %td{ style: 'border-bottom: 2px dotted #ededed;' }
        %strong Invoice Date
      %td{ style: 'width: 20%; border-bottom: 2px dotted #ededed;' }
        &nbsp;
    - contact_issues.each do |issue|
      %tr
        %td{ style: 'border-bottom: 1px dotted #ededed;' }
          = issue.invoice_number
        %td{ style: 'width: 40%; border-bottom: 1px dotted #ededed;' }
          = issue.customer_name
        %td{ style: 'border-bottom: 1px dotted #ededed;' }
          = issue.invoice_date
        %td{ style: 'width: 20%; border-bottom: 1px dotted #ededed;' }
          = render 'emails/button', text: 'View PDF', link: issue.invoice_url, kind: :bordered, is_last: true
