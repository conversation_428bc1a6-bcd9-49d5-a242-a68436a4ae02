%p Hi Accounts Team,

%p
  Find below the on-boarding details of the Yordar staff named
  %strong
    = "`#{customer_name}`."

%strong{ style: 'font-size: 18px' } Personal Details
%table{ width: '100%', style: 'padding-bottom: 10px;' }
  - personal.each do |key, value|
    %tr
      %td{ style: 'width: 40%;' }
        %strong
          = "#{key.titleize}:"
      %td{ style: 'width: 60%;' }
        = value

%strong{ style: 'font-size: 18px' } Emergency Contact
%table{ width: '100%', style: 'padding-bottom: 10px;' }
  - emergency_contact.each do |key, value|
    %tr
      %td{ style: 'width: 40%;' }
        %strong
          = "#{key.titleize}:"
      %td{ style: 'width: 60%;' }
        = value

%strong{ style: 'font-size: 18px' } Bank Details
%table{ width: '100%', style: 'padding-bottom: 10px;' }
  - bank.each do |key, value|
    %tr
      %td{ style: 'width: 40%;' }
        %strong
          = "#{key.titleize}:"
      %td{ style: 'width: 60%;' }
        = value

%strong{ style: 'font-size: 18px' } Tax Details
%table{ width: '100%', style: 'padding-bottom: 10px;' }
  %tr
    %td{ style: 'width: 40%;' }
      %strong TFN declaration:
    %td{ style: 'width: 60%;' }
      %a{ style: email_link_style, href: tax.tfn }
        Click Here

  - if tax.super.present?
    %tr
      %td{ style: 'width: 40%;' }
        %strong Superannuation:
      %td{ style: 'width: 60%;' }
        %a{ style: email_link_style, href: tax.super }
          Click Here


