%p Hi,
%p See below a breakdown of orders auto confirmed for delivery tomorrow.

%table{ width: '100%', style: 'border-collapse: collapse;' }
  - auto_confirmed_orders.each_with_index do |order, idx|
    - border_top = idx == 0 ? 'border-top: 2px solid #ededed;' : ''
    %tr
      %td{ style: "width: 50%; border-right: 1px solid #ededed; border-bottom: 2px solid #ededed; #{border_top}" }
        %table{ width: '100%' }
          %tr
            %td{ style: 'width: 30%;' }
              Order
            %td
              %strong
                = "##{order.id}"
          %tr
            %td{ style: 'width: 30%;' }
              Name
            %td
              = order.name
          %tr
            %td{ style: 'width: 30%;' }
              Customer
            %td
              = order.customer_name;
          %tr
            %td{ colspan: 2, style: 'text-align: center;' }
              = render 'emails/button', text: 'View Order in Admin', link: order.link, kind: :secondary, is_last: true 
      %td{ style: "border-bottom: 2px solid #ededed; #{border_top}" }
        %table{ width: '100%' }
          - order.suppliers.each do |supplier|
            %tr
              %td
                = render 'emails/circle_image', image: supplier.image, name: supplier.name
              %td
                = supplier.name
                - if supplier.phone.present?
                  %br/
                  Ph:
                  %a{ href: "tel:#{supplier.phone}", style: email_link_style }
                    = supplier.phone
                  

