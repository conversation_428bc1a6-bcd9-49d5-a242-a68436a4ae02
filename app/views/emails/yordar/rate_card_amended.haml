%p
  Dear Admin,

%p A rate card has been updated with the following details:

%table{ width: '100%' }
  %tr
    %th{ style: 'width: 50%; border-right: 1px solid #ededed; border-bottom: 2px solid #ededed;' }
      Supplier
    %th{ style: 'border-bottom: 2px solid #ededed;;' }
      Item
  %tr
    %td{ style: 'border-right: 1px solid #ededed; border-bottom: 1px solid #ededed;' }
      %table{ width: '100%;' }
        %tr
          %td
            = render 'emails/circle_image', image: supplier.image, name: supplier.name
          %td
            = supplier.name
    %td{ style: 'width: 50%; border-bottom: 1px solid #ededed;' }
      %table{ width: '100%' }
        %tr
          %td
            = render 'emails/circle_image', image: item.image, name: item.name
          %td
            = item.name      
  %tr
    %td{ colspan: 2, style: 'border-bottom: 1px solid #ededed;' }
      %strong
        Company:
      = rate_card.company_name

= render 'layouts/emails/spacer', background: '#ffffff', height: 20

%table{ width: '100%' }
  %tr
    %th{ style: 'width: 50%; border-bottom: 2px solid #ededed;' }
      NEW PRICE
    %th{ style: 'border-bottom: 2px solid #ededed;;' }
      NEW COST
  %tr
    %td{ style: 'text-align: center; width: 50%; border-bottom: 1px solid #ededed' }
      %strong{ style: 'font-size: 18px;' }
        = rate_card.new_price
    %td{ style: 'text-align: center; border-bottom: 1px solid #ededed;' }
      %strong{ style: 'font-size: 18px;' }
        = rate_card.new_cost

= render 'layouts/emails/spacer', background: '#ffffff', height: 20

- if updated_orders.present?
  %table{ width: '100%' }
    %tr
      %td{ colspan: 4 }
        The following orders were affected by this change:
    %tr
      %th{ style: 'border-bottom: 2px solid #ededed;'}
        Order#
      %th{ style: 'text-align: left; border-bottom: 2px solid #ededed;'}
        Customer
      %th{ style: 'border-bottom: 2px solid #ededed;'}
        Prev total
      %th{ style: 'border-bottom: 2px solid #ededed;'}
        New total
    - updated_orders.each do |updated_order|
      %tr
        %td{ style: 'text-align: center; border-bottom: 1px solid #ededed;'}
          = updated_order.id
        %td{ style: 'text-align: left; border-bottom: 1px solid #ededed;'}
          = updated_order.customer_name
        %td{ style: 'text-align: center; border-bottom: 1px solid #ededed;'}
          = updated_order.old_total
        %td{ style: 'text-align: center; border-bottom: 1px solid #ededed;'}
          = updated_order.new_total
