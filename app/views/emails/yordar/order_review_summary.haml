%p
  Hi Admin,

%p Please find below weekly ratings and comments summary for orders between #{week_start} and #{week_end}.

- if supplier_reviews.present?
  - supplier_reviews.each_with_index do |supplier, idx|
    - if idx > 0
      = render 'layouts/emails/spacer', color: '#ffffff', height: 20
      
    %table{ width: '100%', style: 'border-collapse: collapse;' }
      %tr
        %td{ colspan: 6, style: 'border-bottom: 2px dotted #ededed;' }
          %table{ width: '50%' }
            %tr
              %td
                = render 'emails/circle_image', image: supplier.image, name: supplier.name
              %td
                = supplier.name
      %tr
        %th{ style: 'border-right: 1px solid #ededed;border-bottom: 2px solid #ededed;' }
          Review date
        %th{ style: 'border-right: 1px solid #ededed;border-bottom: 2px solid #ededed;' }
          Food taste
        %th{ style: 'border-right: 1px solid #ededed;border-bottom: 2px solid #ededed;' }
          Presentation
        %th{ style: 'border-right: 1px solid #ededed;border-bottom: 2px solid #ededed;' }
          Delivery punctuality
        %th{ style: 'border-right: 1px solid #ededed;border-bottom: 2px solid #ededed;' }
          Order #
        %th{ style: 'border-bottom: 1px solid #ededed;' }
          Order date

      - supplier.reviews.each_with_index do |review, idx|
        - review_border = review.comment.present? ? 'dotted' : 'solid'
        - review_background = idx % 2 == 0 ? '#ffffff' : '#eeeeee'
        %tr
          %td{ style: "height: 35px; background-color: #{review_background}; border-right: 1px solid #dddddd; border-bottom: 1px #{review_border} #dddddd; text-align: center;" }
            = review.review_date
          %td{ style: "height: 35px; background-color: #{review_background}; border-right: 1px solid #dddddd; border-bottom: 1px #{review_border} #dddddd; text-align: center;" }
            = review.food_taste
          %td{ style: "height: 35px; background-color: #{review_background}; border-right: 1px solid #dddddd; border-bottom: 1px #{review_border} #dddddd; text-align: center;" }
            = review.presentation
          %td{ style: "height: 35px; background-color: #{review_background}; border-right: 1px solid #dddddd; border-bottom: 1px #{review_border} #dddddd; text-align: center;" }
            = review.delivery_punctuality
          %td{ style: "height: 35px; background-color: #{review_background}; border-right: 1px solid #dddddd; border-bottom: 1px #{review_border} #dddddd; text-align: center;" }
            = review.order_number
          %td{ style: "height: 35px; background-color: #{review_background}; border-bottom: 1px #{review_border} #dddddd; text-align: center;" }
            = review.order_date
        - if review.comment.present?
          %tr
            %td{ colspan: 6, style: "background-color: #{review_background}; border-bottom: 1px solid #dddddd; padding-left: 10px" }
              %strong
                comment:
              = review.comment
- else
  %p{ style: 'text-align: center;' }
    No reviews for this week
