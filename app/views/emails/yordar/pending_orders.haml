%p Dear admin,
%p
  This is an email to notify you about
  %strong pending orders
  which are set to be delivered tomorrow:
  = succeed '.' do
    %strong<
      = delivery_date

%table{ width: '100%' }
  %tbody
    - pending_orders.each_with_index do |order, idx|
      %tr
        %td{ style: 'width: 50%; border-bottom: 2px solid #ededed;' + (idx == 0 ? 'border-top: 1px solid #ededed;' : '') }
          %table{ width: '100%' }
            %tr
              %td
                %a{ href: order.link, style: email_link_style('font-size: 16px') }
                  Order ##{order.id}
                (#{order.type})
            %tr
              %td
                Delivery on:
                = order.delivery_time
            %tr
              %td
                Order Total:
                = order.total

        %td{ style: 'border-bottom: 2px solid #ededed;' + (idx == 0 ? 'border-top: 1px solid #ededed;' : '') }
          %table{ width: '100%' }
            %tbody
              - order.suppliers.each do |supplier|
                %tr
                  %td{ style: 'width: 40px' }
                    = render 'emails/circle_image', name: supplier.name, image: supplier.image
                  %td{ style: 'text-align: left' }
                    = supplier.name

%p
  %strong
    N.B.
  Please check with the customer(s) and make sure to either Approve or Cancel the order(s).
- if has_quote_orders
  %p
    %strong
      N.B.
    If left unchecked the non-custom quoted orders will be 
    = succeed '.' do
      %strong
        automatically cancelled
