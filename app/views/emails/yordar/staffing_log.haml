%p Hi #{first_name},

%p
  Here is a recap of your Pantry Manager Schedule for fortnight
  %br
  %strong
    #{from_date} to #{to_date}

%p
  Your total billable hours are:
  = succeed '.' do
    %strong
      #{total_hours} hrs

%table{ width: '100%' }
  %thead
    %tr
      %th{ style: 'text-align: left; border-bottom: 1px solid #000000;' }
        Customer
      %th{ style: 'text-align: center; border-bottom: 1px solid #000000;' }
        Number of Hours
  %tbody
    - schedule.each_with_index do |schedule_data, idx|
      - has_multiple_orders = schedule_data.orders.size > 1
      %tr
        %td{ style: 'border-bottom: 1px solid #ededed;', colspan: has_multiple_orders ? 1 : 2 }
          %strong
            = schedule_data.date
        - if has_multiple_orders
          %td{ style: 'text-align: center; border-bottom: 1px solid #ededed;' }
            = "Total: #{schedule_data.total_hours} hrs"
      - schedule_data.orders.each do |order|
        %tr
          %td
            %strong
              = order.customer_name
            - if order.company_name.present?
              = "(#{order.company_name})"
          %td{ style: 'text-align: center;' }
            = "#{order.hours} hrs"
            - if schedule_data.is_allocated_for_week
              (full week)
      - if idx < schedule.size
        %tr
          %td{ style: 'border-bottom: 1px solid #ededed;', colspan: 2 }
            &nbsp;

%p
  If you have any issues with the above schedule, contact
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    = orders_email
  or
  %a{ href: "mailto:#{accounts_email}?subject=#{@email_subject}", style: email_link_style }
    Accounts Team
  as soon as possible.
