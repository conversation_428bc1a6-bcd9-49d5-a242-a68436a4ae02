%p Hi Accounts Team,

%p
  Here is a recap of all the Pantry Manager Schedules for fortnight
  %br
  %strong
    #{from_date} to #{to_date}

%p
  The total billable staffing hours are:
  = succeed '.' do
    %strong
      #{total_hours} hrs

%table{ width: '100%' }
  %thead
    %tr
      %th{ style: 'text-align: left; border-bottom: 1px solid #000000;' }
        Pantry Manager
      %th{ style: 'text-align: center; border-bottom: 1px solid #000000;' }
        Number of Hours
  %tbody
    - schedule.each_with_index do |schedule_data, idx|
      %tr
        %td{ style: 'text-align: left; border-bottom: 1px solid #ededed;' }
          %strong
            = schedule_data.pantry_manager
        %td{ style: 'text-align: center; border-bottom: 1px solid #ededed;' }
          = "#{schedule_data.hours} hrs"


%p
  Also find attached the 
  %a{ href: document_url, style: email_link_style }
    Spending Report
  to confirm the orders.

%p
  You can get in touch with
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    = orders_email
  for any changes.
