%p Hi #{first_name},

%p
  Your Pantry Manager schedule for the week between #{from_date} to #{to_date}:

%table{ width: '100%' }
  %thead
    %tr
      %th{ style: 'text-align: left; border-bottom: 1px solid #000000;' }
        Customer
      %th{ style: 'text-align: center; border-bottom: 1px solid #000000;' }
        Number of Hours
  %tbody
    - schedule.each_with_index do |schedule_data, idx|
      - has_multiple_orders = schedule_data.orders.size > 1
      - if !schedule_data.is_allocated_for_week
        %tr
          %td{ style: 'border-bottom: 1px solid #ededed;', colspan: has_multiple_orders ? 1 : 2 }
            %strong
              = schedule_data.date
          - if has_multiple_orders
            %td{ style: 'text-align: center; border-bottom: 1px solid #ededed;' }
              = "Total: #{schedule_data.total_hours} hrs"
      - schedule_data.orders.each do |order|
        %tr
          %td
            %strong
              = order.customer_name
            - if order.company_name.present?
              = "(#{order.company_name})"
          %td{ style: 'text-align: center;' }
            - if schedule_data.is_allocated_for_week
              Full Week
            - else
              = "#{order.hours} hrs"
      - if idx < schedule.size
        %tr
          %td{ style: 'border-bottom: 1px solid #ededed;', colspan: 2 }
            &nbsp;

%p
  If you have any issues with the above schedule, contact
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    = orders_email
  as soon as possible.
