%p
  Hi #{firstname},

%p
  The order ##{order.id} by 
  %strong #{customer.name}
  - if customer.company.present?
    %small (#{customer.company})
  was recently submitted.

%p
  Please 
  %strong check/confirm the stock availability
  of this order with the supplier(s).

%table{ width: '100%' }
  %tbody
    %tr
      %td{ colspan: 2 }
        %strong Order Details:

    %tr
      %td{ style: 'width: 40%;' }
        %strong Name:
      %td{ style: 'width: 60%;' }
        = order.name

    %tr
      %td{ style: 'width: 40%;' }
        %strong Date:
      %td{ style: 'width: 60%;' }
        = order.delivery_at
    %tr
      %td{ style: 'width: 40%; vertical-align: top' }
        %strong
          Address:
      %td{ style: 'width: 60%;' }
        =raw order.delivery_address

= render 'emails/order_lines_list', supplier_grouped_order_lines: supplier_grouped_order_lines, totals: totals, order: order

%p
  = render 'emails/button', text: 'View Order', link: order.link, kind: :primary, is_last: true
  %br
  %small
    %strong Note:
    %em
      Need to be logged in as #{customer.name} to view order.



