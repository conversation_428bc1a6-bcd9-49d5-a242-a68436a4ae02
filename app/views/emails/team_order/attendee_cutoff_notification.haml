%p
  Hi #{attendee.name},

- if cutoff_time == '24hr'
  %p
    Friendly reminder: You have a team order that is set to conclude tomorrow. You have
    %strong
      #{remaining_time} remaining
    until your #{team_order.delivery_day} order with
    %em
      = "`#{team_order.supplier.name}`"
    reaches cut-off.
- else
  %p
    Just a reminder there's only
    %strong
      #{remaining_time} remaining
    until your #{team_order.delivery_at} order with
    %em
      = "`#{team_order.supplier.name}`"
    reaches cut-off.

- if %w[24hr 30m].include?(cutoff_time)
  %p
    Once the team order reaches cut-off, you won't be able to make changes or add to your order.
    
= render 'emails/team_order_details', team_order: team_order, team_orders: [team_order], is_attendee: true

- if cutoff_time != '30m'
  %p
    If you're no longer attending, or would like to decline this team order,
    %a{ href: attendee.unsubscribe_url, title: 'Unsubscribe url', style: email_link_style } click here
    to decline and opt out of further notifications.

%p{ style: 'text-align: center; margin-bottom: 4px;' }
  = render 'emails/button', text: (attendee.status == 'pending' ? 'Confirm Order' : 'Place Order'), link: attendee.order_url, kind: :primary, is_last: true


