%p
  Hi #{attendee.name},

%p
  Just updating you on the status of the team order on 
  = succeed '.' do
    %strong<
      = team_order.delivery_at

- if team_order.status == 'pending'
  %p
    The order has been
    %strong{ style: "color: #{Email::ERROR_COLOR};" }
      cancelled by the administrator
    - if attendee.status == 'invited'
      and no longer needs to be updated by you.
    - else
      and any items you've selected won't be delivered. Please contact your team admin for further information.
- else
  %p
    The order has been
    %strong{ style: "color: #{Email::ERROR_COLOR};" }
      CANCELLED
    as it can no longer be fulfilled by the supplier.
  - if %w[pending ordered].include?(attendee.status)
    %p
      Any items you've selected won't be delivered. Please contact your team admin for further information.
