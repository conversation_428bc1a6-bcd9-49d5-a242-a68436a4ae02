%p
  Hi #{firstname},
%p
  You've currently got
  %strong
    = anonymous_attendees.size
    anonymous attendee(s) left to approve / reject
  in your team order.
  %br/
  All anonymous attendees will be auto-approved (unless already rejected), just before sending the order to the suppliers.
%p The following people are pending approval/rejection:
%ul
  - anonymous_attendees.each do |attendee|
    %li
      %strong
        = attendee.name
      \-
      %a{ href: "mailto:#{attendee.email}", style: email_link_style }
        = attendee.email

      - if attendee.status == 'ordered'
        \- (with confirmed order)

= render 'emails/team_order_details', team_order: team_order, team_orders: [team_order]

%p{ style: 'text-align: center;'}
  = render 'emails/button', text: 'View Order', link: team_order.link, kind: :bordered, is_last: true
