%p
  Hi #{admin_name},
%p
  Just a reminder that there's 
  %strong
    #{remaining_time} remaining
  until your team order for #{team_order.delivery_at.in_time_zone.to_s(:date)} with
  %em
    = "`#{team_order.supplier.name}`"
  reaches cut-off and will no longer be able to be edited.

- if remaining_amount.present?
  %p
    Your team order is currently at
    = succeed ',' do
      %strong<
        = team_order.total
    which is 
    %strong
      = remaining_amount
    below your selected suppliers minimum.
  - if team_order.cutoff_option == 'charge_to_minimum'
    %p
      You've chosen to
      = succeed ',' do
        %strong top up your order
      If you'd like to automatically cancel your order instead, you can do that by
      = succeed '.' do
        %a{ href: team_order.link, title: 'Order url', style: email_link_style } changing your preference here
    %p
  - else
    %p
      You've chosen to
      = succeed ',' do
        %strong cancel your order
      If you'd like to automatically top up your order instead, you can do that by
      = succeed '.' do
        %a{ href: team_order.link, title: 'Order url', style: email_link_style } changing your preference here
    
- else
  %p
    Your team order is currently at 
    = succeed ',' do
      %strong<
        = team_order.total
    you have reached the supplier minimum.


- if anonymous_attendee_count.present? && anonymous_attendee_count > 0
  %p
    *You have
    %strong
      = anonymous_attendee_count
      people pending approval
    on your team order.

= render 'emails/team_order_details', team_order: team_order, team_orders: [team_order]


%p{ style: 'text-align: center;' }
  = render 'emails/button', text: 'View Order', link: team_order.link, kind: :bordered, displacement: 40
  = render 'emails/button', text: 'Order As Team Admin', link: admin_order_url, kind: :primary, is_last: true

%p
  Sign in to your account to see the current status of
  = succeed '.' do
    %a{ href: team_order.link, title: 'Order url', style: email_link_style } your team order
