%p
  Hi #{firstname},

%p
  It seems you (or someone using your email) has tried registering to the a Yordar
  = team_order.is_package_order ? 'team order package' : 'team order'
  named
  = succeed '.' do
    %strong
      = team_order.name

%p
  But since the said
  = team_order.is_package_order ? 'team order package' : 'team order'
  is created under your account, you have
  %strong Team Admin Access
  to it and is best to log into your
  %a.eamil-link{ href: profile_url } Yordar Account
  and access said team order(s) to place an
  = succeed '.' do
    %strong
      Order as Admin

%p{ style: 'text-align: center' }
  = render 'emails/button', text: team_order.is_package_order ? 'Team Order Package' : 'Team Order', link: team_order.link, kind: :primary, is_last: true
