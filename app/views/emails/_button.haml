:ruby
  kind ||= :primary
  text ||= ''
  link ||= ''

  case kind
  when :secondary
    background_color = '#241c15'
    border_color = '#241c15'
    font_color = '#ffffff'
  when :bordered
    background_color = '#ffffff'
    border_color = '#4db6ac'
    font_color = '#4db6ac'
  else #when :primary
    background_color = '#4db6ac'
    border_color = '#4db6ac'
    font_color = '#ffffff'
  end

  is_last ||= false
  displacement ||= 40
  displacement_style = is_last ? '' : "margin-right: #{displacement}px;"

%a{ href: link, style: "background-color: #{background_color}; border: 1px solid #{border_color}; border-radius:4px; color: #{font_color}; display:inline-block; line-height:normal; padding: 6px 24px; text-decoration:none; #{displacement_style}", target: '_blank', rel: 'no-follow' }
  = text
