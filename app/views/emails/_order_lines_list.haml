:ruby
  totals ||= nil

%table{ width: '100%' }
  - supplier_grouped_order_lines.each do |info|
    %tr
      %td{ colspan: '4' }
        %table
          %tr
            %td
              = render 'emails/circle_image', image: info.supplier.image, name: info.supplier.name
            %td
              %strong{ style: 'text-transform: uppercase; letter-spacing: 2px; font-size: 14px; color: #424242; vertical-align: top;' }
                = info.supplier.name
    %tr
      %td{ colspan: '4', style: 'border-top: 2px solid #ededed; padding-top: 10px' }

    - info.order_lines.each do |order_line|
      %tr{ style: 'height: 50px;' }
        %td{ style: 'padding: 0 20px; position: relative; left: -2px;' }
          = render 'emails/circle_image', image: order_line.image, name: order_line.name, dimensions: 30

        %td{ style: 'padding-right: 20px; position: relative; left: -2px;' }
          %strong
            = order_line.name
          - if order_line.description.present?
            %br/
            %small= order_line.description
          - if order_line.dietary_preferences.present?
            %br/
            - order_line.dietary_preferences.each_with_index do |(letter, field), idx|
              %span{ style: "font-size: 10px; width: 20px; height: 20px; border-radius: 50%; display: inline-block; margin-bottom: 5px; text-align: center; background-color: #{Email::DIETARY_COLORS[field.to_sym].presence || '#f8ecb6'}; #{idx > 0 ? 'margin-left: 5px;' : ''}" }
                = letter
        %td{ style: 'padding-right: 20px; position: relative; left: -4px; text-align: right' }
          = "x#{order_line.quantity}"
        - if order_line.price
          %td{ style: 'position: relative; text-align: right' }
            = order_line.price

    - if info.has_more
      %tr
        %td{ colspan: '4', style: 'text-align: center' }
          %a{ href: order.link, style: email_link_style } See all

- if totals.present?
  %table{ width: '100%' }
    %tr
      %td{ colspan: '2', style: 'border-bottom: 2px solid #ededed; padding-top: 10px;' }
    %tr
      %td{ style: 'text-align: left; padding-top: 10px;' }
        %strong Subtotal
      %td{ style: 'text-align: right; padding-top: 10px;' }
        = totals.customer_subtotal

    - if totals.order_discount.present?
      %tr
        %td{ style: 'text-align: left;' }
          %strong Discount
        %td{ style: 'text-align: right;' }
          = totals.order_discount

    %tr
      %td{ style: 'text-align: left;' }
        %strong Delivery fee
      %td{ style: 'text-align: right;' }
        = totals.customer_delivery
    %tr
      %td{ style: 'text-align: left;' }
        %strong GST
      %td{ style: 'text-align: right;' }
        = totals.customer_gst
    - if totals.customer_topup.present?
      %tr
        %td{ style: 'text-align: left;' }
          %strong Topup
        %td{ style: 'text-align: right;' }
          = totals.customer_topup
    - if totals.customer_surcharge.present?
      %tr
        %td{ style: 'text-align: left;' }
          %strong Surcharge
        %td{ style: 'text-align: right;' }
          = totals.customer_surcharge
    %tr
      %td{ style: 'text-align: left;' }
        %strong Total
      %td{ style: 'text-align: right;' }
        = totals.customer_total
