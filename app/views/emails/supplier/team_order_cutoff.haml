%p
  Hi #{firstname},

%p
  Just a quick follow up that a team order started by
  %strong
    %em
      = "`#{team_order.customer_name}`"
  is getting close to cut-off.
  - if cutoff_time == '4hr'
    There are still
    = succeed '.' do
      %strong 4 hrs remaining
  - else
    The order will reach cutoff on
    = succeed '.' do
      %strong
        = cutoff_datetime

%p
  They are still building their order, but the information below is what we have for you now so you can get ready.

%table{ width: '100%' }
  %tbody
    %tr
      %td{ style: 'width: 30%;' }
        %strong Company Name:
      %td
        = team_order.customer_name

    %tr
      %td{ style: 'width: 30%;' }
        %strong Number of invitees:
      %td
        - if team_order.number_of_confirmed_orders > 0
          %strong
            = "#{team_order.number_of_confirmed_orders} ordered"
          = "(out of #{team_order.number_of_invitees})"
        - else
          = team_order.number_of_invitees

    %tr
      %td{ style: 'width: 30%;' }
        %strong Order Total so far:
      %td
        = team_order.total

    %tr
      %td{ style: 'width: 30%;' }
        %strong Delivery Date:
      %td
        = team_order.delivery_at

    %tr
      %td{ style: 'width: 30%; vertical-align: top;' }
        %strong Delivery Address:
      %td
        =raw team_order.delivery_address

%p{ style: 'text-align: center;' }
  = render 'emails/button', text: 'View Order', link: team_order.view_url, kind: :primary, is_last: true

%p
  %strong N.B.
  You will receive the finalised order details at your lead time cut-off.
