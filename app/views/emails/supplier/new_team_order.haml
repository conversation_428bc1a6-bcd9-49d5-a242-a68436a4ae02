%p
  Hi #{firstname},
%p
  You have a
  %strong
    new team order
  for #{team_order.customer_name} to be delivered on #{team_order.date}.
%p
  Attached is an
  %a{ href: team_order.pdf_url, target: '_blank', style: email_link_style } order manifest
  with each individual's name, which will help you with production quantities and any individual order instruction (e.g. no onions).

%p
  Team orders require you to package the products with each individuals name written on their order. You can use the 
  %a{ href: team_order.avery_labels_url, style: email_link_style }
    handy spreadsheet
  to generate labels.

- if team_order.flex_url.present?
  %p
    Also find attached a
    %strong
      %a{ href: team_order.flex_url, target: '_blank', style: email_link_style } JSON file
    to be used to enter order into your Flex Catering App.

- if team_order.has_attendee_levels
  %p
    Make sure to add the 
    %strong level location data 
    on the individual labels.

%p
  %strong
    N.B.
  We will automatically confirm the order for you the day prior to delivery.
  %br/
  %em{ style: 'font-size: 12px' }
    Alternatively, you can contact
    = succeed '.' do
      %a{ href: orders_email, style: email_link_style }<
        Yordar

- if has_commission
  %p
    %strong N.B.
    Pricing now displayed on the order form is your supplied pricing; Any Yordar discounted total will be calculated at the bottom. This will make it easier for you to spot pricing discrepancies moving forward.

%p{ style: 'text-align: center;' }
  = render 'emails/button', text: 'View', link: team_order.view_url, kind: :bordered, displacement: 16
  = render 'emails/button', text: 'Confirm', link: team_order.confirm_url, kind: :primary, displacement: 16
  = render 'emails/button', text: 'Reject', link: team_order.reject_url, kind: :secondary, is_last: true

