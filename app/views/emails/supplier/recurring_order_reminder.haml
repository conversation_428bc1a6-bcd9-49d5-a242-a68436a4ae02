%p
  Hi #{firstname},

%p
  This is a courtery reminder of an upcoming recurring order
  %a{ href: order.view_url, style: email_link_style}
    = "##{order.id}"
  for
  %strong
    = order.customer_name
  that needs to be delivered on 
  = succeed '.' do
    %strong
      = order.delivery_at

%p{ style: 'margin-top: 20px; margin-bottom: 0; text-align: center;' }
  = render 'emails/button', text: 'View Order', link: order.view_url, kind: :bordered, displacement: 16, is_last: order.pdf_url.blank?

  - if order.pdf_url.present?
    = render 'emails/button', text: 'View PDF', link: order.pdf_url, kind: :bordered, is_last: true

%p
  %strong
    N.B.
  We will automatically confirm the order for you the day prior to delivery.
  %br/
  Alternatively, you contact
  %a{ href: orders_email, style: email_link_style}
    Yordar
  to get more info.
      
- if has_commission
  %p
    %strong N.B.
    Pricing now displayed on the order form is your supplied pricing; Any Yordar discounted total will be calculated at the bottom. This will make it easier for you to spot pricing discrepancies moving forward.