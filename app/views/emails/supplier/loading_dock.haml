%p
  Hi #{first_name},

%p
  The customer (#{order.customer_name}) for order ##{order.id} to be delivered on #{order.date}, has provided Loading Dock Access Code.

- if loading_dock.code.present?
  %p
    Code:
    %strong{ style: 'font-size: 20px' }
      = loading_dock.code

- if loading_dock.file_url
  %p
    = loading_dock.code.present? ? 'Also' : 'Please'
    find an attached loading dock file, provided by the customer.

%p{ style: 'margin-top: 20px; margin-bottom: 0; text-align: center;' }
  = render 'emails/button', text: 'View Loading Dock Code', link: loading_dock.show_url, kind: :primary, is_last: true

%p
  %strong
    N.B.
  You can contact
  %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }
    #{orders_email}
  for any inquiries.
  