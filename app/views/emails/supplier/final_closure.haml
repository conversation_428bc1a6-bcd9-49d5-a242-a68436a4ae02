%p
  Dear #{firstname},
%p
  Hope you are well. Not long now till the Holidays!!!

%p
  This email is to notify you of the
  %strong
    skipped orders 
  for the Christmas and New Year holiday break with the closure dates from 
  = succeed '.'  do
    %strong<
      = "#{supplier.close_from} - #{supplier.close_to}"

  %table{ width: '100%' }
    %tr
      %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative; text-align: left' }
        Company name
      %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;' }
        Order name
      %th{ style: 'font-weight: bold; padding-right: 20px; border-bottom: 2px solid #ededed; position: relative;' }
        Delivery Address
        
    - orders.each do |order|
      %tr
        %td{ style: 'padding-right: 20px; position: relative;' }
          = order.company_name
        %td{ style: 'padding-right: 20px; position: relative; text-align: center' }
          = order.name
        %td{ style: 'padding-right: 20px; position: relative; text-align: center' }
          = order.delivery_address
