:ruby
  is_multi_day_summary = summary_documents.size > 1
%p
  Hi #{firstname},


- order_dates.each do |order_date|
  %p
    Following #{order_date.total_orders} #{'order'.pluralize(order_date.total_orders)} need to be delivered for
    = succeed '.' do
      %strong
        = order_date.date

  %table{ width: '100%', style: 'padding-bottom: 10px;' }
    - order_date.orders.each do |order|
      %tr
        %td{ style: 'padding: 0 20px; position: relative; left: -2px; width: 10%;' }
          = render 'emails/circle_image', name: order.customer_name , dimensions: 30
        %td{ style: 'padding-right: 20px; position: relative; left: -2px; width: 70%' }
          %strong
            = order.customer_name
        %td{ style: 'text-align: right;' }
          = order.delivery_time
      %tr
        %td{ style: 'border-bottom: 1px solid #ededed; padding: 0 20px; position: relative; left: -2px; width: 10%;' }
          &nbsp;
        %td{ colspan: 2, style: 'border-bottom: 1px solid #ededed; width: 100%;' }
          - if order.is_recurrent
            (S)
          Order:
          %a{ href: order_date.summary_url, style: email_link_style }
            = "#{order.name} - ##{order.id}"

    - if order_date.remaining_orders > 0
      %tr
        %td{ colspan: 3, style: 'text-align: center;' }          
          \...view the rest #{order_date.remaining_orders} #{'order'.pluralize(order_date.remaining_orders)} in the
          = succeed '.' do
            %a{ href: order_date.summary_url, style: email_link_style }
              summary document

%p{ style: 'text-align: center;' }
  - summary_documents.each_with_index do |summary_document, idx|
    - button_label = is_multi_day_summary ? "#{summary_document.day} Orders" : 'View PDF'
    = render 'emails/button', text: button_label, link: summary_document.pdf_url, kind: :primary, is_last: idx == (summary_documents.size - 1), displacement: is_multi_day_summary ? 16 : 0

%p
  If there are any issues with fulfilling any order on this list, please notify us as soon as possible at
  = succeed '.' do
    %a{ href: "mailto:#{orders_email}?subject=#{@email_subject}", style: email_link_style }<
      = orders_email
