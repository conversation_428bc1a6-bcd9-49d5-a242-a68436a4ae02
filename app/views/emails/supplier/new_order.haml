%p
  Hi #{first_name},

- if order.is_recurring
  %p
    A new
    %a{ href: order.pdf_url, target: '_blank', style: email_link_style }
      %strong
        #{order.recurrent_type} recurring order
    has been placed for 
    %em
      = "`#{order.customer_name}`"
    to recur every #{order.day}, starting #{order.date}.
  %p
    There may be other days for this order which will come separately.

- else
  %p
    Please find attached a new
    %strong
      %a{ href: order.pdf_url, target: '_blank', style: email_link_style } one-off order
    for 
    %em
      = "`#{order.customer_name}`"
    to be delivered on #{order.date}.

- if order.flex_url.present?
  %p
    Also find attached a
    %strong
      %a{ href: order.flex_url, target: '_blank', style: email_link_style } JSON file
    to be used to enter order into your Flex Catering App.    

- if order.is_contactless_delivery
  %p
    Note:
    %strong
      This is a contactless delivery order. Please refer to delivery instructions in
      %a{ href: order.pdf_url, target: '_blank', style: email_link_style } attached document
      for more info.

%p{ style: 'margin-top: 20px; margin-bottom: 0; text-align: center;' }
  = render 'emails/button', text: 'View Order', link: order.view_url, kind: :bordered, displacement: 16
  = render 'emails/button', text: 'Confirm Order', link: order.confirm_url, kind: :primary, displacement: 16
  = render 'emails/button', text: 'Reject Order', link: order.reject_url, kind: :secondary, is_last: true

%p
  %strong
    N.B.
  We will automatically confirm the order for you the day prior to delivery.
  %br/
  Alternatively, contact
  = succeed '.' do
    %a{ href: orders_email, style: email_link_style}<
      Yordar
      
- if has_commission
  %p
    %strong N.B.
    Pricing now displayed on the order form is your supplied pricing; Any Yordar discounted total will be calculated at the bottom. This will make it easier for you to spot pricing discrepancies moving forward.
