%p
  Hi #{firstname},
%p
  The following orders are skipped/moved due to a public holiday on
  = succeed '.' do
    %strong
      = holiday.date

%table{ width: '100%' }
  %tr
    %th{ style: 'text-align: left; border-bottom: 1px solid #ededed;' }
      Order ID
    %th{ style: 'text-align: center; border-bottom: 1px solid #ededed;' }
      Order Name
    %th{ style: 'text-align: center; border-bottom: 1px solid #ededed;' }
      Customer Name
    %th{ style: 'text-align: right; border-bottom: 1px solid #ededed;' }
      Status

  - handled_orders.each do |order|
    %tr
      %td{ style: 'text-align: left;' }
        %a{ href: order.link, style: email_link_style }
          = "##{order.id}"
      %td{ style: 'text-align: center;' }
        = order.name
      %td{ style: 'text-align: center;' }
        = order.customer_name
      %td{ style: 'text-align: right;' }
        - if order.skip
          Skipped
        - else
          Pushed to
          %br/
          = order.delivery_at
