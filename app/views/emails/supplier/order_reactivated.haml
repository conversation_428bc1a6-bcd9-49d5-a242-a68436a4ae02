%p
  Hi #{firstname},
%p
  The
  %a{ href: order.pdf_url, target: '_blank', style: email_link_style }
    standing order

  that falls on

  - if reactivate_mode != 'one-off'
    %strong>
      = order.days
    , effective

  %strong
    = order.date

  for

  %em
    = "`#{order.customer_name}` "

  has been

  = succeed '.' do
    %strong
      = order.status

%p
  This customer is expecting deliveries for this order
  - if reactivate_mode == 'one-off'
    on that day. This does not affect some of the other orders that might still be on hold.
  - else
    from that day onwards.

%p
  If in doubt please check your upcoming orders in your
  = succeed '.' do
    %a{ href: profile_url, style: email_link_style }<
      Yordar Supplier Profile

%p
  Please see 
  %a{ href: order.pdf_url, style: email_link_style }
    attached PDF 
  to confirm delivery locations affected by this change.
