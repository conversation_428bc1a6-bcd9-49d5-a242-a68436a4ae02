:ruby
  accounts_email = yordar_credentials(:yordar, :accounts_email)

%p
  Dear #{firstname},
%p
  This is a recipient generated invoice generated by <PERSON><PERSON><PERSON> on behalf of your company for orders that have been completed between 
  %strong #{delivery_times.starts}
  and
  = succeed '.' do
    %strong #{delivery_times.ends}

%p
  = render 'emails/button', text: 'View Invoice', link: pdf_url, kind: :primary, is_last: true

%p
  Please review and check that the attached figures are correct in the next 48 hours and respond to
  %a{ href: "mailto:#{accounts_email}?subject=#{@email_subject}", style: email_link_style }
    = accounts_email
  promptly if you have any queries.

%p
  If you see an issue with one or more order totals on the document, please make sure to attach the invoice from your system, that you believe to be accurate.

%p
  If there are no issues, there is NO need for you to invoice Yordar for any works, we will automatically make payment into your account based on the attached document.

%p
  Our payments to you for this invoice will be procesed by
  = succeed '.' do
    %strong #{delivery_times.due}

%p Thank you for all your effort and support, we really appreciate it!
