%p
  Hi #{firstname},
%p
  The
  %a{ href: order.pdf_url, target: '_blank', style: email_link_style }
    = order.type
    order

  that falls on

  - if cancel_mode != 'one-off'
    %strong>
      = order.days
    , effective

  %strong
    = order.date

  for

  %em
    = "`#{order.customer_name}` "

  has been

  = succeed '.' do
    %strong
      = order.status

%p
  - if cancel_mode == 'related'
    This customer will no longer require deliveries for this order from that day onwards.
  - elsif cancel_mode == 'on-hold'
    This customer will no longer require deliveries for this order from that day onwards, until further notice.
  - else
    Please note this customer may have other orders which remain unaffected by this cancellation.

  In doubt please visit
  = succeed '.' do
    %a{ href: profile_url, style: email_link_style }<
      Yordar Supplier Dashboard

- if ['one off', 'standing'].include?(order.type) && order.pdf_url.present?
  %p
    Please see 
    %a{ href: order.pdf_url, style: email_link_style }
      Order Document (pdf)
    to confirm delivery location(s) affected by this change.
