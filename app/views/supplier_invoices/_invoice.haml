:ruby
  supplier ||= @supplier
  idx ||= rand(1..4)
  orders = invoice.orders
  colors = ['#ff628d66', '#2bffc666', '#24bbff66', '#ffc62666']

  customer_names = orders.map{|order| customer = order.customer_profile; (customer.company.try(:name) || customer.company_name) }.uniq.sort.join(', ')
  order_ids = orders.map{|order| "##{order.id}" }.join(' ')

  date_format = supplier.billing_frequency == 'weekly' ? '%a, %d %b %Y' : '%d %b %Y'

  is_due = invoice.payment_status == 'unpaid' && invoice.due_at >= (Time.zone.now.beginning_of_day + 1.days)

.supplier-invoice.customer-invoice{ data: { invoice_id: invoice.id  } }
  .list-flex-2.invoice-header
    %span.circle-icon{ style: "background: #{colors[idx%4]}; " }
      = invoice.created_at&.to_s(:day_month)

    %strong.invoice-date{style: "--icon-background: #{colors[idx%4]};"}= invoice.created_at&.to_s(:full_date)
  .list-flex-2.searchable
    - if invoice.number.include?('old-rgi')
      = invoice.number.remove('old-rgi-')
    - else
      = "##{invoice.number}"

  .list-flex-2.searchable{ title: orders.size > 1 ? order_ids : customer_names, style: 'cursor: help; color: #9f9f9f', data: { searchable: order_ids + ' ' + customer_names, view_tooltip_el: true }}
    - if orders.present?
      = orders.size > 1 ? 'Multiple Orders 📄' : order_ids
    - else
      = '-'

  .list-flex-3
    #{invoice.from_at.strftime(date_format)} - #{invoice.to_at.strftime(date_format)}

  .list-flex-3.text-center
    #{invoice.due_at.to_s(:date_verbose)}
      
  .list-flex-1
    = invoice.amount.present? && invoice.amount > 0 ? number_to_currency(invoice.amount, precision: 2) : '-'

  .list-flex-2
    - if latest_document = invoice.documents.last.presence
      = link_to latest_document.url, target: '_blank' do
        - if invoice.number.include?('old-rgi')
          Download
        - else
          = "##{invoice.number}"

