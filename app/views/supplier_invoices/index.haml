- content_for :header_title, 'Payments'
- content_for :container_class, 'dashboard-table'
- content_for :webpack, 'supplier'

%div{ data: { view_supplier_invoices: true } }
  
  %input{ placeholder: 'Search invoices by number, customer name or order id', type: 'search', class: 'search-invoices', style: 'max-width: 500px' }/
  
  .invoice-list
    .customer-invoices__headings
      %span.list-flex-2 Invoice Date
      %span.list-flex-2 Number
      %span.list-flex-2 Orders
      %span.list-flex-3 Order Dates
      %span.list-flex-3.text-center Due on
      %span.list-flex-1 Amount
      %span.list-flex-2 Download
      
    - @supplier_invoices.each_with_index do |invoice, idx|
      = render 'supplier_invoices/invoice', invoice: invoice, idx: idx

    = paginate @supplier_invoices
