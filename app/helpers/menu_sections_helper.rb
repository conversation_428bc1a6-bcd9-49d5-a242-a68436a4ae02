module MenuSectionsHelper

  def category_group_info(category_group)
    category_info = {
      'catering-services' => {
        label: 'Catering Services Categories',
        descirption: 'used to tag menus associated to food catering',
      },
      'kitchen-supplies' => {
        label: 'Kitchen Supplies Categories',
        descirption: 'used to tag menus associated to office pantry supplies like milk, fruit, snack, etc',
      },
      'work-from-home' => {
        label: 'Work From Home Categories',
        descirption: 'used to tag menus associated to items that can be delivered to employees working from home, etc',
      }
    }
    category_info[category_group].presence || {}
  end

  def companies_for(menu_section)
    companies = menu_section.companies
    return nil if companies.blank?

    list = 'restricted to: <br/><ul>'
    companies.each do |company|
      list += "<li>#{company.name}</li>"
    end
    list
  end

end

