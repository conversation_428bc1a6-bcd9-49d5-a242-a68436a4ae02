module ApplicationHelper

	# =============== Session based helper methods =============
	def session_profile(profile_id = nil)
		# session_profile ( replaces the session[:profile] object )
		profile = nil
		if profile_id.present?
			session[:profile_id] = profile_id
		else
			profile_id = session[:profile_id]
		end

		if profile_id.present?
			profile = Profile.find(profile_id).profileable
		end

		profile	# return profile object
	end

	def session_order(order_id = nil)
		# session_order ( replaces the session[:order] object )
		order = nil
		if order_id.present?
			session[:order_id] = order_id
		else
			order_id = session[:order_id]
		end

		if order_id.present?
			order = Order.find(order_id)
			# If order has cancelled then we should remove order from session_order.
			order = nil if order.status == 'cancelled'
		end
		order	# return order object
	end

	def session_order_ids(ids = [])
		# session_order ( replaces the session[:order] object )
		if ids.present?

			session[:orders_id] = ids.join(',')

		elsif session[:orders_id].present?

			ids = session[:orders_id].split(',')
		end

		ids
	end

	def clean_up_session_orders
		session.delete('orders_id') if session[:orders_id].present?
		session.delete('order') if session[:order].present?
		session.delete('order_id') if session[:order_id].present?
	end

	def is_yordar_admin?(user: current_user)
		user&.admin? || user&.super_admin?
	end

	def is_admin?(user: current_user)
		user.present? && (is_yordar_admin?(user: user) || is_pantry_manager?(user: user) || user.can_access_suppliers?)
	end

	def is_invoice_admin?(user: current_user)
		is_admin?(user: user) || session[:original_admin_id].present?
	end

	def is_account_manager?(user: current_user)
		customer_profile = customer_profile_from(user: user)
		return false if customer_profile.blank?

		customer_profile.company_team_admin? && customer_profile.active_admin_access_permissions.where(scope: 'account_manager').present?
	end

	def is_pantry_manager?(user: current_user)
		customer_profile = customer_profile_from(user: user)
		return false if customer_profile.blank?

		customer_profile.company_team_admin? && customer_profile.active_admin_access_permissions.where(scope: 'pantry_manager').present?
	end

	def is_company_team_admin?(user: current_user)
		customer_profile = customer_profile_from(user: user)
		return false if customer_profile.blank?

		customer_profile.company_team_admin?
	end

	def is_potential_staff?(user: current_user)
	  customer_profile = customer_profile_from(user: user)
	  return false if customer_profile.blank?

	  CustomerProfile::VALID_STAFF_ROLES.include?(customer_profile.role)
	end

	def can_access_admin_portal?(user: current_user)
		is_admin?(user: user) || is_pantry_manager?(user: user) || is_company_team_admin?(user: user) || is_potential_staff?(user: user)
	end

	def request_country_code
		return 'au' if request&.host.blank? || request.host.exclude?('.')

		request.host.split('.').last.downcase
	end

	def cookie_suburb
	  @_cookie_suburb ||= cookies.present? && cookies[:yordar_suburb_id].present? ? Suburb.where(id: cookies[:yordar_suburb_id]).first : nil
	end

	def flash_message(type, text)
	  flash[type] ||= []
	  flash[type] << text
	end

	def customer_profile_from(user: current_user)
		return nil if user.blank?

		return @_user_profile if defined? @_user_profile

		@_user_profile = begin
			user_profile = user.profile&.profileable
			return nil if user_profile.blank? || !user_profile.is_a?(CustomerProfile)

			user_profile
		end
	end

end
