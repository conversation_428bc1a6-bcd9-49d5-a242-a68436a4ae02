module Date<PERSON><PERSON><PERSON>
  def relative_weekday(date, format: 'long')
    today = Time.zone.today
    this_week = date.cweek == today.cweek && date.year == today.year && date > Time.zone.now.beginning_of_day
    next_week = date.cweek == today.cweek + 1 && today.year == date.year
    last_week = date.cweek == today.cweek - 1 && today.year == date.year

    case
    when today == date
      'Today'
    when today.tomorrow == date
      'Tomorrow'
    when today.yesterday == date
      'Yesterday'
    when this_week
      date.strftime('%A') # Wednesday
    when next_week
      date.strftime('Next %A') # Next Wednesday
    when last_week
      date.strftime('Last %A') # Last Wednesday
    when format == 'short'
      date.strftime("%A, %b #{date.day.ordinalize}") # Wednesday, March 23rd
    when format == 'numeric'
      date.strftime('%d/%m/%y') # 23/03/2021
    else
      date.strftime("%A the #{date.day.ordinalize} of %b") # Wednesday the 23rd of March
    end
  end
end
