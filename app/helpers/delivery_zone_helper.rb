module DeliveryZoneHelper

	def operating_days_html(supplier:, delivery_zone:)
		return 'N/A' if delivery_zone.blank?

	 	data = operating_days_data(supplier: supplier, delivery_zone: delivery_zone)
	 	[data].flatten(1).map do |info|
	 		content_tag(:span, info, { style: 'color: #888888' })
	  end.join(', ').html_safe
	end

	def operating_days_data(supplier:, delivery_zone:)
		Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone).call
	end

	def operating_hours(supplier:, delivery_zone:)
		Suppliers::FetchOperatingHours.new(supplier: supplier, delivery_zone: delivery_zone).call
	end

	def operating_days_for(delivery_zone)
		weekdays = Date::DAYNAMES.map(&:first)
		display_days = weekdays.map.with_index do |weekday, widx|
			is_active = delivery_zone.operating_wdays.present? && delivery_zone.operating_wdays[widx] == '1'
			content_tag(:li, class: (is_active ? 'active' : '')) do
				"#{weekday} " # added space for spacing during display
			end
		end
		display_days.rotate(1).join # rotate moves the first element (Sunday) to the end
	end

	def operating_hours_for(delivery_zone)
		display_hours = delivery_zone.operating_hours_start.present? ? Time.at(delivery_zone.operating_hours_start).utc.to_s(:time_only) : '-'
		display_hours += ' to '
		display_hours += delivery_zone.operating_hours_end.present? ? Time.at(delivery_zone.operating_hours_end).utc.to_s(:time_only) : '-'
		display_hours
	end

end
