# Deprecated code used only when importing menu in Supplier Profile
module ImportHelper

	# return true if the input array is empty, or contains only nil or empty string
	def is_data_empty?(array)
		(array - ['', nil]).blank?
	end

	# create/delete category_menu_sections
 	# input:
	# 	row - a single row from spreadsheet,
	# 	head - header of spreadsheet,
	# 	ms_id - menu_section id
	# 	first_category - first category name
	# 	last_category - last category name
	#
 	def import_category(row, head, ms_id, first_category, last_category)
		# get column numbers (indices) of categories
		first = head.index(first_category)
		last = head.index(last_category)

		(first..last).each do |i|
			cate_id = Category.where(name: head[i]).first.id
			# if user didn't type in anything for category, default value will be set to false
			row[i] = 'false' if row[i].nil?
			if row[i].downcase == 'true' || row[i].to_i == 1
				if MenuSection.find(ms_id).category_menu_sections.where(category_id: cate_id).blank?
					# for value 'true' or 1, create a new category_menu_section unless it exists already
					MenuSection.find(ms_id).categories << Category.find(cate_id)
				end
			else
				# remove the category_menu_section
				MenuSection.find(ms_id).category_menu_sections.where(category_id: cate_id).destroy_all
			end
		end
 	end

	# create/update menu_section
	# input: row - a single row from spreadsheet, head - header of spreadsheet, s_id - supplier id, row_no - row number
	# 	first_category - first category name
	# 	last_category - last category name
	# returns [menu_section, error_message]
	#
	def import_menu_section(row, head, s_id, row_no, first_category, last_category)
		# getting values
		id = row[head.index('MenuSection.id')]
		name = row[head.index('MenuSection.name')]
		data = [id, name]
		if !is_data_empty?(data)
			# if data has an id or at least one attribute
			existing_menu_section = MenuSection.where(name: name, supplier_profile_id: s_id, archived_at: nil).first
			case
			when id.blank? && existing_menu_section.nil?
				# when creating a new menu section
				ms = MenuSection.create(name: name, supplier_profile_id: s_id, weight: row_no)
				# create/delete category_menu_sections
				import_category(row, head, ms.id, first_category, last_category)
			when id.present? && existing_menu_section.nil?
				# when there should be a new menu section that came from modifying an existed menu section's name
				ms = MenuSection.create(name: name, supplier_profile_id: s_id, weight: row_no)
				# create/delete category_menu_sections
				import_category(row, head, ms.id, first_category, last_category)
			when existing_menu_section.present?
				# Only update weight
				ms = existing_menu_section
				ms.update(weight: row_no)
				import_category(row, head, ms.id, first_category, last_category)
			when existing_menu_section.nil?
				# if id belongs to another supplier
				# error message
				return [nil, "Import Error: MenuSection.id on row #{row_no} is invalid"]

			end
			[ms, nil]
		else
			# if no data
			[nil, nil]
		end
	end

	# create/update menu_item
	# input: row - a single row from spreadsheet, head - header of spreadsheet, s_id - supplier id, row_no - row number
	# 	first_category - first category name
	# 	last_category - last category name
	# returns [menu_item, error_message]
	#
	def import_menu_item(row, head, s_id, row_no, first_category, last_category)
		# getting values
		id = row[head.index('MenuItem.id')]
		name = row[head.index('MenuItem.name')]
		desc = row[head.index('MenuItem.description')]
		price = row[head.index('MenuItem.price')]
		is_gst_free = row[head.index('MenuItem.is_gst_free')]
		mini = row[head.index('MenuItem.minimum_quantity')]
		sub_qty = row[head.index('MenuItem.sub_quantity')]
		vegan = row[head.index('MenuItem.is_vegan')]
		veget = row[head.index('MenuItem.is_vegetarian')]
		gluten = row[head.index('MenuItem.is_gluten_free')]
		visible = row[head.index('MenuItem.is_visible')]
		team_order_only = row[head.index('MenuItem.team_order_only')]
		team_order = row[head.index('MenuItem.team_order')]
		hidden = visible == 'false' ? 'true' : 'false'

		data = [id, name, desc, price, mini, sub_qty, vegan, veget, gluten]

		ss_name = row[head.index('ServingSize.name')]
		ss_price = row[head.index('ServingSize.price')]
		if ss_name.present? && ss_price.present?
			# If there is a serving size then the menu item cannot have a price
			price = nil
		end

		if !is_data_empty?(data)
			# if data has an id or at least one attribute
			ms = import_menu_section(row, head, s_id, row_no, first_category, last_category)

			# Find an existing menu item based on EITHER the id or name matching
			existing_menu_item =
					case
					when id.present?
						MenuItem.find_by_id(id)
					when name.present? && ms[0].id.present?
						MenuItem.where(menu_section_id: ms[0].id, name: name).first
					else
						nil
					end

			# if is_gst_free is not set, default value to false
			is_gst_free = false if is_gst_free.blank?
			case
			when id.blank? && existing_menu_item.nil?
				# when creating a new menu item
				[MenuItem.create(name: name, supplier_profile_id: s_id, is_gst_free: is_gst_free, description: desc,
					price: price, minimum_quantity: mini, sub_quantity: sub_qty, is_vegan: vegan, is_vegetarian: veget, is_gluten_free: gluten,
					menu_section: ms[0], weight: row_no, is_hidden: hidden, team_order_only: team_order_only, team_order: team_order), ms[1]]
			when id.present? && existing_menu_item.nil?
				# when there should a new menu item that came from modifying an existed menu item's name
				mi = MenuItem.create(name: name, supplier_profile_id: s_id, description: desc, price: price,
					is_gst_free: is_gst_free, minimum_quantity: mini, sub_quantity: sub_qty, is_vegan: vegan, is_vegetarian: veget,
					is_gluten_free: gluten, menu_section: ms[0], weight: row_no, is_hidden: hidden,
					team_order_only: team_order_only, team_order: team_order)
				mi.touch
				[mi, ms[1]]
			when existing_menu_item.present?
				# we are only updating the existed menu item
				mi = existing_menu_item

				# use assign attributes so it only does update database call once, then price changes will be captured in after_commit
				mi.assign_attributes(name: name, supplier_profile_id: s_id, description: desc, price: price,
					is_gst_free: is_gst_free, minimum_quantity: mini, sub_quantity: sub_qty, is_vegan: vegan, is_vegetarian: veget,
					is_gluten_free: gluten, menu_section: ms[0], is_hidden: hidden, team_order_only: team_order_only, team_order: team_order)
				# only update the menu item's weight when it hasn't got assigned yet since we only follow the first menu item's weight
				mi.assign_attributes(weight: row_no) if mi.weight.nil?
				mi.save!
				mi.touch
				[mi, ms[1]]
			when existing_menu_item.nil?
				# if id belongs to another supplier
				# error message
				[nil, "Import Error: MenuItem.id on row #{row_no} is invalid"]
			end
		else
			# if no data
			[nil, import_menu_section(row, head, s_id, row_no, first_category, last_category)[1]]
		end
	end

	# ENTRY function to importing menu
	#
	# create/update serving_size
	# input: row - a single row from spreadsheet, head - header of spreadsheet, s_id - supplier id, row_no - row number
	# 	first_category - first category name
	# 	last_category - last category name
	# returns [serving_size, error_message]
	#
 	def import_serving_size(row, head, s_id, row_no, first_category, last_category)
		# getting values
		id = row[head.index('ServingSize.id')]
		name = row[head.index('ServingSize.name')]
		price = row[head.index('ServingSize.price')]
		data = [id, name, price]
		price = 0 if (id.present? || name.present?) && price.blank? # If they have a serving size it must have a price

		if !is_data_empty?(data)
			# if data has an id or at least one attribute
			mi = import_menu_item(row, head, s_id, row_no, first_category, last_category)
			if mi[0].present? # if menu item exists
				if id.blank? # if id is not given, create new serving size
					[ServingSize.create(name: name, price: price, menu_item_id: mi[0].id, weight: row_no), mi[1]]
				elsif ServingSize.pluck('id').exclude?(id.to_i) || ServingSize.find(id).menu_item.supplier_profile_id == s_id
					# if it is a new id, or if this id is under a menu item which belong to the supplier
					ss = ServingSize.find_by_id(id)
					# use assign attributes so it only does update database call once, then price changes will be captured in after_commit
					if ss.blank?
						ss = ServingSize.new(name: name, price: price, menu_item_id: mi[0].id, weight: row_no)
						ss.id = id
					else
						ss.assign_attributes(name: name, price: price, menu_item_id: mi[0].id)
						ss.assign_attributes(weight: row_no) if ss.weight.nil?
					end
					ss.save!
					ss.touch
					[ss, mi[1]]
				else
					# if id belongs to another supplier
					# error message for invalid id
					[nil, "Import Error: ServingSize.id on row #{row_no} is invalid"]
				end
			else
				# if menu item is nil
				[nil, "Import Error: ServingSize on row #{row_no} is invalid - can not add serving size without a menu item"]
			end
		else
			# if no data
			[nil, import_menu_item(row, head, s_id, row_no, first_category, last_category)[1]]
		end
 	end

	# create/update menu extra
	# input: row - a single row from spreadsheet, head - header of spreadsheet, s_id - supplier id, row_no - row number
	# 	first_category - first category name
	# 	last_category - last category name
	# returns [menu extra, error_message]
	#
	def import_menu_extra(row, head, s_id, row_no, first_category, last_category)
		# getting values
		id = row[head.index('MenuExtra.id')]
		name = row[head.index('MenuExtra.name')]
		price = row[head.index('MenuExtra.price')]
		data = [id, name, price]
		price = 0 if (id.present? || name.present?) && price.blank? # If they have a menu extra it must have a price

		if !is_data_empty?(data)
			# if data has an id or at least one attribute
			mi = import_menu_item(row, head, s_id, row_no, first_category, last_category)
			if mi[0].present?
				# if menu item exists
				if id.blank?
					# if id is not given, create new serving size
					[MenuExtra.create(name: name, price: price, menu_item_id: mi[0].id, weight: row_no), mi[1]]
				elsif MenuExtra.pluck('id').exclude?(id.to_i) || MenuExtra.find(id).menu_item.supplier_profile_id == s_id
					# if it is a new id, or if this id is under a menu item which belong to the supplier
					me = MenuExtra.find_by_id(id)
					# use assign attributes so it only does update database call once, then price changes will be captured in after_commit
					if me.blank?
						me = MenuExtra.new(name: name, price: price, menu_item_id: mi[0].id, weight: row_no)
						me.id = id
					else
						me.assign_attributes(name: name, price: price, menu_item_id: mi[0].id)
						me.assign_attributes(weight: row_no) if me.weight.nil?
					end
					me.save!
					me.touch
					[me, mi[1]]
				else
					# if id belongs to another supplier
					# error message for invalid id
					[nil, "Import Error: MenuExtra.id on row #{row_no} is invalid"]
				end
			else
				# if menu item is nil
				[nil, "Import Error: MenuExtra on row #{row_no} is invalid - can not add menu extra without a menu item"]
			end
		else
			# if no data
			[nil, import_menu_item(row, head, s_id, row_no, first_category, last_category)[1]]
		end
	end

	# archive untouched data from the database
	def archive_after_import(start, s_id)
		# delete menu items
		menu_items = MenuItem.joins(:menu_section).where('menu_items.menu_section_id = menu_sections.id AND menu_sections.supplier_profile_id = :supplier_id AND menu_items.updated_at < :start', supplier_id: s_id, start: start)
		# all serving sizes will be archived after archiving menu item
		menu_items.each do |menu_item|
			MenuItems::Archive.new(menu_item: menu_item, forced: true).call
		end
		nil
	end

	# FIXME: this method can be deleted as we will archive menu item and serving sizes rather than delete
	# delete untouched data from the database
	def delete_after_import(start, s_id)
		# delete serving sizes that weren't updated, that belong to this supplier
		delete_ss = ServingSize.joins(menu_item: :supplier_profile).where(['serving_sizes.updated_at < :start AND menu_items.supplier_profile_id = :id', start: start, id: s_id])

		# make sure we are not allowed to delete records associated with any orders / rate cards
		msg = handle_records_associated_with_orders(items: delete_ss) || handle_records_associated_with_rate_cards(items: delete_ss)
		return msg if msg.present?

		delete_ss.destroy_all

		# delete menu items
		delete_mi = MenuItem.joins(:menu_section).where('menu_items.menu_section_id = menu_sections.id AND menu_sections.supplier_profile_id = :supplier_id AND menu_items.updated_at < :start', supplier_id: s_id, start: start)

		# make sure we are not allowed to delete records associated with any orders / rate cards
		msg = handle_records_associated_with_orders(items: delete_mi, is_serving_size: false) || handle_records_associated_with_rate_cards(items: delete_mi, is_serving_size: false)
		return msg if msg.present?

		delete_mi.destroy_all

		# delete menu sections when there are no menu items attached to them
		section_without_items = MenuSection.includes(:menu_items).where(menu_items: { id: nil })
		section_without_items.destroy_all
		nil
	end

	# based on the passed over params, find related order lines
	# if there are order lines associated, then not allow to delete the records
	def handle_records_associated_with_orders(items:, is_serving_size: true, is_import: true)
		if is_serving_size.present?
			associated_records = OrderLine.where(serving_size_id: items.pluck(:id))
		else
			associated_records = OrderLine.where(menu_item_id: items.pluck(:id))
		end
		associated_records = associated_records.joins(:order).where('(orders.template_id = orders.id) OR orders.status IN (?)', %w[pending new amended confirmed])

		if associated_records.count > 0
			if is_import
				return "Import error: #{is_serving_size ? "Serving sizes [#{associated_records.pluck(:serving_size_id).uniq.join(', #').insert(0, '#')}" : "Menu items [#{associated_records.pluck(:menu_item_id).uniq.join(', #').insert(0, '#')}"}] have orders associated - can not be deleted. <NAME_EMAIL> for further details."
			else
				# names = is_serving_size ? ServingSize.where(id: associated_records.pluck(:serving_size_id).uniq.join(', ')) : MenuItem.where(id: associated_records.pluck(:menu_item_id).uniq.join(', '))
				return "#{is_serving_size ? 'Serving sizes' : 'Menu items'} [#{name}] can't be deleted since they have orders associated. <NAME_EMAIL> for further details."
			end
		end
		nil
	end

	# based on the passed over params, find related rate cards
	# if there are rate cards associated, then not allow to delete the records
	def handle_records_associated_with_rate_cards(items:, is_serving_size: true, is_import: true)
		if is_serving_size.present?
			associated_records = RateCard.where(serving_size_id: items.pluck(:id))
		else
			associated_records = RateCard.where(menu_item_id: items.pluck(:id))
		end
		if associated_records.count > 0
			if is_import
				return "Import error: #{is_serving_size ? "Serving sizes [#{associated_records.pluck(:serving_size_id).uniq.join(', #').insert(0, '#')}" : "Menu items [#{associated_records.pluck(:menu_item_id).uniq.join(', #').insert(0, '#')}"}] have rate cards associated - can not be deleted. <NAME_EMAIL> for further details."
			else
				# names = is_serving_size ? ServingSize.where(id: associated_records.pluck(:serving_size_id).uniq.join(', ')) : MenuItem.where(id: associated_records.pluck(:menu_item_id).uniq.join(', '))
				return "#{is_serving_size ? 'Serving sizes' : 'Menu items'} [#{name}] can't be deleted since they have rate cards associated. <NAME_EMAIL> for further details."
			end
		end
		nil
	end
end
