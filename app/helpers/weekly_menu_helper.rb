module WeeklyMenuHelper

  def can_rate_this_meal?(week_of, day)
    day_week = week_of.to_date.cweek
    today = Date.today
    day_week < today.cweek || (day_week == today.cweek && Date.parse(day.to_s).wday <= today.wday)
  end

  def week_filter_options
    beginning_of_week = Time.zone.now.beginning_of_week.to_date
    {
      'This Week' => beginning_of_week,
      'Last Week' => beginning_of_week - 1.week,
      'All' => 'all',
    }
  end
end
