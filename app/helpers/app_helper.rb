module <PERSON>pp<PERSON>el<PERSON>

  # checks if there is a of given name
  def class_exists?(name)
    begin
      true if Kernel.const_get(name)
    rescue NameError
      false
    end
  end

  def is_active_path(*paths)
    paths.any?{|path| current_page?(path) } ? 'active' : ''
  end

  def should_be_hidden(*paths)
    paths.any?{|path| current_page?(path) } ? '' : 'hidden'
  end

end

module RailsAdmin
  module ApplicationHelper
    # this function has customized the rails_admin's 'bulk_menu' function
    #
    def bulk_menu_tool(abstract_model = @abstract_model)
      actions = actions(:bulkable, abstract_model)
      return '' if actions.empty?

      content_tag :div, { class: 'dropdown tool', style: '' } do
        content_tag(:a, { class: 'dropdown-toggle', 'data-toggle': 'dropdown', href: '#' }) { t('admin.misc.bulk_menu_title').html_safe + '<b class="caret"></b>'.html_safe } +
          content_tag(:ul, class: 'dropdown-menu', style: 'left:auto; right:0;') do
            actions.map do |action|
              content_tag :li do
                link_to wording_for(:bulk_link, action), '#', onclick: "jQuery('#bulk_action').val('#{action.action_name}'); jQuery('#bulk_form').submit(); return false;"
              end
            end.join.html_safe
          end
      end.html_safe
    end

    # this function has customized the rails_admin's 'menu_for' function
    # instead of rendering ul-li, it renders divs with anchor tags in them
    # parent => :root, :collection, :member
    # perf matters here (no action view trickery)
    def tools_for(parent, abstract_model = nil, object = nil, only_icon = false)
      # in case of emergency return original!
      # return menu_for(parent, abstract_model, object, only_icon)

      hide_tools_for_views = %w[new import export bulk_action form_report_all nestable]
      # don't show any buttons when in these views of an object
      if hide_tools_for_views.include?(params[:action].to_s) || parent == :member
        return ''
      end

      actions = actions(parent, abstract_model, object).select{ |a| a.http_methods.include?(:get) }
      actions.map do |action|
        wording = wording_for(:menu, action)

        # if this is 'collection' and we have received an action of 'List' ignore rendering it..
        # don't show the button for current action
        # don't show any button for index/List
        if params[:action].to_s == action.action_name.to_s || action.action_name == :index
          next
        end

        %(
          <div title="#{wording if only_icon}" rel="#{'tooltip' if only_icon}" class="tool icon #{action.key}_#{parent}_link #{'active' if current_action?(action)}">
            <a class="#{action.pjax? ? 'pjax' : ''}" href="#{url_for({ action: action.action_name, controller: 'rails_admin/main', model_name: abstract_model.try(:to_param), id: (object.try(:persisted?) && object.try(:id) || nil) })}">
              <i class="#{action.link_icon}"></i>
              <span#{only_icon ? " style='display:none'" : ''}>#{wording}</span>
            </a>
          </div>
        )
      end.join.html_safe
    end

  end
end
