module MenuHelper

	def category_for_title(supplier, menu_sections)
		if supplier.categories.map(&:group).include?('catering-services')
			' | Office Catering'
		elsif supplier.categories.map(&:group).include?('kitchen-supplies')
			category_names = menu_sections.map { |s| s.categories.pluck(:name) }.flatten
			category_count = {}
			category_names.uniq.each { |c_name| category_count[c_name] = c_name.count(c_name) }
			common_category = category_count.key(category_count.values.max)
			" | #{common_category}"
		end
	end

end
