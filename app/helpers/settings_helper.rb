module Settings<PERSON><PERSON><PERSON>

  def tag_options(field)
    case field
    when :billing_frequency
      SupplierFlags::VALID_BILLING_FREQUENCIES.map{|frequency| [frequency.humanize, frequency] }
    when :payment_term_days
      SupplierFlags::VALID_TERM_DAYS.map{|days| ["#{days} days", days] }
    when :menu_reminder_frequency
      SupplierFlags::VALID_REMINDER_FREQUENCIES.map{|frequency| [frequency.humanize.gsub('.', ' '), frequency] }
    when :requires_department_identity
      CustomerFlags::VALID_DEPARTMENT_ID_FORMATS.map{|format_name| [format_name.capitalize.gsub('-', ' '), format_name] }
    when :accounting_software
      CustomerFlags::VALID_ACCOUNTING_SOFTWARES.map{|format_name| [format_name.capitalize.gsub('-', ' '), format_name] }
    when :default_orders_view
      CustomerFlags::VALID_ORDER_VIEWS.map{|view| [view.capitalize, view] }
    else
      []
    end
  end

end