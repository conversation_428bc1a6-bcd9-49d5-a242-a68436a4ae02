module Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  def team_order_expiry_options(team_order_cutoff:, attendee_confirmed_order: false)
    case
    when team_order_cutoff.team_order.status == 'cancelled'
      { icon: 'expired', text: 'Order Cancelled', grey: true }
    when team_order_cutoff.has_expired?
      { icon: 'expired', text: 'Expired', grey: true }
    when attendee_confirmed_order
      { icon: 'order-placed', text: "Order Placed", color: 'confirmed' }
    when team_order_cutoff.is_today?
      { icon: 'today', text: "#{team_order_cutoff.hours_left} hours left to order", color: 'warning' }
    else
      { icon: 'soon', text: "Get Your Order In Before: #{team_order_cutoff.days_left}d #{team_order_cutoff.hours_left}h", color: 'urgent' }
    end
  end

  def team_order_current_status(team_order)
    now = Time.zone.now
    status = team_order.status
    return status if %w[confirmed delivered cancelled].include?(status)

    lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call
    cutoff_time = lead_time_fetcher.lead_time
    case
    when status == 'pending' && lead_time_fetcher.past_lead_time?
      'expired'
    when status == 'pending' && now < cutoff_time && now >= cutoff_time - 4.hours
      'cutoff_approaching'
    when cutoff_time < now && (cutoff_time + Order::TEAM_ORDER_GRACE_PERIOD) >= now
      'grace_period'
    else
      status
    end
  end
end
