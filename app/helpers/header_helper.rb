module <PERSON>er<PERSON><PERSON><PERSON>
  def auth_options
    options = []

    if is_admin?
      options << { to: 'Admin Panel', path: admin_path, name: current_user.firstname }
    end

    case
    when current_user.blank?
      options << { to: 'Login', path: new_user_session_path }
    when session_profile.instance_of?(CustomerProfile)
      options << { to: session_profile.name, path: customer_profile_path }
    when session_profile.instance_of?(SupplierProfile)
      options << { to: 'Dashboard', path: supplier_profile_path }
    when session_profile.blank?
      options << { to: 'Home', path: prismic_root_path, name: current_user.firstname }
    end

    options
  end

  def catering_options
    [
      { name: 'Office Catering', slug: 'office-catering', class: 'restaurant-icon' },
      { name: 'Staff Meal Program', slug: 'staff-meal-program', class: 'staff-lunch-icon' },
      { name: 'Team Ordering', slug: 'team-ordering', class: 'team-order-icon' },
      { name: 'Event Catering', slug: 'event-catering', class: 'event-icon' }
    ]
  end

  def kitchen_options
    [
      { name: 'Office Snacks', slug: 'office-snacks-pantry', class: 'chocolate-bar-icon' },
      { name: 'Pantry Management', slug: 'pantry-management', class: 'pantry-icon' }
    ]
  end
end
