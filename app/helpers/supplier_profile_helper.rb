module SupplierProfileHelper

  def minimum_category_options(supplier: nil)
    supplier ||= session_profile
    existing_categories = supplier.minimums.map(&:category).uniq
    showable_categories = supplier.categories.where(show_in_menu: true).joins(:menu_sections).where(menu_sections: { archived_at: nil }).distinct
    showable_categories = Category.where(show_in_menu: true, group: Category::SUPPLIER_CATEGORIES).order(:name) if showable_categories.blank?
    showable_categories - existing_categories
  end

  def lead_time_day_before_options
    return @_day_before_options if @_day_before_options.present?

    day_before = []
    (6..18).step(0.5) do |step|
      start = Time.now.beginning_of_day + step.hours
      res = [start.strftime('%H:%M')]
      day_before << res
    end
    @_day_before_options = day_before
  end

  def format_price(price)
    return 'Unknown' unless price

    number_to_currency(price, precision: 0)
  end

  def format_fee(fee, for_api: false)
    case
    when fee.present? && fee > 0
      "#{number_to_currency(fee, precision: 0)} Delivery Fee"
    when fee.blank? && for_api
      nil
    else
      'Free Delivery'
    end
  end

  def supplier_name_helper(format, name = nil)
    full_name = name || session_profile.company_name
    split_name = full_name.split(' ')
    first_name = split_name[0]
    last_name = split_name[1]
    initials = first_name[0]
    initials += last_name[0] if last_name.present?
    initials.upcase!

    case format
    when :full
      full_name
    when :first
      first_name
    when :initials
      initials
    end
  end

end
