module NotificationsHelper

  def templates_with_preferences
    return {} if session_profile.blank?

    session_profile.instance_of?(CustomerProfile) ? customer_notification_templates : supplier_notification_templates
  end

  def customer_notification_templates
    customer_templates = EmailTemplate.where(account_type: 'CustomerProfile').order(position: :asc)
    templates = {
      'Customer Notifications' => customer_templates.select{|template| template.kind == 'order' },
      'Billing Notifications' => customer_templates.select{|template| template.kind == 'billing' },
    }
    if session_profile.present? && session_profile.team_admin?
      templates['Team Order Admin Notifications'] = customer_templates.select{|template| template.kind == 'team_admin' }
      templates['Team Order Attendee Notifications'] = customer_templates.select{|template| template.kind == 'team_order_attendee' }
    end
    templates
  end

  def supplier_notification_templates
    supplier_templates = EmailTemplate.where(account_type: 'SupplierProfile').order(position: :asc)
    {
      'Order Notifications' => supplier_templates.select{|template| template.kind == 'order' },
    }
  end

  def notification_template_for(notification)
    object_based_templates = {
      'SupplierProfile' => 'supplier',
      'CustomerProfile' => 'customer',
      'Invoice' => 'invoice',
      'Order' => 'order',
      'CustomerQuote' => 'customer_quote',
      'Holiday' => 'holiday',
      'CustomerBudget' => 'customer_budget',
    }

    event_based_templates = {
      'orders-auto-confirmed' => 'auto_confirmed_orders',
      'pending-orders' => 'pending_orders',
      'new-customer-registration' => 'customer'
    }

    object_based_templates[notification.loggable_type] || event_based_templates[notification.event]
  end

  def unread_notifications_count
    @_unread_notifications_count ||= begin
      lister_options = { for_user: current_user, favourites_only: current_user&.favourite_customers&.present?, unviewed_only: true, limit: 1000 }
      Admin::ListNotifications.new(options: lister_options).call.count
    end
  end

end
