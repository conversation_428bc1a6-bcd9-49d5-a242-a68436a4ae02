/* loading spinners */
.sk-three-bounce-top-bar {
  position: absolute;
  top: 10px;
  right: 10px;
}

.sk-three-bounce {
  margin: 4px auto;
  width: 80px;
  text-align: center;
}
.sk-three-bounce .sk-child {
  width: 15px;
  height: 15px;
  background-color: #fff;
  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
  animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
}
.sk-three-bounce.black .sk-child {
  background-color: black;
}
.sk-three-bounce .sk-bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.sk-three-bounce .sk-bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
@-webkit-keyframes sk-three-bounce {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes sk-three-bounce {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
.small-bounce {
  margin: 0 auto;
  width: 33px;
  text-align: center;
}
.small-bounce .sk-child {
  width: 9px;
  height: 9px;
}

#woolworths-search-spinner {
  width: 83px;
  height: 36px;
}
.modal-spinner {
  padding: 0.5rem 1.5rem;
  .sk-three-bounce {
    margin: auto;
  }
}
