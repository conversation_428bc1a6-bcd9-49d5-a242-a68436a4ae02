/* modals */
.modal-window {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.8);
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  opacity: 0;
  pointer-events: none;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  transition: all 0.3s;
}

.modal-window:target {
  opacity: 1;
  pointer-events: auto;
  z-index: 999;
}

.modal-window .modal-window-content {
  max-height: 80vh;
  overflow-y: auto;
  width: 700px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1em;
  background: #FDFDFD;
  color: black;
  border-radius: 6px;
  text-align: center;
}
@media only screen and (max-width: 1100px) {
  .modal-window .modal-window-content {
    width: 100%;
  }
}

.modal-window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1em;
  padding-bottom: 0.5em;
  border-bottom: 1px solid #001C00;
}

.modal-window-header .modal-close {
  color: #001C00;
  display: inline-block;
  text-decoration: none;
  font-size: 40px;
  margin-top: 0;
  margin-bottom: 0;
}

.modal-window-header h3 {
  color: #001C00;
  margin-bottom: 0;
}

.modal-window-body {
  text-align: left;
}

.modal-window-footer {
   display: flex;
   align-items: flex-end;
   justify-content: space-between;
   flex-wrap: wrap;
   margin-top: 1em;
}

.modal-window-footer p {
  font-size: 16px;
  line-height: 2;
  margin: 0;
}
