.ui-datepicker {
  font-family: $body-font-family;
  box-shadow: 0px 0px 6px 1px rgba(40, 40, 40, 0.2);
  border: none;
  &-header {
    background: white;
    border: none;
  }
  thead {
    background: white;
    color: black;
    span {
      font-weight: normal;
    }
  }
  th {
    text-transform: uppercase;
    font-size: 11px;
  }
  .ui-state-default {
    border: none;
    text-align: center;
    background: white;
  }
  .ui-state-active,
  .ui-widget-content .ui-state-active {
    background: $black !important;
    color: white;
    border: none;
  }

  .ui-state-multi-select .ui-state-default {
    background: $primary !important;
    color: white;
  }

  .ui-state-multi-unselect .ui-state-default {
    background: transparent !important;
    color: $black;
  }

  .ui-widget-header .ui-icon {
    background-image: asset-data-url('icons/chevron-up-white');
    transform: rotate(-90deg);
    filter: invert(1);
    background-position: 50% 50%;
    background-size: 11px;
  }
  .ui-widget-header .ui-datepicker-next .ui-icon {
    transform: rotate(90deg);
  }
  .ui-state-hover {
    background: #f2f2f2;
  }
  .ui-state-hover.ui-datepicker-prev,
  .ui-state-hover.ui-datepicker-next {
    background: white;
    border: none;
  }
  .ui-datepicker-next-hover {
    top: 2px;
    right: 1px;
  }
  .ui-datepicker-prev-hover {
    top: 2px;
    right: 2px;
  }
  .ui-timepicker-select {
    height: unset;
    font-size: 15px;
    background: white;
    border: 1px solid #f0f0f0;
  }
  button {
    font-family: $body-font-family;
    color: $black;
    &.ui-priority-secondary {
      border: 1px solid $black;
      opacity: 1;
    }
    &.ui-state-hover {
      background: $black;
      color: white;
    }
  }
}
.ui-timepicker {
  font-family: "Museo Sans";
  &-div {
    dt {
      font-size: 15px;
    }
    .ui_tpicker_time_input {
      background-color: transparent;
      border: 0;
    }
    .ui_tpicker_unit_hide {
      display: none;
    }
  }

}

// React Date (Time) Picker styles
.react-datepicker {
  font-family: $body-font-family !important;
  box-shadow: 0px 0px 6px 3px rgba(40, 40, 40, 0.2);
  border: none !important;
  .react-datepicker__day-names {
    text-transform: uppercase;
  }
  .react-datepicker__header {
    background: white;
  }
  .react-datepicker__month-container .react-datepicker__header {
    border-bottom: none;
  }
  .react-datepicker__day--selected, .react-datepicker__time-list-item--selected {
    background: $black !important;
    border-radius: 6px !important;
  }
  .react-datepicker__navigation-icon--previous, .react-datepicker__navigation-icon--next {
    &:before {
      border-color: $black;
      border-width: 2px 2px 0 0;  
    }
  }
  .react-datepicker__day--keyboard-selected {
    background-color: inherit;
    color: inherit;
  }
  .react-datepicker__day--outside-month {
    color: #ccc;
  }
}
