.tool {
    cursor: help;
    position: relative;
    margin-left: 4px;
}
/*== common styles for both parts of tool tip ==*/
.tool::before,
.tool::after {
    left: 50%;
    opacity: 0;
    position: absolute;
    z-index: -100;
    text-transform: initial;
}

.tool:hover::before,
.tool:focus::before,
.tool:hover::after,
.tool:focus::after {
    opacity: 1;
    transform: scale(1) translateY(0);
    z-index: 100; 
}

/*== pointer tip ==*/
.tool::before {
    border-style: solid;
    border-width: 1em 0.75em 0 0.75em;
    border-color: #3E474F transparent transparent transparent;
    bottom: 100%;
    content: "";
    margin-left: -0.5em;
    transition: all .65s cubic-bezier(.84,-0.18,.31,1.26), opacity .65s .5s;
    transform:  scale(.6) translateY(-90%);
} 

.tool:hover::before,
.tool:focus::before {
    transition: all .65s cubic-bezier(.84,-0.18,.31,1.26) .2s;
}


/*== speech bubble ==*/
.tool::after {
    background: #3E474F;
    border-radius: .25em;
    bottom: 180%;
    color: #EDEFF0;
    content: attr(data-tip);
    margin-left: -8.75em;
    padding: 1em;
    transition: all .65s cubic-bezier(.84,-0.18,.31,1.26) .2s;
    transform:  scale(.6) translateY(50%);  
    width: 17.5em;
}

.tool:hover::after,
.tool:focus::after  {
    transition: all .65s cubic-bezier(.84,-0.18,.31,1.26);
}

@media (max-width: 760px) {
  .tool::after { 
        font-size: .75em;
        margin-left: -5em;
        width: 10em; 
  }
}

.whats-this {
    cursor: help;
    text-decoration: underline dotted;
    text-transform: none;
    float: right;
    &__tooltip {
        transition: background-color 0.5s ease;
        position: relative;
        display: inline-block;
        background: #191919;
        box-shadow: none;
        border: none;
        color: white;
        border-radius: 4px;
        padding: 1rem;
        max-width: 250px;
        font-size: 14px;
    }
}

.summary-tooptip {
    background: #191919;
    box-shadow: none;
    border: none;
    color: white;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 14px;
}


