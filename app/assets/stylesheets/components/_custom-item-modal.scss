.custom-item-modal {
  &__content {
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    padding: 0;
  }

  &__title {
    background: $primary;
    color: white;
    margin: 0;
    padding: 1.5rem 2rem;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    border-top: 1px solid #e5e5e5;

    .button {
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      border-radius: 6px;
      transition: all 0.3s ease;

      &.primary-btn {
        background: $primary;
        color: white;
        border: none;

        &:hover:not(:disabled) {
          background: darken($primary, 10%);
          transform: translateY(-1px);
        }

        &:disabled {
          background: #ccc;
          cursor: not-allowed;
        }
      }

      &.gray-btn {
        background: #f8f9fa;
        color: #666;
        border: 1px solid #ddd;

        &:hover:not(:disabled) {
          background: #e9ecef;
          color: #333;
        }
      }
    }
  }
}

.custom-item-form {
  padding: 2rem;

  &__row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;

    &--half {
      grid-template-columns: 1fr 1fr;

      @include media-down(hamburger) {
        grid-template-columns: 1fr;
      }
    }

    &--checkboxes {
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-bottom: 1rem;

      @include media-down(hamburger) {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
    }
  }

  &__field {
    display: flex;
    flex-direction: column;

    label {
      font-weight: 600;
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    input, textarea {
      padding: 0.75rem;
      border: 2px solid #e5e5e5;
      border-radius: 6px;
      font-size: 1rem;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: $primary;
        box-shadow: 0 0 0 3px rgba(31, 158, 134, 0.1);
      }

      &::placeholder {
        color: #999;
      }
    }

    textarea {
      resize: vertical;
      min-height: 80px;
    }
  }

  &__price-display {
    padding: 0.75rem;
    background: #f8f9fa;
    border: 2px solid #e5e5e5;
    border-radius: 6px;
    font-size: 1.2rem;
    font-weight: 600;
    color: $primary;
    display: flex;
    align-items: center;
    justify-content: space-between;

    small {
      font-size: 0.8rem;
      color: #666;
      font-weight: normal;
    }
  }

  &__total-price {
    padding: 0.75rem;
    background: #e8f4fd;
    border: 2px solid #bee5eb;
    border-radius: 6px;
    font-size: 1.3rem;
    font-weight: bold;
    color: #0c5460;
    text-align: center;
  }

  &__checkbox {
    display: flex;
    align-items: center;

    label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      cursor: pointer;
      margin: 0;
      font-weight: 500;

      input[type="checkbox"] {
        width: 18px;
        height: 18px;
        margin: 0;
      }
    }
  }

  &__dietary-flags {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
  }

  &__dietary-flag {
    label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 6px;
      transition: background-color 0.3s ease;
      margin: 0;
      font-weight: normal;

      &:hover {
        background: #f8f9fa;
      }

      input[type="checkbox"] {
        width: 16px;
        height: 16px;
        margin: 0;
      }
    }

    .dietary-flag-label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .dietary-flag-letter {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      background: $primary;
      color: white;
      border-radius: 4px;
      font-size: 0.7rem;
      font-weight: bold;
      flex-shrink: 0;
    }
  }
}

.custom-item-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e5e5;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  &__title {
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid $primary;
    display: inline-block;
  }
}

// Custom Item Card Styling
.custom-item-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px dashed #ddd;
  background: #fafafa;
  position: relative;

  &:hover {
    border-color: $primary;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .custom-item-card__icon svg {
      stroke: $primary;
    }

    .custom-item-card__cta {
      color: $primary;
    }
  }

  .item-title {
    color: #333;
    font-weight: 600;
  }

  .item-description {
    color: #666;
    font-style: italic;
    margin-bottom: 1rem;
  }

  &__icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1rem 0;

    svg {
      stroke: #999;
      transition: stroke 0.3s ease;
    }
  }

  &__footer {
    text-align: center;
    padding: 1rem;
    border-top: 1px solid #eee;
    background: rgba(255, 255, 255, 0.5);
  }

  &__cta {
    font-weight: 600;
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: color 0.3s ease;
  }

  // Override menu-item styles for custom card
  &.menu-item {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

// Responsive adjustments
@include media-down(hamburger) {
  .custom-item-modal {
    &__content {
      max-width: 95vw;
      margin: 1rem;
    }

    &__title {
      padding: 1rem 1.5rem;
      font-size: 1.3rem;
    }
  }

  .custom-item-form {
    padding: 1.5rem;

    &__dietary-flags {
      grid-template-columns: 1fr;
    }
  }

  .custom-item-card {
    &__icon svg {
      width: 36px;
      height: 36px;
    }
  }
}
