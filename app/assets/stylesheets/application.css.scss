@charset 'utf-8';

@import "base/variables-mixins";
@import "base/fonts";

@import "jquery-ui.css";
@import "jquery-ui-timepicker-addon.css";
@import "select2.css";

@import "vendor/foundation/foundation";

/* */
@import "settings";
@include foundation-global-styles;
@include foundation-grid;
// @include foundation-flex-grid;
@include foundation-typography;
@include foundation-button;
@include foundation-forms;
// @include foundation-range-input;
@include foundation-accordion;
@include foundation-accordion-menu;
@include foundation-badge;
@include foundation-button-group;
@include foundation-callout;
@include foundation-close-button;
@include foundation-menu;
@include foundation-menu-icon;
//@include foundation-drilldown-menu;
@include foundation-dropdown;
@include foundation-dropdown-menu;
//@include foundation-flex-video;
@include foundation-label;
// @include foundation-media-object;
// @include foundation-off-canvas;
//@include foundation-orbit;
//@include foundation-pagination;
//@include foundation-progress-bar;
// @include foundation-progress-element;
// @include foundation-meter-element;
//@include foundation-slider;
// @include foundation-sticky;
@include foundation-reveal;
@include foundation-switch;
@include foundation-table;
// @include foundation-tabs;
//@include foundation-thumbnail;
@include foundation-title-bar;
@include foundation-visibility-classes;
@include foundation-float-classes;
// @include foundation-flex-classes;

//@include motion-ui-transitions;
//@include motion-ui-animations;

@import "base/icons";

@import "layout/common";
@import "layout/header";
@import "layout/banner";
@import "layout/main";
@import "layout/footer";
@import "layout/modal";
@import "layout/tables";
@import "layout/off-canvas";
@import "layout/toastify";
@import "layout/dropdown";
@import "layout/form";

@import "page/users";
@import "page/search";
@import "page/order-details";
@import "page/customer-profile";
@import "page/customer-orders";
@import "page/customer-access";
@import "page/customer-my-suppliers";
@import "page/supplier-profile";
@import "page/customer-sidebar";
@import "page/login";
@import "page/checkout";
@import "page/team-order";
@import "page/report";
@import "page/order-review";
@import "page/weekly-menu-review";
@import "page/rating";
@import "page/account";
@import "page/order-show";
@import "page/order-summary";
@import "page/menu_clients";
@import "page/error";
@import "page/success";
@import "page/supplier-menu";
@import "page/suppliers_show";
@import "page/suppliers_menu_dash";
@import "page/team-order/*";
@import "page/dashboard";
@import "page/survey";
@import "page/admin-orders";
@import "page/meal-plans";
@import "page/staff-onboarding";

@import "components/datepicker";
@import "components/modals";
@import "components/spinners";
@import "components/tooltip";
@import "components/custom-item-modal";

@import "vendor/animate";

.hidden {
  display: none;
}

.no-gutter {
  padding: 0 !important;
}

@include media-down(hamburger) {
  .no-gutter-small {
    padding: 0;
  }
}

.react-responsive-modal-root {
  z-index: ************ !important;
}
