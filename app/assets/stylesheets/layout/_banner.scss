.banner-section {
  position: relative;
  &.has-bg-image {
    @include bg-cover;
    height: 1px;
    min-height: 460px;
  }
  &.has-custom-bg-image {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-color: transparent;
    min-height: 540px;
  }
  &.size-large {
    height: 1px;
    min-height: 540px;
  }
  &.color-white {
    color: $white;
  }
}

.banner-cta-block {
  .block-heading {
    font-family: "Museo Sans", sans-serif;
    line-height: 2.875rem;
    font-size: 2.5rem;
  }
  p {
    margin-bottom: 2rem;
    font-size: 16px;
  }
}

.home-page {
  display: grid;
  place-items: center;
  height: 90vh;
  padding: 10px 0;
}

.logos-and-search {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
}

.home-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    flex: 1;
    width: 300px;
    &:first-of-type {
      transform: translateY(-25px);
    }
  }
}

.searchbar {
  margin-top: 2rem;
  input {
    font-size: 16px;
  }
}

@media screen and (max-width: 639px) {
  .banner-cta-block {
    .block-heading {
      br {
        display: none;
      }
    }
  }
}
