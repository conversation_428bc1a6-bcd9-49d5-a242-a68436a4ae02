body {
  min-width: 320px;
  height: auto;
}
.has-gutter {
  padding: $global-padding 0;
}
.has-large-gutter {
  padding: $global-padding * 2 0;
}
.has-extra-large-gutter {
  padding: $global-padding * 3 0;
}
.has-small-gutter {
  padding: 1rem 0;
}
.primary-bg {
  background-color: $primary-color;
  color: $white;
}
.light-gray-bg {
  background-color: $light-gray;
}
.light-blue-gray-bg {
  background-color: $light-blue-gray;
}
.medium-gray-bg {
  background-color: #e4e5e9;
}
.black-bg {
  background: $black;
  color: $white;
}
.between-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  &.no-center {
    align-items: start;
  }
}

.card {
  background: $white;
  border-radius: $global-radius;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.15);
}
.card__container {
  position: relative;
  padding: $global-padding/4;

  @include breakpoint(medium) {
    padding: 2.5rem 2.5rem;
  }
}

.text-input {
  &.large {
    height: rem-calc(50);
    padding: 1rem;
  }
  &.small {
    height: rem-calc(30);
    font-size: rem-calc(14);
  }
}
.button {
  font-weight: normal;
  font-family: "Museo Sans", sans-serif;
  &.large {
    height: 3.125rem;
  }
  &.tiny {
    font-size: rem-calc(12);
    text-transform: uppercase;
    padding: 0.4rem 0.5rem;
    min-width: 65px;
    border-radius: 2px;
  }
  &:active {
    position: relative;
    top: 1px;
  }
  &.border-red {
    background: transparent;
    color: $anchor-color;
    border-color: $anchor-color;
    &:hover {
      background: $anchor-color;
      color: white;
      border-color: $anchor-color;
    }
  }
  &.primary-btn {
    &:hover {
      background: $primary;
      color: white;
    }
    &.bordered {
      background: transparent;
      color: $primary;
      border-color: $primary;
      &:hover {
        background: $primary;
        color: white;
        border-color: $primary;
      }
    }
  }
  &.alert-btn {
    background: $error;
    color: white;
    &:hover {
      background: white;
      color: $error;
      border: 1px solid $error;
    }
  }
  &.border-black {
    background: transparent;
    color: $black;
    border-color: $black;
    &:hover {
      background: $light-gray;
      border-color: $medium-gray;
    }
  }
  &.white-btn {
    background: transparent;
    color: $black;
    border-color: $black;
    &:hover {
      background: $white;
      color: $black;
    }
  }
  &.gray-btn {
    background: #e4e5e9;
    color: $medium-gray;
    border-color: #e4e5e9;
    &:hover {
      color: $black;
    }
  }
  &.black-btn {
    background-color: $black;
    color: $white;
    &:hover {
      background-color: lighten($black, 5%);
    }
    &.outline-button {
      background-color: $white;
      border: 1px solid $black;
      color: $black;
    }
  }
  &.checked-icon {
    &:before {
      content: "";
      width: 15px;
      height: 11px;
      background: url(checked-icon.png) no-repeat 0 0;
      display: inline-block;
      margin-right: 0.75rem;
    }
  }
  &.rounded {
    border-radius: 20px;
  }
}
.label {
  font-weight: 500;
}
.is-dropdown-submenu-parent {
  &.opens-left {
    .is-dropdown-submenu {
      &:before {
        left: auto;
        right: 16px;
      }
    }
  }
}
.is-dropdown-submenu,
.dropdown-pane {
  border: none;
  background: $white;
  -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  border-radius: $global-radius;
  padding: 1rem 0;
  &:before {
    content: "";
    width: 25px;
    height: 12px;
    position: absolute;
    border: 13px solid transparent;
    border-bottom-color: #fff;
    top: -22px;
    left: 55.7%;
    margin-left: -12.5px;
  }
}
.dropdown-pane {
  .menu {
    border-bottom: 1px solid #e0e0e0;
    padding: 0.5rem 0;
    &:first-child {
      padding-top: 0;
    }
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
    li {
      font-size: rem-calc(13);
      font-weight: 500;
      a {
        color: $black;
        transition: all 0.25s ease-out;
        &:hover {
          color: $anchor-color-hover;
        }
      }
      .show-all-linked {
        color: $primary;
        font-weight: bold;
        &:hover {
          color: darken($primary, 5%);
        }
      }
    }
  }
}

.text-bold {
  font-family: "Museo Sans", sans-serif;
}
.text-big {
  font-size: 1.5em;
}

.help-text {
  font-style: normal;
  &.alert {
    color: $anchor-color;
  }
}

.system-message {
  color: $medium-gray;
  strong {
    color: $black;
  }
}

textarea {
  width: 100%;
  min-width: 100%;
  max-width: 100%;
}
input {
  &[type="radio"] {
    opacity: 0;
    position: absolute;
    & + label {
      position: relative;
      padding-left: 1.8rem;
    }
    & + label:before {
      position: absolute;
      left: 0px;
      top: 5px;
      text-align: center;
      line-height: 1;
      border: 1px solid #bababa;
      width: 16px;
      height: 16px;
      margin-right: 0.8rem;
      color: white;
      background: white;
    }
  }
  &[type="radio"] {
    & + label:before {
      content: "";
      border-radius: 50%;
    }
    &:checked + label:after {
      content: "";
      width: 8px;
      height: 8px;
      background: $primary-color;
      border-radius: 50%;
      position: absolute;
      left: 4px;
      top: 9px;
    }
  }
}
select {
  &:focus {
    outline: none;
  }
}

.switch {
  &.tiny {
    .switch-paddle {
      border-radius: 11px;
      &:after {
        border-radius: 7px;
      }
    }
  }
  &.small {
    .switch-paddle {
      border-radius: 14px;
      &:after {
        border-radius: 10px;
      }
    }
  }
}

@media screen and (min-width: 640px) {
  .medium-float-left {
    float: left;
  }
  .medium-float-right {
    float: right;
  }
}

.ajax-loader {
  visibility: hidden;
  background-color: rgba(255, 255, 255, 0.7);
  display: block;
  position: fixed;
  z-index: +100 !important;
  width: 100%;
  height: 1000px;
  background-repeat: no-repeat;
  background-position: center;
}

.ajax-loader img {
  position: fixed;
  top: 50%;
  left: 43%;
}

.uppercase {
  text-transform: uppercase;
}

// This allows for empty foundation columns
.column,
.columns {
  min-height: 1px;
}
.lds-ring-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.lds-ring {
  display: inline-block;
  position: relative;
  width: 100px;
  height: 100px;
}
.lds-ring div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 100px;
  height: 100px;
  margin: 8px;
  border: 8px solid #fff;
  border-radius: 50%;
  animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #191919 transparent transparent transparent;
}
.lds-ring div:nth-child(1) {
  animation-delay: -0.45s;
}
.lds-ring div:nth-child(2) {
  animation-delay: -0.3s;
}
.lds-ring div:nth-child(3) {
  animation-delay: -0.15s;
}
@keyframes lds-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.no-gutter {
  padding: 0;
}

.bullet-list {
  list-style: disc;
}

html.is-reveal-open body {
  overflow: unset;
  .reveal-overlay {
    overflow: hidden;
  }
}
