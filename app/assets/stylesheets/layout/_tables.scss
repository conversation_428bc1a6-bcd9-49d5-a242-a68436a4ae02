/*
	Table layout
*/

.table-order {
  overflow-x: auto;
}

.table-order__table {
  width: 99%;
  min-width: 500px;
  font-size: 14px;
  border-bottom: 1px solid gray;
}

.table-order__header-item {
  font-size: 14px;
  font-weight: 400;
}
.table-order__header-item--fixed-size-small {
  width: 85px;
}
.table-order__header-item--fixed-size-xsmall {
  width: 60px;
}

.table-order__title-row {
  background-color: $white;
  td {
    padding: 0.5rem;
  }
}

.table-order__subtitle-row {
  background-color: $light-gray;
  border-top: 1px solid darken($light-gray, 5%);
}

.table-order__title {
  background: white;
  color: black;
}

.table-order__subtitle {
  margin-bottom: 0;
  font-family: "Museo Sans", arial, sans-serif;
  color: gray;
}

.table-order__item-row {
  border-top: 1px solid darken($light-gray, 5%);
}

.table-order__item {
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1.15;
  &.day {
    text-transform: capitalize;
  }
}

.table-order__item--numeric {
  text-align: right;
  white-space: nowrap;
}
.table-order__item--bold {
  font-family: "Museo Sans", arial, sans-serif;
}

.table-order__item--narrow {
  width: 30px;
}

.table-order__item-notes {
  display: block;
  margin-bottom: 0;
  font-size: 12px;
  color: #9b9b9b;
  line-height: 1.3;
  margin-top: 5px;
  strong,
  span,
  label {
    font-size: 12px !important;
  }
  input[type="text"] {
    display: inline-block;
    background: white;
    margin-bottom: 0;
    width: calc(100% - 50px);
    border: 0.5px solid #ddd;
  }
}
