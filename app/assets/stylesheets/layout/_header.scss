@import "render-icons";
@import "hamburger";

@include hamburger;

.header {
  position: relative;
  z-index: 50;
  box-shadow: 0 0 15px 5px rgba(0, 0, 0, 0.05);
  font-family: $header-font-family;
  color: $black;
  .header-wrap {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: auto;
    padding: 10px 20px;
    max-width: 80rem;
    @include media-down(hamburger) {
      padding: 8px 15px;
    }
  }
  ul {
    margin: 0;
  }
}

.logo {
  flex: 1;
  display: block;
  width: 120px;
  height: 35px;
  background: asset-data-url("logo.svg") no-repeat;
  background-size: contain;
  font-size: 0;
  &__staging::after {
    content: "STAGING";
    display: inline-block;
    font-size: 16px;
    color: black;
    padding-left: 130px;
  }
}

#main-navigation {
  flex: 2;
  &::before {
    content: "";
    display: none;
    width: 60px;
    height: 60px;
    background: asset-data-url("yo.svg") 50% no-repeat;
    background-size: contain;
    opacity: 0;
  }
  .suppliers-cta-form {
    position: relative;
    width: 500px;
    margin: auto;
    .text-input {
      margin: 0;
      padding: 0 0 0 40px;
      border: 1px solid #e6e6e6;
      border-radius: 2px;
      background: #fff asset-data-url("icons/icon-map-marker.svg") 3% 50%
        no-repeat;
      background-color: #f9f9f9;
    }
  }
  .mobile-dropdown-grid {
    @include media-up(hamburger) {
      display: flex;
      justify-content: right;
      margin: 0;
    }
  }
  .parent {
    position: relative;
    padding: 5px 14px;
    &::after {
      content: "";
      display: inline-block;
      width: 14px;
      height: 14px;
      margin-left: 3px;
      background-image: asset-data-url("icons/expand-arrow.svg");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      transform-origin: center;
      vertical-align: middle;
      transition: transform 0.2s ease-in-out;
      @include media-down(hamburger) {
        display: none;
      }
    }
    &:hover {
      &::after {
        transform: rotate(-180deg);
      }
      .submenu {
        display: block;
      }
    }
  }
}

.mobile-only-links {
  display: none;
}

.drop-title {
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
  text-transform: uppercase;
  @include media-down(hamburger) {
    font-size: 17px;
  }
}

.submenu {
  padding: 8px 0 0;
  width: 100%;
  > span {
    display: none;
  }
  @include media-up(hamburger) {
    display: none;
    position: absolute;
    top: 35px;
    left: -25px;
    background: #ffffff;
    padding: 35px 40px 20px;
    border-radius: 4px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    width: 300px;
    z-index: 10;
    > span {
      display: block;
      font-size: 17px;
      font-weight: 700;
      margin-bottom: 8px;
    }
  }
}

.mobile-list {
  margin-left: 0;
  padding-bottom: 15px;
  a {
    display: block;
    padding: 8px 0;
    font-family: $body-font-family;
    color: black;
    font-size: 15px;
    &::before {
      content: "";
      display: inline-block;
      width: 22px;
      height: 22px;
      background-repeat: no-repeat;
      background-size: contain;
      margin-right: 12px;
      vertical-align: middle;
    }
    &:hover {
      text-decoration: underline;
      &::before {
        transform: translateY(-2px);
      }
    }
    @include header_icons;
  }
  @include media-down(hamburger) {
    padding-bottom: 0;
  }
  .mobile-auth-dash {
    display: flex;
    align-items: center;
  }
}

@include media-down(hamburger) {
  #main-navigation {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: white;
    overflow-y: scroll;
    padding: 25px 20px 60px;
    animation: drawer 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    &::before {
      display: block;
      opacity: 1;
    }
    .suppliers-cta-form {
      display: none;
    }

    &.active {
      display: block;
      .parent {
        padding: 25px 0 0;
        &::after {
          display: none;
        }
      }
      .mobile-only-links {
        display: block;
      }
      .auth-link {
        display: inline-block;
        padding: 15px 10px 20px;
        font-size: 17px;
        font-weight: bold;
      }
      .mobile-dropdown-grid {
        margin: 0;
      }
    }
  }
}

.auth-and-cart {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .secondary-links {
    white-space: nowrap;
    @include media-down(hamburger) {
      display: none;
    }
  }
  .dropdown {
    > li > a {
      text-transform: uppercase;
    }
    a {
      font-weight: 700;
      font-family: $header-font-family;
      transition: background-color 0.25s ease-out, color 0.25s ease-out;
      color: $black;
      font-size: 14px;
      &:hover {
        text-decoration: underline;
      }
      &.quote-button {
        border: 1px solid $yordar-black;
        border-radius: 3px;
        padding: 0.3rem 0.6rem;
        &:hover {
          color: $white;
          background-color: $yordar-black;
          text-decoration: none;
        }
      }
    }
  }
  
}

.cart-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #aaa;
  .clear-cart {
    font-size: 20px;
    margin-left: 10px;
    color: $black;
  }

  .dash-cart {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    &::after {
      content: '';
      display: inline-block;
      width: 20px;
      height: 20px;
      background: asset-data-url("icons/shopcart-dark.svg") no-repeat center center;
      background-size: contain;
      margin-left: 8px;
    }
    .dash-cart-count {
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      background: #fb35dc; // $highlight;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      &.hidden {
        display: none;
      }
    }
  }
}

.cart-checkout-btn {
  display: flex;
  align-items: center;
  color: black;
  padding: 4px 10px;
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
  justify-content: center;
  align-self: center;
  height: 100%;
  &:hover {
    color: black;
    text-decoration: underline;
  }
  &:after {
    content: "";
    width: 20px;
    height: 20px;
    background: asset-data-url("icons/shopcart-dark.svg") no-repeat center center;
    margin-left: 5px;
    background-size: contain;
  }
  @include media-down(hamburger) {
    display: none;
  }
  .shopcart-item-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fb35dc;
    color: #fff;
    margin: 0 0 0 8px;
    width: 20px;
    height: 20px;
    font-size: 14px;
    border-radius: 50%;
    font-family: $body-font-family;
    font-weight: normal;
    &.hidden {
      display: none;
    }
  }
}
.shopcart-clear {
  @include media-down(hamburger) {
    display: none;
  }
}

ul.is-dropdown-submenu {
  max-width: 100%;
  z-index: 9999;
  li {
    a {
      color: $black;
      padding: 0.8rem 1rem;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
.dropdown.menu {
  float: right;
  &.no-float {
    float: none;
  }
}

.auth-dropdown {
  display: flex;
  align-items: center;
}

.team-menu {
  @include media-down(hamburger) {
    display: none;
  }
}

// MISCELLANOUS
.attendee-info {
  display: flex;
  align-items: center;
  p {
    white-space: nowrap;
    font-family: $body-font-family;
    margin-right: 10px;
    margin-bottom: 0;
  }
  @include media-down(hamburger) {
    justify-content: space-between;
    width: 100%;
  }
}
.team-order-cart {
  @include media-down(hamburger) {
    display: flex;
  }
}
.mobile {
  @include media-up(hamburger) {
    display: none;
  }
}
