.docket {
  position: sticky;
  top: 0px;
  margin-top: 3rem;
}
.docket-wrapper {
  display: flex;
  max-height: calc(100vh - 40px);
  flex-direction: column;
  overflow-y: scroll;
  border: $border;
  background-color: $white;
  padding: 10px 0;
  flex: 1;
  &.team-order {
    .cart-switch {
      padding: 20px 10px 0 10px;
    }
    .off-canvas-header {
      background-color: #333333;
    }
  }
}

.add-another-supplier-wrapper {
  background: $white;
}
.add-another-supplier {
  margin: auto;
  text-decoration: underline;
  color: black;
}

@include media-down(mobile) {
  .docket-block {
    flex-direction: column;
    justify-content: flex-start;
    position: absolute;
    left: 0;
    width: 100%;
    height: calc(100vh - 55px);
    top: 55px;
    background: rgba(199, 29, 42, 0.98);
    overflow-y: scroll;
    padding-bottom: 130px;
    -webkit-animation: drawer 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    animation: drawer 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    margin-top: 0;
    z-index: 999;
    padding-top: 30px;
  }
}
