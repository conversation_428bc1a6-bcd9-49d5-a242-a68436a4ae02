.off-canvas-header {
	padding: 1.375rem 1rem;
	background: $anchor-color-hover;
	color: $white;
	h3 {
		font-size: rem-calc(16);
		margin: 0;
		text-transform: uppercase;
		font-weight: 500;
	}
	.close-button {
		color: $white;
		transition: color 0.25s ease-in;
		top: 0.8rem;
		&:hover {
			color: $light-gray;
		}
	}
}


.off-canvas .cart-switch {
	padding: 1rem;

	.cart-switch-label {
		margin: 0;
		line-height: rem-calc(28);
		font-weight: 500;
	}
	.switch {
		margin: 0;
	}
}

.recurring-box,
.copy-order-settings,
.custom-checkboxes {
	padding: 10px;
	label {
		display: inline-block;
		cursor: pointer;
		padding-left: 35px;
		background: asset-data-url("icons/icon-checkbox-empty.svg") 0% 50% no-repeat;
	}
	input[type="checkbox"] {
		position: absolute;
		margin: 0;
		clip: rect(0,0,0,0);
		clip: rect(0 0 0 0);
		&:checked + label {
			background: asset-data-url("icons/icon-checkbox-checked.svg") 0% 50% no-repeat;
		}
	}
}


.off-canvas .close-button {

	@include breakpoint(large) {
		display: none;
	}
}
