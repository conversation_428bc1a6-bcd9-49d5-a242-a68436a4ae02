.frequency-tabs {
	background: none;
  border: none;
  li {
  	float: none;
  	display: inline-block;
  	a {
	    padding: rem-calc(10) 0.4rem;
	    line-height: 1;
	    font-size: rem-calc(14);
	    font-family: 'Museo Sans', sans-serif;
	    color: $black;
  	}
  	&.is-active {
  		a {
  			background: transparent;
  			color: $primary-color;
  		}
  	}
  	&:active {
  		a {
  			background: transparent;
  		}
  	}
  }
}

// Hiding it for jQuery?
.tabs-content {
	border: none;
}


.recurring-settings {
	border-top: 1px solid #e2e2e2;
	padding: 1rem 1rem;
	font-size: rem-calc(13);
	.row {
		margin-left: -.625rem;
		margin-right: -.625rem;
	}
	label {
		color: $black;
		font-family: 'Museo Sans', sans-serif;
		font-size: rem-calc(14);
		//padding: 0;
		//line-height: rem-calc(30);
		margin: 0.25rem 0 0.5rem 0;
	}
	select {
    transition: box-shadow 0.5s,border-color 0.25s ease-in-out;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
	}
	input[type="text"],
	input[type="number"],
	input[type="datetime"],
	input[type="date"],
	input[type="time"],
	select {
		height: rem-calc(30);
		padding-top: 0;
		padding-bottom: 0;
		font-size: rem-calc(13);
		margin-bottom: 0.5rem;
		background-color: $white;
		border: $border;
    border-radius: 2px;
	}
	.order-holiday-settings {
		padding-top: 1rem;
		label {
			margin-bottom: 0;
			line-height: 1.6rem;
		}
		.radio-option-list {
			li {
				input {
					margin-bottom: 0;
				}
				label {
					margin: 0;
					font-weight: normal;
				}
			}
		}
	}
	.day-switch {
		margin-left: 0;
		display: block;
		margin-bottom: 0.5rem;

		border-radius: 2px;
		display: inline-block;
		overflow: hidden;
		.button {
			padding: 0.5rem 0.4rem;
			font-size: rem-calc(12);
			background: $white;
			margin-bottom: 0;
		}
	}
	.recurring-select-fieldset {
		input[type=radio]+label:before {
			top: 6px;
		}
		input[type=radio]:checked + label:after {
			top: 10px;
		}
	}
}


/* Checkbox weekday selection */
.checkbox-weekday {
	display: inline-block;
	position: relative;
	width: 40px;
	height: 40px;

}
.checkbox-weekday__input {
	position: absolute;
  margin: 0;
  z-index: 1;
  opacity: 0;
	width: 40px;
	height: 40px;
  cursor: pointer;
}
// not very elegant targeting elements with tag
label.checkbox-weekday__label {
	position: absolute;
	width: 40px;
	height: 40px;
	margin: 0;
	text-align: center;
	font-size: 11px;
	line-height: 40px;
	background-color: $white;
	border: $border;
}
.checkbox-weekday__input:checked ~ label {
	background-color: $primary-color;
	color: $white;
}

// Hiding it for jQuery?
.recurring-select-fieldset {
	display: none;
}

.recurrent-docket__info {
	display: block;
	padding: .25rem .5rem;
	font-size: 13px;
	;
	background-color: $light-gray;
}

.recurrent-docket__list {
	display: flex;
	margin: 0;
}

.recurrent-docket__list-item {
	flex: 1 1 10px;
	padding: .5rem 0;
	text-align: center;

	a {
		color: darken($light-gray, 7.5%);
		cursor: default;
	}
}
.recurrent-docket__list-item--available {

	a {
		color: $primary-color;
	}

	&:hover,
	&:hover a {
		background-color: $primary-color;
		color: $white;
		cursor: pointer;
	}
}
.recurrent-docket__list-item--active {
	background-color: $primary-color;
	color: $white;

	a {
		background-color: $primary-color;
		color: $white;
	}
}
