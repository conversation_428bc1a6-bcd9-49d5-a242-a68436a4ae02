.cart-list-head {
	padding: 0rem 1rem;
	background: #F7F8FA;
	.cart-list-title {
		font-size: rem-calc(15);
		line-height: rem-calc(28);
		font-weight: 500;
	}
	.arrow-link {
		line-height: rem-calc(28);
		height: rem-calc(28);
		font-size: rem-calc(14);
		font-family: 'Museo Sans', sans-serif;
		color: $yordar-black;
		text-decoration: underline;
	}
}

.cart-list {
	.cart-list-item {
		padding: 0.8rem;
		display: grid;
		grid-template-columns: 50px 2fr 1fr 0fr;
		grid-column-gap: 10px;
		&.processing {
			background-color: #efefef;
		}
		.document-icon {

			&:before {
				display: inline-block;
				position: absolute;
				left: 1rem;
    		top: 1rem;
    		cursor: pointer;
			}
			&:hover,
			&.hover {
				&:before {
					background-color: gray;
				}
			}
			& + .dropdown-pane {
				width: 250px;
				top: 60px !important;
				left: 5px !important;
				textarea {
					margin-bottom: 0;
				}
				// little arrow
				&:before {
					left: 10%;
				}
			}
		}
		.item-title {
			font-size: rem-calc(14);
			line-height: rem-calc(18);
			font-weight: 500;
			margin: 0;
			align-self: center;
		}
		.item-meta {
			padding-bottom: 1rem;
		}

		&:last-child {
			.item-meta {
				border-bottom: none;
			}
		}
	}
	.item-counter {
		color: $medium-gray;
		font-size: rem-calc(14);
		input {
			display: inline-block;
	    width: 50px;
	    margin: 0;
	    height: rem-calc(35);
	    text-align: center;
	    background: #fff;
			color: $yordar-black;
	    border: $border;
	    border-radius: 2px;
			padding: 0;
		}
	}
	.item-remove {
		align-self: center;
	}
	.item-summ {
		line-height: rem-calc(25);
		font-size: rem-calc(14);
		text-align: right;
		align-self: center;
	}
	.delete-ol-icon {
		display: inline-block;
		border: 1px solid $primary-color;
		width: 23px;
		height: 23px;
		text-align: center;
		line-height: 18px;
		border-radius: 15px;
		margin-left: 10px;
		margin-top: 6px;
		font-size: 16px;
	}
	.docket-menu-item-note {
		grid-column: 2 / 4;
		color: #9B9B9B;
		font-size: rem-calc(14);
	}
}
