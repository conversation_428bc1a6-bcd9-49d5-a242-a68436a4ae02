
.cart-footer {
	padding: 1rem;
	background-color: $white;
	.docket-checkout-btn {
		margin: 0;
		background: $primary;
		&:hover {
			background: darken($primary, 10%);
		}
	}
}

.cart-summary-table {
	tr {
		td {
			padding: 0;
    	vertical-align: top;
    	line-height: 1.8rem;
			&:last-child {
				text-align: right;
			}
			span {
				display: block;
			}
			.help-text {				
				font-size: rem-calc(14);
				&.error {
					color: $primary-color;
				}
			}
		}
	}
	tbody {
		td {
			font-size: rem-calc(14);
			color: $medium-gray;
		}
	}
	tfoot {
		background: transparent;		
		td {
			text-transform: uppercase;
			font-size: rem-calc(18);
			font-weight: bold;
			color: $black;
			padding: 1rem 0;
		}
	}
}

