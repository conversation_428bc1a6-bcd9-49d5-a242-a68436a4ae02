
.cart-accordion {
	.accordion-item {
		.accordion-title {
			height: 49px;
			padding: 17px 17px 17px 40px;
		}
		&:first-child>.accordion-title {
			border-radius: 0;
		}
		&:last-child>.accordion-title {
			border-radius: 0;
		}
	}
}
.order-docket-levels__title {
	font-size: rem-calc(16);
	font-weight: 500;
	background: #616161;
	color: $white;
	border: none;
	border-bottom: 3px solid $white;
	position: relative;
	&:before {
		content: '';
    position: absolute;
    left: 1.4rem;
    top: 50%;
    margin-top: -5px;
    width: 0;
		height: 0;
		border-style: solid;
		border-width: 5px 0 5px 6px;
		border-color: transparent transparent transparent #ffffff;
	}
	&:hover,
	&:focus {
		background: $black;
		color: white;
	}
} 

.order-docket-levels.is-active {

	.order-docket-levels__title {
		background: $black;
		&:before {
			content: "";
			border-width: 6px 5px 0 5px;
			border-color: #ffffff transparent transparent transparent;
			margin-top: -2.5px;
		}
	}
}
.order-docket-levels__title-action {
	display: inline-block;
	width: 50px;
	height: 15px;
	padding-top: 1px;
	font-size: 12px;
	line-height: 1.3;
	border-radius: 10px;
	text-align: center;
	margin: 0 5px;
	&:hover, &:focus {
		background-color: white;
		color: $black;
	}
}

.new-location-form,
.edit-location-form {
	background-color: #F7F8FA;
	padding: 10px;
	.location-details {
		background-color: $white;
		border-radius: 2px;
		border: $border;
	}
	button {
		margin-bottom: 0;
	}
}
