// for react-toastify
:root {
  --toastify-color-light: #fff !important;
  --toastify-color-dark: #{$textcolor} !important;
  --toastify-text-color-light: #575757 !important;
  --toastify-color-success: #{$primary} !important;
  --toastify-color-error: #{$error} !important;
  --toastify-color-info: #{$mammoth-charcoal} !important;    
  --toastify-color-warning: #{$tertiary} !important;
}

.Toastify__toast {
  font-family: $body-font-family !important;
  font-size: 14px !important;
  .toastify-injected-html {
    padding: 4px;
    p {
      margin-bottom: 2px;
    }
    a {
      font-weight: bold;
    }
  }
}

.Toastify__progress-bar {
  height: 2px !important;
  border-radius: 2px !important;
}