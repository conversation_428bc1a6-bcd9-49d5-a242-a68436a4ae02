/*=============================================>>>>>
= Common =
===============================================>>>>>*/

.wrap {
  max-width: $wrapper-width;
  margin-left: auto !important;
  margin-right: auto !important;
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.lock {
  position: fixed;
  overflow: hidden;
  header {
    position: fixed!important;
    width: 100%;
  }
}

.prevent-scroll{
  height: 100%;
  overflow: hidden;
}

.get-started-email {
	margin: 0 !important;
	height: auto !important;
}

.text-alert {
  color: $alert-color;
}

@for $num from 0 through 10 {
  // base
  .m-#{$num} {
    margin: #{$num}rem;
  }
  .m-#{$num}-2 {
    margin: #{$num/2}rem;
  }
  .m-#{$num}-4 {
    margin: #{$num/4}rem;
  }

  .p-#{$num} {
    padding: #{$num}rem;
  }
  .p-#{$num}-2 {
    padding: #{$num/2}rem;
  }
  .p-#{$num}-4 {
    padding: #{$num/4}rem;
  }

  // x-axis
  .mx-#{$num} {
    margin-left: #{$num}rem;
    margin-right: #{$num}rem;
  }
  .mx-#{$num}-2 {
    margin-left: #{$num/2}rem;
    margin-right: #{$num/2}rem;
  }
  .mx-#{$num}-4 {
    margin-left: #{$num/4}rem;
    margin-right: #{$num/4}rem;
  }

  .px-#{$num} {
    padding-left: #{$num}rem;
    padding-right: #{$num}rem;
  }
  .px-#{$num}-2 {
    padding-left: #{$num/2}rem;
    padding-right: #{$num/2}rem;
  }
  .px-#{$num}-4 {
    padding-left: #{$num/4}rem;
    padding-right: #{$num/4}rem;
  }

  // y-axis
  .my-#{$num} {
    margin-top: #{$num}rem;
    margin-bottom: #{$num}rem;
  }
  .my-#{$num}-2 {
    margin-top: #{$num/2}rem;
    margin-bottom: #{$num/2}rem;
  }
  .my-#{$num}-4 {
    margin-top: #{$num/4}rem;
    margin-bottom: #{$num/4}rem;
  }

  .py-#{$num} {
    padding-top: #{$num}rem;
    padding-bottom: #{$num}rem;
  }
  .py-#{$num}-2 {
    padding-top: #{$num/2}rem;
    padding-bottom: #{$num/2}rem;
  }
  .py-#{$num}-4 {
    padding-top: #{$num/4}rem;
    padding-bottom: #{$num/4}rem;
  }

  // top
  .mt-#{$num} {
    margin-top: #{$num}rem;
  }
  .mt-#{$num}-2 {
    margin-top: #{$num/2}rem;
  }
  .mt-#{$num}-4 {
    margin-top: #{$num/4}rem;
  }

  .pt-#{$num} {
    padding-top: #{$num}rem;
  }
  .pt-#{$num}-2 {
    padding-top: #{$num/2}rem;
  }
  .pt-#{$num}-4 {
    padding-top: #{$num/4}rem;
  }

  // right
  .mr-#{$num} {
    margin-right: #{$num}rem;
  }
  .mr-#{$num}-2 {
    margin-right: #{$num/2}rem;
  }
  .mr-#{$num}-4 {
    margin-right: #{$num/4}rem;
  }
  
  .pr-#{$num} {
    padding-right: #{$num}rem;
  }
  .pr-#{$num}-2 {
    padding-right: #{$num/2}rem;
  }
  .pr-#{$num}-4 {
    padding-right: #{$num/4}rem;
  }

  // bottom
  .mb-#{$num} {
    margin-bottom: #{$num}rem;
  }
  .mb-#{$num}-2 {
    margin-bottom: #{$num/2}rem;
  }
  .mb-#{$num}-4 {
    margin-bottom: #{$num/4}rem;
  }
  
  .pb-#{$num} {
    padding-bottom: #{$num}rem;
  }
  .pb-#{$num}-2 {
    padding-bottom: #{$num/2}rem;
  }
  .pb-#{$num}-4 {
    padding-bottom: #{$num/4}rem;
  }

  // left
  .ml-#{$num} {
    margin-left: #{$num}rem;
  }
  .ml-#{$num}-2 {
    margin-left: #{$num/2}rem;
  }
  .ml-#{$num}-4 {
    margin-left: #{$num/4}rem;
  }
  
  .pl-#{$num} {
    padding-left: #{$num}rem;
  }
  .pl-#{$num}-2 {
    padding-left: #{$num/2}rem;
  }
  .pl-#{$num}-4 {
    padding-left: #{$num/4}rem;
  }
}
