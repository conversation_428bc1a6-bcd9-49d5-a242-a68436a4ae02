/* ------------------------------------------------------
  Footer
------------------------------------------------------ */
footer.footer {
  position: relative;
  background: #f9f9f9;
  padding: 50px 0 20px;

  label {
    display: none;
  }

  a:hover {
    text-decoration: underline;
  }

  input,
  textarea,
  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: block;
    width: 100%;
    border: 2px solid #ffffff;
    border-radius: 2px;
    padding: 16px 20px;
    background-color: $white;
    box-sizing: border-box;
    @include headings-font();
    color: $yordar-black;
    font-size: 15px;
    line-height: calc(18 / 15);

    &:last-child {
      margin-bottom: 0;
    }

    &:active,
    &:focus {
      outline: none;
    }

    @include placeholder {
      color: rgba($off-black, 0.9);
    }
  }

  .wrap {
    display: flex;
    flex-direction: row;
    @include media-down(tablet) {
      flex-direction: column;
      max-width: 540px;
    }
    #footer-nav {
      display: flex;
      flex-direction: row;
      flex: 8;
      @include media-down(tablet) {
        flex-direction: column;
      }
      .col {
        flex: 1;
        span.heading {
          position: relative;
          display: block;
          font-family: $header-font-family;
          font-weight: 700;
          color: $yordar-black;
          text-transform: uppercase;
          margin-bottom: 20px;
          @include media-down(tablet) {
            cursor: pointer;
          }
          &:after {
            position: absolute;
            top: calc(50% - 6px);
            content: "";
            right: 20px;
            width: 12px;
            height: 12px;
            background-image: asset-data-url("icons/expand-arrow-white.svg");
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            transform-origin: center;
            transition: transform 0.2s ease-in-out;
            display: none;
            @include media-down(tablet) {
              display: block;
            }
            @include media-down(mobile) {
              right: 10px;
            }
          }
          &.active:after {
            transform: rotate(180deg);
          }
          &.active + ul {
            @include media-down(tablet) {
              opacity: 1;
              max-height: 1500px;
              margin-bottom: 35px;
            }
          }
        }
        ul {
          margin-bottom: 20px;
          margin-left: 0;
          @include media-down(tablet) {
            opacity: 0;
            margin-bottom: 0;
            max-height: 0;
            transition: all 0.3s ease;
          }
          li {
            padding-bottom: 9px;
            a {
              display: flex;
              align-items: center;
              font-size: 15px;
              color: $yordar-black;
              &:before {
                content: "";
                margin-right: 10px;
                width: 22px;
                height: 22px;
                display: inline-block;
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
              }
              @include footer_icons;
            }
          }
        }
      }
    }
    .footer-end {
      .footer-logo {
        width: 120px;
        margin-bottom: 3rem;
      }
      flex: 3;
      @include media-down(tablet) {
        margin-top: 30px;
      }
      .get-started-form {
        .heading {
          @include h3();
          text-transform: uppercase;
          margin-bottom: 25px;
          display: block;
        }
        form {
          display: flex;
          width: 100%;
          max-width: 400px;
          margin: 0 auto;
          height: 44px;
          label {
            display: none;
          }
          input {
            width: 100%;
            border: none;
            border-radius: 3px 0 0 3px;
            font-size: 16px;
            padding: 0 20px;
            outline: none;
          }
          .button-footer {
            border-radius: 0 3px 3px 0;
            margin: 0;
            background: #333;
            border: 5px solid #fff;
            padding: 8px 25px;
            @include media-down(mobile) {
              margin-left: 8px;
            }

            .text {
              margin: 0;
              padding: 0;
              border: 0;
              font-size: 100%;
              font: inherit;
              vertical-align: baseline;
              box-sizing: border-box;
            }
          }
        }
      }
      .socials {
        margin-top: 50px;
        margin-bottom: 50px;
        @include media-down(tablet) {
          margin-top: 30px;
          margin-bottom: 20px;
        }
        .icon {
          display: inline-block;
          width: 30px;
          height: 30px;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          margin-right: 16px;
          &.facebook {
            background-image: asset-data-url("icons/facebook.svg");
          }
          &.instagram {
            background-image: asset-data-url("icons/instagram.svg");
          }
          &.pinterest {
            background-image: asset-data-url("icons/pinterest.svg");
          }
          &.linkedin {
            background-image: asset-data-url("icons/linkedin.svg");
          }
        }
      }
      .footer-bottom {
        margin-top: 50px;
        @include media-down(tablet) {
          margin-top: 20px;
        }
        .links {
          color: $yordar-black;
          a {
            color: $yordar-black;
            font-size: 13px;
          }
        }
        .rights {
          color: $yordar-black;
          font-size: 13px;
        }
      }
    }
    .button-footer {
      background-color: $primary;
      padding: 15px 20px;
      border: none;
      @include h3($color-white: true);
      text-transform: uppercase;
      border-radius: 2px;

      &:hover,
      &:focus {
        cursor: pointer;
        background-color: darken($primary, 5%);
      }

      &.wide {
        border-radius: 4px;
        width: 100%;
        max-width: 300px;
      }
    }
  }
}

.intercom-separator {
  padding: 60px 0 0;
}
