.purchase-order-filter {
  font-size: rem-calc(13);
  font-weight: 500;
  select {
    display: inline-block;
    font-weight: 400;       
    margin: 0 0 0 0.5rem;
    height: 50px;
    width: 200px;
    background-color: #fff;
    color: #c91a24;
  }
}

.report-fields {
  padding: 0 30px 0 30px;
}

fieldset.report-fields {
  background: white;
  border-top: none;
}

fieldset.report-fields .fieldset-title.report-label {
  padding: 0 0 0 0;
}

.report-title-border {
  border-bottom: 2px solid lightgrey;
  line-height: 3; 
}

.products_stats {
  text-align: center;
  h4 {
    font-size: 14px !important;
    text-transform: uppercase;
  }
  .num-of-orders {
    font-size: 35px;
  }
  div {
    font-size: 12px;
    font-weight: 500;
  }
}

.report-date-lable {
  font-weight: 500;
}

.spend-chart .sk-three-bounce {
  margin: auto;
  text-align: center;
  display: block;
  margin-top: 200px;
  .sk-child {
    background-color: #000000;
  }
}

.reports-filters {
  margin-right: 300px;
  margin-bottom: 30px;
  @include media-down(hamburger) {
    margin-right: 0;
  }
  &.survey-filters {
    margin-right: 0;
    margin-top: 0;
    margin-bottom: 16px;
  }
}

.generate-report-button {
  background: $primary;
  color: white;
  padding: 4px 20px;
  border-radius: 20px;
  transition: box-shadow 0.3s;
  &:hover {
    box-shadow: inset 0 0 15px 0 rgba(0,0,0,0.3);
  }
  &.export {
    margin-top: 10px;
    border-radius: 0;
    padding: 8px 30px;
  }
}

.report-modal-heading {
  font-weight: bold;
}
.report-modal-options {
  margin-bottom: 10px;
}
.reporting {
  display: grid;
  grid-gap: 20px;
  grid-template-columns: 3fr 1fr;
  min-height: calc(100% - 200px);
  @include media-down(hamburger) {
    display: block;
  }
}

.reporting-graph {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px;
  grid-row: 1/3;
  box-shadow: 0 3px 6px #00000020;
  > .between-flex {
    flex-wrap: wrap;
  }
  @include media-down(hamburger) {
    width: calc(100vw - 0.5rem);
    padding: 8px;
  }
  .button {
    padding: 6px 22px;
    background: white;
    border: 1px solid #a8a8a8;
    color: black;
    font-size: 16px;
    @include media-down(hamburger) {
      padding: 4px;
    }
    &.icon::before {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      background-size: contain;
      background-repeat: no-repeat;
      margin-right: 4px;
      vertical-align: bottom;
    }
    &.csv::before {
      background-image: url('icons/example-csv.svg')
    }
    &.pdf::before {
      background-image: url('icons/pdf.svg')
    }
  }
}

.reports-heading {
  font-weight: 900;
}

.legend {
  font-family: $header-font-family;
  display: flex;
  align-items: center;
  &::before {
    content: '';
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-right: 4px;
  }
  &-catering {
    margin-right: 8px;
    &::before {
      background: black;
    }
  }
  &-snacks::before {
    background: #dedede;
  }
  &-below-budget {
    margin-right: 8px;
    &::before {
      background: #2BFFCF;
      border-radius: 50%;
    }
  }
  &-over-budget {
    margin-right: 8px;
    &::before {
      width: 0;
      height: 0;
      border-bottom: 10px solid rgb(250, 66, 144);
      border-right: 6px solid transparent;
      border-left: 6px solid transparent;
    }
  }
}

.bar-container {
  height: 420px;
}
.doughnut-container {
  margin: auto;
  width: 200px;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  background-color: #fafafa;
  border-radius: 12px;
  border: 1px solid #e5e5e5;
  margin: 40px 0;
  text-align: center;

  &__icon {
    width: 160px;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
  }

  &__image {
    width: 160px;
    height: 160px;
    opacity: 0.6;
  }

  &__title {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-family: 'Museo Slab';
    font-weight: bold;
    color: #333;
  }

  &__description {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #666;
    line-height: 1.5;
    max-width: 400px;
  }

  &__hint {
    margin: 0;
    font-size: 14px;
    color: #999;
    line-height: 1.4;
  }
}

.reporting-doughnut {
  box-shadow: 0 3px 6px #00000020;
  text-align: center;
  width: 300px;
  padding: 12px;    
  @include media-down(hamburger) {
    width: 100%;
  }
  h3 {
    font-weight: bold;
    line-height: 1;
    text-transform: capitalize;
    display: flex;
    justify-content: center;
  }                                                                                                                                                                                                                                                     
}

.category-spend {
  flex: 1;
  color: black;
  margin: 0 4px;
  border-radius: 20px;
  padding: 4px 20px;
  box-shadow: 0 2px 5px 1px #403c4329;
  border: 1px solid #f2f2f2;
  text-transform: capitalize;
  &:hover {
    color: black;
  }
  &.active {
    border-color: $primary;
    color: $primary;
    font-weight: bold;
  }
}

.dashboard-filters.reports-filters {
  .dashboard-filter {
    max-width: 300px;
    color: black;
    @include media-down(hamburger) {
      max-width: initial;
    }
  }
  .dashboard-filter__datepicker {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: initial;
    padding: 0;
    padding-left: 20px;
    > span::before {
      content: '';
      display: inline-block;
      background: url(icons/icon-calendar.svg) no-repeat;
      width: 16px;
      height: 16px;
      margin-right: 10px;
      background-size: contain;
    }
    .react-datepicker-wrapper {
      width: initial;
    }
    .react-datepicker__month--in-range {
      background-color: #000;
    }
    .react-datepicker__input-container {
      .dashboard-filter {
        width: initial;
        margin: 0;
        font-weight: bold;
        text-align: center;
        padding: 6px 8px;
      }
    }
  }
}

.reporting.loading {
  .bar-container {
    width: 100%;
    height: 420px;
    @include shimmer;
  }
  .doughnut-container {
    border-radius: 50%;
    width: 200px;
    height: 200px;
    position: relative;
    &::after {
      content: '';
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      width: 144px;
      height: 144px;
      transform: translate(-50%, -50%);
      background: white;
    }
    @include shimmer;
  }
}

.reports-search {
  min-width: 360px;
  margin: 0;
}

.pantry-report-header {
  margin-bottom: 24px;
}

.for-dates {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: initial;
  padding: 0;
  padding-left: 20px;
  flex: 2;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 5px 1px #403c4329;
  margin-right: 18px;
  max-width: 300px;
  color: black;
  &::before {
    content: '';
    display: inline-block;
    background: asset-data-url("icons/icon-calendar.svg") no-repeat;
    width: 16px;
    height: 16px;
    margin-right: 10px;
    background-size: contain;
  }
  input {
    width: initial;
    margin: 0;
    font-weight: bold;
    text-align: center;
    padding: 6px 8px;
    box-shadow: 0 2px 5px 1px #403c4329;
    border-radius: 20px;
  }
}

.pantry-manager-header {
  margin-bottom: 24px;
}

.report-period-options {
  display: flex;
  input[type="radio"] {
    position: static;
    opacity: 1;
    margin-right: 0.5rem;
  }
}
