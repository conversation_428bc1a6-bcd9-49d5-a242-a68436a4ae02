fieldset.survey-question-field {
  background: white;
  box-shadow: none;
  border: 1px solid #bababa;
  border-radius: 6px;
  .form-input {
    font-size: 16px;
    margin: 12px 0;
  }
  .question-number {
    font-weight: 900;
  }
  .question-tag {
    font-weight: 900;
    font-family: $header-font-family;
    font-size: 14px;
    text-transform: uppercase;
    color: grey;
  }
  // stop react-select position: relative input leading to bad drag preview
  .css-mohuvp-dummyInput-DummyInput {
    position: static;
  }
}

input[type="radio"].survey-create-radio {
  position: static;
  opacity: 1;
  margin-right: 10px;
}

.survey-container.preview {
  font-size: 13px;
  .question-name-container {
    font-size: 14px;
    padding: 6px;
    margin-bottom: 6px;
  }
  .question-name {
    display: flex;
  }
  .question-number {
    width: 18px;
    height: 18px;
    line-height: 18px;
  }
}

.survey-panel-container {
  display: grid;
  grid-template-columns: 70% 30%;
  grid-column-gap: 20px;
  margin-right: 20px;
  @include media-down(tablet) {
    grid-template-columns: 1fr;
    grid-row-gap: 12px;
    margin-right: 0;
  }
}

.survey-container, .survey-result-container {
  background: white;
  border: 1px solid #dadada;
  padding: 12px;
  border-radius: 4px;
}

.survey-result-container {
  text-align: center;
}

.survey-result-container.total {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 260px;
  margin-top: 12px;
}


.survey-title {
  font-weight: 900;
}

.question-name-container {
  background: white;
  border: 1px solid #e2e2e2;
  padding: 12px;
  margin-bottom: 16px;
  border-radius: 4px;
  cursor: pointer;
  .form-input {
    margin-bottom: 16px;
  }
}

.question-name {
  font-weight: bold;
  margin: 0;
}

.question-expand {
  display: flex;
  justify-content: space-between;
  align-items: center;
  &::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(icons/chevron-down) no-repeat;
    transition: all 0.3s ease-in-out;
  }
  &.expanded::after {
    transform: rotate(-180deg);
  }
}

.question-tag {
  font-weight: 900;
  font-family: $header-font-family;
  font-size: 14px;
  text-transform: uppercase;
  color: grey;
  margin-bottom: 8px;
}

.question-number {
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  line-height: 26px;
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.question-fields {
  padding-top: 20px;
}

.total-responses {
  font-size: 50px;
  text-align: center;
  font-weight: 900;
}

.switch-survey-buttons {
  margin-bottom: 12px;
}

.add-question-button {
  width: 100%;
  padding: 16px 0;
  font-weight: 900;
  font-size: 18px;
  text-transform: uppercase;
}