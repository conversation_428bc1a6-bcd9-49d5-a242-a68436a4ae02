.order-show {
  max-width: 1400px;
  h2 {
    margin: 0;
    font-weight: 900;
    font-size: 22px;
  }
  &-header {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-column-gap: 40px;
    @include media-down(hamburger) {
      display: block;
    }
  }
  &-body {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-column-gap: 40px;
    @include media-down(hamburger) {
      display: block;
    }
  }
  &__options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 20px;
    p {
      margin-bottom: 0;
    }
    .date::before {
      content: '';
      display: inline-block;
      width: 20px;
      height: 20px;
      background: asset-data-url('icons/icon-calendar.svg');
      background-repeat: no-repeat;
      background-size: contain;
      margin-right: 8px;
      vertical-align: sub;
    }
    .button {
      margin: 0;
      padding: 4px 32px;
      font-size: 14px;
    }
  }
  &__orderlines, &__details {
    background: white;
    border-radius: 4px;
    border: 1px solid #DBDBDB;
    margin-bottom: 60px;
  }
  &__orderlines {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  &__details {
    position: sticky;
    top: 16px;
    padding: 16px;
    align-self: flex-start;
    &.no-sticky {
      position: static;
    }
  }
  .content {
    border: none;
    margin-bottom: 0;
  }
  .customer-order__heading {
    font-family: $body-font-family;
    background: #ffe3c914;
    border-bottom: 1px solid #DBDBDB;
    padding: 10px 16px;
    text-align: center;
    font-weight: bold;
    &:first-of-type {
      border-top: 1px solid #DBDBDB
    }
  }
  .supplier-name {
    color: #8c8c8c;
    margin: 16px 0 0 20px;
    color: #707070;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 12px;
    margin: 16px 0 0px 20px;
  }
  .order-show-table-row {
    display: grid;
    grid-template-columns: 6fr 4fr 2fr 1fr;
    grid-gap: 4px;
    align-items: center;
    &.team {
      grid-template-columns: 5fr 4fr 2fr;
      li:last-of-type {
        text-align: right;
      }
    }
    @include media-down(hamburger) {
      grid-template-columns: 1fr;
    }
    p {
      margin-bottom: 0;
    }
    .circle-icon {
      min-width: 50px;
      height: 50px;
      background: #3c3c3c;
      font-size: 16px;
      margin-right: 20px;
      &.small {
        min-width: 36px;
        width: 36px;
        height: 36px;
        font-size: 16px;
        color: black;
      }
      @include media-down(hamburger) {
        display: none;
      }
    }
    .orderline-count {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: -6px;
      right: 0;
      background: #00000080;
      color: white;
      border-radius: 50px;
      padding: 0px 13px;
      font-size: 12px;
      line-height: 16px;
      @include media-down(hamburger) {
        display: none;
      }
    }
  }
  .customer-order__totals {
    padding: 20px 16px 8px;
    margin-bottom: 4px;
    font-weight: bold;
    border-top: 1px solid #e2e2e2;
    p {
      margin-bottom: 4px;
    }
  }
  .orderline {
    padding: 1rem 16px;
    border-bottom: 1px solid #DBDBDB;
  }
  .orderline-image-name {
    display: flex;
    align-items: center;
    position: relative;
  }
  .orderline-image {
    position: relative;
  }
  .orderline-name {
    margin-right: 30px;
    font-weight: bold;
  }
  .extras {
    font-size: 12px;
    cursor: help;
  }
  .orderline-note {
    font-size: 12px;
    margin: 0;
    max-width: 280px;
    cursor: help;
    @include media-down(hamburger) {
      margin-left: 0;
    }
  }
  .orderlines-title {
    display: flex;
    justify-content: space-between;
    padding: 16px 16px 0;
    .address {
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 6px;
        background-image: asset-data-url("icons/icon-map-marker.svg");
      }
    }
  }
  .grey {
    color: #767676;
  }
  .orderlines-title__sub {
    color: #5d5d5d;
    font-weight: bold;
    @include media-down(hamburger) {
      display: none;
    }
  }
  .button.hollow {
    margin: 0;
    padding: 4px 32px;
    @include media-down(hamburger) {
      font-size: 14px;
      padding: 4px 16px;
    }
  }
  .button.team-order-edit-btn {
    margin-top: 10px;
    padding: 0.8em 2em;
  }
  &__details {
    @include media-up(hamburger) {
      overflow-y: scroll;
      &.full-height {
        height: auto;
        border: none;
        padding: 0;
        padding-top: 8px;
      }
    }
    @include media-down(hamburger) {
      margin-top: 20px;
    }
    p {
      color: grey;
      margin-bottom: 2px;
      font-size: 14px;
      > strong {
        font-size: 14px;
      }
    }
    .order-show-details-section {
      border-bottom: 1px solid #DBDBDB;
      padding-block: 10px;
      &:last-of-type {
        border-bottom: none;
      }
    }
    .delivery-details-title {
      font-family: $body-font-family;
      font-weight: bold;
      margin-bottom: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .button {
        margin: 0;
        padding: 6px 20px;
        font-size: 14px;
      }
    }
    .order-show-detail-title {
      display: flex;
      align-items: center;
      font-family: $body-font-family;
      letter-spacing: 1px;
      text-transform: uppercase;
      font-size: 12px;
      margin: 10px 0;
      font-weight: bold;
      &::before {
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 6px;
      }
      &.order::before {
        background-image: asset-data-url("icons/order.svg");
        filter: invert(1);
      }
      &.delivery::before {
        background-image: asset-data-url("icons/icon-map-marker.svg");
      }
      &.supplier::before {
        background-image: asset-data-url("icons/supplier.svg");
      }
      &.instructions::before {
        background-image: asset-data-url("icons/note.svg");
      }
      &.cutoff::before {
        background-image: asset-data-url("icons/clock-hollow.svg");
      }
      &.date::before {
        background-image: asset-data-url("icons/icon-calendar.svg");
      }
      &.status::before {
        background-image: asset-data-url("icons/status.svg");
      }
      &.po::before {
        background-image: asset-data-url("icons/PO.svg");
      }
      &.totals::before {
        background-image: asset-data-url("icons/cost.svg");
      }
      &.attendees::before {
        background-image: asset-data-url("icons/user.svg");
      }
      &.magic::before {
        background-image: asset-data-url("icons/wand.svg");
      }
    }
  }
}

.order-show-table-row {
  display: flex;
  align-items: baseline;
  font-size: 14px;
  color: #333333;
  line-height: 20px;
  margin-left: 0;
}

.manifest-document-handle {
  position: absolute;
  bottom: 0;
}