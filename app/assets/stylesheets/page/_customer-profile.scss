.customer-area {
  background: #fbfbfb;
  display: flex;
  .row {
    margin: 0;
    max-width: inherit;
  }
}

.admin-customer {
  ul {
    margin: 0;
  }
}
.customer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 8px;
  &.dash {
    display: grid;
    grid-template-columns: 10fr 1fr 2fr;
    position: sticky;
    top: 0;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem !important;
    padding: 8px 24px;
    padding-left:12px;
    background: white;
    border-bottom: 1px solid #eee;
    border-left: 1px solid #eee;
    z-index: 99;
    font-size: 14px;
    min-height: 55px;
    #dash-hamburger.open {
      top: -26px;
    }
    > * {
      padding-right: 12px;
      height: 100%;
    }
    @include media-down(hamburger) {
      position: static;
      display: flex;
    }
  }
    &__info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: 1.5rem;
    @include media-down(hamburger) {
      flex: 1;
    }
  }
  &__title {
    color: $black;
    font-weight: bold;
    font-family: $body-font-family;
    margin: 0;
  }
  &__auth {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .personalised__tag {
      @include media-down(small-tablet) {
        display: none;
      }
    }
  }
  &__create-new {
    @include media-down(small-tablet) {
      display: none;
    }
    span {
      display: flex;
      align-items: center;
      background: white;
      color: black;
      padding: 8px 21px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: bold;
      text-align: center;
      border: 1px solid #d4d4d4;
      margin-left: 20px;
      cursor: pointer;
      &::after {
        content: "";
        display: inline-block;
        background: asset-data-url("icons/plus-block.svg") no-repeat;
        width: 14px;
        height: 14px;
        margin-left: 10px;
      }
    }
  }
  &__dropdown {
    color: black;
    background: white;
    margin-top: 9px;
    width: 230px;
    a {
      color: black;
      margin: 0 16px;
      border-radius: 4px;
    }
    a:hover {
      cursor: pointer;
      background: #e8edf2;
    }
    .show-all-linked {
      color: $primary;
      font-weight: bold;
      &:hover {
        color: darken($primary, 5%);
      }
    }
    &--linked {
      min-width: 130px;
      width: 100%;
      margin-top: 7px;
      &::before {
        content: none;
      }
      a {
        margin: 0 4px;
      }
    }
  }
}

.supplier-name-dash {
  @include media-down(hamburger) {
    display: none;
  }
}

.personalised {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  @include media-down(hamburger) {
    margin-bottom: 12px;
  }
  &__tag {
    background: #dc2454;
  }
  &__name {
    display: block;
    font-size: 14px;
    line-height: 1.2rem;
    color: $black;
    font-family: $body-font-family;
    margin: 0;
    &.sidebar {
      max-width: 150px;
      @include media-down(hamburger) {
        max-width: initial;
      }
    }
    &--header {
      margin-left: -18px;
      &--dash {
        position: relative;
        display: flex;
        align-items: center;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        overflow: visible;
        cursor: pointer;
        margin-left: 0;
      }
      @include media-down(hamburger) {
        display: none !important;
      }
    }
    &:hover,
    &:focus {
      color: $primary;
    }
    &::after {
      border-color: black transparent transparent !important;
    }
  }
  &__company {
    display: block;
    font-size: 14px;
    color: black;
    line-height: 1.2rem;
  }
}

.masquerade {
  display: flex !important;
  align-items: center;
  &::before {
    content: '';
    margin-right: 8px;
    width: 24px;
    height: 24px;
    background-image: asset-data-url('icons/eye.svg');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }
}

.no-upcoming {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 5em;
  .button {
    margin-top: 2em;
    padding: 15px 40px;
  }
  h2 {
    color: $black;
  }
}

.circle-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  width: 36px;
  font-size: 14px;
  color: white;
  border-radius: 50%;
  margin-right: 10px;
  img {
    border-radius: 50%;
  }
  &.avatar {
    background-repeat: no-repeat;
    background-size: contain;
  }
  &.own-account {
    background-image: url(icons/user-assign-white.svg);
    background-size: 20px;
    background-position: center;
    background-repeat: no-repeat;
  }
}

.view-map-btn {
  padding: 6px 8px;
  background: #191919;
  margin-top: 7px;
  &:hover,
  &:focus {
    background: lighten(#191919, 10%);
  }
}

.day-selector {
  display: inline-block;
  .button {
    border-radius: 0;
    font-size: rem-calc(14);
    text-transform: uppercase;
    color: $medium-gray;
    background: $light-gray;
    padding: 0.719rem 0.5rem;
    vertical-align: baseline;
    transition: all 0.25s ease-out;
    font-weight: 500;
    &:hover {
      color: $black;
    }
    &:first-child {
      border-top-left-radius: $global-radius;
      border-bottom-left-radius: $global-radius;
    }
    &:last-child {
      border-top-right-radius: $global-radius;
      border-bottom-right-radius: $global-radius;
    }
    &.active {
      background: $primary-color;
      color: $white;
    }
  }
}

.customer-header-back-link {
  border: 1px solid #e4e4e4;
  padding: 5px 8px;
  border-radius: 4px;
  background: white;
  margin-right: 12px;
  &::after {
    content: "";
    display: inline-block;
    width: 12px;
    height: 12px;
    background: asset-data-url("icons/chevron-back.svg") no-repeat;
    background-position: center;
    vertical-align: middle;
  }
}

.orders-list {
  padding-bottom: 20rem;
}

.customer-order-list-heading {
  font-weight: bold;
}

.list-flex-1 {
  flex: 1;
}
.list-flex-2 {
  flex: 2;
}
.list-flex-3 {
  flex: 3;
}
.list-flex-4 {
  flex: 4;
}
.list-flex-5 {
  flex: 5;
}
.react-responsive-modal-modal {
  border-radius: 5px;
}

@include media-down(hamburger) {
  .customer-form a {
    font-size: 16px;
  }
  .order-list-columns {
    display: none;
  }
  .order-list-item {
    flex-direction: column;
    font-size: 16px;
    &__type {
      display: none;
    }
  }
}

.customer-invoices__headings, .item-list__headings {
  display: flex;
  padding: 12px 10px;
  color: #9f9f9f;
  font-size: 14px;
  @include media-down(hamburger) {
    display: none;
  }
}

.sticky-container {
  position: sticky;
  top: 0;
  z-index: 997;
}

.item-list__headings.sticky {
  position: sticky;
  top: 0;
  background: #fbfbfb;
  z-index: 9;
  border: 1px solid #c7c7c7;
}

.customer-invoice, .list-item {
  display: flex;
  align-items: center;
  padding: 12px 10px;
  margin-bottom: 10px;
  border: 1px solid #ededed;
  border-radius: 15px;
  font-size: 14px;
  background: white;
  position: relative;
  .pay-button {
    margin: 0;
    padding: 8px 40px;
  }
  @include media-down(hamburger) {
    flex-direction: column;
    padding-top: 40px !important;
    .pay-button {
      padding: 4px 50px;
    }
    > div {
      margin-bottom: 6px;
    }
  }
}

.invoice-header {
  display: flex;
  align-items: center;
  .circle-icon {
    color: black;
    width: 40px;
    height: 40px;
    padding: 22px;
    margin-right: 30px;
    font-size: 13px;
  }
  @include media-down(hamburger) {
    flex-direction: column;
    .invoice-date {
      background: var(--icon-background);
      border: 1px solid rgb(209, 209, 209);
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      text-align: center;
      padding: 6px;
    }
    .circle-icon {
      display: none;
    }
  }
}

.invoice-overdue {
  margin: 0;
  border: 2px solid #ff628d;
  text-transform: uppercase;
  color: #ff628d;
  text-align: center;
  margin-right: 20px;
  font-weight: bold;
  padding: 4px;
  font-size: 12px;
  @include media-down(hamburger) {
    margin-right: 0;
  }
}

.invoice-not-overdue {
  text-align: center;
  margin: 0;
}

.search-invoices, .search-input {
  border-radius: 20px !important;
  border: none;
  box-shadow: 0 2px 5px 1px #403c4329;
  margin-right: 18px;
  padding: 7px 20px;
  background: url(/assets/icons/icon-search.svg) no-repeat;
  background-position: 96% 50%;
  background-size: 15px;
}

.delivery-details-suppliers--admin {
  margin-bottom: 10px;
  font-size: 14px;
  p {
    margin-bottom: 0;
  }
  &__show {
    margin-left: 0;
    p, strong {
      font-size: 16px;
    }
  }
}

.delivery-details-suppliers--customer {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.customer-data-list__headings{
  display: flex;
  padding: 12px 10px;
  color: #9f9f9f;
  font-size: 14px;
}

.customer-data {
  display: flex;
  align-items: center;
  padding: 12px 10px;
  margin-bottom: 10px;
  border: 1px solid #ededed;
  border-radius: 15px;
  font-size: 14px;
  background: white;
  &__flex-field {
    display: flex;
    align-items: center;
  }
  &__flex-action-field {
    display: flex;
    align-items: baseline;
    justify-content: right;
  }
  &.inactive {
    color: #a0a0a0;
  }
  .circle-icon {
    color: black;
    width: 40px;
    height: 40px;
    padding: 22px;
    margin-right: 30px;
    font-size: 13px;
  }
}

.boxed-data {
  padding: 0 10px 12px;
  margin-bottom: 10px;
  border: 1px solid #ededed;
  border-radius: 11px;
  background: white;
  cursor: pointer;
  &:hover {
    border: 1px solid #2BFFC6;
  }
  &.attached {
    &:hover {
      border: 1px solid #FA4290;
    }
    .po-order-banner {
      background: #FFFFF8;
    }
  }
}

.po-grouped-orders-heading {
  font-weight: bold;
  margin: 20px 0;
}

.po-order-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  align-items: start;
  grid-gap: 10px;
  border-top: 0;
  border-radius: 0 0 15px 15px;
  .po-order {
    p {
      margin: 0;
      font-size: 14px;
    }
    &-heading {
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 0;
    }
  }
}

.po-order-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #EFEFEF;
  margin: 0 -10px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  padding: 4px 10px;
  .circle-icon {
    margin-right: 0;
    width: 30px;
    height: 30px;
  }
}

.po-order-supplier {
  display: flex;
  align-items: center;
}

.budget-modal-slider {
  float: right;
  margin: 0 !important;
  padding-top: 0 !important;
  h2 {
    display: flex;
    justify-content: space-between;
  }
  .close-budget-form {
    font-family: $body-font-family;
    background: grey;
    background: #e7e7e7;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 18px;
  }
  .budget-label {
    font-weight: bold;
  }
  .react-datepicker-wrapper {
    margin-right: 4px;
  }
  .budget-range-container {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }
  .budget-amount-input {
    all: unset;
    padding: 10px 10px 0px 0px;
    border: none;
    border-bottom: 1px solid gray;
    margin-bottom: 20px;
    margin-top: 8px;
    font-size: 24px;
    &:focus-visible {
      outline: none;
    }
  }
  .budget-amount-dollar {
    color: $primary;
    border-bottom: 1px solid gray;
    padding-bottom: 5px;
    padding-right: 4px;
    font-size: 24px;
  }
  .budget-details {
    color: gray;
    margin: 0;
  }
  .edit-budget-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-gap: 6px;
    padding: 8px 20px;
    margin-left: -20px;
    margin-right: -20px;
    align-items: center;
    &:nth-of-type(even) {
      background: #fff8ef;
    }
    p, button {
      margin: 0;
    }
    button {
      background: black;
      color: white;
      padding: 6px 8px;
      height: initial;
      font-size: 14px;
      align-self: center;
    }
  }
  .update {
    background: $primary;
    color: white;
  }
  .budget-type {
    color: grey;
    grid-column: 1/5;
    font-family: $header-font-family;
  }
  .create-budget {
    display: flex;
    align-items: center;
    background: $primary;
    color: white;
    padding: 9px 14px;
    align-self: self-start;
    &::before {
      content: '';
      background-image: asset-data-url("icons/plus-circle-white");
      width: 24px;
      height: 24px;
      background-repeat: no-repeat;
      background-size: contain;
      margin-right: 12px;
    }
  }
  .budget-field {
    cursor: pointer;
    display: flex;
    align-items: center;
    border: none;
    box-shadow: 0 2px 5px 1px #534e5729;
    margin: 12px 0;
    color: #9f9f9f;
    padding: 14px 20px;
    height: initial;
    color: black;
    font-size: 16px;
    &.error {
      border: 1px solid $error;
    }
    &.po {
      position: relative;
      padding: 10px 20px;
      .dropdown {
        background-color: white;
        border-radius: 6px;
        box-shadow: 0px 1px 4px 5px #f1f1f1;
        box-sizing: border-box;
        max-height: 200px;
        position: absolute;
        margin-top: 4px;
        top: 100%;
        transform: translateX(-10px);
        width: 96%;
        z-index: 1000;
        padding: 10px;
        overflow-y: scroll;
      }
      .dashboard-filter__type {
        display: flex;
        align-items: center;
        width: 100%;
        &::before {
          content: "";
          display: inline-block;
          width: 18px;
          height: 18px;
          background: url(icons/order-black.svg) no-repeat;
          margin-right: 10px;
        }
        &::after {
          content: "";
          display: inline-block;
          width: 12px;
          height: 12px;
          background: url(icons/chevron-down-light-grey.svg) no-repeat;
          margin-left: auto;
          margin-top: 5px;
        }
      }
    }

  }
  .form-button-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 6px;
    margin-top: 2rem;
    .warning {
      background: #f45c5c;
    }
  }
}

.bold {
  font-weight: bold;
}
