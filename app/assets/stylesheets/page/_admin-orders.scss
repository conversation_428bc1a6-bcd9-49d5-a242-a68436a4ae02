.admin-orders {
  display: grid;
  grid-column-gap: 20px;
  .order-show__details {
    .supplier-list {
      display: flex;
    }
    .circle-icon.loading {
      width: 40px;
      height: 40px;
      @include shimmer;
    }
    .skeleton {
      height: 500px;
      background: grey;
      @include shimmer;
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #241c1573;
  z-index: 999;
  display: none;
}

.overlay.show {
  display: block;
}

.admin-orders {
  position: relative;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100%;
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  background: white;
  overflow: scroll;
  @include media-down(hamburger) {
    width: 100%;
    padding-bottom: 80px;
  }
}

.sidebar-overlay.open {
  animation: slide-in 0.3s forwards;
}

.sidebar-overlay.closed {
  animation: slide-out 0.3s forwards;
}

.admin-order-calendar-container {
  .Datepicker__input {
    font-size: 16px;
    background: white;
    color: black;
    border-radius: 20px;
    padding: 10px 50px;
    margin: 0 20px;
    @include media-down(hamburger) {
      padding: 10px 16px;
      margin: 0;
    }
  }
}

.admin-no-orders {
  margin: 0;
  font-weight: bold;
  font-size: 16px;
  color: grey;
  padding-left: 12px;
}

.calendar-date-change {
  display: flex;
  align-items: center;
  border-radius: 50%;
  background-color: white;
  aspect-ratio: 1 / 1;
  margin: 0;
  filter: invert(1);
  font-size: 7px;
  &::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 20px;
    background-image: asset-data-url('icons/chevron-back.svg');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }
  &.forward {
    transform: rotate(180deg);
  }
  &:hover {
    background-color: #e5798d;
  }
}

.weekdays {
  background: white;
  padding: 20px;
  border: 1px solid #dbdbdb;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  &.skeleton {
    height: 700px;
    background: grey;
    @include shimmer;
  }
  .list-item {
    background: none;
    border: none;
    border-bottom: 1px solid rgb(196, 196, 196);
    padding: 12px;
    border-radius: 0;
    margin: 0;
    cursor: pointer;
    @include media-down(hamburger) {
      padding-top: 8px !important;
    }
    &.active {
      background: $primary;
      border-radius: 6px;
      color: white;
      a, p {
        color: white;
      }
      .order-list-item__status {
        color: white;
      }
      .order-num {
        color: white !important;
      }
    }
    &:last-of-type {
      border-bottom: none;
    }
  }
  .order-list-item__status {
    text-overflow: ellipsis;
    overflow: hidden;
    @include media-down(hamburger) {
      text-align: center;
      text-overflow: initial;
      overflow: auto;
      white-space: initial;
    }
  }
}

a.reorder-button {
  text-transform: uppercase;
  padding: 6px 8px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid $primary;
  color: $primary;
  &:hover {
    background: $primary;
    color: white;
    border: none;
  }
}

.admin-order-slider {
  width: 400px;
  @include media-down(hamburger) {
    width: 100%;
  }
}

.admin-order-address {
  margin-left: 16px;
  p {
    margin-bottom: 0;
    color: grey;
    font-size: 14px;
    line-height: 18px;
  }
  @include media-down(hamburger) {
    text-align: center;
  }
}

.order-functions {
  @include media-down(hamburger) {
    display: block;
    .list-toggle {
      margin: 20px 0;
    }
  }
}

.notification-search {
  margin-bottom: 12px;
}

.admin-order-date {
  position: sticky;
  top: 0;
  background:white;
  z-index: 99;
  padding: 12px;
  font-weight: 900;
  font-size: 24px;
  padding-left: 12px;
  margin-bottom: 0;
  @include media-down(hamburger) {
    text-align: center;
  }
}

.admin-order-name {
  color: black;
}

.company-customers {
  margin: 0;
  margin-bottom: 30px;
  margin-left: 0;
  li {
    border-bottom: 1px solid $medium-gray;
    padding: 1rem 0.2rem;
    font-size: 14px;
    .remove { opacity: 0; }
    &:hover {
      color: $white;
      background: $primary;
      .remove { opacity: 1 }
    }
  }
}

.customer-permissions {
  margin: 0;
  margin-bottom: 30px;
  margin-left: 0;
  li {
    border-bottom: 1px solid $medium-gray;
    padding: 1rem 0.2rem;
    font-size: 14px;
  }
}

.new-order-dropdown {
  position: relative;
}

.admin-sidebar-list {
  min-height: 90vh;
  overflow: scroll;
}

.admin-sidebar-buttons {
  display: flex;
  justify-content: space-between;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  margin: 0;
  border-top: 1px solid rgb(192, 192, 192);
  padding: 16px 0;
  > * {
    margin: 0 8px;
  }
}

.admin-create-order {
  transform: translate(-100%, 20%);
  padding: 16px;
  &:before {
    content: '';
    border: none !important;
    width: 0;
    height: 0;
  }
  .Dropdown-option {
    cursor: pointer;
    margin-bottom: 8px;
    &:last-of-type {
      margin-bottom: 0;
    }
    &:hover {
      color: $primary;
    }
  }
}

.new-order-dropdown {
  .Dropdown-placeholder.is-selected {
    display: none;
  }
}

.admin-suppliers {
  .circle-icon {
    margin: 0;
    transform: translateX(-20%);
    &:first-of-type {
      transform: none;
    }
    &:last-of-type {
      margin-right: 10px;
    }
    color: #0066ff;
  }
}

.admin-flags {
  &::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }
  &.active::before {
    background-image: asset-data-url('icons/check.svg');
  }
  &.inactive::before {
    background-image: asset-data-url('icons/icon-cross-error.svg');
  }
  color: #de6363
}

.admin-order-toggle {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid #a8a8a8;
  color: #565656;
  padding: 4px;
  min-width: 130px;
  text-align: center;
  border-left: none;
  border-right: none;
  cursor: pointer;
  &.order {
    &::before {
      content: '';
      display: inline-block;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
    &:first-of-type {
      &::before {
        background-image: asset-data-url('icons/order-black.svg');
      }
    }
    &:last-of-type {
      &::before {
        background-image: asset-data-url('icons/date.svg');
      }
    }
  }
  &:first-of-type {
    border-left: 2px solid #a8a8a8;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
  }
  &:first-of-type.active {
    border-left: 2px solid $primary;
    border-right: 2px solid $primary;
  }
  &:last-of-type {
    border-right: 2px solid #a8a8a8;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    margin-right: 18px;
  }
  &:last-of-type.active {
    border-left: 2px solid $primary;
  }
  &.active {
    background: #e2fff9;
    color: $primary;
    border-color: $primary;
    font-weight: bold;
  }
}

.order-list-options {
  display: flex;
  align-items: center;
  background: #f6f6f6;
  border: 1px solid #c7c7c7;
  border-bottom: none;
  padding: 8px 30px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  p {
    margin-bottom: 0;
  }
  label {
    font-family: $header-font-family;
    text-transform: none;
    color: $primary;
    font-size: 14px; 
    color: #000;
    & + label {
      margin-left: 12px;
    } 
  }
  .checkbox-content-tick {
    background-color: white;
    width: 18px;
    height: 18px;
    top: 0px;
  }
}

.admin-new-customer {
  background: $highlight;
  color: white;
  font-size: 12px;
  border-radius: 8px;
  padding: 6px 10px;
  margin-right: 6px;
  &.small {
    padding: 2px 10px;
    font-size: 10px;
    font-weight: bold;
    margin-left: 12px;
  }
}

.admin-flag {
  color: white;
  font-size: 12px;
  border-radius: 50%;
  padding: 6px;
  color: black;
  font-weight: bold;
  & + .admin-flag {
    margin-left: 6px;
  }
  &.new {
    background: $highlight;
  }
  &.company {
    background: #2bffc6;
  }
  &.team {
    background: #24bbff;
  }
}

.admin-notification {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 10px;
  margin-bottom: 10px;
  border: 1px solid #ededed;
  border-radius: 15px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  &.read {
    background: #f2f6fc;
    opacity: 0.6;
  }
  b {
    font-weight: bold;
  }
  .circle-icon {
    background-color: #53FFBD;
    color: black;
    font-weight: bold;
  }
  &.error {
    .circle-icon {
      background-color: #FF8989;
    }
  }
  &.warning {
    .circle-icon {
      background-color: #FFCC6A;
    }
  }
}

.notification-count {
  filter: invert(1);
  background: $highlight;
  border-radius: 15px;
  min-width: 20px;
  height: 20px;
  text-align: center;
  padding: 0 5px;
  font-weight: bold;
  line-height: 1.5;
  &.hidden {
    display: none;
  }
  &.quotes {
    display: inline-block;
    filter: none;
    margin-left: auto;
    color: white;
  }
}

.simple-order {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: $primary;
  font-weight: bold;
  cursor: pointer;
  &::after {
    content: '';
    display: inline-block;
    background-image: asset-data-url('icons/chevron-down.svg');
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    transition: transform .3s ease-in-out;
  }
  &.open {
    &::after {
      transform: rotate(180deg);
    }
  }
}

.simple-order-details {
  background: #f9f9f9;
  padding: 2px 12px;
  margin-top: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  &__buttons {
    margin-top: 8px;
  }
}

.admin-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  filter: invert(1);
  padding: 6px 0;
  border-radius: 20px;
  margin-bottom: 20px;
  font-weight: bold;
  &.super-admin {
    box-shadow: 0 0 10px rgba(255, 98, 138, 0.7);
    color: #ff235e;
    border: 1px solid #ff628d;
  }
  &.account-manager {
    box-shadow: 0 0 10px rgba(255, 98, 138, 0.7);
    color: #ff235e;
    border: 1px solid #ff628d;
  }
  &.supplier-access {
    box-shadow: 0 0 10px rgba(255, 98, 138, 0.7);
    color: #ff235e;
    border: 1px solid #ff628d;
  }
  &.pantry-manager {
    box-shadow: 0 0 12px rgba(255, 198, 38, 0.7);
    color: #ffc626;
    border: 1px solid #ffc626;
  }
  &.company-team {
    box-shadow: 0 0 12px #76E2F4;
    color: #76E2F4;
    border: 1px solid #76E2F4;
  }
  p {
    margin-bottom: 0;
    line-height: 18px;
    font-family: $header-font-family;
  }
}

// calendar styling
.weekly-calendar-days {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  align-items: center;
  > span, div {
    min-width: 130px;
    text-align: center;
  }
  > span {
    font-weight: bold;
  }
}
.calendar-week {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  &-dates {
    font-weight: bold;
    padding-top: 12px;
    text-align: center;
  }
  &.striped {
    background: #f5f5f5;
    .calendar-event {
      border-bottom: 1px solid #eaeaea;
    }
  }
  > div {
    min-width: 130px;
    min-height: 140px;
  }
}

.calendar-day {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #eaeaea;
  border-left: none;
  position: relative;
  & + .calendar-day {
    border-left: none;
  }
  &.today {
    border: 2px solid $primary;
    background: #f0f9f0;
  }
  .day-number {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 12px;
    font-weight:bold;
    margin-left: 4px;
    color:black;
    display:inline-block;
    &.spacer {
      position: static;
      color: grey;
    }
    &.today {
      text-align: center;
      width: 20px;
      height: 20px;
      line-height: 20px;
      background: $primary;
      border-radius: 50%;
      color:white;
      font-weight: bold;
    }
  }
}

.calendar-event {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-top: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
  &.link {
    position: relative;
    color: black;
    font-size: 16px;
    transition: background .1s ease-in;
    cursor: pointer;
    &:hover {
      background: #dedede;
    }
  }
  &.un-assigned {
    box-shadow: 0 0 10px inset $alert-color;
    font-weight: 800;
  }
  &.filtered-out {
    opacity: 0.1;
  }
  .calendar-view-slider {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  &--holiday {
    background-color: #ffe6e6;
    padding: 0;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    > div {   
      max-width: 150px;
      text-wrap: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .event-image {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    margin-right: 8px;
  }
  .event-info {
    border: 1px solid black;
    margin-left: auto;
    padding: 4px 8px;
    border-radius: 4px;
    &.week-long {
      color: white;
      background-color: black;
    }
  }
  @include media-down(desktop) {
    .event-image {
      width: 30px;
      height: 30px;
    }
    .event-time {
      font-size: 14px;
    }
    .event-info {
      padding: 2px 4px;
      font-size: 12px;
    }
  }
}

.copy-staff-register-link {
  display: flex;
  align-items: center;
  width: 210px;
  padding: 4px 8px;
  border: 1px solid #1f9e86;
  color: #1f9e86;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  text-align: center;
  margin-bottom: 12px;
  span {
    margin: auto;
  }
  &::before {
    display: inline-block;
    content: '';
    background: asset-data-url('icons/copy.svg');
    background-repeat: no-repeat;
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}