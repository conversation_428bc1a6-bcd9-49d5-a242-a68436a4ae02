span.coupon {
  display: block;
  text-align: right;
}

.coupon-input {
  width: 275px;
  display: inline-block;
  height: 37px;
  padding: 10px;
  border: 1px solid #ddd;
  background: white;
}

.apply-coupon {
  display: inline-block;
  position: relative;
  margin: 0px 0px 0px 4px;
  padding: 10px;
  top: -1px;
}

span.coupon-message {
  display: block;
  font-size: 13px;
  padding-bottom: 15px;
  padding-right: 2px;
  color: green;
}

span.coupon-message.error {
  color: $primary;
}

.woolworths-order-validation-errors {
  padding: 0;
  margin-top: 1em;
  border: 1px solid #e7eaf0;

  ul {
    margin: 0;
    list-style-type: none;
    li {
      padding: 3px 0;
    }
  }
}

.order-line-info{
  &.processing { background-color: #efefef;}
}

.woolworths-info{
  padding: 14px;
}

.woolworths-order-delivery-window-text{
  margin-bottom: 6px;
}


.woolworths-order-validation-errors {
  .woolworths-order-delivery-window-heading {
    &::before{
      content: asset-data-url("icons/truck.svg");
      margin-left: 11px;
      margin-right: 10px;
    }
  }
  .woolworths-order-delivery-window-wrapper {
    margin-top: 0.5em;
  }

  .woolworths-heading {
    color: #FBFBF4;
    font-size: 16px;
    text-transform: uppercase;
    font-weight: bold;
    background: #333;
    padding-left: 20px;
    height: 45px;
    display: flex;
    align-items: center;
    margin-bottom: 0;
  }
  .delivery-window {
    color: $primary;
    font-weight: bold;
  }

  .woolworths-delivery-window {
    display: inline-block;
    margin: 0.3em !important;
    padding: 0.4rem;
    &.selected {
      background: $primary;
      border: 1px solid $primary;
      color: #ffffff;
    }
  }
}

.delivery-window-day {
  background-color: #fefefe;
  float: left;
  padding: 0.5rem;
  border: 1px solid #ededed;
  font-size: 11px;
  text-align: center;
  cursor: pointer;
  &.active {
    background-color: $primary;
    border: 1px solid $primary;
    color: #FFF;
  }
}

.delivery-window-day-panel {
  display: none;
  padding: 1rem;
  &.active {
    display: block;
  }
}
