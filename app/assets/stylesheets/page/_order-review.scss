.star {
  display: inline-block;
  &::before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    background-size: contain;
    background-image: asset-data-url("icons/star.svg");
  }

  &.selected::before {
    background-image: asset-data-url("icons/star-selected.svg");
  }
}

#order-lines-table {
  position: relative;
  .table-order__table {
    border-bottom: 0;
    tbody td {
      padding: 0.625rem;
    }
  }
}

.summary-page {
  max-width: 45rem;
}

.table-order {
  .button.outline-button {
    background-color: white;
    color: $yordar-black;
    border-color: $yordar-black;
    margin: 0;
  }

  tfoot {
    background-color: transparent;
    color: #333;
  }

  tfoot td {
    font-weight: 400;
    &.right {
      text-align: right;
    }
  }

  .value-counter {
    display: table;
    margin: 0 auto;
    min-width: 98px;
  }
}

.table-order__location-select {
  width: 224px;
  background: white;
  border: 1px solid #ddd;
  background-image: asset-data-url("icons/chevron-down.svg");
  background-repeat: no-repeat;
  background-position: 95% !important;
  background-size: 14px;
}

.day-orders {
  position: absolute;
  right: 10px;
  text-align: right;
}

.table-order__order-button {
  background-color: white;
  color: $mammoth-charcoal;
  border-color: $mammoth-charcoal;
  text-transform: uppercase;
  &:hover{
    background-color: $mammoth-charcoal;
    color: white;
  }
}
.table-order__active-order-button {
  background-color: $mammoth-charcoal;
  color: white;
}

tr.table-order__title-row {
  background-color: #333333;
  height: 40px;
  text-align: left;
}

tr.table-gap {
  height: 15px;
  &.large {
    height: 30px;
  }
}

tr.table-border td {
  border-top: 2px solid #333333;
}

tbody.table-order__details {
  border: 1px solid #e7eaf0;
}

th.table-order__subtitle {
  color: #fbfbfb;
  padding: 10px;
  background: #333;
  text-transform: uppercase;
  text-align: left;
  font-size: 16px;
  &::before {
    content: asset-data-url("icons/invoice2.svg");
    margin-left: 20px;
    margin-right: 6px;
  }
}

tr.table-order__headers-row {
  text-align: left;
  th {
    font-size: 12px;
    padding: 0.625rem;
  }
}

tr.total-bold {
  border-top: 2px solid #ddd;
  text-transform: uppercase;
  td {
    font-size: 18px;
    font-family: $header-font-family;
  }
}
.recurring-switch-link {
  color: $primary;
  font-size: 16px !important;
  &:hover {
    color: darken($primary, 10%);
  }
}
.menu-return-btn {
  background-color: white;
  color: $yordar-black;
  border-color: $yordar-black;
}

.table-order__item--centered {
  text-align: center;
}

.error-msg {
  color: $error;
}

.summary-btns {
  display: flex;
  justify-content: space-between;
  @include media-down(hamburger) {
    flex-direction: column-reverse;
  }
  .button {
    width: 30%;
  }
  @include media-down(hamburger) {
    .button {
      width: 100%;
    }
  }
}
