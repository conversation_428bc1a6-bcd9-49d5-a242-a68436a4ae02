.customer-sticky-sidebar {
  top: 0;
  min-width: 250px;
  @include media-down(hamburger) {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 998;
  }
}
.dashboard-table {
  @include media-down(hamburger) {
    width: 100%;
    .personalised__tag {
      margin-right: 16px;
    }
  }
}

.supplier-icon-header {
  @include media-down(hamburger) {
    display: none;
  }
}

.customer-area-sidebar {
  position: sticky;
  top: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: white;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.15);
  overflow-y: scroll;
  height: 100vh;
  padding: 0 24px;
  max-width: 250px;
  &.invert {
    filter: invert(1);
  }
  @include media-down(hamburger) {
    padding: 0 20px 80px;
    max-width: initial;
  }
  &__image {
    display: flex;
    align-items: center;
    margin: 16px 0;
    img {
      width: 95px;
      height: auto;
    }
  }
  &__title {
    font-size: 14px;
    text-transform: uppercase;
    color: #828D99;
  }
  &__admin-title {
    font-size: 20px;
    font-weight: 800;
    text-transform: uppercase;
    color: $black;
  }
  &__ul {
    display: block;
    margin: 0;
    & > li {
      font-size: rem-calc(14);
      font-weight: 500;
      &.nested-list {
        .customer-sidebar-link {
          display: flex;
          align-items: center;
          &::after {
            content: '';
            display: inline-block;
            background: asset-data-url('icons/chevron-down') 50% no-repeat;
            transform: rotate(270deg);
            width: 14px;
            height: 14px;
            margin-left: auto;
          }
        }
        &.active {
          .customer-sidebar-link::after {
            background: asset-data-url('icons/chevron-down-white') 50% no-repeat;
            transform: rotate(0);
          }
          .customer-sidebar-link::before {
            filter: invert(1);
          }
        }
        &.open {
          .customer-sidebar-link::after {
            background: asset-data-url('icons/chevron-down') 50% no-repeat;
            transform: rotate(0);
          }
        }
      }
      .customer-sidebar-link__access::after {
        content: 'ADMIN';
        background: #fb35dc;
        color: white;
        font-size: 10px;
        font-weight: bold;
        padding: 8px 5px;
        margin-left: 8px;
        height: 14px;
        align-items: center;
        border-radius: 4px;
        display: flex;
        align-items: center;
        line-height: 1px;
      }
      & > a {
        display: flex;
        align-items: center;
        color: #191919;
        font-size: 14px;
        padding: 10px 17px;
        margin: 0 4px 10px;
        border-radius: 4px;
        &:hover {
          background: #E8EDF2;
        }
        &.shortcut {
          padding: 10px 5px;
        }
      }
      &.active {
        cursor: pointer;
        & > a {
          color: #F4F0EB;
          background: #191919;
          border-top: none;
        }
      }
    }
    .nested {
      padding: 6px 0;
      margin: 0;
      & > li {
        & > a {
          color: #191919;
          opacity: 0.5;
          font-size: 14px;
          transition: background-color 0.25s ease-out, color 0.25s ease-out;
          background: none;
          border-radius: 10px;
          margin: 0 1rem;
          margin-left: 22px;
          &:hover {
            cursor: pointer;
            opacity: 1;
          }
        }
        &.active {
          & > a {
            cursor: default;
            color: #191919;
            opacity: 1;
            background: none;
          }
        }
      }
    }
  }
  &__li {
    transition: background-color 0.25s ease-out, color 0.25s ease-out;
    color: #191919;
    margin: 0 -20px;
    &:hover {
      cursor: pointer;
    }
  }
  &__button {
    border: 1px solid black;
    color: black;
    border-radius: 4px;
    padding: 6px;
    display: inline-block;
    font-size: 14px;
    margin-bottom: 20px;
    &:hover {
      color: black;
    }
    &:last-of-type {
      margin-left: 10px;
    }
    @include media-down(hamburger) {
      display: inline-block;
    }
    @include media-up(hamburger) {
      display: none;
    }
  }
  &__shortcuts {
    margin-bottom: 20px;
  }
}


.orders-list, .team-orders-list, .quotes-list, .with-favourite-heart, .billing-list, .reports-list, .surveys-list, .settings-list, .catering, .snacks, .meal-plans-icon {
  &::before {
    content: '';
    display: inline-block;
    background-size: contain;
    width: 24px;
    height: 24px;
    background-position: center;
    background-repeat: no-repeat;
    margin-right: 12px;
  }
}

.customer-area-sidebar__li.active {
  .customer-sidebar-link::before {
    filter: invert(1);
  }
}

.orders-list::before {
  background-image: asset-data-url('icons/orders-list.svg');
}

.catering::before {
  background-image: asset-data-url('icons/catering-black.svg');
}

.snacks::before {
  background-image: asset-data-url('icons/snacks-black.svg');
}

.meal-plans-icon::before {
  background-image: asset-data-url('icons/meal-plans.svg');
}

.team-orders-list::before {
  background-image: asset-data-url('icons/team.svg');
}

.quotes-list::before {
  background-image: asset-data-url('icons/quote.svg');
}

.with-favourite-heart::before {
  background-image: asset-data-url('icons/heart.svg');
}

.billing-list::before {
  background-image: asset-data-url('icons/billing.svg');
}

.reports-list::before {
  background-image: asset-data-url('icons/chart.svg');
}

.surveys-list::before {
  background-image: asset-data-url('icons/survey.svg');
}

.settings-list::before {
  background-image: asset-data-url('icons/settings.svg');
}