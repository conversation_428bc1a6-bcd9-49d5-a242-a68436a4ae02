.supplier-show-container {
  background: #f9f9f9;
}
.supplier-show-wrapper {
  display: flex;
  margin: 0 auto;
  max-width: 1400px;
  padding: 0 20px;
  @include media-down(hamburger) {
    padding: 0 8px;
    .supplier-details-container {
      width: 100%;
    }
    .supplier-banner {
      display: block;
    }
    .docket-container {
      display: none;
      width: 100%;
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 999;
    }
    .docket {
      height: 100%;
      margin-top: 0;
    }
    .docket-wrapper {
      max-height: initial;
      height: 100%;
    }
    .menu-items-search {
      display: none;
    }
    .menu-item-modal {
      width: 100%;
    }
  }
}
.supplier-banner {
  display: flex;
  max-width: 1200px;
  margin: 0 auto 20px;
}
.supplier-details-container {
  width: 70%;
  z-index: 1;
  margin-top: 2rem;
  margin-bottom: 3rem;
}

.docket-open {
  @include media-up(hamburger) {
    display: none;
  }
  position: fixed;
  bottom: 0%;
  left: 50%;
  width: 100%;
  transform: translate(-50%);
  background: black;
  color: white;
  font-weight: bold;
  padding: 10px 30px;
  text-align: center;
  z-index: 1;
  button {
    text-transform: uppercase;
  }
}

.docket-close {
  @include media-up(hamburger) {
    display: none;
  }
  width: 100%;
  padding: 6px;
  background: black;
  color: white;
  &:hover {
    background: lighten(black, 5%)
  }
}



.supplier-banner-image {
  flex: 1;
  max-width: 400px;
  height: 220px;
  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
    opacity: 1;
  }
}
.supplier-banner-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 20px;
  h1 {
    font-size: 32px;
    margin-bottom: 0;
    padding: 4px 0;
    font-weight: bold;
    line-height: 1.1em;
  }
  p {
    margin: 0;
    font-size: 14px;
    color: #555;
  }
}

.supplier-banner-navigation {
  line-height: 1;
  &__link {
    color: $primary;
    font-family: $header-font-family;
    &:hover {
      color: $primary;
      text-decoration: underline;
    }
  }
}

.icon-display {
  display: flex;
  img {
    width: 28px;
    height: 100%;
    padding: 0;
    position: relative;
    border: 1px solid;
    border-radius: 50%;
    white-space-collapsing: discard;
  }
}

.menu-item-modal-content {
  position: relative;
  max-height: 500px;
  overflow-y: scroll;
  padding: 8px 24px 16px;
}

.menu-item-modal-heading {
  display: flex;
  justify-content: space-between;
  font-family: $body-font-family;
  padding: 20px 24px 8px;
  font-size: 23px;
  text-transform: capitalize;
  margin: 0;
}

.menu-item-modal-close {
  color: black;
  text-transform: none;
  &:hover {
    color: black;
  }
}

.menu-item-modal-description {
  margin-bottom: 30px;
  color: #828585;
}

.menu-item-modal-image {
  width: 100%;
  height: 220px;
  object-fit: cover;
  margin-bottom: 30px;
  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
    opacity: 1;
  }
}

@keyframes skeltonAnimation {
  0% {
    left:  -30%;
  }
  100% {
    left:  130%;
  }
}

.menu-skeleton {
  background: #e2e2e2 !important;
  position: relative;
  overflow: hidden;
  color: white;
}
.menu-skeleton::before {
  content: '';
  position: absolute;
  background: linear-gradient(to right, #e2e2e2 25%, #d5d5d5 50%, #e2e2e2 100%);
  filter: blur(5px);
  animation: 1s linear 0s infinite skeltonAnimation;
  height: 100%;
  width: 80px;
  top: 0;
}

.menu-skeleton-div {
  padding: 0.7rem 1rem;
}

.drag-handle {
  float: left;
}

.expandable {
  transition: 0.3s ease-in-out width;
}
