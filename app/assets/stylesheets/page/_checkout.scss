.summary-body {
  background: #fbfbfb;
}

.reveal.react-responsive-modal-modal {
  top: auto;
}

.checkout-container {
  padding: 0;
  background: #fbfbfb;
  .row {
    max-width: inherit;
    margin-right: 0;
    margin-left: auto;
    position: relative;
  }
  .checkout-btns {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 50px;
    @include media-down(hamburger) {
      flex-direction: column-reverse;
      text-align: center;
    }
    &__back {
      font-size: 16px;
      color: $medium-gray;
      &::before {
        content: "";
        display: inline-block;
        background: asset-data-url("icons/back-arrow.svg");
        background-repeat: no-repeat;
        background-position: center;
        width: 20px;
        height: 10px;
      }
    }
    &__forward {
      text-transform: uppercase;
      background-color: $primary;
      color: white;
      border-radius: 4px;
      padding: 10px 25px;
      font-size: 14px;
      &:hover {
        background-color: darken($primary, 10%);
        cursor: pointer;
      }
      @include media-down(hamburger) {
        margin-bottom: 25px;
        width: 100%;
      }
    }
    &__alternate {
      background: transparent;
      border: 1px solid $primary-color;
      color: $primary-color;
      transition: all 0.25s ease-out;
      &:hover {
        background: $white;
      }
    }
  }
}

.customer-order {
  padding: 45px 30px;
  background-color: white;
  height: 100%;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  overflow-y: scroll;
  @include media-down(hamburger) {
    border-bottom: 1px solid #eee;
  }
  &__supplier-info {
    margin-bottom: 12px;
    img {
      border-radius: 4px;
    }
  }
  &__supplier-banner {
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    img {
      width: 40px;
      height: 40px;
    }
  }
  &__supplier-name {
    margin-left: 20px;
    font-weight: bold;
    font-size: 16px;
  }
  &__ol-quantity {
    width: 3rem;
    color: #de1f52;
    font-size: 15px;
    font-weight: bold;
    padding-right: 1rem;
  }
  &__ol-name {
    padding: 0 10px;
  }
  &__ol-price {
    margin-left: auto;
    @include media-down(hamburger) {
      margin-left: 0;
    }
  }
  &__heading {
    margin: 0;
    padding-bottom: 15px;
  }
  &__totals {
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
  @include media-down(hamburger) {
    position: static;
  }
}

.modal-drawer {
  .order-map {
    padding: 0;
    margin-bottom: 1rem;
  }
  .customer-order {
    &__supplier-banner {
      padding-bottom: 0.5rem;
      img {
        border-radius: 50%;
      }
    }
    &__ol-container {
      padding-left: 4rem;
    }
    &__ol-quantity {
      color: #191919;
      font-size: 14px;
      padding-right: 2rem;
    }
    &__ol-name {
      width: auto;
    }
    &__ol-extras {
      padding-left: 4rem;
      font-size: 12px;
      color: #7f7f7f;
      margin-top: -0.8rem;
      span {
        font-weight: bold;
      }
    }
    &__ol-note {
      padding-left: 4rem;
      font-size: 12px;
      color: #828d99;
      font-style: italic;
      margin-top: -0.8rem;
      span {
        font-weight: bold;
      }
    }
  }
}

.main {
  border-bottom: 1px solid #d4d9dc;
  margin-bottom: 10px;
  font-size: 18px;
}

.content {
  background-color: white;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
  border-bottom: 1px solid #d4d9dc;
  margin-bottom: 10px;
  &--no-border {
    border: none;
    margin: 0;
  }
}

.with-chevron {
  cursor: pointer;
  &::after {
    content: "";
    width: 8px;
    height: 8px;
    background-image: asset-data-url('icons/chevron-down.svg');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 14px;
  }
}

.checkout-module {
  overflow: hidden;
  position: relative;
  background: #fbfbfb;
}
.details-and-success {
  padding: 40px 110px;
  @include media-down(hamburger) {
    padding: 40px 10px;
  }
}

.add-new-credit-card-btn {
  border: 1px solid $primary;
  color: $primary;
  background: white;
  padding: 0.5em 1em;
}

.stripe-element {
  padding: 10px 8px;
  border-radius: 4px;
}

.action-bar {
  .checkout-progress-heading {
    font-size: 14px;
    font-weight: normal;
    text-transform: uppercase;
    font-family: "Museo Sans", sans-serif;
    padding-bottom: 20px;
    border-bottom: 1px solid #d4d9dc;
    color: $medium-gray;
    position: relative;
    background: $light-white;
    &.active {
      border-color: $primary;
      color: $primary;
      z-index: 10;
    }
  }
}

.order-details__container {
  margin-top: 2rem;
  background: #ffffff;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.checkout__fields-container {
  padding: 15px 30px;
}

.payment-details__container {
  padding-bottom: 15px;
  background: white;
}

.credit-card-charge-info-new {
  padding: 10px;
}

.select2-container .select2-selection--single {
  height: 37px;
}

.checkout-form {
  padding: 0;
  box-shadow: none;
  background: $light-white;
  .form-title {
    font-size: rem-calc(30);
    margin-bottom: 2rem;
  }
  .form-description {
    color: black;
    font-family: $body-font-family;
    margin-bottom: 2rem;
    margin-top: -1rem;
    a {
      text-decoration: underline;
    }
  }
  p {
    color: $medium-gray;
  }
  .columns {
    position: relative;
  }
  .callout {
    font-size: rem-calc(12.5);
    margin: 10px 0;
    b {
      color: $black;
    }
  }
}

@media screen and (max-width: 1023px) {
  .checkout-form {
    .callout {
      position: static;
    }
  }
}

@media screen and (max-width: 639px) {
  .checkout-module {
    padding: 1rem;
  }
  .action-bar.expanded {
    display: block;
    li {
      display: block;
      &:nth-child(2) {
        a {
          left: auto;
          width: auto;
        }
      }
      &:first-child {
        a {
          border-top-left-radius: $global-radius;
          border-top-right-radius: $global-radius;
        }
      }
      &:last-child {
        a {
          border-bottom-left-radius: $global-radius;
          border-bottom-right-radius: $global-radius;
        }
      }
      a {
        border-radius: 0;
      }
      &.active {
        a {
          border: 1px solid $black;
        }
      }
    }
  }
}

.payment-options {
  display: flex;
  justify-content: space-between;
  &__toggle {
    background: #f0f0f0;
    font-family: "Museo Slab";
    flex: 1;
    cursor: pointer;
    text-transform: uppercase;
    padding: 14px 40px;
    border-bottom-left-radius: 6px;
    &:first-of-type {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 6px;
    }
    &.active {
      background: white;
      color: black;
      text-decoration: underline;
      text-decoration-color: $primary;
      text-underline-position: under;
      text-decoration-thickness: 2px;
    }
    &.disabled {
      pointer-events: none;
      background: #b6b6b6;
    }
    p {
      font-size: 13px;
      margin: 0;
    }
  }
  &__panel {
    display: flex;
    align-items: center;
    padding: 20px 40px !important;
    &--content {
      flex: 1;
      max-width: 500px;
    }
    &--card-heading {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    h3 {
      font-size: 22px;
      font-weight: 700;
    }
    h5 {
      font-size: 18px;
      font-weight: 700;
    }
    p {
      font-size: 14px;
    }
  }
}
.card-charge-note {
  margin: 0.5rem 0 1rem 0;
  padding: 0.4rem;
  font-size: 12px;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  position: relative;
  background-color: #fff3d9;
  color: #888;
  .card-deprecation {
    color: #c71e2a;
  }
}

// select 2 creatable styles
.form-input {
  &.error {
    border: 1px solid red;
  }
}

@keyframes skeltonAnimation {
  0% {
    left:  -30%;
  }
  100% {
    left:  130%;
  }
}

.form-skeleton {
  background: #e2e2e2 !important;
  position: relative;
  overflow: hidden;
  color: white;
  &:before {
    content: '';
    position: absolute;
    background: linear-gradient(to right, #e2e2e2 25%, #d5d5d5 50%, #e2e2e2 100%);
    filter: blur(5px);
    animation: 1s linear 0s infinite skeltonAnimation;
    height: 100%;
    width: 80px;
    top: 0;
  }
}

.checkout-modal {
  .modal-footer {
    margin: 0 -1.25rem -1.25rem; // make it full width
    padding: 1rem 1rem 0;
    border-top: 1px solid #dee0e4;
  }
  &.submission {
    min-height: 350px;
  }
}

.saved-address {
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  margin-bottom: 16px;
  cursor: pointer;
  strong {
    font-size: 16px;
  }
  &:hover {
    outline: 1px solid $secondary-darker;
  }
}
.saved-address-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  background: #ffe3c933;
  padding: 4px 10px;
  .button {
    margin: 0;
  }
}

.saved-address-instructions {
  padding: 6px 10px 10px;
  font-size: 14px;
  strong {
    font-size: 14px;
  }
}

.checkout-hint {
  display: block;
  margin-top: $form-spacing * -0.5;
  margin-bottom: $form-spacing;
  font-size: $input-error-font-size;
}
