.team-order-header {
  padding: 1.3rem 1rem;
  &__heading {
    font-family: $body-font-family;
    margin-top: 5px;
    margin-bottom: 0;
    &--event-details {
      margin-left: 0.6rem;
    }
    &--edit {
      padding: 1rem 1rem;
    }
  }
  &__add {
    @include button(
      $background: #191919,
      $background-hover: lighten(#191919, 10%)
    );
    width: 100%;
    padding: 11px 15px;
    margin: 0;
    float: right;
  }
  .team-order-dropdown {
    padding-left: 0.3rem;
    padding-right: 0.3rem;
  }
}

.team-order-filter {
  border-radius: 3px;
  border: 1px solid #e7e7e7;
  margin: 0;
  background: white;
  height: 2.3125rem;
  padding: 0.5rem;
  width: 100%;
  padding-left: 35px;
  color: #b5c0cd;
  font-size: 0.875rem;
  &--search {
    background: asset-data-url('icons/icon-search.svg') 0.5rem center no-repeat;
    background-size: 14px;
    color: black;
  }
  &--teams,
  &--category,
  &--other {
    padding: 0.5rem;
  }
  &::placeholder {
    color: #b5c0cd;
  }
  &:focus {
    background-color: white;
    border: 1px solid #191919;
  }
  button {
    line-height: inherit;
  }
  @include icons_team_order_dropdowns;
}

.team-order-list {
  &__headings {
    padding: 0 1.5rem 0.5rem;
  }
  &__heading {
    font-size: 12px;
    color: #828d99;
    margin-top: 5px;
    text-transform: uppercase;
    &--highlight {
      color: $primary;
      font-weight: bold;
      text-align: right;
    }
  }
  &__table {
    height: 55vh;
    overflow-y: scroll;
  }
  &__row {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e7e7e7;
    &--show-page {
      padding: 1rem 0;
      border-top: 1px solid #e7e7e7;
    }
  }
  &__cell {
    font-size: 14px;
    margin-top: 9px;
    &--greyed {
      color: #828d99;
    }
    &--show-page {
      margin-top: 8px;
    }
  }
  &__btn {
    background: #f2f4f4;
    color: black;
    font-size: 25px;
    font-weight: bold;
    transition: all 0.3s;
    float: right;
    margin-right: 0;
    &.contact-selected {
      background: $primary;
      color: white;
    }
    &:hover {
      background: darken(#f2f4f4, 10%);
    }
    &.delete-contact:hover {
      color: black;
    }
  }
  &__supplier-btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 2px;
    .supplier-menu {
      margin: 0;
      background: #f2f4f4;
      color: #5f5f5f;
      padding: 8px 16px;
      font-size: 14px;
      &:hover {
        background: darken(#f2f4f4, 10%);
      }
    }
  }
  &__magic-setup {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
  }
}

.no-team-contacts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 5em auto 0;
  max-width: 600px;
  margin-top: 5em;
  &__text {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 0.625rem;
  }
  img {
    margin-bottom: 1rem;
  }
}

.team-supplier {
  &-image-container {
    img {
      border-radius: 50%;
      width: 40px;
      height: 40px;
    }
  }
  &-name {
    padding-left: 1rem;
    &.new-supplier {
      background: black;
    }
  }
  &-new {
    background: #fb35dc;
    border-radius: 4px;
    text-align: center;
    font-size: 10px;
    font-weight: bold;
    color: white;
    margin-left: auto;
    text-transform: uppercase;
    padding: 3px 5px;
  }
  &-custom {
    background: black;
    border-radius: 4px;
    text-align: center;
    font-size: 10px;
    font-weight: bold;
    color: white;
    margin-left: auto;
    text-transform: uppercase;
    padding: 3px 5px;
    white-space: nowrap;
  }
  &-rating {
    &::before {
      content: "";
      display: inline-block;
      width: 9px;
      height: 9px;
      border-radius: 50%;
      // background: #f2f4f4;
      margin-right: 4px;
    }
    &--approval::before {
      background: $primary;
    }
  }
}

.choose-supplier-btn {
  border-radius: 50%;
  padding: 0.4rem;
  margin-right: 0;
  &:before {
    content: "+";
  }
  &.selected-supplier {
    background: $primary;
    color: #fff;
    &:before {
      content: "x";
    }
  }
}

.team-order-label {
  color: #828d99;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 12px;
}

.team-order-event-details {
  margin-right: 7rem;
  margin-left: 1.8rem;
  padding-right: 11rem;
  padding-bottom: 4rem;
  border-top: 1px solid #e7e7e7;
  &__input {
    padding: 0;
    padding-top: 1.3rem;
    padding-right: 1rem;
    select {
      margin-bottom: 0;
    }
  }
  .skip-attendee-invite-toggle {
    margin-top: 8px;
  }
}

.multiple-days-label {
  display: inline-block;
  margin-left: 12px;
  color: $primary;
  background: #eaeaea;
  border-radius: 10px;
  padding: 0 8px;
  letter-spacing: normal;
}

.team-order-type-selection {
  margin-top: 1.3rem;
  border-bottom: 1px solid #e7e7e7;
  padding-bottom: 1.3rem;
  .button {
    margin: 0;
    font-weight: bold;
    background: #b7b7b7;
    &.active {
      background: $primary;
    }
    &:last-of-type {
      margin-left: 6px;
    }
  }
  span {
    font-weight: bold;
    margin-right: 20px;
  }
}

.multi-day-supplier-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
  border-top: 1px solid #f0f1f2;
  border-bottom: 1px solid #f0f1f2;
  margin: 0;
}

.supplier-day {
  text-align: center;
  border-right: 1px solid #f0f1f2;
  font-weight: bold;
  padding: 8px;
  background: #fafafa;
  cursor: pointer;
  &.active {
    background: white;
    border-bottom: 2px solid $primary;
  }
  &.selected-supplier:after {
    background: asset-data-url('icons/check.svg') center/15px no-repeat;
    content: "";
    margin-left: 1rem;
    width: 15px;
    height: 15px;
    display: inline-block;
    vertical-align: middle;
  }
}

.bulk-upload-info {
  text-align: center;
  a {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: underline;
    color: black;
    margin-bottom: 2rem;
    &::before {
      content: "";
      display: inline-block;
      background: asset-data-url('icons/example-csv') 50% center no-repeat;
      width: 25px;
      height: 25px;
    }
  }
  p {
    font-size: 14px;
    color: #8c96a1;
  }
}

.add-contact-footer {
  position: absolute;
  bottom: 0;
  left: 1.5rem;
  right: 1.5rem;
}

.invite-btn {
  &:before {
    content: "+";
  }
}

.uninvite-btn {
  background: $primary;
  color: white;
  &:before {
    content: "-";
  }
  &:hover {
    background: darken($primary, 5%);
  }
}

.mobile-tag {
  display: none;
}

.team-order-show {
  margin-top: 1rem;
  &__contact-list {
    background: white;
    border-radius: 4px;
    border: 1px solid #DBDBDB;
    margin-bottom: 60px;
  }
  .change-payment-details {
    margin-bottom: 1rem;
    border: 1px solid black;
    padding: 4px 8px;
    font-size: 12px;
    color: black;
    border-radius: 4px;
    &:hover, &:focus {
      background: black;
      color: white;
    }
  }
}

.team-order-attendee-numbers p {
  font-size: 14px;
  margin: 0.25rem 0.5rem;
}

.invite-link-info--copied {
  animation: fade-in 0.5s;
  color: #4e9e2d;
  margin-bottom: 4px;
}

.team-order-attendees {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 6px 0;
  }
  &__table-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    &--name {
      display: flex;
      align-items: center;
      &::after {
        content: "";
        display: inline-block;
        vertical-align: bottom;
        width: 12px;
        height: 12px;
        background: asset-data-url('icons/chevron-down-grey') no-repeat;
        margin-left: 14px;
      }
    }
    p {
      color: gray;
      font-size: 12px;
      margin: 0;
      text-transform: uppercase;
    }
  }
  &__show-orders {
    background: black;
    color: white;
    padding: 8px 12px;
    font-size: 13px;
    &:hover,
    &:focus {
      background: lighten(black, 5%);
    }
  }
  &__show-orders--hollow {
    background: white;
    border: 1px solid black;
    color: black;
    &:hover,
    &:focus {
      background: white;
      font-weight: bold;
      color: black;
    }
  }
}

.team-order-attendees-status {
  margin-bottom: 0;
  font-size: 14px;
  margin-right: 12px;
  color: gray;
  &::before {
    content: "";
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 4px;
  }
  @include icons_order_attendee_status;
}

.team-order-attendee-status {
  margin-bottom: 0;
  text-transform: capitalize;
  font-size: 14px;
  margin-top: 8px;
  &::before {
    content: "";
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
  }
  @include icons_order_attendee_status;
}

.team-attendee-order-line {
  margin-top: 4px;
  font-size: 12px;
  margin-bottom: 0;
  &__quantity {
    font-weight: bold;
    padding-left: 0.5rem;
    padding-right: 0.25rem;
  }
}

@include media-down(medium) {
  .contact {
    text-align: center;
  }
  .team-order-header {
    &__filter {
      &--teams {
        background-position: 4%;
      }
      &--category {
        background-position: 4%;
      }
      &--other {
        background-position: 4%;
      }
    }
  }
  .team-order-list {
    &__row {
      padding-left: 2rem;
      padding-right: 2rem;
    }
    .center-text-small {
      text-align: center;
    }
  }
  .team-supplier {
    &-image-container img {
      border-radius: 0;
      width: 100%;
      height: 60px;
      object-fit: cover;
    }
    &-name {
      padding-left: 0;
    }
  }
  .team-order-list {
    &__btn {
      border-radius: 0;
      width: 100%;
    }
    &__supplier-btns {
      flex-direction: column;
      .supplier-menu,
      .choose-supplier-btn {
        width: 100%;
        margin-top: 9px;
        border-radius: 0;
      }
      .supplier-menu {
        background: #191919;
        color: white;
      }
    }
  }
  .mobile-tag {
    display: inline;
    font-weight: bold;
    color: #000;
  }
  .team-order-event-details {
    padding: 1.7rem;
    margin: 0;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.select2-selection__rendered {
  height: 40px;
}
.select2-container--default .select2-selection--multiple {
  .select2-selection__choice {
    background-color: $mammoth-charcoal;
    color: $white;
    border: none; 
    &__remove {
      float: right;
      color: #a7a7a7;
      margin-left: 0.5rem;
      &:hover {
        color: $white
      }
    }
  }      
}
