.modal-drawer {
  width: 520px;
  height: 100%;
  margin: 0;
  margin-left: auto;
  border-radius: 0;
  animation: slide-left 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  padding: 1rem 1.5rem;
  @include media-down(hamburger) {
    width: 100%;
    padding: 0;
  }
  &--no-padding {
    padding: 0;
    overflow-x: hidden;
  }
  &--no-top-padding {
    padding: 0 1.5rem 1rem;
  }
  .order-info {
    border: 2px solid #f4f4f4;
    padding: 1rem;
    border-radius: 4px;
  }
  .order-items {
    padding: 1rem;
    border: 2px solid #f4f4f4;
    margin-top: 1rem;
    border-radius: 4px;
  }
  .form-header {
    margin: 0 -1.5rem;
    .add-contact-banner {
      display: flex;
      align-items: baseline;
    }
    .modal-title {
      font-family: $body-font-family;
      padding: 0 1.5rem;
    }
    .close-modal-drawer {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background: #F2F4F4;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      color: #191919;
      &:hover {
        background: darken(#F2F4F4, 10%)
      }
    }
    .change-panel{
      cursor: pointer;
      padding: 1rem 1.5rem;
      background: #F2F4F4;
      font-size: 15px;
      border-right: solid 1px #e3e3e3;
      &.active {
        background: #fff;
      }
    }
  }
  .add-contact-tabs {
    border-top: 1px solid #F2F4F4;
  }
  .add-contact-panel {
    margin-top: 2rem;
  }
  .invite-link-panel {
    .invite-link-info {
      font-weight: bold;
      &--italic {
        font-style: italic;
      }
      &--link {
        text-decoration: underline;
      }
      &--copied {
        animation: fade-in 0.5s;
        color: #4E9E2D;
        text-align: center;
      }
    }
    a {
      margin-bottom: 1rem;
     }
    p {
      margin-bottom: 1.5rem;
    }
  }
  
}

@keyframes slide-left {
  0% {
    transform: translateX(400px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Slide out to the right */
@keyframes slide-out {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes fade-in { 
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
