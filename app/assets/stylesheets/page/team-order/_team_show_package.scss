.team-order-package-page {
  height: 100%;
  background-color: #fafbfb;
  display: grid;
  &--admin {
    height: initial; 
  }
  p {
    margin-bottom: 0;
  }
  li,
  span:not(.team-order-details__label) {
    font-size: 14px;
  }
  ul {
    margin: 0;
  }
  .row {
    display: flex;
    align-items: center;
    max-width: unset;
    @include media-down(small-tablet) {
      display: block;
      .button {
        width: 100%;
        margin-top: 1rem;
      }
    }
  }
}

.show-package {
  width: 80vw;
  padding: 20px 40px;
  margin: auto;
  max-width: 1100px;
  &.team-attendee {
    margin: initial;
    justify-self: center;
    @include media-down(hamburger) {
      padding: 40px 40px;
    }
  }
  &--admin {
    max-width: 1400px;
    padding: 0 30px;
  }
  @include media-down(small-tablet) {
    width: 100%;
    padding: 20px 10px;
  }
  &__logo {
    margin: 1rem 0 2rem;
    text-align: center;
    img {
      width: 140px;
    }
  }
  &__container {
    background: white;
    box-shadow: 0px 0px 3px 2px #ebebeb;
    border-radius: 4px;
    max-height: 80vh;
    overflow-y: scroll;
    overflow-x: hidden;
    img {
      border-radius: 50%;
      margin: 4px 0;
    }
  }
  &__banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    .team-order-details__info.attendee-invite-link {
      max-width: 300px;
      border: 1px solid $primary;
      padding: 2px 8px;
      font-weight: bold;
      color: $primary;
      border-radius: 25px;
      cursor: pointer;
    }
  }
  &__magic-link {
    margin-right: 8px;
    font-weight: bold;
  }
  &__row {
    padding: 10px 20px;
    border-top: 1px solid #f0f1f2;
    &--grey {
      background: #f9f9f9;
    }
    @include media-down(small-tablet) {
      display: flex;
      flex-direction: column;
    }
  }
  &__supplier-name {
    margin-left: 20px;
  }
  .budget {
    margin-left: 20px;
    color: gray;
    @include media-down(small-tablet) {
      margin: 0;
    }
  }
  .button {
    margin: 0;
    padding: 8px 12px;
    font-weight: bold;
    font-size: 14px;
    &.small {
      padding: 4px 20px;
    }
  }
  .load-more {
    margin: auto;
  }
  .attendee-count {
    color: white;
    background: #ccc;
    padding: 2px 10px;
    font-weight: bold;
    margin-right: 20px;
    border-radius: 8px;
  }
  .package-separator {
    margin-left: 3rem;
    @include media-down(tablet) {
      margin-left: 0;
    }
  }
}

.show-all-linked {
  color: $primary;
  font-weight: bold;
  &:hover {
    color: darken($primary, 5%);
  }
}

.package-update-modal__container  {
  padding: 0.5rem 2rem;
  .modal-subtitle {
    font-weight: bold;
  }
  .package-update-menu-warning {
    font-size: 14px;
    color: gray;
    margin-bottom: 6px;
  }
  a {
    font-size: 16px;
    color: $primary;
  }
}
