
.team-order-filter {
  .dropdown-filter-button {
    width: 100%;
    text-align: left;
    &::before {
      content: '';
      display: inline-block;
      width: 14px;
      height: 14px;
      padding-right: 1.2rem;
    }
    &::after {
      content: '';
      width: 8px;
      height: 5px;
      background: asset-data-url("icons/chevron-down-grey.svg") no-repeat;
      float: right;
      margin-top: 8px;
      transition: transform 0.3s;
    }
    &.spin::after {
      transform: rotate(-180deg);
      transition: transform 0.3s;
    }
  }
  .dropdown-content {
    min-width: 350px;
  }
  &--teams {
    .dropdown-content {
      min-width: 200px;
      ul li {
        display: block;
        width: auto;
      }
      .section-toggle {
        margin-right: 1rem;
      }
      .drop-text {
        font-size: 14px;
      }
      .event-team {
        display: flex;
      }
      .edit-team-btn, .delete-team-btn {
        margin: 0;
        padding: 0;
        img {
          width: 14px;
          height: 14px;
        }
      }
      .team-name {
        flex: 1;
        color: black;
      }
      .edit-team-btn {
        margin-left: 4px;
      }
    }
    &.active-team {
      border-color: $primary;
      .dropdown-filter-button {
        color: $primary;
        font-weight: bold;
      }
    }
  }
}
