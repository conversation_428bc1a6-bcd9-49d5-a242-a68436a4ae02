.team-supplier-banner {
  position: sticky;
  top: 0;
  z-index: 9999;
  background: white;
  border-bottom: 1px solid #dededf;
  &__image {
    img {
      width: 100%;
      height: 80px;
      object-fit: cover;
    }
  }
  &__details {
    padding: 0.625rem 2rem;
  }
  &__title {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 0.2rem;
    font-weight: bold;
    span {
      display: flex;
      justify-content: center;
      align-items: center;
      top: -2px;
      right: -55px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: #e8edf2;
      &:hover {
        background: darken(#e8edf2, 10%);
      }
    }
  }
  &__day-chosen-info {
    font-size: 12px;
    color: gray;
    margin-bottom: 10px;
  }
  &__supplier-tag {
    margin-bottom: 0;
    font-size: 14px;
    color: gray;
  }
  &__info {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    p {
      font-size: 14px;
      color: #828d99;
      margin-bottom: 0;
      padding-right: 14px;
    }
    div {
      display: flex;
    }
    .preview-menu {
      padding: 4px 8px;
      margin-bottom: 0;
    }
  }
}

.team-menu-section {
  &__heading {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dededf;
    padding: 0.625rem 2rem;
    cursor: pointer;
    background: #fdfdfd;
    & > span {
      display: flex;
      flex: 1;
      justify-content: space-between;
      align-items: center;
      margin-left: 18px;
      &::after {
        content: "";
        display: inline-block;
        background: asset-data-url('icons/chevron-down') 50% no-repeat;
        transform: rotate(0);
        transition: transform 0.3s;
        width: 14px;
        height: 14px;
      }
    }
    &.menu-open > span::after {
      transform: rotate(180deg);
    }
  }
}

.attendee-items-info {
  display: flex;
  justify-content: space-between;
  padding: 1rem 2rem;
  border-bottom: 1px solid #dededf;
  span {
    text-transform: uppercase;
    font-size: 11px;
    letter-spacing: 1px;
  }
}

.team-menu-section__items {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
  &--attendee {
    max-height: 60vh;
    overflow: scroll;
  }
  .team-menu-item {
    display: flex;
    border-bottom: 1px solid #dededf;
    padding: 1rem 2rem;
    .circle-icon {
      width: 40px;
      height: 40px;
      margin: 0;
      background: #191919;
    }
    .serving-options {
      display: flex;
      align-items: center;
      margin-left: auto;
      cursor: pointer;
      &::after {
        content: "";
        display: inline-block;
        background: asset-data-url('icons/chevron-down') 50% no-repeat;
        width: 12px;
        height: 12px;
        margin-left: 8px;
        transform: rotate(0);
        transition: transform 0.3s;
      }
      &.options-open::after {
        transform: rotate(180deg);
      }
    }
    &__details {
      flex: 1;
      margin-left: 18px;
    }
    &__name {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      .letter-icon {
        margin: 0;
        margin-left: 5px;
      }
    }
    &__description {
      color: #828d99;
      p {
        margin-bottom: 0.625rem;
      }
    }
    &__price {
      color: #828d99;
      font-weight: bold;
    }
    &__footer {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    p {
      font-size: 14px;
      margin-bottom: 0.5rem;
    }
  }
}

.serving-sizes {
  width: 100%;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
  .serving {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #828d99;
  }
}

.slider-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.slider-footer {
  width: calc(100% - 4rem);
  margin: 1.25rem 2rem;
}
