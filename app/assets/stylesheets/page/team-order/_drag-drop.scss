.drop-zone {
  width: 100%;
  height: 300px;
  padding: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  color: #cccccc;
  border: 2px dashed #E2E2E2;
  box-shadow: 0 0 9px 0 rgba(0,0,0,0.10);
  border-radius: 2px;
  margin-bottom: 2rem;
}

.drop-zone--over {
  border-style: solid;
}

.drop-zone__input {
  display: none;
}

.drop-zone__prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 60px;
    height: 60px;
    margin-bottom: 2rem;
  }
}

.drop-zone__thumb {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
  background-color: #cccccc;
  background-size: cover;
  position: relative;
}

.drop-zone__thumb::after {
  content: attr(data-label);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 5px 0;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.75);
  font-size: 14px;
  text-align: center;
}