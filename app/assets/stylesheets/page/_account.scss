.status-changes {
  font-size: small;
  text-transform: capitalize;
}

.customer-form {
  nav {
    text-align: right;
  }
}

.customer-form {
  .customer-reports-field {
    padding-top: 1rem;
    padding-bottom: 1rem;
    box-shadow: none;
    border: 1px solid #e0e0e0 !important;
    border-radius: 4px;
    .form-input {
      margin-bottom: 1rem;
    }
  }
}
.notification-pref {
  padding: 0.5rem 2rem;
  @include media-down(hamburger) {
    padding: 0.5rem;
  }
  &__title-row {
    display: flex;
    justify-content: space-between;
  }
  &__loading {
    .sk-three-bounce {
      .sk-child {
        width: 5px;
        height: 5px;
        background-color: black;
      }
    }
  }
  &__title {
    font-size: 20px;
    font-family: $header-font-family;
    margin: 1rem 0;
  }
  &__row {
    border-top: 1px solid #e7e7e7;
    padding: 0.5rem 1rem;
  }
  &__heading {
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 1px;
    margin-bottom: 5px;
  }
  &__description {
    font-size: 14px;
    margin-bottom: 0;
  }
  .preference-overrides {
    margin-top: 10px;
  }
  .toggle-overrides {
    color: black;
    text-decoration: underline;
    &:hover {
      color: lighten(black, 5%);
    }
  }
}

.notification-header {
  .circle-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }
  .circle-icon::before {
    content: '';
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 13px;
    width: 26px;
    height: 26px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    color: black;
  }
  .circle-icon.order::before {
    background-image: asset-data-url('icons/order-black.svg');
    width: 28px;
    height: 28px;
  }
  .circle-icon.customerprofile::before {
    background-image: asset-data-url('icons/user.svg');
  }
  .user-assign {
    box-sizing: content-box;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0;
    padding: 4px;
    border-radius: 50%;
    transition: border .1s linear;
    &::before {
      content: '';
      display: inline-block;
      width: 24px;
      height: 24px;
      background-image: asset-data-url('icons/user-assign.svg');
      background-repeat: no-repeat;
      background-size: contain;
    }
    &:hover {
      border: 1px solid black;
    }
  }
  .user-assign.assigned {
    background: #53FFBD;
    margin-right: 0;
    border: 1px solid black;
  }
  .circle-icon.supplierprofile::before {
    background-image: asset-data-url('icons/truck-black.svg');
  }
  .circle-icon.customerbudget::before {
    background-image: asset-data-url('icons/billing.svg');
  }
  .circle-icon.holiday::before {
    background-image: asset-data-url('icons/holiday.svg');
  }
  .circle-icon.customerquote::before {
    background-image: asset-data-url('icons/PO.svg');
  }
  .circle-icon.invoice::before {
    background-image: asset-data-url('icons/invoice2-black.svg');
  }
  .circle-icon.new::before {
    content: 'NEW';
  }
  .circle-icon.team::before {
    background-image: asset-data-url('icons/team-order.svg')
  }
  .circle-icon.cancelled::before {
    background-image: asset-data-url('icons/icon-cross-black.svg');
  }
  .circle-icon.system::before {
    background-image: asset-data-url('yo-black.svg');
  }
}

.video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
