.dashboard-orders-header,
.dashboard-orders-title {
  background: white;
  color: black;
  text-transform: uppercase;
}

.customer-form {
  background: $light-white;
  // padding: 1.25rem 1.25rem;
  flex-grow: 1;
  @include media-down(hamburger) {
    padding: 1.25rem 0.5rem;
  }
  .form-header {
    margin: -1.25rem -1.25rem 1.25rem;
    padding: 0.7rem 2rem;
    position: relative;
    h3 {
      line-height: rem-calc(30);
      margin: 0;
    }
    .download-menu-btn {
      margin-bottom: 0;
    }
  }
  .form-content {
    margin: 0rem -1.25rem -1.25rem;
    padding: 1.25rem 1.25rem;
  }
  .form-footer {
    border-top: 1px solid #dee0e4;
    margin: 0rem -1.25rem -1.25rem;
    padding: 1.25rem 1.25rem;
    border-bottom-left-radius: $global-radius;
    border-bottom-right-radius: $global-radius;
  }
  textarea,
  input:not[type="submit"],
  select {
    font-size: rem-calc(14);
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  label {
    color: #656565;
    margin: 0;
    text-transform: uppercase;
    &.button {
      color: $white;
    }
    small {
      text-transform: initial;
    }
  }
  select {
    transition: box-shadow 0.5s, border-color 0.25s ease-in-out;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
  }

  .customer-area-container {
    padding: 1.25rem 1.25rem;
    &.dash {
      padding:0.5rem 1.25rem 1.25rem;
    }
    table {
      margin-bottom: 0;
    }
    .edit-order-form {
      @include media-up(small-tablet) {
        padding: 0 50px;
      }

      .columns {
        padding-left: 0.625rem;
        padding-right: 0.625rem;
      }
      .form-footer {
        border: none;
      }
    }
  }
  .customer-area-form {
    .columns {
      padding: 0.625rem;
    }
  }

  fieldset,
  .form-fieldset {
    border-top: 1px solid #efefef;
    margin: 0 auto 30px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
    &:nth-child(2) {
      border-top: none;
    }
    .fieldset-title {
      font-size: rem-calc(17);
      font-family: $header-font-family;
      font-weight: 500;
      text-transform: uppercase;
      padding: rem-calc(18 0);
    }
  }
  fieldset.supplier-profile-fieldset {
    box-shadow: none;
    input[type="checkbox"] {
      margin: 0;
      vertical-align: bottom;
    }
  }
  .form-footer {
    .button {
      margin-left: 0.5rem;
      font-size: rem-calc(13);
    }
    .save-all {
      background: transparent;
      border: 1px solid $primary-color;
      color: $primary-color;
      transition: all 0.25s ease-out;
      &:hover {
        box-shadow: 0 0 5px inset;
      }
      .sk-three-bounce .sk-child {
        background-color: $primary-color,
      }
    }
    .cancel {
      background: transparent;
      color: $medium-gray;
      padding-left: 1rem;
      padding-right: 1rem;
      transition: color 0.25s ease-out;
      font-weight: 500;
      &:hover {
        color: $black;
      }
    }
  }
  a {
    font-size: rem-calc(14);
  }
  address {
    font-size: rem-calc(14);
    font-style: normal;
    padding: 0.6rem 0;
  }
  .table-scroll {
    margin-bottom: 0.5rem;
    table {
      width: 100%;
      table-layout: fixed;
    }
    .delivery-zones-table {
      table-layout: auto;
    }
  }
  strong {
    font-size: rem-calc(14);
  }
  .required-mark {
    color: $primary-color;
  }
  .order-title {
    font-size: rem-calc(24);
    font-weight: bold;
    margin-bottom: 2rem;
  }
  .order-date {
    color: grey;
  }
  .order-address {
    p {
      text-transform: capitalize;
    }
    p:last-of-type {
      text-transform: uppercase;
    }
  }
  .day-filter {
    font-size: rem-calc(13);
    font-weight: 500;
    select {
      display: inline-block;
      font-weight: 400;
      margin-left: 0.3rem;
      background-color: #fff;
      margin-bottom: 0;
    }
  }
}

.edit-order-form {
  .skip-order {
    font-weight: 500;
    font-size: rem-calc(14);
    line-height: rem-calc(35.78);
  }
}
.billing-form {
  min-height: 100%;
}

.delivery-schedule-container,
.edit-credit-card-form-container {
  .columns {
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }
}

.day-switch {
  display: inline-block;
  margin-left: 0.5rem;
  .button {
    border-radius: 0;
    font-size: rem-calc(14);
    text-transform: uppercase;
    color: $medium-gray;
    background: $light-gray;
    padding: 0.719rem 0.5rem;
    vertical-align: baseline;
    transition: all 0.25s ease-out;
    font-weight: 500;
    &:hover {
      color: $black;
    }
    &:first-child {
      border-top-left-radius: $global-radius;
      border-bottom-left-radius: $global-radius;
    }
    &:last-child {
      border-top-right-radius: $global-radius;
      border-bottom-right-radius: $global-radius;
    }
    &.active {
      background: $primary-color;
      color: $white;
    }
  }
}
.radio-option-list {
  margin-top: 0.45rem;
  li {
    line-height: 1.2rem;
    label {
      font-weight: 500;
      color: $black;
    }
  }
}

.order-type-icon {
  width: 20px;
}

.customer-area-table {
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.75);
  border-radius: 0;
  tr {
    td {
      font-size: rem-calc(14);
      line-height: 1.2rem;
      position: relative;
      &.status-icon {
        white-space: nowrap;
        &::before {
          content: "";
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          margin-right: 6px;
        }
      }
      @include icons_order_status;
    }
  }
  .order-item.processing {
    background-color: #c2c2c2;
    color: #ffffff;
  }
  thead {
    tr {
      .dashboard-orders-title {
        font-weight: bold;
        font-size: 16px;
        vertical-align: middle;
      }
      td {
        font-weight: 500;
        height: 50px;
      }
    }
  }
  tbody {
    tr {
      border-bottom: 1px solid #f0f0f0;
    }
  }
  tfoot {
    background-color: transparent;
    color: $black;
    tr {
      &:first-child {
        border-top: 1px solid $black;
        td {
          padding-top: 1.5rem;
        }
      }
      td {
        font-weight: normal;
        vertical-align: top;
      }
    }
    .total-row {
      td {
        font-size: rem-calc(18);
        font-weight: normal;
        font-family: "Museo Sans", sans-serif;
      }
    }
  }
}

.credit-cards-table {
  thead {
    background: #fbfbfb;
    color: #9f9f9f;
  }
  tbody {
    tr {
        td {
          padding: 0.85rem 0.625rem 1.25rem;
        }
    }
  }
}

.delivery-schedule-table {
  tbody {
    tr:nth-child(odd) {
      background: #f7f7f7;
    }
    tr {
      position: relative;
      td {
        padding: 1rem 0.625rem 1rem;
        position: static;
        &:last-child {
          width: 50px;
          position: relative;
        }
      }
    }
  }
  .edit-btn {
    margin: 0 auto;
    padding: 0.3rem 0.5rem;
    color: #a9b8c8;
    font-size: 12px;
    &:hover {
      cursor: pointer;
    }
  }
  .dropdown-pane {
    left: auto !important;
    top: 38px !important;
    right: -5px !important;
    max-width: 270px;
    &:before {
      margin-left: 0;
      left: auto;
      top: -12px;
      right: 38px;
    }
  }
}

.dashboard-table {
  tr {
    td {
      font-size: rem-calc(14);
      vertical-align: top;
      padding-top: 0.75rem;
      padding-bottom: 0.5rem;
    }
  }
  .button {
    text-transform: none;
    min-width: auto;
  }
}

.category-settings-table {
  input[type="text"] {
    display: inline-block;
    max-width: 100px;
    margin: 0;
    height: 30px;
  }
  .hours-input {
    width: 60px;
  }
  label {
    font-weight: 500;
    color: $black;
    display: inline-block;
    margin: 0;
  }
  tbody {
    tr {
      td {
        padding: 0.325rem 0.625rem;
        &:first-child {
          width: 280px;
        }
        &:nth-child(2) {
          min-width: 135px;
        }
        &:nth-child(3) {
          min-width: 110px;
        }
        &:last-child {
          width: 36px;
        }
      }
    }
  }
}

@media screen and (max-width: 1023px) {
  .customer-form {
    .form-footer {
      .button {
        margin: 0 0 0.5rem 0.5rem;
      }
    }
  }
  .category-settings-table {
    tbody {
      tr {
        td {
          &:first-child {
            width: auto;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 899px) {
  .delivery-schedule-table {
    min-width: auto;
    tbody {
      tr {
        td {
          padding-top: 0;
          &:first-child {
            padding-top: 1rem;
          }
          &:last-child {
            padding-bottom: 1rem;
          }
          br {
            display: none;
          }
        }
      }
    }
    .dropdown-pane {
      right: -14px !important;
    }
  }
}

@media screen and (max-width: 639px) {
  .customer-form {
    label {
      margin-bottom: 0;
    }
    .form-footer {
      text-align: center;
      .button {
        margin: 0 0 0.5rem 0;
        display: block;
        width: 100%;
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .customer-form .form-header {
    margin-bottom: 0.5rem;
  }
}

// custom media query
@media screen and (max-width: 720px) {
  .customer-area-sidebar__ul > li > a {
    padding-left: 0.8rem;
    padding-right: 0.8rem;
  }
}

.ui-helper-hidden-accessible {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

// past orders table
table.dashboard-table.customer-area-table {
  tr td {
    padding: 8px;
  }
  tr td span.request-date {
    font-weight: bold;
  }
  tr:nth-child(odd) {
    background-color: rgba(26, 176, 204, 0.1);
    td:first-child {
      background-color: rgba(26, 176, 204, 0.2);
    }
  }
  tr:nth-child(even) {
    td:first-child {
      background-color: rgba(26, 176, 204, 0.1);
    }
  }
}

a#print-link {
  float: right;
  margin-right: 10px;
}

@media print {
  * {
    .sign-out-admin-banner {
      display: none;
    }
    .header {
      display: none;
    }
    div#responsive-menu {
      display: none;
    }

    footer.site-footer {
      display: none;
    }

    footer.footer {
      display: none;
    }
    a.button.small.secondary {
      display: none;
    }
  }
}

.loader {
  position: absolute;
  visibility: hidden;
  width: 80px;
  top: 300px;
  left: 45%;
}
