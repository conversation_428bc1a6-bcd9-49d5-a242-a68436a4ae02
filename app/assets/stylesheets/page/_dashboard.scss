.dashboard-container {
  background: white;
  margin-top: 2rem;
  box-shadow: 0px 0px 3px 2px #ebebeb;
  border-radius: 4px;
  label {
    text-transform: uppercase;
    font-size: 11px;
    letter-spacing: 1px;
    margin-bottom: 5px;
  }
  .drop-text {
    letter-spacing: 0;
    text-transform: none;
  }
  &__section {
    padding-top: 1.3rem;
    padding-right: 1rem;
    padding-left: 1rem;
  }
  &__footer {
    padding: 2rem 0 0.5rem;
  }
}

.dashboard-cards-row {
  display: flex;
  @include media-down(hamburger) {
    flex-direction: column;
    .dashboard-card {
      width: 100%;
    }
  }
}
.dashboard-card {
  background: white;
  padding: 1.2rem;
  box-shadow: 0px 0px 3px 2px #ebebeb;
  border-radius: 4px;
  max-height: 590px;
  overflow-y: scroll;
  overflow-x: hidden;
  &--with-flex {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  &--edit-order {
    position: relative; // For the PO select box child to prevent its absolute overflow
    max-height: 80vh;
  }
}
