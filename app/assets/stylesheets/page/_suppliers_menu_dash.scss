.list-unstyled {
  list-style: none;
  margin: 0;
}
.supplier-menu-dash {
  font-size: rem-calc(14);
  margin-bottom: 5rem; // to add padding at the bottom

  .menu-section-detail {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
    border-radius: 3px;
    cursor: pointer;
    transition: background 0.25s ease-out;
    background-color: #fff;
    margin-bottom: 0;
    padding: 1rem;
    border: 1px solid #dadada;
    &.updating {
      background-color: #d5e5ee;
    }
    &.open {
      .menu-item-title:before {
        border-color: #333333 transparent transparent transparent;
        border-width: 4.5px 4.5px 0 4.5px;
        margin-top: 0;
      }
    }
  }

  .menu-section-edit-form {
    background-color: #fff;
    border-bottom: 3px solid #e2e2e2;
    border-left: 1px solid #e2e2e2;
    border-right: 1px solid #e2e2e2;
    padding: 1rem;
  }

  .new-menu-section-form {
    padding: 1rem 1rem 0;
    box-shadow: 0px 5px 10px #aaa;
  }

  .menu-item-item {
    background-color: #fff;
    box-shadow: 0 3-x 10px #e2e2e2;
    border-radius: 5px;
    margin-bottom: 1rem;
    .menu-item-detail {
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: background 0.25s ease-out;
      margin-bottom: 0;
      padding: 0.4rem 1rem;
      &.updating {
        background-color: #d5e5ee;
      }
    }
    
    .menu-item-form {
      padding: 30px 20px;
    }

    .serving-size-list, .menu-extras-list {
      padding: 0 1rem;
    }
  }

  .new-menu-item-form {
    padding: 1rem 1rem 0;
    border-bottom: 1px solid #e2e2e2;
  }

  .with-group-label {
    margin: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .new-menu-item,
  .new-menu-section {
    padding: 1rem;
  }

  .new-association-container {
    padding: 0 1rem 1rem;
  }

  tr {
    td {
      border-bottom: 1px solid #e2e2e2;
      &:last-child {
        width: rem-calc(56);
      }
    }
    &:last-child td {
      border-bottom: none;
    }
  }

  .edit-icon {
    display: inline-block;
    vertical-align: middle;
  }
  .delete-icon {
    display: inline-block;
    vertical-align: middle;
  }
  .checked-icon-green {
    display: inline-block;
    height: 38px;
    vertical-align: middle;
  }
  .input-group-label {
    border: 1px solid black;
    background-color: white;
    padding: 0 14px;
  }

  .menu-item-title {
    padding-left: 1rem;
    padding-right: 6px;
    position: relative;
    font-weight: bold;
  }

  .hint {
    display: block;
    margin-top: $form-spacing * -0.5;
    margin-bottom: $form-spacing;
  }
}

.supplier-menu-admin {
  margin-bottom: 20px;
  @include media-down(hamburger) {
    flex-wrap: wrap;
  }
  &__options {
    a {
      margin: 0;
    }
    form {
      margin: 0 10px;
    }
  }
}

.supplier-searchable {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .toggle-checkbox {
    margin-left: 10px;
  }
}

.supplier-menu-search-form {
  .select2-container .select2-selection--single {
    height: auto;
  }
}

.menu-item-name-image {
  display: flex;
  align-items: center;
  > a {
    min-width: 60px;
  }
}

.menu-item-name {
  font-weight: bold;
}

.item-image-container {
  width: 400px;
  height: 200px;
  position: relative;
}

.menu-item-image {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  &.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    background: black;
    color: white;
  }
}

.menu-item-image-remove {
  position: absolute;
  top: 8px;
  right: 10px;
  background: #ff1a1a;
  color: white;
  text-transform: uppercase;
  padding: 4px;
  border-radius: 4px;
  &:hover {
    background: darken(#ff1a1a, 10%);
    color: white;
  }
}

.menu-item-image-placeholder {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-color: #e7e7e7;
  background-image: asset-data-url(icons/plus-block);
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: 80px;
  cursor: pointer;
  &.opaque {
    opacity: 0;
    &:hover {
      opacity: 0.4;
    }
  }
  &:hover {
    background-color: darken(#e7e7e7, 10%);
    opacity: 1;
  }
}

.menu-item-container {
  width: 300px;
  height: 300px;
  object-fit: cover;
}

.menu-item-preferences {
  display: flex;
  padding-bottom: 20px;
  > div {
    width: 100%;
  }
}

.menu-item-preferences-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  grid-column-gap: 16px;
}

.menu-item-preferences-label {
  color: black;
  font-weight: bold;
  text-transform: uppercase;
}

.menu-item-preference {
  display: flex;
  align-items: center;
}

.menu-item-section-title {
  font-weight: bold;
}

.menu-item-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  @include media-down(hamburger) {
    display: block;
  }
}

.menu-item-extra-section {
  border-bottom: 1px solid #333;
  padding-bottom: 10px;
  background: white;
  border-radius: 0;
  font-weight: bold;
  &:focus {
    background: white;
    border-bottom: 1px solid black;
  }
}

.add-new-menu-option {
  display: flex;
  margin-top: 1rem;
  color: black;
  &:hover {
    color: black;
    text-decoration: underline;
  }
  &::before {
    content: '';
    display: inline-block;
    background: asset-data-url('icons/plus-circle');
    background-repeat: no-repeat;
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
}

.rate-card-margin {
  margin: 0;
  padding-top: 6px;
  color: red;
}