.team-order-instructions {
  &__container {
    display: flex;
    align-items: center;
    margin: 6rem auto;
    max-width: 1340px;
    border-radius: 6px;
    @include media-down(small-tablet) {
      flex-direction: column;
      margin: 0 1rem;
    }
  }
  .customer-form {
    box-shadow: none;
  }
}

.team-supplier.unavailable {
  color: #ccc;
  filter: grayscale(1);
  background: aliceblue;
}

.unavailable-notice {
  padding: 1rem;
}

.supplier-card.unavailable {
  button {
    display: none;
  }
  img {
    opacity: 0.5;
  }
}

.team-order-attendee-name {
  display: flex;
  align-content: center;
}

.team-admin-attendee-order {
  color: gray;
  text-decoration: underline;
  font-size: 14px;
  margin-top: 8px;
  text-align: right;
}

.team-attendee-order-link {
  text-align: right;
}

.team-order-supplier-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
  img {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-right: 4px;
  }
  a {
    color: white;
    background: $primary;
    padding: 6px 18px;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 1rem;
    &.hollow {
      background: white;
      border: 1px solid $primary;
      color: $primary;
    }
    &:hover {
      background: darken($primary, 3%);
    }
  }
}

.attendee-invite-link {
  white-space: nowrap;
  overflow-x: hidden;
  max-width: 100%;
  text-overflow: ellipsis;
  border: 1px solid #f0f0f0;
  padding: 8px;
  // A before element is used instead of after because of text-overflow affecting positioning
  &::before {
    content: "";
    background: asset-data-url("icons/copy.svg");
    width: 20px;
    height: 20px;
    float: right;
    cursor: pointer;
  }
}

.min-spend-notice {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-style: italic;
  &.success::before {
    content: "";
    display: inline-block;
    width: 14px;
    height: 14px;
    background: asset-data-url("icons/check.svg") no-repeat;
    margin-right: 4px;
  }
}

.team-order-details {
  border-top: 1px solid #f0f1f2;
  padding-top: 10px;
  padding-bottom: 10px;
  font-size: 14px;
  &__info {
    &.attendee-invite-link {
      white-space: nowrap;
      overflow-x: hidden;
      max-width: 100%;
      text-overflow: ellipsis;
      border: 1px solid #f0f0f0;
      padding: 8px;
      // A before element is used instead of after because of text-overflow affecting positioning
      &::before {
        content: "";
        background: asset-data-url("icons/copy.svg");
        width: 20px;
        height: 20px;
        float: right;
        cursor: pointer;
      }
    }
  }
  &__see-more {
    margin-bottom: 4px;
    text-decoration: underline;
    cursor: pointer;
  }
}

.status-icon {
  white-space: nowrap;
  &::before {
    content: "";
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
  }
  @include icons_order_attendee_status;
  @include icons_team_order_expiring_status;
  @include icons_order_status;
}

.linked-orders {
  border: 1px solid #c0c0c0;
  padding: 4px 12px;
  color: #3c3c3c;
  background: white;
  border-radius: 4px;
  font-size: 14px;
  margin-left: 20px;
  cursor: pointer;
  .is-dropdown-submenu {
    padding: 0.5rem 0;
  }
  span::after {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background: asset-data-url("icons/chevron-down-grey.svg") center no-repeat;
    vertical-align: middle;
    margin-left: 10px;
  }
}

.edit-team-order {
  background: $primary;
  color: white;
  font-size: 14px;
  padding: 4px 8px;
  margin: 0;
  margin-left: 8px;
  line-height: 1.6;
  border-radius: 4px;
  &:hover,
  &:focus {
    color: white;
    background: darken($primary, 5%);
  }
}

.menu-dates {
  margin: 0;
  padding: 0.625rem 2rem;
  display: flex;
  flex-wrap: wrap;
}

.menu-date-selector {
  background: #fafafa;
  border-radius: 5px;
  margin-right: 8px;
  margin-bottom: 10px;
  padding: 2px 8px;
  border: 1px solid #dcdcdc;
  &.active {
    border: 1px solid $primary;
  }
}

.day-selector__label {
  display: flex;
  align-items: center;
  &:hover {
    cursor: pointer;
  }
  input[type="checkbox"] {
    display: none;
    &:checked ~ .delivery-day-tick {
      background: asset-data-url("icons/check-white.svg") center 90% no-repeat;
      background-color: $primary;
      border-color: $primary;
    }
    & ~ .delivery-day-tick__day-chosen {
      background: asset-data-url("icons/minus.svg") center 90% no-repeat;
    }
    & ~ .delivery-day-tick__day-inactive {
      background: asset-data-url("icons/icon-cross.svg") center 90% no-repeat;
      background-color: #ccc;
    }
  }
}

.delivery-day-tick {
  display: inline-block;
  background-color: #f7f8fa;
  width: 14px;
  height: 14px;
  border: 1px solid #ccc;
  border-radius: 2px;
  margin-left: 6px;
  &:hover {
    border-color: $primary;
  }
}

.remove-date::after {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-top: 1px;
  background: asset-data-url('icons/cancel.svg')
}

.remove-attendee::after {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-top: 1px;
  background: asset-data-url('icons/bin-black.svg')
}

.magic-link-illustration {
  max-width: 550px;
  align-self: center;
  margin-top: 100px;
}

.no-attendees-magic-link {
  color: $primary;
  margin-bottom: 0;
  white-space: nowrap;
  overflow-x: hidden;
  max-width: 100%;
  text-overflow: ellipsis;
  border: 1px solid #f0f0f0;
  padding: 8px;
  font-weight: bold;
  // A before element is used instead of after because of text-overflow affecting positioning
  &::before {
    content: "";
    background: asset-data-url("icons/copy.svg");
    width: 20px;
    height: 20px;
    float: right;
    cursor: pointer;
  }
}

.open-team-days {
  padding-left: 0;
}

.open-team-day-label.open-team-day-label.open-team-day-label {
  margin-top: 10px;
}

.open-team-day-selector {
  position: relative;
  display: inline-block;
  width: 100%;
  background-color: #fafafa;
  color: black;
  border: 1px solid #f0f0f0;
  border-radius: 5px;
  text-align: center;
  margin-right: 5px;
  font-size: 13px;
  color: #666;
  font-weight: bold;
  &__day {
    position: absolute;
    top: 10px;
    transform: translateX(-50%);
  }
  &__toggle::before {
    content: '';
    background: asset-data-url('icons/plus-circle.svg');
    position: absolute;
    top: 60%;
    width: 20px;
    height: 20px;
    background-size: cover;
    transform: translateX(-50%);
  }
  &::after {
    content: '';
    display: block;
    padding-bottom: 100%;
  }
}
.open-team-day-selector:hover {
  border-color: black;
}

.drop-text input[type="checkbox"]:checked ~ .open-team-day-selector {
  background: black;
  color: white;
  font-weight: bold;
  .open-team-day-selector__toggle::before {
    background: asset-data-url('icons/minus-white.svg');
  }
}

.become-team-admin {
  display: grid;
  grid-template-columns: 1fr 2fr;
  grid-gap: 20px;
  padding: 16px;
  @include media-down(tablet) {
    display: flex;
    flex-direction: column;
    padding: 0;
  }
  h3 {
    font-family: $body-font-family;
    font-size: 24px;
    font-weight: bold;
  }
  h6 {
    font-size: 20px;
    font-weight: bold;
    font-family: $body-font-family;
    margin-bottom: 12px;
  }
  p {
    font-size: 14px;
    line-height: 16px;
  }
  &__info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 #00000011;
    padding: 16px; 
    .button {
      margin: 0;
      width: 100%;
      & + .button {
        margin-top: 8px;
      }
    }
    img {
      width: 280px;
    }
  }
  &__blurb {
    margin-top: 20px;
  }
  &__options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 30px 12px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 0 10px 0 #00000011;
    padding: 16px;
    &.no-grid {
      display: block;
      padding: 4px;
    }
  }
  &__image {
    width: 30px;
    height: auto;
    padding-bottom: 4px;
  }
}

.info-navigate {
  margin-top: 40px;
  text-align: center;
  .navigate-ellipsis {
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid black;
    &.active {
      border: none;
      background: $primary;
    }
    & + .navigate-ellipsis {
      margin-left: 20px;
    }
  }
}
