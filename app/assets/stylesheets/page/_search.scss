.suppliers-list {
  .row {
    .columns {
      margin-bottom: 1.5rem;
      transition: transform 0.3s;
      .supplier-card .supplier-image {
        overflow: hidden;
        img {
          transform: scale(1);
          transition: transform 0.3s;
        }
      }
      &:hover {
        .supplier-card .supplier-image img {
          transform: scale(1.02);
        }
      }
    }
  }
}
.supplier-card {
  height: 360px;
  display: block;
  background: transparent;
  position: relative;
  padding-top: 0.625rem;
  hr {
    margin: 0.6rem 0;
  }
  @media screen and (max-width: 40em) {
    height: auto;
  }
  .supplier-image {
    position: relative;
    height: 240px;
    overflow: hidden;
    margin: 0 0 0.75rem;
    background-color: #e1e1e1;
    img {
      display: block;
      width: 100%;
      max-width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  h3 {
    font-size: 1rem;
  }

  .supplier-modal-options {
    display: none;
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    .button {
      text-transform: uppercase;
      min-width: 150px;
      margin-left: 0.5rem;
      margin-right: 0.5rem;
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }
  .favourite-supplier {
    font-size: 90%;
    position: absolute;
    right: 0;
    top: 267px;
    z-index: 10;
    label {
      position: relative;
      font-family: "Museo Sans", sans-serif;
      color: #585858;
      line-height: 22px;
      font-size: 13px;
      cursor: pointer;
    }
    input[type="checkbox"] {
      opacity: 0;
      margin-right: 6px;
    }
    input[type="checkbox"] ~ div {
      position: absolute;
      top: 0;
      width: 15px;
      height: 15px;

      background-size: cover;
    }
    .remove-fav-supp ~ div {
      background: asset-data-url("icon-heart.svg") no-repeat 0 0;
    }
    .add-fav-supp ~ div {
      &:hover {
        background: asset-data-url("icon-heart.svg") no-repeat 0 0;
      }
      background: asset-data-url("icon-heart-empty.svg") no-repeat 0 0;
    }
  }
  .supplier-title {
    display: block;
    margin: 0;
    color: $black;
    font-weight: 700;
  }
  &:hover {
    .supplier-modal-options {
      display: block;
    }
  }
}

@media screen and (max-width: 1023px) {
  .supplier-card .supplier-image img {
    width: 125%;
    max-width: 125%;
  }
}

@media screen and (max-width: 640px) {
  .supplier-card {
    & > a {
      height: auto;
      min-height: auto;
    }
  }
  .suppliers-list {
    .row {
      display: block;
    }
  }
  .supplier-card .supplier-image {
    height: 200px;
    overflow: hidden;
  }
}

.no-address-heading {
  text-align: center;
  margin-bottom: 36px;
}

.suppliers-cta-form {
  display: flex;
  font-family: $body-font-family;
}

.reveal-overlay {
  z-index: 999;
}

/* Dropdown Button */
.dropbtn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: $white;
  width: 200px;
  height: 44px;
  font-family: $body-font-family;
  font-size: 14px;
  border: none;
  border-radius: 0;
  cursor: pointer;
  margin-right: 10px;
  padding: 0 15px;
  border: solid 0.5px #dddddd;
  &::after {
    content: "";
    display: inline-block;
    width: 14px;
    height: 14px;
    background: asset-data-url("icons/chevron-down.svg") 50% no-repeat;
  }
}
.down {
  background: asset-data-url("icons/chevron-up.svg") 90% center no-repeat;
}

/* The container <div> - needed to position the dropdown content */
.dropdown {
  position: relative;
  display: inline-block;
}

/* Dropdown Content (Hidden by Default) */
.dropdown-content {
  display: none;
  position: absolute;
  background-color: $white;
  max-width: 440px;
  min-width: 440px;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  z-index: 999;
  right: 0px;
}

.filter-content {
  display: block;
}

/* Links inside the dropdown */
.dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.dropdown-content ul {
  list-style-type: none;
  margin: 15px auto;
  width: 90%;
}
.dropdown-content ul li {
  width: 49%;
  display: inline-block;
}
.dropdown-content ul .drop-text-header {
  display: block;
  font-size: 12px;
  font-weight: bold;
  margin: 5px 0 5px;
}
/* The container */
.checkbox-container {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

// Beginning of speech bubble
.dropdown-bubble .dropdown-content {
  margin-top: 1em;
}

.dropdown-bubble .dropdown-content:before,
.dropdown-bubble .dropdown-content:after {
  content: " ";
  display: block;
  border-style: solid;
  border-width: 8px;
  border-color: rgba(255, 255, 255, 0);
  position: absolute;
  right: 28px;
}
.dropdown-bubble .dropdown-content:before {
  top: -17px;
  border-bottom-color: #eee;
}
.dropdown-bubble .dropdown-content:after {
  top: -16px;
  border-bottom-color: #fff;
}
.checkbox-content-tick {
  background-color: #f7f8fa;
  color: white;
  min-width: 13px;
  height: 13px;
  border: 1px solid #ccc;
  border-radius: 2px;
  display: inline-block;
  text-align: center;
  line-height: 1.1;
  display: inline-block;
  margin-right: 5px;
  position: relative;
  left: 0px;
  top: 1px;
}
.checkbox-content-tick:hover {
  border-color: $primary;
}

.drop-text input[type="checkbox"]:checked ~ .checkbox-content-tick {
  border-color: $primary;
  background: asset-data-url("icons/check.svg") center no-repeat;
}

.drop-text input[type="checkbox"] {
  display: none;
}

.drop-text {
  display: flex;
  font-size: 12px;
  &:hover {
    cursor: pointer;
  }
}

.new-filter-tag {
  align-self: center;
  border-radius: 3px;
  background: #fb35dc;
  color: white;
  font-size: 9px;
  font-weight: bold;
  margin-left: 5px;
  padding: 1px 4px;
}
/* Show the dropdown menu */
.show {
  display: block;
}

.overlay {
  width: 100vw;
  height: 100vh;
  background-color: #000000;
  opacity: 0.8;
  position: fixed;
  top: 0;
  z-index: 12;
  &.transparent {
    background-color: unset;
  }
}
