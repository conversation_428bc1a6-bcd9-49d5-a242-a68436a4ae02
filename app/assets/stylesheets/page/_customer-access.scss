.customer-access {
  &__title {
    font-size: 16px;
    font-weight: bold;
    font-family: $body-font-family;
    margin-bottom: 10px;
  }
  &__headings {
    display: flex;
    padding: 12px 10px;
    color: #9f9f9f;
    font-size: 14px;
    span {
      flex: 1;
      &:first-of-type {
        display: flex;
        align-items: center;
        &::after {
          content: '';
          display: inline-block;
          margin-top: 4px;
          margin-left: 6px;
          width: 10px;
          height: 10px;
          background: asset-data-url(icons/chevron-down-light-grey) no-repeat;
        }
      }
    }
  }
  &__customer {
    display: flex;
    align-items: center;
    padding: 12px 10px;
    margin-bottom: 10px;
    border: 1px solid #ededed;
    border-radius: 15px;
    font-size: 14px;
    background: white;
    &-name {
      display: flex;
      align-items: center;
      flex:1;
      span + span {
        margin-left: 8px;
      }
    }
    &-suburb {
      flex:1;
      color: #9f9f9f;
    }
    &-sign-in {
      flex: 1;
      color: $primary;
      text-decoration: underline
    }
  }
}