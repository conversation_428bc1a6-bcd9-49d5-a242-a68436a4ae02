.my-suppliers-category {
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: bold;
  margin-bottom: 0;
}

.my-suppliers-info-message {
  color: #a7a7a7;
  font-size: 15px;
  margin-bottom: 0;
}
.favourite-suppliers-options {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  &:first-of-type {
    margin-top: 26px;
  }
  ~ .favourite-suppliers-options {
    margin-top: 40px;
  }
  .button {
    margin: 0 6px 0 auto;
    & ~ .button {
      margin: 0;
    }
  }
  @include media-down(hamburger) {
    flex-wrap: wrap;
    .favourites-filter {
      width: 100%;
      max-width: none;
      flex: 1;
    }
    .favourite-suppliers-show-all {
      margin: 20px 0;
      font-size: 16px;
      flex-basis: 100%;
    }
    .button {
      flex-basis: 50%;
      margin: 0;
      border-radius: 0;
      padding: 0.5em 0;
    }
  }
}

.favourites-filter {
  border-radius: 20px;      
  border: none;
  box-shadow: 0 2px 5px 1px #403c4329;
  margin-left: 18px;
  margin-bottom: 0;
  color: #9f9f9f;
  padding: 7px 20px;
  width: 25%;
  background: url(icons/icon-search.svg) no-repeat;
  background-position: 96% 50%;
  background-size: 15px;
  max-width: 300px;
  &:hover, &:focus {
    outline: none;
    box-shadow: 0 2px 8px 1px #403c433d;
  }
}

.favourite-suppliers-show-all {
  color: $primary;
  margin-left: 2em;
  text-decoration: underline;
  &:hover {
    color: darken($primary, 10%);
  }
}

.favourite-suppliers {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
  grid-gap: 20px;
  padding-bottom: 20px;
  margin-bottom: 20px;

  .supplier-card {
    height: unset;
  }
  .supplier-image {
    height: 170px;
    margin: 0;
  }
  .supplier-details {
    padding: 12px 20px 16px;
    box-shadow: 0 3px 6px #00000020;
    background: white;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px
  }
  .supplier-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    .supplier-name {
      font-family: $body-font-family;
      width: 300px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 16px;
      margin-bottom: 2px;
    }
  }
  
  .my-supplier-delivery {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-bottom: 10px;
    &::before {
      content: '';
      display: inline-block;
      width: 20px;
      height: 12px;
      margin-right: 6px;
      background-size: contain;
    }
    &:hover {
      text-decoration: underline;
    }
    &--next {
      color: #482A9A;
      &::before {
        background: url(icons/delivery-truck-purple.svg) no-repeat;
      }
    }
    &--none {
      color: #9f9f9f;
      &::before {
        background: url(icons/delivery-truck-grey.svg) no-repeat;
      }
    }
  }
  .my-supplier-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 16px;
    .button {
      margin-bottom: 0;
      padding: 0.5em;
      @include media-down(hamburger) {
        font-size: 14px;
      }
    }
  }
}

.no-favourites {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 80px auto 0;
  background:white;
  box-shadow: 0 0 10px 0 #00000011;
  border-radius: 6px;
  max-width: 400px;
  padding: 20px;
  img {
    min-width: 220px;
  }
  .button {
    width: 100%;
  }
  p {
    margin: 30px 0;
  }
}