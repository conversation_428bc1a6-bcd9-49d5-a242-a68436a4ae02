.supplier-dashboard-container {
  min-height: 600px;
}

.dashboard-container {
  &__form {
    padding: 18px;
    padding-right: 7rem;
    @include media-down(small-tablet) {
      padding-right: 18px;
    }
    .columns {
      padding-left: 0;
    }
    .small-12 {
      padding-top: 6px;
    }
    p {
      margin-bottom: 6px;
    }
    .help-text {
      font-style: italic;
    }
    img {
      max-height: 230px;
      padding-bottom: 6px;
      object-fit: contain;
    }
  }
  .supplier-profile {
    &-fields {
      border-right: 1px solid #e5e5e5;
      padding-left: 4px;
      padding-right: 16px;
    }
    &-image {
      padding-left: 24px;
      min-height: 340px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      @include media-down(hamburger) {
        padding-left: 0;
      }
    }
    &-agreement {
      padding-left: 24px;
      &-link {
        font-size: 18px;
        margin-top: 15px;
        &.icon-pdf:before {
          content: '';
          display: inline-block;
          background: asset-data-url('icons/pdf.svg') no-repeat;
          width: 20px;
          height: 20px;
          margin-right: 10px;
          font-size: 36px;
        }
      }
    }
  }
  &__title {
    font-size: 17px;
    font-weight: bold;
    padding-bottom: 12px;
    margin-bottom: 22px;
  }
}

.supplier-table {
  background: white;
  box-shadow: 0px 0px 3px 2px #ebebeb;
  border-radius: 4px;
  &__header {
    padding: 1rem;
    &--heading {
      font-family: $body-font-family;
      margin-top: 5px;
      margin-bottom: 0;
    }
  }
}

.supplier-table-list {
  max-height: 65vh;
  overflow-y: scroll;
  &__headings {
    padding: 0 1rem 0.5rem;
    @include media-down(medium) {
      display: none;
    }
  }
  &__heading {
    font-size: 11px;
    color: #828d99;
    margin-top: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  &__row {
    padding: 1rem;
    border-top: 1px solid #e7e7e7;
  }
  &__order {
    color: $primary;
    text-decoration: underline;
    &:hover {
      color: darken($primary, 10%);
    }
    &::after {
      content: "";
      display: inline-block;
      width: 15px;
      height: 15px;
      margin-left: 4px;
      background-image: asset-data-url("icons/external_link.svg");
      background-size: cover;
      vertical-align: middle;
    }
  }
  &__status {
    white-space: nowrap;
    &::before {
      content: "";
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 6px;
    }
    @include icons_order_status;
  }
  li {
    list-style: none;
    font-size: 14px;
  }
  p {
    margin-bottom: 0;
  }
  .supplier-order-btns {
    @include media-up(medium) {
      text-align: right;
    }
  }
  .supplier-order-btn {
    font-size: 14px;
    padding: 8px 16px;
    margin: 0 4px;
  }
}

select.filter-by-order-days {
  padding-left: 3rem;
  color: gray;
  background-image: asset-data-url("icons/clock.svg"),
    url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' width='32' height='24' viewBox='0 0 32 24'><polygon points='0,0 32,0 16,24' style='fill: %23bbb'></polygon></svg>");
  background-position: 3% center, 93% center;
  background-origin: border-box;
  background-size: 20px, 9px 6px;
  background-repeat: no-repeat;
}

.supplier-subnav {
  border-radius: 0;
  background: #191919;
  .menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
  }
  .customer-area-sidebar__ul {
    & > li > a {
      border: none;
      padding: 0.5em 1em;
      color: white;
      margin: 0;
    }
    & > li.active > a {
      background: white;
      color: #191919;
    }
    & > li > a:hover {
      background: white;
      color: black;
    }
  }
}
.order-company {
  padding-left: 26px;
}

.order-address {
  @include media-up(medium) {
    padding-left: 2rem;
  }
}

.contactless-terms-box {
  height: 160px;
  overflow-y: scroll;
  border: 1px solid;
  margin-bottom: 14px;
  padding: 6px;
  font-size: 13px;
  &-title {
    font-weight: bold;
    margin: 0;
  }
}

.contactless-options {
  display: flex;
  justify-content: space-between;
}

.supplier-order-print-btn {
  display: inline-block;
  margin-top: 12px;
  padding: 4px 8px;
  border: 1px solid black;
  color: black;
  font-size: 13px;
  border-radius: 4px;
  &:hover {
    background: black;
    color: white;
  }
}

.supplier-logout.supplier-logout {
  margin-left: 1rem;
  text-decoration: underline;
  &:hover {
    color: $primary;
  }
  @include media-down(hamburger) {
    display: none;
  }
}

.supplier-modal-optns {
  display: flex;
  margin: 20px 0;
  button {
    flex-grow: 1;
  }
}

.category-minimum-list {
  .form-input {
    margin-bottom: 0;
  }
}

.delivery-zones-table {
  margin-bottom: 0;
  tbody {
    tr {
      transition: background 0.25s ease-out;
      &:hover {
        background: $light-gray;
      }
      &.recently-updated {
        animation: bounce 2s 2;
      }
      td {
        padding: 0.625rem 0.625rem;
      }
    }
  }
  @include media-down(hamburger) {
    thead {
      display: none;
    }
    td {
      display: flex;
    }
    td::before {
      content: attr(label);
      font-weight: bold;
      width: 120px;
      min-width: 120px;
    }
  }
}

.delivery-zone {
  td:last-of-type {
    display: flex;
  }
  .edit-icon, .delete-icon {
    display: inline-block;
  }
  @include media-down(hamburger) {
    .edit-icon {
      margin-left: 0;
    }
  }
}

.opening-days-list {
  margin: 0;
  min-width: 100px;
  li {
    display: inline;
    font-weight: 500;
    color: $medium-gray;
    &.active {
      color: $black;
      font-weight: 800;
    }
    &.weekend.active {
      color: $primary-color;
      font-weight: 800;
    }
  }
}

.supplier-preview-menu {
  display: inline-block;
  margin-bottom: 16px;
  background: $primary;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  &:hover, &:focus {
    color: white;
    background: darken($primary, 5%);
  }
}

.icon-info-circle::before {
  background: asset-data-url("icons/icon-info-circle.svg") no-repeat center;
  content: '';
  height: 1rem;
  width: 1rem;
  display: inline-block;
}

.favourite-supplier {
  .favourite-team-supplier {
    cursor: pointer;
    margin: 0;
  }
  input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
    margin: 0;
  }
  input[type="checkbox"] ~ div {
    width: 18px;
    height: 18px;
    background-size: cover;
  }
  .add-fav-supp ~ div {
    background: asset-data-url("icon-heart-empty.svg") no-repeat 0 0;
    &:hover {
      background: asset-data-url("icon-heart.svg") no-repeat 0 0;
      background-size: cover;
    }
  }
  .remove-fav-supp ~ div {
    background: asset-data-url("icon-heart.svg") no-repeat 0 0;
  }
}

.supplier-minimums {
  font-size: 14px;
  padding: 0;
  margin: 0;
  li {
    border-bottom: 1px solid #ccc;
    padding: 0.5rem 0;
  }

  .supplier-minimum.processing {
    background-color: #efefef;
  }

  .heading {
    padding: 1rem 0;
  }
  input[type='text'] {
    max-width: 100px;
  }
}

@for $num from 0 through 12 {
  .list-flex-#{$num} {
    flex: #{$num};
  }
}

.react-responsive-modal-modal {
  border-radius: 5px;
}

.supplier-data-list__headings{
  display: flex;
  padding: 12px 10px;
  color: #9f9f9f;
  font-size: 14px;
  @include media-down(hamburger) {
    display: none;
  }
}

.supplier-data {
  display: flex;
  align-items: center;
  padding: 12px 10px;
  margin-bottom: 10px;
  border: 1px solid #ededed;
  border-radius: 15px;
  font-size: 14px;
  background: white;
  &__flex-field {
    display: flex;
    align-items: center;
  }
  &__flex-action-field {
    display: flex;
    align-items: baseline;
    justify-content: right;
  }
  &.inactive {
    color: #a0a0a0;
  }
  .circle-icon {
    color: black;
    width: 40px;
    height: 40px;
    padding: 22px;
    margin-right: 30px;
    font-size: 13px;
  }
  @include media-down(hamburger) {
    flex-direction: column;
    align-items: flex-start;
    &.form {
      width: 100%;
      > div {
        width: 100%;
      }
      label{
        white-space: nowrap;
        margin-right: 6px;
        min-width: 80px;
      }
    }
    & > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 6px 0;
      &[label]::before {
        content: attr(label);
        font-weight: bold;
        width: 120px;
        min-width: 120px;
      }
    }
  }
}

.icon-upload::after {
  content: '';
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-top: 1px;
  background: asset-data-url('icons/upload.svg') no-repeat center;
  background-size: 100%;
}

.icon-edit::after {
  content: '';
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-top: 1px;
  background: asset-data-url('icons/edit-icon.svg')
}

.icon-plus {
  content: '';
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-top: 1px;
  background: asset-data-url("icons/plus-block.svg") no-repeat center center;
}

.icon-trash::after {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-top: 1px;
  background: asset-data-url('icons/bin-black.svg')
}

.supplier-delivery-zones {
  .form-input {
    margin: 0;
  }
}
