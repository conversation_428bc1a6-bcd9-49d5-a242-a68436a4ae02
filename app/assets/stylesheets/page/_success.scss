.success-page {
  @include media-down(hamburger) {
    .customer-order {
      display: none;
    }
  }
}

.success-container{
  text-align: center;
  svg {
  width: 40px;
  display: inline-block;
  margin-bottom: 15px;
  }
  .success-message {
    color: #5ec9bd;
    font-family: $body-font-family;
    font-size: 20px;
  }
  .success-order-no{
    color: #DE1F52;
  }
}

.success-confirmation{
  display: flex;
  align-items: center;
  &::before{
    content: "";
    display: inline-block;
    min-width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    margin-right: 11px;
  }
  @include icons_checkout;
}

.order-map {
  margin: 20px 0;
  &.loading {
    width: 100%;
    height: 192px;
    @include shimmer;
  }
  img{
    width: 100%;
  }
  &--small {
    max-width: 600px;
  }
}

.order-info-header {
  margin-bottom: 0;
}

.delivery-instructions {
  padding-bottom: 20px;
  border-bottom: 1px solid #d4d9dc
}

.instructions-container{
  margin-bottom: 10px;
}
.instructions-reminder{
  font-size: 13px;
  font-style: italic;
  margin-bottom: 5px;
}


//Animation for successful order checkmark
.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  &.circle {
    animation: dash 1.5s ease-in-out;
  }
  &.line {
    stroke-dashoffset: 1000;
    animation: dash 1.5s .35s ease-in-out forwards;
  }
  &.check {
    stroke-dashoffset: -100;
    animation: dash-check 1.5s .35s ease-in-out forwards;
  }
}
