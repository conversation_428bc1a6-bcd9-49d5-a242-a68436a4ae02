.dashboard-filters {
  display: flex;
  margin-bottom: 20px;
  .customer-order-dropdown {
    position: relative;
    cursor: pointer;
    &--order-type {
      .dropdown-option::before {
        content: "";
        display: inline-block;
        width: 20px;
        height: 20px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        margin-right: 8px;
      }
    }
  }
  .dropdown {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0px 1px 4px 5px #f1f1f1;
    box-sizing: border-box;
    max-height: 200px;
    position: absolute;
    margin-top: 4px;
    top: 100%;
    transform: translateX(-10px);
    width: 96%;
    z-index: 1000;
    padding: 10px;
    overflow-y: scroll;
    .dropdown-option {
      display: flex;
      align-items: center;
      cursor: pointer;
      &:hover {
        color: $primary;
      }
    }
    .recurring-dropdown::before {
      background-image: asset-data-url("icons/recurring-icon.svg");
    }
    .one-off-dropdown::before {
      background-image: asset-data-url("icons/one-off-icon.svg");
    }
    .team-order-dropdown::before {
      background-image: asset-data-url("icons/team-order.svg");
    }
  }
  .Dropdown-placeholder {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .dashboard-filter {
    flex: 2;
    border-radius: 20px;      
    border: none;
    box-shadow: 0 2px 5px 1px #403c4329;
    margin-right: 18px;
    color: #9f9f9f;
    padding: 7px 20px;
    &.no-radius {
      border-radius: 0;
    }
    &:hover, &:focus {
      outline: none;
      box-shadow: 0 2px 8px 1px #403c433d;
    }
    &__search {
      background: url(icons/icon-search.svg) no-repeat;
      background-position: 96% 50%;
      background-size: 15px;
      flex: 3;
    }
    &__type {
      display: flex;
      align-items: center;
      &::before {
        content: "";
        display: inline-block;
        width: 18px;
        height: 18px;
        background: url(icons/order-black.svg) no-repeat;
        margin-right: 10px;
      }
      &--recurrent::before {
        background-image: url(icons/recurring-icon.svg);
      }
      &--one-off::before {
        background-image: url(icons/one-off-icon.svg);
      }
      &--team-order::before {
        background-image: url(icons/team-order.svg);
      }
      &::after {
        content: "";
        display: inline-block;
        width: 12px;
        height: 12px;
        background: url(icons/chevron-down-light-grey.svg) no-repeat;
        margin-left: auto;
        margin-top: 5px;
      }
      &.black::after {
        background: url(icons/chevron-down.svg) no-repeat;
      }
    }
    &__supplier {
      display: flex;
      align-items: center;
      &::before {
        content: "";
        display: inline-block;
        width: 18px;
        height: 18px;
        background: url(icons/supplier.svg) no-repeat;
        margin-right: 10px;
      }
      &::after {
        content: "";
        display: inline-block;
        width: 12px;
        height: 12px;
        background: url(icons/chevron-down-light-grey.svg) no-repeat;
        margin-left: auto;
        margin-top: 5px;
      }
    }
  }
  .DatePicker {
    display: flex;
    align-items: center;
    z-index: 1;
    cursor: pointer;
    border-radius: 20px;      
    border: none;
    box-shadow: 0 2px 5px 1px #403c4329;
    color: #9f9f9f;
    flex: 2;
    &__input {
      font-size: 16px;
      text-align: left;
      cursor: pointer;
      border: none;
      padding: 7px 20px;
      outline: none;
      background: white;
      background: url(icons/icon-calendar.svg) no-repeat;
      background-position: 10% 50%;
      background-size: 15px;
      text-indent: 27px;
      &:focus {
        border: none;
      }
    }
    &__calendarContainer {
      box-shadow: 0 14px 36px 2px #bdbdbd8c;
      border: none;
    }
  }
  @include media-down(hamburger) {
    flex-direction: column;
    .dashboard-filter {
      width: 100%;
      margin-right: 0;
    }
    & > * {
      margin-bottom: 8px;
      min-height: 40px;
    }
  }
}

.no-orders {
  margin: 10px 0;
  text-align: center;
  margin-top: 4rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 240px;
  }
  p {
    font-size: 20px;
    font-weight: bold;
    margin: 30px 0;
    font-weight: 900;
  }
  .options-toggle {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 200px;
    > button {
      background: black;
      width: 100%;
      margin-bottom: 0;
    }
    .tooltip {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      width: 100%;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
      > a {
        font-size: 16px;
        color: black;
        font-weight: bold;
        & + a {
          margin-top: 16px;
        }
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
.order-list-columns {
  display: flex;
  padding: 8px 10px;
  color: #9f9f9f;
  font-size: 14px;
  @include media-down(hamburger) {
    display: none;
  }
  span:first-of-type {
    display: flex;
    align-items: center;
    &::after {
      content: '';
      display: inline-block;
      margin-top: 4px;
      margin-left: 6px;
      width: 10px;
      height: 10px;
      background: asset-data-url(icons/chevron-down-light-grey) no-repeat;
    }
  }
}
.order-list-item {
  display: flex;
  position: relative;
  align-items: center;
  padding: 8px 10px;
  margin-bottom: 10px;
  border: 1px solid #ededed;
  border-radius: 15px;
  font-size: 14px;
  background: white;
  &.loading {
    width: 100%;
    height: 80px;
    background: grey;
    @include shimmer;
  }
  &__suppliers {
    display: flex;
    z-index: 5;
    .circle-icon {
      width: 45px;
      height: 45px;
      margin: 0;
      &:nth-of-type(2){
        transform: translateX(-40%);
      }
      &:nth-of-type(3){
        transform: translateX(-75%);
      }
      &:nth-of-type(4){
        transform: translateX(-110%);
      }
    }
  }
  &__id {
    color: #9f9f9f;
  }
  &__date {
    font-weight: bold;
  }
  &__name {
    color: #9f9f9f;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding-right: 10px;
  }
  &__total {
    color: #9f9f9f;
  }
  &__status {
    color: #9f9f9f;
    white-space: nowrap;
    &::before {
      content: "";
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 6px;
    }
    @include icons_order_status;
  }
  &__options {
    position: relative;
    display: flex;
    justify-content: center;
    &-button {
      display: inline-block;
      background: url(icons/ellipsis.svg) no-repeat;
      width: 22px;
      height: 22px;
    }
    &-dropdown {
      position: absolute;
      display: flex;
      flex-direction: column;
      z-index: 1;
      background: white;
      box-shadow: 0 2px 5px 1px #403c4329;
      min-width: 180px;
      top: 26px;
      right: 0;
      padding: 10px;
      z-index: 999999999;
      @include media-down(hamburger) {
        left: 50%;
        right: 50%;
        transform: translateX(-50%);
      }
      a {
        color: $primary;
        margin-bottom: 8px;
        &:last-of-type {
          margin-bottom: 0;
        }
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  .view-slider-link::after {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

.customer-order-modal.customer-order-modal {
  max-width: 400px;
}

.cancel-link-options {
  display: flex;
  flex-direction: column;
}

.calendar-skeleton-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.calendar-skeleton-inner {
  width: 3em;
  height: 3em;
  border-radius: 50%;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top-color: rgba(0, 0, 0, 0.5);
  animation: rotate-spinner 1.2s linear infinite;
}

@keyframes rotate-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.popup-open {
  position: absolute;
  display: inline-block;
  background: asset-data-url('icons/open.svg');
  background-size: contain;
  background-repeat: no-repeat;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  margin-bottom: 4px;
  z-index: 9;
  cursor: pointer;
  &:hover {
    background-image: asset-data-url('icons/open-primary.svg');
  }
}

.popup-order-option {
  display: block;
  text-transform: capitalize;
}

.day-cell.add-order::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  background: asset-data-url('icons/plus-block-grey.svg') center/90px no-repeat;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
}

// this weird css is to allow hover effect on day cell
// .rbc-row-content and .rbc-row overlap the .day-cell and prevent hover effects even for squares with no events
.rbc-row-content {
  pointer-events: none;
  .rbc-row {
    pointer-events: none;
    * {
      pointer-events: all;
    }
  }
}

.popup-container {
  position: relative;
}

.popup-order-create {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 24px;
  left: 0;
  z-index: 10;
  background: white;
  padding: 12px;
  background: white;
  a {
    color: black;
    padding: 0.4rem 0;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: inline-block;
      margin-right: 4px;
      width: 20px;
      height: 20px;
      background-repeat: no-repeat;
      background-size: contain;
      filter: grayscale(1);
    }
    &:hover {
      &::before {
        filter: none;
      }
    }
    &.snacks::before {
      background-image: asset-data-url('icons/snacks.svg')
    }
    &.catering::before {
      background-image: asset-data-url('icons/catering.svg');
    }
    &.team::before {
      background-image: asset-data-url('icons/team-order-primary.svg');
    }
    &:hover {
      text-decoration: underline;
      color: $primary;
      cursor: pointer;
    }
  }
}

.day-cell.add-order:hover::before {
  opacity: 1;
}

.view-slider-link-calendar.auto-height > div {
  height: auto !important;
}

.day-cell{
  width: 100%;
  height: 100%;
  position: relative;
}

.day-cell + .day-cell {
  border-left: 1px solid #bababa;
}

.custom-popup {
  position: absolute;
  background: white;
  z-index: 999;
  padding: 14px 20px;
  box-shadow: 0 0 20px 2px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  width: 300px;
  transform-origin: top left;
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  transform: translate(-20px, 20px) scale(0);
  opacity: 0;
  &::before {
    content: "";
    position: absolute;
    top: -10px;
    left: 20px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid white;
  }
  .popup-order-outer{
    display: grid;
    grid-template-columns: 1fr auto;
    border-radius: 4px;
    margin-bottom: 4px;
    padding: 4px;
    align-items: center;
  }
  .hidden-event {
    display: none;
  }
  &.active {
    transform: translate(-20px, 20px) scale(1);
    opacity: 1;
  }
  &.closing {
    transform: translate(-20px, 20px) scale(0);
    opacity: 0;
  }
  .popup-order-container {
    display: flex;
    align-items: center;
  }
  .circle-icon {
    width: 36px;
    height: 36px;
  }
  .popup-header {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-bottom: 8px;
    font-family: $header-font-family;
    span {
      font-weight: 900;
      padding-right: 6px;
    }
    &::before {
      content: '';
      display: inline-block;
      background: asset-data-url('icons/icon-calendar');
      background-repeat: no-repeat;
      background-size: contain;
      width: 14px;
      height: 14px;
      margin-right: 6px;
      margin-bottom: 2px;
    }
  }
  .popup-order {
    & + .popup-order {
      margin-top: 16px;
    }
     .popup-order-row {
      display: flex;
      align-items: center;
      .order-description {
        flex: 1;
        color: black;
        font-size: 14px;
        text-overflow: ellipsis;
        max-width: 170px;
        white-space: nowrap;
        overflow: hidden;
        margin-right: 8px;
        &:hover {
          text-decoration: underline;
          cursor: pointer;
        }
      }
      > p {
        margin: 0;
      }
    }
  }
  .popup-details {
    display: flex;
    align-items: center;
  }
  .popup-detail {
    color: #828282;
    font-size: 12px;
    margin-bottom: 0;
    margin-right: 10px;
    &.time {
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: inline-block;
        background: asset-data-url('icons/clock-hollow.svg');
        background-repeat: no-repeat;
        background-size: contain;
        width: 10px;
        height: 10px;
        margin-right: 10px;
      }
    }
    &.status {
    min-width: 80px;
      &::before {
        content: '';
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 10px;
      }
      &.new::before{
        background: #24bbff;
      }
      &.confirmed::before{
        background: #71ffe4;
      }
      &.quoted::before, &.saved::before{
        background: gray;
      }
      &.amended::before{
        background: #ffe396;
      }
      &.delivered::before{
        background: #b388f9;
      }
      &.pending::before{
        background: aqua;
      }
      &.cancelled::before{
        background: #ff628d;
      }
    }
  }
  .icon-options {
    &::before {
      content: '';
      display: inline-block;
      background: asset-data-url('icons/ellipsis.svg');
      background-size: contain;
      background-repeat: no-repeat;
      width: 18px;
      height: 18px;
    }
    &.spin::before {
      background: asset-data-url('icons/ellipsis-primary.svg')
    }
  }
  .popup-link-container {
    margin-top: 8px;
    a {
      font-size: 12px;
      &:hover {
        text-transform: underline;
      }
    }
  }
}

.rbc-event-content {
  font-size: 11px !important;
}

.rbc-show-more {
  pointer-events: none;
  color: #ababab !important;
  font-size: 12px !important;
}

.rbc-day-bg {
  width: 100%;
  height: 100%;
}

.rbc-event {
  display: flex;
  align-items: center;
  &-content {
    font-size: 12px;
  }
  &::before {
    content: '';
    display: inline-block;
    min-width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 10px;
  }
  &.new::before{
    background: #24bbff;
  }
  &.confirmed::before{
    background: #71ffe4;
  }
  &.quoted::before, &.saved::before{
    background: gray;
  }
  &.amended::before{
    background: #ffe396;
  }
  &.delivered::before{
    background: #b388f9;
  }
  &.pending::before{
    background: aqua;
  }
  &.cancelled::before{
    background: #ff628d;
  }
  &.skipped::before, &.paused::before {
    background: rgb(255, 212, 104)
  } 
  &:hover {
    text-decoration: underline;
  }
}

.calendar-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  margin-bottom: 300px;
  .inner-container {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }
}

.calendar-period-switch-container {
  flex: 1;
  text-align: right;
  button {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
  }
  button.active {
    background: black !important;
    color: white !important;
  }
  button + button {
    margin-left: 10px;
  }
}

.rbc-header {
  text-transform: uppercase;
  font-family: $header-font-family;
  border: none !important;
}

.rbc-day-bg {
  background: #ffffff !important;
}

.rbc-btn-group {
  flex: 1;
}

.rbc-button-link {
  font-size: 14px;
}

.rbc-month-row {
  border-top: 1px solid #bababa !important;
  border-left: 1px solid #bababa !important;
  border-right: 1px solid #bababa !important;
}

.rbc-month-row:last-of-type {
  border-bottom: 1px solid #bababa !important;
}

.rbc-date-cell {
  padding: 6px 8px;
}

.rbc-month-view {
  border: none !important;
}
.rbc-header + .rbc-header {
  border: none !important;
}
.rbc-day-bg + .rbc-day-bg {
  border-left: 1px solid #bababa !important;
}

.rbc-off-range-bg {
  background: #f5f5f5 !important;
}

.rbc-now.rbc-current > .rbc-button-link {
  background: $highlight;
  border-radius: 50%;
  padding: 4px;
  color: white;
} 

.rbc-toolbar {
  margin-bottom: 20px !important;
}

.rbc-event-label {
  display: none !important;
}

.rbc-toolbar-label {
  font-family: $header-font-family;
  font-size: 20px;
  flex: 1;
  &::first-word {
    font-weight: bold;
  }
}

.month-name {
  font-weight: bold;
}

.month-change {
  background: asset-data-url('icons/chevron-back') !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  border: none !important;
  &.right {
    transform: rotate(180deg);
  }
}

.holiday-modal {
  min-width: 500px;
}

.holiday-container {
  display: flex;
  justify-content: space-between;
  > div {
    flex: 1;
  }
}

.holiday-image {
  display: grid;
  position: relative;
  place-items: center;
  font-family: $header-font-family;
  font-size: 36px;
  aspect-ratio: 1;
  padding: 16px;
  font-weight: 900;
  &::before {
    content: '';
    position: absolute;
    top: 26px;
    left: 20px;
    display: inline-block;
    background-image: asset-data-url('yo-black.svg');
    width: 40px;
    height: 40px;
    background-size: contain;
    background-repeat: no-repeat;
    filter: invert(1)
  }
}

.holiday-description {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 12px;
  .holiday-date {
    font-weight: bold;
    font-size: 18px;
  }
  .holiday-states {
    font-size: 16px;
    font-weight: normal;
  }
  .button {
    background: white;
    color: black;
    border: 1px solid black;
    width: 100%;
    transition: all .1s ease-in-out;
    text-align: center;
    &:hover {
      background: black;
      color: white;
    }
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}