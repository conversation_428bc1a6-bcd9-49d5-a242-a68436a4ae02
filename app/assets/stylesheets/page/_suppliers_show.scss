.view-supplier-container {
  display: flex;
  padding: 0 2rem;
  background: #f9f9f9;
  @include media-down(large) {
    display: block;
    padding: 0;
  }
}
.menu-container {
  width: 70%;
  padding: 0;
  @include media-down(large) {
    width: 100%;
  }
}
.docket-container {
  width: 30%;
  padding: 0;
  @include media-down(large) {
    width: 100%;
  }
}
.heading-block {
  position: absolute;
  width: 100%;
  height: 315px;
  background: white;
  @include media-down(medium) {
    height: 538px;
  }
}

.woolworths-heading-block {
  height: 366px;
}
.liquor-license-warning {
  height: 378px;
}
.warning-description {
  position: relative;
}

// header
.supplier-header {
  position: relative;
  padding: 3rem 0 2rem;
  @include media-down(small-tablet) {
    padding-top: 2rem;
  }
  .supplier-image-wrapper img {
    height: 180px;
    width: 100%;
    object-fit: cover;
    border-radius: 2px;
  }
}

//navigation
.menu-navigation {
  position: sticky;
  visibility: hidden;
  top: 0;
  margin: 0;
  margin-left: -2rem;
  padding-left: 2rem;
  border-bottom: $border;
  border-top: $border;
  background: white;
  z-index: 2;
  .menu-section-list-wrapper {
    padding: 0;
  }
  .menu-section-list {
    border-right: $border;
    white-space: nowrap;
    > li {
      display: inline-block;
      &.hidden {
        display: none;
      }
    }
    .more-button-wrapper {
      float: right;
      .more-button-link {
        width: 130px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 5px;
        &:hover,
        &.menu-section-link-selected {
          svg {
            min-width: 13px;
            #chevron {
              fill: $white;
              min-width: 13px;
            }
          }
        }
      }
    }
  }
  &.menu-section-group-navigation {
    z-index: 2;
  }
  &.second-level-navigation {
    top: 52px;
  }
  .menu-section-link {
    display: block;
    padding: 11px 14px;
    color: inherit;
    font-size: 0.9rem;
    line-height: 1;
    white-space: nowrap;
    &:hover,
    &.menu-section-link-selected,
    &.menu-section-group-link-selected {
      background-color: $yordar-black;
      color: $white;
      border-radius: 3px;
    }
    span {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  ul {
    margin: 0;
    padding: 8px 0;
  }
  .dropdown-pane__list {
    position: relative;
    overflow: auto;
    max-height: 300px;
  }
  .open {
    svg {
      -ms-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      -webkit-transform: rotate(180deg);
      transform: rotate(180deg);
    }
  }
}

// Search Bar on menu page
.menu-items-search {
  margin: 0;
  padding: 8px;
  &__search-input {
    background: asset-data-url("icons/icon-search.svg") right center no-repeat;
    background-size: 18px;
    border-radius: 0;
    margin: 0;
    &::-webkit-input-placeholder {
      color: #9b9b9b;
    }
    &::-moz-placeholder {
      color: #9b9b9b;
    }
    &:-moz-placeholder {
      color: #9b9b9b;
    }
    &:-ms-input-placeholder {
      color: #9b9b9b;
    }
  }
}

// menu section / item-cards

.menu-section-wrapper {
  padding-bottom: 2rem;
  background: #f9f9f9;
  padding-right: 10px;
}

.menu-section {
  margin-top: 2rem;
}

.section-heading {
  font-size: 24px;
  margin-bottom: 20px;
  font-weight: bold;
}

.menu-item-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  grid-gap: 16px;
  @include media-down(medium) {
    grid-template-columns: 1fr;
  }
}

@keyframes pulsate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
}

.menu-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  min-height: 180px;
  background: white;
  border: 1px solid #e3e3e3;
  border-radius: 2px;
  padding: 10px;
  overflow: hidden;
  cursor: pointer;
  .item-card {
    &:hover {
      .image img {
        transform: scale(1.04);
      }
    }
  }
  .item-heading {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    height: 30px;
  }

  .item-details,
  .item-footer,
  .item-box,
  .icons-and-fav {
    display: flex;
    justify-content: space-between;
  }

  .item-details {
    flex: 1;
    display: flex;
  }

  .favourite {
    &.processing {
      animation: pulsate 0.5s infinite;
    }
    &:hover {
      transform: scale(1.2);
    }
  }

  .item-title {
    margin: 0;
    font-size: 15px;
    margin-bottom: 6px;
    font-weight: bold;
    font-family: $body-font-family;
  }

  .add-menu-item {
    display: flex;
    align-items: center;
    font-family: $body-font-family;
    font-size: 0.7rem;
    font-weight: bold;
    height: 20px;
    padding: 0.1rem 0.5rem;
    border-radius: 3px;
    border: 1px solid $primary;
    color: $primary;
    &:hover {
      background: darken($primary, 5%);
      color: white;
    }
  }

  .description-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 14px;
    &.with-image {
      margin-right: 0.4rem;
    }
  }

  .item-description {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 0;
    line-height: 16px;
    font-size: 13px;
    color: #656464;
    word-break: break-word;
  }

  .icons-and-fav {
    align-items: flex-end;
  }

  .no-icons-margin {
    margin-bottom: 18px;
  }

  .item-preview {
    overflow: hidden;
  }

  .menu-item-image-container {
    flex: 1;
    max-width: 110px;
    margin-left: 10px;
    img {
      border-radius: 2px;
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center center;
      opacity: 1;
      transition: transform 0.3s;
    }
  }

  .item-footer {
    align-items: center;
    margin-top: 10px;
    font-size: 14px;
    white-space: nowrap;
    font-weight: bold;
  }

  .serving-options {
    font-size: 11px;
  }

  .item-quantity {
    font-size: 11px;
    color: $medium-gray;
  }

  .strike {
    text-decoration: line-through;
    color: red;
  }
  .special-pricing {
    color: green
  }
  .special-label {
    border: 2px solid green;
    padding: 2px 5px;    
  }
}

.out-of-budget {
  position: relative;
  .item-card {
    opacity: 0.35;
  }
  &__text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-transform: uppercase;
    font-size: 17px;
    white-space: nowrap;
    z-index: 1;
    font-family: "Museo Slab";
    font-weight: bold;
  }
}
.hide-out-of-budget-items {
  .out-of-budget {
    display: none !important;
  }
}

// Menu-item modal
.menu-item-modal {
  padding: 0;
  width: 500px; // override the modal padding
  @include media-down(hamburger) {
    width: 100%;
  }
  .modal-heading {
    background-color: $off-white-lighter;
    padding: 1rem 2rem;
    .modal-title {
      margin: 0;
    }
    .close-reveal {
      font-weight: bold;
      color: #ddd;
      float: right;
    }
  }
  .modal-content {
    padding: 1rem 2rem;
  }

  .modal-footer {
    text-align: right;
    padding: 16px 0;
    .add-to-cart {
      margin: 0; // remove when we remove the menu-item-reveal button style
      width: 50%;
      margin-right: 10px;
      background: black;
      text-transform: uppercase;
      font-family: $header-font-family;
      padding: 10px;
      &.processing {
        background-image: asset-data-url("icons/three-dots.svg");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 3rem;
        text-indent: -999999px;
      }
    }
  }
}

.menu-item-reveal {
  .menu-item-modal-list {
    margin: 0;
    li.menu-item-modal-list-item {
      display: grid;
      grid-template-columns: 1fr 50px 100px;
      grid-column-gap: 15px;
      align-items: center;
      margin-bottom: rem-calc(13);
    }
    .cart-item-name {
      padding: 0.5em;
    }
    .item-quantity-warning {
      color: #c71e2a;
    }
    .cart-item-price {
      text-align: right;
      font-size: rem-calc(14);
    }
  }
}
.item-options-table {
  font-size: rem-calc(13);
  color: $medium-gray;
  margin: 0;
}

.value-counter {
  // conflicting with add button
  .minus-btn {
    float: left;
    margin: 0 -1px 0 0;
    text-indent: 0;
    width: 30px;
    height: 27px;
    padding-bottom: 3px;
    border: 1px solid #b7b7b7;
    border-radius: 3px 0 0 3px;
    font-weight: 600;
    &:hover {
      border-color: $primary-color;
    }
  }
  .count-input {
    float: left;
    width: rem-calc(38);
    padding: 0;
    text-align: center;
    background: transparent;
    border: 1px solid #b7b7b7;
    border-radius: 2px;
    font-weight: bold;
    color: $black;
    margin: 0;
    height: rem-calc(25);
    box-sizing: content-box;
    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
  .plus-btn {
    float: left;
    margin: 0 0 0 -1px;
    text-indent: 0;
    width: 30px;
    height: 27px;
    padding-bottom: 3px;
    border: 1px solid #b7b7b7;
    border-radius: 0 3px 3px 0;
    font-weight: 600;
    &:hover {
      border-color: $primary-color;
    }
  }
}

.menu-section-items-loading-spinner {
  display: block;
  margin: 1rem auto;
  width: 4rem;
}
