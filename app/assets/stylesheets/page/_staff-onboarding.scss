.staff-onboarding {
  margin: 0 auto;
  padding: 0 2rem;
  border-radius: 12px;

  &__header {
    text-align: center;
    
    h1 {
      color: $primary;
      font-family: 'Museo Slab';
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }
    
    p {
      color: #666;
      font-size: 1.1rem;
      margin: 0;
    }
  }

  &__progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1rem;
    position: sticky;
    top: 0;
    background: #fbfbfb;
    z-index: 2;

    &-step {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 15px;
        left: 60%;
        right: -40%;
        height: 2px;
        background: #e5e5e5;
        z-index: 1;
      }

      &.completed::after {
        background: $primary;
      }

      &-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #e5e5e5;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #999;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;

        &.completed {
          background: $primary;
          color: white;
        }

        &.active {
          background: $primary;
          color: white;
          box-shadow: 0 0 0 4px rgba(31, 158, 134, 0.2);
        }
      }

      &-label {
        font-size: 0.8rem;
        color: #666;
        text-align: center;
        font-weight: 500;

        &.active {
          color: $primary;
          font-weight: 600;
        }
      }
    }
  }
}

.staff-accordion {
  margin-bottom: 1rem;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &.active {
    background: white;
    border-color: $primary;
    box-shadow: 0 2px 12px rgba(31, 158, 134, 0.1);
  }

  &__header {
    background: #f8f9fa;
    padding: 1.25rem 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    border: none;
    width: 100%;
    text-align: left;

    &:hover {
      background: #f0f1f3;
    }

    &.active {
      background: $primary;
      color: white;
    }

    &-content {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    &-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: $primary;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      font-weight: bold;
      flex-shrink: 0;

      &.completed {
        background: #28a745;
      }
    }

    &-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0;
      color: inherit;
    }

    &-subtitle {
      font-size: 0.9rem;
      opacity: 0.8;
      margin: 0;
      color: inherit;
    }

    &-chevron {
      width: 20px;
      height: 20px;
      transition: transform 0.3s ease;
      opacity: 0.7;

      &.active {
        transform: rotate(180deg);
      }
    }
  }

  &__content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;

    &.active {
      max-height: 1000px;
      padding: 1.5rem;
    }

    &-inner {
      background: white;
    }
  }
}

.staff-form {
  &__row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;

    &.single {
      grid-template-columns: 1fr;
    }

    @include media-down(hamburger) {
      grid-template-columns: 1fr;
    }
  }

  &__field {
    margin-bottom: 1rem;

    label {
      display: block;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 1px solid #dde3e8;
      border-radius: 6px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: white;

      &:focus {
        outline: none;
        border-color: $primary;
        box-shadow: 0 0 0 3px rgba(31, 158, 134, 0.1);
      }

      &::placeholder {
        color: #999;
      }
    }

    &__p {
      margin-bottom: 1rem;
      color: #666;
      font-size: 0.9rem;
    }
  }

  &__notice {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
    text-align: center;
    &--green {
      background: #d4edda;
      border: 1px solid #c3e6cb;
    }
    &--yellow {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
    }
    &--blue {
      background: #e8f4fd;
      border: 1px solid #bee5eb;
    }
  }

  &__document-section {
    background: #f8f9fa;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;

    h4 {
      margin: 0 0 0.75rem 0;
      color: #333;
      font-size: 1rem;
      font-weight: 600;
    }

    &-actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }
  }

  &__button {
    padding: 0.5rem 1rem;
    border: 2px solid $primary;
    background: white;
    color: $primary;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;

    &:hover {
      background: $primary;
      color: white;
    }

    &.primary {
      background: $primary;
      color: white;

      &:hover {
        background: darken($primary, 10%);
      }
    }

    &.small {
      padding: 0.4rem 0.8rem;
      font-size: 0.8rem;
    }
  }
}

.staff-completion {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 2rem;

  &__icon {
    width: 60px;
    height: 60px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
  }

  h3 {
    color: #28a745;
    margin-bottom: 0.5rem;
  }

  p {
    color: #666;
    margin: 0;
  }
}

.google-address-input {
  position: relative;

  &__hint {
    margin-top: 0.25rem;

    small {
      color: #666;
      font-size: 0.8rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }
  }

  .staff-form__input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #dde3e8;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 3px rgba(31, 158, 134, 0.1);
    }

    &.google-selected {
      border-color: #28a745;
    }

    &::placeholder {
      color: #999;
    }
  }
}

// Google Places Autocomplete dropdown styling
.pac-container {
  border-radius: 6px;
  border: 1px solid #e5e5e5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 2px;
  font-family: $body-font-family;
  z-index: 9999;

  .pac-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.9rem;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f8f9fa;
    }

    &.pac-item-selected {
      background: $primary;
      color: white;

      .pac-item-query {
        color: white;
      }
    }

    .pac-icon {
      margin-right: 0.75rem;
      width: 16px;
      height: 16px;
      background-size: contain;
    }

    .pac-item-query {
      font-weight: 600;
      color: #333;
    }

    .pac-matched {
      font-weight: bold;
      color: $primary;
    }
  }

  &:after {
    display: none;
  }
}

// Ensure Google Places dropdown appears above other elements in staff onboarding
.staff-onboarding {
  .pac-container {
    z-index: 10000;
  }
}
