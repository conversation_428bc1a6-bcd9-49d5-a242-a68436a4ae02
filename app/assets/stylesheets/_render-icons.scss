$footer_icons: (
  "cleaning-supplies": "housekeeping",
  "breaky": "toaster",
  "morning-tea": "teapot",
  "working-lunch": "restaurant",
  "grazing-tables": "restaurant",
  "kitchenware": "restaurant",
  "office-bread": "bread",
  "afternoon-tea": "teacup",
  "finger-food": "hand-right",
  "view-all": "plus",
  "team-ordering": "team-order",
  "event": "event",
  "office-snacks": "chocolate-bar",
  "office-fruit": "apple",
  "office-milk": "milk",
  "office-alcohol": "wine-glass",
  "office-coffee": "coffee",
  "office-coffee-supplies": "coffee",
  "office-drinks": "solo-cup",
  "pantry": "vegetarian_food",
  "office-snacks-pantry": "chocolate-bar",
  "help": "technical-support-white",
  "about": "about-us",
  "why": "why-yordar",
  "contact": "contact-us",
  "marker": "marker",
  "suppliers": "suppliers",
  "blog": "hand-right",
  "indigenous-catering": 'indigenous',
  "diverse": 'diverse',
  'eco-friendly': 'eco-friendly'
);

$header_icons: (
  "account-billing",
  "apple",
  "chocolate-bar",
  "coffee",
  "credit-card",
  "event",
  "hand-right",
  "icon-search-white",
  "invoice",
  "logout",
  "marker",
  "milk",
  "my-orders",
  "new-order",
  "pantry",
  "plus",
  "restaurant",
  "shopcart",
  "shopcart-dark",
  "shopping-basket",
  "solo-cup",
  "staff-lunch",
  "teacup",
  "team-order",
  "teapot",
  "technical-support",
  "toaster",
  "user",
  "wine-glass"
);

$icons_checkout: (
  "company",
  "user-grey",
  "phone",
  "email",
  "frequency",
  "date",
  "marker-grey",
  "note",
  "burger",
  "purchase-order"
);

$icons_team_order_dropdowns: (
  "teams": "teams",
  "category": "cuisines",
  "other": "other",
);

$icons_order_status: (
  draft: #cccccc,
  confirmed: #2bffc6,
  new: #24bbff,
  amended: #ffc626,
  cancelled: #ff628d,
  paused: #ff628d,
  delivered: #b388f9,
  skipped: pink,
  pending: aqua,
  rejected: #ff628d,
  quoted: gray,
  saved: gray,
  voided: $black,
);

$icons_order_attendee_status: (
  invited: #24bbff,
  registered: #24bbff,
  pending: aqua,
  ordered: #2bffc6,
  declined: #ff628d,
  cancelled: #ff628d,
);

$icons_team_order_expiring_status: (
  order-placed: #24bbff,
  soon: #2bffc6,
  today: #ffc626,
  expired: #ff628d,
);

@mixin header_icons {
  @each $name in $header_icons {
    &.#{$name}-icon {
      &::before {
        background-image: asset-data-url("icons/#{$name}.svg");
      }
    }
  }
}

@mixin footer_icons {
  @each $name, $icon in $footer_icons {
    &.#{$name}::before {
      background-image: asset-data-url("icons/#{$icon}.svg");
    }
  }
}

@mixin icons_checkout {
  @each $name in $icons_checkout {
    &.#{$name}-icon::before {
      background-image: asset-data-url("icons/#{$name}.svg");
    }
  }
}

@mixin icons_order_status {
  @each $name, $color in $icons_order_status {
    &.#{$name}::before {
      background-color: $color;
    }
  }
}

@mixin icons_order_attendee_status {
  @each $name, $color in $icons_order_attendee_status {
    &.#{$name}::before {
      background-color: $color;
    }
  }
}

@mixin icons_team_order_expiring_status {
  @each $name, $color in $icons_team_order_expiring_status {
    &.#{$name}::before {
      background-color: $color;
    }
  }
}

@mixin icons_team_order_dropdowns {
  @each $class, $icon in $icons_team_order_dropdowns {
    &--#{$class} .dropdown-filter-button::before {
      background: asset-data-url("icons/#{$icon}.svg") no-repeat;
    }
  }
}
