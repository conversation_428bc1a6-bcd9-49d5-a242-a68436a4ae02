/*=============================================>>>>>
= Body font colour =
===============================================>>>>>*/
$textcolor: #241c15;

/*=============================================>>>>>
= Brand colours =
===============================================>>>>>*/
$primary: #1f9e86;
$secondary: #4db6ac;
$tertiary: #efc900;
$secondary-darker: #3e4248;
$mammoth-charcoal: #2d2d2d;
$green: #d8eacc;
$green-darker: #bff457;
$yellow: #f8ecb6;
$highlight: #fb35dc;

/*=============================================>>>>>
= Additional =
===============================================>>>>>*/
$border: 1px solid #e3e3e3;

/*=============================================>>>>>
= Black/white =
===============================================>>>>>*/
$white: #ffffff;
$black: #000000;
$yordar-black: $textcolor;
$light-white: #fbfbfb;
$off-white: #dedede; // light grey
$off-white-lighter: #f4f4f4; // lighter grey
$off-white-lightest: #fafafa; // lightest grey
$off-white-mid: #d9d9d9;
$off-white-darker: #c7c7c7;
$off-black: #7b7b7b; // grey
$off-black-lighter: #97938c;
$off-black-darker: #666666;
$off-white-secondary: #f2f2f2; // #SorryNotSorrySam #BirdBrainBrown

/*=============================================>>>>>
= User notification colours =
===============================================>>>>>*/
$warning: #f0ad4e;
$error: #d9534f; // #c71e2a


/*=============================================>>>>>
= Media =
===============================================>>>>>*/
$wrapper-width: 1300px;

$breakpoints: (
  small: 0,
  small-mobile: 400px,
  mobile: 550px,
  medium: 640px,
  large-mobile: 750px,
  hamburger: 900px,
  small-tablet: 960px,
  large: 1024px,
  tablet: 1080px,
  small-desktop: 1200px,
  xlarge: 1200px,
  wrapper: $wrapper-width + 40px,
  desktop: 1340px,
  xxlarge: 1440px,
  xxxlarge: 1600px,
) !default;

/*=============================================>>>>>
= Media Up =
===============================================>>>>>*/

// @include media-up(mobile) {}
@mixin media-up($breakpoint) {
  // If the breakpoint exists in the map.
  @if map-has-key($breakpoints, $breakpoint) {
    // Get the breakpoint value.
    $breakpoint-value: map-get($breakpoints, $breakpoint);

    // Write the media query.
    @media (min-width: $breakpoint-value) {
      @content;
    }

    // If the breakpoint doesn't exist in the map.
  } @else {
    // Log a warning.
    @warn 'Invalid breakpoint: #{$breakpoint}.';
  }
}

/*=============================================>>>>>
= Media Down =
===============================================>>>>>*/

// @include media-down(mobile) {}
@mixin media-down($breakpoint) {
  // If the breakpoint exists in the map.
  @if map-has-key($breakpoints, $breakpoint) {
    // Get the breakpoint value.
    $breakpoint-value: map-get($breakpoints, $breakpoint);

    // Write the media query.
    @media (max-width: ($breakpoint-value - 1)) {
      @content;
    }

    // If the breakpoint doesn't exist in the map.
  } @else {
    // Log a warning.
    @warn 'Invalid breakpoint: #{$breakpoint}.';
  }
}

/*=============================================>>>>>
= Media Between =
===============================================>>>>>*/

// @include media-between(mobile, tablet) {}
@mixin media-between($lower, $upper) {
  // If both the lower and upper breakpoints exist in the map.
  @if map-has-key($breakpoints, $lower) and map-has-key($breakpoints, $upper) {
    // Get the lower and upper breakpoints.
    $lower-breakpoint: map-get($breakpoints, $lower);
    $upper-breakpoint: map-get($breakpoints, $upper);

    // Write the media query.
    @media (min-width: $lower-breakpoint) and (max-width: ($upper-breakpoint - 1)) {
      @content;
    }

    // If one or both of the breakpoints don't exist.
  } @else {
    // If lower breakpoint is invalid.
    @if (map-has-key($breakpoints, $lower) == false) {
      // Log a warning.
      @warn 'Your lower breakpoint was invalid: #{$lower}.';
    }
    // If upper breakpoint is invalid.
    @if (map-has-key($breakpoints, $upper) == false) {
      // Log a warning.
      @warn 'Your upper breakpoint was invalid: #{$upper}.';
    }
  }
}

/*=============================================>>>>>
= Placeholder =
===============================================>>>>>*/
@mixin placeholder {
  &::placeholder {
    @content;
  }
}

/*=============================================>>>>>
= Animations =
===============================================>>>>>*/

@mixin keyframes($animation_name) {
  @keyframes $animation_name {
    @content;
  }
}

@mixin animation($animation: bounceIn, $duration: 1s, $delay: 0s) {
  animation-delay: $delay;
  animation-duration: $duration;
  animation-name: $animation;
  animation-fill-mode: forwards; /* this prevents the animation from restarting! */
}

/*=============================================>>>>>
= Ellipsis =
===============================================>>>>>*/

@mixin ellipsis($width: 100%) {
  display: inline-block;
  max-width: $width;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/*=============================================>>>>>
= Position =
===============================================>>>>>*/

@mixin vertical-align($position: absolute) {
  position: $position;
  top: 50%;
  transform: translateY(-50%);
}

@mixin horizontal-align($position: absolute) {
  position: $position;
  left: 50%;
  transform: translateX(-50%);
}

@mixin center-align($position: absolute) {
  position: $position;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
}

/*=============================================>>>>>
= Hamburger =
===============================================>>>>>*/

@mixin hamburger($color: $white) {
  width: 24px;
  height: 24px;
  position: relative;
  outline: none;
  cursor: pointer;
  border: 0;
  background: none;
  padding: 0;
  &:before,
  &:after {
    content: "";
    display: block;
    width: 100%;
    height: 2px;
    background: $color;
    transition: transform 0.3s;
  }
  &:after {
    margin: 8px 0 0;
  }
  &.active {
    &:before {
      position: absolute;
      top: 11px;
      transform: rotate(45deg);
    }
    &:after {
      position: absolute;
      bottom: 11px;
      transform: rotate(-45deg);
    }
  }
}

/*=============================================>>>>>
= Clearfix =
===============================================>>>>>*/

@mixin clear {
  // For modern browsers
  &:before,
  &:after {
    content: " ";
    display: table;
  }

  &:after {
    clear: both;
  }

  // For IE 6/7 (trigger hasLayout)
  & {
    *zoom: 1;
  }
}

/*=============================================>>>>>
= Overflow Y =
===============================================>>>>>*/

@mixin overflowY {
  overflow-y: auto;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  -webkit-overflow-scrolling: touch;
}

/*=============================================>>>>>
= Typography Mixins and Variables =
===============================================>>>>>*/

// Font weight variables
$font-weight-thin: 100;
$font-weight-extra-light: 200;
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semi-bold: 600;
$font-weight-bold: 700;
$font-weight-extra-bold: 800;
$font-weight-black: 900;

@mixin headings-font() {
  font-family: "Museo Sans", sans-serif;
}

@mixin body-font() {
  font-family: "Museo Sans", sans-serif;
}

@mixin h1($color-white: false) {
  @include headings-font();
  font-size: 36px;
  line-height: calc(45 / 36);
  @if $color-white == true {
    color: $white;
  } @else {
    color: $textcolor;
  }
  @include media-down(mobile) {
    font-size: 29px;
  }
}
@mixin h2($color-white: false) {
  @include headings-font();
  font-size: 30px;
  line-height: calc(40 / 30);
  @if $color-white == true {
    color: $white;
  } @else {
    color: $textcolor;
  }
  @include media-down(tablet) {
    font-size: 23px;
  }
}
@mixin h3($color-white: false) {
  @include headings-font();
  font-size: 16px;
  line-height: calc(19 / 16);
  @include media-down(tablet) {
    font-size: 15px;
  }
  @if $color-white == true {
    color: $white;
  } @else {
    color: $textcolor;
  }
}
@mixin h4() {
  @include headings-font();
  font-size: 14px;
  font-weight: $font-weight-bold;
  line-height: calc(20 / 14);
  text-transform: uppercase;
  color: $textcolor;
}

@mixin p1() {
  @include body-font();
  font-size: 16px;
  line-height: calc(26 / 16);
  font-weight: $font-weight-medium;
  color: $textcolor;
}
@mixin p2() {
  @include body-font();
  font-weight: $font-weight-medium;
  font-size: 13px;
  color: $off-black-darker;
  line-height: calc(16 / 13);
  letter-spacing: 0;
}

@mixin transition($sec) {
  -webkit-transition: all $sec;
  -moz-transition: all $sec;
  -ms-transition: all $sec;
  -o-transition: all $sec;
  transition: all $sec;
}

@mixin rotate($deg, $tX, $tY, $sX, $sY) {
  -moz-transform: scale(1) rotate($deg) translate($tX, $tY) skew($sX, $sY);
  -webkit-transform: scale(1) rotate($deg) translate($tX, $tY) skew($sX, $sY);
  -o-transform: scale(1) rotate($deg) translate($tX, $tY) skew($sX, $sY);
  -ms-transform: scale(1) rotate($deg) translate($tX, $tY) skew($sX, $sY);
  transform: scale(1) rotate($deg) translate($tX, $tY) skew($sX, $sY);
}

@mixin bg-cover($img_url: null) {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: transparent;
  @if $img_url != null {
    background-image: url($img_url);
  }
}

@mixin bg-contain($img_url: null) {
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: transparent;
  @if $img_url != null {
    background-image: url($img_url);
  }
}

@mixin bg-hundred($img_url: null) {
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: transparent;
  @if $img_url != null {
    background-image: url($img_url);
  }
}

@mixin vertical-parent {
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

@mixin vertical-align($top: 50%, $translateY: 50%) {
  position: absolute;
  top: $top;
  -webkit-transform: translateY(-$translateY);
  -moz-transform: translateY(-$translateY);
  -ms-transform: translateY(-$translateY);
  -o-transform: translateY(-$translateY);
  transform: translateY(-$translateY);
}

@mixin horizontal-align($left: 50%, $translateX: 50%) {
  position: absolute;
  left: $left;
  -webkit-transform: translateX(-$translateX);
  -moz-transform: translateX(-$translateX);
  -ms-transform: translateX(-$translateX);
  -o-transform: translateX(-$translateX);
  transform: translateX(-$translateX);
}

@mixin vertical-horizontal-align(
  $top: 50%,
  $translateY: 50%,
  $left: 50%,
  $translateX: 50%
) {
  position: absolute;
  top: $top;
  left: $left;
  -webkit-transform: translate(-$translateY, -$translateX);
  -moz-transform: translate(-$translateY, -$translateX);
  -ms-transform: translate(-$translateY, -$translateX);
  -o-transform: translate(-$translateY, -$translateX);
  transform: translate(-$translateY, -$translateX);
}

@mixin hover_brighten($color: #000, $opacity: 0.5, $transition: 0.5s) {
  position: relative;
  @include transition($transition);
  &:hover:after {
    opacity: 1;
  }
  &:after {
    content: " ";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba($color, $opacity);
    opacity: 0;
    @include transition(0.6s);
  }
}

@mixin object-fit($val: cover) {
  object-fit: $val;
  -webkit-object-fit: $val;
  -moz-object-fit: $val;
  -ms-object-fit: $val;
  -o-object-fit: $val;
  /** available values: fill, cover, contain, unset, initial, inherit, scale-down, none**/
}

@mixin object-position($posX: center, $posY: center) {
  object-position: $posX $posY;
  -webkit-object-position: $posX $posY;
  -moz-object-position: $posX $posY;
  -ms-object-position: $posX $posY;
  -o-object-position: $posX $posY;
}

@mixin image-cover-center {
  object-fit: cover;
  -webkit-object-fit: cover;
  -moz-object-fit: cover;
  -ms-object-fit: cover;
  -o-object-fit: cover;

  object-position: center center;
  -webkit-object-position: center center;
  -moz-object-position: center center;
  -ms-object-position: center center;
  -o-object-position: center center;
}

.v-align-row {
  display: table;
  width: 100%;
  height: 100%;
  .v-align-cell {
    display: table-cell;
    height: 100%;
    vertical-align: middle;
    float: none;
  }
}

/* Margin and Padding */
.no-margin {
  margin: 0 !important;
}
.margin-bottom {
  &-half {
    margin-bottom: 0.5rem;
  }
  &-1 {
    margin-bottom: 1rem;
  }
  &-2 {
    margin-bottom: 2rem;
  }
}
.margin-top {
  &-1 {
    margin-top: 1rem;
  }
  &-2 {
    margin-top: 2rem;
  }
}
.margin-left {
  &-half {
    margin-left: 0.5rem !important;
  }
  &-1 {
    margin-left: 1rem;
  }
  &-2 {
    margin-left: 2rem;
  }
}

/* Dividers and borders */
.divider {
  &-bottom {
    border-bottom: 1px solid #e2e2e2;
  }
  &-top {
    border-top: 1px solid #e2e2e2;
  }
}

@mixin shimmer {
  background: #f6f7f8;
  background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
  background-repeat: no-repeat;
  background-size: 1000px 1000px;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: placeholderShimmer;
  animation-timing-function: linear;
}

@keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}
