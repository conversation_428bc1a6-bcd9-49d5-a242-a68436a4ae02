/*=============================================>>>>>
= Font Helper =
===============================================>>>>>*/

@font-face {
  font-family: 'Museo Slab';
  src: asset-data-url("museoslab-500-webfont.woff2") format("woff2"), asset-data-url("museoslab-500-webfont.woff") format("woff");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Slab';
  src: asset-data-url("museoslab-700-webfont.woff2") format("woff2"), asset-data-url("museoslab-700-webfont.woff") format("woff");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Slab';
  src: asset-data-url("museoslab-900-webfont.woff2") format("woff2"), asset-data-url("museoslab-900-webfont.woff") format("woff");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Sans';
  src: asset-data-url("museosans_500-webfont.woff2") format("woff2"), asset-data-url("museosans_500-webfont.woff") format("woff");
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Sans';
  src: asset-data-url("museosans_700-webfont.woff2") format("woff2"), asset-data-url("museosans_700-webfont.woff") format("woff");
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Sans';
  src: asset-data-url("museosans_900-webfont.woff2") format("woff2"), asset-data-url("museosans_900-webfont.woff") format("woff");
  font-weight: 900;
  font-display: swap;
  font-style: normal;
}
