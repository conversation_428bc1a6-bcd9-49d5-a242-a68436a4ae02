// Foundation for Sites by ZURB
// foundation.zurb.com
// Licensed under MIT Open Source

// scss-lint:disable MergeableSelector, QualifyingElement

////
/// @group table
////

/// Default color for table background.
/// @type Color
$table-background: $white  !default;

/// Default scale for darkening the striped table rows and the table border.
/// @type Number
$table-color-scale: 5% !default;

/// Default style for table border.
/// @type List
$table-border: 1px solid smart-scale($table-background, $table-color-scale) !default;

/// Default padding for table.
/// @type Number
$table-padding: rem-calc(8 10 10) !default;

/// Default scale for darkening the table rows on hover.
/// @type Number
$table-hover-scale: 2% !default;

/// Default color of standard rows on hover.
/// @type List
$table-row-hover: darken($table-background, $table-hover-scale) !default;

/// Default color of striped rows on hover.
/// @type List
$table-row-stripe-hover: darken($table-background, $table-color-scale + $table-hover-scale) !default;

/// Default background color for striped rows.
/// @type Color
$table-striped-background: smart-scale($table-background, $table-color-scale) !default;

/// Default value for showing the stripe on rows of the tables, excluding the header and footer. If even, the even rows will have a background color. If odd, the odd rows will have a background color. If empty, or any other value, the table rows will have no striping.
/// @type Keyword
$table-stripe: even !default;

/// Default color for header background.
/// @type Color
$table-head-background: smart-scale($table-background, $table-color-scale / 2) !default;

/// Default color for footer background.
/// @type Color
$table-foot-background: smart-scale($table-background, $table-color-scale) !default;

/// Default font color for header.
/// @type Color
$table-head-font-color: $body-font-color !default;

/// Default value for showing the header when using stacked tables.
/// @type Boolean
$show-header-for-stacked: false !default;

@mixin -zf-table-children-styles($stripe: $table-stripe) {
  thead,
  tbody,
  tfoot {
    border: $table-border;
    background-color: $table-background;
  }

  // Caption
  caption {
    font-weight: $global-weight-bold;
    padding: $table-padding;
  }

  // Table head and foot
  thead,
  tfoot {
    background: $table-head-background;
    color: $table-head-font-color;

    // Rows within head and foot
    tr {
      background: transparent;
    }

    // Cells within head and foot
    th,
    td {
      padding: $table-padding;
      font-weight: $global-weight-bold;
      text-align: #{$global-left};
    }
  }

  // Table rows
  tbody {
    tr {
      // If stripe is set to even, darken the even rows.
      @if $stripe == even {
        &:nth-child(even) {
          background-color: $table-striped-background;
        }
      }

      // If stripe is set to odd, darken the odd rows.
      @else if $stripe == odd {
        &:nth-child(odd) {
          background-color: $table-striped-background;
        }
      }
    }

    th,
    td {
      padding: $table-padding;
    }
  }
}

/// Adds the general styles for tables.
/// @param {Keyword} $stripe [$table-stripe] - Uses keywords even, odd, or none to darken rows of the table. The default value is even.
@mixin table(
  $stripe: $table-stripe,
  $nest: false
) {
  width: 100%;
  margin-bottom: $global-margin;
  border-radius: $global-radius;

  @if $nest {
    @include -zf-table-children-styles($stripe);
  }
  @else {
    @at-root {
      @include -zf-table-children-styles($stripe);
    }
  }
}

/// Adds the ability to horizontally scroll the table when the content overflows horizontally.
@mixin table-scroll {
  display: block;
  width: 100%;
  overflow-x: auto;
}

/// Slightly darkens the table rows on hover.
@mixin table-hover {
  tr {
    //Darkens the non-striped table rows on hover.
    &:hover {
      background-color: $table-row-hover;
    }

    //Darkens the even striped table rows.
    @if($table-stripe == even) {
      &:nth-of-type(even):hover {
        background-color: $table-row-stripe-hover;
      }
    }

    //Darkens the odd striped table rows.
    @elseif($table-stripe == odd) {
      &:nth-of-type(odd):hover {
        background-color: $table-row-stripe-hover;
      }
    }
  }
}

/// Adds styles for a stacked table. Useful for small-screen layouts.
/// @param {Boolean} $header [$show-header-for-stacked] - Show the first th of header when stacked.
@mixin table-stack($header: $show-header-for-stacked) {
  @if $header {
    thead {
      th:first-child {
        display: block;
      }

      th {
        display: none;
      }
    }
  }
  @else {
    thead {
      display: none;
    }
  }

  tfoot {
    display: none;
  }

  tr,
  th,
  td {
    display: block;
  }

  td {
    border-top: 0;
  }
}

@mixin foundation-table($nest: false) {
  table {
    @include table($nest: $nest);
  }

  table.stack {
    @include breakpoint(medium down) {
      @include table-stack;
    }
  }

  table.scroll {
    @include table-scroll;
  }

  table.hover {
    @include table-hover;
  }

  .table-scroll {
    overflow-x: auto;

    table {
      width: auto;
    }
  }
}
