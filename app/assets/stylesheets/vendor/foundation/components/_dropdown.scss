// Foundation for Sites by ZURB
// foundation.zurb.com
// Licensed under MIT Open Source

////
/// @group dropdown
////

/// Padding for dropdown panes.
/// @type List
$dropdown-padding: 1rem !default;

/// Border for dropdown panes.
/// @type List
$dropdown-border: 1px solid $medium-gray !default;

/// Font size for dropdown panes.
/// @type List
$dropdown-font-size: 1rem !default;

/// Width for dropdown panes.
/// @type Number
$dropdown-width: 300px !default;

/// Border radius dropdown panes.
/// @type Number
$dropdown-radius: $global-radius !default;

/// Sizes for dropdown panes. Each size is a CSS class you can apply.
/// @type Map
$dropdown-sizes: (
  tiny: 100px,
  small: 200px,
  large: 400px,
) !default;

/// Applies styles for a basic dropdown.
@mixin dropdown-container {
  background-color: $body-background;
  border: $dropdown-border;
  border-radius: $dropdown-radius;
  display: block;
  font-size: $dropdown-font-size;
  padding: $dropdown-padding;
  position: absolute;
  visibility: hidden;
  width: $dropdown-width;
  z-index: 10;

  &.is-open {
    visibility: visible;
  }
}

@mixin foundation-dropdown {
  .dropdown-pane {
    @include dropdown-container;
  }

  @each $name, $size in $dropdown-sizes {
    .dropdown-pane.#{$name} {
      width: $size;
    }
  }
}
