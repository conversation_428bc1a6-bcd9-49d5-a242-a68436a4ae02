// Foundation for Sites by ZURB
// foundation.zurb.com
// Licensed under MIT Open Source

////
/// @group menu
////

/// Margin of a menu.
/// @type Number
$menu-margin: 0 !default;

/// Left-hand margin of a nested menu.
/// @type Number
$menu-margin-nested: 1rem !default;

/// Padding for items in a menu.
/// @type Number
$menu-item-padding: 0.7rem 1rem !default;

/// Text color of an active menu item.
/// @type Color
$menu-item-color-active: $white !default;

/// Background color of an active menu item.
/// @type Color
$menu-item-background-active: map-get($foundation-palette, primary) !default;

/// Spacing between an icon and text in a menu item.
/// @type Number
$menu-icon-spacing: 0.25rem !default;

/// Creates the base styles for a Menu.
@mixin menu-base {
  margin: $menu-margin;
  list-style-type: none;

  @if $global-flexbox {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }

  // List items are table cell to allow for vertical alignment
  > li {
    @include disable-mouse-outline;

    @if $global-flexbox {
      flex: 0 0 auto;
    }
    @else {
      display: table-cell;
      vertical-align: middle;
    }
  }

  // Reset line height to make the height of the overall item easier to calculate
  > li > a {
    display: block;
    padding: $menu-item-padding;
    line-height: 1;
  }

  // Reset styles of inner elements
  input,
  a,
  button {
    margin-bottom: 0;
  }
}

/// Expands the items of a Menu, so each item is the same width.
@mixin menu-expand {
  @if $global-flexbox {
    // scss-lint:disable ZeroUnit
    > li {
      flex: 1 1 0px;
    }
  }
  @else {
    width: 100%;
    display: table;
    table-layout: fixed;
  }
}

/// Sets the direction of a Menu.
/// @param {Keyword} $dir [horizontal] - Direction of the Menu. Can be `horizontal` or `vertical`.
@mixin menu-direction($dir: horizontal) {
  @if $dir == horizontal {
    @if $global-flexbox {
      flex-wrap: nowrap;

      > li {
        flex: 0 0 auto;
      }
    }
    @else {
      > li {
        display: table-cell;
      }
    }
  }
  @else if $dir == vertical {
    @if $global-flexbox {
      flex-wrap: wrap;

      > li {
        flex: 0 0 100%;
        max-width: 100%;
      }

      > li  > a {
        align-items: flex-start;
        justify-content: flex-start;
      }
    }
    @else {
      > li {
        display: block;
      }
    }
  }
  @else {
    @warn 'The direction used for menu-direction() must be horizontal or vertical.';
  }
}

/// Creates a simple Menu, which has no padding or hover state.
@mixin menu-simple {
  li {
    line-height: 1;
    display: inline-block;
    margin-#{$global-right}: get-side($menu-item-padding, $global-right);
  }

  a {
    padding: 0;
  }
}

/// Adds styles for a nested Menu, by adding `margin-left` to the menu.
/// @param {Keyword|Number} $padding [auto] - Length of the margin.
@mixin menu-nested($margin: $menu-margin-nested) {
  margin-#{$global-left}: $margin;
}

/// Adds support for icons to Menu items.
/// @param {Keyword} $position [side] - Positioning for icons. Can be `side` (left, or right on RTL) or `top`.
/// @param {Boolean} $base [true] - Set to `false` to prevent the shared CSS between side- and top-aligned icons from being printed. Set this to `false` if you're calling the mixin multiple times on the same element.
@mixin menu-icons($position: side, $base: true) {
  @if $base {
    @if $global-flexbox {
      > li > a {
        display: flex;
      }
    }
    @else {
      > li > a {
        img,
        i,
        svg {
          vertical-align: middle;

          + span {
            vertical-align: middle;
          }
        }
      }
    }
  }

  @if $position == side {
    > li > a {
      @if $global-flexbox {
        flex-flow: row nowrap;
      }

      img,
      i,
      svg {
        margin-#{$global-right}: $menu-icon-spacing;

        @if not $global-flexbox {
          display: inline-block;
        }
      }
    }
  }
  @else if $position == top {
    > li > a {
      @if $global-flexbox {
        flex-flow: column nowrap;
      }
      @else {
        text-align: center;
      }

      img,
      i,
      svg {
        @if not $global-flexbox {
          display: block;
          margin: 0 auto $menu-icon-spacing;
        }
        @else {
          align-self: stretch;
          text-align: center;
          margin-bottom: $menu-icon-spacing;
        }
      }
    }
  }
}

@mixin menu-text {
  font-weight: bold;
  color: inherit;
  line-height: 1;
  padding-top: 0;
  padding-bottom: 0;
  padding: $menu-item-padding;
}

@mixin foundation-menu {
  .menu {
    @include menu-base;
    @include menu-icons;

    // Orientation
    @include menu-direction(horizontal);

    &.vertical {
      @include menu-direction(vertical);
    }

    @each $size in $breakpoint-classes {
      @if $size != $-zf-zero-breakpoint {
        @include breakpoint($size) {
          &.#{$size}-horizontal {
            @include menu-direction(horizontal);
          }

          &.#{$size}-vertical {
            @include menu-direction(vertical);
          }
        }
      }
    }

    // Simple
    &.simple {
      @include menu-simple;
    }

    // Align right
    &.align-#{$global-right} {
      @if $global-flexbox {
        justify-content: flex-end;
      }
      @else {
        @include clearfix;

        > li {
          float: $global-right;
        }
      }
    }

    // Even-width
    &.expanded {
      @include menu-expand;

      > li:first-child:last-child {
        width: 100%;
      }
    }

    // Vertical icons
    &.icon-top {
      @include menu-icons(top, $base: false);
    }

    // Nesting
    &.nested {
      @include menu-nested;
    }

    // Active state
    .active > a {
      color: $menu-item-color-active;
      background: $menu-item-background-active;
    }
  }

  .menu-text {
    @include menu-text;
  }

  // Align center
  .menu-centered {
    text-align: center;

    > .menu {
      display: inline-block;
    }
  }

  // Prevent FOUC when using the Responsive Menu plugin
  .no-js [data-responsive-menu] ul {
    display: none;
  }
}
