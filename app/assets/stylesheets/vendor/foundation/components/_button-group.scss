// Foundation for Sites by ZURB
// foundation.zurb.com
// Licensed under MIT Open Source

////
/// @group button-group
////

/// Margin for button groups.
/// @type Number
$buttongroup-margin: 1rem !default;

/// Margin between buttons in a button group.
/// @type Border
$buttongroup-spacing: 1px !default;

/// Selector for the buttons inside a button group.
/// @type String
$buttongroup-child-selector: '.button' !default;

/// Maximum number of buttons that can be in an even-width button group.
/// @type Number
$buttongroup-expand-max: 6 !default;

/// Add styles for a button group container.
/// @param {String} $child-selector [$buttongroup-child-selector] - Selector for the buttons inside a button group.
@mixin button-group(
  $child-selector: $buttongroup-child-selector
) {
  @include clearfix;
  margin-bottom: $buttongroup-margin;

  @if $global-flexbox {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
  }
  @else {
    font-size: 0;
  }

  #{$child-selector} {
    margin: 0;
    margin-#{$global-right}: $buttongroup-spacing;
    margin-bottom: $buttongroup-spacing;
    font-size: map-get($button-sizes, default);

    @if $global-flexbox {
      flex: 0 0 auto;
    }

    &:last-child {
      margin-#{$global-right}: 0;
    }
  }
}

/// Creates a full-width button group, making each button equal width.
/// @param {String} $selector [$buttongroup-child-selector] - Selector for the buttons inside a button group.
@mixin button-group-expand(
  $selector: $buttongroup-child-selector,
  $count: null
) {
  @if not $global-flexbox {
    margin-#{$global-right}: -$buttongroup-spacing;

    &::before,
    &::after {
      display: none;
    }
  }

  // scss-lint:disable ZeroUnit
  #{$selector} {
    @if $global-flexbox {
      flex: 1 1 0px;
    }
    @else {
      @for $i from 2 through $buttongroup-expand-max {
        &:first-child:nth-last-child(#{$i}) {
          &, &:first-child:nth-last-child(#{$i}) ~ #{$selector} {
            display: inline-block;
            width: calc(#{percentage(1 / $i)} - #{$buttongroup-spacing});
            margin-#{$global-right}: $buttongroup-spacing;

            &:last-child {
              margin-#{$global-right}: $buttongroup-spacing * -$buttongroup-expand-max;
            }
          }
        }
      }
    }
  }
}

/// Stacks the buttons in a button group.
/// @param {String} $selector [$buttongroup-child-selector] - Selector for the buttons inside the button group.
@mixin button-group-stack(
  $selector: $buttongroup-child-selector
) {
  @if $global-flexbox {
    flex-wrap: wrap;
  }

  #{$selector} {
    @if $global-flexbox {
      flex: 0 0 100%;
    }
    @else {
      width: 100%;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/// Un-stacks the buttons in a button group.
/// @param {String} $selector [$buttongroup-child-selector] - Selector for the buttons inside the button group.
@mixin button-group-unstack(
  $selector: $buttongroup-child-selector
) {
  // scss-lint:disable ZeroUnit
  #{$selector} {
    @if $global-flexbox {
      flex: 1 1 0px;
    }
    @else {
      width: auto;
    }
    margin-bottom: 0;
  }
}

@mixin foundation-button-group {
  .button-group {
    @include button-group;

    // Sizes
    @each $size, $value in map-remove($button-sizes, default) {
      &.#{$size} #{$buttongroup-child-selector} {
        font-size: $value;
      }
    }

    // Even-width Group
    &.expanded { @include button-group-expand; }

    // Colors
    @each $name, $color in $foundation-palette {
      @if $button-fill != hollow {
        &.#{$name} #{$buttongroup-child-selector} {
          @include button-style($color, auto, auto);
        }
      }
      @else {
        &.#{$name} #{$buttongroup-child-selector} {
          @include button-hollow;
          @include button-hollow-style($color);
        }
      }
    }

    &.stacked,
    &.stacked-for-small,
    &.stacked-for-medium {
      @include button-group-stack;
    }

    &.stacked-for-small {
      @include breakpoint(medium) {
        @include button-group-unstack;
      }
    }

    &.stacked-for-medium {
      @include breakpoint(large) {
        @include button-group-unstack;
      }
    }

    // scss-lint:disable MergeableSelector
    &.stacked-for-small.expanded {
      @include breakpoint(small only) {
        display: block;

        #{$buttongroup-child-selector} {
          display: block;
          margin-#{$global-right}: 0;
        }
      }
    }
  }
}
