//= require highcharts
//= require highcharts/highcharts-more
//= require ./components/reports/chart

$(document).ready(function () {
  var $wrapper = $(document)
  initDatepickers($wrapper);
  bindReportEvents($wrapper);
});

function initDatepickers ($wrapper) {
  $wrapper.find('.datepicker').datetimepicker({
    format: 'DD-MM-YYYY',
  });
}

function bindReportEvents($wrapper) {
  $wrapper.on('click', '.generate-graph', function(e) { updateSpendChart(e, $wrapper) });
}

function updateSpendChart (event, $wrapper) {
  var $button = $(event.currentTarget);
  var containerId = $button.data('container-id');
  var $reportForm = $button.parents('form');
  var $spend_container = $('#' + containerId);

  $button.text('Generating...');

  // $spend_container.html(spinner_html);
  var report_request = $.ajax({
    url: $reportForm.prop('action'),
    type: 'GET',
    dataType: 'JSON',
    data: $reportForm.serialize(),
  });

  report_request.done(function(response){
    renderSpendChart(response.report_type, response.source_type, response.report_data, containerId);
  });

  report_request.fail(function(){
    $spend_container.html('');
    alert('Sorry we were unable to gather data for the filter parameters. Please try again!')
  });

  report_request.always(function () {
    $button.text('Generate graph');
  })
}
