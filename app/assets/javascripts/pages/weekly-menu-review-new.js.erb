$(document).ready(function(){
  $('.stars li').on('click', function(){
    var onStar = parseInt($(this).data('value'), 10);
    var stars = $(this).parent().children('li.star');
    for (i = 0; i < stars.length; i++) {
      $(stars[i]).removeClass('selected');
    }
    for (i = 0; i < onStar; i++) {
      $(stars[i]).addClass('selected');
    }
    var target = $(this).closest("div").data("target");
    $(target).val(onStar);
  });
  $('.thumbs li').on('click', function(){
    var selectedThumb = $(this)
    var thumbs = $(this).parent().children('li.thumb');
    // Remove selected class from both thumbs
    for (i = 0; i < thumbs.length; i++) {
      $(thumbs[i]).removeClass('selected');
    }
    selectedThumb.addClass('selected');
    var target = $(this).closest("div").data("target");
    $(target).val(selectedThumb.data('value'));
  });
});

// validate form before submission
function validateForm() {
  // all ratings are required
  var firstName = $('input#weekly_menu_review_first_name').val();
  var lastName = $('input#weekly_menu_review_last_name').val();
  var taste = $('input#weekly_menu_review_taste').val();
  var presentation = $('input#weekly_menu_review_presentation').val();
  var quantity = $('input#weekly_menu_review_quantity').val();
  var seeAgain = $('input#weekly_menu_review_see_again').val();

  if ( firstName && lastName && taste && presentation && quantity && seeAgain ) {
    return true;
  } else {
    // show eror message in pooup
    var displayMsg = "Please don't forget to set your name and ratings";
    $(this).yordarPOP({
      title: "More information required",
      innerContent: displayMsg,
      cancel: false
    });
    setTimeout(function(){
      // Reenable submit button
      $('input.submit').attr('value', 'Submit');
      $('input.submit').removeAttr('disabled');
    }, 300);
    return false;
  }
}
