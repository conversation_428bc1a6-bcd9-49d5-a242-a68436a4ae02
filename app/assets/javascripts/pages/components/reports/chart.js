function renderSpendChart (report_type, source_type, report_data, containerId) {
  var header_text = '';
  if (report_type === 'weekly') {
    header_text = "Week Start: {point.key}";
  } else {
    header_text = "{point.key}";
  }
  var categories = []
  var spend_data = []

  var title = report_type
  title += source_type == 'CustomerProfile' ? ' spends' : ' sales'

  var dataType = source_type == 'CustomerProfile' ? 'Spends' : 'Sales'

  $.each(report_data, function (idx, data) {
    categories[idx] = data.key_date.label
    spend_data[idx] = parseFloat(data.total_spend)
  });

  Highcharts.chart(containerId, {
    chart: {
      type: 'column'
    },
    title: {
      text: title
    },
    xAxis: {
      categories: categories,
      crosshair: true
    },
    yAxis: {
      min: 0
    },
    tooltip: {
      headerFormat: '<span style="font-size:10px">' + header_text + '</span><table>',
      pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
        '<td style="padding:0"><b>${point.y:.2f}</b></td>' +
        '</tr>',
      footerFormat: '</table>',
      shared: true,
      useHTML: true
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0
      }
    },
    series: [{
      name: dataType,
      data: spend_data,
      color: '#fa7980'
    }]
  });
}
