$(document).ready(function () {
  var $wrapper = $(document)
  bindMenuItemElements($wrapper);
});
var MAX_QUANTITY = 9999; // overridden according to the menu item
var BASE_ORDER_LINE_URL = "/api/order_lines";

function bindMenuItemElements($wrapper) {
  $wrapper.on("click", ".favourite", function (e) { toggleFavourite(e) });
  $wrapper.on("click", ".menu-item[data-reveal-id]", function (event) { toggleModal(event, "open") });
  $wrapper.on("click", ".menu-item-reveal .close-reveal", function (event) { toggleModal(event, "close") });

  $wrapper.on("focus", ".menu-item-reveal .menu-item-qty", function (event) { selectQuantity(event) });
  $wrapper.on("change", ".menu-item-reveal .menu-item-qty", function (event) { validateQuantity(event) });
  $wrapper.on("keyup", ".menu-item-reveal .menu-item-qty", function (event) { autoAddToCart(event) })
  $wrapper.on("click", ".menu-item-reveal .toggle-quantity", function (event) { toggleQuantity(event) });
  $wrapper.on("click", ".menu-item-reveal .menu-extra-checbox", function (event) { checkExtrasLimit(event) });
  $wrapper.on("click", ".menu-item-reveal .add-to-cart", function (event) { addToCart(event) });
}

function toggleModal(event, toggleState) {
  var modalId = $(event.currentTarget).data("reveal-id");
  var $modal = $("#" + modalId);
  $modal.foundation(toggleState);
  var $itemQuantities = $modal.find(".menu-item-qty");
  if ($itemQuantities.length == 1) {
    $itemQuantities.first().focus();
  }
}

function selectQuantity (event) {
  var $quantityField = $(event.currentTarget);
  $quantityField.select();
}

function toggleQuantity(event) {
  var $toggleEl = $(event.currentTarget)
  var toggleState = $toggleEl.data("toggle");
  var $quantityInput = $toggleEl.siblings().closest(".menu-item-qty");

  var currentVal = parseInt($quantityInput.val());
  if (isNaN(currentVal)) {
    $quantityInput.val(0);
    currentVal = 0
  }
  var maxQuantity = $quantityInput.prop("max") || MAX_QUANTITY;
  if (toggleState == "increase" && currentVal < maxQuantity) {
    $quantityInput.val(currentVal + 1);
  } else if (toggleState == "decrease" && currentVal > 0) {
    $quantityInput.val(currentVal - 1);
  }
}

function validateQuantity (event) {
  var $quantityInput = $(event.currentTarget);
  var quantity = parseInt($quantityInput.val());
  var maxQuantity = $quantityInput.prop("max") || MAX_QUANTITY;
  if (isNaN(quantity) || quantity < 0) {
    $quantityInput.val(0);
  } else if (quantity > maxQuantity) {
    alert("Cannot add more than " + maxQuantity)
    $quantityInput.val(maxQuantity);
  }
}

// Add to cart if enter on a single input
function autoAddToCart(event) {
  if (event.which == 13 || event.keyCode == 13) { // if enter is pressed
    var $quantityInput = $(event.currentTarget);
    var $menuItem = $quantityInput.parents(".menu-item-modal");
    var isSingleInput = $menuItem.find(".menu-item-qty").length == 1
    var prevQuantity = $quantityInput.val();
    if (isSingleInput) {
      var validQuantity = parseInt($quantityInput.val()) > 0
      var quantityNotChanged = prevQuantity == $quantityInput.val();
      if (validQuantity && quantityNotChanged) {
        $menuItem.find('.add-to-cart').click();
      }
    }
  }
}

function addToCart(event) {
  var possible_order_lines = []

  var $menuItem = $(event.currentTarget).parents(".menu-item-modal");
  var $quantityFields = $menuItem.find(".menu-item-qty")

  for (var i = 0; i < $quantityFields.length; i++) {
    var $quantityField = $($quantityFields[i]);
    var quantity = parseInt($quantityField.val());
    if (quantity > 0) {
      var $servingSize = $quantityField.parents(".serving-size-item")
      var selection_data = set_order_line_data($menuItem, $servingSize, quantity)
      possible_order_lines.push(selection_data)
    }
  }

  if (possible_order_lines.length > 0) {
    add_order_line_to_cart(possible_order_lines, $menuItem);
  }
}

function set_order_line_data($menuItem, $servingSize, quantity) {
  var data = {};
  data.item_id = $menuItem.data("menu-item-id");
  data.serving_size_id = $servingSize.data("serving-size-id");
  data.quantity = quantity;
  data.note = $servingSize.find("input.menu-item-modal-note").val();
  data.is_gst_free = $menuItem.data("is-gst-free");
  data.attendee_id = $(".docket-wrapper").data("team-order-attendee-id");
  // data.company_id = $(".docket-wrapper:visible").data("company-id");

  var $selectedMenuExtras = $menuItem.find("input.menu-extra-checbox:checked");
  if ($selectedMenuExtras.length > 0) {
    data.selected_menu_extra_ids = []
    for (var ex = 0; ex < $selectedMenuExtras.length; ex++) {
      var $menuExtra = $($selectedMenuExtras[ex]);
      data.selected_menu_extra_ids.push($menuExtra.data("menu-extra-id"));
    }
  }
  return data
}


function add_order_line_to_cart(possible_order_lines, $menuItem) {
  var $docket = $(".docket-wrapper");
  var saveData = { order_lines: JSON.stringify(possible_order_lines) };
  saveData.location_id = $(".location-level.is-active").data("location-id");
  saveData.order_id = $docket.data("order-id");
  saveData.supplier_id = $docket.data("supplier-id");
  saveData.is_home_delivery = $docket.data("is-home-delivery");
  saveData.want_htmls = true

  var $submitButton = $menuItem.find(".add-to-cart");
  $submitButton.addClass("processing");
  $submitButton.attr("disabled", true);

  var save_request = $.ajax({
    url: BASE_ORDER_LINE_URL,
    type: "POST",
    data: saveData,
    dataType: "JSON",
  })

  save_request.fail(function(response) {
    var error = response.responseJSON;
    if (error !== undefined && error.redirect_to) {
      resetMenuItem($menuItem);
      $menuItem.yordarPOP({
        container: "#yordarPopUp",
        innerContent: error.message,
        submit: "Ok",
        cancel: 'Close',
        submitHandler: function(event) {
          window.location = error.redirect_to;
        }
      });
    } else {
      alert("Sorry we couldn't add the item to cart");
    }
  });

  save_request.then(function (response) {
    updateDocket(response);
    updateTotals(response);
    updateHeader(response);
    resetMenuItem($menuItem);
  })

  save_request.always(function () {
    $submitButton.removeClass("processing");
    $submitButton.attr("disabled", false);
  })
}

function updateDocket(response) {
  var $docket = $(".docket-wrapper");
  if (!$docket.data("order-id")) {
    $docket.data("order-id", response.order.id);
  }
  var $location = $docket.find(".location-level[data-location-id=" + response.location.id + "]");
  if ($location.length) {
    addOrderLinesToLocation($location, response)
  } else {
    addLocationToDocket($docket, response)
  }
}

function addLocationToDocket($docket, response) {
  var $locationBreak = $docket.find(".location-break");
  var $location = $(response.location_html);
  $locationBreak.before($location);
  Foundation.reInit($docket.find('.cart-accordion'));
  addOrderLinesToLocation($location, response)
}

function addOrderLinesToLocation($location, response) {
  if (!$location.hasClass('is-active')) {
    $location.find('.accordion-title').click();
  }
  var $supplier = $location.find(".supplier-level[data-supplier-id=" + response.supplier.id + "]");
  if ($supplier.length) {
    addOrderLinesToSupplier($supplier, response)
  } else {
    addSupplierToLocation($location, response)
  }
}

function addSupplierToLocation($location, response) {
  var $supplierList = $location.find(".suppliers-list");
  var $supplier = $(response.supplier_html);
  $supplierList.append($supplier);
  addOrderLinesToSupplier($supplier, response);
}

function addOrderLinesToSupplier($supplier, response) {
  var order_lines = response.order_lines;
  for (var ol = 0; ol < order_lines.length; ol++) {
    var order_line = order_lines[ol];
    var $orderLine = $supplier.find(".cart-list-item[data-order-line-id=" + order_line.id + "]")
    if ($orderLine.length) {
      $orderLine.replaceWith(order_line.html);
    } else {
      var $cartList = $supplier.find(".cart-list");
      $cartList.append(order_line.html);
    }
  }
}

function updateTotals(response) {
  var order = response.order;
  var orderTotals = order.totals;
  var $cartFooter = $(".cart-footer");
  $cartFooter.find("#docket-subtotal").text(orderTotals.subtotal);
  $cartFooter.find("#docket-delivery").text(orderTotals.delivery);
  $cartFooter.find("#docket-gst").text(orderTotals.gst);

   var $docketTotal = $cartFooter.find(".docket-total")
  $docketTotal.data('total', orderTotals.total_number);
  if (!$docketTotal.data('hide-total')) {
    $docketTotal.text(orderTotals.total);
  }

  if (orderTotals.topup && orderTotals.topup != '$0.00') {
     $cartFooter.find(".docket-topup").text(orderTotals.topup);
     $cartFooter.find(".topup-wrapper").removeClass('hidden')
  } else {
    $cartFooter.find(".topup-wrapper").addClass('hidden')
  }

  if (orderTotals.surcharge && orderTotals.surcharge != '$0.00') {
     $cartFooter.find(".docket-surcharge").text(orderTotals.surcharge);
     $cartFooter.find(".surcharge-wrapper").removeClass('hidden')
  } else {
    $cartFooter.find(".surcharge-wrapper").addClass('hidden')
  }

  if (order.total_spend && order.remaining_spend) {
    var $docket = $(".docket-wrapper");
    $docket.find(".team-order-total-spend").text(order.total_spend)
    $docket.find(".team-order-remaining-spend").text(order.remaining_spend)
  }
}

function updateHeader(response) {
  var order = response.order;
  var $headerCounter = $(".shopcart-item-counter");
  if ($headerCounter.length > 0) {
    $headerCounter.text(order.order_line_count);
    $headerCounter.removeClass('hidden');
  }
  var $shopcartClear = $('.shopcart-clear.hidden')
  if ($shopcartClear.length > 0) {
    $shopcartClear.removeClass('hidden');
  }
}

function resetMenuItem($menuItem) {
  var $quantityFields = $menuItem.find(".menu-item-qty");
  for (var i = 0; i < $quantityFields.length; i++) {
    var $quantityField = $($quantityFields[i]);
    if ($quantityField.data("with-defaults")) {
      $quantityField.val(1);
    } else {
      $quantityField.val("");
    }
  }
  var $extrasFields = $menuItem.find('.menu-extra-checbox')
  for (var i = 0; i < $extrasFields.length; i++) {
    var $extrasField = $($extrasFields[i]);
    $extrasField.attr('checked', false)
  }
  $menuItem.foundation("close");
}

function checkExtrasLimit (event) {
  var $checkbox = $(event.currentTarget);
  var $menuExtrasList = $checkbox.parents(".menu-extras-list");
  var extrasLimit = $menuExtrasList.data("limit");
  if (extrasLimit) {
   if ($menuExtrasList.find(".menu-extra-checbox:checked").length > extrasLimit) {
      alert("You cannot select more than " + extrasLimit + " extras for this section.");
      $checkbox.prop("checked", false);
    }
  }
}

function toggleFavourite(event) {
  event.stopImmediatePropagation();
  var $favouriteIcon = $(event.currentTarget);
  var menuItemId = $favouriteIcon.closest(".menu-item").data("menu-item-id");
  var isFavourite = $favouriteIcon.hasClass("hr-icon")
  $favouriteIcon.addClass('processing');

  var favourite_request = $.ajax({
    type: (isFavourite ? "DELETE" : "PUT"),
    url: $favouriteIcon.data("url")
  });

  favourite_request.success(function () {
    if (isFavourite) {
      $favouriteIcon.removeClass("hr-icon");
      $favouriteIcon.addClass("he-icon");
    } else {
      $favouriteIcon.removeClass("he-icon");
      $favouriteIcon.addClass("hr-icon");
    }
  })
  favourite_request.always(function () {
    $favouriteIcon.removeClass('processing');
  })
}
