//= require ./single-level-menu-navigation
//= require ./multi-level-menu-navigation
$(document).ready(function () {
  dynamicMenuSectionsNav();
  var $wrapper = $(document);
  bindNavigationElements($wrapper);
});

function bindNavigationElements($wrapper) {
  $(window).on("resize", function () {
    dynamicMenuSectionsNav();
  });
}

function dynamicMenuSectionsNav() {
  var sectionsList = queryAll(".menu-section-list");
  for (var i = 0; i < sectionsList.length; i++) {
    var sectionList = sectionsList[i];
    dynamicMenuSectionNav(sectionList);
  }
}

function dynamicMenuSectionNav(sectionList) {
  var sections = sectionList.querySelectorAll(".menu-section-item");
  var moreButton = sectionList.querySelector(".more-button");
  var spaceAvailable = sectionList.offsetWidth - 20;
  $(sections).removeClass("hidden");
  $(moreButton).addClass("hidden");

  // get all widths
  var allSectionsWidth = 0;
  for (var i = 0; i < sections.length; i++) {
    var section = sections[i];
    allSectionsWidth += section.offsetWidth;
  }

  // if more button required
  if (allSectionsWidth > spaceAvailable) {
    // check items to be moved to more Button
    var moreButtonNavs = [];
    var spaceUsed = 0;
    var buttonWidth = $(moreButton).width();
    var spaceAvailableWithButton = spaceAvailable - buttonWidth - 40;

    for (var i = 0; i < sections.length; i++) {
      var section = sections[i];
      spaceUsed += section.offsetWidth;
      // if section cannot fit
      if (spaceUsed > spaceAvailableWithButton) {
        moreButtonNavs.push(section);
        $(section).addClass("hidden");
      }
    }
    // add to more button list
    var dropdownList = moreButton.querySelector(".dropdown-pane__list");
    dropdownList.innerHTML = "";
    if (moreButtonNavs.length > 0) {
      for (var n = 0; n < moreButtonNavs.length; n++) {
        var nav = moreButtonNavs[n];
        var dropDownNav = document.createElement("li");
        dropDownNav.innerHTML = nav.innerHTML;
        dropdownList.appendChild(dropDownNav);
      }
      $(moreButton).removeClass("hidden");
    }
  }
  //show Nav when categories have been measured and moved into more button
  $(".menu-navigation").css("visibility", "visible");
}
