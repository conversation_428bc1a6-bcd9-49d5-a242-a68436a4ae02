$(document).ready(function () {
  var $wrapper = $(document)
  var multiLevelNav = $(".menu-section-wrapper").data("major-supplier") !== undefined
  if (multiLevelNav) {
    bindMultiLevelElements($wrapper)
  }
});

function bindMultiLevelElements($wrapper) {
  $wrapper.on("click", ".menu-section-group-link", function (e) { showMenuSectionGroup($wrapper, e) });
  $wrapper.on("click", ".menu-section-item-link", function (e) { showMenuSection($wrapper, e) });
  $wrapper.on("keyup", ".menu-items-search__search-input", function (e) { searchMenuSection($wrapper, e) });

  openDefaultMenuSection($wrapper)
}

function openDefaultMenuSection ($wrapper) {
  $(".menu-section-wrapper").data("initial-load", "true") // mark as initial load

  var $firstMenuSectionGroupLink = $wrapper.find(".menu-section-group-link:visible:first")  
  $firstMenuSectionGroupLink.trigger("click");
}

function setMoreButtonText(context, targetSection) {
  var $moreButton = $(context + " .more-button-link");
  if (targetSection.parents(".more-button-wrapper").length > 0) {
    $moreButton.find("span").text(targetSection.text());
    $moreButton.addClass("menu-section-link-selected");
  } else {
    $moreButton.find("span").text("More");
    $moreButton.removeClass("menu-section-link-selected");
  }
}

function showMenuSectionGroup($wrapper, event) {
  var $menuSectionGroupLink = $(event.currentTarget);

  // mark current Menu Section Group as selected
  $wrapper.find(".menu-section-group-link").removeClass("menu-section-group-link-selected");
  var $sectionNavId = $menuSectionGroupLink.attr("data-nav-id");
  var $selectedLink = $wrapper.find("[data-nav-id='" + $sectionNavId + "']");
  $selectedLink.addClass("menu-section-group-link-selected");

  setMoreButtonText(".menu-section-group-navigation", $menuSectionGroupLink)
  // reveal Menu Section
  $wrapper.find(".second-level-navigation").addClass("hidden");
  var $menuSectionGroup = $("." + $menuSectionGroupLink.data("reveal-class"));
  $menuSectionGroup.removeClass("hidden");

  // resize dynamic menu with more button
  dynamicMenuSectionNav($menuSectionGroup.find('.menu-section-list')[0]);
  // show Menu Section
  var $firstMenuSectionLink = $menuSectionGroup.find(".menu-section-item-link").first()
  $firstMenuSectionLink.trigger("click");
}

function showMenuSection($wrapper, event) {
  event.preventDefault();
  // mark menu section as selected
  var $menuSection = $(event.currentTarget)
  $wrapper.find(".menu-section-item-link").removeClass("menu-section-link-selected");
  $menuSection.addClass("menu-section-link-selected")

  setMoreButtonText(".second-level-navigation", $menuSection);

  var $menuSectionWrapper = $wrapper.find(".menu-section-wrapper");
  var $spinner = $wrapper.find(".menu-section-items-loading-spinner")
  $menuSectionWrapper.html("")
  $spinner.removeClass("hidden")

  // clean up search box
  $(".menu-items-search__search-input").val("");

  // check search param on initial load
  var isInitialLoad = $(".menu-section-wrapper").data("initial-load");
  if (isInitialLoad) {
    $(".menu-section-wrapper").data("initial-load", null); // reset
    var search_query = getURLParameter("query");
    if (search_query) {
      var $searchBar = $(".menu-items-search__search-input:visible")
      $searchBar.val(search_query)
      var e = jQuery.Event("keyup");
      e.which = 13;
      $searchBar.trigger(e);
      return
    }
  }  

  var menuSectionRequest = $.ajax({
    type: "GET",
    dataType: "HTML",
    url: $menuSection.attr("href"),
  })

  menuSectionRequest.done(function (response) {
    $menuSectionWrapper.html(response)
    $menuSectionWrapper.foundation()
  })

  menuSectionRequest.fail(function () {
    $menuSectionWrapper.html("<h3>Oops! we are experiencing some trouble getting the menu.</h3>")
  })

  menuSectionRequest.always(function () {
    $spinner.addClass("hidden")
  })
}

var filter_params = {};

function getURLParameter(param_name) {
  if (!filter_params) {
    get_filter_params();
  }
  return filter_params[param_name];
}

function get_filter_params() {
  var sPageURL = window.location.search.substring(1);
  if (!sPageURL) {
    filter_params = {};
    return
  }
  var sURLVariables = sPageURL.split("&");
  var params = {};
  for (var i = 0; i < sURLVariables.length; i++) {
    var sParameterName = sURLVariables[i].split("=");
    var name = sParameterName[0];
    var value = sParameterName[1];
    if (value.indexOf('%20')) {
      value = value.replace(/%20/g,' ')
    }
    if (name.indexOf("[]") > 0 || name.indexOf("%5B%5D") > 0) {
      var betterName = name.replace(/\[\]|\%5B|\%5D/g, "");
      if (params[betterName] == undefined) {
        params[betterName] = [];
      }
      params[betterName].push(value);
    } else {
      params[name] = value;
    }
    sParameterName[0].indexOf("");
  }
  filter_params = params
}

function searchMenuSection($wrapper, event) {
  var $searchInput = $(event.currentTarget);
  var searchQuery = $searchInput.val();
  var hitEnter = event.which == 13 || event.keyCode == 13
  var validQuery = searchQuery.length == 0 || searchQuery.length >= 3
  if (hitEnter && validQuery) {
    event.preventDefault();

    if (!searchQuery) {
      $wrapper.find(".menu-section-item-link:visible").first().trigger("click");
    } else {
      // remove selected menu section
      $(document).find(".menu-section-item-link").removeClass("menu-section-link-selected")
      var $menuSectionWrapper = $wrapper.find(".menu-section-wrapper");
      var $spinner = $wrapper.find(".menu-section-items-loading-spinner")
      $menuSectionWrapper.html("")
      $spinner.removeClass("hidden")

      var menuSectionRequest = $.ajax({
        type: "GET",
        dataType: "HTML",
        url: $searchInput.data("url"),
        data: { query: searchQuery },
      })

      menuSectionRequest.done(function (response) {
        $menuSectionWrapper.html(response)
        $menuSectionWrapper.foundation()
      })

      menuSectionRequest.fail(function () {
        $menuSectionWrapper.html("<h3>Oops! we are experiencing some trouble getting the menu.</h3>")
      })

      menuSectionRequest.always(function () {
        $spinner.addClass("hidden")
      })
    }
  }
}
