var BASE_LOCATION_URL = "/api/locations";

$(document).ready(function() {
  var $wrapper = $(".docket-wrapper");
  bindLocationElements($wrapper)
  toggleFirstLocation($wrapper)
})

function bindLocationElements ($wrapper) {
  $wrapper.on("keyup", ".new-location-details", function(e) { addNewLocationOnEnter($wrapper, e) });
  $wrapper.on("click", ".new-location-btn", function(e) { submitNewLocation($wrapper, e) });
  $wrapper.on("click", ".new-location-cancel-btn", function(e) { cancelNewLocation($wrapper, e) });
  $wrapper.on("click", ".edit-location", function(e) { editLocationForm($wrapper, e) });
  $wrapper.on("keyup", ".edit-location-details", function(e) { updateLocationOnEnter($wrapper, e) });
  $wrapper.on("click", ".edit-location-btn", function (e) { submitEditLocation($wrapper, e) });
  $wrapper.on("click", ".edit-location-cancel-btn", function (e) { cancelEditLocation($wrapper, e) });
  $wrapper.on("click", ".delete-docket-level", function (e) { startLocationRemoval($wrapper, e) });
}

function toggleFirstLocation ($wrapper) {
  var $locations = $wrapper.find(".location-level .accordion-title")
  if ($locations.length) {
    $locations.first().click()
  }
}

function addNewLocationOnEnter ($wrapper, event) {
  var $locationDetails = $(event.currentTarget);
  var $location = $locationDetails.parents(".new-location-form");
  if (event.which == 13 || event.keyCode == 13) {
    addNewLocation($wrapper, $location);
  }
}

function submitNewLocation ($wrapper, event) {
  var $buttonEl = $(event.currentTarget);
  var $location = $buttonEl.parents(".new-location-form");
  addNewLocation($wrapper, $location);
}

function addNewLocation ($wrapper, $location) {
  var $locationDetails = $location.find(".new-location-details")
  var newLocation = $locationDetails.val();
  if (newLocation === "") {
    var display_msg = "Location name cannot be empty!";
    $(this).yordarPOP({
      title: "New Location Name Error",
      innerContent: display_msg,
      cancel: false
    });
  } else {
    data = {
      order_id: $wrapper.data("order-id"),
      location: { details: newLocation },
      wants_html: true
    };

    var newLocationRequest = $.ajax({
        type: "POST",
        dataType: "JSON",
        url: BASE_LOCATION_URL,
        data: data
      })

    newLocationRequest.done(function (response) {
      updateDocketLocation(response, $wrapper);
    });

    newLocationRequest.always(function (){
      $locationDetails.val("");
    })

    newLocationRequest.fail(function () {
      alert("Could not add a new level/service point");
    })
  }
}

function updateDocketLocation(response, $docket) {
  if (!$docket.data("order-id")) {
    $docket.data("order-id", response.order_id);
  }
  var $existingLocation = $docket.find(".location-level[data-location-id=" + response.id + "]");
  var $location = null;
  if ($existingLocation.length) {
    $location = $existingLocation;
  } else {
    var $locationBreak = $docket.find(".location-break");
    $location = $(response.location_html);
    $locationBreak.before($location);
    Foundation.reInit($docket.find('.cart-accordion'));
  }
  $location.find(".accordion-title").click();
}

function cancelNewLocation($wrapper, event) {
  toggleFirstLocation($wrapper)
}

function editLocationForm ($wrapper, event) {
  var locationId = $(event.currentTarget).attr("id");
  var $form = $wrapper.find(".edit-location-form#" + locationId);
  $form.slideToggle();
  $form.find('.edit-location-details').select();
}

function updateLocationOnEnter ($wrapper, event) {
  var $locationDetails = $(event.currentTarget)
  var $location = $locationDetails.parents(".location-level");
  if (event.which == 13 || event.keyCode == 13) {
    updateLocation($wrapper, $location)
  }
}

function submitEditLocation ($wrapper, event) {
  var $buttonEl = $(event.currentTarget);
  var $location = $buttonEl.parents(".location-level");
  updateLocation($wrapper, $location);
}

function updateLocation ($wrapper, $location) {
  var $locationDetails = $location.find(".edit-location-details")
  var newLocation = $locationDetails.val();
  if (newLocation === "") {
    var display_msg = "Location name cannot be empty!";
    $(this).yordarPOP({
      title: "New Location Name Error",
      innerContent: display_msg,
      cancel: false
    });
  } else {
    var orderId = $wrapper.data("order-id")
    var locationId = $location.data("location-id");
    data = {
      order_id: orderId,
      location: { id: locationId, details: newLocation },
      wants_html: false
    };

    var updateLocationRequest = $.ajax({
        type: "PUT",
        dataType: "JSON",
        url: BASE_LOCATION_URL + "/" + locationId,
        data: data
      })

    updateLocationRequest.done(function (response) {
      $location.find(".order-docket-location-title").text(response.details);
      $location.find(".edit-location-cancel-btn").click(); // to hide form and show the order lines if any
    });

    updateLocationRequest.fail(function () {
      alert("Could not update a the level/service point");
    })
  }
}

function cancelEditLocation($wrapper, event) {
  var $buttonEl = $(event.currentTarget);
  $buttonEl.parents(".edit-location-form").slideToggle();
  $buttonEl.parents(".accordion-item").find(".accordion-title").click();
}

function startLocationRemoval($wrapper, event) {
  var $location = $(event.currentTarget).parents(".location-level");
  $(this).yordarPOP({
    title: "Delete this location?",
    innerContent: "All the items under this location will be deleted as well.",
    submit: "Delete",
    submitHandler: function() {
      removeLocation($wrapper, $location);
    }
  });
}

function removeLocation ($wrapper, $location) {
  var locationId = $location.data("location-id");
  if ($wrapper.find('.location-level[data-location-id=' + locationId + ']').length < 1) {
    // check to see if the location data still exists in docket
    // check to geta away from YordarPop calling the function again on multiple location removal
    return
  } else {
    var locationRemovalRequest = $.ajax({
        type: "DELETE",
        dataType: "JSON",
        url: BASE_LOCATION_URL + "/" + locationId,
        data: { order_id: $wrapper.data("order-id") },
      })

    locationRemovalRequest.done(function (response) {
      $location.remove();
      updateTotals(response);
      updateHeader(response);
    });

    locationRemovalRequest.fail(function () {
      alert("Could not remove the level/service point");
    })
  }
}
