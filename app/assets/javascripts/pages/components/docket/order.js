$(document).ready(function() {
  var $wrapper = $(".docket-wrapper");
  bindOrderElements($wrapper)
})
var BASE_ORDER_URL = "/api/orders";

function bindOrderElements ($wrapper) {
  $wrapper.on("change", "#remove-delivery-fee-switch", function(e) { changeDeliveryConfig(e) });
  $wrapper.on('submit', '.recurring-form-wrapper form', function(e) { convertToRecurrent(e, $wrapper) });
}

function convertToRecurrent(event, $wrapper) {
  event.preventDefault();
  var $form = $(event.currentTarget);
  var $submitButton = $form.find('input[type=submit]')
  var request = $.ajax({
    url: $form.attr('action'),
    type: 'POST',
    dataType: 'JSON',
    data: $form.serialize(),
  });

  request.done(function(response) {
    $submitButton.html(spinner_html);
    window.location = window.location; // refresh page
  })

  request.fail(function(response) {
    if (response.responseJSON) {
      var errors = response.responseJSON.errors
      $(this).yordarPOP({
        container: "#yordarPopUp",
        title: "Recurrent Orders Error!",
        innerContent: errors.join('<br>'),
        cancel: false
      });
    }
    $submitButton.attr('disabled', false);
  });
}


function changeDeliveryConfig (event) {
  var $switch = $(event.currentTarget);
  var orderId = $switch.parents(".docket-wrapper").data("order-id");
  var data = {}
  data.order = { no_delivery_charge: $switch.is(":checked") };

  var deliveryChangeRequest = $.ajax({
    url: BASE_ORDER_URL + "/" + orderId,
    dataType: "JSON",
    type: "PUT",
    data: data,
  });

  deliveryChangeRequest.done(function(response) {
    updateTotals(response); // as setup in components/menu-items.js
  });

  deliveryChangeRequest.fail(function(response) {
    alert("Sorry we couldn't update the order")
  });
}
