$(document).ready(function () {
  var $wrapper = $(document);
  bindElements($wrapper)
})
var BASE_ORDER_LINE_URL = "/api/order_lines";

function bindElements($wrapper) {
  $wrapper.on("focus", ".docket-qty-menu-item input", function (event) { selectQuantity(event) }); // defined in menu-item.js
  $wrapper.on("change", ".docket-qty-menu-item input", function (e) { orderLineQuantityChanged(e) });
  $wrapper.on("click", ".cart-list-item .delete-icon", function (e) { removeOrderLine(e) });
  $wrapper.on("click", "#docket-check-out-button", function (e) { checkoutOrder(e) });
  $wrapper.on("click", "#team-order-docket-check-out-button", function (e) { checkoutTeamOrder($wrapper, e) });
}

function orderLineQuantityChanged(e) {
  var $quantityEl = $(e.currentTarget)
  var quantity = $quantityEl.val()
  var $orderLine = $quantityEl.parents(".cart-list-item");
  $orderLine.addClass("processing");
  $quantityEl.attr("disabled", true);

  if (quantity == 0 || quantity == "") {
    deleteOrderLine($orderLine, $quantityEl);
  } else {
    updateOrderLine($orderLine, $quantityEl);
  }
}

function updateOrderLine($orderLine, $quantityEl) {
  var $orderDocket = $orderLine.parents(".docket-wrapper")
  var saveData = {};
  saveData.location_id = $orderLine.parents(".location-level").data("location-id");
  saveData.order_id = $orderLine.parents(".docket-wrapper:visible").data("order-id");
  saveData.id = $orderLine.data("order-line-id");
  saveData.quantity = $quantityEl.val();
  saveData.want_htmls = true

  var save_request = $.ajax({
    url: BASE_ORDER_LINE_URL + "/" + saveData.id,
    type: "PUT",
    data: saveData,
    dataType: "JSON",
  })

  save_request.fail(function (err) {
    alert("Sorry we couldn't update the item in cart")
    $quantityEl.val($quantityEl.data("prev-value"))
  })

  save_request.then(function (response) {
    $orderLine.replaceWith(response.order_line.html);
    updateTotals(response);
    updateHeader(response);
  })

  save_request.always(function () {
    $orderLine.removeClass("processing");
    $quantityEl.attr("disabled", false);
  })
}

function removeOrderLine (event) {
  var $orderLine = $(event.currentTarget).parents(".cart-list-item");
  deleteOrderLine($orderLine);
}

function deleteOrderLine($orderLine, $quantityEl) {
  var saveData = {};
  saveData.location_id = $orderLine.parents(".location-level").data("location-id");
  saveData.order_id = $(".docket-wrapper:visible").data("order-id");
  saveData.id = $orderLine.data("order-line-id");
  $orderLine.addClass("processing");
  if ($quantityEl) {
    $quantityEl.attr("disabled", true);
  }

  var save_request = $.ajax({
    url: BASE_ORDER_LINE_URL + "/" + saveData.id,
    type: "DELETE",
    data: saveData,
    dataType: "JSON",
  });

  save_request.fail(function (response) {
    if (response.responseJSON && response.responseJSON.errors) {
      var errors = response.responseJSON.errors
      $orderLine.yordarPOP({
        container: "#yordarPopUp",
        title: "Removal Error",
        innerContent: errors.join('<br>'),
        cancel: false,
        submit: 'Ok',
      });
    } else {
      alert("Sorry we couldn't remove the item from cart")
    }
    $orderLine.removeClass("processing");
    if ($quantityEl) {
      $quantityEl.val($quantityEl.data("prev-value"));
      $quantityEl.attr("disabled", false);
    }
  });

  save_request.then(function (response) {
    var isLastOrderLine = $orderLine.siblings().length == 0
    var $supplier = $orderLine.parents(".supplier-level")
    $orderLine.slideUp(150, function() {
      $orderLine.remove();
      if (isLastOrderLine) {
        $supplier.remove();
      }
    });
    updateTotals(response);
    updateHeader(response);
  });
}

function cartIsEmpty () {
  var isEmpty = $(".cart-list-item").length < 1;
  if (isEmpty) {
    var display_msg = "Your order is currently empty!";
    $('body').yordarPOP({
      title: "Checkout Error",
      innerContent: display_msg,
      cancel: false
    });
  }
  return isEmpty;
}

function checkoutOrder (event) {
  if (cartIsEmpty()) {
    return;
  }
  var $checkoutButton = $(event.currentTarget);
  $checkoutButton.html(spinner_html)
  window.location = $(".docket-checkout-btn").data("url");
}

function isWithinBudget ($wrapper, $checkoutButton) {
  var budget = $checkoutButton.data('budget')
  var total = $wrapper.find('.docket-total').data('total')
  var withinBudget = !budget || parseFloat(total) <= parseFloat(budget);

  if (!withinBudget) {
    var display_msg = "You have exceeded your budget";
    if (!$checkoutButton.data('hide-budget')) {
      display_msg += " of $" + budget;
    }
    $checkoutButton.yordarPOP({
      title: "Team order error",
      innerContent: display_msg,
      cancel: false
    });
  }
  return withinBudget;
}

function selectedDeliveryLevel ($wrapper, $levelSelect) {
  var selectedLevel;
  
  if ($levelSelect.val()) {
    selectedLevel = $levelSelect.val();
  } else {
    $levelSelect.yordarPOP({
      title: "Team order error",
      innerContent: "You need to select a delivery level",
      cancel: false
    });
  }
  return selectedLevel;
}

function checkoutTeamOrder ($wrapper, event) {
  var $checkoutButton = $(event.currentTarget);
  var checkoutData = {};

  if (cartIsEmpty()) {
    return;
  }

  if (!isWithinBudget($wrapper, $checkoutButton)) {
    return;
  }

  var $levelSelect = $wrapper.find('select[name="team_order_level_id"]');  
  if ($levelSelect.length > 0) {
    var selectedLevel = selectedDeliveryLevel($wrapper, $levelSelect)
    if (!!selectedLevel) {
      checkoutData[$levelSelect.attr('name')] = selectedLevel;
    } else {
      return;
    }
  }
  
  var buttonText = $checkoutButton.text();
  $checkoutButton.html(spinner_html)
  var checkout_request = $.ajax({
    url: $checkoutButton.data('url'),
    dataType: 'JSON',
    type: 'POST',
    data: checkoutData
  });
  checkout_request.done(function(response) {
    var display_msg = "Your order has been confirmed"
    $checkoutButton.yordarPOP({
      title: "Team order confirmed",
      innerContent: display_msg,
      cancel: false,
      submitHandler: function(event) {
        window.location = response.redirect_url
      }
    });
  });
  checkout_request.fail(function(response) {
    var jsonResponse = response.responseJSON
    if (jsonResponse) {
      var isRedirected = jsonResponse.redirect_url != null;
      var display_msg = jsonResponse.errors.join('<br>');
      if (isRedirected) {
        display_msg += '<br>You will be redirected if you click OK';
      }
      $checkoutButton.yordarPOP({
        title: "Team order error",
        innerContent: display_msg,
        cancel: isRedirected ? 'Close' : false,
        submitHandler: function(event) {
          if (isRedirected) {
            window.location = jsonResponse.redirect_url
          }
        }
      });
    } else {
      $checkoutButton.yordarPOP({
        title: "Team order error",
        innerContent: "We had some issues confirming your order. Please try again!",
        cancel: false
      });
    }
  });
  checkout_request.always(function() {
    $checkoutButton.html(buttonText)
  });
}
