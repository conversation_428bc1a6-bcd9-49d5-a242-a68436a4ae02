$(document).ready(function() {
  var $wrapper = $(document);
  bindRecurringOrderElements($wrapper)
})

function bindRecurringOrderElements ($wrapper) {
  $wrapper.on("change", "#recurring-switch", function (e) { showRecurringForm($wrapper, e) });
  $wrapper.on("change", ".frequency-tabs", function (e) { changeFrequencyLabel($wrapper, e) });
  $wrapper.on("click", ".recurrent-docket__list-item", function (e) { switchDay(e) });
}

function showRecurringForm ($wrapper, event) {
  var $checkbox = $(event.currentTarget)
  var $settings = $wrapper.find(".recurring-settings");
  if ($checkbox.is(":checked")) {
    $settings.removeClass("hidden")
  } else {
    $settings.addClass("hidden")
  }
}

function changeFrequencyLabel($wrapper, event) {
  var selectedOption = $(event.currentTarget).val();
  var $formWrapper = $wrapper.find(".recurring-settings");
  var frequencyLabel = {
    "weekly": "Select Days",
    "fortnightly": "Every fortnight on",
    "monthly": "Every month on",
  }[selectedOption];
  $formWrapper.find(".frequency-label").text(frequencyLabel);
}

function switchDay (event) {
  window.location = $(event.currentTarget).data("next-url");
}
