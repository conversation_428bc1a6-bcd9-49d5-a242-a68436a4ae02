$(document).ready(function () {
  var $wrapper = $(document)
  var singleLevelNav = $(".menu-section-wrapper").data("major-supplier") === undefined
  if (singleLevelNav) {
    bindSingleLevelElements($wrapper)
  }
});

function bindSingleLevelElements($wrapper) {
  var $menuWrapper = $(".menu-section-wrapper")
  $wrapper.on("keyup", ".menu-items-search__search-input", function (e) { quickSearchMenuItems($menuWrapper, e) });
  $wrapper.on("click", ".menu-section-link", function (e) { scrollToSection(e) });
  $(window).on("scroll", function (e) { activeScrollMenuSection($wrapper) });
}

function scrollToSection(event) {
  event.preventDefault();
  var targetMenuSection = $(event.currentTarget).attr("href");
  var scrollTop = $(targetMenuSection).offset().top - 80;
  $("html, body").animate({ scrollTop: scrollTop }, 800);
}

function activeScrollMenuSection($wrapper) {
  var $moreButton = $wrapper.find(".more-button-link");
  var windowScrollTop = $(window).scrollTop() + 185;
  var $menuLinks = $wrapper.find(".menu-section-link");
  var $sectionHeaders = $wrapper.find(".section-heading");
  $menuLinks.removeClass("menu-section-link-selected");
  $moreButton.removeClass("menu-section-link-selected");
  for (var ms = 0; ms < $sectionHeaders.length; ms++) { // loop through menu sections
    var $sectionHeader = $($sectionHeaders[ms]);
    var sectionScrollTop = $sectionHeader.offset().top;
    var nextSectionScollTop = 9999;
    if (ms + 1 < $sectionHeaders.length) {
      var $nextSectionHeader = $($sectionHeaders[ms + 1]);
      nextSectionScollTop = $nextSectionHeader.offset().top;
    }
    if (windowScrollTop > sectionScrollTop && windowScrollTop <= nextSectionScollTop) {
      var sectionTarget = "#" + $sectionHeader.attr("id");
      for (var ml = 0; ml < $menuLinks.length; ml++) { // loop throug menu link to get a match
        var $sectionLink = $($menuLinks[ml]);
        if ($sectionLink.attr("href") == sectionTarget) {
          $sectionLink.addClass("menu-section-link-selected");
          if ($sectionLink.parents(".more-button-wrapper").length > 0) {
            $moreButton.find("span").text($sectionLink.text());
            $moreButton.addClass("menu-section-link-selected");
          } else {
            $moreButton.find("span").text("More");
          }
        }
      }
    }
  }
}

function quickSearchMenuItems($wrapper, event) {
  var $searchInput = $(event.currentTarget);
  var searchQuery = query_sanitized($searchInput.val());
  var $menuSections = $wrapper.find(".menu-section");
  var $menuItems = $wrapper.find(".menu-item");

  if (searchQuery) {
    for (var ms = 0; ms <= $menuSections.length; ms++) {
      var $menuSection = $($menuSections[ms]);
      var sectionHeading = $menuSection.find(".section-heading").text()
      var matchedSection = query_sanitized(sectionHeading).indexOf(searchQuery) > -1

      var $sectionMenuItems = $menuSection.find(".menu-item");
      var matchedItems = 0;

      for (var mi = 0; mi <= $sectionMenuItems.length; mi++) {
        var $menuItem = $($sectionMenuItems[mi]);
        var itemHeading = $menuItem.find(".item-title").text()
        var matchedItem = query_sanitized(itemHeading).indexOf(searchQuery) > -1
        if (matchedSection || matchedItem) {
          $menuItem.removeClass("hidden");
          matchedItems += 1
        } else {
          $menuItem.addClass("hidden");
        }
      }
      // hide section if no matched items or section
      if (matchedItems == 0) {
        $menuSection.addClass("hidden")
      } else {
        $menuSection.removeClass("hidden")
      }
    }
  } else {
    $menuSections.removeClass("hidden")
    $menuItems.removeClass("hidden")
  }
}


function query_sanitized(str) {
  return $.trim(str).replace(/ +/g, " ").toLowerCase();
}
