/* -- this is a environment variable share file --*/

const query = document.querySelector.bind(document);
const queryAll = document.querySelectorAll.bind(document);
const spinner_html = '<span class="sk-three-bounce"><span class="sk-child sk-bounce1"></span><span class="sk-child sk-bounce2"></span><span class="sk-child sk-bounce3"></span></span</span>';


var delay = (function(){
	var timer = 0;
	return function(callback, ms){
		clearTimeout (timer);
		timer = setTimeout(callback, ms);
	};
})();

jQuery( function($) {
  $('#footer-nav .heading').on('click', function() {
    $(this).toggleClass('active');
  });

  if($.fn.cloudinary_fileupload !== undefined && $("input.cloudinary-fileupload[type=file]").length > 0) {
    $("input.cloudinary-fileupload[type=file]").cloudinary_fileupload();
  }
});
