document.onload = hamburger();
function hamburger() {
  let body = query("body");
  let nav_hamburger = query("#menu-toggle");
  let dash_hamburger = query("#dash-hamburger");
  let mainNav = query("#main-navigation");
  let sidebar = query(".customer-sticky-sidebar");
  if (nav_hamburger) {
    nav_hamburger.addEventListener("click", function () {
      $(nav_hamburger).toggleClass('open');
      $(mainNav).toggleClass('active');
      $(body).toggleClass('lock');
    });
  }
  else if (dash_hamburger) {
    window.addEventListener('resize', function (e) {
      if (window.innerWidth > 900) {
        $(sidebar).show();
      }
    })
    dash_hamburger.addEventListener("click", function () {
      $(dash_hamburger).toggleClass('open');
      $(sidebar).toggle();
      $(body).toggleClass('prevent-scroll')
    })
  }
}
