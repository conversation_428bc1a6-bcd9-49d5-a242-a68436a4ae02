//   EXIST FUNCTION
jQuery.fn.exists = function () {
  return $(this).length;
}

$(document).ready(function () {
  var $wrapper = $(document)
  $wrapper.foundation();
  bindHeaderEvents($wrapper);
})

function bindHeaderEvents ($wrapper) {
  $wrapper.on('click', '#customer-admin-notes', function (e) { toggleCustomerAdminNotes(e, $wrapper) });

  $wrapper.on('click', '.cart-checkout-btn', function (e) { headerCartCheckout(e) });
  $wrapper.on('click', '.shopcart-clear', function (e) { clearCart(e) });
}

// For admins signed in as customers
function toggleCustomerAdminNotes (event, $wrapper) {
  var $link = (event.currentTarget);
  var $notes = $wrapper.find('#customer-notes-text');
  $notes.toggleClass('hidden');
  if ($notes.hasClass('hidden')) {
    link_text = "Click here to view customer notes";
  } else {
    link_text = "Click here to hide customer notes";
  }
  $link.text(link_text);
}

function headerCartCheckout (event) {
  event.preventDefault();
  var $button = $(event.currentTarget);
  //when team order attendee clicks cart, toggle docket if mobile, otherwise ignore click
  var teamCheckoutButton = query("#team-checkout-btn");
  if (teamCheckoutButton) {
    if ($(window).width() < 550) {
      $(".docket").toggleClass("show");
      $(".docket").toggleClass("docket-block");
      $("body").toggleClass("lock");
      $(".close-docket").removeClass("hidden");
    }
    return;
  }
  if (ON_MENU === "true" && $('.cart-list-item').length > 0) {
    window.location = $button.attr('href');
  } else if (ON_MENU === "false" && ALLOW_CHECKOUT === "true") {
    window.location = $button.attr('href');
  } else {
    var display_msg = "Your order is currently empty!";
    $(this).yordarPOP({
      title: "Checkout Error",
      innerContent: display_msg,
      cancel: false
    });
  }
}

function clearCart (event) {
  event.preventDefault();
  var $link = $(event.currentTarget);

  var display_msg = '';
  var cancelButton = 'No';
  var submitButton = 'Yes';

  if ($link.data('is-order-edit')) {
    display_msg = 'You are editing an order, any changes to the cart/order are already made live.'
    cancelButton = 'Cancel';
    submitButton = 'Ok';
  }
  $link.yordarPOP({
    title: "Are you sure you want to clear the cart?",
    innerContent: display_msg,
    cancel: cancelButton,
    submit: submitButton,
    submitHandler: function (event) {
      window.location = $link.attr('href');
    }
  });
}

