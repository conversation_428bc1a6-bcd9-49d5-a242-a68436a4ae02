//   EXIST FUNCTION
jQuery.fn.exists = function () {
  return $(this).length;
}

$(document).ready(function () {
  var $wrapper = $(document);
  $wrapper.foundation();
  bindCustomElementEvents($wrapper);
  bindResizeEvents($wrapper);
});

function bindCustomElementEvents ($wrapper) {
  $wrapper.on('click', '.day-switch .button', function (e) { switchDay(e) });

  $wrapper.on('click', 'a[href$=supplier-search-office-catering]', function(e) { openSuburModal(e, $wrapper, 'office-catering') });
  $wrapper.on('click', 'a[href$=supplier-search-office-snacks]', function(e) { openSuburModal(e, $wrapper, 'office-snacks') });
}

function bindResizeEvents($wrapper){
  if ($('.delivery-schedule-table').exists()) {
    if ($(window).width() < 900) {
      $('.delivery-schedule-table').addClass('stack');
    }
    $(window).resize(function () {
      if ($(window).width() < 900) {
        $('.delivery-schedule-table').addClass('stack');
      } else {
        $('.delivery-schedule-table').removeClass('stack');
      }
    });
  }

  //make sure body is not locked if window is resized with docket open
  $(window).resize(function () {
    if ($(window).width() > 550) {
      $("body").removeClass("lock");
    }
  });
}

function openSuburModal (event, $wrapper, category_group) {
  event.preventDefault();
  var $modal = $wrapper.find('#enter-address-modal');
  var $locationInput = $modal.find('#location_search');
  $locationInput.data('category_group', category_group)
  $modal.foundation('open');
}

// Switch Day
function switchDay(event) {
  $(this).toggleClass('active');
}