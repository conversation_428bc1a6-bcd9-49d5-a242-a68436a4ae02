class EventAttendeesController < ApplicationController

  before_action :fetch_team_admin

  def index
    if !signed_in? || !session_profile.team_admin?
      redirect_to new_user_session_path and return
    end

    @event_attendees = session_profile.event_attendees.where(active: true).sort_by{|attendee| attendee.name.downcase }
    respond_to do |format|
      format.html { render layout: 'customer_profiles' }
      format.csv do
        filename = @event_attendees ? "contact_list_#{Time.zone.now.to_s(:filename)}.csv" : 'contact_list_template.csv'
        send_data (render_to_string 'event_attendees/index', layout: false), filename: filename
      end
    end
  end

  def create
    attendee_creator = EventAttendees::CreateAnonymousAttendee.new(team_admin: @team_admin, attendee_params: event_attendee_params).call
    if attendee_creator.success?
      @event_attendee = attendee_creator.event_attendee
      render 'event_attendees/success'
    else
      flash[:error] = attendee_creator.errors.compact.join('.')
      render :new
    end
  end

private

  def fetch_team_admin
    @team_admin = CustomerProfile.where(uuid: params[:team_admin_id]).first if params[:team_admin_id].present?
  end

  def event_attendee_params
    params.require(:event_attendee).permit(:first_name, :last_name, :email)
  end

end
