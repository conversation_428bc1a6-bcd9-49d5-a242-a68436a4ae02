class OrdersController < ApplicationController

	before_action :redirect_to_next_app_checkout, only: %i[summary checkout]
	before_action :authenticate_user!, only: %i[show show_for_supplier edit]
	before_action :ensure_customer, only: %i[show edit]
	before_action :ensure_supplier, only: %i[show_for_supplier]
	before_action :load_orders, only: %i[edit]

	include CloudinaryHelper

	# order summary page - redirected to Next APP
	def summary
		switch_to_original_order if session_order.is_recurrent?
		validate_woolworths_order if session_order.is_woolworths_order?
	end

 	# order checkout page - redirected to Next APP
	def checkout
		@checkout_order = Orders::SetupForCheckout.new(order: session_order, profile: session_profile, cookies: cookies).call
	end

	def clear_cart
		order_from_session = session_order
		order = order_from_session&.dup
		clean_up_woolworths_order
		clean_up_session
		respond_to do |format|
			format.html do
				redirect_path = case
				when current_user.present? && order&.meal_plan.present?
					customer_meal_plans_path(mealUUID: order.meal_plan.uuid)
				when current_user.present?
					after_sign_in_path_for(current_user)
				else
					prismic_root_url
				end
				redirect_to redirect_path
			end
			format.json {render json: true}
		end
	end

	# based on the new order email's urls, update order and order_lines status
	def confirm_or_reject
		if verify_recaptcha
			# we need to make sure the passed over hash value matches the actual order+supplier+secret token md5 value for security reasons
			if Digest::MD5.hexdigest(params[:order_id] + params[:profile_id] + yordar_credentials(:random_salt)) == params[:hashed_value]
				order = Order.find(params[:order_id])
				action = %w[cs_reject rejected].include?(params[:mode]) ? 'reject' : 'confirm'

				# Do not allow to modify cancelled order
				if order.status != 'cancelled'
					# Store action and order in session and use them later on on profile page
					session[:confirm_or_reject] = action
					session[:confirm_or_reject_order_id] = order.id
				else
					flash[:warning] = "This order can no longer be #{action}ed as it has been cancelled."
				end

				# Make sure we go back to the right profile page
				if params[:profile_type] != 'ffc'
					user = SupplierProfile.find(params[:profile_id]).user
					after_sign_in_path = '/s_profile'
				end

				# Sign in the user
				profile_id = Profile.where(user_id: user.id).first.id
				session_profile(profile_id)
				sign_in user

				redirect_to after_sign_in_path

			else
				flash[:error] = 'Invalid Request.'
				redirect_to '/'
			end
		else
			render layout: 'team_order'
		end
	end

	# we need to make sure the passed over hash value matches the actual order.id+customer.id+secret token md5 value for security reasons
	def approve_or_reject
		if verify_recaptcha
			quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: approve_or_reject_params).call

			if quote_order_handler.success?
				order = quote_order_handler.order
				user = order.customer_profile.user

				if quote_order_handler.warnings.present?
					flash[:warning] = quote_order_handler.warnings.join('. ')
				end

				profile_id = Profile.where(user_id: user.id).first.id
				session_profile(profile_id)
				sign_in user

				redirect_path = case quote_order_handler.redirect_to
				when :edit_page
					next_app_order_edit_url(order)
				when :approve_page
					next_app_order_edit_url(order, finaliseQuote: true)
				else # show_page
					order_show_path(order)
				end
				redirect_to redirect_path
			else
				flash[:errors] = quote_order_handler.errors.join('.')
				render 'orders/missing_order' and return
			end
		else
			render layout: 'team_order'
		end
	end

	def show
		@order = session_profile.present? && session_profile.orders.where(id: params[:id]).first
		if @order.present?
			respond_to do |format|
				format.html { render layout: 'customer_profiles' }
				format.json
			end
		else
			respond_to do |format|
			  format.html { render 'orders/missing_order' }
			  format.json { render json: { not_found: true }, status: 404 }
			end
		end
	end

	def show_for_supplier
		@supplier = session_profile
		@order = @supplier.present? && Order.joins(:supplier_profiles).where(supplier_profiles: { id: @supplier.id }).where(orders: { id: params[:id] }).first
		if @order.present?
			respond_to do |format|
				format.html { render layout: 'supplier_profiles' }
				format.json
			end
		else
			respond_to do |format|
			  format.html { render 'orders/missing_order' }
			  format.json { render json: { not_found: true }, status: 404 }
			end
		end
	end

	def edit
		respond_to do |format|
			format.html do
				redirect_to next_app_order_edit_url(params[:id]) and return
			end
			format.json { render json: { not_found: true }, status: 404 }
		end
	end

	def add_more_products
		order = session_profile.present? && session_profile.orders.where(id: params[:id]).first
		supplier = SupplierProfile.where(id: params[:supplier_id]).first
		supplier ||= order&.order_lines&.order(:updated_at)&.first&.supplier_profile
		delivery_suburb = order.delivery_suburb
		if order.present? && supplier.present?
			session_order(order.id)
			save_suburb_cookie(delivery_suburb)
   		redirect_to next_app_supplier_show_url(supplier.slug, addMore: true)
   	elsif order.present?
   		save_suburb_cookie(delivery_suburb)
   		redirect_to next_app_supplier_search_url(category_group: 'office-catering', state: delivery_suburb&.state, suburb: delivery_suburb&.name&.gsub(/\s/, '-'))
		else
			respond_to do |format|
			  format.html { render 'orders/missing_order' }
			  format.json { render json: { not_found: true }, status: 404 }
			end
		end
	end

	# deprecated to only used in Rails App
	# TODO - remove after setting up NextApp + Rails App development sync
	# Switches the current or active recurrent orders to manage
	def switch
		# setup new active order
		order = Order.where(id: params[:id]).first
		session_order(order.id)

		redirect_to request.referer # redirect to where u came from
	end

	# Used for rejecting a Category Solutions order. This is different from the confirm_or_reject route as we ask
	# for additional information when rejecting which we do not for Yordar
	# Used for processing the rejection popup on the Supplier and FFC profile pages and also for processing the
	# rejection form on the new reject order page (accessible through email links)
	# deprecated
	def reject
		if params[:profile_id].present?
			if params[:profile_type] == 'ffc'
				order = Order.find(params[:order_id])
				is_ffc = true
			else
				supplier = SupplierProfile.find(params[:profile_id])
				order = Order.joins(:supplier_profiles).where(supplier_profiles: { id: supplier.id }).where(id: params[:order_id]).first
			end

			status = 'rejected'
			if order.status != 'cancelled'
				is_ffc ? order.update_ffc_order_lines_status!(status) : order.set_supplier_order_lines_status!(supplier.id, status)
				if params[:rejected_reason].present? && params[:estimated_delivery].present?
					# Find the order again to prevent readonly record errors
					Order.find(order.id).update(rejected_reason: params[:rejected_reason], estimated_delivery: params[:estimated_delivery])
				end
				flash[:success] = "You just #{status} order ##{params[:order_id]}."
			else
				flash[:warning] = "This order can no longer be #{status} as it has been cancelled."
			end
		else
			flash[:error] = 'Invalid Request.'
		end
		redirect_back(fallback_location: prismic_root_url)
	end

private

	def approve_or_reject_params
		params.permit(:order_id, :profile_type, :profile_id, :hashed_value, :mode)
	end

	def switch_to_original_order
		order = session_order
		session_order(order.recurrent_id) if order.id != order.recurrent_id
	end

	def load_orders
		if session_order_ids.present?
			@orders = Order.where(id: session_order_ids)
		elsif session_order.present?
			@orders = [session_order]
		end
	end

	def validate_woolworths_order
		begin
			order_validator = Woolworths::Order::Validate.new(order: session_order).call
			if order_validator.success?
				@woolworths_delivery_window = order_validator.delivery_window_text
			else
				@woolworths_errors = order_validator.errors
			end
		rescue Woolworths::API::Connection::ConnectionError
			@woolworths_errors = ['We encountered an error when communicating with Woolworths, please try again.']
		end
	end

	def clean_up_woolworths_order
		return if session_order.blank? || session_order.woolworths_order.blank?

		session_order.woolworths_order.update(account_in_use: false)
	end

	def clean_up_session
		session.delete('edit_params') if session[:edit_params].present?
		clean_up_session_orders
	end

	def redirect_to_next_app_checkout
		redirect_to order_checkout_url
	end

end
