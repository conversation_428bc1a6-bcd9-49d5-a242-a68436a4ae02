class ConfirmationsController < Devise::ConfirmationsController

  # YOR 590 Override the show method by adding sign in step after confirmation
  def show
    self.resource = resource_class.confirm_by_token(params[:confirmation_token])
    yield resource if block_given?

    case
    when resource.errors.blank?
      set_flash_message(:notice, :confirmed) if is_flashing_format?
      # -- YOR 590 Added the following lines to allow sign in after confirmation
      sign_in(resource)
      # save profile_id instead of the profile object as a whole, See create method from RegistrationsController
      session[:profile_id] = User.find(resource.id).profile.id
      # -- END YOR 590
      respond_with_navigational(resource){ redirect_to after_confirmation_path_for(resource) }
    when resource.errors.full_messages.include?('<PERSON><PERSON> was already confirmed, please try signing in')
      flash[:error] = resource.errors.full_messages.join('. ') if is_flashing_format?
      respond_with_navigational(resource){ redirect_to new_user_session_path }
    else
      respond_with_navigational(resource.errors, status: :unprocessable_entity){ render :new }
    end
  end

private

  def after_confirmation_path_for(resource)
    return prismic_root_url if resource.blank? || resource.profile.blank?

    if resource.profile.is_customer?
      customer_profile_path
    else # supplier
      supplier_profile_path
    end
  end

end
