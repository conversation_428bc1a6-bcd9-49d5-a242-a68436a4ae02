class API::ReportsController < ApplicationController

  before_action :authenticate_user!

  def index
    if report_options[:detail_type].present? && report_options[:detail_type] == 'invoices'
      @report_retriever = Reports::RetrieveInvoiceData.new(options: report_options).call
    else
      @report_retriever = Reports::RetrieveData.new(options: report_options).call
    end
    respond_to do |format|
      format.json { render }
      format.csv do
        send_data (render_to_string csv_template(@report_retriever.options), layout: false), filename: report_filename
      end
    end
  end

private

  def report_options
    default_options = {
      with_order_data: (params[:format].present? && params[:format] == 'csv') || params[:react_data].present?,
    }
    [default_options, session_filter_options, filter_options.to_h.symbolize_keys].inject(&:merge)
  end

  def session_filter_options
    case
    when is_admin? && params[:is_admin_reports].present?
      {}
    when params[:is_admin_reports].present? && filter_options[:customer_id].blank?
      {
        source_type: 'CustomerProfile',
        admin_user: current_user
      }
    when session_profile.is_a?(CustomerProfile)
      {
        source_type: 'CustomerProfile',
        customer_id: session_profile.id
      }
    when session_profile.is_a?(SupplierProfile)
      {
        source_type: 'SupplierProfile',
        supplier_id: session_profile.id
      }
    else
      {

      }
    end
  end

  def filter_options
    report_fields = %i[start_date end_date report_type source_type detail_type react_data]
    filter_fields = %i[purchase_order_id category_group customer_id supplier_id company_wide with_supplier_costs exclude_staffing]
    params.permit(*report_fields, *filter_fields, source_types: [])
  end

  def report_filename
    name_data = []
    name_data << @report_retriever.options.report_type
    name_data << @report_retriever.options.detail_type
    name_data << 'company_wide' if @report_retriever.options.company_wide
    name_data << 'report'
    name_data << Time.zone.now.to_s(:filename)
    "#{name_data.join('_')}.csv"
  end

  def csv_template(report_options)
    source_type = report_options.source_type.sub('Profile', '').downcase
    "api/reports/#{source_type}/#{report_options.detail_type}"
  end

end
