class API::SuburbsController < ApplicationController

  def index
    lister_options = {
      limit: 10
    }.merge(list_params.to_h.deep_symbolize_keys)
    @suburbs = Suburbs::List.new(options: lister_options).call

    if params[:order_id]
      check_order_supplier_availability
      render 'api/orders/check_supplier_suburb_availability' and return
    end
  end

  private

  def list_params
    _params = params.permit(:postcode, :name, :term, :country_code, :order_id, best_matched_to: {})
    if params[:best_matched_to].present?
      _params[:best_matched_to] = params[:best_matched_to].is_a?(String) ? JSON.parse(params[:best_matched_to]) : params[:best_matched_to].permit!
    end
    _params
  end

  def check_order_supplier_availability
    order = Order.where(id: params[:order_id]).first
    suburb = @suburbs.first
    @availability_fetcher = Orders::FetchSupplierAvailability.new(order: order, suburb: suburb).call    
  end

end