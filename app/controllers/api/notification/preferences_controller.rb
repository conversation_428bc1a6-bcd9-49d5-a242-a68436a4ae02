class API::Notification::PreferencesController < ApplicationController

  before_action :fetch_preference, only: :update

  def create
    preference_creator = Notifications::UpsertPreference.new(account: session_profile, preference_params: preference_params).call
    if preference_creator.success?
      render partial: 'api/notifications/preference', locals: { preference: preference_creator.preference }
    else
      render json: { errors: preference_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    preference_updator = Notifications::UpsertPreference.new(account: session_profile, preference: @preference, preference_params: preference_params).call
    if preference_updator.success?
      render partial: 'api/notifications/preference', locals: { preference: preference_updator.preference }
    else
      render json: { errors: preference_updator.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_preference
    @preference = session_profile.present? ? session_profile.notification_preferences.where(id: params[:id]).first : Notification::Preference.where(id: params[:id]).first
  end

  def preference_params
    params.require(:notification_preference).permit(:active, :template_name, :email_recipients, :salutation, :variation)
  end

end
