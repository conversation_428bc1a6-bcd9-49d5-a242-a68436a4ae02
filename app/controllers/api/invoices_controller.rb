class API::InvoicesController < ApplicationController

  before_action :ensure_customer
  before_action :fetch_customer
  before_action :fetch_invoice, except: %i[index create]

  def index
    limit = 10
    page = params[:page] || 1
    invoices = session_profile.invoices
    invoices = invoices.order(created_at: :desc)
    invoices = invoices.distinct.page(page).per(limit)
    @invoices = invoices.includes(:orders)
    get_data_for(@invoices)
  end

  def create
    invoice_creator = Invoices::Upsert.new(invoice_params: invoice_params, customer: @customer).call
    if invoice_creator.success?
      render partial: 'api/invoices/invoice', locals: { invoice: invoice_creator.invoice }
      get_data_for([invoice_creator.invoice])
    else
      render json: { errors: invoice_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    invoice_updator = Invoices::Upsert.new(invoice: @invoice, customer: @customer, invoice_params: invoice_params).call
    if invoice_updator.success?
      render partial: 'api/invoices/invoice', locals: { invoice: invoice_updator.invoice }
      get_data_for([invoice_updator.invoice])
    else
      render json: { errors: invoice_updator.errors }, status: :unprocessable_entity
    end
  end

  def attach_orders
    orders = Order.where(id: params[:order_ids])
    order_attacher = Invoices::AttachOrders.new(invoice: @invoice, orders: orders).call
    if order_attacher.success?
      render partial: 'api/invoices/invoice', locals: { invoice: order_attacher.invoice }
      get_data_for([order_attacher.invoice])
    else
      render json: { errors: order_attacher.errors }, status: :unprocessable_entity
    end
  end

  def invoice_orders
    invoice_orders = @invoice.invoice_orders.order(delivery_at: :asc, id: :asc)
    # fetch non-invoiced customer orders
    non_invoiced_start = (@invoice.from_at - 1.month).beginning_of_month
    non_invoiced_end = (@invoice.to_at + 1.month).end_of_month
    non_invoiced_orders = @customer.orders.where(status: %w[new amended confirmed delivered], invoice_id: nil)
    non_invoiced_orders = non_invoiced_orders.where(delivery_at: non_invoiced_start..non_invoiced_end)

    @orders = non_invoiced_orders + invoice_orders
  end

  def generate_documents
    document_generator = Invoices::GenerateDocuments.new(invoice: @invoice, notify_customer: params[:notify_customer], regenerate: @invoice.documents.present?).call
    if document_generator.success?
      @invoice.reload
      get_data_for([@invoice])
      render partial: 'api/invoices/invoice', locals: { invoice: @invoice, newly_generated_documents: document_generator.generated_documents }
    else
      render json: { errors: document_generator.errors }, status: :unprocessable_entity
    end
  end

private

  def invoice_params
    params.require(:invoice).permit(:number, :from_at, :to_at, :do_not_notify)
  end

  def fetch_customer
    @customer = session_profile
  end

  def fetch_invoice
    @invoice = @customer.present? ? @customer.invoices.where(id: params[:id] || params[:invoice_id]).first : nil
  end

  def get_data_for(invoices)
    @invoice_documents = Document.where(documentable_type: 'Invoice', documentable_id: invoices.map(&:id)).order(version: :desc)
    return if !is_invoice_admin?

    email_templates = [Customers::Emails::SendOrderInvoiceEmail::EMAIL_TEMPLATE, Customers::Emails::SendInvoiceReceiptEmail::EMAIL_TEMPLATE]
    ref_condition = invoices.map(&:id).join('|')
    @invoice_emails = Email.where(template_name: email_templates).where('ref SIMILAR TO ?', "%-(#{ref_condition})").where.not(sent_at: nil).order(sent_at: :desc)
  end

end