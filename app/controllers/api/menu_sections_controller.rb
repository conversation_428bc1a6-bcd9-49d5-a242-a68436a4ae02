class API::MenuSectionsController < ApplicationController

  before_action :authenticate_user!
  before_action :fetch_supplier, only: :show
  before_action :fetch_menu_section, only: %i[update destroy]

  # used by Woolworths dynamic menu section nav and menu item search box on Supplier Menu Page
  def show
    @suburb = Suburb.where(id: cookies[:yordar_suburb_id]).first
    if @supplier.present? && @supplier.is_major_supplier?
      @supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: @supplier, suburb: @suburb, menu_options: menu_params, profile: session_profile).call
    else
      @supplier_menu = Suppliers::FetchMenu.new(supplier: @supplier, suburb: @suburb, menu_options: menu_params, profile: session_profile).call
    end

    respond_to do |format|
      format.html do
        render layout: false
      end
      format.json
    end
  end

  def create
    section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params, forced: true).call
    respond_to do |format|
      format.json do
        if section_creator.success?
          render partial: 'api/menu_sections/menu_section', locals: { menu_section: section_creator.menu_section }
        else
          render json: { errors: section_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    section_updater = MenuSections::Upsert.new(menu_section: @menu_section, menu_section_params: menu_section_params).call
    respond_to do |format|
      format.json do
        if section_updater.success?
          render partial: 'api/menu_sections/menu_section', locals: { menu_section: section_updater.menu_section }
        else
          render json: { errors: section_updater.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def destroy
    forced_archive = is_admin? && params[:is_forced].present?
    section_archiver = MenuSections::Archive.new(menu_section: @menu_section, is_forced: forced_archive).call
    respond_to do |format|
      format.json do
        if section_archiver.success?
          render partial: 'api/menu_sections/menu_section', locals: { menu_section: section_archiver.menu_section }
        else
          render json: { errors: section_archiver.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def fetch_supplier
    @supplier = SupplierProfile.includes(:minimums, delivery_zones: :suburb).where(id: params[:supplier_profile_id]).first
  end

  def menu_params
    p = params.permit(:query)
    p[:is_admin] = is_admin?
    p[:menu_section] = @supplier.menu_sections.where(id: params[:id]).first
    p[:favourites_only] = params[:id].try(:to_i) == -1
    p[:recent_orders_only] = params[:id].try(:to_i) == -3
    p
  end

  def fetch_menu_section
    @menu_section = MenuSection.where(id: params[:id]).first
  end

  def menu_section_params
    params.require(:menu_section).permit(:name, :weight, :is_hidden, :supplier_profile_id, category_ids: [], company_ids: [])
  end

end

