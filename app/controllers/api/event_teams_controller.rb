class API::EventTeamsController < ApplicationController

  before_action :ensure_session_customer
  before_action :fetch_event_team, only: %i[update destroy]

  def create
    team_creator = EventTeams::Upsert.new(team_params: event_teams_params, team_admin: session_profile).call
    if team_creator.success?
      render partial: 'api/event_teams/event_team', locals: { event_team: team_creator.event_team }
    else
      render json: { errors: team_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    team_updator = EventTeams::Upsert.new(team_params: event_teams_params, team_admin: session_profile, event_team: @event_team).call
    if team_updator.success?
      render partial: 'api/event_teams/event_team', locals: { event_team: team_updator.event_team }
    else
      render json: { errors: team_updator.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    team_remover = EventTeams::Upsert.new(team_params: { active: false }, team_admin: session_profile, event_team: @event_team).call
    if team_remover.success?
      render partial: 'api/event_teams/event_team', locals: { event_team: team_remover.event_team }
    else
      render json: { errors: team_remover.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_event_team
    @event_team = session_profile.event_teams.where(id: params[:id]).first
  end

  def event_teams_params
    params.require(:event_team).permit(:name)
  end

  def ensure_session_customer
    head :forbidden unless session_profile.present? && session_profile.profile.is_customer?
  end

end
