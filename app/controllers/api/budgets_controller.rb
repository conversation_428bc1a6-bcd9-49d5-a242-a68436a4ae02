class API::BudgetsController < ApplicationController

  before_action :ensure_customer
  before_action :fetch_customer
  before_action :fetch_budget, only: %i[update destroy]

  def index
    @budgets = @customer.budgets.order(starts_on: :desc)
  end

  def create
    budget_creator = Customers::Budgets::Upsert.new(customer: @customer, budget_params: budget_params).call
    if budget_creator.success?
      render partial: 'api/budgets/budget', locals: { budget: budget_creator.budget }
    else
      render json: { errors: budget_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    budget_updator = Customers::Budgets::Upsert.new(customer: @customer, budget: @budget, budget_params: budget_params).call
    if budget_updator.success?
      render partial: 'api/budgets/budget', locals: { budget: budget_updator.budget }
    else
      render json: { errors: budget_updator.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    if @budget.present?
      if @budget.destroy
        head :ok
      else
        render json: { errors: @budget.errors.full_messages }, status: :unprocessable_entity
      end
    else
      render json: { errors: ['Could not find budget'] }, status: :unprocessable_entity
    end
  end

private

  def budget_params
    params.require(:budget).permit(:starts_on, :ends_on, :value, :frequency, :customer_purchase_order_id)
  end

  def fetch_customer
    @customer = session_profile
  end

  def fetch_budget
    @budget = @customer.present? && @customer.budgets.where(id: (params[:id] || params[:budget_id])).first
  end

end