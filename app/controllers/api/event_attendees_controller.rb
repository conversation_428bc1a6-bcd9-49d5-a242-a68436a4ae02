class API::EventAttendeesController < ApplicationController

  before_action :fetch_event_attendee, only: %i[team_update destroy]

  respond_to :json

  def create
    attendee_creator = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params, team_admin: session_profile).call
    if attendee_creator.success?
      render partial: 'api/event_attendees/event_attendee', locals: { event_attendee: attendee_creator.event_attendee }
    else
      render json: { errors: attendee_creator.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    attendee_remover = EventAttendees::Remove.new(event_attendee: @event_attendee, team_admin: session_profile).call
    if attendee_remover.success?
      render partial: 'api/event_attendees/event_attendee', locals: { event_attendee: attendee_remover.event_attendee }
    else
      render json: { errors: attendee_remover.errors }, status: :unprocessable_entity
    end
  end

  def create_from_csv
    csv_data = params[:contact_csv].present? ? CSV.read(params[:contact_csv].path, headers: true) : nil
    @attendees_creator = EventAttendees::CreateFromCsv.new(csv_data: csv_data, team_admin: session_profile).call
    if @attendees_creator.success?
      render layout: false
    else
      render json: { errors: @attendees_creator.errors.uniq }, status: :unprocessable_entity
    end
  end

  # api_event_attendee_team_update(event_attendee, event_team_id: event_team.id)
  def team_update
    @event_team = session_profile.present? && session_profile.event_teams.where(id: params[:event_team_id]).first
    if params[:attendee_action] == 'add'
      @attendee_updater = EventAttendees::AddAttendeeToTeam.new(event_team: @event_team, event_attendee: @event_attendee).call
    else # 'remove'
      @attendee_updater = EventAttendees::RemoveAttendeeFromTeam.new(event_team: @event_team, event_attendee: @event_attendee).call
    end
    if @attendee_updater.success?
      render # 'api/event_attendees/team_update'
    else
      render json: { errors: @attendee_updater.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_event_attendee
    @event_attendee = session_profile.event_attendees.where(id: params[:id] || params[:event_attendee_id]).first
  end

  def event_attendee_params
    params.require(:event_attendee).permit(:first_name, :last_name, :email, teams: [])
  end
end
