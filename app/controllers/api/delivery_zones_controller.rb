class API::DeliveryZonesController < ApplicationController

  # before_action :authenticate_user!
  before_action :ensure_supplier
  before_action :fetch_supplier
  before_action :fetch_delivery_zone, only: %i[update destroy]

  def index
    @delivery_zones = @supplier.delivery_zones.joins(:suburb).order('suburbs.name ASC, radius ASC, delivery_fee ASC')
  end

  def create
    zone_creator = DeliveryZones::Upsert.new(supplier: @supplier, delivery_zone_params: delivery_zone_params).call
    if zone_creator.success?
      render partial: 'api/delivery_zones/delivery_zone', locals: { delivery_zone: zone_creator.delivery_zone }
    else
      render json: { errors: zone_creator.errors.uniq }, status: :unprocessable_entity
    end
  end

  def update
    zone_updator = DeliveryZones::Upsert.new(supplier: @supplier, delivery_zone: @delivery_zone, delivery_zone_params: delivery_zone_params).call
    if zone_updator.success?
      render partial: 'api/delivery_zones/delivery_zone', locals: { delivery_zone: zone_updator.delivery_zone }
    else
      render json: { errors: zone_updator.errors.uniq }, status: :unprocessable_entity
    end
  end

  def destroy
    zone_remover = DeliveryZones::Remove.new(supplier: @supplier, delivery_zone: @delivery_zone).call
    if zone_remover.success?
      render json: { removed: 'success' }
    else
      render json: { errors: zone_remover.errors.uniq }, status: :unprocessable_entity
    end
  end

private

  def delivery_zone_params
    suburb_fields = %i[suburb_id radius]
    fee_fields = %i[delivery_fee]
    operating_fields = %i[operating_wdays operating_hours_start operating_hours_end]
    params.require(:delivery_zone).permit(*suburb_fields, *fee_fields, *operating_fields)
  end

  def fetch_supplier
    @supplier = session_profile
  end

  def fetch_delivery_zone
    @delivery_zone = @supplier.present? ? @supplier.delivery_zones.where(id: params[:id]).first : DeliveryZone.where(id: params[:id]).first
  end

end
