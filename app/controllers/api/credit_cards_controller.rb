class API::CreditCardsController < ApplicationController

  respond_to :json

  def create
    card_creator = CreditCards::Create.new(customer: session_profile, card_params: credit_card_params).call
    respond_to do |format|
      format.json do
        if card_creator.success?
          render partial: 'api/credit_cards/credit_card', locals: { credit_card: card_creator.card }
        else
          render json: { errors: card_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    credit_card = CreditCard.where(id: params[:id]).first
    card_updater = CreditCards::Update.new(credit_card: credit_card, customer: session_profile, card_params: credit_card_params).call
    respond_to do |format|
      format.json do
        if card_updater.success?
          render partial: 'api/credit_cards/credit_card', locals: { credit_card: card_updater.card }
        else
          render json: { errors: card_updater.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def credit_card_params
    card_fields = %i[number name expiry_month expiry_year cvv]
    usage_fields = %i[saved_for_future enabled auto_pay_invoice]
    params.require(:credit_card).permit(*card_fields, *usage_fields)
  end

end
