class API::PurchaseOrdersController < ApplicationController

  before_action :ensure_customer
  before_action :fetch_purchase_order, only: %i[update destroy]

  def index
    limit = 10
    purchase_orders = session_profile.customer_purchase_orders
    purchase_orders = purchase_orders.order(active: :desc, created_at: :desc)
    purchase_orders = purchase_orders.includes(:orders)
    @purchase_orders = purchase_orders.page(params[:page]).per(limit)
  end

  def create
    purchase_order = Customers::FetchPurchaseOrder.new(customer: session_profile, cpo_id: purchase_order_params[:po_number]).call
    if purchase_order.update(purchase_order_params)
      render partial: 'api/purchase_orders/purchase_order', locals: { purchase_order: purchase_order }
    else
      render json: { errors: purchase_order.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @purchase_order.update(purchase_order_params)
      render partial: 'api/purchase_orders/purchase_order', locals: { purchase_order: @purchase_order }
    else
      render json: { errors: @purchase_order.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def destroy
    if @purchase_order.present? && @purchase_order.destroy
      head :ok
    else
      render json: { errors: @purchase_order.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def orders
    uninvoiced_orders = Order.where(customer_profile: session_profile)
    uninvoiced_orders = uninvoiced_orders.where(invoice_id: nil)
    uninvoiced_orders = uninvoiced_orders.where(status: %w[pending new amended confirmed delivered])
    uninvoiced_orders = uninvoiced_orders.includes(:supplier_profiles, :customer_purchase_order, :delivery_suburb)
    uninvoiced_orders = uninvoiced_orders.order(delivery_at: :desc)
    @orders = uninvoiced_orders
  end

  def update_order
    order = session_profile.orders.where(id: params[:order_id]).first
    if order.update(cpo_id: params[:cpo_id])
      render partial: 'api/purchase_orders/order', locals: { order: order }
    else
      render json: { errors: order.errors.full_messages }, status: :unprocessable_entity
    end
  end

private

  def fetch_purchase_order
    @purchase_order = session_profile.customer_purchase_orders.where(id: params[:id] || params[:purchase_order_id]).first
  end

  def purchase_order_params
    params.require(:purchase_order).permit(:po_number, :description, :active)
  end

end
