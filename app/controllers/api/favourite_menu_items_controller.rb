class API::FavouriteMenuItemsController < ApplicationController
  before_action :ensure_session_customer
  skip_before_action :verify_authenticity_token, if: :is_react_app?

  def index
    render json: favourite_menu_items.map(&:menu_item_id)
  end

  def update
    item_favourite = favourite_menu_items.where(menu_item_id: params[:id]).first_or_initialize
    if item_favourite.save
      head :ok
    else
      head :bad_request
    end
  end

  def destroy
    item_favourites = favourite_menu_items.where(menu_item_id: params[:id])
    item_favourites.delete_all
    if item_favourites.reload.blank?
      head :ok
    else
      head :bad_request
    end
  end

private

  def favourite_menu_items
    session_profile.favourite_menu_items
  end

  def ensure_session_customer
    head :forbidden unless session_profile && session_profile.profile.is_customer?
  end
end
