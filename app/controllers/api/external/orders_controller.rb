class API::External::OrdersController < API::External::BaseController

  def index
    case
    when @session_resource_profile.blank?
      head :unprocessable_entity
    when @session_resource_profile.instance_of?(CustomerProfile)
      @orders = orders_for_customer
    when @session_resource_profile.instance_of?(SupplierProfile)
      @orders = orders_for_supplier
    end
  end

  def show
    case
    when @session_resource_profile.blank?
      head :unprocessable_entity
    when @session_resource_profile.instance_of?(CustomerProfile)
      @order = @session_resource_profile.orders.where(id: params[:id]).first
    when @session_resource_profile.instance_of?(SupplierProfile)
      @order = @session_resource_profile.orders.where(id: params[:id]).first
    end
  end

private

  def orders_for_supplier
    page = params[:page].try(:to_i) || 1
    limit = params[:limit].try(:to_i) || 20
    lister_options = {
      for_supplier: @session_resource_profile,
      with_pagination: { page: page, limit: limit },
    }.merge(order_list_params.to_h.symbolize_keys)
    order_lister = Orders::ListForSuppliers.new(options: lister_options).call
    order_lister.orders
  end

  def orders_for_customer
    page = params[:page].try(:to_i) || 1
    limit = params[:limit].try(:to_i) || 20
    lister_options = {
      for_customer: @session_resource_profile,
      for_duration: params[:for_duration] || 'all',
      with_pagination: { page: page, limit: limit },
      ignore_cancelled_recurrent: order_list_params[:show_past].blank?
    }.merge(order_list_params.to_h.symbolize_keys)
    Orders::List.new(options: lister_options, includes: %i[supplier_profiles team_supplier_profiles]).call
  end

  def order_list_params
    params.permit(:name, :list_type, :query, :order_variant, :order_type, :show_past, :date, :for_date, supplier_ids: [])
  end

end
