class API::External::BaseController < ApplicationController

  before_action :doorkeeper_authorize!
  before_action :fetch_session_resource_profile

private

  def fetch_session_resource_profile
    @session_resource_profile = doorkeeper_token.present? ? doorkeeper_token.application&.owner : nil
  end

  def ensure_resource_supplier
    unless @session_resource_profile.present? && @session_resource_profile.profile.is_supplier?
      respond_to do |format|
        format.html do
          flash[:error] = 'You don\'t have access!'
          redirect_to after_sign_in_path_for(current_user)
        end
        format.json { head :forbidden }
      end
    end
  end

end