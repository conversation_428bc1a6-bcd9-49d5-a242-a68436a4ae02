class API::External::MenuController < API::External::BaseController

  before_action :ensure_resource_supplier
  skip_before_action :verify_authenticity_token, only: :create

  def index
    lister_options = {
      show_active: true,
      supplier: @session_resource_profile,
      for_cache: true,
      ignore_custom_menu_sections: true,
      order_by: { weight: :asc },
    }
    menu_items = MenuItems::List.new(options: lister_options, includes: [:menu_section]).call
    @section_grouped_menu_items = menu_items.group_by(&:menu_section).sort_by{|section, _| section.weight }
  end

  def create
    item_handlers = []
    if bulk_item_params.present? && bulk_item_params[:items].present?
      bulk_item_params[:items].each do |item_params|
        item_handler = Suppliers::Webhooks::HandleItemUpdate.new(supplier: @session_resource_profile, item_params: item_params).call
        item_handlers << item_handler
      end
    else
      item_handler = Suppliers::Webhooks::HandleItemUpdate.new(supplier: @session_resource_profile, item_params: single_item_params).call
      item_handlers << item_handler
    end
    if item_handlers.all?(&:success?)
      head :ok
    else
      render json: { errors: item_handlers.map(&:errors).flatten(1) }, status: :unprocessable_entity
    end
  end

private

  def single_item_params
    params.permit(:sku, :name, :stock_quantity)
  end

  def bulk_item_params
    params.permit(items: %i[sku name stock_quantity])
  end

end
