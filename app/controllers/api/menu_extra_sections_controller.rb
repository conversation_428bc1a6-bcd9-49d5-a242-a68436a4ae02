class API::MenuExtraSectionsController < ApplicationController

  before_action :authenticate_user!
  before_action :fetch_menu_extra_section, only: %i[update destroy]

  def index
    @menu_extra_sections = MenuExtraSection.where(menu_item_id: params[:menu_item_id])
  end

  def create
    extra_section_creator = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params, forced: true).call
    respond_to do |format|
      format.json do
        if extra_section_creator.success?
          render partial: 'api/menu_extra_sections/menu_extra_section', locals: { menu_extra_section: extra_section_creator.menu_extra_section }
        else
          render json: { errors: extra_section_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    extra_section_updater = MenuExtraSections::Upsert.new(menu_extra_section: @menu_extra_section, menu_extra_section_params: menu_extra_section_params, forced: true).call
    respond_to do |format|
      format.json do
        if extra_section_updater.success?
          render partial: 'api/menu_extra_sections/menu_extra_section', locals: { menu_extra_section: extra_section_updater.menu_extra_section }
        else
          render json: { errors: extra_section_updater.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def destroy
    extra_section_archiver = MenuExtraSections::Archive.new(menu_extra_section: @menu_extra_section).call
    respond_to do |format|
      format.json do
        if extra_section_archiver.success?
          render partial: 'api/menu_extra_sections/menu_extra_section', locals: { menu_extra_section: extra_section_archiver.menu_extra_section }
        else
          render json: { errors: extra_section_archiver.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def fetch_menu_extra_section
    @menu_extra_section = MenuExtraSection.where(id: params[:id]).first
  end

  def menu_extra_section_params
    params.require(:menu_extra_section).permit(:name, :weight, :min_limit, :max_limit, :menu_item_id)
  end
end
