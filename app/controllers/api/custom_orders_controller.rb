class API::CustomOrdersController < ApplicationController

  before_action :fetch_custom_order, only: :destroy

  # also works for update / quote / confirm
  def create
    custom_order_syncer = Orders::Custom::Upsert.new(order_params: custom_order_params).call
    if custom_order_syncer.success?
      render json: {
        redirect_to: rails_admin.custom_order_path('custom_order', custom_order_syncer.order),
        message: custom_order_message(custom_order_syncer),
      }
    else
      render json: { errors: custom_order_syncer.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    if @custom_order.present?
      if params[:cancel_mode].present?
        cancel_order && return
      else
        delete_order && return
      end
    else
      render json: { errors: ['Could not find custom order'] }, status: :unprocessable_entity
    end
  end

  def suppliers
    @custom_order_suppliers = []
    if supplier_params[:supplier_ids].present?
      supplier_params[:supplier_ids].each do |supplier_id|
        @custom_order_suppliers << CustomOrderSupplier.where(order_id: supplier_params[:order_id], supplier_profile_id: supplier_id).first_or_initialize
      end
    end
  end

private

  def fetch_custom_order
    @custom_order = CustomOrder.where(id: params[:id]).first
  end

  def cancel_order
    order_canceller = Orders::Cancel.new(order: @custom_order, mode: params[:cancel_mode]).call
    if order_canceller.success?
      render json: { success: true }
    else
      render json: { errors: order_canceller.errors }, status: :unprocessable_entity
    end
  end

  def delete_order
    if @custom_order.destroy
      render json: { success: true }
    else
      render json: { errors: @custom_order.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def custom_order_params
    custom_order_fields = %i[custom_action major_category_id location_id location_name]
    order_fields = %i[event_name number_of_people meal_plan_id order_id customer_profile_id whodunnit_id credit_card_id invoice_individually coupon_code]
    quote_fields = %i[quote_emails quote_message]
    contact_fields = %i[company_name contact_name contact_email contact_phone company_name cpo_id po_number department_identity]
    delivery_fields = %i[delivery_at delivery_address suburb_id delivery_instruction]
    total_fields = %i[commission delivery total]
    sanitized_order_params = params.permit(*custom_order_fields, *order_fields, *quote_fields, *contact_fields, *delivery_fields, *total_fields)
    sanitized_order_params[:order_lines] = params[:order_lines].present? && params[:order_lines].keys.present? ? params[:order_lines].values : []
    sanitized_order_params[:suppliers] = params[:suppliers].present? ? params[:suppliers] : {}
    sanitized_order_params
  end

  def custom_order_message(custom_order_syncer)
    case custom_order_syncer.action
    when 'save'
      'Your custom order is created/saved successfully.'
    when 'confirm'
      "Order ##{custom_order_syncer.order.id} has been confirmed successfully."
    when 'save_quote'
      'Order has been saved as a quote. Generated a new quote document.'
    when 'send_quote'
      'Quote has been sent to the passed in quote emails.'
    else
      'Some error occurred.. please try again'
    end
  end

  def supplier_params
    params.permit(:order_id, supplier_ids: [])
  end

end
