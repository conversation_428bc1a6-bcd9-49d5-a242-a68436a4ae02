class API::MinimumsController < ApplicationController

  before_action :ensure_supplier
  before_action :fetch_supplier
  before_action :fetch_supplier_section_categories, only: [:index]
  before_action :fetch_minumum, only: %i[update destroy]

  def index
    supplier_minimums = @supplier.minimums.includes(:category).order(:id)
    @minimums = (supplier_minimums + unset_category_minimums(supplier_minimums)).sort_by{|minimum| minimum.category.name }
  end

  def create
    @minimum = @supplier.minimums.new(minimum_params)
    if @minimum.save
      fetch_supplier_section_categories(category: @minimum.category)
      render partial: 'api/minimums/minimum', locals: { minimum: @minimum }
    else
      render json: { errors: @minimum.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @minimum.update(minimum_params)
      fetch_supplier_section_categories(category: @minimum.category)
      render partial: 'api/minimums/minimum', locals: { minimum: @minimum }
    else
      render json: { errors: @minimum.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def destroy
    destroyed_category = @minimum.category
    if @minimum.destroy
      fetch_supplier_section_categories(category: destroyed_category)
      new_minimum = @supplier.minimums.new(category: destroyed_category)
      render partial: 'api/minimums/minimum', locals: { minimum: new_minimum }
    else
      render json: { errors: @minimum.errors.full_messages }, status: :unprocessable_entity
    end
  end

private

  def fetch_supplier
    @supplier ||= session_profile
  end

  def fetch_minumum
    @minimum = @supplier.minimums.where(id: params[:id]).first
  end

  def minimum_params
    params.require(:minimum).permit(:category_id, :spend_price, :lead_time, :lead_time_day_before)
  end

  def fetch_supplier_section_categories(category: nil)
    supplier_sections = @supplier.menu_sections.where(archived_at: nil).includes(:categories)
    supplier_sections = supplier_sections.where(categories: { id: category.id }) if category.present?
    category_grouped_sections = {}
    supplier_sections.each do |menu_section|
      menu_section.categories.each do |category|
        category_grouped_sections[category] ||= []
        category_grouped_sections[category] << menu_section
      end
    end
    @category_grouped_sections = category_grouped_sections
  end

  def unset_category_minimums(existing_minimums)
    return [] if @category_grouped_sections.blank?

    existing_category_ids = existing_minimums.map(&:category_id)

    unset_categories = @category_grouped_sections.reject do |category, _|
      existing_category_ids.include?(category.id)
    end.keys
    unset_categories.map do |category|
      @supplier.minimums.new(category: category)
    end
  end

end
