class API::OrderLinesController < ApplicationController

  before_action :check_valid_supplier_suburb, only: :create
  before_action :fetch_order, only: :create
  before_action :fetch_order_and_location, only: %i[update destroy]
  skip_before_action :verify_authenticity_token, if: :is_react_app?

  def create
    @order_lines_creator = OrderLines::CreateMultiple.new(order: @order, order_params: order_params, order_lines_params: order_lines_params).call
    respond_to do |format|
      format.json do
        if @order_lines_creator.success?
          retrieve_totals(order_line: @order_lines_creator.created_order_lines.first)
          render
        else
          render json: @order_lines_creator.errors, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    order_line_upserter = OrderLines::Upsert.new(order: @order, location: @location, order_line_params: order_line_params).call
    respond_to do |format|
      format.json do
        if order_line_upserter.success?
          @order_line = order_line_upserter.order_line
          retrieve_totals(order_line: order_line_upserter.order_line)
          render
        else
          render json: { errors: order_line_upserter.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def destroy
    order_line_remover = OrderLines::Remove.new(order: @order, location: @location, order_line_params: order_line_params).call
    respond_to do |format|
      format.json do
        if order_line_remover.success?
          retrieve_totals(order_line: order_line_remover.order_line)
          @removed_supplier_id = order_line_remover.order_line.supplier_profile_id
          render
        else
          render json: { errors: order_line_remover.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def fetch_order
    @order = (order_params[:order_id].present? && Order.where(id: order_params[:order_id]).first.presence) || session_order
    if @order.blank?
      order_creator = Orders::Create.new(order_params: order_creation_params, customer: session_profile, cookies: cookies, whodunnit: current_user).call
      if order_creator.success?
        @order = order_creator.order
        session_order(@order.id)
      end
    end
  end

  def fetch_order_and_location
    @order = Order.where(id: order_params[:order_id]).first
    @location = @order.locations.where(id: order_params[:location_id]).first
  end

  def order_creation_params
    params.permit(:mealUUID, :quoteUUID)
  end

  def order_params
     params.permit(:location_id, :order_id, :is_home_delivery)
  end

  def order_lines_params
    return [] if params[:order_lines].blank?

    case
    when params[:order_lines].is_a?(Hash)
      params[:order_lines].values
    when params[:order_lines].is_a?(String)
      JSON.parse(params[:order_lines]).map(&:deep_symbolize_keys)
    else
      params[:order_lines]
    end
  end

  def order_line_params
    params.permit(:id, :quantity, :note, :attendee_id)
  end

  def retrieve_totals(order_line:)
    order_line_totals = OrderLines::CalculateOrderTotals.new(order_line: order_line, profile: session_profile).call
    @totals = order_line_totals.order_totals
    @team_order_spends = order_line_totals.team_order_spends
  end

  def check_valid_supplier_suburb
    if params[:supplier_id].present? && cookies[:yordar_suburb_id].present? && !supplier_delivers_in_suburb?
      render json: {
        message: 'The supplier you were trying to order from does not deliver to your area, you\'ll be redirected to a list of potential suppliers within the area.',
        redirect_to: next_app_supplier_search_url(category_group: 'office-catering', state: cookie_suburb&.state, suburb: cookie_suburb&.name&.gsub(/\s/, '-')),
      }, status: :unprocessable_entity and return
    end
  end

  def supplier_delivers_in_suburb?
    return true if params[:supplier_id].blank? || cookies[:yordar_suburb_id].blank?

    supplier = SupplierProfile.where(id: params[:supplier_id]).first
    potential_delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: cookie_suburb).call
    potential_delivery_zone.present?
  end

end
