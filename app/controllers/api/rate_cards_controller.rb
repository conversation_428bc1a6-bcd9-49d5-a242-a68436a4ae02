class API::RateCardsController < ApplicationController

  before_action :ensure_admin
  before_action :ensure_supplier
  before_action :fetch_rate_card, only: %i[update destroy]

  def create
    rate_card_creator = RateCards::Upsert.new(rate_card_params: rate_card_params).call
    respond_to do |format|
      format.json do
        if rate_card_creator.success?
          render partial: 'api/rate_cards/rate_card', locals: { rate_card: rate_card_creator.rate_card }
        else
          render json: { errors: rate_card_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    rate_card_updater = RateCards::Upsert.new(rate_card: @rate_card, rate_card_params: rate_card_params).call
    respond_to do |format|
      format.json do
        if rate_card_updater.success?
          render partial: 'api/rate_cards/rate_card', locals: { rate_card: rate_card_updater.rate_card }
        else
          render json: { errors: rate_card_updater.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def destroy
    rate_card_archiver = RateCards::Archive.new(rate_card: @rate_card, forced: params[:forced]).call
    respond_to do |format|
      format.json do
        if rate_card_archiver.success?
          render partial: 'api/rate_cards/rate_card', locals: { rate_card: rate_card_archiver.rate_card }
        else
          render json: { errors: rate_card_archiver.errors, warnings: rate_card_archiver.warnings }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def fetch_rate_card
    @rate_card = RateCard.where(id: params[:id]).first
  end

  def rate_card_params
    params.require(:rate_card).permit(:company_id, :menu_item_id, :serving_size_id, :price, :cost)
  end
end
