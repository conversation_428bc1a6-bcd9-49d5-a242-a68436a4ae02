class API::EmployeeSurveysController < ApplicationController

  before_action :ensure_customer, except: %i[show submit]
  before_action :fetch_customer, except: :submit
  before_action :fetch_employee_survey, only: %i[show update submit]

  skip_before_action :verify_authenticity_token, if: :is_react_app?, only: :submit

  def index
    @employee_surveys = @customer.employee_surveys
  end

  def show
    if @employee_survey.present?
      respond_to do |format|
        format.json do
          render partial: 'api/employee_surveys/employee_survey', locals: { employee_survey: @employee_survey }
        end
        format.pdf do
          document = Documents::Generate::EmployeeSurveyPromo.new(employee_survey: @employee_survey).call
          open(document.url) do |f|
            send_data f.read.force_encoding('BINARY'), filename: "#{document.name}.pdf", type: 'application/pdf', disposition: 'attachment'
          end
        end
      end
    else
      respond_to do |format|
        format.json { render json: { not_found: true }, status: 404 }
      end
    end
  end

  def create
    survey_creator = EmployeeSurveys::Upsert.new(customer: @customer, survey_params: employee_survey_params, prefill: params[:prefill]).call

    if survey_creator.success?
      employee_survey = survey_creator.employee_survey
      render partial: 'api/employee_surveys/employee_survey', locals: { employee_survey: employee_survey }
    else
      render json: { errors: survey_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    survey_updator = EmployeeSurveys::Upsert.new(customer: @customer, employee_survey: @employee_survey, survey_params: employee_survey_params, prefill: params[:prefill]).call

    if survey_updator.success?
      employee_survey = survey_updator.employee_survey
      render partial: 'api/employee_surveys/employee_survey', locals: { employee_survey: employee_survey }
    else
      render json: { errors: survey_updator.errors }, status: :unprocessable_entity
    end
  end

  def submit
    survey_submitter = EmployeeSurveys::Submit.new(employee_survey: @employee_survey, submission_params: submission_params).call

    if survey_submitter.success?
      render json: { success: true }
    else
      render json: { errors: survey_submitter.errors }, status: :unprocessable_entity
    end
  end

private

  def employee_survey_params
    params.require(:employee_survey).permit(:name, :category_group, :active)
  end

  def submission_params
    params.require(:employee_survey_submission).permit(:name, :overall_rating, survey_answers: %i[survey_question_id value])
  end

  def fetch_customer
    @customer = session_profile
  end

  def fetch_employee_survey
    survey_id = params[:id] || params[:employee_survey_id]
    @employee_survey = case
    when @customer.present?
      @customer.employee_surveys.where(id: survey_id).first
    else
      EmployeeSurvey.where(uuid: survey_id).first
    end
  end

end