class API::CustomersController < ApplicationController

  before_action :ensure_customer
  before_action :fetch_customer

  def update
    if customer_flag_params.present?
      customer_updated = @customer.customer_flags.update(customer_flag_params)
    else
      customer_updated = @customer.update(customer_params)
    end
    if customer_updated
      head :ok
    else
      render json: { errors: @customer.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def order_suppliers
    key_name = params[:show_past] ? 'past-supplier-ids' : 'upcoming-supplier-ids'
    cache_key = [@customer.cache_key, key_name]
    order_supplier_ids = Rails.cache.fetch(cache_key) do
      Customers::ListOrderSupplierIds.new(customer: @customer, show_past: params[:show_past].present?).call
    end
    @order_suppliers = SupplierProfile.where(id: order_supplier_ids).order(:company_name)
  end

  def checkout_details
    @order = params[:order_id].present? ? Order.where(id: params[:order_id]).first : session_order
  end

  def become_company_team_admin
    customer = session_profile
    request_sender = Customers::Emails::SendCompanyTeamAdminRequestEmail.new(customer: customer, message: admin_request_params[:message]).call
    EventLogs::Create.new(event_object: customer, event: 'company-team-admin-request', **admin_request_params&.to_h).delay(queue: :notifications).call
    if request_sender.success?
      render json: { success: true, message: 'Your request was successfully sent.' }
    else
      errors = ['There was an error and we couldn\'t send the request', 'Please try again!']
      render json: { errors: errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_customer
    if (params_id = (params[:id] || params[:customer_id]).presence)
      @customer = CustomerProfile.where(id: params_id).first
    else
      @customer = session_profile
    end
  end

  def customer_params
    params.require(:customer_profile).permit(:team_admin, :company_team_admin)
  end

  def customer_flag_params
    params.require(:customer_flags).permit(*CustomerProfile::CUSTOMER_FLAG_FIELDS) rescue nil
  end

  def admin_request_params
    params.permit(:message)
  end

end
