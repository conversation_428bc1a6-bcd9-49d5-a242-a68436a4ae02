class API::LocationsController < ApplicationController

  before_action :fetch_order
  before_action :fetch_location, only: %i[update destroy]
  skip_before_action :verify_authenticity_token, if: :is_react_app?

  def create
    location_creator = Locations::Upsert.new(order: @order, location_params: location_params).call
    if location_creator.success?
      @location = location_creator.location
      render 'show'
    else
      render json: { errors: location_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    location_updater = Locations::Upsert.new(order: @order, location: @location, location_params: location_params).call
    if location_updater.success?
      @location = location_updater.location
      render 'show'
    else
      render json: { errors: location_updater.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    location_remover = Locations::Remove.new(order: @order, location: @location).call
    if location_remover.success?
      @totals = Orders::CalculateCustomerTotals.new(order: @order, save_totals: true).call
      render
    else
      render json: { errors: location_remover.errors }, status: :unprocessable_entity
    end
  end

private

  def location_params
    params.require(:location).permit(:id, :details, :note)
  end

  def fetch_order
    @order = (params[:order_id].present? && Order.where(id: params[:order_id]).first.presence) || session_order
    if @order.blank?
      order_creator = Orders::Create.new(order_params: {}, customer: session_profile, cookies: cookies, whodunnit: current_user).call
      if order_creator.success?
        @order = order_creator.order
        session_order(@order.id)
      end
    end
  end

  def fetch_location
    @location = Location.where(id: params[:id]).first
  end

end
