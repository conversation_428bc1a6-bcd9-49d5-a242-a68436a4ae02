class API::FavouriteSuppliersController < ApplicationController

  before_action :ensure_session_customer
  before_action :fetch_customer
  before_action :fetch_supplier
  skip_before_action :verify_authenticity_token, if: :is_react_app?

  def update
    supplier_favouriter = Customers::FavouriteSupplier.new(customer: @customer, supplier: @supplier, kind: params[:kind]).call
    if supplier_favouriter.success?
      head :no_content
    else
      head :bad_request
    end
  end

  def destroy
    supplier_favouriter = Customers::FavouriteSupplier.new(customer: @customer, supplier: @supplier, kind: params[:kind]).call
    if supplier_favouriter.success?
      head :no_content
    else
      head :bad_request
    end
  end

private

  def fetch_customer
    @customer = session_profile
  end

  def fetch_supplier
    @supplier = SupplierProfile.where(id: params[:id]).first
  end

  def ensure_session_customer
    head :forbidden unless session_profile&.profile&.is_customer?
  end
end
