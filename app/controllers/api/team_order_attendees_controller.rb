class API::TeamOrderAttendeesController < ApplicationController

  before_action :fetch_team_admin, only: %i[update destroy]
  before_action :fetch_team_order_attendee_for_admin, only: %i[update destroy]
  skip_before_action :verify_authenticity_token, only: :checkout, if: :is_react_app?

  respond_to :json

  # deprecated - handle attendee links/buttons now removed from attendees panel
  def update
    attendee_updater = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: @team_order_attendee, team_admin: session_profile, anonymous_status: team_order_attendee_params[:anonymous_status]).call
    if attendee_updater.success?
      render partial: 'api/team_order_attendees/team_order_attendee', locals: { team_order_attendee: attendee_updater.team_order_attendee }
    else
      render json: { errors: attendee_updater.errors }, status: :unprocessable_entity
    end
  end

  def show
    team_order_attendee = TeamOrderAttendee.where(uniq_code: params[:id]).first
    render partial: 'api/team_order_attendees/team_order_attendee', locals: { team_order_attendee: team_order_attendee }
  end

  def show_package
    @package_order_attendee = TeamOrderAttendees::Fetch.new(attendee_code: attendee_params[:code], profile: session_profile).call
    @package_order = @package_order_attendee.present? && @package_order_attendee.order

    if @package_order.present? && @package_order.is_package_order?
      scoped_to = params[:scoped_to] || 'recent_week'
      scoped_time = scoped_to != 'all' && (params[:scoped_time].present? ? Time.zone.parse(params[:scoped_time]) : [@package_order.delivery_at, Time.zone.now].max)
      lister_options = {
        scoped_to: scoped_to,
        scoped_time: scoped_time,
      }
      @package_orders = TeamOrders::ListPackageOrders.new(team_order: @package_order, options: lister_options).call
    else
      format.json { render json: { not_found: true }, status: 404 }
    end
  end

  def checkout
    order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: params[:team_order_attendee_id], checkout_params: attendee_checkout_params, profile: session_profile).call
    redirect_url = checkout_redirect_url(order_checkout)
    if order_checkout.success?
      clean_up_session_orders
      render json: { success: true, redirect_url: redirect_url }
    else
      render json: { errors: order_checkout.errors, redirect_url: redirect_url }, status: :unprocessable_entity
    end
  end

  def destroy
    attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: @team_order_attendee, team_admin: session_profile).call
    if attendee_detacher.success?
      render partial: 'api/team_order_attendees/team_order_attendee', locals: { team_order_attendee: attendee_detacher.team_order_attendee }
    else
      render json: { errors: attendee_detacher.errors }, status: :unprocessable_entity
    end
  end

  def destroy_package_attendee
    package_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: attendee_params, profile: session_profile).call
    attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: package_attendee, team_admin: session_profile).call
    respond_to do |format|
      format.json do
        if attendee_detacher.success?
          render partial: 'api/team_order_attendees/team_order_attendee', locals: { team_order_attendee: attendee_detacher.team_order_attendee }
        else
          render json: { errors: attendee_detacher.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def fetch_team_admin
    unless session_profile.present? && session_profile.team_admin?
      redirect_to prismic_root_url, error: 'You don\'t have access to this!'
    end
  end

  def fetch_team_order_attendee_for_admin
    @team_order_attendee = session_profile.team_order_attendees.where(id: params[:id]).first
  end

  def team_order_attendee_params
    params.permit(:anonymous_status)
  end

  def attendee_checkout_params
    params.permit(:team_order_level_id)
  end

  def attendee_params
    params.permit(:code, :event_id)
  end

  def checkout_redirect_url(order_checkout)
    return nil if !order_checkout.success? && !order_checkout.is_redirected

    team_order = order_checkout.team_order
    case
    when session_profile.present? && team_order&.customer_profile == session_profile # is admin
      team_order_url(team_order)
    when team_order&.is_package_order?
      team_order_attendee_package_url(code: order_checkout.team_order_attendee.uniq_code)
    else
      nil
    end
  end
end
