class API::ServingSizesController < ApplicationController

  before_action :authenticate_user!
  before_action :fetch_serving_size, only: %i[update destroy]

  def index
    @serving_sizes = ServingSize.where(menu_item_id: params[:menu_item_id], archived_at: nil)
    respond_to do |format|
      format.json
    end
  end

  def create
    serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params, forced: true).call
    respond_to do |format|
      format.json do
        if serving_creator.success?
          render partial: 'api/serving_sizes/serving_size', locals: { serving_size: serving_creator.serving_size }
        else
          render json: { errors: serving_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    serving_updater = ServingSizes::Upsert.new(serving_size: @serving_size, serving_size_params: serving_size_params).call
    respond_to do |format|
      format.json do
        if serving_updater.success?
          render partial: 'api/serving_sizes/serving_size', locals: { serving_size: serving_updater.serving_size }
        else
          render json: { errors: serving_updater.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def destroy
    serving_archiver = ServingSizes::Archive.new(serving_size: @serving_size).call
    respond_to do |format|
      format.json do
        if serving_archiver.success?
          render partial: 'api/serving_sizes/serving_size', locals: { serving_size: serving_archiver.serving_size }
        else
          render json: { errors: serving_archiver.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def fetch_serving_size
    @serving_size = ServingSize.where(id: params[:id]).first
  end

  def serving_size_params
    params.require(:serving_size).permit(:name, :sku, :stock_quantity, :price, :weight, :menu_item_id, :is_default, :available_for_team_order)
  end
end
