class API::StripeCreditCardsController < ApplicationController

  skip_before_action :verify_authenticity_token, only: :create, if: :is_react_app?
  respond_to :json

  def create
    card_creator = Stripe::UpsertCreditCard.new(card_params: credit_card_params, customer: card_customer).call
    respond_to do |format|
      format.json do
        if card_creator.success?
          render partial: 'api/credit_cards/credit_card', locals: { credit_card: card_creator.credit_card }
        else
          render json: { errors: card_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    credit_card = CreditCard.where(id: params[:id]).first
    card_updator = Stripe::UpsertCreditCard.new(credit_card: credit_card, card_params: credit_card_params, customer: card_customer).call
    respond_to do |format|
      format.json do
        if card_updator.success?
          render partial: 'api/credit_cards/credit_card', locals: { credit_card: card_updator.credit_card }
        else
          render json: { errors: card_updator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def card_customer
    customer = params[:invoice_id].present? ? Invoice.where(uuid: params[:invoice_id]).first.invoice_orders.first.customer_profile : nil
    customer || session_profile
  end

  def credit_card_params
    params.require(:credit_card).permit(:last4, :brand, :country_code, :name, :expiry_month, :expiry_year, :saved_for_future, :stripe_token)
  end

end
