class API::Gatsby::StatusController < API::Gatsby::ApplicationController

  before_action :setup_suburb_from_name

  def fetch
    status_type = params[:type]

    case status_type
    when 'login'
      clean_up_session_orders if params[:clear_session_order_id].present? && session_order&.id == params[:clear_session_order_id].to_i
      render 'api/gatsby/status/login'
    when 'cart'
      render 'api/gatsby/status/cart'
    else
      render json: { status: nil }, status: :unprocessable_entity
    end
  end

private

  def fetch_customised_suppliers
    @customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: logged_in_session_profile, scoped_supplier_ids: params[:supplier_ids]).call
  end

  def fetch_customised_supplier
    @customised_supplier = Customers::FetchCustomisedSupplier.new(customer: logged_in_session_profile, supplier_id: params[:supplier_id]).call
  end

  def setup_suburb_from_name
    return if suburb_params.blank?

    @suburb = Suburbs::FetchForSearch.new(suburb_params: suburb_params, suburb_cookies: cookies, host: request&.host).call
    save_suburb_cookie(@suburb)
  end

  def suburb_params
    params.permit(:suburb, :state, :postcode, :street_address, :suburb_id)
  end

end
