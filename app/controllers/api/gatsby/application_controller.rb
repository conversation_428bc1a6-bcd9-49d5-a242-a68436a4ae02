class API::Gatsby::<PERSON>Controller < ApplicationController

  protect_from_forgery
  respond_to :json
  before_action :check_needs_contact_details

  def logged_in_user
    return nil if current_user.blank?
    return @_logged_in_user if !@_logged_in_user.nil?

    @_logged_in_user = logged_in_session_profile.present? ? logged_in_session_profile.try(:user) : current_user
  end
  helper_method :logged_in_user

  def logged_in_session_profile
    return nil unless current_user.present? && session.present? && session[:profile_id].present?

    @_logged_in_session_profile ||= Profile.where(id: session[:profile_id]).first.try(:profileable)
  end
  helper_method :logged_in_session_profile

  def session_order
    return nil if session.blank? || session[:order_id].blank?

    @_session_order ||= Order.where(id: session[:order_id]).first
  end
  helper_method :session_order

private

    def check_needs_contact_details
      @needs_contact_details = params[:needs_contact_details].present?
    end

    def save_suburb_cookie(suburb)
      return if suburb.blank?

      cookies[:yordar_suburb_id] = { value: suburb.id, domain: cookie_domain(host: request&.host) }
      cookies[:yordar_suburb_label] = { value: suburb.label, domain: cookie_domain(host: request&.host) }
      cookies[:yordar_suburb] = { value: suburb.name, domain: cookie_domain(host: request&.host) }
      cookies[:yordar_postcode] = { value: suburb.postcode, domain: cookie_domain(host: request&.host) }
      cookies[:yordar_state] = { value: suburb.state, domain: cookie_domain(host: request&.host) }
    end

end
