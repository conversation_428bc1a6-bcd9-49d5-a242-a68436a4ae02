class API::Saved<PERSON><PERSON>ressesController < ApplicationController

  before_action :ensure_customer
  before_action :fetch_saved_address, only: %i[update destroy]

  def index
    limit = 10
    @saved_addresses = session_profile.saved_addresses.order(created_at: :desc).page(params[:page]).per(limit)
  end

  def create
    saved_address = session_profile.saved_addresses.new
    if saved_address.update(saved_address_params)
      render partial: 'api/saved_addresses/saved_address', locals: { saved_address: saved_address }
    else
      render json: { errors: saved_address.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @saved_address.update(saved_address_params)
      render partial: 'api/saved_addresses/saved_address', locals: { saved_address: @saved_address }
    else
      render json: { errors: @saved_address.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def destroy
    if @saved_address.present? && @saved_address.destroy
      head :ok
    else
      render json: { errors: @saved_address.errors.full_messages }, status: :unprocessable_entity
    end
  end

private

  def fetch_saved_address
    @saved_address = session_profile.saved_addresses.where(id: params[:id]).first
  end

  def saved_address_params
    params.require(:saved_address).permit(:level, :street_address, :suburb_id, :instructions)
  end

end
