class API::SuppliersController < ApplicationController

  before_action :setup_suburb_from_name
  before_action :ensure_supplier, only: :update
  before_action :ensure_manageable_supplier, only: :update
  before_action :ensure_admin, only: :archive_menu
  before_action :fetch_supplier, only: %i[update menu archive_menu delivery_details]

  def update
    supplier_related_params = supplier_flag_params.present? ? {} : supplier_params
    supplier_updater = Suppliers::Update.new(supplier: @supplier, supplier_params: supplier_related_params, supplier_flag_params: supplier_flag_params).call

    respond_to do |format|
      format.json do
        if supplier_updater.success?
          render json: { success: true }
        else
          render json: { errors: supplier_updater.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def customer_suppliers
    @customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: session_profile, scoped_supplier_ids: params[:supplier_ids]).call
  end

  def menu
    @supplier = session_profile
    @show_archived = is_admin? && params[:show_archived].present?
    if @supplier.woolworths?
      @menu_item = @supplier.menu_items.where(id: params[:menu_item_id]).first
      render 'api/suppliers/item_menu'
    else
      lister_options = {
        supplier: @supplier,
        profile: session_profile,
        show_active: true,
        ignore_custom: true,
        order_by: 'menu_sections.weight ASC, menu_sections.created_at ASC'
      }
      menu_sections = MenuSections::List.new(options: lister_options).call
      menu_sections = menu_sections.includes(:companies, category_menu_sections: :category)
      case
      when @show_archived
        archived_menu_sections = MenuSection.joins(menu_items: { order_lines: :order }).where(menu_sections: { supplier_profile: @supplier }).where.not(menu_sections: { archived_at: nil })
        archived_menu_sections = archived_menu_sections.where('orders.delivery_at > ?', Time.zone.now - 1.day)
        archived_menu_sections = archived_menu_sections.where(orders: { status: %w[new pending amended confirmed paused] }).distinct
        menu_sections += archived_menu_sections if archived_menu_sections.present?
      when !is_admin?
        menu_sections = menu_sections.where(companies: { id: nil })
      end
      @menu_sections = menu_sections
    end
  end

  def delivery_details
    @supplier_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: @supplier, suburb: @suburb).call
    # render :json
  end

  def archive_menu
    menu_archiver = Suppliers::ArchiveMenu.new(supplier: @supplier).call
    if menu_archiver.success?
      flash[:success] = 'The menu was successfully archived'
    else
      flash[:error] = menu_archiver.errors.join('. ')
    end
    redirect_to supplier_menu_path(@supplier)
  end

  # used mainly by the react app for creating static supplier pages
  def cache_list
    if is_react_app? || Rails.env.development?
      lister_options = {
        searchable: true,
        for_cache: params[:for_cache]
      }
      @suppliers = Suppliers::List.new(options: lister_options).call
    else
      @suppliers = SupplierProfile.none
    end
  end

  def fetch_order_summary
    document_fetcher = Suppliers::FetchOrderSummary.new(supplier: session_profile, summary_day: params[:summary_day], regenerate: params[:regenerate].present? && params[
      :regenerate] == 'true').call
    if document_fetcher.success?
      respond_to do |format|
        format.json do
          document = document_fetcher.document
          render json: { name: document.name, url: document.url, version: document.version }
        end
      end
    else
      respond_to do |format|
        format.json do
          render json: { errors: document_fetcher.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def fetch_supplier
    @supplier = SupplierProfile.where(id: (params[:id] || params[:supplier_id])).first
  end

  def setup_suburb_from_name
    @suburb = Suburbs::FetchForSearch.new(suburb_params: suburb_params, suburb_cookies: cookies, host: request&.host).call
    save_suburb_cookie(@suburb)
  end

  def suburb_params
    params.permit(:suburb, :state, :postcode, :street_address, :suburb_id)
  end

  def supplier_params
    params.require(:supplier_profile).permit(:is_searchable, :lead_mode, :minimum_delivery_fee)
  end

  def supplier_flag_params
    params.require(:supplier_flags).permit(*SupplierProfile::SUPPLIER_FLAG_FIELDS) rescue nil
  end

  def ensure_manageable_supplier
    if session_profile.id != params[:id].to_i
      respond_to do |format|
        format.html { redirect_to prismic_root_url and return }
        format.json do
          render json: { errors: 'Do not have access to this supplier!' }, status: :unprocessable_entity
        end
      end
    end
  end

end
