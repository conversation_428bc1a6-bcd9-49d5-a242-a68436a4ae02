class API::SupplierClosuresController < ApplicationController

  before_action :ensure_supplier
  before_action :fetch_supplier_closure, only: %i[update destroy]

  def index
    limit = 10
    @supplier_closures = session_profile.closure_dates.order(starts_at: :desc).page(params[:page]).per(limit)
    render status: :ok
  end

  def create
    closure_creator = SupplierClosures::Upsert.new(supplier: session_profile, closure_params: closure_params).call
    if closure_creator.success?
      render partial: 'api/supplier_closures/supplier_closure', locals: { supplier_closure: closure_creator.supplier_closure }
    else
      render json: { errors: closure_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    closure_updator = SupplierClosures::Upsert.new(supplier: session_profile, supplier_closure: @supplier_closure, closure_params: closure_params).call
    if closure_updator.success?
      render partial: 'api/supplier_closures/supplier_closure', locals: { supplier_closure: closure_updator.supplier_closure }
    else
      render json: { errors: closure_updator.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    if @supplier_closure.present? && @supplier_closure.destroy
      head :ok
    else
      render json: { errors: @supplier_closure.errors.full_messages }, status: :unprocessable_entity
    end
  end

private

  def fetch_supplier_closure
    @supplier_closure = SupplierClosure.where(id: params[:id]).first
  end

  def closure_params
    params.require(:supplier_closure).permit(:starts_at, :ends_at, :reason, :description)
  end

end
