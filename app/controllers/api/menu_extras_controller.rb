class API::MenuExtrasController < ApplicationController

  before_action :authenticate_user!
  before_action :fetch_menu_extra, only: %i[update destroy]

  def index
    @menu_extras = MenuExtra.where(menu_item_id: params[:menu_item_id])
  end

  def create
    extra_creator = MenuExtras::Upsert.new(menu_extra_params: menu_extra_params, forced: true).call
    respond_to do |format|
      format.json do
        if extra_creator.success?
          render partial: 'api/menu_extras/menu_extra', locals: { menu_extra: extra_creator.menu_extra }
        else
          render json: { errors: extra_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    extra_updater = MenuExtras::Upsert.new(menu_extra: @menu_extra, menu_extra_params: menu_extra_params, forced: true).call
    respond_to do |format|
      format.json do
        if extra_updater.success?
          render partial: 'api/menu_extras/menu_extra', locals: { menu_extra: extra_updater.menu_extra }
        else
          render json: { errors: extra_updater.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def destroy
    extra_archiver = MenuExtras::Archive.new(menu_extra: @menu_extra).call
    respond_to do |format|
      format.json do
        if extra_archiver.success?
          render partial: 'api/menu_extras/menu_extra', locals: { menu_extra: extra_archiver.menu_extra }
        else
          render json: { errors: extra_archiver.errors }, status: :unprocessable_entity
        end
      end
    end
  end

private

  def fetch_menu_extra
    @menu_extra = MenuExtra.where(id: params[:id]).first
  end

  def menu_extra_params
    params.require(:menu_extra).permit(:name, :sku, :price, :weight, :menu_item_id, :menu_extra_section_id)
  end
end
