class API::LeadsController < ApplicationController
  respond_to :json

  def create
    @lead_creator = Leads::Create.new(lead_params: lead_params).call
    respond_to do |format|
      format.json do
        if @lead_creator.success?
          render json: { lead: @lead_creator.lead.id }
        else
          render json: { errors: @lead_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  private

  def lead_params
    params.require(:lead).permit(:lead_type, :progress, :email, :firstname, :lastname)
  end
end
