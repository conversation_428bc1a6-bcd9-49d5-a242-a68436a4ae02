class API::SurveyQuestionsController < ApplicationController

  before_action :ensure_customer
  before_action :fetch_employee_survey
  before_action :fetch_survey_question, only: :update

  def create
    question_creator = EmployeeSurveys::UpsertQuestion.new(employee_survey: @employee_survey, question_params: question_params).call

    if question_creator.success?
      survey_question = question_creator.survey_question
      render partial: 'api/survey_questions/survey_question', locals: { survey_question: survey_question }
    else
      render json: { errors: question_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    question_updator = EmployeeSurveys::UpsertQuestion.new(employee_survey: @employee_survey, question: @survey_question, question_params: question_params).call

    if question_updator.success?
      survey_question = question_updator.survey_question
      render partial: 'api/survey_questions/survey_question', locals: { survey_question: survey_question }
    else
      render json: { errors: question_updator.errors }, status: :unprocessable_entity
    end
  end

private

  def question_params
    params.require(:survey_question).permit(:label, :input_type, :active, :position, options: [])
  end

  def fetch_employee_survey
    return if session_profile.blank?

    survey_id = params[:employee_survey_id] || (params[:survey_question].present? ? params[:survey_question][:employee_survey_id] : nil)
    @employee_survey = session_profile.employee_surveys.where(id: survey_id).first
  end

  def fetch_survey_question
    @survey_question = case
    when @employee_survey.present?
      @employee_survey.survey_questions.where(id: params[:id]).first
    else
      SurveyQuestion.where(id: params[:id]).first
    end
  end

end
