class API::EmployeeSurveySubmissionsController < ApplicationController

  before_action :ensure_customer
  before_action :fetch_customer
  before_action :fetch_employee_survey

  def index
    submissions = EmployeeSurveys::ListSubmissions.new(employee_survey: @employee_survey, options: lister_options, includes: [:survey_answers]).call
    @employee_survey_submissions = submissions.order('employee_survey_submissions.created_at DESC')
    respond_to do |format|
      format.json
      format.csv do
        filename = "#{@employee_survey.name.parameterize}-submissions_#{Time.zone.now.to_s(:filename)}.csv"
        send_data (render_to_string 'api/employee_survey_submissions/index', layout: false), filename: filename
      end
    end
  end  

private

  def lister_options
    params.permit(:starts_on, :ends_on)
  end

  def fetch_customer
    @customer = session_profile
  end

  def fetch_employee_survey
    @employee_survey = @customer.present? && @customer.employee_surveys.where(id: params[:employee_survey_id]).first
  end

end
