class API::LoadingDocksController < ApplicationController

  before_action :fetch_order
  before_action :fetch_loading_dock, only: :create
  skip_before_action :verify_authenticity_token, if: :is_react_app?, only: :create

  # Loading Docks View Page
  def index
    errors = []
    case
    when @order.blank?
      errors << 'Could not locate Order'
    when @order.loading_dock.blank?
      errors << 'Order does not have a Loading Dock Code yet!'
    end
    if errors.present?
      render json: { errors: errors, redirect_url: after_sign_in_path_for(current_user, true) }, status: :unprocessable_entity
    else
      render
    end
  end

  # Loading Docks request page
  def new
    @loading_dock_request_handler = LoadingDocks::HandleOrderRequest.new(order: @order, request_uuid: params[:requestUUID]).call
    if @loading_dock_request_handler.success?
      render
    else
      render json: { errors: @loading_dock_request_handler.errors, redirect_url: after_sign_in_path_for(current_user, true) }, status: :unprocessable_entity
    end
  end

  def create
    loading_dock_creator = LoadingDocks::Upsert.new(loading_dock: @loading_dock, loading_dock_params: loading_dock_params, order: @order).call
    if loading_dock_creator.success?
      render partial: 'api/loading_docks/loading_dock', locals: { loading_dock: loading_dock_creator.loading_dock }
    else
      render json: { errors: loading_dock_creator.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_order
    @order = params[:order_id].present? ? Order.where(uuid: params[:order_id]).first : nil
  end

  def fetch_loading_dock
    return if params[:loading_dock].blank? || params[:loading_dock][:id].blank?

    @loading_dock = LoadingDock.where(id: params[:loading_dock][:id]).first
  end

  def loading_dock_params
    params.require(:loading_dock).permit(:customer_profile_id, :code, :saved_for_future, :file_url)
  end

end