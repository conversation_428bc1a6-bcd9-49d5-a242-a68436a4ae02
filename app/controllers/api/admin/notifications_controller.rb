class API::Admin::NotificationsController < API::AdminController

  before_action :fetch_notification, except: %i[index mark_all_as_viewed]

  def index
    lister_options = {
      page: 1,
      limit: 20,
      for_user: current_user,
    }.merge(list_params.to_h.symbolize_keys)
    @notifications = ::Admin::ListNotifications.new(options: lister_options).call
    @viewed_notification_ids = current_user.event_log_views.where(event_log_id: @notifications.map(&:id)).select(:event_log_id).map(&:event_log_id)
  end

  def show; end

  def mark_as_viewed
    view_marker = EventLogs::MarkAsViewed.new(event_log: @notification, user: current_user).call
    if view_marker.success?
      render partial: 'api/admin/notifications/notification', locals: { notification: @notification.reload, is_viewed: true }
    else
      render json: { errors: view_marker.errors }, status: :unprocessable_entity
    end
  end

  def mark_as_assigned
    assigned_marker = EventLogs::MarkAsAssigned.new(event_log: @notification, user: current_user).call
    if assigned_marker.success?
      render partial: 'api/admin/notifications/notification', locals: { notification: @notification.reload, is_viewed: true }
    else
      render json: { errors: view_marker.errors }, status: :unprocessable_entity
    end    
  end

  def mark_all_as_viewed
    @view_marker = EventLogs::MarkAllAsViewed.new(user: current_user, lister_options: list_params, include_high_severity: params[:include_high_severity].present?).call
    if @view_marker.success?
      render 
    else
      render status: :unprocessable_entity
    end
  end

private

  def list_params
    params.permit(:page, :limit, :event_type, :query, :favourites_only, :show_all_events, :unviewed_only, :severity)
  end

  def fetch_notification
    @notification = EventLog.where(id: params[:id] || params[:notification_id]).first
  end

end