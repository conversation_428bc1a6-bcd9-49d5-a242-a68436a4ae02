class API::Admin::StaffDetailsController < API::AdminController

  before_action :authenticate_user!
  before_action :fetch_customer
  before_action :fetch_staff_details

  def index; end

  def create
    if @staff_details.present?
      if @staff_details.update(detail_params)
        render 'index'
      else
        render json: { errors: @staff_details.errors.full_messages }, status: :unprocessable_entity
      end
    else
      head :not_found
    end
  end

private

  def fetch_customer
    return if current_user.blank?

    user_profile = current_user.profile&.profileable
    return if user_profile.blank? || !user_profile.is_a?(CustomerProfile)

    @customer = user_profile
  end

  def detail_params
    params.require(:staff_details).permit(personal: {}, emergency_contact: {}, bank: {}, tax: {}, documents: {})
  end

  def fetch_staff_details
    return if @customer.blank?

    @staff_details = @customer.staff_details.presence || @customer.build_staff_details
  end

end