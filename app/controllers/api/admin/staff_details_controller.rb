class API::Admin::StaffDetailsController < API::AdminController

  before_action :authenticate_user!
  before_action :fetch_customer
  before_action :fetch_staff_details, only: :index

  def index; end

  def create
    details_saver = Customers::SaveStaffDetails.new(customer: @customer, detail_params: detail_params).call
    if details_saver.success?
      @staff_details = details_saver.staff_details
      render :index, locals: { accounts_team_notified: details_saver.accounts_team_notified }
    else
      render json: { errors: details_saver.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_customer
    return if current_user.blank?

    user_profile = current_user.profile&.profileable
    return if user_profile.blank? || !user_profile.is_a?(CustomerProfile)

    @customer = user_profile
  end

  def detail_params
    params.require(:staff_details).permit(personal: {}, emergency_contact: {}, bank: {}, tax: {}, documents: {})
  end

  def fetch_staff_details
    return if @customer.blank?

    @staff_details = @customer.staff_details.presence || @customer.build_staff_details
  end

end