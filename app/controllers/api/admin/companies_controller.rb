class API::Admin::CompaniesController < API::AdminController

  before_action :fetch_company, only: :update
  before_action :ensure_company_access, only: :update

  def index
    lister_options = {
      page: 1,
      limit: 20,
      for_user: current_user,
      order_by: { name: :asc }
    }.merge(list_params.to_h.symbolize_keys)
    @companies = ::Admin::ListCompanies.new(options: lister_options, includes: [:customer_profiles]).call
  end

  def create
    company = Company.new(company_params)
    if company.save
      render partial: 'api/admin/companies/company', locals: { company: company }
    else
      render json: { errors: company.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @company.blank?
      render json: { errors: 'could not find company' }, status: :not_found
    end

    if @company.update(company_params)
      render partial: 'api/admin/companies/company', locals: { company: @company.reload }
    else
      render json: { errors: @company.errors.full_messages }, status: :unprocessable_entity
    end
  end

private

  def list_params
    params.permit(:page, :limit, :query, :favourites_only)
  end

  def fetch_company
    @company = Company.where(id: (params[:id] || params[:company_id])).first
  end

  def company_params
    params.require(:company).permit(:name, :can_pay_on_account, :can_pay_by_credit_card, :requires_po, :invoice_by_po, :payment_term_days, :accounting_software, customer_profile_ids: [])
  end

  def ensure_company_access
    adminable_company = nil
    if @company.present?
      lister_options = { for_user: current_user, company: @company }
      companies = ::Admin::ListCompanies.new(options: lister_options).call
      adminable_company = companies.first
    end
    if adminable_company.blank?
      respond_to do |format|
        format.json do
          render json: { errors: 'Do not have access to this Company!' }, status: :unprocessable_entity
        end
      end
    end
  end

end