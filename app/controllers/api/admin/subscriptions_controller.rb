class API::Admin::SubscriptionsController < API::AdminController

  before_action :ensure_admin
  before_action :fetch_promotion

  def index
    @subscriptions = PromotionSubscription.where(promotion: @promotion)
  end

  def create
    permissions_manager = Promotions::ManageSubscriptions.new(promotion: @promotion, subscriptions: subscription_params[:subscriptions]).call
    if permissions_manager.success?
      render partial: 'api/admin/promotions/promotion', locals: { promotion: permissions_manager.promotion.reload }
    else
      render json: { errors: permissions_manager.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_promotion
    @promotion = Promotion.where(id: params[:promotion_id]).first
  end

  def subscription_params
    params.permit(subscriptions: [:id, :subscriber_type, :subscriber_id, :active, :_delete])
  end

end