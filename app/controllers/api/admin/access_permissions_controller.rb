class API::Admin::AccessPermissionsController < API::AdminController

  before_action :ensure_admin
  before_action :fetch_customer

  def index
    @access_permissions = AccessPermission.where(admin: @customer).left_outer_joins(:customer_profile).order('access_permissions.active DESC, customer_profiles.customer_name ASC')
  end

  def create
    permissions_manager = Customers::ManageAccessPermissions.new(customer: @customer, access_permissions: edit_params[:access_permissions]).call
    if permissions_manager.success?
      render partial: 'api/admin/customers/customer', locals: { customer: @customer.reload }
    else
      render json: { errors: permissions_manager.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_customer
    @customer = CustomerProfile.where(id: params[:customer_id]).first
  end

  def edit_params
    params.permit(access_permissions: %i[id customer_profile_id scope active _delete])
  end

end