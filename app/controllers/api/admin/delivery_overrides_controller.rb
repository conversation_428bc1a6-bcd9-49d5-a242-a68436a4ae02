class API::Admin::DeliveryOverridesController < API::AdminController

  before_action :ensure_admin
  before_action :fetch_customer

  def index
    @delivery_overrides = @customer.delivery_overrides.order(created_at: :desc, active: :desc)
  end

  def create
    permissions_manager = Customers::ManageDeliveryOverrides.new(customer: @customer, delivery_overrides: edit_params[:delivery_overrides]).call
    if permissions_manager.success?
      render partial: 'api/admin/customers/customer', locals: { customer: @customer.reload }
    else
      render json: { errors: permissions_manager.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_customer
    @customer = CustomerProfile.where(id: params[:customer_id]).first
  end

  def edit_params
    params.permit(delivery_overrides: %i[id supplier_kind supplier_profile_id customer_override supplier_override active _delete])
  end

end