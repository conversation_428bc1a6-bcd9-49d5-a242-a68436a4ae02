class API::Admin::SupplierMarkupOverridesController < API::AdminController

  before_action :ensure_admin
  before_action :fetch_supplier

  def index
    @markup_overrides = @supplier.markup_overrides.order(created_at: :desc, active: :desc)
  end

  def create
    overrides_manager = Suppliers::ManageMarkupOverrides.new(supplier: @supplier, markup_overrides: edit_params[:markup_overrides]).call
    if overrides_manager.success?
      render partial: 'api/admin/suppliers/supplier', locals: { supplier: @supplier.reload }
    else
      render json: { errors: overrides_manager.errors }, status: :unprocessable_entity
    end
  end

private

  def fetch_supplier
    @supplier = SupplierProfile.where(id: params[:supplier_id]).first
  end

  def edit_params
    params.permit(markup_overrides: %i[id overridable_type overridable_id markup commission_rate active _delete])
  end

end