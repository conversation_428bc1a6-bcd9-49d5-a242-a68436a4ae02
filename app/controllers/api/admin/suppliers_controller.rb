class API::Admin::SuppliersController < API::AdminController

  before_action :fetch_supplier, only: :update
  before_action :ensure_supplier_update_access, only: :update

  def index
    lister_options = {
      page: 1,
      limit: 20,
      for_user: current_user,
    }.merge(list_params.to_h.symbolize_keys)
    @suppliers = ::Admin::ListSuppliers.new(options: lister_options, includes: [:user]).call
  end

  def update
    if @supplier.blank?
      render json: { errors: 'could not find supplier' }, status: :not_found
    end

    supplier_updater = ::Suppliers::Update.new(supplier: @supplier, supplier_params: supplier_params).call
    if supplier_updater.success?
      render partial: 'api/admin/suppliers/supplier', locals: { supplier: supplier_updater.supplier }
    else
      render json: { errors: supplier_updater.errors.full_messages }, status: :unprocessable_entity
    end
  end

private

  def list_params
    params.permit(:page, :limit, :query)
  end

  def fetch_supplier
    @supplier = SupplierProfile.where(id: (params[:id] || params[:supplier_id])).first
  end

  def supplier_params
    params.require(:supplier_profile).permit(:is_searchable, :team_supplier, :markup, :commission_rate, :close_from, :close_to, customer_profile_ids: [])
  end

  def ensure_supplier_update_access
    if !is_admin?
      respond_to do |format|
        format.json do
          render json: { errors: 'Do not have access to this Supplier!' }, status: :unprocessable_entity
        end
      end
    end
  end

end