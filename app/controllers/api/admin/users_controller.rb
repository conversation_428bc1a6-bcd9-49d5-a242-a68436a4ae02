class API::Admin::UsersController < API::AdminController

  before_action :ensure_admin, only: %i[show update]
  before_action :ensure_super_admin, only: :deprecate_user
  before_action :fetch_user
  
  def show
    if @user.present?
      render
    else
      render json: { errors: ['Could not find user'] }, status: :not_found
    end
  end

  def update
    if @user.update(user_update_params)
      case
      when @profileable.is_a?(CustomerProfile)
        render partial: 'api/admin/customers/customer', locals: { customer: @profileable.reload }
      when @profileable.is_a?(SupplierProfile)
        render partial: 'api/admin/suppliers/supplier', locals: { supplier: @profileable.reload }
      else
        render :nothing
      end
    else
      render json: { errors: @user.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def deprecate_user
    user_deprecater = ::Admin::DeprecateUser.new(user: @user, admin: current_user).call
    if user_deprecater.success?
      case
      when @profileable.is_a?(CustomerProfile)
        render partial: 'api/admin/customers/customer', locals: { customer: @profileable.reload }
      when @profileable.is_a?(SupplierProfile)
        render partial: 'api/admin/suppliers/supplier', locals: { supplier: @profileable.reload }
      else
        render :nothing
      end
    else
      render json: { errors: user_deprecater.errors }, status: :unprocessable_entity
    end
  end

private

  def user_update_params
    params.require(:user).permit(:firstname, :lastname, :email, :password, :password_confirmation)
  end
  
  def fetch_user
    if params[:profileable_type].present?
      fetch_user_from_profileable
    else
      @user = User.where(id: params[:id] || params[:user_id]).first
    end
  end

  def fetch_user_from_profileable
    case params[:profileable_type]
    when 'CustomerProfile', 'SupplierProfile'
      @profileable = params[:profileable_type].constantize.where(id: params[:id] || params[:user_id]).first
      @user = @profileable&.user
    else
      @user = nil
      @profileable = nil
    end    
  end

end