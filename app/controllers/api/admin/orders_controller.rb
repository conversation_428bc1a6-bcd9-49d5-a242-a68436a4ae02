class API::Admin::OrdersController < API::AdminController

  before_action :fetch_order, only: :show
  before_action :ensure_order_access, only: :show

  def index
    lister_options = {
      page: 1,
      limit: 20,
      for_user: current_user,
    }.merge(list_params.to_h.symbolize_keys)
    @orders = ::Admin::ListOrders.new(options: lister_options, includes: [:customer_profile, :supplier_profiles]).call
  end

  def show; end

private
  
  def list_params
    params.permit(:page, :limit, :query, :from_date, :to_date, :favourites_only, :show_past, :custom_orders_only)
  end

  def fetch_order
    @order = Order.where(id: params[:id]).first
  end

  def ensure_order_access
    adminable_order = nil
    if @order.present?
      lister_options = { for_user: current_user, order: @order }
      orders = ::Admin::ListOrders.new(options: lister_options).call
      adminable_order = orders.first
    end
    if adminable_order.blank?
      respond_to do |format|
        format.json do
          render json: { errors: 'Do not have access to this Order!' }, status: :unprocessable_entity
        end
      end
    end
  end

end