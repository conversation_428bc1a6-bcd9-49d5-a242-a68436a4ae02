class InvoicesController < ApplicationController

	include CloudinaryHelper
	before_action :authenticate_user!, only: :index

	# /c_profile/invoices
	def index
		render layout: 'customer_profiles'
	end

	def pay
		invoice_permission = Invoices::CheckAccessPermission.new(invoice_id: params[:id], customer: session_profile, is_admin: is_admin?).call
		@invoice = invoice_permission.invoice
		if invoice_permission.can_access?
			render 'pay' and return
		else
			if @invoice.present? && invoice_permission.redirect_to == new_user_session_path
				session['user_return_to'] = pay_invoice_path(@invoice.number)
			end
			flash[:warning] = invoice_permission.errors.join('. ')
			redirect_to invoice_permission.redirect_to and return
		end
	end

	def process_payment
		invoice = Invoice.where(uuid: payment_params[:invoice_uuid]).first
		credit_card = CreditCard.where(id: payment_params[:credit_card_id]).first

		payment_processor = Invoices::ProcessPayment.new(invoice: invoice, credit_card: credit_card).call
		if payment_processor.success?
			@invoice = payment_processor.invoice
			render 'confirmation'
		else
			flash[:warning] = "Sorry, but your payment was unsuccessful. Please try again. #{payment_processor.errors.first}"
			redirect_to pay_invoice_path(invoice.uuid) and return
		end
	end

private

	def payment_params
		params.require(:payment).permit(:invoice_uuid, :credit_card_id)
	end

end
