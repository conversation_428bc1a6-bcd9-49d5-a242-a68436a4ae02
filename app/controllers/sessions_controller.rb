# Extends the DeviseSession controller to implement a few customisations
class SessionsController < Devise::SessionsController

	include ApplicationHelper

	skip_before_action :verify_authenticity_token, only: :destroy, if: :is_react_app?

	def new
		session['user_return_to'] = params[:user_return_to] if params[:user_return_to].present?
		super
	end

	# Allows for an admin to act as customer or supplier by setting the session[:profile]
	def sign_in_as
		if helpers.is_admin?
			session[:profile_id] = User.find(params[:user_id]).profile.id
			# when we have this variable set, admin can modify an order which hasn't been invoiced yet.
			session[:sign_in_as_admin] = true
			# clean up order and orders in session so that no previous user order details won't be passed over to new signed-in-as user
			clean_up_session_orders

		end
		redirect_to after_sign_in_path_for(current_user)
	end

	# Clears the session[:profile]
	def sign_out_as
		if can_access_admin_portal?
			session.delete :profile_id

			session.delete :profile
			session.delete :sign_in_as_admin
			session.delete :last_flash
		end
		redirect_to admin_path
	end

	def sign_in_as_admin
		sign_into_user = User.where(id: params[:user_id]).first
		if sign_into_user.present? && is_yordar_admin?(user: current_user) && can_access_admin_portal?(user: sign_into_user)
			session[:original_admin_id] = current_user.id.dup
			sign_in(sign_into_user)
			clean_up_session_orders
			redirect_to params[:redirect_path] || admin_path
		else
			flash[:error] = 'You do not have access to admin'
			redirect_to request.referer || prismic_root_url
		end
	end

	def sign_out_as_admin
		if can_access_admin_portal? && session[:original_admin_id].present?
			session.delete :profile_id

			session.delete :profile
			session.delete :sign_in_as_admin
			session.delete :last_flash

			original_admin = User.where(id: session[:original_admin_id]).first
			session.delete :original_admin_id
			sign_in(original_admin)
			redirect_to admin_path
		else
			flash[:error] = 'You do not have access to admin'
			redirect_to request.referer || prismic_root_url
		end
	end

	def sign_in_as_customer
		sign_into_user = User.where(id: params[:user_id]).first
		sign_into_customer = sign_into_user.present? && sign_into_user.profile.profileable.is_a?(CustomerProfile) && sign_into_user.profile.profileable
		customer_is_adminable = false
		if sign_into_customer.present?
			lister_options = {
				customer: sign_into_customer,
				for_user: current_user,
			}
			customer_is_adminable = Admin::ListCustomers.new(options: lister_options).call.present?
		end
		if sign_into_customer.present? && customer_is_adminable
			session[:profile_id] = sign_into_user.profile.id
			clean_up_session_orders
			redirect_to params[:redirect_path] || customer_profile_path
		else
			flash[:error] = 'You do not have access to this customer'
			redirect_to request.referer || prismic_root_url
		end
	end

	# Clears the session[:profile]
	def sign_out_as_customer
		session.delete :profile_id
		session.delete :profile
		session.delete :last_flash

		session_profile(current_user.profile.id)
		redirect_to after_sign_in_path_for(current_user)
	end

	def sign_in_as_supplier
		sign_into_user = User.where(id: params[:user_id]).first
		sign_into_supplier = sign_into_user.present? && sign_into_user.profile.profileable.is_a?(SupplierProfile) && sign_into_user.profile.profileable
		supplier_is_adminable = false
		if sign_into_supplier.present?
			lister_options = {
				supplier: sign_into_supplier,
				for_user: current_user,
			}
			supplier_is_adminable = Admin::ListSuppliers.new(options: lister_options).call.present?
		end
		if sign_into_supplier.present? && supplier_is_adminable
			session[:profile_id] = sign_into_user.profile.id
			clean_up_session_orders
			redirect_to params[:redirect_path] || supplier_profile_path
		else
			flash[:error] = 'You do not have access to this supplier'
			redirect_to request.referer
		end
	end

	# POST /resource/sign_in
	def create
		self.resource = warden.authenticate!(auth_options)
		set_flash_message(:notice, :signed_in) if is_navigational_format?
		session[:last_flash] = 1.hour.ago if resource.profile.present? && resource.profile.is_customer? && resource.profile.profileable.present? && resource.profile.profileable.billing_details.blank?
		sign_in(resource_name, resource)
		respond_with resource, location: after_sign_in_path_for(resource)
	end

end
