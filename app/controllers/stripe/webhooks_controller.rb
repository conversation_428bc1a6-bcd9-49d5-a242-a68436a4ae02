class Stripe::WebhooksController < ApplicationController

  skip_before_action :verify_authenticity_token, raise: false
  before_action :retrieve_stripe_event

  def create
    if @stripe_event.present? && @stripe_event != 'invalid'
      Stripe::NotifyEvent.new(event: @stripe_event).delay(queue: :developer).call
      head :ok
    else
      render nothing: true, status: :bad_request
    end
  end

private

  def retrieve_stripe_event
    payload = request.body.read
    header_signature = request.env['HTTP_STRIPE_SIGNATURE']
    event = nil
    begin
      event = Stripe::Webhook.construct_event(
        payload, header_signature, yordar_credentials(:stripe, :webhook_secret)
      )
    rescue JSON::ParserError
      event = 'invalid'
    rescue Stripe::SignatureVerificationError
      event = 'invalid'
    end
    @stripe_event = event
  end

end
