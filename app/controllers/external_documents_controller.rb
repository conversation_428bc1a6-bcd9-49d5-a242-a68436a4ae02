class ExternalDocumentsController < ApplicationController

	# Used by Category Solutions suppliers and freight forwarders to submit a Proof of Delivery Document (POD)
	#  or an external invoice (Invoice Document)
	def submit_documents
		if Digest::MD5.hexdigest(params[:order_id] + params[:profile_id] + yordar_credentials(:random_salt)) == params[:hashed_value]
			@document = ExternalDocument.new
			order_params = params[:external_document]
			@order = Order.find(params[:order_id])
			@allow_pod_upload = (params[:profile_type] == 'ffc' || (params[:profile_type] == 'supplier' && !SupplierProfile.find(params[:profile_id]).needs_freight?))
			%w[pod invoice].each do |k|
				next if params[k].blank?

				# We have a document to upload
				preloaded = Cloudinary::PreloadedFile.new(params[k])
				if k == 'invoice'
					# Determine which invoice - supplier or FFC
					k = "#{params[:profile_type]}_invoice"
				end
				# Store the whole URL for the file as it is not an image and we don't need to do any processing with it
				ed = ExternalDocument.where(order_id: @order.id, doc_type: k).first_or_initialize
				ed.url = ApplicationController.helpers.cloudinary_url(preloaded.identifier)
				ed.ref_number = order_params[:ref_number] if order_params[:ref_number].present?
				@success = ed.save
			end
		else
			flash[:error] = 'Invalid Request.'
			redirect_to '/'
		end
		render 'orders/submit_documents'
	end
end
