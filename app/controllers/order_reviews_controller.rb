class OrderReviewsController < ApplicationController

  before_action :fetch_order
  before_action :fetch_supplier
  before_action :is_reviewable
  before_action :fetch_order_lines

  layout 'basic'

  def new
    @order_review = OrderReview.new(order: @order, supplier_profile: @supplier)
  end

  def create
    @order_review = OrderReview.new(review_params)
    if !@order_review.save
      @errors = @order_review.errors.full_messages
    end
    render :new
  end

private

  def review_params
    review_fields = %i[order_id supplier_profile_id comment]
    score_fields = %i[food_taste_score presentation_score delivery_punctuality_score]
    params.require(:order_review).permit(*review_fields, *score_fields)
  end

  def fetch_order
    order_id = params[:order_review].present? ? params[:order_review][:order_id] : params[:order_id]
    @order = Order.where(id: order_id).first
  end

  def fetch_supplier
    supplier_id = params[:order_review].present? ? params[:order_review][:supplier_profile_id] : params[:supplier_profile_id]
    @supplier = @order.present? ? @order.supplier_profiles.where(id: supplier_id).first : nil
  end

  def is_reviewable
    @error_message = case
    when @order.blank?
      'Cannot locate order to add review'
    when @supplier.blank?
      'Cannot locate supplier to add review'
    when OrderReview.where(order: @order, supplier_profile: @supplier).present?
      'Feedback for this order/supplier has already been submitted'
    end
    if @error_message.present?
      render 'order_reviews/missing_review' and return
    end
  end

  def fetch_order_lines
    lister_options = {
      order: @order,
      supplier: @supplier
    }
    @order_lines = OrderLines::List.new(options: lister_options).call.order(:name)
  end
end
