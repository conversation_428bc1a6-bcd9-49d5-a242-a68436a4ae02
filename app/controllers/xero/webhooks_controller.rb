class Xero::WebhooksController < ApplicationController

  skip_before_action :verify_authenticity_token, raise: false
  before_action :validate_payload

  def create
    begin
      if params['events'].present? && params['events'].is_a?(Array)
        params['events'].each do |event|
          Xero::HandleWebhookEvent.new(event: event).call
        end
      end
    rescue => exception
      puts "Exception: #{exception.inspect}"
    end
    head :ok
  end

private

  def validate_payload
    header_data = request.headers[Xero::API::Base::SIGNATURE]
    payload = request.body.read
    digest = OpenSSL::Digest.new('sha256')
    digested_data = OpenSSL::HMAC.digest(digest, yordar_credentials(:xero, :webhook_secret), payload)
    base64_data = Base64.encode64(digested_data).gsub(/\n/, '')
    if header_data != base64_data
      head :unauthorized and return
    end
  end

end
