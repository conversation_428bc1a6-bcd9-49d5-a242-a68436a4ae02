class RegistrationsController < Devise::RegistrationsController

	before_action :configure_permitted_parameters
	before_action :fetch_company_team_admin, only: %i[new create]
	before_action :fetch_adminable_customer, only: %i[new create]

	include <PERSON>Helper

	def new
		@is_staff_registration = params[:as_staff].present?
		build_resource
		%i[email firstname lastname].each do |field|
			resource.send("#{field}=", new_registration_params[field]) if resource.present? && new_registration_params[field].present?
		end
		respond_with resource, layout: @company_team_admin.present? ? 'team_order' : 'application'
	end

	def create
		if verify_recaptcha
			customer_registration = Customers::Register.new(user_params: sign_up_params, registration_params: registration_params, company_team_admin: @company_team_admin, adminable_customer: @adminable_customer).call

			case
			when customer_registration.success?
				customer = customer_registration.customer
				user = customer_registration.user

				sign_in(user)
				session_profile(user.profile.id)

				# successful registration..
				redirect_path = case
				when session[:user_return_to].present?
					session[:user_return_to]
				when CustomerProfile::VALID_STAFF_ROLES.include?(customer.role)
					admin_path
				else
					customer_profile_path
				end

				if @company_team_admin.present? && @company_team_admin.active_adminable_customer_profiles.include?(customer)
					company_name = @company_team_admin.company&.name || @company_team_admin.company_name
					flash_message(:success, "Successfully registered under #{company_name}")
					flash_message(:success, 'You will receive an email of confirmation shortly. Please confirm your account.')
				else
					flash[:success] = 'Registered yourself successfully. You will receive an email of confirmation shortly. Please confirm your account.'
				end
				session[:last_flash] = 1.hour.ago
				session[:user_return_to] = '' # cleanup!

				respond_to do |format|
					format.html { redirect_to redirect_path }
					format.json do
						render json: { redirect_to: redirect_path }
					end
				end

				
			when customer_registration.errors.include?('An account already exists with these details')
				error_message = "#{customer_registration.errors.join('. ')} Try logging in with that email or resetting the password using the Forget Password form."
				respond_to do |format|
					format.html do
						flash[:warning] = error_message
						render template: 'devise/sessions/new', locals: { resource: customer_registration.user }
					end
					format.json do
						render json: { errors: [error_message] }, status: :unprocessable_entity
					end
				end
			else
				respond_to do |format|
					format.html do
						flash[:warning] = customer_registration.errors.join('. ')
						@registration_step = 2
						render template: 'devise/registrations/new', locals: { resource: customer_registration.user }
					end
					format.json do
						render json: { errors: customer_registration.errors }, status: :unprocessable_entity
					end
				end
			end
		else
			error_message = 'Please check the captcha tickbox and verify that you are human!'
			respond_to do |format|
				format.html do
					flash[:warning] = error_message
					redirect_to new_user_registration_path
				end
				format.json do
					render json: { errors: [error_message] }, status: :unprocessable_entity
				end
			end
		end
	end

	# handles password change for logged-in user
	def change_password
		@user = current_user

		layout_name = case
		when session_profile.present? && session_profile.profile.is_customer?
			'customer_profiles'
		when session_profile.present? && session_profile.profile.is_supplier?
			'supplier_profiles'
		else
			'application'
		end

		if params[:user].present?
			# if there is something to edit, let's edit
			@user = User.find(current_user.id)
			if @user.update_with_password(user_params)
				# Sign in the user by passing validation in case his password changed
				bypass_sign_in @user
				# flash and move on..
				flash[:success] = 'Your password was changed successfully.'
				redirect_to after_sign_in_path_for(@user.reload)
			else
				# has error
				flash[:warning] = 'Please check the fields for errors.'
				render action: 'edit', layout: layout_name
			end
		else
			render action: 'edit', layout: layout_name
		end
	end

protected

	def new_registration_params
		params.permit(:email, :firstname, :lastname)
	end

	def registration_params
		params.permit(:role, :company_name, :contact_phone)
	end

	def configure_permitted_parameters
		devise_parameter_sanitizer.permit(:sign_up, keys: %i[firstname lastname suburb_id])
		# devise_parameter_sanitizer.permit(:account_update, keys: [:first_name, :last_name, :phone, :email, bank_attributes: [:bank_name, :bank_account]])
	end

	def user_params
		params.require(:user).permit(:current_password, :password, :password_confirmation)
	end

	def fetch_company_team_admin
		return if params[:company_team_admin_code].blank?

		@company_team_admin = CustomerProfile.where(uuid: params[:company_team_admin_code]).first
	end

	def fetch_adminable_customer
		return if params[:adminable_customer_code].blank?

		@adminable_customer = CustomerProfile.where(uuid: params[:adminable_customer_code]).first
	end

end
