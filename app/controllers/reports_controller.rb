class ReportsController < ApplicationController

  before_action :authenticate_user!

  def index
    if session_profile.profile.is_customer?
      render 'reports/customers/index', layout: 'customer_profiles'
    else
      render 'reports/suppliers/index', layout: 'supplier_profiles'
    end
  end

private

  def report_options
    params.permit(:start_date, :end_date, :report_type, :purchase_order_id, :category_group)
  end

end
