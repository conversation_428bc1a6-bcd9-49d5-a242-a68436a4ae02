# Handle webhook callbacks from Docusign with status updates for
# Envelopes (SupplierAgreementDocument model) that have previously
# been created by the system.
module Docusign
  class WebhooksController < ApplicationController
    skip_before_action :verify_authenticity_token, raise: false

    def create
      begin
        Docusign::HandleWebhookEvent.new(event: docusign_event).call
      rescue => exception
        Raven.capture_exception(exception,
          message: 'Docusign Webhook Handler Failed',
          extra: { event: request.raw_post }
        )
      end

      head :ok
    end

  private

    def docusign_event
      Hash.from_xml(request.raw_post)
    end

  end
end
