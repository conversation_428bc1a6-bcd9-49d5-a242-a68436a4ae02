class AdminController < ApplicationController

  before_action :authenticate_user!
  before_action :ensure_admin_portal_access
  before_action :ensure_yordar_admin, only: %i[admins pantry_managers coupons suburbs holidays promotions invoice_summary reminders]
  before_action :ensure_super_admin, only: :woolworths_accounts

  layout 'admin'

  def dashboard
    template = case
    when current_user.allow_all_supplier_access?
      'suppliers'
    when is_potential_staff? && !is_pantry_manager?
      'staff_on_boarding'
    else
      'customers'
    end
    render template
  end

  def staff_on_boarding; end

  def customers; end

  def admins; end

  def companies; end

  def suppliers; end

  def invoices; end

  def invoice_summary; end

  def orders; end

  def reports; end

  def pantry_managers; end

  def coupons; end

  def suburbs; end

  def holidays; end

  def promotions; end

  def reminders; end

  def woolworths_accounts; end

  def notifications; end

end