class SupplierInvoicesController < ApplicationController

  before_action :authenticate_user!
  before_action :ensure_supplier
  before_action :retrieve_supplier

  # /s_profile/invoices
  def index
    page = params[:page] || 1
    limit = params[:limit] || 20
    supplier_invoices = @supplier.supplier_invoices
    supplier_invoices = supplier_invoices.order(created_at: :desc)
    supplier_invoices = supplier_invoices.distinct.page(page).per(limit)
    @supplier_invoices = supplier_invoices.includes(:documents, orders: :customer_profile)
    render layout: 'supplier_profiles'
  end

private

  def retrieve_supplier
    @supplier = session_profile
  end

end
