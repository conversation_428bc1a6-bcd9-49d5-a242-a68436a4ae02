class WeeklyMenuReviewsController < ApplicationController
  def new
    unless weekly_menu
      flash[:error] = 'Cannot locate weekly menu to add review'
      redirect_back(fallback_location: prismic_root_url) and return
    end
    unless day
      flash[:error] = 'Cannot review without a day'
      redirect_back(fallback_location: prismic_root_url) and return
    end

    render locals: new_params
  end

  def create
    menu_review = WeeklyMenuReview.new(create_params)

    if menu_review.save
      flash[:success] = 'Thank you for your feedback'

      redirect_to weekly_menus_path(
        slug: menu_review.weekly_menu.weekly_menu_client.slug,
        week_of: menu_review.weekly_menu.week_of.to_date
      )
    else
      flash[:warning] = 'Unable to save feedback'
      render :new, locals: new_params
    end
  end

  private

  def new_params
    { weekly_menu: weekly_menu, day: day }
  end

  def create_params
    params.require(:weekly_menu_review).permit(
      :first_name,
      :last_name,
      :taste,
      :presentation,
      :quantity,
      :see_again,
      :comments
    ).merge(
      day: day,
      weekly_menu_id: weekly_menu.id
    )
  end

  def weekly_menu
    return @weekly_menu if defined? @weekly_menu

    @weekly_menu = WeeklyMenu.find_by(id: params[:weekly_menu_id])
  end

  def day
    return @day if defined? @day

    @day = params[:day]
  end
end
