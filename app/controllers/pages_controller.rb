class PagesController < ApplicationController

	before_action :redirect_to_prismic

private

	def redirect_to_prismic
		rails_subdomain = yordar_credentials(:rails_subdomain)
		prismic_subdomain = yordar_credentials(:prismic_subdomain)
		if request.subdomain.present? && prismic_subdomain && rails_subdomain.present? && request.subdomain.include?(rails_subdomain)
			if redirect_page_param.blank? || redirect_page_param == 'home'
				redirect_to prismic_root_url and return
			else
				redirect_to prismic_page_url(page: redirect_page_param), status: 301 and return
			end
		else
			render 'home'
		end
	end

	def redirect_page_param
		page_param = params[:slug].presence || params[:page] || ''
		page_param = 'staff-lunch' if page_param == 'working-lunch'
		page_param = 'supplier-partnership' if page_param == 'suppliers'
		page_param.parameterize.dasherize
	end

end
