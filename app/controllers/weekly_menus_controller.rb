class WeeklyMenusController < ApplicationController
  rescue_from ActiveRecord::RecordNotFound, with: :menu_not_found

  def show
    @menu_client = WeeklyMenuClient.find_by!(slug: params[:slug])
    @menu_fetcher = MenuFetcher.new(menu_client: @menu_client, week_of: week_of)
  end

  private

  def menu_not_found
    redirect_to :root
  end

  def week_of
    if params[:week_of].present?
      Time.zone.parse(params[:week_of])
    else
      Time.zone.now
    end
  end
end
