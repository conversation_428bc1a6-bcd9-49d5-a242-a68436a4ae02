class SupplierProfilesController < ApplicationController

	include CloudinaryHelper

	before_action :authenticate_user!, except: :anonymous_settings
	before_action :ensure_supplier, except: %i[anonymous_settings search]
	before_action :ensure_admin, only: :search

	respond_to :json
	layout 'supplier_profiles'

	def show
		page = params[:page].try(:to_i) || 1
		limit = params[:limit].try(:to_i) || 20
		lister_options = {
			for_supplier: session_profile,
			for_duration: params[:for_duration] || 'week',
			with_pagination: { page: page, limit: limit },
			list_type: 'pending',
		}.merge(order_list_params.to_h.symbolize_keys)
		order_lister = Orders::ListForSuppliers.new(options: lister_options).call
		@orders = order_lister.orders
		@lister_options = order_lister.lister_options
		respond_to do |format|
			format.html do
				if @lister_options[:list_type] == 'pending' && @orders.blank?
					flash[:notice] = 'You do not have any orders pending approval'
					redirect_to supplier_profile_path(list_type: 'upcoming') and return
				end
			end
			format.json { render layout: false }
		end
	end

	def menu
		@supplier = session_profile
	end

	def notification_preferences
		@notification_preferences = session_profile.notification_preferences
		render 'notification_preferences/index'
	end

	def export_menu
		supplier = SupplierProfile.find(params[:supplier_profile_id])
		if params[:instant].present?
			csv_generator = Suppliers::ExportMenuToCsv.new(supplier: supplier).call
			if csv_generator.success?
				send_data csv_generator.csv_data, filename: "#{supplier.company_name}_menu.csv"
			else
				flash[:error] = csv_generator.errors.join('. ')
			end
		else
			Suppliers::Emails::SendExportedMenuCsv.new(supplier: supplier, email: params[:email].presence).delay(queue: :notifications).call
			email_sent_to = params[:email].presence || yordar_credentials(:yordar, :orders_email)
			flash[:notice] = "Menu export is occurring in the background and will be emailed (to #{email_sent_to}) when complete."
		end
		redirect_back(fallback_location: prismic_root_url)
	end

	def import_menu
		# file type validation temporarily disabled due to missing asset 'checkbox.png' issue
		# uncomment code when it is fixed
		# begin
		# file uploading logic should be in controller
		csv_arrays = read_csv_file(params[:file])
		# handling the process of saving the content of the file to database
		SupplierProfile.find(params[:supplier_profile_id]).delay.import_menu_from_csv(csv_arrays)
		flash[:notice] = 'Menu uploading is occurring in the background. Please check again in 2-3 minutes.'
		redirect_back(fallback_location: prismic_root_url)
		# rescue
		# flash[:notice] =	'Invalid file format. Please select a .csv file'
		# redirect_back(fallback_location: prismic_root_url)
		# end
	end

	def ratings
		options = {
			supplier: session_profile,
		}
		options[:time] = Time.zone.parse(params[:date]) if params[:date].present?
		@ratings = OrderReviews::FetchRatingSummary.new(options: options).call
	end

	def send_agreement
		if current_user.present? && (current_user.admin? || current_user.allow_all_supplier_access?)
			document_generator = Docusign::GenerateSupplierAgreement.new(supplier: session_profile).call
			if document_generator.success?
				flash[:notice] = "Generated document in DocuSign with id #{document_generator.docusign_document.envelope_id}"
			else
				flash[:warning] = document_generator.errors.join('. ')
			end
		else
			flash[:error] = 'Only Yordar Admins can trigger this action.'
		end
		redirect_to supplier_account_path
	end

	def fetch_agreement
		lister_options = {
			latest: params[:latest],
			status: params[:status],
		}
		documents = Suppliers::ListAgreementDocuments.new(supplier: session_profile, options: lister_options).call
		latest_document = documents.last

		document_fetcher = Docusign::FetchAgreementDocument.new(envelope_id: latest_document.try(:docusign_envelope_id)).call
		if document_fetcher.success?
			tempfile = document_fetcher.docusign_document
			tempfile.open
			send_file tempfile,
								filename: 'yordar_supplier_agreement.pdf',
								type: 'application/pdf'
		else
			flash[:error] = document_fetcher.errors.join('. ')
			redirect_to supplier_account_path
		end
	end

	def update
		supplier_updater = Suppliers::Update.new(supplier: session_profile, supplier_params: supplier_profile_params).call

		respond_to do |format|
			format.html do
				if supplier_updater.success?
					flash[:success] = 'Your account details have been updated'
					if supplier_updater.updated_fields.present? && (%i[bsb_number bank_account_number] & supplier_updater.updated_fields).present?
						flash[:notice] = 'Yordar Admin has been notifed about your changes in bank details. They\'ll be in touch with you soon.'
					end
				else
					flash[:error] = supplier_updater.errors.join('. ')
				end
				redirect_to supplier_account_path
			end
		end
	end

	def anonymous_settings
		@config_setter = Suppliers::SetConfigAnonymously.new(uuid: params[:id], field: params[:field], value: params[:value]).call
	end

	# used in custom orders form
	def search
		@suppliers = SupplierProfile.where('company_name ilike ?', "%#{params[:term]}%").limit(15)
		render 'supplier_profiles/search', layout: false
	end

private

	# read user uploaded csv menu file
	def read_csv_file(file)
		CSV.read(file.path)
	end

	def supplier_profile_params
		info_fields = %i[company_name description email phone mobile]
		company_fields = %i[company_address company_address_suburb_id abn_acn liquor_license_no bsb_number bank_account_number]
		user_fields = %i[firstname lastname email id]
		profile_fields = %i[id avatar]
		params.require(:supplier_profile).permit(*info_fields, *company_fields, user_attributes: user_fields, profile_attributes: profile_fields)
	end

	def order_list_params
		params.permit(:name, :list_type)
	end
end
