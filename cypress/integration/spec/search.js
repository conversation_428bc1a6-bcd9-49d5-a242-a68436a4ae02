describe("Supplier Search / Filtering", () => {

  it("Takes you to the supplier menu page on click", function() {
    cy.visit("/search?suburb=Sydney")
    cy.get("[data-supplier-id='10654'] a").click({force: true})
    cy.url().should("include", "/show/ipoh-town-barangaroo")
    cy.get("h1.department-title").should(($header) => {
      expect($header).to.have.text("Ipoh Town Barangaroo")
    })
  })

  it ("Can filter the suppliers by name", () => {
    cy.visit("/search?suburb=Sydney")
    cy.get("#search").type("Bella{enter}")
    cy.get(".supplier-title").should(($title) => {
      expect($title.first()).to.have.text("Bella Catering & Events")
    })
  })

  it ("Can filter the suppliers by cuisines", () => {
    cy.visit("/search?suburb=Sydney")
    cy.get("[data-category-group='catering-services'][data-kind='category'] .dropdown-filter-button").click()
    cy.get("[data-category-group='catering-services'][data-kind='category'] .filter-content > ul > li:nth-child(4) > .drop-text").click()
    // cy.get(".dropdown:nth-child(4) > .filter-content > ul > li:nth-child(4) > .drop-text > #category_").check("morning-tea", { force: true })
    cy.get(".supplier-page > .overlay").click()
    // does not list Ipoh Town Barangaroo as it does not cater for morning-tea
    cy.wait(5000) // wait for the ajax filtering to finish
    cy.get(".supplier-title").each(($supplierTitle, index, $list) => {
      expect($supplierTitle).to.not.have.text("Ipoh Town Barangaroo")
    })
  })

  it ("Can filter the suppliers by dietary", () => {
    cy.visit("/search?suburb=Sydney")
    cy.get("[data-category-group='catering-services'][data-kind='category'] .dropdown-filter-button").click()
    cy.get("[data-category-group='catering-services'][data-kind='category'] .filter-content > ul > li:nth-child(29) > .drop-text").click()
    // cy.get("[data-category-group='catering-services'][data-kind='category'] .filter-content [name='dietary[]']").check("is_vegan", { force: true })
    cy.get(".supplier-page > .overlay").click()
    // does not list "Cheese Celebration as it does not cater for vegans
    cy.wait(5000) // wait for the ajax filtering to finish
    cy.get(".supplier-title").each(($supplierTitle, index, $list) => {
      expect($supplierTitle).to.not.have.text("Cheese Celebration")
    })
  })
});


