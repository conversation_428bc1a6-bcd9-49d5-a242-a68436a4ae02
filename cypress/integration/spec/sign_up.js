describe("User can sign-up", function () {
  it("Renders Homepage", function () {
    cy.visit("/")
  })
  it("Can select register", function () {
    cy.get("a.no-reset").contains("Register").click()
    cy.url().should('include', '/register')
  })
  it("Can type email into first box", function () {
    cy.get("#user_email").type("<EMAIL>")
  })
  it("Can fill out register form", function () {
    cy.get("#continue-with-email").click()
    cy.get("#user_firstname").type("Test")
    cy.get("#user_lastname").type("Guy")
    cy.get("#postcode").type("Sydney").wait(1000).type("{downarrow}").type("{enter}")
    cy.get("#user_company_name").type("Test Company")
    cy.get("#new_user_password").type("testing123")
    cy.get("#new_user_password_confirmation").type("testing123")
    cy.get("#sign-up-button").click()
  })
})
