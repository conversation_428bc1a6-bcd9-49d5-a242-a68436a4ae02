describe("App Homepage", function () {

  it("Can search suppliers for Sydney from homepage", function () {
    cy.visit("/")
    cy.get(".block-heading").should(($header) => {
      expect($header).to.have.text("All your office food is here")
    })
    cy.get("#location_search").type("Sydney")
    cy.get(".pac-container").find(".pac-item:first-child").click()

    cy.url().should("include", "/search?suburb=Sydney")
    cy.get(".suppliers-in").should(($header) => {
      expect($header).to.have.text("Office Catering in Sydney")
    })
  })

})
