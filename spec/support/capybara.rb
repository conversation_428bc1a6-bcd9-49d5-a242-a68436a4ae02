module CapybaraSignInHelper
  # Pass in a SupplierProfile or CustomerProfile.
  def sign_in_as(profileable)
    visit '/login'

    expect(page).to have_current_path '/login'

    within "form[action='/login']" do
      fill_in 'user[email]', with: profileable.profile.user.email
      fill_in 'user[password]', with: profileable.profile.user.password
      click_button 'Continue'
    end

    case
    when profileable.is_a?(SupplierProfile)
      expect(page).to have_current_path '/s_profile'
    when profileable.is_a?(CustomerProfile)
      expect(page).to have_current_path '/c_profile'
    else
      raise 'Unexpected type of profile.'
    end
  end
end

RSpec.configure do |config|
  config.include CapybaraSignInHelper, type: :feature
end

Capybara.register_driver :poltergeist do |app|
  Capybara::Poltergeist::Driver.new(app, {
    js_errors: false
  })
end

Capybara.javascript_driver = :poltergeist
