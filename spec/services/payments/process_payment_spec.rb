require 'rails_helper'

RSpec.describe Payments::ProcessPayment, type: :service, payments: true, eway: true, stripe: true do

  let!(:eway_payment_processor) { double(Eway::ProcessPayment) }
  let!(:stripe_payment_processor) { double(Stripe::ProcessPayment) }

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:credit_card) { create(:credit_card, :random) }
  let!(:order) { create(:order, :delivered, customer_profile: customer, customer_total: 1000, credit_card_id: credit_card.id) }

  let!(:invoice) { create(:invoice, :random, amount_price: order.customer_total) }
  let!(:payment) { create(:payment, order: order, amount: order.customer_total, invoice: invoice, user_id: customer.user.id, credit_card: credit_card) }

  before do
    allow(Eway::ProcessPayment).to receive(:new).and_return(eway_payment_processor)
    allow(Stripe::ProcessPayment).to receive(:new).and_return(stripe_payment_processor)

    order.update_column(:invoice_id, invoice.id)
  end

  it 'cannot process payment thats already proccessed' do
    payment.update_column(:response_text, 'some existing response')

    payment_processor = Payments::ProcessPayment.new(payment: payment).call
    expect(payment_processor).to_not be_success
    expect(payment_processor.errors).to include('Payment is already processed')

    expect(Eway::ProcessPayment).to_not receive(:new).with(payment: payment)
    expect(Stripe::ProcessPayment).to_not receive(:new).with(payment: payment)
  end

  it 'cannot process payment for an invalid card' do
    credit_card.update_columns(stripe_token: nil, gateway_token: nil)

    payment_processor = Payments::ProcessPayment.new(payment: payment).call
    expect(payment_processor).to_not be_success
    expect(payment_processor.errors).to include('Cannot pay with invalid credit card')

    expect(Eway::ProcessPayment).to_not receive(:new).with(payment: payment)
    expect(Stripe::ProcessPayment).to_not receive(:new).with(payment: payment)
  end

  context 'for an eway card', eway: true do
    before do
      credit_card.update_columns(gateway_token: SecureRandom.hex(7), stripe_token: nil)
    end

    context 'with successfull payment response' do
      let!(:successfull_response) do
        OpenStruct.new(
          success?: true,
          response_message: 'successfull response message',
          auth_code: 'auth_code',
          transaction_number: 'transaction_number',
          errors: []
        )
      end
      before do
        allow(eway_payment_processor).to receive(:call).and_return(successfull_response)
      end

      it 'processes payment via eway for a credit card saved via eWay' do
        expect(Eway::ProcessPayment).to receive(:new).with(payment: payment)
        expect(Stripe::ProcessPayment).to_not receive(:new)

        payment_processor = Payments::ProcessPayment.new(payment: payment).call
        expect(payment_processor).to be_success
      end

      it 'saves payment reponse data within payment' do
        payment_processor = Payments::ProcessPayment.new(payment: payment).call
        expect(payment_processor).to be_success
        updated_payment = payment_processor.payment
        expect(updated_payment.id).to eq(payment.id)

        expect(updated_payment.response_text).to eq('successfull response message')
        expect(updated_payment.auth_code).to eq('auth_code')
        expect(updated_payment.transaction_number).to eq('transaction_number')
      end
    end

    context 'with successfull payment response message of 08,Honour With Identification' do
      let!(:successfull_honour_response) do
        OpenStruct.new(
          success?: true,
          response_message: '08,Honour With Identification',
          auth_code: 'honour_auth_code',
          transaction_number: 'honour_transaction_number',
          errors: []
        )
      end
      before do
        allow(eway_payment_processor).to receive(:call).and_return(successfull_honour_response)
      end

      it 'saves payment reponse data within payment' do
        payment_processor = Payments::ProcessPayment.new(payment: payment).call
        expect(payment_processor).to be_success
        updated_payment = payment_processor.payment
        expect(updated_payment.id).to eq(payment.id)

        expect(updated_payment.response_text).to eq('08,Honour With Identification')
        expect(updated_payment.auth_code).to eq('honour_auth_code')
        expect(updated_payment.transaction_number).to eq('honour_transaction_number')
      end
    end

    context 'with an unsuccessfull payment response' do
      let!(:unsuccessfull_response) do
        OpenStruct.new(
          success?: false,
          response_message: 'unsuccessfull response message',
          auth_code: nil,
          transaction_code: nil,
          errors: ['inspected payment response']
        )
      end
      before do
        allow(eway_payment_processor).to receive(:call).and_return(unsuccessfull_response)
      end

      it 'saves payment reponse data within payment' do
        payment_processor = Payments::ProcessPayment.new(payment: payment).call

        expect(payment_processor).to_not be_success
        expect(payment_processor.errors).to include('inspected payment response')

        updated_payment = payment_processor.payment
        expect(updated_payment.id).to eq(payment.id)
        expect(updated_payment.response_text).to eq('unsuccessfull response message')
        expect(updated_payment.auth_code).to be_blank
        expect(updated_payment.transaction_number).to be_blank
      end
    end
  end

  context 'with a stripe card', stripe: true do

    before do
      credit_card.update_columns(stripe_token: SecureRandom.hex(7), gateway_token: nil)
    end

    context 'with successfull payment response' do
      let!(:successfull_response) do
        OpenStruct.new(
          success?: true,
          response_message: 'successfull response message',
          auth_code: 'auth_code',
          transaction_number: 'transaction_number',
          errors: []
        )
      end

      before do
        allow(stripe_payment_processor).to receive(:call).and_return(successfull_response)
      end

      it 'processes payment via stripe for a credit card saved via Stripe' do
        expect(Stripe::ProcessPayment).to receive(:new).with(customer: customer, payment: payment)
        expect(Eway::ProcessPayment).to_not receive(:new)

        payment_processor = Payments::ProcessPayment.new(payment: payment).call
        expect(payment_processor).to be_success
      end
    end

    context 'with successfull payment response', skip: 'Needs error codes'
  end

end
