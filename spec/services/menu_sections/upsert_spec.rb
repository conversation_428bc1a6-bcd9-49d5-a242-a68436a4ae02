require 'rails_helper'

RSpec.describe MenuSections::Upsert, type: :service, menu_sections: true do

  let(:supplier) { create(:supplier_profile, :random) }
  let(:category1) { create(:category, :random) }
  let(:category2) { create(:category, :random) }

  let!(:menu_section_params) do
    {
      name: Faker::Name.name
    }
  end

  before do
    category_group_cacher = delayed_category_group_cacher = double(Suppliers::Cache::CategoryGroups)
    allow(Suppliers::Cache::CategoryGroups).to receive(:new).and_return(category_group_cacher)
    allow(category_group_cacher).to receive(:delay).and_return(delayed_category_group_cacher)
    allow(delayed_category_group_cacher).to receive(:call).and_return(true)
  end

  it 'lets you create a new menu section' do
    section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params).call

    expect(section_creator).to be_success
    created_menu_section = section_creator.menu_section
    expect(created_menu_section).to be_present
    expect(created_menu_section).to be_persisted
  end

  it 'create a new menu section with default params' do
    section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params).call

    expect(section_creator).to be_success
    created_menu_section = section_creator.menu_section
    expect(created_menu_section.is_hidden).to be_falsey
    expect(created_menu_section.weight).to eq(MenuSection.pluck(:weight).compact.max)
  end

  it 'sanitizes name (with spaces) when saving' do
    name_with_space = [" #{Faker::Name.name}", "#{Faker::Name.name} "].sample
    menu_section_params = { name: name_with_space }
    section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params).call

    expect(section_creator).to be_success
    created_menu_section = section_creator.menu_section
    expect(created_menu_section.name).to eq(name_with_space.strip)
  end

  it 'create a new menu section with passed in params' do
    menu_section_params_with_data = menu_section_params.merge({ supplier_profile_id: supplier.id, weight: 10, group_name: Faker::Name.name, is_hidden: true })
    section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params_with_data).call

    expect(section_creator).to be_success
    created_menu_section = section_creator.menu_section
    expect(created_menu_section.name).to eq(menu_section_params_with_data[:name])
    expect(created_menu_section.supplier_profile).to eq(supplier)
    expect(created_menu_section.weight).to eq(menu_section_params_with_data[:weight])
    expect(created_menu_section.group_name).to eq(menu_section_params_with_data[:group_name])
    expect(created_menu_section.is_hidden).to be_truthy
  end

  it 'creates menu_sections with passed in category ids' do
    menu_section_params_with_category_ids = menu_section_params.merge({ category_ids: [category1.id, '', category2.id, nil] })
    section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params_with_category_ids).call

    expect(section_creator).to be_success
    created_menu_section = section_creator.menu_section
    expect(created_menu_section.categories).to include(category1, category2)
  end

  context 'supplier flags update' do
    it 'updates the supplier has_<category_group> flags of a menu section attached to a supplier' do
      menu_section_params_with_supplier = menu_section_params.merge({ supplier_profile_id: supplier.id })
      expect(Suppliers::Cache::CategoryGroups).to receive(:new).with(suppliers: [supplier])

      section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params_with_supplier).call
      expect(section_creator).to be_success
    end

    it 'does not request to update supplier has_<category_group> flags of a menu section not attached to a supplier' do
      expect(Suppliers::Cache::CategoryGroups).to_not receive(:new)

      section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params).call
      expect(section_creator).to be_success
    end
  end

  context 'with existing menu section' do
    let!(:menu_section) { create(:menu_section, :random, supplier_profile: supplier) }

    it 'does not creates a new menu section with the name + group_name of an existing menu section' do
      section_name = [menu_section.name, " #{menu_section.name}", "#{menu_section.name} "].sample
      menu_section_params = { name: section_name, group_name: menu_section.group_name, supplier_profile: supplier }
      section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params).call

      expect(section_creator).to be_success
      created_menu_section = section_creator.menu_section
      expect(created_menu_section.name).to eq(menu_section.name)
      expect(created_menu_section.group_name).to eq(menu_section.group_name)
      expect(created_menu_section.id).to eq(menu_section.id) # same menu section
    end

    it 'does not create a new `custom` menu section for the supplier' do
      existing_custom_menu_section = create(:menu_section, :random, supplier_profile: supplier, name: 'custom', group_name: nil)
      menu_section_params = { name: 'custom', supplier_profile: supplier }
      section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params).call

      expect(section_creator).to be_success
      created_menu_section = section_creator.menu_section
      expect(created_menu_section.id).to eq(existing_custom_menu_section.id) # same menu section
    end

    it 'creates a new menu section with the name of an existing menu section if it does not belong to the supplier' do
      new_supplier = create(:supplier_profile, :random)
      menu_section_params = { name: menu_section.name, group_name: menu_section.group_name, supplier_profile: new_supplier }
      section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params).call

      expect(section_creator).to be_success
      created_menu_section = section_creator.menu_section
      expect(created_menu_section.id).to_not eq(menu_section.id) # not the same menu section
      expect(created_menu_section.name).to eq(menu_section.name)
      expect(created_menu_section.group_name).to eq(menu_section.group_name)
      expect(created_menu_section.supplier_profile).to eq(new_supplier)
    end

    it 'creates a new menu section with the name of an existing menu section if forced' do
      menu_section_params = { name: menu_section.name, group_name: menu_section.group_name }
      section_creator = MenuSections::Upsert.new(menu_section_params: menu_section_params, forced: true).call

      expect(section_creator).to be_success
      created_menu_section = section_creator.menu_section
      expect(created_menu_section.name).to eq(menu_section.name)
      expect(created_menu_section.group_name).to eq(menu_section.group_name)
      expect(created_menu_section.id).to_not eq(menu_section.id) # not the same menu section
    end

    it 'updates the menu section with passed in params' do
      menu_section_update_params = menu_section_params.merge({ supplier_profile_id: supplier.id, weight: 10, group_name: Faker::Name.name, is_hidden: true })
      section_updator = MenuSections::Upsert.new(menu_section: menu_section, menu_section_params: menu_section_update_params).call

      expect(section_updator).to be_success
      updated_menu_section = section_updator.menu_section
      expect(updated_menu_section.id).to eq(menu_section.id)
      expect(updated_menu_section.name).to eq(menu_section_update_params[:name])
      expect(updated_menu_section.supplier_profile).to eq(supplier)
      expect(updated_menu_section.weight).to eq(menu_section_update_params[:weight])
      expect(updated_menu_section.group_name).to eq(menu_section_update_params[:group_name])
      expect(updated_menu_section.is_hidden).to be_truthy
    end

    it 'updates menu_sections with passed in category ids' do
      menu_section_params = { category_ids: [category2.id, '', category1.id, nil] }
      section_updator = MenuSections::Upsert.new(menu_section: menu_section, menu_section_params: menu_section_params).call

      expect(section_updator).to be_success
      updated_menu_section = section_updator.menu_section
      expect(updated_menu_section.categories).to include(category1, category2)
    end

    it 'removes categories from existing menu_sections if passed an array containing blanks' do
      menu_section.category_ids = [category1, category2].map(&:id)
      expect(menu_section.reload.categories).to include(category1, category2)

      menu_section_params = { category_ids: ['', nil] }
      section_updator = MenuSections::Upsert.new(menu_section: menu_section, menu_section_params: menu_section_params).call

      expect(section_updator).to be_success
      updated_menu_section = section_updator.menu_section
      expect(updated_menu_section.categories).to be_blank
    end
  end # with existing menu section

end
