require 'rails_helper'

RSpec.describe MenuSections::Archive, type: :service, menu_sections: true do

  let!(:menu_section) { create(:menu_section, :random) }

  before do
    category_group_cacher = delayed_category_group_cacher = double(Suppliers::Cache::CategoryGroups)
    allow(Suppliers::Cache::CategoryGroups).to receive(:new).and_return(category_group_cacher)
    allow(category_group_cacher).to receive(:delay).and_return(delayed_category_group_cacher)
    allow(delayed_category_group_cacher).to receive(:call).and_return(true)
  end

  it 'archives the menu section' do
    archiver = MenuSections::Archive.new(menu_section: menu_section).call

    expect(archiver).to be_success
    expect(menu_section.reload.archived_at).to be_present
  end

  context 'supplier flags update' do
    let!(:supplier) { create(:supplier_profile, :random) }
  let!(:menu_section) { create(:menu_section, :random) }
    let!(:menu_section_with_supplier) { create(:menu_section, :random, supplier_profile: supplier) }

    it 'updates the supplier has_<category_group> flags when archiving a menu section with a supplier' do
      expect(Suppliers::Cache::CategoryGroups).to receive(:new).with(suppliers: [supplier])

      archiver = MenuSections::Archive.new(menu_section: menu_section_with_supplier).call
      expect(archiver).to be_success
    end

    it 'does not request to update the supplier has_<category_group> flags when archiving a menu section with no supplier' do
      expect(Suppliers::Cache::CategoryGroups).to_not receive(:new)

      archiver = MenuSections::Archive.new(menu_section: menu_section).call
      expect(archiver).to be_success
    end
  end

  describe 'errors' do
    it 'cannot archive a missing menu section' do
      archiver = MenuSections::Archive.new(menu_section: nil).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archive a missing Menu Section')
    end

    it 'cannot archive already archived menu section' do
      menu_section.update_column(:archived_at, Time.zone.now)

      archiver = MenuSections::Archive.new(menu_section: menu_section).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archvie an already archived Menu Section')
    end

    context 'with menu items' do
      let!(:menu_item1) { create(:menu_item, :random, menu_section: menu_section, supplier_profile: menu_section.supplier_profile) }
      let!(:menu_item2) { create(:menu_item, :random, menu_section: menu_section, supplier_profile: menu_section.supplier_profile, archived_at: Time.zone.now) }

      it 'cannot archive menu section with any un-archived menu items' do
        archiver = MenuSections::Archive.new(menu_section: menu_section).call
        expect(archiver).to_not be_success
        expect(archiver.errors).to include('Menu Sections can only be archived/removed after archiving all associated menu items')
      end

      it 'can force archive menu section along with its un-archived menu items' do
        archiver = MenuSections::Archive.new(menu_section: menu_section, is_forced: true).call
        expect(archiver).to be_success
        expect(menu_section.reload.archived_at).to be_present
        expect(menu_item1.reload.archived_at).to be_present
      end

      it 'can archive a menu section if all the menu items are archived' do
        menu_item1.update_column(:archived_at, Time.zone.now)

        archiver = MenuSections::Archive.new(menu_section: menu_section).call
        expect(archiver).to be_success
        expect(menu_section.reload.archived_at).to be_present
      end
    end
  end

end
