require 'rails_helper'

RSpec.describe MenuSections::List, type: :service, menu_sections: true do
  subject { MenuSections::List }

  let(:supplier1) { create(:supplier_profile, :random) }
  let(:supplier2) { create(:supplier_profile, :random) }
  let(:supplier3) { create(:supplier_profile, :random) }

  let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1, is_hidden: true) }
  let!(:menu_section12) { create(:menu_section, :random, supplier_profile: supplier1, archived_at: Time.now) }
  let!(:menu_section13) { create(:menu_section, :random, supplier_profile: supplier1, name: 'custom') }

  let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2, name: 'cusTOM', archived_at: Time.now) }
  let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2, name: 'Customer') }
  let!(:menu_section23) { create(:menu_section, :random, supplier_profile: supplier2, is_hidden: true) }

  let!(:menu_section31) { create(:menu_section, :random, supplier_profile: supplier3) }
  let!(:menu_section32) { create(:menu_section, :random, supplier_profile: supplier3, name: 'Custom', is_hidden: true) }
  let!(:menu_section33) { create(:menu_section, :random, supplier_profile: supplier3, archived_at: Time.now) }

  it 'lists all menu sections by default' do
    menu_sections = subject.new.call

    expect(menu_sections).to include(menu_section11, menu_section12, menu_section13, menu_section21, menu_section22, menu_section23, menu_section31, menu_section32, menu_section33)
  end

  it 'lists menu sections for a single supplier' do
    lister_options = { supplier: supplier1 }
    menu_sections = subject.new(options: lister_options).call

    expect(menu_sections).to include(menu_section11, menu_section12, menu_section13)
    expect(menu_sections).to_not include(menu_section21, menu_section22, menu_section23, menu_section31, menu_section32, menu_section33)
  end

  it 'lists menu sections for a multiple suppliers' do
    lister_options = { suppliers: [supplier1, supplier3] }
    menu_sections = subject.new(options: lister_options).call

    expect(menu_sections).to include(menu_section11, menu_section12, menu_section13, menu_section31, menu_section32, menu_section33)
    expect(menu_sections).to_not include(menu_section21, menu_section22, menu_section23)
  end

  it 'lists only visible (non hidden) menu sections' do
    lister_options = { show_visible: true }
    menu_sections = subject.new(options: lister_options).call

    expect(menu_sections).to include(menu_section12, menu_section13, menu_section21, menu_section22, menu_section31, menu_section33)
    expect(menu_sections).to_not include(menu_section11, menu_section23, menu_section32)
  end

  it 'lists only active (non archived) menu sections' do
    lister_options = { show_active: true }
    menu_sections = subject.new(options: lister_options).call

    expect(menu_sections).to include(menu_section11, menu_section13, menu_section22, menu_section23, menu_section31, menu_section32)
    expect(menu_sections).to_not include(menu_section12, menu_section21, menu_section33)
  end

  it 'does not lists custom menu sections if specified to ignore' do
    lister_options = { ignore_custom: true }
    menu_sections = subject.new(options: lister_options).call

    expect(menu_sections).to include(menu_section11, menu_section12, menu_section22, menu_section23, menu_section31, menu_section33)
    expect(menu_sections).to_not include(menu_section13, menu_section21, menu_section32)
  end

  context 'with categories' do
    let!(:catering_category) { create(:category, :random, group: 'catering-services') }
    let!(:home_delivery_category) { create(:category, :random, group: 'home-deliveries') }

    before do
      menu_section11.categories = [catering_category]
      menu_section21.categories = [catering_category]
      menu_section31.categories = [catering_category]

      menu_section12.categories = [catering_category, home_delivery_category]
      menu_section22.categories = [home_delivery_category]
      menu_section32.categories = [home_delivery_category, catering_category]
    end

    it 'only lists menu sections who belong to the home-delivery categories if is_home_delivery is true' do
      lister_options = { is_home_delivery: true }
      menu_sections = subject.new(options: lister_options).call

      expect(menu_sections).to include(menu_section12, menu_section22, menu_section32)
      expect(menu_sections).to_not include(menu_section11, menu_section13, menu_section21, menu_section23, menu_section31, menu_section33)
    end

    it 'lists menu sections who belong to atleast 1 non-home deliveries categories if is_home_delivery is false' do
      lister_options = { is_home_delivery: false }
      lister_options = {} if [true, false].sample # is_home_delivery is false by default
      menu_sections = subject.new(options: lister_options).call

      expect(menu_sections).to include(menu_section11, menu_section21, menu_section31)
      expect(menu_sections).to include(menu_section12, menu_section32)
      expect(menu_sections).to_not include(menu_section22)
    end

    it 'lists menu sections who belong to no categories if is_home_delivery is false' do
      lister_options = { is_home_delivery: false }
      lister_options = {} if [true, false].sample # is_home_delivery is false by default
      menu_sections = subject.new(options: lister_options).call

      expect(menu_sections).to include(menu_section13, menu_section23, menu_section33)
    end

  end

  context 'with customer menu section visibility (via a company)' do
    let(:company1) { create(:company, :random) }
    let(:company2) { create(:company, :random) }
    let(:customer1) { create(:customer_profile, :random, company: company1) }
    let(:customer2) { create(:customer_profile, :random, company: company2) }

    before do
       menu_section12.companies = [company2]
       menu_section21.companies = [company2]
       menu_section33.companies = [company2]
    end

    it 'lists menu sections if can be viewed by the customer' do
      lister_options = { profile: customer2 }
      menu_sections = subject.new(options: lister_options).call

      expect(menu_sections).to include(menu_section11, menu_section12, menu_section13, menu_section21, menu_section22, menu_section23, menu_section31, menu_section32, menu_section33)
    end

    it 'does not list menu sections that cannot be viewed by the customer' do
      lister_options = { profile: customer1 }
      menu_sections = subject.new(options: lister_options).call

      expect(menu_sections).to include(menu_section11, menu_section13, menu_section22, menu_section23, menu_section31, menu_section32)
      expect(menu_sections).to_not include(menu_section12, menu_section21, menu_section33)
    end

    it 'does not list the menu sections with a company scope if profile is not passed' do
      lister_options = { profile: nil }
      menu_sections = subject.new(options: lister_options).call

      expect(menu_sections).to include(menu_section11, menu_section13, menu_section22, menu_section23, menu_section31, menu_section32)
      expect(menu_sections).to_not include(menu_section12, menu_section21, menu_section33)
    end

    it 'lists all menu sections if the passed in profile is the same as the supplier' do
      lister_options = { supplier: supplier1, profile: supplier1 }
      menu_sections = subject.new(options: lister_options).call

      expect(menu_sections).to include(menu_section11, menu_section12, menu_section13)
    end
  end
end
