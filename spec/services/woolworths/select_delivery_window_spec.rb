require 'rails_helper'

RSpec.describe Woolworths::SelectDeliveryWindow, type: :service, woolworths: true do
  describe '.execute' do
    let(:available_12th_delivery_windows) do
      [
        { windowid: 1, selected: false, available: true, starttime: '2019-06-12T06:00:00', endtime: '2019-06-12T13:00:00', status: 'Available', promoprice: 10, price: 10 },
        { windowid: 2, selected: false, available: true, starttime: '2019-06-12T09:00:00', endtime: '2019-06-12T12:00:00', status: 'Available', promoprice: 10, price: 10 },
        { windowid: 3, selected: false, available: true, starttime: '2019-06-12T10:00:00', endtime: '2019-06-12T13:00:00', status: 'Available', promoprice: 10, price: 10 },
        { windowid: 4, selected: false, available: true, starttime: '2019-06-12T13:00:00', endtime: '2019-06-12T16:00:00', status: 'Available', promoprice: 10, price: 10 },
        { windowid: 5, selected: false, available: true, starttime: '2019-06-12T15:00:00', endtime: '2019-06-12T18:00:00', status: 'Available', promoprice: 10, price: 10 }
      ].map do |delivery_window|
        Woolworths::DeliveryWindow.new(
          id: delivery_window[:windowid],
          date: Date.parse(delivery_window[:starttime]),
          time_range: (Time.zone.parse(delivery_window[:starttime])..Time.zone.parse(delivery_window[:endtime]))
        )
      end
    end

    let(:available_14th_delivery_windows) do
      [
        { windowid: 8, selected: false, available: true, starttime: '2019-06-14T09:00:00', endtime: '2019-06-14T12:00:00', status: 'Available', promoprice: 10, price: 10 },
        { windowid: 9, selected: false, available: true, starttime: '2019-06-14T10:00:00', endtime: '2019-06-14T13:00:00', status: 'Available', promoprice: 10, price: 10 },
        { windowid: 10, selected: false, available: true, starttime: '2019-06-14T14:00:00', endtime: '2019-06-14T16:00:00', status: 'Available', promoprice: 10, price: 10 },
        { windowid: 11, selected: false, available: true, starttime: '2019-06-14T15:00:00', endtime: '2019-06-14T18:00:00', status: 'Available', promoprice: 10, price: 10 }
      ].map do |delivery_window|
        Woolworths::DeliveryWindow.new(
          id: delivery_window[:windowid],
          date: Date.parse(delivery_window[:starttime]),
          time_range: (Time.zone.parse(delivery_window[:starttime])..Time.zone.parse(delivery_window[:endtime]))
        )
      end
    end

    context 'with available delivery dates' do
      it 'returns the delivery window when the delivery at overlaps a delivery window (and starts after 7am)' do
        delivery_at = Time.zone.parse('2019-06-12 12:10:00')
        selected_delivery_window = Woolworths::SelectDeliveryWindow.new(delivery_at: delivery_at, delivery_windows: available_12th_delivery_windows).call
        expect(selected_delivery_window.id).to_not eq(1) # even if it overlaps it starts before Woolworths::SelectDeliveryWindow::MORNING_THRESHOLD_HOUR (7am)
        expect(selected_delivery_window.id).to eq(3)

        delivery_at = Time.zone.parse('2019-06-14 12:10:00')
        selected_delivery_window = Woolworths::SelectDeliveryWindow.new(delivery_at: delivery_at, delivery_windows: available_14th_delivery_windows).call
        expect(selected_delivery_window.id).to eq(9)
      end

      it 'returns the next available window when the delivery at does not overlap any delivery windows' do
        delivery_at = Time.zone.parse('2019-06-14 13:30:00')
        selected_delivery_window = Woolworths::SelectDeliveryWindow.new(delivery_at: delivery_at, delivery_windows: available_14th_delivery_windows).call
        expect(selected_delivery_window.id).to eq(10)
      end
    end

    describe 'Errors' do
      it 'raises an error when the delivery windows are not available' do
        delivery_at = Time.zone.parse('2019-06-13 12:10:00')
        delivery_windows = [nil, []].sample
        expect{ Woolworths::SelectDeliveryWindow.new(delivery_at: delivery_at, delivery_windows: delivery_windows).call }.to raise_error(Woolworths::SelectDeliveryWindow::NoDeliveryWindowError)
      end

      it 'raises an error when the delivery at is not specified' do
        delivery_at = nil
        delivery_windows = [available_12th_delivery_windows, available_14th_delivery_windows].sample
        expect{ Woolworths::SelectDeliveryWindow.new(delivery_at: delivery_at, delivery_windows: delivery_windows).call }.to raise_error(Woolworths::SelectDeliveryWindow::NoDeliveryWindowError)
      end

      it 'raises an error if it cannot find any delivery window after the delivery at' do
        delivery_at = Time.zone.parse('2019-06-14 19:10:00')
        delivery_windows = [available_12th_delivery_windows, available_14th_delivery_windows].sample
        expect{ Woolworths::SelectDeliveryWindow.new(delivery_at: delivery_at, delivery_windows: delivery_windows).call }.to raise_error(Woolworths::SelectDeliveryWindow::NoDeliveryWindowError)
      end
    end
  end
end
