require 'rails_helper'

RSpec.describe Woolworths::Cleanup::RemoveSavedAddresses, type: :service, woolworths: true do

  let!(:woolworths_account1) { create(:woolworths_account, :random) }
  let!(:woolworths_account2) { create(:woolworths_account, :random) }
  let!(:woolworths_connection) { double(Woolworths::API::Connection) }

  let!(:address_lister_response) do
    [
      {
        id: 13_400_111,
        text: 'Level 2, 411 Collins St,  3000',
        isprimary: false,
        postalcode: '3000',
        street1: 'Level 2, 411 Collins St',
        street2: '',
        suburbid: 4539,
        suburbname: 'MELBOURNE',
        ispartner: false,
        partnerbranchid: nil
      },
      {
        id: 13_493_496,
        text: '263 Clarence St,  2000',
        isprimary: false,
        postalcode: '2000',
        street1: '263 Clarence St',
        street2: '',
        suburbid: 1,
        suburbname: 'SYDNEY',
        ispartner: false,
        partnerbranchid: nil
      }
    ]
  end

  let!(:address_deleter_response) { true }

  before do
    allow(Woolworths::API::Connection).to receive(:new).and_return(woolworths_connection)

    address_lister = double(Woolworths::API::ListAddresses)
    allow(Woolworths::API::ListAddresses).to receive(:new).and_return(address_lister)
    allow(address_lister).to receive(:call).and_return(address_lister_response)

    address_deleter = double(Woolworths::API::DeleteAddressById)
    allow(Woolworths::API::DeleteAddressById).to receive(:new).and_return(address_deleter)
    allow(address_deleter).to receive(:call).and_return(address_deleter_response)
  end

  context 'with valid address address_lister_response' do
    context 'with valid address deleter response' do
      it 'requests the addresses to be listed per account' do
        expect(Woolworths::API::ListAddresses).to receive(:new).with(account: woolworths_account1, connection: woolworths_connection)
        expect(Woolworths::API::ListAddresses).to receive(:new).with(account: woolworths_account2, connection: woolworths_connection)

        address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call
        expect(address_remover).to be_success
      end

      it 'requests the deletion of each address within the account' do
        expect(Woolworths::API::DeleteAddressById).to receive(:new).with(account: woolworths_account1, connection: woolworths_connection, address_id: 13_400_111)
        expect(Woolworths::API::DeleteAddressById).to receive(:new).with(account: woolworths_account1, connection: woolworths_connection, address_id: 13_493_496)

        expect(Woolworths::API::DeleteAddressById).to receive(:new).with(account: woolworths_account2, connection: woolworths_connection, address_id: 13_400_111)
        expect(Woolworths::API::DeleteAddressById).to receive(:new).with(account: woolworths_account2, connection: woolworths_connection, address_id: 13_493_496)

        address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call
        expect(address_remover).to be_success
      end

      it 'deletes the addresses from the available accounts' do
        address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call

        expect(address_remover).to be_success
        expect(address_remover.accounts.size).to eq(2)
        expect(address_remover.accounts).to include(woolworths_account1, woolworths_account2)
      end

      it 'deletes the addresses as listed' do
        address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call

        expect(address_remover).to be_success
        expect(address_remover.deleted_addresses.size).to eq(4)
        expect(address_remover.deleted_addresses).to include(*address_lister_response)
      end

      context 'with an already attached woolworths account' do
        let!(:woolworths_order1) { create(:woolworths_order, :random, account: woolworths_account1, account_in_use: true) }
        let!(:woolworths_order2) { create(:woolworths_order, :random, account: woolworths_account2, account_in_use: false) }

        it 'only deletes the addresses from the available accounts' do
          address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call

          expect(address_remover).to be_success
          expect(address_remover.accounts.size).to eq(1)
          expect(address_remover.accounts).to_not include(woolworths_account1)
          expect(address_remover.accounts).to include(woolworths_account2)
        end

        it 'deletes the addresses as listed only from available accounts' do
          address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call

          expect(address_remover).to be_success
          expect(address_remover.deleted_addresses.size).to eq(2)
          expect(address_remover.deleted_addresses).to include(*address_lister_response)
        end
      end

      context 'with a primary address' do
        let!(:address_lister_response) do
          [
            {
              id: 13_400_111,
              text: 'Level 2, 411 Collins St,  3000',
              isprimary: true, # this address is marked as primary in Woolworths
              postalcode: '3000',
              street1: 'Level 2, 411 Collins St',
              street2: '',
              suburbid: 4539,
              suburbname: 'MELBOURNE',
              ispartner: false,
              partnerbranchid: nil
            },
            {
              id: 13_493_496,
              text: '263 Clarence St,  2000',
              isprimary: false,
              postalcode: '2000',
              street1: '263 Clarence St',
              street2: '',
              suburbid: 1,
              suburbname: 'SYDNEY',
              ispartner: false,
              partnerbranchid: nil
            }
          ]
        end

        it 'does not request the deletion of primary address within the account' do
          expect(Woolworths::API::DeleteAddressById).to_not receive(:new).with(account: woolworths_account1, connection: woolworths_connection, address_id: 13_400_111)
          expect(Woolworths::API::DeleteAddressById).to receive(:new).with(account: woolworths_account1, connection: woolworths_connection, address_id: 13_493_496)

          expect(Woolworths::API::DeleteAddressById).to_not receive(:new).with(account: woolworths_account2, connection: woolworths_connection, address_id: 13_400_111)
          expect(Woolworths::API::DeleteAddressById).to receive(:new).with(account: woolworths_account2, connection: woolworths_connection, address_id: 13_493_496)

          address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call
          expect(address_remover).to be_success
        end
      end
    end

    context 'with invalid address deleter response' do
      let!(:error_message) { "Could not delete the address with ID #{rand(1..10)}" }

      before do
        address_deleter = double(Woolworths::API::DeleteAddressById)
        allow(Woolworths::API::DeleteAddressById).to receive(:new).and_return(address_deleter)
        allow(address_deleter).to receive(:call).and_raise(RuntimeError.new(error_message))
      end

      it 'requests the deletion of each address within the account' do
        address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call
        expect(address_remover).to_not be_success
        error_messages = (2 * 2).times.map do |_| # each address for each account
          error_message
        end
        expect(address_remover.errors).to match_array(error_messages)
      end
    end
  end

  context 'with invalid address lister response' do
    let!(:error_message) { 'Could not retrieve saved addresses' }

    before do
      address_lister = double(Woolworths::API::ListAddresses)
      allow(Woolworths::API::ListAddresses).to receive(:new).and_return(address_lister)
      allow(address_lister).to receive(:call).and_raise(RuntimeError.new(error_message))
    end

    it 'requests the deletion of each address within the account' do
      address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new.call
      expect(address_remover).to_not be_success

      error_messages = 2.times.map do |_| # for each account
        error_message
      end
      expect(address_remover.errors).to match_array(error_messages)
    end
  end

end
