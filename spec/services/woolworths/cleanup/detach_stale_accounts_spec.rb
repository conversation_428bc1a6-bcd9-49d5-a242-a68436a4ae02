require 'rails_helper'

RSpec.describe Woolworths::Cleanup::DetachStaleAccounts, type: :service, woolworths: true do

  subject { Woolworths::Cleanup::DetachStaleAccounts.new(time: now).call }

  let(:account1) { create(:woolworths_account, :random) }
  let(:account2) { create(:woolworths_account, :random) }
  let(:account3) { create(:woolworths_account, :random) }
  let(:account4) { create(:woolworths_account, :random) }
  let(:account5) { create(:woolworths_account, :random) }

  let!(:order1) { create(:order, :amended) }
  let!(:order2) { create(:order, :amended) }
  let!(:order3) { create(:order, :amended) }
  let!(:order4) { create(:order, :amended) }
  let!(:order5) { create(:order, :amended) }

  let!(:woolworths_order1) { create(:woolworths_order, order: order1, account_in_use: true, account: account1) }
  let!(:woolworths_order2) { create(:woolworths_order, order: order2, account_in_use: true, account: account2) }
  let!(:woolworths_order3) { create(:woolworths_order, order: order3, account_in_use: true, account: account3) }
  let!(:woolworths_order4) { create(:woolworths_order, order: order4, account_in_use: true, account: account4) }
  let!(:woolworths_order5) { create(:woolworths_order, order: order5, account_in_use: true, account: account5) }

  let(:now) { Time.zone.now }
  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier) }
  let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier) }
  let!(:order_line51) { create(:order_line, :random, order: order5, supplier_profile: supplier) }

  before do
    order1.update_column(:updated_at, now - 2.days - 3.hours) # 2 days old
    order2.update_column(:updated_at, now - 1.day) # day old
    order3.update_column(:updated_at, now - 2.days) # 2 days old
    order4.update_column(:updated_at, now + 1.day) # future
    order5.update_column(:updated_at, now - 1.day - 2.hours) # more than a day old
  end

  it 'detaches the accounts from orders older than 2 days' do
    detachable_orders = subject.stale_orders
    detached_orders = detachable_orders.map(&:order).compact

    expect(detached_orders).to include(order1.reload, order3.reload)
    expect(account1).to be_available
    expect(account3).to be_available
  end

  it 'removes the order lines from draft orders older than 2 days' do
    order1.update_column(:status, :draft)
    order3.update_column(:status, :draft)
    subject

    expect(order1.reload.order_lines).to be_blank
    expect(order3.reload.order_lines).to be_blank
  end

  it 'detaches accounts from orders older than 1 day with no order lines' do
    detachable_orders = subject.stale_orders
    detached_orders = detachable_orders.map(&:order).compact
    expect(detached_orders).to include(order2.reload)
    expect(account2).to be_available

    # 1 day old order with order lines
    expect(detached_orders).to_not include(order5.reload)
    expect(account5).to_not be_available
    expect(order5.reload.order_lines).to_not be_blank
  end

  context 'DRY RUN' do
    subject { Woolworths::Cleanup::DetachStaleAccounts.new(time: now).call(dry_run: true) }

    it 'does not detach any accounts' do
      detachable_orders = subject.stale_orders
      detached_orders = detachable_orders.map(&:order).compact

      expect(detached_orders).to include(order1, order2, order3)
      expect(account1).to_not be_available
      expect(account2).to_not be_available
      expect(account3).to_not be_available
    end

    it 'does not destroy any order lines' do
      subject

      expect(order1.reload.order_lines).to_not be_blank
      expect(order3.reload.order_lines).to_not be_blank
    end
  end
end
