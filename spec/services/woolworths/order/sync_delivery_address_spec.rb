require 'rails_helper'

RSpec.describe Woolworths::Order::SyncDeliveryAddress, type: :service, orders: true, woolworths: true do

  let(:order) { create(:order, :draft) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account_in_use: true, account: woolworths_account) }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }
  let(:woolworths_account) { build(:woolworths_account, :random) }

  let!(:woolworths_address_getter) { double(Woolworths::API::GetAddress) }
  let!(:woolworths_delivery_address_id) { 59_193 }

  before do
    allow(Woolworths::API::Connection).to receive(:new).and_return(woolworths_connection)

    # mock address getter
    allow(Woolworths::API::GetAddress).to receive(:new).and_return(woolworths_address_getter)
    allow(woolworths_address_getter).to receive(:call).and_return(nil)

    # mock address adder
    address_adder = double(Woolworths::API::AddAddress)
    allow(Woolworths::API::AddAddress).to receive(:new).and_return(address_adder)
    allow(address_adder).to receive(:call).and_return({ id: woolworths_delivery_address_id })

    # mock address setter
    address_setter = double(Woolworths::API::SetDeliveryAddress)
    allow(Woolworths::API::SetDeliveryAddress).to receive(:new).and_return(address_setter)
    allow(address_setter).to receive(:call).and_return(true)
  end

  # context 'With no delivery_address_id set within Woolworths::Order and Address Getter returning nil' # clean slate

  it 'request request to check if an address exists in the Woolworths Account' do
    expect(Woolworths::API::GetAddress).to receive(:new).with(connection: woolworths_connection, street_1: order.delivery_address, suburb: order.delivery_suburb.name, postcode: order.delivery_suburb.postcode)

    address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call
    expect(address_syncer).to be_success
  end

  it 'requests a new address to be added to the connected Woolworths Account' do
    expect(Woolworths::API::AddAddress).to receive(:new).with(connection: woolworths_connection, street_1: order.delivery_address, suburb: order.delivery_suburb.name, postcode: order.delivery_suburb.postcode)

    address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call
    expect(address_syncer).to be_success
  end

  it 'sanitizes the address before adding it' do
    order.update_columns(delivery_address_level: ' L 5 ', delivery_address: ' 1 High Street ')    
    expect(Woolworths::API::AddAddress).to receive(:new).with(connection: woolworths_connection, street_1: 'L 5, 1 High Street', suburb: order.delivery_suburb.name, postcode: order.delivery_suburb.postcode)

    address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call
    expect(address_syncer).to be_success
  end

  it 'requests the new added delivery address to be set within Woolworths (making it primary)' do
    expect(Woolworths::API::SetDeliveryAddress).to receive(:new).with(connection: woolworths_connection, address_id: woolworths_delivery_address_id)

    address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call
    expect(address_syncer).to be_success
  end

  it 'sets the delivery_address_id in Woolworths Order and returns it' do
    address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call

    expect(address_syncer).to be_success
    expect(address_syncer.delivery_address_id).to eq(woolworths_delivery_address_id)
    expect(woolworths_order.reload.delivery_address_id).to eq(woolworths_delivery_address_id)
  end

  it 'returns errors on validation while syncing delivery address' do
    expect(Woolworths::API::AddAddress).to receive(:new).with(connection: woolworths_connection, street_1: order.delivery_address, suburb: order.delivery_suburb.name, postcode: order.delivery_suburb.postcode)
    expect(Woolworths::API::SetDeliveryAddress).to receive(:new).with(connection: woolworths_connection, address_id: woolworths_delivery_address_id)

    address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order, validate: true).call

    expect(address_syncer).to_not be_success
    expect(address_syncer.errors).to include(Woolworths::Order::SyncDeliveryAddress::DELIVERY_ADDRESS_SYNC_ERROR)

    expect(address_syncer.delivery_address_id).to eq(woolworths_delivery_address_id)
    expect(woolworths_order.reload.delivery_address_id).to eq(woolworths_delivery_address_id)
  end

  context 'with an existing address in Woolworths' do
    let(:address_in_woolworths) do
      {
        id: 13_092,
        text: order.delivery_address_arr.join(', '),
        isprimary: true,
        postalcode: order.delivery_suburb.postcode,
        street1: order.delivery_address,
        street2: '',
        suburbid: 1,
        suburbname: order.delivery_suburb.name,
        ispartner: false,
        partnerbranchid: nil
      }
    end

    before do
      # mock address getter
      allow(woolworths_address_getter).to receive(:call).and_return(address_in_woolworths)
    end

    it 'request to set the address in Woolies and sets it in the Woolworths Order and returns it' do
      expect(Woolworths::API::SetDeliveryAddress).to receive(:new).with(connection: woolworths_connection, address_id: address_in_woolworths[:id])

      address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call

      expect(address_syncer).to be_success
      expect(address_syncer.delivery_address_id).to eq(address_in_woolworths[:id])
      expect(woolworths_order.reload.delivery_address_id).to eq(address_in_woolworths[:id])
    end

    it 'does not make a request to add the address' do
      expect(Woolworths::API::AddAddress).to_not receive(:new)

      address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call

      expect(address_syncer).to be_success
    end

    it 'returns errors on validation while syncing the delivery address' do
      expect(Woolworths::API::SetDeliveryAddress).to receive(:new).with(connection: woolworths_connection, address_id: address_in_woolworths[:id])

      address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order, validate: true).call

      expect(address_syncer).to_not be_success
      expect(address_syncer.errors).to include(Woolworths::Order::SyncDeliveryAddress::DELIVERY_ADDRESS_SYNC_ERROR)

      expect(address_syncer.delivery_address_id).to eq(address_in_woolworths[:id])
      expect(woolworths_order.reload.delivery_address_id).to eq(address_in_woolworths[:id])
    end

    context 'with an existing saved delivery_address_id in Woolworths::Order' do
      before do
        woolworths_order.update_column(:delivery_address_id, address_in_woolworths[:id])
      end

      it 'doesn\'t do anything and returns the delivery address id (even if validating)' do
        expect(Woolworths::API::AddAddress).to_not receive(:new)
        expect(Woolworths::API::SetDeliveryAddress).to_not receive(:new)

        address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order, validate: [true, false].sample).call

        expect(address_syncer).to be_success
        expect(address_syncer.delivery_address_id).to eq(woolworths_order.delivery_address_id)
        expect(address_syncer.delivery_address_id).to eq(address_in_woolworths[:id])
      end

      context 'with mismatched delivery address id' do
        let!(:mismatched_delivery_id) { 30_921 }

        before do
          woolworths_order.update_column(:delivery_address_id, mismatched_delivery_id)
        end

        it 'resets the delivery address id in Woolworths Order and syncs it with Woolworths' do
          expect(Woolworths::API::SetDeliveryAddress).to receive(:new).with(connection: woolworths_connection, address_id: address_in_woolworths[:id])

          address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call

          expect(address_syncer).to be_success
          expect(address_syncer.delivery_address_id).to eq(address_in_woolworths[:id])

          woolworths_order.reload
          expect(woolworths_order.delivery_address_id).to_not eq(mismatched_delivery_id)
          expect(woolworths_order.delivery_address_id).to eq(address_in_woolworths[:id])
        end

        it 'returns errors on validation while resyncing delivery_address_id' do
          expect(Woolworths::API::SetDeliveryAddress).to receive(:new).with(connection: woolworths_connection, address_id: address_in_woolworths[:id])

          address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order, validate: true).call

          expect(address_syncer).to_not be_success
          expect(address_syncer.errors).to include(Woolworths::Order::SyncDeliveryAddress::DELIVERY_ADDRESS_SYNC_ERROR)

          expect(address_syncer.delivery_address_id).to eq(address_in_woolworths[:id])

          woolworths_order.reload
          expect(woolworths_order.delivery_address_id).to_not eq(mismatched_delivery_id)
          expect(woolworths_order.delivery_address_id).to eq(address_in_woolworths[:id])
        end
      end # mismatched delivery address ID

      context 'with the fetched address not being primary' do
        let!(:non_primary_address_in_woolworths) do
          address_in_woolworths.merge({ isprimary: false })
        end

        before do
          allow(woolworths_address_getter).to receive(:call).and_return(non_primary_address_in_woolworths)
        end

        it 'requests to set the Delivery Address in Woolworths (to make it primary)' do
          expect(Woolworths::API::SetDeliveryAddress).to receive(:new).with(connection: woolworths_connection, address_id: address_in_woolworths[:id])

          address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order).call

          expect(address_syncer).to be_success
          expect(address_syncer.delivery_address_id).to eq(non_primary_address_in_woolworths[:id])
          expect(woolworths_order.reload.delivery_address_id).to eq(non_primary_address_in_woolworths[:id])
        end

        it 'returns errors on validation while syncing Delivery Address in Woolworths (to make it primary)' do
          expect(Woolworths::API::SetDeliveryAddress).to receive(:new).with(connection: woolworths_connection, address_id: address_in_woolworths[:id])

          address_syncer = Woolworths::Order::SyncDeliveryAddress.new(order: order, validate: true).call

          expect(address_syncer).to_not be_success
          expect(address_syncer.errors).to include(Woolworths::Order::SyncDeliveryAddress::DELIVERY_ADDRESS_SYNC_ERROR)

          expect(address_syncer.delivery_address_id).to eq(non_primary_address_in_woolworths[:id])
          expect(woolworths_order.reload.delivery_address_id).to eq(non_primary_address_in_woolworths[:id])
        end
      end # non primary address fetched
    end # with an existing saved delivery_address_id
  end # with a valid fetched address from Woolies
end