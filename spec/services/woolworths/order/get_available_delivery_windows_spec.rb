require 'rails_helper'

RSpec.describe Woolworths::Order::GetAvailableDeliveryWindows, type: :service, woolworths: true do
  let(:woolworths_connection) { double(Woolworths::API::Connection) }
  subject { Woolworths::Order::GetAvailableDeliveryWindows.new(order: order, connection: woolworths_connection).call }

  let(:delivery_windows) do
    {
      windows: [
        {
          selected: false,
          date: '2019-06-12T00:00:00',
          available: true,
          slots: [
            { windowid: 1, selected: false, available: true, starttime: '2019-06-12T09:00:00', endtime: '2019-06-12T12:00:00', status: 'Available', promoprice: 10, price: 10 },
            { windowid: 2, selected: false, available: true, starttime: '2019-06-12T10:00:00', endtime: '2019-06-12T13:00:00', status: 'Available', promoprice: 10, price: 10 },
            { windowid: 3, selected: false, available: false, starttime: '2019-06-12T11:00:00', endtime: '2019-06-12T14:00:00', status: 'Full', promoprice: 10, price: 10 },
            { windowid: 4, selected: false, available: true, starttime: '2019-06-12T13:00:00', endtime: '2019-06-12T16:00:00', status: 'Available', promoprice: 10, price: 10 },
            { windowid: 5, selected: false, available: true, starttime: '2019-06-12T15:00:00', endtime: '2019-06-12T18:00:00', status: 'Available', promoprice: 10, price: 10 }
          ]
        },
        {
          selected: false,
          date: '2019-06-13T00:00:00',
          available: false,
          slots: [
            { windowid: 6, selected: false, available: false, starttime: '2019-06-13T07:00:00', endtime: '2019-06-13T10:00:00', status: 'Full', promoprice: 10, price: 10 },
            { windowid: 7, selected: false, available: false, starttime: '2019-06-13T14:00:00', endtime: '2019-06-13T19:00:00', status: 'Full', promoprice: 10, price: 10 }
          ]
        },
        {
          selected: false,
          date: '2019-06-14T00:00:00',
          available: true,
          slots: [
            { windowid: 8, selected: false, available: true, starttime: '2019-06-14T09:00:00', endtime: '2019-06-14T12:00:00', status: 'Available', promoprice: 10, price: 10 },
            { windowid: 9, selected: false, available: true, starttime: '2019-06-14T10:00:00', endtime: '2019-06-14T13:00:00', status: 'Available', promoprice: 10, price: 10 },
            { windowid: 10, selected: false, available: false, starttime: '2019-06-14T11:00:00', endtime: '2019-06-14T13:00:00', status: 'Full', promoprice: 10, price: 10 },
            { windowid: 11, selected: false, available: true, starttime: '2019-06-14T14:00:00', endtime: '2019-06-14T16:00:00', status: 'Available', promoprice: 10, price: 10 },
            { windowid: 12, selected: false, available: true, starttime: '2019-06-14T15:00:00', endtime: '2019-06-14T18:00:00', status: 'Available', promoprice: 10, price: 10 }
          ]
        }
      ]
    }
  end

  before do
    window_getter = double(Woolworths::API::GetDeliveryWindows)
    allow(Woolworths::API::GetDeliveryWindows).to receive(:new).with(connection: woolworths_connection).and_return(window_getter)
    allow(window_getter).to receive(:call).and_return(delivery_windows)
  end

  context 'for an order with delivery date with no delivery windows' do
    let(:order) { double(Order, id: 'blah', delivery_at: Time.zone.local(2019, 6, 13, 15)) }

    it 'raises and error' do
      expect{ subject }.to raise_error(Woolworths::Order::GetAvailableDeliveryWindows::NoAvailabeDeliveryWindowsError)
    end

  end

  context 'for an order with delivery date with some delivery windows' do
    let(:order) { double(Order, id: 'blah', delivery_at: Time.zone.local(2019, 6, 14, 15)) }

    it 'returns only the available windows' do
      available_delivery_windows = subject

      expect(available_delivery_windows.first).to be_a(Woolworths::DeliveryWindow)
      expect(available_delivery_windows.map(&:id)).to include(8, 9, 11, 12)
      expect(available_delivery_windows.map(&:id)).to_not include(1, 2, 3, 4, 5, 6, 7, 10)
      expect(available_delivery_windows.map(&:date)).to include(order.delivery_at.to_date)
    end

    it 'caches all the available delivery windows' do
      subject

      cache_key = Woolworths::API::DELIVERY_WINDOWS_CACHE_KEY.sub(':id', order.id.to_s)
      cached_delivery_windows = Rails.cache.fetch(cache_key)

      expect(cached_delivery_windows.map(&:id)).to include(1, 2, 4, 5, 8, 9, 11, 12)
      expect(cached_delivery_windows.map(&:id)).to_not include(3, 6, 7, 10)
      expect(cached_delivery_windows.map(&:date)).to include(Date.parse('2019-06-14T00:00:00'), Date.parse('2019-06-12T00:00:00'))
      expect(cached_delivery_windows.map(&:date)).to_not include(Date.parse('2019-06-13T00:00:00'))
    end

  end
end
