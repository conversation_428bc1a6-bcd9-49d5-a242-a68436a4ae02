require 'rails_helper'

RSpec.describe Woolworths::Order::SyncTrolleyProduct, type: :service, woolworths: true do
  let!(:order) { create(:order, :random) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }

  let!(:menu_item1) { create(:menu_item, :random) }
  let!(:menu_item2) { create(:menu_item, :random) }

  let!(:order_line11) { create(:order_line, :random, order: order, menu_item: menu_item1, quantity: 5) }
  let!(:order_line21) { create(:order_line, :random, order: order, menu_item: menu_item2, quantity: 5) }
  let!(:order_line22) { create(:order_line, :random, order: order, menu_item: menu_item2, quantity: 3) }

  context 'Sync event ended' do
    before do
      order.reload
      expect(order.synced_woolworths_order_lines).to be_empty
    end

    it 'adds the order line from synced_order_lines' do
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line11).call(sync_event: :end)
      expect(order.synced_woolworths_order_lines).to include(order_line11)
    end

    it 'adds multiple order lines of the same menu item from synced_order_lines' do
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line21).call(sync_event: :end)
      expect(order.synced_woolworths_order_lines).to include(order_line21, order_line22)
    end

    it 'does not add order lines which do not exist' do
      order_line22.destroy
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line21).call(sync_event: :end)
      expect(order.synced_woolworths_order_lines).to include(order_line21)
      expect(order.synced_woolworths_order_lines).to_not include(order_line22)
    end

    it 'adds any error if passed in' do
      errors = ["#{order_line21.menu_item.name} does not exist"]
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line21).call(sync_event: :end, errors: errors)
      order.reload
      expect(order.synced_woolworths_order_lines).to include(order_line21, order_line22)
      expect(order.woolworths_products.map(&:trolley_errors)).to match_array([errors, errors]) # adds errors to all order lines
    end
  end

  context 'Sync event started' do
    let!(:woolworths_trolley_product1) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line11, synced: true) }
    let!(:woolworths_trolley_product2) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line21, synced: true, trolley_errors: ['Some error1']) }
    let!(:woolworths_trolley_product3) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line22, synced: true, trolley_errors: ['Some error2']) }

    before do
      order.reload
      expect(order.reload.synced_woolworths_order_lines).to include(order_line11, order_line21, order_line22)
    end

    it 'removes the order line from synced_order_lines' do
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line11).call(sync_event: :start)

      expect(order.woolworths_products.map(&:order_line)).to include(order_line11)
      expect(order.reload.synced_woolworths_order_lines).to_not include(order_line11)
    end

    it 'removes multiple order lines of the same menu item from synced_order_lines' do
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line21).call(sync_event: :start)

      expect(order.woolworths_products.map(&:order_line)).to include(order_line21, order_line22)
      expect(order.reload.synced_woolworths_order_lines).to_not include(order_line21, order_line22)
    end

    it 'removes the errors for the specified order lines' do
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line21).call(sync_event: :start)

      expect(order.woolworths_products.map(&:order_line)).to include(order_line21, order_line22)
      expect(order.woolworths_products.map(&:trolley_errors).flatten).to be_blank
    end

    it 'creates a new entry for an new order line' do
      order_line12 = create(:order_line, :random, order: order, menu_item: menu_item1, quantity: 3)
      expect(order.synced_woolworths_order_lines).to_not include(order_line12)
      expect(order.woolworths_products.map(&:order_line)).to_not include(order_line12)

      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line12).call(sync_event: :start)
      order.reload
      expect(order.synced_woolworths_order_lines).to_not include(order_line12)
      expect(order.woolworths_products.map(&:order_line)).to include(order_line12)
    end

  end

  context 'Sync event remove (for deleted items)' do
    let!(:woolworths_trolley_product1) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line11, synced: true) }
    let!(:woolworths_trolley_product2) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line21, synced: true) }
    let!(:woolworths_trolley_product3) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line22, synced: true) }

    before do
      order.reload
      expect(order.synced_woolworths_order_lines).to include(order_line11, order_line21, order_line22)
    end

    it 'removes the order line from synced_order_lines' do
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line11).call(sync_event: :remove)
      expect(order.synced_woolworths_order_lines).to_not include(order_line11)
    end

    it 'removes multiple order lines of the same menu item from synced_order_lines' do
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line21).call(sync_event: :remove)
      expect(order.synced_woolworths_order_lines).to_not include(order_line21, order_line22)
    end

    it 'removes order line ids even if they don\'t exist' do
      order_line11.destroy
      Woolworths::Order::SyncTrolleyProduct.new(order: order, order_line: order_line11).call(sync_event: :remove)
      expect(order.synced_woolworths_order_lines).to_not include(order_line11)
    end
  end

end
