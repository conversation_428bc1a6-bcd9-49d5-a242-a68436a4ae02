require 'rails_helper'

RSpec.describe Woolworths::Order::Validate, type: :service, woolworths: true do

  let(:order) { create(:order, :draft, customer_subtotal: 100) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account_in_use: true, account: woolworths_account, delivery_address_id: 26_147_547, delivery_window_id: 48_054, delivery_fee: 10) }
  let!(:order_line1) { create(:order_line, :random, order: order) }
  let!(:order_line2) { create(:order_line, :random, order: order) }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }
  let(:woolworths_account) { build(:woolworths_account, :random) }

  let!(:delivery_window_text) { 'Monday between 5:00am and 8:00am' }
  let!(:order_sync_checker) { double(Woolworths::CheckOrderSync) }
  let!(:delivery_address_syncer) { double(Woolworths::Order::SyncDeliveryAddress) }
  let!(:delivery_window_setter) { double(Woolworths::API::SetDeliveryWindow) }
  let!(:delivery_window_selector) { double(Woolworths::Order::SetBestAvailableDeliveryWindow) }
  let!(:woolworths_order_validator) { double(Woolworths::API::ValidateOrder) }

  before do
    allow(Woolworths::API::Connection).to receive(:new).and_return(woolworths_connection)

    # mock delivery address syncer / validater
    allow(Woolworths::Order::SyncDeliveryAddress).to receive(:new).and_return(delivery_address_syncer)
    successful_response = OpenStruct.new(success?: true, delivery_address_id: woolworths_order.delivery_address_id)
    allow(delivery_address_syncer).to receive(:call).and_return(successful_response)

    # mock order_line_syncer
    allow(Woolworths::CheckOrderSync).to receive(:new).and_return(order_sync_checker)
    allow(order_sync_checker).to receive(:call).and_return(true)

    # mock delivery window setter
    allow(Woolworths::API::SetDeliveryWindow).to receive(:new).and_return(delivery_window_setter)
    allow(delivery_window_setter).to receive(:call).and_return(delivery_window_text)

    # mock delivery window selector
    allow(Woolworths::Order::SetBestAvailableDeliveryWindow).to receive(:new).and_return(delivery_window_selector)
    allow(delivery_window_selector).to receive(:call).and_return("#{delivery_window_text} RESET")

    # mock Woolworths Order API Validator
    allow(Woolworths::API::ValidateOrder).to receive(:new).and_return(woolworths_order_validator)
    valid_woolworths_api_order = OpenStruct.new(success?: true)
    allow(woolworths_order_validator).to receive(:call).and_return(valid_woolworths_api_order)

    # mock totals calculator
    totals_calculator = double(Orders::CalculateCustomerTotals)
    allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(totals_calculator)
    allow(totals_calculator).to receive(:call).and_return(true)

    # mock trolley syncer
    trolley_snycer = double(Woolworths::Order::SyncTrolleyProduct)
    allow(Woolworths::Order::SyncTrolleyProduct).to receive(:new).and_return(trolley_snycer)
    allow(trolley_snycer).to receive(:call).and_return(true)

    # mock orderline process
    order_line_processor = delayed_order_line_processor = double(Woolworths::ProcessOrderLine)
    allow(Woolworths::ProcessOrderLine).to receive(:new).and_return(order_line_processor)
    allow(order_line_processor).to receive(:delay).and_return(delayed_order_line_processor)
    allow(delayed_order_line_processor).to receive(:call).and_return(true)
  end

  it 'returns successfully as a valid order' do
    order_validator = Woolworths::Order::Validate.new(order: order).call

    expect(order_validator).to be_success
  end

  it 'makes request to check order line sync' do
    expect(Woolworths::CheckOrderSync).to receive(:new).with(order: order)

    order_validator = Woolworths::Order::Validate.new(order: order).call
    expect(order_validator).to be_success
  end

  context 'Address Check' do
    it 'requests validation of the delivery address' do
      expect(Woolworths::Order::SyncDeliveryAddress).to receive(:new).with(order: order, woolworths_order: woolworths_order, connection: woolworths_connection, validate: true)

      order_validator = Woolworths::Order::Validate.new(order: order).call
      expect(order_validator).to be_success
    end

    context 'with delivery address errors' do
      before do
        unsuccessful_response = OpenStruct.new(success?: false, errors: ['delivery-address-error-1', 'delivery-address-error-2'])
        allow(delivery_address_syncer).to receive(:call).and_return(unsuccessful_response)
      end

      it 'returns with delvery_address_validation errors' do
        order_validator = Woolworths::Order::Validate.new(order: order).call

        expect(order_validator).to_not be_success
        expect(order_validator.errors[:details]).to include('delivery-address-error-1', 'delivery-address-error-2')
      end
    end
  end # Address check

  it 'makes request to set the delivery window' do
    expect(Woolworths::API::SetDeliveryWindow).to receive(:new).with(order: order, connection: woolworths_connection)

    order_validator = Woolworths::Order::Validate.new(order: order).call
    expect(order_validator).to be_success
    expect(order_validator.delivery_window_text).to eq(delivery_window_text)
  end

  context 'when API validation returns a delivery fee' do
    let!(:woolworths_delivery_fee) { woolworths_order.delivery_fee + 10 }

    before do
      valid_woolworths_api_order_with_delivery_fee = OpenStruct.new(success?: true, delivery_fee: woolworths_delivery_fee)
      allow(woolworths_order_validator).to receive(:call).and_return(valid_woolworths_api_order_with_delivery_fee)
    end

    it 'updates the woolworths order\'s delivery fee' do
      order_validator = Woolworths::Order::Validate.new(order: order).call

      expect(order_validator).to be_success
      expect(woolworths_order.reload.delivery_fee).to eq(woolworths_delivery_fee)
    end

    it 'recalculates the order totals' do
      expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, save_totals: true)

      order_validator = Woolworths::Order::Validate.new(order: order).call
      expect(order_validator).to be_success
    end
  end

  context 'with Order Validation errors' do
    context 'when the Woolworths account is no-longer in use (stale order)' do
      before do
        woolworths_order.update_column(:account_in_use, false)
      end

      it 'returns with account errors' do
        order_validator = Woolworths::Order::Validate.new(order: order).call

        expect(order_validator).to_not be_success
        expect(order_validator.errors[:order]).to include(Woolworths::Order::Validate::ACCOUNT_NOT_IN_USE_ERROR)
        # We seem to have lost connection with Woolworths. Please start a fresh order by clearing cart
      end
    end

    context 'with unsynced order lines' do
      before do
        allow(order_sync_checker).to receive(:call).and_return(false)
      end

      it 'returns with order line sync errors' do
        order_validator = Woolworths::Order::Validate.new(order: order).call

        expect(order_validator).to_not be_success
        expect(order_validator.errors[:order]).to include(Woolworths::Order::Validate::ORDER_LINE_SYNC_ERROR)
        # It\'s taking some time for your products to be synced with Woolworths; please wait a few seconds and validate again
      end
    end

    context 'delivery window errors' do
      it 'returns with errors if the order delivery at is missing' do
        order.update_column(:delivery_at, nil)
        order_validator = Woolworths::Order::Validate.new(order: order).call

        expect(order_validator).to_not be_success
        expect(order_validator.errors[:details]).to include(Woolworths::Order::Validate::WINDOW_NOT_SET_ERROR) # Window has not been set for this checkout
      end

      it 'returns with errors if the woolworths order delivery window id is missing' do
        woolworths_order.update_column(:delivery_window_id, nil)
        order_validator = Woolworths::Order::Validate.new(order: order).call

        expect(order_validator).to_not be_success
        expect(order_validator.errors[:details]).to include(Woolworths::Order::Validate::WINDOW_NOT_SET_ERROR) # Window has not been set for this checkout
      end

      context 'with setting a delivery window raises an error' do
        before do
          # mock raised error
          allow(delivery_window_setter).to receive(:call).and_raise(Woolworths::API::SetDeliveryWindow::DeliveryWindowError.new)
        end

        it 'makes a request to set the next best available delivery window and returns a notice as error' do
          expect(Woolworths::Order::SetBestAvailableDeliveryWindow).to receive(:new).with(order: order, connection: woolworths_connection)

          order_validator = Woolworths::Order::Validate.new(order: order).call

          expect(order_validator).to_not be_success
          expect(order_validator.delivery_window_text).to eq("#{delivery_window_text} RESET")
          expect(order_validator.errors[:details]).to include('The previously selected delivery window is no-longer available! We\'ve selected the next available one, please review before proceeding')
        end

        context 'when setting the next best available delivery window fails' do
          before do
            # mock raised error
            allow(delivery_window_selector).to receive(:call).and_raise(RuntimeError.new)
          end

          it 'returns with an error' do
            order_validator = Woolworths::Order::Validate.new(order: order).call

            expect(order_validator).to_not be_success
            expect(order_validator.errors[:details]).to include('We had some troubles setting your delivery window please try a different date')
          end
        end # SetBestAvailableDeliveryWindow raised error
      end # SetDeliveryWindow raised error
    end # delivery window errors
  end # validation errors

  context 'API validation passed but has Trolley errors (in case if quantity limits)' do
    let!(:synced_trolley_product1) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line1, synced: true, trolley_errors: ["Only #{order_line1.quantity - 1} items available."]) }
    let!(:synced_trolley_product2) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line2, synced: true, trolley_errors: ["Only #{order_line1.quantity - 2} items available."]) }

    it 'returns with an error' do
      order_validator = Woolworths::Order::Validate.new(order: order).call

      expect(order_validator).to_not be_success
      expect(order_validator.errors[:order]).to include('Order Items have Trolley errors!')
    end
  end

  context 'with API validation errors' do
    let!(:invalid_response) do
      OpenStruct.new(
        success?: false,
        errors: [
          'MinimumSpend',
          'Checkout Order has unavailable items.',
          'Woolworths API Order error 1.',
          'Woolworths API Order error 2.'
        ]
      )
    end

    before do
      allow(woolworths_order_validator).to receive(:call).and_return(invalid_response)
    end

    it 'returns the errors as is' do
      order_validator = Woolworths::Order::Validate.new(order: order).call

      expect(order_validator).to_not be_success
      expect(order_validator.errors[:order]).to include('Woolworths API Order error 1.', 'Woolworths API Order error 2.')
    end

    it 'returns the sanitized MinimumSpend error' do
      order_validator = Woolworths::Order::Validate.new(order: order).call

      expect(order_validator).to_not be_success
      expect(order_validator.errors[:order]).to_not include('MinimumSpend')
      expect(order_validator.errors[:order]).to include("The subtotal of $#{order.customer_subtotal.to_i}.00 is less than the Delivery minimum spend of $#{Woolworths::Order::Validate::DELIVERY_THRESHOLD.to_i}.00.")
    end

    context 'with unavailable items' do
      it 'does not return the unavailable items error' do
        order_validator = Woolworths::Order::Validate.new(order: order).call

        expect(order_validator).to_not be_success
        expect(order_validator.errors[:order]).to_not include('Checkout Order has unavailable items.')
      end

      it 're-syncs the order lines' do
        expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new).with(order: order, order_line: order_line1)
        expect(Woolworths::ProcessOrderLine).to receive(:new).with(order_line: order_line1)

        expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new).with(order: order, order_line: order_line2)
        expect(Woolworths::ProcessOrderLine).to receive(:new).with(order_line: order_line2)

        order_validator = Woolworths::Order::Validate.new(order: order).call
        expect(order_validator).to_not be_success
        expect(order_validator.errors[:order]).to_not include('Checkout Order has unavailable items.')
        expect(order_validator.errors[:order]).to include('Checkout Order has unavailable items - we are currently trying to re-sync the items.')
      end

      context 'with trolley errors' do
        let!(:synced_trolley_product1) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line1, synced: true, trolley_errors: ['Item1 is unavailable; please remove it from your order']) }
        let!(:synced_trolley_product2) { create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line2, synced: true, trolley_errors: ['Item2 is unavailable; please remove it from your order']) }

        it 'returns with generic unavailable items errors ad not the trolley errors' do
          order_validator = Woolworths::Order::Validate.new(order: order).call

          expect(order_validator).to_not be_success
          expect(order_validator.errors[:order]).to include('Checkout Order has unavailable items.')
          expect(order_validator.errors[:order]).to_not include('Item1 is unavailable; please remove it from your order', 'Item2 is unavailable; please remove it from your order')
        end

        context 'with no items available error' do
          let!(:invalid_response) do
            OpenStruct.new(
              success?: false,
              errors: [
                'MinimumSpend',
                'Checkout has no available product items.', # No available items error
                'Checkout Order has unavailable items.',
                'Woolworths API Order error 1.',
                'Woolworths API Order error 2.'
              ]
            )
          end

          before do
            allow(woolworths_order_validator).to receive(:call).and_return(invalid_response)
          end

          it 'returns with on the no available product items error' do
            order_validator = Woolworths::Order::Validate.new(order: order).call

            expect(order_validator).to_not be_success
            expect(order_validator.errors[:order]).to include('Checkout has no available product items.')
            expect(order_validator.errors[:order]).to_not include('Checkout Order has unavailable items.')
          end
        end

        it 'does not try to rsync the order lines' do
          expect(Woolworths::Order::SyncTrolleyProduct).to_not receive(:new)
          expect(Woolworths::ProcessOrderLine).to_not receive(:new)

          order_validator = Woolworths::Order::Validate.new(order: order).call
          expect(order_validator).to_not be_success
        end
      end
    end
  end

end
