require 'rails_helper'

RSpec.describe Woolworths::Order::AttachToOrder, type: :service, woolworths: true do
  subject { Woolworths::Order::AttachToOrder.new(order: order).call }

  let!(:woolworths_account) { create(:woolworths_account, :random) }
  let(:order) { create(:order, :random) }

  it 'connects the Yordar order to Woolworths Order with (an available) Woolworths Account' do
    expect(order.woolworths_order).to be_blank

    subject

    woolworths_order = order.reload.woolworths_order
    expect(woolworths_order).to be_present
    expect(woolworths_order.account).to eq(woolworths_account)
    expect(woolworths_order.account_in_use).to be_truthy
  end

  it 'raises an error if no woolworths account is available' do
    new_order = create(:order, :random)
    create(:woolworths_order, order: new_order, account: woolworths_account, account_in_use: true)

    expect{ subject }.to raise_error(Woolworths::Order::AttachToOrder::NoAccountsAvailableError)
  end

  it 'connects the Yordar order to Woolworths Order even if account is connected to another order but not in use' do
    new_order = create(:order, :random)
    create(:woolworths_order, order: new_order, account: woolworths_account, account_in_use: false)

    subject

    woolworths_order = order.reload.woolworths_order
    expect(woolworths_order).to be_present
    expect(woolworths_order.account).to eq(woolworths_account)
  end
end
