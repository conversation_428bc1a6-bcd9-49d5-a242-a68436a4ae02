require 'rails_helper'

RSpec.describe Woolworths::Order::SetBestAvailableDeliveryWindow, type: :service, woolworths: true do

  let(:order) { create(:order, :draft) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account_in_use: true, delivery_window_id: 48_054) }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  let!(:delivery_window_text) { 'Monday between 5:00am and 8:00am' }
  let!(:delivery_window_setter) { double(Woolworths::API::SetDeliveryWindow) }

  let!(:available_delivery_windows) do
    [
      OpenStruct.new(
        id: rand(10_000),
        time_range: OpenStruct.new(
          first: Time.zone.parse('2022-10-31 12:00:00'),
          last: Time.zone.parse('2022-10-31 13:00:00')
        )
      ),
      OpenStruct.new(
        id: rand(10_000),
        time_range: OpenStruct.new(
          first: Time.zone.parse('2022-10-31 15:00:00'),
          last: Time.zone.parse('2022-10-31 16:00:00')
        )
      )
    ]
  end

  let!(:selected_delivery_window) { available_delivery_windows.sample }

  before do
    # mock delivery windows getter
    delivery_windows_getter = double(Woolworths::Order::GetAvailableDeliveryWindows)
    allow(Woolworths::Order::GetAvailableDeliveryWindows).to receive(:new).and_return(delivery_windows_getter)
    allow(delivery_windows_getter).to receive(:call).and_return(available_delivery_windows)

    # mock delivery windows selector
    delivery_window_selector = Woolworths::SelectDeliveryWindow
    allow(Woolworths::SelectDeliveryWindow).to receive(:new).and_return(delivery_window_selector)
    allow(delivery_window_selector).to receive(:call).and_return(selected_delivery_window)

    # mock delivery window setter
    allow(Woolworths::API::SetDeliveryWindow).to receive(:new).and_return(delivery_window_setter)
    allow(delivery_window_setter).to receive(:call).and_return(delivery_window_text)
  end

  it 'makes a request to get available delivery windows' do
    expect(Woolworths::Order::GetAvailableDeliveryWindows).to receive(:new).with(order: order, connection: woolworths_connection)

    Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: woolworths_connection).call
  end

  it 'makes a request to select an available delivery window' do
    expect(Woolworths::SelectDeliveryWindow).to receive(:new).with(delivery_at: order.delivery_at, delivery_windows: available_delivery_windows)

    Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: woolworths_connection).call
  end

  it 'makes a request to set the delivery window via Woolworths API' do
    expect(Woolworths::API::SetDeliveryWindow).to receive(:new).with(order: order, connection: woolworths_connection, window_id: selected_delivery_window.id)

    Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: woolworths_connection).call
  end

  it 'saves the delivery window against the wooloworths_order' do
    Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: woolworths_connection).call

    expect(woolworths_order.reload.delivery_window_id).to eq(selected_delivery_window.id)
  end

  it 'saves the order\'s delivery datetime to the last of the selected delivery windows time range' do
    Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: woolworths_connection).call

    expect(order.reload.delivery_at).to eq(selected_delivery_window.time_range.last)
  end

  it 'returns the delivery window text' do
    set_delivery_window_text = Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: woolworths_connection).call

    expect(set_delivery_window_text).to eq(delivery_window_text)
  end

  context 'if Woolworths::API::SetDeliveryWindow raises an error' do
    let!(:delivery_window_error) { Woolworths::API::SetDeliveryWindow::DeliveryWindowError.new('Could not set the delivery window') }

    before do
      allow(delivery_window_setter).to receive(:call).and_raise(delivery_window_error)
    end

    it 'raises the delivery window error' do
      window_setter = Woolworths::Order::SetBestAvailableDeliveryWindow.new(order: order, connection: woolworths_connection)

      expect{ window_setter.call }.to raise_error(delivery_window_error)
    end
  end

end
