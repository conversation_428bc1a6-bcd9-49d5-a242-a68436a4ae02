require 'rails_helper'

RSpec.describe Woolworths::Order::SyncOrderLines, type: :service, orders: true, woolworths: true do

  let!(:order) { create(:order, :draft) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }

  let!(:woolworths_supplier) { create(:supplier_profile, :random) }
  let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: woolworths_supplier) }
  let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: woolworths_supplier) }

  let!(:trolley_syncer) { double(Woolworths::Order::SyncTrolleyProduct) }

  before do
    # mock woolworths supplier
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(woolworths_supplier.id)

    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

    # mock setting up trolley sync
    allow(Woolworths::Order::SyncTrolleyProduct).to receive(:new).and_return(trolley_syncer)
    allow(trolley_syncer).to receive(:call).and_return(true)

    # mock order line processing
    order_line_processor = delayed_order_line_processor = double(Woolworths::ProcessOrderLine)
    allow(Woolworths::ProcessOrderLine).to receive(:new).and_return(order_line_processor)
    allow(order_line_processor).to receive(:delay).and_return(delayed_order_line_processor)
    allow(delayed_order_line_processor).to receive(:call).and_return(true)
  end

  it 'makes a request to start syncing the trolley product' do
    expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new).with(order: order, order_line: order_line1)
    expect(trolley_syncer).to receive(:call).with(sync_event: :start)

    expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new).with(order: order, order_line: order_line2)
    expect(trolley_syncer).to receive(:call).with(sync_event: :start)

    order_lines_syncer = Woolworths::Order::SyncOrderLines.new(order: order).call
    expect(order_lines_syncer).to be_success
  end

  it 'syncs all the order Woolworths order lines' do
    order_lines_syncer = Woolworths::Order::SyncOrderLines.new(order: order).call

    expect(order_lines_syncer).to be_success
    expect(order_lines_syncer.syncable_order_lines).to include(order_line1, order_line2)
  end

  it 'only syncs passed in order lines' do
    expect(Woolworths::Order::SyncTrolleyProduct).to_not receive(:new).with(order: order, order_line: order_line1)
    expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new).with(order: order, order_line: order_line2)

    order_lines_syncer = Woolworths::Order::SyncOrderLines.new(order: order, order_lines: [order_line2]).call
    expect(order_lines_syncer).to be_success
    expect(order_lines_syncer.syncable_order_lines).to include(order_line2)
    expect(order_lines_syncer.syncable_order_lines).to_not include(order_line1)
  end

  context 'errors' do
    it 'cannot sync if the order is missing' do
      order_lines_syncer = Woolworths::Order::SyncOrderLines.new(order: nil).call

      expect(order_lines_syncer).to_not be_success
      expect(order_lines_syncer.errors).to include('Order is missing')
    end

    it 'cannot sync if the order is not a Woolworths order' do
      if [true, false].sample
        new_order = create(:order, :draft)
      else
        woolworths_order.destroy
        new_order = order.reload
      end
      order_lines_syncer = Woolworths::Order::SyncOrderLines.new(order: new_order).call

      expect(order_lines_syncer).to_not be_success
      expect(order_lines_syncer.errors).to include('Cannot sync a non Woolworths order\'s lines')
    end

    it 'cannot sync if the order is no longer draft' do
      order.update_column(:status, %w[new amended confirmed delivered cancelled skipped].sample)
      order_lines_syncer = Woolworths::Order::SyncOrderLines.new(order: order).call

      expect(order_lines_syncer).to_not be_success
      expect(order_lines_syncer.errors).to include('Order is no longer draft')
    end

    it 'cannot sync if the order does not contain any Woolworths order lines' do
      non_woolworths_supplier = create(:supplier_profile, :random)
      [order_line1, order_line2].each do |order_line|
        order_line.update_column(:supplier_profile_id, non_woolworths_supplier.id)
      end
      order_lines_syncer = Woolworths::Order::SyncOrderLines.new(order: order).call

      expect(order_lines_syncer).to_not be_success
      expect(order_lines_syncer.errors).to include('Order does not contain any Woolworths items')
    end
  end

end