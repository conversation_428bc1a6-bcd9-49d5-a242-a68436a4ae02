require 'rails_helper'

RSpec.describe Woolworths::Order::FetchSavedDeliveryWindows, type: :service, woolworths: true do
  let!(:delivery_date) { Date.today }
  let!(:delivery_windows) do
    [
      {
        id: SecureRandom.hex(7),
        label: 'delivery-window-1-1',
        date: delivery_date,
      },
      {
        id: SecureRandom.hex(7),
        label: 'delivery-window-1-2',
        date: delivery_date,
      },
      {
        id: SecureRandom.hex(7),
        label: 'delivery-window-2-1',
        date: delivery_date + 2.days,
      },
      {
        id: SecureRandom.hex(7),
        label: 'delivery-window-3-1',
        date: delivery_date + 3.days,
      },
    ]
  end

  let!(:order) { create(:order, :draft, delivery_at: delivery_date.to_time + 10.hours) }

  let!(:memory_store) { double('memory_store') }

  before do
    # mock Rails cache read
    allow(Rails).to receive(:cache).and_return(memory_store)
    allow(memory_store).to receive(:read).and_return(delivery_windows.map{|window| OpenStruct.new(window) })
  end

  it 'makes a request to get the cached delivery windows for the passed in order' do
    expect(memory_store).to receive(:read).with(Woolworths::API::DELIVERY_WINDOWS_CACHE_KEY.sub(':id', order.id.to_s))

    delivery_windows_fetcher = Woolworths::Order::FetchSavedDeliveryWindows.new(order: order).call
  end

  it 'returns the grouped delivery windows' do
    delivery_windows_fetcher = Woolworths::Order::FetchSavedDeliveryWindows.new(order: order).call

    fetched_delivery_windows = delivery_windows_fetcher.delivery_windows
    expect(fetched_delivery_windows).to be_present

    fetched_dates = fetched_delivery_windows.keys
    expect(fetched_dates).to include(delivery_date, delivery_date + 2.days, delivery_date + 3.days)
  end

  context 'active delivery date' do
    it 'returns the active date as the one similar to the orders delivery date' do
      delivery_windows_fetcher = Woolworths::Order::FetchSavedDeliveryWindows.new(order: order).call

      active_delivery_date = delivery_windows_fetcher.active_date
      expect(active_delivery_date).to eq(order.delivery_at.to_date)
    end

    it 'returns the active date as one ahead of delivery date if present' do
      order.update_column(:delivery_at, (delivery_date + 1.day).to_time + 10.hours)
      delivery_windows_fetcher = Woolworths::Order::FetchSavedDeliveryWindows.new(order: order).call

      active_delivery_date = delivery_windows_fetcher.active_date
      expect(active_delivery_date).to_not eq(order.delivery_at.to_date)
      expect(active_delivery_date).to eq(delivery_date + 2.days) # order.delivery_at.to_date + 1.day
    end

    it 'returns the active date as one behind of delivery date if present' do
      order.update_column(:delivery_at, (delivery_date + 4.days).to_time + 10.hours)
      delivery_windows_fetcher = Woolworths::Order::FetchSavedDeliveryWindows.new(order: order).call

      active_delivery_date = delivery_windows_fetcher.active_date
      expect(active_delivery_date).to_not eq(order.delivery_at.to_date)
      expect(active_delivery_date).to eq(delivery_date + 3.days) # order.delivery_at.to_date - 1.day
    end
  end

  context 'errors' do
    it 'returns without any delivery_windows if order is missing' do
      delivery_windows_fetcher = Woolworths::Order::FetchSavedDeliveryWindows.new(order: nil).call

      expect(delivery_windows_fetcher.delivery_windows).to be_blank
      expect(delivery_windows_fetcher.active_date).to be_nil
    end

    it 'returns without any delivery_windows if order is missing an id' do
      new_order = Order.new
      delivery_windows_fetcher = Woolworths::Order::FetchSavedDeliveryWindows.new(order: new_order).call

      expect(delivery_windows_fetcher.delivery_windows).to be_blank
      expect(delivery_windows_fetcher.active_date).to be_nil
    end
  end

end