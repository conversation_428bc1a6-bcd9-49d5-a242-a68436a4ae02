require 'rails_helper'

RSpec.describe Woolworths::Import::SyncProduct, type: :service, woolworths: true, imports: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:menu_section) { create(:menu_section, :random, supplier_profile: supplier) }
  let!(:availability_adder) { double(Woolworths::Import::AddAvailability) }

  let!(:product_to_import) do
    OpenStruct.new(
      sku: SecureRandom.hex(7),
      is_for_delivery: true,
      is_ranged: true,
      menu_section_id: menu_section.id,
      name: Faker::Name.name,
      description: Faker::Lorem.sentence,
      image: Faker::Internet.url,
      price: rand(10.11..30.22).round(2),
      promo_price: nil,
      is_gst_free: false,
      fulfilment_store_id: rand(2000...6000),
      stock_quantity: 36
    )
  end

  before do
    # mock availability adder (as success)
    allow(Woolworths::Import::AddAvailability).to receive(:new).and_return(availability_adder)
    successful_response = OpenStruct.new(success?: true, errors: [], warnings: [])
    allow(availability_adder).to receive(:call).and_return(successful_response)
  end

  it 'creates a new menu item for the supplier within the passed menu section' do
    product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call
    expect(product_syncer).to be_success

    synced_menu_item = product_syncer.synced_item
    expect(synced_menu_item).to be_present
    expect(synced_menu_item).to be_a(MenuItem)
    expect(synced_menu_item).to be_persisted

    expect(synced_menu_item.supplier_profile).to eq(supplier)
    expect(synced_menu_item.menu_section).to eq(menu_section)
  end

  it 'saves the item with the passed in information' do
    product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call
    expect(product_syncer).to be_success

    synced_menu_item = product_syncer.synced_item
    expect(synced_menu_item).to be_present
    expect(synced_menu_item.sku).to eq(product_to_import.sku)
    expect(synced_menu_item.name).to eq(product_to_import.name)
    expect(synced_menu_item.description).to eq(product_to_import.description)
    expect(synced_menu_item.price).to eq(product_to_import.price)
    expect(synced_menu_item.is_gst_free).to eq(product_to_import.is_gst_free)
  end

  context 'with promotional pricing' do
    it 'saves the promotional price along with the normal price if present' do
      promotional_product_to_import = product_to_import.dup
      promotional_product_to_import.promo_price = rand(0.11..10.32).round(2)
      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: promotional_product_to_import).call

      expect(product_syncer).to be_success
      synced_menu_item = product_syncer.synced_item
      expect(synced_menu_item).to be_present
      expect(synced_menu_item.price).to eq(promotional_product_to_import.price)
      expect(synced_menu_item.promo_price.to_s).to eq(promotional_product_to_import.promo_price.to_s)
    end

    it 'saves the promotional price as nil if the promotional price is not a value greater than 0' do
      promotional_product_to_import = product_to_import.dup
      promotional_product_to_import.promo_price = [nil, 0, 0.00, -1.02, '', false].sample
      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: promotional_product_to_import).call

      expect(product_syncer).to be_success
      synced_menu_item = product_syncer.synced_item
      expect(synced_menu_item).to be_present
      expect(synced_menu_item.price).to eq(promotional_product_to_import.price)
      expect(synced_menu_item.promo_price).to be_nil
    end
  end

  it 'makes a request to add store availability of the item', skip: 'No longer using store availaibilities' do
    expect(Woolworths::Import::AddAvailability).to receive(:new).with(menu_item: anything, mapped_product: product_to_import) # menu item is created

    product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call
    expect(product_syncer).to be_success
  end

  context 'for an existing menu item with same SKU' do
    let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier, sku: product_to_import.sku) }

    it 'updates the item with the passed in information' do
      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call

      expect(product_syncer).to be_success
      synced_menu_item = product_syncer.synced_item
      expect(synced_menu_item).to be_present
      expect(synced_menu_item.id).to eq(menu_item.id) # same product

      expect(synced_menu_item.sku).to eq(product_to_import.sku)
      expect(synced_menu_item.name).to eq(product_to_import.name)
      expect(synced_menu_item.description).to eq(product_to_import.description)
      expect(synced_menu_item.price).to eq(product_to_import.price)
      expect(synced_menu_item.is_gst_free).to eq(product_to_import.is_gst_free)
    end

    it 'doesn\'t update the image if it is already present and is stored in cloudinary' do
      menu_item.update_column(:image, "#{Faker::Internet.url}-cloudinary")

      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call

      expect(product_syncer).to be_success
      synced_menu_item = product_syncer.synced_item
      expect(synced_menu_item).to be_present
      expect(synced_menu_item.image).to_not eq(product_to_import.image)
    end

    it 'updates the image if it is already present but not stored in cloudinary' do
      menu_item.update_column(:image, Faker::Internet.url)

      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call

      expect(product_syncer).to be_success
      synced_menu_item = product_syncer.synced_item
      expect(synced_menu_item).to be_present
      expect(synced_menu_item.image).to eq(product_to_import.image)
    end

    it 'makes a request to add store availability of the item', skip: 'No longer using store availaibilities' do
      expect(Woolworths::Import::AddAvailability).to receive(:new).with(menu_item: menu_item, mapped_product: product_to_import)

      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call
      expect(product_syncer).to be_success
    end
  end
  context 'errors' do
    it 'errors if the supplier is missing' do
      product_syncer = Woolworths::Import::SyncProduct.new(supplier: nil, product_to_import: product_to_import).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.errors).to include('Cannot sync product without a supplier')
    end

    it 'errors if the mapped product (to import) is missing' do
      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: nil).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.errors).to include('Cannot sync product without a mapping')
    end

    it 'errors if the mapped product does not have an SKU' do
      product_to_import.sku = nil
      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.errors).to include('Cannot sync product without a mapped SKU')
    end

    it 'errors if the mapped product does not have a menu section id' do
      product_to_import.menu_section_id = nil
      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.errors).to include('Cannot sync product without a mapped menu section')
    end

    it 'fails and returns errors if store availability failed and errored', skip: 'No longer using store availaibilities' do
      errored_response = OpenStruct.new(success?: false, errors: ['availability-error'], warnings: [])
      allow(availability_adder).to receive(:call).and_return(errored_response)

      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.errors).to include(*errored_response.errors)
    end

    it 'fails and returns warnings if store availability failed returned with warnings', skip: 'No longer using store availaibilities' do
      warning_response = OpenStruct.new(success?: false, errors: [], warnings: ['availability-warning'])
      allow(availability_adder).to receive(:call).and_return(warning_response)

      product_syncer = Woolworths::Import::SyncProduct.new(supplier: supplier, product_to_import: product_to_import).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.warnings).to include(*warning_response.warnings)
    end
  end

end