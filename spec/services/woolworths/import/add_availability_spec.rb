require 'rails_helper'

RSpec.describe Woolworths::Import::AddAvailability, type: :service, woolworths: true, imports: true do

  let!(:store_id) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys.sample }
  let!(:menu_item) { create(:menu_item, :random) }

  let!(:mapped_product) do
    OpenStruct.new(
      fulfilment_store_id: store_id,
      stock_quantity: 20,
      is_ranged: true
      # , ...
    )
  end

  it 'creates a new Woolworths Store Availability for the menu item and store ID' do
    availability_adder = Woolworths::Import::AddAvailability.new(menu_item: menu_item, mapped_product: mapped_product).call
    expect(availability_adder).to be_success

    created_store_availability = availability_adder.store_availability
    expect(created_store_availability).to be_present
    expect(created_store_availability).to be_persisted
    expect(created_store_availability.menu_item).to eq(menu_item)
    expect(created_store_availability.store_id).to eq(mapped_product.fulfilment_store_id)
    expect(created_store_availability.stock_quantity).to eq(mapped_product.stock_quantity)
  end

  context 'errors' do
    it 'cannot add store availability if the item is missing' do
      availability_adder = Woolworths::Import::AddAvailability.new(menu_item: nil, mapped_product: mapped_product).call

      expect(availability_adder).to_not be_success
      expect(availability_adder.errors).to include('Cannot add store availability without an item')
    end

    it 'cannot add store availability if the fulfilment_store_id is missing' do
      mapped_product.fulfilment_store_id = nil
      availability_adder = Woolworths::Import::AddAvailability.new(menu_item: menu_item, mapped_product: mapped_product).call

      expect(availability_adder).to_not be_success
      expect(availability_adder.errors).to include('Cannot add store availability without a Store ID')
    end

    it 'cannot add store availability if the mapped product stock quantity is equal or less than zero(0)' do
      mapped_product.stock_quantity = [0, -1].sample
      availability_adder = Woolworths::Import::AddAvailability.new(menu_item: menu_item, mapped_product: mapped_product).call

      expect(availability_adder).to_not be_success
      expect(availability_adder.warnings).to include('Product does does not have stock left')
    end

    it 'cannot add store availability if the mapped product is not ranged' do
      mapped_product.is_ranged = false
      availability_adder = Woolworths::Import::AddAvailability.new(menu_item: menu_item, mapped_product: mapped_product).call

      expect(availability_adder).to_not be_success
      expect(availability_adder.warnings).to include('Product is not ranged')
    end
  end

end