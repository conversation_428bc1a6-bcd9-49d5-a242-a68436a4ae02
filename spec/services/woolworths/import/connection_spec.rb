require 'rails_helper'

RSpec.describe Woolworths::Import::Connection, type: :lib, woolworths: true, imports: true do

  subject { Woolworths::Import::Connection.new(store_id: store_id).call }

  let!(:woolworths_connection) { double(Woolworths::API::Connection) }
  let!(:store_id) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys.sample }

  let!(:fulfilment_store) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores[store_id] }

  let!(:store_address) do
    {
      street_1: fulfilment_store[:street_1],
      street_2: Faker::Name.name,
      suburb: fulfilment_store[:suburb],
      postcode: fulfilment_store[:postcode],
    }
  end

  let!(:address_getter) { double(Woolworths::API::GetAddress) }
  let!(:address_adder) { double(Woolworths::API::AddAddress) }
  let!(:windows_fetcher) { double(Woolworths::Order::GetAvailableDeliveryWindows) }
  let!(:delivery_windows) { [] }
  let!(:window_setter) { double(Woolworths::API::SetDeliveryWindow) }

  before do
    # mock woolworths connection
    allow(Woolworths::API::Connection).to receive(:new).and_return(woolworths_connection)
    allow(woolworths_connection).to receive(:authenticate).and_return(true)
    allow(woolworths_connection).to receive(:primary_address).and_return(store_address)
    allow(woolworths_connection).to receive(:fulfilment_store_id).and_return(store_id.to_s)

    # mock address Adder
    allow(Woolworths::API::AddAddress).to receive(:new).and_return(address_adder)
    allow(address_adder).to receive(:call).and_return(true)

    # mock windows fetcher
    allow(Woolworths::Order::GetAvailableDeliveryWindows).to receive(:new).and_return(windows_fetcher)
    allow(windows_fetcher).to receive(:call).and_return(delivery_windows)

    # mock window setter
    allow(Woolworths::API::SetDeliveryWindow).to receive(:new).and_return(window_setter)
    allow(window_setter).to receive(:call).and_return(true)    
  end

  it 'makes a request to create the connection with the importer account' do
    expect(Woolworths::API::Connection).to receive(:new).with(use_importer_account: true)

    subject
  end

  it 'makes a request to authenticate the connection' do
    expect(woolworths_connection).to receive(:authenticate)

    subject
  end

  context 'authenticated primary address is not similar to fulfilment_store address' do
    let!(:non_store_address) do
      {
        street_1: Faker::Name.name,
        street_2: Faker::Name.name,
        suburb: Faker::Name.name,
        postcode: rand(2000..6000),
      }
    end

    before do
      allow(woolworths_connection).to receive(:primary_address).and_return(non_store_address)
    end

    it 'makes a request to add the address id' do
      expect(Woolworths::API::AddAddress).to receive(:new).with({ connection: woolworths_connection }.merge(store_address.except(:street_2)))

      subject
    end

    it 'makes a request to re-authenticate the connection (to get the new fulfilment store ID))' do
      expect(woolworths_connection).to receive(:authenticate) # initial authentication
      expect(woolworths_connection).to receive(:authenticate) # re-authentication

      subject
    end
  end

  context 'the fulfilment store ID is not the same as the store ID' do
    before do
      allow(woolworths_connection).to receive(:fulfilment_store_id).and_return(rand(100..200))
    end

    it 'raises an error' do
      expect { subject }.to raise_error(RuntimeError, "Connection is not linked to the correct fulfilment store (ID ##{store_id})")
    end
  end

  it 'makes a request to fetch the delivery windows for a temp order with delivery date-time' do
    expect(Woolworths::Order::GetAvailableDeliveryWindows).to receive(:new).with(order: anything, connection: woolworths_connection) # order is a temp order

    subject
  end

  context 'Delivery Window Setter' do
    let!(:tommorrow) { Time.zone.now.beginning_of_day + 2.days }

    let!(:delivery_windows) do
      [
        Woolworths::DeliveryWindow.new(id: 111, date: tommorrow.to_date, time_range: (tommorrow + 5.hours)..(tommorrow + 7.hours), label: ' 5am - 7am'), # outside range
        Woolworths::DeliveryWindow.new(id: 222, date: tommorrow.to_date, time_range: (tommorrow + 15.hours)..(tommorrow + 18.hours), label: ' 3pm - 6pm'), # after 1pm
        Woolworths::DeliveryWindow.new(id: 333, date: tommorrow.to_date, time_range: (tommorrow + 12.hours)..(tommorrow + 14.hours), label: '12pm - 2pm'), # overlaps    
        Woolworths::DeliveryWindow.new(id: 444, date: tommorrow.to_date, time_range: (tommorrow + 13.hours)..(tommorrow + 15.hours), label: ' 1pm - 3pm') # starts at 1pm        
      ]
    end

    it 'makes a request to set the delivery windows for a temp order and the best delivery window (2 days from now 1pm - 3pm)' do
      expect(Woolworths::API::SetDeliveryWindow).to receive(:new).with(order: anything, connection: woolworths_connection, window_id: 444) # order is a temp order
      
      subject
    end

    it 'makes a request to set the delivery windows for a temp order and an overlaping delivery window if match (for begin) is not found' do
      changed_delivery_windows = delivery_windows.reject{|window| window.label == ' 1pm - 3pm'}
      allow(windows_fetcher).to receive(:call).and_return(changed_delivery_windows)

      expect(Woolworths::API::SetDeliveryWindow).to receive(:new).with(order: anything, connection: woolworths_connection, window_id: 333)
      
      subject
    end

    it 'makes a request to set the delivery windows for a temp order and delivery window that starts after 1pm, if an exact match or overlaping delivery zone is not found' do
      changed_delivery_windows = delivery_windows.reject{|window| [' 1pm - 3pm', '12pm - 2pm'].include?(window.label) }
      allow(windows_fetcher).to receive(:call).and_return(changed_delivery_windows)

      expect(Woolworths::API::SetDeliveryWindow).to receive(:new).with(order: anything, connection: woolworths_connection, window_id: 222)
      
      subject
    end
  end

end
