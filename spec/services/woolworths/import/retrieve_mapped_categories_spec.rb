require 'rails_helper'

RSpec.describe Woolworths::Import::RetrieveMappedCategories, type: :service, woolworths: true, imports: true do

  let!(:woolworths_connection) { double(Woolworths::API::Connection) }
  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:categories_fetcher) { double(Woolworths::Import::GetCategories) }

  let!(:woolworths_categories) do
    [
      Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish),
      Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish)
    ]
  end

  before do
    # mock Woolworths categories fetcher
    allow(Woolworths::Import::GetCategories).to receive(:new).and_return(categories_fetcher)
    allow(categories_fetcher).to receive(:call).and_return(woolworths_categories)
  end

  it 'requests to fetch the categories from Woolworths' do
    expect(Woolworths::Import::GetCategories).to receive(:new).with(connection: woolworths_connection)

    retrieved_categories = Woolworths::Import::RetrieveMappedCategories.new(connection: woolworths_connection, supplier: supplier).call
    expect(retrieved_categories).to be_present
  end

  it 'returns the fetched an array of `CategoryMapping`s' do
    retrieved_categories = Woolworths::Import::RetrieveMappedCategories.new(connection: woolworths_connection, supplier: supplier).call
    expect(retrieved_categories).to be_present

    expect(retrieved_categories.sample).to be_a(Woolworths::Import::RetrieveMappedCategories::CategoryMapping)
  end

  it 'returns the fetched Woolworths categories `WoolworthsCategory`' do
    retrieved_categories = Woolworths::Import::RetrieveMappedCategories.new(connection: woolworths_connection, supplier: supplier).call
    expect(retrieved_categories).to be_present

    expect(retrieved_categories.map(&:woolworths_category).sample).to be_a(Woolworths::Import::GetCategories::WoolworthsCategory)
    expect(retrieved_categories.map(&:woolworths_category)).to include(*woolworths_categories)
  end

  it 'creates and returns the menu sections per category' do
    expect(supplier.menu_sections.count).to eq(0)

    retrieved_categories = Woolworths::Import::RetrieveMappedCategories.new(connection: woolworths_connection, supplier: supplier).call
    expect(retrieved_categories).to be_present

    expect(retrieved_categories.sample).to be_a(Woolworths::Import::RetrieveMappedCategories::CategoryMapping)
    expect(retrieved_categories.map(&:woolworths_category)).to include(*woolworths_categories)

    expect(supplier.menu_sections.count).to eq(2)
    created_menu_sections = supplier.menu_sections.where(id: retrieved_categories.map(&:menu_section_id))
    expect(created_menu_sections.map(&:supplier_profile)).to include(supplier)
    expect(created_menu_sections.map(&:name)).to include(*woolworths_categories.map(&:category_name).map(&:titleize))
    expect(created_menu_sections.map(&:group_name)).to include(*woolworths_categories.map(&:aisle_name).map(&:titleize))
  end

  context 'exclusions' do
    let!(:excluded_aisle) { Rails.configuration.woolworths.category_mappings.exclusions[:aisles].map(&:downcase).sample }
    let!(:excluded_category) { Rails.configuration.woolworths.category_mappings.exclusions[:categories].map(&:downcase).sample }

    let!(:woolworths_categories_with_exclusions) do
      [
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: excluded_aisle, category_name: 'with-excluded-aisle'),
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: 'with-excluded-category', category_name: excluded_category),
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish)
      ]
    end

    before do
      allow(categories_fetcher).to receive(:call).and_return(woolworths_categories_with_exclusions)
    end

    it 'does not create (and return) menu sections for excluded aisles or categories' do
      expect(supplier.menu_sections.count).to eq(0)

      retrieved_categories = Woolworths::Import::RetrieveMappedCategories.new(connection: woolworths_connection, supplier: supplier).call
      expect(retrieved_categories).to be_present

      expect(retrieved_categories.sample).to be_a(Woolworths::Import::RetrieveMappedCategories::CategoryMapping)
      expect(retrieved_categories.map(&:woolworths_category)).to_not include(*woolworths_categories_with_exclusions[0..1])
      expect(retrieved_categories.map(&:woolworths_category)).to include(woolworths_categories_with_exclusions[2])

      expect(supplier.menu_sections.count).to eq(1)
      created_menu_sections = supplier.menu_sections.where(id: retrieved_categories.map(&:menu_section_id))

      expect(created_menu_sections.map(&:name)).to_not include('with-excluded-category')
      expect(created_menu_sections.map(&:name)).to include(woolworths_categories_with_exclusions[2].category_name.titleize)

      expect(created_menu_sections.map(&:group_name)).to_not include('with-excluded-aisle')
      expect(created_menu_sections.map(&:group_name)).to include(woolworths_categories_with_exclusions[2].aisle_name.titleize)
    end
  end

  context 'reimports' do
    let!(:reimportable_category) { Rails.configuration.woolworths.category_mappings.reimport[:categories].map(&:downcase).sample }

    let!(:woolworths_categories_with_reimports) do
      [
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: 'with-reimportable-category', category_name: reimportable_category),
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish)
      ]
    end

    before do
      allow(categories_fetcher).to receive(:call).and_return(woolworths_categories_with_reimports)
    end

    it 'does not creates (and returns) menu sections from the reimportable categories' do
      expect(supplier.menu_sections.count).to eq(0)

      retrieved_categories = Woolworths::Import::RetrieveMappedCategories.new(connection: woolworths_connection, supplier: supplier, is_reimport: true).call
      expect(retrieved_categories).to be_present

      expect(retrieved_categories.sample).to be_a(Woolworths::Import::RetrieveMappedCategories::CategoryMapping)
      expect(retrieved_categories.map(&:woolworths_category)).to include(woolworths_categories_with_reimports[0])
      expect(retrieved_categories.map(&:woolworths_category)).to_not include(woolworths_categories_with_reimports[1])

      expect(supplier.menu_sections.count).to eq(1)
      created_menu_sections = supplier.menu_sections.where(id: retrieved_categories.map(&:menu_section_id))

      expect(created_menu_sections.map(&:name)).to include(reimportable_category.titleize)
      expect(created_menu_sections.map(&:name)).to_not include(woolworths_categories_with_reimports[1].category_name)

      expect(created_menu_sections.map(&:group_name)).to include('with-reimportable-category'.titleize)
      expect(created_menu_sections.map(&:group_name)).to_not include(woolworths_categories_with_reimports[1].aisle_name)
    end
  end

  context 'with gst free categories' do
    let!(:gst_free_category) { Rails.configuration.woolworths.category_mappings.gst_free_categories.map(&:downcase).sample }

    let!(:woolworths_categories_with_gst) do
      [
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: 'with-gst-free-category', category_name: gst_free_category),
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish)
      ]
    end

    before do
      allow(categories_fetcher).to receive(:call).and_return(woolworths_categories_with_gst)
    end

    it 'returns menu sections as GST if from GST free list' do
      expect(supplier.menu_sections.count).to eq(0)

      retrieved_categories = Woolworths::Import::RetrieveMappedCategories.new(connection: woolworths_connection, supplier: supplier).call
      expect(retrieved_categories).to be_present

      expect(retrieved_categories.sample).to be_a(Woolworths::Import::RetrieveMappedCategories::CategoryMapping)
      expect(retrieved_categories.map(&:woolworths_category)).to include(*woolworths_categories_with_gst)
      expect(retrieved_categories[0].gst_free).to be_truthy
      expect(retrieved_categories[1].gst_free).to be_falsey

      expect(supplier.menu_sections.count).to eq(2)
      created_menu_sections = supplier.menu_sections.where(id: retrieved_categories.map(&:menu_section_id))

      expect(created_menu_sections.map(&:name)).to include(*woolworths_categories_with_gst.map(&:category_name).map(&:titleize))
      expect(created_menu_sections.map(&:group_name)).to include(*woolworths_categories_with_gst.map(&:aisle_name).map(&:titleize))
    end
  end

  context 'with menu section categories', skip: 'Section ategories managed on Menu Page by Admin' do
    let!(:categorisable_woolworths_aisle_name) { Woolworths::Import::RetrieveMappedCategories::SECTION_CATEGTORIES.to_a.sample[0] }
    let!(:category) { create(:category, :random, name: Woolworths::Import::RetrieveMappedCategories::SECTION_CATEGTORIES[categorisable_woolworths_aisle_name]) }

    let!(:gst_free_category) { Rails.configuration.woolworths.category_mappings.gst_free_categories.map(&:downcase).sample }

    let!(:categorisable_woolworths_categories) do
      [
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: categorisable_woolworths_aisle_name, category_name: 'with-attached-category'),
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish)
      ]
    end

    before do
      allow(categories_fetcher).to receive(:call).and_return(categorisable_woolworths_categories)
    end

    it 'creates (and returns) menu sections with the attached category' do
      expect(supplier.menu_sections.count).to eq(0)

      retrieved_categories = Woolworths::Import::RetrieveMappedCategories.new(connection: woolworths_connection, supplier: supplier).call
      expect(retrieved_categories).to be_present

      expect(retrieved_categories.sample).to be_a(Woolworths::Import::RetrieveMappedCategories::CategoryMapping)
      expect(retrieved_categories.map(&:woolworths_category)).to include(*categorisable_woolworths_categories)

      expect(supplier.menu_sections.count).to eq(2)
      created_menu_sections = supplier.menu_sections.where(id: retrieved_categories.map(&:menu_section_id))
      expect(created_menu_sections.map(&:name)).to include(*categorisable_woolworths_categories.map(&:category_name).map(&:titleize))
      expect(created_menu_sections.map(&:group_name)).to include(*categorisable_woolworths_categories.map(&:aisle_name).map(&:titleize))

      created_menu_section1 = supplier.menu_sections.where(id: retrieved_categories.map(&:menu_section_id)[0]).first
      expect(created_menu_section1.categories).to be_present
      expect(created_menu_section1.categories).to include(category)

      created_menu_section2 = supplier.menu_sections.where(id: retrieved_categories.map(&:menu_section_id)[1]).first
      expect(created_menu_section2.categories).to_not be_present
    end
  end

end
