require 'rails_helper'

RSpec.describe Woolworths::Import::GetCategoryProducts, type: :service, woolworths: true, imports: true do
  subject { Woolworths::Import::GetCategoryProducts.new(connection: woolworths_connection, fulfilment_store_id: store_id, category_mapping: mapped_category).call }

  let!(:woolworths_connection) { double(Woolworths::API::Connection) }
  let!(:store_id) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys.sample }

  let!(:mapped_category) do
    Woolworths::Import::RetrieveMappedCategories::CategoryMapping.new(
      Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish),
      rand(100..199),
      false
    )
  end

  let!(:products_response) do
    OpenStruct.new(
      status: 200,
      body: {
        nextpage: false,
        products: [
          {
            article: SecureRandom.hex(7),
            is: {
              fordelivery: [true, false].sample,
              ranged: [true, false].sample
            },
            description: Faker::Name.name,
            images: {
              thumbnail: Faker::Name.name
            },
            instoreprice: {
              pricegst: rand(10.9..15.8)
            },
            promotions: {
              price: rand(2.4..5.8)
            },
            stockqty: rand(10..29)
          },
          {
            article: SecureRandom.hex(7),
            is: {
              fordelivery: [true, false].sample,
              ranged: [true, false].sample
            },
            description: Faker::Name.name,
            images: {
              thumbnail: Faker::Name.name
            },
            instoreprice: {
              pricegst: rand(10.9..15.8)
            },
            promotions: {
              price: rand(2.4..5.8)
            },
            stockqty: rand(10..29)
          }
        ]
      }
    )
  end

  before do
    allow(woolworths_connection).to receive(:request).and_return(products_response)
  end

  it 'makes a request to fetch the category products from Woolworths API' do
    expect(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::PRODUCTS_ENDPOINT, params: {
        mode: 'online',
        store: store_id,
        aisle: mapped_category.woolworths_category.aisle_name,
        category: mapped_category.woolworths_category.category_name,
        page: 1
      }
    )

    subject
  end

  it 'returns an array of products along with its individual data from the response' do
    imported_products = subject

    expect(imported_products).to be_present
    expect(imported_products.size).to eq(2)

    response_products = products_response.body[:products]

    expect(imported_products.map(&:sku)).to include(*response_products.map{|product| product[:article] })
    expect(imported_products.map(&:is_for_delivery)).to include(*response_products.map{|product| product[:is][:fordelivery] })
    expect(imported_products.map(&:is_ranged)).to include(*response_products.map{|product| product[:is][:ranged] })
    expect(imported_products.map(&:name)).to include(*response_products.map{|product| product[:description].strip })
    expect(imported_products.map(&:description)).to include(*response_products.map{|product| product[:description].strip })
    expect(imported_products.map(&:image)).to include(*response_products.map{|product| product[:images][:thumbnail] })
    expect(imported_products.map(&:price)).to include(*response_products.map{|product| product[:instoreprice][:pricegst] })
    expect(imported_products.map(&:promo_price)).to include(*response_products.map{|product| product[:promotions][:price] })
    expect(imported_products.map(&:stock_quantity)).to include(*response_products.map{|product| product[:stockqty] })

    expect(imported_products.map(&:menu_section_id)).to include(mapped_category.menu_section_id)
    expect(imported_products.map(&:is_gst_free)).to include(mapped_category.gst_free)
    expect(imported_products.map(&:fulfilment_store_id)).to include(store_id)
  end

  context 'Custom dictionary description addition' do
    let!(:products_response) do
      OpenStruct.new(
        status: 200,
        body: {
          nextpage: false,
          products: [
            {
              article: SecureRandom.hex(7),
              is: {
                fordelivery: [true, false].sample,
                ranged: [true, false].sample
              },
              description: 'Coca - Cola Diet Soft Drink Bottle 600ml',
              images: {
                thumbnail: Faker::Name.name
              },
              instoreprice: {
                pricegst: rand(10.9..15.8)
              },
              promotions: {
                price: rand(2.4..5.8)
              },
              stockqty: rand(10..29)
            },
            {
              article: SecureRandom.hex(7),
              is: {
                fordelivery: [true, false].sample,
                ranged: [true, false].sample
              },
              description: 'Coca-cola Cherry 330ml',
              images: {
                thumbnail: Faker::Name.name
              },
              instoreprice: {
                pricegst: rand(10.9..15.8)
              },
              promotions: {
                price: rand(2.4..5.8)
              },
              stockqty: rand(10..29)
            }
          ]
        }
      )
    end

    it 'returns an array of products along with its individual customised description (Coke)' do
      imported_products = subject

      expect(imported_products).to be_present
      expect(imported_products.size).to eq(2)

      response_products = products_response.body[:products]

      expect(imported_products.map(&:name)).to include(*response_products.map{|product| product[:description].strip })
      expect(imported_products.map(&:description)).to include(*response_products.map{|product| "#{product[:description].strip} - also known as Coke" })
    end

  end

  context 'with API error' do
    before do
      errored_response = OpenStruct.new(status: [402, 404, 500].sample)
      allow(woolworths_connection).to receive(:request).and_return(errored_response)
    end

    it 'does not raise an error' do
      expect { subject }.to_not raise_error
    end
  end

end