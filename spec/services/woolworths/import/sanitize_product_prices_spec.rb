require 'rails_helper'

RSpec.describe Woolworths::Import::SanitizeProductPrices, type: :services, woolworths: true, imports: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:imported_10_pack_item1) { create(:menu_item, :random, name: 'Mount Franklin Lightly Sparkling Flavour 1 Multipack Cans 375mL x10 Pack', price: 25.0, promo_price: nil, supplier_profile: supplier) }
  let!(:store_availability1) { create(:woolworths_store_availability, :random, menu_item: imported_10_pack_item1) }

  let!(:imported_10_pack_item2) { create(:menu_item, :random, name: 'Mount Franklin Lightly Sparkling Flavour 2 Multipack Cans 375mL x10 Pack', price: 19.0, promo_price: nil, supplier_profile: supplier) }  
  let!(:store_availability2) { create(:woolworths_store_availability, :random, menu_item: imported_10_pack_item2) }

  let!(:imported_6_pack_item1) { create(:menu_item, :random, name: 'Mount Franklin Lightly Sparkling Flavour 1 250ml x 6 Pack', price: 9, promo_price: nil, supplier_profile: supplier) }
  let!(:store_availability3) { create(:woolworths_store_availability, :random, menu_item: imported_6_pack_item1) }

  let!(:imported_6_pack_item2) { create(:menu_item, :random, name: 'Mount Franklin Lightly Sparkling Flavour 2 250ml x 6 Pack', price: 6.75, promo_price: nil, supplier_profile: supplier) }
  let!(:store_availability4) { create(:woolworths_store_availability, :random, menu_item: imported_6_pack_item2) }

  let!(:always_available_10_pack_item) { create(:menu_item, :random, name: 'Mount Franklin Lightly Sparkling Flavour 3 Multipack Cans 375mL x10 Pack', price: 23.0, promo_price: nil, supplier_profile: supplier) }
  let!(:always_available_6_pack_item) { create(:menu_item, :random, name: 'Mount Franklin Lightly Sparkling Flavour 3 250ml x 6 Pack', price: 8.0, promo_price: nil, supplier_profile: supplier) }


  before do
    # tag the items as always avaialable by setting ID
    id1, id2 = Rails.configuration.woolworths.category_mappings.always_available_item_ids.sample(2)
    always_available_10_pack_item.update_column(:id, id1)
    always_available_6_pack_item.update_column(:id, id2)    
  end

  context 'updates the pricing of the always available items' do
    it 'based on matching items (by name) least pricing' do
      Woolworths::Import::SanitizeProductPrices.new(supplier: supplier).call

      expect(always_available_10_pack_item.reload.price).to eq(imported_10_pack_item2.price)
      expect(always_available_10_pack_item.reload.promo_price).to eq(imported_10_pack_item2.promo_price)

      expect(always_available_6_pack_item.reload.price).to eq(imported_6_pack_item2.price)
      expect(always_available_6_pack_item.reload.promo_price).to eq(imported_6_pack_item2.promo_price)
    end

    it 'based on matching items (by name) least pricing of available items only' do
      store_availability2.destroy
      store_availability4.destroy
      Woolworths::Import::SanitizeProductPrices.new(supplier: supplier).call

      expect(always_available_10_pack_item.reload.price).to eq(imported_10_pack_item1.price)
      expect(always_available_10_pack_item.reload.promo_price).to eq(imported_10_pack_item1.promo_price)

      expect(always_available_6_pack_item.reload.price).to eq(imported_6_pack_item1.price)
      expect(always_available_6_pack_item.reload.promo_price).to eq(imported_6_pack_item1.promo_price)
    end

    it 'based on least matching items (by name) promo pricing if present' do
      imported_10_pack_item1.update_column(:promo_price, imported_10_pack_item1.price - 3)
      imported_6_pack_item1.update_column(:promo_price, imported_6_pack_item1.price - 2)

      Woolworths::Import::SanitizeProductPrices.new(supplier: supplier).call

      expect(always_available_10_pack_item.reload.price).to eq(imported_10_pack_item1.price)
      expect(always_available_10_pack_item.reload.promo_price).to eq(imported_10_pack_item1.promo_price)
      
      expect(always_available_6_pack_item.reload.price).to eq(imported_6_pack_item1.price)
      expect(always_available_6_pack_item.reload.promo_price).to eq(imported_6_pack_item1.promo_price)
    end

    it 'doesn\'t update the items in dry run' do
      Woolworths::Import::SanitizeProductPrices.new(supplier: supplier, dry_run: true).call

      imported_10_pack_item = [imported_10_pack_item1, imported_10_pack_item2].sample
      expect(always_available_10_pack_item.reload.price).to_not eq(imported_10_pack_item.price)

      imported_6_pack_item = [imported_6_pack_item1, imported_6_pack_item2].sample
      expect(always_available_6_pack_item.reload.price).to_not eq(imported_6_pack_item.price)
    end 
  end

end 