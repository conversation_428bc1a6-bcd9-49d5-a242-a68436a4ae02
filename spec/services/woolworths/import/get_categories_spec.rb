require 'rails_helper'

RSpec.describe Woolworths::Import::GetCategories, type: :service, woolworths: true, imports: true do
  subject { Woolworths::Import::GetCategories.new(connection: woolworths_connection).call }

  let!(:woolworths_connection) { double(Woolworths::API::Connection) }

  let!(:categories_response) do
    OpenStruct.new(
      status: 200,
      body: {
        aisles: [
          {
            title: 'aisle-1',
            categories: [
              {
                title: 'aisle-1-category-1'
              },
              {
                title: 'aisle-1-category-2'
              }
            ]
          },
          {
            title: 'aisle-2',
            categories: [
              {
                title: 'aisle-2-category-1'
              }
            ]
          }
        ]
      }
    )
  end

  before do
    allow(woolworths_connection).to receive(:request).and_return(categories_response)
  end

  it 'makes a request to fetch the categories from Woolworths API' do
    expect(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::CATEGORIES_ENDPOINT, params: { mode: :online })

    subject
  end

  it 'returns categories along with the name and aisle name' do
    imported_categories = subject

    expect(imported_categories).to be_present
    expect(imported_categories.size).to eq(3)
    expect(imported_categories.sample).to be_a(Woolworths::Import::GetCategories::WoolworthsCategory)

    expected_aisle_names = categories_response.body[:aisles].map{|aisle| aisle[:title] }
    expect(imported_categories.map(&:aisle_name).uniq).to include(*expected_aisle_names)

    expected_category_names = categories_response.body[:aisles].map{|aisle| aisle[:categories].map{|category| category[:title] } }.flatten(1)
    expect(imported_categories.map(&:category_name)).to include(*expected_category_names)
  end

  context 'with API error' do
    before do
      errored_response = OpenStruct.new(status: [402, 404, 500].sample)
      allow(woolworths_connection).to receive(:request).and_return(errored_response)
    end

    it 'raises the error' do
      expect { subject }.to raise_error(RuntimeError, 'Could not fetch categories from Woolworths')
    end
  end

end