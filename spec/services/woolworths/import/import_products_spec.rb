require 'rails_helper'

RSpec.describe Woolworths::Import::ImportProducts, type: :service, woolworths: true, imports: true do

  let!(:woolworths_connection) { double(Woolworths::API::Connection) }

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:categories_fetcher) { double(Woolworths::Import::GetCategories) }
  let!(:store_id) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys.sample }

  let!(:woolworths_categories) do
    [
      Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish),
      Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish)
    ]
  end

  let!(:category1_products_response) do
    OpenStruct.new(
      status: 200,
      body: {
        products: [
          {
            article: SecureRandom.hex(7),
            description: Faker::Name.name,
            stockqty: rand(30..36),
            is: {
              fordelivery: true,
              ranged: true
            },
            images: {
              thumbnail: ''
            },
            instoreprice: {
              pricegst: rand(10.2..15.9)
            },
            promotions: nil
          },
          {
            article: SecureRandom.hex(7),
            description: Faker::Name.name,
            stockqty: rand(30..36),
            is: {
              fordelivery: true,
              ranged: true
            },
            images: {
              thumbnail: ''
            },
            instoreprice: {
              pricegst: rand(10.2..15.9)
            },
            promotions: nil
          }
        ],
        nextpage: false
      }
    )
  end

  let!(:category2_products_response) do
    OpenStruct.new(
      status: 200,
      body: {
        products: [
          {
            article: SecureRandom.hex(7),
            description: Faker::Name.name,
            stockqty: rand(30..36),
            is: {
              fordelivery: true,
              ranged: true
            },
            images: {
              thumbnail: ''
            },
            instoreprice: {
              pricegst: rand(10.2..15.9)
            },
            promotions: nil,
          }
        ],
        nextpage: false
      }
    )
  end

  let!(:response_products) { category1_products_response.body[:products] + category2_products_response.body[:products] }

  before do
    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier.id)

    # mock woolworths connection
    allow(Woolworths::Import::Connection).to receive(:new).and_return(woolworths_connection)
    allow(woolworths_connection).to receive(:call).and_return(woolworths_connection)

    # mock Woolworths categories fetcher
    allow(Woolworths::Import::GetCategories).to receive(:new).and_return(categories_fetcher)
    allow(categories_fetcher).to receive(:call).and_return(woolworths_categories)

    # mock image uploader
    image_uploader = double(Woolworths::Import::UploadProductImages)
    allow(Woolworths::Import::UploadProductImages).to receive(:new).and_return(image_uploader)
    allow(image_uploader).to receive(:call).and_return(true)

    # mock pricing sanitizer
    pricing_sanitizer = double(Woolworths::Import::SanitizeProductPrices)
    allow(Woolworths::Import::SanitizeProductPrices).to receive(:new).and_return(pricing_sanitizer)
    allow(pricing_sanitizer).to receive(:call).and_return(true)

    # mock products fetcher
    allow(woolworths_connection).to receive(:request).with(method: :get, path: 'v2/products', params: {
      mode: 'online',
      store: anything,
      aisle: woolworths_categories[0].aisle_name,
      category: woolworths_categories[0].category_name,
      page: 1
    }).and_return(category1_products_response)
    allow(woolworths_connection).to receive(:request).with(method: :get, path: 'v2/products', params: {
      mode: 'online',
      store: anything,
      aisle: woolworths_categories[1].aisle_name,
      category: woolworths_categories[1].category_name,
      page: 1
    }).and_return(category2_products_response)
  end

  it 'creates a connection for the passed in fulfilment_store_id' do
    expect(Woolworths::Import::Connection).to receive(:new).with(store_id: store_id, is_reimport: anything)

    products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
    expect(products_importer).to be_success
  end

  it 'creates menu section from the fetched in categories' do
    expect(supplier.menu_sections.count).to eq(0)

    products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
    expect(products_importer).to be_success

    expect(supplier.menu_sections.count).to eq(2)

    imported_menu_sections = products_importer.imported_menu_sections
    expect(imported_menu_sections).to be_present

    store_imported_sections = imported_menu_sections[store_id.to_s]
    expect(store_imported_sections).to be_present
    expect(store_imported_sections.size).to eq(2)
    expect(store_imported_sections.map(&:supplier_profile)).to include(supplier)
    expect(store_imported_sections.map(&:name)).to eq(woolworths_categories.map(&:category_name).map(&:titleize))
    expect(store_imported_sections.map(&:group_name)).to eq(woolworths_categories.map(&:aisle_name).map(&:titleize))
  end

  it 'makes a request to fetch the products per mapped category' do
    expect(woolworths_connection).to receive(:request).with(method: :get, path: 'v2/products', params: {
        mode: 'online',
        store: store_id,
        aisle: woolworths_categories[0].aisle_name,
        category: woolworths_categories[0].category_name,
        page: 1
      })
    expect(woolworths_connection).to receive(:request).with(method: :get, path: 'v2/products', params: {
        mode: 'online',
        store: store_id,
        aisle: woolworths_categories[1].aisle_name,
        category: woolworths_categories[1].category_name,
        page: 1
      })

    products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
    expect(products_importer).to be_success
  end

  it 'imports (syncs) the fetched products within each menu sections' do
    products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
    expect(products_importer).to be_success

    imported_products = products_importer.imported_products
    expect(imported_products).to be_present

    store_imported_products = imported_products[store_id.to_s].uniq
    expect(store_imported_products.size).to eq(3) # 2 from first section and 1 from the second

    expect(store_imported_products.map(&:sku)).to include(*response_products.map{|product| product[:article] })
    expect(store_imported_products.map(&:name)).to include(*response_products.map{|product| product[:description] })
    expect(store_imported_products.map(&:description)).to include(*response_products.map{|product| product[:description] })
    expect(store_imported_products.map{|item| item.price.round(2) }).to include(*response_products.map{|product| product[:instoreprice][:pricegst].round(2) })
  end

  it 'creates store availabilities for the imported items', skip: 'No longer using store availaibilities' do
    products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
    expect(products_importer).to be_success

    imported_products = products_importer.imported_products
    expect(imported_products).to be_present

    store_imported_products = imported_products[store_id.to_s]
    expect(store_imported_products.size).to eq(3)

    store_availabilities = Woolworths::StoreAvailability.where(store_id: store_id)
    expect(store_availabilities).to be_present
    expect(store_availabilities.size).to eq(3) # 1 per item created
    expect(store_availabilities.map(&:menu_item)).to include(*store_imported_products)
  end

  context 'with existing store availabilities', skip: 'No longer using store availaibilities' do
    let!(:store_availability1) { create(:woolworths_store_availability, :random, store_id: store_id) }
    let!(:store_availability2) { create(:woolworths_store_availability, :random, store_id: store_id) }
    let!(:store_availability3) { create(:woolworths_store_availability, :random, store_id: store_id) }
    let!(:store_availability4) { create(:woolworths_store_availability, :random, store_id: store_id) }

    let!(:store_availability5) { create(:woolworths_store_availability, :random, store_id: (Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys - [store_id]).sample) }

    it 'clears out all existing store availabilities for the store id' do
      expect(Woolworths::StoreAvailability.count).to eq(5) # created above

      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
      expect(products_importer).to be_success

      expect(Woolworths::StoreAvailability.count).to eq(4) # 3 created as part of import + 1 non-store availability
      remaining_store_availabilities = Woolworths::StoreAvailability.where(store_id: store_id)
      expect(remaining_store_availabilities.map(&:id)).to_not include(*[store_availability1, store_availability2, store_availability3, store_availability4].map(&:id))
    end

    it 'does not remove store availaibilities of always available items' do
      always_available_item_id = Rails.configuration.woolworths.category_mappings.always_available_item_ids.sample

      always_available_item = create(:menu_item, :random)
      always_available_item.update_column(:id, always_available_item_id) # update item ID
      item_availability = create(:woolworths_store_availability, :random, menu_item: always_available_item, store_id: store_id) # create new availability with new menu item ID
      
      expect(Woolworths::StoreAvailability.count).to eq(6) # created above

      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
      expect(products_importer).to be_success

      expect(Woolworths::StoreAvailability.count).to eq(5) # 1 always available + 3 created as part of import + 1 non-store availability
      remaining_store_availabilities = Woolworths::StoreAvailability.all
      expect(remaining_store_availabilities.map(&:id)).to include(item_availability.id)
    end
  end

  context 'with undeliverable products' do
    let!(:category1_products_response) do
      OpenStruct.new(
        status: 200,
        body: {
          products: [
            {
              article: SecureRandom.hex(7),
              description: Faker::Name.name,
              stockqty: rand(30..36),
              is: {
                fordelivery: true,
                ranged: true
              },
              images: {
                thumbnail: ''
              },
              instoreprice: {
                pricegst: rand(10.2..15.9)
              },
              promotions: nil
            },
            {
              article: SecureRandom.hex(7),
              description: Faker::Name.name,
              stockqty: rand(30..36),
              is: {
                fordelivery: false, # mark product as not deliverable
                ranged: true,
              },
              images: {
                thumbnail: ''
              },
              instoreprice: {
                pricegst: rand(10.2..15.9)
              },
              promotions: nil
            }
          ],
          nextpage: false
        }
      )
    end

    it 'doesn\'t import (sync) the non-deliverable fetched products' do
      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
      expect(products_importer).to be_success

      imported_products = products_importer.imported_products
      expect(imported_products).to be_present

      store_imported_products = imported_products[store_id.to_s]
      expect(store_imported_products.size).to eq(2)

      expect(store_imported_products.map(&:name)).to_not include(category1_products_response.body[:products][1][:description])
      expect(store_imported_products.map(&:sku)).to_not include(category1_products_response.body[:products][1][:article])
      expect(store_imported_products.map(&:description)).to_not include(category1_products_response.body[:products][1][:description])
      expect(store_imported_products.map{|item| item.price.round(2) }).to_not include(category1_products_response.body[:products][1][:instoreprice][:pricegst].round(2))

      expect(store_imported_products.map(&:name)).to include(category1_products_response.body[:products][0][:description], category2_products_response.body[:products][0][:description])
      expect(store_imported_products.map(&:description)).to include(category1_products_response.body[:products][0][:description], category2_products_response.body[:products][0][:description])
      expect(store_imported_products.map(&:sku)).to include(category1_products_response.body[:products][0][:article], category2_products_response.body[:products][0][:article])
      expect(store_imported_products.map{|item| item.price.round(2) }).to include(category1_products_response.body[:products][0][:instoreprice][:pricegst].round(2), category2_products_response.body[:products][0][:instoreprice][:pricegst].round(2))
    end
  end

  context 'with products with missing pricing' do
    let!(:category1_products_response) do
      OpenStruct.new(
        status: 200,
        body: {
          products: [
            {
              article: SecureRandom.hex(7),
              description: Faker::Name.name,
              stockqty: rand(30..36),
              is: {
                fordelivery: true,
                ranged: true
              },
              images: {
                thumbnail: ''
              },
              instoreprice: {
                pricegst: rand(10.2..15.9)
              },
              promotions: nil
            },
            {
              article: SecureRandom.hex(7),
              description: Faker::Name.name,
              stockqty: rand(30..36),
              is: {
                fordelivery: true,
                ranged: true,
              },
              images: {
                thumbnail: ''
              },
              instoreprice: {
                # pricegst: rand(10.2..15.9)
              },
              promotions: nil
            }
          ],
          nextpage: false
        }
      )
    end

    it 'doesn\'t import (sync) the fetched products with missing pricing' do
      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call
      expect(products_importer).to be_success

      imported_products = products_importer.imported_products
      expect(imported_products).to be_present

      store_imported_products = imported_products[store_id.to_s]
      expect(store_imported_products.size).to eq(2)

      expect(store_imported_products.map(&:name)).to_not include(category1_products_response.body[:products][1][:description])
      expect(store_imported_products.map(&:sku)).to_not include(category1_products_response.body[:products][1][:article])
      expect(store_imported_products.map(&:description)).to_not include(category1_products_response.body[:products][1][:description])
      # expect(store_imported_products.map{|item| item.price.round(2) }).to_not include(category1_products_response.body[:products][1][:instoreprice][:pricegst].round(2))

      expect(store_imported_products.map(&:name)).to include(category1_products_response.body[:products][0][:description], category2_products_response.body[:products][0][:description])
      expect(store_imported_products.map(&:description)).to include(category1_products_response.body[:products][0][:description], category2_products_response.body[:products][0][:description])
      expect(store_imported_products.map(&:sku)).to include(category1_products_response.body[:products][0][:article], category2_products_response.body[:products][0][:article])
      expect(store_imported_products.map{|item| item.price.round(2) }).to include(category1_products_response.body[:products][0][:instoreprice][:pricegst].round(2), category2_products_response.body[:products][0][:instoreprice][:pricegst].round(2))
    end
  end

  context 'when importable_store_ids is NOT passed' do
    let!(:store_ids) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys }

    it 'creates a connection and imports products for the list of fulfilment_store_ids' do
      store_ids.each do |store_id|
        expect(Woolworths::Import::Connection).to receive(:new).with(store_id: store_id, is_reimport: anything)
      end

      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: nil).call
      expect(products_importer).to be_success

      expect(products_importer.imported_menu_sections.keys).to include(*store_ids.map(&:to_s))
      expect(products_importer.imported_products.keys).to include(*store_ids.map(&:to_s))
    end
  end

  context 'with multiple importable_store_ids' do
    let!(:store_ids) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys.sample(2) }

    it 'creates a connection and imports products for the list of fulfilment_store_ids' do
      store_ids.each do |store_id|
        expect(Woolworths::Import::Connection).to receive(:new).with(store_id: store_id, is_reimport: anything)
      end

      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_ids).call
      expect(products_importer).to be_success

      expect(products_importer.imported_menu_sections.keys).to include(*store_ids.map(&:to_s))
      expect(products_importer.imported_products.keys).to include(*store_ids.map(&:to_s))
    end
  end

  it 'makes a request to upload Woolworths product images for the supplier' do
    expect(Woolworths::Import::UploadProductImages).to receive(:new).with(supplier: supplier)

    products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier).call
    expect(products_importer).to be_success
  end

  it 'makes a request to sanitize Woolworths product pricing for the supplier' do
    expect(Woolworths::Import::SanitizeProductPrices).to receive(:new).with(supplier: supplier)

    products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier).call
    expect(products_importer).to be_success
  end

  context 'reimporting' do
    let!(:reimportable_category) { Rails.configuration.woolworths.category_mappings.reimport[:categories].map(&:downcase).sample }

    let!(:woolworths_categories) do
      [
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: 'with-reimportable-category', category_name: reimportable_category),
        Woolworths::Import::GetCategories::WoolworthsCategory.new(aisle_name: Faker::Name.name, category_name: Faker::Food.dish)
      ]
    end

    it 'creates a connection with reimport config' do
      expect(Woolworths::Import::Connection).to receive(:new).with(store_id: store_id, is_reimport: true)

      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id, is_reimport: true).call
      expect(products_importer).to be_success
    end

    it 'only imports reimportable categories and its product' do # and adds store availabilities' do
      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id, is_reimport: true).call
      expect(products_importer).to be_success

      imported_menu_sections = products_importer.imported_menu_sections
      expect(imported_menu_sections).to be_present
      expect(imported_menu_sections.size).to eq(1)

      imported_products = products_importer.imported_products
      expect(imported_products).to be_present

      store_imported_products = imported_products[store_id.to_s]
      expect(store_imported_products.size).to eq(2)

      # store_availabilities = Woolworths::StoreAvailability.where(store_id: store_id)
      # expect(store_availabilities.size).to eq(2) # 1 per item
    end

    context 'with existing store availabilities', skip: 'No longer using store availaibilities' do
      let!(:menu_section1) { create(:menu_section, :random, supplier_profile: supplier, name: woolworths_categories[0].category_name.titleize, group_name: woolworths_categories[0].aisle_name.titleize)}
      let!(:menu_item11) { create(:menu_item, :random, menu_section: menu_section1) }
      let!(:menu_item12) { create(:menu_item, :random, menu_section: menu_section1) }
      let!(:menu_item13) { create(:menu_item, :random, menu_section: menu_section1) }
      let!(:menu_item14) { create(:menu_item, :random, menu_section: menu_section1) }
      let!(:store_availability11) { create(:woolworths_store_availability, :random, menu_item: menu_item11, store_id: store_id) }
      let!(:store_availability12) { create(:woolworths_store_availability, :random, menu_item: menu_item12, store_id: store_id) }
      let!(:store_availability13) { create(:woolworths_store_availability, :random, menu_item: menu_item13, store_id: store_id) }
      let!(:store_availability14) { create(:woolworths_store_availability, :random, menu_item: menu_item14, store_id: store_id) }

      let!(:menu_section2) { create(:menu_section, :random, supplier_profile: supplier, name: woolworths_categories[0].category_name.titleize, group_name: woolworths_categories[0].aisle_name.titleize)}
      let!(:menu_item21) { create(:menu_item, :random, menu_section: menu_section2) }
      let!(:menu_item22) { create(:menu_item, :random, menu_section: menu_section2) }
      let!(:store_availability21) { create(:woolworths_store_availability, :random, menu_item: menu_item21, store_id: store_id) }
      let!(:store_availability22) { create(:woolworths_store_availability, :random, menu_item: menu_item22, store_id: store_id) }

      let!(:store_availability1) { create(:woolworths_store_availability, :random, store_id: store_id) }
      let!(:store_availability2) { create(:woolworths_store_availability, :random, store_id: store_id) }

      let!(:store_availability3) { create(:woolworths_store_availability, :random, store_id: (Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys - [store_id]).sample) }

      it 'only removes / refreshes woolworths store availability for the items in the existing menu menu section' do
        expect(Woolworths::StoreAvailability.count).to eq(9) # created above

        products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id, is_reimport: true).call
        expect(products_importer).to be_success

        expect(Woolworths::StoreAvailability.count).to eq(7) # 2 created as part of (re) import + 4 belonging to non importable section items + 1 non-store availability
        remaining_store_availabilities = Woolworths::StoreAvailability.all
        expect(remaining_store_availabilities.map(&:id)).to_not include(*[store_availability11, store_availability12, store_availability13, store_availability14].map(&:id))
        expect(remaining_store_availabilities.map(&:id)).to include(*[store_availability21, store_availability22, store_availability1, store_availability2, store_availability3].map(&:id))
      end

      it 'does not remove store availaibilities of always available items' do
        always_available_item_id = Rails.configuration.woolworths.category_mappings.always_available_item_ids.sample

        store_availability11.destroy # remove previous availabiltity
        menu_item11.update_column(:id, always_available_item_id) # update item ID
        store_availability15 = create(:woolworths_store_availability, :random, menu_item: menu_item11, store_id: store_id) # create new availability with new menu item ID
        
        expect(Woolworths::StoreAvailability.count).to eq(9) # created above

        products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id, is_reimport: true).call
        expect(products_importer).to be_success

        expect(Woolworths::StoreAvailability.count).to eq(8) # 1 always available + 2 created as part of (re) import + 4 belonging to non importable section items + 1 non-store availability
        remaining_store_availabilities = Woolworths::StoreAvailability.all
        expect(remaining_store_availabilities.map(&:id)).to include(store_availability15.id)
      end
    end # with existing store availabilities
  end # re-import

  context 'errors' do
    it 'cannot import without a supplier' do
      products_importer = Woolworths::Import::ImportProducts.new(supplier: nil, importable_store_ids: [nil, store_id].sample).call

      expect(products_importer).to_not be_success
      expect(products_importer.errors).to include('Cannot import products without a supplier')
    end

    it 'cannot import products for a non-Woolworths supplier' do
      supplier2 = create(:supplier_profile, :random)
      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier2, importable_store_ids: [nil, store_id].sample).call

      expect(products_importer).to_not be_success
      expect(products_importer.errors).to include('Cannot import products for a non-Woolworths supplier')
    end

    it 'cannot import products for a non-valid store id' do
      products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: 'invalid-store-id').call

      expect(products_importer).to_not be_success
      expect(products_importer.errors).to include('Cannot import wihtout valid fulfilment_store_ids')
    end

    context 'if any API calls fail' do
      before do
        connection_failed = [true, false].sample
        fetching_categories_failed = [true, false].sample
        case
        when connection_failed
          allow(woolworths_connection).to receive(:call).and_raise(RuntimeError.new('error-message'))
        when fetching_categories_failed
          allow(Woolworths::Import::GetCategories).to receive(:new).and_raise(RuntimeError.new('error-message'))
        else
          allow(woolworths_connection).to receive(:request).and_raise(RuntimeError.new('error-message'))
        end
      end

      it 'errors if anything fails' do
        products_importer = Woolworths::Import::ImportProducts.new(supplier: supplier, importable_store_ids: store_id).call

        expect(products_importer).to_not be_success
        expect(products_importer.errors).to include("Failed to import products for store ID: #{store_id} - error-message")
      end
    end # other API failures
  end

end