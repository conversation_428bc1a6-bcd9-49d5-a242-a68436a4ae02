require 'rails_helper'

RSpec.describe Woolworths::ProcessOrderDeliveryDetails, type: :service, woolworths: true do
  describe '.execute' do
    subject { Woolworths::ProcessOrderDeliveryDetails.new(order: order).call }

    let(:woolworths_account) { create(:woolworths_account, :random) }
    let(:delivery_suburb) { create(:suburb, :rosebay) }
    let(:order) { create(:order, :random, delivery_at: Time.zone.local(2019, 6, 6, 17, 0, 0)) }
    let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account: woolworths_account, account_in_use: true) }
    let(:woolworths_connection) { double(Woolworths::API::Connection) }
    let(:woolworths_delivery_address_id) { 'woolworths-delivery-address-id' }
    let(:woolworths_delivery_windows) do
      {
        windows: [
          {
            selected: false,
            date: '2019-06-06T00:00:00',
            available: true,
            slots: [
              { windowid: 141_679, selected: false, available: true, starttime: '2019-06-06T17:00:00', endtime: '2019-06-06T23:00:00', status: 'Available', promoprice: 6, price: 6 },
              { windowid: 48_054, selected: false, available: true, starttime: '2019-06-06T20:00:00', endtime: '2019-06-06T22:00:00', status: 'Available', promoprice: 6, price: 6 }
            ]
          },
          {
            selected: false,
            date: '2019-06-12T00:00:00',
            available: true,
            slots: [
              { windowid: 47_983, selected: false, available: true, starttime: '2019-06-12T05:00:00', endtime: '2019-06-12T08:00:00', status: 'Available', promoprice: 6, price: 6 }
            ]
          }
        ]
      }
    end

    let(:avaialbe_delivery_windows) do
      woolworths_delivery_windows[:windows].detect{|x| Date.parse(x[:date]) == order.delivery_at.to_date }[:slots].map do |delivery_window|
        Woolworths::DeliveryWindow.new(
          id: delivery_window[:windowid],
          date: Date.parse(delivery_window[:starttime]),
          time_range: (Time.zone.parse(delivery_window[:starttime])..Time.zone.parse(delivery_window[:endtime]))
        )
      end
    end
    let(:delivery_window) { Woolworths::DeliveryWindow.new(id: 141_679, date: Date.parse('2019-06-06'), time_range: Time.zone.parse('2019-06-06T17:00:00')..Time.zone.parse('2019-06-06T23:00:00')) }
    let!(:delivery_window_text) { 'Monday between 5:00am and 8:00am' }

    before do
      allow(Woolworths::API::Connection).to receive(:new).and_return(woolworths_connection)

      # Stub the individual service classes; check that they are invoked correctly in a specific example.
      address_syncer = double(Woolworths::Order::SyncDeliveryAddress)
      allow(Woolworths::Order::SyncDeliveryAddress).to receive(:new).and_return(address_syncer)
      allow(address_syncer).to receive(:call).and_return(true)

      windows_getter = double(Woolworths::API::GetDeliveryWindows)
      allow(Woolworths::API::GetDeliveryWindows).to receive(:new).and_return(windows_getter)
      allow(windows_getter).to receive(:call).and_return(woolworths_delivery_windows) 

      delivery_window_selector = double(Woolworths::SelectDeliveryWindow)
      allow(Woolworths::SelectDeliveryWindow).to receive(:new).and_return(delivery_window_selector)
      allow(delivery_window_selector).to receive(:call).and_return(delivery_window)

      window_setter = double(Woolworths::API::SetDeliveryWindow)
      allow(Woolworths::API::SetDeliveryWindow).to receive(:new).and_return(window_setter)
      allow(window_setter).to receive(:call).and_return(delivery_window_text)

      instruction_setter = double(Woolworths::API::SetDeliveryInstructions)
      allow(Woolworths::API::SetDeliveryInstructions).to receive(:new).and_return(instruction_setter)
      allow(instruction_setter).to receive(:call).and_return(true)

      allow(Rails.cache).to receive(:write)
    end

    it 'invokes the service classes to make the required API calls to Woolworths' do
      expect(Woolworths::API::Connection).to receive(:new).with(account: woolworths_account, authenticated: true)

      expect(Woolworths::Order::SyncDeliveryAddress).to receive(:new).with(order: order, connection: woolworths_connection) # internal API calls tested within Service Object

      expect(Woolworths::API::SetDeliveryInstructions).to receive(:new).with(order: order, connection: woolworths_connection)

      expect(Woolworths::API::GetDeliveryWindows).to receive(:new).with(connection: woolworths_connection)
      expect(Woolworths::API::SetDeliveryWindow).to receive(:new).with(order: order, connection: woolworths_connection, window_id: 141_679)

      subject
    end

    it 'updates the window ID of the Woolworths Order with delivery window that was automatically selected' do
      expect { subject }.to change { order.woolworths_order.delivery_window_id }.from(nil).to(141_679)
    end

    it 'updates the delivery_at of the order' do
      expect(Woolworths::SelectDeliveryWindow).to receive(:new)

      subject
      expect(order.delivery_at).to eq(delivery_window.time_range.last)
    end

    it 'errors out if No Delivery Window can be selected' do
      allow(Woolworths::SelectDeliveryWindow).to receive(:new).and_raise(Woolworths::SelectDeliveryWindow::NoDeliveryWindowError)
      expect{ subject }.to raise_error(Woolworths::SelectDeliveryWindow::NoDeliveryWindowError)
    end

  end
end
