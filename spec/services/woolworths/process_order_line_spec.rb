require 'rails_helper'

RSpec.describe Woolworths::ProcessOrderLine, type: :service, woolworths: true do
  subject { Woolworths::ProcessOrderLine.new(order_line: order_line).call }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  let(:woolworths_account) { build(:woolworths_account, :random) }
  let(:menu_item) { create(:menu_item, :random, sku: '000000000000123456', name: 'Milk') }
  let(:order) { build(:order, :random) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account: woolworths_account, account_in_use: true) }
  let(:order_line) { create(:order_line, :random, order: order, menu_item: menu_item, quantity: 5) }
  let!(:trolley_product) { create(:woolworths_trolley_product, woolworths_order: order.woolworths_order, order_line: order_line) }

  let(:expected_trolley_product_data) do
    {
      menu_item: order_line.menu_item,
      quantity: order_line.quantity
    }
  end

  before do
    expect(Woolworths::API::Connection).to receive(:new).with(account: woolworths_account, authenticated: true).and_return(woolworths_connection)

    trolley_product_adder = double(Woolworths::API::AddProductToTrolley)
    allow(Woolworths::API::AddProductToTrolley).to receive(:new).and_return(trolley_product_adder)
    allow(trolley_product_adder).to receive(:call).and_return(product_added_to_trolley)
  end

  context 'invoking the right method' do
    let(:product_added_to_trolley) { Woolworths::API::AddProductToTrolley::TrolleyProduct.new(menu_item, true, nil, 10) }

    it 'invokes the service class to make the Woolworths API call to add/update the product to the trolley' do
      expect(Woolworths::API::AddProductToTrolley).to receive(:new).with(connection: woolworths_connection, product: expected_trolley_product_data)

      subject
    end
  end

  context 'the product of the order_line was added to the trolley' do
    let(:product_added_to_trolley) { Woolworths::API::AddProductToTrolley::TrolleyProduct.new(menu_item, true, nil, 10) }

    it 'does not add an error to the woolworths trolley product' do
      expect { subject }.to_not change { trolley_product.trolley_errors }.from([])
    end
  end

  context 'the product of the order_line was not added to the trolley cause it was unavailable' do
    let(:product_added_to_trolley) { Woolworths::API::AddProductToTrolley::TrolleyProduct.new(menu_item, false, nil, 10) }

    it 'adds an error to the woolworths trolley product for the specified order line' do
      expected_errors = ['Item is currently unavailable.']
      expect do
        subject
      end.to change { trolley_product.reload.trolley_errors }.from([]).to(expected_errors)
    end
  end

  context 'the product of the order_line was added to the trolley but not the full expected quantity' do
    let!(:stock_quantity_in_woolies) { [2, 3, 4].sample }
    let(:product_added_to_trolley) { Woolworths::API::AddProductToTrolley::TrolleyProduct.new(menu_item, true, stock_quantity_in_woolies, 10) }

    it 'adds an error to the woolworths trolley product for the specified order line' do
      expected_errors = ["Only #{stock_quantity_in_woolies} items available."]
      expect do
        subject
      end.to change { trolley_product.reload.trolley_errors }.from([]).to(expected_errors)
    end
  end

  context 'the product of the order_line was not added to the trolley because it caused a Woolworths::API::AddProductToTrolley::ExceededDeliverySaversLimitError' do
    let(:product_added_to_trolley) { nil }

    before do
      trolley_product_adder = double(Woolworths::API::AddProductToTrolley)
      allow(Woolworths::API::AddProductToTrolley).to receive(:new).and_return(trolley_product_adder)
      allow(trolley_product_adder).to receive(:call).and_raise(Woolworths::API::AddProductToTrolley::ExceededDeliverySaversLimitError)
    end

    it 'adds an error to the woolworths trolley product for the specified order line' do
      expected_errors = ['You can only have a maximum of 2 active Delivery Savers on your account at any one time. These will be used consecutively.']
      expect do
        subject
      end.to change { trolley_product.reload.trolley_errors }.from([]).to(expected_errors)
    end
  end

  context 'the product of the order_line was not added to the trolley because it caused a Woolworths::API::AddProductToTrolley::CannotProcessProductToTrolley' do
    let(:product_added_to_trolley) { nil }

    before do
      trolley_product_adder = double(Woolworths::API::AddProductToTrolley)
      allow(Woolworths::API::AddProductToTrolley).to receive(:new).and_return(trolley_product_adder)
      allow(trolley_product_adder).to receive(:call).and_raise(Woolworths::API::AddProductToTrolley::CannotProcessProductToTrolley)
    end

    it 'adds an error to the woolworths trolley product for the specified order line' do
      expected_errors = ["Could not add/update #{menu_item.name} to the Woolworths trolley."]
      expect do
        subject
      end.to change { trolley_product.reload.trolley_errors }.from([]).to(expected_errors)
    end
  end

  context 'with pricing mismatch' do
    before do
      order_line.update_columns(price: 15, cost: 10)
    end
    let(:product_added_to_trolley) { Woolworths::API::AddProductToTrolley::TrolleyProduct.new(menu_item, true, 0, 8) }

    it 'updates the order line costs and price based on passed in in_store_price (no order line supplier)' do
      subject

      order_line.reload
      expect(order_line.cost.round(2).to_s).to eq(8.0.round(2).to_s)
      expect(order_line.price.round(2).to_s).to eq(8.0.round(2).to_s)
    end

    context 'with supplier markups / commission' do
      let!(:supplier) { create(:supplier_profile, :random, markup: 0, commission_rate: 0) }
      before do
        supplier.update_columns(markup: 10, commission_rate: 10)
        order_line.update_columns(supplier_profile_id: supplier.id)
      end

      it 'updates the order line costs and price based on passed in in_store_price and supplier commission' do
        subject

        order_line.reload
        expect(order_line.cost.round(2).to_s).to eq(7.2.round(2).to_s) # in_store price * (1 - (supplier.commission_rate / 100))
        expect(order_line.price.round(2).to_s).to eq(8.8.round(2).to_s) # in_store price * (1 + (supplier.markup / 100))
      end

      context 'with markup overrides' do
        let!(:customer) { create(:customer_profile, :random) }
        let!(:markup_override) { create(:supplier_markup_override, :random, overridable: customer, supplier_profile: supplier, markup: 15, commission_rate: 15) }
        before do
          order.update_column(:customer_profile_id, customer.id)
        end

        it 'updates the order line costs and price based on passed in in_store_price and markup overrides' do
          subject

          order_line.reload
          expect(order_line.cost.round(2).to_s).to eq(6.8.round(2).to_s) # in_store price * (1 - (markup_override.commission_rate / 100))
          expect(order_line.price.round(2).to_s).to eq(9.2.round(2).to_s) # in_store price * (1 + (markup_override.markup / 100))
        end
      end # with markup overrides
    end # with supplier commisisons

    context 'slack notification' do
      before do
        # mock slack notifier
        allow(SlackNotifier).to receive(:send)

        # activate price check notification
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :pricing_check).and_return(true)
      end

      it 'send a slack notification' do
        expected_message = ":warning: Woolworths Product Pricing Mistmatch for order ##{order.id}"
        expect(SlackNotifier).to receive(:send).with(expected_message, attachments: anything)

        subject
      end

      context 'with non-available items' do
        let(:product_added_to_trolley) { Woolworths::API::AddProductToTrolley::TrolleyProduct.new(menu_item, false, 0, 8) }

        it 'does not update order line pricing and does not notify on Slack' do
          expect(SlackNotifier).to_not receive(:send)

          subject
          order_line.reload
          expect(order_line.cost.round(2).to_s).to eq(10.0.round(2).to_s)
          expect(order_line.price.round(2).to_s).to eq(15.0.round(2).to_s)
        end
      end # non available items
    end # slack notifications
  end # pricing mismatch

  context 'when the product of the order_line is removed' do
    let(:product_added_to_trolley) { Woolworths::API::AddProductToTrolley::TrolleyProduct.new(menu_item, false, 0, 10) }
    let!(:existing_error) { { menu_item.id.to_s => 'Item is currently unavailable.' } }
    before do
      trolley_product_adder = double(Woolworths::API::AddProductToTrolley)
      allow(Woolworths::API::AddProductToTrolley).to receive(:new).and_return(trolley_product_adder)
      allow(trolley_product_adder).to receive(:call).and_return(product_added_to_trolley)
      order_line.update_column(:quantity, 0)
    end

    it 'removes the trolley product altogether' do
      subject
      expect{ trolley_product.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end

  context 'with multiple order lines for the same menu item' do
    let(:order_line2) { create(:order_line, :random, order: order, menu_item: menu_item, quantity: 7) }
    let(:product_added_to_trolley) { nil }

    it 'passes the right (cumulative) quantity to woolworths' do
      expected_trolley_product_data = {
        menu_item: order_line.menu_item,
        quantity: order_line.quantity + order_line2.quantity
      }
      expect(Woolworths::API::AddProductToTrolley).to receive(:new).with(connection: woolworths_connection, product: expected_trolley_product_data)

      subject
    end
  end
end
