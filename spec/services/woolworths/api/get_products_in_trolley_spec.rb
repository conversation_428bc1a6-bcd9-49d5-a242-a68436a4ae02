require 'rails_helper'

RSpec.describe Woolworths::API::GetProductsInTrolley, type: :service, woolworths: true do
  subject { Woolworths::API::GetProductsInTrolley.new(connection: woolworths_connection).call }

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:menu_section) { create(:menu_section, supplier_profile: supplier) }
  let!(:menu_item_1) { create(:menu_item, supplier_profile: supplier, menu_section: menu_section, name: 'Product #1', sku: '000000000000123456') }
  let!(:menu_item_2) { create(:menu_item, supplier_profile: supplier, menu_section: menu_section, name: 'Product #2', sku: '000000000000654321') }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier.id)
    expect(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::TROLLEY_ENDPOINT).and_return(trolley_response)
  end

  context 'the trolley can be retrieved' do
    let(:trolley_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        # This is not every key, just the ones we use.
        items: [
          {
            article: menu_item_1.sku,
            itemquantityintrolley: 3
          },
          {
            article: menu_item_2.sku,
            itemquantityintrolley: 1
          }
        ],
        totalproducts: 3,
        deliveryfee: 2.50,
        totaltrolleyprice: 57.99
      })
    end

    it 'returns the trolley' do
      expect(subject).to be_a Woolworths::API::GetProductsInTrolley::Trolley

      expect(subject.products.count).to eq 2

      expect(subject.products.first).to be_a Woolworths::API::GetProductsInTrolley::TrolleyProduct
      expect(subject.products.first.menu_item).to eq menu_item_1
      expect(subject.products.first.quantity).to eq 3

      expect(subject.products.second).to be_a Woolworths::API::GetProductsInTrolley::TrolleyProduct
      expect(subject.products.second.menu_item).to eq menu_item_2
      expect(subject.products.second.quantity).to eq 1

      expect(subject.products_count).to eq 3
      expect(subject.delivery_fee).to eq 2.50
      expect(subject.total_price).to eq 57.99
    end
  end

  context 'the trolley cannot be retrieved' do
    let(:trolley_response) { double(Woolworths::API::Connection::Response, status: 500) }

    it 'raises an exception' do
      expect { subject }.to raise_error(Woolworths::API::GetProductsInTrolley::TrolleyProductRetrievalError)
    end
  end
end
