require 'rails_helper'

RSpec.describe Woolworths::API::DeleteAddressById, type: :service, woolworths: true do
  subject { Woolworths::API::DeleteAddressById.new(account: woolworths_account, connection: woolworths_connection, address_id: address_id).call }

  let(:woolworths_account) { create(:woolworths_account, :random) }
  let(:address_id) { rand(1000..2000) }
  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  let(:delete_addresses_response) do
    double(Woolworths::API::Connection::Response,
      status: 200,
      body: {
        deleted: 'true'
      }
    )
  end

  before do
    allow(woolworths_connection).to receive(:request).and_return(delete_addresses_response)
  end

  context 'can find the address' do
    before do
      expect(woolworths_connection).to receive(:request).with(method: :delete, path: "#{Woolworths::API::ADDRESS_ENDPOINT}/#{address_id}").and_return(delete_address_response)
    end

    context 'the address could be deleted' do
      let(:delete_address_response) { double(Woolworths::API::Connection::Response, status: 200) }

      it 'returns true' do
        expect(subject).to be true
      end
    end

    context 'the address could not be deleted' do
      let(:delete_address_response) { double(Woolworths::API::Connection::Response, status: [500, 400].sample) }

      it 'raises an exception' do
        expect { subject }.to raise_error(RuntimeError, "Could not delete the address for account ##{woolworths_account.id} with ID #{address_id}")
      end
    end
  end

end
