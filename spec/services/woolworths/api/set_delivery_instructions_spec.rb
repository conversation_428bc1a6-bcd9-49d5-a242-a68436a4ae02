require 'rails_helper'

RSpec.describe Woolworths::API::SetDeliveryInstructions, type: :service, woolworths: true do
  subject { Woolworths::API::SetDeliveryInstructions.new(order: order, connection: woolworths_connection).call }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  let(:order) { create(:order, :random, delivery_instruction: 'Beware of the dog!') }

  before do
    allow(woolworths_connection).to receive(:request).and_return(response)
  end

  context 'the delivery instructions could be set' do
    let(:response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        delivery: {
          address: {
            id: 1_234_567,
            text: '1 High Street, SYDNEY 2000',
            windowshref: 'https://uat.mobile-api.woolworths.com.au/wow/v2/fulfilment/windows'
          },
          window: {
            id: 7_654_321,
            text: 'Monday between 9:00am and 5:00pm'
          }
        },
        pickup: {},
        instructions: order.delivery_instruction,
        fulfilmentstoreid: 1234
      })
    end

    it 'makes a request to send the delivery instructions' do
      expect(woolworths_connection).to receive(:request).with(method: :post, path: Woolworths::API::DELIVERY_INSTRUCTIONS_ENPOINT, body: { instructions: order.delivery_instruction })

      subject
    end

    it 'sanitizes multi-line delivery instructions before sending it to Woolworths' do
      order.update_column(:delivery_instruction, "line-1-delivery-instructions. \nline-2-delivery-instruction.")

      expect(woolworths_connection).to receive(:request).with(method: :post, path: Woolworths::API::DELIVERY_INSTRUCTIONS_ENPOINT, body: { instructions: 'line-1-delivery-instructions. line-2-delivery-instruction.' })

      subject
    end

    it 'returns true' do
      expect(subject).to be true
    end

    it 'doesn\'t make a request if the delivery instructions are blank' do
      order.update_column(:delivery_instruction, [nil, '', "\n\n"].sample)

      expect(woolworths_connection).to_not receive(:request)

      subject
    end
  end

  context 'the delivery instructions could not be set' do
    let(:response) { double(Woolworths::API::Connection::Response, status: 500) }

    it 'raises an exception' do
      expect { subject }.to raise_error(RuntimeError, 'Could not set delivery instructions')
    end
  end
end
