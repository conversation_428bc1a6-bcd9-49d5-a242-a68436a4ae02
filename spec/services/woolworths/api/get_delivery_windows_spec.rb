require 'rails_helper'

RSpec.describe Woolworths::API::GetDeliveryWindows, type: :service, woolworths: true do

  subject { Woolworths::API::GetDeliveryWindows.new(connection: woolworths_connection).call }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  before do
    expect(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::DELIVERY_WINDOWS_ENDPOINT).and_return(response)
  end

  context 'could get the delivery windows' do
    let(:response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        # This is significantly stripped down. The real response data has 7 windows,
        # and each window has between 18 and 24 slots.
        windows: [
          {
            selected: false,
            date: '2019-06-06T00:00:00',
            available: true,
            slots: [
              {
                windowid: 141_679,
                selected: false,
                available: true,
                starttime: '2019-06-06T17:00:00',
                endtime: '2019-06-06T23:00:00',
                status: 'Available',
                promoprice: 6,
                price: 6
              },
              {
                windowid: 48_054,
                selected: false,
                available: true,
                starttime: '2019-06-06T20:00:00',
                endtime: '2019-06-06T22:00:00',
                status: 'Available',
                promoprice: 6,
                price: 6
              }
            ]
          },
          {
            selected: false,
            date: '2019-06-12T00:00:00',
            available: true,
            slots: [
              {
                windowid: 47_983,
                selected: false,
                available: true,
                starttime: '2019-06-12T05:00:00',
                endtime: '2019-06-12T08:00:00',
                status: 'Available',
                promoprice: 6,
                price: 6
              }
            ]
          }
        ]
      })
    end

    it 'returns the delivery window data' do
      expect(subject).to eq response.body
    end
  end

  context 'could not get the delivery windows' do
    let(:response) { double(Woolworths::API::Connection::Response, status: 500, body: {}) }

    it 'raises an exception' do
      expect { subject }.to raise_error(RuntimeError, 'Could not get delivery windows')
    end
  end
end
