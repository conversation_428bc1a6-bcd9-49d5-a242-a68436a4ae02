require 'rails_helper'

RSpec.describe Woolworths::API::AddProductToTrolley, type: :service, woolworths: true do

  subject { Woolworths::API::AddProductToTrolley.new(connection: woolworths_connection, product: product).call }

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:menu_section) { create(:menu_section, supplier_profile: supplier) }
  let!(:menu_item) { create(:menu_item, supplier_profile: supplier, menu_section: menu_section, name: 'Product #1', sku: '000000000000123456') }

  let(:product) do
    { menu_item: menu_item, quantity: 30 }
  end

  let(:products_data) do
    [
      { article: menu_item.sku, itemquantityintrolley: 30 }
    ]
  end

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier.id)
    expect(woolworths_connection).to receive(:request).with(method: :post, path: Woolworths::API::TROLLEY_ITEMS_ENDPOINT, body: { items: products_data, replacetrolley: false }).and_return(trolley_response)
  end

  let(:trolley_response) do
    double(Woolworths::API::Connection::Response, status: 200, body: {
      # API returns all products in trolley.
      totalproducts: 3,
      items: [
        {
          name: menu_item.name,
          article: menu_item.sku,
          itemquantityintrolley: 30,
          maxquantitylimit: 36, 
          is: {
            ranged: true
          },
          instoreprice: {
            pricegst: rand(20.01..30.99)
          }
        },
        {
          name: Faker::Name.name,
          article: SecureRandom.hex(7),
          itemquantityintrolley: 15,
          maxquantitylimit: 36,
          is: {
            ranged: false
          },
          instoreprice: {
            pricegst: rand(20.01..30.99)
          }
        },
        {
          name: Faker::Name.name,
          article: SecureRandom.hex(7),
          itemquantityintrolley: 17,
          maxquantitylimit: 36,
          is: {
            ranged: true
          },
          instoreprice: {
            pricegst: rand(20.01..30.99)
          }
        }
      ]
    })
  end

  it 'adds the products to the trolley, and returns the added product in the trolley, their availability, quantity added and instore price' do
    added_trolley_product = subject

    expect(added_trolley_product).to be_a(Woolworths::API::AddProductToTrolley::TrolleyProduct)
    expect(added_trolley_product.menu_item).to eq menu_item
    expect(added_trolley_product.available).to be_truthy
    expect(added_trolley_product.stock_quantity).to be_nil
    expected_instore_price = trolley_response.body[:items][0].dig(:instoreprice, :pricegst)
    expect(added_trolley_product.instore_price.round(2).to_s).to eq(expected_instore_price.round(2).to_s)
  end

  context 'with promotional price' do
    let(:trolley_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        # API returns all products in trolley.
        totalproducts: 1,
        items: [
          {
            name: menu_item.name,
            article: menu_item.sku,
            itemquantityintrolley: 30,
            maxquantitylimit: 36, 
            is: {
              ranged: true
            },
            instoreprice: {
              pricegst: rand(20.01..30.99)
            },
            promotions: {
              price: rand(10.01..19.99)
            }
          },
        ]
      })
    end

    it 'returns the troley product with promotional price' do
      added_trolley_product = subject

      expected_instore_price = trolley_response.body[:items][0].dig(:promotions, :price)
      expect(added_trolley_product.instore_price.round(2).to_s).to eq(expected_instore_price.round(2).to_s)
    end
  end

  context 'with mismatched (max stock) quantity' do
    let(:product) do
      { menu_item: menu_item, quantity: 40 }
    end

    let(:products_data) do
      [
        { article: menu_item.sku, itemquantityintrolley: 40 }
      ]
    end
    let(:trolley_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        # API returns all products in trolley.
        totalproducts: 1,
        items: [
          {
            name: menu_item.name,
            article: menu_item.sku,
            itemquantityintrolley: 36,  # item quantity in store set to limit
            maxquantitylimit: 36, 
            is: {
              ranged: true
            },
            instoreprice: {
              pricegst: rand(20.01..30.99)
            },
          },
        ]
      })
    end

    it 'returns the troley product with updated stock quantity (itemquantityintrolley)' do
      added_trolley_product = subject

      expect(added_trolley_product.stock_quantity).to be_present
      expect(added_trolley_product.stock_quantity).to eq(trolley_response.body[:items][0][:maxquantitylimit]) # 36 same as returned :itemquantityintrolley
    end
  end

  context 'with mismatched (limited stock) quantity' do
    let(:product) do
      { menu_item: menu_item, quantity: 10 }
    end

    let(:products_data) do
      [
        { article: menu_item.sku, itemquantityintrolley: 10 }
      ]
    end
    let(:trolley_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        # API returns all products in trolley.
        totalproducts: 1,
        items: [
          {
            name: menu_item.name,
            article: menu_item.sku,
            itemquantityintrolley: 10, # item quantity in store set to sent quanity
            maxquantitylimit: 5, # item has less items
            is: {
              ranged: true
            },
            instoreprice: {
              pricegst: rand(20.01..30.99)
            },
          },
        ]
      })
    end

    it 'returns the troley product with updated stock quantity (maxquantitylimit)' do
      added_trolley_product = subject

      expect(added_trolley_product.stock_quantity).to be_present
      expect(added_trolley_product.stock_quantity).to eq(trolley_response.body[:items][0][:maxquantitylimit]) # 5
    end
  end

  context 'with non ranged item' do
    let(:trolley_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        # API returns all products in trolley.
        totalproducts: 1,
        items: [
          {
            name: menu_item.name,
            article: menu_item.sku,
            itemquantityintrolley: 30,
            maxquantitylimit: 36, 
            is: {
              ranged: false
            },
            instoreprice: {
              pricegst: rand(20.01..30.99)
            }
          },
        ]
      })
    end

    it 'returns the troley product as unavailable' do
      added_trolley_product = subject

      expect(added_trolley_product.available).to be_falsey
    end
  end

  context 'the products cannot be added to the trolley (API responds with a non-200 status)' do
    let(:trolley_response) { double(Woolworths::API::Connection::Response, status: 500, body: {}) }

    it 'attempts to add the products to the trolley, but raises an exception' do
      expect { subject }.to raise_error Woolworths::API::AddProductToTrolley::CannotProcessProductToTrolley
    end
  end

  context 'the products cannot be added to the trolley (API responds with a 200 status but an error message about Delivery Savers)' do
    let(:trolley_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        errors: [
          {
            article: menu_item.sku,
            message: 'Ooops you can only have a maximum of 2 active Delivery Savers on your account at any one time.  These will be used consecutively.'
          }
        ]
      })
    end

    it 'attempts to add the products to the trolley, but raises an exception' do
      expect { subject }.to raise_error Woolworths::API::AddProductToTrolley::ExceededDeliverySaversLimitError
    end
  end
end
