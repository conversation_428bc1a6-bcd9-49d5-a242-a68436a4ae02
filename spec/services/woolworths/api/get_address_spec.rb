require 'rails_helper'

RSpec.describe Woolworths::API::GetAddress, type: :service, woolworths: true do
  subject { Woolworths::API::GetAddress.new(connection: woolworths_connection, street_1: street_1, suburb: suburb, postcode: postcode).call }

  let(:street_1) { '1 High Street' }
  let(:suburb) { 'Sydney' }
  let(:postcode) { '2000' }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  before do
    expect(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::ADDRESS_ENDPOINT).and_return(get_addresses_response)
  end

  context 'could retrieve addresses' do
    let(:address_1) { { id: 1, street1: '1 Main Street', street2: '', suburbname: 'SYDNEY', postalcode: '2000' } }
    let(:address_2) { { id: 2, street1: '1 High Street', street2: '', suburbname: 'BARANGAROO', postalcode: '2000' } }
    let(:address_3) { { id: 3, street1: '1 High Street', street2: '', suburbname: 'SYDNEY', postalcode: '2000' } }

    let(:get_addresses_response) { double(Woolworths::API::Connection::Response, status: 200, body: { addresses: [address_1, address_2, address_3] }) }

    it 'returns the address' do
      expect(subject).to eq address_3
    end
  end

  context 'could not retrieve addresses' do
    let(:get_addresses_response) { double(Woolworths::API::Connection::Response, status: 500) }

    it 'raises an exception' do
      expect { subject }.to raise_error(RuntimeError, 'Could not retrieve saved addresses')
    end
  end
end
