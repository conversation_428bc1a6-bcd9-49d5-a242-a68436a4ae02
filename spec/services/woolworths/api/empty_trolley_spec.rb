require 'rails_helper'

RSpec.describe Woolworths::API::EmptyTrolley, type: :service, woolworths: true do

  subject { Woolworths::API::EmptyTrolley.new(order: order).call }

  let(:woolworths_account) { create(:woolworths_account, :random) }
  let(:order) { create(:order, :random) }
  let!(:woolworths_order) { create(:woolworths_order, :random, account: woolworths_account, order: order, account_in_use: true) }
  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  before do
    expect(Woolworths::API::Connection).to receive(:new).with(account: woolworths_account, authenticated: true).and_return(woolworths_connection)
  end

  context 'for a new order (no order lines)' do

    it 'empties the trolley successfully' do
      clear_trolley_response = double(Woolworths::API::Connection::Response, status: 200, body: {})
      expect(woolworths_connection).to receive(:request).with(method: :post, path: Woolworths::API::EMPTY_TROLLEY_ENDPOINT).and_return(clear_trolley_response)

      subject
    end

    it 'raises an exception when trolley cannot be successfully emptied' do
      error_trolley_response = double(Woolworths::API::Connection::Response, status: 500, body: {})
      expect(woolworths_connection).to receive(:request).with(method: :post, path: Woolworths::API::EMPTY_TROLLEY_ENDPOINT).and_return(error_trolley_response)
      expect { subject }.to raise_error(Woolworths::API::EmptyTrolley::EmptyTrolleyError)
    end

  end

end
