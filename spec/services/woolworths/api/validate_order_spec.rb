require 'rails_helper'

RSpec.describe Woolworths::API::ValidateOrder, type: :service, woolworths: true do
  let(:woolworths_connection) { double(Woolworths::API::Connection) }
  let(:initial_delivery_at) { Time.zone.local(2019, 10, 1, 12) }

  let(:woolworths_account) { build(:woolworths_account, :random) }
  let(:order) { create(:order, :random, delivery_at: initial_delivery_at) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account_in_use: true, account: woolworths_account, delivery_window_id: 48_054) }

  before do
    allow(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::VALIDATE_ENDPOINT).and_return(checkout_response)
  end

  context 'with an valid Woolworths Order response' do
    let(:checkout_response) { double(Woolworths::API::Connection::Response, status: 200, body: {}) }

    it 'returns a successful result' do
      order_validator = Woolworths::API::ValidateOrder.new(connection: woolworths_connection).call

      expect(order_validator).to be_success
    end

    context 'with delivery fee' do
      let(:checkout_response) do
        double(Woolworths::API::Connection::Response, status: 200, body: {
          deliveryFee: 15,
        })
      end

      it 'returns the delivery_fee' do
        order_validator = Woolworths::API::ValidateOrder.new(connection: woolworths_connection).call

        expect(order_validator).to be_success
        expect(order_validator.delivery_fee).to eq(15.0)
      end

      context 'with packaging fee' do
        let(:checkout_response) do
          double(Woolworths::API::Connection::Response, status: 200, body: {
            deliveryFee: 15,
            packagingFee: {
              amount: 1,
              label: 'Reusable bags',
              customerMessage: 'Your order will now be packed into reusable bags'
            },
          })
        end

        it 'returns the delivert fee along with the packaging fee' do
          order_validator = Woolworths::API::ValidateOrder.new(connection: woolworths_connection).call

          expect(order_validator).to be_success
          expect(order_validator.delivery_fee).to eq(16.0)
        end
      end # with packaging fee
    end # with delivery fee
  end # valid response

  context 'with an invalid Woolworths Order response' do
    let(:checkout_response) do
      double(Woolworths::API::Connection::Response, status: 400, body: {
        # There are a lot more keys in the response than this, but these are
        # just the ones we care about.
        placed: false,
        subtotal: 45.00,
        deliveryFee: 15,
        total: 60.00,
        totalRemaining: 60.00,
        errors: [
          {
            Key: 'MinimumSpend',
            ErrorCode: 1001,
            Message: 'The subtotal of $45.00 is less than the Delivery minimum spend of $50.00.'
          },
          {
            Key: 'UnavailableItems',
            ErrorCode: 1007,
            Message: 'Checkout Order has unavailable items.'
          },
          {
            Key: 'GenericError',
            ErrorCode: 9999,
            Message: 'Generic Woolworths Error.'
          }
        ],
        canProceedToPayment: false
      })
    end

    it 'returns the sanitized error messages' do
      order_validator = Woolworths::API::ValidateOrder.new(connection: woolworths_connection).call

      expect(order_validator).to_not be_success
      expect(order_validator.errors).to include('Checkout Order has unavailable items.', 'Generic Woolworths Error.')
    end

    it 'sanitized the MinimumSpend error message' do
      order_validator = Woolworths::API::ValidateOrder.new(connection: woolworths_connection).call

      expect(order_validator).to_not be_success
      expect(order_validator.errors).to_not include('The subtotal of $45.00 is less than the Delivery minimum spend of $50.00.')
      expect(order_validator.errors).to include('MinimumSpend')
    end
  end

end
