require 'rails_helper'

RSpec.describe Woolworths::API::Connection, type: :lib, woolworths: true do

  let(:valid_connection_response) do
    double(Woolworths::API::Connection::Response, status: 200, body: {
      # There are many more keys in the real response body; these are just the ones we care about.
      access_token: 'access-token',
      refresh_token: 'refresh-token',
      expires_in: '3299',
      fulfilmentstoreid: '1800',
      deliveryaddressstreet1: '1 High Street',
      deliveryaddressstreet2: '',
      deliveryaddresssuburb: 'SYDNEY'
    })
  end

  let(:errored_connection_response) { double(Woolworths::API::Connection::Response, status: 400, body: {}) }

  context 'using the importer Woolworths API account from the credentials' do
    let(:connection) { Woolworths::API::Connection.new(use_importer_account: true) }
    let(:expected_authentication_credentials) { { user_name: 'woolworths_account_email', password: 'woolworths_account_password' } }

    before do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(anything, anything).and_return('')
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :yordar_account_email).and_return('woolworths_account_email')
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :yordar_account_password).and_return('woolworths_account_password')
      allow(connection).to receive(:request).with(method: :post, path: 'v2/commerce/token', body: expected_authentication_credentials).and_return(valid_connection_response)
    end

    it 'authenticates the connection to Woolworths using the credentials in the environment variables (used by the importer process)' do
      expect(connection).to receive(:request).with(method: :post, path: 'v2/commerce/token', body: expected_authentication_credentials).and_return(valid_connection_response)
      connection.authenticate
      expect(connection.access_token).to be_present
    end
  end

  context 'using a woolworths account' do
    let(:connection) { Woolworths::API::Connection.new(account: woolworths_account) }
    let(:preauth_connection) { Woolworths::API::Connection.new(account: woolworths_account, authenticated: true) }

    let(:woolworths_account) { create(:woolworths_account, :random) }
    let(:expected_authentication_credentials) { { user_name: woolworths_account.try(:email), password: woolworths_account.try(:password) } }

    before do
      allow_any_instance_of(Woolworths::API::Connection).to receive(:request).with(method: :post, path: 'v2/commerce/token', body: expected_authentication_credentials).and_return(valid_connection_response)
    end

    it 'authenticates the connection to Woolworths using the credentials in the specified account record' do
      expect(connection).to receive(:request).with(method: :post, path: 'v2/commerce/token', body: expected_authentication_credentials).and_return(valid_connection_response)
      expect(connection.access_token).to_not be_present
      connection.authenticate
      expect(connection.access_token).to be_present
    end

    it 'pre-authenticates the connection to Woolworths using the credentials in the specified account record' do
      # cannot expect as the object calls authenticate as part of initialization
      # expect(preauth_connection).to receive(:request).with(method: :post, path: 'v2/commerce/token', body: expected_authentication_credentials).and_return(valid_connection_response)
      expect(preauth_connection.access_token).to be_present # token present without explicit authentication
    end

    it 'saves the access_token, refresh_token and expiry against the woolworths account on authentication' do
      if [true, false].sample
        preauth_connection
      else
        connection.authenticate
      end
      woolworths_account.reload
      expect(woolworths_account.access_token).to eq('access-token')
      expect(woolworths_account.refresh_token).to eq('refresh-token')
      expect(woolworths_account.token_expires_at).to be_within(0.2).of(Time.now + 3299.seconds)
    end

    context 'with a refresh token' do

      before do
        woolworths_account.update_columns(
          access_token: 'access-token-old',
          refresh_token: 'refresh-token-old',
          token_expires_at: Time.now - 1.day
        )
      end

      it 'refreshes the woolworths account token on pre-authentication if token has expired' do
        expect_any_instance_of(Woolworths::API::Connection).to receive(:request).with(method: :post, path: 'v2/commerce/token/refresh-token-old', body: expected_authentication_credentials).and_return(valid_connection_response)

        preauth_connection
        woolworths_account.reload
        expect(woolworths_account.access_token).to eq('access-token')
        expect(woolworths_account.refresh_token).to eq('refresh-token')
        expect(woolworths_account.token_expires_at).to be_within(0.2).of(Time.now + 3299.seconds)
      end

      context 'invalid refresh token' do
        before do
          allow_any_instance_of(Woolworths::API::Connection).to receive(:request).with(method: :post, path: 'v2/commerce/token/refresh-token-old', body: expected_authentication_credentials).and_return(errored_connection_response)
        end

        it 'gets a new access and refresh tokens, if the old refresh token is invalid' do
          expect_any_instance_of(Woolworths::API::Connection).to receive(:request).with(method: :post, path: 'v2/commerce/token', body: expected_authentication_credentials).and_return(valid_connection_response)

          preauth_connection
          woolworths_account.reload
          expect(woolworths_account.access_token).to eq('access-token')
          expect(woolworths_account.refresh_token).to eq('refresh-token')
          expect(woolworths_account.token_expires_at).to be_within(0.2).of(Time.now + 3299.seconds)
        end
      end
    end

    context 'with connection error to the API' do

      before do
        allow_any_instance_of(Woolworths::API::Connection).to receive(:request).with(method: :post, path: 'v2/commerce/token', body: expected_authentication_credentials).and_return(errored_connection_response)
      end
      it 'raises an error if the response is not successful' do
        expect{ connection.authenticate }.to raise_error Woolworths::API::Connection::ConnectionError
        expect{ preauth_connection }.to raise_error Woolworths::API::Connection::ConnectionError
      end
    end

    context 'the Woolworths API account is nil' do
      let(:woolworths_account) { nil }

      it 'raises an exception' do
        expect { connection.authenticate }.to raise_error Woolworths::API::Connection::NoAccountError
        expect { preauth_connection }.to raise_error Woolworths::API::Connection::NoAccountError
      end
    end
  end

end
