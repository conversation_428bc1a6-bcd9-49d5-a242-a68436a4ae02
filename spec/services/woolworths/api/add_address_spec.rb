require 'rails_helper'

RSpec.describe Woolworths::API::AddAddress, type: :service, woolworths: true do
  subject { Woolworths::API::AddAddress.new(connection: woolworths_connection, street_1: street_1, street_2: street_2, suburb: suburb, postcode: postcode).call }

  let(:street_1) { '1 High Street' }
  let(:street_2) { nil }
  let(:suburb) { 'Barangaroo' }
  let(:postcode) { '2000' }

  let(:suburbs) do
    [
      { id: 1, text: 'SYDNEY', postcode: '2000' },
      { id: 2, text: 'BARANGAROO', postcode: '2000' },
      { id: 3, text: 'HAYMARKET', postcode: '2000' }
    ]
  end

  let(:woolworths_connection) { double(Woolworths::API::Connection) }
  let(:suburb_search_response) { double(Woolworths::API::Connection::Response, status: 200, body: { suburbs: suburbs }) }

  before do
    expect(Woolworths::API::DeleteAddress).to receive(:new).with(connection: woolworths_connection, street_1: street_1, street_2: street_2, suburb: suburb, postcode: postcode)
    expect(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::ADDRESS_ENDPOINT, params: { postcode: postcode }).and_return(suburb_search_response)
  end

  context 'suburb could be found' do
    before do
      expect(woolworths_connection).to receive(:request).with(method: :post, path: Woolworths::API::ADDRESS_ENDPOINT, body: { Street1: street_1, Street2: street_2, SuburbId: 2, Postcode: postcode, IsForBilling: false }).and_return(add_address_response)
    end

    context 'address could be added' do
      let(:add_address_response) { double(Woolworths::API::Connection::Response, status: 200, body: { id: 123, text: "#{street_1}, #{postcode}" }) }

      it 'returns the ID of the address' do
        expect(subject[:id]).to eq 123
      end
    end

    context 'address could not be added (request responded with a 500 status)' do
      let(:add_address_response) { double(Woolworths::API::Connection::Response, status: 500, body: {}) }

      it 'raises an exception' do
        expect { subject }.to raise_error(RuntimeError, 'Could not add address')
      end
    end
  end

  context 'suburb could not be found' do
    context 'the suburb search request responded with a 500 status' do
      let(:suburb_search_response) { double(Woolworths::API::Connection::Response, status: 500, body: {}) }

      it 'raises an exception' do
        expect { subject }.to raise_error(RuntimeError, "Could not search for suburb by postcode: #{postcode}")
      end
    end

    context 'the suburb search response does not include the suburb' do
      let(:suburb) { 'The Rocks' }

      it 'raises an exception' do
        expect { subject }.to raise_error(RuntimeError, "Could not find a suburb with postcode '#{postcode}' and name '#{suburb}'")
      end
    end
  end
end
