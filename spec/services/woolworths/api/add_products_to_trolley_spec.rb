require 'rails_helper'

RSpec.describe Woolworths::API::AddProductsToTrolley, type: :service, woolworths: true do

  subject { Woolworths::API::AddProductsToTrolley.new(connection: woolworths_connection, products: products).call }

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:menu_section) { create(:menu_section, supplier_profile: supplier) }
  let!(:menu_item_1) { create(:menu_item, supplier_profile: supplier, menu_section: menu_section, name: 'Product #1', sku: '000000000000123456') }
  let!(:menu_item_2) { create(:menu_item, supplier_profile: supplier, menu_section: menu_section, name: 'Product #2', sku: '000000000000654321') }
  let!(:menu_item_3) { create(:menu_item, supplier_profile: supplier, menu_section: menu_section, name: 'Product #3', sku: '000000000000135790') }
  let!(:menu_item_4) { create(:menu_item, supplier_profile: supplier, menu_section: menu_section, name: 'Product #4', sku: '000000000000135794') }
  let!(:menu_item_5) { create(:menu_item, supplier_profile: supplier, menu_section: menu_section, name: 'Product #5', sku: '000000000000135754') }

  let(:products) do
    [
      { menu_item: menu_item_3, quantity: 40 },
      { menu_item: menu_item_1, quantity: 10 },
      { menu_item: menu_item_2, quantity: 15 },
      { menu_item: menu_item_4, quantity: 17 },
      { menu_item: menu_item_5, quantity: 6 }
    ]
  end

  let(:products_data) do
    [
      { article: menu_item_3.sku, itemquantityintrolley: 40 },
      { article: menu_item_1.sku, itemquantityintrolley: 10 },
      { article: menu_item_2.sku, itemquantityintrolley: 15 },
      { article: menu_item_4.sku, itemquantityintrolley: 17 },
      { article: menu_item_5.sku, itemquantityintrolley: 6 }
    ]
  end

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier.id)
    expect(woolworths_connection).to receive(:request).with(method: :post, path: Woolworths::API::TROLLEY_ITEMS_ENDPOINT, body: { items: products_data, replacetrolley: false }).and_return(add_products_to_trolley_response)
  end

  context 'the products can be added to the trolley' do
    let(:add_products_to_trolley_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        # There are more attributes, but not including them all.
        totalproducts: 3,
        items: [
          # Quantity in trolley is different to what we attempted to add.
          {
            name: menu_item_3.name,
            article: menu_item_3.sku,
            itemquantityintrolley: 36, # item quantity in store set to limit
            maxquantitylimit: 36, 
            is: {
              ranged: true
            }
          },
          {
            name: menu_item_1.name,
            article: menu_item_1.sku,
            itemquantityintrolley: 10, # item quantity in store set to sent quanity
            maxquantitylimit: 5, # item has less items
            instoreprice: {
              pricegst: 20
            },
            is: {
              ranged: true
            }
          },
          # Ranged is false.
          {
            name: menu_item_2.name,
            article: menu_item_2.sku,
            itemquantityintrolley: 15,
            maxquantitylimit: 36,
            is: {
              ranged: false
            }
          },
          {
            name: menu_item_4.name,
            article: "#{menu_item_4.sku}X", # item has a different code in Woolworths
            itemquantityintrolley: 17,
            maxquantitylimit: 36,
            is: {
              ranged: true
            }
          }
        ]
      })
    end

    it 'adds the products to the trolley, and returns an array of the products in the trolley, their availability, quantity added and instore price' do
      expect(subject).to be_a Array
      expect(subject.length).to eq 3

      expect(subject.first).to be_a Woolworths::API::AddProductsToTrolley::TrolleyProduct
      expect(subject.first.menu_item).to eq menu_item_3
      expect(subject.first.available).to be true # is available but added quantity is less than order line quantity
      expect(subject.first.stock_quantity).to eq 36 # same as max quanity and in-store quantity (Woolworths automatically sets quantity to 36)
      expect(subject.first.instore_price).to be_blank

      expect(subject.second).to be_a Woolworths::API::AddProductsToTrolley::TrolleyProduct
      expect(subject.second.menu_item).to eq menu_item_1
      expect(subject.second.available).to be true
      expect(subject.second.stock_quantity).to eq 5 # stock quantity is same as the max quantity
      expect(subject.second.instore_price).to eq 20

      expect(subject.third).to be_a Woolworths::API::AddProductsToTrolley::TrolleyProduct
      expect(subject.third.menu_item).to eq menu_item_2
      expect(subject.third.available).to be false
      expect(subject.third.stock_quantity).to be_nil
      expect(subject.third.instore_price).to be_blank
    end

    context 'Unavailable products' do
      it 'returns items as available with mismatched quantity (instore)' do
        product = subject.detect{|p| p.menu_item == menu_item_3 }

        expect(product).to be_present
        expect(product.stock_quantity).to eq(36)
        expect(product.available).to_not be_falsey
      end

      it 'returns items as available with mismatched quantity (maxquantity)' do
        product = subject.detect{|p| p.menu_item == menu_item_1 }

        expect(product).to be_present
        expect(product.stock_quantity).to eq(5)
        expect(product.available).to_not be_falsey
      end

      it 'returns items with similar item code but marks it as unavailable based on it not being ranged' do
        product = subject.detect{|p| p.menu_item == menu_item_2 }

        expect(product).to be_present
        expect(product.stock_quantity).to be_nil
        expect(product.available).to be_falsey
      end

      it 'does not return items which has a different code in woolworths' do
        expect(subject.map(&:menu_item)).to_not include(menu_item_4)
      end

      it 'does not return items which it does not find on the Woolworths side' do
        expect(subject.map(&:menu_item)).to_not include(menu_item_5)
      end
    end
  end

  context 'the products cannot be added to the trolley (API responds with a non-200 status)' do
    let(:add_products_to_trolley_response) { double(Woolworths::API::Connection::Response, status: 500, body: {}) }

    it 'attempts to add the products to the trolley, but raises an exception' do
      expect { subject }.to raise_error Woolworths::API::AddProductsToTrolley::CannotProcessProductToTrolley
    end
  end

  context 'the products cannot be added to the trolley (API responds with a 200 status but an error message about Delivery Savers)' do
    let(:add_products_to_trolley_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        errors: [
          {
            article: menu_item_1.sku,
            message: 'Ooops you can only have a maximum of 2 active Delivery Savers on your account at any one time.  These will be used consecutively.'
          }
        ]
      })
    end

    it 'attempts to add the products to the trolley, but raises an exception' do
      expect { subject }.to raise_error Woolworths::API::AddProductsToTrolley::ExceededDeliverySaversLimitError
    end
  end
end
