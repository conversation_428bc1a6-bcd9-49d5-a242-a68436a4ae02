require 'rails_helper'

RSpec.describe Woolworths::API::ListAddresses, type: :service, woolworths: true do
  subject { Woolworths::API::ListAddresses.new(connection: woolworths_connection, account: woolworths_account).call }

  let(:street_1) { '1 High Street' }
  let(:suburb) { 'Sydney' }
  let(:postcode) { '2000' }

  let!(:woolworths_account) { create(:woolworths_account, :random) }
  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  before do
    expect(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::ADDRESS_ENDPOINT).and_return(addresses_response)
  end

  context 'could retrieve addresses' do
    let(:addresses_response) do
      double(Woolworths::API::Connection::Response,
        status: 200,
        body: {
          addresses: [
            {
              id: 13_400_111,
              text: 'Level 2, 411 Collins St,  3000',
              isprimary: false,
              postalcode: '3000',
              street1: 'Level 2, 411 Collins St',
              street2: '',
              suburbid: 4539,
              suburbname: 'MELBOURNE',
              ispartner: false,
              partnerbranchid: nil
            },
            {
              id: 13_493_496,
              text: '263 Clarence St,  2000',
              isprimary: false,
              postalcode: '2000',
              street1: '263 <PERSON> St',
              street2: '',
              suburbid: 1,
              suburbname: 'SYDNEY',
              ispartner: false,
              partnerbranchid: nil
            }
          ]
        }
      )
    end

    it 'returns the address' do
      expect(subject).to match_array(addresses_response.body[:addresses])
    end
  end

  context 'could not retrieve addresses' do
    let(:addresses_response) do
      double(Woolworths::API::Connection::Response,
        status: [500, 400].sample,
        body: {
          errorDetail: {
            Message: 'woolworths-error-message'
          }
        }
      )
    end

    it 'raises an exception' do
      expect { subject }.to raise_error(RuntimeError, "Could not retrieve saved addresses for account with ID #{woolworths_account.id} - woolworths-error-message")
    end
  end
end
