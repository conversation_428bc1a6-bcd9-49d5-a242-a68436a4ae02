require 'rails_helper'

RSpec.describe Woolworths::API::SetDeliveryAddress, type: :service, woolworths: true do
  subject { Woolworths::API::SetDeliveryAddress.new(connection: woolworths_connection, address_id: address_id).call }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  let(:address_id) { 4_123_456 }

  before do
    expect(woolworths_connection).to receive(:request).with(method: :post, path: Woolworths::API::DELIVERY_ADDRESS_ENDPOINT, body: { address: address_id }).and_return(response)
  end

  context 'the delivery address could be set' do
    let(:response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        delivery: {
          address: {
            id: address_id,
            text: '1 High Street, SYDNEY 2000',
            windowshref: 'https://uat.mobile-api.woolworths.com.au/wow/v2/fulfilment/windows'
          },
          window: {}
        },
        pickup: {},
        instructions: '',
        fulfilmentstoreid: 1234
      })
    end

    it 'returns true' do
      expect(subject).to be true
    end
  end

  context 'the delivery address could not be set' do
    let(:response) { double(Woolworths::API::Connection::Response, status: 500) }

    it 'raises an exception' do
      expect { subject }.to raise_error(RuntimeError, 'Could not set delivery address')
    end
  end
end
