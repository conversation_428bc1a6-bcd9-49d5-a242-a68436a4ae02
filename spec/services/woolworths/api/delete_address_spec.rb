require 'rails_helper'

RSpec.describe Woolworths::API::DeleteAddress, type: :service, woolworths: true do
  subject { Woolworths::API::DeleteAddress.new(connection: woolworths_connection, street_1: street_1, street_2: street_2, suburb: suburb, postcode: postcode).call }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }

  let(:street_1) { '2 High Street' }
  let(:street_2) { nil }
  let(:suburb) { 'Barangaroo' }
  let(:postcode) { '2000' }

  let(:get_addresses_response) do
    double(Woolworths::API::Connection::Response, status: 200, body: {
      addresses: [
        { id: 123, street1: '1 High Street', street2: '', suburbname: 'BARANGARO<PERSON>', postalcode: '2000' },
        { id: 456, street1: '2 High Street', street2: '', suburbname: 'BARANGAROO', postalcode: '2000' },
        { id: 789, street1: '3 High Street', street2: '', suburbname: 'BARANGAROO', postalcode: '2000' }
      ]
    })
  end

  before do
    expect(woolworths_connection).to receive(:request).with(method: :get, path: Woolworths::API::ADDRESS_ENDPOINT).and_return(get_addresses_response)
  end

  context 'can find the address' do
    before do
      expect(woolworths_connection).to receive(:request).with(method: :delete, path: "#{Woolworths::API::ADDRESS_ENDPOINT}/456").and_return(delete_address_response)
    end

    context 'the address could be deleted' do
      let(:delete_address_response) { double(Woolworths::API::Connection::Response, status: 200) }

      it 'returns true' do
        expect(subject).to be true
      end
    end

    context 'the address could not be deleted' do
      let(:delete_address_response) { double(Woolworths::API::Connection::Response, status: 400) }

      it 'raises an exception' do
        expect { subject }.to raise_error(RuntimeError, 'Could not delete the address')
      end
    end
  end

  context 'cannot find the address' do
    context 'get addresses request responded with a 500 status' do
      let(:get_addresses_response) { double(Woolworths::API::Connection::Response, status: 500, body: {}) }

      it 'raises an exception' do
        expect { subject }.to raise_error(RuntimeError, 'Could not retrieve saved addresses')
      end
    end

    context 'the address is not included in the retrieved addresses' do
      let(:street_1) { '4 High Street' }

      it 'returns true (no need to delete the address as it does not appear to be saved)' do
        expect(subject).to be true
      end
    end
  end
end
