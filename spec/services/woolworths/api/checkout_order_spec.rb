require 'rails_helper'

RSpec.describe Woolworths::API::CheckoutOrder, type: :service, woolworths: true do
  subject { Woolworths::API::CheckoutOrder.new(order: order).call }

  let(:woolworths_connection) { double(Woolworths::API::Connection) }
  let(:woolworths_account) { create(:woolworths_account, :random) }
  let(:order) { create(:order, :random) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account: woolworths_account, account_in_use: true) }
  let(:woolworths_order_id) { 536_217_675 }

  before do
    # mock connection
    allow(Woolworths::API::Connection).to receive(:new).with(account: woolworths_account, authenticated: true).and_return(woolworths_connection)

    # mock checkout API request
    allow(woolworths_connection).to receive(:request).with(
      method: :post,
      path: Woolworths::API::CHECKOUT_ENDPOINT,
      body: {
        Password: woolworths_account.password,
      }
    ).and_return(response)

    # mock notification sender
    email_sender = delayed_email_sender = double(Admin::Emails::SendFailedWoolworthsCheckoutEmail)
    allow(Admin::Emails::SendFailedWoolworthsCheckoutEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  context 'checks out successfully' do
    let(:response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        IsSuccessful: true,
        OrderId: woolworths_order_id,
      })
    end
    it 'adds the woolworths order ID to the orders\'s woolworths status' do
      subject
      expect(order.woolworths_order.status).to include(woolworths_order_id.to_s)
    end

    it 'disconnects the order from the woolworths account' do
      subject
      expect(order.woolworths_account).to be_blank
      expect(order.attached_woolworths_account).to_not be_blank
      expect(order.woolworths_order.account_in_use).to be_falsey
    end
  end

  context 'check out errors' do
    let(:response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        IsSuccessful: false,
      })
    end
    it 'add error message to the orders\'s woolworth status' do
      subject
      expect(order.woolworths_order.status).to include('Payment with Woolworths errored.')
    end

    it 'disconnects the order from the woolworths account' do
      subject
      expect(order.woolworths_account).to be_blank
      expect(order.attached_woolworths_account).to_not be_blank
      expect(order.woolworths_order.account_in_use).to be_falsey
    end

    it 'notifies admin about the failed checkout' do
      expect(Admin::Emails::SendFailedWoolworthsCheckoutEmail).to receive(:new).with(order: order)

      subject
    end

    it 'logs the Woolworths checkout failed event', event_logs: true do
      expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'woolworths-checkout-failed', severity: 'error', account: woolworths_account.short_name)

      subject
    end
  end

  context 'Subsequent checkout wihtout attached woolworths account' do
    let(:response) do
      double(Woolworths::API::Connection::Response, status: 400, body: {
        IsSuccessful: false,
      })
    end
    before do
      order.woolworths_order.update_column(:account_id, nil)
    end
    it 'returns without changing anything if the order woolworth account isn\'t set' do
      subject
      expect(order.woolworths_order.status).to be_blank
    end
  end
end
