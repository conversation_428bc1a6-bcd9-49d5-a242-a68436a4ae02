require 'rails_helper'

RSpec.describe Woolworths::API::SetDeliveryWindow, type: :service, woolworths: true do
  let(:woolworths_connection) { double(Woolworths::API::Connection) }
  let(:order) { create(:order, :draft) }
  let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account_in_use: true, delivery_window_id: 47_123) }

  let(:window_date) { }
  let(:window_id) { 47_123 }

  before do
    allow(Rails.cache).to receive(:write)
  end

  context 'with a valid response where it could set the delivery window' do
    let(:valid_response) do
      double(Woolworths::API::Connection::Response, status: 200, body: {
        delivery: {
          address: {
            id: 4_123_456,
            text: '1 High Street, SYDNEY 2000',
            windowshref: 'https://uat.mobile-api.woolworths.com.au/wow/v2/fulfilment/windows'
          },
          window: {
            id: window_id,
            text: 'Monday between 5:00am and 8:00am'
          }
        },
        pickup: {},
        instructions: '',
        fulfilmentstoreid: 1234,
        confirmationhref: 'https://uat.mobile-api.woolworths.com.au/wow/v2/commerce/checkout/payment'
      })
    end

    before do
      allow(woolworths_connection).to receive(:request).and_return(valid_response)
    end

    it 'makes a POST request' do
      window_id = woolworths_order.delivery_window_id
      window_date = order.delivery_at.strftime('%Y-%m-%dT%H:%M:00.0000000')
      expect(woolworths_connection).to receive(:request).with(
        method: :post, path: 'v2/fulfilment/window',
        body: {
          window: window_id,
          date: window_date
        }
      )
      Woolworths::API::SetDeliveryWindow.new(order: order, connection: woolworths_connection).call
    end

    it 'makes a POST request with passed in window ID' do
      custom_window_id = 'custom_window_id'
      window_date = order.delivery_at.strftime('%Y-%m-%dT%H:%M:00.0000000')
      expect(woolworths_connection).to receive(:request).with(
        method: :post, path: 'v2/fulfilment/window',
        body: {
          window: custom_window_id,
          date: window_date
        }
      )

      Woolworths::API::SetDeliveryWindow.new(order: order, connection: woolworths_connection, window_id: custom_window_id).call
    end

    it 'returns the delivery window text' do
      window_setter = Woolworths::API::SetDeliveryWindow.new(order: order, connection: woolworths_connection)

      delivery_window_text = window_setter.call
      expect(delivery_window_text).to eq('Monday between 5:00am and 8:00am') # response[:delivery][:window][:text]
    end

    it 'saves the delivery window text against the woolworths_order' do
      Woolworths::API::SetDeliveryWindow.new(order: order, connection: woolworths_connection).call

      woolworths_order.reload
      expect(woolworths_order.delivery_window_text).to eq('Monday between 5:00am and 8:00am')  # response[:delivery][:window][:text]
    end
  end

  context 'could not set the delivery window' do
    let(:invalid_response) { double(Woolworths::API::Connection::Response, status: 500) }

    before do
      allow(woolworths_connection).to receive(:request).and_return(invalid_response)
    end

    it 'raises an exception' do
      window_setter = Woolworths::API::SetDeliveryWindow.new(order: order, connection: woolworths_connection)
      expect { window_setter.call }.to raise_error(Woolworths::API::SetDeliveryWindow::DeliveryWindowError, 'Could not set the delivery window')
    end
  end
end
