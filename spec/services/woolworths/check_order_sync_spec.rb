require 'rails_helper'

RSpec.describe Woolworths::CheckOrderSync, type: :service, woolworths: true do
  let(:order) { create(:order, :random) }
  let(:woolworths_order) { create(:woolworths_order, :random, order: order) }
  let(:menu_item1) { create(:menu_item, :random) }
  let!(:order_line1) { create(:order_line, :random, order: order, menu_item: menu_item1, quantity: 5) }

  let(:menu_item2) { create(:menu_item, :random) }
  let!(:order_line2) { create(:order_line, :random, order: order, menu_item: menu_item2, quantity: 5) }

  let(:menu_item3) { create(:menu_item, :random) }
  let!(:order_line3) { create(:order_line, :random, order: order, menu_item: menu_item3, quantity: 5) }

  let(:order2) { create(:order, :random) }
  let(:menu_item4) { create(:menu_item, :random) }
  let!(:order_line4) { create(:order_line, :random, order: order2, menu_item: menu_item4, quantity: 5) }

  it 'return true if all items are synced' do
    [order_line1, order_line2, order_line3].each do |order_line|
      create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line, synced: true)
    end
    order.reload

    is_synced = Woolworths::CheckOrderSync.new(order: order).call
    expect(is_synced).to be_truthy
  end

  it 'returns true even if synced order lines contains non-order order lines in addition to all the order\'s order lines' do
    [order_line1, order_line2, order_line3, order_line4].each do |order_line|
      create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line, synced: true)
    end
    order.reload

    is_synced = Woolworths::CheckOrderSync.new(order: order).call
    expect(is_synced).to be_truthy
  end

  context 'Order NOT synced' do

    it 'returns false if not all order lines are synced' do
      [order_line1, order_line2, order_line3].sample(2).each do |order_line|
        create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line, synced: true)
      end
      order.reload

      is_synced = Woolworths::CheckOrderSync.new(order: order).call
      expect(is_synced).to be_falsey
    end

    it 'returns false if the woolworths order contains a non-order orderline' do
      create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line4, synced: true)
      order.reload

      is_synced = Woolworths::CheckOrderSync.new(order: order).call
      expect(is_synced).to be_falsey
    end

    context 'resync stale order lines' do

      let!(:order_line4) { create(:order_line, :random, order: order, menu_item: menu_item3, quantity: 5) }

      before do
        sync_response = double(Woolworths::Order::SyncTrolleyProduct)
        allow(Woolworths::Order::SyncTrolleyProduct).to receive(:new).and_return(sync_response)
        allow(sync_response).to receive(:call).and_return(true)

        process_response, delayed_process_response = double(Woolworths::ProcessOrderLine)
        allow(Woolworths::ProcessOrderLine).to receive(:new).and_return(process_response)
        allow(process_response).to receive(:delay).and_return(delayed_process_response)
        allow(delayed_process_response).to receive(:call).and_return(true)
      end

      it 'resync order lines that have not been updated in the last 30 seconds' do
        order_line1.update_column(:updated_at, Time.now - 31.seconds)
        order_line2.update_column(:updated_at, Time.now - 3.seconds)
        create(:woolworths_trolley_product, woolworths_order: woolworths_order, order_line: order_line3, synced: true)
        order_line4.update_column(:updated_at, Time.now - 30.seconds)

        order.reload

        expect(order_line1.updated_at).to be <= (Time.now - 30.seconds)
        expect(order_line4.updated_at).to be <= (Time.now - 30.seconds)

        # resyncs stale order line - order line 1
        expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new).with(order: order, order_line: order_line1)
        expect(Woolworths::ProcessOrderLine).to receive(:new).with(order_line: order_line1)

        # resyncs stale order line order line 4
        expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new).with(order: order, order_line: order_line4)
        expect(Woolworths::ProcessOrderLine).to receive(:new).with(order_line: order_line4)

        # does not resync order line 2 and 3
        expect(Woolworths::Order::SyncTrolleyProduct).to_not receive(:new).with(order: order, order_line: order_line2)
        expect(Woolworths::ProcessOrderLine).to_not receive(:new).with(order_line: order_line2)
        expect(Woolworths::Order::SyncTrolleyProduct).to_not receive(:new).with(order: order, order_line: order_line3)
        expect(Woolworths::ProcessOrderLine).to_not receive(:new).with(order_line: order_line3)

        is_synced = Woolworths::CheckOrderSync.new(order: order).call
        expect(is_synced).to be_falsey

        # it also refreshes the updated_at of the stale order lines
        expect(order_line1.reload.updated_at).to be >= (Time.now - 30.seconds)
        expect(order_line4.reload.updated_at).to be >= (Time.now - 30.seconds)
      end

    end
  end
end
