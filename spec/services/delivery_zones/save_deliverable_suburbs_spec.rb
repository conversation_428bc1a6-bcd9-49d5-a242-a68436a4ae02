require 'rails_helper'

RSpec.describe DeliveryZones::SaveDeliverableSuburbs, type: :service, deliverable_suburbs: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:suburb) { create(:suburb, :random) }
  let!(:delivery_zone) { create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb) }

  it 'saves the delivery zone\'s suburb as a deliverable suburb with distance of 0' do
    deliverable_suburbs = DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone).call

    expect(deliverable_suburbs).to be_present
    expect(deliverable_suburbs.size).to eq(1)

    deliverable_suburb = deliverable_suburbs.first
    expect(deliverable_suburb).to be_a(DeliverableSuburb)
    expect(deliverable_suburb.suburb).to eq(suburb)
    expect(deliverable_suburb.delivery_zone).to eq(delivery_zone)
    expect(deliverable_suburb.supplier_profile).to eq(supplier)
    expect(deliverable_suburb.distance).to eq(0)

    expect(deliverable_suburbs).to eq(delivery_zone.deliverable_suburbs)
  end

  it 'creates an entry for a suburb with the same postcode as delivery zone suburb along with its distance' do
    suburb2 = create(:suburb, :random, postcode: suburb.postcode)

    deliverable_suburbs = DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone).call

    expect(deliverable_suburbs).to be_present
    expect(deliverable_suburbs.size).to eq(2)

    expect(deliverable_suburbs.map(&:suburb)).to include(suburb, suburb2)
    suburb2_distance = Haversine.distance(delivery_zone.suburb.latitude, delivery_zone.suburb.longitude, suburb2.latitude, suburb2.longitude).to_kilometers
    expect(deliverable_suburbs.map(&:distance).map{|d| d.round(2).to_s }).to include(*[0.0, suburb2_distance].map{|d| d.round(2).to_s })
  end

  context 'with suburbs in the same state' do
    let!(:delivery_suburb) { create(:suburb, :random, longitude: 151.2078, latitude: -33.9031) }
    let!(:delivery_zone) { create(:delivery_zone, :random, supplier_profile: supplier, suburb: delivery_suburb, radius: 10) }
    let!(:state_suburb1) { create(:suburb, :random, state: delivery_suburb.state, longitude: 151.2079, latitude: -33.9032) }
    let!(:state_suburb2) { create(:suburb, :random, state: delivery_suburb.state, longitude: 141.2078, latitude: -39.9031) }

    let!(:non_state_suburb1) { create(:suburb, :random, state: 'ANOTHER-STATE', longitude: 151.2078, latitude: -33.9031) }

    it 'only saves delivery suburb entries for suburbs within the range of the delivery suburb' do
      deliverable_suburbs = DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone).call

      expect(deliverable_suburbs).to be_present
      expect(deliverable_suburbs.size).to eq(2)

      expect(deliverable_suburbs.map(&:suburb)).to include(state_suburb1)
      expect(deliverable_suburbs.map(&:suburb)).to_not include(state_suburb2) # not near in term is lat-long
      expect(deliverable_suburbs.map(&:suburb)).to_not include(non_state_suburb1) # near in terms of lat-long, but not in same state

      state_suburb1_distance = Haversine.distance(delivery_zone.suburb.latitude, delivery_zone.suburb.longitude, state_suburb1.latitude, state_suburb1.longitude).to_kilometers
      expect(deliverable_suburbs.map(&:distance).map{|d| d.round(2) }).to include(0, state_suburb1_distance.round(2))
    end
  end

  context 'with existing deliverable suburb' do
    let!(:deliverable_suburb1) { create(:deliverable_suburb, :random, supplier_profile: supplier, delivery_zone: delivery_zone, suburb: suburb, distance: 200_000) }
    let!(:deliverable_suburb2) { create(:deliverable_suburb, :random, supplier_profile: supplier, delivery_zone: delivery_zone) }

    it 'updates the deliverable suburb entry' do
      deliverable_suburbs = DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone).call

      expect(deliverable_suburbs.map(&:id)).to include(deliverable_suburb1.id)

      updated_deliverable_suburb = deliverable_suburbs.detect{|x| x.id == deliverable_suburb1.id }
      expect(updated_deliverable_suburb.distance).to_not eq(200_000)
    end

    it 'keeps the old deliverable suburbs by default' do
      deliverable_suburbs = DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone).call

      expect(deliverable_suburbs.map(&:id)).to_not include(deliverable_suburb2.id)
      expect(deliverable_suburbs).to_not eq(delivery_zone.deliverable_suburbs)

      expect(delivery_zone.deliverable_suburbs).to include(deliverable_suburb2)
    end

    it 'purges the old deliverable suburbs if set to refresh' do
      deliverable_suburbs = DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone, refresh: true).call

      expect(deliverable_suburbs.map(&:id)).to_not include(deliverable_suburb2.id)
      expect(deliverable_suburbs).to eq(delivery_zone.deliverable_suburbs)

      expect(delivery_zone.deliverable_suburbs).to_not include(deliverable_suburb2)
    end
  end

end
