require 'rails_helper'

RSpec.describe DeliveryZones::Remove, type: :service, suppliers: true, delivery_zones: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:delivery_zone) { create(:delivery_zone, :random, supplier_profile: supplier) }

  before do
    # mock supplier delivery details saver
    details_saver = delayed_details_saver = double(Suppliers::Cache::DeliveryDetails)
    allow(Suppliers::Cache::DeliveryDetails).to receive(:new).and_return(details_saver)
    allow(details_saver).to receive(:delay).and_return(delayed_details_saver)
    allow(delayed_details_saver).to receive(:call).and_return(true)
  end

  it 'destroys the delivery zone' do
    zone_remover = DeliveryZones::Remove.new(supplier: supplier, delivery_zone: delivery_zone).call

    expect(zone_remover).to be_success
    expect{ delivery_zone.reload }.to raise_error(ActiveRecord::RecordNotFound)
  end

  it 'makes a request to update supplier delivery details' do
    expect(Suppliers::Cache::DeliveryDetails).to receive(:new).with(supplier: supplier)

    zone_remover = DeliveryZones::Remove.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(zone_remover).to be_success
  end

  context 'with errors' do
    it 'cannot save without a supplier' do
      zone_remover = DeliveryZones::Remove.new(supplier: nil, delivery_zone: delivery_zone).call

      expect(zone_remover).to_not be_success
      expect(zone_remover.errors).to include('Cannot remove a delivery zone without a supplier')
    end

    it 'cannot save without a delivery zone' do
      zone_remover = DeliveryZones::Remove.new(supplier: supplier, delivery_zone: nil).call

      expect(zone_remover).to_not be_success
      expect(zone_remover.errors).to include('Cannot remove a missing delivery zone')
    end

    it 'cannot save if delivery zone does not belong to the supplier' do
      supplier2 = create(:supplier_profile, :random)

      zone_remover = DeliveryZones::Remove.new(supplier: supplier2, delivery_zone: delivery_zone).call

      expect(zone_remover).to_not be_success
      expect(zone_remover.errors).to include('You do not have access to this delivery zone')
    end
  end

end
