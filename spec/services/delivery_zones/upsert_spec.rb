require 'rails_helper'

RSpec.describe DeliveryZones::Upsert, type: :service, suppliers: true, delivery_zones: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_user) }

  let!(:suburb) { create(:suburb, :random) }
  let!(:delivery_zone_params) do
    {
      suburb_id: suburb.id,
      radius: '1',
      delivery_fee: '0.0',
      operating_hours_start: '11:00',
      operating_hours_end: '17:00',
      operating_wdays: '0111110'
    }
  end

  before do
    # mock supplier delivery details saver
    details_saver = delayed_details_saver = double(Suppliers::Cache::DeliveryDetails)
    allow(Suppliers::Cache::DeliveryDetails).to receive(:new).and_return(details_saver)
    allow(details_saver).to receive(:delay).and_return(delayed_details_saver)
    allow(delayed_details_saver).to receive(:call).and_return(true)

    # mock deliverable suburbs saver
    deliverable_suburb_saver = delayed_deliverable_suburb_saver = double(DeliveryZones::SaveDeliverableSuburbs)
    allow(DeliveryZones::SaveDeliverableSuburbs).to receive(:new).and_return(deliverable_suburb_saver)
    allow(deliverable_suburb_saver).to receive(:delay).and_return(delayed_deliverable_suburb_saver)
    allow(delayed_deliverable_suburb_saver).to receive(:call)
  end

  it 'creates a new delivery zone for the supplier' do
    zone_creator = DeliveryZones::Upsert.new(supplier: supplier, delivery_zone_params: delivery_zone_params).call

    expect(zone_creator).to be_success
    created_zone = zone_creator.delivery_zone

    expect(created_zone).to be_present
    expect(created_zone.supplier_profile).to eq(supplier)
  end

  it 'saves the passed in params' do
    zone_creator = DeliveryZones::Upsert.new(supplier: supplier, delivery_zone_params: delivery_zone_params).call

    expect(zone_creator).to be_success
    created_zone = zone_creator.delivery_zone
    expect(created_zone.suburb).to eq(suburb)
    expect(created_zone.radius).to eq(1)
    expect(created_zone.delivery_fee).to eq(0.0)
  end

  it 'saves the calculated operting hours (start/end)' do
    zone_creator = DeliveryZones::Upsert.new(supplier: supplier, delivery_zone_params: delivery_zone_params).call

    expect(zone_creator).to be_success
    created_zone = zone_creator.delivery_zone
    expect(created_zone.operating_hours_start).to eq(Time.zone.parse(delivery_zone_params[:operating_hours_start]).seconds_since_midnight)
    expect(created_zone.operating_hours_end).to eq(Time.zone.parse(delivery_zone_params[:operating_hours_end]).seconds_since_midnight)
  end

  context 'operating days' do
    it 'saves the operating wdays as passed in params' do
      zone_creator = DeliveryZones::Upsert.new(supplier: supplier, delivery_zone_params: delivery_zone_params).call

      expect(zone_creator).to be_success
      created_zone = zone_creator.delivery_zone
      expect(created_zone.operating_wdays).to eq(delivery_zone_params[:operating_wdays])
    end

    it 'saves the operating weekdays based on passed in day params' do
      delivery_zone_params_with_days = delivery_zone_params.merge(
      {
        mon: '0',
        tue: '0',
        wed: '1',
        thu: '1',
        fri: '1',
        sat: '0',
        sun: '0'
      })
      zone_creator = DeliveryZones::Upsert.new(supplier: supplier, delivery_zone_params: delivery_zone_params_with_days).call

      expect(zone_creator).to be_success
      created_zone = zone_creator.delivery_zone
      expect(created_zone.operating_wdays).to eq('0001110')
    end
  end

  it 'calls the supplier delivery details to be updated' do
    expect(Suppliers::Cache::DeliveryDetails).to receive(:new).with(supplier: supplier)

    zone_creator = DeliveryZones::Upsert.new(supplier: supplier, delivery_zone_params: delivery_zone_params).call
    expect(zone_creator).to be_success
  end

  it 'calls the delivery zones deliverable suburbs to be updated', deliverable_suburbs: true do
    expect(DeliveryZones::SaveDeliverableSuburbs).to receive(:new).with(delivery_zone: anything, refresh: true) # passes creaed delivery zone

    zone_creator = DeliveryZones::Upsert.new(supplier: supplier, delivery_zone_params: delivery_zone_params).call
    expect(zone_creator).to be_success
  end

  context 'with existing delivery zone' do
    let!(:suburb2) { create(:suburb, :random) }
    let!(:delivery_zone) { create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb2) }

    it 'updates the delivery zone with the passed in params' do
      zone_updator = DeliveryZones::Upsert.new(supplier: supplier, delivery_zone: delivery_zone, delivery_zone_params: delivery_zone_params).call

      expect(zone_updator).to be_success
      updated_zone = zone_updator.delivery_zone
      expect(updated_zone.id).to eq(delivery_zone.id) # updates the same delivery zone
      expect(updated_zone.suburb).to eq(suburb)
      expect(updated_zone.radius).to eq(1)
      expect(updated_zone.delivery_fee).to eq(0.0)
      expect(updated_zone.operating_hours_start).to eq(Time.zone.parse(delivery_zone_params[:operating_hours_start]).seconds_since_midnight)
      expect(updated_zone.operating_hours_end).to eq(Time.zone.parse(delivery_zone_params[:operating_hours_end]).seconds_since_midnight)
      expect(updated_zone.operating_wdays).to eq('0111110')
    end
  end

  context 'with errors' do
    it 'cannot save without a supplier' do
      zone_creator = DeliveryZones::Upsert.new(supplier: nil, delivery_zone_params: delivery_zone_params).call

      expect(zone_creator).to_not be_success
      expect(zone_creator.errors).to include('Cannot create a delivery zone without a supplier')
    end

    it 'cannot save if the passed in supplier is not actually a supplier' do
      customer = create(:customer_profile, :random, :with_user)

      zone_creator = DeliveryZones::Upsert.new(supplier: customer, delivery_zone_params: delivery_zone_params).call

      expect(zone_creator).to_not be_success
      expect(zone_creator.errors).to include('Cannot create a delivery zone without a supplier')
    end

    it 'does not call the delivery details to be saved for the supplier' do
      expect(Suppliers::Cache::DeliveryDetails).to_not receive(:new)

      zone_creator = DeliveryZones::Upsert.new(supplier: nil, delivery_zone_params: delivery_zone_params).call
      expect(zone_creator).to_not be_success
    end
  end

end
