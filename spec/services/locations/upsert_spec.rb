require 'rails_helper'

RSpec.describe Locations::Upsert, type: :servivce, locations: true, orders: true do

  let(:order) { create(:order, :draft) }

  it 'creates a new location for an order' do
    location_creator = Locations::Upsert.new(order: order).call

    expect(location_creator).to be_success
    created_location = location_creator.location

    expect(created_location).to be_persisted
    expect(created_location.order).to eq(order)
    expect(created_location.details).to eq('Your Office')
  end

  it 'creates a location with the passed in details' do
    location_params = { details: 'My Location' }
    location_creator = Locations::Upsert.new(order: order, location_params: location_params).call

    expect(location_creator).to be_success

    created_location = location_creator.location
    expect(created_location.details).to eq(location_params[:details])
  end

  it 'errors out if order is absent' do
    location_creator = Locations::Upsert.new(order: nil).call

    expect(location_creator).to_not be_success
  end

  context 'with existing location' do

    let!(:location) { create(:location, :random, order: order) }

    it 'updates the location details if passed in' do
      location_updater = Locations::Upsert.new(order: order, location: location, location_params: { details: 'New Name' }).call

      expect(location_updater).to be_success
      updated_location = location_updater.location

      expect(updated_location.id).to eq(location.id)
      expect(updated_location.details).to eq('New Name')
    end

    it 'errors out if location does not belong to order' do
      location2 = create(:location, :random)
      location_creator = Locations::Upsert.new(order: order, location: location2).call

      expect(location_creator).to_not be_success
    end
  end
end
