require 'rails_helper'

RSpec.describe Locations::Remove, type: :service, locations: true, orders: true do

  let!(:supplier) { create(:supplier_profile, :random) }

  let(:order) { create(:order, :draft) }
  let!(:location) { create(:location, :random, order: order) }

  it 'removes a location for an order' do
    location_remover = Locations::Remove.new(order: order, location: location).call

    expect(location_remover).to be_success
    expect{ location.reload }.to raise_error(ActiveRecord::RecordNotFound)
  end

  it 'errors out if location does not belong to order' do
    location2 = create(:location, :random)
    location_remover = Locations::Remove.new(order: order, location: location2).call

    expect(location_remover).to_not be_success
  end

  context 'with order lines' do

    let!(:order_line1) { create(:order_line, :random, order: order, location: location, supplier_profile: supplier) }
    let!(:order_line2) { create(:order_line, :random, order: order, location: location, supplier_profile: supplier) }

    let(:location2) { create(:location, :random) }
    let!(:order_line3) { create(:order_line, :random, order: order, location: location2, supplier_profile: supplier) }

    it 'removes the order lines for the location' do
      location_remover = Locations::Remove.new(order: order, location: location.reload).call

      expect(location_remover).to be_success
      expect{ order_line1.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ order_line2.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'does not remove order lines from a different location' do
      location_remover = Locations::Remove.new(order: order, location: location.reload).call

      expect(location_remover).to be_success
      expect{ order_line3.reload }.to_not raise_error(ActiveRecord::RecordNotFound)
    end

  end

end
