require 'rails_helper'

RSpec.describe Locations::Fetch, type: :service do

  let(:order) { create(:order, :random, status: 'confirmed') }

  it 'creates a new location' do
    fetched_location = Locations::Fetch.new(order: order).call

    expect(fetched_location).to be_a(Location)
    expect(fetched_location).to be_valid
    expect(fetched_location).to be_persisted
    expect(fetched_location.order).to eq(order)
  end

  it 'returns nil if an order is not passed' do
    location_id = [nil, 'random_location_id'].sample
    fetched_location = Locations::Fetch.new(order: nil, location_params: { id: location_id }).call

    expect(fetched_location).to_not be_present
  end

  context 'with existing location' do
    let!(:existing_location1) { create(:location, :random, order: order) }

    let(:order2) { create(:order, :random, status: 'confirmed') }
    let!(:existing_location2) { create(:location, :random, order: order2) }

    it 'returns an existing location from same order' do
      fetched_location = Locations::Fetch.new(order: order, location_params: { id: existing_location1.id }).call

      expect(fetched_location).to eq(existing_location1)
    end

    it 'returns an existing location even if order is not passed' do
      fetched_location = Locations::Fetch.new(order: nil, location_params: { id: existing_location1.id }).call

      expect(fetched_location).to eq(existing_location1)
    end
  end
end
