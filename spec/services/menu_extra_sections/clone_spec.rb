require 'rails_helper'

RSpec.describe MenuExtraSections::Clone, type: :service, menu_extra_sections: true, menu_items: true, cloning: true do

  let(:menu_item) { create(:menu_item, :random) }
  let!(:menu_extra_section) { create(:menu_extra_section, :random, menu_item: menu_item) }

  it 'clones the menu extra section with the same data' do
    extra_section_cloner = MenuExtraSections::Clone.new(menu_extra_section: menu_extra_section).call

    expect(extra_section_cloner).to be_success
    cloned_menu_extra_section = extra_section_cloner.cloned_menu_extra_section

    expect(cloned_menu_extra_section).to be_present
    expect(cloned_menu_extra_section.id).to_not eq(menu_extra_section.id) # new menu extra section

    # different data

    expect(cloned_menu_extra_section.weight).to_not eq(menu_extra_section.weight)
    expect(cloned_menu_extra_section.name).to eq("#{menu_extra_section.name} - CLONED")

    # cloned data
    expect(cloned_menu_extra_section.max_limit).to eq(menu_extra_section.max_limit)
    expect(cloned_menu_extra_section.menu_item).to eq(menu_extra_section.menu_item)
  end

  it 'clones the menu extra section with the same data in a different menu item' do
    other_menu_item = create(:menu_item, :random)
    extra_section_cloner = MenuExtraSections::Clone.new(menu_extra_section: menu_extra_section, menu_item: other_menu_item).call

    expect(extra_section_cloner).to be_success
    cloned_menu_extra_section = extra_section_cloner.cloned_menu_extra_section

    expect(cloned_menu_extra_section).to be_present
    expect(cloned_menu_extra_section.id).to_not eq(menu_extra_section.id)

    # different data
    expect(cloned_menu_extra_section.weight).to_not eq(menu_extra_section.weight)
    expect(cloned_menu_extra_section.menu_item).to_not eq(menu_extra_section.menu_item)
    expect(cloned_menu_extra_section.menu_item).to eq(other_menu_item)

    # cloned data
    expect(cloned_menu_extra_section.name).to eq(menu_extra_section.name)
    expect(cloned_menu_extra_section.max_limit).to eq(menu_extra_section.max_limit)
  end

  it 'does not clone an archvied menu extra section' do
    menu_extra_section.update_column(:archived_at, Time.zone.now)
    extra_section_cloner = MenuExtraSections::Clone.new(menu_extra_section: menu_extra_section).call

    expect(extra_section_cloner).to_not be_success
    expect(extra_section_cloner.errors).to include('Cannot clone an archived menu extra section')
  end

  context 'with menu extras' do
    let!(:menu_extra1) { create(:menu_extra, :random, menu_extra_section: menu_extra_section, menu_item: menu_item) }
    let!(:menu_extra2) { create(:menu_extra, :random, menu_extra_section: menu_extra_section, menu_item: menu_item) }
    let!(:archived_menu_extra) { create(:menu_extra, :random, menu_extra_section: menu_extra_section, menu_item: menu_item, archived_at: Time.zone.now) }

    it 'clones any non-archived menu extras' do
      extra_section_cloner = MenuExtraSections::Clone.new(menu_extra_section: menu_extra_section).call

      expect(extra_section_cloner).to be_success
      cloned_menu_extra_section = extra_section_cloner.cloned_menu_extra_section

      cloned_menu_extras = cloned_menu_extra_section.menu_extras
      expect(cloned_menu_extras).to be_present

      expect(cloned_menu_extras.map(&:id)).to_not include(menu_extra1.id, menu_extra2.id) # new menu extras
      expect(cloned_menu_extras.map(&:weight)).to_not include(menu_extra1.weight, menu_extra2.weight) # new menu extra weights
      expect(cloned_menu_extras.map(&:name)).to include(menu_extra1.name, menu_extra2.name)
      expect(cloned_menu_extras.map(&:price)).to include(menu_extra1.price, menu_extra2.price)
      expect(cloned_menu_extras.map(&:archived_at)).to match_array([nil, nil])

      expect(cloned_menu_extras.map(&:menu_extra_section)).to include(cloned_menu_extra_section)
      expect(cloned_menu_extras.map(&:menu_item)).to include(cloned_menu_extra_section.menu_item)
    end

    it 'does not clone archived menu extras' do
      extra_section_cloner = MenuExtraSections::Clone.new(menu_extra_section: menu_extra_section).call

      expect(extra_section_cloner).to be_success
      cloned_menu_extra_section = extra_section_cloner.cloned_menu_extra_section

      cloned_menu_extras = cloned_menu_extra_section.menu_extras
      expect(cloned_menu_extras).to be_present # clones non-archived menu extras
      expect(cloned_menu_extras.map(&:id)).to_not include(archived_menu_extra.id)
      expect(cloned_menu_extras.map(&:weight)).to_not include(archived_menu_extra.weight)
      expect(cloned_menu_extras.map(&:name)).to_not include(archived_menu_extra.name)
      expect(cloned_menu_extras.map(&:price)).to_not include(archived_menu_extra.price)
    end
  end

end
