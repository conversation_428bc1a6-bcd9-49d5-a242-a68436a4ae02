require 'rails_helper'

RSpec.describe MenuExtraSections::Archive, type: :service, menu_extra_sections: true do

  let!(:menu_extra_section) { create(:menu_extra_section, :random) }

  it 'archives the menu extra section' do
    archiver = MenuExtraSections::Archive.new(menu_extra_section: menu_extra_section).call

    expect(archiver).to be_success
    expect(menu_extra_section.reload.archived_at).to be_present
  end

  context 'with menu extras' do
    let!(:menu_extra1) { create(:menu_extra, :random, menu_item: menu_extra_section.menu_item, menu_extra_section: menu_extra_section) }
    let!(:menu_extra2) { create(:menu_extra, :random, menu_item: menu_extra_section.menu_item, menu_extra_section: menu_extra_section) }

    it 'archives menu item even with existing non-archived menu extra sections' do
      archiver = MenuExtraSections::Archive.new(menu_extra_section: menu_extra_section).call
      expect(archiver).to be_success
      expect(menu_extra_section.reload.archived_at).to be_present
    end

    it 'archives associated menu extras when menu item is archived' do
      archiver = MenuExtraSections::Archive.new(menu_extra_section: menu_extra_section).call

      expect(archiver).to be_success
      expect(menu_extra1.reload.archived_at).to be_present
      expect(menu_extra2.reload.archived_at).to be_present
    end
  end

  describe 'errors' do
    it 'cannot archive a missing menu extra section' do
      archiver = MenuExtraSections::Archive.new(menu_extra_section: nil).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archive a missing menu extra section')
    end

    it 'cannot archive already archived menu extra section' do
      menu_extra_section.update_column(:archived_at, Time.zone.now)

      archiver = MenuExtraSections::Archive.new(menu_extra_section: menu_extra_section).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archvie an already archived menu extra section')
    end
  end
end
