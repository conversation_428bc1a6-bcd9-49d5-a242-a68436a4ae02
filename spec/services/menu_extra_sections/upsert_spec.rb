require 'rails_helper'

RSpec.describe MenuExtraSections::Upsert, type: :service, menu_extra_sections: true do

  let(:menu_item) { create(:menu_item, :random) }

  it 'lets you create a new menu extra section' do
    menu_extra_section_params = { name: Faker::Name.name, max_limit: 10, menu_item: menu_item }
    extras_section_upserter = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params).call

    expect(extras_section_upserter).to be_success
    created_menu_extra_section = extras_section_upserter.menu_extra_section
    expect(created_menu_extra_section).to be_present
    expect(created_menu_extra_section).to be_persisted
  end

  it 'creates a new menu extra section within the passed menu item' do
    menu_extra_section_params = { name: Faker::Name.name, max_limit: 10, menu_item: menu_item }
    extras_section_upserter = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params).call

    expect(extras_section_upserter).to be_success
    created_menu_extra_section = extras_section_upserter.menu_extra_section
    expect(created_menu_extra_section.menu_item).to eq(menu_item)
  end

  it 'create a new menu extra section with default params' do
    menu_extra_section_params = { name: Faker::Name.name, max_limit: 10, menu_item: menu_item }
    extras_section_upserter = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params).call

    expect(extras_section_upserter).to be_success
    created_menu_extra_section = extras_section_upserter.menu_extra_section
    expect(created_menu_extra_section.weight).to be_present
    expect(created_menu_extra_section.weight).to eq(MenuExtraSection.pluck(:weight).compact.max)
  end

  it 'create a new menu extra section with passed in params' do
    menu_extra_section_params = { name: Faker::Name.name, max_limit: 10, menu_item: menu_item, weight: 10 }
    extras_section_upserter = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params).call

    expect(extras_section_upserter).to be_success
    created_menu_extra_section = extras_section_upserter.menu_extra_section
    expect(created_menu_extra_section.name).to eq(menu_extra_section_params[:name])
    expect(created_menu_extra_section.max_limit).to eq(menu_extra_section_params[:max_limit])
    expect(created_menu_extra_section.weight).to eq(menu_extra_section_params[:weight])
  end

  it 'sanitizes name (with spaces) when saving' do
    name_with_space = [" #{Faker::Name.name}", "#{Faker::Name.name} "].sample
    menu_extra_section_params = { name: name_with_space, max_limit: 10, menu_item: menu_item, weight: 10 }
    extras_section_upserter = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params).call

    expect(extras_section_upserter).to be_success
    created_menu_extra_section = extras_section_upserter.menu_extra_section
    expect(created_menu_extra_section.name).to eq(name_with_space.strip)
  end

  context 'with existing menu extra section' do

    let!(:menu_extra_section) { create(:menu_extra_section, :random, menu_item: menu_item) }

    it 'does not creates a new menu extra section with the name of an existing menu extra section (instead it updates) ' do
      section_name = [menu_extra_section.name, " #{menu_extra_section.name}", "#{menu_extra_section.name} "].sample
      menu_extra_section_params = { name: section_name, max_limit: 10, menu_item: menu_item }
      extras_section_upserter = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params).call

      expect(extras_section_upserter).to be_success
      created_menu_extra_section = extras_section_upserter.menu_extra_section
      expect(created_menu_extra_section.name).to eq(menu_extra_section.name)
      expect(created_menu_extra_section.id).to eq(menu_extra_section.id) # same menu extra section
      expect(created_menu_extra_section.max_limit).to eq(menu_extra_section_params[:max_limit])
    end

    it 'creates a new menu extra section with the name of an existing menu extra section if it does not belong to the menu item' do
      menu_item2 = create(:menu_item, :random)
      menu_extra_section_params = { name: menu_extra_section.name, max_limit: 10, menu_item: menu_item2 }
      extras_section_upserter = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params).call

      expect(extras_section_upserter).to be_success
      created_menu_extra_section = extras_section_upserter.menu_extra_section
      expect(created_menu_extra_section.id).to_not eq(menu_extra_section.id) # not the same menu extra section
      expect(created_menu_extra_section.name).to eq(menu_extra_section.name)
      expect(created_menu_extra_section.menu_item).to eq(menu_item2)
    end

    it 'creates a new menu extra section with the name of an existing menu extra section if forced' do
      menu_extra_section_params = { name: menu_extra_section.name, max_limit: 10, menu_item: menu_item }
      extras_section_upserter = MenuExtraSections::Upsert.new(menu_extra_section_params: menu_extra_section_params, forced: true).call

      expect(extras_section_upserter).to be_success
      created_menu_extra_section = extras_section_upserter.menu_extra_section
      expect(created_menu_extra_section.name).to eq(menu_extra_section.name)
      expect(created_menu_extra_section.id).to_not eq(menu_extra_section.id) # not the same menu extra section
    end

    it 'updates the menu extra section with passed in params' do
      menu_extra_section_params = { name: Faker::Name.name, menu_item: menu_item, weight: 10 }
      serving_updator = MenuExtraSections::Upsert.new(menu_extra_section: menu_extra_section, menu_extra_section_params: menu_extra_section_params).call

      expect(serving_updator).to be_success
      updated_menu_extra_section = serving_updator.menu_extra_section
      expect(updated_menu_extra_section.id).to eq(menu_extra_section.id)
      expect(updated_menu_extra_section.name).to eq(menu_extra_section_params[:name])
      expect(updated_menu_extra_section.menu_item).to eq(menu_item)
      expect(updated_menu_extra_section.weight).to eq(menu_extra_section_params[:weight])
    end

  end
end
