require 'rails_helper'

RSpec.describe Holidays::LogCalendarEvents, type: :service, holidays: true, event_logs: true do
  
  let!(:calendar_month) { Date.today.beginning_of_month }

  let!(:holiday1) { create(:holiday, :random, on_date: calendar_month + 5.days) }
  let!(:holiday2) { create(:holiday, :random, on_date: calendar_month + 10.days) }
  let!(:holiday3) { create(:holiday, :random, on_date: calendar_month + 15.days) }

  let!(:event_logger) { double(EventLogs::Create) }

  before do
    # mock event logger
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    logger_successful_response = OpenStruct.new(success?: true, event_log: 'event-log')
    allow(event_logger).to receive(:call).and_return(logger_successful_response)
  end

  it 'logs a `Monthly Calendar Event` event for each holiday in the calendar month' do
    expect(EventLogs::Create).to receive(:new).with(event_object: holiday1, event: 'monthly-calendar-event')
    expect(EventLogs::Create).to receive(:new).with(event_object: holiday2, event: 'monthly-calendar-event')
    expect(EventLogs::Create).to receive(:new).with(event_object: holiday3, event: 'monthly-calendar-event')

    holiday_events_logger = Holidays::LogCalendarEvents.new(month: calendar_month).call
    expect(holiday_events_logger).to be_success
    expect(holiday_events_logger.holidays).to include(holiday1, holiday2, holiday3)
    expect(holiday_events_logger.events.size).to eq(3)
    expect(holiday_events_logger.events).to include('event-log')
  end

  it 'does not log an event for holidays not in the calendar month' do
    holiday2.update_column(:on_date, [(calendar_month + 1.month + 5.days), (calendar_month - 1.month)].sample)
    expect(EventLogs::Create).to_not receive(:new).with(event_object: holiday2, event: 'monthly-calendar-event')

    holiday_events_logger = Holidays::LogCalendarEvents.new(month: calendar_month).call
    expect(holiday_events_logger).to be_success
    expect(holiday_events_logger.holidays).to_not include(holiday2)
  end

  context 'with multiple holidays on the same day with same name' do
    let!(:holiday_states) { %w[NSW VIC QLD] }

    before do
      [holiday1, holiday2, holiday3].each_with_index do |holiday, idx|
        holiday.update_columns(name: holiday1.name, on_date: holiday1.on_date, state: holiday_states[idx])
      end
    end

    it 'logs a single `Monthly Calendar Event` event passed in map of states' do
      expect(EventLogs::Create).to receive(:new).with(event_object: holiday1, event: 'monthly-calendar-event', states: holiday_states.sort)
      expect(EventLogs::Create).to_not receive(:new).with(event_object: holiday2, event: 'monthly-calendar-event')
      expect(EventLogs::Create).to_not receive(:new).with(event_object: holiday3, event: 'monthly-calendar-event')

      holiday_events_logger = Holidays::LogCalendarEvents.new(month: calendar_month).call
      expect(holiday_events_logger).to be_success
      expect(holiday_events_logger.holidays).to include(holiday1)
      expect(holiday_events_logger.holidays).to_not include(holiday2, holiday3)
      expect(holiday_events_logger.events.size).to eq(1)
    end
  end

  context 'with event logger errors' do
    before do
      logger_failed_response = OpenStruct.new(success?: false, errors: ['event-logger-error'])
      allow(event_logger).to receive(:call).and_return(logger_failed_response)
    end

    it 'returns with errors' do
      holiday_events_logger = Holidays::LogCalendarEvents.new(month: calendar_month).call

      expect(holiday_events_logger).to_not be_success
      expect(holiday_events_logger.errors).to include('event-logger-error')
      expect(holiday_events_logger.errors.size).to eq(3) # for each holiday
    end
  end

end