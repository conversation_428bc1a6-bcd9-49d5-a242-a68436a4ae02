require 'rails_helper'

RSpec.describe Holidays::SyncWithAPI, type: :service, holidays: true do
  subject { Holidays::SyncWithAPI.new.call }
  let!(:api_holiday) { Holidays::MapFromAPI::ApiHoliday }
  let(:mapped_holidays) do
    [
      api_holiday.new('Before Normal Holiday', Time.zone.parse('2020-05-12 00:00:00'), 'NSW'),
      api_holiday.new('Normal Holiday', Time.zone.parse('2020-05-13 00:00:00'), 'NSW'),

      # weekend holiday
      api_holiday.new('Saturday Holiday', Time.zone.parse('2020-07-18 00:00:00'), 'NSW'),
      api_holiday.new('Sunday Holiday', Time.zone.parse('2020-11-29 00:00:00'), 'NSW'),

      # irrelevant holiday
      api_holiday.new('New South Wales Bank Holiday', Time.zone.parse('2020-03-19 00:00:00'), 'NSW'),

      # non-valid state holidays
      api_holiday.new('Country-Wide Holiday', Time.zone.parse('2020-01-27 00:00:00'), nil),
      api_holiday.new('NT Holiday', Time.zone.parse('2020-03-19 00:00:00'), 'NT'),
      api_holiday.new('TAS Holiday', Time.zone.parse('2020-03-19 00:00:00'), 'TAS'),

      # mis-named holidays
      api_holiday.new('May Day', Time.zone.parse('2020-10-05 00:00:00'), 'NSW'),
      api_holiday.new('Proclamation Day', Time.zone.parse('2020-12-28 00:00:00'), 'SA'),
      api_holiday.new('Holiday 1 Observed', Time.zone.parse('2020-01-27 00:00:00'), 'NSW'),
      api_holiday.new('Holiday 2 Additional Public Holiday', Time.zone.parse('2020-01-27 00:00:00'), 'NSW'),
      api_holiday.new('Friday before the Holiday 3', Time.zone.parse('2020-01-27 00:00:00'), 'NSW'),

      api_holiday.new('Good Friday', Time.zone.parse('2020-04-10 00:00:00'), 'NSW'),
      api_holiday.new('Friday Holiday', Time.zone.parse('2020-06-19 00:00:00'), 'NSW'),
      api_holiday.new('End of Year Holiday', Time.zone.parse('2020-12-31 00:00:00'), 'QLD')
    ]
  end
  before do
    map_from_api = double(Holidays::MapFromAPI)
    allow(Holidays::MapFromAPI).to receive(:new).and_return(map_from_api)
    allow(map_from_api).to receive(:call).and_return(mapped_holidays)
  end

  it 'creates a holiday record for a normal holiday' do
    subject

    normal_holiday = Holiday.where(name: 'Normal Holiday').first
    expect(normal_holiday).to be_present
    expect(normal_holiday.on_date).to eq(Time.zone.parse('2020-05-13 00:00:00').beginning_of_day)
    expect(normal_holiday.state).to eq('NSW')
  end

  it 'creates a country wide holiday' do
    subject

    normal_holiday = Holiday.where(name: 'Country-Wide Holiday').first
    expect(normal_holiday).to be_present
    expect(normal_holiday.on_date).to eq(Time.zone.parse('2020-01-27 00:00:00').beginning_of_day)
    expect(normal_holiday.state).to be_blank
  end

  context 'with filtered holidays' do
    it 'filters out holidays from non-valid states (NT, TAS)' do
      imported_holidays = subject
      expect(imported_holidays.map(&:name)).to_not include('NT Holiday', 'TAS Holiday')
    end

    it 'filters out irrelevant holidays' do
      imported_holidays = subject
      expect(imported_holidays.map(&:name)).to_not include('New South Wales Bank Holiday')
    end

    it 'filters out weekend holidays' do
      imported_holidays = subject
      expect(imported_holidays.map(&:name)).to_not include('Saturday Holiday', 'Sunday Holiday')
    end
  end

  context 'with sanitized names' do
    it 'stores the Holiday with name "May Day" as "Labour Day"' do
      imported_holidays = subject
      expect(imported_holidays.map(&:name)).to_not include('May Day')
      expect(imported_holidays.map(&:name)).to include('Labour Day')
    end

    it 'stores the Holiday with name "Proclamation Day" as "Boxing day"' do
      imported_holidays = subject
      expect(imported_holidays.map(&:name)).to_not include('Proclamation Day')
      expect(imported_holidays.map(&:name)).to include('Boxing Day')
    end

    it 'removes "Observed" from the Holiday name' do
      imported_holidays = subject
      expect(imported_holidays.map(&:name)).to_not include('Holiday 1 Observed')
      expect(imported_holidays.map(&:name)).to include('Holiday 1')
    end

    it 'removes "Additional Public Holiday" from the Holiday name' do
      imported_holidays = subject
      expect(imported_holidays.map(&:name)).to_not include('Holiday 2 Additional Public Holiday')
      expect(imported_holidays.map(&:name)).to include('Holiday 2')
    end

    it 'removes "Friday before the" from the Holiday name' do
      imported_holidays = subject
      expect(imported_holidays.map(&:name)).to_not include('Friday before the Holiday 3')
      expect(imported_holidays.map(&:name)).to include('Holiday 3')
    end
  end

  describe 'push to dates' do
    it 'pushes the normal weekday holiday to the next weekday' do
      subject

      normal_holiday = Holiday.where(name: 'Normal Holiday').first
      expect(normal_holiday.push_to).to eq(Time.zone.parse('2020-05-14 00:00:00'))
    end

    it 'pushes a friday holiday to Thursday' do
      subject
      friday_holiday = Holiday.where(name: 'Friday Holiday').first
      expect(friday_holiday.push_to).to eq(Time.zone.parse('2020-06-18 00:00:00'))
    end

    it 'prepones the Good Friday holiday to the the Thursday before' do
      subject
      friday_holiday = Holiday.where(name: 'Good Friday').first
      expect(friday_holiday.push_to).to eq(Time.zone.parse('2020-04-09 00:00:00'))
    end

    it 'pushes past a listed public holiday from the same state' do
      subject
      before_normal_holiday = Holiday.where(name: 'Before Normal Holiday').first
      expect(before_normal_holiday.push_to).to_not eql(Time.zone.parse('2020-05-13 00:00:00'))
      expect(before_normal_holiday.push_to).to eql(Time.zone.parse('2020-05-14 00:00:00'))
    end

    it 'pushes past the new years day' do
      subject
      end_of_year_holiday = Holiday.where(name: 'End of Year Holiday').first
      expect(end_of_year_holiday.push_to).to_not eql(Time.zone.parse('2021-01-01 00:00:00'))
      expect(end_of_year_holiday.push_to).to eql(Time.zone.parse('2021-01-04 00:00:00')) # past new year and weekends
    end
  end

  context 'dry run' do
    it 'returns a holiday object without actually storing it in database' do
      imported_holidays = Holidays::SyncWithAPI.new(dry_run: true).call

      imported_normal_holiday = imported_holidays.detect{|holiday| holiday.name == 'Normal Holiday' }
      expect(imported_normal_holiday).to be_present
      expect(imported_normal_holiday.on_date).to eq(Time.zone.parse('2020-05-13 00:00:00').beginning_of_day)
      expect(imported_normal_holiday.state).to eq('NSW')
      expect(imported_normal_holiday).to_not be_persisted

      stored_normal_holiday = Holiday.where(name: 'Normal Holiday').first
      expect(stored_normal_holiday).to be_blank
    end
  end

end
