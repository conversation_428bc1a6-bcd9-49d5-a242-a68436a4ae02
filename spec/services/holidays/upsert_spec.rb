require 'rails_helper'

RSpec.describe Holidays::Upsert, type: :service, holidays: true do

  let!(:holiday_date) { (Time.zone.now.beginning_of_week + 1.day).to_date }

  let!(:holiday_params) do
    {
      name: Faker::Name.name,
      description: Faker::Lorem.sentence,
      image: Faker::Internet.url,
      state: Faker::Name.initials,
      on_date: holiday_date,
      push_to: holiday_date + 1.day,
      effective_from:  holiday_date.beginning_of_day,
      effective_to:  holiday_date.end_of_day,
    }
  end

  it 'creates a new holiday with the passed in params' do
    holiday_creator = Holidays::Upsert.new(holiday_params: holiday_params).call

    expect(holiday_creator).to be_success

    created_holiday = holiday_creator.holiday
    expect(created_holiday).to be_present
    expect(created_holiday).to be_persisted
    expect(created_holiday.name).to eq(holiday_params[:name])
    expect(created_holiday.description).to eq(holiday_params[:description])
    expect(created_holiday.image).to eq(holiday_params[:image])
    expect(created_holiday.state).to eq(holiday_params[:state])
    expect(created_holiday.on_date.to_s(:date_verbose)).to eq(holiday_params[:on_date].to_s(:date_verbose))
    expect(created_holiday.effective_from.to_s(:datetime)).to eq(holiday_params[:effective_from].to_s(:datetime))
    expect(created_holiday.effective_to.to_s(:datetime)).to eq(holiday_params[:effective_to].to_s(:datetime))
  end

  context 'with an existing holiday' do
    let!(:holiday) { create(:holiday, :random) }

    it 'updates the passed in holiday with the passed in holiday params' do
      holiday_updator = Holidays::Upsert.new(holiday: holiday, holiday_params: holiday_params).call

      expect(holiday_updator).to be_success

      updated_holiday = holiday_updator.holiday
      expect(updated_holiday).to be_present
      expect(updated_holiday.id).to eq(holiday.id) # same holiday
      expect(updated_holiday.name).to eq(holiday_params[:name])
      expect(updated_holiday.description).to eq(holiday_params[:description])
      expect(updated_holiday.image).to eq(holiday_params[:image])
      expect(updated_holiday.state).to eq(holiday_params[:state])
      expect(updated_holiday.on_date.to_s(:date_verbose)).to eq(holiday_params[:on_date].to_s(:date_verbose))
      expect(updated_holiday.effective_from.to_s(:datetime)).to eq(holiday_params[:effective_from].to_s(:datetime))
      expect(updated_holiday.effective_to.to_s(:datetime)).to eq(holiday_params[:effective_to].to_s(:datetime))
    end
  end

  it 'returns with model errors if it fails' do
    invalid_holiday_params = holiday_params.merge({ name: nil, on_date: nil })
    holiday_upserter = Holidays::Upsert.new(holiday_params: invalid_holiday_params).call

    expect(holiday_upserter).to_not be_success
    expect(holiday_upserter.errors).to include('Name can\'t be blank', 'On date can\'t be blank')
  end

end