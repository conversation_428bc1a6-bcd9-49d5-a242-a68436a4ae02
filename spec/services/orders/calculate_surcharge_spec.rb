require 'rails_helper'

RSpec.describe Orders::CalculateSurcharge, type: :service, orders: true, credit_cards: true do

  let!(:on_account_card) { create(:credit_card, :on_account_card) }
  let(:order) { create(:order, :draft, customer_total: 1000) }

  it 'returns the surcharge as 0 if totals is 0' do
    calculated_surcharge = Orders::CalculateSurcharge.new(order: order, total: 0).call

    expect(calculated_surcharge).to eq(0)
  end

  it 'returns the surcharge as 0 if there are no cards associated with the order or if its a pay_on_account' do
    order.update_column(:credit_card_id, [on_account_card.id, nil].sample)
    calculated_surcharge = Orders::CalculateSurcharge.new(order: order, total: order.customer_total).call

    expect(calculated_surcharge).to eq(0)
  end

  context 'with an attached eway card', eway: true do
    let!(:eway_credit_card) { create(:credit_card, :valid_eway_payment) }

    before do
      order.update_column(:credit_card_id, eway_credit_card.id)
    end

    it 'returns surcharge for attached eway card as surcharge_percent(1.5) of amount + surcharge fee(25c)' do
      calculated_surcharge = Orders::CalculateSurcharge.new(order: order.reload, total: order.customer_total).call

      expected_surcharge = (order.customer_total * (CreditCards::FetchSurcharge::EWAY_PERCENT / 100)) + CreditCards::FetchSurcharge::EWAY_FEE
      expect(calculated_surcharge).to eq(expected_surcharge)
    end

    it 'returns surcharge for attached eway diners card as surcharge_percent(3.7) of amount + surcharge fee(25c)' do
      eway_credit_card.update_column(:brand, 'diners')
      calculated_surcharge = Orders::CalculateSurcharge.new(order: order.reload, total: order.customer_total).call

      expected_surcharge = (order.customer_total * (CreditCards::FetchSurcharge::EWAY_DINERS_PERCENT / 100)) + CreditCards::FetchSurcharge::EWAY_FEE
      expect(calculated_surcharge).to eq(expected_surcharge)
    end
  end

  context 'with an attached stripe card', stripe: true do
    let!(:stripe_credit_card) { create(:credit_card, :valid_stripe_payment) }

    before do
      order.update_column(:credit_card_id, stripe_credit_card.id)
    end

    it 'surcharge is (surcharge percent(1.75) of amount + surcharge fee(30c)) divided by 1 - surcharge percent(0.0175)'

    it 'returns surcharge for attached domestic stripe card with surcharge_percent = 1.75', stripe: true do
      calculated_surcharge = Orders::CalculateSurcharge.new(order: order.reload, total: order.customer_total).call

      expected_surcharge = (order.customer_total * (CreditCards::FetchSurcharge::STRIPE_DOMESTIC_PERCENT / 100)) + CreditCards::FetchSurcharge::STRIPE_FEE
      expected_surcharge /= (1 - (CreditCards::FetchSurcharge::STRIPE_DOMESTIC_PERCENT / 100))
      expect(calculated_surcharge).to eq(expected_surcharge)
    end

    it 'returns surcharge for attached international stripe card with surcharge percent = 2.9', stripe: true do
      stripe_credit_card.update_column(:country_code, %w[us nz].sample)
      calculated_surcharge = Orders::CalculateSurcharge.new(order: order.reload, total: order.customer_total).call

      expected_surcharge = (order.customer_total * (CreditCards::FetchSurcharge::STRIPE_INTERNATIONAL_PERCENT / 100)) + CreditCards::FetchSurcharge::STRIPE_FEE
      expected_surcharge /= (1 - (CreditCards::FetchSurcharge::STRIPE_INTERNATIONAL_PERCENT / 100))
      expect(calculated_surcharge).to eq(expected_surcharge)
    end
  end

  # attendee related specs coming later

end
