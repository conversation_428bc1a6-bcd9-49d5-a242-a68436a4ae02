require 'rails_helper'

RSpec.describe Orders::GetNextRecurringDeliveryAt, type: :servive, orders: true do

  let(:base_delivery_at) { Time.zone.now.beginning_of_week + 5.hours + 4.minutes }

  let!(:mon_order) { create(:order, :draft, delivery_at: nil, name: 'Mon') }
  let!(:wed_order) { create(:order, :draft, delivery_at: nil, name: 'Wed') }
  let!(:fri_order) { create(:order, :draft, delivery_at: nil, name: 'Fri') }

  before do
    [mon_order, wed_order, fri_order].each do |order|
      order.update_columns(order_type: 'recurring', recurrent_id: mon_order.id, template_id: order.id)
    end
  end

  it 'returns the passed delivery datetime if the days match' do
    monday_delivery_at = base_delivery_at
    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: mon_order, new_delivery_at: monday_delivery_at).call

    expect(recurring_delivery_at).to eq(monday_delivery_at)

    wednesday_delivery_at = base_delivery_at + 2.days
    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: wed_order, new_delivery_at: wednesday_delivery_at).call

    expect(recurring_delivery_at).to eq(wednesday_delivery_at)

    friday_delivery_at = base_delivery_at + 4.days
    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: fri_order, new_delivery_at: friday_delivery_at).call

    expect(recurring_delivery_at).to eq(friday_delivery_at)
  end

  it 'returns the next day in the calendar that matches the orders delivery day by name' do
    sunday_delivery_at = base_delivery_at - 1.day

    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: mon_order, new_delivery_at: sunday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at)

    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: wed_order, new_delivery_at: sunday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at + 2.days)

    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: fri_order, new_delivery_at: sunday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at + 4.days)
  end

  it 'returns the corrent day of the next week in the calendar that matches the orders delivery day' do
    saturday_delivery_at = base_delivery_at + 6.days

    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: mon_order, new_delivery_at: saturday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at + 7.days)

    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: wed_order, new_delivery_at: saturday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at + 9.days)

    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: fri_order, new_delivery_at: saturday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at + 11.days)
  end

  it 'returns the next day in the calendar that matches the orders delivery_at day' do
    sunday_delivery_at = base_delivery_at - 1.day

    mon_order.update_column(:delivery_at, base_delivery_at - 1.week)
    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: mon_order, new_delivery_at: sunday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at)

    wed_order.update_column(:delivery_at, base_delivery_at - 1.week + 2.days)
    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: wed_order, new_delivery_at: sunday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at + 2.days)

    fri_order.update_column(:delivery_at, base_delivery_at - 1.week + 4.days)
    recurring_delivery_at = Orders::GetNextRecurringDeliveryAt.new(order: fri_order, new_delivery_at: sunday_delivery_at).call
    expect(recurring_delivery_at).to eq(base_delivery_at + 4.days)
  end

end
