require 'rails_helper'

RSpec.describe Orders::RenewOrders, type: :service, orders: true do

  let!(:now) { Time.zone.now }
  let!(:order1) { create(:order, :confirmed, name: 'order1', delivery_at: now + 1.weeks) }
  let!(:order2) { create(:order, :confirmed, name: 'order2', delivery_at: now + 2.weeks) }
  let!(:order3) { create(:order, :confirmed, name: 'order3', delivery_at: now + 3.weeks) }

  before do
    # make orders recurring orders
    [order1, order2, order3].each do |order|
      order.update_columns(order_type: 'recurrent', recurrent_id: order.id, template_id: order.id, renewed_from_id: order.id, pattern: '1.week')
    end

    order_renewer = double(Orders::Recurring::Renew)
    allow(Orders::Recurring::Renew).to receive(:new).and_return(order_renewer)
    renewed_order = OpenStruct.new(id: rand(1..10), delivery_at: now + rand(1..10).weeks)
    renew_result = OpenStruct.new(success?: true, errors: false, renewed_order: renewed_order)
    allow(order_renewer).to receive(:call).and_return(renew_result)
  end

  it 'renews all renewable orders' do
    expect(Orders::Recurring::Renew).to receive(:new).with(order: order1)
    expect(Orders::Recurring::Renew).to receive(:new).with(order: order2)
    expect(Orders::Recurring::Renew).to receive(:new).with(order: order3)

    orders_renewer = Orders::RenewOrders.new(time: now).call
    expect(orders_renewer).to be_success
  end

  it 'does not renew orders without a recurrent_id' do
    order1.update_column(:recurrent_id, nil)
    expect(Orders::Recurring::Renew).to_not receive(:new).with(order: order1)

    orders_renewer = Orders::RenewOrders.new(time: now).call
    expect(orders_renewer).to be_success
  end

  it 'does not renew orders without a template_id' do
    order2.update_column(:template_id, nil)
    expect(Orders::Recurring::Renew).to_not receive(:new).with(order: order2)

    orders_renewer = Orders::RenewOrders.new(time: now).call
    expect(orders_renewer).to be_success
  end

  it 'does not renew orders without a renewed_from_id' do
    order3.update_column(:renewed_from_id, nil)
    expect(Orders::Recurring::Renew).to_not receive(:new).with(order: order3)

    orders_renewer = Orders::RenewOrders.new(time: now).call
    expect(orders_renewer).to be_success
  end

  it 'does not renew orders which are not confirmed, amended, paused or skipped' do
    order1.update_column(:status, %w[new delivered pending cancelled].sample)
    expect(Orders::Recurring::Renew).to_not receive(:new).with(order: order1)

    orders_renewer = Orders::RenewOrders.new(time: now).call
    expect(orders_renewer).to be_success
  end

  it 'does not renew orders delivered past 5 weeks from now' do
    order2.update_column(:delivery_at, now + rand(6..10).weeks)
    expect(Orders::Recurring::Renew).to_not receive(:new).with(order: order2)

    orders_renewer = Orders::RenewOrders.new(time: now).call
    expect(orders_renewer).to be_success
  end

  context 'recurring orders saved as one-off' do
    let!(:one_off_order) { [order1, order2, order3].sample }
    before do
      one_off_order.update_columns(order_type: 'one-off')
    end

    it 'renews one-off cancelled / skipped orders' do
      one_off_order.update_column(:status, %w[cancelled skipped].sample)
      expect(Orders::Recurring::Renew).to receive(:new).with(order: one_off_order)

      orders_renewer = Orders::RenewOrders.new(time: now).call
      expect(orders_renewer).to be_success
    end

    it 'does not renew one-off orders which are not cancelled and/or skipped' do
      one_off_order.update_column(:status, %w[new draft pending delivered].sample)
      puts "Status: #{one_off_order.status}" if ENV['VERBOSE']
      expect(Orders::Recurring::Renew).to_not receive(:new).with(order: one_off_order)

      orders_renewer = Orders::RenewOrders.new(time: now).call
      expect(orders_renewer).to be_success
    end
  end

end
