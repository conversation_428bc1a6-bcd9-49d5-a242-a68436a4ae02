require 'rails_helper'

RSpec.describe Orders::HandlePublicHolidayOrders, type: :service, orders: true, holidays: true do

  let!(:holiday_on) { Time.zone.now.beginning_of_day }
  let!(:customer1) { create(:customer_profile, :random) }
  let!(:customer2) { create(:customer_profile, :random) }

  let!(:order1) { create(:order, :new, customer_profile: customer1, delivery_at: holiday_on + (1..23).to_a.sample.hour) }
  let!(:order2) { create(:order, :new, customer_profile: customer2, delivery_at: holiday_on + (1..23).to_a.sample.hours) }
  let!(:order3) { create(:order, :new, customer_profile: customer1, delivery_at: holiday_on + (1..23).to_a.sample.hours) }

  let!(:holiday) { create(:holiday, :random, on_date: holiday_on, effective_from: holiday_on.beginning_of_day, effective_to: holiday_on.end_of_day, push_to: (holiday_on + 1.day), state: nil) }

  before do
    # mock customer email sender
    customer_email_sender = delayed_customer_email_sender = double(Customers::Emails::SendPublicHolidayOrdersEmail)
    allow(Customers::Emails::SendPublicHolidayOrdersEmail).to receive(:new).and_return(customer_email_sender)
    allow(customer_email_sender).to receive(:delay).and_return(delayed_customer_email_sender)
    successfull_email_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
    allow(delayed_customer_email_sender).to receive(:call).and_return(successfull_email_sender_response)

    # mock supplier email sender
    supplier_email_sender = delayed_supplier_email_sender = double(Suppliers::Emails::SendPublicHolidayOrdersEmail)
    allow(Suppliers::Emails::SendPublicHolidayOrdersEmail).to receive(:new).and_return(supplier_email_sender)
    allow(supplier_email_sender).to receive(:delay).and_return(delayed_supplier_email_sender)
    successfull_email_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
    allow(delayed_supplier_email_sender).to receive(:call).and_return(successfull_email_sender_response)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'handles the order that deliver on public holiday' do
    order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

    expect(order_handler).to be_success
    expect(order_handler.handled_orders).to include(order1, order2, order3)
  end

  it 'does not handle orders that do not delivery on the public holiday' do
    order1.update_column(:delivery_at, holiday_on - 1.day)
    order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

    expect(order_handler).to be_success
    expect(order_handler.handled_orders).to_not include(order1)
  end

  it 'notifes the customer about the handled orders', notifications: true do
    expect(Customers::Emails::SendPublicHolidayOrdersEmail).to receive(:new).with(customer: customer1, holiday: holiday, handled_orders: [order1, order3])
    expect(Customers::Emails::SendPublicHolidayOrdersEmail).to receive(:new).with(customer: customer2, holiday: holiday, handled_orders: [order2])

    order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

    expect(order_handler).to be_success
  end

  context 'with a holiday for a state' do
    let!(:state1) { %w[NSW VIC QLD WA SA].sample }
    let!(:suburb1) { create(:suburb, :random, state: state1) }

    let!(:state2) { (%w[NSW VIC QLD WA SA] - [state1]).sample }
    let!(:suburb2) { create(:suburb, :random, state: state2) }

    before do
      holiday.update_column(:state, state1)
      [order1, order3].each do |order|
        order.update_column(:delivery_suburb_id, suburb1.id)
      end
      order2.update_column(:delivery_suburb_id, suburb2.id)
    end

    it 'only handles the orders that delivery in the state of the holiday' do
      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

      expect(order_handler).to be_success
      expect(order_handler.handled_orders).to include(order1, order3)
      expect(order_handler.handled_orders).to_not include(order2)
    end
  end

  context 'orders with skip config = true' do
    before do
      [order1, order2, order3].each do |order|
        order.update_column(:skip, true)
      end
    end

    it 'marks the holiday orders as skipped' do
      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

      expect(order_handler).to be_success
      handled_orders = order_handler.handled_orders
      expect(handled_orders).to include(order1, order2, order3)
      expect(handled_orders.map(&:status).uniq).to eq(['skipped'])

      [order1, order2, order3].each(&:reload).each do |order|
        expect(order.status).to eq('skipped')
      end
    end

    it 'marks recurring orders as one-off' do
      [order1, order2, order3].each{|order| order.update_column(:order_type, 'recurrent') }
      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

      expect(order_handler).to be_success
      handled_orders = order_handler.handled_orders
      expect(handled_orders.map(&:order_type).uniq).to eq(['one-off'])
    end

    it 'logs an `Upcomming Public Holiday` event with the passed in skipped orders', event_logs: true do
      expect(EventLogs::Create).to receive(:new).with(event_object: holiday, event: 'upcoming-public-holiday', skipped_orders: [order1, order2, order3].map(&:id).sort, pushed_orders: anything)

      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call
      expect(order_handler).to be_success
    end
  end

  context 'orders with skip config = false (pushed orders)' do
    before do
      [order1, order2, order3].each do |order|
        order.update_column(:skip, false)
      end
    end

    it 'does not skip orders not marked to be skipped' do
      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

      expect(order_handler).to be_success
      expect(order_handler.handled_orders).to include(order1, order2, order3)
      [order1, order2, order3].each(&:reload).each do |order|
        expect(order.status).to_not eq('skipped')
      end
    end

    it 'pushes the order the next day as per holiday config' do
      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

      expect(order_handler).to be_success
      handled_orders = order_handler.handled_orders
      expect(handled_orders).to include(order1, order2, order3)

      order1_new_delivery_at = holiday.push_to.change(hour: order1.delivery_at.hour, min: order1.delivery_at.min, sec: order1.delivery_at.sec)
      expect(order1.reload.delivery_at).to eq(order1_new_delivery_at)

      order2_new_delivery_at = holiday.push_to.change(hour: order2.delivery_at.hour, min: order2.delivery_at.min, sec: order2.delivery_at.sec)
      expect(order2.reload.delivery_at).to eq(order2_new_delivery_at)

      order3_new_delivery_at = holiday.push_to.change(hour: order3.delivery_at.hour, min: order3.delivery_at.min, sec: order3.delivery_at.sec)
      expect(order3.reload.delivery_at).to eq(order3_new_delivery_at)
    end

    it 'marks recurring orders as one-off' do
      [order1, order2, order3].each{|order| order.update_column(:order_type, 'recurrent') }
      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

      expect(order_handler).to be_success
      handled_orders = order_handler.handled_orders
      expect(handled_orders.map(&:order_type).uniq).to eq(['one-off'])
    end

    it 'logs an `Upcomming Public Holiday` event with the passed in pushed orders', event_logs: true do
      expect(EventLogs::Create).to receive(:new).with(event_object: holiday, event: 'upcoming-public-holiday', pushed_orders: [order1, order2, order3].map(&:id).sort, skipped_orders: anything)

      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call
      expect(order_handler).to be_success
    end
  end

  context 'with suppliers' do
    let!(:supplier1) { create(:supplier_profile, :random, company_name: 'supplier1') }
    let!(:supplier2) { create(:supplier_profile, :random, company_name: 'supplier2') }

    let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }

    let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }
    let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

    let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }

    it 'notifes the suppliers about the handled orders', notifications: true do
      expect(Suppliers::Emails::SendPublicHolidayOrdersEmail).to receive(:new).with(supplier: supplier1, holiday: holiday, handled_orders: anything) # pass anything as orders have changed
      expect(Suppliers::Emails::SendPublicHolidayOrdersEmail).to receive(:new).with(supplier: supplier2, holiday: holiday, handled_orders: anything)

      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

      expect(order_handler).to be_success
    end
  end

  context 'with a non-pushable holiday OR no holiday orders' do
    before do
      if [true, false].sample
        holiday.update_column(:push_to, nil) # make holiday non-pushable
      else
        [order1, order2, order3].each do |order|
          order.update_column(:delivery_at, holiday_on + 2.days) # mark the orders as not on Holiday
        end        
      end
    end

    it 'returns as un-successful but without errors' do
      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call

      expect(order_handler).to_not be_success
      expect(order_handler.errors).to be_blank
    end

    it 'does not notify any customers', notifications: true do
      expect(Customers::Emails::SendPublicHolidayOrdersEmail).to_not receive(:new)

      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call
      expect(order_handler).to_not be_success
    end

    it 'does not notify any suppliers', notifications: true do
      expect(Suppliers::Emails::SendPublicHolidayOrdersEmail).to_not receive(:new)

      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call
      expect(order_handler).to_not be_success
    end

    it 'does not logs an `Upcomming Public Holiday` event', event_logs: true do
      expect(EventLogs::Create).to_not receive(:new)

      order_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call
      expect(order_handler).to_not be_success
    end
  end

end
