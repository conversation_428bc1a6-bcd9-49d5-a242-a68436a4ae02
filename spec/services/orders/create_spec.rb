require 'rails_helper'

RSpec.describe Orders::Create, type: :service, orders: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:suburb) { create(:suburb, :random) }

  it 'creates an one-off draft order by default' do
    order_creator = Orders::Create.new(order_params: {}, customer: customer).call
    expect(order_creator).to be_success

    created_order = order_creator.order
    expect(created_order).to be_present
    expect(created_order).to be_persisted
    expect(created_order.status).to eq('draft')
    expect(created_order.order_type).to eq('one-off')
    expect(created_order.order_variant).to eq('general')
    expect(created_order.uuid).to be_present
  end

  it 'assigns the passed in params to the order' do
    order_params = {
      delivery_address_level: rand(1..10).to_s,
      delivery_address: Faker::Address.street_address,
      delivery_suburb_id: suburb.id,
      delivery_at: Time.zone.now + 2.days,
    }

    order_creator = Orders::Create.new(order_params: order_params, customer: customer).call
    expect(order_creator).to be_success

    created_order = order_creator.order
    expect(created_order.delivery_address_level).to eq(order_params[:delivery_address_level])
    expect(created_order.delivery_address).to eq(order_params[:delivery_address])
    expect(created_order.delivery_suburb_id).to eq(order_params[:delivery_suburb_id])
    expect(created_order.delivery_at).to eq(order_params[:delivery_at])
  end

  it 'attaches the customer to the order' do
    order_creator = Orders::Create.new(order_params: {}, customer: customer).call
    expect(order_creator).to be_success

    created_order = order_creator.order
    expect(created_order.customer_profile).to eq(customer)
  end

  it 'saves the whodunnit of the order' do
    user = customer.user
    order_creator = Orders::Create.new(order_params: {}, customer: customer, whodunnit: user).call
    expect(order_creator).to be_success

    created_order = order_creator.order
    expect(created_order.whodunnit_id).to eq(user.id)
  end

  context 'with cookies' do
    let!(:cookie_suburb) { create(:suburb, :random) }
    let!(:cookies) do
      {
        yordar_street_address: Faker::Address.street_address,
        yordar_suburb_id: cookie_suburb.id
      }
    end

    it 'sets the order delivery details as per cookie data' do
      order_creator = Orders::Create.new(order_params: {}, customer: customer, cookies: cookies).call
      expect(order_creator).to be_success

      created_order = order_creator.order
      expect(created_order.delivery_address).to eq(cookies[:yordar_street_address])
      expect(created_order.delivery_suburb).to eq(cookie_suburb)
    end

    it 'overrides the delivery suburb data from passed in suburb over the data from the cookies' do
      explicit_suburb = create(:suburb, :random)
      order_creator = Orders::Create.new(order_params: {}, suburb: explicit_suburb, customer: customer, cookies: cookies).call
      expect(order_creator).to be_success

      created_order = order_creator.order
      expect(created_order.delivery_address).to eq(cookies[:yordar_street_address]) # from cookie
      expect(created_order.delivery_suburb).to eq(explicit_suburb) # from passed in suburb
    end

    it 'overrides the data from the params over the data from the cookies' do
      order_params = { delivery_address: Faker::Address.street_address }
      order_creator = Orders::Create.new(order_params: order_params, customer: customer, cookies: cookies).call
      expect(order_creator).to be_success

      created_order = order_creator.order
      expect(created_order.delivery_address).to eq(order_params[:delivery_address]) # from order params
      expect(created_order.delivery_suburb).to eq(cookie_suburb) # from cookie
    end
  end # with cookies

  context 'with suburb' do
    let!(:explicit_suburb) { create(:suburb, :random) }
    it 'sets the order delivery details as per passed in suburb' do
      order_creator = Orders::Create.new(order_params: {}, suburb: explicit_suburb, customer: customer).call
      expect(order_creator).to be_success

      created_order = order_creator.order
      expect(created_order.delivery_suburb).to eq(explicit_suburb)
    end

    it 'overrides the delivery suburb data from passed in params over passed in suburb' do
      order_params = { delivery_suburb_id: suburb.id }
      order_creator = Orders::Create.new(order_params: order_params, suburb: explicit_suburb, customer: customer).call
      expect(order_creator).to be_success

      created_order = order_creator.order
      expect(created_order.delivery_address).to_not eq(explicit_suburb) # passed in
      expect(created_order.delivery_suburb).to eq(suburb) # from params
    end
  end

  it 'sets the order variant as home_delivery if it is_home_delivery' do
    order_params = { is_home_delivery: true }
    order_creator = Orders::Create.new(order_params: order_params, customer: customer).call
    expect(order_creator).to be_success

    created_order = order_creator.order
    expect(created_order.order_variant).to eq('home_delivery')
  end

  context 'with Meal Plan ID', meal_plans: true do
    let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer) }

    let!(:meal_plan_order_params) do
      {
        mealUUID: meal_plan.uuid,
        delivery_at: Time.zone.now + 2.days
      }
    end

    let!(:meal_plan_attacher) { double(MealPlans::AttachToOrder) }

    before do
      # mock woolworths order attacher
      allow(MealPlans::AttachToOrder).to receive(:new).and_return(meal_plan_attacher)
      successfull_response = OpenStruct.new(success?: true, errors: [])
      allow(meal_plan_attacher).to receive(:call).and_return(successfull_response)
    end

    it 'makes a requrest to attach the meal plan to the order' do
      expect(MealPlans::AttachToOrder).to receive(:new).with(order: anything, meal_plan: meal_plan, order_params: meal_plan_order_params) # order is created in service object

      order_creator = Orders::Create.new(order_params: meal_plan_order_params, customer: customer).call
      expect(order_creator).to be_success
    end

    it 'doesn\'t make a request to attach meal plan to order if order already has a meal plan attached' do
      order_params_with_meal_id = { meal_plan_id: meal_plan.id } # the inital order update already sets the meal plan id against the order
      expect(MealPlans::AttachToOrder).to_not receive(:new)

      order_creator = Orders::Create.new(order_params: order_params_with_meal_id, customer: customer).call
      expect(order_creator).to be_success
    end
  end

  context 'as a Woolworths order', woolworths: true do
    let!(:woolworths_order_params) do
      {
        delivery_address_level: rand(1..10).to_s,
        delivery_address: Faker::Address.street_address,
        delivery_suburb_id: suburb.id,
        delivery_at: Time.zone.now + 2.days,
        is_woolworths_order: true
      }
    end

    let!(:woolworths_order_attacher) { double(Woolworths::Order::AttachToOrder) }
    let!(:woolworths_cart_emptyer) { double(Woolworths::API::EmptyTrolley) }

    before do
      # mock woolworths order attacher
      allow(Woolworths::Order::AttachToOrder).to receive(:new).and_return(woolworths_order_attacher)
      allow(woolworths_order_attacher).to receive(:call).and_return(true)

      # mock woolworths cart emptyer
      allow(Woolworths::API::EmptyTrolley).to receive(:new).and_return(woolworths_cart_emptyer)
      allow(woolworths_cart_emptyer).to receive(:call).and_return(true)

      # mock woolworths delivery details processor
      woolworths_details_processor = delayed_woolworths_details_processor = double(Woolworths::ProcessOrderDeliveryDetails)
      allow(Woolworths::ProcessOrderDeliveryDetails).to receive(:new).and_return(woolworths_details_processor)
      allow(woolworths_details_processor).to receive(:delay).and_return(delayed_woolworths_details_processor)
      allow(delayed_woolworths_details_processor).to receive(:call).and_return(true)
    end

    it 'attaches a Woolworths Order to the order' do
      expect(Woolworths::Order::AttachToOrder).to receive(:new).with(order: anything) # created order

      order_creator = Orders::Create.new(order_params: woolworths_order_params, customer: customer).call
      expect(order_creator).to be_success
    end

    it 'requests the Woolworths Cart to be emptyied' do
      expect(Woolworths::API::EmptyTrolley).to receive(:new).with(order: anything) # created order

      order_creator = Orders::Create.new(order_params: woolworths_order_params, customer: customer).call
      expect(order_creator).to be_success
    end

    it 'requests Woolworths Process Delivery Details' do
      expect(Woolworths::ProcessOrderDeliveryDetails).to receive(:new).with(order: anything) # created order

      order_creator = Orders::Create.new(order_params: woolworths_order_params, customer: customer).call
      expect(order_creator).to be_success
    end

    it 'does not process Woolworths Order if any delivery details are misssing' do
      delivery_errors = {
        delivery_address: 'Please select a valid street address',
        delivery_suburb_id: 'We are unable to process the order for this suburb',
        delivery_at: 'Please select a valid delivery date-time'
      }
      missing_field = delivery_errors.keys.sample

      expect(Woolworths::Order::AttachToOrder).to_not receive(:new)
      expect(Woolworths::API::EmptyTrolley).to_not receive(:new)
      expect(Woolworths::ProcessOrderDeliveryDetails).to_not receive(:new)

      order_creator = Orders::Create.new(order_params: woolworths_order_params.except(missing_field), customer: customer).call
      expect(order_creator).to_not be_success
      expect(order_creator.errors).to include(delivery_errors[missing_field])
    end

    context 'with Woolworths errors' do
      before do
        no_account_available = [true, false].sample
        has_connection_error = [true, false].sample
        no_account_error = [true, false].sample
        case
        when no_account_available
          allow(woolworths_order_attacher).to receive(:call).and_raise(Woolworths::Order::AttachToOrder::NoAccountsAvailableError.new)
        when has_connection_error
          allow(woolworths_cart_emptyer).to receive(:call).and_raise(Woolworths::API::Connection::ConnectionError.new)
        when no_account_error
          allow(woolworths_cart_emptyer).to receive(:call).and_raise(Woolworths::API::Connection::NoAccountError.new)
        else
          allow(woolworths_cart_emptyer).to receive(:call).and_raise(Woolworths::API::EmptyTrolley::EmptyTrolleyError.new)
        end
      end

      it 'returns with errors' do
        order_creator = Orders::Create.new(order_params: woolworths_order_params, customer: customer).call

        expect(order_creator).to_not be_success
        expect(order_creator.errors).to include('The Woolworths monkeys are busy right now, please try again in a few minutes')
      end

      it 'does not process Woolworths delivery details' do
        expect(Woolworths::ProcessOrderDeliveryDetails).to_not receive(:new)
        order_creator = Orders::Create.new(order_params: woolworths_order_params, customer: customer).call

        expect(order_creator).to_not be_success
      end
    end # Woolworths Errors
  end # Woolworths Order

end
