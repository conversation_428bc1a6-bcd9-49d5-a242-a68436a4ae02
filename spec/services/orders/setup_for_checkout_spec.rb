require 'rails_helper'

RSpec.describe Orders::SetupForCheckout, type: :service, orders: true do

  let!(:customer) { create(:customer_profile, :random, :with_flags) }
  let!(:order) { create(:order, :draft, customer_profile: nil, delivery_address: nil, delivery_suburb: nil, delivery_at: nil) }
  let!(:suburb) { create(:suburb, :random) }

  before do
    # mock promotions syncer
    promotions_syncer = double(Promotions::SyncWithOrder)
    allow(Promotions::SyncWithOrder).to receive(:new).and_return(promotions_syncer)
    allow(promotions_syncer).to receive(:call).and_return(true)
  end

  context 'customer details' do
    it 'sets the orders customer equal to the passed in profile' do
      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

      expect(order_setter).to be_success
      checkout_order = order_setter.order
      expect(checkout_order.customer_profile).to eq(customer)
    end

    context 'customer contact details' do
      it 'sets the order\'s contact details based on customer' do
        order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

        expect(order_setter).to be_success
        checkout_order = order_setter.order

        expect(checkout_order.contact_name).to eq(customer.name)
        expect(checkout_order.phone).to eq(customer.contact_phone)
      end

      it 'sets the order\'s phone to customer\'s mobile if customer\'s phone is missing' do
        customer.update_columns(contact_phone: nil, mobile: Faker::PhoneNumber.cell_phone)

        order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

        expect(order_setter).to be_success
        checkout_order = order_setter.order
        expect(checkout_order.phone).to eq(customer.mobile)
      end
    end # contact details

    context 'customer company' do
      let!(:company) { create(:company, :random) }
      before do
        customer.update_column(:company_name, Faker::Company.name)
      end

      it 'sets the order\'s company name to the customer\'s (non-associated) saved company\'s name' do
        order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

        expect(order_setter).to be_success
        checkout_order = order_setter.order
        expect(checkout_order.company_name).to eq(customer.company_name)
      end

      it 'sets the order\'s company name to the customer\'s associated company\'s name if present' do
        customer.update_column(:company_id, company.id)
        order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

        expect(order_setter).to be_success
        checkout_order = order_setter.order
        expect(checkout_order.company_name).to eq(company.name)
      end
    end # company details
  end # customer details

  context 'latest order delivery details' do
    let!(:customer_order1) { create(:order, customer_profile: customer, delivery_instruction: Faker::Lorem.sentence, status: %w[new confirmed delivered].sample) }
    let!(:customer_order2) { create(:order, customer_profile: customer, delivery_instruction: Faker::Lorem.sentence, status: %w[new confirmed delivered].sample) }

    it 'sets the delivery suburb to the delivery suburb of the customer\'s latest order' do
      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

      expect(order_setter).to be_success
      checkout_order = order_setter.order
      expect(checkout_order.delivery_suburb).to eq(customer_order2.delivery_suburb)
    end

    it 'sets the delivery instructions to the delivery instructions from the customer\'s latest order' do
      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

      expect(order_setter).to be_success
      checkout_order = order_setter.order
      expect(checkout_order.delivery_instruction).to eq(customer_order2.delivery_instruction)
    end

    it 'doesn\'t reset the delivery suburb and/or instructions to the customer\'s latest order if already set' do
      delivery_instruction = Faker::Lorem.sentence
      order.update_columns(delivery_suburb_id: suburb.id, delivery_instruction: delivery_instruction)

      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

      expect(order_setter).to be_success
      checkout_order = order_setter.order

      expect(checkout_order.delivery_suburb).to eq(suburb)
      expect(checkout_order.delivery_instruction).to eq(delivery_instruction)

      expect(checkout_order.delivery_suburb).to_not eq(customer_order2.delivery_suburb)
      expect(checkout_order.delivery_instruction).to_not eq(customer_order2.delivery_instruction)
    end
  end

  context 'cookie details', cookies: true do
    it 'sets the delivery suburb to the suburb cookie' do
      cookies = { yordar_suburb_id: suburb.id }
      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer, cookies: cookies).call

      expect(order_setter).to be_success
      checkout_order = order_setter.order

      expect(checkout_order.delivery_suburb).to eq(suburb)
    end

    it 'sets the delivery address to the street address cookie only if suburb matches' do
      cookies = { yordar_suburb_id: suburb.id, yordar_street_address: Faker::Address.street_address }
      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer, cookies: cookies).call

      expect(order_setter).to be_success
      checkout_order = order_setter.order

      expect(checkout_order.delivery_address).to eq(cookies[:yordar_street_address])
    end

    context 'exiting delivery suburb' do
      let!(:delivery_suburb) { create(:suburb, :random) }
      before do
        order.update_column(:delivery_suburb_id, delivery_suburb.id)
      end

      it 'does not update delivery suburb from cookie if already set' do
        cookies = { yordar_suburb_id: suburb.id, street_address: Faker::Address.street_address }
        order_setter = Orders::SetupForCheckout.new(order: order, profile: customer, cookies: cookies).call

        expect(order_setter).to be_success
        checkout_order = order_setter.order

        expect(checkout_order.delivery_suburb_id).to_not eq(cookies[:yordar_suburb_id])
      end

      it 'does not set the delivery address to the street address saved in cookie if cookie suburb does not match order suburb' do
        cookies = { yordar_suburb_id: suburb.id, yordar_street_address: Faker::Address.street_address }
        order_setter = Orders::SetupForCheckout.new(order: order, profile: customer, cookies: cookies).call

        expect(order_setter).to be_success
        checkout_order = order_setter.order

        expect(checkout_order.delivery_address).to_not eq(cookies[:yordar_street_address])
      end
    end # existing delivery suburb
  end

  it 'makes a request to sync promotions with the order', promotions: true do
    expect(Promotions::SyncWithOrder).to receive(:new).with(order: order)

    order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call
    expect(order_setter).to be_success
  end

  context 'with recurring orders' do
    let!(:order2) { create(:order, :draft) }
    let!(:order3) { create(:order, :draft) }

    before do
      [order, order2, order3].each do |order|
        order.update_columns(order_type: 'recurrent', recurrent_id: order2.id) # make order2 the recurrent order
      end
    end

    it 'returns the first recurrent order as the order' do
      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

      expect(order_setter).to be_success
      expect(order_setter.order).to eq(order2)
    end

    it 'returns all the other recurrent orders' do
      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

      expect(order_setter).to be_success
      expect(order_setter.recurrent_orders).to include(order, order2, order3)
    end
  end # recurring orders

  context 'with Woolworths order' do
    let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }
    let!(:order_validator) { double(Woolworths::Order::Validate) }

    before do
      allow(Woolworths::Order::Validate).to receive(:new).and_return(order_validator)
      successfull_response = OpenStruct.new(success?: true, delivery_window_text: 'Delivery Window Text')
      allow(order_validator).to receive(:call).and_return(successfull_response)
    end

    it 'requests the order to be validated' do
      expect(Woolworths::Order::Validate).to receive(:new).with(order: order)

      order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call
      expect(order_setter).to be_success
    end

    context 'with validation errors' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, errors: { order: ['order-validation-error'] })
        allow(order_validator).to receive(:call).and_return(unsuccessfull_response)
      end

      it 'returns unsuccessfully with the validation errors' do
        order_setter = Orders::SetupForCheckout.new(order: order, profile: customer).call

        expect(order_setter).to_not be_success
        expect(order_setter.errors.keys).to include(:order)
        expect(order_setter.errors[:order]).to include('order-validation-error')
      end
    end
  end
end
