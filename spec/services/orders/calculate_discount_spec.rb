require 'rails_helper'

RSpec.describe Orders::CalculateDiscount, type: :service, orders: true do

  let(:order) { create(:order, :random) }
  let(:delivery_fee) { 10 }
  let(:subtotal) { 1000.0 }

  let!(:discountable_amount) { delivery_fee + subtotal }

  it 'returns the discount if the order already has it calculated' do
    order.update_column(:discount, 100.0)
    calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount).call

    expect(calculated_discount).to eq(100.0)
  end

  context 'with a coupon', coupons: true do
    let!(:amount_coupon) { create(:coupon, :random, type: 'amount', amount: 100) }
    let!(:percent_coupon) { create(:coupon, :random, type: 'percentage', amount: 15) }

    it 'calculates the discount for a coupon of type amount' do
      order.update_column(:coupon_id, amount_coupon.id)
      calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount).call

      expect(calculated_discount).to eq(100.0)
    end

    it 'calculates the discount for a coupon of type percent based on delivery and sub total' do
      order.update_column(:coupon_id, percent_coupon.id)
      calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount).call

      expect(calculated_discount).to eq(151.5)
    end

    it 'returns the saved discount if greater than 0 even for an order with attached coupon' do
      order.update_columns(coupon_id: [amount_coupon, percent_coupon].sample.id, discount: 200)

      calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount).call

      expect(calculated_discount).to eq(200)
    end

    it 'recalculates discount from coupon even if discount is saved' do
      order.update_columns(coupon_id: [amount_coupon, percent_coupon].sample.id, discount: 200)

      calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount, recalculate: true).call

      expect(calculated_discount).to_not eq(200)

      expected_discount = order.coupon == amount_coupon ? 100 : 151.5
      expect(calculated_discount).to eq(expected_discount)
    end
  end

  context 'with a promotion', promotions: true do
    let!(:amount_promotion) { create(:promotion, :random, kind: 'amount', amount: 100) }
    let!(:percent_promotion) { create(:promotion, :random, kind: 'percentage', amount: 15) }

    it 'calculates the discount for a promotion of kind amount' do
      order.update_column(:promotion_id, amount_promotion.id)
      calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount).call

      expect(calculated_discount).to eq(100.0)
    end

    it 'calculates the discount for a promotion of kind percent based on delivery and sub total' do
      order.update_column(:promotion_id, percent_promotion.id)
      calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount).call

      expect(calculated_discount).to eq(151.5)
    end

    it 'returns the saved discount if greater than 0 even for an order with attached promotion' do
      order.update_columns(promotion_id: [amount_promotion, percent_promotion].sample.id, discount: 200)

      calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount).call

      expect(calculated_discount).to eq(200)
    end

    it 'recalculates discount from promotion even if discount is saved' do
      order.update_columns(promotion_id: [amount_promotion, percent_promotion].sample.id, discount: 200)

      calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount, recalculate: true).call

      expect(calculated_discount).to_not eq(200)

      expected_discount = order.promotion == amount_promotion ? 100 : 151.5
      expect(calculated_discount).to eq(expected_discount)
    end
  end

  it 'returns a discount of 0 if no coupons or no promotions or discounts are saved' do
    calculated_discount = Orders::CalculateDiscount.new(order: order, discountable_amount: discountable_amount).call

    expect(calculated_discount).to eq(0.0)
  end

end
