require 'rails_helper'

RSpec.describe Orders::PayByCreditCard, type: :service, orders: true, invoices: true, payments: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:credit_card) { create(:credit_card, :valid_stripe_payment) }
  let!(:order) { create(:order, :delivered, customer_profile: customer, customer_surcharge: 0, customer_total: 1000, credit_card_id: credit_card.id) }

  let!(:invoice) { create(:invoice, :random, amount_price: order.customer_total) }

  before do
    order.update_with_invoice = true
    order.update_column(:invoice_id, invoice.id)
    invoice.reload

    # mock payment processor
    payment_processor = double(Payments::ProcessPayment)
    allow(Payments::ProcessPayment).to receive(:new).and_return(payment_processor)
    successfull_response = OpenStruct.new(success?: true, payment: 'payment', errors: [])
    allow(payment_processor).to receive(:call).and_return(successfull_response)

    # mock email sender
    email_sender = delayed_email_sender = double(Customers::Emails::SendInvoiceReceiptEmail)
    allow(Customers::Emails::SendInvoiceReceiptEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock charge refunder
    charge_refunder = double(Stripe::RefundOrderCharge)
    allow(Stripe::RefundOrderCharge).to receive(:new).and_return(charge_refunder)
    stripe_refund = OpenStruct.new(id: SecureRandom.hex(7))
    refunder_response = OpenStruct.new(success?: true, refund: stripe_refund)
    allow(charge_refunder).to receive(:call).and_return(refunder_response)
  end

  it 'creates a payment record' do
    order_payer = Orders::PayByCreditCard.new(order: order).call
    expect(order_payer).to be_success

    created_payment = order_payer.payment
    expect(created_payment).to be_a(Payment)
    expect(created_payment.amount).to eq(order.customer_total)
    expect(created_payment.order).to eq(order)
    expect(created_payment.invoice).to eq(invoice)
    expect(created_payment.credit_card).to eq(credit_card)
    expect(created_payment.user_id).to eq(customer.user.id)
  end

  it 'processes the payment' do
    expect(Payments::ProcessPayment).to receive(:new)

    order_payer = Orders::PayByCreditCard.new(order: order).call
    expect(order_payer).to be_success
  end

  it 'marks the order as being paid' do
    order_payer = Orders::PayByCreditCard.new(order: order).call
    expect(order_payer).to be_success

    expect(order.reload.payment_status).to eq('paid')
  end

  it 'updates the invoice with payment totals' do
    order_payer = Orders::PayByCreditCard.new(order: order).call
    expect(order_payer).to be_success

    invoice.reload
    expect(invoice.payment_value).to eq(order.customer_total)
    expect(invoice.payment_status).to eq('paid')
  end

  it 'updates the invoice with payment status as partial if paid amount is less than invoice amount' do
    invoice.update_column(:amount_price, order.customer_total + rand(1..10))

    order_payer = Orders::PayByCreditCard.new(order: order).call
    expect(order_payer).to be_success

    invoice.reload
    expect(invoice.payment_value).to eq(order.customer_total)
    expect(invoice.payment_status).to eq('partial')
  end

  it 'send the tax receipt to the customer', notifications: true do
    expect(Customers::Emails::SendInvoiceReceiptEmail).to receive(:new).with(customer: customer, invoice: invoice)

    order_payer = Orders::PayByCreditCard.new(order: order).call
    expect(order_payer).to be_success
  end

  context 'with existing on-hold order charges' do
    let!(:order_charge1) { create(:order_charge, :random, order: order) }
    let!(:order_charge2) { create(:order_charge, :random, order: order, refund_token: SecureRandom.hex(7), status: 'refunded') }
    let!(:order_charge3) { create(:order_charge, :random, order: order) }
    let!(:order_charge4) { create(:order_charge, :random, order: order, stripe_token: nil) }
    let!(:order_charge5) { create(:order_charge, :random, order: order, status: %w[refunded failed].sample) }

    it 'refunds/cancels all active order charges' do
      expect(Stripe::RefundOrderCharge).to receive(:new).with(order_charge: order_charge1)
      expect(Stripe::RefundOrderCharge).to_not receive(:new).with(order_charge: order_charge2) # already refunded
      expect(Stripe::RefundOrderCharge).to receive(:new).with(order_charge: order_charge3)
      expect(Stripe::RefundOrderCharge).to_not receive(:new).with(order_charge: order_charge4) # no stripe token
      expect(Stripe::RefundOrderCharge).to_not receive(:new).with(order_charge: order_charge5) # refunded / failed on-hold charge

      order_payer = Orders::PayByCreditCard.new(order: order).call
      expect(order_payer).to be_success

      expect(order_charge1.reload).to be_refunded
      expect(order_charge3.reload).to be_refunded
    end
  end

  context 'with an unsuccessfull payment process' do
    before do
      payment_processor = double(Payments::ProcessPayment)
      allow(Payments::ProcessPayment).to receive(:new).and_return(payment_processor)
      unsuccessfull_response = OpenStruct.new(success?: false, payment: 'payment', errors: ['payment process error'])
      allow(payment_processor).to receive(:call).and_return(unsuccessfull_response)
    end

    it 'marks the order as being errored' do
      order_payer = Orders::PayByCreditCard.new(order: order).call
      expect(order_payer).to_not be_success

      expect(order_payer.errors).to include('payment process error')
      expect(order.reload.payment_status).to eq('error')
    end

    it 'does not updates the invoice with payment totals' do
      order_payer = Orders::PayByCreditCard.new(order: order).call
      expect(order_payer).to_not be_success

      invoice.reload
      expect(invoice.payment_value).to_not eq(order.customer_total)
      expect(invoice.payment_status).to_not eq('paid')
    end

    it 'does not send the tax receipt to the customer', notifications: true do
      expect(Customers::Emails::SendInvoiceReceiptEmail).to_not receive(:new).with(customer: customer, invoice: invoice)

      order_payer = Orders::PayByCreditCard.new(order: order).call
      expect(order_payer).to_not be_success
    end
  end

  context 'with an errored payment process' do
    before do
      allow(Payments::ProcessPayment).to receive(:new).and_raise(RuntimeError.new)
    end

    it 'errors out' do
      order_payer = Orders::PayByCreditCard.new(order: order).call
      expect(order_payer).to_not be_success

      expect(order_payer.errors).to include("Failed to process payment or send tax receipt for order #{order.id}")
    end
  end

  context 'with a passed in credit card' do
    let!(:new_credit_card) { create(:credit_card, :valid_stripe_payment) }

    it 'creates a payment record with the passed in credit card' do
      order_payer = Orders::PayByCreditCard.new(order: order, credit_card: new_credit_card).call
      expect(order_payer).to be_success

      created_payment = order_payer.payment
      expect(created_payment.credit_card).to eq(new_credit_card)
    end

    it 'attaches the passed in credit card' do
      order_payer = Orders::PayByCreditCard.new(order: order, credit_card: new_credit_card).call
      expect(order_payer).to be_success

      expect(order.credit_card).to eq(new_credit_card)

      # recalculate surcharge
      expect(order.customer_surcharge).to eq(0) # same as before
    end

    context 'for an order with unattached credit card' do
      let!(:supplier) { create(:supplier_profile, :random) }
      let!(:order_line) { create(:order_line, :random, supplier_profile: supplier, order: order, quantity: 9, price: 101.01) }

      before do
        order.update_column(:credit_card_id, nil)
      end

      it 'adds a surcharge of the newly attached credit card' do
        expect(order.customer_surcharge).to eq(0)

        order_payer = Orders::PayByCreditCard.new(order: order, credit_card: new_credit_card).call
        expect(order_payer).to be_success

        gst_inc_value = 9 * 101.01 * 1.1
        expected_surcharge = (gst_inc_value * CreditCards::FetchSurcharge::STRIPE_DOMESTIC_PERCENT / 100) + CreditCards::FetchSurcharge::STRIPE_FEE
        expected_surcharge /= (1 - (CreditCards::FetchSurcharge::STRIPE_DOMESTIC_PERCENT / 100))
        expect(order.customer_surcharge).to eq(expected_surcharge.round(2))
      end
    end
  end

  context 'errors' do
    it 'cannot pay without an order' do
      order_payer = Orders::PayByCreditCard.new(order: nil).call
      expect(order_payer).to_not be_success

      expect(order_payer.errors).to include('Cannot pay without an order')
    end

    it 'cannot pay without an attached or passed in credit card' do
      order.update_column(:credit_card_id, nil)
      order_payer = Orders::PayByCreditCard.new(order: order).call
      expect(order_payer).to_not be_success

      expect(order_payer.errors).to include('Cannot pay without a credit card')
    end

    it 'cannot pay with a malformed eway/stripe credit card' do
      credit_card.update_columns(gateway_token: nil, stripe_token: nil)
      order_payer = Orders::PayByCreditCard.new(order: order).call
      expect(order_payer).to_not be_success

      expect(order_payer.errors).to include('Cannot pay without a valid credit card')
    end

    it 'cannot pay without an attached or passed in credit card' do
      order.update_column(:payment_status, 'paid')
      order_payer = Orders::PayByCreditCard.new(order: order).call
      expect(order_payer).to_not be_success

      expect(order_payer.errors).to include('Order is already paid for')
    end
  end

end
