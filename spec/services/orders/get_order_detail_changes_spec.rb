require 'rails_helper'

RSpec.describe Orders::GetOrderDetailChanges, type: :service, orders: true, order_amended_email: true do

  let!(:now) { Time.zone.now }
  let!(:order) { create(:order, :amended, delivery_at: now, suppliers_notified_at: now - 1.day) }

  it 'returns a blank array for a non-updated order' do
    detail_changes = Orders::GetOrderDetailChanges.new(order: order).call

    expect(detail_changes).to be_blank
  end

  context 'with an update of order.delivery_at' do
    let!(:date_format) { Date::DATE_FORMATS[:full] }

    it 'returns the change value of delivery at' do
      cached_delivery_date = order.delivery_at.dup
      order.update(delivery_at: now + 2.days)

      detail_changes = Orders::GetOrderDetailChanges.new(order: order).call
      expect(detail_changes).to be_present

      delivery_date_change = detail_changes.detect{|change| change.field == 'delivery_at' }
      expect(delivery_date_change).to be_present
      expect(delivery_date_change.label).to eq(I18n.t('order.column_label.delivery_at'))
      expect(delivery_date_change.value).to eq("from #{cached_delivery_date.strftime(date_format)} to #{order.delivery_at.strftime(date_format)}")
    end

    it 'returns the new delivery at value if not previously present in the order (version)' do
      order_without_delivery_at = create(:order, :draft, delivery_at: nil, suppliers_notified_at: now - 1.day) # had to create draft order to set delivery at as nil
      order_without_delivery_at.update(delivery_at: now + 2.days)

      detail_changes = Orders::GetOrderDetailChanges.new(order: order_without_delivery_at).call
      expect(detail_changes).to be_present

      delivery_date_change = detail_changes.detect{|change| change.field == 'delivery_at' }
      expect(delivery_date_change).to be_present
      expect(delivery_date_change.value).to eq("from N/A to #{order_without_delivery_at.delivery_at.strftime(date_format)}")
    end
  end

  context 'with other change field update' do
    let!(:changed_fields) { (Orders::GetOrderDetailChanges::CHANGE_FIELDS - %w[delivery_at delivery_type]).sample(2) } # skipped delivery_type field as it has model inclusion check

    it 'returns the new values for each changed field' do
      changed_fields.each do |field|
        order.update("#{field}": "changed value for #{field}")
      end
      order.update(delivery_at: now + 2.days)

      detail_changes = Orders::GetOrderDetailChanges.new(order: order).call
      expect(detail_changes).to be_present

      expect(detail_changes.map(&:field)).to include(*changed_fields)

      field1 = changed_fields.first
      field1_change = detail_changes.detect{|change| change.field == field1 }
      expect(field1_change).to be_present
      expect(field1_change.label).to eq(I18n.t("order.column_label.#{field1}"))
      expect(field1_change.value).to eq(order.send(field1))

      field2 = changed_fields.last
      field2_change = detail_changes.detect{|change| change.field == field2 }
      expect(field2_change).to be_present
      expect(field2_change.label).to eq(I18n.t("order.column_label.#{field2}"))
      expect(field2_change.value).to eq(order.send(field2))
    end

    it 'does not return changes for a field whose value hasn\'t changed from previous value' do
      field1 = changed_fields.first
      field2 = changed_fields.last
      cached_field1_value = order.send(field1).dup

      order.update("#{field1}": cached_field1_value, "#{field2}": "changed value for #{field2}")

      detail_changes = Orders::GetOrderDetailChanges.new(order: order).call
      expect(detail_changes.map(&:field)).to_not include(field1)
      expect(detail_changes.map(&:field)).to include(field2)
    end
  end

  it 'does not return changes if supplier is already notified' do
    field = Orders::GetOrderDetailChanges::CHANGE_FIELDS.sample
    order.update("#{field}": 'updated_values', suppliers_notified_at: Time.zone.now + 1.hour)

    detail_changes = Orders::GetOrderDetailChanges.new(order: order).call
    expect(detail_changes).to be_blank
  end

end
