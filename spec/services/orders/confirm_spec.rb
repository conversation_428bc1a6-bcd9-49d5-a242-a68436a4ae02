require 'rails_helper'

RSpec.describe Orders::Confirm, type: :service, orders: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :new, customer_profile: customer) }

  before do
    email_sender = delayed_email_sender = double(Customers::Emails::SendOrderConfirmationEmail)
    allow(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  it 'successfully confirms a new order' do
    order_confirmer = Orders::Confirm.new(order: order).call

    expect(order_confirmer).to be_success
    confirmed_order = order_confirmer.order
    expect(confirmed_order.id).to eq(order.id)
    expect(confirmed_order.status).to eq('confirmed')
  end

  it 'sends a confirmation email to the customer', notifications: true do
    expect(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).with(customer: customer, order: order)
    order_confirmer = Orders::Confirm.new(order: order).call

    expect(order_confirmer).to be_success
  end

  it 'does not send a confirmation email when a paused subsequent order is confirmed (re-activated)', notifications: true do
    template_order = create(:order, :random)
    order.update_columns(order_type: 'recurrent', status: 'paused', template_id: template_order.id)

    expect(Customers::Emails::SendOrderConfirmationEmail).to_not receive(:new).with(customer: customer, order: order)
    order_confirmer = Orders::Confirm.new(order: order).call

    expect(order_confirmer).to be_success
  end

  context 'with order lines' do
    let!(:order_line1) { create(:order_line, :random, order: order, status: %w[pending notified].sample) }
    let!(:order_line2) { create(:order_line, :random, order: order, status: 'accepted') }

    before do
      order.reload
    end

    it 'does not confirm order if it all of its order lines are not accepted' do
      order_confirmer = Orders::Confirm.new(order: order).call

      expect(order_confirmer).to_not be_success
      non_accepted_order_lines = order.order_lines.where.not(status: 'accepted')
      expect(order_confirmer.errors).to include("#{non_accepted_order_lines.size} order line(s) have not been accepted")
      expect(order.reload.status).to_not eq('confirmed')
    end

    it 'accepts all the order\'s orderlines and confirms order if auto confirming' do
      order_confirmer = Orders::Confirm.new(order: order, auto_confirmation: true).call

      expect(order_confirmer).to be_success
      confirmed_order = order_confirmer.order
      confirmed_order_lines = confirmed_order.order_lines

      expect(confirmed_order_lines.map(&:status).uniq).to match_array(['accepted'])
      expect(confirmed_order.status).to eq('confirmed')
    end
  end

  context 'for a team order' do
    let!(:team_order_attendee1) { create(:team_order_attendee, :random, order: order, status: 'ordered') }
    let!(:order_line1) { create(:order_line, :random, order: order, status: 'accepted', team_order_attendee: team_order_attendee1) }

    let!(:team_order_attendee2) { create(:team_order_attendee, :random, order: order, status: 'pending') }
    let!(:order_line2) { create(:order_line, :random, order: order, status: 'accepted', team_order_attendee: team_order_attendee2) }

    # admin order lines
    let!(:order_line3) { create(:order_line, :random, order: order, status: 'accepted', team_order_attendee: nil) }

    before do
      order.reload.update_column(:order_variant, 'team_order')
    end

    it 'removes (somehow accepted but) non confirmed order lines from the order' do
      order_confirmer = Orders::Confirm.new(order: order, auto_confirmation: true).call

      expect(order_confirmer).to be_success

      expect(order_line1.reload).to be_present
      expect{ order_line2.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect(order_line3.reload).to be_present

      confirmed_order = order_confirmer.order
      expect(confirmed_order.id).to eq(order.id)
      expect(confirmed_order.status).to eq('confirmed')
    end

    it 'sends an order confirmation email to customer if confirmed by supplier (auto_confirmatio = false)', notifications: true do
      [order_line1, order_line2, order_line3].each do |order_line|
        order_line.update_column(:status, 'accepted')
      end
      expect(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).with(customer: customer, order: order)

      order_confirmer = Orders::Confirm.new(order: order, auto_confirmation: false).call
      expect(order_confirmer).to be_success
    end

    it 'does not send an order confimation email to customers on auto confirmation', notifications: true do
      [order_line1, order_line2, order_line3].each do |order_line|
        order_line.update_column(:status, 'accepted')
      end
      expect(Customers::Emails::SendOrderConfirmationEmail).to_not receive(:new).with(customer: customer, order: order)

      order_confirmer = Orders::Confirm.new(order: order, auto_confirmation: true).call

      expect(order_confirmer).to be_success
    end
  end

  context 'errors' do
    it 'does not confirm a missing order' do
      order_confirmer = Orders::Confirm.new(order: nil).call

      expect(order_confirmer).to_not be_success
      expect(order_confirmer.errors).to include('Cannot confirm a missing order')
    end

    it 'does not confirm a an already confirmed order' do
      order.update_column(:status, 'confirmed')
      order_confirmer = Orders::Confirm.new(order: order).call

      expect(order_confirmer).to_not be_success
      expect(order_confirmer.errors).to include('Cannot confirm a an already confirmed order')
    end

    it 'does not confirm if the order is not :new, :amended or :paused' do
      order.update_column(:status, %w[draft pending cancelled delivered].sample)
      order_confirmer = Orders::Confirm.new(order: order).call

      expect(order_confirmer).to_not be_success
      expect(order_confirmer.errors).to include("Cannot confirm this order (current status = #{order.status})")
    end

    it 'does not send a confirmation email to the customer on error', notifications: true do
      order.update_column(:status, %w[draft pending cancelled delivered confirmed].sample)

      expect(Customers::Emails::SendOrderConfirmationEmail).to_not receive(:new).with(customer: customer, order: order)
      order_confirmer = Orders::Confirm.new(order: order).call

      expect(order_confirmer).to_not be_success
    end
  end
end
