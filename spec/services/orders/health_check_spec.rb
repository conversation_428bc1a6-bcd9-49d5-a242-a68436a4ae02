require 'rails_helper'

RSpec.describe Orders::HealthCheck, type: :service, orders: true, documents: true do

  let!(:time) { Time.zone.now }

  let!(:order1) { create(:order, :new, delivery_at: time + 1.day) }
  let!(:order_supplier1) { create(:order_supplier, :random, order: order1) }

  let!(:order2) { create(:order, :new, delivery_at: time + 1.day + 1.hour) }
  let!(:order_supplier2) { create(:order_supplier, :random, order: order2) }

  let!(:order3) { create(:order, :new, delivery_at: time + 1.day + 2.hours) }
  let!(:order_supplier3) { create(:order_supplier, :random, order: order3) }

  before do
    allow(SlackNotifier).to receive(:send).and_return(true)
  end

  context 'with order documents' do
    let!(:order_document1) { create(:document, :random, documentable: order1, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD + 1) }
    let!(:order_document2) { create(:document, :random, documentable: order2, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD - 1) }
    let!(:order_document3) { create(:document, :random, documentable: order3, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD) }

    it 'notifies all orders who have documents with version higher or equal to than the threshold' do
      notifed_orders = Orders::HealthCheck.new(time: time).call

      expect(notifed_orders.size).to eq(2)
      expect(notifed_orders.keys).to include(order1, order3)
    end
  end

  context 'with order supplier documents' do
    let!(:order_supplier_document1) { create(:document, :random, documentable: order_supplier1, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD + 1) }
    let!(:order_supplier_document2) { create(:document, :random, documentable: order_supplier2, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD) }
    let!(:order_supplier_document3) { create(:document, :random, documentable: order_supplier3, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD - 1) }

    it 'notifies all orders who have order suppliers documents with version higher than or equal to the threshold' do
      notifed_orders = Orders::HealthCheck.new(time: time).call

      expect(notifed_orders.size).to eq(2)
      expect(notifed_orders.keys).to include(order1, order2)
    end
  end

  context 'with a combination of documents' do
    let!(:order_document1) { create(:document, :random, documentable: order1, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD - 1) }
    let!(:order_supplier_document1) { create(:document, :random, documentable: order_supplier1, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD + 1) }

    let!(:order_document2) { create(:document, :random, documentable: order2, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD + 2) }
    let!(:order_supplier_document2) { create(:document, :random, documentable: order_supplier2, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD + 1) }

    let!(:order_document3) { create(:document, :random, documentable: order3, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD - 3) }
    let!(:order_supplier_document3) { create(:document, :random, documentable: order_supplier3, kind: 'supplier_order_details', version: Orders::HealthCheck::THRESHOLD - 1) }

    it 'notifies the order if at least 1 order related document has version higher than or equal to the threshold' do
      notifed_orders = Orders::HealthCheck.new(time: time).call

      expect(notifed_orders.size).to eq(2)
      expect(notifed_orders.keys).to include(order1, order2)
    end

    it 'notifies the order with the highest document version' do
      notifed_orders = Orders::HealthCheck.new(time: time).call

      order1_pdf_version = notifed_orders[order1]
      expect(order1_pdf_version).to eq(order_supplier_document1.version)

      order2_pdf_version = notifed_orders[order2]
      expect(order2_pdf_version).to eq([order_document2.version, order_supplier_document2.version].max) # 22
    end

    it 'does not notify orders not to be delivered in the next 2 days' do
      order1.update_column(:delivery_at, [(time + 3.days), (time - 1.day)].sample)
      notifed_orders = Orders::HealthCheck.new(time: time).call

      expect(notifed_orders.keys).to_not include(order1)
    end

    it 'does not notify orders with status not in new/amended/confirmed/pending' do
      order2.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[pending new amended confirmed]).sample)
      notifed_orders = Orders::HealthCheck.new(time: time).call

      expect(notifed_orders.keys).to_not include(order2)
    end

    it 'notifies via Slack' do
      expected_message = ':face_with_head_bandage: Order health check warning: found order(s) with pdf version > 20'
      expect(SlackNotifier).to receive(:send).with(expected_message, attachments: anything)

      Orders::HealthCheck.new(time: time).call
    end
  end

end
