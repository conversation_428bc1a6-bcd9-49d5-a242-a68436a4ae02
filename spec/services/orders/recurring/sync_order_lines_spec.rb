require 'rails_helper'

RSpec.describe Orders::Recurring::SyncOrderLines, type: :service, orders: true, order_lines: true do

  let(:base_delivery_at) { Time.zone.now + 1.day }
  let(:order1) { create(:order, :confirmed, name: 'Order 1', order_type: 'recurrent', pattern: '1.week', delivery_at: base_delivery_at) }
  let(:order2) { create(:order, :confirmed, name: 'Order 2', order_type: 'recurrent', pattern: '1.week', delivery_at: base_delivery_at + 1.week) }

  let(:supplier) { create(:supplier_profile, :random) }
  let(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier, price: 10) }
  let(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier, price: 10) }

  let!(:location1) { create(:location, order: order1) }
  let!(:order_line11) { create(:order_line, :random, order: order1, location: location1, menu_item: menu_item1, supplier_profile: supplier, quantity: 10) }
  let!(:order_line12) { create(:order_line, :random, order: order1, location: location1, menu_item: menu_item2, supplier_profile: supplier, quantity: 10) }

  let!(:location2) { create(:location, order: order2) }
  let!(:order_line21) { create(:order_line, :random, order: order2, location: location2, menu_item: menu_item1, supplier_profile: supplier, quantity: 10) }
  let!(:order_line22) { create(:order_line, :random, order: order2, location: location2, menu_item: menu_item2, supplier_profile: supplier, quantity: 10) }

  let(:orders) { [order1, order2] }

  before do
    # make the orders subsequent recurrents of each other
    orders.each_with_index do |order, oidx|
      attributes = {
        recurrent_id: order1.id,
        template_id: order1.id,
        renewed_from_id: oidx == 0 ? nil : orders[oidx - 1].try(:id),
        renewed_to_id: orders[oidx + 1].try(:id)
      }
      order.update_columns(attributes)
    end
    orders.each(&:reload)
  end

  it 'removes order lines from subsequent order if removed from template' do
    [order_line11, order_line12].each(&:destroy)
    order1.update_column(:status, 'amended')

    Orders::Recurring::SyncOrderLines.new(order: order2).call
    orders.each(&:reload)

    expect(order2.order_lines).to be_blank
  end

  it 'updates order lines in subsequent orders if updated in template' do
    order_line11.destroy
    order_line12.update_column(:quantity, 15)
    order1.update_column(:status, 'amended')

    Orders::Recurring::SyncOrderLines.new(order: order2).call
    orders.each(&:reload)

    # refreshes order lines from susequent order
    order2_lines = order2.order_lines
    expect(order2_lines).to_not include(order_line21, order_line22) # new order lines
    expect{ order_line21.reload }.to raise_error(ActiveRecord::RecordNotFound) # destroys order line
    expect{ order_line22.reload }.to raise_error(ActiveRecord::RecordNotFound) # destroys order line

    expect(order2_lines.map(&:menu_item).uniq).to match_array([menu_item2])
    expect(order2_lines.map(&:quantity).uniq).to match_array([15])
    expect(order2_lines.map(&:status).uniq).to match_array(['amended'])
  end

  context 'location update' do
    before do
      location1.update_column(:details, 'new-location-details')
      order1.update_column(:status, 'amended')
    end

    it 'updates the location information if updated in template' do
      Orders::Recurring::SyncOrderLines.new(order: order2).call
      orders.each(&:reload)

      # refreshes order line locations within susequent order
      order2_locations = order2.locations
      expect(order2_locations).to_not include(location1, location2) # creates new location
      expect(order2_locations.map(&:details).uniq).to match_array(['new-location-details'])

      expect{ location1.reload }.to_not raise_error(ActiveRecord::RecordNotFound) # template order location not destroyed
      expect{ location2.reload }.to raise_error(ActiveRecord::RecordNotFound) # destroys location
    end

    it 'doesn\'t destroy order lines location if it belongs to the template order' do
      [order_line21, order_line22].each{|order_line| order_line.update_column(:location_id, location1.id) }

      Orders::Recurring::SyncOrderLines.new(order: order2).call
      orders.each(&:reload)

      # refreshes order line locations within susequent order
      order2_locations = order2.locations
      expect(order2_locations).to_not include(location1, location2) # creates new location
      expect(order2_locations.map(&:details).uniq).to match_array(['new-location-details'])

      expect{ location1.reload }.to_not raise_error(ActiveRecord::RecordNotFound) # template order location not destroyed
      expect{ location2.reload }.to_not raise_error(ActiveRecord::RecordNotFound) # does not belong to either order
    end
  end

  context 'with order line with a mismatched menu item (now containing serving sizes)' do
    # menu_item1 now has serving size instead of being a menu item only item
    let!(:serving_size11) { create(:serving_size, :random, menu_item: menu_item1, is_default: false) }
    let!(:serving_size12) { create(:serving_size, :random, menu_item: menu_item1, is_default: true) }

    before do
      menu_item1.update_column(:price, nil)
    end

    it 'updates order lines in subsequent orders with the default (serving size) setup' do
      Orders::Recurring::SyncOrderLines.new(order: order2).call
      order2.reload

      menu_item1_order_line = order2.order_lines.where(menu_item: menu_item1).first
      expect(menu_item1_order_line.serving_size).to eq(serving_size12)
    end
  end

end
