require 'rails_helper'

RSpec.describe Orders::Recurring::SaveAsOneOff, type: :service, orders: true do

  let(:customer) { create(:customer_profile, :random) }

  let(:delivery_at) { Time.zone.now + 1.day }

  let(:order1) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', delivery_at: delivery_at) }
  let(:order2) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', delivery_at: delivery_at + 1.week) }
  let(:order3) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', delivery_at: delivery_at + 2.weeks) }
  let(:order4) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', delivery_at: delivery_at + 3.weeks) }
  let(:order5) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', delivery_at: delivery_at + 4.weeks) }

  let(:orders) { [order1, order2, order3, order4, order5] }

  before do
    # make the orders subsequent recurrents of each other
    orders.each_with_index do |order, oidx|
      attributes = {
        template_id: order1.id,
        renewed_from_id: oidx == 0 ? nil : orders[oidx - 1].try(:id),
        renewed_to_id: orders[oidx + 1].try(:id)
      }
      order.update_columns(attributes)
    end
  end

  it 'updates the order as one-off and doesn\'t change anything if order is not parent template' do
    order3.update_column(:status, 'amended')
    Orders::Recurring::SaveAsOneOff.new(order: order3).call
    orders.each(&:reload)

    orders.each do |order|
      expect(order.template_id).to eq(order1.template_id)
    end

    expect(order3.order_type).to eq('one-off')
    expect(order3.status).to eq('amended')

    [order1, order2, order4, order5].each do |order|
      expect(order.order_type).to eq('recurrent')
      expect(order.status).to eq('confirmed')
    end
  end

  it 'updates the order as one-off and sets the next order as parent template' do
    order1.update_column(:status, 'amended')
    Orders::Recurring::SaveAsOneOff.new(order: order1).call
    orders.each(&:reload)

    expect(order1.order_type).to eq('one-off')
    expect(order1.status).to eq('amended')

    [order2, order3, order4, order5].each do |order|
      expect(order.order_type).to eq('recurrent')
      expect(order.template_id).to eq(order2.id)
      expect(order.status).to eq('confirmed')
    end
  end
end
