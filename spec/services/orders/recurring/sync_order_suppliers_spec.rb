require 'rails_helper'

RSpec.describe Orders::Recurring::SyncOrderSuppliers, type: :service, orders: true, order_lines: true do

  let(:supplier1) { create(:supplier_profile, :random) }
  let(:supplier2) { create(:supplier_profile, :random) }

  let(:base_delivery_at) { Time.zone.now + 1.day }
  let(:order1) { create(:order, :confirmed, name: 'Order 1', order_type: 'recurrent', pattern: '1.week', delivery_at: base_delivery_at) }
  let(:order2) { create(:order, :confirmed, name: 'Order 2', order_type: 'recurrent', pattern: '1.week', delivery_at: base_delivery_at + 1.week) }

  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
  let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier2) }

  let!(:order_supplier11) { create(:order_supplier, :random, order: order1, supplier_profile: supplier1) }
  let!(:order_supplier12) { create(:order_supplier, :random, order: order1, supplier_profile: supplier2) }

  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

  let!(:order_supplier21) { create(:order_supplier, :random, order: order2, supplier_profile: supplier1) }
  let!(:order_supplier22) { create(:order_supplier, :random, order: order2, supplier_profile: supplier2) }

  let(:orders) { [order1, order2] }

  before do
    # make the orders subsequent recurrents of each other
    orders.each_with_index do |order, oidx|
      attributes = {
        recurrent_id: order1.id,
        template_id: order1.id,
        renewed_from_id: oidx == 0 ? nil : orders[oidx - 1].try(:id),
        renewed_to_id: orders[oidx + 1].try(:id)
      }
      order.update_columns(attributes)
    end
  end

  it 'updates order suppliers in subsequent orders if updated in template' do
    order_supplier11.update_column(:delivery_fee_override, rand(2.03..20.9))
    order_supplier12.update_column(:delivery_fee_override, rand(2.03..20.9))

    Orders::Recurring::SyncOrderSuppliers.new(order: order2, template_order: order1).call
    orders.each(&:reload)

    # refreshes order suppliers within susequent order along with delivery overrides
    order2_supplier1_order_supplier = order2.order_suppliers.where(supplier_profile: order_supplier11.supplier_profile).first
    expect(order2_supplier1_order_supplier).to be_present
    expect(order2_supplier1_order_supplier.delivery_fee_override).to eq(order_supplier11.delivery_fee_override)

    order2_supplier2_order_supplier = order2.order_suppliers.where(supplier_profile: order_supplier12.supplier_profile).first
    expect(order2_supplier2_order_supplier).to be_present
    expect(order2_supplier2_order_supplier.delivery_fee_override).to eq(order_supplier12.delivery_fee_override)
  end

  it 'removes any order supliers not having an orderline in template order' do
    [order_line12, order_line22].each(&:destroy)

    Orders::Recurring::SyncOrderSuppliers.new(order: order2, template_order: order1).call
    orders.each(&:reload)

    order2_order_suppliers = order2.order_suppliers
    expect(order2_order_suppliers.map(&:supplier_profile)).to include(supplier1)
    expect(order2_order_suppliers.map(&:supplier_profile)).to_not include(supplier2)
  end

end