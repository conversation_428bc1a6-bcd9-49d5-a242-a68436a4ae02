require 'rails_helper'

RSpec.describe Orders::Recurring::UpdateSubsequentOrders, type: :service, orders: true do

  let(:customer) { create(:customer_profile, :random) }

  let(:delivery_at) { Time.zone.now + 1.day }

  let(:order1) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', pattern: '1.week', delivery_at: delivery_at) }
  let(:order2) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', pattern: '1.week', delivery_at: delivery_at + 1.week) }
  let(:order3) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', pattern: '1.week', delivery_at: delivery_at + 2.weeks) }
  let(:order4) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', pattern: '1.week', delivery_at: delivery_at + 3.weeks) }
  let(:order5) { create(:order, :confirmed, customer_profile: customer, order_type: 'recurrent', pattern: '1.week', delivery_at: delivery_at + 4.weeks) }

  let(:orders) { [order1, order2, order3, order4, order5] }

  before do
    # make the orders subsequent recurrents of each other
    orders.each_with_index do |order, oidx|
      attributes = {
        recurrent_id: order1.id,
        template_id: order1.id,
        renewed_from_id: oidx == 0 ? nil : orders[oidx - 1].try(:id),
        renewed_to_id: orders[oidx + 1].try(:id)
      }
      order.update_columns(attributes)
    end

    # mock document generator
    document_generator = delayed_document_generator = double(Documents::Generate::SupplierOrderDetails)
    allow(Documents::Generate::SupplierOrderDetails).to receive(:new).and_return(document_generator)
    allow(document_generator).to receive(:delay).and_return(delayed_document_generator)
    allow(delayed_document_generator).to receive(:call).and_return(true)
  end

  it 'updates attributes of subsequent orders as passed' do # tested in Orders::Recurring::UpdateSubsequentOrders spec
    order_params = { name: Faker::Name.name }
    Orders::Recurring::UpdateSubsequentOrders.new(order: order3, order_params: order_params).call
    orders.each(&:reload)

    # previous orders don't change
    [order1, order2].each do |order|
      expect(order.name).to_not eq(order_params[:name])
      expect(order.status).to_not eq('amended')
    end

    [order4, order5].each do |order|
      expect(order.name).to eq(order_params[:name])
      expect(order.status).to eq('amended')
    end
  end

  it 'updates delivery_at of subsequent orders as calculated using passed in delivery_difference' do # tested in Orders::Recurring::UpdateSubsequentOrders spec
    delivery_difference = Time.zone.now + 13.days - order3.delivery_at
    order4_orginal_delivery_at = order4.delivery_at
    order5_original_delivery_at = order5.delivery_at

    order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 13.days }
    Orders::Recurring::UpdateSubsequentOrders.new(order: order3, order_params: order_params, delivery_difference: delivery_difference).call
    orders.each(&:reload)

    expect(order4.delivery_at.to_s).to eq((order4_orginal_delivery_at + delivery_difference).to_s)
    expect(order5.delivery_at.to_s).to eq((order5_original_delivery_at + delivery_difference).to_s)
  end

  it 'updates the template id of subsequent orders only' do
    order3.update_column(:status, 'amended')
    Orders::Recurring::UpdateSubsequentOrders.new(order: order3).call
    orders.each(&:reload)

    # previous orders don't change
    [order1, order2].each do |order|
      expect(order.template_id).to eq(order1.id)
      expect(order.status).to eq('confirmed')
    end

    expect(order3.status).to eq('amended')

    [order3, order4, order5].each do |order|
      expect(order.template_id).to eq(order3.id)
    end
  end

  context 'with order lines', order_lines: true do # also tested in Orders::Recurring::SyncOrderLines spec
    let(:supplier) { create(:supplier_profile, :random, :with_flags) }

    let(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier, price: 10) }
    let(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier, price: 10) }

    let!(:order_line11) { create(:order_line, :random, order: order1, menu_item: menu_item1, supplier_profile: supplier, quantity: 10) }
    let!(:order_line12) { create(:order_line, :random, order: order1, menu_item: menu_item2, supplier_profile: supplier, quantity: 10) }

    let!(:order_line21) { create(:order_line, :random, order: order2, menu_item: menu_item1, supplier_profile: supplier, quantity: 10) }
    let!(:order_line22) { create(:order_line, :random, order: order2, menu_item: menu_item2, supplier_profile: supplier, quantity: 10) }

    let!(:order_line31) { create(:order_line, :random, order: order3, menu_item: menu_item1, supplier_profile: supplier, quantity: 10) }
    let!(:order_line32) { create(:order_line, :random, order: order3, menu_item: menu_item2, supplier_profile: supplier, quantity: 10) }

    let!(:order_line41) { create(:order_line, :random, order: order4, menu_item: menu_item1, supplier_profile: supplier, quantity: 10) }
    let!(:order_line42) { create(:order_line, :random, order: order4, menu_item: menu_item2, supplier_profile: supplier, quantity: 10) }

    let!(:order_line51) { create(:order_line, :random, order: order5, menu_item: menu_item1, supplier_profile: supplier, quantity: 10) }
    let!(:order_line52) { create(:order_line, :random, order: order5, menu_item: menu_item2, supplier_profile: supplier, quantity: 10) }

    let!(:updated_totals) { orders.each{|order| Orders::CalculateCustomerTotals.new(order: order.reload, save_totals: true).call }}

    it 'removes order lines only from subsequent orders if removed from current' do
      [order_line31, order_line32].each(&:destroy)
      order3.update_column(:status, 'amended')

      Orders::Recurring::UpdateSubsequentOrders.new(order: order3.reload).call
      orders.each(&:reload)

      expect(order3.order_lines).to be_blank

      # removes order lines from susequent order
      expect(order4.order_lines).to be_blank
      expect(order5.order_lines).to be_blank

      # previous orders stay the same
      expect(order1.order_lines).to include(order_line11, order_line12)
      expect(order2.order_lines).to include(order_line21, order_line22)
    end

    it 'updates order lines in all subsequent orders' do
      order_line31.destroy
      order_line32.update_column(:quantity, 15)
      order3.update_column(:status, 'amended')

      Orders::Recurring::UpdateSubsequentOrders.new(order: order3.reload).call
      orders.each(&:reload)

      # removes order lines from susequent order
      order4_lines = order4.order_lines
      expect(order4_lines).to_not include(order_line41, order_line42)
      expect(order4_lines.map(&:menu_item).uniq).to match_array([menu_item2])
      expect(order4_lines.map(&:quantity).uniq).to match_array([15])

      order5_lines = order5.order_lines
      expect(order5_lines).to_not include(order_line51, order_line52)
      expect(order5_lines.map(&:menu_item).uniq).to match_array([menu_item2])
      expect(order5_lines.map(&:quantity).uniq).to match_array([15])

      # updated order totals
      expect(order4.customer_total).to eq(165)
      expect(order5.customer_total).to eq(165)

      # previous orders stay the same
      expect(order1.order_lines).to include(order_line11, order_line12)
      expect(order2.order_lines).to include(order_line21, order_line22)
    end

    context 'with supplier delivery_fee_overrides' do
      let!(:delivery_override_fee) { [nil, rand(11.9..20.2)].sample}
      let!(:order_supplier1) { create(:order_supplier, :random, order: order1, supplier_profile: supplier, delivery_fee_override: [nil, rand(2.3..10.9)].sample) }
      let!(:order_supplier2) { create(:order_supplier, :random, order: order2, supplier_profile: supplier, delivery_fee_override: [nil, rand(2.3..10.9)].sample) }
      let!(:order_supplier3) { create(:order_supplier, :random, order: order3, supplier_profile: supplier, delivery_fee_override: delivery_override_fee) }
      let!(:order_supplier4) { create(:order_supplier, :random, order: order4, supplier_profile: supplier, delivery_fee_override: [nil, rand(2.3..10.9)].sample) }
      let!(:order_supplier5) { create(:order_supplier, :random, order: order5, supplier_profile: supplier, delivery_fee_override: [nil, rand(2.3..10.9)].sample) }

      it 'updates order supplier delivery fee overrides in all subsequent orders' do
        new_order_total = (200.0 + (delivery_override_fee || 0.0)) * 1.1
        Orders::Recurring::UpdateSubsequentOrders.new(order: order3.reload).call
        orders.each(&:reload)

        expect(order4.order_suppliers).to be_present
        expect(order4.order_suppliers.size).to eq(1)

        order4_order_supplier = order4.order_suppliers.first
        expect(order4_order_supplier.id).to_not eq(order_supplier3.id) # not template order supplier
        expect(order4_order_supplier.id).to_not eq(order_supplier4.id) # not it's orignial order supplier
        expect(order4_order_supplier.supplier_profile).to eq(supplier)
        expect(order4_order_supplier.delivery_fee_override).to eq(order_supplier3.delivery_fee_override)
        # updated order totals
        expect(order4.customer_total.round(2).to_s).to eq(new_order_total.round(2).to_s)

        expect(order5.order_suppliers).to be_present
        expect(order5.order_suppliers.size).to eq(1)

        order5_order_supplier = order5.order_suppliers.first
        expect(order5_order_supplier.id).to_not eq(order_supplier3.id) # not template order supplier
        expect(order5_order_supplier.id).to_not eq(order_supplier5.id) # not it's orignial order supplier
        expect(order5_order_supplier.supplier_profile).to eq(supplier)
        expect(order5_order_supplier.delivery_fee_override).to eq(order_supplier3.delivery_fee_override)

        # updated order totals
        expect(order5.customer_total.round(2).to_s).to eq(new_order_total.round(2).to_s)

        # previous orders order suppliers stay the same
        expect(order1.order_suppliers).to include(order_supplier1)
        expect(order2.order_suppliers).to include(order_supplier2)
      end
    end # with delivery fee overrides

    context 'supplier documents' do
      it 'makes a request to generate the order details supplier documents (along with delivery docket) for all the subsequent orders' do
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: order4, supplier: supplier)
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: order4, supplier: supplier, variation: 'delivery_docket')
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: order5, supplier: supplier)
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: order5, supplier: supplier, variation: 'delivery_docket')

        Orders::Recurring::UpdateSubsequentOrders.new(order: order3.reload).call
      end

      it 'makes a request to generate the order details JSON supplier documents for suppliers that use flex catering' do
        supplier.supplier_flags.update_column(:uses_flex_catering, true)
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: order4, supplier: supplier, variation: 'json')
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: order5, supplier: supplier, variation: 'json')

        Orders::Recurring::UpdateSubsequentOrders.new(order: order3.reload).call
      end
    end # supplier documents
  end # order line updates
end
