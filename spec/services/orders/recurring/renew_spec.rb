require 'rails_helper'

RSpec.describe Orders::Recurring::Renew, type: :service, orders: true do

  let!(:pay_on_account_card) { create(:credit_card, :on_account_card, name: 'P<PERSON>') }

  let!(:company) { create(:company, :random, can_pay_on_account: true) }
  let!(:customer) { create(:customer_profile, :random, company: company) }
  let(:order) { create(:order, :confirmed, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: %w[mon wed fri] }, customer_total: 0, customer_surcharge: 0, customer_profile: customer, whodunnit_id: rand(2000)) }

  before do
    order.update_columns(
      recurrent_id: order.id,
      template_id: order.id
    )

    # mock promotions syncer
    promotions_syncer = double(Promotions::SyncWithOrder)
    allow(Promotions::SyncWithOrder).to receive(:new).and_return(promotions_syncer)
    allow(promotions_syncer).to receive(:call).and_return(true)

    # mock document generator
    document_generator = double(Documents::Generate::SupplierOrderDetails)
    allow(Documents::Generate::SupplierOrderDetails).to receive(:new).and_return(document_generator)
    allow(document_generator).to receive(:call).and_return(true)
  end

  context 'RenewalException' do
    it 'cannot renew an order that does not exist' do
      expect{ Orders::Recurring::Renew.new(order: nil).call }.to raise_error(Orders::Recurring::Renew::RenewalException, 'Cannot renew a non-existant order')
    end

    it 'cannot renew an order if recurrent order id does not exist' do
      order.update_column(:recurrent_id, nil)
      expect{ Orders::Recurring::Renew.new(order: order).call }.to raise_error(Orders::Recurring::Renew::RenewalException, 'Cannot renew a one-off order (recurrent_id: nil)')
    end

    it 'cannot renew an order if template order id does not exist' do
      order.update_column(:template_id, nil)
      expect{ Orders::Recurring::Renew.new(order: order).call }.to raise_error(Orders::Recurring::Renew::RenewalException, 'Cannot renew an order without a template (template_id: nil)')
    end

    it 'cannot renew an already renewed order' do
      renewed_to_id = rand(10)
      order.update_column(:renewed_to_id, renewed_to_id)
      expect{ Orders::Recurring::Renew.new(order: order).call }.to raise_error(Orders::Recurring::Renew::RenewalException, "This order was already renewed (renewed_to_id: #{renewed_to_id})")
    end

    it 'cannot renew an unconfirmed order' do
      order_status = %w[draft new].sample
      order.update_column(:status, order_status)
      expect{ Orders::Recurring::Renew.new(order: order).call }.to raise_error(Orders::Recurring::Renew::RenewalException, "Cannot renew unconfirmed order (status: '#{order_status}')")
    end

    it 'cannot renew a permanently cancelled recurring order' do
      order.update_columns(order_type: 'recurrent', status: 'cancelled')
      expect{ Orders::Recurring::Renew.new(order: order).call }.to raise_error(Orders::Recurring::Renew::RenewalException, "Cannot renew order with template order being cancelled permanently (order_id: '#{order.id}', template_order_id: '#{order.id}')")
    end

    it 'cannot renew an order without a pattern (1.week/2.weeks/1.month/4.weeks)' do
      order.update_column(:pattern, nil)
      expect{ Orders::Recurring::Renew.new(order: order).call }.to raise_error(Orders::Recurring::Renew::RenewalException, 'Cannot renew an order with no pattern (pattern: nil)')
    end
  end

  it 'creates a new recurring order' do
    renewer = Orders::Recurring::Renew.new(order: order).call
    expect(renewer).to be_success

    renewed_order = renewer.renewed_order
    expect(renewed_order.id).to_not eq(order.id)

    expect(renewed_order.order_type).to eq('recurrent')
    expect(renewed_order.name).to eq(order.name)
    expect(renewed_order.template_id).to eq(order.id)
    expect(renewed_order.renewed_from_id).to eq(order.id)
    expect(renewed_order.renewed_to_id).to be_blank
    expect(renewed_order.uuid).to be_present
    expect(renewed_order.uuid).to_not eq(order.uuid)
  end

  it 'create the new recurring order and copies over details' do
    renewer = Orders::Recurring::Renew.new(order: order).call
    expect(renewer).to be_success

    renewed_order = renewer.renewed_order
    expect(renewed_order.id).to_not eq(order.id)
    expect(renewed_order.customer_profile).to eq(order.customer_profile)
    expect(renewed_order.major_category).to eq(order.major_category)
    expect(renewed_order.number_of_people).to eq(order.number_of_people)

    expect(renewed_order.delivery_address).to eq(order.delivery_address)
    expect(renewed_order.delivery_suburb).to eq(order.delivery_suburb)
    expect(renewed_order.delivery_instruction).to eq(order.delivery_instruction)

    expect(renewed_order.contact_name).to eq(order.contact_name)
    expect(renewed_order.contact_email).to eq(order.contact_email)
    expect(renewed_order.phone).to eq(order.phone)
    expect(renewed_order.whodunnit_id).to eq(order.whodunnit_id)
  end

  context 'with meal plan details', meal_plans: true do
    let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer) }
    before do
      order.update_column(:meal_plan_id, meal_plan.id)
    end

    it 'create the new recurring order and copies over meal plan details' do
      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.id).to_not eq(order.id)
      expect(renewed_order.meal_plan).to eq(order.meal_plan)
      expect(renewed_order.meal_plan).to eq(meal_plan)
    end
  end

  context 'with attached pantry manager', pantry_managers: true do
    let!(:pantry_manager) { create(:customer_profile, :random) }
    before do
      order.update_column(:pantry_manager_id, pantry_manager.id)
    end

    it 'create the new recurring order and copies over pantry manager' do
      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.id).to_not eq(order.id)
      expect(renewed_order.pantry_manager).to eq(order.pantry_manager)
      expect(renewed_order.pantry_manager).to eq(pantry_manager)
    end
  end

  context 'with purchase orders', gst_split_invoicing: true do
    let!(:gst_purchase_order) { create(:customer_purchase_order, :random, customer_profile: customer) }
    let!(:gst_free_purchase_order) { create(:customer_purchase_order, :random, customer_profile: customer) }

    before do
      order.update_columns(cpo_id: gst_purchase_order.id, gst_free_cpo_id: gst_free_purchase_order.id)
    end

    it 'create the new recurring order and copies over the purchase orders' do
      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.customer_purchase_order).to eq(gst_purchase_order)
      expect(renewed_order.gst_free_customer_purchase_order).to eq(gst_free_purchase_order)
    end
  end

  it 'creates the new recurring order and resets some details' do
    renewer = Orders::Recurring::Renew.new(order: order).call
    expect(renewer).to be_success

    renewed_order = renewer.renewed_order
    expect(renewed_order.id).to_not eq(order.id)
    expect(renewed_order.status).to eq('confirmed')
    expect(renewed_order.payment_status).to eq('unpaid')
    expect(renewed_order.invoice_id).to be_blank
    expect(renewed_order.suppliers_notified_at).to_not eq(order.suppliers_notified_at)
  end

  it 'creates a new recurring order with the right delivery date' do
    renewer = Orders::Recurring::Renew.new(order: order).call
    expect(renewer).to be_success

    renewed_order = renewer.renewed_order
    expect(renewed_order.old_delivery_at).to be_blank
    expect(renewed_order.delivery_at).to eq(order.delivery_at + 1.week) # pattern of template order (order)
  end

  it 'set the orginal order to be renewed to the new order' do
    renewer = Orders::Recurring::Renew.new(order: order).call
    expect(renewer).to be_success

    renewed_order = renewer.renewed_order
    expect(order.reload.renewed_to_id).to eq(renewed_order.id)
  end

   it 'request the promotion to be synced for the new order', promotions: true do
    expect(Promotions::SyncWithOrder).to receive(:new).with(order: anything, customer: customer) # order is the newly created recurring order

    renewer = Orders::Recurring::Renew.new(order: order).call
    expect(renewer).to be_success
   end

  context 'a paused order' do
    before do
      order.update_column(:status, 'paused')
    end

    it 'creates a new recurring order' do
      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.id).to_not eq(order.id)
      expect(renewed_order.order_type).to eq('recurrent')
      expect(renewed_order.name).to eq(order.name)
      expect(renewed_order.template_id).to eq(order.id)
      expect(renewed_order.renewed_from_id).to eq(order.id)
      expect(renewed_order.renewed_to_id).to be_blank
    end

    it 'keeps the status of the new order as paused' do
      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.status).to eq('paused')
    end
  end

  context 'with order lines', order_lines: true do
    let(:supplier) { create(:supplier_profile, :random, :with_flags) }
    let(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier, price: 10) }
    let(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier, price: 10) }

    let(:location1) { create(:location, :random, order: order) }
    let!(:order_line11) { create(:order_line, :random, order: order, supplier_profile: supplier, location: location1, menu_item: menu_item1, quantity: 20) }
    let!(:order_line12) { create(:order_line, :random, order: order, supplier_profile: supplier, location: location1, menu_item: menu_item2, quantity: 10) }

    let(:location2) { create(:location, :random, order: order) }
    let!(:order_line21) { create(:order_line, :random, order: order, supplier_profile: supplier, location: location2, menu_item: menu_item1, quantity: 12) }

    it 'duplicates the order lines and location within the order' do
      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.id).to_not eq(order.id)
      renewed_order_lines = renewed_order.order_lines
      expect(renewed_order_lines).to be_present
      expect(renewed_order_lines.size).to eq(3)

      expect(renewed_order_lines.map(&:id)).to_not include(order_line11, order_line12, order_line21) # new order lines
      expect(renewed_order_lines.map(&:order).uniq).to match_array([renewed_order]) # order line in the new order
      expect(renewed_order_lines.map(&:menu_item)).to include(menu_item1, menu_item2)
      expect(renewed_order_lines.map(&:quantity)).to include(10, 12, 20)

      expect(renewed_order_lines.map(&:sent_as_rgi_to_xero).uniq.compact).to be_blank
      expect(renewed_order_lines.map(&:status).uniq).to match_array(['accepted'])

      renewed_order_locations = renewed_order_lines.map(&:location)
      expect(renewed_order_locations).to_not include(location1, location2)
      expect(renewed_order_locations.map(&:order)).to include(renewed_order)
      expect(renewed_order_lines.map(&:location)).to_not include(location1, location2) # order lines in new locations
    end

    it 'recalculates and saves the total in the renewed order' do
      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order

      expect(renewed_order.id).to_not eq(order.id)
      expect(renewed_order.customer_subtotal).to eq(420.0)
      expect(renewed_order.customer_gst).to eq(42.0)
      expect(renewed_order.customer_delivery).to eq(0.0)
      expect(renewed_order.customer_total).to eq(462.0)

      expect(renewed_order.status).to eq('confirmed')
    end

    context 'order lines with serving sizes' do
      let!(:serving_size21) { create(:serving_size, :random, menu_item: menu_item2, price: 8) }
      let!(:serving_size22) { create(:serving_size, :random, menu_item: menu_item2, price: 9, is_default: true) }

      before do
        menu_item2.update_columns(price: nil)
      end

      it 'stores the serving sizes within the order line' do
        order_line12.update_column(:serving_size_id, serving_size21.id)
        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success

        renewed_order = renewer.renewed_order
        renewed_order_lines = renewed_order.order_lines
        menu_item2_order_lines = renewed_order_lines.select{|order_line| order_line.menu_item == menu_item2 }
        expect(menu_item2_order_lines.map(&:serving_size)).to include(serving_size21)
      end

      it 'stores the default serving size if the existing order line does not contain the serving size but the menu item does' do
        order_line12.update_column(:serving_size_id, nil)

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success

        renewed_order = renewer.renewed_order
        renewed_order_lines = renewed_order.order_lines
        menu_item2_order_lines = renewed_order_lines.select{|order_line| order_line.menu_item == menu_item2 }
        expect(menu_item2_order_lines.map(&:serving_size)).to include(serving_size22)
      end

      it 'stores the first non-default serving size if the existing order line does not contain the serving size but the menu item does' do
        order_line12.update_column(:serving_size_id, nil)
        serving_size22.update_column(:is_default, false)

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success

        renewed_order = renewer.renewed_order
        renewed_order_lines = renewed_order.order_lines
        menu_item2_order_lines = renewed_order_lines.select{|order_line| order_line.menu_item == menu_item2 }
        expect(menu_item2_order_lines.map(&:serving_size)).to include(serving_size21)
      end

      it 'stores the first non-default serving size if the existing order line does not contain the serving size but the menu item does' do
        order_line12.update_column(:serving_size_id, nil)
        serving_size22.update_column(:is_default, false)

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success

        renewed_order = renewer.renewed_order
        renewed_order_lines = renewed_order.order_lines
        menu_item2_order_lines = renewed_order_lines.select{|order_line| order_line.menu_item == menu_item2 }
        expect(menu_item2_order_lines.map(&:serving_size)).to include(serving_size21)
      end

      it 'stores the order line without a serving size if all serving sizes for the menu item are archived' do
        order_line12.update_column(:serving_size_id, serving_size21.id)
        serving_size21.update_column(:archived_at, Time.zone.now)
        serving_size22.update_column(:archived_at, Time.zone.now)
        menu_item2.update_column(:price, rand(20))

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success

        renewed_order = renewer.renewed_order
        renewed_order_lines = renewed_order.order_lines
        menu_item2_order_lines = renewed_order_lines.select{|order_line| order_line.menu_item == menu_item2 }
        expect(menu_item2_order_lines.map(&:serving_size).compact).to be_blank
      end
    end

    context 'with order supplier delivery_fee_overrides' do
      let!(:order_supplier) { create(:order_supplier, :random, order: order, supplier_profile: supplier, delivery_fee_override: rand(2.3..10.9)) }

      it 'copies over the overriden order supplier to the renewed order' do
        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success

        renewed_order = renewer.renewed_order

        renewed_order_supplier = renewed_order.order_suppliers.first
        expect(renewed_order_supplier).to be_present
        expect(renewed_order_supplier.id).to_not eq(order_supplier.id)
        expect(renewed_order_supplier.order).to_not eq(order.id) # renewed_order.id
        expect(renewed_order_supplier.supplier_profile).to eq(supplier)
        expect(renewed_order_supplier.delivery_fee_override).to eq(order_supplier.delivery_fee_override)
      end
    end

    context 'order totals' do
      before do
        customer_total_calculator = double(Orders::CalculateCustomerTotals)
        allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(customer_total_calculator)
        allow(customer_total_calculator).to receive(:call).and_return(true)

        supplier_total_calculator = double(Orders::CalculateSupplierTotals)
        allow(Orders::CalculateSupplierTotals).to receive(:new).and_return(supplier_total_calculator)
        allow(supplier_total_calculator).to receive(:call).and_return(true)
      end

      it 'calculates and saves customer totals' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: anything, save_totals: true)

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success
      end

      it 'calculates and saves supplier(s) totals' do
        expect(Orders::CalculateSupplierTotals).to receive(:new).with(order: anything, supplier: supplier, save_totals: true)

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success
      end
    end # order totals

    context 'with dear suppliers', dear: true do
      let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier) }

      before do
        # mock Dear Order Syncer
        order_syncer = delayed_order_syncer = double(Dear::SyncOrder)
        allow(Dear::SyncOrder).to receive(:new).and_return(order_syncer)
        allow(order_syncer).to receive(:delay).and_return(delayed_order_syncer)
        allow(delayed_order_syncer).to receive(:call).and_return(true)
      end

      it 'syncs the order with the dear supplier' do
        expect(Dear::SyncOrder).to receive(:new).with(order: anything, supplier: supplier) # order is the new order

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success
      end

      it 'does not sync order with renewed (and original) order is paused' do
        order.update_column(:status, 'paused')
        expect(Dear::SyncOrder).to_not receive(:new)

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success
      end

      it 'does not sync order with Dear if an active dear account isn\'t present' do
        if [true, false].sample
          dear_account.update_column(:active, false)
        else
          dear_account.destroy
        end
        expect(Dear::SyncOrder).to_not receive(:new)

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success
      end
    end # Dear suppliers

    context 'supplier documents' do
      it 'makes a request to generate the v1 order details supplier documents (along with delivery docket)' do
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: anything, supplier: supplier, version_override: 1) # order is renewed order
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: anything, supplier: supplier, variation: 'delivery_docket', version_override: 1) # order is renewed order

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success
      end

      it 'makes a request to generate the v1 order details JSON supplier documents for suppliers that use flex catering' do
        supplier.supplier_flags.update_column(:uses_flex_catering, true)
        expect(Documents::Generate::SupplierOrderDetails).to receive(:new).with(order: anything, supplier: supplier, variation: 'json', version_override: 1) # order is renewed order

        renewer = Orders::Recurring::Renew.new(order: order).call
        expect(renewer).to be_success
      end
    end # supplier documents
  end # with order lines

  context 'with an attached credit card' do
    let!(:customer_credit_card) { create(:credit_card, :random, name: 'CC') }

    before do
      customer.credit_cards << customer_credit_card # attach card to customer
    end
    it 'creates the new recurring order and resets to be pay on account for attached credit card if the customer can pay on account' do
      order.update_column(:credit_card_id, customer_credit_card.id)

      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.id).to_not eq(order.id)
      expect(renewed_order.credit_card_id).to eq(pay_on_account_card.id)
    end

    it 'creates the new recurring order and resets to be pay on account for a attached nominated credit card' do
      customer_credit_card.update_column(:auto_pay_invoice, true)
      order.update_column(:credit_card_id, customer_credit_card.id)

      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.id).to_not eq(order.id)
      expect(renewed_order.credit_card_id).to eq(pay_on_account_card.id)
    end

    it 'creates the new recurring order with the same pay on account card' do
      order.update_column(:credit_card_id, pay_on_account_card.id)

      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.id).to_not eq(order.id)
      expect(renewed_order.credit_card_id).to eq(pay_on_account_card.id)
    end

    it 'creates the new recurring order with the attached customer credit card if the customer cannot pay on account' do
      company.update_column(:can_pay_on_account, false)
      order.update_column(:credit_card_id, customer_credit_card.id)

      renewer = Orders::Recurring::Renew.new(order: order).call
      expect(renewer).to be_success

      renewed_order = renewer.renewed_order
      expect(renewed_order.id).to_not eq(order.id)
      expect(renewed_order.credit_card_id).to eq(customer_credit_card.id)
    end
  end

end
