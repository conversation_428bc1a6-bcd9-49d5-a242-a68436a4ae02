require 'rails_helper'

RSpec.describe Orders::SetupForSupplierMenu, type: :service, orders: true, suppliers: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:suburb) { create(:suburb, :random) }

  let!(:order) { create(:order, :draft) }
  let!(:session) { { order_id: order.id } }

  it 'resets the delivery suburb of a draft order in the session' do
    order_setup = Orders::SetupForSupplierMenu.new(session_order: order, suburb: suburb).call

    expect(order_setup).to be_success
    order.reload
    expect(order.delivery_suburb).to eq(suburb)
  end

  it 'does not reset delivery suburb for a non-draft order' do
    order_suburb = create(:suburb, :random)
    order.update_columns({ status: 'new', delivery_suburb_id: order_suburb.id })

    order_setup = Orders::SetupForSupplierMenu.new(session_order: order, suburb: suburb).call

    expect(order_setup).to be_success
    order.reload
    expect(order.delivery_suburb).to_not eq(suburb)
  end

  context 'as a woolworths order', woolworths: true do
    let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }

    it 'does not reset delivery suburb for woolworths order with an existing suburb' do
      order_suburb = create(:suburb, :random)
      order.update_column(:delivery_suburb_id, order_suburb.id)

      order_setup = Orders::SetupForSupplierMenu.new(session_order: order, suburb: suburb).call

      expect(order_setup).to be_success
      order.reload
      expect(order.delivery_suburb).to_not eq(suburb)
    end

    it 'resets delivery suburb for woolworths order without an existing suburb' do
      order.update_column(:delivery_suburb_id, nil)

      order_setup = Orders::SetupForSupplierMenu.new(session_order: order, suburb: suburb).call

      expect(order_setup).to be_success
      order.reload
      expect(order.delivery_suburb).to eq(suburb)
    end
  end

  it 'does not return any recurrent orders' do
    order_setup = Orders::SetupForSupplierMenu.new(session_order: order, suburb: suburb).call

    expect(order_setup).to be_success
    expect(order_setup.recurrent_orders).to be_blank
  end

  context 'as a new order' do
    it 'returns a new draft one-off order if session does not include the order' do
      order_setup = Orders::SetupForSupplierMenu.new(session_order: nil, suburb: suburb).call

      expect(order_setup).to be_success
      order = order_setup.order
      expect(order).to be_new_record
      expect(order.status).to eq('draft')
      expect(order.order_type).to eq('one-off')
    end

    context 'with passed in cookies' do
      let!(:cookies) do
        {
          yordar_suburb_id: suburb.id,
        }
      end

      it 'returns the new draft order with the suburb set as the cookie suburb' do
        order_setup = Orders::SetupForSupplierMenu.new(session_order: nil, suburb: nil, cookies: cookies).call

        expect(order_setup).to be_success
        order = order_setup.order
        expect(order).to be_new_record
        expect(order.status).to eq('draft')
        expect(order.order_type).to eq('one-off')
        expect(order.delivery_suburb).to eq(suburb)
        expect(order.delivery_address).to be_nil
      end

      it 'returns the new draft order with the delivery (street) address set as per the cookie' do
        cookies_with_street_address = cookies.merge({ yordar_street_address: Faker::Address.street_address })
        order_setup = Orders::SetupForSupplierMenu.new(session_order: nil, suburb: nil, cookies: cookies_with_street_address).call

        expect(order_setup).to be_success
        order = order_setup.order
        expect(order).to be_new_record
        expect(order.status).to eq('draft')
        expect(order.order_type).to eq('one-off')
        expect(order.delivery_suburb).to eq(suburb)
        expect(order.delivery_address).to eq(cookies_with_street_address[:yordar_street_address])
      end
    end # with cookies
  end # new order

  context 'as a recurring order' do
    let!(:order2) { create(:order, :draft, order_type: 'recurrent', recurrent_id: order.id, template_id: order.id, pattern: '1.week', credit_card_id: nil) }
    let!(:order3) { create(:order, :draft, order_type: 'recurrent', recurrent_id: order.id, template_id: order.id, pattern: '1.week', credit_card_id: nil) }
    before do
      order.update_columns(recurrent_id: order.id, order_type: 'recurrent', template_id: order.id, pattern: '1.week', credit_card_id: nil)
    end

    it 'returns the related recurrent orders if current order is in draft' do
      order_setup = Orders::SetupForSupplierMenu.new(session_order: order, suburb: suburb).call

      expect(order_setup).to be_success
      expect(order_setup.recurrent_orders).to include(order, order2, order3)
    end
  end

end
