require 'rails_helper'

RSpec.describe Orders::SkipClosureOrders, type: :service, orders: true, chistmas_closures: true do

  let!(:start_of_month) { Time.zone.now.beginning_of_month }
  let!(:supplier1) { create(:supplier_profile, :random, close_from: start_of_month, close_to: start_of_month.end_of_month) }
  let!(:supplier2) { create(:supplier_profile, :random, close_from: start_of_month, close_to: start_of_month.end_of_month) }

  let!(:order1) { create(:order, :new, order_type: 'recurrent', pattern: '1.week', delivery_at: start_of_month + rand(10).days) }
  let!(:order2) { create(:order, :new, order_type: 'recurrent', pattern: '1.week', delivery_at: start_of_month + rand(10).days) }
  let!(:order3) { create(:order, :new, order_type: 'recurrent', pattern: '1.week', delivery_at: start_of_month + rand(10).days) }

  # order containing both orders
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
  let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier2) }

  # supplier 1 only order
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }

  # supplier 2 only order
  let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }
  let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }

  it 'skips reccurring orders that fall within the supplier closure periods' do
    order_skipper = Orders::SkipClosureOrders.new.call

    expect(order_skipper).to be_success
    skipped_orders = order_skipper.skipped_orders
    expect(skipped_orders.map(&:id)).to include(order1.id, order2.id, order3.id)
    expect(skipped_orders.map(&:status).uniq).to eq(['skipped'])
  end

  it 'doesn\'t skip a draft/pending/skipped/rejected/cancelled order within the supplier closure period' do
    order1.update_column(:status, %w[draft pending rejected skipped cancelled paused].sample)

    order_skipper = Orders::SkipClosureOrders.new.call

    expect(order_skipper).to be_success
    skipped_orders = order_skipper.skipped_orders
    expect(skipped_orders.map(&:id)).to_not include(order1.id)
  end

  it 'doesn\'t skip an order outside the supplier closure dates' do
    order2.update_column(:delivery_at, start_of_month - 1.day)

    order_skipper = Orders::SkipClosureOrders.new.call

    expect(order_skipper).to be_success
    skipped_orders = order_skipper.skipped_orders
    expect(skipped_orders.map(&:id)).to_not include(order2.id)
    expect(order2.reload.status).to_not eq('skipped')
  end

  context 'with different supplier closure periods' do
    before do
      supplier2.update_columns(close_from: start_of_month + 11.days, close_to: start_of_month.end_of_month + 11.days)
    end

    it 'skips reccurring orders that fall within the order\'s supplier closure periods' do
      order_skipper = Orders::SkipClosureOrders.new.call

      expect(order_skipper).to be_success
      skipped_orders = order_skipper.skipped_orders
      expect(skipped_orders.map(&:id)).to include(order1.id, order2.id)
      expect(skipped_orders.map(&:id)).to_not include(order3.id) # order3 belongs to only supplier 2 which is not closed

      expect(skipped_orders.map(&:status).uniq).to eq(['skipped'])
    end
  end

  context 'one-off orders' do
    before do
      order3.update_column(:order_type, 'one-off')
    end

    it 'doesn\'t skip (by default) one-off orders within the supplier closure period' do
      order_skipper = Orders::SkipClosureOrders.new(options: {}).call

      expect(order_skipper).to be_success
      skipped_orders = order_skipper.skipped_orders
      expect(skipped_orders.map(&:id)).to_not include(order3.id)
      expect(order3.reload.status).to_not eq('skipped')
    end

    it 'skips one-off orders within the supplier closure period for passed in option' do
      order_skipper = Orders::SkipClosureOrders.new(options: { exclude_one_offs: false }).call

      expect(order_skipper).to be_success
      skipped_orders = order_skipper.skipped_orders
      expect(skipped_orders.map(&:id)).to include(order3.id)
      expect(order3.reload.status).to eq('skipped')
    end
  end

  context 'dry run' do
    it 'only marks orders within the supplier closure period as skipped' do
      order_skipper = Orders::SkipClosureOrders.new.call(dry_run: true)

      expect(order_skipper).to be_success
      skipped_orders = order_skipper.skipped_orders
      expect(skipped_orders.map(&:id)).to include(order1.id, order2.id, order3.id)
      expect(skipped_orders.map(&:status).uniq).to eq(['skipped'])
      expect(skipped_orders.each(&:reload).map(&:status)).to_not include('skipped')
    end
  end

end
