require 'rails_helper'

RSpec.describe Orders::CancelBlankOrders, type: :service, orders: true do

  let!(:now) { Time.zone.now }
  let!(:order_status) { %w[new amended confirmed delivered].sample }
  let!(:order1) { create(:order, status: order_status, order_variant: 'general', delivery_at: now - 1.day) }
  let!(:order2) { create(:order, status: order_status, order_variant: 'general', delivery_at: now - 3.day) }
  let!(:order3) { create(:order, status: order_status, order_variant: 'general', delivery_at: now - 2.day) }
  let!(:order4) { create(:order, status: order_status, order_variant: 'general', delivery_at: now - 1.day) }

  it 'cancels all blank general non-draft / cancelled  orders' do
    order_canceller = Orders::CancelBlankOrders.new(time: now).call

    expect(order_canceller.cancelled_orders).to include(order1, order2, order3, order4)
  end

  it 'does not cancel non-general orders' do
    [order2, order4].each do |order|
      order.update_column(:order_variant, %w[team_order recurring_team_order event_order home_delivery].sample)
    end

    order_canceller = Orders::CancelBlankOrders.new(time: now).call

    expect(order_canceller.cancelled_orders).to include(order1, order3)
    expect(order_canceller.cancelled_orders).to_not include(order2, order4)
  end

  it 'does not cancel order that are not at least a day old' do
    [order1, order3].each do |order|
      order.update_column(:delivery_at, now - 3.hours)
    end
    order_canceller = Orders::CancelBlankOrders.new(time: now).call

    expect(order_canceller.possible_orders).to include(order2, order4)
    expect(order_canceller.cancelled_orders).to_not include(order1, order3)
  end

  it 'does not cancel any draft or already cancelled orders' do
    [order1, order4].each do |order|
      order.update_column(:status, %w[draft cancelled].sample)
    end
    order_canceller = Orders::CancelBlankOrders.new(time: now).call

    expect(order_canceller.possible_orders).to include(order2, order3)
    expect(order_canceller.cancelled_orders).to_not include(order1, order4)
  end

  context 'with order lines' do
    let!(:order_line11) { create(:order_line, :random, order: order1) }
    let!(:order_line31) { create(:order_line, :random, order: order3) }

    before do
      [order1, order2, order3, order4].each(&:reload)
    end

    it 'does not cancel orders with any order lines' do
      order_canceller = Orders::CancelBlankOrders.new(time: now).call

      expect(order_canceller.possible_orders).to include(order2, order4)
      expect(order_canceller.cancelled_orders).to_not include(order1, order3)
    end
  end

end
