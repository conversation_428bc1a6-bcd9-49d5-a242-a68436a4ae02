require 'rails_helper'

RSpec.describe Orders::FetchMismatchedOrderLines, type: :service, orders: true, menu_items: true, order_lines: true, serving_size: true do

  let!(:menu_item) { create(:menu_item, :random, name: 'menu_item') }
  let!(:menu_item_with_serving_size) { create(:menu_item, :random, price: nil, name: 'menu_item_with_serving_size') }
  let!(:serving_size1) { create(:serving_size, :random, menu_item: menu_item_with_serving_size) }
  let!(:serving_size2) { create(:serving_size, :random, menu_item: menu_item_with_serving_size, is_default: true) }

  let!(:order) { create(:order, :random) }

  it 'returns as blank for an order with no order lines' do
    mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
    expect(mismatched_order_lines).to be_blank
  end

  context 'with order_lines' do
    let!(:item_order_line) { create(:order_line, :random, name: 'item line', order: order, menu_item: menu_item) }
    let!(:serving_size_order_line) { create(:order_line, :random, name: 'serving_size line', order: order, menu_item: menu_item_with_serving_size, serving_size: serving_size1) }

    before do
      order.reload
    end

    it 'returns as blank when there are no mismatches' do
      mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
      expect(mismatched_order_lines).to be_blank
    end

    it 'returns the order line with missing serving size for an item having serving sizes' do
      serving_size_order_line.update_column(:serving_size_id, nil)
      mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
      expect(mismatched_order_lines).to_not be_blank
      expect(mismatched_order_lines.map(&:id)).to include(serving_size_order_line.id)
    end

    it 'returns the order line with missing serving size for an item having serving sizes' do
      serving_size_order_line.update_column(:serving_size_id, nil)
      mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
      expect(mismatched_order_lines).to_not be_blank
      expect(mismatched_order_lines.map(&:id)).to include(serving_size_order_line.id)
    end

    it 'returns the order line with missing serving size for an item having serving sizes along with the default serving size' do
      serving_size_order_line.update_column(:serving_size_id, nil)
      mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
      expect(mismatched_order_lines).to_not be_blank
      expect(mismatched_order_lines.map(&:id)).to include(serving_size_order_line.id)
      expect(mismatched_order_lines.map(&:serving_size_id)).to include(serving_size2.id)
    end

    it 'returns the order line with missing serving size for an item having serving sizes along with the first serving size (if no defaults are set)' do
      serving_size_order_line.update_column(:serving_size_id, nil)
      serving_size2.update_column(:is_default, false)
      mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
      expect(mismatched_order_lines).to_not be_blank
      expect(mismatched_order_lines.map(&:id)).to include(serving_size_order_line.id)
      expect(mismatched_order_lines.map(&:serving_size_id)).to include(serving_size1.id)
    end

    it 'returns the order line with a serving size wherein the attached item no longer has serving sizes' do
      menu_item_with_serving_size.update_column(:price, rand(20))
      serving_size1.update_column(:archived_at, Time.zone.now)
      serving_size2.update_column(:archived_at, Time.zone.now)
      mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
      expect(mismatched_order_lines).to_not be_blank
      expect(mismatched_order_lines.map(&:id)).to include(serving_size_order_line.id)
      expect(mismatched_order_lines.map(&:serving_size_id).compact).to be_blank
    end

    it 'does not return the order line with a serving size even if the serving size is archived but the item still has serving sizes' do
      serving_size1.update_column(:archived_at, Time.zone.now)
      mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
      expect(mismatched_order_lines).to be_blank
    end

    it 'does not return the order line with a serving size wherein the attached item with archived serving sizes is also archived' do
      menu_item_with_serving_size.update_column(:archived_at, Time.zone.now)
      serving_size1.update_column(:archived_at, Time.zone.now)
      serving_size2.update_column(:archived_at, Time.zone.now)
      mismatched_order_lines = Orders::FetchMismatchedOrderLines.new(order: order).call
      expect(mismatched_order_lines).to be_blank
    end
  end

end
