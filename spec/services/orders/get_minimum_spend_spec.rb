require 'rails_helper'

RSpec.describe Orders::GetMinimumSpends, type: :service, orders: true, order_lines: true, suppliers: true, minimums: true do

  let!(:order) { create(:order, :draft) }

  before do
    # mocking this as individual supplier spend check is already tested
    spend_getter = double(Orders::GetSupplierSpends)
    allow(Orders::GetSupplierSpends).to receive(:new).and_return(spend_getter)
    allow(spend_getter).to receive(:call).and_return(true)
  end

  it 'gets the minimum spend for single order' do
    expect(Orders::GetSupplierSpends).to receive(:new).with(order: order, exclude_surcharge: false)

    minimum_spends = Orders::GetMinimumSpends.new(order: order).call

    expect(minimum_spends.size).to eq(1)
  end

  context 'for a recurring order' do
    let!(:recurring_order1) { create(:order, :draft, order_type: 'recurrent', pattern: '1.week') }
    let!(:recurring_order2) { create(:order, :draft, order_type: 'recurrent', pattern: '1.week') }

    before do
      [recurring_order1, recurring_order2].each do |recurring_order|
        recurring_order.update_columns(template_id: recurring_order1.id, recurrent_id: recurring_order1.id)
      end
    end

    it 'gets the minimum spend for each recurring order' do
      expect(Orders::GetSupplierSpends).to receive(:new).with(order: recurring_order1, exclude_surcharge: false)
      expect(Orders::GetSupplierSpends).to receive(:new).with(order: recurring_order2, exclude_surcharge: false)

      minimum_spends = Orders::GetMinimumSpends.new(order: recurring_order1).call

      expect(minimum_spends.size).to eq(2)
    end

    it 'only gets the individual order\'s supplier minimum if order is not in draft' do
      recurring_order1.update_column(:status, %w[new amended confirmed].sample)

      expect(Orders::GetSupplierSpends).to receive(:new).with(order: recurring_order1, exclude_surcharge: false)
      expect(Orders::GetSupplierSpends).to_not receive(:new).with(order: recurring_order2, exclude_surcharge: false)

      minimum_spends = Orders::GetMinimumSpends.new(order: recurring_order1).call

      expect(minimum_spends.size).to eq(1)
    end
  end

end
