require 'rails_helper'

RSpec.describe Orders::GetSupplierSpends, type: :service, team_orders: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order) { create(:order, :draft) }

  let!(:supplier_minimum1) { create(:minimum, :random, supplier_profile: supplier, spend_price: 100) }
  let!(:supplier_minimum2) { create(:minimum, :random, supplier_profile: supplier, spend_price: 200) }
  let!(:supplier_minimum3) { create(:minimum, :random, supplier_profile: supplier, spend_price: 300) }

  let!(:order_line1) { create(:order_line, :random, supplier_profile: supplier, order: order, price: 11, quantity: 3, category: nil, is_gst_free: true) }
  let!(:order_line2) { create(:order_line, :random, supplier_profile: supplier, order: order, price: 11, quantity: 5, category: nil, is_gst_free: true) }

  before do
    order.reload
  end

  context 'with order lines that are under minimum spend' do
    context 'supplier specific spends' do
      it 'returns a list of supplier specific spend values' do
        order_spends = Orders::GetSupplierSpends.new(order: order).call

        expect(order_spends.supplier_spends).to be_present
        expect(order_spends.supplier_spends.map(&:supplier)).to include(supplier)
      end

      it 'returns the total amount spent for the supplier within the order' do
        order_spends = Orders::GetSupplierSpends.new(order: order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend.total_spend).to eq(88.0)
      end

      it 'returns the minimum spend for the supplier within the order' do
        order_spends = Orders::GetSupplierSpends.new(order: order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend.minimum_spend).to eq(300.0)
      end

      it 'returns the remaining spend for the supplier within the order' do
        order_spends = Orders::GetSupplierSpends.new(order: order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend.remaining_spend).to eq(212.0)
      end

      it 'returns if the spend is under the supplier minimum' do
        order_spends = Orders::GetSupplierSpends.new(order: order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend).to be_is_under
      end
    end

    it 'returns the total amount spent for the order' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      expect(order_spends.total_spend).to eq(88.0)
    end

    it 'returns the minimum spend for the order' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      expect(order_spends.minimum_spend).to eq(300.0)
    end

    it 'returns the remaining spend for the order' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      expect(order_spends.remaining_spend).to eq(212.0)
    end

    it 'returns if the order spending is under the supplier minimum' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      expect(order_spends).to be_is_under
    end

    it 'returns the order as not being under if supplier has no minimums' do
      [supplier_minimum1, supplier_minimum2, supplier_minimum3].each(&:destroy)
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      expect(order_spends).to_not be_is_under
      expect(order_spends.remaining_spend).to be_nil
    end
  end

  context 'with existing order supplier surcharge' do
    let!(:order_supplier) { create(:order_supplier, :random, supplier_profile: supplier, order: order) }

    it 'returns the total amount spent / minimum spend / remaining spend for the supplier within the order including the surcharge' do
      order_supplier.update_column(:surcharge, 30)

      order_spends = Orders::GetSupplierSpends.new(order: order).call

      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
      expect(order_supplier_spend.total_spend).to eq(118.0)
      expect(order_supplier_spend.minimum_spend).to eq(300.0)
      expect(order_supplier_spend.remaining_spend).to eq(182.0)
      expect(order_supplier_spend).to be_is_under
    end

    it 'returns the total amount spent / minimum spend for the supplier within the order including the surcharge' do
      order_supplier.update_column(:surcharge, 250)

      order_spends = Orders::GetSupplierSpends.new(order: order).call

      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
      expect(order_supplier_spend.total_spend).to eq(338.0)
      expect(order_supplier_spend.minimum_spend).to eq(300.0)
      expect(order_supplier_spend.remaining_spend).to_not be_present
      expect(order_supplier_spend).to_not be_is_under
    end

    it 'does not include surcharge if not passed' do
      order_supplier.update_column(:surcharge, 250)

      order_spends = Orders::GetSupplierSpends.new(order: order, exclude_surcharge: true).call

      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
      expect(order_supplier_spend.total_spend).to eq(88.0)
      expect(order_supplier_spend.minimum_spend).to eq(300.0)
      expect(order_supplier_spend.remaining_spend).to eq(212.0)
      expect(order_supplier_spend).to be_is_under
    end
  end

  context 'with order lines that are over minimum spend' do
    before do
      order_line2.update_column(:quantity, 30)
    end

    context 'supplier specific spends' do
      it 'returns the total amount spent / minimum spend for the supplier within the order' do
        order_spends = Orders::GetSupplierSpends.new(order: order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend.total_spend).to eq(363.0)
        expect(order_supplier_spend.minimum_spend).to eq(300.0)
      end

      it 'returns a blank remaining spend for the supplier within the order' do
        order_spends = Orders::GetSupplierSpends.new(order: order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend.remaining_spend).to_not be_present
      end

      it 'returns that the spend is not under the supplier minimum' do
        order_spends = Orders::GetSupplierSpends.new(order: order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend).to_not be_is_under
      end
    end

    it 'returns the total amount spent and the minimum_spend for the order' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      expect(order_spends.total_spend).to eq(363.0)
      expect(order_spends.minimum_spend).to eq(300.0)
    end

    it 'returns a blank remaining spend for the order' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      expect(order_spends.remaining_spend).to_not be_present
    end

    it 'returns that the order spending is not under the supplier minimum' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      expect(order_spends).to_not be_is_under
    end
  end

  context 'as a team order' do
    let!(:team_order) { order }
    let!(:team_order_supplier) { create(:order_supplier, :random, supplier_profile: supplier, order: team_order) }

    let!(:team_order_attendee1) { create(:team_order_attendee, :random, order: team_order, status: 'ordered') }
    let!(:team_order_attendee2) { create(:team_order_attendee, :random, order: team_order, status: 'ordered') }

    before do
      order.update_columns(status: 'pending', order_variant: 'team_order')
      order_line1.update_column(:attendee_id, team_order_attendee1.id)
      order_line2.update_column(:attendee_id, team_order_attendee2.id)
    end

    context 'team attendees with mixed status' do
      before do
        team_order_attendee2.update_column(:status, 'pending')
      end

      it 'returns the total amount spent for the supplier within the order only for the confirmed attendees' do
        order_spends = Orders::GetSupplierSpends.new(order: team_order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend.total_spend).to eq(33.0)
      end

      it 'returns the total amount spent for the order only from confirmed attendees' do
        order_spends = Orders::GetSupplierSpends.new(order: team_order).call

        expect(order_spends.total_spend).to eq(33.0)
      end
    end

    context 'team order with admin selected order lines' do
      let!(:admin_order_line) { create(:order_line, :random, supplier_profile: supplier, order: team_order, price: 11, quantity: 4, is_gst_free: true) }

      it 'returns the total amount spent for the supplier within the order including order lines by admin' do
        order_spends = Orders::GetSupplierSpends.new(order: team_order).call

        order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}
        expect(order_supplier_spend.total_spend).to eq(132.0)
      end

      it 'returns the total amount spent for the order only from confirmed attendees' do
        order_spends = Orders::GetSupplierSpends.new(order: team_order).call

        expect(order_spends.total_spend).to eq(132.0)
      end
    end
  end

  context 'with non gst-free items' do
    before do
      order_line2.update_column(:is_gst_free, false)
    end

    it 'returns the total amount spent for the supplier as well as order (in total) excluding gst' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}

      expect(order_spends.total_spend).to eq(88)
      expect(order_supplier_spend.total_spend).to eq(88)
    end

    it 'returns the remaining amount left for the supplier as well as order excluding gst' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}

      expect(order_spends.remaining_spend).to eq(212)
      expect(order_supplier_spend.remaining_spend).to eq(212)
    end
  end

  context 'with gst-inc items' do
    before do
      order_line2.update_column(:is_gst_inc, true)
    end

    it 'returns the total amount spent for the supplier as well as order (in total) excluding gst' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}

      expect(order_spends.total_spend).to eq(83)
      expect(order_supplier_spend.total_spend).to eq(83)
    end

    it 'returns the remaining amount left for the supplier as well as order excluding gst' do
      order_spends = Orders::GetSupplierSpends.new(order: order).call

      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}

      expect(order_spends.remaining_spend).to eq(217)
      expect(order_supplier_spend.remaining_spend).to eq(217)
    end
  end

  context 'with multiple category minimums' do
    let!(:category1) { create(:category, :random, group: 'catering-services') }
    let!(:category2) { create(:category, :random, group: 'catering-services') }
    let!(:category3) { create(:category, :random, group: 'kitchen-supplies') }

    it 'returns the supplier and the order total minimum based on the category of the order line' do
      supplier_minimum1.update_column(:category_id, category3.id)
      supplier_minimum2.update_column(:category_id, category1.id)
      supplier_minimum3.update_column(:category_id, category3.id)

      order_line1.update_column(:category_id, category1.id)

      order_spends = Orders::GetSupplierSpends.new(order: order).call
      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}

      expect(order_spends.minimum_spend).to eq(supplier_minimum2.spend_price)
      expect(order_supplier_spend.minimum_spend).to eq(supplier_minimum2.spend_price)
    end

    it 'returns the supplier and the order total minimum based on the category group of the order line' do
      supplier_minimum1.update_column(:category_id, category1.id)
      supplier_minimum2.update_column(:category_id, category3.id)
      supplier_minimum3.update_column(:category_id, category3.id)
      order_line1.update_column(:category_id, category2.id)

      order_spends = Orders::GetSupplierSpends.new(order: order).call
      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}

      expect(order_spends.minimum_spend).to eq(supplier_minimum1.spend_price)
      expect(order_supplier_spend.minimum_spend).to eq(supplier_minimum1.spend_price)
    end

    it 'returns the maximum spend for the supplier no minimums are found with the same order_line category or category group' do
      supplier_minimum1.update_column(:category_id, category1.id)
      supplier_minimum2.update_column(:category_id, category1.id)
      supplier_minimum3.update_column(:category_id, category1.id)
      order_line1.update_column(:category_id, category2.id)

      order_spends = Orders::GetSupplierSpends.new(order: order).call
      order_supplier_spend = order_spends.supplier_spends.detect{|supplier_spend| supplier_spend.supplier == supplier}

      expect(order_spends.minimum_spend).to eq(supplier_minimum3.spend_price)
      expect(order_supplier_spend.minimum_spend).to eq(supplier_minimum3.spend_price)
    end
  end

end
