require 'rails_helper'

RSpec.describe Orders::OnHoldCharges::Refund, type: :service, orders: true, stripe: true do

  let!(:credit_card) { create(:credit_card, :valid_stripe_payment) }
  let!(:order) { create(:order, :new, credit_card: credit_card, customer_total: 1000) }
  let!(:order_charge1) { create(:order_charge, :random, order: order) }
  let!(:order_charge2) { create(:order_charge, :random, order: order) }

  let!(:stripe_refund) { Stripe::RefundOrderCharge::StripeRefund.new(SecureRandom.hex(7), SecureRandom.hex(7), SecureRandom.hex(7)) }

  before do
    # mock stripe refunder
    charge_refunder = double(Stripe::RefundOrderCharge)
    allow(Stripe::RefundOrderCharge).to receive(:new).and_return(charge_refunder)
    charge_refunder_response = OpenStruct.new(success?: true, errors: [], refund: stripe_refund)
    allow(charge_refunder).to receive(:call).and_return(charge_refunder_response)

    allow(SlackNotifier).to receive(:send).and_return(true)
  end

  it 'makes a request to refund active charges' do
    expect(Stripe::RefundOrderCharge).to receive(:new).with(order_charge: order_charge1)
    expect(Stripe::RefundOrderCharge).to receive(:new).with(order_charge: order_charge2)

    order_charger = Orders::OnHoldCharges::Refund.new(order: order).call

    expect(order_charger).to be_success
  end

  it 'saves the refund token within the existing charges and marks it as refunded' do
    order_charger = Orders::OnHoldCharges::Refund.new(order: order).call

    expect(order_charger).to be_success

    order_charge1.reload
    expect(order_charge1.refund_token).to eq(stripe_refund.id)
    expect(order_charge1).to be_refunded

    order_charge2.reload
    expect(order_charge2.refund_token).to eq(stripe_refund.id)
    expect(order_charge2).to be_refunded
  end

  it 'only refunds passed in order charges' do
    order_charger = Orders::OnHoldCharges::Refund.new(order: order, order_charges: Order::Charge.where(id: order_charge2.id)).call

    expect(order_charger).to be_success

    order_charge1.reload
    expect(order_charge1.refund_token).to be_blank
    expect(order_charge1).to_not be_refunded

    order_charge2.reload
    expect(order_charge2.refund_token).to eq(stripe_refund.id)
    expect(order_charge2).to be_refunded
  end

  context 'notification' do
    it 'sends a notification via Slack if env.STRIPE_SLACK_NOTIFICATION is set to \'true\'' do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:stripe, :slack_notification).and_return('true')

      message = ":heavy_dollar_sign: An on-hold charge has been refunded/canceled for order ##{order.id}"
      expect(SlackNotifier).to receive(:send).with(message, attachments: anything)

      order_charger = Orders::OnHoldCharges::Refund.new(order: order).call

      expect(order_charger).to be_success
    end

    it 'does not send a notification via Slack if env.STRIPE_SLACK_NOTIFICATION is not \'true\'' do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:stripe, :slack_notification).and_return('not-true')

      expect(SlackNotifier).to_not receive(:send)

      order_charger = Orders::OnHoldCharges::Refund.new(order: order).call

      expect(order_charger).to be_success
    end
  end

  context 'with stripe errors' do

  end

end
