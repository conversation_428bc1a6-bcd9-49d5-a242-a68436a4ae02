require 'rails_helper'

RSpec.describe Orders::OnHoldCharges::Upsert, type: :service, orders: true, charges: true, stripe: true do

  let!(:credit_card) { create(:credit_card, :valid_stripe_payment) }
  let!(:order) { create(:order, :new, credit_card: credit_card, customer_total: 1000) }

  let!(:charge_creator) { double(Stripe::CreateOrderCharge) }
  let!(:stripe_charge) { Stripe::CreateOrderCharge::StripeCharge.new(SecureRandom.hex(7), SecureRandom.hex(7)) }
  let!(:stripe_refund) { Stripe::RefundOrderCharge::StripeRefund.new(SecureRandom.hex(7), SecureRandom.hex(7), SecureRandom.hex(7)) }

  before do
    # mock stripe charger
    allow(Stripe::CreateOrderCharge).to receive(:new).and_return(charge_creator)
    charge_creator_response = OpenStruct.new(success?: true, errors: [], charge: stripe_charge)
    allow(charge_creator).to receive(:call).and_return(charge_creator_response)

    # mock refunder
    charge_refunder = double(Orders::OnHoldCharges::Refund)
    allow(Orders::OnHoldCharges::Refund).to receive(:new).and_return(charge_refunder)
    charge_refunder_response = OpenStruct.new(success?: true, errors: [], refunds: [stripe_refund])
    allow(charge_refunder).to receive(:call).and_return(charge_refunder_response)

    # mock card error handler
    card_error_handler = double(Orders::OnHoldCharges::HandleCardError)
    allow(Orders::OnHoldCharges::HandleCardError).to receive(:new).and_return(card_error_handler)
    allow(card_error_handler).to receive(:call).and_return(true)

    # mock slack notifier
    allow(SlackNotifier).to receive(:send).and_return(true)
  end

  it 'creates a charge for the order' do
    order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

    expect(order_charger).to be_success
    created_charge = order_charger.charge

    expect(created_charge).to be_present
    expect(created_charge.order).to eq(order)
    expect(created_charge.amount).to eq(order.customer_total)
    expect(created_charge.stripe_token).to eq(stripe_charge.payment_id)
    expect(created_charge.status).to eq('charged')
  end

  it 'makes a request to create a new on-hold charge in Stripe' do
    expect(Stripe::CreateOrderCharge).to receive(:new).with(order: order)
    order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

    expect(order_charger).to be_success
  end

  it 'does not make a request to refund' do
    expect(Stripe::RefundOrderCharge).to_not receive(:new)
    order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

    expect(order_charger).to be_success
  end

  context 'notification' do
    it 'sends a notification via Slack if env.STRIPE_SLACK_NOTIFICATION is set to true' do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:stripe, :slack_notification).and_return(true)

      message = ":credit_card: An on-hold charge was created for order ##{order.id} (via stripe)"
      expect(SlackNotifier).to receive(:send).with(message, attachments: anything)

      order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

      expect(order_charger).to be_success
    end

    it 'does not send a notification via Slack if env.STRIPE_SLACK_NOTIFICATION is not \'true\'' do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:stripe, :slack_notification).and_return([false, nil].sample)
      expect(SlackNotifier).to_not receive(:send)

      order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

      expect(order_charger).to be_success
    end
  end

  context 'with existing on-hold charge' do
    let!(:order_charge) { create(:order_charge, :random, order: order) }

    it 'makes a request to refund the charge' do
      expect(Orders::OnHoldCharges::Refund).to receive(:new).with(order: order, order_charges: [order_charge])

      order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

      expect(order_charger).to be_success
    end

    it 'creates a new active charge for the order' do
      order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

      expect(order_charger).to be_success
      created_charge = order_charger.charge

      expect(created_charge).to be_present
      expect(created_charge.id).to_not eq(order_charge.id)
      expect(created_charge.order).to eq(order)
      expect(created_charge.amount).to eq(order.customer_total)
      expect(created_charge.stripe_token).to eq(stripe_charge.payment_id)
    end
  end

  context 'errors' do
    it 'does not create a charge for a missing order' do
      order_charger = Orders::OnHoldCharges::Upsert.new(order: nil).call

      expect(order_charger).to_not be_success
      expect(order_charger.errors).to include('Cannot charge without an order')
    end

    it 'does not create a charge for a pay on account order' do
      credit_card.update_column(:pay_on_account, true)
      order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

      expect(order_charger).to_not be_success
      expect(order_charger.errors).to include('Cannot charge without a valid credit card')
    end

    it 'does not create a charge for a credit card not saved with stripe' do
      credit_card.update_column(:stripe_token, nil)
      order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

      expect(order_charger).to_not be_success
      expect(order_charger.errors).to include('Cannot charge on a non-stripe card')
    end

    context 'with Stripe Charge creation Invalid Request Error' do
      before do
        invalid_charge_creator_response = OpenStruct.new(success?: false, errors: ['invalid request error message'], charge: nil)
        allow(charge_creator).to receive(:call).and_return(invalid_charge_creator_response)
      end

      it 'returns the error messages' do
        order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

        expect(order_charger).to_not be_success
        expect(order_charger.errors).to include('invalid request error message')
      end

      context 'with slack notifications turned on' do
        before do
          allow_any_instance_of(Object).to receive(:yordar_credentials).with(:stripe, :slack_notification).and_return('true')
        end

        it 'notifies errors (via slack) for failuire to create Stripe Charge' do
          error_message = ":warning: :credit_card: Could not create on-hold charge for order ##{order.id} (via stripe)"
          expect(SlackNotifier).to receive(:send).with(error_message, attachments: anything)

          order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call
          expect(order_charger).to_not be_success
          expect(order_charger.errors).to include('invalid request error message')
        end
      end
    end

    context 'with Stripe Charge creation - Card Error' do
      let!(:card_error_message) { 'Your card was declined.'  }
      let!(:card_error_json_body) do
        {
          error: {
            charge: SecureRandom.hex(7),
            code: 'card_declined',
            decline_code: 'fraudulent',
            doc_url: 'https://stripe.com/docs/error-codes/card-declined',
            message: card_error_message,
            payment_intent: {
              # ...
            }
          }
        }
      end

      let!(:card_error) { Stripe::CardError.new(card_error_message, nil, 'card_declined', json_body: card_error_json_body) }

      before do
        invalid_charge_creator_response = OpenStruct.new(success?: false, errors: [card_error], charge: nil)
        allow(charge_creator).to receive(:call).and_return(invalid_charge_creator_response)
      end

      it 'return with errors' do
        order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call

        expect(order_charger).to_not be_success
        expect(order_charger.errors).to include('On-Hold Charge could not be created due to Card Error on Stripe')
      end

      it 'makes a request to handle the Stripe Card Error' do
        expect(Orders::OnHoldCharges::HandleCardError).to receive(:new).with(order: order, card_error: card_error)

        order_charger = Orders::OnHoldCharges::Upsert.new(order: order).call        
        expect(order_charger).to_not be_success
      end
    end
  end

end
