require 'rails_helper'

RSpec.describe Orders::OnHoldCharges::HandleCardError, type: :service, stripe: true, orders: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :new, customer_profile: customer) }

  let!(:card_error_message) { 'Your card was declined.' }
  let!(:card_error_json_body) do
    {
      error: {
        charge: SecureRandom.hex(7),
        code: 'card_declined',
        decline_code: 'fraudulent',
        doc_url: 'https://stripe.com/docs/error-codes/card-declined',
        message: card_error_message,
        payment_intent: {
          # ...
        }
      }
    }
  end

  let!(:card_error) { Stripe::CardError.new(card_error_message, nil, 'card_declined', json_body: card_error_json_body) }

  before do
    # mock email sender
    email_sender = double(Orders::Emails::SendOrderChargeFailedEmail)
    allow(Orders::Emails::SendOrderChargeFailedEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:call).and_return(true)
  end

  it 'creates a failed order-charge' do
    error_handler = Orders::OnHoldCharges::HandleCardError.new(order: order, card_error: card_error).call

    expect(error_handler).to be_success

    failed_charge = error_handler.on_hold_charge
    expect(failed_charge).to be_present
    expect(failed_charge).to be_persisted
    expect(failed_charge.order).to eq(order)
    expect(failed_charge.status).to eq('failed')
  end

  context 'order status' do
    let!(:order2) { create(:order, customer_profile: customer, status: (Order::VALID_ORDER_STATUSES - ['delivered']).sample, delivery_at: Time.zone.now - 2.days) }

    it 'cancels the order if the customer does not have any delivered orders in the past' do
      error_handler = Orders::OnHoldCharges::HandleCardError.new(order: order, card_error: card_error).call

      expect(error_handler).to be_success

      handled_order = error_handler.order
      expect(handled_order).to be_present
      expect(handled_order.id).to eq(order.id)
      expect(handled_order.status).to eq('cancelled')
    end

    it 'does not update the order status if the customer has delivered orders in the past' do
      order2.update_column(:status, 'delivered') # mark past order as delivered

      error_handler = Orders::OnHoldCharges::HandleCardError.new(order: order, card_error: card_error).call

      expect(error_handler).to be_success

      handled_order = error_handler.order
      expect(handled_order).to be_present
      expect(handled_order.id).to eq(order.id)
      expect(handled_order.status).to_not eq('cancelled')
    end
  end

  context 'nofitications' do
    let!(:expected_card_error) do
      OpenStruct.new(
        message: card_error_message,
        code: card_error.code,
        decline_code: card_error.json_body.dig(:error, :decline_code)
      )
    end

    it 'notifies the admin (with a modified card error)' do
      expect(Orders::Emails::SendOrderChargeFailedEmail).to receive(:new).with(order: order, customer: customer, card_error: expected_card_error)

      error_handler = Orders::OnHoldCharges::HandleCardError.new(order: order, card_error: card_error).call
      expect(error_handler).to be_success
    end

    it 'logs an `On Hold Charge Failed` event', event_logs: true do
      expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'on-hold-charge-failed', severity: 'error', message: expected_card_error.message, decline_code: expected_card_error.decline_code)

      error_handler = Orders::OnHoldCharges::HandleCardError.new(order: order, card_error: card_error).call
      expect(error_handler).to be_success
    end
  end

  context 'errors' do
    it 'errors if the order is missing' do
      error_handler = Orders::OnHoldCharges::HandleCardError.new(order: nil, card_error: card_error).call

      expect(error_handler).to_not be_success
      expect(error_handler.errors).to include('Cannot handle without an order')
    end

    it 'errors if the card error is missing or invalid' do
      error_handler = Orders::OnHoldCharges::HandleCardError.new(order: order, card_error: [nil, 'invalid-card-error'].sample).call

      expect(error_handler).to_not be_success
      expect(error_handler.errors).to include('Cannot handle without a valid card error')
    end
  end

end