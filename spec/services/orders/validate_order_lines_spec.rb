require 'rails_helper'

RSpec.describe Orders::ValidateOrderLines, type: :service, orders: true do

  let!(:order) { create(:order, :draft) }

  let!(:order_line1) { create(:order_line, :random, order: order) }
  let!(:order_line2) { create(:order_line, :random, order: order) }

  it 'returns with success if the order has all valid order lines' do
    order_validator = Orders::ValidateOrderLines.new(order: order).call

    expect(order_validator).to be_success
  end

  context 'with missing item errors or supplier error' do
    let!(:item_error) { ['The Supplier is no longer available', 'The Item is no longer available'].sample }

    before do
      [order_line1, order_line2].each do |order_line|
        order_line.update_column(:last_errors, [item_error])
      end
    end

    it 'returns with errors' do
      order_validator = Orders::ValidateOrderLines.new(order: order).call

      expect(order_validator).to_not be_success
      expect(order_validator.warnings).to be_blank

      validation_errors = order_validator.errors
      expect(validation_errors.sample).to be_a(Orders::ValidateOrderLines::ValidationError)
      expect(validation_errors.map(&:name)).to include(order_line1.name, order_line2.name)
      expect(validation_errors.map(&:message)).to include(item_error)
    end
  end

  context 'with missing menu extra errors' do
    let!(:item_error) { 'Selected item extras are no longer available' }

    before do
      [order_line1, order_line2].each do |order_line|
        order_line.update_column(:last_errors, [item_error])
      end
    end

    it 'returns with warnings' do
      order_validator = Orders::ValidateOrderLines.new(order: order).call

      expect(order_validator).to_not be_success
      expect(order_validator.errors).to be_blank

      validation_warnings = order_validator.warnings
      expect(validation_warnings.sample).to be_a(Orders::ValidateOrderLines::ValidationError)
      expect(validation_warnings.map(&:name)).to include(order_line1.name, order_line2.name)
      expect(validation_warnings.map(&:message)).to include(item_error)
    end
  end

end