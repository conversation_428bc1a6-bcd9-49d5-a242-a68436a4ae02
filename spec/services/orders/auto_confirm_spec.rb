require 'rails_helper'

RSpec.describe Orders::AutoConfirm, type: :service, orders: true do

  let!(:now) { Time.zone.parse('2020-07-22 13:00:00') } # Wednesday, 22 Jul 2020 13:00:00 AEST +10:00
  let!(:order1) { create(:order, :new, delivery_at: now + 1.hours) } # Wednesday 22 Jul
  let!(:order2) { create(:order, :new, delivery_at: now + 3.days) } # Saturday 25 Jul
  let!(:order3) { create(:order, :new, delivery_at: now + 1.days) } # Thursday 23 Jul
  let!(:order4) { create(:order, :new, delivery_at: now + 4.days) } # Sunday 26 Jul

  before do
    # mock admin email sender
    email_sender = delayed_email_sender = double(Orders::Emails::SendConfirmationCheckEmail)
    allow(Orders::Emails::SendConfirmationCheckEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock customer email sender
    confirm_email_sender = delayed_confirm_email_sender = double(Customers::Emails::SendOrderConfirmationEmail)
    allow(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).and_return(confirm_email_sender)
    allow(confirm_email_sender).to receive(:delay).and_return(delayed_confirm_email_sender)
    allow(delayed_confirm_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'auto confirms order which are a day from now, when auto confirming on a non-friday weekday' do
    auto_confirmer = Orders::AutoConfirm.new(time: now).call

    expect(auto_confirmer).to be_success
    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders

    expect(auto_confirmed_orders.map(&:id)).to include(order1.id, order3.id)
    expect(auto_confirmed_orders.map(&:status).uniq).to match_array(['confirmed'])
  end

  it 'does not auto confirm orders outside time range' do
    auto_confirmer = Orders::AutoConfirm.new(time: now).call

    expect(auto_confirmer).to be_success

    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders
    expect(auto_confirmed_orders.map(&:id)).to_not include(order2.id)
    expect(order2.reload.status).to eq('new')
  end

  it 'does not auto confirm any `team order`', team_orders: true do
    order1.update_column(:order_variant, 'team_order')

    auto_confirmer = Orders::AutoConfirm.new(time: now).call
    expect(auto_confirmer).to be_success

    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders
    expect(auto_confirmed_orders.map(&:id)).to_not include(order1.id)
    expect(order1.reload.status).to eq('new')
  end

  it 'does not auto confirm any orders which are not new or amended' do
    non_confirmable_status = %w[draft delivered pending paused].sample
    order1.update_column(:status, non_confirmable_status)

    auto_confirmer = Orders::AutoConfirm.new(time: now).call
    expect(auto_confirmer).to be_success

    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders
    expect(auto_confirmed_orders.map(&:id)).to_not include(order1.id)
    expect(order1.reload.status).to eq(non_confirmable_status)
  end

  it 'auto confirms weekend orders when auto confirming on Friday (now = friday)' do
    auto_confirmer = Orders::AutoConfirm.new(time: now + 2.days).call

    expect(auto_confirmer).to be_success
    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders

    expect(auto_confirmed_orders.map(&:id)).to include(order2.id, order4.id)
    expect(auto_confirmed_orders.map(&:status).uniq).to match_array(['confirmed'])

    expect(auto_confirmed_orders.map(&:id)).to_not include(order1.id, order3.id)
  end

  it 'sends an email to the admin about the auto order confirmation' do
    expect(Orders::Emails::SendConfirmationCheckEmail).to receive(:new)

    auto_confirmer = Orders::AutoConfirm.new(time: now).call
    expect(auto_confirmer).to be_success
  end

  it 'sends a confirmation email for the confirmed orders', notifications: true do # from Orders::Confirm
    expect(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).with(customer: order1.customer_profile, order: order1)
    expect(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).with(customer: order3.customer_profile, order: order3)

    auto_confirmer = Orders::AutoConfirm.new(time: now).call
    expect(auto_confirmer).to be_success
  end

  it 'logs an `Orders Auto Confirmed` event with the auto-confirmed order IDs', event_logs: true do
    expect(EventLogs::Create).to receive(:new).with(event: 'orders-auto-confirmed', orders: [order1.id, order3.id])

    auto_confirmer = Orders::AutoConfirm.new(time: now).call
    expect(auto_confirmer).to be_success
  end

end
