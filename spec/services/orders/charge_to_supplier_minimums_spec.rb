require 'rails_helper'

RSpec.describe Orders::ChargeToSupplierMinimums, type: :service, orders: true, suppliers: true do

  let!(:spend_calculator) { double(Orders::GetSupplierSpends) }

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier_order_spend1) { SupplierSpend.new(supplier: supplier1, total_spend: 10, minimum_spend: 100) }

  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:supplier_order_spend2) { SupplierSpend.new(supplier: supplier2, total_spend: 90, minimum_spend: 100) }

  let!(:order) { create(:order, :draft, charge_to_minimum: true) }

  let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
  let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

  before do
    # mock this as Orders::GetSupplierSpends is tested separately
    allow(Orders::GetSupplierSpends).to receive(:new).and_return(spend_calculator)
    supplier_spends = OpenStruct.new(order: order, supplier_spends: [supplier_order_spend1, supplier_order_spend2])
    allow(spend_calculator).to receive(:call).and_return(supplier_spends)
  end

  it 'creates order_suppliers for the order' do
    Orders::ChargeToSupplierMinimums.new(order: order).call

    order_suppliers = order.reload.order_suppliers

    expect(order_suppliers).to be_present
    expect(order_suppliers.map(&:supplier_profile)).to include(supplier1, supplier2)
  end

  it 'adds remaining spend as surcharge to the supplier and the order' do
    Orders::ChargeToSupplierMinimums.new(order: order).call

    order_suppliers = order.reload.order_suppliers
    expect(order_suppliers).to be_present

    remaining_spend1 = supplier_order_spend1.minimum_spend - supplier_order_spend1.total_spend
    remaining_spend2 = supplier_order_spend2.minimum_spend - supplier_order_spend2.total_spend
    expect(order_suppliers.map(&:surcharge)).to include(remaining_spend1, remaining_spend2)

    order_total = Orders::CalculateCustomerTotals.new(order: order).call
    expect(order_total[:topup]).to eq(remaining_spend1 + remaining_spend2)
  end

  it 'doesn\'t add any surcharge if order is not set to charge_to_minimum' do
    order.update_column(:charge_to_minimum, false)

    Orders::ChargeToSupplierMinimums.new(order: order).call

    order_suppliers = order.reload.order_suppliers
    expect(order_suppliers).to_not be_present

    order_total = Orders::CalculateCustomerTotals.new(order: order).call
    expect(order_total[:topup]).to eq(0)
  end

  context 'with existing order_suppliers' do
    let!(:order_supplier1) { create(:order_supplier, order: order, supplier_profile: supplier1, surcharge: 50) }
    let!(:order_supplier2) { create(:order_supplier, order: order, supplier_profile: supplier2, surcharge: 70) }

    let!(:supplier3) { create(:supplier_profile, :random) }
    let!(:order_supplier3) { create(:order_supplier, order: order, supplier_profile: supplier3, surcharge: 22) }

    it 'updates the order suppliers' do
      Orders::ChargeToSupplierMinimums.new(order: order).call

      remaining_spend1 = supplier_order_spend1.minimum_spend - supplier_order_spend1.total_spend
      expect(order_supplier1.reload.surcharge).to eq(remaining_spend1)

      remaining_spend2 = supplier_order_spend2.minimum_spend - supplier_order_spend2.total_spend
      expect(order_supplier2.reload.surcharge).to eq(remaining_spend2)
    end

    it 'removes order suppliers records of suppliers not within the order\'s order lines' do
      Orders::ChargeToSupplierMinimums.new(order: order).call
      expect{ order_supplier3.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'doesn\'t removes order suppliers records of suppliers not within the order\'s order lines for a team order', team_orders: true do
      order.update_column(:order_variant, 'team_order')
      Orders::ChargeToSupplierMinimums.new(order: order).call
      expect{ order_supplier3.reload }.to_not raise_error(ActiveRecord::RecordNotFound)
    end
  end

  context 'with a supplier spends above minimum' do
    let!(:supplier_order_spend2) { SupplierSpend.new(supplier: supplier2, total_spend: [100, 101].sample, minimum_spend: 100) }

    before do
      allow(Orders::GetSupplierSpends).to receive(:new).and_return(spend_calculator)
      supplier_spends_under = OpenStruct.new(order: order, supplier_spends: [supplier_order_spend1, supplier_order_spend2])
      allow(spend_calculator).to receive(:call).and_return(supplier_spends_under)
    end

    it 'doesn\'t add any surcharge for a supplier who not not under in spends' do
      Orders::ChargeToSupplierMinimums.new(order: order).call

      supplier2_order_supplier = order.reload.order_suppliers.where(supplier_profile: supplier2).first
      expect(supplier2_order_supplier).to be_present
      expect(supplier2_order_supplier.surcharge).to be_nil

      order_total = Orders::CalculateCustomerTotals.new(order: order).call
      expect(order_total[:topup]).to eq(90)
    end
  end

end
