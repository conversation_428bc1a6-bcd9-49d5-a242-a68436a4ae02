require 'rails_helper'

RSpec.describe Orders::ConvertToRecurrent, type: :service, orders: true do

  let(:order) { create(:order, :draft, order_type: 'one-off') }

  context 'with valid params' do
    let(:recurrent_params) { { days: { mon: true, wed: true, fri: true }, frequency: 'weekly' } }

    it 'converts a one-off order to a recurrent order' do
      order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params).call
      expect(order_converter).to be_success
      recurrent_order = order_converter.order
      expect(recurrent_order).to eq(order.reload)
      expect(recurrent_order.order_type).to eq('recurrent')
      expect(recurrent_order.recurrent_id).to eq(order.id)
      expect(recurrent_order.template_id).to eq(order.id)
      expect(recurrent_order.name).to eq('mon')
    end

    it 'converts a one-off order to a recurrent order for 1 day' do
      recurrent_params[:days] = { mon: true }
      order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params).call
      expect(order_converter).to be_success
      recurrent_order = order_converter.order
      expect(recurrent_order).to eq(order.reload)
      expect(recurrent_order.order_type).to eq('recurrent')
      expect(recurrent_order.recurrent_id).to eq(order.id)
      expect(recurrent_order.template_id).to eq(order.id)
      expect(recurrent_order.name).to eq('mon')
    end

    it 'creates duplicate recurrent orders for the specifed days' do
      order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params).call
      expect(order_converter).to be_success

      recurrent_order = order_converter.order
      recurrent_orders = order_converter.recurrent_orders
      expect(recurrent_orders.size).to eq(recurrent_params[:days].size - 1)
      expect(recurrent_orders.map(&:template_id)).to_not include(recurrent_order.id)
      expect(recurrent_orders.map(&:template_id)).to match_array(recurrent_orders.map(&:id))
      expect(recurrent_orders.map(&:recurrent_id)).to include(recurrent_order.id)
      expect(recurrent_orders.map(&:order_type)).to include('recurrent')
      expect(recurrent_orders.map(&:name)).to include('wed', 'fri')
      expect(recurrent_orders.map(&:pattern)).to include(recurrent_order.pattern)
      expect(recurrent_orders.map(&:skip)).to include(recurrent_order.skip)
      expect(recurrent_orders.map(&:recurring_order_params)).to include(recurrent_order.recurring_order_params)
      expect(recurrent_orders.map{|o| o.order_lines.size }).to include(recurrent_order.order_lines.size) # 0
      expect(recurrent_orders.map(&:uuid).all?(&:present?)).to be_truthy # each order has a uuid
    end

    context 'with recurrent days' do
      it 'creates duplicate recurrent orders for the days specified as recurrent_days' do
        recurrent_params_with_recurrent_days = recurrent_params
        recurrent_params_with_recurrent_days[:recurrent_days] = %w[tue thu sat]
        order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params_with_recurrent_days).call
        expect(order_converter).to be_success

        recurrent_order = order_converter.order
        expect(recurrent_order.name).to eq('tue')

        recurrent_orders = order_converter.recurrent_orders
        expect(recurrent_orders.size).to eq(recurrent_params_with_recurrent_days[:recurrent_days].size - 1)
        expect(recurrent_orders.map(&:name)).to include('thu', 'sat')
      end

      it 'rearranges recurrent orders for the unsorted days specified as recurrent_days' do
        recurrent_params_with_recurrent_days = recurrent_params
        recurrent_params_with_recurrent_days[:recurrent_days] = %w[thu sat tue]
        order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params_with_recurrent_days).call
        expect(order_converter).to be_success

        recurrent_order = order_converter.order
        expect(recurrent_order.name).to eq('tue')

        recurrent_orders = order_converter.recurrent_orders
        expect(recurrent_orders.size).to eq(recurrent_params_with_recurrent_days[:recurrent_days].size - 1)
        expect(recurrent_orders.map(&:name)).to match_array(%w[thu sat])
      end

      it 'only creates recurrent order for proper (3-letter) day names passed as recurrent days' do
        recurrent_params_with_recurrent_days = recurrent_params
        recurrent_params_with_recurrent_days[:recurrent_days] = %w[thu saturday tue]
        order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params_with_recurrent_days).call
        expect(order_converter).to be_success

        recurrent_order = order_converter.order
        expect(recurrent_order.name).to eq('tue')

        recurrent_orders = order_converter.recurrent_orders
        expect(recurrent_orders.size).to eq(recurrent_params_with_recurrent_days[:recurrent_days].size - 2)
        expect(recurrent_orders.map(&:name)).to match_array(['thu'])
      end
    end

    it 'copies over the whodunnit id' do
      order.update_column(:whodunnit_id, rand(2000))
      order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params).call
      expect(order_converter).to be_success

      recurrent_orders = order_converter.recurrent_orders.select{|order| %w[wed fri].include?(order.name) }
      expect(recurrent_orders.map(&:whodunnit_id).uniq).to match_array([order.whodunnit_id])
    end

    context 'with order lines' do
      let(:location1) { create(:location, :random, order: order) }
      let!(:order_line11) { create(:order_line, :random, order: order, location: location1) }
      let!(:order_line12) { create(:order_line, :random, order: order, location: location1) }

      let(:location2) { create(:location, :random, order: order) }
      let!(:order_line21) { create(:order_line, :random, order: order, location: location2) }

      let!(:recurrent_params_with_order_lines) { recurrent_params.merge({ copy_all: true }) }

      it 'clones all the order lines for the order within the recurrent orders' do
        order_converter = Orders::ConvertToRecurrent.new(order: order.reload, recurrent_params: recurrent_params_with_order_lines).call
        expect(order_converter).to be_success

        recurrent_orders = order_converter.recurrent_orders
        recurrent_order_lines = recurrent_orders.map(&:order_lines).flatten

        expect(recurrent_order_lines.map(&:name)).to include(order_line11.name, order_line12.name, order_line21.name)
        expect(recurrent_order_lines.map(&:menu_item)).to include(order_line11.menu_item, order_line12.menu_item, order_line21.menu_item)
        expect(recurrent_order_lines.map(&:quantity)).to include(order_line11.quantity, order_line12.quantity, order_line21.quantity)
        expect(recurrent_order_lines.map(&:price)).to include(order_line11.price, order_line12.price, order_line21.price)
        expect(recurrent_order_lines.map(&:cost)).to include(order_line11.cost, order_line12.cost, order_line21.cost)
        expect(recurrent_order_lines.map(&:id)).to_not include(order_line11.id, order_line12.id, order_line21.id) # creates new order_lines
      end

      it 'clones all the order lines for the order within the recurrent orders' do
        order_converter = Orders::ConvertToRecurrent.new(order: order.reload, recurrent_params: recurrent_params_with_order_lines).call
        expect(order_converter).to be_success

        recurrent_orders = order_converter.recurrent_orders
        recurrent_locations = Location.where(order: recurrent_orders)

        expect(recurrent_locations.map(&:details)).to include(location1.details, location2.details)
        expect(recurrent_locations.map(&:note)).to include(location1.note, location2.note)
        expect(recurrent_locations.map(&:id)).to_not include(location1.id, location2.id) # creates new locations
      end
    end

    context 'with empty locations' do
      let!(:empty_location1) { create(:location, :random, order: order) }
      let!(:empty_location2) { create(:location, :random, order: order) }

      let(:recurrent_params_copy_all) { recurrent_params.merge({ copy_all: true }) }

      it 'clones the empty locations for the order within the recurrent orders' do
        order_converter = Orders::ConvertToRecurrent.new(order: order.reload, recurrent_params: recurrent_params_copy_all).call
        expect(order_converter).to be_success

        recurrent_orders = order_converter.recurrent_orders
        recurrent_order_locations = Location.where(order: recurrent_orders)

        expect(recurrent_order_locations.map(&:details)).to include(empty_location1.details, empty_location2.details)
        expect(recurrent_order_locations.map(&:id)).to_not include(empty_location1.id, empty_location2.id) # creates new (empty) locations
      end
    end # with empty locations
  end # with valid params

  context 'errors' do

    it 'errors out if order is missing' do
      recurrent_params = { days: { mon: true, wed: true, fri: true }, frequency: 'weekly' }
      order_converter = Orders::ConvertToRecurrent.new(order: nil, recurrent_params: recurrent_params).call

      expect(order_converter).to_not be_success
    end

    it 'errors out if repeat days are not specified' do
      recurrent_params = { days: {}, frequency: 'weekly' }
      order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params).call

      expect(order_converter).to_not be_success
    end

    it 'errors out if frequency is missing' do
      recurrent_params = { days: { mon: true, wed: true, fri: true }, frequency: nil }
      order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: recurrent_params).call

      expect(order_converter).to_not be_success
    end

  end

end
