require 'rails_helper'

RSpec.describe Orders::CalculateTopup, type: :service, orders: true do

  context 'team order with supplier surcharge (top up)', team_orders: true do
    let!(:team_order) { create(:order, :draft, status: 'pending', order_variant: 'team_order') }
    let!(:team_order_supplier1) { create(:order_supplier, :random, order: team_order, surcharge: 100.2) }
    let!(:team_order_supplier2) { create(:order_supplier, :random, order: team_order, surcharge: 100.2) }
    let!(:team_order_detail) { create(:team_order_detail, :random, order: team_order, cutoff_option: 'charge_to_minimum') }

    it 'returns the supplier surcharge(topup)' do
      calculated_topup = Orders::CalculateTopup.new(order: team_order.reload).call

      expected_surcharge = team_order_supplier1.surcharge + team_order_supplier2.surcharge
      expect(calculated_topup.round(2)).to eq(expected_surcharge.round(2))
    end

    it 'returns a top up of 0 if team order cutoff option is not charge_to_minimum' do
      team_order_detail.update_column(:cutoff_option, [nil, 'cancel_order'].sample)
      calculated_topup = Orders::CalculateTopup.new(order: team_order.reload).call

      expect(calculated_topup).to eq(0)
    end
  end

  context 'with a charge_to_minimum order (non-team-order)' do
    let(:order) { create(:order, :draft, customer_total: 1000) }
    let!(:order_supplier1) { create(:order_supplier, :random, order: order, surcharge: 57.21) }
    let!(:order_supplier2) { create(:order_supplier, :random, order: order, surcharge: 10.22) }

    before do
      order.update_column(:charge_to_minimum, true)
    end

    it 'returns the supplier surcharge(topup)' do
      calculated_topup = Orders::CalculateTopup.new(order: order).call

      expected_surcharge = order_supplier1.surcharge + order_supplier2.surcharge
      expect(calculated_topup.round(2)).to eq(expected_surcharge.round(2))
    end

    it 'returns top up as 0 the order is not marked as charged to minimum' do
      order.update_column(:charge_to_minimum, false)
      calculated_topup = Orders::CalculateTopup.new(order: order).call

      expect(calculated_topup).to eq(0)
    end
  end

end
