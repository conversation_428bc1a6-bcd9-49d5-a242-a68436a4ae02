require 'rails_helper'

RSpec.describe Orders::GetSanitizedAddress, type: :service, orders: true do

  let!(:order) { create(:order, :delivered, delivery_address: '201 Sussex Street', delivery_address_level: 'Level 20') }
  subject { Orders::GetSanitizedAddress.new(order: order).call }

  it 'returns the saved level and address for a normal street address' do
    sanitized_address = subject

    expect(sanitized_address.level).to eq(order.delivery_address_level)
    expect(sanitized_address.street_address).to eq(order.delivery_address)
    expect(sanitized_address.label).to eq("#{order.delivery_address_level}, #{order.delivery_address}")
  end

  it 'returns the orders delivery instructions and delivery suburb' do
    sanitized_address = subject

    expect(sanitized_address.suburb).to eq(order.delivery_suburb)
    expect(sanitized_address.instructions).to eq(order.delivery_instruction)
  end

  it 'returns just the street address if order.delivery_address_level is blank' do
    order.update_column(:delivery_address_level, nil)
    sanitized_address = subject

    expect(sanitized_address.level).to be_blank
    expect(sanitized_address.street_address).to eq(order.delivery_address)
    expect(sanitized_address.label).to eq(order.delivery_address.to_s)
  end

  context 'with missing delivery_address level and street address containing level (or lvl)' do
    before do
      order.update_column(:delivery_address_level, nil)
    end

    it 'returns the level info from the street address (level info at start)' do
      order.update_column(:delivery_address, 'Level 14, 420 George St')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('Level 14')
      expect(sanitized_address.street_address).to eq('420 George St')
    end

    it 'returns the level info until last comma (,) from the street address - level info at start' do
      order.update_column(:delivery_address, 'Level 12, Tower 3, Darling Park 201 Sussex Street')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('Level 12 Tower 3')
      expect(sanitized_address.street_address).to eq('Darling Park 201 Sussex Street')
    end

    it 'returns the level info until last back-slash (/) from the street address - level info at start' do
      order.update_column(:delivery_address, 'level 27 / 161 Castelreagh St')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('level 27')
      expect(sanitized_address.street_address).to eq('161 Castelreagh St')
    end

    it 'returns the level info until last hyphen (-) from the street address - level info at start' do
      order.update_column(:delivery_address, 'Level 18 - 1 York Street')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('Level 18')
      expect(sanitized_address.street_address).to eq('1 York Street')
    end

    it 'returns level info from street address - containing suburb label' do
      suburb = create(:suburb, :random, name: 'Sydney', state: 'NSW', postcode: 2000)
      order.update_columns(delivery_suburb_id: suburb.id, delivery_address: 'Level 25/85 Castlereagh St, Sydney, NSW 2000')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('Level 25')
      expect(sanitized_address.street_address).to eq('85 Castlereagh St')
    end

    it 'returns level info from street address - level info after other info' do
      order.update_column(:delivery_address, 'Tower 3, Level 12,201 Sussex Street')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('Tower 3, Level 12')
      expect(sanitized_address.street_address).to eq('201 Sussex Street')
    end

    it 'sanitizes duplicate `level` info from street_address' do
      order.update_column(:delivery_address, 'Level level 2 , 101 Grenfell Street')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('level 2')
      expect(sanitized_address.street_address).to eq('101 Grenfell Street')
    end

    it 'returns level info from street address - if saved as lvl' do
      order.update_column(:delivery_address, 'Tower 3, lvl 12,201 Sussex Street')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('Tower 3, level 12')
      expect(sanitized_address.street_address).to eq('201 Sussex Street')
    end
  end

  context 'with exiting order.delivery_address_level and level info in street address' do
    it 'returns the saved delivery_address_level instead of level info from street_address if delivery address level contain level (or lvl)' do
      order.update_columns(delivery_address_level: 'Level 20', delivery_address: 'Level 14, 420 George St')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('Level 20')
      expect(sanitized_address.street_address).to eq('420 George St')
    end

    it 'returns the combined delivery_address_level and level info from street_address if delivery address level does not contain level (or lvl)' do
      order.update_columns(delivery_address_level: 'L20 Darling Tower', delivery_address: 'Level 14, 420 George St')

      sanitized_address = subject

      expect(sanitized_address.level).to eq('L20 Darling Tower Level 14')
      expect(sanitized_address.street_address).to eq('420 George St')
    end
  end

end
