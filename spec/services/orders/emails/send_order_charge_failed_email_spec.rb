require 'rails_helper'

RSpec.describe Orders::Emails::SendOrderChargeFailedEmail, type: :service, emails: true, stripe: true, orders: true do

  subject { Orders::Emails::SendOrderChargeFailedEmail.new(order: order, card_error: card_error).call }

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:credit_card) { create(:credit_card, :random, label: 'Card 1234') }
  let!(:order) { create(:order, :confirmed, customer_profile: customer, credit_card: credit_card, customer_total: 200.32) }
  let!(:card_error) do
    OpenStruct.new(
      message: 'Your card was declined.',
      code: 'card_declined',
      decline_code: 'insufficient_funds'
    )
  end
  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :accounts_email).and_return('accounts-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :developer_email).and_return('developer-email')
  end

  it 'returns the sent email' do
    sent_email = subject

    expect(sent_email).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Orders::Emails::SendOrderChargeFailedEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything
      )
    subject
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "YORDAR: FAILED On Hold Charge for order ##{order.id}",
        cc: anything,
        email_options: anything,
        email_variables: anything
      )

    subject
  end

  it 'sends the email to the correct recipients (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: 'orders-email',
      subject: anything,
      cc: 'accounts-email, developer-email',
      email_options: anything,
      email_variables: anything
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{Orders::Emails::SendOrderChargeFailedEmail::EMAIL_TEMPLATE}-#{order.id}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: 0, ref: email_ref },
        email_variables: anything
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct order data' do
      expected_order_data = {
        id: order.id,
        name: order.name,
        status: order.status,
        delivery_datetime: order.delivery_at.to_s(:full),
        card_in_use: credit_card.last4,
        total: '$200.32',
        supplier_names: []
      }
      expected_email_variables = {
        order: deep_struct(expected_order_data),
        error: anything,
        customer: anything,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end

    it 'sends email with the correct error data' do
      expected_error_data = {
        message: card_error.message,
        code: card_error.code.titleize,
        decline_code: card_error.decline_code.titleize,
        is_fraudulent: false,
      }
      expected_email_variables = {
        order: anything,
        error: deep_struct(expected_error_data),
        customer: anything,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end

    it 'sends email with the correct customer data' do
      expected_customer_data = {
        name: customer.customer_name,
        email: customer.email,
        phones: [customer.contact_phone],
        pending_orders_count: 0
      }
      expected_email_variables = {
        order: anything,
        error: anything,
        customer: deep_struct(expected_customer_data),
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end
  end

  context 'with a fradulent card error' do
    let!(:card_error) do
      OpenStruct.new(
        message: 'Your card was declined.',
        code: 'card_declined',
        decline_code: 'fraudulent'
      )
    end

    it 'sends email with FRAUD DETECTED added to the email subject' do
      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: "YORDAR: FAILED On Hold Charge for order ##{order.id} - FRAUD DETECTED",
          cc: anything,
          email_options: anything,
          email_variables: anything
        )

      subject
    end

    it 'sends email with the correct error data (with is_fraudulent)' do
      expected_error_data = {
        message: card_error.message,
        code: card_error.code.titleize,
        decline_code: card_error.decline_code.titleize,
        is_fraudulent: true,
      }
      expected_email_variables = {
        order: anything,
        error: deep_struct(expected_error_data),
        customer: anything,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end
  end # with fraudulent card error

  context 'errors' do
    it 'doesn\'t send email if order is missing' do
      expect(Emails::Send).to_not receive(:new)

      Orders::Emails::SendOrderChargeFailedEmail.new(order: nil, card_error: card_error).call
    end

    it 'doesn\'t send email if card error is missing' do
      expect(Emails::Send).to_not receive(:new)

      Orders::Emails::SendOrderChargeFailedEmail.new(order: order, card_error: nil).call
    end

    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Orders::Emails::SendOrderChargeFailedEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        failed_order_charge_email_sender = Orders::Emails::SendOrderChargeFailedEmail.new(order: order, card_error: card_error)

        expected_error_message = "Failed to send order charge failed email to Yordar Admin orders-email"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(failed_order_charge_email_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

        failed_order_charge_email_sender.call
      end
    end # email sender error
  end

end