require 'rails_helper'

RSpec.describe Orders::HandleQuoteOrder, type: :service, orders: true, quote: true do

  let!(:customer) { create(:customer_profile, :with_user) }
  let!(:order) { create(:order, :new, status: 'quoted', customer_profile: customer) }
  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier) } # needs order line for approval

  let!(:md5_hash) { Digest::MD5.hexdigest(order.id.to_s + customer.id.to_s + yordar_credentials(:random_salt)) }
  let!(:handle_params) do
    {
      order_id: order.id,
      profile_id: customer.id,
      mode: Orders::HandleQuoteOrder::VALID_MODES.sample,
      hashed_value: md5_hash,
    }
  end

  before do
    # mock order submitter
    order_submitter = double(Orders::Submit)
    allow(Orders::Submit).to receive(:new).and_return(order_submitter)
    submit_response = OpenStruct.new(success?: true, order: order)
    allow(order_submitter).to receive(:call).and_return(submit_response)

    # mock order rejector (cancel)
    order_rejector = double(Orders::Cancel)
    allow(Orders::Cancel).to receive(:new).and_return(order_rejector)
    reject_response = OpenStruct.new(success?: true, order: order)
    allow(order_rejector).to receive(:call).and_return(reject_response)

    # mock email sender
    email_sender = delayed_email_sender = double(Admin::Emails::SendRejectedOrderQuoteEmail)
    allow(Admin::Emails::SendRejectedOrderQuoteEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  context 'editing' do
    let!(:edit_handle_params) do
      handle_params.merge({ mode: 'edit' })
    end

    it 'neither submits nor cancels the quoted order' do
      expect(Orders::Submit).to_not receive(:new)
      expect(Orders::Cancel).to_not receive(:new)

      quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: edit_handle_params).call

      expect(quote_order_handler).to be_success
    end

    it 'returns redirects_to as edit page (no warnings)' do
      quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: edit_handle_params).call

      expect(quote_order_handler).to be_success
      expect(quote_order_handler.redirect_to).to eq(:edit_page)
      expect(quote_order_handler.warnings).to be_blank
    end
  end

  context 'approval' do
    let!(:approve_handle_params) do
      handle_params.merge({ mode: 'approve' })
    end

    context 'mode = approve' do
      it 'does not submit a quoted order' do
        expect(Orders::Submit).to_not receive(:new)

        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: approve_handle_params).call

        expect(quote_order_handler).to be_success
      end

      it 'returns redirects_to as approve page (edit page with finaliseQuote=true) (with no warnings)' do
        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: approve_handle_params).call

        expect(quote_order_handler).to be_success
        expect(quote_order_handler.redirect_to).to eq(:approve_page)
        expect(quote_order_handler.warnings).to be_blank
      end
    end

    context 'forced approve' do
      it 'submits a quoted order' do
        expect(Orders::Submit).to receive(:new).with(order: order, customer: customer)

        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: approve_handle_params, forced: true).call
        expect(quote_order_handler).to be_success
      end

      it 'doesn\'t submit a quote order wihtout an attached credit card (even if forced)' do
        order.update_column(:credit_card_id, nil)
        expect(Orders::Submit).to_not receive(:new)

        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: approve_handle_params, forced: true).call

        expect(quote_order_handler).to be_success
      end

      it 'returns a redirect to show page' do
        forced_quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: approve_handle_params, forced: true).call

        expect(forced_quote_order_handler).to be_success
        expect(forced_quote_order_handler.redirect_to).to eq(:show_page)
      end

      it 'sends customer and supplier(s) new order emails', skip: 'Already tested in Orders::Submit' do
      end
    end # forced approve
  end # with approve handle params

  context 'rejection' do
    let!(:reject_handle_params) do
      handle_params.merge({ mode: 'reject' })
    end

    context 'mode == reject' do
      it 'does not make a request to cancel the order' do
        expect(Orders::Cancel).to_not receive(:new)
        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: reject_handle_params).call

        expect(quote_order_handler).to be_success
      end

      it 'returns with a redirect to the show page with warnings' do
        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: reject_handle_params).call

        expect(quote_order_handler).to be_success
        expect(quote_order_handler.redirect_to).to eq(:show_page)
        expect(quote_order_handler.warnings).to include('We\'ve notified Yordar Admin about the quote being rejected')
      end

      it 'emails Yordar Admin about the quote being rejected' do
        expect(Admin::Emails::SendRejectedOrderQuoteEmail).to receive(:new).with(order: order)

        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: reject_handle_params).call
        expect(quote_order_handler).to be_success
      end
    end

    context 'forced reject' do
      it 'request the order to be cancelled' do
        expect(Orders::Cancel).to receive(:new).with(order: order, mode: 'one-off')

        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: reject_handle_params, forced: true).call
        expect(quote_order_handler).to be_success
      end

      it 'returns redirects_to as show page' do
        quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: reject_handle_params, forced: true).call

        expect(quote_order_handler).to be_success
        expect(quote_order_handler.redirect_to).to eq(:show_page)
      end

      it 'does not send a supplier cancelation email', skip: 'Already tested in Orders::Cancel' do
      end
    end
  end

  context 'errors' do
    it 'errors out if the passed in hash does not match the calculated hash' do
      invalid_hash_params = handle_params.merge({ hashed_value: 'invalid-hash' })
      quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: invalid_hash_params).call

      expect(quote_order_handler).to_not be_success
      expect(quote_order_handler.errors).to include('Invalid Request')
      expect(quote_order_handler.redirect_to).to be_nil
    end

    it 'errors out if the passed in mode is invalid' do
      invalid_mode_params = handle_params.merge({ mode: 'invalid-mode' })
      quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: invalid_mode_params).call

      expect(quote_order_handler).to_not be_success
      expect(quote_order_handler.errors).to include('We don\'t know what you mean to do with the order. action => invalid-mode')
      expect(quote_order_handler.redirect_to).to be_nil
    end
  end

  context 'for a non-quoted order' do
    before do
      order.update_column(:status, (Order::VALID_ORDER_STATUSES - ['quoted']).sample)
    end

    it 'returns with a warning' do
      quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: handle_params).call

      expect(quote_order_handler).to be_success # still successfull
      action = handle_params[:mode] == 'approve' ? 'approved' : 'rejected'
      expect(quote_order_handler.warnings).to include("This order can no longer be #{action}, as it's not a quoted order")
    end

    it 'returns a redirect to show page' do
      quote_order_handler = Orders::HandleQuoteOrder.new(handle_params: handle_params).call

      expect(quote_order_handler).to be_success
      expect(quote_order_handler.redirect_to).to eq(:show_page)
    end
  end

end
