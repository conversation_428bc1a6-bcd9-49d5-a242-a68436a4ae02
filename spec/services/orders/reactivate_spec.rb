require 'rails_helper'

RSpec.describe Orders::Cancel, type: :service, orders: true do

  let!(:supplier1) { create(:supplier_profile, :random, company_name: 'supplier1') }
  let!(:supplier2) { create(:supplier_profile, :random, company_name: 'supplier2') }
  let!(:email_sender) { double(Suppliers::Emails::SendOrderReactivatedEmail) }
  let!(:delayed_email_sender) { double(Suppliers::Emails::SendOrderReactivatedEmail) }

  before do
    # mock that the email will be sent
    allow(Suppliers::Emails::SendOrderReactivatedEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call)
  end

  context 'recurring orders' do
    # root order (Monday)
    let!(:order1) { create(:order, :confirmed, status: 'paused', name: 'order1', order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: %w[mon wed] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-09 12:00:00')) }
    let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
    let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier2) }

    # Monday orders
    let!(:order2) { create(:order, :confirmed, status: 'paused', name: 'order2', recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-16 12:00:00')) }
    let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }
    let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

    let!(:order3) { create(:order, :confirmed, status: 'paused', name: 'order3', recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-23 12:00:00')) }
    let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }
    let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }

    let!(:order4) { create(:order, :confirmed, status: 'paused', name: 'order4', recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-30 12:00:00')) }
    let!(:order_line41) { create(:order_line, :random, order: order4, supplier_profile: supplier1) }
    let!(:order_line42) { create(:order_line, :random, order: order4, supplier_profile: supplier2) }

    let!(:order5) { create(:order, :confirmed, status: 'paused', name: 'order5', recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-04-06 12:00:00')) }
    let!(:order_line51) { create(:order_line, :random, order: order5, supplier_profile: supplier1) }
    let!(:order_line52) { create(:order_line, :random, order: order5, supplier_profile: supplier2) }

    # Wednesday orders
    let!(:order6) { create(:order, :confirmed, status: 'paused', name: 'order6', recurrent_id: order1.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-11 12:00:00')) }
    let!(:order_line61) { create(:order_line, :random, order: order6, supplier_profile: supplier1) }
    let!(:order_line62) { create(:order_line, :random, order: order6, supplier_profile: supplier2) }

    let!(:order7) { create(:order, :confirmed, status: 'paused', name: 'order7', recurrent_id: order1.id, template_id: order6.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-18 12:00:00')) }
    let!(:order_line71) { create(:order_line, :random, order: order7, supplier_profile: supplier1) }
    let!(:order_line72) { create(:order_line, :random, order: order7, supplier_profile: supplier2) }

    let!(:order8) { create(:order, :confirmed, status: 'paused', name: 'order8', recurrent_id: order1.id, template_id: order6.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-03-25 12:00:00')) }
    let!(:order_line81) { create(:order_line, :random, order: order8, supplier_profile: supplier1) }
    let!(:order_line82) { create(:order_line, :random, order: order8, supplier_profile: supplier2) }

    let!(:order9) { create(:order, :confirmed, status: 'paused', name: 'order9', recurrent_id: order1.id, template_id: order6.id, order_type: 'recurrent', pattern: '1.week', recurring_order_params: { days: ['mon'] }, customer_total: 0, customer_surcharge: 0, delivery_at: Time.zone.parse('2020-04-01 12:00:00')) }
    let!(:order_line91) { create(:order_line, :random, order: order9, supplier_profile: supplier1) }
    let!(:order_line92) { create(:order_line, :random, order: order9, supplier_profile: supplier2) }

    before do
      order1.update_columns(recurrent_id: order1.id, template_id: order1.id)
      order6.update_columns(template_id: order6.id)
    end

    context 'one-off reactivation' do
      it 'reactivates the specified order' do
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'one-off').call

        expect(order_reactivator).to be_success
        expect(order_reactivator.reactivated_orders).to include(order3)
        expect(order3.status).to eq('confirmed')
      end

      it 'accepts order lines of all subsequent orders' do
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'one-off').call

        expect(order_reactivator).to be_success
        expect(order3.order_lines.map(&:status).uniq).to eq(['accepted'])
      end

      it 'sets the order as a one-off' do
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'one-off').call

        expect(order_reactivator).to be_success
        expect(order3.order_type).to eq('one-off')
      end

      it 'does not reactivate any non-specified order' do
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'one-off').call

        expect(order_reactivator).to be_success
        expect(order_reactivator.reactivated_orders).to_not include(order1, order2, order4, order5, order6, order7, order8, order9)
        [order1, order2, order4, order5, order6, order7, order8, order9].each(&:reload).each do |order|
          expect(order.status).to_not eq('confirmed')
        end
      end

      it 'sends an order reactivated email to the order suppliers' do
        expect(Suppliers::Emails::SendOrderReactivatedEmail).to receive(:new).with(mode: 'one-off', supplier: supplier1, orders: [order3]).and_return(email_sender)
        expect(Suppliers::Emails::SendOrderReactivatedEmail).to receive(:new).with(mode: 'one-off', supplier: supplier2, orders: [order3]).and_return(email_sender)

        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'one-off').call
        expect(order_reactivator).to be_success
      end

      it 'updates the recurrent change if the template itself is reactivated' do
        order_reactivator = Orders::Reactivate.new(order: order6, mode: 'one-off').call

        expect(order_reactivator).to be_success
        expect(order6.status).to eq('confirmed')
        expected_tempalte_id = [order7, order8, order9].map(&:id).min
        [order7, order8, order9].each(&:reload).each do |order|
          expect(order.status).to_not eq('confirmed')
          expect(order.template_id).to eq(expected_tempalte_id)
        end
      end
    end

    context 'reactivate subsequent' do

      it 'reactivates all subsequent orders' do
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'subsequent').call

        expect(order_reactivator).to be_success
        expect(order_reactivator.reactivated_orders).to include(order3, order4, order5)
        [order3, order4, order5].each(&:reload).each do |order|
          expect(order.status).to eq('confirmed')
          expect(order.template_id).to eq(order3.id)
        end
      end

      it 'accepts order lines of all subsequent orders' do
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'subsequent').call

        expect(order_reactivator).to be_success
        [order3, order4, order5].each(&:reload).each do |order|
          expect(order.order_lines.map(&:status).uniq).to eq(['accepted'])
        end
      end

      it 'does not reactivate any past orders' do
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'subsequent').call

        expect(order_reactivator).to be_success
        expect(order_reactivator.reactivated_orders).to_not include(order1, order2)
        [order1, order2].each(&:reload).each do |order|
          expect(order.status).to_not eq('confirmed')
          expect(order.template_id).to eq(order1.id)
        end
      end

      it 'does not reactivate any subsequent related orders' do
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'subsequent').call

        expect(order_reactivator).to be_success
        expect(order_reactivator.reactivated_orders).to_not include(order6, order7, order8, order9)
        [order6, order7, order8, order9].each(&:reload).each do |order|
          expect(order.status).to_not eq('confirmed')
          expect(order.template_id).to eq(order6.id)
        end
      end

      it 'does not reactivate any subsequent non-paused orders' do
        order4.update_column(:status, 'cancelled')
        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'subsequent').call

        expect(order_reactivator).to be_success
        order4.reload
        expect(order4.status).to_not eq('confirmed')
        expect(order4.template_id).to eq(order3.id) # template is changed
      end

      it 'sends an order reactivated email to the order suppliers' do
        expect(Suppliers::Emails::SendOrderReactivatedEmail).to receive(:new).with(mode: 'subsequent', supplier: supplier1, orders: [order3, order4, order5].sort_by(&:id)).and_return(email_sender)
        expect(Suppliers::Emails::SendOrderReactivatedEmail).to receive(:new).with(mode: 'subsequent', supplier: supplier2, orders: [order3, order4, order5].sort_by(&:id)).and_return(email_sender)

        order_reactivator = Orders::Reactivate.new(order: order3, mode: 'subsequent').call
        expect(order_reactivator).to be_success
      end
    end
  end
end
