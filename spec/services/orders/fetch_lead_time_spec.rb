require 'rails_helper'

RSpec.describe Orders::FetchLeadTime, type: :service, suppliers: true, orders: true do

  let!(:catering_category) { create(:category, :random, group: 'catering-services') }
  let!(:kitchen_category) { create(:category, :random, group: 'kitchen-supplies') }
  let!(:delivery_time) { Time.zone.parse('2020-08-05 13:00:00') } # Wednesday

  let!(:order) { create(:order, :draft, delivery_at: delivery_time) }

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1, category: nil) }

  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2, category: nil) }

  context 'without supplier minimums' do
    it 'returns the delivery time as lead time with no supplier minimums' do
      lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call

      expect(lead_time_fetcher.lead_time).to eq(order.delivery_at)
      expect(lead_time_fetcher.lead_time_hours).to eq(0.0)
    end

    it 'return the weekday before delivery time if delivery time is on a weekend' do
      saturday = 3
      sunday = 4
      days_to_weekend = [saturday, sunday].sample
      order.update_column(:delivery_at, delivery_time + days_to_weekend.days)
      lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call

      expect(lead_time_fetcher.lead_time).to eq(delivery_time + 2.days) # Friday
      case days_to_weekend
      when saturday
        expect(lead_time_fetcher.lead_time_hours).to eq(24.0)
      when sunday
        expect(lead_time_fetcher.lead_time_hours).to eq(48.0)
      end
    end

    it 'return the weekday before delivery time if delivery time is on a (public) holiday' do
      create(:holiday, :random, :public_holiday, on_date: delivery_time.to_date)
      lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call

      expect(lead_time_fetcher.lead_time).to eq(delivery_time - 1.days)
      expect(lead_time_fetcher.lead_time_hours).to eq(24.0)
    end
  end

  context 'supplier minimums with lead times' do
    let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 24.0, lead_time_day_before: nil, category: catering_category) }
    let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 30.0, lead_time_day_before: nil, category: catering_category) }

    let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 12.0, lead_time_day_before: nil, category: catering_category) }
    let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 27.0, lead_time_day_before: nil, category: catering_category) }

    it 'returns lead time as the maximum lead time from the supplier minimums (irrespective of category)' do
      if [true, false]
        minimum12.update_column(:category_id, kitchen_category.id)
      end
      lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call

      expect(lead_time_fetcher.lead_time_hours).to eq(30.0)
      expect(lead_time_fetcher.lead_time).to eq(delivery_time - 30.hours) # picks minimum12
    end
  end

  context 'supplier minimums with lead_time_day_before' do
    let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: nil, lead_time_day_before: '13:15', category: catering_category) }
    let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time: nil, lead_time_day_before: '15:30', category: catering_category) }

    let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time: nil, lead_time_day_before: '14:20', category: catering_category) }
    let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time: nil, lead_time_day_before: '12:45', category: catering_category) }

    it 'returns lead time the day before delivery time with earliest time (irrespective of category group)' do
      if [true, false]
        minimum12.update_column(:category_id, kitchen_category.id)
      end
      lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call

      expect(lead_time_fetcher.lead_time).to eq(Time.zone.parse('2020-08-04 12:45:00')) # picks up minimum12
      expect(lead_time_fetcher.lead_time_hours).to eq(24.25)
    end
  end

  context 'supplier minimums with lead_time_day_before and lead times' do
    let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 24.0, lead_time_day_before: nil, category: catering_category) }
    let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time: nil, lead_time_day_before: '12:45', category: catering_category) }

    let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time: nil, lead_time_day_before: '09:30', category: catering_category) }
    let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 30.0, lead_time_day_before: nil, category: catering_category) }

    it 'returns lead time the day before delivery time with max time (irrespective of category)' do
      if [true, false]
        minimum22.update_column(:category_id, kitchen_category.id)
      end
      lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call

      expect(lead_time_fetcher.lead_time).to eq(delivery_time - minimum22.lead_time.hours) # picks minimum22
      expect(lead_time_fetcher.lead_time_hours).to eq(30.0)
    end

    context 'with order_line categories' do
      let!(:catering_category2) { create(:category, :random, group: 'catering-services') }
      let!(:minimum13) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 7.0, lead_time_day_before: nil, category: catering_category2) }
      let!(:minimum23) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 5.0, lead_time_day_before: nil, category: catering_category2) }

      before do
        minimum22.update_column(:category_id, kitchen_category.id) # make minimum a kitchen category minimum

        [order_line1, order_line2].each do |order_line|
          order_line.update_column(:category_id, catering_category2.id) # make order lines belong to catering category 2
        end
      end

      it 'returns lead time based on the minimums belonging to order line categories' do
        lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call

        expect(lead_time_fetcher.lead_time).to eq(delivery_time - minimum13.lead_time.to_i.hours) # picks minimum13
        expect(lead_time_fetcher.lead_time_hours.round(2)).to eq(7.0)
      end

      it 'returns lead time based on the minimums belonging to order line\'s category group (if category minimums are missing)' do
        catering_category3 = create(:category, :random, group: 'catering-services')
        [order_line1, order_line2].each do |order_line|
          order_line.update_column(:category_id, catering_category3.id) # make order lines belong to catering category 3
        end
        lead_time_fetcher = Orders::FetchLeadTime.new(order: order).call

        expect(lead_time_fetcher.lead_time).to eq(Time.zone.parse('2020-08-04 09:30:00')) # picks minimum21
        expect(lead_time_fetcher.lead_time_hours.round(2)).to eq(27.5)
      end
    end
  end

  context 'as a team order', team_orders: true do
    let!(:team_order) { create(:order, :draft, status: 'pending', order_variant: 'team_order', delivery_at: delivery_time) }

    let!(:order_supplier1) { create(:order_supplier, :random, order: team_order, supplier_profile: supplier1) }
    let!(:order_supplier2) { create(:order_supplier, :random, order: team_order, supplier_profile: supplier2) }

    context 'supplier minimums with lead times' do
      let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 24.0, lead_time_day_before: nil, category: catering_category) }
      let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 30.0, lead_time_day_before: nil, category: kitchen_category) }

      let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 12.0, lead_time_day_before: nil, category: catering_category) }
      let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 27.0, lead_time_day_before: nil, category: catering_category) }

      it 'returns lead time as the maximum lead time from the (order) supplier minimums only belonging to catering services or none' do
        lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call

        expect(lead_time_fetcher.lead_time_hours).to eq(27.0)
        expect(lead_time_fetcher.lead_time).to eq(delivery_time - 27.hours) # picks minimum22
      end
    end

    context 'supplier minimums with lead_time_day_before' do
      let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: nil, lead_time_day_before: '13:15', category: catering_category) }
      let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time: nil, lead_time_day_before: '15:30', category: kitchen_category) }

      let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time: nil, lead_time_day_before: '14:20', category: catering_category) }
      let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time: nil, lead_time_day_before: '12:45', category: catering_category) }

      it 'returns lead time the day before delivery time with max time from the (order) supplier minimums only belonging to catering services or none' do
        lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call

        expect(lead_time_fetcher.lead_time).to eq(Time.zone.parse('2020-08-04 12:45:00')) # picks up minimum22
        expect(lead_time_fetcher.lead_time_hours.round(2)).to eq(24.25)
      end
    end

    context 'supplier minimums with lead_time_day_before and lead times' do
      let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 24.0, lead_time_day_before: nil, category: catering_category) }
      let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time: nil, lead_time_day_before: '12:45', category: catering_category) }

      let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time: nil, lead_time_day_before: '09:30', category: kitchen_category) }
      let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 27.0, lead_time_day_before: nil, category: kitchen_category) }

      it 'returns lead time the day before delivery time with max time from the (order) supplier minimums only belonging to catering services or none' do
        lead_time_fetcher = Orders::FetchLeadTime.new(order: team_order).call

        expect(lead_time_fetcher.lead_time).to eq(Time.zone.parse('2020-08-04 09:30:00')) # picks minimum21
        expect(lead_time_fetcher.lead_time_hours.round(2)).to eq(27.5)
      end
    end
  end # as team order
end
