require 'rails_helper'

RSpec.describe Orders::ListForSuppliers, type: :service, orders: true, suppliers: true do

  let!(:now) { Time.zone.now }

  # list future orders
  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:order11) { create(:order, :confirmed, delivery_at: now + 1.days) }
  let!(:order_line11) { create(:order_line, :random, order: order11, supplier_profile: supplier1) }

  let!(:order12) { create(:order, :confirmed, delivery_at: now + 1.week + 1.days) }
  let!(:order_line12) { create(:order_line, :random, order: order12, supplier_profile: supplier1) }

  let!(:order13) { create(:order, :confirmed, delivery_at: now + 1.month + 1.days) }
  let!(:order_line13) { create(:order_line, :random, order: order13, supplier_profile: supplier1) }

  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:order21) { create(:order, :confirmed, delivery_at: now + 2.days) }
  let!(:order_line21) { create(:order_line, :random, order: order21, supplier_profile: supplier2) }

  let!(:order22) { create(:order, :confirmed, delivery_at: now + 1.week + 2.days) }
  let!(:order_line22) { create(:order_line, :random, order: order22, supplier_profile: supplier2) }

  let!(:order23) { create(:order, :confirmed, delivery_at: now + 1.month + 2.days) }
  let!(:order_line23) { create(:order_line, :random, order: order23, supplier_profile: supplier2) }

  let(:all_orders) { [order11, order12, order13, order21, order22, order23] }

  it 'lists orders for a passed in duration' do
    lister_options_with_duration = { for_duration: 'week' }
    order_lister = Orders::ListForSuppliers.new(options: lister_options_with_duration).call

    expect(order_lister.orders).to include(order11, order21)
    expect(order_lister.orders).to_not include(order12, order13, order22, order23)

    lister_options_with_duration = { for_duration: 'fortnight' }
    order_lister = Orders::ListForSuppliers.new(options: lister_options_with_duration).call

    expect(order_lister.orders).to include(order11, order12, order21, order22)
    expect(order_lister.orders).to_not include(order13, order23)
  end

  it 'does not list non-accepted orders with status = draft/cancelled/skipped/paused' do
    [order11, order23].each do |order|
      order.update_column(:status, %w[draft cancelled skipped paused].sample)
    end

    lister_options = { list_type: %w[pending upcoming past].sample }
    order_lister = Orders::ListForSuppliers.new(options: lister_options).call

    expect(order_lister.orders).to_not include(order11, order23)
  end

  it 'lists orders for a passed in supplier' do
    lister_options_with_supplier = { for_supplier: supplier1 }
    order_lister = Orders::ListForSuppliers.new(options: lister_options_with_supplier).call

    expect(order_lister.orders).to include(order11, order12, order13)
    expect(order_lister.orders).to_not include(order21, order22, order23)
  end

  context 'pending orders - orders in future with non-accepted order lines' do
    let!(:lister_options) { { list_type: 'pending' } }

    it 'lists all pending orders' do
      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      expect(order_lister.orders).to include(order11, order12, order13, order21, order22, order23)
    end

    it 'it only lists orders with status = pending/new/amended/confirmed' do
      [order12, order22].each do |order|
        order.update_column(:status, %w[new amended confirmed].sample)
      end

      [order13, order21].each do |order|
        order.update_column(:status, 'pending')
      end

      [order11, order23].each do |order|
        order.update_column(:status, %w[draft cancelled skipped paused].sample)
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      expect(order_lister.orders).to_not include(order11, order23)

      expect(order_lister.orders).to include(order12, order22)
      expect(order_lister.orders).to include(order13, order21) # even pending team orders
    end

    it 'sorts the orders by ascending delivery at value' do
      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      delivery_sorted_orders = all_orders.sort_by{|order| order.delivery_at.to_i }
      order_lister.orders.each_with_index do |order, idx|
        expect(order).to eq(delivery_sorted_orders[idx])
      end
    end

    it 'lists pending orders in the last month' do
      [order11, order22].each do |order|
        order.update_column(:delivery_at, (now - 1.month + 2.days))
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to include(order11, order22)
    end

    it 'does not list pending orders past the last month' do
      [order13, order21].each do |order|
        order.update_column(:delivery_at, (now - 1.month - 3.days))
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to_not include(order13, order21)
    end

    it 'does not list orders where the order lines are accepted / rejected' do
      [order_line12, order_line23].each do |order_line|
        order_line.update_column(:status, %w[accepted rejected].sample)
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to_not include(order12, order23)
    end

    it 'lists orders with at least 1 order line as non-accepted' do
      _order_line232 = create(:order_line, :random, order: order23, supplier_profile: supplier2, status: 'accepted')
      # _order23 has 1 order line as not-accepted

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to include(order23)
    end
  end

  context 'upcoming orders - orders in future with accepted/rejected orders lines' do
    let!(:lister_options) { { list_type: 'upcoming' } }

    before do
      [order_line11, order_line12, order_line13, order_line21, order_line22, order_line23].each do |order_line|
        order_line.update_column(:status, 'accepted')
      end
    end

    it 'lists all orders with accepted orderlines' do
      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      expect(order_lister.orders).to include(order11, order12, order13, order21, order22, order23)
    end

    it 'sorts the orders by ascending delivery at value' do
      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      delivery_sorted_orders = all_orders.sort_by{|order| order.delivery_at.to_i }
      order_lister.orders.each_with_index do |order, idx|
        expect(order).to eq(delivery_sorted_orders[idx])
      end
    end

    it 'lists orders to be delivered on the current day but past Time.now' do
      [order11, order22].each do |order|
        order.update_column(:delivery_at, now.beginning_of_day + 2.seconds)
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to include(order11, order22)
    end

    it 'does not list orders past the current day' do
      [order13, order21].each do |order|
        order.update_column(:delivery_at, now.beginning_of_day - 5.seconds)
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to_not include(order13, order21)
    end

    it 'does not list orders where the order lines are not accepted' do
      [order_line12, order_line23].each do |order_line|
        order_line.update_column(:status, %w[pending rejected amended notified].sample)
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to_not include(order12, order23)
    end

    context 'retrieving more orders' do
      before do
        all_orders.each do |order|
          order.update_column(:delivery_at, now + [2, 3].sample.weeks)
        end
      end

      it 'retrieves a month worth of orders when there are no orders within the week' do
        lister_options_with_duration = { list_type: 'upcoming', for_duration: 'week' }
        order_lister = Orders::ListForSuppliers.new(options: lister_options_with_duration).call

        expect(order_lister.orders).to include(*all_orders)
        expect(order_lister.lister_options[:for_duration]).to eq('month')
      end
    end
  end

  context 'past orders - deliveries in the past' do
    let!(:lister_options) { { list_type: 'past' } }

    before do
      all_orders.each_with_index do |order, idx|
        order.update_column(:status, 'delivered')
        order.update_column(:delivery_at, now - (idx + 1).days)
      end
    end

    it 'lists all past delivered orders' do
      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      expect(order_lister.orders).to include(order11, order12, order13, order21, order22, order23)
    end

    it 'sorts the orders by descending delivery at value' do
      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      delivery_sorted_orders = all_orders.sort_by{|order| -order.delivery_at.to_i }
      order_lister.orders.each_with_index do |order, idx|
        expect(order).to eq(delivery_sorted_orders[idx])
      end
    end

    it 'does not list non- amended, confirmed or delivered orders past orders' do
      [order12, order23].each do |order|
        order.update_column(:status, %w[new pending rejected notified].sample)
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to_not include(order12, order23)
    end

    it 'does not list orders delivered in the future' do
      [order13, order21].each do |order|
        order.update_column(:delivery_at, now + 2.days)
      end

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to_not include(order13, order21)
    end
  end

  context 'with invalid list type' do
    let!(:lister_options) { { list_type: 'invalid' } }

    it 'returns an empty list' do
      order_lister = Orders::ListForSuppliers.new(options: lister_options).call
      expect(order_lister.orders).to be_blank
    end

  end

  context 'with a date' do
    it 'returns orders delivered within the passed in date' do
      delivery_date = (now + 1.month + 1.days).to_date
      lister_options = { date: [delivery_date, delivery_date.to_s].sample }

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      expect(order_lister.orders).to include(order13)
      expect(order_lister.orders).to_not include(order11, order12, order21, order22, order23)
    end

    it 'does not return orders which are not active' do # inactive = status in 'draft', paused', 'skipped', 'cancelled'
      order22.update_column(:status, %w[draft paused skipped cancelled].sample)
      delivery_date = (now + 1.week + 2.days).to_date
      lister_options = { date: [delivery_date, delivery_date.to_s].sample }

      order_lister = Orders::ListForSuppliers.new(options: lister_options).call

      expect(order_lister.orders).to_not include(order22) # not active
      expect(order_lister.orders).to_not include(order11, order12, order13, order21, order23) # not delivered on date
    end
  end

end
