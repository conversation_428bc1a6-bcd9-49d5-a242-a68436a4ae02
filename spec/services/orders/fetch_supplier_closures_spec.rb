require 'rails_helper'

RSpec.describe Orders::FetchSupplierClosures, type: :service, orders: true, suppliers: true, closure_dates: true do

  let!(:last_year) { Time.zone.now - 1.year }
  let!(:closure_from) { (last_year.end_of_year - 8.days).beginning_of_week + 1.day } # set closure from Monday (for recurring order check)
  let!(:closure_to) { (last_year.end_of_year + 6.days).end_of_day }

  let(:supplier1) { create(:supplier_profile, :random) }
  let(:supplier2) { create(:supplier_profile, :random) }
  let(:suburb) { create(:suburb, :random) }

  let!(:closure_date_kind) { %i[saved associated].sample }

  before do
    case closure_date_kind
    when :saved # with closure dates saved within supplier record
      [supplier1, supplier2].each do |supplier|
        supplier.update_columns(close_from: closure_from, close_to: closure_to)
      end
    when :associated # with closure dates saved as associated records
      [supplier1, supplier2].each do |supplier|
        supplier.update_columns(close_from: nil, close_to: nil)
      end
      @supplier_closure1 = create(:supplier_closure, :random, supplier_profile: supplier1, starts_at: closure_from, ends_at: closure_to)
      @supplier_closure2 = create(:supplier_closure, :random, supplier_profile: supplier2, starts_at: closure_from, ends_at: closure_to)
    end
  end

  context 'for a one-off order' do
    let(:order) { create(:order, :draft) }
    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

    it 'return without any closures if delivery date is outside closure times' do
      new_delivery_at = closure_from - 1.day
      supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at).call

      expect(supplier_closure_fetcher.has_closures?).to be_falsey
      expect(supplier_closure_fetcher.closures).to be_empty
    end

    it 'returns with closures if delivery date is within closure times' do
      new_delivery_at = closure_from + 1.day
      supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at).call

      expect(supplier_closure_fetcher.has_closures?).to be_truthy
      supplier_closures = supplier_closure_fetcher.closures

      expect(supplier_closures).to_not be_empty
      expect(supplier_closures.map(&:supplier)).to include(supplier1, supplier2)
    end

    it 'returns the appropriate closure periods' do
      new_delivery_at = closure_from + 1.day
      supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at).call

      expect(supplier_closure_fetcher.has_closures?).to be_truthy
      supplier_closures = supplier_closure_fetcher.closures

      expect(supplier_closures.sample).to be_a(Orders::FetchSupplierClosures::Closure) # returns as a Closure Object
      expect(supplier_closures.map{|closure| closure.close_from.to_s }).to include(closure_from.to_s)
      expect(supplier_closures.map{|closure| closure.close_to.to_s }).to include(closure_to.to_s)
    end

    it 'only gets the closure dates for order suppliers' do
      order_line2.destroy

      new_delivery_at = closure_from + 1.day
      supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at).call

      expect(supplier_closure_fetcher.has_closures?).to be_truthy
      supplier_closures = supplier_closure_fetcher.closures
      expect(supplier_closures.map(&:supplier)).to include(supplier1)
      expect(supplier_closures.map(&:supplier)).to_not include(supplier2)
    end

    it 'only gets closure dates for supplier closed during delivery' do
       # mark supplier as closed after delivery at
      if closure_date_kind == :saved
        supplier1.update_column(:close_from, closure_from + 2.days)
      else
        @supplier_closure1.update_column(:starts_at, closure_from + 2.days)
      end

      new_delivery_at = closure_from + 1.day
      supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at).call

      expect(supplier_closure_fetcher.has_closures?).to be_truthy
      supplier_closures = supplier_closure_fetcher.closures
      expect(supplier_closures.map(&:supplier)).to include(supplier2)
      expect(supplier_closures.map(&:supplier)).to_not include(supplier1)
    end

    context 'check directly against the supplier(s) instead of the order' do
      it 'only gets the closure dates for passed in suppliers' do
        new_delivery_at = closure_from + 1.day
        supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: nil, new_delivery_at: new_delivery_at, supplier_ids: [supplier1.id]).call

        expect(supplier_closure_fetcher.has_closures?).to be_truthy
        supplier_closures = supplier_closure_fetcher.closures
        expect(supplier_closures.map(&:supplier)).to include(supplier1)
        expect(supplier_closures.map(&:supplier)).to_not include(supplier2)
      end

      it 'only gets closure dates for supplier closed during delivery' do
         # mark supplier as closed after delivery at
        if closure_date_kind == :saved
          supplier1.update_column(:close_from, closure_from + 2.days)
        else
          @supplier_closure1.update_column(:starts_at, closure_from + 2.days)
        end

        new_delivery_at = closure_from + 1.day
        supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: nil, new_delivery_at: new_delivery_at, supplier_ids: [supplier1.id, supplier2.id]).call

        expect(supplier_closure_fetcher.has_closures?).to be_truthy
        supplier_closures = supplier_closure_fetcher.closures
        expect(supplier_closures.map(&:supplier)).to include(supplier2)
        expect(supplier_closures.map(&:supplier)).to_not include(supplier1)
      end
    end

    context 'with mixed closures' do
      let!(:this_year) { Time.zone.now }

      let!(:additional_closure_from) { (this_year.beginning_of_year + 20.days).beginning_of_day }
      let!(:additional_closure_to) { (this_year.beginning_of_year + 30.days).end_of_day }

      before do
        case closure_date_kind
        when :saved # supplier already has christmas closures
          @supplier_closure1 = create(:supplier_closure, :random, supplier_profile: supplier1, starts_at: additional_closure_from, ends_at: additional_closure_to)
          @supplier_closure2 = create(:supplier_closure, :random, supplier_profile: supplier2, starts_at: additional_closure_from, ends_at: additional_closure_to)
        when :associated # supplier already has associated closures dates
          [supplier1, supplier2].each do |supplier|
            supplier.update_columns(close_from: additional_closure_from, close_to: additional_closure_to)
          end
        end
      end

      it 'cannot process if delivery date is within closure times' do
        new_delivery_at = additional_closure_from + 1.day
        supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at).call

        expect(supplier_closure_fetcher.has_closures?).to be_truthy
        supplier_closures = supplier_closure_fetcher.closures

        expect(supplier_closures).to_not be_empty
        expect(supplier_closures.map(&:supplier)).to include(supplier1, supplier2)
      end

      it 'returns the closure dates of the relevant closure only' do
        new_delivery_at = additional_closure_from + 1.days
        supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at).call

        expect(supplier_closure_fetcher.has_closures?).to be_truthy
        supplier_closures = supplier_closure_fetcher.closures

        expect(supplier_closures.map{|closure| closure.close_from.to_s }).to include(additional_closure_from.to_s) # only including the relevant closure period
        expect(supplier_closures.map{|closure| closure.close_to.to_s }).to include(additional_closure_to.to_s) # only including the relevant closure period

        expect(supplier_closures.map{|closure| closure.close_from.to_s }).to_not include(closure_from.to_s)
        expect(supplier_closures.map{|closure| closure.close_to.to_s }).to_not include(closure_to.to_s)
      end
    end # mixed closure periods

    context 'with supplier operating days' do
      let!(:delivery_zone11) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier1, operating_wdays: '0001000') }
      let!(:delivery_zone21) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier2, operating_wdays: '0000100') }

      it 'returns with closures if delivery date is within supplier non-operating days' do
        new_delivery_at = closure_from - 2.days
        supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at, suburb_id: suburb.id).call

        expect(supplier_closure_fetcher.has_closures?).to be_truthy
        supplier_closures = supplier_closure_fetcher.closures

        expect(supplier_closures.map(&:supplier)).to include(supplier1, supplier2)
        expect(supplier_closures.map{|closure| closure.close_from.to_s }).to include(new_delivery_at.beginning_of_day.to_s) # returns delivery date for operating days overlap
        expect(supplier_closures.map{|closure| closure.close_to.to_s }).to include(new_delivery_at.end_of_day.to_s) # returns delivery date for operating days overlap
      end
    end # with operating days

    context 'with supplier operating hours' do
      let!(:delivery_zone11) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier1, operating_wdays: '0111110', operating_hours_start: 39_600, operating_hours_end: 54_000) } # 11am to 3pm
      let!(:delivery_zone21) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier2, operating_wdays: '0111110', operating_hours_start: 36_000, operating_hours_end: 54_000) } # 10am to 3pm

      it 'returns with closure only for suppliers who are operating on the day but are outside operating hours' do
        new_delivery_at = closure_from - 6.day + 10.hours + 30.minutes # Tuesday 10:30am
        supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order, new_delivery_at: new_delivery_at, suburb_id: suburb.id).call

        expect(supplier_closure_fetcher.has_closures?).to be_falsey
        expect(supplier_closure_fetcher.outside_operating_hours?).to be_truthy
        supplier_closures = supplier_closure_fetcher.closures

        expect(supplier_closures.map(&:supplier)).to include(supplier1) # outside operating hours
        expect(supplier_closures.map(&:supplier)).to_not include(supplier2) # inside operating hours

        expect(supplier_closures.map(&:close_from).compact).to be_blank
        expect(supplier_closures.map(&:close_to).compact).to be_blank

        # supplier operating hours
        supplier1_closure = supplier_closures.detect{|closure| closure.supplier == supplier1 }
        expect(supplier1_closure).to be_present
        expect(supplier1_closure.close_from).to be_blank
        expect(supplier1_closure.close_to).to be_blank
        expect(supplier1_closure.operating_hours).to be_present

        # expected operating hours
        operating_starts_at = Time.at(delivery_zone11.operating_hours_start).utc
        operating_ends_at = Time.at(delivery_zone11.operating_hours_end).utc
        starts_at_on_delivery_date = new_delivery_at.change(hour: operating_starts_at.hour, min: operating_starts_at.min)
        ends_at_on_delivery_date = new_delivery_at.change(hour: operating_ends_at.hour, min: operating_ends_at.min)
        expect(supplier1_closure.operating_hours.map(&:to_s)).to include(starts_at_on_delivery_date.to_s, ends_at_on_delivery_date.to_s)
      end
    end # with operating hours
  end

  context 'for a draft recurrring order' do
    let(:order1) { create(:order, :draft, name: 'mon', delivery_at: nil) }
    let(:order2) { create(:order, :draft, name: 'wed', delivery_at: nil) }
    let(:order3) { create(:order, :draft, name: 'fri', delivery_at: nil) }

    let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
    let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier2) }

    let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }

    let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }

    before do
      [order1, order2, order3].each do |order|
        order.update_columns(order_type: 'recurrent', recurrent_id: order1.id, template_id: order.id)
      end
    end

    it 'does not return with closurses if all (calculated) delivery dates for the orders are outside closure times' do
      new_delivery_at = closure_from - 10.day
      supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order1, new_delivery_at: new_delivery_at).call

      expect(supplier_closure_fetcher.has_closures?).to be_falsey
      expect(supplier_closure_fetcher.closures).to be_empty
    end

    it 'returns with closures if delivery date is within closure times' do
      new_delivery_at = closure_from + 1.day
      supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order1, new_delivery_at: new_delivery_at).call

      expect(supplier_closure_fetcher.has_closures?).to be_truthy
      supplier_closures = supplier_closure_fetcher.closures

      expect(supplier_closures).to_not be_empty
      expect(supplier_closures.map(&:supplier)).to include(supplier1, supplier2)
      expect(supplier_closures.map{|closure| closure.close_from.to_s }).to include(closure_from.to_s)
      expect(supplier_closures.map{|closure| closure.close_to.to_s }).to include(closure_to.to_s)
    end

    it 'returns with closures if any one recurring order\'s delivery date is within closure times' do
      new_delivery_at = closure_from - 3.days
      supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: order1, new_delivery_at: new_delivery_at).call

      expect(supplier_closure_fetcher.has_closures?).to be_truthy
      supplier_closures = supplier_closure_fetcher.closures

      expect(supplier_closures).to_not be_empty
      expect(supplier_closures.map(&:supplier)).to include(supplier1)
      expect(supplier_closures.map(&:supplier)).to_not include(supplier2) # as order 1 is not in closure period
    end
  end

end
