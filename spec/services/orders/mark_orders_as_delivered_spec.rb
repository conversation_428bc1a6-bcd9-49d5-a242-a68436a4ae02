require 'rails_helper'

RSpec.describe Orders::MarkOrdersAsDelivered, type: :service, orders: true do

  let!(:now) { Time.zone.now }

  let!(:order1) { create(:order, :confirmed, delivery_at: now - 2.days) }
  let!(:order2) { create(:order, :confirmed, delivery_at: now - 2.days) }

  context 'with order lines' do
    let!(:order_line1) { create(:order_line, :random, order: order1) }
    let!(:order_line2) { create(:order_line, :random, order: order2) }

    it 'marks all day old amended/confirmed orders with order lines as delivered' do
      order_marker = Orders::MarkOrdersAsDelivered.new(time: now).call

      delivered_orders = order_marker.delivered_orders
      expect(delivered_orders).to include(order1, order2)
      expect(order1.reload.status).to eq('delivered')
      expect(order2.reload.status).to eq('delivered')
    end

    it 'only marks the amended/confirmed orders as delivered' do
      order2.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[amended confirmed delivered]).sample)

      order_marker = Orders::MarkOrdersAsDelivered.new(time: now).call

      expect(order_marker).to be_success
      delivered_orders = order_marker.delivered_orders
      expect(delivered_orders).to include(order1)
      expect(order1.reload.status).to eq('delivered')
      expect(delivered_orders).to_not include(order2)
      expect(order2.reload.status).to_not eq('delivered')
    end

    it 'only marks non-splited orders as delivered' do
      order1.update_column(:split_order_id, rand(2000))

      order_marker = Orders::MarkOrdersAsDelivered.new(time: now).call

      expect(order_marker).to be_success
      delivered_orders = order_marker.delivered_orders
      expect(delivered_orders).to include(order2)
      expect(order2.reload.status).to eq('delivered')
      expect(delivered_orders).to_not include(order1)
      expect(order1.reload.status).to_not eq('delivered')
    end

    it 'only marks day old orders as delivered' do
      order2.update_column(:delivery_at, now.beginning_of_day + 2.hours)

      order_marker = Orders::MarkOrdersAsDelivered.new(time: now).call

      expect(order_marker).to be_success
      delivered_orders = order_marker.delivered_orders
      expect(delivered_orders).to include(order1)
      expect(order1.reload.status).to eq('delivered')
      expect(delivered_orders).to_not include(order2)
      expect(order2.reload.status).to_not eq('delivered')
    end
  end

  context 'without order lines' do
    it 'marks confirmed orders without any order lines as draft' do
      order_marker = Orders::MarkOrdersAsDelivered.new(time: now).call

      expect(order_marker).to be_success
      delivered_orders = order_marker.delivered_orders
      expect(delivered_orders).to be_blank
      expect(order1.reload.status).to eq('draft')
      expect(order2.reload.status).to eq('draft')
    end

    it 'does not change the state of non-amended/non-confirmed orders' do
      new_status = (Order::VALID_ORDER_STATUSES - %w[draft amended confirmed delivered]).sample
      order2.update_column(:status, new_status)

      order_marker = Orders::MarkOrdersAsDelivered.new(time: now).call

      expect(order_marker).to be_success
      delivered_orders = order_marker.delivered_orders
      expect(delivered_orders).to be_blank
      expect(order2.reload.status).to_not eq('delivered')
      expect(order2.reload.status).to_not eq('draft')
      expect(order2.reload.status).to eq(new_status)
    end
  end

end
