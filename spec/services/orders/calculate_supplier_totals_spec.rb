require 'rails_helper'

RSpec.describe Orders::CalculateSupplierTotals, type: :service, orders: true do

  let(:supplier) { create(:supplier_profile, :random) }
  let(:order) { create(:order, :draft) }
  let(:calculated_totals) { Orders::CalculateSupplierTotals.new(order: order, supplier: supplier).call }

  it 'returns an object with various attributes' do
    attributes = calculated_totals.class.instance_methods(false)
    expect(attributes).to include(:order_line_count, :subtotal, :topup, :delivery, :gst, :total)
  end

  it 'returns all totals as 0 for an order without order lines' do
    order_totals = calculated_totals
    expect(order_totals.order_line_count).to eq(0)
    expect(order_totals.subtotal).to eq(0)
    expect(order_totals.delivery).to eq(0)
    expect(order_totals.topup).to eq(0)
    expect(order_totals.gst).to eq(0)
    expect(order_totals.total).to eq(0)
  end

  context 'with order lines' do
    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier, cost: 10.0, quantity: 5, is_gst_free: false) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier, cost: 10.0, quantity: 5, is_gst_free: false) }
    let!(:order_line3) { create(:order_line, :random, order: order, supplier_profile: supplier, cost: 10.0, quantity: 5, is_gst_free: false) }

    let!(:order_line4) { create(:order_line, :random, order: order, supplier_profile: supplier, cost: 10.0, quantity: 5, is_gst_free: false) }
    let!(:order_line5) { create(:order_line, :random, order: order, supplier_profile: supplier, cost: 10.0, quantity: 5, is_gst_free: false) }
    let!(:order_line6) { create(:order_line, :random, order: order, supplier_profile: supplier, cost: 10.0, quantity: 5, is_gst_free: false) }

    let(:calculated_totals) { Orders::CalculateSupplierTotals.new(order: order.reload).call }

    before do
      Order.skip_callback(:save, :after)
    end

    it 'returns the subtotal as the sum of the order line price totals' do
      expect(calculated_totals.subtotal).to eq(300)
    end

    it 'returns the gst (10%) based on the total of the non-gst products' do
      expect(calculated_totals.gst).to eq(30)
    end

    it 'returns the total as the sum of subtotal and gst' do
      expect(calculated_totals.total).to eq(330)
    end

    context 'with some gst_free products' do
      before do
        [order_line3, order_line4, order_line6].each do |order_line|
          order_line.update_column(:is_gst_free, true)
        end
      end

      it 'does not include gst for non-gst products' do
        expect(calculated_totals.gst).to eq(15)
      end

      it 'returns the total according the gst products' do
        expect(calculated_totals.total).to eq(315)
      end
    end

    context 'with a delivery fee' do
      let(:calculated_delivery) { 10 }

      before do
        calculate_delivery = double(Orders::CalculateDelivery)
        allow(Orders::CalculateDelivery).to receive(:new).and_return(calculate_delivery)
        allow(calculate_delivery).to receive(:call).and_return(calculated_delivery)
      end

      it 'returns the delivery value' do
        expect(calculated_totals.delivery).to eq(calculated_delivery)
      end

      it 'includes the delivery gst (10%) in gst' do
        expect(calculated_totals.gst).to eq(31) # normal gst + gst (10%) of delivery
      end

      it 'includes the delivery (with gts) in total' do
        expect(calculated_totals.total).to eq(341)
      end

      context 'with all GST free items' do
        before do
          [order_line1, order_line2, order_line3, order_line4, order_line5, order_line6].each do |order_line|
            order_line.update_column(:is_gst_free, true)
          end
        end

        it 'does not add GST for the delivery' do
          expect(calculated_totals.gst).to eq(0) # no gst for items and delivery
        end

        it 'includes delivery (excluding GST) in total' do
          expect(calculated_totals.total).to eq(310)
        end
      end
    end

    context 'with save totals' do
      let(:calculated_totals) { Orders::CalculateSupplierTotals.new(order: order.reload, supplier: supplier, save_totals: true).call }

      it 'saves the supplier totals in the order supplier record' do
        calculated_totals

        order_supplier = order.reload.order_suppliers.where(supplier_profile: supplier).first
        expect(order_supplier).to be_present
        expect(order_supplier.subtotal).to eq(300)
        expect(order_supplier.delivery).to eq(0) # no delivery added
        expect(order_supplier.gst).to eq(30)
        expect(order_supplier.total).to eq(330)
      end

      it 'updates the supplier totals in the order supplier record' do
        order_supplier = create(:order_supplier, :random, order: order, supplier_profile: supplier, subtotal: 201.0, gst: 10.2, delivery: 10.2, total: 221.4)
        calculated_totals

        order_supplier.reload
        expect(order_supplier.subtotal).to eq(300)
        expect(order_supplier.delivery).to eq(0) # no delivery added
        expect(order_supplier.gst).to eq(30)
        expect(order_supplier.total).to eq(330)
      end
    end

    context 'specified order lines' do
      let!(:calculated_totals) { Orders::CalculateSupplierTotals.new(order: order.reload, order_lines: [order_line1, order_line3, order_line5, order_line6], supplier: supplier).call }

      it 'calculates totals for specified order lines' do
        expect(calculated_totals.order_line_count).to eq(4)
        expect(calculated_totals.subtotal).to eq(200)
        expect(calculated_totals.delivery).to eq(0) # no delivery added
        expect(calculated_totals.gst).to eq(20)
        expect(calculated_totals.topup).to eq(0)
        expect(calculated_totals.total).to eq(220)
      end
    end

    context 'with supplier surcharge/topup' do
      let!(:supplier2) { create(:supplier_profile, :random) }
      let!(:order_supplier1) { create(:order_supplier, order: order, supplier_profile: supplier, surcharge: 30) }
      let!(:order_supplier2) { create(:order_supplier, order: order, supplier_profile: supplier2, surcharge: 40) }

      it 'returns 0 surcharge/topup amount for the supplier within the order if order is not charged to minimum' do
        order.reload
        expect(calculated_totals.topup).to eq(0)
      end

      it 'returns 0 surcharge/topup amount for the supplier within the order if order is a team order and not charged to minimum', team_orders: true do
        order.update_column(:order_type, 'team_order')
        create(:team_order_detail, :random, order: order, cutoff_option: 'cancel_order') # create team order detail
        order.reload
        expect(calculated_totals.topup).to eq(0)
      end

      it 'returns surcharge/topup amount (minus supplier commission) for the supplier within the order which is marked as charged to minimum' do
        order.update_column(:charge_to_minimum, true)
        order.reload

        supplier_topup = 30 * (1 - (supplier.commission_rate / 100))

        expect(calculated_totals.topup).to eq(supplier_topup)
        expect(calculated_totals.total).to eq(330 + supplier_topup)
      end
    end

    context 'with baseline pricing' do
      let(:calculated_baseline_totals) { Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, baseline: true).call }

      before do
        # set baseline values
        [order_line1, order_line2, order_line3, order_line4, order_line5, order_line6].each do |order_line|
          order_line.update_column(:baseline, order_line.cost + 3)
        end
      end

      it 'returns the subtotal as the sum of the order line price totals' do
        expect(calculated_baseline_totals.subtotal).to eq(390)
      end

      it 'returns the gst (10%) based on the total of the non-gst products' do
        expect(calculated_baseline_totals.gst).to eq(39)
      end

      it 'returns the total as the sum of subtotal and gst' do
        expect(calculated_baseline_totals.total).to eq(429)
      end

      it 'doesn\'t update totals within the record if calculating baseline totals' do
        order_supplier = create(:order_supplier, :random, order: order, supplier_profile: supplier, subtotal: 201.0, gst: 10.2, delivery: 10.2, total: 221.4)
        Orders::CalculateSupplierTotals.new(order: order, supplier: supplier, baseline: true, save_totals: true).call

        order_supplier.reload
        expect(order_supplier.subtotal).to_not eq(300)
        expect(order_supplier.subtotal).to eq(201.0)

        expect(order_supplier.delivery).to_not eq(0)
        expect(order_supplier.delivery).to eq(10.2)

        expect(order_supplier.gst).to_not eq(30)
        expect(order_supplier.gst).to eq(10.2)

        expect(order_supplier.total).to_not eq(330)
        expect(order_supplier.total).to eq(221.4)
      end
    end # base line pricing
  end # with order lines

end
