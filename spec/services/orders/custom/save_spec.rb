require 'rails_helper'

RSpec.describe Orders::Custom::Save, type: :service, orders: true, custom_orders: true do

  let(:suburb) { create(:suburb, :random) }
  let(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:generic_catering_category) { create(:category, :random, group: 'catering-services', is_generic: true) }
  let!(:generic_pantry_category) { create(:category, :random, group: 'kitchen-supplies', is_generic: true) }

  context 'New Custom Order' do
    it 'create a draft custom order by default' do
      custom_order_creator = Orders::Custom::Save.new(order_params: {}).call

      expect(custom_order_creator).to be_success
      created_order = custom_order_creator.order
      expect(created_order.status).to eq('draft')
      expect(created_order.order_type).to eq('one-off')
      expect(created_order.order_variant).to eq('event_order')
      expect(created_order.major_category).to eq(generic_catering_category)
      expect(created_order.uuid).to be_present
    end

    it 'creates a custom order with the specified params' do
      order_params = {
        event_name: Faker::Name.name,
        number_of_people: rand(20),

        delivery_at: '2029-03-01 13:00:00',
        delivery_level: rand(20).to_s,
        delivery_address: Faker::Address.street_address,
        suburb_id: suburb.id,
        delivery_instruction: Faker::Lorem.sentence,
        delivery: rand(20),

        department_identity: 'something', # ???
        commission: rand(10),
        major_category_id: generic_pantry_category.id,

        contact_name: Faker::Name.name,
        contact_email: Faker::Internet.email,
        contact_phone: Faker::PhoneNumber.phone_number,
        company_name: Faker::Name.name,
        whodunnit_id: rand(2000),
      }
      custom_order_creator = Orders::Custom::Save.new(order_params: order_params).call

      expect(custom_order_creator).to be_success
      created_order = custom_order_creator.order
      expect(created_order.name).to eq(order_params[:event_name])
      expect(created_order.number_of_people).to eq(order_params[:number_of_people])

      expect(created_order.delivery_at).to eq(Time.zone.parse(order_params[:delivery_at]))
      expect(created_order.delivery_address_level).to eq(order_params[:delivery_level])
      expect(created_order.delivery_address).to eq(order_params[:delivery_address])
      expect(created_order.delivery_suburb).to eq(suburb)
      expect(created_order.delivery_instruction).to eq(order_params[:delivery_instruction])
      expect(created_order.customer_delivery).to eq(order_params[:delivery])

      expect(created_order.department_identity).to eq(order_params[:department_identity])
      expect(created_order.commission).to eq(order_params[:commission])
      expect(created_order.major_category).to eq(generic_pantry_category)

      expect(created_order.contact_name).to eq(order_params[:contact_name])
      expect(created_order.phone).to eq(order_params[:contact_phone])
      expect(created_order.company_name).to eq(order_params[:company_name])

      expect(created_order.whodunnit_id).to eq(order_params[:whodunnit_id])
    end

    context 'with a meal plan', meal_plans: true do
      let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer) }

      it 'creates a custom order with passed in meal plan' do
        order_params = { meal_plan_id: meal_plan.id }
        custom_order_creator = Orders::Custom::Save.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.meal_plan).to eq(meal_plan)
      end
    end

    context 'with purchase order details', purchase_orders: true do
      it 'creates a custom order with cpo_id as passed in even if po_number (string) is passed' do
        customer_po = create(:customer_purchase_order, :random, customer_profile: customer)
        order_params = { customer_profile_id: customer.id, cpo_id: customer_po.id, po_number: 'random PO #' }
        custom_order_creator = Orders::Custom::Save.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.cpo_id).to eq(order_params[:cpo_id])
        # expect(created_order.read_attribute(:po_number)).to be_blank
        expect(created_order.po_number).to eq(customer_po.po_number)
      end

      it 'creates a custom order with po_number (string) as passed if no cpo_id is passed' do
        order_params = { customer_profile_id: customer.id, po_number: 'random PO #', cpo_id: nil }
        custom_order_creator = Orders::Custom::Save.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.po_number).to eq('random PO #')
        expect(created_order.cpo_id).to_not be_blank # creates a new CPO and attaches it to the order
      end
    end
  end

  context 'Update existing Custom Order' do
    let!(:custom_order) { create(:order, :draft, order_type: 'one-off', order_variant: 'event_order', major_category: generic_catering_category, customer_profile: customer) }

    it 'does not create a duplicate custom order' do
      order_params = { id: custom_order.id }
      custom_order_updater = Orders::Custom::Save.new(order_params: order_params).call

      expect(custom_order_updater).to be_success
      updated_order = custom_order_updater.order
      expect(updated_order.id).to eq(custom_order.id)
    end

    it 'updates the custom order details as passed' do
      order_params = {
        id: custom_order.id,
        event_name: Faker::Name.name,
        major_category_id: generic_pantry_category.id,
        # ...
        contact_name: Faker::Name.name,
        contact_phone: Faker::PhoneNumber.phone_number,
      }
      custom_order_updater = Orders::Custom::Save.new(order_params: order_params).call

      expect(custom_order_updater).to be_success
      updated_order = custom_order_updater.order
      expect(updated_order.id).to eq(custom_order.id)
      expect(updated_order.name).to eq(order_params[:event_name])
      expect(updated_order.contact_name).to eq(order_params[:contact_name])
      expect(updated_order.phone).to eq(order_params[:contact_phone])
      expect(updated_order.major_category).to eq(generic_pantry_category)
    end

    it 'saves the new selected purchase order', purchase_orders: true do
      customer_po = create(:customer_purchase_order, :random, customer_profile: customer)
      custom_order.update_column(:po_number, 'random po number')
      order_params = { id: custom_order.id, cpo_id: customer_po.id }
      custom_order_updater = Orders::Custom::Save.new(order_params: order_params).call

      expect(custom_order_updater).to be_success
      updated_order = custom_order_updater.order
      expect(updated_order.po_number).to eq(customer_po.po_number)
    end

  end
end

