require 'rails_helper'

RSpec.describe Orders::Custom::GetSupplierChanges, type: :service, orders: true, custom_orders: true, order_amended_email: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:custom_order) { create(:order, :amended, :custom_order) }
  let!(:custom_order_supplier) { create(:custom_order_supplier, :random, supplier_profile: supplier, order: custom_order) }

  before do
    custom_order.update_column(:suppliers_notified_at, custom_order_supplier.updated_at)
  end

  it 'returns blank if there are not supplier changes' do
    supplier_changes = Orders::Custom::GetSupplierChanges.new(order: custom_order, supplier: supplier).call

    expect(supplier_changes).to be_blank
  end

  context 'with delivery at updates' do
    let!(:date_format) { Date::DATE_FORMATS[:full] }
    let!(:cached_delivery_at) { custom_order_supplier.delivery_at.dup }

    before do
      custom_order_supplier.update(delivery_at: cached_delivery_at + 10.days)
    end

    it 'returns the changes for delivery at' do
      supplier_changes = Orders::Custom::GetSupplierChanges.new(order: custom_order, supplier: supplier).call

      expect(supplier_changes).to be_present
      expect(supplier_changes.map(&:field)).to include('delivery_at')

      delivery_at_change = supplier_changes.detect{|change| change.field == 'delivery_at' }
      expect(delivery_at_change).to be_present
      expect(delivery_at_change.label).to eq('Delivery date')
      expect(delivery_at_change.old_value).to eq(cached_delivery_at.strftime(date_format))
      expect(delivery_at_change.new_value).to eq(custom_order_supplier.reload.delivery_at.strftime(date_format))
    end
  end

  context 'with other field changes' do
    let!(:changed_field) { (Orders::Custom::GetSupplierChanges::CHANGE_FIELDS - ['delivery_at']).sample }
    let!(:cached_value) { custom_order_supplier.send(changed_field).dup }

    before do
      custom_order_supplier.update("#{changed_field}": "changed value for #{changed_field}")
    end

    it 'returns the changes for changed field' do
      supplier_changes = Orders::Custom::GetSupplierChanges.new(order: custom_order, supplier: supplier).call

      expect(supplier_changes).to be_present
      expect(supplier_changes.map(&:field)).to include(changed_field)

      field_change = supplier_changes.detect{|change| change.field == changed_field }
      expect(field_change).to be_present
      expect(field_change.label).to eq(changed_field.humanize)
      expect(field_change.old_value).to eq(cached_value)
      expect(field_change.new_value).to eq(custom_order_supplier.reload.send(changed_field))
    end
  end

  context 'with any updates' do
    let!(:changed_field) { Orders::Custom::GetSupplierChanges::CHANGE_FIELDS.sample }
    let!(:cached_value) { custom_order_supplier.send(changed_field).dup }

    before do
      custom_order_supplier.update("#{changed_field}": "changed value for #{changed_field}")
    end

    it 'returns blank if order is not a custom order' do
      custom_order.update_column(:order_variant, 'general')

      supplier_changes = Orders::Custom::GetSupplierChanges.new(order: custom_order, supplier: supplier).call
      expect(supplier_changes).to be_blank
    end

    it 'returns blank if no custom order supplier versions are present' do
      custom_order_supplier.versions.destroy_all

      supplier_changes = Orders::Custom::GetSupplierChanges.new(order: custom_order, supplier: supplier).call
      expect(supplier_changes).to be_blank
    end

    it 'returns blank if supplier is already notified' do
      custom_order.update_column(:suppliers_notified_at, Time.zone.now + 1.hour)

      supplier_changes = Orders::Custom::GetSupplierChanges.new(order: custom_order, supplier: supplier).call
      expect(supplier_changes).to be_blank
    end
  end

end
