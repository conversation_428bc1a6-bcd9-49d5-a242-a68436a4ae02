require 'rails_helper'

RSpec.describe Orders::Custom::UpsertLocation, type: :service, orders: true, custom_orders: true do

  let(:custom_order) { create(:order, :draft, :custom_order) }
  let(:supplier) { create(:supplier_profile, :random) }

  context 'New Custom Location' do

    it 'creates a new location for the order' do
      location_params = { location_name: 'New Location' }
      custom_order_location_creator = Orders::Custom::UpsertLocation.new(order: custom_order, location_params: location_params).call

      expect(custom_order_location_creator).to be_success
      created_location = custom_order_location_creator.location
      expect(created_location).to be_present
      expect(created_location.order).to eq(custom_order)
      expect(created_location.details).to eq('New Location')

    end

    context 'with order line params' do
      let(:order_line_params1) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier.id, quantity: 5, note: Faker::Lorem.sentence } }
      let(:order_line_params2) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier.id, quantity: 5, note: Faker::Lorem.sentence } }
      let(:order_line_params3) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier.id, quantity: 5, note: Faker::Lorem.sentence } }
      let(:order_lines_params) { [order_line_params1, order_line_params2, order_line_params3] }

      it 'creates a custom menu section for the supplier' do
        skip 'Already tested in OrderLines::UpsertCustomOrderLine'
      end

      it 'creates custom menu items for the supplier' do
        skip 'Already tested in OrderLines::UpsertCustomOrderLine'
      end

      it 'creates new order lines for the new location' do
        location_params = { location_name: Faker::Name.name, order_lines: order_lines_params }
        custom_order_location_creator = Orders::Custom::UpsertLocation.new(order: custom_order, location_params: location_params).call

        expect(custom_order_location_creator).to be_success

        created_order_lines = custom_order.reload.order_lines
        expect(created_order_lines).to be_present
        expect(created_order_lines.map{|ol| ol.location.details }).to include(location_params[:location_name])
      end
    end

  end

  context 'with existing Order location' do

    let!(:custom_location) { create(:location, :random, order: custom_order) }

    it 'does not create a new order location' do
      location_params = { id: custom_order.id, location_name: custom_location.details }
      custom_order_location_updater = Orders::Custom::UpsertLocation.new(order: custom_order, location_params: location_params).call

      expect(custom_order_location_updater).to be_success
      updated_location = custom_order_location_updater.location
      expect(updated_location.id).to eq(custom_location.id)
    end

    context 'with exiting order lines for the location' do
      let!(:custom_order_line1) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }
      let!(:custom_order_line2) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }
      let!(:custom_order_line3) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }

      let(:order_line_params1) { { id: custom_order_line1.id, quantity: 5 } }
      let(:order_line_params2) { { id: custom_order_line2.id, quantity: 10 } }
      let(:order_line_params3) { { id: custom_order_line3.id, quantity: 9 } }
      let(:order_lines_params) { [order_line_params1, order_line_params2, order_line_params3] }

      it 'updates the quantities of the exiting order lines' do
        expect(custom_order.order_lines.size).to eq(3)

        location_params = { location_id: custom_location.id, order_lines: order_lines_params }
        custom_order_location_updater = Orders::Custom::UpsertLocation.new(order: custom_order, location_params: location_params).call

        expect(custom_order_location_updater).to be_success

        updated_order_lines = custom_order.reload.order_lines
        expect(updated_order_lines.size).to eq(3)
        expect(updated_order_lines.map(&:id)).to include(custom_order_line1.id, custom_order_line2.id, custom_order_line3.id)
        expect(updated_order_lines.map(&:quantity)).to include(5, 10, 9)
      end

      it 'deletes the order lines that are not passed' do
        expect(custom_order.order_lines.size).to eq(3)
        order_lines_params = [order_line_params1, order_line_params3]
        location_params = { location_id: custom_location.id, order_lines: order_lines_params }
        custom_order_location_updater = Orders::Custom::UpsertLocation.new(order: custom_order, location_params: location_params).call

        expect(custom_order_location_updater).to be_success

        updated_order_lines = custom_order.reload.order_lines
        expect(updated_order_lines.size).to eq(2)
        expect(updated_order_lines.map(&:id)).to include(custom_order_line1.id, custom_order_line3.id)
        expect(updated_order_lines.map(&:id)).to_not include(custom_order_line2.id)
        expect(updated_order_lines.map(&:quantity)).to include(5, 9)
        expect(updated_order_lines.map(&:quantity)).to_not include(10)
      end
    end # existing order lines
  end
end

