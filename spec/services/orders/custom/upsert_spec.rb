require 'rails_helper'

RSpec.describe Orders::Custom::Upsert, type: :service, orders: true, custom_orders: true do

  let(:suburb) { create(:suburb, :random) }
  let(:credit_card) { create(:credit_card, :random) }
  let(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:generic_catering_category) { create(:category, :random, group: 'catering-services', is_generic: true) }
  let!(:generic_pantry_category) { create(:category, :random, group: 'kitchen-supplies', is_generic: true) }

  let!(:order_params) do
    {
      event_name: Faker::Name.name,
      number_of_people: rand(20),

      delivery_at: (Time.zone.now + 3.days).strftime('%Y-%m-%d %H:%M'),
      delivery_level: rand(20).to_s,
      delivery_address: Faker::Address.street_address,
      suburb_id: suburb.id,
      delivery_instruction: Faker::Lorem.sentence,

      department_identity: 'something', # ???
      commission: rand(10),
      major_category_id: generic_pantry_category.id,

      contact_name: Faker::Name.name,
      contact_email: Faker::Internet.email,
      contact_phone: Faker::PhoneNumber.phone_number,
      company_name: Faker::Name.name,
      credit_card_id: credit_card.id,
    }
  end

  before do
    # mock promotions syncer
    promotions_syncer = double(Promotions::SyncWithOrder)
    allow(Promotions::SyncWithOrder).to receive(:new).and_return(promotions_syncer)
    allow(promotions_syncer).to receive(:call).and_return(true)

    # mock customer syncer
    contact_syncer = delayed_syncer = double(Hubspot::SyncContact)
    allow(Hubspot::SyncContact).to receive(:new).and_return(contact_syncer)
    allow(contact_syncer).to receive(:delay).and_return(delayed_syncer)
    allow(delayed_syncer).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  context 'New Custom Order' do
    it 'create a draft custom order by default' do
      custom_order_creator = Orders::Custom::Upsert.new(order_params: {}).call

      expect(custom_order_creator).to be_success
      created_order = custom_order_creator.order
      expect(created_order.status).to eq('draft')
      expect(created_order.order_type).to eq('one-off')
      expect(created_order.order_variant).to eq('event_order')
      expect(created_order.major_category).to eq(generic_catering_category)
    end

    it 'creates a custom order with the specified params' do
      custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

      expect(custom_order_creator).to be_success
      created_order = custom_order_creator.order
      expect(created_order.name).to eq(order_params[:event_name])
      expect(created_order.number_of_people).to eq(order_params[:number_of_people])

      expect(created_order.delivery_at).to eq(Time.zone.parse(order_params[:delivery_at]))
      expect(created_order.delivery_address_level).to eq(order_params[:delivery_level])
      expect(created_order.delivery_address).to eq(order_params[:delivery_address])
      expect(created_order.delivery_suburb).to eq(suburb)
      expect(created_order.delivery_instruction).to eq(order_params[:delivery_instruction])

      expect(created_order.department_identity).to eq(order_params[:department_identity])
      expect(created_order.commission).to eq(order_params[:commission])
      expect(created_order.major_category).to eq(generic_pantry_category)

      expect(created_order.contact_name).to eq(order_params[:contact_name])
      expect(created_order.phone).to eq(order_params[:contact_phone])
      expect(created_order.company_name).to eq(order_params[:company_name])
    end

    context 'with purchase order details', purchase_orders: true do
      it 'creates a custom order with cpo_id as passed in even if po_number (string) is passed' do
        customer_po = create(:customer_purchase_order, :random, customer_profile: customer)
        order_params = { customer_profile_id: customer.id, cpo_id: customer_po.id, po_number: 'random PO #' }
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.cpo_id).to eq(order_params[:cpo_id])
        expect(created_order.po_number).to eq(customer_po.po_number)
      end

      it 'creates a custom order with po_number (string) as passed if no cpo_id is passed' do
        order_params = { customer_profile_id: customer.id, po_number: 'random PO #', cpo_id: nil }
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.po_number).to eq('random PO #')
        expect(created_order.cpo_id).to_not be_blank # creates a new CPO and attaches it to the order
      end
    end

    it 'creates a new location for the order' do
      order_params = { location_name: 'New Location' }
      custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

      expect(custom_order_creator).to be_success
      created_order = custom_order_creator.order
      order_location = Location.where(order: created_order).first
      expect(order_location).to be_present
      expect(order_location.details).to eq('New Location')
    end

    it 'creates custom order with passed in whodunnit' do
      user = create(:user, :random)
      order_params = { whodunnit_id: user.id }
      custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

      expect(custom_order_creator).to be_success
      created_order = custom_order_creator.order
      expect(created_order.whodunnit_id).to eq(user.id)
      expect(created_order.original_creator).to eq(user)
    end

    context 'with order line params' do
      let(:supplier1) { create(:supplier_profile, :random) }
      let(:supplier2) { create(:supplier_profile, :random) }
      let(:order_line_params1) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier1.id, quantity: 5, note: Faker::Lorem.sentence } }
      let(:order_line_params2) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier1.id, quantity: 5, note: Faker::Lorem.sentence } }
      let(:order_line_params3) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier2.id, quantity: 5, note: Faker::Lorem.sentence } }
      let(:order_lines_params) { [order_line_params1, order_line_params2, order_line_params3] }

      it 'creates a custom menu section for the supplier' do
        skip 'Already tested in OrderLines::UpsertCustomOrderLine'
      end

      it 'creates custom menu items for the supplier' do
        skip 'Already tested in OrderLines::UpsertCustomOrderLine'
      end

      it 'creates new order lines for the new location' do
        order_params = { location_name: Faker::Name.name, order_lines: order_lines_params }
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        created_order_lines = created_order.order_lines
        expect(created_order_lines.size).to eq(3)
        expect(created_order_lines).to be_present
        expect(created_order_lines.map{|ol| ol.location.details }).to include(order_params[:location_name])
        # other order line attributes already tested in OrderLines::UpsertCustomOrderLine
      end

      it 'creates custom order supplier records' do
        supplier1 = create(:supplier_profile, :random)
        supplier2 = create(:supplier_profile, :random)
        order_params = { suppliers: { supplier1.id.to_s => {}, supplier2.id.to_s => {} } }
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.custom_order_suppliers.map(&:supplier_profile)).to include(supplier1, supplier2)
        expect(created_order.custom_order_suppliers.map(&:order)).to include(created_order)
      end

      it 'sets delivery accodring to the supplier delivery data' do
        order_params = { location_name: Faker::Name.name, order_lines: order_lines_params }
        order_params = order_params.merge({ suppliers: { supplier1.id.to_s => { delivery_fee: 10 }, supplier2.id.to_s => { delivery_fee: 20 } } })
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.custom_order_suppliers.map(&:delivery_fee)).to include(10, 20)
        expect(created_order.customer_delivery).to eq(30)
      end

      it 'errors out if item has malformed data' do
        malformed_order_lines_params = order_lines_params.dup
        malformed_order_lines_params[1][:menu_item_description] = Faker::Lorem.characters(number: 256)

        order_params = { location_name: Faker::Name.name, order_lines: malformed_order_lines_params }
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to_not be_success
        expect(custom_order_creator.errors).to include('Name - Item name is too long (max 255)')
      end
    end

    it 'logs a `Draft Order Created` event', event_logs: true do
      expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'custom-order-saved-as-draft') # event object is the created custom order

      custom_order_creator = Orders::Custom::Upsert.new(order_params: {}).call
      expect(custom_order_creator).to be_success
    end

    context 'with order commission below threshold', event_logs: true do
      let!(:markdown) { 0.0 } # defaulted to 0.0
      let!(:commission) { rand(1.03..19.03) }

      it 'logs a `Order Below Margin Threshold` event' do
        expected_comission = (1 - (1 - (markdown / 100)) / (1 + (commission.to_f / 100))).round(2)
        expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'order-below-margin-threshold', severity: 'warning', commission: expected_comission) # event object is the created custom order

        custom_order_creator = Orders::Custom::Upsert.new(order_params: { commission: commission }).call
        expect(custom_order_creator).to be_success
      end
    end

    context 'with a coupon code', coupons: true do
      let(:coupon) { create(:coupon, :random) }

      it 'attaches the coupon (with the code) to the order' do
        order_params = { coupon_code: coupon.code }
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.coupon).to eq(coupon)
      end

      it 'returns any errors if coupon cannot be attached' do
        coupon.update_column(:valid_until, Time.zone.now - 1.day) # expired coupon
        order_params = { coupon_code: coupon.code }
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to_not be_success
        expect(custom_order_creator.errors).to include('Coupon has expired')
      end
    end # with a coupon

    context 'with a meal plan ID', meal_plans: true do
      let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer) }

      it 'attaches the meal plan to the order' do
        order_params = { meal_plan_id: meal_plan.id }
        custom_order_creator = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_creator).to be_success
        created_order = custom_order_creator.order
        expect(created_order.meal_plan).to eq(meal_plan)
      end
    end
  end

  context 'Update existing Custom Order' do

    let!(:custom_order) { create(:order, :draft, order_type: 'one-off', order_variant: 'event_order', major_category: generic_catering_category, customer_profile: customer) }

    it 'does not create a duplicate custom order' do
      order_params = { id: custom_order.id }
      custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call

      expect(custom_order_updater).to be_success
      updated_order = custom_order_updater.order
      expect(updated_order.id).to eq(custom_order.id)
    end

    it 'updates the custom order details as passed' do
      order_params = {
        id: custom_order.id,
        event_name: Faker::Name.name,
        major_category_id: generic_pantry_category.id,
        # ...
        contact_name: Faker::Name.name,
        contact_phone: Faker::PhoneNumber.phone_number,
      }
      custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call

      expect(custom_order_updater).to be_success
      updated_order = custom_order_updater.order
      expect(updated_order.id).to eq(custom_order.id)
      expect(updated_order.name).to eq(order_params[:event_name])
      expect(updated_order.contact_name).to eq(order_params[:contact_name])
      expect(updated_order.phone).to eq(order_params[:contact_phone])
      expect(updated_order.major_category).to eq(generic_pantry_category)
    end

    it 'removes stored po_number if a cpo_id is passed', purchase_orders: true do
      customer_po = create(:customer_purchase_order, :random, customer_profile: customer)
      custom_order.update_column(:po_number, 'random po number')
      order_params = { id: custom_order.id, cpo_id: customer_po.id }
      custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call

      expect(custom_order_updater).to be_success
      updated_order = custom_order_updater.order
      expect(updated_order.po_number).to eq(customer_po.po_number)
    end

    it 'requests the promotions to be sycned with the custom order', promotions: true do
      expect(Promotions::SyncWithOrder).to receive(:new).with(order: custom_order)

      order_params = { id: custom_order.id }

      custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call
      expect(custom_order_updater).to be_success
    end

    it 'does not sync the Hubspot customer account when just updating', hubspot: true do
      expect(Hubspot::SyncContact).to_not receive(:new)

      custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call
      expect(custom_order_updater).to be_success
    end

    context 'with existing order location' do

      let!(:custom_location) { create(:location, :random, order: custom_order) }

      it 'does not create a new order location' do
        order_params = { id: custom_order.id, location_name: custom_location.details }
        custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_updater).to be_success
        updated_order = custom_order_updater.order
        expect(updated_order.id).to eq(custom_order.id)
        order_location = Location.where(order: updated_order).first
        expect(order_location).to be_present
        expect(order_location).to eq(custom_location)
      end

      context 'wit exiting order lines for the location' do
        let(:supplier) { create(:supplier_profile, :random) }
        let!(:custom_order_line1) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }
        let!(:custom_order_line2) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }
        let!(:custom_order_line3) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }

        let(:order_line_params1) { { id: custom_order_line1.id, quantity: 5 } }
        let(:order_line_params2) { { id: custom_order_line2.id, quantity: 10 } }
        let(:order_line_params3) { { id: custom_order_line3.id, quantity: 9 } }
        let(:order_lines_params) { [order_line_params1, order_line_params2, order_line_params3] }

        it 'updates the quantities of the exiting order lines' do
          expect(custom_order.order_lines.size).to eq(3)

          order_params = { id: custom_order.id, location_id: custom_location.id, order_lines: order_lines_params }
          custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call

          expect(custom_order_updater).to be_success
          updated_order = custom_order_updater.order
          expect(updated_order.id).to eq(custom_order.id)

          updated_order_lines = custom_order.reload.order_lines
          expect(custom_order.order_lines.size).to eq(3)
          expect(updated_order_lines.map(&:id)).to include(custom_order_line1.id, custom_order_line2.id, custom_order_line3.id)
          expect(updated_order_lines.map(&:quantity)).to include(5, 10, 9)
        end

        it 'deletes the order lines that are not passed' do
          expect(custom_order.order_lines.size).to eq(3)
          order_lines_params = [order_line_params1, order_line_params3]
          order_params = { id: custom_order.id, location_id: custom_location.id, order_lines: order_lines_params }
          custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call

          expect(custom_order_updater).to be_success
          updated_order = custom_order_updater.order
          expect(updated_order.id).to eq(custom_order.id)

          updated_order_lines = custom_order.reload.order_lines
          expect(custom_order.order_lines.size).to eq(2)
          expect(updated_order_lines.map(&:id)).to include(custom_order_line1.id, custom_order_line3.id)
          expect(updated_order_lines.map(&:id)).to_not include(custom_order_line2.id)
          expect(updated_order_lines.map(&:quantity)).to include(5, 9)
          expect(updated_order_lines.map(&:quantity)).to_not include(10)
        end
      end # existing order lines
    end # existing location

    context 'with existing custom order supplier records' do
      let(:supplier1) { create(:supplier_profile, :random) }
      let(:supplier2) { create(:supplier_profile, :random) }
      let!(:custom_order_supplier1) { create(:custom_order_supplier, order: custom_order, supplier_profile: supplier1) }
      let!(:custom_order_supplier2) { create(:custom_order_supplier, order: custom_order, supplier_profile: supplier2) }

      it 'does not create duplicate custom order supplier records' do
        expect(custom_order.custom_order_suppliers.size).to eq(2)
        order_params = { id: custom_order.id, suppliers: { supplier1.id.to_s => {}, supplier2.id.to_s => {} } }
        custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_updater).to be_success
        updated_order = custom_order_updater.order
        expect(updated_order.id).to eq(custom_order.id)
        expect(updated_order.custom_order_suppliers.size).to eq(2) # still 2
        expect(updated_order.custom_order_suppliers).to include(custom_order_supplier1, custom_order_supplier2)
      end

      it 'deletes any custom order supplier records that are not passed' do
        expect(custom_order.custom_order_suppliers.size).to eq(2)
        order_params = { id: custom_order.id, suppliers: { supplier2.id.to_s => {} } }
        custom_order_updater = Orders::Custom::Upsert.new(order_params: order_params).call

        expect(custom_order_updater).to be_success
        updated_order = custom_order_updater.order
        expect(updated_order.id).to eq(custom_order.id)
        expect(updated_order.custom_order_suppliers.size).to eq(1) # still 2
        expect(updated_order.custom_order_suppliers).to include(custom_order_supplier2)
        expect(updated_order.custom_order_suppliers).to_not include(custom_order_supplier1)
      end
    end

    context 'order confirmation' do
      let!(:confirmation_order_params) do
        {
          id: custom_order.id,
          custom_action: 'confirm'
        }.merge(order_params)
      end

      before do
        custom_order.update_columns(customer_profile_id: customer.id, status: %w[draft quoted].sample)

        # mock new order email sender
        email_sender = delayed_email_sender = double(Suppliers::Emails::SendNewOrderEmail)
        allow(Suppliers::Emails::SendNewOrderEmail).to receive(:new).and_return(email_sender)
        allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
        allow(delayed_email_sender).to receive(:call).and_return(true)

        # mock ameneded order notifier
        notifier = delayed_notifier = double(Orders::Notifications::SendOrderAmendedSupplierNotifications)
        allow(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).and_return(notifier)
        allow(notifier).to receive(:delay).and_return(delayed_notifier)
        allow(delayed_notifier).to receive(:call).and_return(true)
      end

      it 'comfirms a draft/quoted order and sets it to new' do
        custom_order_updater = Orders::Custom::Upsert.new(order_params: confirmation_order_params).call

        expect(custom_order_updater).to be_success
        updated_order = custom_order_updater.order
        expect(updated_order.id).to eq(custom_order.id)
        expect(updated_order.status).to eq('new')
      end

      it 'errors out if required fields are not passed' do
        missing_field = Orders::Custom::Upsert::REQUIRED_FIELDS.sample

        custom_order_updater = Orders::Custom::Upsert.new(order_params: confirmation_order_params.except(missing_field)).call
        expect(custom_order_updater).to_not be_success
        missing_field_name = I18n.t("admin.custom_order_fields.#{missing_field}")
        expect(custom_order_updater.errors).to include("Missing field => #{missing_field_name}")
      end

      context 'delivery date validation' do
        let!(:week_start) { Time.zone.now.beginning_of_week }
        let!(:month_start) { Time.zone.now.beginning_of_month }

        it 'errors out if delivery date before start of week' do
          delivery_at = [(week_start - 1.day), (week_start - 1.second)].sample
          confirmation_order_params_with_delivery_date = confirmation_order_params.merge({ delivery_at: delivery_at.strftime('%d-%m-%Y %H:%M') })

          custom_order_updater = Orders::Custom::Upsert.new(order_params: confirmation_order_params_with_delivery_date).call
          expect(custom_order_updater).to_not be_success
          expect(custom_order_updater.errors).to include('Delivery date needs to be set in future for the order to be picked up for invoicing')
        end

        context 'with billing frequency of monthly' do
          let!(:billing_details) { create(:billing_details, :random, customer_profile: customer, frequency: 'monthly') }

          it 'errors out if delivery date before start of month (only if start of month is after start of week)' do
            customer.reload
            if (month_start > week_start)
              delivery_at = [(month_start - 1.day), (month_start - 1.second)].sample
            else 
              delivery_at = [(week_start - 1.day), (week_start - 1.second)].sample
            end
            confirmation_order_params_with_delivery_date = confirmation_order_params.merge({ delivery_at: delivery_at.strftime('%d-%m-%Y %H:%M') })

            custom_order_updater = Orders::Custom::Upsert.new(order_params: confirmation_order_params_with_delivery_date).call
            expect(custom_order_updater).to_not be_success
            expect(custom_order_updater.errors).to include('Delivery date needs to be set in future for the order to be picked up for invoicing')
          end
        end
      end

      it 'syncs with the Hubspot customer account on order confirmation', hubspot: true do
        expect(Hubspot::SyncContact).to receive(:new).with(contact: customer.user)

        custom_order_updater = Orders::Custom::Upsert.new(order_params: confirmation_order_params).call
        expect(custom_order_updater).to be_success
      end

      it 'logs a `New Order Submitted` event', event_logs: true do
        expect(EventLogs::Create).to receive(:new).with(event_object: custom_order, event: 'new-custom-order-submitted')

        custom_order_updater = Orders::Custom::Upsert.new(order_params: confirmation_order_params).call
        expect(custom_order_updater).to be_success
      end

      context 'with an attached coupon', coupons: true do
        let!(:coupon) { create(:coupon, :random) }
        let!(:coupon_redeemder) { double(Coupons::Redeem) }
        before do
          # attach coupon to order
          custom_order.update_column(:coupon_id, coupon.id)
        end

        it 'redeems the coupon for the order' do
          expect(Coupons::Redeem).to receive(:new).with(coupon: coupon, order: custom_order).and_return(coupon_redeemder)
          expect(coupon_redeemder).to receive(:call)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: confirmation_order_params).call
          expect(custom_order_updater).to be_success
        end
      end # with attached coupon

      context 'with order line suppliers' do
        let(:supplier1) { create(:supplier_profile, :random) }
        let(:supplier2) { create(:supplier_profile, :random) }
        let(:order_line_params1) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier1.id, quantity: 5, note: Faker::Lorem.sentence } }
        let(:order_line_params2) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier2.id, quantity: 5, note: Faker::Lorem.sentence } }
        let(:order_lines_params) { [order_line_params1, order_line_params2] }

        let!(:confirmation_order_params_with_order_lines) do
          {
            location_name: Faker::Name.name,
            order_lines: order_lines_params
          }.merge(confirmation_order_params)
        end

        it 'sends a new order email to the order suppliers' do
          expect(Suppliers::Emails::SendNewOrderEmail).to receive(:new).with(order: custom_order, supplier: supplier1)
          expect(Suppliers::Emails::SendNewOrderEmail).to receive(:new).with(order: custom_order, supplier: supplier2)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: confirmation_order_params_with_order_lines).call
          expect(custom_order_updater).to be_success
        end
      end

      context 'for a non draft/quoted order' do
        let!(:updated_confirmation_order_params) do
          {
            customer_profile_id: customer.id,
            delivery_at: Time.zone.now.to_s,
            order_category: 'catering-services',
            delivery_address: Faker::Address.street_address,
            credit_card_id: credit_card.id,
            event_name: Faker::Name.name,
            suburb_id: suburb.id
          }.merge(confirmation_order_params)
        end

        before do
          custom_order.update_column(:status, %w[new amended confirmed].sample)
        end

        it 'confirms a non-draft order and sets it to ammended' do          
          custom_order_updater = Orders::Custom::Upsert.new(order_params: updated_confirmation_order_params).call
          expect(custom_order_updater).to be_success

          updated_order = custom_order_updater.order
          expect(updated_order.id).to eq(custom_order.id)
          expect(updated_order.status).to eq('amended')
        end

        it 'request to send order amended notifications (emails to suppliers)' do
          expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: custom_order, notify_now: true)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: updated_confirmation_order_params).call
          expect(custom_order_updater).to be_success
        end
      end
    end

    context 'Quoting' do
      before do
        custom_order.update_column(:status, 'draft')

        # mock quote document generator
        document_generator = double(Documents::Generate::CustomerOrderDetails)
        allow(Documents::Generate::CustomerOrderDetails).to receive(:new).and_return(document_generator)
        allow(document_generator).to receive(:call).and_return(true)

        # mock email sender
        email_sender = double(Customers::Emails::SendEventOrderQuoteEmail)
        allow(Customers::Emails::SendEventOrderQuoteEmail).to receive(:new).and_return(email_sender)
        sender_response = OpenStruct.new(success?: true, sent_notification: true, errors: [])
        allow(email_sender).to receive(:call).and_return(sender_response)
      end

      context 'saving a quote - action = save_quote' do
        let!(:quote_order_params) do
          {
            id: custom_order.id,
            custom_action: 'save_quote'
          }.merge(order_params)
        end

        it 'saves the order as quoted' do
          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call

          expect(custom_order_updater).to be_success          
          updated_custom_order = custom_order_updater.order
          expect(updated_custom_order.status).to eq('quoted')
        end

        it 'requests the promotions to be sycned with the custom order', promotions: true do
          expect(Promotions::SyncWithOrder).to receive(:new).with(order: custom_order)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
          expect(custom_order_updater).to be_success
        end

        it 'generates a quote document' do
          expected_document_reference = "#{Customers::Emails::SendEventOrderQuoteEmail::EMAIL_TEMPLATE}-#{custom_order.customer_profile_id}-#{custom_order.version_ref}"
          expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: custom_order, reference: expected_document_reference, variation: 'quote')

          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
          expect(custom_order_updater).to be_success
        end

        it 'does not sends a quote email', notifications: true do
          expect(Customers::Emails::SendEventOrderQuoteEmail).to_not receive(:new)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
          expect(custom_order_updater).to be_success
          updated_order = custom_order_updater.order
          expect(updated_order.id).to eq(custom_order.id)
        end

        it 'does not sync the Hubspot customer account when quoting an order', hubspot: true do
          expect(Hubspot::SyncContact).to_not receive(:new)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
          expect(custom_order_updater).to be_success
        end

        it 'errors out if required fields are not passed' do
          missing_field = Orders::Custom::Upsert::REQUIRED_FIELDS.sample
          quote_order_params_with_missing_fields = quote_order_params.except(missing_field)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params_with_missing_fields).call

          expect(custom_order_updater).to_not be_success
          missing_field_name = I18n.t("admin.custom_order_fields.#{missing_field}")
          expect(custom_order_updater.errors).to include("Missing field => #{missing_field_name}")
        end

        context 'delivery date validation' do
          let!(:week_start) { Time.zone.now.beginning_of_week }
          let!(:month_start) { Time.zone.now.beginning_of_month }

          it 'errors out if delivery date before start of week' do
            delivery_at = [(week_start - 1.day), (week_start - 1.second)].sample
            quote_order_params_with_delivery_date = quote_order_params.merge({ delivery_at: delivery_at.strftime('%d-%m-%Y %H:%M') })

            custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params_with_delivery_date).call
            expect(custom_order_updater).to_not be_success
            expect(custom_order_updater.errors).to include('Delivery date needs to be set in future for the order to be picked up for invoicing')
          end

          context 'with billing frequency of monthly' do
            let!(:billing_details) { create(:billing_details, :random, customer_profile: customer, frequency: 'monthly') }

            it 'errors out if delivery date before start of month (only if start of month is after start of week)' do
              customer.reload
              if (month_start > week_start)
                delivery_at = [(month_start - 1.day), (month_start - 1.second)].sample
              else 
                delivery_at = [(week_start - 1.day), (week_start - 1.second)].sample
              end
              quote_order_params_with_delivery_date = quote_order_params.merge({ delivery_at: delivery_at.strftime('%d-%m-%Y %H:%M') })

              custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params_with_delivery_date).call
              expect(custom_order_updater).to_not be_success
              expect(custom_order_updater.errors).to include('Delivery date needs to be set in future for the order to be picked up for invoicing')
            end
          end
        end

        context 'Event Logs', event_logs: true do
          it 'logs a `New Order Quoted` event' do
            expect(EventLogs::Create).to receive(:new).with(event_object: custom_order, event: 'new-custom-order-quoted')

            custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
            expect(custom_order_updater).to be_success
          end

          it 'does not log a `New Order Quote` event if it is already present' do
            create(:event_log, :random, loggable: custom_order, event: 'new-custom-order-quoted')
            expect(EventLogs::Create).to_not receive(:new).with(event_object: custom_order, event: 'new-custom-order-quoted')

            custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
            expect(custom_order_updater).to be_success
          end
        end
      end # saving a quote

      context 'sending a quote - action = send_quote' do
        let!(:quote_order_params) do
          {
            id: custom_order.id,
            custom_action: 'send_quote',
            quote_emails: 3.times.map{|_| Faker::Internet.email }.join(';'),
            quote_message: Faker::Lorem.sentences(number: 5).join(' ')
          }.merge(order_params)
        end

        it 'saves the order as quoted' do
          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call

          expect(custom_order_updater).to be_success          
          updated_custom_order = custom_order_updater.order
          expect(updated_custom_order.status).to eq('quoted')
        end

        it 'sends a quote email to passed in quote emails', notifications: true do
          expect(Customers::Emails::SendEventOrderQuoteEmail).to receive(:new).with(customer: customer, order: custom_order, quote_emails: quote_order_params[:quote_emails], quote_message: quote_order_params[:quote_message])

          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
          expect(custom_order_updater).to be_success
          updated_order = custom_order_updater.order
          expect(updated_order.id).to eq(custom_order.id)
        end

        it 'does not sync the Hubspot customer account when quoting an order', hubspot: true do
          expect(Hubspot::SyncContact).to_not receive(:new)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
          expect(custom_order_updater).to be_success
        end

        it 'errors out if required fields are not passed' do
          missing_field = Orders::Custom::Upsert::REQUIRED_FIELDS.sample
          quote_order_params_with_missing_fields = quote_order_params.except(missing_field)

          custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params_with_missing_fields).call

          expect(custom_order_updater).to_not be_success
          missing_field_name = I18n.t("admin.custom_order_fields.#{missing_field}")
          expect(custom_order_updater.errors).to include("Missing field => #{missing_field_name}")
        end

        context 'delivery date validation' do
          let!(:week_start) { Time.zone.now.beginning_of_week }
          let!(:month_start) { Time.zone.now.beginning_of_month }

          it 'errors out if delivery date before start of week' do
            delivery_at = [(week_start - 1.day), (week_start - 1.second)].sample
            quote_order_params_with_delivery_date = quote_order_params.merge({ delivery_at: delivery_at.strftime('%d-%m-%Y %H:%M') })

            custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params_with_delivery_date).call
            expect(custom_order_updater).to_not be_success
            expect(custom_order_updater.errors).to include('Delivery date needs to be set in future for the order to be picked up for invoicing')
          end

          context 'with billing frequency of monthly' do
            let!(:billing_details) { create(:billing_details, :random, customer_profile: customer, frequency: 'monthly') }

            it 'errors out if delivery date before start of month (only if start of month is after start of week)' do
              customer.reload
              if (month_start > week_start)
                delivery_at = [(month_start - 1.day), (month_start - 1.second)].sample
              else 
                delivery_at = [(week_start - 1.day), (week_start - 1.second)].sample
              end
              quote_order_params_with_delivery_date = quote_order_params.merge({ delivery_at: delivery_at.strftime('%d-%m-%Y %H:%M') })

              custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params_with_delivery_date).call
              expect(custom_order_updater).to_not be_success
              expect(custom_order_updater.errors).to include('Delivery date needs to be set in future for the order to be picked up for invoicing')
            end
          end
        end

        context 'Event Logs', event_logs: true do
          it 'logs a `New Order Quoted` event' do
            expect(EventLogs::Create).to receive(:new).with(event_object: custom_order, event: 'new-custom-order-quoted')

            custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
            expect(custom_order_updater).to be_success
          end

          it 'does not log a `New Order Quote` event if it is already present' do
            create(:event_log, :random, loggable: custom_order, event: 'new-custom-order-quoted')
            expect(EventLogs::Create).to_not receive(:new).with(event_object: custom_order, event: 'new-custom-order-quoted')

            custom_order_updater = Orders::Custom::Upsert.new(order_params: quote_order_params).call
            expect(custom_order_updater).to be_success
          end
        end # Event Logs
      end # Sending a quote
    end # Order Quoting
  end # Updating an existing custom order
end

