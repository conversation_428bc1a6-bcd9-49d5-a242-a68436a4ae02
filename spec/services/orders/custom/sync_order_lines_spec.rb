require 'rails_helper'

RSpec.describe Orders::Custom::SyncOrderLines, type: :service, orders: true, custom_orders: true do

  let!(:custom_order) { create(:order, :draft, :custom_order) }
  let!(:custom_location) { create(:location, :random, order: custom_order) }

  context 'New Order Lines - with order line params' do
    let(:supplier) { create(:supplier_profile, :random) }
    let(:order_line_params1) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier.id, quantity: 5, note: Faker::Lorem.sentence } }
    let(:order_line_params2) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier.id, quantity: 5, note: Faker::Lorem.sentence } }
    let(:order_line_params3) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier.id, quantity: 5, note: Faker::Lorem.sentence } }
    let(:order_lines_params) { [order_line_params1, order_line_params2, order_line_params3] }

    it 'creates a custom menu section for the supplier' do
      skip 'Already tested in OrderLines::UpsertCustomOrderLine'
    end

    it 'creates custom menu items for the supplier' do
      skip 'Already tested in OrderLines::UpsertCustomOrderLine'
    end

    it 'creates new order lines for the new location' do
      custom_order_lines_creator = Orders::Custom::SyncOrderLines.new(location: custom_location, order_lines_params: order_lines_params).call

      expect(custom_order_lines_creator).to be_success
      created_order_lines = custom_order_lines_creator.order_lines
      expect(created_order_lines).to be_present
      expect(created_order_lines.map(&:order)).to include(custom_order)
      expect(created_order_lines.map(&:location)).to include(custom_location)
      expect(created_order_lines.map(&:name)).to include(order_line_params1[:menu_item_description], order_line_params2[:menu_item_description], order_line_params3[:menu_item_description])
      # other order line attributes already tested in OrderLines::UpsertCustomOrderLine
    end
  end
  context 'with exiting order lines for the location' do
    let(:supplier) { create(:supplier_profile, :random) }
    let!(:custom_order_line1) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }
    let!(:custom_order_line2) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }
    let!(:custom_order_line3) { create(:order_line, :random, location: custom_location, order: custom_order, supplier_profile: supplier) }

    let(:order_line_params1) { { id: custom_order_line1.id, quantity: 5 } }
    let(:order_line_params2) { { id: custom_order_line2.id, quantity: 10 } }
    let(:order_line_params3) { { id: custom_order_line3.id, quantity: 9 } }
    let(:order_lines_params) { [order_line_params1, order_line_params2, order_line_params3] }

    it 'updates the quantities of the exiting order lines' do
      expect(custom_location.order_lines.size).to eq(3)
      custom_order_lines_updater = Orders::Custom::SyncOrderLines.new(location: custom_location, order_lines_params: order_lines_params).call

      expect(custom_order_lines_updater).to be_success

      updated_order_lines = custom_order_lines_updater.order_lines
      expect(custom_order.order_lines.size).to eq(3)
      expect(updated_order_lines.map(&:id)).to include(custom_order_line1.id, custom_order_line2.id, custom_order_line3.id)
      expect(updated_order_lines.map(&:quantity)).to include(5, 10, 9)
    end

    it 'deletes the order lines that are not passed' do
      expect(custom_location.order_lines.size).to eq(3)
      order_lines_params = [order_line_params1, order_line_params3]
      custom_order_lines_updater = Orders::Custom::SyncOrderLines.new(location: custom_location, order_lines_params: order_lines_params).call

      expect(custom_order_lines_updater).to be_success

      updated_order_lines = custom_order_lines_updater.order_lines
      expect(custom_location.order_lines.size).to eq(2)
      expect(updated_order_lines.map(&:id)).to include(custom_order_line1.id, custom_order_line3.id)
      expect(updated_order_lines.map(&:id)).to_not include(custom_order_line2.id)
      expect(updated_order_lines.map(&:quantity)).to include(5, 9)
      expect(updated_order_lines.map(&:quantity)).to_not include(10)
    end
  end

end

