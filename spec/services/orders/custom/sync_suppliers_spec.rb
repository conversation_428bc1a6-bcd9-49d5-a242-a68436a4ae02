require 'rails_helper'

RSpec.describe Orders::Custom::SyncSuppliers, type: :service, orders: true, custom_orders: true do

  let(:custom_order) { create(:order, :draft, :custom_order) }
  let(:supplier1) { create(:supplier_profile, :random) }
  let(:supplier2) { create(:supplier_profile, :random) }

  context 'New Custom Order' do

    it 'creates custom order supplier records' do
      suppliers_params = { supplier1.id.to_s => {}, supplier2.id.to_s => {} }
      custom_order_supplier_creator = Orders::Custom::SyncSuppliers.new(order: custom_order, suppliers_params: suppliers_params).call

      expect(custom_order_supplier_creator).to be_success
      created_order_suppliers = custom_order_supplier_creator.suppliers
      expect(created_order_suppliers.map(&:supplier_profile)).to include(supplier1, supplier2)
      expect(created_order_suppliers.map(&:order)).to include(custom_order)
    end
  end

  context 'with existing custom order supplier records' do

    let!(:custom_order_supplier1) { create(:custom_order_supplier, order: custom_order, supplier_profile: supplier1) }
    let!(:custom_order_supplier2) { create(:custom_order_supplier, order: custom_order, supplier_profile: supplier2) }

    it 'does not create duplicate custom order supplier records' do
      expect(custom_order.custom_order_suppliers.size).to eq(2)
      suppliers_params = { supplier1.id.to_s => {}, supplier2.id.to_s => {} }
      custom_order_supplier_updater = Orders::Custom::SyncSuppliers.new(order: custom_order, suppliers_params: suppliers_params).call

      expect(custom_order_supplier_updater).to be_success
      custom_order_suppliers = custom_order_supplier_updater.suppliers
      expect(custom_order.reload.custom_order_suppliers.size).to eq(2) # still 2
      expect(custom_order_suppliers).to include(custom_order_supplier1, custom_order_supplier2)
    end

    it 'deletes any custom order supplier records that are not passed' do
      expect(custom_order.custom_order_suppliers.size).to eq(2)
      suppliers_params = { supplier2.id.to_s => {} }
      custom_order_suppliers_updater = Orders::Custom::SyncSuppliers.new(order: custom_order, suppliers_params: suppliers_params).call

      expect(custom_order_suppliers_updater).to be_success
      custom_order_suppliers = custom_order_suppliers_updater.suppliers
      expect(custom_order.reload.custom_order_suppliers.size).to eq(1)
      expect(custom_order_suppliers).to include(custom_order_supplier2)
      expect(custom_order_suppliers).to_not include(custom_order_supplier1)
    end
  end
end

