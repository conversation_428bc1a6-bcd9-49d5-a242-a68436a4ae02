require 'rails_helper'

RSpec.describe Orders::Notifications::RequestLoadingDockCode, type: :service, orders: true, loading_docks: true, notifications: true do

  let!(:notification_date) { Time.zone.now.beginning_of_week + 2.days + 10.hours } # Tuesday 10am

  let!(:customer1) { create(:customer_profile, :random, :with_flags) }
  let!(:order11) { create(:order, :confirmed, customer_profile: customer1, delivery_type: 'loading_dock', delivery_at: notification_date + 3.days) }
  let!(:order12) { create(:order, :confirmed, customer_profile: customer1, delivery_type: 'loading_dock', delivery_at: notification_date + 3.days) }

  let!(:customer2) { create(:customer_profile, :random, :with_flags) }
  let!(:order21) { create(:order, :confirmed, customer_profile: customer2, delivery_type: 'loading_dock', delivery_at: notification_date + 3.days) }
  let!(:order22) { create(:order, :confirmed, customer_profile: customer2, delivery_type: 'loading_dock', delivery_at: notification_date + 3.days) }

  before do
    email_sender = delayed_email_sender = double(Customers::Emails::SendLoadingDockRequestEmail)
    allow(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  it 'sends emails to customers with orders requiring loading dock code' do
    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer1, orders: [order11, order12], date: notification_date)
    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer2, orders: [order21, order22], date: notification_date)

    notifications_sender = Orders::Notifications::RequestLoadingDockCode.new(date: notification_date).call
    expect(notifications_sender).to be_success

    expect(notifications_sender.notified_customers.keys).to include(customer1, customer2)
    expect(notifications_sender.notified_customers[customer1]).to match_array([order11, order12])
    expect(notifications_sender.notified_customers[customer2]).to match_array([order21, order22])
  end

  it 'only notifies for orders to be delivered in the next 3 days' do
    [order12, order21].each{|order| order.update_column(:delivery_at, [notification_date - 2.days, notification_date + 5.days].sample) }

    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer1, orders: [order11], date: notification_date)
    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer2, orders: [order22], date: notification_date)

    notifications_sender = Orders::Notifications::RequestLoadingDockCode.new(date: notification_date).call
    expect(notifications_sender).to be_success
  end

  it 'only notifies for orders which are new/amended/confirmed/pending' do
    [order12, order21].each{|order| order.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[new amended confirmed pending]).sample) }

    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer1, orders: [order11], date: notification_date)
    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer2, orders: [order22], date: notification_date)

    notifications_sender = Orders::Notifications::RequestLoadingDockCode.new(date: notification_date).call
    expect(notifications_sender).to be_success
  end

  it 'only notifies for orders with delivery type of `loading dock`' do
    [order11, order22].each{|order| order.update_column(:delivery_type, (Order::VALID_DELIVERY_TYPES - ['loading_dock']).sample) }

    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer1, orders: [order12], date: notification_date)
    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer2, orders: [order21], date: notification_date)

    notifications_sender = Orders::Notifications::RequestLoadingDockCode.new(date: notification_date).call
    expect(notifications_sender).to be_success
  end

  it 'only notifies for orders with no attached loading dock' do
    loading_dock = create(:loading_dock, :random)
    [order12, order22].each{|order| order.update_column(:loading_dock_id, loading_dock.id) }

    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer1, orders: [order11], date: notification_date)
    expect(Customers::Emails::SendLoadingDockRequestEmail).to receive(:new).with(customer: customer2, orders: [order21], date: notification_date)

    notifications_sender = Orders::Notifications::RequestLoadingDockCode.new(date: notification_date).call
    expect(notifications_sender).to be_success
  end

end