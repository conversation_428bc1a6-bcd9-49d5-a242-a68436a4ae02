require 'rails_helper'

RSpec.describe Orders::Notifications::SendOrderAmendedNotifications, type: :service, notifications: true, orders: true, suppliers: true do

  let!(:notification_time) { Time.zone.now.beginning_of_day + 8.hours }

  let!(:order1) { create(:order, :amended, order_type: 'one-off', updated_at: notification_time - 1.hours, suppliers_notified_at: notification_time - 2.hours) }
  let!(:order2) { create(:order, :amended, order_type: 'one-off', updated_at: notification_time - 1.hours, suppliers_notified_at: notification_time - 2.hours) }
  let!(:order3) { create(:order, :amended, order_type: 'one-off', updated_at: notification_time - 1.hours, suppliers_notified_at: notification_time - 2.hours) }

  before do
    # mock supplier notifications sender
    supplier_notification_sender = delayed_supplier_notification_sender = double(Orders::Notifications::SendOrderAmendedSupplierNotifications)
    allow(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).and_return(supplier_notification_sender)
    successfull_response = OpenStruct.new(success?: true, errors: [], sent_notifications: ['sent-supplier-notification'])
    allow(supplier_notification_sender).to receive(:call).and_return(successfull_response)

    # mock delayed suppluer notifications sender
    allow(supplier_notification_sender).to receive(:delay).and_return(delayed_supplier_notification_sender)
    allow(delayed_supplier_notification_sender).to receive(:call).and_return(successfull_response)
  end

  it 'sends notifications for all future amended (one-off) orders' do
    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order1)
    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order2)
    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order3)

    notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: notification_time).call

    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_orders).to include(order1, order2, order3)
    expect(notifications_sender.sent_notifications.size).to eq(3)
  end

  it 'doesn\'t send notifications for non-template recurrent orders' do
    order2.update_columns(order_type: 'recurrent', template_id: order2.id)
    order3.update_columns(order_type: 'recurrent', template_id: order2.id)

    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order1) # still one-off
    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order2) # recurrent template
    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to_not receive(:new).with(order: order3) # recurrent non-template

    notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: notification_time).call

    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_orders).to_not include(order3)
    expect(notifications_sender.notified_orders).to include(order1, order2)
  end

  it 'doesn\'t send notifications for order whose supplier have already being notified after the order update' do
    order2.update_column(:updated_at, notification_time - 3.hours)

    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order1) # order amended after supplier notification
    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to_not receive(:new).with(order: order2) # order amended before supplier notification
    expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order3) # order amended after supplier notification

    notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: notification_time).call

    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_orders).to_not include(order2)
    expect(notifications_sender.notified_orders).to include(order1, order3)
  end

  context 'with order lines and changes (even if order is not updated)' do
    let!(:order_line11) { create(:order_line, :random, status: 'amended', order: order1, updated_at: notification_time - 1.hours) }
    let!(:order_line21) { create(:order_line, :random, status: 'amended', order: order2, updated_at: notification_time - 1.hours) }
    let!(:order_line31) { create(:order_line, :random, status: 'amended', order: order3, updated_at: notification_time - 1.hours) }

    before do
      [order1, order2, order3].each do |order|
        order.update_column(:updated_at, notification_time - 3.hours) # mark as order as being updated before supplier notification
      end
    end

    it 'only notifies for orders with an amended order line' do
      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order1) # amended order lines
      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order2) # amended order lines
      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order3) # amended order lines

      notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: notification_time).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.notified_orders).to include(order1, order2, order3)
    end

    it 'does not notify for order with non amended order lines' do
      order_line31.update_column(:status, %w[notifed approved].sample)

      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order1) # amended order lines
      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order2) # amended order lines
      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to_not receive(:new).with(order: order3) # order line already marked as approved / notified

      notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: notification_time).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.notified_orders).to_not include(order3)
      expect(notifications_sender.notified_orders).to include(order1, order2)
    end

    it 'does not notify for order whose supplier who have already being notified after their order line(s) update' do
      order_line21.update_column(:updated_at, notification_time - 3.hours)

      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order1) # amended order lines after supplier notification
      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to_not receive(:new).with(order: order2) # amended order lines before supplier notification
      expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order3) # amended order lines after supplier notification

      notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: notification_time).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.notified_orders).to_not include(order2)
      expect(notifications_sender.notified_orders).to include(order1, order3)
    end

    context 'with no order line changes after supplier notifications' do
      before do
        [order_line11, order_line21, order_line31].each do |order_line|
          order_line.update_column(:updated_at, notification_time - 3.hours) # mark order line as changed before supplier notification
        end
      end

      it 'doesn\'t send any update notifications' do
        expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to_not receive(:new).with(order: order1) # amended order lines before supplier notification
        expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to_not receive(:new).with(order: order2) # amended order lines before supplier notification
        expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to_not receive(:new).with(order: order3) # amended order lines before supplier notification

        notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: notification_time).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.notified_orders).to be_blank
      end

      it 'notfies for order whose order line has been removed' do
        [order_line11, order_line31].each(&:destroy) # creates versions

        expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order1) # amended order lines before supplier notification
        expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to_not receive(:new).with(order: order2) # amended order lines before supplier notification
        expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order3) # amended order lines before supplier notification

        notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: notification_time).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.notified_orders).to include(order1, order3)
        expect(notifications_sender.notified_orders).to_not include(order2)
      end
    end # with no order line changes
  end # with order line and changes
end