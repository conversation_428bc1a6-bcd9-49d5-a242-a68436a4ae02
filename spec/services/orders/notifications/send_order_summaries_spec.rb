require 'rails_helper'

RSpec.describe Orders::Notifications::SendOrderSummaries, type: :service, orders: true, notifications: true, emails: true do

  let!(:tuesday) { Time.zone.parse('2020-09-15 00:00:00') }
  let!(:friday) { Time.zone.parse('2020-09-18 00:00:00') }
  let!(:saturday) { Time.zone.parse('2020-09-19 00:00:00') }
  let!(:sunday) { Time.zone.parse('2020-09-20 00:00:00') }
  let!(:weekend_summary_day) { [saturday, sunday].sample }

  before do
    supplier_notification_sender = double(Suppliers::Notifications::SendOrderSummariesForDay)
    allow(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).and_return(supplier_notification_sender)
    valid_supplier_notification_response = OpenStruct.new(success?: true, sent_notifications: ['sent-supplier-notification'], errors: [])
    allow(supplier_notification_sender).to receive(:call).and_return(valid_supplier_notification_response)

    customer_notification_sender = double(Customers::Notifications::SendOrderSummariesForDay)
    allow(Customers::Notifications::SendOrderSummariesForDay).to receive(:new).and_return(customer_notification_sender)
    valid_customer_notification_response = OpenStruct.new(success?: true, sent_notifications: ['sent-customer-notification'], errors: [])
    allow(customer_notification_sender).to receive(:call).and_return(valid_customer_notification_response)
  end

  context 'Summary type of daily' do
    let!(:summary_type) { 'daily' }

    context 'summary day for a weekday (non-friday)' do
      it 'sends supplier summary notifications for the next days orders' do
        expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: tuesday + 1.day, summary_type: summary_type)

        notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: tuesday, summary_type: summary_type).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.supplier_notifications.size).to eq(1)
      end

      it 'sends customer summary notifications for the next days orders' do
        expect(Customers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: tuesday + 1.day)

        notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: tuesday, summary_type: summary_type).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.customer_notifications.size).to eq(1)
      end
    end

    context 'summary day of friday' do
      it 'sends supplier summary notifications for the next 3 days orders' do
        expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 1.day, summary_type: summary_type)
        expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 2.days, summary_type: summary_type)
        expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 3.days, summary_type: summary_type)

        notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: friday, summary_type: summary_type).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.supplier_notifications.size).to eq(3)
      end

      it 'sends customer summary notifications for the next 3 days orders' do
        expect(Customers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 1.day)
        expect(Customers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 2.days)
        expect(Customers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 3.days)

        notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: friday, summary_type: summary_type).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.customer_notifications.size).to eq(3)
      end
    end

    it 'doesn\'t send any supplier/customer order summary notifications on weekend' do
      expect(Suppliers::Notifications::SendOrderSummariesForDay).to_not receive(:new).with(summary_day: weekend_summary_day + 1.day, summary_type: summary_type)
      expect(Customers::Notifications::SendOrderSummariesForDay).to_not receive(:new).with(summary_day: weekend_summary_day + 1.day)

      notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: weekend_summary_day, summary_type: summary_type).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.supplier_notifications).to be_blank
      expect(notifications_sender.customer_notifications).to be_blank
    end
  end

  context 'Summary type of morning' do
    let!(:summary_type) { 'morning' }
    let!(:weekday_summary_day) { [tuesday, friday].sample }

    it 'sends supplier summary notifications for the passed in weekday summary day' do
      expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: weekday_summary_day, summary_type: summary_type)

      notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: weekday_summary_day, summary_type: summary_type).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.supplier_notifications.size).to eq(1)
    end

    it 'does not sent any customer summary notifications for the passed in non-weekend summary day' do
      expect(Customers::Notifications::SendOrderSummariesForDay).to_not receive(:new).with(summary_day: weekday_summary_day)

      notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: weekday_summary_day, summary_type: summary_type).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.customer_notifications).to be_blank
    end

    it 'does not send any supplier/customer order summary notification for a passed in weekend summary day' do
      expect(Suppliers::Notifications::SendOrderSummariesForDay).to_not receive(:new).with(summary_day: weekend_summary_day, summary_type: summary_type)
      expect(Customers::Notifications::SendOrderSummariesForDay).to_not receive(:new).with(summary_day: weekend_summary_day)

      notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: weekend_summary_day, summary_type: summary_type).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.supplier_notifications).to be_blank
      expect(notifications_sender.customer_notifications).to be_blank
    end
  end

  context 'Summary type of reminder' do
    let!(:summary_type) { 'reminder' }

    context 'summary day for a weekday (non-friday)' do
      it 'sends supplier reminder notifications for the orders delivered in the next 2 days' do
        expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: tuesday + 2.days, summary_type: summary_type)

        notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: tuesday, summary_type: summary_type).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.supplier_notifications.size).to eq(1)
      end
    end

    context 'summary day of friday' do
      it 'sends supplier reminder notifications for the next 3 days orders' do
        expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 2.days, summary_type: summary_type)
        expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 3.days, summary_type: summary_type)
        expect(Suppliers::Notifications::SendOrderSummariesForDay).to receive(:new).with(summary_day: friday + 4.days, summary_type: summary_type)

        notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: friday, summary_type: summary_type).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.supplier_notifications.size).to eq(3)
      end
    end

    it 'doesn\'t send any supplier order reminder notifications on weekend' do
      expect(Suppliers::Notifications::SendOrderSummariesForDay).to_not receive(:new).with(summary_day: weekend_summary_day + 2.days, summary_type: summary_type)

      notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: weekend_summary_day, summary_type: summary_type).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.supplier_notifications).to be_blank
      expect(notifications_sender.customer_notifications).to be_blank
    end
  end

end
