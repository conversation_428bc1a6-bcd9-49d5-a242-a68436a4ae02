require 'rails_helper'

RSpec.describe Orders::Notifications::NotifyAccountManagers, type: :service, orders: true, notifications: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :new, order_type: 'one-off', customer_profile: customer) }

  let!(:account_manager1) { create(:customer_profile, :random) }
  let!(:account_manager2) { create(:customer_profile, :random) }

  let!(:manager_permissions1) { create(:access_permission, admin: account_manager1, customer_profile: customer, scope: 'account_manager') }
  let!(:manager_permissions2) { create(:access_permission, admin: account_manager2, customer_profile: customer, scope: 'account_manager') }

  let!(:pantry_category) { create(:category, :random, group: 'kitchen-supplies') }
  let!(:catering_category) { create(:category, :random, group: 'catering-services') }

  let!(:email_sender) { double(Admin::Emails::SendManagedOrderEmail) }
  let!(:category_retriever) { double(Orders::RetrieveMajorOrderCategory) }

  before do
    # mock sending email successfully
    allow(Admin::Emails::SendManagedOrderEmail).to receive(:new).and_return(email_sender)
    email_success_response = OpenStruct.new(success?: true, sent_notification: 'sent-email')
    allow(email_sender).to receive(:call).and_return(email_success_response)

    # mock retrieving major category
    allow(Orders::RetrieveMajorOrderCategory).to receive(:new).and_return(category_retriever)
    allow(category_retriever).to receive(:call).and_return(pantry_category)
  end

  it 'sends the emails to all the account managers' do
    expect(Admin::Emails::SendManagedOrderEmail).to receive(:new).with(order: order, account_manager: account_manager1)
    expect(Admin::Emails::SendManagedOrderEmail).to receive(:new).with(order: order, account_manager: account_manager2)

    notifications_sender = Orders::Notifications::NotifyAccountManagers.new(order: order).call
    expect(notifications_sender).to be_success
  end

  it 'only sends the emails to all active account managers' do
    manager_permissions2.update_column(:active, false) # deactivate access permissions for account_manager2

    expect(Admin::Emails::SendManagedOrderEmail).to receive(:new).with(order: order, account_manager: account_manager1)
    expect(Admin::Emails::SendManagedOrderEmail).to_not receive(:new).with(order: order, account_manager: account_manager2)

    notifications_sender = Orders::Notifications::NotifyAccountManagers.new(order: order).call
    expect(notifications_sender).to be_success
  end

  context 'errors' do
    it 'returns with errors if the order is missing' do
      notifications_sender = Orders::Notifications::NotifyAccountManagers.new(order: nil).call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.errors).to include('Cannot notify without an order')
    end

    it 'returns with warnings if the order customer does not have any account managers' do
      # associated permissions to another customer
      customer2 = create(:customer_profile, :random)
      [manager_permissions1, manager_permissions2].each do |permission|
        permission.update_column(:customer_profile_id, customer2.id)
      end

      notifications_sender = Orders::Notifications::NotifyAccountManagers.new(order: order).call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.warnings).to include('Order customer does not have any account managers')
    end

    it 'returns with warnings if order is not a one-off order' do
      order.update_column(:order_type, 'recurrent')
      notifications_sender = Orders::Notifications::NotifyAccountManagers.new(order: order).call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.warnings).to include('Order is not a one-off order')
    end

    it 'returns with warnings if order is not a majorly pantry order' do
      allow(category_retriever).to receive(:call).and_return(catering_category)
      notifications_sender = Orders::Notifications::NotifyAccountManagers.new(order: order).call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.warnings).to include('Order is not pantry order')
    end

    context 'with email sender errors' do
      before do
        # mock sending email with errors
        email_errored_response = OpenStruct.new(success?: false, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(email_errored_response)
      end

      it 'returns with error if the order is missing' do
        notifications_sender = Orders::Notifications::NotifyAccountManagers.new(order: order).call

        expect(notifications_sender).to_not be_success
        expect(notifications_sender.errors).to include('email-sender-error')
      end
    end # email sending errors
  end # errors

end