require 'rails_helper'

RSpec.describe Orders::Notifications::SendOrderAmendedSupplierNotifications, type: :service, orders: true, suppliers: true, notifications: true, order_amended_email: true do

  let!(:order) { create(:order, :amended, suppliers_notified_at: Time.zone.now - 40.minutes, updated_at: Time.zone.now - 30.minutes) }

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:order_line11) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
  let!(:order_line12) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
  let!(:order_line13) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:order_line21) { create(:order_line, :random, order: order, supplier_profile: supplier2) }
  let!(:order_line22) { create(:order_line, :random, order: order, supplier_profile: supplier2) }
  let!(:order_line23) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

  before do
    order.reload

    # mock email sender
    email_sender = double(Suppliers::Emails::SendOrderAmendedEmail)
    allow(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).and_return(email_sender)
    email_sender_response = OpenStruct.new(success?: true, sent_notification: 'email', errors: [])
    allow(email_sender).to receive(:call).and_return(email_sender_response)
  end

  it 'does not send notifications for an amended order without any order or order line changes' do
    expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(order: order, supplier: supplier1, detail_changes: anything, item_changes: anything)
    expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(order: order, supplier: supplier2, detail_changes: anything, item_changes: anything)

    notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

    expect(notifications_sender).to be_success # no errors
    expect(notifications_sender.sent_notifications).to be_blank
  end

  context 'with order changes' do
    before do
      changed_field = Orders::Notifications::SendOrderAmendedSupplierNotifications::ORDER_CHANGE_FIELDS.sample
      changed_value = case changed_field
      when :delivery_at
        (Time.zone.now + rand(20).hours)
      when :delivery_type
        (Order::VALID_DELIVERY_TYPES - [order.delivery_type].compact).sample
      else
        'changed value'
      end
      order.update(Hash[changed_field, changed_value]) # creates a version
      order.update_column(:updated_at, Time.zone.now - 30.minutes) # reset the order as being updated 30 mins prior to skip notification time check
    end

    it 'notifies the suppliers about order changes' do
      expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(2)
    end

    it 'does not send notifications if there are no order changes (order versions)' do
      order.versions.each(&:destroy)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications).to be_blank
    end

    it 'does not send notifications if there order changes for non-notifiable fields like name' do
      order.versions.each(&:destroy)
      order.update_column(:name, 'changed value') # creates a version but not picked up
      order.update_column(:updated_at, Time.zone.now - 30.minutes) # reset the order as being updated 30 mins prior to skip notification time check

      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications).to be_blank
    end

  end

  context 'with order line changes' do
    let!(:supplier1_order_line) { [order_line11, order_line12, order_line13].sample }
    let!(:supplier2_order_line) { [order_line21, order_line22, order_line23].sample }

    it 'notifies the suppliers about amended order lines' do
      supplier1_order_line.update_columns(status: 'amended', updated_at: Time.zone.now - 30.minutes)
      supplier2_order_line.update_columns(status: 'amended', updated_at: Time.zone.now - 30.minutes)

      expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(2)
    end

    it 'notifies the suppliers about order lines being removed' do
      supplier1_order_line.destroy

      expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    it 'only sends notification to suppliers of non approved/notified order lines' do
      supplier1_order_line.update_columns(status: %w[approved notified].sample, updated_at: Time.zone.now - 30.minutes)
      supplier2_order_line.update_columns(status: 'amended', updated_at: Time.zone.now - 30.minutes)

      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    it 'only sends notifications to suppliers who are not already notified' do
      supplier1_order_line.update_columns(status: 'amended', updated_at: Time.zone.now - 30.minutes)
      supplier2_order_line.update_columns(status: 'amended', updated_at: Time.zone.now - 30.minutes)

      email_ref = "#{Suppliers::Emails::SendOrderAmendedEmail::EMAIL_TEMPLATE}-#{order.version_ref}"
      create(:email, :random, fk_id: supplier2.id, ref: email_ref) # create email for supplier 2

      expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    context 'timed notifications' do
      before do
        supplier1_order_line.update_columns(status: 'amended', updated_at: Time.zone.now - 10.minutes)
        supplier2_order_line.update_columns(status: 'amended', updated_at: Time.zone.now - 10.minutes)
      end

      it 'does not send notification to suppliers if there are no changes in the last 20 minutes' do
        expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
        expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

        notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

        expect(notifications_sender).to_not be_success
        expect(notifications_sender.errors).to include('Cannot notify now')
        expect(notifications_sender.sent_notifications).to be_blank
      end

      it 'notifies suppliers with recent changes for a team order' do
        order.update_column(:order_variant, 'team_order')
        expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
        expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

        notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.sent_notifications.size).to eq(2)
      end

      it 'notifies suppliers for recent changes if notify_now is set to true' do
        expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier1, order: order, detail_changes: anything, item_changes: anything)
        expect(Suppliers::Emails::SendOrderAmendedEmail).to receive(:new).with(supplier: supplier2, order: order, detail_changes: anything, item_changes: anything)

        notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order, notify_now: true).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.sent_notifications.size).to eq(2)
      end
    end
  end

  context 'with errors' do
    it 'does not send any notifications for a non-amended order' do
      order.update_column(:status, %w[new pending confirmed delivered cancelled].sample)
      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.errors).to include('Order needs to be amended')
      expect(notifications_sender.sent_notifications).to be_blank
    end

    it 'does not send any notifications for a recurrent order' do
      order.update_column(:order_type, 'recurrent')
      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.errors).to include('Order needs to be a one-off or template order')
      expect(notifications_sender.sent_notifications).to be_blank
    end

    it 'does not make a request for the emails to be sent' do
      if [true, false].sample
        order.update_column(:status, %w[new pending confirmed delivered cancelled].sample)
      else
        order.update_column(:order_type, 'recurrent')
      end

      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(order: order, supplier: supplier1, detail_changes: anything, item_changes: anything)
      expect(Suppliers::Emails::SendOrderAmendedEmail).to_not receive(:new).with(order: order, supplier: supplier2, detail_changes: anything, item_changes: anything)

      notifications_sender = Orders::Notifications::SendOrderAmendedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.sent_notifications).to be_blank
    end
  end

end
