require 'rails_helper'

RSpec.describe Orders::Notifications::SendOrderAdjustedSupplierNotifications, type: :service, orders: true, suppliers: true, notifications: true, order_amended_email: true do

  let!(:order) { create(:order, :delivered, suppliers_notified_at: Time.zone.now - 40.minutes, updated_at: Time.zone.now + 30.minutes) }

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:order_line11) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
  let!(:order_line12) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
  let!(:order_line13) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:order_line21) { create(:order_line, :random, order: order, supplier_profile: supplier2) }
  let!(:order_line22) { create(:order_line, :random, order: order, supplier_profile: supplier2) }
  let!(:order_line23) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

  let!(:items_change_getter) { double(Orders::GetOrderItemChanges) }

  before do
    order.reload

    # mock email sender
    email_sender = double(Suppliers::Emails::SendOrderAdjustedEmail)
    allow(Suppliers::Emails::SendOrderAdjustedEmail).to receive(:new).and_return(email_sender)
    email_sender_response = OpenStruct.new(success?: true, sent_notification: 'email', errors: [])
    allow(email_sender).to receive(:call).and_return(email_sender_response)

    # mock order item changes getting
    allow(Orders::GetOrderItemChanges).to receive(:new).and_return(items_change_getter)
    allow(items_change_getter).to receive(:call).and_return([])
  end

  it 'does not send notifications for a delivered order without any order or order line changes' do
    expect(Suppliers::Emails::SendOrderAdjustedEmail).to_not receive(:new).with(order: order, supplier: supplier1, item_changes: anything)
    expect(Suppliers::Emails::SendOrderAdjustedEmail).to_not receive(:new).with(order: order, supplier: supplier2, item_changes: anything)

    notifications_sender = Orders::Notifications::SendOrderAdjustedSupplierNotifications.new(order: order).call

    expect(notifications_sender).to be_success # no errors
    expect(notifications_sender.sent_notifications).to be_blank
  end

  context 'with order line changes' do
    let!(:item_changes) { [{ mock_item_change: true }] }

    before do
      # mock order line changes
      [order_line11, order_line12, order_line13, order_line21, order_line22, order_line23].each do |order_line|
        order_line.update_column(:status, 'amended')
        order_line.update_column(:updated_at, Time.zone.now - 30.minutes)
      end

      allow(items_change_getter).to receive(:call).and_return(item_changes)
    end

    it 'notifies the suppliers about changes to the order lines' do
      expect(Suppliers::Emails::SendOrderAdjustedEmail).to receive(:new).with(supplier: supplier1, order: order, item_changes: item_changes)
      expect(Suppliers::Emails::SendOrderAdjustedEmail).to receive(:new).with(supplier: supplier2, order: order, item_changes: item_changes)

      notifications_sender = Orders::Notifications::SendOrderAdjustedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(2)
    end

    it 'only sends notification to suppliers whose order lines have changed' do # , skip: 'Already tested in Orders::GetOrderItemChanges' do
      [order_line11, order_line12, order_line13].each do |order_line|
        order_line.update_column(:status, %w[approved notified].sample)
      end

      expect(Suppliers::Emails::SendOrderAdjustedEmail).to_not receive(:new).with(supplier: supplier1, order: order, item_changes: item_changes)
      expect(Suppliers::Emails::SendOrderAdjustedEmail).to receive(:new).with(supplier: supplier2, order: order, item_changes: item_changes)

      notifications_sender = Orders::Notifications::SendOrderAdjustedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    it 'only sends notifications to suppliers who are not already notified' do
      email_ref = "#{Suppliers::Emails::SendOrderAdjustedEmail::EMAIL_TEMPLATE}-#{order.version_ref}"
      create(:email, :random, fk_id: supplier2.id, ref: email_ref) # create email for supplier 2

      expect(Suppliers::Emails::SendOrderAdjustedEmail).to receive(:new).with(supplier: supplier1, order: order, item_changes: item_changes)
      expect(Suppliers::Emails::SendOrderAdjustedEmail).to_not receive(:new).with(supplier: supplier2, order: order, item_changes: item_changes)

      notifications_sender = Orders::Notifications::SendOrderAdjustedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end
  end

  context 'with errors' do
    it 'does not send any notifications for a non-delivered order' do
      order.update_column(:status, %w[new pending confirmed amended cancelled].sample)
      notifications_sender = Orders::Notifications::SendOrderAdjustedSupplierNotifications.new(order: order).call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.errors).to include('Order needs to be adjusted post delivery')
      expect(notifications_sender.sent_notifications).to be_blank
    end
  end

end
