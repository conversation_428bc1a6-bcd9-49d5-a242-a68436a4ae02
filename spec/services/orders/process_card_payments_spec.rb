require 'rails_helper'

RSpec.describe Orders::ProcessCardPayments, type: :service, orders: true, payments: true do

  let!(:now) { Time.zone.now }

  let!(:eway_card) { create(:credit_card, :valid_eway_payment) }
  let!(:stripe_card) { create(:credit_card, :valid_stripe_payment) }

  let!(:credit_card) { [eway_card, stripe_card].sample }
  let!(:invoice1) { create(:invoice, :random, payment_status: 'unpaid') }
  let!(:order1) { create(:order, :delivered, update_with_invoice: true, invoice: invoice1, credit_card: credit_card) }

  let!(:invoice2) { create(:invoice, :random, payment_status: 'unpaid') }
  let!(:order2) { create(:order, :delivered, update_with_invoice: true, invoice: invoice2, credit_card: credit_card) }

  let!(:invoice3) { create(:invoice, :random, payment_status: 'unpaid') }
  let!(:order3) { create(:order, :delivered, update_with_invoice: true, invoice: invoice3, credit_card: credit_card) }

  let!(:card_payer) { double(Orders::PayByCreditCard) }

  before do
    allow(Orders::PayByCreditCard).to receive(:new).and_return(card_payer)
    card_payer_response = OpenStruct.new(success?: true, payment: 'payment', errors: [])
    allow(card_payer).to receive(:call).and_return(card_payer_response)
  end

  it 'process card payments for orders invoiced in the last week' do
    payment_processor = Orders::ProcessCardPayments.new(time: now).call

    expect(payment_processor).to be_success
    expect(payment_processor.processed_payments.size).to eq(3)
  end

  it 'makes a request to pay the orders using attached card' do
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order1)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order2)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order3)

    payment_processor = Orders::ProcessCardPayments.new(time: now).call
    expect(payment_processor).to be_success
  end

  it 'does not process payment for an undelivered order' do
    order1.update_column(:status, %w[draft amended confirmed pending].sample)
    expect(Orders::PayByCreditCard).to_not receive(:new).with(order: order1)

    expect(Orders::PayByCreditCard).to receive(:new).with(order: order2)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order3)

    payment_processor = Orders::ProcessCardPayments.new(time: now).call
    expect(payment_processor).to be_success
    expect(payment_processor.processed_payments.size).to eq(2)
  end

  it 'does not process payment for an already paid orders' do
    order2.update_column(:payment_status, 'paid')
    expect(Orders::PayByCreditCard).to_not receive(:new).with(order: order2)

    expect(Orders::PayByCreditCard).to receive(:new).with(order: order1)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order3)

    payment_processor = Orders::ProcessCardPayments.new(time: now).call
    expect(payment_processor).to be_success
    expect(payment_processor.processed_payments.size).to eq(2)
  end

  it 'does not process payment for an orders attached to pay_on_account cards' do
    pay_on_account_card = create(:credit_card, :on_account_card)
    order3.update_column(:credit_card_id, pay_on_account_card.id)
    expect(Orders::PayByCreditCard).to_not receive(:new).with(order: order3)

    expect(Orders::PayByCreditCard).to receive(:new).with(order: order1)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order2)

    payment_processor = Orders::ProcessCardPayments.new(time: now).call
    expect(payment_processor).to be_success
    expect(payment_processor.processed_payments.size).to eq(2)
  end

  it 'does not process payment for an orders attached to auto pay invoice cards' do
    nominated_card = create(:credit_card, :valid_payment, auto_pay_invoice: true)
    order1.update_column(:credit_card_id, nominated_card.id)
    expect(Orders::PayByCreditCard).to_not receive(:new).with(order: order1)

    expect(Orders::PayByCreditCard).to receive(:new).with(order: order2)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order3)

    payment_processor = Orders::ProcessCardPayments.new(time: now).call
    expect(payment_processor).to be_success
    expect(payment_processor.processed_payments.size).to eq(2)
  end

  it 'does not process payment for an orders attached to credit card without a valid gateway token (eway or stripe)' do
    invalid_card = create(:credit_card, :random, gateway_token: nil, stripe_token: nil)
    order2.update_column(:credit_card_id, invalid_card.id)
    expect(Orders::PayByCreditCard).to_not receive(:new).with(order: order2)

    expect(Orders::PayByCreditCard).to receive(:new).with(order: order1)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order3)

    payment_processor = Orders::ProcessCardPayments.new(time: now).call
    expect(payment_processor).to be_success
    expect(payment_processor.processed_payments.size).to eq(2)
  end

  it 'does not process payment for an orders invoiced before previous week' do
    invoice3.update_column(:created_at, now - 2.weeks)
    expect(Orders::PayByCreditCard).to_not receive(:new).with(order: order3)

    expect(Orders::PayByCreditCard).to receive(:new).with(order: order1)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order2)

    payment_processor = Orders::ProcessCardPayments.new(time: now).call
    expect(payment_processor).to be_success
    expect(payment_processor.processed_payments.size).to eq(2)
  end

  it 'does not process payment for an orders with paid invoice' do
    invoice1.update_column(:payment_status, 'paid')
    expect(Orders::PayByCreditCard).to_not receive(:new).with(order: order1)

    expect(Orders::PayByCreditCard).to receive(:new).with(order: order2)
    expect(Orders::PayByCreditCard).to receive(:new).with(order: order3)

    payment_processor = Orders::ProcessCardPayments.new(time: now).call
    expect(payment_processor).to be_success
    expect(payment_processor.processed_payments.size).to eq(2)
  end

end
