require 'rails_helper'

RSpec.describe Orders::FetchSwipeCardAccessSuppliers, type: :service, orders: true, suppliers: true do

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags, flags: { needs_swipe_card_access: true }) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags, flags: { needs_swipe_card_access: false }) }
  let!(:supplier3) { create(:supplier_profile, :random, :with_flags, flags: { needs_swipe_card_access: true }) }

  let(:order) { create(:order, :draft) }
  let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
  let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

  context 'order is on a weekend OR before/after work hours' do
    before do
      is_weekend = [true, false].sample
      is_before_work_ours = [true, false].sample
      case
      when is_weekend
        order.update_column(:delivery_at, Time.zone.now.end_of_week) # weekend
      when is_before_work_ours
        order.update_column(:delivery_at, (Time.zone.now.beginning_of_week + 2.days).beginning_of_day) # before work hours
      else
        order.update_column(:delivery_at, (Time.zone.now.beginning_of_week + 2.days).end_of_day) # after work hours
      end
    end

    it 'only lists suppliers which need swipe access' do
      swipe_suppliers = Orders::FetchSwipeCardAccessSuppliers.new(order: order).call
      expect(swipe_suppliers).to include(supplier1)
      expect(swipe_suppliers).to_not include(supplier2)
    end

    it 'only lists suppliers for the orders' do
      swipe_suppliers = Orders::FetchSwipeCardAccessSuppliers.new(order: order).call
      expect(swipe_suppliers).to include(supplier1)
      expect(swipe_suppliers).to_not include(supplier3)
    end
  end

  it 'returns nothing if order is missing' do
    swipe_suppliers = Orders::FetchSwipeCardAccessSuppliers.new(order: nil).call
    expect(swipe_suppliers).to be_empty
  end

  it 'returns nothing if order delivery at is not present' do
    order.update_column(:delivery_at, nil)
    swipe_suppliers = Orders::FetchSwipeCardAccessSuppliers.new(order: order).call
    expect(swipe_suppliers).to be_empty
  end

  it 'returns nothing if order delivery at between working times' do
    order.update_column(:delivery_at, Time.zone.now.beginning_of_week + 2.days + 9.hours)
    swipe_suppliers = Orders::FetchSwipeCardAccessSuppliers.new(order: order).call
    expect(swipe_suppliers).to be_empty
  end

end
