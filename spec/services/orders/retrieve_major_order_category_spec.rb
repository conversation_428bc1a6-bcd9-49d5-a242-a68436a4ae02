require 'rails_helper'

RSpec.describe Orders::RetrieveMajorOrderCategory, type: :service, orders: true, categories: true do

  let!(:catering_category1) { create(:category, name: 'catering_category1', group: 'catering-services', weight: 1) }
  let!(:catering_category2) { create(:category, name: 'catering_category2', group: 'catering-services', weight: 2) }
  let!(:generic_catering_category) { create(:category, name: 'generic_catering_category', group: 'catering-services', weight: 3, is_generic: true) }
  
  let!(:pantry_category1) { create(:category, name: 'pantry_category1', group: 'kitchen-supplies', weight: 1) }
  let!(:pantry_category2) { create(:category, name: 'pantry_category2', group: 'kitchen-supplies', weight: 2) }
  let!(:generic_pantry_category) { create(:category, name: 'generic_pantry_category', group: 'kitchen-supplies', weight: 3, is_generic: true) }  

  let!(:order) { create(:order, :draft) }
  
  it 'returns nothing for an order with no order lines' do
    major_category = Orders::RetrieveMajorOrderCategory.new(order: order).call

    expect(major_category).to be_blank
  end

  context 'with order_lines' do
    let!(:supplier1) { create(:supplier_profile, :random, :with_flags, flags: { has_catering_services: true, has_kitchen_supplies: false }) }
    let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1, categories: [catering_category2, catering_category1]) }
    let!(:menu_item11) { create(:menu_item, :random, menu_section: menu_section11, supplier_profile: supplier1) }

    let!(:order_line11) { create(:order_line, :random, name: 'order11', order: order, supplier_profile: supplier1, menu_item: menu_item11, category: catering_category2, quantity: 20) }
    let!(:order_line12) { create(:order_line, :random, name: 'order12', order: order, supplier_profile: supplier1, menu_item: menu_item11, category: catering_category1, quantity: 10) }

    let!(:supplier2) { create(:supplier_profile, :random, :with_flags, flags: { has_catering_services: false, has_kitchen_supplies: true }) }
    let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2, categories: [pantry_category2]) }
    let!(:menu_item21) { create(:menu_item, :random, menu_section: menu_section21, supplier_profile: supplier2) }
    let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2, categories: [pantry_category1]) }
    let!(:menu_item22) { create(:menu_item, :random, menu_section: menu_section22, supplier_profile: supplier2) }

    let!(:order_line21) { create(:order_line, :random, name: 'order21', order: order, supplier_profile: supplier2, menu_item: menu_item21, category: pantry_category2, quantity: 5) }
    let!(:order_line22) { create(:order_line, :random, name: 'order22', order: order, supplier_profile: supplier2, menu_item: menu_item22, category: pantry_category1, quantity: 3) }

    it 'returns the major category based on number of order lines (multiplied by quantity) in a category (based on order line categories)' do
      major_category = Orders::RetrieveMajorOrderCategory.new(order: order).call

      expect(major_category).to eq(catering_category2) # order11 with maximum quantity 
    end

    it 'returns the major category based on number of order lines (multiplied by quantity) in a category for a given supplier (based on order line categories)' do
      major_category = Orders::RetrieveMajorOrderCategory.new(order: order, supplier: supplier2).call

      expect(major_category).to eq(pantry_category2) # order21 with maximum quantity 
    end

    it 'saves the category against the order if passed with `save_category`' do
      expect{ Orders::RetrieveMajorOrderCategory.new(order: order, save_category: [nil, false].sample).call }.to_not change{ order.major_category }.from(nil)
      expect{ Orders::RetrieveMajorOrderCategory.new(order: order, save_category: true).call }.to change{ order.major_category }.from(nil).to(catering_category2)
    end

    context 'with saved order major_category' do
      let!(:saved_major_category) { [catering_category1, generic_catering_category, pantry_category1, pantry_category2, generic_pantry_category].sample }

      before do
        order.update_column(:major_category_id, saved_major_category.id)  
      end

      it 'returns saved order major_category if present and not saving' do
        major_category = Orders::RetrieveMajorOrderCategory.new(order: order, save_category: [nil, false].sample).call

        expect(major_category).to eq(saved_major_category)
      end

      it 'returns calculated major category if saving to order' do
        major_category = Orders::RetrieveMajorOrderCategory.new(order: order, save_category: true).call
        
        expect(major_category).to eq(catering_category2) # order11 with maximum quantity 
      end

      it 'returns calculated major category if retrieving for a supplier' do
        major_category = Orders::RetrieveMajorOrderCategory.new(order: order, supplier: supplier2).call
        
        expect(major_category).to eq(pantry_category2) # order21 with maximum quantity 
      end
    end

    context 'with no order line categories' do
      before do
        [order_line11, order_line12, order_line21, order_line22].each do |order_line|
          order_line.update_column(:category_id, nil)
        end
      end

      it 'returns the major category based on number of order lines (multiplied by quantity) in a category (based on menu item - menu section weighted categories)' do
        major_category = Orders::RetrieveMajorOrderCategory.new(order: order).call

        expect(major_category).to eq(catering_category1) # menu_section11 weighted category
      end

      context 'with no menu section categories' do
        before do
          [menu_section11, menu_section21, menu_section22].each do |menu_section|
            menu_section.update(categories: [])
          end
        end

        it 'returns the major category based supplier flags' do
          major_category = Orders::RetrieveMajorOrderCategory.new(order: order).call

          expect(major_category).to eq(generic_catering_category) # generic supplier category
        end

        it 'returns the major category based on passed in suppliers flags' do
          major_category = Orders::RetrieveMajorOrderCategory.new(order: order, supplier: supplier2).call

          expect(major_category).to eq(generic_pantry_category) # generic supplier category
        end

        context 'for a custom order', custom_orders: true do
          before do
            order.update_columns(order_variant: 'event_order', major_category_id: generic_catering_category.id)
          end

          it 'returns the saved major_category' do
            major_category = Orders::RetrieveMajorOrderCategory.new(order: order).call

            expect(major_category).to eq(generic_catering_category)
          end

          context 'with no saved major_category_id' do
            before do
              order.update_columns(major_category_id: nil, order_category: 'kitchen-supplies')
            end

            it 'returns the major category based on saved order_category' do
              major_category = Orders::RetrieveMajorOrderCategory.new(order: order).call

              expect(major_category).to eq(generic_pantry_category) # generic category based on order_category
            end

            it 'still returns the major category based on saved order_category even for a passed in supplier' do
              order.update_column(:order_category, 'catering-services')
              major_category = Orders::RetrieveMajorOrderCategory.new(order: order, supplier: [nil, supplier1, supplier2].sample).call

              expect(major_category).to eq(generic_catering_category) # generic category based on order_category
            end
          end
        end # for a custom order

        context 'with no supplier flags' do
          before do
            [supplier1, supplier2].each do |supplier|
              supplier.supplier_flags.update_columns(has_catering_services: false, has_kitchen_supplies: false)
            end
          end

          it 'returns nothing (irrespective of passed in supplier)' do
            major_category = Orders::RetrieveMajorOrderCategory.new(order: order, supplier: [nil, supplier1, supplier2].sample).call

            expect(major_category).to be_blank
          end
        end # with no supplier flags
      end # with no menu section categories
    end # with no order line categories
  end # with order lines

end