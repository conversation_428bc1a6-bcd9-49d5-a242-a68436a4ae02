require 'rails_helper'

RSpec.describe Orders::GetOrderItemChanges, type: :servive, order_lines: true, orders: true, order_amended_email: true do

  let!(:now) { Time.zone.now }
  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order) { create(:order, :amended) }

  let!(:order_line1) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }
  let!(:order_line2) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }
  let!(:order_line3) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }
  let!(:order_line4) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }

  let!(:location1) { create(:location, :random, order: order) }
  let!(:location2) { create(:location, :random, order: order) }
  let!(:location3) { create(:location, :random, order: order) }

  before do
    # add order lines to locations
    [order_line1, order_line4].each do |order_line|
      order_line.update_column(:location_id, location1.id)
    end
    order_line2.update_column(:location_id, location2.id)
    order_line3.update_column(:location_id, location3.id)
  end

  before do
    last_location = order.reload.locations.order(updated_at: :desc).first
    order.update(suppliers_notified_at: last_location.updated_at)
  end

  it 'returns a blank array for an order with no item / location changes (order line/location versions later than supplier notified at)' do
    item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call

    expect(item_changes).to be_blank
  end

  context 'with order line creation / updates / removal' do
    let!(:order_line5) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier, location: location3) }

    before do
      order_line1.update(quantity: order_line1.quantity + 10)
      order_line4.destroy
    end

    it 'returns updated order (line) locations' do
      item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call

      expect(item_changes.map(&:id)).to include(*[location1, location3].map(&:id))
      expect(item_changes.map(&:id)).to_not include(location2.id)

      # only order line changed
      expect(item_changes.map(&:change_type)).to include(nil)
      expect(item_changes.map(&:changes).flatten).to be_blank
    end

    it 'returns updated order lines per location' do
      item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call

      location1_change = item_changes.detect{|change| change.id == location1.id }
      location1_order_line_changes = location1_change.order_line_changes
      expect(location1_order_line_changes).to be_present
      expect(location1_order_line_changes.map(&:id)).to include(*[order_line1, order_line4].map(&:id))
      expect(location1_order_line_changes.map(&:change_type)).to include('updated', 'removed')

      location3_change = item_changes.detect{|change| change.id == location3.id }
      location3_order_line_changes = location3_change.order_line_changes
      expect(location3_order_line_changes).to be_present
      expect(location3_order_line_changes.map(&:id)).to include(order_line5.id)
      expect(location3_order_line_changes.map(&:change_type)).to include('created')
    end

    it 'returns changes per updated order lines', skip: 'Already tested in OrderLines::GetSupplierItemChanges' do
    end

    it 'only returns order line changes belonging to the passed in supplier' do
      supplier2 = create(:supplier_profile, :random)
      order_line5.update_column(:supplier_profile_id, supplier2.id)

      item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call
      expect(item_changes.map(&:id)).to_not include(location3.id)
    end

    it 'returns a blank array if supplier is already notified' do
      order.update(suppliers_notified_at: Time.zone.now + 1.hour)

      item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call
      expect(item_changes).to be_blank
    end

    context 'with location updates' do
      it 'returns the updated location changes, even if order lines haven\'t changed' do
        location1.update(details: 'new-details', note: 'new-note')
        location2.update(details: 'new-location2-details')

        item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call

        expect(item_changes.map(&:id)).to include(location1.id, location2.id)

        location1_change = item_changes.detect{|change| change.id == location1.id }
        expect(location1_change.change_type).to eq('changed')
        location1_changes = location1_change.changes
        expect(location1_changes).to be_present
        expect(location1_changes.map(&:field)).to include('details', 'note')

        location2_change = item_changes.detect{|change| change.id == location2.id }
        expect(location2_change.change_type).to eq('changed')
        location2_changes = location2_change.changes
        expect(location2_changes).to be_present
        expect(location2_changes.map(&:field)).to include('details')
      end

      it 'returns newly added locations provided' do
        location4 = create(:location, :random, order: order)
        create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier, location: location4) # create order line with note

        item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call

        expect(item_changes.map(&:id)).to include(location4.id)

        location4_change = item_changes.detect{|change| change.id == location4.id }
        expect(location4_change.change_type).to eq('created')
        expect(location4_change.changes).to be_blank
      end

      it 'does not returns newly added locations that do not have any order lines' do
        location4 = create(:location, :random, order: order)
        # no order line in new location

        item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call

        expect(item_changes.map(&:id)).to_not include(location4.id)
      end

      it 'returns a removed location' do
        Locations::Remove.new(order: order, location: location1).call

        item_changes = Orders::GetOrderItemChanges.new(order: order, supplier: supplier).call

        expect(item_changes.map(&:id)).to include(location1.id)
        location1_change = item_changes.detect{|change| change.id == location1.id }
        expect(location1_change.change_type).to eq('removed')
        expect(location1_change.changes).to be_blank
      end
    end
  end

end
