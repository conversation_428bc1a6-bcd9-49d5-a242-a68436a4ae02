require 'rails_helper'

RSpec.describe Orders::GetMaximumLeadTime, type: :service, ordersx: true, order_lixnes: true, supxpliers: true, minimums: true do

  let!(:order) { create(:order, :draft) }
  let!(:now) { Time.zone.now }
  let!(:time) { now.beginning_of_week.change(hour: 12, min: 30, sec: 0) + rand(1..2).days }
  let!(:category1) { create(:category, :random, group: 'catering-services') }
  let!(:category2) { create(:category, :random, group: 'home-deliveries') }
  let!(:category3) { create(:category, :random, group: 'catering-services') }

  context 'with no suppliers' do

    it 'gets the max lead time as the current time' do
      lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

      expect(lead_time_fetcher.formatted_lead_time).to be_blank
      expect(lead_time_fetcher.minimum_delivery_at).to eq(time)
    end

    context 'check directly against the supplier instead of the order' do
      it 'get the max lead time based on all supplier minimums' do
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: nil, time: time, supplier_ids: []).call

        expect(lead_time_fetcher.formatted_lead_time).to be_blank
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time)
      end
    end

  end

  context 'with supplier lead_mode = by_hour' do

    context 'order with one supplier' do
      let(:supplier) { create(:supplier_profile, :random, lead_mode: 'by_hour') }
      let!(:minimum1) { create(:minimum, :random, supplier_profile: supplier, lead_time: 27.0, category: category1) }
      let!(:minimum2) { create(:minimum, :random, supplier_profile: supplier, lead_time: 24.0, category: category1) }
      let!(:minimum3) { create(:minimum, :random, supplier_profile: supplier, lead_time: 48.0, category: category2) }
      let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier) }

      it 'get the max lead time based on all supplier minimums' do
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum3.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 48.hours)
      end

      it 'gets the max lead time based on order line categories' do
        order_line.update_column(:category_id, category1.id)

        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum1.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 27.hours)
      end

      it 'gets the max lead time based on order line categories\' group' do
        order_line.update_column(:category_id, category3.id)

        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum1.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 27.hours)
      end

      context 'check directly against the supplier instead of the order' do
        it 'get the max lead time based on all supplier minimums' do
          lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: nil, time: time, supplier_ids: [supplier.id]).call

          expect(lead_time_fetcher.formatted_lead_time).to eq(minimum3.formatted_lead_time)
          expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 48.hours)
        end
      end

    end

    context 'order with multiple suppliers' do
      let(:supplier1) { create(:supplier_profile, :random, lead_mode: 'by_hour') }
      let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 27.0, category: category1) }
      let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 24.0, category: category1) }
      let!(:minimum13) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 48.0, category: category2) }
      let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

      let(:supplier2) { create(:supplier_profile, :random, lead_mode: 'by_hour') }
      let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 20.0, category: category1) }
      let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 50.0, category: category1) }
      let!(:minimum23) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 30.0, category: category2) }
      let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

      it 'get the max lead time based on all supplier minimums' do
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq('50 hrs')
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 50.hours)
      end

      it 'gets the max lead time based on order line categories' do
        order_line1.update_column(:category_id, category2.id)
        order_line2.update_column(:category_id, category2.id)

        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq('48 hrs')
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 48.hours)
      end

      it 'gets the max lead time based on order line categories\' group' do
        order_line1.update_column(:category_id, category3.id)
        order_line2.update_column(:category_id, category3.id)

        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq('50 hrs')
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 50.hours)
      end

      context 'check directly against the suppliers instead of the order' do
        it 'get the max lead time based on all supplier minimums' do
          lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: nil, time: time, supplier_ids: [supplier1.id, supplier2.id]).call

          expect(lead_time_fetcher.formatted_lead_time).to eq('50 hrs')
          expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 50.hours)
        end
      end
    end
  end

  context 'with supplier lead_mode = by_day_before' do

    context 'order with a one supplier' do
      let(:supplier) { create(:supplier_profile, :random, lead_mode: 'by_day_before') }
      let!(:minimum1) { create(:minimum, :random, supplier_profile: supplier, lead_time_day_before: (time - 5.hours).strftime('%H:%M'), category: category1) }
      let!(:minimum2) { create(:minimum, :random, supplier_profile: supplier, lead_time_day_before: (time + 5.hours).strftime('%H:%M'), category: category2) }
      let!(:minimum3) { create(:minimum, :random, supplier_profile: supplier, lead_time_day_before: (time + 3.hours).strftime('%H:%M'), category: category2) }
      let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier) }

      it 'get the max lead time based on all supplier minimums' do
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum1.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 2.days)
      end

      it 'gets the max lead time based on order line categories' do
        order_line.update_column(:category_id, category2.id)

        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum3.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 1.day)
      end

      it 'gets the max lead time based on order line categories\' group' do
        order_line.update_column(:category_id, category3.id)

        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum1.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 2.days)
      end

      context 'check directly against the supplier instead of the order' do
        it 'get the max lead time based on all supplier minimums' do
          lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: nil, time: time, supplier_ids: [supplier.id]).call

          expect(lead_time_fetcher.formatted_lead_time).to eq(minimum1.formatted_lead_time)
          expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 2.days)
        end
      end
    end

    context 'order with a multiple suppliers' do
      let(:supplier1) { create(:supplier_profile, :random, lead_mode: 'by_day_before') }
      let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time_day_before: (time - 5.hours).strftime('%H:%M'), category: category1) }
      let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time_day_before: (time + 5.hours).strftime('%H:%M'), category: category2) }
      let!(:minimum13) { create(:minimum, :random, supplier_profile: supplier1, lead_time_day_before: (time + 3.hours).strftime('%H:%M'), category: category2) }
      let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

      let(:supplier2) { create(:supplier_profile, :random, lead_mode: 'by_day_before') }
      let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time_day_before: (time - 5.hours).strftime('%H:%M'), category: category1) }
      let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time_day_before: (time + 5.hours).strftime('%H:%M'), category: category2) }
      let!(:minimum23) { create(:minimum, :random, supplier_profile: supplier2, lead_time_day_before: (time + 3.hours).strftime('%H:%M'), category: category2) }
      let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

      it 'get the max lead time based on all supplier minimums' do
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum21.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 2.days)
      end

      it 'gets the max lead time based on order line categories' do
        order_line1.update_column(:category_id, category2.id)
        order_line2.update_column(:category_id, category2.id)

        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum23.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 1.day)
      end

      it 'gets the max lead time based on order line categories\' group' do
        order_line1.update_column(:category_id, category3.id)
        order_line2.update_column(:category_id, category3.id)

        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum21.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 2.days)
      end

      context 'check directly against the supplier instead of the order' do
        it 'get the max lead time based on all supplier minimums' do
          lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: nil, time: time, supplier_ids: [supplier1.id, supplier2.id]).call

          expect(lead_time_fetcher.formatted_lead_time).to eq(minimum21.formatted_lead_time)
          expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 2.days)
        end
      end
    end

  end

  context 'order with multiple suppliers with mixed lead modes' do
    let(:supplier1) { create(:supplier_profile, :random, lead_mode: 'by_hour') }
    let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 3.0, category: category1) }
    let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 2.0, category: category1) }
    let!(:minimum13) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 48.0, category: category2) }
    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

    let(:supplier2) { create(:supplier_profile, :random, lead_mode: 'by_day_before') }
    let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time_day_before: (time - 5.hours).strftime('%H:%M'), category: category2) }
    let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, lead_time_day_before: (time + 5.hours).strftime('%H:%M'), category: category1) }
    let!(:minimum23) { create(:minimum, :random, supplier_profile: supplier2, lead_time_day_before: (time + 3.hours).strftime('%H:%M'), category: category1) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

    it 'get the max lead time based on all supplier minimums' do
      lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

      expect(lead_time_fetcher.formatted_lead_time).to eq(minimum13.formatted_lead_time)
      expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 48.hours)
    end

    it 'gets the max lead time based on order line categories' do
      order_line1.update_column(:category_id, category1.id)
      order_line2.update_column(:category_id, category1.id)

      lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

      expect(lead_time_fetcher.formatted_lead_time).to eq(minimum23.formatted_lead_time)
      expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 1.day)
    end

    it 'gets the max lead time based on order line categories\' group' do
      order_line1.update_column(:category_id, category3.id)
      order_line2.update_column(:category_id, category3.id)

      lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

      expect(lead_time_fetcher.formatted_lead_time).to eq(minimum23.formatted_lead_time)
      expect(lead_time_fetcher.minimum_delivery_at).to eq(time.beginning_of_day + 1.day)
    end

    context 'can_process?' do # calculated lead time of time + 48.hours from minimum13
      it 'returns that that it can process if delivery_at is later than lead time value' do
        order.update_column(:delivery_at, time + 50.hours) # more than lead time of time + 48 hours
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher).to be_can_process
      end

      it 'returns that that it cannot process if delivery_at is later than lead time value' do
        order.update_column(:delivery_at, time + 47.hours) # less than lead time of time + 48 hours
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

        expect(lead_time_fetcher).to_not be_can_process
      end
    end

    context 'check directly against the supplier instead of the order' do
      it 'get the max lead time based on all supplier minimums' do
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: nil, time: time, supplier_ids: [supplier1.id, supplier2.id]).call

        expect(lead_time_fetcher.formatted_lead_time).to eq(minimum13.formatted_lead_time)
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time + 48.hours)
      end
    end
  end

  context 'suppliers with no lead times' do
    let(:supplier1) { create(:supplier_profile, :random, lead_mode: 'by_hour') }
    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

    let(:supplier2) { create(:supplier_profile, :random, lead_mode: 'by_day_before') }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

    it 'gets the max lead time as the current time' do
      lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order, time: time).call

      expect(lead_time_fetcher.formatted_lead_time).to be_blank
      expect(lead_time_fetcher.minimum_delivery_at).to eq(time)
    end

    context 'check directly against the supplier instead of the order' do
      it 'gets the max lead time as the current time' do
        lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: nil, time: time, supplier_ids: [supplier1.id, supplier2.id]).call

        expect(lead_time_fetcher.formatted_lead_time).to be_blank
        expect(lead_time_fetcher.minimum_delivery_at).to eq(time)
      end
    end
  end

end
