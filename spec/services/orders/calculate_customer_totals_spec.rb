require 'rails_helper'

RSpec.describe Orders::CalculateCustomerTotals, type: :service, orders: true do

  let(:customer) { create(:customer_profile, :random) }
  let!(:suburb) { create(:suburb, :random, country_code: 'AU') }
  let(:order) { create(:order, :draft, customer_profile: customer, delivery_suburb: suburb) }
  let(:calculated_totals) { Orders::CalculateCustomerTotals.new(order: order).call }

  let!(:gst_percent_au) { 0.1 }
  let!(:gst_percent_nz) { 0.15 }

  before do
    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(nil)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(gst_percent_au)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(gst_percent_nz)
  end

  it 'returns an object with various attributes' do
    attributes = calculated_totals.class.instance_methods(false)
    expect(attributes).to include(:order_line_count, :subtotal, :delivery, :discount, :gst, :surcharge, :total)
  end

  it 'returns all totals as 0 for an order without order lines' do
    order_totals = calculated_totals
    expect(order_totals.order_line_count).to eq(0)
    expect(order_totals.subtotal).to eq(0)
    expect(order_totals.delivery).to eq(0)
    expect(order_totals.topup).to eq(0)
    expect(order_totals.discount).to eq(0)
    expect(order_totals.gst).to eq(0)
    expect(order_totals.surcharge).to eq(0)
    expect(order_totals.total).to eq(0)
  end

  it 'refreshes the totals to 0 for an order without order lines' do
    order.update_columns(
      customer_subtotal: 300,
      customer_delivery: 10,
      discount: 1,
      customer_gst: 30,
      customer_surcharge: 1,
      customer_topup: 3,
      customer_total: 330
    ) # set arbitrary values
    expect(order.customer_subtotal).to eq(300)
    expect(order.customer_delivery).to eq(10)
    expect(order.discount).to eq(1)
    expect(order.customer_gst).to eq(30)
    expect(order.customer_surcharge).to eq(1)
    expect(order.customer_topup).to eq(3)
    expect(order.customer_total).to eq(330)

    Orders::CalculateCustomerTotals.new(order: order, save_totals: true).call
    order.reload
    expect(order.customer_subtotal).to eq(0)
    expect(order.customer_delivery).to eq(0)
    expect(order.discount).to eq(0)
    expect(order.customer_gst).to eq(0)
    expect(order.customer_surcharge).to eq(0)
    expect(order.customer_topup).to eq(0)
    expect(order.customer_total).to eq(0)
  end

  context 'with order lines' do
    let(:supplier) { create(:supplier_profile, :random) }

    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 10.0, quantity: 5, is_gst_free: false) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 10.0, quantity: 5, is_gst_free: false) }
    let!(:order_line3) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 10.0, quantity: 5, is_gst_free: false) }

    let!(:order_line4) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 10.0, quantity: 5, is_gst_free: false) }
    let!(:order_line5) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 10.0, quantity: 5, is_gst_free: false) }
    let!(:order_line6) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 10.0, quantity: 5, is_gst_free: false) }

    let(:calculated_totals) { Orders::CalculateCustomerTotals.new(order: order.reload).call }

    before do
      Order.skip_callback(:save, :after)
    end

    it 'returns the subtotal as the sum of the order line price totals' do
      expect(calculated_totals.subtotal).to eq(300)
    end

    it 'returns the gst (10%) based on the total of the non-gst products' do
      expect(calculated_totals.gst).to eq(30)
      expect(calculated_totals.gst).to eq(300 * gst_percent_au)
    end

    it 'returns the total as the sum of subtotal and gst' do
      expect(calculated_totals.total).to eq(330)
      expect(calculated_totals.total).to eq(300 + (300 * gst_percent_au))
    end

    it 'returns the total as a sum of GSTed individual order line totals instead of total of gst order_line * 1 + gst', xero: true do
      order_line1.update_column(:price, 11.73)
      order_line2.update_column(:price, 11.36)
      order_line3.update_column(:price, 14.46)
      order_line4.update_column(:price, 11.73)
      order_line5.update_column(:price, 11.36)
      order_line6.update_column(:price, 14.46)
      [order_line1, order_line2, order_line3, order_line4, order_line5, order_line6].each do |order_line|
        order_line.update_column(:quantity, 1)
      end
      calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call
      expect(calculated_totals.total.round(2)).to_not eq(82.61) # order_lines.sum(&:total_price) * ( 1.0 + 0.1 ) gst of sum

      subtotal_with_gst = order.order_lines.sum{|order_line| (order_line.total_price * (1.0 + 0.1)).round(2) } # sum of individually gsted values
      expect(calculated_totals.total.round(2)).to eq(subtotal_with_gst) # 82.62
    end

    context 'with an NZ order' do
      let!(:nz_suburb) { create(:suburb, :random, country_code: 'NZ') }
      before do
        order.update_column(:delivery_suburb_id, nz_suburb.id)
      end

      it 'returns the gst (15%) based on the total of the non-gst products' do
        expect(calculated_totals.subtotal).to eq(300)
        expect(calculated_totals.gst).to eq(45)
        expect(calculated_totals.gst).to eq(300 * gst_percent_nz)
      end

      it 'returns the total as the sum of subtotal and gst' do
        expect(calculated_totals.total).to eq(345)
        expect(calculated_totals.total).to eq(300 + (300 * gst_percent_nz))
      end

      it 'returns the total as a sum of GSTed individual order line totals instead of total of gst order_line * 1 + gst', xero: true do
        order_line1.update_column(:price, 11.75)
        order_line2.update_column(:price, 11.36)
        order_line3.update_column(:price, 14.46)
        order_line4.update_column(:price, 11.75)
        order_line5.update_column(:price, 11.36)
        order_line6.update_column(:price, 14.46)
        [order_line1, order_line2, order_line3, order_line4, order_line5, order_line6].each do |order_line|
          order_line.update_column(:quantity, 1)
        end
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call
        expect(calculated_totals.total.round(2)).to_not eq(86.41) # order_lines.sum(&:total_price) * ( 1.0 + 0.15 ) gst of sum

        subtotal_with_gst = order.order_lines.sum{|order_line| (order_line.total_price * (1.0 + 0.15)).round(2) } # sum of individually gsted values
        expect(calculated_totals.total.round(2)).to eq(subtotal_with_gst) # 86.40
      end
    end

    context 'with some gst_free products' do
      before do
        [order_line3, order_line4, order_line6].each do |order_line|
          order_line.update_column(:is_gst_free, true)
        end
      end

      it 'does not include gst for non-gst products' do
        expect(calculated_totals.gst).to eq(15)
      end

      it 'returns the total according the gst products' do
        expect(calculated_totals.total).to eq(315)
      end
    end

    context 'with a delivery fee' do
      let(:calculated_delivery) { 10 }

      before do
        calculate_delivery = double(Orders::CalculateDelivery)
        allow(Orders::CalculateDelivery).to receive(:new).and_return(calculate_delivery)
        allow(calculate_delivery).to receive(:call).and_return(calculated_delivery)
      end

      it 'returns the delivery value' do
        expect(calculated_totals.delivery).to eq(calculated_delivery)
      end

      it 'includes the delivery gst (10%) in gst' do
        expect(calculated_totals.gst).to eq(31) # normal gst + gst (10%) of delivery
      end

      it 'includes the delivery (with gts) in total' do
        expect(calculated_totals.total).to eq(341)
      end

      context 'with all GST free items' do
        before do
          [order_line1, order_line2, order_line3, order_line4, order_line5, order_line6].each do |order_line|
            order_line.update_column(:is_gst_free, true)
          end
        end

        it 'does not add GST for the delivery' do
          expect(calculated_totals.gst).to eq(0) # no gst for items and or delivery
        end

        it 'includes delivery (excluding GST) in total' do
          expect(calculated_totals.total).to eq(310)
        end
      end
    end

    context 'with a discount' do
      let(:calculated_discount) { 100 }

      before do
        calculate_discount = double(Orders::CalculateDiscount)
        allow(Orders::CalculateDiscount).to receive(:new).and_return(calculate_discount)
        allow(calculate_discount).to receive(:call).and_return(calculated_discount)
      end

      it 'returns the discount value' do
        expect(calculated_totals.discount).to eq(calculated_discount)
      end

      it 'excludes the discount gst (10%) in gst' do
        expect(calculated_totals.gst).to eq(20) # normal gst - gst (10%) of discount
      end

      it 'includes the delivery (with gts) in total' do
        expect(calculated_totals.total).to eq(220)
      end

      context 'with a massive discount' do
        let(:calculated_discount) { 1000 }

        it 'sets the gst as 0' do
          expect(calculated_totals.gst).to eq(0)
        end

        it 'returns the discount value as the order\'s subtotal + delivery' do
          expect(calculated_totals.discount).to eq(calculated_totals.subtotal + calculated_totals.delivery)
        end

        it 'returns the total as 0' do
          expect(calculated_totals.total).to eq(0)
        end
      end
    end

    context 'with a surcharge' do
      let(:calculated_surcharge) { 15 }

      before do
        calculate_surcharge = double(Orders::CalculateSurcharge)
        allow(Orders::CalculateSurcharge).to receive(:new).and_return(calculate_surcharge)
        allow(calculate_surcharge).to receive(:call).and_return(calculated_surcharge)
      end

      it 'returns the surcharge value' do
        expect(calculated_totals.surcharge).to eq(calculated_surcharge)
      end

      it 'does not include the surcharg in the gst' do
        expect(calculated_totals.gst).to eq(30) # normal gst - gst (10%) of discount
      end

      it 'includes the surcharge in total' do
        expect(calculated_totals.total).to eq(345)
      end
    end

    context 'with save totals' do
      let!(:calculated_totals) { Orders::CalculateCustomerTotals.new(order: order.reload, save_totals: true).call }

      it 'saves the customer totals in the order' do
        order.reload
        expect(calculated_totals.order_line_count).to eq(6)
        expect(order.customer_subtotal).to eq(300)
        expect(order.customer_delivery).to eq(0) # no delivery added
        expect(order.discount).to eq(0) # no discount added
        expect(order.customer_gst).to eq(30)
        expect(order.customer_surcharge).to eq(0) # no surcharge added
        expect(order.customer_topup).to eq(0) # no topup added
        expect(order.customer_total).to eq(330)
      end
    end

    context 'specified order lines' do
      let!(:calculated_totals) { Orders::CalculateCustomerTotals.new(order: order.reload, order_lines: [order_line1, order_line3, order_line5, order_line6]).call }

      it 'calculates totals for specified order lines' do
        expect(calculated_totals.order_line_count).to eq(4)
        expect(calculated_totals.subtotal).to eq(200)
        expect(calculated_totals.delivery).to eq(0) # no delivery added
        expect(calculated_totals.discount).to eq(0) # no discount added
        expect(calculated_totals.gst).to eq(20)
        expect(calculated_totals.surcharge).to eq(0) # no surcharge added
        expect(calculated_totals.total).to eq(220)
      end
    end

    context 'for a team order attendee', team_orders: true do
      let(:attendee) { create(:team_order_attendee, :random, order: order, status: 'invited') }

      before do
        order.update_column(:order_variant, 'team_order')
        # associate order lines with attendee
        [order_line1, order_line3, order_line6].each do |order_line|
          order_line.update_column(:attendee_id, attendee.id)
        end
      end

      it 'calculates totals for order lines associated with the specified attendee' do
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, attendee: attendee).call
        expect(calculated_totals.order_line_count).to eq(3)
        expect(calculated_totals.subtotal).to eq(150)
        expect(calculated_totals.delivery).to eq(0) # attendee delivery is always 0
        expect(calculated_totals.discount).to eq(0) # no discount added
        expect(calculated_totals.gst).to eq(15)
        expect(calculated_totals.surcharge).to eq(0) # no surcharge added
        expect(calculated_totals.topup).to eq(0) # no surcharge added
        expect(calculated_totals.total).to eq(165)
      end

      it 'does not calculate and add discount for an attendee' do
        expect(Orders::CalculateDelivery).to_not receive(:new)

        Orders::CalculateCustomerTotals.new(order: order.reload, attendee: attendee).call
      end

      it 'does not calculate and add surcharge for an attendee' do
        expect(Orders::CalculateSurcharge).to_not receive(:new)

        Orders::CalculateCustomerTotals.new(order: order.reload, attendee: attendee).call
      end

      it 'does not calculate and add topup for an attendee' do
        expect(Orders::CalculateTopup).to_not receive(:new)

        Orders::CalculateCustomerTotals.new(order: order.reload, attendee: attendee).call
      end

      it 'does not save the attendee totals to the order' do
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, attendee: attendee, save_totals: true).call
        expect(calculated_totals.order_line_count).to eq(3)

        order.reload
        # default values
        expect(order.customer_subtotal).to be_blank
        expect(order.customer_delivery).to be_blank
        expect(order.discount).to eq(0.0)
        expect(order.customer_gst).to be_blank
        expect(order.customer_surcharge).to be_blank
        expect(order.customer_topup).to be_blank
        expect(order.customer_total).to be_blank
      end
    end

    context 'with supplier surcharge/topup' do
      let!(:supplier2) { create(:supplier_profile, :random) }
      let!(:order_supplier1) { create(:order_supplier, order: order, supplier_profile: supplier, surcharge: 10) }
      let!(:order_supplier2) { create(:order_supplier, order: order, supplier_profile: supplier2, surcharge: 30) }

      it 'returns 0 surcharge/topup amount for the suppliers within the order if order is not charged to minimum' do
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call

        expect(calculated_totals.topup).to eq(0)
      end

      it 'returns surcharge/topup amount for the suppliers within the order which is marked as charged to minimum' do
        order.update_column(:charge_to_minimum, true)
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call

        expect(calculated_totals.topup).to eq(40)
        expect(calculated_totals.total).to eq(330 + 40)
      end
    end # with supplier topup

    context 'GST Split Totals', gst_split_invoicing: true do
      before do
        order_line1.update_columns(name: 'order_line1', price: 11.75, is_gst_free: true, quantity: 1)
        order_line2.update_columns(name: 'order_line2', price: 11.36, is_gst_free: false, quantity: 1)
        order_line3.update_columns(name: 'order_line3', price: 14.46, is_gst_free: true, quantity: 1)
        order_line4.update_columns(name: 'order_line4', price: 11.75, is_gst_free: false, quantity: 1)
        order_line5.update_columns(name: 'order_line5', price: 11.36, is_gst_free: true, quantity: 1)
        order_line6.update_columns(name: 'order_line6', price: 14.46, is_gst_free: false, quantity: 1)
      end

      it 'calculates the totals for GST items only' do
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST').call

        expect(calculated_totals.order_line_count).to eq(3)
        # [order_line2, order_line4, order_line6].map{|order_line| (order_line.total_price * 1.1).round(2) }.sum
        # (11.36 * 1.1).round(2) + (11.75 * 1.1).round(2) + (14.46 * 1.1).round(2)
        expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)

        expect(calculated_totals.delivery).to eq(0) # no delivery fee yet
        expect(calculated_totals.discount).to eq(0) # no discount added
        expect(calculated_totals.gst.to_s).to eq(3.77.to_s) # subtotal * 0.1
        expect(calculated_totals.surcharge).to eq(0) # no CC surcharge added
        expect(calculated_totals.topup).to eq(0) # no supplier surcharge added
        expect(calculated_totals.total.to_s).to eq(41.34.to_s) # subtotal + GST
      end

      it 'calculated the totals for the GST-FREE items only' do
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST-FREE').call

        expect(calculated_totals.order_line_count).to eq(3)
        expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s) # [order_line1, order_line3, order_line5].map{|order_line| order_line.total_price.round(2) }.sum
        expect(calculated_totals.delivery).to eq(0) # no delivery fee yet
        expect(calculated_totals.discount).to eq(0) # no discount added
        expect(calculated_totals.gst).to eq(0)
        expect(calculated_totals.surcharge).to eq(0) # no CC surcharge added
        expect(calculated_totals.topup).to eq(0) # no supplier surcharge added
        expect(calculated_totals.total.to_s).to eq(37.57.to_s) # same as subtotal
      end

      context 'with a delivery fee' do
        let(:calculated_delivery) { 10 }

        before do
          calculate_delivery = double(Orders::CalculateDelivery)
          allow(Orders::CalculateDelivery).to receive(:new).and_return(calculate_delivery)
          allow(calculate_delivery).to receive(:call).and_return(calculated_delivery)
        end

        it 'requests the delivery fee to be calculated with all order lines (including GST-FREE items)' do # to avoid minimum spend delivery fee issue
          expect(Orders::CalculateDelivery).to receive(:new).with(order: order, order_lines: order.order_lines, profile: customer, recalculate: false)

          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST').call
        end

        it 'includes delivery fee (with gst) in the totals for GST items' do
          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST').call

          expect(calculated_totals.order_line_count).to eq(3)
          expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)
          expect(calculated_totals.delivery).to eq(10) # mocked calculated delivery
          expect(calculated_totals.discount).to eq(0)
          expect(calculated_totals.gst.to_s).to eq(4.77.to_s) # (subtotal + delivery) * 0.1
          expect(calculated_totals.surcharge).to eq(0)
          expect(calculated_totals.topup).to eq(0)
          expect(calculated_totals.total.to_s).to eq(52.34.to_s) # subtotal + GST
        end

        it 'does not includes delivery fee (with gst) in the totals for GST-FREE items' do
          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST-FREE').call

          expect(calculated_totals.order_line_count).to eq(3)
          expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)
          expect(calculated_totals.delivery).to eq(0)
          expect(calculated_totals.discount).to eq(0)
          expect(calculated_totals.gst).to eq(0)
          expect(calculated_totals.surcharge).to eq(0)
          expect(calculated_totals.topup).to eq(0)
          expect(calculated_totals.total.to_s).to eq(37.57.to_s) # same as subtotal
        end
      end # with delivery fee

      context 'with a discount' do
        let(:calculated_discount) { 10 }

        before do
          calculate_discount = double(Orders::CalculateDiscount)
          allow(Orders::CalculateDiscount).to receive(:new).and_return(calculate_discount)
          allow(calculate_discount).to receive(:call).and_return(calculated_discount)
        end

        it 'calculates and removes the discount (including gst) in the the totals for GST items only' do
          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST').call

          expect(calculated_totals.order_line_count).to eq(3)
          expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)
          expect(calculated_totals.delivery).to eq(0)
          expect(calculated_totals.discount).to eq(10)
          expect(calculated_totals.gst.to_s).to eq(2.77.to_s)
          expect(calculated_totals.surcharge).to eq(0)
          expect(calculated_totals.topup).to eq(0)
          expect(calculated_totals.total.to_s).to eq(30.34.to_s)
        end

        it 'calculates and removes the discount (excluding discount gst) in the the totals for the GST-FREE items only' do
          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST-FREE').call

          expect(calculated_totals.order_line_count).to eq(3)
          expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)
          expect(calculated_totals.delivery).to eq(0)
          expect(calculated_totals.discount).to eq(10)
          expect(calculated_totals.gst).to eq(0) # no GST calculated even for discount
          expect(calculated_totals.surcharge).to eq(0)
          expect(calculated_totals.topup).to eq(0)
          expect(calculated_totals.total.to_s).to eq(27.57.to_s)
        end
      end # with a discount

      context 'with a CC surcharge' do
        let(:calculated_surcharge) { 15 }

        before do
          calculate_surcharge = double(Orders::CalculateSurcharge)
          allow(Orders::CalculateSurcharge).to receive(:new).and_return(calculate_surcharge)
          allow(calculate_surcharge).to receive(:call).and_return(calculated_surcharge)
        end

        it 'does not include surcharge for the totals of GST items only' do
          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST').call

          expect(calculated_totals.order_line_count).to eq(3)
          expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)
          expect(calculated_totals.delivery).to eq(0)
          expect(calculated_totals.discount).to eq(0)
          expect(calculated_totals.gst.to_s).to eq(3.77.to_s)
          expect(calculated_totals.surcharge).to eq(0)
          expect(calculated_totals.topup).to eq(0)
          expect(calculated_totals.total.to_s).to eq(41.34.to_s)
        end

        it 'includes surcharge for the totals of GST-FREE items only' do
          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST-FREE').call

          expect(calculated_totals.order_line_count).to eq(3)
          expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)
          expect(calculated_totals.delivery).to eq(0)
          expect(calculated_totals.discount).to eq(0)
          expect(calculated_totals.gst).to eq(0)
          expect(calculated_totals.surcharge).to eq(15)
          expect(calculated_totals.topup).to eq(0)
          expect(calculated_totals.total.to_s).to eq(52.57.to_s)
        end
      end # with CC surcharge

      context 'with supplier surcharge/topup' do
        let!(:supplier2) { create(:supplier_profile, :random) }
        let!(:order_supplier1) { create(:order_supplier, order: order, supplier_profile: supplier, surcharge: 10) }
        let!(:order_supplier2) { create(:order_supplier, order: order, supplier_profile: supplier2, surcharge: 30) }

        before do
          # allow topups on order
          order.update_column(:charge_to_minimum, true)
        end

        it 'does not include surcharge/topup for the totals of GST items only' do
          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST').call

          expect(calculated_totals.order_line_count).to eq(3)
          expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)
          expect(calculated_totals.delivery).to eq(0)
          expect(calculated_totals.discount).to eq(0)
          expect(calculated_totals.gst.to_s).to eq(3.77.to_s)
          expect(calculated_totals.surcharge).to eq(0)
          expect(calculated_totals.topup).to eq(0)
          expect(calculated_totals.total.to_s).to eq(41.34.to_s)
        end

        it 'includes surcharge/topup for the totals of GST-FREE items only' do
          calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload, gst_split: 'GST-FREE').call

          expect(calculated_totals.order_line_count).to eq(3)
          expect(calculated_totals.subtotal.to_s).to eq(37.57.to_s)
          expect(calculated_totals.delivery).to eq(0)
          expect(calculated_totals.discount).to eq(0)
          expect(calculated_totals.gst).to eq(0)
          expect(calculated_totals.surcharge).to eq(0)
          expect(calculated_totals.topup).to eq(40)
          expect(calculated_totals.total.to_s).to eq(77.57.to_s)
        end
      end # with supplier topup
    end # GST split totals
  end # with order lines

  context 'rounding error check' do # in response to an error caused within order #478343
    let(:supplier) { create(:supplier_profile, :random) }

    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 3.05, quantity: 619, is_gst_free: false) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 3.05, quantity: 652, is_gst_free: false) }
    let!(:order_line3) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 3.05, quantity: 578, is_gst_free: false) }

    it 'gives a total of $6203.40 inclusive of GST' do
      calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call

      expect(calculated_totals.subtotal.to_s).to eq('5639.45')
      expect(calculated_totals.gst.to_s).to eq('563.95') # rounded to 2
      expect(calculated_totals.total.to_s).to eq('6203.4')
    end

    context 'with a full discount' do
      let(:calculated_discount) { 6500 } # kind of like 105% discount

      before do
        calculate_discount = double(Orders::CalculateDiscount)
        allow(Orders::CalculateDiscount).to receive(:new).and_return(calculate_discount)
        allow(calculate_discount).to receive(:call).and_return(calculated_discount)
      end

      it 'gives a total of $0.0' do
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call

        expect(calculated_totals.subtotal.to_s).to eq('5639.45')
        expect(calculated_totals.gst.to_s).to eq('0.0')
        expect(calculated_totals.discount.to_s).to eq('5639.45')
        expect(calculated_totals.total.to_s).to eq('0.0')
      end
    end # full discount
  end # rounding error check

  context 'rounding error check - 2' do # in response to an error caused within order #501594
    let(:supplier) { create(:supplier_profile, :random) }
    let(:calculated_delivery) { 10 }

    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 35.06, quantity: 5, is_gst_free: true) }

    before do
      calculate_delivery = double(Orders::CalculateDelivery)
      allow(Orders::CalculateDelivery).to receive(:new).and_return(calculate_delivery)
      allow(calculate_delivery).to receive(:call).and_return(calculated_delivery)
    end

    it 'gives a total of $185.30 exclusive of GST' do
      calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call

      expect(calculated_totals.subtotal.to_s).to eq('175.3')
      expect(calculated_totals.delivery.to_s).to eq('10')
      expect(calculated_totals.gst.to_s).to eq('0')
      expect(calculated_totals.total.to_s).to eq('185.3')
    end

    context 'with a full discount' do
      let(:calculated_discount) { 185.30 } # 100% discount

      before do
        calculate_discount = double(Orders::CalculateDiscount)
        allow(Orders::CalculateDiscount).to receive(:new).and_return(calculate_discount)
        allow(calculate_discount).to receive(:call).and_return(calculated_discount)
      end

      it 'gives a total of $0.0' do
        calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call

        expect(calculated_totals.subtotal.to_s).to eq('175.3')
        expect(calculated_totals.gst.to_s).to eq('0')
        expect(calculated_totals.discount.to_s).to eq('185.3')
        expect(calculated_totals.total.to_s).to eq('0.0')
      end
    end # full discount
  end # rounding error check - 2

  context 'rounding error check - 3' do # in response to an error caused within order #520384
    let(:supplier) { create(:supplier_profile, :random) }
    let(:calculated_delivery) { 0 }

    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 5.63, quantity: 15, is_gst_free: false) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 22.38, quantity: 1, is_gst_free: false) }
    let!(:order_line3) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 111.25, quantity: 1, is_gst_free: false) }
    let!(:order_line4) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 111.25, quantity: 1, is_gst_free: false) }
    let!(:order_line5) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 111.25, quantity: 1, is_gst_free: false) }
    let!(:order_line7) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 136.25, quantity: 1, is_gst_free: false) }
    let!(:order_line8) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 161.25, quantity: 1, is_gst_free: false) }
    let!(:order_line9) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 161.25, quantity: 1, is_gst_free: false) }
    let!(:order_line10) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 373.75, quantity: 1, is_gst_free: false) }

    before do
      calculate_delivery = double(Orders::CalculateDelivery)
      allow(Orders::CalculateDelivery).to receive(:new).and_return(calculate_delivery)
      allow(calculate_delivery).to receive(:call).and_return(calculated_delivery)
    end

    it 'gives a total of $1400.43 inclusive of GST' do
      calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call

      expect(calculated_totals.subtotal.to_s).to eq('1273.08')
      expect(calculated_totals.delivery.to_s).to eq('0')
      expect(calculated_totals.gst.to_s).to eq('127.35')
      expect(calculated_totals.total.to_s).to eq('1400.43')
    end
  end # rounding error check - 3

  context 'rounding error check - 4' do # in response to an discount gst discrepancy caused within order #555651
    let(:supplier) { create(:supplier_profile, :random) }
    let(:calculated_delivery) { 11.44 }
    let(:calculated_discount) { 459.92 }

    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 4.95, quantity: 15, is_gst_free: false) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 4.95, quantity: 15, is_gst_free: false) }
    let!(:order_line3) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 4.95, quantity: 10, is_gst_free: false) }
    let!(:order_line4) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 4.95, quantity: 10, is_gst_free: false) }
    let!(:order_line5) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 4.95, quantity: 10, is_gst_free: false) }
    let!(:order_line6) { create(:order_line, :random, order: order, supplier_profile: supplier, price: 75.74, quantity: 2, is_gst_free: false) }
    
    before do
      calculate_delivery = double(Orders::CalculateDelivery)
      allow(Orders::CalculateDelivery).to receive(:new).and_return(calculate_delivery)
      allow(calculate_delivery).to receive(:call).and_return(calculated_delivery)

      calculate_discount = double(Orders::CalculateDiscount)
        allow(Orders::CalculateDiscount).to receive(:new).and_return(calculate_discount)
        allow(calculate_discount).to receive(:call).and_return(calculated_discount)
    end

    it 'gives a total of 0 inclusive of GST' do
      calculated_totals = Orders::CalculateCustomerTotals.new(order: order.reload).call

      expect(calculated_totals.subtotal.to_s).to eq('448.48')
      expect(calculated_totals.delivery.to_s).to eq('11.44')
      expect(calculated_totals.discount.to_s).to eq('459.92')
      expect(calculated_totals.gst.to_s).to eq('0')
      expect(calculated_totals.total.to_s).to eq('0.0')
    end
  end # rounding error check - 4

end
