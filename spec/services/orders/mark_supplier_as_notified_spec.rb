require 'rails_helper'

RSpec.describe Orders::MarkSupplierAsNotified, type: :service, orders: true, suppliers: true, notifications: true do

  let!(:notified_at) { Time.zone.now }

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }

  let!(:order) { create(:order, :new, name: 'order1', delivery_at: Time.zone.now + 3.days) }

  let!(:order_line11) { create(:order_line, :random, order: order, supplier_profile: supplier1, status: 'accepted') }
  let!(:order_line12) { create(:order_line, :random, order: order, supplier_profile: supplier2, status: 'accepted') }

  it 'updates the order\'s suppliers_notified_at' do
    Orders::MarkSupplierAsNotified.new(order: order, supplier: [supplier1, supplier2].sample, notified_at: notified_at).call

    expect(order.reload.suppliers_notified_at).to eq(notified_at)
  end

  it 'marks the supplier\'s order lines as notified' do
    Orders::MarkSupplierAsNotified.new(order: order, supplier: supplier1, notified_at: notified_at).call

    expect(order_line11.reload.status).to eq('notified')
    expect(order_line12.reload.status).to_not eq('notified') # remains accepted
  end

  context 'with recurrent order' do
    let!(:order2) { create(:order, :new, name: 'order2', delivery_at: Time.zone.now + 5.days) }
    let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1, status: 'accepted') }

    let!(:order3) { create(:order, :new, name: 'order3', delivery_at: Time.zone.now + 2.days) }
    let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1, status: 'accepted') }

    let!(:order4) { create(:order, :new, name: 'order4', delivery_at: Time.zone.now + 7.days) }
    let!(:order_line41) { create(:order_line, :random, order: order4, supplier_profile: supplier2, status: 'accepted') }

    before do
      # make orders recurrent
      [order, order2, order3, order4].each do |recurrent_order|
        recurrent_order.update_columns(order_type: 'recurrent', template_id: order.id)
      end
    end

    it 'marks all subsequent orders as notifed' do
      Orders::MarkSupplierAsNotified.new(order: order, supplier: supplier1, notified_at: notified_at).call

      expect(order.reload.suppliers_notified_at).to eq(notified_at)
      expect(order2.reload.suppliers_notified_at).to eq(notified_at)
      expect(order4.reload.suppliers_notified_at).to eq(notified_at)

      expect(order3.reload.suppliers_notified_at).to_not eq(notified_at) # delivery_at is before order.delivery_at
    end

    it 'marks all subsequent order\'s supplier order_lines as notifed' do
      Orders::MarkSupplierAsNotified.new(order: order, supplier: supplier1, notified_at: notified_at).call

      expect(order_line21.reload.status).to eq('notified')
      expect(order_line31.reload.status).to_not eq('notified') # order_line not within subsequent orders
      expect(order_line41.reload.status).to_not eq('notified') # another supplier
    end
  end

end
