require 'rails_helper'

RSpec.describe Orders::ListSupplierClosureOrders, type: :service, orders: true, chistmas_closures: true do

  let!(:start_of_month) { Time.zone.now.beginning_of_month }
  let!(:supplier1) { create(:supplier_profile, :random, close_from: start_of_month, close_to: start_of_month.end_of_month) }
  let!(:supplier2) { create(:supplier_profile, :random, close_from: start_of_month, close_to: start_of_month.end_of_month) }

  let!(:order1) { create(:order, :new, order_type: 'recurrent', pattern: '1.week', delivery_at: start_of_month + rand(1..10).days) }
  let!(:order2) { create(:order, :new, order_type: 'recurrent', pattern: '1.week', delivery_at: start_of_month + rand(1..10).days) }
  let!(:order3) { create(:order, :new, order_type: 'recurrent', pattern: '1.week', delivery_at: start_of_month + rand(1..10).days) }

  # order containing both orders
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
  let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier2) }

  # supplier 1 only order
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }

  # supplier 2 only order
  let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }
  let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }

  it 'returns the orders where delivery dates are within the supplier closure dates' do
    closure_orders = Orders::ListSupplierClosureOrders.new.call

    expect(closure_orders).to include(order1, order2, order3)
  end

  it 'does not return an order with a status of draft/pending/skipped/rejected/cancelled/delivered/voided' do
    order1.update_column(:status, %w[draft pending rejected skipped cancelled paused delivered voided].sample)

    closure_orders = Orders::ListSupplierClosureOrders.new.call

    expect(closure_orders).to_not include(order1)
    expect(closure_orders).to include(order2, order3)    
  end

  it 'does not return an order outside the supplier closure dates' do
    order2.update_column(:delivery_at, start_of_month - 1.day)

    closure_orders = Orders::ListSupplierClosureOrders.new.call

    expect(closure_orders).to_not include(order2)
    expect(closure_orders).to include(order1, order3)
  end

  context 'with different supplier closure periods' do
    before do
      supplier2.update_columns(close_from: start_of_month + 11.days, close_to: start_of_month.end_of_month + 11.days)
    end

    it 'does not return orders that fall within the order\'s all supplier closure periods' do
      closure_orders = Orders::ListSupplierClosureOrders.new.call
      
      expect(closure_orders).to include(order1, order2)
      expect(closure_orders).to_not include(order3) # order3 belongs to only supplier 2 which is not closed
    end
  end

  context 'one-off orders' do
    before do
      order3.update_column(:order_type, 'one-off')
    end

    it 'does not list (by default) one-off orders' do
      closure_orders = Orders::ListSupplierClosureOrders.new(options: {}).call
        
      expect(closure_orders).to_not include(order3)
      expect(closure_orders).to include(order1, order2)
    end

    it 'returns one-off orders if exclude_one_offs is pased as false' do
      closure_orders = Orders::ListSupplierClosureOrders.new(options: { exclude_one_offs: false }).call

      expect(closure_orders).to include(order1, order2, order3)
    end
  end

end