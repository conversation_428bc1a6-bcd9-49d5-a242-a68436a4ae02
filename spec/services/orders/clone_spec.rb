require 'rails_helper'

RSpec.describe Orders::Clone, type: :service, orders: true, order_lines: true do

  let(:order) { create(:order, :confirmed) }
  let(:clonable_fields) { Orders::Clone::ORDER_FIELDS + Orders::Clone::CONTACT_FIELDS + Orders::Clone::DELIVERY_FIELDS }

  it 'create a clone of an order' do
    order_cloner = Orders::Clone.new(order: order).call

    expect(order_cloner).to be_success
    cloned_order = order_cloner.cloned_order

    expect(cloned_order).to be_present
    expect(cloned_order.id).to_not eq(order.id)
    clonable_fields.each do |field|
      expect(cloned_order.send(field)).to eq(order.send(field))
    end
  end

  it 'set the order stats back to defaults' do
    order_cloner = Orders::Clone.new(order: order).call

    expect(order_cloner).to be_success
    cloned_order = order_cloner.cloned_order

    expect(cloned_order.status).to eq('draft')
    expect(cloned_order.invoice).to be_blank
    expect(cloned_order.delivery_at).to be_blank
    expect(cloned_order.customer_total).to eq(0.0)
    expect(cloned_order.uuid).to be_present
  end

  context 'with order lines', order_lines: true do
    let(:supplier) { create(:supplier_profile, :random, commission_rate: 10) }
    let(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier, price: 10) }
    let(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier, price: 10) }

    let(:location1) { create(:location, :random, order: order) }
    let!(:order_line11) { create(:order_line, :random, order: order, supplier_profile: supplier, location: location1, menu_item: menu_item1, quantity: 20) }
    let!(:order_line12) { create(:order_line, :random, order: order, supplier_profile: supplier, location: location1, menu_item: menu_item2, quantity: 10) }

    let(:location2) { create(:location, :random, order: order) }
    let!(:order_line21) { create(:order_line, :random, order: order, supplier_profile: supplier, location: location2, menu_item: menu_item1, quantity: 12) }

    let!(:availability_checker) { double(Orders::CheckItemAvailability) }

    before do
      allow(Orders::CheckItemAvailability).to receive(:new).and_return(availability_checker)
      all_items_available = OpenStruct.new(success?: true, errors: [])
      allow(availability_checker).to receive(:call).and_return(all_items_available)
    end

    it 'duplicates the order lines within the order' do
      cloner = Orders::Clone.new(order: order.reload).call
      expect(cloner).to be_success

      cloned_order = cloner.cloned_order
      expect(cloned_order.id).to_not eq(order.id)
      cloned_order_lines = cloned_order.order_lines
      expect(cloned_order_lines).to be_present
      expect(cloned_order_lines.size).to eq(3)

      expect(cloned_order_lines.map(&:id)).to_not include(order_line11, order_line12, order_line21) # new order lines
      expect(cloned_order_lines.map(&:order).uniq).to match_array([cloned_order]) # order line in the new order
      expect(cloned_order_lines.map(&:menu_item)).to include(menu_item1, menu_item2)
      expect(cloned_order_lines.map(&:quantity)).to include(10, 12, 20)
    end

    it 'creates the order lines in duplicated locations' do
      cloner = Orders::Clone.new(order: order.reload).call
      expect(cloner).to be_success

      cloned_order = cloner.cloned_order
      expect(cloned_order.id).to_not eq(order.id)
      cloned_order_lines = cloned_order.order_lines
      cloned_locations = cloned_order_lines.map(&:location) || cloned_order.locations
      expect(cloned_locations).to_not include(location1, location2)

      expect(cloned_locations.map(&:details)).to include(location1.details, location2.details)
    end

    it 'recalculates and saves the customer totals in the cloned order' do
      cloner = Orders::Clone.new(order: order.reload).call
      expect(cloner).to be_success

      cloned_order = cloner.cloned_order

      expect(cloned_order.id).to_not eq(order.id)
      expect(cloned_order.customer_subtotal).to eq(420.0)
      expect(cloned_order.customer_gst).to eq(42.0)
      expect(cloned_order.customer_delivery).to eq(0.0)
      expect(cloned_order.customer_total).to eq(462.0)
    end

    it 'recalculates and saves the supplier totals of the cloned order' do
      cloner = Orders::Clone.new(order: order.reload).call
      expect(cloner).to be_success

      cloned_order = cloner.cloned_order
      supplier_totals = cloned_order.order_suppliers.where(supplier_profile: supplier).first
      expect(supplier_totals.subtotal).to eq(378) # based on 10% commission
      expect(supplier_totals.delivery).to eq(0)
      expect(supplier_totals.gst).to eq(37.8)
      expect(supplier_totals.total).to eq(415.8)
    end

    context 'check item availability' do
      it 'makes a request to check item availability' do
        expect(Orders::CheckItemAvailability).to receive(:new).with(order: anything) # passes cloned order created within SO

        cloner = Orders::Clone.new(order: order.reload).call
        expect(cloner).to be_success
      end

      it 'returns all order items as available (aka no warnings)' do
        cloner = Orders::Clone.new(order: order.reload).call

        expect(cloner).to be_success
        expect(cloner.warnings).to be_empty
      end

      context 'with item unavailability' do
        before do
          some_items_available = OpenStruct.new(success?: false, errors: ["#{order_line11.name} - The Item is no longer available"])
          allow(availability_checker).to receive(:call).and_return(some_items_available)
        end

        it 'returns as items being unavailable as a warning' do
          cloner = Orders::Clone.new(order: order.reload).call

          expect(cloner).to be_success
          expect(cloner.warnings).to include("#{order_line11.name} - The Item is no longer available")
        end
      end
    end # item availability

    context 'with order supplier delivery_fee_overrides' do
      let!(:order_supplier) { create(:order_supplier, :random, order: order, supplier_profile: supplier, delivery_fee_override: rand(2.9..10.2)) }

      it 'clones the order with the delivery override' do
        cloner = Orders::Clone.new(order: order.reload).call
        expect(cloner).to be_success

        cloned_order = cloner.cloned_order

        expect(cloned_order.id).to_not eq(order.id)
        cloned_order_supplier = cloned_order.order_suppliers.first
        expect(cloned_order_supplier).to be_present
        expect(cloned_order_supplier.id).to_not eq(order_supplier.id)
        expect(cloned_order_supplier.order).to_not eq(order.id) # cloned_order.id
        expect(cloned_order_supplier.supplier_profile).to eq(supplier)
        expect(cloned_order_supplier.delivery_fee_override).to eq(order_supplier.delivery_fee_override)
      end
    end
  end # with order lines

  context 'for a Saved Woolworths Order' do
    let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }
    before do
      order.update_column(:status, 'saved')
    end

    it 'create a clone of the order' do
      order_cloner = Orders::Clone.new(order: order).call

      expect(order_cloner).to be_success
      cloned_order = order_cloner.cloned_order

      expect(cloned_order).to be_present
      expect(cloned_order.id).to_not eq(order.id)
      clonable_fields.each do |field|
        expect(cloned_order.send(field)).to eq(order.send(field))
      end
    end

    it 'also clones the delivery_at if set in future for saved order' do
      order.update_column(:delivery_at, Time.zone.now + 2.days)
      order_cloner = Orders::Clone.new(order: order).call

      expect(order_cloner).to be_success
      cloned_order = order_cloner.cloned_order

      expect(cloned_order).to be_present
      expect(cloned_order.id).to_not eq(order.id)
      expect(cloned_order.delivery_at).to eq(order.delivery_at)
    end

    it 'voids the original order' do
      order_cloner = Orders::Clone.new(order: order).call

      expect(order_cloner).to be_success
      expect(order.reload.status).to eq('voided')
    end

  end

end
