require 'rails_helper'

RSpec.describe Orders::FetchSupplierAvailability, type: :service, orders: true, suppliers: true, deliverable_suburbs: true do

  let!(:order) { create(:order, :new) }
  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }

  let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
  let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

  let!(:suburb1) { create(:suburb, :random) }
  let!(:delivery_zone11) { create(:delivery_zone, :random, suburb: suburb1, supplier_profile: supplier1) }
  let!(:delivery_zone12) { create(:delivery_zone, :random, suburb: suburb1, supplier_profile: supplier2) }

  let!(:suburb2) { create(:suburb, :random) }
  let!(:delivery_zone21) { create(:delivery_zone, :random, suburb: suburb2, supplier_profile: supplier1) }

  let!(:deliverable_delivery_zone) { [delivery_zone11, delivery_zone12, delivery_zone21].sample }

  context 'with delivery zones with deliverable suburbs' do
    before do
      # mock delivery zone fetcher
      delivery_zone_fetcher = double(Suppliers::FetchPotentialDeliveryZone)
      allow(Suppliers::FetchPotentialDeliveryZone).to receive(:new).and_return(delivery_zone_fetcher)
      allow(delivery_zone_fetcher).to receive(:call).and_return(deliverable_delivery_zone)
    end

    it 'makes a request for each of the orders\'s suppliers if no suppliers are passed' do
      expect(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier1, suburb: suburb1, delivery_date: order.delivery_at)
      expect(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier2, suburb: suburb1, delivery_date: order.delivery_at)

      Orders::FetchSupplierAvailability.new(order: order, suburb: suburb1).call
    end

    it 'makes request for the each of the passed in suppliers to check if it has delivery zones in range' do
      expect(Suppliers::FetchPotentialDeliveryZone).to_not receive(:new).with(supplier: supplier1, suburb: suburb1, delivery_date: order.delivery_at) # did not pass supplier2
      expect(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier2, suburb: suburb1, delivery_date: order.delivery_at)

      Orders::FetchSupplierAvailability.new(order: order, suppliers: [supplier2], suburb: suburb1).call
    end

    it 'makes a request for the order\'s delivery suburb if no suburb is passed' do
      delivery_suburb = create(:suburb, :random)
      order.update_column(:delivery_suburb_id, delivery_suburb.id)
      order.reload

      expect(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier1, suburb: delivery_suburb, delivery_date: order.delivery_at)
      expect(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier2, suburb: delivery_suburb, delivery_date: order.delivery_at)

      Orders::FetchSupplierAvailability.new(order: order).call
    end

    it 'returns as available' do
      availability_fetcher = Orders::FetchSupplierAvailability.new(order: order, suburb: suburb1).call

      expect(availability_fetcher).to be_available
    end

    it 'returns deliverable suppliers and delivery_zones' do
      availability_fetcher = Orders::FetchSupplierAvailability.new(order: order, suburb: suburb1).call

      expect(availability_fetcher.deliverable_suppliers.keys).to include(supplier1, supplier2)
      expect(availability_fetcher.deliverable_suppliers.values.flatten(1)).to include(deliverable_delivery_zone)
    end

    it 'returns as available even if passed in suburb is not contained in returned deliverable_suburbs' do
      in_range_suburb = create(:suburb, :random)
      availability_fetcher = Orders::FetchSupplierAvailability.new(order: order, suburb: in_range_suburb).call

      expect(availability_fetcher).to be_available # as we've mocked Suppliers::FetchPotentialDeliveryZone to always return a delivery zone
    end

    context 'with at least 1 supplier not having any delivery zones in suburb' do
      before do
        # mock supplier2 to not return any delivery zones in range of suburb2
        supplier2_delivery_zone_fetcher = double(Suppliers::FetchPotentialDeliveryZone)
        allow(Suppliers::FetchPotentialDeliveryZone).to receive(:new).with(supplier: supplier2, suburb: suburb2, delivery_date: order.delivery_at).and_return(supplier2_delivery_zone_fetcher)
        allow(supplier2_delivery_zone_fetcher).to receive(:call).and_return(nil)
      end

      it 'returns as unavailable' do
        availability_fetcher = Orders::FetchSupplierAvailability.new(order: order, suburb: suburb2).call

        expect(availability_fetcher).to_not be_available
      end
    end # 1 supplier without delivery zones in range
  end # all supplier delivery zones in range

  context 'with no delivery zones in range' do
    before do
      delivery_zone_fetcher = double(Suppliers::FetchPotentialDeliveryZone)
      allow(Suppliers::FetchPotentialDeliveryZone).to receive(:new).and_return(delivery_zone_fetcher)
      allow(delivery_zone_fetcher).to receive(:call).and_return(nil)
    end

    it 'returns as unavailable' do
      availability_fetcher = Orders::FetchSupplierAvailability.new(order: order, suburb: [suburb1, suburb2].sample).call

      expect(availability_fetcher).to_not be_available
    end
  end

end
