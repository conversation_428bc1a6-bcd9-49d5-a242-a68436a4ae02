require 'rails_helper'

RSpec.describe Orders::FetchSupplierMarkups, type: :service, order: true, markups: true do

  let!(:customer) { create(:customer_profile, :random, :with_flags) }

  let!(:supplier1) { create(:supplier_profile, :random, markup: rand(10.3..19.8), commission_rate: rand(10.3..19.8)) }
  let!(:supplier2) { create(:supplier_profile, :random, markup: rand(10.3..19.8), commission_rate: rand(10.3..19.8)) }

  let!(:order) { create(:order, :confirmed, customer_profile: customer) }

  let!(:baseline_price) { rand(10.2..20.9) }
  let!(:supplier1_price) { baseline_price.to_f * (1 + (supplier1.markup / 100)) }
  let!(:supplier2_price) { baseline_price.to_f * (1 + (supplier2.markup / 100)) }

  let!(:order_line1) { create(:order_line, :random, baseline: baseline_price, price: supplier1_price, order: order, supplier_profile: supplier1) }
  let!(:order_line2) { create(:order_line, :random, baseline: baseline_price, price: supplier2_price, order: order, supplier_profile: supplier2) }

  it 'returns the base supplier markups for each of the order suppliers' do
    supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

    expect(supplier_markups).to be_present
    expect(supplier_markups.size).to eq(2)
    expect(supplier_markups.sample).to be_a(Orders::FetchSupplierMarkups::SupplierMarkup)

    supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
    expect(supplier1_markup).to be_present
    expect(supplier1_markup.supplier).to eq(supplier1)
    expect(supplier1_markup.markup.round(2)).to eq(supplier1.markup.round(2))

    supplier2_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier2 }
    expect(supplier2_markup).to be_present
    expect(supplier2_markup.supplier).to eq(supplier2)
    expect(supplier2_markup.markup.round(2)).to eq(supplier2.markup.round(2))
  end

  it 'returns the base supplier markups for passed in order line suppliers' do
    supplier_markups = Orders::FetchSupplierMarkups.new(order: order, order_lines: [order_line2]).call

    expect(supplier_markups).to be_present
    expect(supplier_markups.size).to eq(1)

    supplier2_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier2 }
    expect(supplier2_markup).to be_present
    expect(supplier2_markup.supplier).to eq(supplier2)
    expect(supplier2_markup.markup.round(2)).to eq(supplier2.markup.round(2))
  end

  context 'with no supplier commission' do
    before do
      supplier2.update_column(:commission_rate, [0, nil].sample)
    end

    it 'only returns the base supplier markups for supplier with a commission rate' do
      supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

      expect(supplier_markups).to be_present
      expect(supplier_markups.size).to eq(1)

      supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
      expect(supplier1_markup).to be_present
      expect(supplier1_markup.supplier).to eq(supplier1)
      expect(supplier1_markup.markup.round(2)).to eq(supplier1.markup.round(2))
    end
  end

  context 'with supplier markup overrides' do
    let!(:markup_override2) { create(:supplier_markup_override, overridable: customer, supplier_profile: supplier2, markup: rand(10.3..19.8), commission_rate: rand(10.3..19.8)) }
    let!(:supplier2_overriden_price) { baseline_price.to_f * (1 + (markup_override2.markup / 100)) }

    before do
      order_line2.update_column(:price, supplier2_overriden_price)
    end

    it 'returns the overriden supplier markups for each of the order suppliers if present else returns base supplier markup' do
      supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

      expect(supplier_markups).to be_present
      expect(supplier_markups.size).to eq(2)

      supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
      expect(supplier1_markup).to be_present
      expect(supplier1_markup.supplier).to eq(supplier1)
      expect(supplier1_markup.markup.round(2)).to eq(supplier1.markup.round(2))

      supplier2_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier2 }
      expect(supplier2_markup).to be_present
      expect(supplier2_markup.supplier).to eq(supplier2)
      expect(supplier2_markup.markup.round(2)).to eq(markup_override2.markup.round(2))
    end

    context 'with overriden supplier commission as 0' do
      before do
        markup_override2.update_column(:commission_rate, 0)
      end

      it 'returns the overriden supplier markups only if overridden commission non-zero' do
        supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

        expect(supplier_markups).to be_present
        expect(supplier_markups.size).to eq(1)

        supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
        expect(supplier1_markup).to be_present
        expect(supplier1_markup.supplier).to eq(supplier1)
        expect(supplier1_markup.markup.round(2)).to eq(supplier1.markup.round(2))
      end
    end

    context 'with missing overriden supplier commission' do
      before do
        markup_override2.update_column(:commission_rate, nil)
      end

      it 'returns the overriden supplier markups even if overridden commission rate blank (nil)' do
        supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

        expect(supplier_markups).to be_present
        expect(supplier_markups.size).to eq(2)

        supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
        expect(supplier1_markup).to be_present
        expect(supplier1_markup.supplier).to eq(supplier1)
        expect(supplier1_markup.markup.round(2)).to eq(supplier1.markup.round(2))

        supplier2_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier2 }
        expect(supplier2_markup).to be_present
        expect(supplier2_markup.supplier).to eq(supplier2)
        expect(supplier2_markup.markup.round(2)).to eq(markup_override2.markup.round(2))
      end

      context 'with no base supplier commission' do
        before do
          supplier2.update_column(:commission_rate, [0, nil].sample)
        end

        it 'only returns the overriden supplier markups (with no commission override) if base commission rate is non-zero' do
          supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

          expect(supplier_markups).to be_present
          expect(supplier_markups.size).to eq(1)

          supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
          expect(supplier1_markup).to be_present
          expect(supplier1_markup.supplier).to eq(supplier1)
          expect(supplier1_markup.markup.round(2)).to eq(supplier1.markup.round(2))
        end
      end # no base supplier commission
    end # no override commission
  end # with markup overrides

  # if the supplier markup changed between the order line creation and fetching of markups
  context 'order line with a momentary mismatched markup price' do
    let!(:markup_variance) { (rand(2.1..5.3) * ([true, false].sample ? 1 : -1)).round(2) }
    let!(:momentary_supplier_1_markup) { (supplier1.markup + markup_variance).round(2) }
    let!(:momentary_supplier1_price) { baseline_price.to_f * (1 + (momentary_supplier_1_markup / 100)) }

    before do
      order_line1.update_column(:price, momentary_supplier1_price)
    end

    it 'returns the supplier markups based on order_line price against baseline' do
      puts "  (variance: #{markup_variance})"
      supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

      expect(supplier_markups).to be_present
      expect(supplier_markups.size).to eq(2)

      supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
      expect(supplier1_markup).to be_present
      expect(supplier1_markup.supplier).to eq(supplier1)
      expect(supplier1_markup.markup.round(2)).to be_within(0.05).of(momentary_supplier_1_markup.round(2))
    end

    context 'with the mismatch variance is within MARKUP ROUNDING THRESHOLD (0.25)' do
      let!(:markup_variance) { (rand(0.15..Orders::FetchSupplierMarkups::MARKUP_ROUNDING_THRESHOLD) * ([true, false].sample ? 1 : -1)).round(2) }

      it 'returns the base supplier markups' do
        puts "    (variance: #{markup_variance})"
        supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

        expect(supplier_markups).to be_present
        expect(supplier_markups.size).to eq(2)

        supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
        expect(supplier1_markup).to be_present
        expect(supplier1_markup.supplier).to eq(supplier1)
        expect(supplier1_markup.markup.round(2)).to_not be_within(0.05).of(momentary_supplier_1_markup.round(2))
        expect(supplier1_markup.markup.round(2)).to eq(supplier1.markup.round(2))
      end
    end # variance within MARKUP ROUNDING THRESHOLD
  end # with a order line price markup variance

  context 'for a custom order' do
    before do
      order.update_columns(order_variant: 'event_order', commission: rand(10.3..19.8))
    end

    it 'returns the custom order markup `order.commission` for each of the order suppliers' do
      supplier_markups = Orders::FetchSupplierMarkups.new(order: order).call

      expect(supplier_markups).to be_present
      expect(supplier_markups.size).to eq(2)
      expect(supplier_markups.sample).to be_a(Orders::FetchSupplierMarkups::SupplierMarkup)

      supplier1_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier1 }
      expect(supplier1_markup).to be_present
      expect(supplier1_markup.supplier).to eq(supplier1)
      expect(supplier1_markup.markup.round(2)).to eq(order.commission.round(2))

      supplier2_markup = supplier_markups.detect{|supplier_markup| supplier_markup.supplier == supplier2 }
      expect(supplier2_markup).to be_present
      expect(supplier2_markup.supplier).to eq(supplier2)
      expect(supplier2_markup.markup.round(2)).to eq(order.commission.round(2))
    end
  end
end