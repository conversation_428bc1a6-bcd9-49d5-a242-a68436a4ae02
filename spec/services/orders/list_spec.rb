require 'rails_helper'

RSpec.describe Orders::List, type: :service, orders: true do
  let!(:now) { Time.zone.now }
  let!(:order1) { create(:order, :confirmed, name: 'My first order') }
  let!(:order2) { create(:order, :confirmed, name: 'My second order') }
  let!(:order3) { create(:order, :confirmed, name: 'My third order') }
  let!(:order4) { create(:order, :confirmed, name: 'My fourth order') }

  it 'lists all the orders' do
    lister_options = {}
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order1, order2, order3, order4)
  end

  it 'lists the order by (customer) profile' do
    lister_options = { for_customer: order2.customer_profile }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order2)
    expect(orders).to_not include(order1, order3, order4)
  end

  context 'with attached pantry managers', pantry_managers: true do
    let!(:pantry_manager1) { create(:customer_profile, :random) }
    let!(:pantry_manager2) { create(:customer_profile, :random) }

    before do
      order1.update_column(:pantry_manager_id, pantry_manager2.id)
      order3.update_column(:pantry_manager_id, pantry_manager1.id)
    end

    it 'lists the orders attached to passed in pantry managers' do
      lister_options = { for_pantry_managers: [pantry_manager1, pantry_manager2] }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order1, order3)
      expect(orders).to_not include(order2, order4)
    end
  end

  it 'list non draft orders' do
    [order1, order3].each {|order| order.update_column(:status, 'draft') }
    lister_options = { non_draft: true }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order2, order4)
    expect(orders).to_not include(order1, order3)
  end

  it 'list non voided orders' do
    [order2, order4].each {|order| order.update_column(:status, 'voided') }
    lister_options = { non_voided: true }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order1, order3)
    expect(orders).to_not include(order2, order4)
  end

  it 'lists orders whose name contains the specified query' do
    lister_options = { name: 'third' }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order3)
    expect(orders).to_not include(order1, order2, order4)
  end

  context 'filter by query' do # name
    it 'lists orders whose name contains the specified query' do
      lister_options = { query: 'third' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order3)
      expect(orders).to_not include(order1, order2, order4)
    end
  end

  context 'filter by ID' do
    before do
      # clear old orders
      Order.all.destroy_all
      # reset ID counter
      ActiveRecord::Base.connection.reset_pk_sequence!('orders')
      # create new orders
      @id_order1 = create(:order, :confirmed, name: 'My first order')
      @id_order2 = create(:order, :confirmed, name: 'My second order')
      @id_order3 = create(:order, :confirmed, name: 'My third order')
      @id_order4 = create(:order, :confirmed, name: 'My fourth order')
    end

    it 'lists orders whose ID starts with passed in query' do
      lister_options = { query: @id_order1.id.to_s[0] }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(@id_order1)
      expect(orders).to_not include(@id_order2, @id_order3, @id_order4)
    end

    it 'lists orders whose ID ends with passed in query' do
      lister_options = { query: @id_order2.id.to_s[-1] }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(@id_order2)
      expect(orders).to_not include(@id_order1, @id_order3, @id_order4)
    end
  end

  it 'lists orders with a specific order type' do
    [order1, order4].each {|order| order.update_column(:order_type, 'one-off') }
    lister_options = { order_type: 'one-off' }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order1, order4)
    expect(orders).to_not include(order2, order3)
  end

  it 'lists orders with a specific order variant' do
    [order2, order4].each {|order| order.update_column(:order_variant, 'event_order') }
    lister_options = { order_variant: 'event_order' }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order2, order4)
    expect(orders).to_not include(order1, order3)
  end

  context 'filtering team orders' do
    it 'lists all team orders based on passed in filter for order_type = team-order' do
      [order1, order3].each {|order| order.update_columns(order_variant: 'team_order', order_type: 'one-off') }
      lister_options = { order_type: 'team-order' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order1, order3)
      expect(orders).to_not include(order2, order4)
    end

    it 'lists all team orders based on passed in filter for order_type = team_order' do
      [order2, order4].each {|order| order.update_column(:order_variant, %w[team_order recurring_team_order].sample) }
      lister_options = { order_variant: 'team_order' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order2, order4)
      expect(orders).to_not include(order1, order3)
    end
  end

  it 'lists orders with passed in order statuses' do
    [order1, order3].each {|order| order.update_column(:status, %w[new amended].sample) }

    lister_options = { statuses: %w[new amended] }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order1, order3)
    expect(orders).to_not include(order2, order4)
  end

  it 'lists orders with passed in delivery type' do
    delivery_type = (Order::VALID_DELIVERY_TYPES - ['normal']).sample
    [order1, order4].each {|order| order.update_column(:delivery_type, delivery_type) }

    lister_options = { delivery_type: delivery_type }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order1, order4)
    expect(orders).to_not include(order2, order3)
  end

  context 'filtering by meal plans', meal_plans: true do
    let!(:meal_plan) { create(:meal_plan, :random) }

    before do
      [order1, order4].each{|order| order.update_column(:meal_plan_id, meal_plan.id) }
    end

    it 'lists orders belonging to the meal plan' do
      lister_options = { meal_plan: meal_plan }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order1, order4)
      expect(orders).to_not include(order2, order3)
    end

    it 'lists orders belonging to the meal plan ID' do
      lister_options = { meal_plan_id: meal_plan.id }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order1, order4)
      expect(orders).to_not include(order2, order3)
    end
  end

  context 'filter for delivery duration' do
    let!(:order_with_time_1) { create(:order, :confirmed, name: 'order_with_time_1',  order_type: 'recurrent', delivery_at: now - 20.days) }
    let!(:order_with_time_2) { create(:order, :confirmed, name: 'order_with_time_2',  order_type: 'recurrent', delivery_at: now + 1.hours) }
    let!(:order_with_time_3) { create(:order, :confirmed, name: 'order_with_time_3',  order_type: 'recurrent', delivery_at: now - 1.day) }
    let!(:order_with_time_4) { create(:order, :confirmed, name: 'order_with_time_4',  order_type: 'recurrent', delivery_at: now + 3.days) }
    let!(:order_with_time_5) { create(:order, :confirmed, name: 'order_with_time_5', order_type: 'one-off', delivery_at: now + 50.days) }
    let!(:order_with_time_6) { create(:order, :confirmed, name: 'order_with_time_6', order_type: 'recurrent', delivery_at: now + 50.days) }

    it 'lists upcoming orders within the range' do
      lister_options = { for_duration: 'week' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order_with_time_2, order_with_time_4)
      expect(orders).to include(order_with_time_5) # out of range but a one-off
      expect(orders).to_not include(order_with_time_6) # not in range
      expect(orders).to_not include(order_with_time_1, order_with_time_3) # past orders
    end

    it 'lists all upcoming orders' do
      lister_options = { for_duration: 'all' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order_with_time_2, order_with_time_4)
      expect(orders).to include(order_with_time_5, order_with_time_6)
      expect(orders).to_not include(order_with_time_1, order_with_time_3) # past orders
    end

    it 'also list orders for today' do
      order_with_time_3.update_column(:delivery_at, now.beginning_of_day)
      lister_options = { for_duration: 'all' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order_with_time_3)
    end

    it 'it also lists delivered upcoming orders' do
      order_with_time_2.update_column(:status, 'delivered')
      lister_options = { for_duration: 'week' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order_with_time_2)
    end

    it 'also lists upcoming one-off orders irrespective of range' do
      lister_options = { for_duration: 'week' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order_with_time_5) # one-off outside range
    end

    it 'does not list recurrent orders outside range' do
      lister_options = { for_duration: 'week' }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to_not include(order_with_time_6) # not in range
    end

    context 'past orders' do
      it 'lists past orders' do
        lister_options = { for_duration: 'fortnight', show_past: true }
        orders = Orders::List.new(options: lister_options).call

        expect(orders).to include(order_with_time_3)
        expect(orders).to_not include(order_with_time_1) # not in range
        expect(orders).to_not include(order_with_time_2, order_with_time_4) # future orders
      end

      it 'only shows orders before the current date' do
        order_with_time_1.update_column(:delivery_at, now.beginning_of_day - 1.hour)
        order_with_time_3.update_column(:delivery_at, now.beginning_of_day)

        lister_options = { for_duration: 'fortnight', show_past: true }
        orders = Orders::List.new(options: lister_options).call

        expect(orders).to include(order_with_time_1) # before today
        expect(orders).to_not include(order_with_time_3) # today
      end
    end

    it 'lists both upcoming and past orders' do
      lister_options = { for_duration: 'fortnight', include_past: true }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order_with_time_2, order_with_time_4) # upcoming recurrent in range
      expect(orders).to include(order_with_time_3) # past order in range
      expect(orders).to include(order_with_time_5) # future one-off order
      expect(orders).to_not include(order_with_time_1, order_with_time_6) # not in range
    end

    context 'filter by date' do
      it 'lists orders based on passed in date' do
        lister_options = { for_date: now }
        orders = Orders::List.new(options: lister_options).call

        expect(orders).to include(order_with_time_2)
        expect(orders).to_not include(order_with_time_1, order_with_time_3, order_with_time_4, order_with_time_5, order_with_time_6)
      end

      it 'lists orders based on passed in date string' do
        filter_date = now + 50.days
        lister_options = { for_date: [filter_date.strftime('%d/%m/%Y'), filter_date.strftime('%Y/%m/%d'), filter_date.strftime('%Y-%m-%d')].sample }
        orders = Orders::List.new(options: lister_options).call

        expect(orders).to include(order_with_time_5, order_with_time_6)
        expect(orders).to_not include(order_with_time_1, order_with_time_2, order_with_time_3, order_with_time_4)
      end
    end # filter by date

    context 'filter by date range' do
      it 'lists orders based on passed in date' do
        lister_options = { from_date: now, to_date: now + 3.days }
        orders = Orders::List.new(options: lister_options).call

        expect(orders).to include(order_with_time_2, order_with_time_4)
        expect(orders).to_not include(order_with_time_1, order_with_time_3, order_with_time_5, order_with_time_6)
      end
    end
  end # filter by duration

  it 'can exclude the orders as specified' do
    lister_options = { excluded_order_ids: [order2.id, order4.id] }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order1, order3)
    expect(orders).to_not include(order2, order4)
  end

  it 'paginates the list of orders' do
    lister_options = { order_by: :id, with_pagination: { page: 2, limit: 1 } }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order2)
    expect(orders).to_not include(order1, order3, order4)
  end

  it 'orders the list of orders as specified' do
    lister_options = { order_by: { id: :desc } }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to match_array([order4, order3, order2, order1])
  end

  it 'does not list cancelled recurrent orders - unless its the template order' do
    Order.all.each{|order| order.update_column(:order_type, 'recurrent') } # make all recurrent
    [order1, order2, order3].each{|order| order.update_column(:status, 'cancelled') } # cancel orders
    order2.update_column(:template_id, order2.id) # make order2 a template order

    lister_options = { ignore_cancelled_recurrent: true }
    orders = Orders::List.new(options: lister_options).call

    expect(orders).to include(order4) # listed cause it is not-cancelled
    expect(orders).to include(order2) # listed cause is template order (even if cancelled)
    expect(orders).to_not include(order1, order3)
  end

  context 'filter by suppliers' do
    let!(:supplier1) { create(:supplier_profile, :random) }
    let!(:supplier2) { create(:supplier_profile, :random) }
    let!(:supplier3) { create(:supplier_profile, :random) }

    let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }

    let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1) }
    let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

    let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier3) }

    let!(:order_supplier41) { create(:order_supplier, :random, order: order4, supplier_profile: supplier3) }

    it 'lists the orders whose supplier profiles (through order lines) are within the passed in supplier ids' do
      lister_options = { supplier_ids: [supplier1, supplier2].map(&:id) }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order1, order2)
      expect(orders).to_not include(order3, order4)
    end

    it 'lists the orders whose supplier profiles (through order suppliers) are within the passed in supplier ids' do
      lister_options = { supplier_ids: [supplier3].map(&:id) }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order4)
      expect(orders).to include(order3)
      expect(orders).to_not include(order1, order2)
    end
  end

  context 'filter only quotes' do
    before do
      [order2, order3].each{|order| order.update_column(:status, 'quoted') }
    end

    it 'only returns quoted orders' do
      lister_options = { only_quotes: true }
      orders = Orders::List.new(options: lister_options).call

      expect(orders).to include(order2, order3)
      expect(orders).to_not include(order1, order4)
    end
  end

end
