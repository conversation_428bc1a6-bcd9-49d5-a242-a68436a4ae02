require 'rails_helper'

RSpec.describe Orders::CheckItemAvailability, type: :service, orders: true do

  let!(:order) { create(:order, :draft) }

  let!(:supplier) {create(:supplier_profile, :random) }

  let!(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier) }
  let!(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier) }

  let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier, menu_item: menu_item1) }
  let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier, menu_item: menu_item2) }

  it 'returns that the order has available items' do
    availablability_checker = Orders::CheckItemAvailability.new(order: order).call

    expect(availablability_checker).to be_success
  end

  it 'returns with errors if the order line\'s supplier is no longer available' do
    supplier.update_column(:is_searchable, false) # mark supplier as not searchable
    availablability_checker = Orders::CheckItemAvailability.new(order: order).call

    expect(availablability_checker).to_not be_success
    expect(availablability_checker.errors).to include("#{order_line1.name} - The Supplier is no longer available")
    expect(availablability_checker.errors).to include("#{order_line2.name} - The Supplier is no longer available")

    expect(order_line1.reload.last_errors).to include('The Supplier is no longer available')
    expect(order_line2.reload.last_errors).to include('The Supplier is no longer available')
  end

  it 'returns with errors if at least order line\'s menu item is archived' do
    archived_menu_items = [menu_item1, menu_item2].sample
    archived_menu_items.update_column(:archived_at, Time.zone.now) # archive menu item

    availablability_checker = Orders::CheckItemAvailability.new(order: order).call

    expect(availablability_checker).to_not be_success
    errored_order_line = [order_line1, order_line2].detect{|order_line| order_line.menu_item == archived_menu_items }

    expect(availablability_checker.errors).to include("#{errored_order_line.name} - The Item is no longer available")
    expect(errored_order_line.reload.last_errors).to include('The Item is no longer available')
  end

  it 'returns with errors if at least order line\'s menu item is marked as hidden' do
    archived_menu_items = [menu_item1, menu_item2].sample
    archived_menu_items.update_column(:is_hidden, true) # hide menu item

    availablability_checker = Orders::CheckItemAvailability.new(order: order).call

    expect(availablability_checker).to_not be_success
    errored_order_line = [order_line1, order_line2].detect{|order_line| order_line.menu_item == archived_menu_items }

    expect(availablability_checker.errors).to include("#{errored_order_line.name} - The Item is no longer available")
    expect(errored_order_line.reload.last_errors).to include('The Item is no longer available')
  end

  context 'with serving sizes' do
    let!(:serving_size1) { create(:serving_size, :random, menu_item: menu_item1) }
    let!(:serving_size2) { create(:serving_size, :random, menu_item: menu_item2) }

    before do
      # attach serving sizes to the order lines
      order_line1.update_column(:serving_size_id, serving_size1.id)
      order_line2.update_column(:serving_size_id, serving_size2.id)
    end

    it 'returns that the order has available items (including attached serving sizes)' do
      availablability_checker = Orders::CheckItemAvailability.new(order: order).call

      expect(availablability_checker).to be_success
    end

    it 'returns with errors if at least one order line\'s serving size is archived' do
      archived_serving_size = [serving_size1, serving_size2].sample
      archived_serving_size.update_column(:archived_at, Time.zone.now) # archive serving size

      availablability_checker = Orders::CheckItemAvailability.new(order: order).call

      expect(availablability_checker).to_not be_success
      errored_order_line = [order_line1, order_line2].detect{|order_line| order_line.serving_size.present? && order_line.serving_size == archived_serving_size }

      expect(availablability_checker.errors).to include("#{errored_order_line.name} - The Item is no longer available")
      expect(errored_order_line.reload.last_errors).to include('The Item is no longer available')
    end
  end # with serving sizes

  context 'with selected menu extras' do
    let!(:menu_extra_section1) { create(:menu_extra_section, :random, menu_item: menu_item1) }
    let!(:menu_extra11) { create(:menu_extra, :random, menu_extra_section: menu_extra_section1) }
    let!(:menu_extra12) { create(:menu_extra, :random, menu_extra_section: menu_extra_section1) }

    let!(:menu_extra_section2) { create(:menu_extra_section, :random, menu_item: menu_item2) }
    let!(:menu_extra21) { create(:menu_extra, :random, menu_extra_section: menu_extra_section2) }

    before do
      # attach selected menu extras to the order lines
      order_line1.update(selected_menu_extras: [menu_extra11, menu_extra12].map(&:id))
      order_line2.update(selected_menu_extras: [menu_extra21].map(&:id))
    end

    it 'returns that the order has available items (including attached menu extras)' do
      availablability_checker = Orders::CheckItemAvailability.new(order: order).call

      expect(availablability_checker).to be_success
    end

    it 'returns with errors if at least one order line selected menu extras is archived' do
      menu_item_with_archived_extras = [menu_item1, menu_item2].sample
      menu_item_with_archived_extras.menu_extras.sample.update_column(:archived_at, Time.zone.now) # archive menu extra

      availablability_checker = Orders::CheckItemAvailability.new(order: order).call

      expect(availablability_checker).to_not be_success
      errored_order_line = [order_line1, order_line2].detect{|order_line| order_line.menu_item == menu_item_with_archived_extras }

      expect(availablability_checker.errors).to include("#{errored_order_line.name} - Selected item extras are no longer available")
      expect(errored_order_line.reload.last_errors).to include('Selected item extras are no longer available')
    end
  end # with menu extras

end