require 'rails_helper'

RSpec.describe Orders::Quote, type: :service, orders: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:order) { create(:order, :draft, customer_profile: customer) }
  let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier) }

  # extra elements
  let!(:suburb) { create(:suburb, :random) }
  let!(:credit_card) { create(:credit_card, :random, customer_profiles: [customer]) }

  let!(:order_params) do
    {
      name: Faker::Name.name,
      delivery_at: Time.zone.now + 22.days,
      delivery_address: Faker::Address.street_address,
      delivery_suburb_id: suburb.id,
      delivery_instruction: Faker::Lorem.sentences(number: 2).join('. '),
      credit_card_id: credit_card.id,
    }
  end

  let(:generated_quote_document) { double(Customers::OrderDetails) }

  before do
    # mock promotions syncer
    promotions_syncer = double(Promotions::SyncWithOrder)
    allow(Promotions::SyncWithOrder).to receive(:new).and_return(promotions_syncer)
    allow(promotions_syncer).to receive(:call).and_return(true)

    # mock quote document generator
    document_generator = double(Documents::Generate::CustomerOrderDetails)
    allow(Documents::Generate::CustomerOrderDetails).to receive(:new).and_return(document_generator)
    allow(document_generator).to receive(:call).and_return(generated_quote_document)

    # mock quote email sender
    email_sender, delayed_email_sender = double(Customers::Emails::SendOrderQuoteEmail)
    allow(Customers::Emails::SendOrderQuoteEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'saves the order as quoted' do
    order_quoter = Orders::Quote.new(order: order, order_params: order_params).call

    expect(order_quoter).to be_success

    quoted_order = order_quoter.order
    expect(quoted_order.id).to eq(order.id) # updates the draft order
    expect(quoted_order.status).to eq('quoted')
  end

  it 'makes a request to sync with promotions', promotions: true do
    expect(Promotions::SyncWithOrder).to receive(:new).with(order: order, customer: customer)

    order_quoter = Orders::Quote.new(order: order, order_params: order_params).call
    expect(order_quoter).to be_success
  end

  context 'with an attached customer quote', quotes: true do
    let!(:customer_quote) { create(:customer_quote, :random, customer_profile: customer, status: 'submitted') }

    before do
      order.update_column(:customer_quote_id, customer_quote.id)
    end

    it 'marks the customer quote as `quoted`' do
      order_quoter = Orders::Quote.new(order: order, order_params: order_params).call
      expect(order_quoter).to be_success

      expect(customer_quote.reload.status).to eq('quoted')
    end
  end

  it 'generates the quote document' do
    document_reference = "#{Customers::Emails::SendOrderQuoteEmail::EMAIL_TEMPLATE}-#{order.id}-#{Time.zone.now.strftime('%d-%m-%Y')}"
    expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: anything, reference: document_reference, variation: 'quote') # order will be the to be a quoted order

    order_quoter = Orders::Quote.new(order: order, order_params: order_params).call
    expect(order_quoter).to be_success
  end

  context 'with purchase order detials', purchase_orders: true do
    let!(:customer_purchase_order) { create(:customer_purchase_order, customer_profile: customer, po_number: SecureRandom.hex(7)) }

    it 'adds the purchase order based on passed in ID' do
      qupte_order_params = order_params.merge({ cpo_id: customer_purchase_order.id })
      order_quoter = Orders::Quote.new(order: order, order_params: qupte_order_params).call

      expect(order_quoter).to be_success
      quoted_order = order_quoter.order
      expect(quoted_order.cpo_id).to eq(customer_purchase_order.id)
      expect(quoted_order.po_number).to eq(customer_purchase_order.po_number)
    end

    it 'saves the purchase order based on passed in PO number' do
      qupte_order_params = order_params.merge({ cpo_id: customer_purchase_order.po_number })
      order_quoter = Orders::Quote.new(order: order, order_params: qupte_order_params).call

      expect(order_quoter).to be_success
      quoted_order = order_quoter.order
      expect(quoted_order.cpo_id).to eq(customer_purchase_order.id)
      expect(quoted_order.po_number).to eq(customer_purchase_order.po_number)
    end

    it 'creates and saves a new customer purchase order based on passed in PO number' do
      qupte_order_params = order_params.merge({ cpo_id: customer_purchase_order.po_number + SecureRandom.hex(2) })
      order_quoter = Orders::Quote.new(order: order, order_params: qupte_order_params).call

      expect(order_quoter).to be_success
      quoted_order = order_quoter.order
      expect(quoted_order.customer_purchase_order).to be_present
      expect(quoted_order.customer_purchase_order.customer_profile).to eq(order.customer_profile)
      expect(quoted_order.po_number).to eq(qupte_order_params[:cpo_id])
    end

    it 'removes an exsiting purchase order if not passed in params' do
      order.update_column(:cpo_id, customer_purchase_order.id)

      qupte_order_params = order_params.merge({ cpo_id: nil })
      order_quoter = Orders::Quote.new(order: order, order_params: qupte_order_params).call

      expect(order_quoter).to be_success
      quoted_order = order_quoter.order
      expect(quoted_order.customer_purchase_order).to be_blank
    end
  end

  context 'with GST-free purchase order detials', purchase_orders: true do
    let!(:gst_free_customer_purchase_order) { create(:customer_purchase_order, customer_profile: customer, po_number: SecureRandom.hex(7)) }

    it 'adds the purchase order based on passed in ID' do
      qupte_order_params = order_params.merge({ gst_free_cpo_id: gst_free_customer_purchase_order.id })
      order_quoter = Orders::Quote.new(order: order, order_params: qupte_order_params).call

      expect(order_quoter).to be_success
      quoted_order = order_quoter.order
      expect(quoted_order.gst_free_cpo_id).to eq(gst_free_customer_purchase_order.id)
      expect(quoted_order.gst_free_po_number).to eq(gst_free_customer_purchase_order.po_number)
    end

    it 'saves the purchase order based on passed in PO number' do
      qupte_order_params = order_params.merge({ gst_free_cpo_id: gst_free_customer_purchase_order.po_number })
      order_quoter = Orders::Quote.new(order: order, order_params: qupte_order_params).call

      expect(order_quoter).to be_success
      quoted_order = order_quoter.order
      expect(quoted_order.gst_free_cpo_id).to eq(gst_free_customer_purchase_order.id)
      expect(quoted_order.gst_free_po_number).to eq(gst_free_customer_purchase_order.po_number)
    end

    it 'creates and saves a new customer purchase order based on passed in PO number' do
      qupte_order_params = order_params.merge({ gst_free_cpo_id: gst_free_customer_purchase_order.po_number + SecureRandom.hex(2) })
      order_quoter = Orders::Quote.new(order: order, order_params: qupte_order_params).call

      expect(order_quoter).to be_success
      quoted_order = order_quoter.order
      expect(quoted_order.gst_free_customer_purchase_order).to be_present
      expect(quoted_order.gst_free_customer_purchase_order.customer_profile).to eq(order.customer_profile)
      expect(quoted_order.gst_free_po_number).to eq(qupte_order_params[:gst_free_cpo_id])
    end

    it 'removes an exsiting purchase order if not passed in params' do
      order.update_column(:gst_free_cpo_id, gst_free_customer_purchase_order.id)

      qupte_order_params = order_params.merge({ gst_free_cpo_id: nil })
      order_quoter = Orders::Quote.new(order: order, order_params: qupte_order_params).call

      expect(order_quoter).to be_success
      quoted_order = order_quoter.order
      expect(quoted_order.gst_free_customer_purchase_order).to be_blank
    end
  end

  context 'Quote Email(s)' do
    let!(:quote_emails) { 3.times.map{|_| Faker::Internet.email }.join(';') }
    let!(:quote_message) { Faker::Lorem.sentences(number: 2).join(' ') }

    let!(:quote_params) do
      {
        mode: 'send-quote',
        quote_emails: quote_emails,
        quote_message: quote_message
      }
    end

    it 'does not send any emails if quote mode is `save-quote`' do
      moded_quote_params = quote_params.merge({ mode: 'save-quote' })

      expect(Customers::Emails::SendOrderQuoteEmail).to_not receive(:new)

      order_quoter = Orders::Quote.new(order: order, customer: customer, order_params: order_params, quote_params: moded_quote_params).call
      expect(order_quoter).to be_success
    end

    context 'with passed in quote emails' do
      it 'sends non-customer quote email with generated document to passed in quote_emails' do
        expect(Customers::Emails::SendOrderQuoteEmail).to receive(:new).with(order: anything, customer: customer, document: generated_quote_document, quote_emails: quote_emails, quote_message: quote_message) # passed in emails

        order_quoter = Orders::Quote.new(order: order, customer: customer, order_params: order_params, quote_params: quote_params).call
        expect(order_quoter).to be_success
      end

      it 'does not send the customer an email if the quote emails does not contain the customer\'s email' do
        expect(Customers::Emails::SendOrderQuoteEmail).to_not receive(:new).with(order: anything, customer: customer, document: generated_quote_document, quote_message: quote_message) # no quote emails

        order_quoter = Orders::Quote.new(order: order, customer: customer, order_params: order_params, quote_params: quote_params).call
        expect(order_quoter).to be_success
      end

      it 'send a customer email and also non-customer quotes email with generated document if the passed in quote emails contain the customer\'s email' do
        customer_email = customer.email
        customer_quote_params = quote_params.merge({ quote_emails: "#{customer_email};#{quote_emails}" })

        expect(Customers::Emails::SendOrderQuoteEmail).to receive(:new).with(order: anything, customer: customer, document: generated_quote_document, quote_message: quote_message) # customer email
        non_customer_quote_emails = quote_emails.split(';').reject{|email| email == customer_email }.join(';')
        expect(Customers::Emails::SendOrderQuoteEmail).to receive(:new).with(order: anything, customer: customer, document: generated_quote_document, quote_emails: non_customer_quote_emails, quote_message: quote_message) # passed in emails

        order_quoter = Orders::Quote.new(order: order, customer: customer, order_params: order_params, quote_params: customer_quote_params).call
        expect(order_quoter).to be_success
      end
    end
  end

  it 'logs a `New Order Quoted` event', event_logs: true do
    expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'new-order-quoted')

    order_quoter = Orders::Quote.new(order: order, order_params: order_params).call
    expect(order_quoter).to be_success
  end

  context 'errors' do
    it 'cannot quote a non-draft and non-quoted order' do
      order.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[quoted draft]).sample)

      order_quoter = Orders::Quote.new(order: order).call

      expect(order_quoter).to_not be_success
      expect(order_quoter.errors).to include('Cannot quote a non-draft order')
    end

    it 'cannot quote a recurring order' do
      order.update_column(:order_type, 'recurrent')

      order_quoter = Orders::Quote.new(order: order).call

      expect(order_quoter).to_not be_success
      expect(order_quoter.errors).to include('Cannot quote a recurring order')
    end

    it 'cannot quote an empty order (without any order lines)' do
      order_line.destroy

      order_quoter = Orders::Quote.new(order: order).call

      expect(order_quoter).to_not be_success
      expect(order_quoter.errors).to include('Cannot quote an empty order')
    end

    context 'for a Woolworths order' do
      let!(:woolworths_order) { create(:woolworths_order, :random, order: order, status: 'connected to an account.', account_in_use: true) }

      it 'saves the Woolworths order with a status of `saved`' do
        order_quoter = Orders::Quote.new(order: order).call

        expect(order_quoter).to be_success

        quoted_order = order_quoter.order
        expect(quoted_order.id).to eq(order.id) # updates the draft order
        expect(quoted_order.status).to eq('saved')
      end

      it 'updates the status of the attached woolworths_order and detaches from account' do
        order_quoter = Orders::Quote.new(order: order).call

        expect(order_quoter).to be_success
        quoted_order = order_quoter.order
        quoted_woolworths_order = quoted_order.woolworths_order

        expect(quoted_woolworths_order).to be_present
        expect(quoted_woolworths_order.id).to eq(woolworths_order.id)
        expect(quoted_woolworths_order.status).to eq('connected to an account. Saved for future')
        expect(quoted_woolworths_order.account_in_use).to be_falsey
      end
    end
  end

end
