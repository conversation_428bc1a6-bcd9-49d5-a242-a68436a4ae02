require 'rails_helper'

RSpec.describe OrderReviews::SendInvitations, type: :service, orders: true, emails: true, reviews: true, customers: true, notifications: true do

  let!(:base_delivery_at) { Time.zone.now.beginning_of_month }

  let!(:customer1) { create(:customer_profile, :random, :with_flags) }
  let!(:customer2) { create(:customer_profile, :random, :with_flags) }

  let!(:order11) { create(:order, :delivered, order_type: 'one-off',  customer_profile: customer1, delivery_at: base_delivery_at - 1.day) }
  let!(:order12) { create(:order, :delivered, order_type: 'one-off',  customer_profile: customer1, delivery_at: base_delivery_at + 1.month + 3.day) }

  let!(:order21) { create(:order, :delivered, order_type: 'one-off',  customer_profile: customer2, delivery_at: base_delivery_at + 1.month + 4.day) }
  let!(:order22) { create(:order, :delivered, order_type: 'one-off',  customer_profile: customer2, delivery_at: base_delivery_at + 5.day) }

  before do
    email_sender = double(Customers::Emails::SendOrderReviewEmail)
    allow(Customers::Emails::SendOrderReviewEmail).to receive(:new).and_return(email_sender)
    successfull_email_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
    allow(email_sender).to receive(:call).and_return(successfull_email_sender_response)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :review_invitation_start_date).and_return(Time.zone.now.beginning_of_year - 1.year)
  end

  it 'sends review invitation emails to all customers for each delivered orders' do
    expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order11, customer: customer1)
    expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order12, customer: customer1)
    expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order21, customer: customer2)
    expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order22, customer: customer2)

    invitation_sender = OrderReviews::SendInvitations.new.call
    expect(invitation_sender).to be_success
  end

  it 'does not send review invitations for order delivered before the Yordar review invitation start date' do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :review_invitation_start_date).and_return(Time.zone.now.end_of_month)

    expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order12, customer: customer1)
    expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order21, customer: customer2)

    expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order11, customer: customer1)
    expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order22, customer: customer2)

    invitation_sender = OrderReviews::SendInvitations.new.call
    expect(invitation_sender).to be_success
  end

  context 'with notification preferences for email template' do
    let!(:template_name) { OrderReviews::SendInvitations::EMAIL_TEMPLATE }
    let!(:notification_preference) { create(:notification_preference, account: customer1, template_name: template_name, active: false) }

    it 'does not send emails to customer with notification preference as in-active' do
      expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order11, customer: customer1)
      expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order12, customer: customer1)

      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order21, customer: customer2)
      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order22, customer: customer2)

      invitation_sender = OrderReviews::SendInvitations.new.call
      expect(invitation_sender).to be_success
    end

    it 'sends notification to customers with active notification preference or missing notification preference for email template' do
      notification_preference.update_column(:active, true)

      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order11, customer: customer1)
      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order12, customer: customer1)
      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order21, customer: customer2)
      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order22, customer: customer2)

      invitation_sender = OrderReviews::SendInvitations.new.call
      expect(invitation_sender).to be_success
    end

  end

  context 'if Yordar review invitation is not set' do
    before do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :review_invitation_start_date).and_return(nil)
    end

    it 'does not send review invitations for order delivered before the current month' do
      expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order11, customer: customer1)

      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order12, customer: customer1)
      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order21, customer: customer2)
      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order22, customer: customer2)

      invitation_sender = OrderReviews::SendInvitations.new.call
      expect(invitation_sender).to be_success
    end
  end

  it 'does not send review invitations for non-delivered orders' do
    [order11, order21].each do |order|
      order.update_column(:status, %w[new pending amended confirmed].sample)
    end

    expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order11, customer: customer1)
    expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order21, customer: customer2)

    invitation_sender = OrderReviews::SendInvitations.new.call
    expect(invitation_sender).to be_success
  end

  context 'with recurrent orders' do
    before do
      # make orders recurrent tempalate orders
      [order11, order21].each do |order|
        order.update_columns(order_type: 'recurrent', recurrent_id: order.id, template_id: order.id)
      end
      # non-template recurrent orders
      order12.update_columns(order_type: 'recurrent', recurrent_id: order11.id, template_id: order11.id)
      order22.update_columns(order_type: 'recurrent', recurrent_id: order21.id, template_id: order21.id)
    end

    it 'does not send review invitations for non-template recurrent orders' do
      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order11, customer: customer1)
      expect(Customers::Emails::SendOrderReviewEmail).to receive(:new).with(order: order21, customer: customer2)

      expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order12, customer: customer1)
      expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order22, customer: customer2)

      invitation_sender = OrderReviews::SendInvitations.new.call
      expect(invitation_sender).to be_success
    end
  end

  context 'with already sent review emails' do
    let!(:customer1_order12_review_email) { create(:email, :random, recipient: nil, ref: 'customer-order_review_invitation', fk_id: order12.id) }
    let!(:customer2_order21_review_email) { create(:email, :random, recipient: nil, ref: 'customer-order_review_invitation', fk_id: order21.id) }

    it 'does not re-send review initation for order' do
      expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order12, customer: customer1)
      expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order21, customer: customer2)

      invitation_sender = OrderReviews::SendInvitations.new.call
      expect(invitation_sender).to be_success
    end
  end

  it 'does not send review invitations to customers with cancel_review_requests customer flags set to true' do
    customer1.customer_flags.update_column(:cancel_review_requests, true)

    expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order11, customer: customer1)
    expect(Customers::Emails::SendOrderReviewEmail).to_not receive(:new).with(order: order12, customer: customer1)

    invitation_sender = OrderReviews::SendInvitations.new.call
    expect(invitation_sender).to be_success
  end

end
