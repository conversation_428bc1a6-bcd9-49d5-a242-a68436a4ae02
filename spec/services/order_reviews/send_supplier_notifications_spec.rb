require 'rails_helper'

RSpec.describe OrderReviews::SendSupplierNotifications, type: :serice, order_reviews: true do

  let!(:review_time) { Time.zone.now }
  let!(:review_week) { review_time.beginning_of_week }

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }

  let!(:order_review11) { create(:order_review, :random, :random_scores, supplier_profile: supplier1, created_at: review_week + 1.day) }
  let!(:order_review12) { create(:order_review, :random, :random_scores, supplier_profile: supplier1, created_at: review_week + 3.day) }

  let!(:order_review21) { create(:order_review, :random, :random_scores, supplier_profile: supplier2, created_at: review_week + 4.day) }
  let!(:order_review22) { create(:order_review, :random, :random_scores, supplier_profile: supplier2, created_at: review_week + 5.day) }

  before do
    email_sender = double(Suppliers::Emails::SendOrderReviewsEmail)
    allow(Suppliers::Emails::SendOrderReviewsEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call).and_return(true)
  end

  it 'sends notifications for each supplier about reviews for its orders' do
    expect(Suppliers::Emails::SendOrderReviewsEmail).to receive(:new).with(supplier: supplier1, time: review_time, order_reviews: [order_review11, order_review12])
    expect(Suppliers::Emails::SendOrderReviewsEmail).to receive(:new).with(supplier: supplier2, time: review_time, order_reviews: [order_review21, order_review22])

    notifications_sender = OrderReviews::SendSupplierNotifications.new(time: review_time).call
    expect(notifications_sender).to be_success
    expect(notifications_sender.sent_notifications.size).to eq(2) # per supplier
  end

  it 'only sends notifications about reviews within the review week' do
    [order_review11, order_review21].each do |review|
      review.update_column(:created_at, [review_week - 1.day, review_week + 1.week].sample)
    end

    expect(Suppliers::Emails::SendOrderReviewsEmail).to receive(:new).with(supplier: supplier1, time: review_time, order_reviews: [order_review12]) # does not have order_review11
    expect(Suppliers::Emails::SendOrderReviewsEmail).to receive(:new).with(supplier: supplier2, time: review_time, order_reviews: [order_review22]) # does not have order_review21

    notifications_sender = OrderReviews::SendSupplierNotifications.new(time: review_time).call
    expect(notifications_sender).to be_success
  end

  context 'with already sent review email' do
    let!(:supplier1_review_email) { create(:email, :random, recipient: nil, ref: "supplier-review_notification-#{review_time.strftime('%Y%W')}", fk_id: supplier1.id) }

    it 'does not send a weekly review notification to the supplier who already received the email' do
      expect(Suppliers::Emails::SendOrderReviewsEmail).to_not receive(:new).with(supplier: supplier1, time: review_time, order_reviews: [order_review11, order_review12])
      expect(Suppliers::Emails::SendOrderReviewsEmail).to receive(:new).with(supplier: supplier2, time: review_time, order_reviews: [order_review21, order_review22])

      notifications_sender = OrderReviews::SendSupplierNotifications.new(time: review_time).call
      expect(notifications_sender).to be_success
    end
  end

end
