require 'rails_helper'

RSpec.describe OrderReviews::FetchRatingSummary, type: :service do

  let!(:review_time) { Time.zone.now.beginning_of_week + 1.day + 3.hours }

  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:review1) { create(:order_review, :random, :random_scores, supplier_profile: supplier, created_at: review_time + 1.hours) }
  let!(:review2) { create(:order_review, :random, :random_scores, supplier_profile: supplier, created_at: review_time + 2.hours) }
  let!(:review3) { create(:order_review, :random, :random_scores, supplier_profile: supplier, created_at: review_time + 3.hours) }

  let!(:all_reviews) { [review1, review2, review3] }

  it 'returns the start and end week as the start and end times' do
    ratings_summary = OrderReviews::FetchRatingSummary.new.call

    current_week = Time.zone.now
    expect(ratings_summary.starts_at).to eq(current_week.beginning_of_week)
    expect(ratings_summary.ends_at).to eq(current_week.end_of_week)
  end

  it 'returns the rating fields' do
    ratings_summary = OrderReviews::FetchRatingSummary.new.call

    expect(ratings_summary.rating_fields).to eq(OrderReviews::FetchRatingSummary::RATING_FIELDS)
  end

  it 'gets all last week order reviews by default' do
    ratings_summary = OrderReviews::FetchRatingSummary.new.call

    expect(ratings_summary.order_reviews).to include(review1, review2, review3)
  end

  context 'with passed in time' do
    let!(:fetcher_options) { { time: review_time } }

    it 'returns order reviews within the passed in time\'s week' do
      ratings_summary = OrderReviews::FetchRatingSummary.new(options: fetcher_options).call

      expect(ratings_summary.order_reviews).to include(review1, review2, review3)
    end

    it 'does not return order reviews not created within the passed in time\'s week' do
      review1.update_column(:created_at, review_time + 1.week)
      ratings_summary = OrderReviews::FetchRatingSummary.new(options: fetcher_options).call

      expect(ratings_summary.order_reviews).to_not include(review1)
      expect(ratings_summary.order_reviews).to include(review2, review3)
    end
  end

  context 'with passed in supplier' do
    let!(:fetcher_options) { { supplier: supplier } }

    it 'returns order reviews for the passed in supplier' do
      ratings_summary = OrderReviews::FetchRatingSummary.new(options: fetcher_options).call

      expect(ratings_summary.order_reviews).to include(review1, review2, review3)
    end

    it 'does not return order reviews not for the passed in supplier' do
      review2.update_column(:created_at, review_time + 1.week)
      ratings_summary = OrderReviews::FetchRatingSummary.new(options: fetcher_options).call

      expect(ratings_summary.order_reviews).to_not include(review2)
      expect(ratings_summary.order_reviews).to include(review1, review3)
    end
  end

  describe 'calculated scores and counts' do
    it 'returns the scores and counts as an OpenStruct' do
      ratings_summary = OrderReviews::FetchRatingSummary.new.call

      expect(ratings_summary.scores).to be_is_a(OpenStruct)
      expect(ratings_summary.counts).to be_is_a(OpenStruct)
    end

    OrderReviews::FetchRatingSummary::RATING_FIELDS.each do |field|
      it "returns the total scores and count of the #{field}" do
        ratings_summary = OrderReviews::FetchRatingSummary.new.call

        field_scores = all_reviews.map{|review| review.send("#{field}_score") }
        expect(ratings_summary.scores.send(field)).to eq(field_scores.compact.sum)
        expect(ratings_summary.counts.send(field)).to eq(field_scores.compact.size)
      end
    end

    it 'returns the total score and count for the reviews' do
      ratings_summary = OrderReviews::FetchRatingSummary.new.call

      expected_scores = []
      OrderReviews::FetchRatingSummary::RATING_FIELDS.each do |field|
        expected_scores += all_reviews.map{|review| review.send("#{field}_score") }
      end

      expect(ratings_summary.total_score).to eq(expected_scores.compact.sum)
      expect(ratings_summary.total_count).to eq(expected_scores.compact.size)
    end
  end

  context 'average values' do
    it 'responds to a method called average' do
      ratings_summary = OrderReviews::FetchRatingSummary.new.call

      expect(ratings_summary).to respond_to(:average)
    end

    it 'returns the average (rouded to 1 decimal place) of the passed in values' do
      ratings_summary = OrderReviews::FetchRatingSummary.new.call

      score = ratings_summary.total_score
      count = ratings_summary.total_count
      expect(ratings_summary.average(score, count)).to eq((score / count.to_f).round(1))
    end

    it 'returns the the first value of passed in count is 0' do
      ratings_summary = OrderReviews::FetchRatingSummary.new.call

      score = ratings_summary.total_score
      expect(ratings_summary.average(score, 0)).to eq(score)
    end
  end

end
