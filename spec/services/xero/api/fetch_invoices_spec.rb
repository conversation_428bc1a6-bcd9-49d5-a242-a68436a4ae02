require 'rails_helper'

RSpec.describe Xero::API::FetchInvoices, type: :service, xero: true, invoices: true do

  let!(:invoice1) { create(:invoice, :random) }
  let!(:invoice2) { create(:invoice, :random) }
  let!(:invoice3) { create(:invoice, :random) }
  let!(:invoice4) { create(:invoice, :random) }
  let!(:invoices) { [invoice1, invoice2, invoice3, invoice4] }

  let!(:xero_invoice_model) { double('XeroInvoice') }

  let!(:xero_invoice1) { double('XeroInvoice', invoice_number: invoice1.number) }
  let!(:xero_invoice2) { double('XeroInvoice', invoice_number: "INV#{invoice2.number}") }
  let!(:xero_invoice3) { double('XeroInvoice', invoice_number: "BK#{invoice3.number}") }
  let!(:xero_invoice4) { double('XeroInvoice', invoice_number: invoice4.number) }

  let!(:invoice5) { create(:invoice, :random) }
  let!(:xero_invoice5) { double('XeroInvoice', invoice_number: invoice5.number) }

  let!(:xero_client) { double(Xeroizer::OAuth2Application) }

  before do
    allow_any_instance_of(Xero::API::FetchInvoices).to receive(:xero_client).and_return(xero_client)

    allow(xero_client).to receive(:Invoice).and_return(xero_invoice_model)
    allow(xero_invoice_model).to receive(:all).and_return([xero_invoice1, xero_invoice2, xero_invoice3, xero_invoice4]) # mock the fetch
  end

  it 'makes a request to fetch Xero Invoices (via API) with the appropriate where condition' do
    where_conditions = invoices.map{|invoice| "InvoiceNumber==\"#{invoice.number}\"" }
    where_conditions += invoices.map{|invoice| "InvoiceNumber==\"BK#{invoice.number}\"" }
    where_conditions += invoices.map{|invoice| "InvoiceNumber==\"INV#{invoice.number}\"" }
    expect(xero_invoice_model).to receive(:all).with(where: where_conditions.join(' OR '))

    Xero::API::FetchInvoices.new(invoices: invoices).call
  end

  it 'returns mapped invoices' do
    mapped_invoices = Xero::API::FetchInvoices.new(invoices: invoices).call

    expect(mapped_invoices.sample).to be_a(Xero::API::FetchInvoices::MappedInvoice)
    expect(mapped_invoices.map(&:invoice)).to include(invoice1, invoice2, invoice3, invoice4)
    expect(mapped_invoices.map(&:xero_invoice)).to include(xero_invoice1, xero_invoice2, xero_invoice3, xero_invoice4)

    expect(mapped_invoices.map(&:invoice)).to_not include(invoice5)
    expect(mapped_invoices.map(&:xero_invoice)).to_not include(xero_invoice5)
  end

  it 'maps the xero invoice with the correct invoice via number' do
    mapped_invoices = Xero::API::FetchInvoices.new(invoices: invoices).call

    invoice_1_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.invoice == invoice1 }
    expect(invoice_1_map.xero_invoice).to eq(xero_invoice1)

    invoice_2_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.invoice == invoice2 }
    expect(invoice_2_map.xero_invoice).to eq(xero_invoice2)

    invoice_3_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.invoice == invoice3 }
    expect(invoice_3_map.xero_invoice).to eq(xero_invoice3)

    invoice_4_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.invoice == invoice4 }
    expect(invoice_4_map.xero_invoice).to eq(xero_invoice4)
  end

end
