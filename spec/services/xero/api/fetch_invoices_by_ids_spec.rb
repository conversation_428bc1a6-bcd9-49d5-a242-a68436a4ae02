require 'rails_helper'

RSpec.describe Xero::API::FetchInvoicesByIds, type: :service, xero: true, invoices: true do

  let!(:invoice1) { create(:invoice, :random) }
  let!(:invoice2) { create(:invoice, :random) }
  let!(:invoice3) { create(:invoice, :random) }
  let!(:invoice4) { create(:invoice, :random) }
  let!(:invoices) { [invoice1, invoice2, invoice3, invoice4] }

  let!(:xero_invoice1) { create(:xero_invoice, :random) }
  let!(:xero_invoice2) { create(:xero_invoice, :random) }
  let!(:xero_invoice3) { create(:xero_invoice, :random) }
  let!(:xero_invoice4) { create(:xero_invoice, :random) }
  let!(:xero_invoices) { [xero_invoice1, xero_invoice2, xero_invoice3, xero_invoice4] }

  let!(:xero_api_invoice_model) { double('XeroAPIInvoice') }

  let!(:xero_api_invoice1) { double('XeroAPIInvoice', invoice_id: xero_invoice1.invoice_id, invoice_number: invoice1.number) }
  let!(:xero_api_invoice2) { double('XeroAPIInvoice', invoice_id: xero_invoice2.invoice_id, invoice_number: "INV#{invoice2.number}") }
  let!(:xero_api_invoice3) { double('XeroAPIInvoice', invoice_id: xero_invoice3.invoice_id, invoice_number: "BK#{invoice3.number}") }
  let!(:xero_api_invoice4) { double('XeroAPIInvoice', invoice_id: xero_invoice4.invoice_id, invoice_number: invoice4.number) }

  let!(:invoice5) { create(:invoice, :random) }
  let!(:xero_invoice5) { create(:xero_invoice, :random) }
  let!(:xero_api_invoice5) { double('XeroAPIInvoice', invoice_ids: xero_invoice5.invoice_id, invoice_number: invoice5.number) }

  let!(:xero_client) { double(Xeroizer::OAuth2Application) }

  before do
    allow_any_instance_of(Xero::API::FetchInvoicesByIds).to receive(:xero_client).and_return(xero_client)

    allow(xero_client).to receive(:Invoice).and_return(xero_api_invoice_model)
    allow(xero_api_invoice_model).to receive(:all).and_return([xero_api_invoice1, xero_api_invoice2, xero_api_invoice3, xero_api_invoice4]) # mock the fetch
  end

  it 'makes a request to fetch Xero Invoices (via API) with the appropriate IDs condition' do
    expect(xero_api_invoice_model).to receive(:all).with(IDs: xero_invoices.map(&:invoice_id).join(','))

    Xero::API::FetchInvoicesByIds.new(xero_invoices: xero_invoices).call
  end

  it 'returns mapped invoices' do
    mapped_invoices = Xero::API::FetchInvoicesByIds.new(xero_invoices: xero_invoices).call

    expect(mapped_invoices.sample).to be_a(Xero::API::FetchInvoicesByIds::MappedInvoice)
    expect(mapped_invoices.map(&:invoice)).to include(invoice1, invoice2, invoice3, invoice4)
    expect(mapped_invoices.map(&:xero_invoice)).to include(xero_invoice1, xero_invoice2, xero_invoice3, xero_invoice4)
    expect(mapped_invoices.map(&:xero_api_invoice)).to include(xero_api_invoice1, xero_api_invoice2, xero_api_invoice3, xero_api_invoice4)

    # xero_invoice not passed in
    expect(mapped_invoices.map(&:invoice)).to_not include(invoice5)
    expect(mapped_invoices.map(&:xero_invoice)).to_not include(xero_invoice5)
    expect(mapped_invoices.map(&:xero_api_invoice)).to_not include(xero_api_invoice5)
  end

  it 'maps the yordar invoice with the correct xero api invoice via number and xero api invoice via invoice id' do
    mapped_invoices = Xero::API::FetchInvoicesByIds.new(xero_invoices: xero_invoices).call

    xero_invoice1_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice1 }
    expect(xero_invoice1_map.invoice).to eq(invoice1)
    expect(xero_invoice1_map.xero_api_invoice).to eq(xero_api_invoice1)

    xero_invoice2_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice2 }
    expect(xero_invoice2_map.invoice).to eq(invoice2)
    expect(xero_invoice2_map.xero_api_invoice).to eq(xero_api_invoice2)

    xero_invoice3_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice3 }
    expect(xero_invoice3_map.invoice).to eq(invoice3)
    expect(xero_invoice3_map.xero_api_invoice).to eq(xero_api_invoice3)

    xero_invoice4_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice4 }
    expect(xero_invoice4_map.invoice).to eq(invoice4)
    expect(xero_invoice4_map.xero_api_invoice).to eq(xero_api_invoice4)
  end

  it 'return mapping for passed in xero invoice with missing invoice and xero api invoice if fetch doesn\'t return any mapping' do
    mapped_invoices = Xero::API::FetchInvoicesByIds.new(xero_invoices: xero_invoices + [xero_invoice5]).call

    expect(mapped_invoices.map(&:xero_invoice)).to include(xero_invoice5)

    xero_invoice5_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice5 }
    expect(xero_invoice5_map.invoice).to be_blank
    expect(xero_invoice5_map.xero_api_invoice).to be_blank
  end

  context 'with mismatched/missing invoice numbers' do
    let!(:mismatched_xero_api_invoice1) { double('XeroAPIInvoice', invoice_id: xero_invoice1.invoice_id, invoice_number: 'mismatched-invoice-number-1') }
    let!(:mismatched_xero_api_invoice2) { double('XeroAPIInvoice', invoice_id: xero_invoice2.invoice_id, invoice_number: nil) }
    let!(:mismatched_xero_api_invoice3) { double('XeroAPIInvoice', invoice_id: xero_invoice3.invoice_id, invoice_number: 'mismatched-invoice-number-3') }
    let!(:mismatched_xero_api_invoice4) { double('XeroAPIInvoice', invoice_id: xero_invoice4.invoice_id, invoice_number: 'mismatched-invoice-number-4') }

    before do
      allow(xero_api_invoice_model).to receive(:all).and_return([mismatched_xero_api_invoice1, mismatched_xero_api_invoice2, mismatched_xero_api_invoice3, mismatched_xero_api_invoice4]) # mock the fetch
    end

    it 'maps the xero api invoice with xero invoice via invoice id and return with missing invoice' do
      mapped_invoices = Xero::API::FetchInvoicesByIds.new(xero_invoices: xero_invoices).call

      xero_invoice1_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice1 }
      expect(xero_invoice1_map.invoice).to be_blank
      expect(xero_invoice1_map.xero_api_invoice).to eq(mismatched_xero_api_invoice1)

      xero_invoice2_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice2 }
      expect(xero_invoice2_map.invoice).to be_blank
      expect(xero_invoice2_map.xero_api_invoice).to eq(mismatched_xero_api_invoice2)

      xero_invoice3_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice3 }
      expect(xero_invoice3_map.invoice).to be_blank
      expect(xero_invoice3_map.xero_api_invoice).to eq(mismatched_xero_api_invoice3)

      xero_invoice4_map = mapped_invoices.detect{|mapped_invoice| mapped_invoice.xero_invoice == xero_invoice4 }
      expect(xero_invoice4_map.invoice).to be_blank
      expect(xero_invoice4_map.xero_api_invoice).to eq(mismatched_xero_api_invoice4)
    end
  end

end
