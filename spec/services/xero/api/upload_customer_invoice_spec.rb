require 'rails_helper'

RSpec.describe Xero::API::UploadCustomerInvoice, type: :service, xero: true do

  let!(:customer) { create(:customer_profile, :random, :with_user, :with_flags) }

  let!(:invoice) { create(:invoice, :random) }
  let!(:order1) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: invoice) }
  let!(:order2) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: invoice) }

  let!(:xero_contact) { 'mock-xero-contact' }
  let!(:xero_client) { double(Xeroizer::OAuth2Application) }
  let!(:contact_uploader) { double(Xero::API::UploadCustomerContact) }
  let!(:xero_invoice_model) { double('XeroInvoice') }
  let!(:xero_invoice) { double('XeroInvoice', invoice_id: SecureRandom.uuid) }
  let!(:xero_line_item) { double('XeroLineItem') }

  let!(:gst_free_code) { 'gst-free-code' }
  let!(:non_gst_free_code) { 'non-gst-free-code' }
  let!(:delivery_code) { 'delivery-code' }
  let!(:gst_free_delivery_code) { 'gst-free-delivery-code' }
  let!(:discount_code) { 'discount-code' }
  let!(:surcharge_code) { 'surcharge-code' }

  before do
    # stub gst
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(0.1)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(0.15)

    # stub xero constants (saved per environment)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:xero, :customer_gst_free).and_return(gst_free_code)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:xero, :customer_non_gst_free).and_return(non_gst_free_code)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:xero, :surcharge_code).and_return(surcharge_code)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:xero, :delivery_code).and_return(delivery_code)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:xero, :gst_free_delivery_code).and_return(gst_free_delivery_code)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:xero, :discount_code).and_return(discount_code)

    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

    # mock contact uploader request
    allow(Xero::API::UploadCustomerContact).to receive(:new).and_return(contact_uploader)
    contact_response = OpenStruct.new(success?: true, contact: xero_contact)
    allow(contact_uploader).to receive(:call).and_return(contact_response)

    # mock Xero API client
    allow_any_instance_of(Xero::API::UploadCustomerInvoice).to receive(:xero_client).and_return(xero_client)

    # mock Xero Invoice Model
    allow(xero_client).to receive(:Invoice).and_return(xero_invoice_model)
    # mock no exsting contact
    allow(xero_invoice_model).to receive(:all).and_return([])
    # mock new contact build
    allow(xero_invoice_model).to receive(:build).and_return(xero_invoice)

    # mock xero_invoice line item methods
    allow(xero_invoice).to receive(:add_line_item).and_return(xero_line_item)
    allow(xero_line_item).to receive(:add_tracking).and_return(true) # mock line item method

    # mock xero_invoice methods
    allow(xero_invoice).to receive('url=').and_return(true)
    allow(xero_invoice).to receive(:save).and_return(true)
  end

  it 'makes a request to upload the invoice contact' do
    expect(Xero::API::UploadCustomerContact).to receive(:new).with(customer: customer)

    invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
    expect(invoice_uploader).to be_success
    expect(invoice_uploader.contact).to eq(xero_contact)
  end

  it 'checks for existing invoices via invoice number' do
    expect(xero_invoice_model).to receive(:all).with(where: { invoice_number: invoice.number })

    invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
    expect(invoice_uploader).to be_success
  end

  it 'builds and returns a Xero Invoice' do
    expect(xero_invoice_model).to receive(:build).with(
      type: Xero::API::Base::CUSTOMER_INVOICE_TYPE,
      contact: xero_contact,
      invoice_number: invoice.number,
      date: invoice.to_at,
      due_date: invoice.due_at,
      reference: customer.name
    )

    invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
    expect(invoice_uploader).to be_success
    expect(invoice_uploader.xero_invoice).to eq(xero_invoice)
  end

  context 'with customer invoice accounting software' do
    let!(:customer_accounting_software) { CustomerFlags::VALID_ACCOUNTING_SOFTWARES.sample }

    before do
      customer.customer_flags.update_column(:accounting_software, customer_accounting_software)
    end

    it 'send the reference with the accounting software in brackets [] upcased' do
      expect(xero_invoice_model).to receive(:build).with(
        type: anything,
        contact: anything,
        invoice_number: anything,
        date: anything,
        due_date: anything,
        reference: "#{customer.name} [#{customer_accounting_software.upcase}]"
      )

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'doesn\'t add the accounting software to reference if the invoice contains a single order paid by credit card' do
      order2.update_column(:invoice_id, nil) # detach order from invoice

      credit_card = create(:credit_card, :random, pay_on_account: false)
      order1.update_column(:credit_card_id, credit_card.id) # attach non-nominated credit card to single (remaining) invoice order

      expect(xero_invoice_model).to receive(:build).with(
        type: anything,
        contact: anything,
        invoice_number: anything,
        date: anything,
        due_date: anything,
        reference: customer.name # no accounting software
      )

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    context 'with Company accounting software', skip: 'tested in customer profile .invoice_accounting_software specs' do
      let!(:company_accounting_software) { (CustomerFlags::VALID_ACCOUNTING_SOFTWARES - [customer_accounting_software]).sample }
      let!(:company) { create(:company, :random, customer_profiles: [customer], accounting_software: company_accounting_software) }

      before do
        customer.reload
      end

      it 'send the reference with the customer\'s accounting software even with company accounting software' do
        expect(xero_invoice_model).to receive(:build).with(
          type: anything,
          contact: anything,
          invoice_number: anything,
          date: anything,
          due_date: anything,
          reference: "#{customer.name} [#{customer_accounting_software.upcase}]"
        )

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'send the reference with the customer\'s company accounting software if customer accounting software is blank' do
        customer.customer_flags.update_column(:accounting_software, nil)
        expect(xero_invoice_model).to receive(:build).with(
          type: anything,
          contact: anything,
          invoice_number: anything,
          date: anything,
          due_date: anything,
          reference: "#{customer.name} [#{company_accounting_software.upcase}]"
        )

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'sends the reference with no accounting software if the customer\'s accounting software is set to `none`' do
        customer.customer_flags.update_column(:accounting_software, 'none')

        expect(xero_invoice_model).to receive(:build).with(
          type: anything,
          contact: anything,
          invoice_number: anything,
          date: anything,
          due_date: anything,
          reference: customer.name # without accounting software
        )

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end
    end
  end # customer invoice accounting software

  context 'with order lines' do
    let!(:category1) { create(:category, :random) }
    let!(:category2) { create(:category, :random) }

    let!(:supplier1) { create(:supplier_profile, :random) }
    let!(:supplier2) { create(:supplier_profile, :random) }

    let!(:order_line111) { create(:order_line, :random, order: order1, supplier_profile: supplier1, category: category1, is_gst_free: false) }
    let!(:order_line112) { create(:order_line, :random, order: order1, supplier_profile: supplier1, category: category2, is_gst_free: true) }

    let!(:order_line211) { create(:order_line, :random, order: order2, supplier_profile: supplier1, category: nil, is_gst_free: true) }
    let!(:order_line221) { create(:order_line, :random, order: order2, supplier_profile: supplier2, category: category2, is_gst_free: false) }

    it 'adds a line item per order line' do
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line111.name} - ##{order1.id}", quantity: order_line111.quantity, unit_amount: order_line111.price_exc_gst, account_code: non_gst_free_code)
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line112.name} - ##{order1.id}", quantity: order_line112.quantity, unit_amount: order_line112.price_exc_gst, account_code: gst_free_code)

      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line211.name} - ##{order2.id}", quantity: order_line211.quantity, unit_amount: order_line211.price_exc_gst, account_code: gst_free_code)
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line221.name} - ##{order2.id}", quantity: order_line221.quantity, unit_amount: order_line221.price_exc_gst, account_code: non_gst_free_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'add extra tracking information per order line (item)' do
      # order_line111
      expect(xero_line_item).to receive(:add_tracking).with(option: category1.name, name: 'Sales Revenue')
      expect(xero_line_item).to receive(:add_tracking).with(option: supplier1.name, name: 'Suppliers')

      # order_line112
      expect(xero_line_item).to receive(:add_tracking).with(option: category2.name, name: 'Sales Revenue')
      expect(xero_line_item).to receive(:add_tracking).with(option: supplier1.name, name: 'Suppliers')

      # order_line211
      expect(xero_line_item).to receive(:add_tracking).with(option: '', name: 'Sales Revenue')
      expect(xero_line_item).to receive(:add_tracking).with(option: supplier1.name, name: 'Suppliers')

      # order_line221
      expect(xero_line_item).to receive(:add_tracking).with(option: category2.name, name: 'Sales Revenue')
      expect(xero_line_item).to receive(:add_tracking).with(option: supplier2.name, name: 'Suppliers')

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end
  end # with order lines

  context 'Delivery Fees' do
    before do
      order1.update_columns(customer_gst: rand(10..20), customer_delivery: rand(10..20))
      order2.update_columns(customer_gst: rand(10..30), customer_delivery: rand(10..30))
    end

    it 'adds a line items for delivery fee of each order' do
      expect(xero_invoice).to receive(:add_line_item).with(description: "Delivery fee - ##{order1.id}", quantity: 1, unit_amount: order1.customer_delivery, account_code: delivery_code)
      expect(xero_invoice).to receive(:add_line_item).with(description: "Delivery fee - ##{order2.id}", quantity: 1, unit_amount: order2.customer_delivery, account_code: delivery_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'does not add a line item of delivery for an order whose delivery fee is blank or 0' do
      order1.update_column(:customer_delivery, [0, nil].sample)
      expect(xero_invoice).to_not receive(:add_line_item).with(description: "Delivery fee - ##{order1.id}", quantity: 1, unit_amount: order1.customer_delivery, account_code: delivery_code)
      expect(xero_invoice).to receive(:add_line_item).with(description: "Delivery fee - ##{order2.id}", quantity: 1, unit_amount: order2.customer_delivery, account_code: delivery_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    context 'invoice orders with GST as Zero (0) (all gst free items)' do
      before do
        order2.update_columns(customer_gst: 0)
      end

      it 'add a line item for cumulative order delivery with GST Free Delivery (account) code' do
        expect(xero_invoice).to receive(:add_line_item).with(description: "Delivery fee - ##{order1.id}", quantity: 1, unit_amount: order1.customer_delivery, account_code: delivery_code)
        expect(xero_invoice).to receive(:add_line_item).with(description: "Delivery fee - ##{order2.id}", quantity: 1, unit_amount: order2.customer_delivery, account_code: gst_free_delivery_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end
    end
  end # Delivery Fee

  context 'Coupon Discounts' do
    let!(:discount1) { rand(10..20) }
    let!(:coupon1) { create(:coupon, :random) }

    let!(:discount2) { rand(10..20) }
    let!(:coupon2) { create(:coupon, :random) }

    before do
      order1.update_columns(coupon_id: coupon1.id, discount: discount1)
      order2.update_columns(coupon_id: coupon2.id, discount: discount2)
    end

    it 'adds a line item for cumulative order discounts' do
      expect(xero_invoice).to receive(:add_line_item).with(description: anything, quantity: 1, unit_amount: (-1 * [discount1, discount2].sum), account_code: discount_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'adds a line item for cumulative available order discounts' do
      order1.update_column(:discount, [0, nil].sample)
      expect(xero_invoice).to receive(:add_line_item).with(description: anything, quantity: 1, unit_amount: (-1 * discount2), account_code: discount_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'adds all attached coupon codes within the line item description' do
      coupon_codes = [coupon1, coupon2].map(&:code).reject(&:blank?)
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{'Coupon'.pluralize(coupon_codes.size)} - #{coupon_codes.join(', ')}", quantity: 1, unit_amount: anything, account_code: discount_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'does not adds a line item for discount if cumulative discounts is 0' do
      order1.update_column(:discount, [0, nil].sample)
      order2.update_column(:discount, [0, nil].sample)
      expect(xero_invoice).to_not receive(:add_line_item).with(description: anything, quantity: anything, unit_amount: anything, account_code: discount_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end
  end # Coupon Discount

  context 'Customer Topup' do
    let!(:topup1) { rand(10..20) }
    let!(:topup2) { rand(10..20) }

    before do
      order1.update_column(:customer_topup, topup1)
      order2.update_column(:customer_topup, topup2)
    end

    it 'adds a line item for topup per order' do
      expect(xero_invoice).to receive(:add_line_item).with(description: "Topup - ##{order1.id}", quantity: 1, unit_amount: topup1, account_code: gst_free_code)
      expect(xero_invoice).to receive(:add_line_item).with(description: "Topup - ##{order2.id}", quantity: 1, unit_amount: topup2, account_code: gst_free_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'does not adds a line item for topup if the order does not have any topup' do
      order1.update_column(:customer_topup, [0, nil].sample)
      expect(xero_invoice).to_not receive(:add_line_item).with(description: "Topup - ##{order1.id}", quantity: 1, unit_amount: topup1, account_code: gst_free_code)
      expect(xero_invoice).to receive(:add_line_item).with(description: "Topup - ##{order2.id}", quantity: 1, unit_amount: topup2, account_code: gst_free_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end
  end # customer topup

  context 'Customer CC Surcharge' do
    let!(:surcharge1) { rand(10..20) }
    let!(:surcharge2) { rand(10..20) }

    let!(:delivery_country_code) { %i[au nz].sample }
    let!(:delivery_suburb) { create(:suburb, :random, country_code: delivery_country_code.to_s.upcase) }

    before do
      order1.update_columns(customer_surcharge: surcharge1, delivery_suburb_id: delivery_suburb.id)
      order2.update_columns(customer_surcharge: surcharge2, delivery_suburb_id: delivery_suburb.id)
    end

    it 'adds a line item for cumulative order surcharges (sent as GST Exc)' do
      gst_percent = yordar_credentials(:yordar, :gst_percent, delivery_country_code)
      gst_exc_surcharge = [surcharge1, surcharge2].map{|surcharge| surcharge / (1 + gst_percent) }.sum.round(2)

      expect(xero_invoice).to receive(:add_line_item).with(description: 'Surcharge', quantity: 1, unit_amount: gst_exc_surcharge, account_code: surcharge_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'adds a line item for cumulative available order surcharge' do
      gst_percent = yordar_credentials(:yordar, :gst_percent, delivery_country_code)
      order1.update_column(:customer_surcharge, [0, nil].sample)

      gst_exc_surcharge = (surcharge2 / (1 + gst_percent)).round(2)
      expect(xero_invoice).to receive(:add_line_item).with(description: 'Surcharge', quantity: 1, unit_amount: gst_exc_surcharge, account_code: surcharge_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'does not adds a line item for surcharge if cumulative order surcharge is 0' do
      order1.update_column(:customer_surcharge, [0, nil].sample)
      order2.update_column(:customer_surcharge, [0, nil].sample)
      expect(xero_invoice).to_not receive(:add_line_item).with(description: 'Surcharge', quantity: anything, unit_amount: anything, account_code: surcharge_code)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end
  end # Customer Surcharge

  context 'customer with GST split invoicing', gst_split_invoicing: true do
    let!(:gst_po) { create(:customer_purchase_order, :random, customer_profile: customer, po_number: 'GST PO') }
    let!(:gst_free_po) { create(:customer_purchase_order, :random, customer_profile: customer, po_number: 'GST-Free PO') }

    let!(:order_line11) { create(:order_line, :random, order: order1, is_gst_free: false) }
    let!(:order_line12) { create(:order_line, :random, order: order1, is_gst_free: true) }

    let!(:order_line21) { create(:order_line, :random, order: order2, is_gst_free: true) }
    let!(:order_line22) { create(:order_line, :random, order: order2, is_gst_free: false) }

    let!(:delivery_fee1) { rand(10..20) }
    let!(:delivery_fee2) { rand(10..30) }

    let!(:discount1) { rand(10..20) }
    let!(:coupon1) { create(:coupon, :random) }

    let!(:discount2) { rand(10..20) }
    let!(:coupon2) { create(:coupon, :random) }

    let!(:topup1) { rand(10..20) }
    let!(:topup2) { rand(10..20) }

    let!(:surcharge1) { rand(10..20) }
    let!(:surcharge2) { rand(10..20) }

    let!(:delivery_country_code) { %i[au nz].sample }
    let!(:delivery_suburb) { create(:suburb, :random, country_code: delivery_country_code.to_s.upcase) }

    before do
      # required for totals calculation of GST split orders
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(nil)

      customer.customer_flags.update_column(:has_gst_split_invoicing, true)
      # attach POs to the order
      order1.update_columns(cpo_id: gst_po.id, gst_free_cpo_id: gst_free_po.id)
      order2.update_column(:cpo_id, gst_po.id)

      # update order totals
      order1.update_columns(customer_delivery: delivery_fee1, coupon_id: coupon1.id, discount: discount1, customer_gst: rand(10..20), customer_topup: topup1, customer_surcharge: surcharge1, delivery_suburb_id: delivery_suburb.id)
      order2.update_columns(customer_delivery: delivery_fee2, coupon_id: coupon2.id, discount: discount2, customer_gst: rand(10..30), customer_topup: topup2, customer_surcharge: surcharge2, delivery_suburb_id: delivery_suburb.id)

      # mock orders totals calculator
      order_totals_calculator = double(Orders::CalculateCustomerTotals)
      allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(order_totals_calculator)
      allow(order_totals_calculator).to receive(:call).and_return(order_totals)
    end

    context 'Invoice for GST PO' do
      let!(:order_totals) do
        totals = Orders::CalculateCustomerTotals::Totals.new(order_line_count: 2)
        totals.delivery = 55
        totals.discount = 25
        totals.surcharge = nil # surcharge for GST only items is nil
        totals.topup = nil # topup for GST only items is nil
        totals.gst = 10 # GST invoice has some GST
        totals
      end

      before do
        invoice.update_column(:cpo_id, gst_po.id)
        order1.update_columns(invoice_id: invoice.id, gst_free_invoice_id: nil)
        order2.update_columns(invoice_id: invoice.id, gst_free_invoice_id: nil)
      end

      it 'adds a line item per GST only order lines of GST split orders (by PO)' do
        expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line11.name} - ##{order1.id}", quantity: order_line11.quantity, unit_amount: order_line11.price_exc_gst, account_code: non_gst_free_code)
        expect(xero_invoice).to_not receive(:add_line_item).with(description: "#{order_line12.name} - ##{order1.id}", quantity: order_line12.quantity, unit_amount: order_line12.price_exc_gst, account_code: gst_free_code) # GST split Order

        expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line21.name} - ##{order2.id}", quantity: order_line21.quantity, unit_amount: order_line21.price_exc_gst, account_code: gst_free_code)
        expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line22.name} - ##{order2.id}", quantity: order_line22.quantity, unit_amount: order_line22.price_exc_gst, account_code: non_gst_free_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'makes a request to calculate the GST totals of the GST split orders (order1) (via Invoice Totals Calculator)' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order1, gst_split: 'GST')
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order2, gst_split: anything)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'adds a line item for (calculated and/or saved) delivery fee per order' do
        expect(xero_invoice).to receive(:add_line_item).with(description: "Delivery fee - ##{order1.id}", quantity: 1, unit_amount: order_totals.delivery, account_code: delivery_code)
        expect(xero_invoice).to receive(:add_line_item).with(description: "Delivery fee - ##{order2.id}", quantity: 1, unit_amount: delivery_fee2, account_code: delivery_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'adds a line item for cumulative (calculated and saved) order discounts along with coupon codes within the line item description ' do
        coupon_codes = [coupon1, coupon2].map(&:code).reject(&:blank?)
        expect(xero_invoice).to receive(:add_line_item).with(description: "#{'Coupon'.pluralize(coupon_codes.size)} - #{coupon_codes.join(', ')}", quantity: 1, unit_amount: (-1 * [order_totals.discount, discount2].sum), account_code: discount_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'adds a line item for topup per order only for non GST-spit orders' do
        expect(xero_invoice).to_not receive(:add_line_item).with(description: "Topup - ##{order1.id}", quantity: 1, unit_amount: topup1, account_code: gst_free_code)
        expect(xero_invoice).to receive(:add_line_item).with(description: "Topup - ##{order2.id}", quantity: 1, unit_amount: topup2, account_code: gst_free_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'adds a line item for cumulative (calculated and saved) order surcharges ' do
        gst_percent = yordar_credentials(:yordar, :gst_percent, delivery_country_code)
        gst_exc_surcharge = [order_totals.surcharge, surcharge2].compact.map{|surcharge| surcharge / (1 + gst_percent) }.sum.round(2)

        expect(xero_invoice).to receive(:add_line_item).with(description: 'Surcharge', quantity: 1, unit_amount: gst_exc_surcharge, account_code: surcharge_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end
    end

    context 'Invoice for GST-Free PO' do
      let!(:order_totals) do
        totals = Orders::CalculateCustomerTotals::Totals.new(order_line_count: 2)
        totals.delivery = [nil, 0].sample # delivery fee for GST-Free items is nil
        totals.discount = 33
        totals.surcharge = 27
        totals.topup = topup1 # same as saved at order
        totals.gst = 0 # GST-Free invoice does not have any GST
        totals
      end

      before do
        invoice.update_column(:cpo_id, gst_free_po.id)
        order1.update_columns(invoice_id: nil, gst_free_invoice_id: invoice.id)
        order2.update_columns(invoice_id: invoice.id, gst_free_invoice_id: nil, customer_gst: 0)
      end

      it 'adds a line item per GST-Free only order linesof GST split orders (by PO)' do
        expect(xero_invoice).to_not receive(:add_line_item).with(description: "#{order_line11.name} - ##{order1.id}", quantity: order_line11.quantity, unit_amount: order_line11.price_exc_gst, account_code: non_gst_free_code)
        expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line12.name} - ##{order1.id}", quantity: order_line12.quantity, unit_amount: order_line12.price_exc_gst, account_code: gst_free_code)

        expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line21.name} - ##{order2.id}", quantity: order_line21.quantity, unit_amount: order_line21.price_exc_gst, account_code: gst_free_code)
        expect(xero_invoice).to receive(:add_line_item).with(description: "#{order_line22.name} - ##{order2.id}", quantity: order_line22.quantity, unit_amount: order_line22.price_exc_gst, account_code: non_gst_free_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'makes a request to calculate the GST-free totals of the GST split orders (order1) (via Invoice Totals Calculator)' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order1, gst_split: 'GST-FREE')
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order2, gst_split: anything)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'adds a line item for (calculated and/or saved) delivery fee per order (if present and non-zero)' do
        expect(xero_invoice).to_not receive(:add_line_item).with(description: "Delivery fee - ##{order1.id}", quantity: 1, unit_amount: order_totals.delivery, account_code: gst_free_delivery_code) # blank calculated delivery fee
        expect(xero_invoice).to receive(:add_line_item).with(description: "Delivery fee - ##{order2.id}", quantity: 1, unit_amount: delivery_fee2, account_code: gst_free_delivery_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'adds a line item for cumulative (calculated and saved) order discounts along with coupon codes within the line item description ' do
        coupon_codes = [coupon1, coupon2].map(&:code).reject(&:blank?)
        expect(xero_invoice).to receive(:add_line_item).with(description: "#{'Coupon'.pluralize(coupon_codes.size)} - #{coupon_codes.join(', ')}", quantity: 1, unit_amount: (-1 * [order_totals.discount, discount2].sum), account_code: discount_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'adds a line item for topup per order only for non GST-spit orders' do
        expect(xero_invoice).to receive(:add_line_item).with(description: "Topup - ##{order1.id}", quantity: 1, unit_amount: topup1, account_code: gst_free_code)
        expect(xero_invoice).to receive(:add_line_item).with(description: "Topup - ##{order2.id}", quantity: 1, unit_amount: topup2, account_code: gst_free_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'adds a line item for cumulative (calculated and saved) order surcharges ' do
        gst_percent = yordar_credentials(:yordar, :gst_percent, delivery_country_code)
        gst_exc_surcharge = [order_totals.surcharge, surcharge2].compact.map{|surcharge| surcharge / (1 + gst_percent) }.sum.round(2)

        expect(xero_invoice).to receive(:add_line_item).with(description: 'Surcharge', quantity: 1, unit_amount: gst_exc_surcharge, account_code: surcharge_code)

        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
        expect(invoice_uploader).to be_success
      end
    end
  end

  context 'Invoice Document' do
    let!(:document1) { create(:document, :random, documentable: invoice, kind: 'tax_invoice', version: 1) }
    let!(:document2) { create(:document, :random, documentable: invoice, kind: 'tax_invoice', version: 2) }

    it 'sets the invoice ur as per url of latest tax_invoice document (based on version)' do
      expect(xero_invoice).to receive('url=').with(document2.url)

      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
      expect(invoice_uploader).to be_success
    end
  end

  it 'saves the xero invoice' do
    expect(xero_invoice).to receive(:save)

    invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
    expect(invoice_uploader).to be_success
  end

  context 'with an existing Xero Invoice' do
    let(:existing_invoice) { double('XeroInvoice', invoice_id: SecureRandom.uuid) }

    before do
      allow(xero_invoice_model).to receive(:all).and_return([existing_invoice])
    end

    it 'errors and returns the existing Xero Invoice' do
      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call

      expect(invoice_uploader).to_not be_success
      expect(invoice_uploader.xero_invoice).to eq(existing_invoice)
      expect(invoice_uploader.errors).to include("Invoice ##{invoice.number} - already exists in Xero")
    end

    it 'sets the invoice as pushed to Xero' do
      expect(invoice.pushed_to_xero).to be_falsey
      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call

      expect(invoice_uploader).to_not be_success
      expect(invoice.pushed_to_xero).to be_truthy
    end
  end # existing Invoice

  context 'errors' do
    it 'errors if the invoice is missing' do
      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: nil).call

      expect(invoice_uploader).to_not be_success
      expect(invoice_uploader.errors).to include('Cannot upload a missing invoice')
    end

    it 'errors if invoice is already pushed to Xero' do
      invoice.update_column(:pushed_to_xero, true)
      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call

      expect(invoice_uploader).to_not be_success
      expect(invoice_uploader.errors).to include('Invoice already uploaded to Xero')
    end

    it 'errors if the invoice is not attached to any orders' do
      non_order_invoice = create(:invoice, :random)
      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: non_order_invoice).call

      expect(invoice_uploader).to_not be_success
      expect(invoice_uploader.errors).to include('Invoice does not contain any orders')
    end

    context 'customer with errors' do
      let!(:customer_errors) { OpenStruct.new(full_messages: %w[customer-error-1 customer-error-2]) }

      before do
        # mock customer instance errors
        # allow(customer).to receive(:errors).and_return(customer_errors)
        allow_any_instance_of(CustomerProfile).to receive(:errors).and_return(customer_errors) # had to go to Model level as model_instance.errors is a restricted method
      end

      it 'errors if the invoice customer has errors' do
        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call

        expect(invoice_uploader).to_not be_success
        expect(invoice_uploader.errors).to include(*customer_errors.full_messages)
      end
    end # customer errors

    context 'with contact upload errors' do
      let!(:contact_upload_errors) { %w[contact-upload-error1 contact-upload-error2]}

      before do
        invalid_contact_response = OpenStruct.new(success?: false, errors: contact_upload_errors)
        allow(contact_uploader).to receive(:call).and_return(invalid_contact_response)
      end

      it 'return the contact upload errors' do
        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call

        expect(invoice_uploader).to_not be_success
        expect(invoice_uploader.errors).to include("Failed to save/update customer #{customer.id}")
        expect(invoice_uploader.errors).to include(*contact_upload_errors)
      end
    end # conact upload errors

    context 'with Xero Invoice build/save errors' do
      before do
        invoice_does_not_exist = [true, false].sample
        has_build_errors = [true, false].sample
        has_line_item_errors = [true, false].sample
        case
        when invoice_does_not_exist
          allow(xero_invoice_model).to receive(:all).and_raise(RuntimeError.new) # error on Invoice existance check
        when has_build_errors
          allow(xero_invoice_model).to receive(:build).and_raise(RuntimeError.new) # errors on build
        when has_line_item_errors
          create(:order_line, :random, order: order1, is_gst_free: false) # to at least run 1 add_line_item
          allow(xero_invoice).to receive(:add_line_item).and_raise(RuntimeError.new) # errors on adding line items
        else
          allow(xero_invoice).to receive(:save).and_raise(RuntimeError.new) # errors on save
        end
      end

      it 'return with an error' do
        invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call

        expect(invoice_uploader).to_not be_success
        expect(invoice_uploader.errors).to include("Could not upload Invoice #{invoice.number} (##{invoice.id}) to Xero")
      end
    end
  end # errors

end
