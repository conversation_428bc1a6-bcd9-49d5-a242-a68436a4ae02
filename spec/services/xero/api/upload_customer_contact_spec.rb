require 'rails_helper'

RSpec.describe Xero::API::UploadCustomerContact, type: :service, xero: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }

  let!(:xero_client) { double(Xeroizer::OAuth2Application) }
  let!(:xero_contact_model) { double('XeroContact') }
  let!(:xero_contact) { double('XeroContact', email_address: nil) }

  before do
    allow_any_instance_of(Xero::API::UploadCustomerContact).to receive(:xero_client).and_return(xero_client)

    allow(xero_client).to receive(:Contact).and_return(xero_contact_model)

    # mock no exsting contact
    allow(xero_contact_model).to receive(:all).and_return([])

    # mock new contact build
    allow(xero_contact_model).to receive(:build).and_return(xero_contact)

    # mock xero_contact methods
    allow(xero_contact).to receive('email_address=').and_return(true)
    allow(xero_contact).to receive(:add_address).and_return(true)
    allow(xero_contact).to receive(:add_phone).and_return(true)
    allow(xero_contact).to receive(:save).and_return(true)
  end

  it 'checks for an existing contact via contact number and name' do
    expect(xero_contact_model).to receive(:all).with(where: { contact_number: customer.user.id })
    formatted_name_condition = format('Name.ToLower()=="%<name>s"', name: customer.customer_or_company_name.downcase.strip)
    expect(xero_contact_model).to receive(:all).with(where: formatted_name_condition)

    contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
    expect(contact_uploader).to be_success
  end

  it 'builds a new Xero Contact' do
    expect(xero_contact_model).to receive(:build).with(
      name: customer.customer_or_company_name,
      contact_number: customer.user.id,
      first_name: customer.user.firstname,
      last_name: customer.user.lastname
    )

    contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call

    expect(contact_uploader).to be_success
    expect(contact_uploader.contact).to be(xero_contact)
  end

  it 'saves the Xero Contact' do
    expect(xero_contact).to receive(:save)

    contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
    expect(contact_uploader).to be_success
  end

  context 'with customer billing details' do
    let!(:billing_details) { create(:billing_details, :random, customer_profile: customer) }
    let!(:suburb) { billing_details.suburb }

    it 'adds the billing email address to the contact' do
      expect(xero_contact).to receive('email_address=').with(billing_details.email)

      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
      expect(contact_uploader).to be_success
    end

    it 'adds the first billing email address to the contact' do
      expect(xero_contact).to receive('email_address=').with(billing_details.email)

      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
      expect(contact_uploader).to be_success
    end

    it 'adds the billing adddress' do
      expect(xero_contact).to receive(:add_address).with(type: Xero::API::UploadCustomerContact::ADDRESS_TYPE, line1: billing_details.address, line2: suburb.name, postal_code: suburb.postcode, region: suburb.state)

      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
      expect(contact_uploader).to be_success
    end

    it 'adds the billing phone' do
      expect(xero_contact).to receive(:add_phone).with(type: Xero::API::UploadCustomerContact::PHONE_TYPE, number: billing_details.phone)

      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
      expect(contact_uploader).to be_success
    end
  end

  it 'does not updates billing details if they customer billing details are absent' do
    expect(xero_contact).to_not receive('email_address=')
    expect(xero_contact).to_not receive(:add_address)
    expect(xero_contact).to_not receive(:add_phone)

    contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
    expect(contact_uploader).to be_success
  end

  context 'it finds an existing contact' do
    let!(:existing_contact) { double('XeroContact', email_address: Faker::Internet.email) }

    before do
      allow(xero_contact_model).to receive(:all).and_return([existing_contact])

      # mock xero_contact methods
      allow(existing_contact).to receive(:add_address).and_return(true)
      allow(existing_contact).to receive(:add_phone).and_return(true)
      allow(existing_contact).to receive(:save).and_return(true)
    end

    it 'returns the existing contact' do
      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call

      expect(contact_uploader).to be_success
      expect(contact_uploader.contact).to eq(existing_contact)
    end

    context 'with customer billing details' do
      let!(:billing_details) { create(:billing_details, :random, customer_profile: customer) }
      let!(:suburb) { billing_details.suburb }

      it 'does not update existing contact email address if it already exists' do
        expect(existing_contact).to_not receive('email_address=')

        contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
        expect(contact_uploader).to be_success
      end

      it 'updates existing contact\'s billing details' do
        expect(existing_contact).to receive(:add_address)
        expect(existing_contact).to receive(:add_phone)

        contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
        expect(contact_uploader).to be_success
      end
    end

    it 'saves the Xero Contact' do
      expect(existing_contact).to receive(:save)

      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
      expect(contact_uploader).to be_success
    end

    it 'resets the xero_push_fault flag of the supplier' do
      customer.user.update_column(:xero_push_fault, true)
      expect(customer.user.xero_push_fault).to be_truthy

      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
      expect(contact_uploader).to be_success
      expect(customer.user.reload.xero_push_fault).to be_falsey
    end

    it 'does not check for an existing contact via contact name if already found by ID' do
      expect(xero_contact_model).to receive(:all).with(where: { contact_number: customer.user.id }) # returns existing contact as per mock
      expect(xero_contact_model).to_not receive(:all).with(where: format('Name.ToLower()=="%<name>s"', name: customer.customer_or_company_name.downcase.strip))

      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call
      expect(contact_uploader).to be_success
    end
  end

  context 'errors' do
    it 'errors if the customer is missing' do
      contact_uploader = Xero::API::UploadCustomerContact.new(customer: nil).call

      expect(contact_uploader).to_not be_success
      expect(contact_uploader.errors).to include('Cannot upload a missing contact')
    end

    it 'errors if the customer does not have an associated user records' do
      customer.user.destroy
      contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer.reload).call

      expect(contact_uploader).to_not be_success
      expect(contact_uploader.errors).to include('Cannot upload a missing contact')
    end

    context 'with contact API save error' do
      before do
        allow(xero_contact).to receive(:save).and_return(false)
      end

      it 'returns unsuccessfully with an error' do
        contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call

        expect(contact_uploader).to_not be_success
        expect(contact_uploader.errors).to include("Could not save contact for #{customer.id}")
      end

      it 'saves the customer\'s user as xero push failed' do
        expect(customer.user.xero_push_fault).to be_falsey
        contact_uploader = Xero::API::UploadCustomerContact.new(customer: customer).call

        expect(contact_uploader).to_not be_success
        expect(customer.user.xero_push_fault).to be_truthy
      end
    end # API errors
  end # errors
end
