require 'rails_helper'

RSpec.describe Xero::API::UploadSupplierInvoice, type: :service, xero: true, rgi: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_user) }

  let!(:supplier_invoice) { create(:supplier_invoice, :random, supplier_profile: supplier) }

  let!(:order1) { create(:order, :delivered) }
  let!(:order2) { create(:order, :delivered) }

  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier, is_gst_free: false) }
  let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier, is_gst_free: true) }

  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier, is_gst_free: true) }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier, is_gst_free: true) }

  let!(:order_supplier1) { create(:order_supplier, :random, supplier_profile: supplier, order: order1, supplier_invoice: supplier_invoice) }
  let!(:order_supplier2) { create(:order_supplier, :random, supplier_profile: supplier, order: order2, supplier_invoice: supplier_invoice) }

  let!(:xero_contact) { 'mock-xero-contact' }
  let!(:xero_client) { double(Xeroizer::OAuth2Application) }
  let!(:contact_uploader) { double(Xero::API::UploadSupplierContact) }
  let!(:totals_calculator) { double(Orders::CalculateSupplierTotals) }
  let!(:supplier_totals) { OpenStruct.new(subtotal: 100, gst: 10, delivery: 20, topup: nil) }
  let!(:xero_invoice_model) { double('XeroInvoice') }
  let!(:xero_invoice) { double('XeroInvoice', invoice_id: SecureRandom.uuid) }
  let!(:xero_line_item) { double('XeroLineItem') }

  let!(:orders) { [order1, order2] }
  let!(:delivery_date) { orders.sample.delivery_at }

  let!(:push_frequency) { 'weekly' } # default
  let!(:document_url) { 'document-url' }
  let!(:reference) { SecureRandom.hex(7) }

  let!(:gst_free_code) { 'gst-free-code' }
  let!(:non_gst_free_code) { 'non-gst-free-code' }

  before do
    # stub xero constants (saved per environment)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:xero, :supplier_gst_free).and_return(gst_free_code)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:xero, :supplier_non_gst_free).and_return(non_gst_free_code)

    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

    # mock contact uploader request
    allow(Xero::API::UploadSupplierContact).to receive(:new).and_return(contact_uploader)
    contact_response = OpenStruct.new(success?: true, contact: xero_contact)
    allow(contact_uploader).to receive(:call).and_return(contact_response)

    # mock totals calculation
    allow(Orders::CalculateSupplierTotals).to receive(:new).and_return(totals_calculator)
    allow(totals_calculator).to receive(:call).and_return(supplier_totals)

    # mock Xero API client
    allow_any_instance_of(Xero::API::UploadSupplierInvoice).to receive(:xero_client).and_return(xero_client)

    # mock Xero Invoice Model
    allow(xero_client).to receive(:Invoice).and_return(xero_invoice_model)
    # mock new contact build
    allow(xero_invoice_model).to receive(:build).and_return(xero_invoice)

    # mock xero_invoice line item methods
    allow(xero_invoice).to receive(:add_line_item).and_return(xero_line_item)
    allow(xero_line_item).to receive('account_code=').and_return(true) # mock line item method
    allow(xero_line_item).to receive('tax_type=').and_return(true) # mock line item method

    # mock xero_invoice methods
    allow(xero_invoice).to receive(:save).and_return(true)
  end

  it 'makes a request to upload the invoice contact' do
    expect(Xero::API::UploadSupplierContact).to receive(:new).with(supplier: supplier)

    invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
    expect(invoice_uploader).to be_success
    expect(invoice_uploader.contact).to eq(xero_contact)
  end

  it 'builds and returns a Xero Invoice' do
    expect(xero_invoice_model).to receive(:build).with(
      type: Xero::API::Base::SUPPLIER_INVOICE_TYPE,
      contact: xero_contact,
      date: supplier_invoice.created_at,
      due_date: supplier_invoice.due_at,
      invoice_number: supplier_invoice.number,
      url: anything
    )

    invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
    expect(invoice_uploader).to be_success
    expect(invoice_uploader.xero_invoice).to eq(xero_invoice)
  end

  context 'RGI document' do
    let!(:rgi_document1) { create(:document, :random, kind: 'recipient_generated_invoice', documentable: supplier_invoice, version: 1) }
    let!(:rgi_document2) { create(:document, :random, kind: 'recipient_generated_invoice', documentable: supplier_invoice, version: 2) }

    it 'builds the Xero Invoice with the latest (by version) RGI document' do
      expect(xero_invoice_model).to receive(:build).with(
        type: Xero::API::Base::SUPPLIER_INVOICE_TYPE,
        contact: anything,
        date: anything,
        due_date: anything,
        invoice_number: supplier_invoice.number,
        url: rgi_document2.url
      )

      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
      expect(invoice_uploader).to be_success
      expect(invoice_uploader.xero_invoice).to eq(xero_invoice)
    end

    it 'builds the Xero Invoice with the no RGI document if it is missing' do
      [rgi_document1, rgi_document2].each(&:destroy)

      expect(xero_invoice_model).to receive(:build).with(
        type: Xero::API::Base::SUPPLIER_INVOICE_TYPE,
        contact: anything,
        date: anything,
        due_date: anything,
        invoice_number: supplier_invoice.number,
        url: nil
      )

      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
      expect(invoice_uploader).to be_success
      expect(invoice_uploader.xero_invoice).to eq(xero_invoice)
    end
  end

  context 'Per Order data' do
    it 'adds a line item per order with totals' do
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order1.name} - ##{order1.id}", quantity: 1,  tax_amount: supplier_totals.gst.round(2),  unit_amount: supplier_totals.subtotal.round(2), account_code: anything)
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order2.name} - ##{order2.id}", quantity: 1,  tax_amount: supplier_totals.gst.round(2),  unit_amount: supplier_totals.subtotal.round(2), account_code: anything)

      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'adds a line item per order with correct account code based on GST items' do
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order1.name} - ##{order1.id}", quantity: 1,  tax_amount: anything,  unit_amount: anything, account_code: non_gst_free_code)
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order2.name} - ##{order2.id}", quantity: 1,  tax_amount: anything,  unit_amount: anything, account_code: gst_free_code)

      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'adds a line item per with gst free account code only if all items in order are gst free' do
      [order_line11, order_line12].each{|order_line| order_line.update_column(:is_gst_free, false) }
      [order_line21, order_line22].each{|order_line| order_line.update_column(:is_gst_free, true) }

      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order1.name} - ##{order1.id}", quantity: 1,  tax_amount: anything,  unit_amount: anything, account_code: non_gst_free_code)
      expect(xero_invoice).to receive(:add_line_item).with(description: "#{order2.name} - ##{order2.id}", quantity: 1,  tax_amount: anything,  unit_amount: anything, account_code: gst_free_code)

      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
      expect(invoice_uploader).to be_success
    end

    context 'as an override supplier' do
      let!(:override_supplier_id) { Xero::API::Base::SUPPLIER_ACCOUNT_CODES.keys.sample.to_i }

      before do
        # mock supplier as an override order
        supplier.user.update_column(:id, override_supplier_id)
      end

      it 'add extra acccont and tax type information per' do
        override_account_code = Xero::API::Base::SUPPLIER_ACCOUNT_CODES[override_supplier_id.to_s]

        # order1
        expect(xero_line_item).to receive('account_code=').with(override_account_code)
        expect(xero_line_item).to receive('tax_type=').with('INPUT')

        # order2
        expect(xero_line_item).to receive('account_code=').with(override_account_code)
        expect(xero_line_item).to receive('tax_type=').with('EXEMPTEXPENSES')

        invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
        expect(invoice_uploader).to be_success
      end
    end

    context 'with Order Topups' do
      it 'does not add an item line for per order topup by default' do
        expect(xero_invoice).to_not receive(:add_line_item).with(description: "Topup - ##{order1.id}", quantity: 1, unit_amount: anything)
        expect(xero_invoice).to_not receive(:add_line_item).with(description: "Topup - ##{order2.id}", quantity: 1, unit_amount: anything)

        invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'does not adds a line item for per order topup if topup is 0' do
        supplier_totals_with_topup = OpenStruct.new(subtotal: 100, gst: 10, delivery: 20, topup: 0)
        allow(totals_calculator).to receive(:call).and_return(supplier_totals_with_topup)

        expect(xero_invoice).to_not receive(:add_line_item).with(description: "Topup - ##{order1.id}", quantity: 1, unit_amount: anything)
        expect(xero_invoice).to_not receive(:add_line_item).with(description: "Topup - ##{order2.id}", quantity: 1, unit_amount: anything)

        invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
        expect(invoice_uploader).to be_success
      end

      it 'add an item line for per order topup' do
        totals_with_topup = OpenStruct.new(subtotal: 100, gst: 10, delivery: 20, topup: 10)
        allow(totals_calculator).to receive(:call).and_return(totals_with_topup)

        expect(xero_invoice).to receive(:add_line_item).with(description: "Topup - ##{order1.id}", quantity: 1, unit_amount: totals_with_topup.topup)
        expect(xero_invoice).to receive(:add_line_item).with(description: "Topup - ##{order2.id}", quantity: 1, unit_amount: totals_with_topup.topup)

        invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
        expect(invoice_uploader).to be_success
      end
    end # Order Topups

  end # order data

  context 'Delivery Fees' do
    it 'adds a line item for cumulative order delivery' do
      cumulative_delivery_fee = orders.map{|_| supplier_totals.delivery }.sum
      expect(xero_invoice).to receive(:add_line_item).with(description: 'Delivery fee', quantity: 1, unit_amount: cumulative_delivery_fee.round(2))

      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
      expect(invoice_uploader).to be_success
    end

    it 'adds a line item for delivery fee even if cumulative order delivery is 0' do
      # let totals calculator return delivery (fee) of 0
      no_delivery_supplier_totals = OpenStruct.new(subtotal: 100, gst: 10, delivery: [nil, 0].sample, topup: nil)
      allow(totals_calculator).to receive(:call).and_return(no_delivery_supplier_totals)

      expect(xero_invoice).to receive(:add_line_item).with(description: 'Delivery fee', quantity: 1, unit_amount: 0)

      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
      expect(invoice_uploader).to be_success
    end
  end # Delivery Fee

  it 'saves the xero invoice' do
    expect(xero_invoice).to receive(:save)

    invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
    expect(invoice_uploader).to be_success
  end

  it 'marks the order lines as sent_as_rgi_to_xero' do
    invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
    expect(invoice_uploader).to be_success

    [order_line11, order_line12, order_line21, order_line22].each do |order_line|
      expect(order_line.reload.sent_as_rgi_to_xero).to be_truthy
    end
  end

  it 'marks the supplier invoice as pushed to the xero invoice' do
    invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call
    expect(invoice_uploader).to be_success

    expect(supplier_invoice.reload.pushed_to_xero).to be_truthy
  end

  context 'errors' do
    it 'errors if the invoice is missing' do
      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: nil).call

      expect(invoice_uploader).to_not be_success
      expect(invoice_uploader.errors).to include('Cannot push a missing Supplier Invoice')
    end

    it 'errors if the orders are missing' do
      non_order_supplier_invoice = create(:supplier_invoice, :random, supplier_profile: supplier)
      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: non_order_supplier_invoice).call

      expect(invoice_uploader).to_not be_success
      expect(invoice_uploader.errors).to include('Cannot push missing purchases')
    end

    it 'errors if the invoice does not have a number' do
      supplier_invoice.update_column(:number, nil)
      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call

      expect(invoice_uploader).to_not be_success
      expect(invoice_uploader.errors).to include('Cannot push without an Invoice number')
    end

    context 'with contact upload errors' do
      let!(:contact_upload_errors) { %w[contact-upload-error1 contact-upload-error2]}

      before do
        invalid_contact_response = OpenStruct.new(success?: false, errors: contact_upload_errors)
        allow(contact_uploader).to receive(:call).and_return(invalid_contact_response)
      end

      it 'return the contact upload errors' do
        invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call

        expect(invoice_uploader).to_not be_success
        expect(invoice_uploader.errors).to include("Failed to save/update supplier #{supplier.id}")
        expect(invoice_uploader.errors).to include(*contact_upload_errors)
      end
    end # conact upload errors

    context 'with Xero Invoice build/save errors' do
      before do
        has_errors_on_build = [true, false].sample
        has_line_item_errors = [true, false].sample
        case
        when has_errors_on_build
          allow(xero_invoice_model).to receive(:build).and_raise(RuntimeError.new) # errors on build
        when has_line_item_errors
          allow(xero_invoice).to receive(:add_line_item).and_raise(RuntimeError.new) # errors on adding line items
        else
          allow(xero_invoice).to receive(:save).and_raise(RuntimeError.new) # errors on save
        end
      end

      it 'return with an error' do
        invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: supplier_invoice).call

        expect(invoice_uploader).to_not be_success
        expect(invoice_uploader.errors).to include("Could not upload purchase orders for supplier #{supplier.name} (##{supplier.id}) to Xero")
      end
    end
  end # errors

end
