require 'rails_helper'

RSpec.describe Xero::API::UploadSupplierContact, type: :service, xero: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_user) }

  let!(:xero_client) { double(Xeroizer::OAuth2Application) }
  let!(:xero_contact_model) { double('XeroContact') }
  let!(:xero_contact) { double('XeroContact', contact_id: SecureRandom.uuid) }

  before do
    allow_any_instance_of(Xero::API::UploadSupplierContact).to receive(:xero_client).and_return(xero_client)

    allow(xero_client).to receive(:Contact).and_return(xero_contact_model)

    # mock no exsting contact
    allow(xero_contact_model).to receive(:all).and_return([])

    # mock new contact build
    allow(xero_contact_model).to receive(:build).and_return(xero_contact)

    # mock xero_contact methods
    allow(xero_contact).to receive('bank_account_details=').and_return(true)
    allow(xero_contact).to receive('tax_number=').and_return(true)
    allow(xero_contact).to receive(:add_address).and_return(true)
    allow(xero_contact).to receive(:add_phone).and_return(true)
    allow(xero_contact).to receive(:save).and_return(true)
  end

  it 'checks for an existing contact via contact number and name' do
    expect(xero_contact_model).to receive(:all).with(where: { contact_number: supplier.user.id })
    formatted_name_condition = format('Name.ToLower()=="%<name>s"', name: supplier.company_name.downcase.strip)
    expect(xero_contact_model).to receive(:all).with(where: formatted_name_condition)

    contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
    expect(contact_uploader).to be_success
  end

  it 'builds a new Xero Contact' do
    expect(xero_contact_model).to receive(:build).with(name: supplier.company_name, contact_number: supplier.user.id)

    contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call

    expect(contact_uploader).to be_success
    expect(contact_uploader.contact).to be(xero_contact)
  end

  it 'saves the Xero Contact' do
    expect(xero_contact).to receive(:save)

    contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
    expect(contact_uploader).to be_success
  end

  context 'supplier bank account details' do
    before do
      supplier.update_columns(bsb_number: Faker::Bank.swift_bic, bank_account_number: Faker::Bank.account_number)
    end

    it 'adds the bank account details' do
      expect(xero_contact).to receive('bank_account_details=').with("#{supplier.bsb_number}#{supplier.bank_account_number}")

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end

    it 'does not add back account details if any of the account details are missing' do
      %i[bsb_number bank_account_number].sample([1, 2].sample).each do |field| # either one of both fields are empty
        supplier.update_column(field, ['', nil].sample)
      end

      expect(xero_contact).to_not receive('bank_account_details=')

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end
  end

  context 'supplier tax details details' do
    before do
      supplier.update_column(:abn_acn, Faker::Company.ein)
    end

    it 'adds the tax details' do
      expect(xero_contact).to receive('tax_number=').with(supplier.abn_acn)

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end

    it 'does not add tax details if missing' do
      supplier.update_column(:abn_acn, ['', nil].sample)

      expect(xero_contact).to_not receive('tax_number=')

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end
  end

  context 'with company address' do
    let!(:suburb) { create(:suburb, :random) }

    before do
      supplier.update_column(:company_address_suburb_id, suburb.id)
    end

    it 'adds the adddress' do
      expect(xero_contact).to receive(:add_address).with(type: Xero::API::UploadSupplierContact::ADDRESS_TYPE, line1: supplier.company_address, line2: suburb.name, postal_code: suburb.postcode, region: suburb.state)

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end
  end

  context 'Phone details' do
    it 'adds the phone' do
      expect(xero_contact).to receive(:add_phone).with(type: Xero::API::UploadSupplierContact::PHONE_TYPE, number: supplier.phone)

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end

    it 'adds the mobile if present' do
      expect(xero_contact).to receive(:add_phone).with(type: Xero::API::UploadSupplierContact::MOBILE_TYPE, number: supplier.mobile)

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end
  end

  context 'it finds a existing contact' do
    let!(:existing_contact) { double('XeroContact', contact_id: SecureRandom.uuid) }
    before do
      allow(xero_contact_model).to receive(:all).and_return([existing_contact])
    end

    it 'returns the existing contact' do
      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call

      expect(contact_uploader).to be_success
      expect(contact_uploader.contact).to eq(existing_contact)
    end

    it 'does not build, add_details or saves the Xero Contact', skip: '.to_not receive` is not supported since it doesn\'t really make sense' do
      expect(xero_contact_model).to_not receive(:build)
      allow(existing_contact).to_not receive('bank_account_details=')
      allow(existing_contact).to_not receive('tax_number=')
      allow(xero_contact).to_not receive(:add_address)
      allow(xero_contact).to_not receive(:add_phone)
      allow(xero_contact).to_not receive(:save)

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end

    it 'does not check for an existing contact via contact name if already found by ID' do
      expect(xero_contact_model).to receive(:all).with(where: { contact_number: supplier.user.id }) # returns existing contact as per mock
      expect(xero_contact_model).to_not receive(:all).with(where: format('Name.ToLower()=="%<name>s"', name: supplier.company_name.downcase.strip))

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
    end

    it 'resets the xero_push_fault flag for the supplier\'s user' do
      supplier.user.update_column(:xero_push_fault, true)
      expect(supplier.user.xero_push_fault).to be_truthy

      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call
      expect(contact_uploader).to be_success
      expect(supplier.user.reload.xero_push_fault).to be_falsey
    end
  end

  context 'errors' do
    it 'errors if the supplier is missing' do
      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: nil).call

      expect(contact_uploader).to_not be_success
      expect(contact_uploader.errors).to include('Cannot upload a missing contact')
    end

    it 'errors if the supplier does not have an associated user records' do
      supplier.user.destroy
      contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier.reload).call

      expect(contact_uploader).to_not be_success
      expect(contact_uploader.errors).to include('Cannot upload a missing contact')
    end

    context 'with contact API save error' do
      before do
        allow(xero_contact).to receive(:save).and_return(false)
      end

      it 'returns unsuccessfully with an error' do
        contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call

        expect(contact_uploader).to_not be_success
        expect(contact_uploader.errors).to include("Could not save contact for #{supplier.id}")
      end

      it 'saves the supplier\'s user as xero push failed' do
        expect(supplier.user.xero_push_fault).to be_falsey
        contact_uploader = Xero::API::UploadSupplierContact.new(supplier: supplier).call

        expect(contact_uploader).to_not be_success
        expect(supplier.user.xero_push_fault).to be_truthy
      end
    end # API errors
  end # errors
end
