require 'rails_helper'

RSpec.describe Xero::HandleWebhookEvent, type: :service, xero: true, invoices: true do

  let!(:webhook_event) do
    {
      'eventCategory' => 'INVOICE',
      'eventType' => 'UPDATE',
      'resourceId' => SecureRandom.uuid,
      # ... extra details
      # 'resourceUrl' => "https://https://api.xeroxxx.com/api.xro/2.0/Invoices/#{xero_invoice.invoice_id}",
      # 'tenantId' => SecureRandom.uuid,
      # 'tenantType' => 'ORGANISATION',
      # 'eventDateUtc' => Time.zone.now
    }
  end

  before do
    notifier = double(Xero::NotifyWebhookEvent)
    allow(Xero::NotifyWebhookEvent).to receive(:new).and_return(notifier)
    allow(notifier).to receive(:call).and_return(true)
  end

  it 'creates a new Xero Invoice record' do
    event_handler = Xero::HandleWebhookEvent.new(event: webhook_event).call

    expect(event_handler).to be_success
    created_xero_invoice = event_handler.xero_invoice
    expect(created_xero_invoice).to be_present
    expect(created_xero_invoice).to be_persisted
    expect(created_xero_invoice).to be_a(Xero::Invoice)
    expect(created_xero_invoice.invoice_id).to eq(webhook_event['resourceId'])
  end

  it 'notifies about the event' do
    expect(Xero::NotifyWebhookEvent).to receive(:new).with(event: webhook_event, xero_invoice: anything) # xero invoice is created

    event_handler = Xero::HandleWebhookEvent.new(event: webhook_event).call
    expect(event_handler).to be_success
  end

  context 'with an existing Xero Invoice' do
    let!(:xero_invoice) { create(:xero_invoice, invoice_id: webhook_event['resourceId']) }

    it 'does not create a duplicate Xero Invoice record' do
      event_handler = Xero::HandleWebhookEvent.new(event: webhook_event).call

      expect(event_handler).to be_success
      handled_xero_invoice = event_handler.xero_invoice
      expect(handled_xero_invoice.id).to eq(xero_invoice.id)
    end

    it 'does not notify about the event' do
      expect(Xero::NotifyWebhookEvent).to_not receive(:new)

      event_handler = Xero::HandleWebhookEvent.new(event: webhook_event).call
      expect(event_handler).to be_success
    end
  end

  context 'errors' do
    it 'cannot handle a missing event' do
      event_handler = Xero::HandleWebhookEvent.new(event: nil).call

      expect(event_handler).to_not be_success
      expect(event_handler.errors).to include('Cannot handle a missing webhook event')
    end

    it 'cannot handle a non invoice update event' do
      invalid_webhook_event = case
      when [true, false].sample
        webhook_event.merge({ 'eventCategory' => 'NOT-INVOICE' })
      else
        webhook_event.merge({ 'eventType' => 'NOT-UPDATE' })
      end
      event_handler = Xero::HandleWebhookEvent.new(event: invalid_webhook_event).call

      expect(event_handler).to_not be_success
      expect(event_handler.errors).to include("Cannot handle this event - #{invalid_webhook_event['eventCategory']} - #{invalid_webhook_event['eventType']}")
    end

    it 'errors if the Xero Invoice save failed' do
      event_handler = Xero::HandleWebhookEvent.new(event: webhook_event.merge({ 'resourceId' => nil })).call

      expect(event_handler).to_not be_success
      expect(event_handler.errors).to include('Invoice ID (Xero) is required')
    end
  end # errors

end
