require 'rails_helper'

RSpec.describe Xero::SyncInvoices, type: :service, invoices: true, xero: true do

  let!(:statuses) do
    %w[
      AUTHORISED
      PAID
      VOIDED
      DELETED
      RANDOM
    ]
  end

  let!(:xero_invoice1) { create(:xero_invoice, :random) }
  let!(:xero_invoice2) { create(:xero_invoice, :random) }
  let!(:xero_invoice3) { create(:xero_invoice, :random) }
  let!(:xero_invoice4) { create(:xero_invoice, :random) }
  let!(:xero_invoice5) { create(:xero_invoice, :random) }

  let!(:invoice1) { create(:invoice, :random, number: 'invoice-1', pushed_to_xero: true, payment_status: 'unpaid') }
  let!(:invoice2) { create(:invoice, :random, number: 'invoice-2', pushed_to_xero: true, payment_status: 'unpaid') }
  let!(:invoice3) { create(:invoice, :random, number: 'invoice-3', pushed_to_xero: true, payment_status: 'unpaid') }
  let!(:invoice4) { create(:invoice, :random, number: 'invoice-5', pushed_to_xero: true, payment_status: 'unpaid') }
  let!(:invoice5) { create(:invoice, :random, number: 'invoice-6', pushed_to_xero: true, payment_status: 'unpaid') }

  let!(:xero_api_invoice1) { double('XeroAPIInvoice', invoice_id: xero_invoice1.invoice_id, invoice_number: invoice1.number, type: Xero::API::Base::CUSTOMER_INVOICE_TYPE, status: statuses[0]) }
  let!(:xero_api_invoice2) { double('XeroAPIInvoice', invoice_id: xero_invoice2.invoice_id, invoice_number: invoice2.number, type: Xero::API::Base::CUSTOMER_INVOICE_TYPE, status: statuses[1]) }
  let!(:xero_api_invoice3) { double('XeroAPIInvoice', invoice_id: xero_invoice3.invoice_id, invoice_number: invoice3.number, type: Xero::API::Base::CUSTOMER_INVOICE_TYPE, status: statuses[2]) }
  let!(:xero_api_invoice4) { double('XeroAPIInvoice', invoice_id: xero_invoice4.invoice_id, invoice_number: invoice4.number, type: Xero::API::Base::CUSTOMER_INVOICE_TYPE, status: statuses[3]) }
  let!(:xero_api_invoice5) { double('XeroAPIInvoice', invoice_id: xero_invoice5.invoice_id, invoice_number: invoice5.number, type: Xero::API::Base::CUSTOMER_INVOICE_TYPE, status: statuses[4]) }

  let!(:invoices) { [invoice1, invoice2, invoice3, invoice4, invoice5] }
  let!(:xero_invoices) { [xero_invoice1, xero_invoice2, xero_invoice3, xero_invoice4, xero_invoice5] }
  let!(:xero_api_invoices) { [xero_api_invoice1, xero_api_invoice2, xero_api_invoice3, xero_api_invoice4, xero_api_invoice5] }

  let!(:invoice_mapper) { double(Xero::API::FetchInvoices) }
  let(:mapped_invoices) do
    invoices.map.with_index do |invoice, idx|
      double(Xero::API::FetchInvoicesByIds::MappedInvoice,
          invoice: invoice,
          xero_invoice: xero_invoices[idx],
          xero_api_invoice: xero_api_invoices[idx]
        )
    end
  end

  before do
    allow(Xero::API::FetchInvoicesByIds).to receive(:new).and_return(invoice_mapper)
    allow(invoice_mapper).to receive(:call).and_return(mapped_invoices)
  end

  context 'pending  xero invoices' do
    it 'makes a request to get invoice mapping for pending xero invoices' do
      expect(Xero::API::FetchInvoicesByIds).to receive(:new).with(xero_invoices: xero_invoices)

      invoice_syncer = Xero::SyncInvoices.new.call
      expect(invoice_syncer).to be_success
    end

    it 'makes a request to get invoice mapping for pending non-failed xero invoices' do
      xero_invoice2.update_columns(failed_at: Time.zone.now, last_errors: 'some-error')
      expect(Xero::API::FetchInvoicesByIds).to receive(:new).with(xero_invoices: [xero_invoice1, xero_invoice3, xero_invoice4, xero_invoice5])

      invoice_syncer = Xero::SyncInvoices.new.call
      expect(invoice_syncer).to be_success
    end

    it 'updates the invoices status / payment_status according to status map and returns the invoice as synced' do
      invoice_syncer = Xero::SyncInvoices.new.call

      expect(invoice_syncer).to be_success
      expect(invoice_syncer.synced_invoices).to include(invoice2, invoice3, invoice4)
      expect(invoice2.reload.payment_status).to eq('paid')
      expect(invoice3.reload.status).to eq('voided')
      expect(invoice4.reload.status).to eq('deleted')

      expect(invoice_syncer.synced_invoices).to_not include(invoice1, invoice5)
      expect(invoice1.reload.payment_status).to eq('unpaid') # no change
      expect(invoice5.reload.payment_status).to eq('unpaid') # no change
    end

    it 'removes the xero invoice records even if relevant Yordar invoice is not synced' do
      invoice_syncer = Xero::SyncInvoices.new.call

      expect(invoice_syncer).to be_success
      xero_invoices.each do |xero_invoice|
        expect{ xero_invoice.reload }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  it 'only makes a request to get invoice mapping for passed in xero invoices' do
    requested_xero_invoices = xero_invoices - [xero_invoices.sample]
    expect(Xero::API::FetchInvoicesByIds).to receive(:new).with(xero_invoices: requested_xero_invoices)

    invoice_syncer = Xero::SyncInvoices.new(xero_invoices: requested_xero_invoices).call
    expect(invoice_syncer).to be_success
  end

  context 'with the invoice fetcher not finding an invoice match' do
    let(:mapped_invoices) do
      invoices.map.with_index do |_, idx|
        double(Xero::API::FetchInvoicesByIds::MappedInvoice,
            invoice: nil,
            xero_invoice: xero_invoices[idx],
            xero_api_invoice: xero_api_invoices[idx]
          )
      end
    end

    it 'returns with an error' do
      invoice_syncer = Xero::SyncInvoices.new.call

      expect(invoice_syncer).to_not be_success

      expected_errors = xero_api_invoices.map do |xero_api_invoice|
        "Could not sync Xero Invoice #{xero_api_invoice.invoice_id} with invoice ##{xero_api_invoice.invoice_number}"
      end

      expect(invoice_syncer.errors).to include(*expected_errors)
    end

    it 'saves the xero invoice record as failed' do
      invoice_syncer = Xero::SyncInvoices.new.call

      expect(invoice_syncer).to_not be_success

      xero_invoices.each do |xero_invoice|
        expect(xero_invoice.failed_at).to be_present

        found_xero_api_invoice = xero_api_invoices.detect{|xero_api_invoice| xero_api_invoice.invoice_id == xero_invoice.invoice_id }
        expect(xero_invoice.last_errors).to include("Could not sync Xero Invoice #{found_xero_api_invoice.invoice_id} with invoice ##{found_xero_api_invoice.invoice_number}")
      end
    end

    context "Xero API Invoices is not of type `#{Xero::API::Base::CUSTOMER_INVOICE_TYPE}`" do
      let!(:supplier_xero_api_invoice1) { double('XeroAPIInvoice', invoice_id: xero_invoice1.invoice_id, invoice_number: invoice1.number, type: Xero::API::Base::SUPPLIER_INVOICE_TYPE, status: statuses[0]) }
      let!(:supplier_xero_api_invoice2) { double('XeroAPIInvoice', invoice_id: xero_invoice2.invoice_id, invoice_number: invoice2.number, type: Xero::API::Base::SUPPLIER_INVOICE_TYPE, status: statuses[1]) }
      let!(:supplier_xero_api_invoice3) { double('XeroAPIInvoice', invoice_id: xero_invoice3.invoice_id, invoice_number: invoice3.number, type: Xero::API::Base::SUPPLIER_INVOICE_TYPE, status: statuses[2]) }
      let!(:supplier_xero_api_invoice4) { double('XeroAPIInvoice', invoice_id: xero_invoice4.invoice_id, invoice_number: invoice4.number, type: Xero::API::Base::SUPPLIER_INVOICE_TYPE, status: statuses[3]) }
      let!(:supplier_xero_api_invoice5) { double('XeroAPIInvoice', invoice_id: xero_invoice5.invoice_id, invoice_number: invoice5.number, type: Xero::API::Base::SUPPLIER_INVOICE_TYPE, status: statuses[4]) }

      let!(:xero_api_invoices) { [supplier_xero_api_invoice1, supplier_xero_api_invoice2, supplier_xero_api_invoice3, supplier_xero_api_invoice4, supplier_xero_api_invoice5] }

      it 'removes the xero invoice records without errors' do
        invoice_syncer = Xero::SyncInvoices.new.call

        expect(invoice_syncer).to be_success
        xero_invoices.each do |xero_invoice|
          expect{ xero_invoice.reload }.to raise_error(ActiveRecord::RecordNotFound)
        end
      end
    end

    context 'when API Invoices could not be found' do
      let!(:xero_api_invoices) { [] }

      it 'saves the xero invoice record as failed' do
        invoice_syncer = Xero::SyncInvoices.new.call

        expect(invoice_syncer).to_not be_success

        xero_invoices.each do |xero_invoice|
          expect(xero_invoice.failed_at).to be_present
          expect(xero_invoice.last_errors).to include("Could not find an API Xero Invoice for #{xero_invoice.id}")
        end
      end
    end

  end

  context 'with Invoice fetch errors' do
    before do
      allow(invoice_mapper).to receive(:call).and_raise(RuntimeError.new)
    end

    it 'returns with an error' do
      invoice_syncer = Xero::SyncInvoices.new.call

      expect(invoice_syncer).to_not be_success
      "Could not get mapping for #{xero_invoices.map(&:id).join(' - ')}"
    end
  end

end
