require 'rails_helper'

class FailingEmailNotificationTest < Notifications::Base

  def initialize(error: nil, sentry: false, error_objects: {})
    @error = error
    @sentry = sentry
    @error_objects = error_objects
  end

  def call
    begin
      raise @error if @error.present?
    rescue => exception
      error_message = exception.message
      log_errors(exception: exception, message: error_message, sentry: @sentry, error_objects: @error_objects)
    end
  end

end

RSpec.describe Notifications::Base, type: :service, notifications: true do

  before do
    # mock Sentry logging
    allow(Raven).to receive(:capture_exception).and_return(true)
  end

  it 'logs the errors in Rails logger' do
    error = RuntimeError.new('runtime-error')

    expect(Rails.logger).to receive(:error).with('runtime-error')
    expect(Rails.logger).to receive(:error).with(error.inspect)
    expect(Rails.logger).to receive(:error).with(anything) # backtrace
    FailingEmailNotificationTest.new(error: error).call
  end

  it 'does not log anything if error is not raised' do
    expect(Rails.logger).to_not receive(:error).with(anything)
    expect(Raven).to_not receive(:capture_exception)

    FailingEmailNotificationTest.new(error: nil, sentry: true).call
  end

  it 'captures the error in sentry if sentry flag is set to true' do
    error = RuntimeError.new('runtime-error')
    error_objects = { error: true }

    expect(Raven).to_not receive(:capture_exception).with(message: 'runtime-error', extras: error_objects, transaction: FailingEmailNotificationTest.name)

    FailingEmailNotificationTest.new(error: error, sentry: true).call
  end

  it 'does not capture the error in sentry if sentry flag is set to false' do
    expect(Raven).to_not receive(:capture_exception)

    error = RuntimeError.new('runtime-error')
    FailingEmailNotificationTest.new(error: error, sentry: false).call
  end

end

