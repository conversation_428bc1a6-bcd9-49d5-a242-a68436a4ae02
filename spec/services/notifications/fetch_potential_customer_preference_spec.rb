require 'rails_helper'

RSpec.describe Notifications::FetchPotentialCustomerPreference, type: :service, notifications: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:order_template_name) { EmailTemplate::VALID_TEMPLATE_NAMES.sample }
  let!(:team_order_template_name) { (EmailTemplate::VALID_TEMPLATE_NAMES - [order_template_name]).sample }
  let!(:billing_template_name) { (EmailTemplate::VALID_TEMPLATE_NAMES - [order_template_name, team_order_template_name]).sample }

  let!(:customer_order_template) { create(:email_template, :random, name: order_template_name, account_type: 'CustomerProfile', kind: 'order') }
  let!(:customer_team_admin_template) { create(:email_template, :random, name: team_order_template_name, account_type: 'CustomerProfile', kind: 'team_admin') }
  let!(:customer_billing_template) { create(:email_template, :random, name: billing_template_name, account_type: 'CustomerProfile', kind: 'billing') }

  it 'returns the customers email salutation' do
    potential_preference = Notifications::FetchPotentialCustomerPreference.new(customer: customer, template_name: [order_template_name, team_order_template_name, billing_template_name].sample).call
    expect(potential_preference.email_salutation).to eq(customer.email_salutation)
  end

  it 'returns the customers email recipients for a customer order email' do
    potential_preference = Notifications::FetchPotentialCustomerPreference.new(customer: customer, template_name: order_template_name).call
    expect(potential_preference.email_recipients).to eq(customer.user.email)
  end

  it 'returns the customers email recipients for a team order admin email' do
    potential_preference = Notifications::FetchPotentialCustomerPreference.new(customer: customer, template_name: team_order_template_name).call
    expect(potential_preference.email_recipients).to eq(customer.user.email)
  end

  it 'returns the customers email recipients for a customer billing email - when customer is missing billing details' do
    potential_preference = Notifications::FetchPotentialCustomerPreference.new(customer: customer, template_name: billing_template_name).call
    expect(potential_preference.email_recipients).to eq(customer.user.email)
  end

  context 'with customer billing details' do
    let!(:billing_details) { create(:billing_details, :random, customer_profile: customer) }

    before do
      customer.reload
    end

    it 'returns the customers billing email recipients for a customer billing email' do
      potential_preference = Notifications::FetchPotentialCustomerPreference.new(customer: customer, template_name: billing_template_name).call
      expect(potential_preference.email_recipients).to eq(billing_details.email)
    end

    it 'adds the users secondary email if present for a customer billing email' do
      customer.user.update_column(:secondary_email, Faker::Internet.email)
      potential_preference = Notifications::FetchPotentialCustomerPreference.new(customer: customer, template_name: billing_template_name).call
      expect(potential_preference.email_recipients).to eq("#{billing_details.email}, #{customer.user.secondary_email}")
    end
  end

end
