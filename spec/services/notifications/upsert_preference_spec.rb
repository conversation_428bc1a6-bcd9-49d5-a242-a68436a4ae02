require 'rails_helper'

RSpec.describe Notifications::UpsertPreference, type: :service, notifications: true, customers: true do

  let!(:template_name) { EmailTemplate::VALID_TEMPLATE_NAMES.sample }
  let!(:customer) { create(:customer_profile, :random) }
  let!(:email_template) { create(:email_template, :random, name: template_name, account_type: 'CustomerProfile', variations: nil) }
  let!(:preference_params) { { template_name: template_name, active: false, variation: nil } }

  it 'creates a new preference for the account' do
    preference_creator = Notifications::UpsertPreference.new(account: customer, preference_params: preference_params).call

    expect(preference_creator).to be_success
    created_preference = preference_creator.preference
    expect(created_preference.account).to eq(customer)
    expect(created_preference.template_name).to eq(template_name)
    expect(created_preference.active).to be_falsey
  end

  it 'creates the preference with the passed in data' do
    preference_params_with_recipient_data = preference_params.merge({ email_recipients: Faker::Internet.email, salutation: Faker::Name.name })
    preference_creator = Notifications::UpsertPreference.new(account: customer, preference_params: preference_params_with_recipient_data).call

    expect(preference_creator).to be_success
    created_preference = preference_creator.preference

    expect(created_preference.email_recipients).to eq(preference_params_with_recipient_data[:email_recipients])
    expect(created_preference.salutation).to eq(preference_params_with_recipient_data[:salutation])
  end

  context 'with existing notification preference' do
    let!(:preference) { create(:notification_preference, :random_customer, account: customer, template_name: template_name, variation: nil) }

    it 'updates the existing notification preference' do
      preference_params = { active: false, email_recipients: Faker::Internet.email, salutation: Faker::Name.name }
      preference_updator = Notifications::UpsertPreference.new(account: customer, preference: preference, preference_params: preference_params).call

      expect(preference_updator).to be_success
      updated_preference = preference_updator.preference

      expect(updated_preference.id).to eq(preference.id) # same id
      expect(updated_preference.account).to eq(customer) # same customer
      expect(updated_preference.template_name).to eq(template_name) # same template
      expect(updated_preference.active).to be_falsey
      expect(updated_preference.email_recipients).to eq(preference_params[:email_recipients])
      expect(updated_preference.salutation).to eq(preference_params[:salutation])
    end

    it 'does not create a duplicate preference' do
      preference_params = { template_name: template_name, active: false }
      preference_updator = Notifications::UpsertPreference.new(account: customer, preference_params: preference_params).call

      expect(preference_updator).to be_success
      updated_preference = preference_updator.preference

      expect(updated_preference.id).to eq(preference.id) # same id
      expect(updated_preference.active).to be_falsey
    end
  end

  context 'errors' do
    it 'cannot upsert a preference without an account' do
      preference_upserter = Notifications::UpsertPreference.new(account: nil, preference_params: preference_params).call

      expect(preference_upserter).to_not be_success
      expect(preference_upserter.errors).to include('Cannot create/update a preference without an account')
    end

    it 'cannot upsert a preference belonging to a different account' do
      preference = create(:notification_preference, :random, account: customer, template_name: template_name)
      another_account = create(:customer_profile, :random)
      preference_upserter = Notifications::UpsertPreference.new(account: another_account, preference: preference, preference_params: preference_params).call

      expect(preference_upserter).to_not be_success
      expect(preference_upserter.errors).to include('You do not have access to this preference')
    end

    it 'cannot upsert if the email template with template name does not exist' do
      preference_params = { template_name: 'non-email-template-name' }
      preference_upserter = Notifications::UpsertPreference.new(account: customer, preference_params: preference_params).call

      expect(preference_upserter).to_not be_success
      expect(preference_upserter.errors).to include('Cannot upsert a preference with this template name')
    end

    it 'cannot upsert if the email template does not belong to the account type' do
      supplier = create(:supplier_profile, :random)
      preference_upserter = Notifications::UpsertPreference.new(account: supplier, preference_params: preference_params).call

      expect(preference_upserter).to_not be_success
      expect(preference_upserter.errors).to include('Cannot upsert a preference with this template name')
    end
  end

  context 'for a preference with variation' do
    let!(:variation_template_name) { (EmailTemplate::VALID_TEMPLATE_NAMES - [template_name]).sample }
    let!(:email_template_with_variation) { create(:email_template, :random, name: variation_template_name, account_type: 'CustomerProfile', variations: %w[variation1 variation2]) }
    let!(:preference_params) { { template_name: variation_template_name, variation: email_template_with_variation.variations.sample, active: false } }

    it 'upserts a notification preferene with variation' do
      preference_upserter = Notifications::UpsertPreference.new(account: customer, preference_params: preference_params).call

      expect(preference_upserter).to be_success
      upserted_preference = preference_upserter.preference
      expect(upserted_preference.account).to eq(customer)
      expect(upserted_preference.template_name).to eq(variation_template_name)
      expect(upserted_preference.variation).to eq(preference_params[:variation])
      expect(upserted_preference.active).to be_falsey
    end

    context 'errors' do
      it 'cannot upsert if the email templates does not have any variations but the its trying to upsert a preference with variation' do
        email_template_with_variation.update_column(:variations, [])
        preference_params = { template_name: variation_template_name, variation: 'a-variation' }
        preference_upserter = Notifications::UpsertPreference.new(account: customer, preference_params: preference_params).call

        expect(preference_upserter).to_not be_success
        expect(preference_upserter.errors).to include('Cannot upsert a notification with variation for a template that does not have any variations')
      end

      it 'cannot upsert if the email templates has variations and its trying to upsert a preference withithout a variation' do
        preference_params = { template_name: variation_template_name, variation: nil }
        preference_upserter = Notifications::UpsertPreference.new(account: customer, preference_params: preference_params).call

        expect(preference_upserter).to_not be_success
        expect(preference_upserter.errors).to include('Cannot upsert a notification without variation for a template that has variations')
      end

      it 'cannot upsert if the email templates has variations and its trying to upsert a in-valid variation' do
        preference_params = { template_name: variation_template_name, variation: 'invalid-variation' }
        preference_upserter = Notifications::UpsertPreference.new(account: customer, preference_params: preference_params).call

        expect(preference_upserter).to_not be_success
        expect(preference_upserter.errors).to include('Cannot upsert a notification with an invalid template variation')
      end
    end
  end

end
