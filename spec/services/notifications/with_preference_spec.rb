require 'rails_helper'

class EmailNotificationTest < Notifications::WithPreference

  def initialize(notifying_account:, template_name:, notification_variation: nil)
    @notifying_account = notifying_account
    @notification_variation = notification_variation
    @template_name = template_name
    @result = Result.new
  end

  def call
    result.preferred_notification = preferred_notification?
    result.email_recipients = email_recipients(default: '<EMAIL>')
    result.email_salutation = email_salutation(default: 'default salutation')

    result
  end

private

  attr_reader :result

  class Result
    attr_accessor :preferred_notification, :email_recipients, :email_salutation

    def initialize
      @preferred_notification = true
      @email_recipients = nil
      @email_salutation = nil
    end
  end

end

RSpec.describe Notifications::WithPreference, type: :service, notifications: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:email_template) { create(:email_template, :random, account_type: customer.class.name) }
  let!(:template_name) { email_template.name }

  context 'without a notification preference for the account and template' do
    it 'returns preferred notification as true' do
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name).call

      expect(emailer.preferred_notification).to be_truthy
    end

    it 'returns email email_recipients / email salutation as default' do
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name).call

      expect(emailer.email_recipients).to eq('<EMAIL>')
      expect(emailer.email_salutation).to eq('default salutation')
    end
  end

  context 'with a missing notifying account' do
    it 'returns preferred notification as true' do
      emailer = EmailNotificationTest.new(notifying_account: nil, template_name: template_name).call

      expect(emailer.preferred_notification).to be_truthy
    end

    it 'returns email email_recipients / email salutation as default' do
      emailer = EmailNotificationTest.new(notifying_account: nil, template_name: template_name).call

      expect(emailer.email_recipients).to eq('<EMAIL>')
      expect(emailer.email_salutation).to eq('default salutation')
    end
  end

  context 'with a missing template name' do
    it 'returns preferred notification as true' do
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: nil).call

      expect(emailer.preferred_notification).to be_truthy
    end

    it 'returns email email_recipients / email salutation as default' do
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: nil).call

      expect(emailer.email_recipients).to eq('<EMAIL>')
      expect(emailer.email_salutation).to eq('default salutation')
    end
  end

  context 'with an existing notification preference' do
    let!(:notification_preference) { create(:notification_preference, account: customer, template_name: template_name, active: false) }

    it 'returns preferred notification as per saved preference' do
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name).call

      expect(emailer.preferred_notification).to be_falsey
    end

    it 'returns the override email salutation only if set in preference else returns default' do
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name).call
      expect(emailer.email_recipients).to eq('<EMAIL>')

      notification_preference.update_column(:email_recipients, 'recipient override')
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name).call
      expect(emailer.email_recipients).to eq('recipient override')
    end

    it 'returns the override email salutation only if set in preference else returns default' do
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name).call
      expect(emailer.email_salutation).to eq('default salutation')

      notification_preference.update_column(:salutation, 'salutation override')
      emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name).call
      expect(emailer.email_salutation).to eq('salutation override')
    end

    context 'with a variation' do
      let!(:variation) { %w[4hr 2hr 30m].sample }
      before do
        notification_preference.update_column(:variation, variation)
      end

      it 'returns preferred notification as per saved preference with variation' do
        emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name, notification_variation: variation).call

        expect(emailer.preferred_notification).to be_falsey
      end

      it 'returns preferred notification as true if the preference with variation cannot be found' do
        emailer = EmailNotificationTest.new(notifying_account: customer, template_name: template_name, notification_variation: ['other-variation', nil].sample).call

        expect(emailer.preferred_notification).to be_truthy
      end
    end
  end

end
