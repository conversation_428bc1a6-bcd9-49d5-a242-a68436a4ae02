require 'rails_helper'

RSpec.describe Stripe::UpsertCreditCard, type: :service, credit_cards: true, stripe: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:now) { Time.zone.now }

  let!(:visa_card_params) { { last4: '1234', brand: 'visa', country_code: 'au', expiry_month: now.strftime('%m'), expiry_year: (now + 1.year).strftime('%Y'), name: 'card name', stripe_token: SecureRandom.hex(7), } }

  before do
    card_attacher = double(Stripe::AttachCardToCustomer)
    allow(Stripe::AttachCardToCustomer).to receive(:new).and_return(card_attacher)
    allow(card_attacher).to receive(:call).and_return(true)
  end

  it 'creates a credit card for the customer' do
    card_creator = Stripe::UpsertCreditCard.new(card_params: visa_card_params, customer: customer).call
    expect(card_creator).to be_success

    created_card = card_creator.credit_card
    expect(created_card).to be_present
  end

  it 'saves the card with the correct data' do
    card_creator = Stripe::UpsertCreditCard.new(card_params: visa_card_params, customer: customer).call
    expect(card_creator).to be_success

    created_card = card_creator.credit_card
    expect(created_card.name).to eq(visa_card_params[:name])

    expect(created_card.cvv).to be_blank
    expect(created_card.name).to eq(visa_card_params[:name])
    expect(created_card.country_code).to eq(visa_card_params[:country_code])
    expect(created_card.expiry_month).to eq(visa_card_params[:expiry_month].to_i)
    expect(created_card.expiry_year).to eq(visa_card_params[:expiry_year].to_i)
    expect(created_card.stripe_token).to eq(visa_card_params[:stripe_token])
  end

  context 'branded card' do

    it 'saves the formatted brand number and brand name for a visa card' do
      card_creator = Stripe::UpsertCreditCard.new(card_params: visa_card_params, customer: customer).call
      expect(card_creator).to be_success

      created_card = card_creator.credit_card
      expect(created_card.brand).to eq(visa_card_params[:brand])
      expect(created_card.number).to eq("####-####-####-#{visa_card_params[:last4]}")
      expect(created_card.label).to eq("Visa (####-####-####-#{visa_card_params[:last4]})")
    end

    it 'saves the formatted brand number and brand name for a amex card' do
      amex_card_params = visa_card_params.merge(brand: 'amex')
      card_creator = Stripe::UpsertCreditCard.new(card_params: amex_card_params, customer: customer).call
      expect(card_creator).to be_success

      created_card = card_creator.credit_card
      expect(created_card.brand).to eq(amex_card_params[:brand])
      expect(created_card.number).to eq("####-######-##{amex_card_params[:last4]}")
      expect(created_card.label).to eq("American Express (####-######-##{amex_card_params[:last4]})")
    end

    it 'saves the formatted brand number and brand name for a mastercard card' do
      mastercard_card_params = visa_card_params.merge(brand: 'mastercard')
      card_creator = Stripe::UpsertCreditCard.new(card_params: mastercard_card_params, customer: customer).call
      expect(card_creator).to be_success

      created_card = card_creator.credit_card
      expect(created_card.brand).to eq(mastercard_card_params[:brand])
      expect(created_card.number).to eq("####-####-####-#{mastercard_card_params[:last4]}")
      expect(created_card.label).to eq("MasterCard (####-####-####-#{mastercard_card_params[:last4]})")
    end
  end

  it 'attaches the credit card to the yordar customer' do
    card_creator = Stripe::UpsertCreditCard.new(card_params: visa_card_params, customer: customer).call
    expect(card_creator).to be_success

    created_card = card_creator.credit_card
    expect(customer.reload.credit_cards).to include(created_card)
  end

  it 'attaches the credit card to the Stripe Customer' do
    expect(Stripe::AttachCardToCustomer).to receive(:new).with(customer: customer, credit_card: anything)

    card_creator = Stripe::UpsertCreditCard.new(card_params: visa_card_params, customer: customer).call
    expect(card_creator).to be_success
  end

end
