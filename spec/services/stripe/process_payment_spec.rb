require 'rails_helper'

RSpec.describe Stripe::ProcessPayment, type: :service, stripe: true do

  let!(:customer) { create(:customer_profile, :random, :with_user, stripe_token: SecureRandom.hex(7)) }
  let!(:credit_card) { create(:credit_card, :valid_stripe_payment) }
  let!(:order) { create(:order, :delivered, customer_profile: customer, customer_total: 1000, credit_card_id: credit_card.id) }

  let!(:invoice) { create(:invoice, :random, amount_price: order.customer_total) }
  let!(:payment) { create(:payment, order: order, amount: order.customer_total, invoice: invoice, user_id: customer.user.id, credit_card: credit_card) }

  context 'with successfull eway response' do
    let!(:successfull_response) do
      OpenStruct.new({
        status: 'succeeded',
        charges: OpenStruct.new({
          data: [
            OpenStruct.new({
              id: SecureRandom.hex(7),
              balance_transaction: SecureRandom.hex(7),
            })
          ]
        })
      })
    end

    before do
      allow(Stripe::PaymentIntent).to receive(:create).and_return(successfull_response)
      order.update_column(:invoice_id, invoice.id)
    end

    it 'creates a payment intent within Stripe with correct attributes' do
      amount_in_cents = (payment.amount * 100).to_i
      payment_attributes = {
        amount: amount_in_cents,
        currency: 'aud',
        customer: customer.stripe_token,
        payment_method: credit_card.stripe_token,
        error_on_requires_action: true,
        confirm: true,
        description: "Payment for invoice ##{invoice.number}",
        metadata: {
          order_ids: invoice.order_ids.join(','),
        },
      }
      expect(Stripe::PaymentIntent).to receive(:create).with(payment_attributes)

      payment_processor = Stripe::ProcessPayment.new(customer: customer, payment: payment).call
      expect(payment_processor).to be_success
    end

    it 'returns payment details' do
      payment_processor = Stripe::ProcessPayment.new(customer: customer, payment: payment).call
      expect(payment_processor).to be_success

      expect(payment_processor.response_message).to eq(successfull_response.status)
      expect(payment_processor.auth_code).to eq(successfull_response.charges.data.first.id)
      expect(payment_processor.transaction_number).to eq(successfull_response.charges.data.first.balance_transaction)
    end
  end

  context 'with invalid request error' do
    let!(:invalid_request_error_message) { "You cannot confirm this PaymentIntent because it's missing a payment method. To confirm the PaymentIntent with #{customer.stripe_token}, specify a payment method attached to this customer along with the customer ID" }

    before do
      allow(Stripe::PaymentIntent).to receive(:create).and_raise(Stripe::InvalidRequestError.new(invalid_request_error_message, {}))
    end

    it 'errors out with the response error' do
      payment_processor = Stripe::ProcessPayment.new(customer: customer, payment: payment).call
      expect(payment_processor).to_not be_success

      expect(payment_processor.errors).to include(invalid_request_error_message)
      expect(payment_processor.response_message).to eq(invalid_request_error_message)
    end
  end

  context 'with invalid request error' do
    let!(:invalid_card_error_message) { 'This is an invalid card' }

    before do
      allow(Stripe::PaymentIntent).to receive(:create).and_raise(Stripe::CardError.new(invalid_card_error_message, {}, {}))
    end

    it 'errors out with the response error' do
      payment_processor = Stripe::ProcessPayment.new(customer: customer, payment: payment).call
      expect(payment_processor).to_not be_success

      expect(payment_processor.errors).to include(invalid_card_error_message)
      expect(payment_processor.response_message).to eq(invalid_card_error_message)
    end
  end

end
