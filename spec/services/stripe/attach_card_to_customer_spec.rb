require 'rails_helper'

RSpec.describe Stripe::AttachCardToCustomer, type: :service, credit_cards: true, stripe: true do

  let!(:stripe_customer) { OpenStruct.new(id: SecureRandom.hex(7)) }
  let!(:customer) { create(:customer_profile, :random) }
  let!(:credit_card) { create(:credit_card, :valid_stripe_payment) }
  let!(:customer_creator) { double(Stripe::CreateCustomer) }

  before do
    # mock Stripe Customer Creator
    allow(Stripe::CreateCustomer).to receive(:new).and_return(customer_creator)
    creator_result = OpenStruct.new(success?: true, customer: customer, stripe_customer: stripe_customer)
    allow(customer_creator).to receive(:call).and_return(creator_result)
    allow(Stripe::PaymentMethod).to receive(:attach).and_return(stripe_customer)
  end

  context 'for a new stripe customer (customer.stripe_token = nil)' do
    it 'requests stripe to create a new customer' do
      expect(Stripe::CreateCustomer).to receive(:new).with(customer: customer, payment_token: credit_card.stripe_token)
      card_attacher = Stripe::AttachCardToCustomer.new(customer: customer, credit_card: credit_card).call

      expect(card_attacher).to be_success
    end

    it 'does not requests a stripe customer to be updated', skipx: 'Stripe::PaymentMethod not implemented with stripe gem 4.0.3' do
      expect(Stripe::PaymentMethod).to_not receive(:attach)
      card_attacher = Stripe::AttachCardToCustomer.new(customer: customer, credit_card: credit_card).call

      expect(card_attacher).to be_success
    end
  end

  context 'if Stripe::CreateCustomer returns with errors' do
    before do
      failed_creator_result = OpenStruct.new(success?: false, errors: ['stripe creator error'])
      allow(customer_creator).to receive(:call).and_return(failed_creator_result)
    end

    it 'returns with the stripe creator errors' do
      card_attacher = Stripe::AttachCardToCustomer.new(customer: customer, credit_card: credit_card).call

      expect(card_attacher).to_not be_success
      expect(card_attacher.errors).to include('stripe creator error')
    end
  end # Stripe Create Customer error

  context 'for an existing stripe customer (customer.stripe_token != nil)' do
    before do
      customer.update_column(:stripe_token, stripe_customer.id)
    end

    it 'requests stripe to attach the new card(payment_method) to the existing', skipx: 'Stripe::PaymentMethod not implemented with stripe gem 4.0.3' do
      expect(Stripe::PaymentMethod).to receive(:attach).with(credit_card.stripe_token, { customer: customer.stripe_token })
      card_attacher = Stripe::AttachCardToCustomer.new(customer: customer, credit_card: credit_card).call

      expect(card_attacher).to be_success
    end

    it 'does not requests a new stripe customer to be created' do
      expect(Stripe::Customer).to_not receive(:create)
      card_attacher = Stripe::AttachCardToCustomer.new(customer: customer, credit_card: credit_card).call

      expect(card_attacher).to be_success
    end

    context 'if Stripe::PaymentMethod attachment returns with errors' do
      let!(:card_error_message) { 'Your card was declined.'  }
      let!(:card_error_json_body) do
        {
          error: {
            charge: SecureRandom.hex(7),
            code: 'card_declined',
            decline_code: 'fraudulent',
            doc_url: 'https://stripe.com/docs/error-codes/card-declined',
            message: card_error_message,
            payment_intent: {
              # ...
            }
          }
        }
      end

      let!(:card_error) { Stripe::CardError.new(card_error_message, nil, 'card_declined', json_body: card_error_json_body) }

      before do        
        allow(Stripe::PaymentMethod).to receive(:attach).and_raise(card_error)
      end

      it 'returns an abridged Card error' do
        card_attacher = Stripe::AttachCardToCustomer.new(customer: customer, credit_card: credit_card).call

        expect(card_attacher).to_not be_success
        expect(card_attacher.errors).to include("#{card_error_message} - #{card_error.json_body&.dig(:error, :decline_code)}")
      end
    end
  end
end
