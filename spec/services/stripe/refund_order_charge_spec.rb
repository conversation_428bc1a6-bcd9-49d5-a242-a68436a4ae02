require 'rails_helper'

RSpec.describe Stripe::RefundOrderCharge, type: :service, stripe: true do

  let!(:order_charge) { create(:order_charge, :random) }

  let!(:stripe_refund_response) do
    OpenStruct.new({
      id: SecureRandom.hex(7),
      status: 'canceled',
      charges: OpenStruct.new({
        data: [
          OpenStruct.new({
            id: SecureRandom.hex(7),
            balance_transaction: SecureRandom.hex(7),
            payment_intent: order_charge.stripe_token,
            refunds: OpenStruct.new({
              data: [
                OpenStruct.new({
                  id: SecureRandom.hex(7)
                })
              ]
            })
          })
        ]
      })
    })
  end

  before do
    allow(Stripe::PaymentIntent).to receive(:cancel).and_return(stripe_refund_response)
  end

  it 'requests to cancel the payment intent attached to the order charge' do
    expect(Stripe::PaymentIntent).to receive(:cancel).with(order_charge.stripe_token)

    charge_refunder = Stripe::RefundOrderCharge.new(order_charge: order_charge).call
    expect(charge_refunder).to be_success
  end

  it 'returns a refund object' do
    charge_creator = Stripe::RefundOrderCharge.new(order_charge: order_charge).call

    expect(charge_creator).to be_success
    created_refund = charge_creator.refund
    expect(created_refund).to be_present
    expect(created_refund).to be_is_a(Stripe::RefundOrderCharge::StripeRefund)
    expect(created_refund.id).to eq(stripe_refund_response.charges.data.first.refunds.data.first.id)
    expect(created_refund.charge_id).to eq(stripe_refund_response.charges.data.first.id)
    expect(created_refund.payment_id).to eq(stripe_refund_response.charges.data.first.payment_intent)
  end

  context 'with invalid request error' do
    let!(:invalid_request_error_message) { 'You cannot cancel this PaymentIntent because it has a status of canceled. Only a PaymentIntent with one of the following statuses may be canceled: requires_payment_method, requires_capture, requires_confirmation, requires_action.' }

    before do
      allow(Stripe::PaymentIntent).to receive(:cancel).and_raise(Stripe::InvalidRequestError.new(invalid_request_error_message, {}))
    end

    it 'errors out with the response error' do
      charge_creator = Stripe::RefundOrderCharge.new(order_charge: order_charge).call
      expect(charge_creator).to_not be_success

      expect(charge_creator.errors).to include(invalid_request_error_message)
      expect(charge_creator.response_message).to eq(invalid_request_error_message)
    end
  end

  context 'with invalid response status' do
    let!(:invalid_stripe_refund_response) do
      OpenStruct.new({
        id: SecureRandom.hex(7),
        status: Faker::Name.name,
        charges: ['...']
      })
    end

    before do
      allow(Stripe::PaymentIntent).to receive(:cancel).and_return(invalid_stripe_refund_response)
    end

    it 'errors out as not being able to refund the order charge' do
      charge_creator = Stripe::RefundOrderCharge.new(order_charge: order_charge).call
      expect(charge_creator).to_not be_success

      expect(charge_creator.errors).to include("Could not refund charge for order_charge ##{order_charge.id}")
      expect(charge_creator.response_message).to eq(invalid_stripe_refund_response.status)
    end
  end

end
