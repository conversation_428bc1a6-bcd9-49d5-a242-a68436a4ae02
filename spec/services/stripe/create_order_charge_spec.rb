require 'rails_helper'

RSpec.describe Stripe::CreateOrderCharge, type: :service, stripe: true do

  let!(:stripe_card) { create(:credit_card, :valid_stripe_payment) }
  let!(:order) { create(:order, :new, customer_total: 1000, credit_card: stripe_card) }

  let!(:stripe_payment_response) do
    OpenStruct.new({
      id: SecureRandom.hex(7),
      status: 'requires_capture',
      charges: OpenStruct.new({
        data: [
          OpenStruct.new({
            id: SecureRandom.hex(7),
            balance_transaction: SecureRandom.hex(7),
          })
        ]
      })
    })
  end

  before do
    allow(Stripe::PaymentIntent).to receive(:create).and_return(stripe_payment_response)
  end

  it 'requests a on-hold charge (payment intent) to be created on the order\'s credit card' do
    payment_options = {
      currency: 'aud',
      error_on_requires_action: true,

      confirm: true,
      capture_method: 'manual', # required to create on-hold charge

      amount: (order.customer_total * 100).to_i,
      description: "On-Hold charge for order ##{order.id}",
      customer: order.customer_profile.stripe_token,
      payment_method: order.credit_card.stripe_token,
      metadata: {
        order_id: order.id,
      },
    }
    expect(Stripe::PaymentIntent).to receive(:create).with(payment_options)

    order_charger = Stripe::CreateOrderCharge.new(order: order).call
    expect(order_charger).to be_success
  end

  it 'returns a charge with the payment intent id and charge id' do
    charge_creator = Stripe::CreateOrderCharge.new(order: order).call

    expect(charge_creator).to be_success
    created_charge = charge_creator.charge
    expect(created_charge).to be_present
    expect(created_charge).to be_is_a(Stripe::CreateOrderCharge::StripeCharge)
    expect(created_charge.payment_id).to eq(stripe_payment_response.id)
    expect(created_charge.charge_id).to eq(stripe_payment_response.charges.data.first.id)
  end

  context 'with invalid request error' do
    let!(:invalid_request_error_message) { 'You cannot cancel this PaymentIntent because it has a status of canceled. Only a PaymentIntent with one of the following statuses may be canceled: requires_payment_method, requires_capture, requires_confirmation, requires_action.' }
    let!(:invalide_request_error) { Stripe::InvalidRequestError.new(invalid_request_error_message, {}) }

    before do
      allow(Stripe::PaymentIntent).to receive(:create).and_raise(invalide_request_error)
    end

    it 'errors out with the response error' do
      charge_creator = Stripe::CreateOrderCharge.new(order: order).call
      expect(charge_creator).to_not be_success

      expect(charge_creator.errors).to include(invalid_request_error_message)
      expect(charge_creator.response_message).to eq(invalid_request_error_message)
    end
  end

  context 'with a card error' do
    let!(:card_error_message) { 'Your card was declined.'  }
    let!(:card_error_json_body) do
      {
        error: {
          charge: SecureRandom.hex(7),
          code: 'card_declined',
          decline_code: 'fraudulent',
          doc_url: 'https://stripe.com/docs/error-codes/card-declined',
          message: card_error_message,
          payment_intent: {
            # ...
          }
        }
      }
    end
    # (message, param, code: nil, http_status: nil, http_body: nil, json_body: nil, http_headers: nil)
    let!(:card_error) { Stripe::CardError.new(card_error_message, nil, 'card_declined', json_body: card_error_json_body) }

    before do
      allow(Stripe::PaymentIntent).to receive(:create).and_raise(card_error)
    end

    it 'errors out with the response error' do
      charge_creator = Stripe::CreateOrderCharge.new(order: order).call
      expect(charge_creator).to_not be_success

      expect(charge_creator.response_message).to eq("#{card_error.message} - #{card_error.json_body[:error][:decline_code]}")
      expect(charge_creator.errors).to include(card_error)
    end
  end

  context 'with invalid response status' do
    let!(:invalid_stripe_payment_response) do
      OpenStruct.new({
        id: SecureRandom.hex(7),
        status: Faker::Name.name,
        charges: ['...']
      })
    end

    before do
      allow(Stripe::PaymentIntent).to receive(:create).and_return(invalid_stripe_payment_response)
    end

    it 'errors out as not being able to create the on-hold charge' do
      charge_creator = Stripe::CreateOrderCharge.new(order: order).call
      expect(charge_creator).to_not be_success

      expect(charge_creator.errors).to include("Could not create on-hold charge for order ##{order.id}")
      expect(charge_creator.response_message).to eq(invalid_stripe_payment_response.status)
    end
  end

end
