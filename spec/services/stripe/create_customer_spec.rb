require 'rails_helper'

RSpec.describe Stripe::CreateCustomer, type: :service, stripe: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:payment_method_id) { SecureRandom.hex(7) }
  let!(:stripe_customer) { OpenStruct.new(id: SecureRandom.hex(7)) }

  before do
    allow(Stripe::Customer).to receive(:create).and_return(stripe_customer)
  end

  it 'creates a new customer in stripe' do
    expect(Stripe::Customer).to receive(:create)

    customer_creator = Stripe::CreateCustomer.new(customer: customer, payment_token: payment_method_id).call
    expect(customer_creator).to be_success
  end

  it 'attaches the stripe customer Id to the Yordar Customer' do
    customer_creator = Stripe::CreateCustomer.new(customer: customer, payment_token: payment_method_id).call
    expect(customer_creator).to be_success

    expect(customer.reload.stripe_token).to eq(stripe_customer.id)
  end

  context 'errors' do

    it 'cannot create a Stripe customer without a Yordar Customer' do
      customer_creator = Stripe::CreateCustomer.new(customer: nil, payment_token: payment_method_id).call
      expect(customer_creator).to_not be_success

      expect(customer_creator.errors).to include('Cannot create a stripe customer without a Yordar customer')
    end

    it 'cannot create a Stripe customer for Yordar Customer who already has a stripe token' do
      customer.update_column(:stripe_token, stripe_customer.id)
      customer_creator = Stripe::CreateCustomer.new(customer: customer, payment_token: payment_method_id).call
      expect(customer_creator).to_not be_success

      expect(customer_creator.errors).to include('Yordar Customer is already a Stripe customer')
    end
  end

end
