require 'rails_helper'

RSpec.describe Coupons::CalculateDiscount, type: :service, coupons: true do

  let!(:coupon) { create(:coupon, :random) }
  let!(:redeemable_amount) { 1000 }

  it 'returns the reedeemable_amount, discount and discount total as keys' do
    coupon_values = Coupons::CalculateDiscount.new(coupon: coupon, amount: redeemable_amount).call

    expect(coupon_values.keys).to include(:amount, :discount, :total)
    expect(coupon_values[:amount]).to eq(redeemable_amount)
  end

  context 'an amount type coupon' do
    let!(:amount_coupon) { create(:coupon, :random, amount: 50, type: 'amount') }

    it 'returns the discount as coupon amount' do
      coupon_values = Coupons::CalculateDiscount.new(coupon: amount_coupon, amount: redeemable_amount).call

      expect(coupon_values[:discount]).to eq(amount_coupon.amount)
    end

    it 'returns the discounted total' do
      coupon_values = Coupons::CalculateDiscount.new(coupon: amount_coupon, amount: redeemable_amount).call

      expect(coupon_values[:total]).to eq(redeemable_amount - amount_coupon.amount)
    end
  end

  context 'a pecentage type coupon' do
    let!(:precent_coupon) { create(:coupon, :random, amount: 50, type: 'percentage') }

    it 'returns the discount as coupon amount percent of redeemable_amount' do
      coupon_values = Coupons::CalculateDiscount.new(coupon: precent_coupon, amount: redeemable_amount).call

      expect(coupon_values[:discount]).to eq(redeemable_amount * precent_coupon.amount / 100)
    end

    it 'returns the discounted total' do
      coupon_values = Coupons::CalculateDiscount.new(coupon: precent_coupon, amount: redeemable_amount).call

      expect(coupon_values[:total]).to eq(redeemable_amount - (redeemable_amount * precent_coupon.amount / 100))
    end
  end

  context 'invalid/un-redeemable coupon' do
    it 'returns discount of 0 if coupon is missing' do
      coupon_values = Coupons::CalculateDiscount.new(coupon: nil, amount: redeemable_amount).call

      expect(coupon_values[:discount]).to eq(0)
      expect(coupon_values[:total]).to eq(redeemable_amount)
    end

    it 'returns discount of 0 if redeemable_amount is 0' do
      coupon_values = Coupons::CalculateDiscount.new(coupon: coupon, amount: 0).call

      expect(coupon_values[:amount]).to eq(0)
      expect(coupon_values[:discount]).to eq(0)
      expect(coupon_values[:total]).to eq(0)
    end

    it 'returns discount of 0 if coupon validation hasn\'t started' do
      coupon.update_columns(valid_from: Time.now + 10.days)

      coupon_values = Coupons::CalculateDiscount.new(coupon: coupon, amount: redeemable_amount).call

      expect(coupon_values[:discount]).to eq(0)
      expect(coupon_values[:total]).to eq(redeemable_amount)
    end

    it 'returns discount of 0 if coupon has expired' do
      coupon.update_columns(valid_from: Time.now - 10.days, valid_until: Time.now - 2.days)

      coupon_values = Coupons::CalculateDiscount.new(coupon: coupon, amount: redeemable_amount).call

      expect(coupon_values[:discount]).to eq(0)
      expect(coupon_values[:total]).to eq(redeemable_amount)
    end

    it 'returns discount of 0 if coupon redemption count is equal (or above) the limit' do
      redemption_limit = 2
      coupon.update_columns(redemption_limit: redemption_limit)
      (redemption_limit + rand(10)).times do
        create(:coupon_redemption, coupon: coupon)
      end

      coupon_values = Coupons::CalculateDiscount.new(coupon: coupon, amount: redeemable_amount).call

      expect(coupon_values[:discount]).to eq(0)
      expect(coupon_values[:total]).to eq(redeemable_amount)
    end
  end

end
