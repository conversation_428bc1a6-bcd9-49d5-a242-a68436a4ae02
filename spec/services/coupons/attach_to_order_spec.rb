require 'rails_helper'

RSpec.describe Coupons::AttachToOrder, type: :service, orders: true, coupons: true do

  let!(:order) { create(:order, :draft) }
  let!(:coupon) { create(:coupon, :random) }
  let!(:totals) { double(Orders::CalculateCustomerTotals) }

  before do
    allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(totals)
    allow(totals).to receive(:call).and_return(true)
  end

  it 'attachs the coupon to the order' do
    coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: coupon.code).call

    expect(coupon_attacher).to be_success
    expect(coupon_attacher.coupon).to eq(coupon)

    expect(order.reload.coupon).to eq(coupon)
  end

  it 're-calculates the order totals' do
    expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, save_totals: true)

    coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: coupon.code).call
    expect(coupon_attacher).to be_success
  end

  it 'redeems the coupon if attaching to a non-draft order' do
    order.update_column(:status, 'new')

    coupon_redeemer = double(Coupons::Redeem)
    expect(Coupons::Redeem).to receive(:new).with(coupon: coupon, order: order).and_return(coupon_redeemer)
    expect(coupon_redeemer).to receive(:call)

    coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: coupon.code).call
    expect(coupon_attacher).to be_success
  end

  context 'errors' do
    it 'does not attach coupon if code is blank' do
      coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: '').call

      expect(coupon_attacher).to_not be_success
      expect(coupon_attacher.errors).to include('Invalid coupon')
    end

    it 'does not attach coupon if code is invalid' do
      coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: 'invalid-code').call

      expect(coupon_attacher).to_not be_success
      expect(coupon_attacher.errors).to include('Invalid coupon')
    end

    it 'does not attach coupon if its expired' do
      coupon.update_column(:valid_until, Time.zone.now - 1.day)
      coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: coupon.code).call

      expect(coupon_attacher).to_not be_success
      expect(coupon_attacher.errors).to include('Coupon has expired')
    end

    it 'does not attach coupon if its reached its redemption limit' do
      coupon.redemption_limit.times do
        create(:coupon_redemption, coupon: coupon)
      end
      coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: coupon.code).call

      expect(coupon_attacher).to_not be_success
      expect(coupon_attacher.errors).to include('Coupon has already been used')
    end

    it 'does not re-calculate order totals' do
      expect(Orders::CalculateCustomerTotals).to_not receive(:new)

      coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: '').call
      expect(coupon_attacher).to_not be_success
    end

    context 'coupon already used by customer' do
      let!(:customer) { create(:customer_profile, :random) }
      let!(:customer_order) { create(:order, :confirmed, customer_profile: customer, coupon: coupon) }

      it 'does not attach coupon if its already used by the customer' do
        coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: coupon.code, profile: customer).call

        expect(coupon_attacher).to_not be_success
        expect(coupon_attacher.errors).to include('Coupon can only be used once')
      end

      it 'allows you to attach coupon even if its already used by the customer in a draft / cancelled / skipped / paused order' do
        customer_order.update_column(:status, %w[draft cancelled skipped paused].sample)
        coupon_attacher = Coupons::AttachToOrder.new(order: order, coupon_code: coupon.code, profile: customer).call

        expect(coupon_attacher).to be_success
        expect(order.reload.coupon).to eq(coupon)
      end
    end
  end

end
