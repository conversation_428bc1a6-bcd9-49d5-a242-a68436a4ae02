require 'rails_helper'

RSpec.describe Coupons::Redeem, type: :service, orders: true, coupons: true do

  let!(:coupon) { create(:coupon, :random, redemption_limit: 10) }
  let!(:order) { create(:order, :new, coupon: coupon) }

  it 'redeems the coupon for the order' do
    redeemer = Coupons::Redeem.new(coupon: coupon, order: order).call
    expect(redeemer).to be_success
  end

  it 'updates the redemption count of the coupon' do
    redeemer = Coupons::Redeem.new(coupon: coupon, order: order)

    expect{ redeemer.call }.to change{ coupon.reload.coupon_redemptions_count }.from(0).to(1)
    expect(redeemer.result).to be_success
  end

  it 'returns the discounted amount' do
    redeemer = Coupons::Redeem.new(coupon: coupon, order: order).call

    expect(redeemer).to be_success
    expect(redeemer.discount).to eq(coupon.amount)
  end

  context 'errors' do
    it 'cannot redeem coupon without the coupon' do
      redeemer = Coupons::Redeem.new(coupon: nil, order: order).call

      expect(redeemer).to_not be_success
      expect(redeemer.errors).to include('Cannot redeem a missing coupon')
    end

    it 'cannot redeem a coupon without an order' do
      redeemer = Coupons::Redeem.new(coupon: coupon, order: nil).call

      expect(redeemer).to_not be_success
      expect(redeemer.errors).to include('Cannot redeem a coupon without an order (amount)')
    end

    it 'cannot redeem an coupon that does not belong to the order' do
      order.update_column(:coupon_id, nil)

      redeemer = Coupons::Redeem.new(coupon: coupon, order: order).call
      expect(redeemer).to_not be_success
      expect(redeemer.errors).to include('Coupon does not belong to the order')
    end
  end

  context 'coupon attached to other orders' do
    let!(:coupon) { create(:coupon, :random, redemption_limit: 2) }
    let!(:coupon_redemption) { create(:coupon_redemption, coupon: coupon) }
    let!(:order) { create(:order, :new, coupon: coupon) }
    let!(:draft_order) { create(:order, :draft, coupon: coupon) }
    let(:total_calculator) { double(Orders::CalculateCustomerTotals) }

    it 'detaches the coupon from other draft orders if is not redeemable if past the current redemption' do
      # recalculates totals without coupon
      expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: draft_order, save_totals: true).and_return(total_calculator)
      expect(total_calculator).to receive(:call)

      redeemer = Coupons::Redeem.new(coupon: coupon, order: order).call

      expect(redeemer).to be_success
      expect(redeemer.coupon.coupon_redemptions_count).to eq(2)
      expect(draft_order.reload.coupon).to be_blank
    end
  end

end
