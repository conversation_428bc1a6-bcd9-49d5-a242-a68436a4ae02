require 'rails_helper'

RSpec.describe Eway::ProcessPayment, type: :service, payments: true, eway: true do

  let!(:eway_merchant) { double(ActiveMerchant::Billing::Base.gateway(:eway_managed)) }

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:eway_credit_card) { create(:credit_card, :valid_eway_payment) }
  let!(:order) { create(:order, :delivered, customer_profile: customer, customer_total: 1000, credit_card_id: eway_credit_card.id) }

  let!(:invoice) { create(:invoice, :random, amount_price: order.customer_total) }
  let!(:payment) { create(:payment, order: order, amount: order.customer_total, invoice: invoice, user_id: customer.user.id, credit_card: eway_credit_card) }

  before do
    allow(ActiveMerchant::Billing::Base.gateway(:eway_managed)).to receive(:new).and_return(eway_merchant)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:eway, :login).and_return('eway-login')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:eway, :username).and_return('eway-username')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:eway, :password).and_return('eway-password')

    order.update_column(:invoice_id, invoice.id)
  end

  context 'with successfull eway response' do
    let!(:successfull_response) do
      OpenStruct.new(params: {
        'message' => 'successfull response message',
        'success' => true,
        'auth_code' => 'auth_code',
        'transaction_number' => 'transaction_number'
      })
    end

    before do
      allow(eway_merchant).to receive(:purchase).and_return(successfull_response)
    end

    it 'sets up a payment gateway' do
      expect(ActiveMerchant::Billing::Base.gateway(:eway_managed)).to receive(:new).with(login: 'eway-login', username: 'eway-username', password: 'eway-password')

      payment_processor = Eway::ProcessPayment.new(payment: payment).call
      expect(payment_processor).to be_success
    end

    it 'makes a purchace request to the payment gateway' do
      amount_in_cents = (payment.amount * 100).to_i
      payment_options = {
        invoice: invoice.number,
        description: order.name,
      }
      expect(eway_merchant).to receive(:purchase).with(amount_in_cents, eway_credit_card.gateway_token, payment_options)

      payment_processor = Eway::ProcessPayment.new(payment: payment).call
      expect(payment_processor).to be_success
    end

    it 'returns payment details' do
      payment_processor = Eway::ProcessPayment.new(payment: payment).call
      expect(payment_processor).to be_success

      expect(payment_processor.response_message).to eq('successfull response message')
      expect(payment_processor.auth_code).to eq('auth_code')
      expect(payment_processor.transaction_number).to eq('transaction_number')
    end
  end

  context 'with successfull eway response message of 08,Honour With Identification' do
    let!(:successfull_honour_response) do
      OpenStruct.new(params: {
        'message' => '08,Honour With Identification',
        'success' => true,
        'auth_code' => 'honour_auth_code',
        'transaction_number' => 'honour_transaction_number'
      })
    end
    before do
      allow(eway_merchant).to receive(:purchase).and_return(successfull_honour_response)
    end

    it 'returns payment details' do
      payment_processor = Eway::ProcessPayment.new(payment: payment).call
      expect(payment_processor).to be_success

      expect(payment_processor.response_message).to eq('08,Honour With Identification')
      expect(payment_processor.auth_code).to eq('honour_auth_code')
      expect(payment_processor.transaction_number).to eq('honour_transaction_number')
    end
  end

  context 'with an unsuccessfull eway response' do
    let!(:unsuccessfull_response) do
      OpenStruct.new(params: {
        'message' => 'unsuccessfull response message',
        'success' => false,
        'auth_code' => 'auth_code',
        'transaction_number' => 'transaction_number'
      })
    end
    before do
      allow(eway_merchant).to receive(:purchase).and_return(unsuccessfull_response)
    end

    it 'returns errored payment details' do
      payment_processor = Eway::ProcessPayment.new(payment: payment).call
      expect(payment_processor).to_not be_success

      expect(payment_processor.response_message).to eq('unsuccessfull response message')
      expect(payment_processor.auth_code).to be_blank
      expect(payment_processor.transaction_number).to be_blank
      expect(payment_processor.errors).to include(unsuccessfull_response.inspect)
    end
  end

end
