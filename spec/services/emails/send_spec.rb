require 'rails_helper'

RSpec.describe Emails::Send, type: :service, emails: true, notifications: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:template_name) { EmailTemplate::VALID_TEMPLATE_NAMES.sample }
  let!(:recipient) { customer.email_recipient }
  let!(:subject) { Faker::Lorem.sentence }
  let!(:email_options) do
    {
      fk_id: customer.id,
      ref: SecureRandom.hex(7)
    }
  end

  before do
    template_mailer_sender = OpenStruct.new(deliver: true)
    allow(TemplateMailer).to receive(:send_email).and_return(template_mailer_sender)
  end

  it 'creates a new email record' do
    email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options).call

    expect(email_sender).to be_success
    sent_email = email_sender.email
    expect(sent_email.template_name).to eq(template_name)
    expect(sent_email.recipient).to eq(recipient)
    expect(sent_email.fk_id).to eq(email_options[:fk_id])
    expect(sent_email.options).to be_blank
    expect(sent_email.ref).to eq(email_options[:ref])
    expect(sent_email).to be_persisted
  end

  context 'email details' do
    it 'creates a new email records with recipient details' do
      email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options).call

      expect(email_sender).to be_success
      sent_email = email_sender.email
      expect(sent_email.details).to eq({ subject: subject, recipient: recipient }.stringify_keys)
    end

    it 'Adds cc to the new email record\'s details' do
      email_cc = Faker::Internet.email
      email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, cc: email_cc, email_options: email_options).call

      expect(email_sender).to be_success
      sent_email = email_sender.email
      expect(sent_email.details['cc']).to eq(email_cc)
    end
  end

  it 'sends the email via TemplateMailer (by default)' do
    expect(TemplateMailer).to receive(:send_email).with(template_name: template_name, email_variables: anything, recipient: anything, cc: anything, sender: anything, subject: anything, email_attachments: anything)

    email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options).call
    expect(email_sender).to be_success
  end

  it 'marks the email as being sent' do
    email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options).call

    expect(email_sender).to be_success
    sent_email = email_sender.email
    expect(sent_email.sent_at).to be_present
  end

  it 'sends the email with subject' do
    expect(TemplateMailer).to receive(:send_email).with(template_name: template_name, email_variables: anything, recipient: anything, cc: anything, sender: anything, subject: subject, email_attachments: anything)

    email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options).call
    expect(email_sender).to be_success
  end

  it 'sends the email with cc' do
    email_cc = [nil, Faker::Internet.email].sample

    expect(TemplateMailer).to receive(:send_email).with(template_name: template_name, email_variables: anything, recipient: anything, cc: email_cc, sender: anything, subject: anything, email_attachments: anything)

    email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, cc: email_cc, email_options: email_options).call
    expect(email_sender).to be_success
  end

  context 'with sender' do
    let!(:sender_email) { Faker::Internet.email }

    it 'sends the the passed in sender' do
      expect(TemplateMailer).to receive(:send_email).with(template_name: template_name, email_variables: anything, recipient: anything, cc: anything, sender: sender_email, subject: anything, email_attachments: anything)

      email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, sender: sender_email, email_options: email_options).call
      expect(email_sender).to be_success
    end

    it '<NAME_EMAIL> as default sender' do
      expect(TemplateMailer).to receive(:send_email).with(template_name: template_name, email_variables: anything, recipient: anything, cc: anything, sender: '<EMAIL>', subject: anything, email_attachments: anything)

      email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options).call
      expect(email_sender).to be_success
    end
  end

  context 'with email_variables' do
    let!(:email_variables) do
      {
        customer_name: customer.name,
        page_path: Rails.application.routes.url_helpers.show_page_path(slug: 'page-slug'),
      }
    end

    it 'sends the email via TemplateMailer (default)' do
      expect(TemplateMailer).to receive(:send_email).with(template_name: template_name, email_variables: email_variables, recipient: anything, cc: anything, sender: anything, subject: anything, email_attachments: anything)

      email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options, email_variables: email_variables).call
      expect(email_sender).to be_success
    end
  end

  context 'with attachments' do
    let!(:attachments) do
      [
        { name: 'attachment-1', file_url: 'attachment-file-1.pdf' },
        { name: 'attachment-2', file_url: 'attachment-file-2.csv', is_csv: true }
      ]
    end

    before do
      open_file = OpenStruct.new(read: 'stubbed-read-file')
      allow_any_instance_of(Emails::Send).to receive(:open).and_return(open_file)
    end

    it 'sends the email via TemplateMailer along with attachments' do
      expected_attachments = {
        'attachment-1.pdf' => 'stubbed-read-file',
        'attachment-2.csv' => 'attachment-file-2.csv'
      }
      expect(TemplateMailer).to receive(:send_email).with(template_name: template_name, email_variables: anything, recipient: anything, cc: anything, sender: anything, subject: anything, email_attachments: expected_attachments)

      email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options, attachments: attachments).call
      expect(email_sender).to be_success
    end

    context 'attachments as documents' do
      let!(:csv_document) { create(:document, :random, kind: 'team_order_avery_labels', url: 'http://example.com/spreadsheet.csv') }
      let!(:pdf_document) { create(:document, :random, kind: (Document::VALID_KINDS - ['team_order_avery_labels', 'supplier_json_order_details']).sample, url: 'http://example.com/pdf-document.pdf') }

      let!(:attachments) { [csv_document, pdf_document] }

      it 'sends the email via TemplateMailer along with sanitized attachments hash' do
        expected_attachments = {
          "#{csv_document.name}.csv" => 'stubbed-read-file', # csv document
          "#{pdf_document.name}.pdf" => 'stubbed-read-file', # pdf document
        }
        expect(TemplateMailer).to receive(:send_email).with(template_name: template_name, email_variables: anything, recipient: anything, cc: anything, sender: anything, subject: anything, email_attachments: expected_attachments)

        email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options, attachments: attachments).call
        expect(email_sender).to be_success
      end
    end
  end

  context 'errors' do
    it 'does not send emails without a recipient' do
      email_sender = Emails::Send.new(template_name: template_name, recipient: nil, subject: subject, email_options: email_options).call

      expect(email_sender).to_not be_success
      expect(email_sender.errors).to include('Cannot send email without recipient(s)')
    end

    it 'does not send emails without a subject' do
      email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: nil, email_options: email_options).call

      expect(email_sender).to_not be_success
      expect(email_sender.errors).to include('Cannot send email without a valid subject')
    end

    it 'does not send emails without a valid template name' do
      template_name = [nil, 'random-template-name'].sample
      email_sender = Emails::Send.new(template_name: template_name, recipient: recipient, subject: subject, email_options: email_options)

      expect{ email_sender.call }.to raise_error(RuntimeError, "Unknown email template name '#{template_name}'")
      # expect(email_sender).to_not be_success
      # expect(email_sender.errors).to include("Cannot send email without a valid template - #{template_name}")
    end
  end

end
