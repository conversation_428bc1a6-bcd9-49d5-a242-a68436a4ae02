require 'rails_helper'

RSpec.describe Customers::Notifications::NotifyBudgetSpends, type: :service, event_logs: true, budgets: true, customers: true do

  subject { Customers::Notifications::NotifyBudgetSpends.new(since: time).call }

  let!(:time) { Time.zone.now.beginning_of_month + rand(10..15).days }

  let!(:customer) { create(:customer_profile, :random) }
  let!(:customer_budget) { create(:customer_budget, :random, frequency: 'monthly', customer_profile: customer, starts_on: time - 1.month, ends_on: time + 2.months, value: 100) }
  let!(:order1) { create(:order, :delivered, customer_profile: customer, delivery_at: time + 2.days, customer_total: 25) }
  let!(:order2) { create(:order, :delivered, customer_profile: customer, delivery_at: time + 5.days, customer_total: 30) }

  let(:expected_event_info) do
    budget = customer_budget.value
    spend = [order1, order2].sum(&:customer_total)
    {
      number_of_orders: 2,
      budget: budget.round(2),
      spend: spend.round(2),
      spend_percentage: ((spend / budget)*100).round(2),
      po_number: nil,
      remaining: (budget - spend).round(2)
    }
  end

  let!(:event_log_creator) { double(EventLogs::Create) }

  before do    
    allow(EventLogs::Create).to receive(:new).and_return(event_log_creator)
    event_log_response = OpenStruct.new(success?: true, event_log: 'created-event-log', errors: [])
    allow(event_log_creator).to receive(:call).and_return(event_log_response)
  end

  it 'creates event logs for the budget within time\'s month' do
    expect(EventLogs::Create).to receive(:new).with(event_object: customer_budget, event: anything, severity: anything, **expected_event_info) # budget-spend-50 with severity of info based on above mock data

    sent_notifications = subject
    expect(sent_notifications.size).to eq(1)
  end

  it 'creates an event log even if an existing event log is present for previous month' do
    create(:event_log, :random, loggable: customer_budget, event: 'budget-spend-50', created_at: time - 1.month + 10.days) # previous months event log
    expect(EventLogs::Create).to receive(:new).with(event_object: customer_budget, event: anything, severity: anything, **expected_event_info)

    sent_notifications = subject
    expect(sent_notifications.size).to eq(1)
  end

  context 'customer budget\'s active within (time) range' do
    let!(:potential_start_date) { [time - 1.month, time.beginning_of_month, time].sample }
    let!(:potential_end_date) { [time, time.end_of_month, time + 1.month].sample }

    it 'creates event logs for the ranged budget that is active within  time\'s month' do # ranged budgets = customer budgets with start and end date
      customer_budget.update_columns(starts_on: potential_start_date, ends_on: potential_end_date)
      expect(EventLogs::Create).to receive(:new).with(event_object: customer_budget, event: anything, severity: anything, **expected_event_info)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(1)
    end

    it 'creates event logs for the long running budget that is active within  time\'s month' do # long running budgets = customer budgets with start date but no end date
      customer_budget.update_columns(starts_on: potential_start_date, ends_on: nil)
      expect(EventLogs::Create).to receive(:new).with(event_object: customer_budget, event: anything, severity: anything, **expected_event_info)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(1)
    end
  end

  context 'customer budget\'s not active within (time) range' do
    it 'does not create event logs for the ranged budget that are active outside time\'s month' do
      previous_month = [true, false].sample
      case
      when previous_month
        potential_start_date = time - 2.months
        potential_end_date = time - 1.month
      else # next_month
        potential_start_date = time + 1.month
        potential_end_date = time + 2.months
      end
      customer_budget.update_columns(starts_on: potential_start_date, ends_on: potential_end_date)
      expect(EventLogs::Create).to_not receive(:new)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(0)
    end

    it 'does not create event logs for the long running budget that are active outside time\'s month' do
      customer_budget.update_columns(starts_on: time + rand(2..5).months, ends_on: nil)
      expect(EventLogs::Create).to_not receive(:new)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(0)
    end
  end

  Customers::Notifications::NotifyBudgetSpends::NOTIFIABLE_PERCENTS.each do |notifiable_percent|
    context "within spend percentage of #{notifiable_percent}" do
      before do
        case notifiable_percent
        when 100
          [order1, order2].each{|order| order.update_column(:customer_total, [50, 55].sample) }
        when 90
          [order1, order2].each{|order| order.update_column(:customer_total, [45, 47].sample) }
        when 75
          [order1, order2].each{|order| order.update_column(:customer_total, [38, 40].sample) }
        else # 50
          [order1, order2].each{|order| order.update_column(:customer_total, [25, 30].sample) }
        end
      end
      it "creates an event log with event(type) of budget-spend-#{notifiable_percent}" do
        expect(EventLogs::Create).to receive(:new).with(event_object: customer_budget, event: "budget-spend-#{notifiable_percent}", severity: anything, **expected_event_info)

        sent_notifications = subject
        expect(sent_notifications.size).to eq(1)
      end
    end
  end

  context 'non notifications' do
    it 'does not notify if total spend of orders within (time/since based) month is below lowest NOTIFIABLE PERCENT (50%)' do
      [order1, order2].sample.update_column(:delivery_at, time - 1.month)
      expect(EventLogs::Create).to_not receive(:new)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(0)
    end

    it 'does not notify if spend of confirmed orders is below lowest NOTIFIABLE PERCENT (50%)' do
      [order1, order2].sample.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[new amended pending confirmed delivered]).sample)
      expect(EventLogs::Create).to_not receive(:new)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(0)
    end


    it 'does not notify if spend of orders attached to customer budget\'s PO is below lowest NOTIFIABLE PERCENT (50%)' do
      customer_purchase_order = create(:customer_purchase_order, :random, customer_profile: customer)
      customer_budget.update_column(:customer_purchase_order_id, customer_purchase_order.id)
      [order1, order2].sample.update_column(:cpo_id, customer_purchase_order.id)
      expect(EventLogs::Create).to_not receive(:new)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(0)
    end

    it 'does not notify if total spend is below lowest NOTIFIABLE PERCENT (50%)' do
      [order1, order2].sample.update_column(:customer_total, 10)
      expect(EventLogs::Create).to_not receive(:new)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(0)
    end

    it 'does not notify (again) if an event log is already created for the month' do
      create(:event_log, :random, loggable: customer_budget, event: 'budget-spend-50', created_at: time + 10.days)
      expect(EventLogs::Create).to_not receive(:new)

      sent_notifications = subject
      expect(sent_notifications.size).to eq(0)
    end
  end

end