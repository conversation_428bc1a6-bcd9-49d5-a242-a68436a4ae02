require 'rails_helper'

RSpec.describe Customers::Notifications::SendClosureEmails, type: :service, customers: true, orders: true, chistmas_closures: true, notifications: true do

  let!(:start_of_month) { Time.zone.now.beginning_of_month }

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:customer2) { create(:customer_profile, :random) }

  let!(:supplier1) { create(:supplier_profile, :random, close_from: start_of_month, close_to: start_of_month.end_of_month) }
  let!(:supplier2) { create(:supplier_profile, :random, close_from: start_of_month, close_to: start_of_month.end_of_month) }

  let!(:order1) { create(:order, :new, customer_profile: customer1, delivery_at: start_of_month + rand(10).days, order_type: 'recurrent', pattern: '1.week') }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }

  let!(:order2) { create(:order, :new, customer_profile: customer2, delivery_at: start_of_month + rand(10).days, order_type: 'recurrent', pattern: '1.week') }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

  context 'Initial Notiifcations' do
    let!(:version) { 'initial' }

    before do
      email_sender = double(Customers::Emails::SendInitialClosureEmail)
      allow(Customers::Emails::SendInitialClosureEmail).to receive(:new).and_return(email_sender)
      successfull_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
      allow(email_sender).to receive(:call).and_return(successfull_sender_response)
    end

    it 'sends the notification to customers with orders within closure period' do
      expect(Customers::Emails::SendInitialClosureEmail).to receive(:new).with(customer: customer1, orders: [order1])
      expect(Customers::Emails::SendInitialClosureEmail).to receive(:new).with(customer: customer2, orders: [order2])

      notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(2) # per customer
    end

    it 'doesn\'t send notification to customers for one-off orders' do
      order2.update_column(:order_type, 'one-off')

      expect(Customers::Emails::SendInitialClosureEmail).to receive(:new).with(customer: customer1, orders: [order1])
      expect(Customers::Emails::SendInitialClosureEmail).to_not receive(:new).with(customer: customer2, orders: [order2])

      notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1) # per customer
    end

    it 'doesn\'t send notification to customers with orders not within closure period' do
      order1.update_column(:delivery_at, start_of_month - 1.day)

      expect(Customers::Emails::SendInitialClosureEmail).to_not receive(:new).with(customer: customer1, orders: [order1])

      notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    it 'doesn\'t send notification to customers with orders within closure period but with status of draft/pending/rejected/cancelled' do
      order2.update_column(:status, %w[draft pending rejected cancelled paused].sample)

      expect(Customers::Emails::SendInitialClosureEmail).to_not receive(:new).with(customer: customer2, orders: [order2])

      notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    context 'with passed in closure orders' do
      let!(:customer3) { create(:customer_profile, :random) }
      let!(:order3) { create(:order, :new, customer_profile: customer3, delivery_at: start_of_month - 1.day) }
      let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }

      it 'sends notification to all customers of passed in orders' do
        expect(Customers::Emails::SendInitialClosureEmail).to receive(:new).with(customer: customer1, orders: [order1])
        expect(Customers::Emails::SendInitialClosureEmail).to receive(:new).with(customer: customer2, orders: [order2])
        expect(Customers::Emails::SendInitialClosureEmail).to receive(:new).with(customer: customer3, orders: [order3])

        notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version, closure_orders: [order1, order2, order3]).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.sent_notifications.size).to eq(3) # per customer
      end
    end

    context 'with email sending error' do
      before do
        email_sender = double(Customers::Emails::SendInitialClosureEmail)
        allow(Customers::Emails::SendInitialClosureEmail).to receive(:new).and_return(email_sender)
        unsuccessfull_sender_response = OpenStruct.new(success?: false, sent_notification: nil, errors: ['sending-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_sender_response)
      end

      it 'returns with errors' do
        expect(Customers::Emails::SendInitialClosureEmail).to receive(:new).with(customer: customer1, orders: [order1])
        expect(Customers::Emails::SendInitialClosureEmail).to receive(:new).with(customer: customer2, orders: [order2])

        notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

        expect(notifications_sender).to_not be_success
        expect(notifications_sender.sent_notifications).to be_blank
        expect(notifications_sender.errors).to include('sending-error')
      end
    end
  end

  context 'Final Notiifcations' do
    let!(:version) { 'final' }

    before do
      email_sender = double(Customers::Emails::SendFinalClosureEmail)
      allow(Customers::Emails::SendFinalClosureEmail).to receive(:new).and_return(email_sender)
      successfull_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
      allow(email_sender).to receive(:call).and_return(successfull_sender_response)
    end

    it 'sends the notification to customers with orders within closure period' do
      expect(Customers::Emails::SendFinalClosureEmail).to receive(:new).with(customer: customer1, orders: [order1])
      expect(Customers::Emails::SendFinalClosureEmail).to receive(:new).with(customer: customer2, orders: [order2])

      notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(2) # per customer
    end

    it 'doesn\'t send notification to customers for one-off orders' do
      order1.update_column(:order_type, 'one-off')

      expect(Customers::Emails::SendFinalClosureEmail).to_not receive(:new).with(customer: customer1, orders: [order1])
      expect(Customers::Emails::SendFinalClosureEmail).to receive(:new).with(customer: customer2, orders: [order2])

      notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    it 'doesn\'t send notification to customers with orders not within closure period' do
      order1.update_column(:delivery_at, start_of_month - 1.day)

      expect(Customers::Emails::SendFinalClosureEmail).to_not receive(:new).with(customer: customer1, orders: [order1])

      notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    it 'doesn\'t send notification to customers with orders within closure period but with status of draft/pending/skipped/rejected/cancelled' do
      order2.update_column(:status, %w[draft pending skipped rejected cancelled paused].sample)

      expect(Customers::Emails::SendFinalClosureEmail).to_not receive(:new).with(customer: customer2, orders: [order2])

      notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(1)
    end

    context 'with passed in closure orders' do
      let!(:customer3) { create(:customer_profile, :random) }
      let!(:order3) { create(:order, :new, customer_profile: customer3, delivery_at: start_of_month - 1.day) }
      let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }

      it 'sends notiifcation to all customers that are passed in' do
        expect(Customers::Emails::SendFinalClosureEmail).to receive(:new).with(customer: customer1, orders: [order1])
        expect(Customers::Emails::SendFinalClosureEmail).to receive(:new).with(customer: customer2, orders: [order2])
        expect(Customers::Emails::SendFinalClosureEmail).to receive(:new).with(customer: customer3, orders: [order3])

        notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version, closure_orders: [order1, order2, order3]).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.sent_notifications.size).to eq(3) # per customer
      end
    end

    context 'with email sending error' do
      before do
        email_sender = double(Customers::Emails::SendFinalClosureEmail)
        allow(Customers::Emails::SendFinalClosureEmail).to receive(:new).and_return(email_sender)
        unsuccessfull_sender_response = OpenStruct.new(success?: false, sent_notification: nil, errors: ['sending-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_sender_response)
      end

      it 'returns with errors' do
        expect(Customers::Emails::SendFinalClosureEmail).to receive(:new).with(customer: customer1, orders: [order1])
        expect(Customers::Emails::SendFinalClosureEmail).to receive(:new).with(customer: customer2, orders: [order2])

        notifications_sender = Customers::Notifications::SendClosureEmails.new(version: version).call

        expect(notifications_sender).to_not be_success
        expect(notifications_sender.sent_notifications).to be_blank
        expect(notifications_sender.errors).to include('sending-error')
      end
    end
  end

end
