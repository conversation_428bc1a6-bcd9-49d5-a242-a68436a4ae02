require 'rails_helper'

RSpec.describe Customers::Notifications::SendOrderSummariesForDay, type: :service, orders: true, suppliers: true, notifications: true, emails: true do

  let!(:delivery_at) { Time.zone.parse('2020-09-15 00:00:00') } # Tuesday

  let!(:customer1) { create(:customer_profile, :random, :with_user) }
  let!(:billing_details1) { create(:billing_details, :random, customer_profile: customer1, order_summaries: true) }

  let!(:customer2) { create(:customer_profile, :random, :with_user) }
  let!(:billing_details2) { create(:billing_details, :random, customer_profile: customer2, order_summaries: true) }

  let!(:order1) { create(:order, :confirmed, delivery_at: delivery_at, customer_profile: customer1) }
  let!(:order2) { create(:order, :confirmed, delivery_at: delivery_at, customer_profile: customer2) }

  let!(:order_line11) { create(:order_line, :random, order: order1, status: 'accepted') }
  let!(:order_line12) { create(:order_line, :random, order: order1, status: 'accepted') }

  let!(:order_line21) { create(:order_line, :random, order: order2, status: 'accepted') }
  let!(:order_line22) { create(:order_line, :random, order: order2, status: 'accepted') }

  before do
    email_sender = double(Customers::Emails::SendOrdersSummaryEmail)
    allow(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).and_return(email_sender)
    email_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
    allow(email_sender).to receive(:call).and_return(email_sender_response)
  end

  it 'send an email to the suppliers of confirmed orders' do
    expect(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).with(customer: customer1, summary_day: delivery_at, customer_orders: [order1])
    expect(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).with(customer: customer2, summary_day: delivery_at, customer_orders: [order2])

    summary_sender = Customers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at).call
    expect(summary_sender).to be_success
    expect(summary_sender.sent_notifications.size).to eq(2)
  end

  it 'doesn\'t generate pdf/send emails for suppliers order lines of unconfirmed orders' do
    order1.update_column(:status, %w[new draft pending amended cancelled].sample)

    expect(Customers::Emails::SendOrdersSummaryEmail).to_not receive(:new).with(customer: customer1, summary_day: delivery_at, customer_orders: [order1])
    expect(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).with(customer: customer2, summary_day: delivery_at, customer_orders: [order2])

    summary_sender = Customers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at).call
    expect(summary_sender).to be_success
    expect(summary_sender.sent_notifications.size).to eq(1)
  end

  it 'doesn\'t generate pdf/send emails for suppliers order lines of orders not delivered on summary day' do
    order2.update_column(:delivery_at, delivery_at + rand(1..10).days)

    expect(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).with(customer: customer1, summary_day: delivery_at, customer_orders: [order1])
    expect(Customers::Emails::SendOrdersSummaryEmail).to_not receive(:new).with(customer: customer2, summary_day: delivery_at, customer_orders: [order2])

    summary_sender = Customers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at).call
    expect(summary_sender).to be_success
    expect(summary_sender.sent_notifications.size).to eq(1)
  end

  it 'only sends emails for customers with with billing details with order_summaries flag set to true' do
    if [true, false].sample
      billing_details1.update_column(:order_summaries, false)
    else
      billing_details1.destroy
    end

    expect(Customers::Emails::SendOrdersSummaryEmail).to_not receive(:new).with(customer: customer1, summary_day: delivery_at, customer_orders: [order1])
    expect(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).with(customer: customer2, summary_day: delivery_at, customer_orders: [order2])

    summary_sender = Customers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at).call
    expect(summary_sender).to be_success
    expect(summary_sender.sent_notifications.size).to eq(1)
  end

  it 'only sends emails for customers with user business set as Yordar(yr)' do
    customer2.user.update_column(:business, 'non-yr')

    expect(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).with(customer: customer1, summary_day: delivery_at, customer_orders: [order1])
    expect(Customers::Emails::SendOrdersSummaryEmail).to_not receive(:new).with(customer: customer2, summary_day: delivery_at, customer_orders: [order2])

    summary_sender = Customers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at).call
    expect(summary_sender).to be_success
    expect(summary_sender.sent_notifications.size).to eq(1)
  end

  context 'with already sent summary email' do
    let!(:customer1_summary_email) { create(:email, :random, recipient: nil, ref: "customer-order_summary-#{customer1.id}-#{delivery_at.strftime('%Y%m%d')}", fk_id: customer1.id) }

    it 'doesn\'t send an order summary email to a customer who\'s already got the summary email' do
      expect(Customers::Emails::SendOrdersSummaryEmail).to_not receive(:new).with(customer: customer1, summary_day: delivery_at, customer_orders: [order1])
      expect(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).with(customer: customer2, summary_day: delivery_at, customer_orders: [order2])

      summary_sender = Customers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(1)
    end
  end

  context 'with email sending errors' do
    before do
      email_sender = double(Customers::Emails::SendOrdersSummaryEmail)
      allow(Customers::Emails::SendOrdersSummaryEmail).to receive(:new).and_return(email_sender)
      email_sender_response = OpenStruct.new(success?: false, sent_notification: nil, errors: ['Email Sending ERROR'])
      allow(email_sender).to receive(:call).and_return(email_sender_response)
    end

    it 'returns with an error if the email sending fails' do
      summary_sender = Customers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at).call
      expect(summary_sender).to_not be_success
      expect(summary_sender.sent_notifications).to be_blank
      expect(summary_sender.errors).to include('Email Sending ERROR')
    end
  end

end
