require 'rails_helper'

RSpec.describe Customers::Notifications::NotifyPendingOrders, type: :service, notifications: true, pending_orders: true do

  subject { Customers::Notifications::NotifyPendingOrders.new(time: notification_time).call }

  let!(:notification_time) { Time.zone.now.beginning_of_week + 1.day + 2.hours } # Tuesday
  let!(:delivery_on) { notification_time + 1.day } # Wednesday

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:quote_order11) { create(:order, :quoted, customer_profile: customer1, delivery_at: notification_time + 1.day) }
  let!(:quote_order12) { create(:order, :quoted, customer_profile: customer1, delivery_at: notification_time + 1.day) }

  let!(:customer2) { create(:customer_profile, :random) }
  let!(:quote_order21) { create(:order, :quoted, customer_profile: customer2, delivery_at: notification_time + 1.day) }
  let!(:quote_order22) { create(:order, :quoted, customer_profile: customer2, delivery_at: notification_time + 1.day) }

  before do
    email_sender = double(Customers::Emails::SendPendingOrdersEmail)
    allow(Customers::Emails::SendPendingOrdersEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call)
  end

  it 'notifies each customer about their pending quote orders that are to be delivered tomorrow' do
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer1, pending_orders: [quote_order11, quote_order12], delivery_on: delivery_on.beginning_of_day)
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer2, pending_orders: [quote_order21, quote_order22], delivery_on: delivery_on.beginning_of_day)

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(quote_order11, quote_order12, quote_order21, quote_order22)
  end

  it 'doesn\'t notify non-pending orders' do
    quote_order12.update_column(:status, %w[new confirmed amended cancelled delivered].sample)
    quote_order21.update_column(:status, %w[new confirmed amended cancelled delivered].sample)

    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer1, pending_orders: [quote_order11], delivery_on: delivery_on.beginning_of_day) # does not contain quote order12
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer2, pending_orders: [quote_order22], delivery_on: delivery_on.beginning_of_day) # does not contain quote order21

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(quote_order11, quote_order22)
  end

  it 'doesn\'t notify orders which are not be delivered tomorrow' do
    quote_order11.update_column(:delivery_at, [(notification_time - 1.days), notification_time, (notification_time + 2.days)].sample)
    quote_order22.update_column(:delivery_at, [(notification_time - 1.days), notification_time, (notification_time + 2.days)].sample)
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer1, pending_orders: [quote_order12], delivery_on: delivery_on.beginning_of_day) # does not contain quote order11
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer2, pending_orders: [quote_order21], delivery_on: delivery_on.beginning_of_day) # does not contain quote order22

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(quote_order12, quote_order21)
  end

  it 'doesn\'t notify orders without any customers' do
    quote_order12.update_column(:customer_profile_id, nil)
    quote_order22.update_column(:customer_profile_id, nil)
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer1, pending_orders: [quote_order11], delivery_on: delivery_on.beginning_of_day) # does not contain quote order12
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer2, pending_orders: [quote_order21], delivery_on: delivery_on.beginning_of_day) # does not contain quote order22

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(quote_order11, quote_order21)
  end

  it 'doesn\'t notify orders without any delivery date' do
    quote_order11.update_column(:delivery_at, nil)
    quote_order21.update_column(:delivery_at, nil)
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer1, pending_orders: [quote_order12], delivery_on: delivery_on.beginning_of_day) # does not contain quote order12
    expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer2, pending_orders: [quote_order22], delivery_on: delivery_on.beginning_of_day) # does not contain quote order22

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(quote_order12, quote_order22)
  end

  it 'doesn\'t send an email if there are no pending custom/quote orders' do
    [quote_order11, quote_order12, quote_order21, quote_order22].each do |order|
      order.update_column(:status, 'cancelled')
    end
    expect(Customers::Emails::SendPendingOrdersEmail).to_not receive(:new)

    notification_sender = subject
    expect(notification_sender).to_not be_success
    expect(notification_sender.errors).to include('No Pending Orders')
    expect(notification_sender.pending_orders).to be_blank
  end

  context 'on a Friday' do
    let!(:notification_time) { Time.zone.now.beginning_of_week + 4.days } # Friday

    before do
      [quote_order11, quote_order12, quote_order21, quote_order22].each do |order|
        order.update_column(:delivery_at, notification_time + [1, 2, 3].sample.days)
      end
    end

    it 'notifies customers about all pending quote orders that are to be delivered over the weekend and Monday' do
      expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer1, pending_orders: [quote_order11, quote_order12], delivery_on: delivery_on.beginning_of_day)
      expect(Customers::Emails::SendPendingOrdersEmail).to receive(:new).with(customer: customer2, pending_orders: [quote_order21, quote_order22], delivery_on: delivery_on.beginning_of_day)

      notification_sender = subject
      expect(notification_sender.pending_orders).to include(quote_order11, quote_order12, quote_order21, quote_order22)
    end
  end

  context 'on a weekend' do
    let!(:notification_time) { Time.zone.now.beginning_of_week + [5, 6].sample.days } # Saturday or Sunday

    it 'does not notify on a weekend' do
      expect(Customers::Emails::SendPendingOrdersEmail).to_not receive(:new)

      notification_sender = subject
      expect(notification_sender).to_not be_success
      expect(notification_sender.errors).to include('Cannot notify on a weekend')
      expect(notification_sender.pending_orders).to be_blank
    end
  end

end
