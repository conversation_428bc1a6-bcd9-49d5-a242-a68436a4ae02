require 'rails_helper'

RSpec.describe Customers::Register, type: :service, customers: true do

  let!(:suburb) { create(:suburb, :random) }
  let!(:lead_list_id) { rand(20..30) }

  let!(:user_params) do
    {
      email: Faker::Internet.email,
      password: SecureRandom.hex(7),
      firstname: Faker::Name.first_name,
      lastname: Faker::Name.first_name,
      suburb_id: suburb.id,
    }
  end

  let!(:registration_params) do
    {
      company_name: Faker::Company.name,
      role: CustomerProfile::VALID_ROLES.sample,
    }
  end

  let!(:company_registration) { double(Customers::RegisterUnderCompanyTeamAdmin) }
  let!(:company_team_admin_registration) { double(Customers::RegisterUnderCompanyTeamAdmin) }

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:disable, :welcome_email).and_return(false)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:active_campaign, :lead_list_id).and_return(lead_list_id.to_s)

    # mock register under company team admin
    allow(Customers::RegisterUnderCompanyTeamAdmin).to receive(:new).and_return(company_registration)
    company_registration_success_response = OpenStruct.new(success?: true)
    allow(company_registration).to receive(:call).and_return(company_registration_success_response)

    # mock register as company team admin
    allow(Customers::RegisterAsCompanyTeamAdmin).to receive(:new).and_return(company_team_admin_registration)
    admin_registration_success_response = OpenStruct.new(success?: true)
    allow(company_team_admin_registration).to receive(:call).and_return(admin_registration_success_response)

    # mock company team admin welcome email
    welcome_email_sender = welcome_delayed_email_sender = double(Customers::Emails::SendCompanyTeamAdminWelcomeEmail)
    allow(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to receive(:new).and_return(welcome_email_sender)
    allow(welcome_email_sender).to receive(:delay).and_return(welcome_delayed_email_sender)
    allow(welcome_delayed_email_sender).to receive(:call).and_return(true)

    # mock Hubspot syncer
    contact_syncer = delayed_syncer = double(Hubspot::SyncContact)
    allow(Hubspot::SyncContact).to receive(:new).and_return(contact_syncer)
    allow(contact_syncer).to receive(:delay).and_return(delayed_syncer)
    allow(delayed_syncer).to receive(:call).and_return(true)

    # mock email sender
    email_sender = delayed_email_sender = double(Customers::Emails::SendWelcomeEmail)
    allow(Customers::Emails::SendWelcomeEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'creates a new user record with passed in customer params' do
    customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params).call

    expect(customer_registration).to be_success

    created_user = customer_registration.user
    expect(created_user).to be_present
    expect(created_user.firstname).to eq(user_params[:firstname])
    expect(created_user.lastname).to eq(user_params[:lastname])
    expect(created_user.email).to eq(user_params[:email])
    expect(created_user.suburb).to eq(suburb)
  end

  it 'sanitizes the params' do
    role = CustomerProfile::VALID_ROLES.sample
    unsanitized_user_params = user_params.merge({ firstname: 'name-with-spaces-after   ', lastname: '  name-with-before-spaces' })
    unsanitized_registration_params = registration_params.merge({ company_name: 'company-with-spaces-after   ', role: " #{role}" })
    customer_registration = Customers::Register.new(user_params: unsanitized_user_params, registration_params: unsanitized_registration_params).call

    expect(customer_registration).to be_success
    created_user = customer_registration.user
    created_customer = customer_registration.customer

    expect(created_user.firstname).to eq('name-with-spaces-after')
    expect(created_user.lastname).to eq('name-with-before-spaces')

    expect(created_customer.company_name).to eq('company-with-spaces-after')
    expect(created_customer.role).to eq(role)
  end

  it 'creates a new customer record' do
    customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params).call

    expect(customer_registration).to be_success
    created_user = customer_registration.user

    created_customer = customer_registration.customer
    expect(created_customer).to be_present
    expect(created_customer).to be_a(CustomerProfile)
    expect(created_customer.user).to eq(created_user)

    expect(created_customer.name).to eq("#{user_params[:firstname]} #{user_params[:lastname]}")
  end

  it 'creates a new (associated) customer flags record' do
    customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params).call

    expect(customer_registration).to be_success
    created_customer = customer_registration.customer

    expect(created_customer.customer_flags).to be_present
    expect(created_customer.customer_flags).to be_persisted
  end

  it 'does not make a request to register under a company team admin', access_permissions: true do
    expect(Customers::RegisterUnderCompanyTeamAdmin).to_not receive(:new)

    customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params).call
    expect(customer_registration).to be_success
  end

  context 'with a passed in company team admin', access_permissions: true do
    let!(:admin_customer) { create(:customer_profile, :random, company_team_admin: true) }

    it 'makes a request to register under the passed in company team admin' do
      expect(Customers::RegisterUnderCompanyTeamAdmin).to receive(:new).with(customer: anything, company_team_admin: admin_customer) # passes the newly created customer

      customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params, company_team_admin: admin_customer).call
      expect(customer_registration).to be_success
    end

    it 'does not make a request to register under a company team admin, if passed in customer is not actually a company_team_admin' do
      admin_customer.update_column(:company_team_admin, false)
      expect(Customers::RegisterUnderCompanyTeamAdmin).to_not receive(:new)

      customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params, company_team_admin: admin_customer).call
      expect(customer_registration).to be_success
    end

    context 'when registration under company team admin fails' do
      before do
        # mock Sentry logging
        allow(Raven).to receive(:capture_exception).and_return(true)

        # mock failed company registration
        company_registration_unsuccessful_response = OpenStruct.new(success?: false, errors: ['company-register-error'])
        allow(company_registration).to receive(:call).and_return(company_registration_unsuccessful_response)
      end

      it 'returns successfully but raises a Sentry error' do
        expected_error_message = 'Failed to register customer under company team admin'
        expected_error_objects = { customer_id: (admin_customer.id + 1), company_team_admin_id: admin_customer.id, errors: ['company-register-error']} # customer id is from newly created customer after company team admin customer
        expect(Raven).to receive(:capture_exception).with(RuntimeError.new(expected_error_message), message: expected_error_message, extra: expected_error_objects, transaction: Customers::Register.name)

        customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params, company_team_admin: admin_customer).call
        expect(customer_registration).to be_success
      end
    end # with errors
  end # register under company team admin

  context 'with a passed in adminable customer', access_permissions: true do
    let!(:adminable_customer) { create(:customer_profile, :random, :with_user) }

    it 'makes a request to register the customer as a company team admin with access to adminable customer' do
      expect(Customers::RegisterAsCompanyTeamAdmin).to receive(:new).with(company_team_admin: anything, customer: adminable_customer) # passes the newly created customer

      customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params, adminable_customer: adminable_customer).call
      expect(customer_registration).to be_success
    end

    context 'when registration as a company team admin fails' do
      before do
        # mock Sentry logging
        allow(Raven).to receive(:capture_exception).and_return(true)

        # mock failed company registration
        admin_registration_unsuccessful_response = OpenStruct.new(success?: false, errors: ['admin-register-error'])
        allow(company_team_admin_registration).to receive(:call).and_return(admin_registration_unsuccessful_response)
      end

      it 'returns successfully but raises a Sentry error' do
        expected_error_message = 'Failed to register customer as a company team admin'
        expected_error_objects = { admin_id: (adminable_customer.id + 1), adminable_customer_id: adminable_customer.id, errors: ['admin-register-error']} # customer id is from newly created customer after existing adminable_customer
        expect(Raven).to receive(:capture_exception).with(RuntimeError.new(expected_error_message), message: expected_error_message, extra: expected_error_objects, transaction: Customers::Register.name)

        customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params, adminable_customer: adminable_customer).call
        expect(customer_registration).to be_success
      end
    end # with errors
  end # register as company team admin

  context 'Hubspot Integration', hubspot: true do
    it 'syncs with Hubspot' do
      expect(Hubspot::SyncContact).to receive(:new).with(contact: anything, refresh: true) # passes the newly created user
      expect(Hubspot::SyncContact).to_not receive(:new).with(contact: anything, refresh: true) # called only once

      customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params).call
      expect(customer_registration).to be_success
    end
  end

  it 'sends a welcome email' do
    expect(Customers::Emails::SendWelcomeEmail).to receive(:new).with(customer: anything) # passes the newly created customer

    customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params).call
    expect(customer_registration).to be_success
  end

  it 'logs a `New Registration` event', event_logs: true do
    expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'new-customer-registration', role: registration_params[:role]) # event_object is the newly created customer

    customer_registration = Customers::Register.new(user_params: user_params, registration_params: registration_params).call
    expect(customer_registration).to be_success
  end

  context 'errors' do
    it 'returns an error if user_params do not contain email and/or password' do
      missing_params = [{ email: nil }, { password: nil }, { email: nil, password: nil }].sample
      user_params_with_missing_fields = user_params.merge(missing_params)

      customer_registration = Customers::Register.new(user_params: user_params_with_missing_fields, registration_params: registration_params).call

      expect(customer_registration).to_not be_success
      expect(customer_registration.errors).to include('Cannot register without a valid email and password')
    end

    it 'returns an error if user with passed in emaill already exists' do
      customer = create(:customer_profile, :random, :with_user)
      user_params_with_existing_email = user_params.merge({ email: customer.reload.user.email })

      customer_registration = Customers::Register.new(user_params: user_params_with_existing_email, registration_params: registration_params).call

      expect(customer_registration).to_not be_success
      expect(customer_registration.errors).to include('An account already exists with these details')
    end

    it 'errors and rollbacks all records if the customer creation failed' do
      invalid_registration_params = registration_params.merge({ role: 'invalid-role' })

      customer_registration = Customers::Register.new(user_params: user_params, registration_params: invalid_registration_params).call
      expect(customer_registration).to_not be_success

      expect(customer_registration.errors).to include('Role is not included in the list')

      expect(customer_registration.user).to be_present # creates a user
      expect(customer_registration.user).to_not be_persisted # but rollbacks its save

      expect(customer_registration.customer).to be_blank
    end
  end

end
