require 'rails_helper'

RSpec.describe Customers::Budgets::ManageFutureBudgets, type: :service, customers: true, budgets: true do

  let!(:budget_date) { Date.today.beginning_of_month }
  let!(:customer) { create(:customer_profile, :random) }
  let!(:customer_purchase_order) { create(:customer_purchase_order, :random, customer_profile: customer) }

  # - - current - - b1 - - b2 - - - - TO - - current - - - - - - - - - -
  context 'when managing budgets after creation of an open budget' do
    let!(:budget1) { create(:customer_budget, :random, customer_profile: customer, starts_on: budget_date + 2.months) }
    let!(:budget2) { create(:customer_budget, :random, customer_profile: customer, starts_on: budget_date + 1.year, ends_on: budget_date + 1.year + 2.months) }

    let!(:budget_params) do
      {
        starts_on: budget_date,
        frequency: 'monthly'
      } # no ends_on means the budget is open (aka spans till end of time)
    end

    it 'purges all future budgets' do
      budgets_manager = Customers::Budgets::ManageFutureBudgets.new(customer: customer, budget_params: budget_params).call

      expect(budgets_manager).to be_success
      # expect(budgets_manager.purged_budgets).to include(budget1, budget2) # won't work as budgets are destroyed
      expect(budgets_manager.purged_budgets.map(&:starts_on)).to include(*[budget1, budget2].map(&:starts_on))
      expect(budgets_manager.purged_budgets.map(&:value)).to include(*[budget1, budget2].map(&:value))
      expect{ budget1.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ budget2.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'only purges future budgets belonging to the customer' do
      customer2 = create(:customer_profile, :random)
      budget2.update_column(:customer_profile_id, customer2.id)

      budgets_manager = Customers::Budgets::ManageFutureBudgets.new(customer: customer, budget_params: budget_params).call

      expect(budgets_manager).to be_success
      expect(budgets_manager.purged_budgets.map(&:starts_on)).to_not include(budget2.starts_on)
      expect(budgets_manager.purged_budgets.map(&:value)).to_not include(budget2.value)
      expect{ budget2.reload }.to_not raise_error(ActiveRecord::RecordNotFound)

      expect{ budget1.reload }.to raise_error(ActiveRecord::RecordNotFound) # still removes customer budget
    end

    it 'only purges future budgets not connected to any purchase order' do
      budget1.update_column(:customer_purchase_order_id, customer_purchase_order.id)

      budgets_manager = Customers::Budgets::ManageFutureBudgets.new(customer: customer, budget_params: budget_params).call

      expect(budgets_manager).to be_success
      expect(budgets_manager.purged_budgets.map(&:starts_on)).to_not include(budget1.starts_on)
      expect(budgets_manager.purged_budgets.map(&:value)).to_not include(budget1.value)
      expect{ budget1.reload }.to_not raise_error(ActiveRecord::RecordNotFound)

      expect{ budget2.reload }.to raise_error(ActiveRecord::RecordNotFound) # still removes non-po budget
    end
  end # open budget creation

  # - - current-start - b1 - b2 - current-end - - b3 - TO - - current-start - - - - - current-end - - b3 -
  context 'when managing budgets after creation of a ranged budget' do
    let!(:budget1) { create(:customer_budget, :random, customer_profile: customer, starts_on: budget_date + 2.months, ends_on: budget_date + 3.months) }
    let!(:budget2) { create(:customer_budget, :random, customer_profile: customer, starts_on: budget_date + 3.months, ends_on: budget_date + 5.months) }
    let!(:budget3) { create(:customer_budget, :random, customer_profile: customer, starts_on: budget_date + 15.months, ends_on: [budget_date + 17.months, nil].sample) }

    let!(:budget_params) do
      {
        starts_on: budget_date,
        ends_on: (budget_date + 10.months),
        frequency: 'monthly'
      }
    end

    it 'purges the budgets within the date range' do
      budgets_manager = Customers::Budgets::ManageFutureBudgets.new(customer: customer, budget_params: budget_params).call

      expect(budgets_manager).to be_success
      expect(budgets_manager.purged_budgets.map(&:starts_on)).to include(*[budget1, budget2].map(&:starts_on))
      expect(budgets_manager.purged_budgets.map(&:value)).to include(*[budget1, budget2].map(&:value))

      expect(budgets_manager.purged_budgets.map(&:starts_on)).to_not include(budget3.starts_on)
      expect(budgets_manager.purged_budgets.map(&:value)).to_not include(budget3.value)

      expect{ budget1.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ budget2.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ budget3.reload }.to_not raise_error(ActiveRecord::RecordNotFound) # not within range
    end

    it 'only purges budgets not connected to a PO within range' do
      budget2.update_column(:customer_purchase_order_id, customer_purchase_order.id)

      budgets_manager = Customers::Budgets::ManageFutureBudgets.new(customer: customer, budget_params: budget_params).call

      expect(budgets_manager).to be_success
      expect(budgets_manager.purged_budgets.map(&:starts_on)).to include(budget1.starts_on)
      expect(budgets_manager.purged_budgets.map(&:value)).to include(budget1.value)

      expect(budgets_manager.purged_budgets.map(&:starts_on)).to_not include(budget2.starts_on, budget3.starts_on)
      expect(budgets_manager.purged_budgets.map(&:value)).to_not include(budget2.value, budget3.value)

      expect{ budget1.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ budget2.reload }.to_not raise_error(ActiveRecord::RecordNotFound) # connected to PO
      expect{ budget3.reload }.to_not raise_error(ActiveRecord::RecordNotFound) # not within range
    end
  end # ranged budgets creation

end