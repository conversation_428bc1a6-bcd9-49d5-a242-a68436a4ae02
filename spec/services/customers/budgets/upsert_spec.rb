require 'rails_helper'

RSpec.describe Customers::Budgets::Upsert, type: :service, customers: true, budgets: true do

  let!(:budget_date) { Date.today.beginning_of_month }

  let(:customer) { create(:customer_profile, :random) }
  let(:suburb) { create(:suburb, :random) }
  let(:budget_params) do
    {
      value: rand(10_000...20_000),
      starts_on: budget_date,
      ends_on: budget_date + 3.months,
      frequency: CustomerBudget::VALID_BUDGET_FREQUENCIES.sample
    }
  end

  before do
    future_budget_manager = double(Customers::Budgets::ManageFutureBudgets)
    allow(Customers::Budgets::ManageFutureBudgets).to receive(:new).and_return(future_budget_manager)
    allow(future_budget_manager).to receive(:call).and_return(true)
  end

  it 'creates a new budget if params are passed' do
    budget_creator = Customers::Budgets::Upsert.new(customer: customer, budget_params: budget_params).call

    expect(budget_creator).to be_success
    created_budget = budget_creator.budget

    expect(created_budget.value).to eq(budget_params[:value])
    expect(created_budget.starts_on).to eq(budget_params[:starts_on].beginning_of_month)
    expect(created_budget.ends_on).to eq(budget_params[:ends_on].end_of_month)
    expect(created_budget.frequency).to eq(budget_params[:frequency])
  end

  # only run is Test as we want to test how the budget creation works in Live setting
  it 'calls service object to manage future budgets' do
    expect(Customers::Budgets::ManageFutureBudgets).to receive(:new).with(customer: customer, budget_params: budget_params, current_budget: anything) # current budget is upserted

    budget_upserter = Customers::Budgets::Upsert.new(customer: customer, budget_params: budget_params).call
    expect(budget_upserter).to be_success
  end

  context 'with a passed in purchase order' do
    let!(:customer_purchase_order) { create(:customer_purchase_order, :random, customer_profile: customer) }

    it 'creates purchase order specific budget' do
      budget_params_with_purchase_order = budget_params.merge({ customer_purchase_order_id: customer_purchase_order.id })
      budget_creator = Customers::Budgets::Upsert.new(customer: customer, budget_params: budget_params_with_purchase_order).call

      expect(budget_creator).to be_success

      created_budget = budget_creator.budget
      expect(created_budget.customer_purchase_order).to eq(customer_purchase_order)
    end
  end

  context 'errors' do
    it 'errors out if customer is missing' do
      budget_creator = Customers::Budgets::Upsert.new(customer: nil, budget_params: budget_params).call

      expect(budget_creator).to_not be_success
      expect(budget_creator.errors).to include('Customer is missing')
    end

    it 'does not create a new budget if budget data is missing' do
      budget_creator = Customers::Budgets::Upsert.new(customer: customer, budget_params: {}).call

      expect(budget_creator).to_not be_success
      expect(budget_creator.errors).to include('Cannot create/update budget without data')
    end

    it 'errors out if budget dates are mismatched' do
      invalid_budget_params = budget_params.merge({ ends_on: budget_date - 3.months })
      budget_creator = Customers::Budgets::Upsert.new(customer: customer, budget_params: invalid_budget_params).call

      expect(budget_creator).to_not be_success
      expect(budget_creator.errors).to include('Must end after it starts')
    end
  end

  context 'With exsisting budgets' do
    let!(:budget) { create(:customer_budget, :random, customer_profile: customer, starts_on: budget_date) }

    it 'updates the budget' do
      update_budget_params = {
        value: rand(10_000...20_000),
        starts_on: (budget_date + 2.months).beginning_of_month,
        ends_on: (budget_date + 3.months).end_of_month,
        frequency: CustomerBudget::VALID_BUDGET_FREQUENCIES.sample
      }
      budget_updator = Customers::Budgets::Upsert.new(customer: customer, budget: budget, budget_params: update_budget_params).call

      expect(budget_updator).to be_success
      updated_budget = budget_updator.budget

      expect(updated_budget.id).to eq(budget.id)
      expect(updated_budget.value).to eq(update_budget_params[:value])
      expect(updated_budget.starts_on).to eq(update_budget_params[:starts_on])
      expect(updated_budget.ends_on).to eq(update_budget_params[:ends_on])
      expect(updated_budget.frequency).to eq(update_budget_params[:frequency])
    end

    it 'updates the budgets based on the same starts_on if not passed in' do
      update_budget_params = {
        value: rand(10_000...20_000),
        starts_on: [budget_date, budget_date.to_s].sample,
        frequency: CustomerBudget::VALID_BUDGET_FREQUENCIES.sample
      }
      budget_updator = Customers::Budgets::Upsert.new(customer: customer, budget_params: update_budget_params).call

      expect(budget_updator).to be_success
      updated_budget = budget_updator.budget

      expect(updated_budget.id).to eq(budget.id) # existing budget is updated
      expect(updated_budget.value).to eq(update_budget_params[:value])
      expect(updated_budget.starts_on).to eq(budget.starts_on) # does not change
      expect(updated_budget.frequency).to eq(update_budget_params[:frequency])
    end

    it 'updates the budgets with the same from and to date if not passed in' do
      update_budget_params = {
        value: rand(10_000...20_000),
        starts_on: [budget_date, budget_date.to_s].sample,
        ends_on: budget.starts_on + 10.months,
        frequency: CustomerBudget::VALID_BUDGET_FREQUENCIES.sample
      }
      budget_updator = Customers::Budgets::Upsert.new(customer: customer, budget_params: update_budget_params).call

      expect(budget_updator).to be_success
      updated_budget = budget_updator.budget

      expect(updated_budget.id).to eq(budget.id) # existing budget is updated
      expect(updated_budget.value).to eq(update_budget_params[:value])
      expect(updated_budget.starts_on).to eq(budget.starts_on) # does not change
      expect(updated_budget.ends_on).to eq(update_budget_params[:ends_on].end_of_month) # does not change
      expect(updated_budget.frequency).to eq(update_budget_params[:frequency])
    end

    it 'does not calls service object to manage future budgets when updating an existing budget' do
      expect(Customers::Budgets::ManageFutureBudgets).to_not receive(:new)

      update_budget_params = {
        value: rand(10_000...20_000),
        starts_on: (budget_date + 2.months).beginning_of_month,
        ends_on: (budget_date + 3.months).end_of_month,
        frequency: CustomerBudget::VALID_BUDGET_FREQUENCIES.sample
      }
      budget_updator = Customers::Budgets::Upsert.new(customer: customer, budget: budget, budget_params: update_budget_params).call

      expect(budget_updator).to be_success
    end

    it 'creates a new customer budget for a different from' do
      budget_params = {
        value: budget.value,
        starts_on: budget.starts_on + 10.months,
        frequency: budget.frequency
      }
      budget_creator = Customers::Budgets::Upsert.new(customer: customer, budget_params: budget_params).call

      expect(budget_creator).to be_success
      created_budget = budget_creator.budget

      expect(created_budget.id).to_not eq(budget.id) # new budget created
      expect(created_budget.value).to eq(budget_params[:value])
      expect(created_budget.starts_on).to eq(budget_params[:starts_on])
      expect(created_budget.frequency).to eq(budget_params[:frequency])
    end
  end # with existing budgets
end
