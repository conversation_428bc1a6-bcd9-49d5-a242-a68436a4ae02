require 'rails_helper'

RSpec.describe Customers::Emails::SendOrderInvoiceEmail, type: :service, emails: true, customers: true, invoices: true, notifications: true do
  include Rails.application.routes.url_helpers

  subject { Customers::Emails::SendOrderInvoiceEmail.new(customer: customer, invoice: invoice).call }

  let!(:credit_card) { create(:credit_card, :random, :on_account_card) }
  let!(:customer) { create(:customer_profile, :random, :with_user, :with_flags ) }

  let!(:invoice) { create(:invoice, :random) }
  let!(:order) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: invoice, credit_card: credit_card) }

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :accounts_email).and_return('accounts-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
  end

  it 'returns the sent email' do
    invoice_sender = subject

    expect(invoice_sender).to be_success
    expect(invoice_sender.sent_notification).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Customers::Emails::SendOrderInvoiceEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )

    invoice_sender = subject
    expect(invoice_sender).to be_success
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject:  "YORDAR: Your invoice - ##{invoice.number}",
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )

    subject
  end

  it 'sends the email to the correct customer recipients (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: customer.email_recipient,
      subject: anything,
      cc: 'accounts-email',
      email_options: anything,
      email_variables: anything,
      attachments: anything
    )

    subject
  end

  context 'with billing details' do
    let!(:billing_details) { create(:billing_details, :random, customer_profile: customer) }

    it 'sends the email to the correct  billing details' do
      expect(::Emails::Send).to receive(:new).with(template_name: anything,
        recipient: billing_details.email,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )

      subject
    end
  end

  it 'sends an email with the correct email options' do
    email_ref = "customer-invoice-#{invoice.id}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: customer.id, ref: email_ref },
        email_variables: anything,
        attachments: anything
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct email data' do
      expected_email_variables = {
        invoice: anything,
        account_managers: anything,
        accounting_software: anything,

        first_name: customer.email_salutation,
        header_color: :pink,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    it 'sends email with the correct invoice details' do
      expected_invoice_details = {
        number: invoice.number,
        paid_by_credit_card: false,
        pdf_url: nil,
        payment_url: pay_invoice_url(invoice, host: yordar_credentials(:default_host)),
        with_invoice_spreadsheet: false
      }
      expected_email_variables = {
        first_name: anything,
        header_color: anything,
        account_managers: anything,
        accounting_software: anything,
        
        invoice: deep_struct(expected_invoice_details),
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    it 'sends email with the correct customer accounting software' do
      accounting_software = CustomerFlags::VALID_ACCOUNTING_SOFTWARES.sample
      customer.customer_flags.update_column(:accounting_software, accounting_software)
      expected_invoice_details = {
        number: invoice.number,
        paid_by_credit_card: false,
        pdf_url: nil,
        payment_url: pay_invoice_url(invoice, host: yordar_credentials(:default_host)),
        with_invoice_spreadsheet: false
      }
      expected_email_variables = {
        first_name: anything,
        header_color: anything,
        account_managers: anything,        
        invoice: anything,

        accounting_software: accounting_software,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    context 'with an order paid via a credit card (non pay on account)' do
      before do
        credit_card.update_column(:pay_on_account, false)
      end

      it 'sends email with the correct invoice payment details' do
        expected_invoice_details = {
          number: invoice.number,
          paid_by_credit_card: true,
          pdf_url: nil,
          payment_url: pay_invoice_url(invoice, host: yordar_credentials(:default_host)),
          with_invoice_spreadsheet: false
        }
        expected_email_variables = {
          first_name: anything,        
          header_color: anything,
          account_managers: anything,
          accounting_software: anything,
          
          invoice: deep_struct(expected_invoice_details),
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            cc: anything,
            email_options: anything,
            email_variables: expected_email_variables,
            attachments: anything
          )

        subject
      end
    end

    context 'with invoice documents' do
      let!(:document1) { create(:document, :random, kind: 'tax_invoice_spreadsheet', documentable: invoice) }
      let!(:document2) { create(:document, :random, kind: 'tax_invoice', documentable: invoice) }

      it 'sends email with the invoice PDF url' do
        expected_invoice_details = {
          number: invoice.number,
          paid_by_credit_card: false,
          pdf_url: document2.url,
          payment_url: pay_invoice_url(invoice, host: yordar_credentials(:default_host)),
          with_invoice_spreadsheet: false
        }
        expected_email_variables = {
          first_name: anything,        
          header_color: anything,
          account_managers: anything,
          accounting_software: anything,
          
          invoice: deep_struct(expected_invoice_details),
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            cc: anything,
            email_options: anything,
            email_variables: expected_email_variables,
            attachments: anything
          )

        subject
      end
    end # with invoice documents
  end # email variables

  context 'email attachments' do
    let!(:document1) { create(:document, :random, kind: 'tax_invoice_spreadsheet', documentable: invoice) }
    let!(:document2) { create(:document, :random, kind: 'tax_invoice', documentable: invoice) }

    it 'sends email with the invoice documents' do
      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: anything,
          attachments: [document1, document2]
        )

      subject
    end

    it 'sends email with the passed in documents' do
      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: anything,
          attachments: [document1]
        )

      email_sender = Customers::Emails::SendOrderInvoiceEmail.new(customer: customer, invoice: invoice, documents: [document1]).call
      expect(email_sender).to be_success
    end
  end

  context 'with Notification Preferences' do
    let!(:notification_preference) { create(:notification_preference, account: customer, template_name: Customers::Emails::SendOrderInvoiceEmail::EMAIL_TEMPLATE) }

    it 'send email to the prefered email recipients' do
      notification_preference.update_column(:email_recipients, 'prefered-email-recipients')

      expect(::Emails::Send).to receive(:new).with(template_name: anything,
        template_name: anything,
        recipient: 'prefered-email-recipients',
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )

      subject
    end

    it 'send email with the prefered email salutation' do
      notification_preference.update_column(:salutation, 'prefered-email-salutation')

      expected_email_variables = {
        invoice: anything,
        header_color: anything,
        account_managers: anything,
        accounting_software: anything,

        first_name: 'prefered-email-salutation',        
      }
      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    it 'doesn\'t send email if the preference is set to be inactive' do
      notification_preference.update_column(:active, false)

      expect(Emails::Send).to_not receive(:new)
      subject
    end
  end

  context 'with an invoice email adready sent' do
    let!(:email) { create(:email, :random, fk_id: customer.id, ref: "customer-invoice-#{invoice.id}") }

    it 'returns with error and does not send another email' do
      expect(Emails::Send).to_not receive(:new)

      invoice_sender = subject
      expect(invoice_sender).to_not be_success
      expect(invoice_sender.errors).to include("Order Invoice email already sent for #{customer.id} - ##{invoice.id}")
    end

    it 'resends the email if request with the regenerate argument' do
      invoice_sender = Customers::Emails::SendOrderInvoiceEmail.new(customer: customer, invoice: invoice, is_regenerate: true).call

      expect(invoice_sender).to be_success
      expect(invoice_sender.sent_notification).to eq('sent-email') # from above mock
    end
  end

  context 'with email sender failure' do
    before do
      unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
      allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

      # mock error logger
      allow_any_instance_of(Customers::Emails::SendOrderInvoiceEmail).to receive(:log_errors).and_return(true)
    end

    it 'logs a notification error with the email sender errors' do
      invoice_sender = Customers::Emails::SendOrderInvoiceEmail.new(customer: customer, invoice: invoice)

      expected_error_message = 'Failed to send customer invite email.'
      expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
      expect(invoice_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

      invoice_sender.call
    end
  end # email sender error

end