require 'rails_helper'

RSpec.describe Customers::Emails::SendInvoiceReceiptEmail, type: :service, emails: true, customers: true, orders: true, invoices: true, notifications: true do

  subject { Customers::Emails::SendInvoiceReceiptEmail.new(customer: customer, invoice: invoice).call }

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:invoice) { create(:invoice, :random, payment_status: 'paid') }
  let!(:order1) { create(:order, :confirmed, customer_profile: customer, update_with_invoice: true, invoice: invoice) }
  let!(:order2) { create(:order, :confirmed, customer_profile: customer, update_with_invoice: true, invoice: invoice) }

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    # mock invoice receipt PDF generator
    invoice_receipt_generator = double(Documents::Generate::TaxInvoice)
    allow(Documents::Generate::TaxInvoice).to receive(:new).and_return(invoice_receipt_generator)
    tax_receipt_document = OpenStruct.new(url: 'tax-receipt-document-url')
    allow(invoice_receipt_generator).to receive(:call).and_return(tax_receipt_document)

    # mock order document PDF generator
    order_document_generator = double(Documents::Generate::CustomerOrderDetails)
    allow(Documents::Generate::CustomerOrderDetails).to receive(:new).and_return(order_document_generator)
    order_document = OpenStruct.new(url: 'order-document-url')
    allow(order_document_generator).to receive(:call).and_return(order_document)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :accounts_email).and_return('accounts-email')
  end

  it 'returns the sent email' do
    tax_receipt_sender = subject

    expect(tax_receipt_sender).to be_success
    expect(tax_receipt_sender.sent_notification).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Customers::Emails::SendInvoiceReceiptEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything,
      )

    tax_receipt_sender = subject
    expect(tax_receipt_sender).to be_success
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "YORDAR: Your tax receipt - Invoice ##{invoice.number}",
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything,
      )

    subject
  end

  it 'sends the email to the correct recipients (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: customer.email_recipient,
      subject: anything,
      cc: 'accounts-email',
      email_options: anything,
      email_variables: anything,
      attachments: anything,
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{Customers::Emails::SendInvoiceReceiptEmail::EMAIL_TEMPLATE}-#{invoice.id}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: customer.id, ref: email_ref },
        email_variables: anything,
        attachments: anything,
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct email data' do
      expected_email_variables = {
        firstname: customer.email_salutation,
        invoice: anything,
        account_managers: anything,

        header_color: :pink
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything,
        )

      subject
    end


    it 'sends email with the correct invoice data' do
      expected_invoice_data = {
        id: invoice.id,
        number: invoice.number,
        pdf_url: 'tax-receipt-document-url',
      }

      expected_email_variables = {
        firstname: anything,
        header_color: anything,
        account_managers: anything,

        invoice: deep_struct(expected_invoice_data)
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything,
        )

      subject
    end

    context 'attachment documents' do
      it 'makes a request to generate tax receipt document for the invoice' do
        expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice_receipt')

        subject
      end

      it 'makes a request to generate customer order details documents per each invoice order' do
        expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order1)
        expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order2)
        
        subject
      end

      it 'doesn\'t request to generate an already generated customer order details document' do
        create(:document, :random, kind: 'customer_order_details', documentable: order2)

        expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order1)
        expect(Documents::Generate::CustomerOrderDetails).to_not receive(:new).with(order: order2)
        
        subject
      end
    end
  end

  context 'errors' do
    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Customers::Emails::SendInvoiceReceiptEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        tax_receipt_email_sender = Customers::Emails::SendInvoiceReceiptEmail.new(customer: customer, invoice: invoice)

        expected_error_message = "Failed to send invoice tax receipt to customer #{customer.id} - ##{invoice.id}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(tax_receipt_email_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

        tax_receipt_email_sender.call
      end
    end # email sender error
  end

end