require 'rails_helper'

RSpec.describe Customers::Emails::SendOrderQuoteEmail, type: :service, emails: true, quotes: true, customers: true, notifications: true do
  include Rails.application.routes.url_helpers
  include ActionView::Helpers::NumberHelper

  subject { Customers::Emails::SendOrderQuoteEmail.new(order: order, customer: customer, document: quote_document, quote_emails: quote_emails, quote_message: quote_message).call }

  let!(:customer) { create(:customer_profile, :random, :with_user ) }
  let!(:order) { create(:order, :confirmed, customer_profile: customer) }
  let!(:order_quote_document) { create(:document, :random, kind: 'customer_order_quote', documentable: order, version: 3) }
  let!(:quote_document) { order_quote_document }
  let!(:quote_emails) { '' }
  let!(:quote_message) { '' }

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    # mock document generator
    quote_document_generator = double(Documents::Generate::CustomerOrderDetails)
    allow(Documents::Generate::CustomerOrderDetails).to receive(:new).and_return(quote_document_generator)
    allow(quote_document_generator).to receive(:call).and_return(order_quote_document)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:random_salt).and_return(SecureRandom.hex(7))

    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)
  end

  it 'returns the sent email' do
    order_quote_sender = subject

    expect(order_quote_sender).to be_success
    expect(order_quote_sender.sent_notification).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Customers::Emails::SendOrderQuoteEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )

    order_quote_sender = subject
    expect(order_quote_sender).to be_success
  end

  it 'send the email with the appropriate subject' do
    expected_email_subject = "YORDAR: Your quote for order ##{order.id}"
    expected_email_subject += " (Ver.#{quote_document.version})"

    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: expected_email_subject,
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{Customers::Emails::SendOrderQuoteEmail::EMAIL_TEMPLATE}-#{order.id}-#{Time.zone.now.to_s(:date)}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: customer.id, ref: email_ref },
        email_variables: anything,
        attachments: anything
      )

    subject
  end

  it 'sends the email to the correct recipients (and cc)' do
    expect(::Emails::Send).to receive(:new).with(
      template_name: anything,
      recipient: customer.email_recipient,
      subject: anything,
      cc: 'orders-email',
      email_options: anything,
      email_variables: anything,
      attachments: anything
    )

    subject
  end

  it 'attaches the passed in order quote document' do
    expect(::Emails::Send).to receive(:new).with(
      template_name: anything,
      recipient: anything,
      subject: anything,
      cc: anything,
      email_options: anything,
      email_variables: anything,
      attachments: [quote_document]
    )

    subject
  end

  context 'when document is not passed in' do
    let!(:quote_document) { nil }

    it 'generates and attaches an order quote document' do
      email_ref = "#{Customers::Emails::SendOrderQuoteEmail::EMAIL_TEMPLATE}-#{order.id}-#{Time.zone.now.to_s(:date)}"
      expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order, reference: email_ref, variation: 'quote')

      expect(::Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: [order_quote_document]
      )

      subject
    end
  end

  context 'with quote emails' do
    let!(:quote_emails) { 2.times.map{|_| Faker::Internet.email }.join(';') }

    it 'sends the email to the correct recipients (and cc)' do
      expect(::Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: quote_emails,
        subject: anything,
        cc: 'orders-email',
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )

      subject
    end

    context 'when document is not passed in' do
      let!(:quote_document) { nil }

      it 'does not generate a new document, but picks up the already attached order quote document' do
        expect(Documents::Generate::CustomerOrderDetails).to_not receive(:new)
        
        expect(::Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: anything,
          attachments: [order_quote_document]
        )

        subject
      end
    end
  end

  context 'email variables' do
    it 'sends email with the correct email data' do
      expected_email_variables = {
        order: anything,
        supplier_grouped_order_lines: anything,
        totals: anything,
        customer: anything,
        is_customer_email: anything,
        account_managers: anything,

        firstname: customer.email_salutation,
        quote_message: quote_message,
        header_color: :pink,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    it 'sends email with the correct customer data' do
      expected_customer_data = {
        name: customer.name,
        email: customer.user.email
      }
      expected_email_variables = {
        firstname: anything,
        quote_message: anything,
        header_color: anything,
        order: anything,
        supplier_grouped_order_lines: anything,
        totals: anything,
        account_managers: anything,
        
        customer: deep_struct(expected_customer_data),
        is_customer_email: true, # cause quote emails is blank
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    it 'sends email with the correct order data' do
      md5_hash = Digest::MD5.hexdigest(order.id.to_s + customer.id.to_s + yordar_credentials(:random_salt))

      expected_order_manage_urls = %i[edit approve reject].map do |mode|
        path = order_approve_or_reject_url(order_id: order.id, profile_type: 'customer', profile_id: customer.id, mode: mode, hashed_value: md5_hash, host: yordar_credentials(:default_host))
        url_shortner = Shortener::ShortenedUrl.generate(path)
        url = shortened_url(url_shortner.unique_key, host: yordar_credentials(:default_host))
        [mode, url]
      end.to_h

      expected_order_data = {
        id: order.id,
        name: order.name,
        link: order_show_url(order, host: yordar_credentials(:default_host)),
        date: order.delivery_at.to_s(:full),
        delivery_address: order.delivery_address_arr.join(', '),
        customer_total: number_to_currency(order.customer_total),
        quote_pdf_url: quote_document.url,
        edit_url: expected_order_manage_urls[:edit],
        confirm_url: expected_order_manage_urls[:approve],
        reject_url: expected_order_manage_urls[:reject],
      }

      expected_email_variables = {
        firstname: anything,
        quote_message: anything,
        header_color: anything,
        customer: anything,
        is_customer_email: anything,
        supplier_grouped_order_lines: anything,
        totals: anything,
        account_managers: anything,

        order: deep_struct(expected_order_data)        
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    context 'with order lines' do
      let!(:supplier1) { create(:supplier_profile, :random, :with_user) }
      let!(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier1) }
      let!(:order_line1) { create(:order_line, :random, menu_item: menu_item1, supplier_profile: supplier1, order: order) }

      let!(:supplier2) { create(:supplier_profile, :random, :with_user) }
      let!(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier2) }
      let!(:order_line2) { create(:order_line, :random, menu_item: menu_item2, supplier_profile: supplier2, order: order) }

      it 'sends email with the correct order lines data' do
        expected_order_lines_data = order.reload.order_lines.group_by(&:supplier_profile).map do |supplier, supplier_order_lines|
          hashed_order_lines = supplier_order_lines.first(5).map do |order_line|
            {
              name: order_line.name.strip.truncate(50, omission: ' ...'),
              quantity: order_line.quantity,
              price: number_to_currency(order_line.price),
              image: nil,
              name: order_line.name.strip.truncate(50, omission: ' ...'),
              quantity: order_line.quantity,
              price: number_to_currency(order_line.price),
              image: nil,
              description: order_line.menu_item.description,
              dietary_preferences: [],
            }
          end
          {
            supplier: {
              name: supplier.company_name.strip,
              image: nil,
            },
            has_more: false,
            order_lines: hashed_order_lines
          }
        end

        expected_email_variables = {
          firstname: anything,
          quote_message: anything,
          header_color: anything,
          customer: anything,
          is_customer_email: anything,
          order: anything,
          totals: anything,
          account_managers: anything,         

          supplier_grouped_order_lines: deep_struct(expected_order_lines_data)        
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            cc: anything,
            email_options: anything,
            email_variables: expected_email_variables,
            attachments: anything
          )

        subject
      end
    end

    it 'sends email with the correct order totals data' do
      expected_totals_data = {
        customer_subtotal: number_to_currency(order.customer_subtotal),
        order_discount: nil,
        customer_delivery: number_to_currency(order.customer_delivery),
        customer_gst: number_to_currency(order.customer_gst),
        customer_surcharge: nil,
        customer_topup: nil,
        customer_total: number_to_currency(order.customer_total),
      }

      expected_email_variables = {
        firstname: anything,
        quote_message: anything,
        header_color: anything,
        customer: anything,
        is_customer_email: anything,
        supplier_grouped_order_lines: anything,
        order: anything,
        account_managers: anything,

        totals: deep_struct(expected_totals_data)        
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end   
  end

  context 'errors' do
    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Customers::Emails::SendOrderQuoteEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        invite_email_sender = Customers::Emails::SendOrderQuoteEmail.new(order: order, customer: customer, document: quote_document, quote_emails: quote_emails, quote_message: quote_message)

        expected_error_message = "Failed to send order ##{order.id} quote email to #{customer.email_recipient}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(invite_email_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

        invite_email_sender.call
      end
    end # email sender error
  end

end