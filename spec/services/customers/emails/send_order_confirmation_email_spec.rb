require 'rails_helper'

RSpec.describe Customers::Emails::SendOrderConfirmationEmail, type: :service, emails: true, customers: true, notifications: true do
  include Rails.application.routes.url_helpers
  include ActionView::Helpers::NumberHelper

  subject { Customers::Emails::SendOrderConfirmationEmail.new(customer: customer, order: order).call }

  let!(:customer) { create(:customer_profile, :random, :with_user, uuid: SecureRandom.uuid ) }
  let!(:order) { create(:order, :confirmed, customer_profile: customer) }

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
  end

  it 'returns the sent email' do
    order_confirmation_sender = subject

    expect(order_confirmation_sender).to be_success
    expect(order_confirmation_sender.sent_notification).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Customers::Emails::SendOrderConfirmationEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        email_options: anything,
        email_variables: anything,
      )

    order_confirmation_sender = subject
    expect(order_confirmation_sender).to be_success
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "YORDAR: Your order ##{order.id} was confirmed",
        email_options: anything,
        email_variables: anything,
      )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{Customers::Emails::SendOrderConfirmationEmail::EMAIL_TEMPLATE}-#{order.id}-#{order.version_ref}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        email_options: { fk_id: customer.id, ref: email_ref },
        email_variables: anything,
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct email data' do
      expected_email_variables = {
        profile_url: anything,
        order: anything,
        supplier_grouped_order_lines: anything,
        totals: anything,
        account_managers: anything,

        first_name: customer.email_salutation,        
        header_color: :pink,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          email_options: anything,
          email_variables: expected_email_variables,
        )

      subject
    end

    it 'sends email with the correct customer profile url' do
      expected_email_variables = {
        first_name: anything,        
        header_color: anything,
        order: anything,
        supplier_grouped_order_lines: anything,
        totals: anything,
        account_managers: anything,

        profile_url: customer_profile_url(host: yordar_credentials(:default_host)),
      }

      expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        email_options: anything,
        email_variables: expected_email_variables,
      )

      subject
    end

    it 'sends email with the correct order data' do
      expected_order_data = {
        id: order.id,
        name: order.name,
        link: order_show_url(order, host: yordar_credentials(:default_host)), # default normal order from factory
        delivery_address: order.delivery_address_arr.join(', '),
        type: 'one off', # default from factory
        day: '', # non-recurrent order from factory
        recurrent_type: order.recurrent_type,
        date: order.delivery_at.to_s(:full)
      }

      expected_email_variables = {
        first_name: anything,        
        header_color: anything,
        profile_url: anything,
        supplier_grouped_order_lines: anything,
        totals: anything,
        account_managers: anything,

        order: deep_struct(expected_order_data)
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          email_options: anything,
          email_variables: expected_email_variables,
        )

      subject
    end

    context 'with order lines' do
      before do
        # needed for supplier creation
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)
      end

      let!(:supplier1) { create(:supplier_profile, :random, :with_user) }
      let!(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier1) }
      let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1, menu_item: menu_item1) }

      let!(:supplier2) { create(:supplier_profile, :random, :with_user) }
      let!(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier2) }
      let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2, menu_item: menu_item2) }

      it 'sends email with the correct order lines data' do
        expected_order_lines_data = order.reload.order_lines.group_by(&:supplier_profile).map do |supplier, supplier_order_lines|
          hashed_order_lines = supplier_order_lines.first(5).map do |order_line|
            {
              name: order_line.name.strip.truncate(50, omission: ' ...'),
              quantity: order_line.quantity,
              price: number_to_currency(order_line.price),
              image: nil,
              description: order_line.menu_item.description,
              dietary_preferences: []
            }
          end
          {
            supplier: {
              name: supplier.company_name.strip,
              image: nil,
            },
            has_more: false,
            order_lines: hashed_order_lines
          }
        end

        expected_email_variables = {
          first_name: anything,        
          header_color: anything,
          profile_url: anything,
          order: anything,
          totals: anything,
          account_managers: anything,

          supplier_grouped_order_lines: deep_struct(expected_order_lines_data),
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            email_options: anything,
            email_variables: expected_email_variables,
          )

        subject
      end
    end # with order lines

    context 'with order totals' do
      before do
        order.update_columns(
          customer_subtotal: rand(100..199),
          discount: rand(10..22),
          customer_delivery: rand(30..50),
          customer_gst: rand(10..19),
          customer_surcharge: rand(5..9),
          customer_topup: [0, nil].sample,
          customer_total: rand(200..300),
        )
      end

      it 'sends email with the correct order totals data' do
        expected_totals_data = {
          customer_subtotal: number_to_currency(order.customer_subtotal),
          order_discount: number_to_currency(order.discount),
          customer_delivery: number_to_currency(order.customer_delivery),
          customer_gst: number_to_currency(order.customer_gst),
          customer_surcharge: number_to_currency(order.customer_surcharge),
          customer_topup: nil,
          customer_total: number_to_currency(order.customer_total),
        }

        expected_email_variables = {
          first_name: anything,        
          header_color: anything,
          profile_url: anything,
          order: anything,
          supplier_grouped_order_lines: anything,
          account_managers: anything,

          totals: deep_struct(expected_totals_data)
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            email_options: anything,
            email_variables: expected_email_variables,
          )

        subject
      end
    end # with order totals
  end # email variables

  context 'with Notification Preferences' do
    let!(:notification_preference) { create(:notification_preference, account: customer, template_name: Customers::Emails::SendOrderConfirmationEmail::EMAIL_TEMPLATE) }

    it 'send email to the prefered email recipients' do
      notification_preference.update_column(:email_recipients, 'prefered-email-recipients')

      expect(::Emails::Send).to receive(:new).with(template_name: anything,
        template_name: anything,
        recipient: 'prefered-email-recipients',
        subject: anything,
        email_options: anything,
        email_variables: anything
      )

      subject
    end

    it 'send email with the prefered email salutation' do
      notification_preference.update_column(:salutation, 'prefered-email-salutation')

      expected_email_variables = {
        header_color: anything,
        profile_url: anything,
        order: anything,
        supplier_grouped_order_lines: anything,
        totals: anything,
        account_managers: anything,

        first_name: 'prefered-email-salutation',        
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end

    it 'doesn\'t send email if the preference is set to be inactive' do
      notification_preference.update_column(:active, false)

      expect(Emails::Send).to_not receive(:new)
      subject
    end
  end

  context 'errors' do
    it 'returns with error if email is already sent (and does not send another email)' do
      email_ref = "#{Customers::Emails::SendOrderConfirmationEmail::EMAIL_TEMPLATE}-#{order.id}-#{order.version_ref}"
      create(:email, :random, fk_id: customer.id, ref: email_ref)

      expect(Emails::Send).to_not receive(:new)

      invite_email_sender = subject
      expect(invite_email_sender).to_not be_success
      expect(invite_email_sender.errors).to include('Email already sent!')
    end

    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Customers::Emails::SendOrderConfirmationEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        invite_email_sender = Customers::Emails::SendOrderConfirmationEmail.new(customer: customer, order: order)

        expected_error_message = "Failed to send order confirmation email to customer #{customer.id} - ##{order.id}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(invite_email_sender).to receive(:log_errors)#.with((exception: exception, message: error_message, sentry: true, error_objects: { order_id: order.id, customer_id: customer.id }))

        invite_email_sender.call
      end
    end # email sender error
  end

end