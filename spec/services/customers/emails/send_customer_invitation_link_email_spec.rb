require 'rails_helper'

RSpec.describe Customers::Emails::SendCustomerInvitationLinkEmail, type: :service, emails: true, customers: true, notifications: true do
  include Rails.application.routes.url_helpers

  subject { Customers::Emails::SendCustomerInvitationLinkEmail.new(customer: customer).call }

  let!(:customer) { create(:customer_profile, :random, :with_user, uuid: SecureRandom.uuid ) }

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
  end

  it 'returns the sent email' do
    tax_receipt_sender = subject

    expect(tax_receipt_sender).to be_success
    expect(tax_receipt_sender.sent_notification).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Customers::Emails::SendCustomerInvitationLinkEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
      )

    tax_receipt_sender = subject
    expect(tax_receipt_sender).to be_success
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: 'YORDAR: Your custom user invitation link',
        cc: anything,
        email_options: anything,
        email_variables: anything,
      )

    subject
  end

  it 'sends the email to the correct recipients (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: customer.email_recipient,
      subject: anything,
      cc: 'orders-email',
      email_options: anything,
      email_variables: anything,
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{Customers::Emails::SendCustomerInvitationLinkEmail::EMAIL_TEMPLATE}-#{customer.id}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: customer.id, ref: email_ref },
        email_variables: anything,
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct email data' do
      expected_email_variables = {
        invite_url: anything,
        account_managers: anything,

        first_name: customer.email_salutation,
        header_color: :pink
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
        )

      subject
    end

    it 'sends email with the correct invite url' do
      expected_invite_url = new_company_customer_registration_url(
        company_team_admin_code: customer.uuid,
        host: yordar_credentials(:default_host),
      )
      expected_email_variables = {
        first_name: anything,        
        header_color: :pink,
        account_managers: anything,
        
        invite_url: expected_invite_url,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
        )

      subject
    end
  end

  context 'errors' do
    it 'returns with error if email is already sent (and does not send another email)' do
      email_ref = "#{Customers::Emails::SendCustomerInvitationLinkEmail::EMAIL_TEMPLATE}-#{customer.id}"
      create(:email, :random, fk_id: customer.id, ref: email_ref)

      expect(Emails::Send).to_not receive(:new)

      invite_email_sender = subject
      expect(invite_email_sender).to_not be_success
      expect(invite_email_sender.errors).to include('Email already sent!')
    end

    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Customers::Emails::SendCustomerInvitationLinkEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        invite_email_sender = Customers::Emails::SendCustomerInvitationLinkEmail.new(customer: customer)

        expected_error_message = 'Failed to send customer invite email.'
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(invite_email_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

        invite_email_sender.call
      end
    end # email sender error
  end

end