require 'rails_helper'

RSpec.describe Customers::FetchCustomisedSupplier, type: :service, customers: true, suppliers: true do

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:supplier3) { create(:supplier_profile, :random) }

  let!(:customer) { create(:customer_profile, :random) }

  it 'returns favourites menu item ids and rate_cards' do
    customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: customer).call

    expect(customised_suppliers).to respond_to(:favourite_menu_item_ids)
    expect(customised_suppliers).to respond_to(:rate_cards)
  end

  it 'returns blank values by default' do
    customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: customer).call

    expect(customised_suppliers.favourite_menu_item_ids).to be_empty
    expect(customised_suppliers.rate_cards).to be_empty
  end

  it 'returns blank values for missing customer' do
    customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: nil).call

    expect(customised_suppliers.favourite_menu_item_ids).to be_empty
    expect(customised_suppliers.rate_cards).to be_empty
  end

  context 'with favourite menu items' do
    let!(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier1) }
    let!(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier2) }
    let!(:menu_item3) { create(:menu_item, :random, supplier_profile: supplier3) }

    let!(:favourite1) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item1) }
    let!(:favourite2) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item2) }

    it 'returns the favourite menu item ids for the customer' do
      customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: customer.reload).call

      expect(customised_suppliers.favourite_menu_item_ids).to include(menu_item1.id, menu_item2.id)
      expect(customised_suppliers.favourite_menu_item_ids).to_not include(menu_item3.id)
    end

    it 'returns the favourite menu item ids scoped to the passed in supplier' do
      customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: customer, supplier_id: supplier2.id).call

      expect(customised_suppliers.favourite_menu_item_ids).to include(menu_item2.id)
      expect(customised_suppliers.favourite_menu_item_ids).to_not include(menu_item1.id)
    end
  end

  context 'with a company' do
    let!(:company) { create(:company, :random) }

    before do
      customer.update_column(:company_id, company.id)
    end

    context 'with rate cards' do
      let!(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier1) }
      let!(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier2) }
      let!(:menu_item3) { create(:menu_item, :random, supplier_profile: supplier3) }

      let!(:rate_card1) { create(:rate_card, :random, menu_item: menu_item1, company: company) }
      let!(:rate_card2) { create(:rate_card, :random, menu_item: menu_item2) }
      let!(:rate_card3) { create(:rate_card, :random, menu_item: menu_item3, company: company) }

      it 'returns active rate cards for the customer' do
        customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: customer).call

        expect(customised_suppliers.rate_cards).to include(rate_card1, rate_card3)
        expect(customised_suppliers.rate_cards).to_not include(rate_card2)
      end

      it 'does not return a supplier id with a rate card of an archived menu item' do
        menu_item1.update_column(:archived_at, Time.zone.now)
        customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: customer).call

        expect(customised_suppliers.rate_cards).to include(rate_card3)
        expect(customised_suppliers.rate_cards).to_not include(rate_card1)
      end

      it 'returns the rate cards for the passed in supplier' do
        customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: customer, supplier_id: supplier1.id).call

        expect(customised_suppliers.rate_cards).to include(rate_card1)
        expect(customised_suppliers.rate_cards).to_not include(rate_card2, rate_card3)
      end

      it 'return blank rate card supplier ids if the customer does not belong to a company' do
        customer.update_column(:company_id, nil)
        customised_suppliers = Customers::FetchCustomisedSupplier.new(customer: customer).call

        expect(customised_suppliers.rate_cards).to be_empty
      end
    end # with rate cards
  end # with a company

end
