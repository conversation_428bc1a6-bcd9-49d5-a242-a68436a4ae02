require 'rails_helper'

RSpec.describe Customers::Quotes::AttachToOrder, type: :service, customer_quotes: true, orders: true do

  let!(:quote_form_data) do
    {
      occasion: Faker::Name.name,
      estimatedAttendees: rand(10..100),
      fullName: Faker::Name.name,
      phone: Faker::PhoneNumber.phone_number,
      company: Faker::Company.name,
      email: Faker::Internet.email
    }
  end

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :draft, customer_profile: customer) }
  let!(:customer_quote) { create(:customer_quote, :random, customer_profile: customer, form_data: quote_form_data) }

  it 'attaches the customer quote to the order' do
    order_quote_attacher = Customers::Quotes::AttachToOrder.new(order: order, quote: customer_quote).call

    expect(order_quote_attacher).to be_success
    updated_order = order_quote_attacher.order

    expect(updated_order.customer_quote).to be_present
    expect(updated_order.customer_quote).to eq(customer_quote)
  end

  it 'saves the quote order details against the order' do
    order_quote_attacher = Customers::Quotes::AttachToOrder.new(order: order, quote: customer_quote).call

    expect(order_quote_attacher).to be_success
    updated_order = order_quote_attacher.order

    Customers::Quotes::AttachToOrder::ORDER_FIELDS.each do |order_field, form_field|
      expect(updated_order.send(order_field)).to eq(quote_form_data[form_field])
    end
  end

  context 'errors' do
    it 'returns with errors if the order is not passed' do
      order_quote_attacher = Customers::Quotes::AttachToOrder.new(order: nil, quote: customer_quote).call

      expect(order_quote_attacher).to_not be_success
      expect(order_quote_attacher.errors).to include('Cannot attach quote without an order')
    end

    it 'returns with errors if the customer quote is not passed' do
      order_quote_attacher = Customers::Quotes::AttachToOrder.new(order: order, quote: nil).call

      expect(order_quote_attacher).to_not be_success
      expect(order_quote_attacher.errors).to include('Cannot attach a missing customer quote')
    end

    it 'returns with order customer is not the same as quote customer' do
      quote2 = create(:customer_quote, :random)
      order_quote_attacher = Customers::Quotes::AttachToOrder.new(order: order, quote: quote2).call

      expect(order_quote_attacher).to_not be_success
      expect(order_quote_attacher.errors).to include('You do not have access to this quote')
    end
  end

end