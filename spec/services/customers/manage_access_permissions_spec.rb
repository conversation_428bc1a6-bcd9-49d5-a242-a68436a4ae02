require 'rails_helper'

RSpec.describe Customers::ManageAccessPermissions, type: :service, customers: true, access_permissions: true do

  let!(:allowed_scopes) { AccessPermission::VALID_SCOPES }
  let!(:admin_customer) { create(:customer_profile, :random, company_team_admin: false) }

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:customer2) { create(:customer_profile, :random) }

  let!(:access_permissions) do
    [
      {
        customer_profile_id: customer1.id,
        scope: allowed_scopes.sample,
        active: true
      },
      {
        customer_profile_id: customer2.id,
        scope: allowed_scopes.sample,
        active: true
      }
    ]
  end

  it 'adds the new customers as adminable customers for the admin' do
    permissions_manager = Customers::ManageAccessPermissions.new(customer: admin_customer, access_permissions: access_permissions).call

    expect(permissions_manager).to be_success

    created_access_permissions = permissions_manager.access_permissions
    expect(created_access_permissions.size).to eq(2)
    expect(created_access_permissions.map(&:customer_profile)).to include(customer1, customer2)
    expect(created_access_permissions.map(&:scope)).to include(*access_permissions.map{|permission| permission[:scope] })
  end

  it 'tags the admin_customer as a company team admin' do
    permissions_manager = Customers::ManageAccessPermissions.new(customer: admin_customer, access_permissions: access_permissions)

    expect{ permissions_manager.call }.to change{ admin_customer.company_team_admin }.from(false).to(true)
  end

  context 'with an existing access permissions' do
    let!(:access_permission1) { create(:access_permission, :random, admin: admin_customer, customer_profile: customer1, scope: allowed_scopes.sample, active: true) }

    before do
      admin_customer.update_column(:company_team_admin, true)
    end

    it 'updates the existing access permissions (using passed in ID)' do
      access_permissions = [
        {
          id: access_permission1.id,
          customer_profile_id: customer1.id,
          scope: (allowed_scopes - [access_permission1.scope]).sample,
          active: false
        }
      ]
      permissions_manager = Customers::ManageAccessPermissions.new(customer: admin_customer, access_permissions: access_permissions).call

      expect(permissions_manager).to be_success

      updated_access_permissions = permissions_manager.access_permissions
      expect(updated_access_permissions.size).to eq(1)

      updated_access_permission = updated_access_permissions.first
      expect(updated_access_permission.id).to eq(access_permission1.id)
      expect(updated_access_permission.scope).to eq(access_permissions[0][:scope])
      expect(updated_access_permission.active).to eq(access_permissions[0][:active])
    end

    it 'updates the existing access permissions even without ID (matched on customer_profile_id - does not create duplicates)' do
      access_permissions = [
        {
          # id: access_permission1.id, # no ID sent
          customer_profile_id: customer1.id,
          scope: (allowed_scopes - [access_permission1.scope]).sample,
          active: false
        }
      ]
      permissions_manager = Customers::ManageAccessPermissions.new(customer: admin_customer, access_permissions: access_permissions).call

      expect(permissions_manager).to be_success

      updated_access_permissions = permissions_manager.access_permissions
      expect(updated_access_permissions.size).to eq(1)

      updated_access_permission = updated_access_permissions.first
      expect(updated_access_permission.id).to eq(access_permission1.id)
      expect(updated_access_permission.scope).to eq(access_permissions[0][:scope])
      expect(updated_access_permission.active).to eq(access_permissions[0][:active])
    end

    it 'removes the existing permission if `_delete` is passed' do
      access_permissions = [
        {
          id: access_permission1.id,
          customer_profile_id: customer1.id,
          scope: (allowed_scopes - [access_permission1.scope]).sample,
          active: false,
          _delete: true
        }
      ]
      permissions_manager = Customers::ManageAccessPermissions.new(customer: admin_customer, access_permissions: access_permissions).call

      expect(permissions_manager).to be_success

      updated_access_permissions = permissions_manager.access_permissions
      expect(updated_access_permissions).to be_blank

      expect{ access_permission1.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'un-tags the admin_customer as a company team admin if de-activating all access permissions or deleting last access permission' do
      deactivating = [true, false].sample
      access_permissions = case
      when deactivating
        [
          {
            id: access_permission1.id,
            customer_profile_id: customer1.id,
            scope: (allowed_scopes - [access_permission1.scope]).sample,
            active: false
          }
        ]
      else # deleting
        [
          {
            id: access_permission1.id,
            customer_profile_id: customer1.id,
            scope: (allowed_scopes - [access_permission1.scope]).sample,
            active: false,
            _delete: true
          }
        ]
      end
      permissions_manager = Customers::ManageAccessPermissions.new(customer: admin_customer, access_permissions: access_permissions)

      expect{ permissions_manager.call }.to change{ admin_customer.company_team_admin }.from(true).to(false)
    end
  end
end
