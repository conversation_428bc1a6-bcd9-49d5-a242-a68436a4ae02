require 'rails_helper'

RSpec.describe Customers::ManageDeliveryOverrides, type: :service, customers: true, delivery_overrides: true do

  let!(:customer) { create(:customer_profile, :random) }

  context 'category specific overrides' do
    let!(:catering_override) do
      {
        supplier_kind: 'catering',
        supplier_profile_id: nil,
        customer_override: nil,
        supplier_override: rand(10..20),
        active: true
      }
    end

    let!(:pantry_override) do
      {
        supplier_kind: 'pantry',
        supplier_profile_id: nil,
        customer_override: rand(10..20),
        supplier_override: nil,
        active: true
      }
    end

    let!(:delivery_overrides) { [catering_override, pantry_override] }

    it 'adds the delivery overide for specific suppliers' do
      overrides_manager = Customers::ManageDeliveryOverrides.new(customer: customer, delivery_overrides: delivery_overrides).call

      expect(overrides_manager).to be_success

      created_delivery_overrides = overrides_manager.delivery_overrides
      expect(created_delivery_overrides.size).to eq(2)
      expect(created_delivery_overrides.map(&:supplier_kind)).to include('catering', 'pantry')

      created_catering_override = created_delivery_overrides.detect{|override| override.supplier_kind == 'catering' }
      expect(created_catering_override.customer_override).to eq(catering_override[:customer_override])
      expect(created_catering_override.supplier_override).to eq(catering_override[:supplier_override])

      created_pantry_override = created_delivery_overrides.detect{|override| override.supplier_kind == 'pantry' }
      expect(created_pantry_override.customer_override).to eq(pantry_override[:customer_override])
      expect(created_pantry_override.supplier_override).to eq(pantry_override[:supplier_override])
    end
  end

  context 'supplier specific overrides' do
    let!(:supplier1) { create(:supplier_profile, :random) }
    let!(:supplier2) { create(:supplier_profile, :random) }

    let!(:supplier1_override) do
      {
        supplier_kind: 'specific',
        supplier_profile_id: supplier1.id,
        customer_override: nil,
        supplier_override: rand(10..20),
        active: true
      }
    end

    let!(:supplier2_override) do
      {
        supplier_kind: 'specific',
        supplier_profile_id: supplier2.id,
        customer_override: rand(10..20),
        supplier_override: nil,
        active: true
      }
    end

    let!(:delivery_overrides) { [supplier1_override, supplier2_override] }

    it 'adds the delivery overide for specific suppliers' do
      overrides_manager = Customers::ManageDeliveryOverrides.new(customer: customer, delivery_overrides: delivery_overrides).call

      expect(overrides_manager).to be_success

      created_delivery_overrides = overrides_manager.delivery_overrides
      expect(created_delivery_overrides.size).to eq(2)
      expect(created_delivery_overrides.map(&:supplier_kind)).to include('specific')
      expect(created_delivery_overrides.map(&:supplier_profile)).to include(supplier1, supplier2)

      created_supplier1_override = created_delivery_overrides.detect{|override| override.supplier_profile == supplier1 }
      expect(created_supplier1_override.customer_override).to eq(supplier1_override[:customer_override])
      expect(created_supplier1_override.supplier_override).to eq(supplier1_override[:supplier_override])

      created_supplier2_override = created_delivery_overrides.detect{|override| override.supplier_profile == supplier2 }
      expect(created_supplier2_override.customer_override).to eq(supplier2_override[:customer_override])
      expect(created_supplier2_override.supplier_override).to eq(supplier2_override[:supplier_override])
    end
  end

  context 'with an existing delivery overrides' do
    let!(:supplier) { create(:supplier_profile, :random) }
    let!(:existing_catering_override) { create(:delivery_override, :random, customer_profile: customer, supplier_kind: 'catering', active: true) }
    let!(:existing_pantry_override) { create(:delivery_override, :random, customer_profile: customer, supplier_kind: 'pantry', active: true) }
    let!(:existing_supplier_override) { create(:delivery_override, :random, customer_profile: customer, supplier_kind: 'specific', supplier_profile: supplier, active: true) }

    let!(:catering_override) do
      {
        id: existing_catering_override.id,
        supplier_kind: 'catering',
        supplier_profile_id: nil,
        customer_override: nil,
        supplier_override: rand(10..20),
        active: false
      }
    end

    let!(:pantry_override) do
      {
        id: existing_pantry_override.id,
        supplier_kind: 'pantry',
        supplier_profile_id: nil,
        customer_override: rand(10..20),
        supplier_override: nil,
        active: false
      }
    end

    let!(:supplier_override) do
      {
        id: existing_supplier_override.id,
        supplier_kind: 'specific',
        supplier_profile_id: supplier.id,
        customer_override: rand(10..20),
        supplier_override: rand(10..20),
        active: false
      }
    end

    let!(:delivery_overrides) { [catering_override, pantry_override, supplier_override] }

    it 'updates the existing delivery overrides (using passed in ID)' do
      overrides_manager = Customers::ManageDeliveryOverrides.new(customer: customer, delivery_overrides: delivery_overrides).call

      expect(overrides_manager).to be_success

      updated_delivery_overrides = overrides_manager.delivery_overrides
      expect(updated_delivery_overrides.size).to eq(3)

      updated_catering_override = updated_delivery_overrides.detect{|override| override.supplier_kind == 'catering' }
      expect(updated_catering_override.id).to eq(existing_catering_override.id)
      expect(updated_catering_override.customer_override).to eq(catering_override[:customer_override])
      expect(updated_catering_override.supplier_override).to eq(catering_override[:supplier_override])
      expect(updated_catering_override.active).to eq(catering_override[:active])

      updated_pantry_override = updated_delivery_overrides.detect{|override| override.supplier_kind == 'pantry' }
      expect(updated_pantry_override.id).to eq(existing_pantry_override.id)
      expect(updated_pantry_override.customer_override).to eq(pantry_override[:customer_override])
      expect(updated_pantry_override.supplier_override).to eq(pantry_override[:supplier_override])
      expect(updated_pantry_override.active).to eq(pantry_override[:active])

      updated_supplier_override = updated_delivery_overrides.detect{|override| override.supplier_kind == 'specific' }
      expect(updated_supplier_override.id).to eq(existing_supplier_override.id)
      expect(updated_supplier_override.customer_override).to eq(supplier_override[:customer_override])
      expect(updated_supplier_override.supplier_override).to eq(supplier_override[:supplier_override])
      expect(updated_supplier_override.active).to eq(supplier_override[:active])
    end

    it 'updates the existing delivery overrides even without ID (matched on supplier kind + supplier_profile_id - does not create duplicates)' do
      delivery_overrides_without_ids = delivery_overrides.map{|override| override.except(:id) }
      overrides_manager = Customers::ManageDeliveryOverrides.new(customer: customer, delivery_overrides: delivery_overrides_without_ids).call

      expect(overrides_manager).to be_success

      updated_delivery_overrides = overrides_manager.delivery_overrides
      expect(updated_delivery_overrides.size).to eq(3)

      updated_catering_override = updated_delivery_overrides.detect{|override| override.supplier_kind == 'catering' }
      expect(updated_catering_override.id).to eq(existing_catering_override.id)
      expect(updated_catering_override.customer_override).to eq(catering_override[:customer_override])
      expect(updated_catering_override.supplier_override).to eq(catering_override[:supplier_override])
      expect(updated_catering_override.active).to eq(catering_override[:active])

      updated_pantry_override = updated_delivery_overrides.detect{|override| override.supplier_kind == 'pantry' }
      expect(updated_pantry_override.id).to eq(existing_pantry_override.id)
      expect(updated_pantry_override.customer_override).to eq(pantry_override[:customer_override])
      expect(updated_pantry_override.supplier_override).to eq(pantry_override[:supplier_override])
      expect(updated_pantry_override.active).to eq(pantry_override[:active])

      updated_supplier_override = updated_delivery_overrides.detect{|override| override.supplier_kind == 'specific' }
      expect(updated_supplier_override.id).to eq(existing_supplier_override.id)
      expect(updated_supplier_override.customer_override).to eq(supplier_override[:customer_override])
      expect(updated_supplier_override.supplier_override).to eq(supplier_override[:supplier_override])
      expect(updated_supplier_override.active).to eq(supplier_override[:active])
    end

    it 'removes the existing delivery overrides if `_delete` is passed' do
      delivery_overrides_with_deletes = delivery_overrides.map do |delivery_override|
        if %w[catering pantry].include?(delivery_override[:supplier_kind])
          delivery_override.merge({ _delete: true })
        else
          delivery_override
        end
      end
      overrides_manager = Customers::ManageDeliveryOverrides.new(customer: customer, delivery_overrides: delivery_overrides_with_deletes).call

      expect(overrides_manager).to be_success

      updated_delivery_overrides = overrides_manager.delivery_overrides
      expect(updated_delivery_overrides.size).to eq(1)

      updated_supplier_override = updated_delivery_overrides.detect{|override| override.supplier_kind == 'specific' }
      expect(updated_supplier_override.id).to eq(existing_supplier_override.id)
      expect(updated_supplier_override.customer_override).to eq(supplier_override[:customer_override])
      expect(updated_supplier_override.supplier_override).to eq(supplier_override[:supplier_override])
      expect(updated_supplier_override.active).to eq(supplier_override[:active])

      expect{ existing_catering_override.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ existing_pantry_override.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end    
  end
end
