require 'rails_helper'

RSpec.describe Customers::FavouriteSupplier, type: :service, admin: true, suppliers: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:customer) { create(:customer_profile, :random) }

  it 'lets customers favourite suppliers' do
    supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer, supplier: supplier).call

    expect(supplier_favouriter).to be_success
    expect(customer.reload.favourite_suppliers).to include(supplier)
    expect(supplier_favouriter.favourite_mode).to eq('favourited')
  end

  it 'lets customers favourite suppliers as Team Suppliers' do
    supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer, supplier: supplier, kind: 'team_order').call

    expect(supplier_favouriter).to be_success
    expect(customer.reload.favourite_suppliers).to_not include(supplier)
    expect(customer.reload.favourite_team_suppliers).to include(supplier)
    expect(supplier_favouriter.favourite_mode).to eq('favourited')
  end

  context 'with existing favourited suppliers' do
    let!(:supplier2) { create(:supplier_profile, :random) }
    let!(:supplier3) { create(:supplier_profile, :random) }

    let!(:favourite_supplier1) { create(:favourite_supplier, favouriter: customer, supplier_profile: supplier, kind: 'normal') }
    let!(:favourite_supplier2) { create(:favourite_supplier, favouriter: customer, supplier_profile: supplier2, kind: 'normal') }

    it 'adds a new supplier to the favourites list' do
      supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer, supplier: supplier3).call

      expect(supplier_favouriter).to be_success
      expect(customer.reload.favourite_suppliers).to include(supplier3, supplier, supplier2)
      expect(supplier_favouriter.favourite_mode).to eq('favourited')
    end

    it 'removes an existing supplier form the users favourite list' do
      supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer, supplier: supplier2).call

      expect(supplier_favouriter).to be_success
      expect(customer.reload.favourite_suppliers).to_not include(supplier2)
      expect(customer.reload.favourite_suppliers).to_not include(supplier3) # was not favourited
      expect(customer.reload.favourite_suppliers).to include(supplier)
      expect(supplier_favouriter.favourite_mode).to eq('un-favourited')
    end
  end

  context 'with existing favourited suppliers' do
    let!(:supplier2) { create(:supplier_profile, :random) }
    let!(:supplier3) { create(:supplier_profile, :random) }

    let!(:favourite_team_supplier1) { create(:favourite_supplier, favouriter: customer, supplier_profile: supplier, kind: 'team_order') }
    let!(:favourite_team_supplier2) { create(:favourite_supplier, favouriter: customer, supplier_profile: supplier2, kind: 'team_order') }

    it 'adds a new supplier to the favourites team suppliers list' do
      supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer, supplier: supplier3, kind: 'team_order').call

      expect(supplier_favouriter).to be_success
      expect(customer.reload.favourite_team_suppliers).to include(supplier3, supplier, supplier2)
      expect(supplier_favouriter.favourite_mode).to eq('favourited')
    end

    it 'removes an existing supplier form the users favourite team suppliers list' do
      supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer, supplier: supplier2, kind: 'team_order').call

      expect(supplier_favouriter).to be_success
      expect(customer.reload.favourite_team_suppliers).to_not include(supplier2)
      expect(customer.reload.favourite_team_suppliers).to_not include(supplier3) # was not favourited
      expect(customer.reload.favourite_team_suppliers).to include(supplier)
      expect(supplier_favouriter.favourite_mode).to eq('un-favourited')
    end
  end

  context 'with restricted suppliers' do
    let!(:customer_supplier_restriction) { create(:customer_profiles_supplier_profile, customer_profile: customer, supplier_profile: supplier) }
    
    it 'lets customer favourite a restricted customer they have access to' do
      supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer, supplier: supplier).call

      expect(supplier_favouriter).to be_success
      expect(customer.reload.favourite_suppliers).to include(supplier)
    end

    it 'customer cannot favourite restricted suppliers they do not have access to' do
      customer2 = create(:customer_profile, :random)
      supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer2, supplier: supplier).call

      expect(supplier_favouriter).to_not be_success
      expect(supplier_favouriter.errors).to include('You do not have access to this supplier')
    end
  end

  context 'errors' do
    it 'cannot favourite without a customer' do
      supplier_favouriter = Customers::FavouriteSupplier.new(customer: nil, supplier: supplier).call

      expect(supplier_favouriter).to_not be_success
      expect(supplier_favouriter.errors).to include('Cannot favourite supplier without a customer')
    end

    it 'cannot favourite without a supplier' do
      supplier_favouriter = Customers::FavouriteSupplier.new(customer: customer, supplier: nil).call

      expect(supplier_favouriter).to_not be_success
      expect(supplier_favouriter.errors).to include('Cannot favourite without a supplier')
    end
  end

end