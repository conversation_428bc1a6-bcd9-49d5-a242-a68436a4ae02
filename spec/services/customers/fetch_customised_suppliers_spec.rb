require 'rails_helper'

RSpec.describe Customers::FetchCustomisedSuppliers, type: :service, customers: true, suppliers: true do

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:supplier3) { create(:supplier_profile, :random) }

  let!(:customer) { create(:customer_profile, :random) }

  it 'returns favourites, rate card and customer menu supplier ids' do
    customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

    expect(customised_suppliers).to respond_to(:favourite_supplier_ids)
    expect(customised_suppliers).to respond_to(:rate_card_supplier_ids)
    expect(customised_suppliers).to respond_to(:custom_menu_supplier_ids)
  end

  it 'returns blank values by default' do
    customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

    expect(customised_suppliers.favourite_supplier_ids).to be_empty
    expect(customised_suppliers.rate_card_supplier_ids).to be_empty
    expect(customised_suppliers.custom_menu_supplier_ids).to be_empty
  end

  it 'returns blank values for missing customer' do
    customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: nil).call

    expect(customised_suppliers.favourite_supplier_ids).to be_empty
    expect(customised_suppliers.rate_card_supplier_ids).to be_empty
    expect(customised_suppliers.custom_menu_supplier_ids).to be_empty
  end

  context 'with favourite suppliers' do
    let!(:favourite_supplier1) { create(:favourite_supplier, favouriter: customer, supplier_profile: supplier1, kind: 'normal') }
    let!(:favourite_supplier3) { create(:favourite_supplier, favouriter: customer, supplier_profile: supplier3, kind: 'normal') }

    it 'returns the favourite supplier ids' do
      customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

      expect(customised_suppliers.favourite_supplier_ids).to match([supplier1, supplier3].map(&:id))
    end

    it 'returns scoped favourite supplier ids' do
      customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer, scoped_supplier_ids: [supplier1, supplier2].map(&:id)).call

      expect(customised_suppliers.favourite_supplier_ids).to include(supplier1.id)
      expect(customised_suppliers.favourite_supplier_ids).to_not include(supplier3.id)
    end
  end

  context 'with a company' do
    let!(:company) { create(:company, :random) }

    before do
      customer.update_column(:company_id, company.id)
    end

    context 'with rate cards' do
      let!(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier1) }
      let!(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier2) }
      let!(:menu_item3) { create(:menu_item, :random, supplier_profile: supplier3) }

      let!(:rate_card1) { create(:rate_card, :random, menu_item: menu_item1, company: company) }
      let!(:rate_card2) { create(:rate_card, :random, menu_item: menu_item2) }
      let!(:rate_card3) { create(:rate_card, :random, menu_item: menu_item3, company: company) }

      it 'returns the ids of suppliers with an active rate card' do
        customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

        expect(customised_suppliers.rate_card_supplier_ids).to include(supplier1.id, supplier3.id)
        expect(customised_suppliers.rate_card_supplier_ids).to_not include(supplier2.id)
      end

      it 'does not return a supplier id with a rate card of an archived menu item' do
        menu_item3.update_column(:archived_at, Time.zone.now)
        customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

        expect(customised_suppliers.rate_card_supplier_ids).to include(supplier1.id)
        expect(customised_suppliers.rate_card_supplier_ids).to_not include(supplier3.id)
      end

      it 'returns the ids of suppliers with an active rate card to the scoped supplier ids' do
        customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer, scoped_supplier_ids: [supplier1, supplier2].map(&:id)).call

        expect(customised_suppliers.rate_card_supplier_ids).to include(supplier1.id)
        expect(customised_suppliers.rate_card_supplier_ids).to_not include(supplier2.id, supplier3.id)
      end

      it 'return blank rate card supplier ids if the customer does not belong to a company' do
        customer.update_column(:company_id, nil)
        customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

        expect(customised_suppliers.rate_card_supplier_ids).to be_empty
      end
    end # with rate cards

    context 'with custom menu sections' do
      let!(:menu_section1) { create(:menu_section, :random, supplier_profile: supplier1) }
      let!(:menu_section2) { create(:menu_section, :random, supplier_profile: supplier2) }
      let!(:menu_section3) { create(:menu_section, :random, supplier_profile: supplier3) }

      before do
        [menu_section2, menu_section3].each do |menu_section|
          menu_section.companies << company
        end
      end

      it 'returns the ids of suppliers with a restricted menu sections' do
        customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

        expect(customised_suppliers.custom_menu_supplier_ids).to include(supplier2.id, supplier3.id)
        expect(customised_suppliers.custom_menu_supplier_ids).to_not include(supplier1.id)
      end

      it 'does not return a supplier id with a restricted archived menu sections' do
        menu_section2.update_column(:archived_at, Time.zone.now)
        customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

        expect(customised_suppliers.custom_menu_supplier_ids).to include(supplier3.id)
        expect(customised_suppliers.custom_menu_supplier_ids).to_not include(supplier2.id)
      end

      it 'returns the ids of suppliers with restricted menu section to the scoped supplier ids' do
        customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer, scoped_supplier_ids: [supplier1, supplier2].map(&:id)).call

        expect(customised_suppliers.custom_menu_supplier_ids).to include(supplier2.id)
        expect(customised_suppliers.custom_menu_supplier_ids).to_not include(supplier1.id, supplier3.id)
      end

      it 'return blank custom menu supplier ids if the customer does not belong to a company' do
        customer.update_column(:company_id, nil)
        customised_suppliers = Customers::FetchCustomisedSuppliers.new(customer: customer).call

        expect(customised_suppliers.custom_menu_supplier_ids).to be_empty
      end
    end # with custom menu sections
  end # with a company

end
