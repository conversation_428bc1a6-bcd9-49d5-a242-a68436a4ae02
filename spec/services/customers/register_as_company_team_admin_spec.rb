require 'rails_helper'

RSpec.describe Customers::RegisterAsCompanyTeamAdmin, type: :service, customers: true, access_permissions: true do

  let!(:admin_customer) { create(:customer_profile, :random, :with_user) }
  let!(:customer) { create(:customer_profile, :random, :with_user) }

  before do
    email_sender = delayed_email_sender = double(Customers::Emails::SendCompanyTeamAdminWelcomeEmail)
    allow(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  it 'makes the passed in company_team_admin (customer) a company team admin' do
    expect(admin_customer.company_team_admin).to be_falsey

    admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer, customer: customer).call
    expect(admin_registration).to be_success

    updated_company_team_admin = admin_registration.company_team_admin
    expect(updated_company_team_admin.company_team_admin).to be_truthy
  end

  it 'sends a Company Team Admin Welcome email' do
    expect(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to receive(:new)

    admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer, customer: customer).call
    expect(admin_registration).to be_success    
  end

  it 'gives the customer access permissions to the adminable customer (scope: `company_team_admin`)' do
    admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer, customer: customer).call

    expect(admin_registration).to be_success

    updated_company_team_admin = admin_registration.company_team_admin
    admin_access_permissions = updated_company_team_admin.admin_access_permissions.where(customer_profile: customer).first

    expect(admin_access_permissions).to be_present
    expect(admin_access_permissions.admin).to eq(admin_customer)
    expect(admin_access_permissions.customer_profile).to eq(customer)
    expect(admin_access_permissions.scope).to eq('company_team_admin')
    expect(admin_access_permissions).to be_active

    expect(admin_customer.active_adminable_customer_profiles).to include(customer)
  end

  context 'adminable customer with attached company' do
    let!(:customer_company) { create(:company, :random, customer_profiles: [customer]) }

    it 'attaches the customer under the company team admin\'s company' do
      admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer, customer: customer).call

      expect(admin_registration).to be_success

      updated_company_team_admin = admin_registration.customer
      admin_company = updated_company_team_admin.company

      expect(admin_company).to be_present
      expect(admin_company.id).to eq(customer_company.id)

      expect(customer_company.reload.customer_profiles).to include(updated_company_team_admin)
    end

    it 'does not attach new admin customer under adminable customer\'s company if the customer already has a company' do
      create(:company, :random, customer_profiles: [customer]) # attach customer to a company
      admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer.reload, customer: customer).call

      expect(admin_registration).to be_success
      expect(customer.reload.company.id).to_not eq(customer_company.id)
    end
  end

  context 'customer with billing details' do
    let!(:customer_billing_details) { create(:billing_details, :random, customer_profile: customer) }

    it 'copys over the billing details from the customers billing details' do
      admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer, customer: customer.reload).call

      expect(admin_registration).to be_success
      updated_company_team_admin = admin_registration.company_team_admin
      admin_billing_details = updated_company_team_admin.billing_details

      expect(admin_billing_details.id).to_not eq(customer_billing_details.id)
      expect(admin_billing_details.name).to eq(customer_billing_details.name)
      expect(admin_billing_details.email).to eq(customer_billing_details.email)
      expect(admin_billing_details.phone).to eq(customer_billing_details.phone)
      expect(admin_billing_details.address).to eq(customer_billing_details.address)
      expect(admin_billing_details.suburb).to eq(customer_billing_details.suburb)
    end

    context 'with existing customer billing details' do
      let!(:admin_billing_details) { create(:billing_details, :random, customer_profile: admin_customer) }

      it 'still copys over the billing details from the customers billing details' do
        admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer.reload, customer: customer.reload).call

        expect(admin_registration).to be_success
        updated_company_team_admin = admin_registration.company_team_admin
        updated_admin_billing_details = updated_company_team_admin.billing_details

        # same existing billing details
        expect(updated_admin_billing_details.id).to eq(admin_billing_details.id)
        expect(updated_admin_billing_details.id).to_not eq(customer_billing_details.id)
        
        expect(updated_admin_billing_details.name).to eq(customer_billing_details.name)
        expect(updated_admin_billing_details.email).to eq(customer_billing_details.email)
        expect(updated_admin_billing_details.phone).to eq(customer_billing_details.phone)
        expect(updated_admin_billing_details.address).to eq(customer_billing_details.address)
        expect(updated_admin_billing_details.suburb).to eq(customer_billing_details.suburb)
      end
    end
  end

  context 'errors' do
    it 'returns with errors if customer is missing' do
      admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: nil, customer: customer).call

      expect(admin_registration).to_not be_success
      expect(admin_registration.errors).to include('Cannot register without a potential company team admin (customer)')
    end

    it 'returns with errors if adminable customer is missing' do
      supplier = create(:supplier_profile, :random)
      admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer, customer: [nil, supplier].sample).call

      expect(admin_registration).to_not be_success
      expect(admin_registration.errors).to include('Cannot register without an active customer')
    end

    it 'returns with errors if adminable customer is deactivated' do
      customer.user.update_column(:is_active, false)
      admin_registration = Customers::RegisterAsCompanyTeamAdmin.new(company_team_admin: admin_customer, customer: customer).call

      expect(admin_registration).to_not be_success
      expect(admin_registration.errors).to include('Cannot register without an active customer')
    end
  end

end