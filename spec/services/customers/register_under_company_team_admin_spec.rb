require 'rails_helper'

RSpec.describe Customers::RegisterUnderCompanyTeamAdmin, type: :service, customers: true, access_permissions: true do

  let!(:company_team_admin) { create(:customer_profile, :random, company_team_admin: true) }
  let!(:customer) { create(:customer_profile, :random) }

  it 'gives the company team admin access permission to the customer' do
    company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: customer, company_team_admin: company_team_admin).call

    expect(company_registration).to be_success

    updated_company_team_admin = company_registration.company_team_admin
    customer_access_permissions = updated_company_team_admin.admin_access_permissions.where(customer_profile: customer).first

    expect(customer_access_permissions).to be_present
    expect(customer_access_permissions.admin).to eq(company_team_admin)
    expect(customer_access_permissions.customer_profile).to eq(customer)
    expect(customer_access_permissions.scope).to eq('company_team_admin')
    expect(customer_access_permissions).to be_active

    expect(company_team_admin.active_adminable_customer_profiles).to include(customer)
  end

  context 'company team admin with attached company' do
    let!(:admin_company) { create(:company, :random, customer_profiles: [company_team_admin]) }

    it 'attaches the customer under the company team admin\'s company' do
      company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: customer, company_team_admin: company_team_admin).call

      expect(company_registration).to be_success

      updated_customer = company_registration.customer
      customer_company = updated_customer.company

      expect(customer_company).to be_present
      expect(customer_company.id).to eq(admin_company.id)

      expect(admin_company.reload.customer_profiles).to include(updated_customer)
    end

    it 'does not attach customer under company_team_admin\'s company if the customer already has a company' do
      create(:company, :random, customer_profiles: [customer]) # attach customer to a company
      company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: customer.reload, company_team_admin: company_team_admin).call

      expect(company_registration).to be_success
      expect(customer.reload.company.id).to_not eq(admin_company.id)
    end
  end

  context 'company team admin with billing details' do
    let!(:admin_billing_details) { create(:billing_details, :random, customer_profile: company_team_admin) }

    it 'copys over the billing details from the customers billing details' do
      company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: customer, company_team_admin: company_team_admin.reload).call

      expect(company_registration).to be_success
      updated_customer = company_registration.customer
      customer_billing_details = updated_customer.billing_details

      expect(customer_billing_details.id).to_not eq(admin_billing_details.id)
      expect(customer_billing_details.name).to eq(admin_billing_details.name)
      expect(customer_billing_details.email).to eq(admin_billing_details.email)
      expect(customer_billing_details.phone).to eq(admin_billing_details.phone)
      expect(customer_billing_details.address).to eq(admin_billing_details.address)
      expect(customer_billing_details.suburb).to eq(admin_billing_details.suburb)
    end

    context 'with customer billing details' do
      let!(:customer_billing_details) { create(:billing_details, :random, customer_profile: customer) }

      it 'still copys over the billing details from the customers billing details' do
        company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: customer.reload, company_team_admin: company_team_admin.reload).call

        expect(company_registration).to be_success
        updated_customer = company_registration.customer
        customer_billing_details = updated_customer.billing_details

        # same existing billing details
        expect(customer_billing_details.id).to eq(customer_billing_details.id)
        expect(customer_billing_details.id).to_not eq(admin_billing_details.id)
        
        expect(customer_billing_details.name).to eq(admin_billing_details.name)
        expect(customer_billing_details.email).to eq(admin_billing_details.email)
        expect(customer_billing_details.phone).to eq(admin_billing_details.phone)
        expect(customer_billing_details.address).to eq(admin_billing_details.address)
        expect(customer_billing_details.suburb).to eq(admin_billing_details.suburb)
      end
    end
  end

  context 'errors' do
    it 'returns with errors if customer is missing' do
      company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: nil, company_team_admin: company_team_admin).call

      expect(company_registration).to_not be_success
      expect(company_registration.errors).to include('Cannot register without a customer')
    end

    it 'returns with errors if company team admin is missing' do
      supplier = create(:supplier_profile, :random)
      company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: customer, company_team_admin: [nil, supplier].sample).call

      expect(company_registration).to_not be_success
      expect(company_registration.errors).to include('Cannot register without an active Company team Admin')
    end

    it 'returns with errors if company team admin is not really a company_team_admin' do
      company_team_admin.update_column(:company_team_admin, false)
      company_registration = Customers::RegisterUnderCompanyTeamAdmin.new(customer: customer, company_team_admin: company_team_admin).call

      expect(company_registration).to_not be_success
      expect(company_registration.errors).to include('Cannot register without an active Company team Admin')
    end
  end

end