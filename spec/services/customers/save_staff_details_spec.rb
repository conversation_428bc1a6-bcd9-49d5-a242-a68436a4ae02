require 'rails_helper'

RSpec.describe Customers::SaveStaffDetails, type: :service, customers: true, staff: true do
  
  let!(:customer) { create(:customer_profile, :random, role: CustomerProfile::VALID_STAFF_ROLES.sample) }

  let!(:personal_details) do
    {
      preffered_name: Faker::Name.name,
      email: Faker::Internet.email,
      date_of_birth: Time.zone.now.to_s(:date),
      address: Faker::Address.full_address
    }
  end

  let!(:emergency_contact_details) do
    {
      name: Faker::Name.name,
      number: Faker::PhoneNumber.phone_number,
      relationship: Faker::Relationship.familial
    }
  end

  let!(:bank_details) do
    {
      account_name: Faker::Name.name,
      bsb: Faker::Bank.swift_bic,
      account_number: Faker::Bank.account_number
    }
  end

  let!(:tax_details) do
    {
      tfn: Faker::Internet.url,
      super: Faker::Internet.url
    }
  end

  let!(:document_details) do
    {
      handbook: true,
      safety: true
    }
  end

  let!(:detail_params) do
    {
      personal: personal_details,
      emergency_contact: emergency_contact_details,
      bank: bank_details,
      tax: tax_details,
      documents: document_details
    }
  end

  let!(:email_sender) { double(Admin::Emails::SendStaffDetailsEmail) }

  before do
    allow(Admin::Emails::SendStaffDetailsEmail).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, sent_email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)
  end

  it 'saves a new staff details record for the customer' do
    expect(customer.staff_details).to be_blank

    details_saver = Customers::SaveStaffDetails.new(customer: customer, detail_params: detail_params).call

    expect(details_saver).to be_success
    created_staff_details = details_saver.staff_details
    expect(created_staff_details).to be_present
    expect(created_staff_details).to be_persisted
    expect(created_staff_details.customer_profile).to eq(customer)
    expect(customer.reload.staff_details).to be_present
  end

  it 'saves the staff details as per passed in params' do
    details_saver = Customers::SaveStaffDetails.new(customer: customer, detail_params: detail_params).call

    expect(details_saver).to be_success
    created_staff_details = details_saver.staff_details
    expect(created_staff_details.personal).to eq(personal_details.stringify_keys)
    expect(created_staff_details.emergency_contact).to eq(emergency_contact_details.stringify_keys)
    expect(created_staff_details.bank).to eq(bank_details.stringify_keys)
    expect(created_staff_details.tax).to eq(tax_details.stringify_keys)
    expect(created_staff_details.documents).to eq(document_details.stringify_keys)
  end

  context 'Notify Accounts team' do
    it 'it notifies the account team of completion of all the on-boarding details' do
      expect(Admin::Emails::SendStaffDetailsEmail).to receive(:new).with(customer: customer)

      details_saver = Customers::SaveStaffDetails.new(customer: customer, detail_params: detail_params).call

      expect(details_saver).to be_success
      expect(details_saver.accounts_team_notified).to be_truthy
    end

    it 'does not notify the account team if all on-boarding details are not completed' do
      expect(Admin::Emails::SendStaffDetailsEmail).to_not receive(:new).with(customer: customer)

      details_saver = Customers::SaveStaffDetails.new(customer: customer, detail_params: detail_params.except(:bank)).call

      expect(details_saver).to be_success
      expect(details_saver.accounts_team_notified).to be_falsey
    end
  end

  context 'with existing staff details' do
    let!(:staff_details) { create(:staff_details, :random, customer_profile: customer) }

    it 'updates the staff details as per passed in params' do
      details_saver = Customers::SaveStaffDetails.new(customer: customer, detail_params: detail_params).call

      expect(details_saver).to be_success
      updated_staff_details = details_saver.staff_details
      expect(updated_staff_details.id).to eq(staff_details.id) # save ID
      expect(updated_staff_details.personal).to eq(personal_details.stringify_keys)
      expect(updated_staff_details.emergency_contact).to eq(emergency_contact_details.stringify_keys)
      expect(updated_staff_details.bank).to eq(bank_details.stringify_keys)
      expect(updated_staff_details.tax).to eq(tax_details.stringify_keys)
      expect(updated_staff_details.documents).to eq(document_details.stringify_keys)
    end

    context 'Notify Accounts team' do
      it 'it notifies the account team of completion of all the on-boarding details' do
        expect(Admin::Emails::SendStaffDetailsEmail).to receive(:new).with(customer: customer)

        details_saver = Customers::SaveStaffDetails.new(customer: customer, detail_params: detail_params).call

        expect(details_saver).to be_success
        expect(details_saver.accounts_team_notified).to be_truthy
      end

      it 'does not notify the account team if all on-boarding details are not completed' do
        expect(Admin::Emails::SendStaffDetailsEmail).to_not receive(:new).with(customer: customer)

        details_saver = Customers::SaveStaffDetails.new(customer: customer, detail_params: detail_params.except(:bank)).call

        expect(details_saver).to be_success
        expect(details_saver.accounts_team_notified).to be_falsey
      end

      context 'with email sending failure' do
        before do
          unsuccessfull_response = OpenStruct.new(success?: false, errors: ['Accounts team already notified'])
          allow(email_sender).to receive(:call).and_return(unsuccessfull_response)
        end

        it 'returns with the email sending error' do
          details_saver = Customers::SaveStaffDetails.new(customer: customer, detail_params: detail_params).call

          expect(details_saver).to_not be_success
          expect(details_saver.accounts_team_notified).to be_falsey
          expect(details_saver.errors).to include('Accounts team already notified')
        end
      end # with email sending errors
    end # notify accounts team
  end # existing staff details

end