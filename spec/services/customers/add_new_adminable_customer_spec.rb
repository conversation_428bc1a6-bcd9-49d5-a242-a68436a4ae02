require 'rails_helper'

RSpec.describe Customers::AddNewAdminableCustomer, type: :service, users: true, customers: true, access_permissions: true do

  let!(:company_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

  let!(:customer_params) do
    {
      firstname: Faker::Name.first_name,
      lastname: Faker::Name.last_name,
    }
  end

  before do
    # mock event logger - to avoid Redis::CannotConnectError
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'create a new customer with the passed in name' do
    customer_adder = Customers::AddNewAdminableCustomer.new(customer_params: customer_params, company_team_admin: company_team_admin).call

    expect(customer_adder).to be_success
    new_customer = customer_adder.customer
    expect(new_customer).to be_present
    expect(new_customer).to be_a(CustomerProfile)
    expect(new_customer).to be_persisted
    expect(new_customer.name).to eq("#{customer_params[:firstname]} #{customer_params[:lastname]}")
    expect(new_customer.customer_name).to eq("#{customer_params[:firstname]} #{customer_params[:lastname]}")
  end

  it 'create a new customer with default fields' do
    customer_adder = Customers::AddNewAdminableCustomer.new(customer_params: customer_params, company_team_admin: company_team_admin).call

    expect(customer_adder).to be_success

    new_customer = customer_adder.customer
    expect(new_customer.email).to include("#{customer_params[:firstname].parameterize}.#{customer_params[:lastname].parameterize}+", "@yordar.com.au")
    expect(new_customer.role).to eq('Other')
    expect(new_customer.uuid).to be_present

    new_customer_user = new_customer.user
    expect(new_customer_user).to be_present
    expect(new_customer_user.email).to include("#{customer_params[:firstname].parameterize}.#{customer_params[:lastname].parameterize}+", "@yordar.com.au")
    expect(new_customer_user.suburb_id).to eq(company_team_admin.user.suburb_id)
  end

  it 'creates the new customer under the company team admin' do
    customer_adder = Customers::AddNewAdminableCustomer.new(customer_params: customer_params, company_team_admin: company_team_admin).call

    expect(customer_adder).to be_success
    new_customer = customer_adder.customer
    expect(company_team_admin.active_adminable_customer_profiles).to include(new_customer)
  end

  context 'errors' do
    it 'errors without a passed in company team admin' do
      customer_adder = Customers::AddNewAdminableCustomer.new(customer_params: customer_params, company_team_admin: nil).call

      expect(customer_adder).to_not be_success
      expect(customer_adder.errors).to include('Cannot register customer without an admin')
    end

    it 'errors if the passed in company team admin is not actually a company team admin' do
      company_team_admin.update_column(:company_team_admin, false)
      customer_adder = Customers::AddNewAdminableCustomer.new(customer_params: customer_params, company_team_admin: company_team_admin).call

      expect(customer_adder).to_not be_success
      expect(customer_adder.errors).to include('Cannot register customer without an admin')
    end

    it 'errors with missing customer name' do
      missing_field = customer_params.keys.sample
      invalid_customer_params = customer_params.except(missing_field)
      customer_adder = Customers::AddNewAdminableCustomer.new(customer_params: invalid_customer_params, company_team_admin: company_team_admin).call

      expect(customer_adder).to_not be_success
      expect(customer_adder.errors).to include('Need both first and last name to register')
    end
  end

end