require 'rails_helper'

RSpec.describe Customers::BillingDetails::Upsert, type: :service, customers: true do

  let(:customer) { create(:customer_profile, :random) }
  let(:suburb) { create(:suburb, :random) }
  let(:detail_params) { { name: Faker::Name.name, email: Faker::Internet.email, phone: Faker::PhoneNumber.phone_number, address: Faker::Address.street_address, suburb_id: suburb.id } }

  it 'creates a new billing details if params are passed' do
    billing_creator = Customers::BillingDetails::Upsert.new(customer: customer, detail_params: detail_params).call

    expect(billing_creator).to be_success
    created_billing_details = billing_creator.billing_details

    expect(created_billing_details.name).to eq(detail_params[:name])
    expect(created_billing_details.email).to eq(detail_params[:email])
    expect(created_billing_details.phone).to eq(detail_params[:phone])
    expect(created_billing_details.address).to eq(detail_params[:address])
    expect(created_billing_details.suburb).to eq(suburb)
  end

  context 'errors' do
    it 'errors out if customer is missing' do
      billing_creator = Customers::BillingDetails::Upsert.new(customer: nil, detail_params: detail_params).call

      expect(billing_creator).to_not be_success
      expect(billing_creator.errors).to include('Customer is missing')
    end

    it 'does not create a new billing detail if billing data is missing' do
      billing_creator = Customers::BillingDetails::Upsert.new(customer: customer, detail_params: {}).call

      expect(billing_creator).to_not be_success
      expect(billing_creator.errors).to include('Cannot create new billing detail without data')
    end

    it 'does not create a new billing detail if some billing details are missing' do
      missing_key = detail_params.except(:suburb_id).keys.sample
      detail_params[missing_key] = nil

      billing_creator = Customers::BillingDetails::Upsert.new(customer: customer, detail_params: detail_params).call

      expect(billing_creator).to_not be_success
      expect(billing_creator.errors).to include("#{missing_key.capitalize} can't be blank")
    end
  end

  context 'With exsisting billing details' do
     let!(:billing_details) { create(:billing_details, :random, customer_profile: customer) }

     it 'updates the billing details if params are passed' do
       billing_updator = Customers::BillingDetails::Upsert.new(customer: customer, detail_params: detail_params).call

       expect(billing_updator).to be_success
       updated_billing_details = billing_updator.billing_details

       billing_details.reload
       expect(updated_billing_details.id).to eq(billing_details.id)
       expect(updated_billing_details.name).to eq(detail_params[:name])
       expect(updated_billing_details.email).to eq(detail_params[:email])
       expect(updated_billing_details.phone).to eq(detail_params[:phone])
       expect(updated_billing_details.address).to eq(detail_params[:address])
       expect(updated_billing_details.suburb).to eq(suburb)
     end

     it 'returns successfully without updating billing details if details are missing' do
       billing_updator = Customers::BillingDetails::Upsert.new(customer: customer, detail_params: {}).call

       expect(billing_updator).to be_success
       updated_billing_details = billing_updator.billing_details
       billing_details.reload
       expect(updated_billing_details.id).to eq(billing_details.id)
       expect(updated_billing_details.name).to_not eq(detail_params[:name])
       expect(updated_billing_details.email).to_not eq(detail_params[:email])
       expect(updated_billing_details.phone).to_not eq(detail_params[:phone])
       expect(updated_billing_details.address).to_not eq(detail_params[:address])
       expect(updated_billing_details.suburb).to_not eq(suburb)
     end

  end

end
