require 'rails_helper'

RSpec.describe Customers::FetchPurchaseOrder, customers: true, purchase_orders: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:po_number) { SecureRandom.hex(7) }

  it 'creates a new customer profile for the passed in PO number (as cpo_id)' do
    fetched_po = Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: po_number).call

    expect(fetched_po).to be_present
    expect(fetched_po).to be_a(CustomerPurchaseOrder)
    expect(fetched_po).to be_persisted
    expect(fetched_po.customer_profile).to eq(customer)
    expect(fetched_po.po_number).to eq(po_number)
  end

  context 'with an existing purchase order' do
    let!(:purchase_order) { create(:customer_purchase_order, :random, customer_profile: customer) }

    it 'returns the existing purchase order if the passed in cpo_id is the ID of the existing record' do
      fetched_po = Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: purchase_order.id).call

      expect(fetched_po.id).to eq(purchase_order.id)
    end

    it 'returns the existing purchase order if the passed in cpo_id is the PO number of the existing record' do
      fetched_po = Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: purchase_order.po_number).call

      expect(fetched_po.id).to eq(purchase_order.id)
    end

    it 'reactivates the existing purchase_order (if saved as inactive)' do
      purchase_order.update_column(:active, false)
      fetched_po = Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: [purchase_order.id, purchase_order.po_number].sample).call

      expect(fetched_po.id).to eq(purchase_order.id)
      expect(fetched_po.active).to be_truthy
    end

    it 'creates a new purchase order for the new customer even if passed in cpo_id (ID or PO number) is the same as an existing purchase order' do
      customer2 = create(:customer_profile, :random)
      fetched_po = Customers::FetchPurchaseOrder.new(customer: customer2, cpo_id: [purchase_order.id, purchase_order.po_number].sample).call

      expect(fetched_po.id).to_not eq(purchase_order.id) # not the existing purchase order
      expect(fetched_po.customer_profile).to eq(customer2)
    end
  end

  it 'returns nil if the customer is not passed' do
    fetched_po = Customers::FetchPurchaseOrder.new(customer: nil, cpo_id: po_number).call

    expect(fetched_po).to be_nil
  end

  it 'returns nil if the cpo_id is not passed' do
    fetched_po = Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: nil).call

    expect(fetched_po).to be_nil
  end

end
