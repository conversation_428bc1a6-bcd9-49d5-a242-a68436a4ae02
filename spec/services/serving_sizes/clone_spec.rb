require 'rails_helper'

RSpec.describe ServingSizes::Clone, type: :service, serving_sizes: true, menu_items: true, cloning: true do

  let(:menu_item) { create(:menu_item, :random) }
  let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }

  it 'clones the serving size with the same data' do
    serving_cloner = ServingSizes::Clone.new(serving_size: serving_size).call

    expect(serving_cloner).to be_success
    cloned_serving_size = serving_cloner.cloned_serving_size

    expect(cloned_serving_size).to be_present
    expect(cloned_serving_size.id).to_not eq(serving_size.id) # new serving size

    # different data

    expect(cloned_serving_size.weight).to_not eq(serving_size.weight)
    expect(cloned_serving_size.name).to eq("#{serving_size.name} - CLONED")

    # cloned data
    expect(cloned_serving_size.price).to eq(serving_size.price)
    expect(cloned_serving_size.menu_item).to eq(serving_size.menu_item)
  end

  it 'clones the serving size with the same data in a different menu item' do
    other_menu_item = create(:menu_item, :random)
    serving_cloner = ServingSizes::Clone.new(serving_size: serving_size, menu_item: other_menu_item).call

    expect(serving_cloner).to be_success
    cloned_serving_size = serving_cloner.cloned_serving_size

    expect(cloned_serving_size).to be_present
    expect(cloned_serving_size.id).to_not eq(serving_size.id)

    # different data
    expect(cloned_serving_size.weight).to_not eq(serving_size.weight)
    expect(cloned_serving_size.menu_item).to_not eq(serving_size.menu_item)
    expect(cloned_serving_size.menu_item).to eq(other_menu_item)

    # cloned data
    expect(cloned_serving_size.name).to eq(serving_size.name)
    expect(cloned_serving_size.price).to eq(serving_size.price)
  end

  it 'does not clone an archvied serving size' do
    serving_size.update_column(:archived_at, Time.zone.now)
    serving_cloner = ServingSizes::Clone.new(serving_size: serving_size).call

    expect(serving_cloner).to_not be_success
    expect(serving_cloner.errors).to include('Cannot clone an archived serving size')
  end

end
