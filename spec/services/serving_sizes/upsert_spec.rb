require 'rails_helper'

RSpec.describe ServingSizes::Upsert, type: :service, serving_sizes: true do

  let(:supplier) { create(:supplier_profile, :random, :with_flags, commission_rate: 10, markup: 0) }
  let(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }

  it 'lets you create a new serving size' do
    serving_size_params = { name: Faker::Name.name, price: 10.2, menu_item: menu_item }
    serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params).call

    expect(serving_creator).to be_success
    created_serving_size = serving_creator.serving_size
    expect(created_serving_size).to be_present
    expect(created_serving_size).to be_persisted
  end

  it 'creates a new serving size within the passed menu item' do
    serving_size_params = { name: Faker::Name.name, price: 10.2, menu_item: menu_item }
    serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params).call

    expect(serving_creator).to be_success
    created_serving_size = serving_creator.serving_size
    expect(created_serving_size.menu_item).to eq(menu_item)
  end

  it 'create a new serving size with default params' do
    serving_size_params = { name: Faker::Name.name, price: 10.2, menu_item: menu_item }
    serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params).call

    expect(serving_creator).to be_success
    created_serving_size = serving_creator.serving_size
    expect(created_serving_size.weight).to be_present
    expect(created_serving_size.weight).to eq(ServingSize.pluck(:weight).compact.max)
  end

  it 'create a new serving size with passed in params' do
    serving_size_params = { name: Faker::Name.name, price: 10.2, menu_item: menu_item, weight: 10 }
    serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params).call

    expect(serving_creator).to be_success
    created_serving_size = serving_creator.serving_size
    expect(created_serving_size.name).to eq(serving_size_params[:name])
    expect(created_serving_size.price).to eq(serving_size_params[:price])
    expect(created_serving_size.weight).to eq(serving_size_params[:weight])
  end

  it 'sanitizes name (with spaces) when saving' do
    name_with_space = [" #{Faker::Name.name}", "#{Faker::Name.name} "].sample
    serving_size_params = { name: name_with_space, price: 10.2, menu_item: menu_item }
    serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params).call

    expect(serving_creator).to be_success
    created_serving_size = serving_creator.serving_size
    expect(created_serving_size.name).to eq(name_with_space.strip)
  end

  it 'sets the supplier\'s menu last updated datetime' do
    serving_size_params = { name: Faker::Name.name, price: 10.2, menu_item: menu_item }
    serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params).call
    expect(serving_creator).to be_success

    expect(supplier.reload.menu_last_updated_on).to be_present
    expect(supplier.reload.menu_last_updated_on).to be_within(2.seconds).of(Time.zone.now)
  end

  context 'with existing serving size' do
    let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }

    it 'does not creates a new serving size with the name of an existing serving size (instead it updates) ' do
      serving_size_name = [serving_size.name, " #{serving_size.name}", "#{serving_size.name} "].sample
      serving_size_params = { name: serving_size_name, price: 10.2, menu_item: menu_item }
      serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params).call

      expect(serving_creator).to be_success
      created_serving_size = serving_creator.serving_size
      expect(created_serving_size.name).to eq(serving_size.name)
      expect(created_serving_size.id).to eq(serving_size.id) # same serving size
      expect(created_serving_size.price).to eq(serving_size_params[:price])
    end

    it 'creates a new serving size with the name of an existing serving size if it does not belong to the menu item' do
      menu_item2 = create(:menu_item, :random)
      serving_size_params = { name: serving_size.name, price: 10.2, menu_item: menu_item2 }
      serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params).call

      expect(serving_creator).to be_success
      created_serving_size = serving_creator.serving_size
      expect(created_serving_size.id).to_not eq(serving_size.id) # not the same serving size
      expect(created_serving_size.name).to eq(serving_size.name)
      expect(created_serving_size.menu_item).to eq(menu_item2)
    end

    it 'creates a new serving size with the name of an existing serving size if forced' do
      serving_size_params = { name: serving_size.name, price: 10.2, menu_item: menu_item }
      serving_creator = ServingSizes::Upsert.new(serving_size_params: serving_size_params, forced: true).call

      expect(serving_creator).to be_success
      created_serving_size = serving_creator.serving_size
      expect(created_serving_size.name).to eq(serving_size.name)
      expect(created_serving_size.id).to_not eq(serving_size.id) # not the same serving size
    end

    it 'updates the serving size with passed in params' do
      serving_size_params = { name: Faker::Name.name, menu_item: menu_item, weight: 10 }
      serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call

      expect(serving_updator).to be_success
      updated_serving_size = serving_updator.serving_size
      expect(updated_serving_size.id).to eq(serving_size.id)
      expect(updated_serving_size.name).to eq(serving_size_params[:name])
      expect(updated_serving_size.menu_item).to eq(menu_item)
      expect(updated_serving_size.weight).to eq(serving_size_params[:weight])
    end

    context 'stock quantity' do
      it 'saves the passed in stock quantity (number)' do
        stock_quantity = [0, 10, 3, '15', '2'].sample
        serving_size_params = { stock_quantity: stock_quantity }
        serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call

        expect(serving_updator).to be_success
        updated_serving_size = serving_updator.serving_size
        expect(updated_serving_size.stock_quantity).to eq(stock_quantity.to_i)
      end

      it 'sanitizes the stock quantity value as nil if not a number' do
        serving_size.update_column(:stock_quantity, rand(10..100))
        stock_quantity = ['', ' ', nil].sample
        serving_size_params = { stock_quantity: stock_quantity }
        serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call

        expect(serving_updator).to be_success
        updated_serving_size = serving_updator.serving_size
        expect(updated_serving_size.stock_quantity).to be_nil
      end
    end

    it 're-sets the supplier\'s menu last updated datetime' do
      serving_size_params = { name: Faker::Name.name, menu_item: menu_item, weight: 10 }
      serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call
      expect(serving_updator).to be_success

      expect(supplier.reload.menu_last_updated_on).to be_present
      expect(supplier.reload.menu_last_updated_on).to be_within(2.seconds).of(Time.zone.now)
    end

    context 'with exiting order lines containing serving size' do
      let(:future_order) { create(:order, :draft) }
      let(:location) { create(:location, :random, order: future_order) }
      let!(:order_line) { create(:order_line, :random, location: location, menu_item: menu_item, order: future_order, supplier_profile: supplier) }
      let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
      let!(:serving_size_order_line) { create(:order_line, :random, location: location, menu_item: menu_item, serving_size: serving_size, order: future_order, supplier_profile: supplier) }

      it 'updates the future order lines if the serving size name is updated' do
        serving_size_params = { name: Faker::Name.name }
        serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call

        expect(serving_updator).to be_success
        serving_size_order_line.reload
        expect(serving_size_order_line.name).to include(serving_size_params[:name])

        order_line.reload
        expect(order_line.name).to_not include(serving_size_params[:name])
      end

      it 'updates the future order lines if the serving size price is updated' do
        serving_size_params = { price: 15.89 }
        serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call

        expect(serving_updator).to be_success
        serving_size_order_line.reload
        expect(serving_size_order_line.price.round(2)).to eq(serving_size_params[:price])
        expect(serving_size_order_line.cost.round(2)).to eq(14.30)

        order_line.reload
        expect(order_line.price.round(2)).to_not eq(serving_size_params[:price])
        expect(order_line.cost.round(2)).to_not eq(14.30)
      end

      context 'update as a Delayed Job', delayed_jobs: true do
        let!(:serving_size_params) { { price: 15.89 } }
        let!(:order_line_update_checker) { double(OrderLines::FutureUpdateExists) }

        before do
          # do not run the delayed jobs inline, instead create a Delayed::Job record
          Delayed::Worker.delay_jobs = true

          # mock if a future order line update exists
          allow(OrderLines::FutureUpdateExists).to receive(:new).and_return(order_line_update_checker)
          allow(order_line_update_checker).to receive(:call).and_return(false)
        end

        after do
          # revert back to running delayed jobs in line
          Delayed::Worker.delay_jobs = false
        end

        it 'creates a Delayed Job to update future order lines' do
          serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params)
          expect{ serving_updator.call }.to change{ Delayed::Job.count }.from(0).to(1)

          delayed_job = Delayed::Job.last
          expect(delayed_job).to be_present
          expect(delayed_job.handler).to include('MenuItems::UpdateFutureOrderLines')

          job_handler = YAML.load(delayed_job.handler)
          expect(job_handler.menu_item).to eq(menu_item)
          expect(job_handler.serving_size).to eq(serving_size)
        end

        it 'doesn\'t create a second delayed job to update future order lines if one already exists' do
          # create a serving size update
          MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size).delay(queue: :data_integrity).call
          expect(Delayed::Job.count).to eq(1)

          # mock an update exists
          allow(order_line_update_checker).to receive(:call).and_return(true)

          serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params)
          expect{ serving_updator.call }.not_to change{ Delayed::Job.count }.from(1)
        end
      end # update as a Delayed Job
    end # with existing order lines
  end # with existing serving size
end
