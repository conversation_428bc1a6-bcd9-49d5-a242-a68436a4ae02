require 'rails_helper'

RSpec.describe ServingSizes::Archive, type: :service, serving_sizes: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_flags) }
  let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }
  let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }

  it 'archives the serving size' do
    archiver = ServingSizes::Archive.new(serving_size: serving_size).call

    expect(archiver).to be_success
    expect(serving_size.reload.archived_at).to be_present
  end

  it 'sets the supplier\'s menu last updated datetime' do
    archiver = ServingSizes::Archive.new(serving_size: serving_size).call
    expect(archiver).to be_success

    expect(supplier.reload.menu_last_updated_on).to be_present
    expect(supplier.reload.menu_last_updated_on).to be_within(2.seconds).of(Time.zone.now)
  end

  describe 'errors' do

    it 'cannot archive a missing serving size' do
      archiver = ServingSizes::Archive.new(serving_size: nil).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archive a missing serving size')
    end

    it 'cannot archive already archived serving size' do
      serving_size.update_column(:archived_at, Time.zone.now)

      archiver = ServingSizes::Archive.new(serving_size: serving_size).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archive an already archived serving size')
    end

  end
end
