require 'rails_helper'

RSpec.describe Invoices::GetDates, type: :service, invoices: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:invoice) { create(:invoice, :random) }

  let!(:order1) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: invoice) }
  let!(:order2) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: invoice) }

  it 'returns the previous day of the invoice created at (default billing frequency of instantly)' do
    invoice_dates = Invoices::GetDates.new(invoice: invoice).call

    previous_day = invoice.created_at - 1.day

    expect(invoice_dates.from).to eq(previous_day.beginning_of_day)
    expect(invoice_dates.to).to eq(previous_day.end_of_day)
  end

  it 'returns the same values as the defunct invoice_to_from_date method' do
    invoice_dates = Invoices::GetDates.new(invoice: invoice).call

    expect(invoice_dates.from).to eq(invoice.invoice_to_from_date[:from_date])
    expect(invoice_dates.to).to eq(invoice.invoice_to_from_date[:to_date])
  end

  context 'with customer billing details' do
    let!(:billing_details) { create(:billing_details, :random, customer_profile: customer) }

    context 'billing frequency of instantly' do
      before do
        billing_details.update_column(:frequency, 'instantly')
      end

      it 'returns the previous day of the invoice created at' do
        invoice_dates = Invoices::GetDates.new(invoice: invoice).call

        previous_day = invoice.created_at - 1.day

        expect(invoice_dates.from).to eq(previous_day.beginning_of_day)
        expect(invoice_dates.to).to eq(previous_day.end_of_day)

        # similar to defunct method
        expect(invoice_dates.from).to eq(invoice.invoice_to_from_date[:from_date])
        expect(invoice_dates.to).to eq(invoice.invoice_to_from_date[:to_date])
      end
    end

    context 'billing frequency of weekly' do
      before do
        billing_details.update_column(:frequency, 'weekly')
      end

      it 'returns the previous week dates of the invoice created at' do
        invoice_dates = Invoices::GetDates.new(invoice: invoice).call

        previous_week = invoice.created_at - 1.week

        expect(invoice_dates.from).to eq(previous_week.beginning_of_week)
        expect(invoice_dates.to).to eq(previous_week.end_of_week)

        # similar to defunct method
        expect(invoice_dates.from).to eq(invoice.invoice_to_from_date[:from_date])
        expect(invoice_dates.to).to eq(invoice.invoice_to_from_date[:to_date])
      end
    end

    context 'billing frequency of monthly' do
      before do
        billing_details.update_column(:frequency, 'monthly')
      end

      it 'returns the previous months dates of the invoice created at' do
        invoice_dates = Invoices::GetDates.new(invoice: invoice).call

        previous_month = invoice.created_at - 1.month

        expect(invoice_dates.from).to eq(previous_month.beginning_of_month)
        expect(invoice_dates.to).to eq(previous_month.end_of_month)

        # similar to defunct method
        expect(invoice_dates.from).to eq(invoice.invoice_to_from_date[:from_date])
        expect(invoice_dates.to).to eq(invoice.invoice_to_from_date[:to_date])
      end
    end
  end

  context 'due dates' do
    it 'returns a due date based on default Invoice payment terms' do
      invoice_dates = Invoices::GetDates.new(invoice: invoice).call

      expect(invoice_dates.due).to eq(invoice_dates.to.beginning_of_day + Invoice::DEFAULT_PAYMENT_DAYS.days)
    end

    it 'returns a due date based on customer\'s company\'s  payment term days' do
      company = create(:company, :random, payment_term_days: Company::VALID_PAYMENT_TERM_DAYS.sample)
      customer.update_column(:company_id, company.id)

      invoice_dates = Invoices::GetDates.new(invoice: invoice).call
      expect(invoice_dates.due).to eq(invoice_dates.to.beginning_of_day + company.payment_term_days.days)
    end
  end

end
