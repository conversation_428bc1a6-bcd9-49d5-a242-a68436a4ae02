require 'rails_helper'

RSpec.describe Invoices::AttachOrders, type: :service, invoices: true do

  let!(:customer) { create(:customer_profile, :random, :with_flags) }
  let!(:invoice) { create(:invoice, :random, customer_profile: customer) }

  let!(:order1) { create(:order, :random, name: 'order1', customer_profile: customer) }
  let!(:order2) { create(:order, :random, name: 'order2', customer_profile: customer) }
  let!(:order3) { create(:order, :random, name: 'order3', customer_profile: customer) }

  let!(:orders) { [order1, order2, order3] }

  let!(:invoice_totals) do
    totals = Invoices::CalculateTotals::Totals.new(invoice_orders: orders)
    totals.total = 100.0
    totals
  end

  before do
    totals_calculator = double(Invoices::CalculateTotals)
    allow(Invoices::CalculateTotals).to receive(:new).and_return(totals_calculator)
    allow(totals_calculator).to receive(:call).and_return(invoice_totals)
  end

  it 'attaches the customers orders to the invoice' do
    invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

    expect(invoice_attacher).to be_success
    orders.each(&:reload)

    expect(order1.invoice).to eq(invoice)
    expect(order2.invoice).to eq(invoice)
    expect(order3.invoice).to eq(invoice)
  end

  it 'makes a request to calculate totals for the invoice' do
    expect(Invoices::CalculateTotals).to receive(:new).with(invoice: invoice)

    invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call
    expect(invoice_attacher).to be_success
  end

  it 'updates the amount price of the invoice based on attached orders' do
    invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

    expect(invoice_attacher).to be_success

    updated_invoice = invoice_attacher.invoice
    expect(updated_invoice.id).to eq(invoice.id) # original invoice
    expect(updated_invoice.amount_price.round(2).to_s).to eq(invoice_totals.total.round(2).to_s) # mock calculated invoice totals
  end

  context 'update invoice status' do
    it 'resets the invoice status of a draft / voided / deleted invoice to pending' do
      invoice.update_column(:status, %w[draft voided deleted].sample)

      invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call
      expect(invoice_attacher).to be_success

      updated_invoice = invoice_attacher.invoice
      expect(updated_invoice.status).to eq('pending')
    end

    it 'resets the invoice status of a confirmed / amended invoice to amended' do
      invoice.update_column(:status, %w[amended confirmed].sample)

      invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call
      expect(invoice_attacher).to be_success

      updated_invoice = invoice_attacher.invoice
      expect(updated_invoice.status).to eq('amended')
    end
  end

  it 'only attaches invoice to the orders belonging to the customer' do
    customer2 = create(:customer_profile, :random)
    order2.update_column(:customer_profile_id, customer2.id)

    invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

    expect(invoice_attacher).to be_success
    orders.each(&:reload)

    expect(order1.invoice).to eq(invoice)
    expect(order2.invoice).to_not eq(invoice)
    expect(order3.invoice).to eq(invoice)
  end

  context 'with existing invoice orders' do
    let!(:order4) { create(:order, :random, customer_profile: customer, customer_total: rand(100.01..199.99), update_with_invoice: true, invoice: invoice) }
    let!(:order5) { create(:order, :random, customer_profile: customer, customer_total: rand(100.01..199.99), update_with_invoice: true, invoice: invoice) }

    it 'detaches existing orders that are not passed in orders argument' do
      invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

      expect(invoice_attacher).to be_success

      updated_invoice = invoice_attacher.invoice
      expect(updated_invoice.id).to eq(invoice.id) # original invoice
      expect(invoice.orders).to include(order1, order2, order3)
      expect(invoice.orders).to_not include(order4, order5)

      expect(order4.reload.invoice_id).to be_nil
      expect(order5.reload.invoice_id).to be_nil
    end

    it 'updates the amount price of the invoice based only the attached orders' do
      invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

      expect(invoice_attacher).to be_success

      updated_invoice = invoice_attacher.invoice
      expect(updated_invoice.id).to eq(invoice.id) # original invoice
      expect(updated_invoice.amount_price.round(2).to_s).to eq(invoice_totals.total.round(2).to_s) # mock calculated invoice totals
    end
  end

  context 'with GST split invoicing', gst_split_invoicing: true do
    let!(:gst_po) { create(:customer_purchase_order, :random, po_number: 'GST PO', customer_profile: customer) }
    let!(:gst_free_po) { create(:customer_purchase_order, :random, po_number: 'GST-Free PO', customer_profile: customer) }

    let!(:order_line11) { create(:order_line, :random, order: order1, status: 'accepted', is_gst_free: true, quantity: 5, price: 10) }
    let!(:order_line12) { create(:order_line, :random, order: order1, status: 'accepted', is_gst_free: false, quantity: 4, price: 10) }

    let!(:order_line21) { create(:order_line, :random, order: order2, status: 'accepted', is_gst_free: true, quantity: 5, price: 10) }
    let!(:order_line22) { create(:order_line, :random, order: order2, status: 'accepted', is_gst_free: false, quantity: 4, price: 10) }

    let!(:order_line31) { create(:order_line, :random, order: order3, status: 'accepted', is_gst_free: false, quantity: 6, price: 10) }
    let!(:order_line32) { create(:order_line, :random, order: order3, status: 'accepted', is_gst_free: true, quantity: 3, price: 10) }

    before do
      [order1, order2, order3].each do |order|
        order.update_column(:cpo_id, gst_po.id)
        order.update_column(:gst_free_cpo_id, gst_free_po.id)
      end

      # set custom has required gst split invoices
      customer.customer_flags.update_column(:has_gst_split_invoicing, true)
    end

    context 'for GST PO' do
      before do
        invoice.update_column(:cpo_id, gst_po.id)
      end

      it 'attaches the customers orders to the invoice' do
        invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

        expect(invoice_attacher).to be_success
        orders.each(&:reload)

        expect(order1.invoice).to eq(invoice)
        expect(order2.invoice).to eq(invoice)
        expect(order3.invoice).to eq(invoice)
        expect(invoice.orders).to include(order1, order2, order3)
      end
    end # for GST PO

    context 'for GST Free PO' do
      before do
        invoice.update_column(:cpo_id, gst_free_po.id)
      end

      it 'attaches the Invoice to order as a GST Free Invoice' do
        invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

        expect(invoice_attacher).to be_success
        orders.each(&:reload)

        expect(order1.gst_free_invoice).to eq(invoice)
        expect(order2.gst_free_invoice).to eq(invoice)
        expect(order3.gst_free_invoice).to eq(invoice)

        expect(invoice.gst_free_orders).to include(order1, order2, order3)
      end

      context 'with GST-free PO saved as Normal PO' do
        before do
          order2.update_columns(cpo_id: gst_free_po.id, gst_free_cpo_id: nil)
        end

        it 'attaches the invoice to the order according to the PO being GST free or not' do
          invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

          expect(invoice_attacher).to be_success
          orders.each(&:reload)

          expect(order1.gst_free_invoice).to eq(invoice)
          expect(order2.invoice).to eq(invoice) # PO is saved as normal PO
          expect(order3.gst_free_invoice).to eq(invoice)

          expect(invoice.gst_free_orders).to include(order1, order3)
          expect(invoice.orders).to include(order2)
        end
      end

      context 'order does not have invoice PO (either as normal PO or GST-Free)' do
        before do
          order3.update_columns(gst_free_cpo_id: nil)
        end

        it 'does not attach invoice to order that does not contain the invoice PO' do
          invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

          expect(invoice_attacher).to be_success
          orders.each(&:reload)

          expect(order1.gst_free_invoice).to eq(invoice)
          expect(order2.gst_free_invoice).to eq(invoice)

          expect(order3.invoice).to be_blank
          expect(order3.gst_free_invoice).to be_blank

          expect(invoice.gst_free_orders).to include(order1, order2)
        end
      end

      context 'with existing invoice orders (gst and/or gst-free)' do
        let!(:order4) { create(:order, :random, name: 'order4', customer_profile: customer, update_with_invoice: true, invoice: invoice) }
        let!(:order5) { create(:order, :random, name: 'order5', customer_profile: customer, update_with_invoice: true, gst_free_invoice: invoice) }

        it 'detaches existing orders that are not passed in orders argument' do
          invoice_attacher = Invoices::AttachOrders.new(invoice: invoice, orders: orders).call

          expect(invoice_attacher).to be_success
          updated_invoice = invoice_attacher.invoice
          expect(updated_invoice.id).to eq(invoice.id) # original invoice

          expect(invoice.gst_free_orders).to include(order1, order2, order3)
          expect(invoice.orders).to_not include(order4, order5)
          expect(invoice.gst_free_orders).to_not include(order4, order5)

          expect(order4.reload.invoice_id).to be_nil
          expect(order5.reload.gst_free_invoice_id).to be_nil
        end
      end # with existing PO
    end # for GST-free PO
  end # GST split Invoicing
end