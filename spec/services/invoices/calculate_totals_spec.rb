require 'rails_helper'

RSpec.describe Invoices::CalculateTotals, type: :service, invoices: true, orders: true do

  subject { Invoices::CalculateTotals.new(invoice: invoice).call }

  let!(:invoice) { create(:invoice, :random) }
  let!(:customer) { create(:customer_profile, :random, :with_flags) }
  let!(:order1) { create(:order, :random, customer_profile: customer, customer_subtotal: 12, customer_delivery: 5, discount: 18, customer_gst: 1.7, customer_topup: 0, customer_surcharge: 1, customer_total: 22.7, update_with_invoice: true, invoice: invoice) }
  let!(:order2) { create(:order, :random, customer_profile: customer, customer_subtotal: 24, customer_delivery: 15, discount: nil, customer_gst: 3.9, customer_topup: 10, customer_surcharge: 2, customer_total: 47.9, update_with_invoice: true, invoice: invoice) }

  it 'returns its number of orders' do
    expect(subject.order_count).to eq(2)
  end

  it 'returns its orders subtotal' do
    expect(subject.subtotal).to eq(36) # sum of order.customer_subtotal
  end

  it 'returns its orders\' delivery fee' do
    expect(subject.delivery).to eq(20) # sum of order.customer_delivery
  end

  it 'returns its orders\' discount' do
    expect(subject.discount).to eq(18) # sum of order.discount
  end

  it 'returns its orders\' topup' do
    expect(subject.topup).to eq(10) # sum of order.customer_topup
  end

  it 'returns its orders\' gst' do
    expect(subject.gst).to eq(5.6) # sum of order.customer_gst
  end

  it 'returns its orders\' surcharge' do
    expect(subject.surcharge).to eq(3) # sum of order.customer_surcharge
  end

  it 'returns its orders\' total' do
    expect(subject.total.to_s).to eq(70.6.to_s) # sum of order.customer_total
  end

  context 'gst based values' do
    before do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(0.1)
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(0.15)
    end

    it 'returns its orders\' gst total' do
      expect(subject.gst_total).to eq(56) # subject.gst / yordar_credentials(:yordar, :gst_percent, :au)
    end

    it 'returns its orders\' non-gst total' do
      expect(subject.non_gst_total.to_s).to eq(9.0.to_s) # total - gst_total - gst
    end

    it 'returns non-gst total 0 if non-gst total is less than 0' do
      order1.update_column(:customer_gst, 5.6)
      expect(subject.non_gst_total.to_s).to eq(0.to_s)
    end
  end

  context 'with GST split invoicing', gst_split_invoicing: true do
    let!(:gst_po) { create(:customer_purchase_order, :random, customer_profile: customer, po_number: 'GST PO') }
    let!(:gst_free_po) { create(:customer_purchase_order, :random, customer_profile: customer, po_number: 'GST-Free PO') }

    let!(:order3) { create(:order, :random, customer_profile: customer, customer_purchase_order: gst_po, customer_subtotal: 10, customer_delivery: 8, discount: 5, customer_gst: 1.8, customer_topup: 0, customer_surcharge: 3, customer_total: 17.8, update_with_invoice: true, invoice: invoice) }
    let!(:order4) { create(:order, :random, customer_profile: customer, customer_purchase_order: gst_free_po, customer_subtotal: 40, customer_delivery: 10, discount: 0, customer_gst: 5, customer_topup: 10, customer_surcharge: 0, customer_total: 65, update_with_invoice: true, invoice: invoice) }
    let!(:order5) { create(:order, :random, customer_profile: customer, customer_purchase_order: gst_po, gst_free_customer_purchase_order: gst_free_po, customer_subtotal: 300, customer_delivery: 20, discount: 50, customer_gst: 27, customer_topup: 0, customer_surcharge: 2, customer_total: 299, update_with_invoice: true, gst_free_invoice: invoice) }

    let!(:order_totals) do
      totals = Orders::CalculateCustomerTotals::Totals.new(order_line_count: 2)
      totals.subtotal = 20
      totals.delivery = 10
      totals.discount = 5
      totals.topup = 10
      totals.gst = 1.5
      totals.surcharge = 3
      totals.total = 40
      totals
    end

    before do
      # make customer have split invoicing
      customer.customer_flags.update_column(:has_gst_split_invoicing, true)

      # mock order totals calculations
      order_totals_calculator = double(Orders::CalculateCustomerTotals)
      allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(order_totals_calculator)
      allow(order_totals_calculator).to receive(:call).and_return(order_totals)
    end

    context 'with GST PO' do
      before do
        invoice.update_column(:cpo_id, gst_po.id)
      end

      it 'makes a request to calculate totals only for orders with GST split PO and the Invoice PO as the gst/normal POs' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order5, gst_split: 'GST') # has split POs

        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order1, gst_split: anything) # no POs
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order2, gst_split: anything) # no POs
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order3, gst_split: anything) # only invoice PO
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order4, gst_split: anything) # only invoice PO

        subject
      end
    end

    context 'with GST PO' do
      before do
        invoice.update_column(:cpo_id, gst_free_po.id)
      end

      it 'makes a request to calculate totals only for orders with GST split POs and the Invoice PO as the gst-free PO' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order5, gst_split: 'GST-FREE') # has split POs

        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order1, gst_split: anything) # no POs
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order2, gst_split: anything) # no POs
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order3, gst_split: anything) # no split POs
        expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order4, gst_split: anything) # no split POs

        subject
      end
    end

    it 'returns the totals including the GST split calculated totals' do
      invoice.update_column(:cpo_id, [gst_po.id, gst_free_po.id].sample)

      expect(subject.order_count).to eq(5)
      expect(subject.subtotal).to eq(106) # sum of order1-4.customer_subtotal + calculated subtotal of order 5
      expect(subject.delivery).to eq(48) # sum of order1-4.customer_delivery + calculated delivery of order 5
      expect(subject.discount).to eq(28) # sum of order1-4.discount + calculated discount of order 5
      expect(subject.topup).to eq(30) # sum of order1-4.customer_topup + calculated topup of order 5
      expect(subject.gst).to eq(13.9) # sum of order1-4.customer_gst + calculated gst of order 5
      expect(subject.surcharge).to eq(9) # sum of osum of order1-4.customer_surcharge + calculated surcharge of order 5
      expect(subject.total.to_s).to eq(193.4.to_s) # sum of order1-4.customer_total + calculated total of order 5
    end
  end
end
