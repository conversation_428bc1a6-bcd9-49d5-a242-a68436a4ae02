require 'rails_helper'

RSpec.describe Invoices::GenerateDocuments, type: :service, invoices: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:billing_details) { create(:billing_details, :random, customer_profile: customer) }

  let!(:invoice) { create(:invoice, :random) }

  let!(:order1) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: invoice) }
  let!(:order2) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: invoice) }

  let!(:tax_invoice_document) { create(:document, :random, documentable: invoice, kind: 'tax_invoice') }

  before do
    customer.reload
    # mock  document generator
    order_document_generator = double(Documents::Generate::CustomerOrderDetails)
    allow(Documents::Generate::CustomerOrderDetails).to receive(:new).and_return(order_document_generator)
    allow(order_document_generator).to receive(:call).and_return(true)

    # mock invoice document generator
    invoice_document_generator = double(Documents::Generate::TaxInvoice)
    allow(Documents::Generate::TaxInvoice).to receive(:new).and_return(invoice_document_generator)
    allow(invoice_document_generator).to receive(:call).and_return(tax_invoice_document)

    # mock the email sender
    email_sender = double(Customers::Emails::SendOrderInvoiceEmail)
    allow(Customers::Emails::SendOrderInvoiceEmail).to receive(:new).and_return(email_sender)
    successfull_email = OpenStruct.new(success?: true, sent_notification: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_email)
  end

  context 'customer order details document(s)' do
    it 'makes a request to generate document for all invoice orders' do
      expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order1)
      expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order2)

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
    end

    it 'only requests new customer order details documents to be generate if not already present' do
      create(:document, :random, documentable: order2, kind: 'customer_order_details')

      expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order1)
      expect(Documents::Generate::CustomerOrderDetails).to_not receive(:new).with(order: order2)

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
    end
  end

  context 'with a customer billing frequency of instantly' do
    before do
      billing_details.update_column(:frequency, 'instantly')
    end

    it 'creates generates tax_invoice pdf (document) for the customers order' do
      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice')

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
    end

    it 'only generates tax invoice pdf even if customer needs tax invoice spreadsheet' do
      billing_details.update_columns(invoice_spreadsheet: true)

      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice')
      expect(Documents::Generate::TaxInvoice).to_not receive(:new).with(invoice: invoice, document_type: 'tax_invoice_spreadsheet')

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
    end

    it 'returns the generated `document` record within generated documents' do
      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call

      expect(documents_generator).to be_success
      generated_documents = documents_generator.generated_documents
      expect(generated_documents.size).to eq(1)
      expect(generated_documents).to include(tax_invoice_document)
    end
  end

  context 'with a customer billing frequency of weekly' do
    before do
      billing_details.update_column(:frequency, 'weekly')
    end

    it 'creates generates tax_invoice pdf (document) for the customers orders' do
      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice')

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
    end

    it 'generates tax invoice pdf (with reports) and the tax invoice spreadsheet if customer needs tax invoice spreadsheets' do
      billing_details.update_column(:invoice_spreadsheet, true)

      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice')
      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice_spreadsheet')

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
    end

    it 'returns the generated `document` record(s) within generated documents' do
      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call

      expect(documents_generator).to be_success
      generated_documents = documents_generator.generated_documents
      expect(generated_documents.size).to eq(1)
      expect(generated_documents).to include(tax_invoice_document)
    end

    context 'with invoice containing a single order' do
      let!(:single_order_invoice) { create(:invoice, :random) }
      let!(:order3) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: single_order_invoice) }

      it 'generates the tax invoice document passing the single order invoice' do
        expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: single_order_invoice, document_type: 'tax_invoice')

        documents_generator = Invoices::GenerateDocuments.new(invoice: single_order_invoice).call
        expect(documents_generator).to be_success
      end

      it 'does not generate tax invoice spreadsheet even if customer needs tax invoice spreadsheet' do
        billing_details.update_column(:invoice_spreadsheet, true)

        expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: single_order_invoice, document_type: 'tax_invoice')
        expect(Documents::Generate::TaxInvoice).to_not receive(:new).with(invoice: single_order_invoice, document_type: 'tax_invoice_spreadsheet')

        documents_generator = Invoices::GenerateDocuments.new(invoice: single_order_invoice).call
        expect(documents_generator).to be_success
      end
    end
  end

  context 'with no billing details (same as billing frequency == instantly)' do
    before do
      billing_details.destroy
      customer.reload
    end
    it 'creates a tax invoice document for the customers orders as usual' do
      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice')

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
      generated_documents = documents_generator.generated_documents
      expect(generated_documents.size).to eq(1)
      expect(generated_documents).to include(tax_invoice_document)
    end

    it 'does not generate any spreadsheet' do
      expect(Documents::Generate::TaxInvoice).to_not receive(:new).with(invoice: invoice, document_type: 'tax_invoice_spreadsheet')

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
      generated_documents = documents_generator.generated_documents
      expect(generated_documents.size).to eq(1)
      expect(generated_documents).to include(tax_invoice_document)
    end
  end

  context 'with a customer billing frequency of monthly' do
    before do
      billing_details.update_column(:frequency, 'monthly')
    end

    it 'only generates the tax invoice pdf for the generated invoice' do
      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice')

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
      generated_documents = documents_generator.generated_documents
      expect(generated_documents.size).to eq(1)
      expect(generated_documents).to include(tax_invoice_document)
    end

    it 'generates tax invoice spreadsheet (split document) for the customer who needs tax invoice spreadsheet' do
      billing_details.update_column(:invoice_spreadsheet, true)

      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice')
      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: invoice, document_type: 'tax_invoice_spreadsheet')

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call
      expect(documents_generator).to be_success
      generated_documents = documents_generator.generated_documents
      expect(generated_documents.size).to eq(2)
      expect(generated_documents).to include(tax_invoice_document)
    end

    context 'for a invoice containing a single order' do
      let!(:single_order_invoice) { create(:invoice, :random) }
      let!(:order3) { create(:order, :delivered, customer_profile: customer, update_with_invoice: true, invoice: single_order_invoice) }

      it 'generates just the tax invoice PDF' do
        expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: single_order_invoice, document_type: 'tax_invoice')

        documents_generator = Invoices::GenerateDocuments.new(invoice: single_order_invoice).call
        expect(documents_generator).to be_success
      end

      it 'does not generate tax invoice spreadsheet even if customer needs tax invoice spreadsheet' do
        billing_details.update_columns(invoice_spreadsheet: true)

        expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: single_order_invoice, document_type: 'tax_invoice')
        expect(Documents::Generate::TaxInvoice).to_not receive(:new).with(invoice: single_order_invoice, document_type: 'tax_invoice_spreadsheet')

        documents_generator = Invoices::GenerateDocuments.new(invoice: single_order_invoice).call
        expect(documents_generator).to be_success
      end
    end
  end

  context 'update invoice status' do
    it 'updates the invoice to be confirmed' do
      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice).call

      expect(documents_generator).to be_success
      expect(invoice.reload.status).to eq('confirmed')
    end
  end

  context 'notify customer' do
    it 'sends an email to the customer' do
      expect(Customers::Emails::SendOrderInvoiceEmail).to receive(:new).with(customer: customer, invoice: invoice, documents: [tax_invoice_document], is_regenerate: false)

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice, notify_customer: true).call
      expect(documents_generator).to be_success
    end

    it 'sends an email to the customer with regenerate argument' do
      expect(Customers::Emails::SendOrderInvoiceEmail).to receive(:new).with(customer: customer, invoice: invoice, documents: [tax_invoice_document], is_regenerate: true)

      documents_generator = Invoices::GenerateDocuments.new(invoice: invoice, notify_customer: true, regenerate: true).call
      expect(documents_generator).to be_success
    end
  end
end
