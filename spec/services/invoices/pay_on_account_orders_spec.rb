require 'rails_helper'

RSpec.describe Invoices::PayOnAccountOrders, type: :service, orders: true, invoices: true, payments: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:pay_on_account_card) { create(:credit_card, :on_account_card) }
  let!(:credit_card) { create(:credit_card, :random) }

  let!(:order1) { create(:order, :delivered, customer_profile: customer, customer_total: 1000, payment_status: 'unpaid', credit_card: pay_on_account_card) }
  let!(:order2) { create(:order, :delivered, customer_profile: customer, customer_total: 2000, payment_status: 'unpaid', credit_card: pay_on_account_card) }
  let!(:order3) { create(:order, :delivered, customer_profile: customer, customer_total: 3000, payment_status: 'unpaid', credit_card: pay_on_account_card) }

  let!(:invoice) { create(:invoice, :random, amount_price: [order1, order2, order3].sum(&:customer_total)) }

  before do
    # attach order to invoice
    [order1, order2, order3].each do |order|
      order.update_with_invoice = true
      order.update_column(:invoice_id, invoice.id)
    end
    # mock payment processor
    payment_processor = double(Payments::ProcessPayment)
    allow(Payments::ProcessPayment).to receive(:new).and_return(payment_processor)
    successfull_payment_reponse = OpenStruct.new(success?: true, payment: 'payment', errors: [])
    allow(payment_processor).to receive(:call).and_return(successfull_payment_reponse)

    # mock email sender
    email_sender = delayed_email_sender = double(Customers::Emails::SendInvoiceReceiptEmail)
    allow(Customers::Emails::SendInvoiceReceiptEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock charge refunder
    charge_refunder = double(Stripe::RefundOrderCharge)
    allow(Stripe::RefundOrderCharge).to receive(:new).and_return(charge_refunder)
    stripe_refund = OpenStruct.new(id: SecureRandom.hex(7))
    refunder_response = OpenStruct.new(success?: true, refund: stripe_refund)
    allow(charge_refunder).to receive(:call).and_return(refunder_response)
  end

  it 'creates a payment for the unpaid invoice orders' do
    on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
    expect(on_account_orders_payer).to be_success

    created_payment = on_account_orders_payer.payment

    expect(created_payment).to be_present
    expect(created_payment.invoice).to eq(invoice)
    expect(created_payment.credit_card).to eq(credit_card)

    expect(created_payment.amount).to eq([order1, order2, order3].sum(&:customer_total))
    expect(created_payment.order_id).to be_in([order1, order2, order3].map(&:id))
    expect(created_payment.user_id).to eq(customer.user.id)
  end

  it 'processes the payment' do
    expect(Payments::ProcessPayment).to receive(:new)

    on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
    expect(on_account_orders_payer).to be_success
  end

  it 'updates the orders credit card' do
    on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
    expect(on_account_orders_payer).to be_success

    [order1, order2, order3].each do |order|
      expect(order.reload.credit_card).to eq(credit_card)
    end
  end

  it 'marks the orders as being paid' do
    on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
    expect(on_account_orders_payer).to be_success

    [order1, order2, order3].each do |order|
      expect(order.reload.payment_status).to eq('paid')
    end
  end

  it 'updates the invoice with payment totals' do
    on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
    expect(on_account_orders_payer).to be_success

    invoice.reload
    expected_payment_total = [order1, order2, order3].map(&:customer_total).sum
    expect(invoice.payment_value).to eq(expected_payment_total)
    expect(invoice.payment_status).to eq('paid')
  end

  it 'updates the invoice with payment status as partial if paid amount is less than invoice amount' do
    expected_payment_total = [order1, order2, order3].map(&:customer_total).sum

    invoice.update_column(:amount_price, expected_payment_total + rand(1..10))

    on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
    expect(on_account_orders_payer).to be_success

    invoice.reload
    expect(invoice.payment_value).to eq(expected_payment_total)
    expect(invoice.payment_status).to eq('partial')
  end

  it 'sends the tax receipt to the customer', notifications: true do
    expect(Customers::Emails::SendInvoiceReceiptEmail).to receive(:new).with(customer: customer, invoice: invoice)

    on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
    expect(on_account_orders_payer).to be_success
  end

  it 'does not send the tax receipt to the customer if notify_customer is passed as false', notifications: true do
    expect(Customers::Emails::SendInvoiceReceiptEmail).to_not receive(:new).with(customer: customer, invoice: invoice)

    on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3], notify_customer: false).call
    expect(on_account_orders_payer).to be_success
  end

  context 'with existing on-hold order charges' do
    let!(:order_charge1) { create(:order_charge, :random, order: order1) }
    let!(:order_charge2) { create(:order_charge, :random, order: order2, refund_token: SecureRandom.hex(7)) }
    let!(:order_charge3) { create(:order_charge, :random, order: order2) }
    let!(:order_charge4) { create(:order_charge, :random, order: order3) }

    it 'refunds/cancels all active order charges' do
      expect(Stripe::RefundOrderCharge).to receive(:new).with(order_charge: order_charge1)
      expect(Stripe::RefundOrderCharge).to_not receive(:new).with(order_charge: order_charge2) # already refunded
      expect(Stripe::RefundOrderCharge).to receive(:new).with(order_charge: order_charge3)
      expect(Stripe::RefundOrderCharge).to receive(:new).with(order_charge: order_charge4)

      on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
      expect(on_account_orders_payer).to be_success

      expect(order_charge1.reload).to be_refunded
      expect(order_charge3.reload).to be_refunded
      expect(order_charge4.reload).to be_refunded
    end
  end

  context 'with an errored payment process' do
    before do
      allow(Payments::ProcessPayment).to receive(:new).and_raise(RuntimeError.new('something went wrong'))
    end

    it 'errors out' do
      on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
      expect(on_account_orders_payer).to_not be_success

      expect(on_account_orders_payer.errors).to include("Failed to process payment or send tax receipt for invoice #{invoice.id} - something went wrong")
    end

    it 'does not change the credit cards of the orders' do
      on_account_orders_payer = Invoices::PayOnAccountOrders.new(invoice: invoice, credit_card: credit_card, pay_on_account_orders: [order1, order2, order3]).call
      expect(on_account_orders_payer).to_not be_success

      [order1, order2, order3].each do |order|
        expect(order.reload.credit_card).to_not eq(credit_card)
        expect(order.reload.credit_card).to eq(pay_on_account_card)
      end
    end
  end

end
