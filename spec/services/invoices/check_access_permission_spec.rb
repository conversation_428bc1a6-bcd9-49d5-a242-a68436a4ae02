require 'rails_helper'

RSpec.describe Invoices::CheckAccessPermission, type: :service, invoices: true do

  let!(:invoice) { create(:invoice, :random) }

  it 'let you access using a uuid' do
    invoice_permission = Invoices::CheckAccessPermission.new(invoice_id: invoice.uuid).call

    expect(invoice_permission.can_access?).to be_truthy
    expect(invoice_permission.invoice).to eq(invoice)
  end

  it 'doesn\'t let u access invoice using a number annonymously' do
    invoice_permission = Invoices::CheckAccessPermission.new(invoice_id: invoice.number).call

    expect(invoice_permission.can_access?).to be_falsey
    expect(invoice_permission.errors).to include('You need to log in to access invoice')
    expect(invoice_permission.redirect_to).to eq('/login')
  end

  it 'redirects you to a secure link if signed in as admin (wihtout a customer) and accesing using a number' do
    invoice_permission = Invoices::CheckAccessPermission.new(invoice_id: invoice.number, is_admin: true).call

    expect(invoice_permission.can_access?).to be_falsey
    expect(invoice_permission.errors).to include('Next time use the secure link')
    expect(invoice_permission.redirect_to).to eq("/invoice/pay/#{invoice.uuid}")
  end

  context 'with a customer' do
    let!(:customer) { create(:customer_profile, :random) }
    let!(:order) { create(:order, :confirmed, customer_profile: customer) }

    before do
      order.update_column(:invoice_id, invoice.id) # attach invoice to order
    end

    it 'redirects you to a secure link if signed in a customer with the invoice' do
      invoice_permission = Invoices::CheckAccessPermission.new(invoice_id: invoice.number, customer: customer).call

      expect(invoice_permission.can_access?).to be_falsey
      expect(invoice_permission.errors).to include('Next time use the secure link')
      expect(invoice_permission.redirect_to).to eq("/invoice/pay/#{invoice.uuid}")
    end

    it 'doesn\'t let you access the invoice if signed in as a customer not belonging to the invoice' do
      customer2 = create(:customer_profile, :random)
      invoice_permission = Invoices::CheckAccessPermission.new(invoice_id: invoice.number, customer: customer2).call

      expect(invoice_permission.can_access?).to be_falsey
      expect(invoice_permission.errors).to include('You don\'t have access to this invoice')
      expect(invoice_permission.redirect_to).to eq('/c_profile')
    end

    it 'doesn\'t let you access the invoice if signed in as a customer not belonging to the invoice even if is_admin' do
      customer2 = create(:customer_profile, :random)
      invoice_permission = Invoices::CheckAccessPermission.new(invoice_id: invoice.number, customer: customer2, is_admin: true).call

      expect(invoice_permission.can_access?).to be_falsey
      expect(invoice_permission.errors).to include('You don\'t have access to this invoice')
      expect(invoice_permission.redirect_to).to eq('/c_profile')
    end
  end
end
