require 'rails_helper'

RSpec.describe Invoices::Upsert, type: :service, invoices: true do

  let!(:customer) { create(:customer_profile, :random) }    

  context 'creation' do
    let!(:invoicing_date) { Time.zone.now - rand(1..2).months }
    let!(:invoice_params) do 
      {
        number: SecureRandom.hex(7),
        from_at: invoicing_date.beginning_of_month,
        to_at: invoicing_date.end_of_month,
      }
    end

    it 'creates a new (draft) invoice with the passed in params and customer' do
      invoice_creator = Invoices::Upsert.new(invoice_params: invoice_params, customer: customer).call

      expect(invoice_creator).to be_success

      created_invoice = invoice_creator.invoice
      expect(created_invoice).to be_present
      expect(created_invoice).to be_persisted
      expect(created_invoice).to be_a(Invoice)

      expect(created_invoice.customer_profile).to eq(customer)
      expect(created_invoice.status).to eq('draft')
      expect(created_invoice.number).to eq(invoice_params[:number])
      expect(created_invoice.from_at).to eq(invoice_params[:from_at])
      expect(created_invoice.to_at).to eq(invoice_params[:to_at])
    end

    it 'creates the invoice and set the associated due date based on default payment term days' do
      invoice_creator = Invoices::Upsert.new(invoice_params: invoice_params, customer: customer).call

      expect(invoice_creator).to be_success
      created_invoice = invoice_creator.invoice

      term_days = Invoice::DEFAULT_PAYMENT_DAYS
      expect(created_invoice.due_at).to eq(invoice_params[:to_at] + term_days.days)
    end

    context 'with assocated customer company (with term dates)' do
      let!(:company) { create(:company, :random, payment_term_days: Company::VALID_PAYMENT_TERM_DAYS.sample, customer_profiles: [customer]) }

      it 'creates the invoice with the dues dates based on company payment term days' do
        invoice_creator = Invoices::Upsert.new(invoice_params: invoice_params, customer: customer).call

        expect(invoice_creator).to be_success
        created_invoice = invoice_creator.invoice

        term_days = company.payment_term_days
        expect(created_invoice.due_at).to eq(invoice_params[:to_at] + term_days.days)
      end
    end
  end # creation from dashboard

  context 'update invoice' do
    let!(:invoicing_date) { Time.zone.now - rand(1..2).months }
    let!(:invoice) { create(:invoice, :random, customer_profile: customer) }

    let!(:invoice_params) do 
      {
        number: SecureRandom.hex(7),
        from_at: invoicing_date.beginning_of_month,
        to_at: invoicing_date.end_of_month,
        do_not_notify: true,
      }
    end

    it 'updates the existing invoice with passed in params' do
      invoice_updator = Invoices::Upsert.new(invoice: invoice, invoice_params: invoice_params, customer: customer).call

      expect(invoice_updator).to be_success

      updated_invoice = invoice_updator.invoice
      expect(updated_invoice.id).to eq(invoice.id)
      expect(updated_invoice.number).to eq(invoice_params[:number])
      expect(updated_invoice.from_at).to eq(invoice_params[:from_at])
      expect(updated_invoice.to_at).to eq(invoice_params[:to_at])
      expect(updated_invoice.do_not_notify).to eq(invoice_params[:do_not_notify])
    end

    it 'errors if passed in customer is not the invoice customer' do
      non_invoice_customer = create(:customer_profile, :random)
      invoice_updator = Invoices::Upsert.new(invoice: invoice, invoice_params: invoice_params, customer: non_invoice_customer).call

      expect(invoice_updator).to_not be_success
      expect(invoice_updator.errors).to include('You do not have access to this invoice')
    end
  end

  context 'upserting errors' do
    let!(:invoice) { create(:invoice, :random, customer_profile: customer) }

    it 'errors without any params ' do
      invoice_upserter = Invoices::Upsert.new(invoice_params: {}, customer: customer, invoice: [invoice, nil].sample).call

      expect(invoice_upserter).to_not be_success
      expect(invoice_upserter.errors).to include('Cannot create/update invoice without valid params')
    end
  end

end