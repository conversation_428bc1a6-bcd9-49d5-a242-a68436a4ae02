require 'rails_helper'

RSpec.describe Invoices::ProcessPayment, type: :service, orders: true, invoices: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:pay_on_account_card) { create(:credit_card, :on_account_card) }
  let!(:credit_card) { create(:credit_card, :random) }
  let!(:invoice_credit_card) { create(:credit_card, :random) }

  let!(:order1) { create(:order, :delivered, customer_profile: customer, customer_total: 1000, payment_status: 'unpaid') }
  let!(:order2) { create(:order, :delivered, customer_profile: customer, customer_total: 2000, payment_status: 'unpaid') }
  let!(:order3) { create(:order, :delivered, customer_profile: customer, customer_total: 3000, payment_status: 'unpaid') }

  let!(:invoice) { create(:invoice, :random, amount_price: [order1, order2, order3].sum(&:customer_total)) }

  before do
    [order1, order2, order3].each do |order|
      order.update_column(:invoice_id, invoice.id)
    end
    email_sender = delayed_email_sender = double(Customers::Emails::SendInvoiceReceiptEmail)
    allow(Customers::Emails::SendInvoiceReceiptEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  context 'with all pay on account orders', with_truncated_data: true do
    before do
      [order1, order2, order3].each do |order|
        order.update_column(:credit_card_id, pay_on_account_card.id)
      end

      # mocking this as internal stuff in tested in its own specs
      on_account_payer = double(Invoices::PayOnAccountOrders)
      allow(Invoices::PayOnAccountOrders).to receive(:new).and_return(on_account_payer)
      successfull_payment_reponse = OpenStruct.new(success?: true, payment: 'payment', errors: [])
      allow(on_account_payer).to receive(:call).and_return(successfull_payment_reponse)
    end

    it 'calls payment for the unpaid pay on account invoice orders' do
      expect(Invoices::PayOnAccountOrders).to receive(:new).with(invoice: invoice, pay_on_account_orders: [order1, order2, order3], credit_card: invoice_credit_card, notify_customer: false)

      invoice_payer = Invoices::ProcessPayment.new(invoice: invoice, credit_card: invoice_credit_card).call
      expect(invoice_payer).to be_success

      expect(invoice_payer.payments.size).to eq(1)
    end

    it 'does not call payment for the already paid orders' do
      order1.update_column(:payment_status, 'paid')
      expect(Invoices::PayOnAccountOrders).to receive(:new).with(invoice: invoice, pay_on_account_orders: [order2, order3], credit_card: invoice_credit_card, notify_customer: false) # missing the order1

      invoice_payer = Invoices::ProcessPayment.new(invoice: invoice, credit_card: invoice_credit_card).call
      expect(invoice_payer).to be_success
      expect(invoice_payer.payments.size).to eq(1)
    end

    it 'notifies the customer about the payment', notifications: true do
      expect(Customers::Emails::SendInvoiceReceiptEmail).to receive(:new).with(customer: customer, invoice: invoice)

      invoice_payer = Invoices::ProcessPayment.new(invoice: invoice, credit_card: invoice_credit_card).call
      expect(invoice_payer).to be_success
    end
  end

  context 'with pay by credit card orders' do
    before do
      [order1, order2, order3].each do |order|
        order.update_column(:credit_card_id, credit_card.id)
      end

      # mocking this as internal stuff in tested in its own specs
      credit_card_payer = double(Orders::PayByCreditCard)
      allow(Orders::PayByCreditCard).to receive(:new).and_return(credit_card_payer)
      successfull_payment_reponse = OpenStruct.new(success?: true, payment: 'payment', errors: [])
      allow(credit_card_payer).to receive(:call).and_return(successfull_payment_reponse)
    end

    it 'calls payment for the unpaid unpaid credit card invoice orders' do
      expect(Orders::PayByCreditCard).to receive(:new).with(order: order1, credit_card: invoice_credit_card)
      expect(Orders::PayByCreditCard).to receive(:new).with(order: order2, credit_card: invoice_credit_card)
      expect(Orders::PayByCreditCard).to receive(:new).with(order: order3, credit_card: invoice_credit_card)

      invoice_payer = Invoices::ProcessPayment.new(invoice: invoice, credit_card: invoice_credit_card).call
      expect(invoice_payer).to be_success
      expect(invoice_payer.payments.size).to eq(3)
    end

    it 'does not call payment for the already paid credit card invoice orders' do
      order1.update_column(:payment_status, 'paid')
      expect(Orders::PayByCreditCard).to_not receive(:new).with(order: order1, credit_card: invoice_credit_card)

      invoice_payer = Invoices::ProcessPayment.new(invoice: invoice, credit_card: invoice_credit_card).call

      expect(invoice_payer).to be_success
      expect(invoice_payer.payments.size).to eq(2)
    end

    it 'notifies the customer about the payments once', notifications: true do
      expect(Customers::Emails::SendInvoiceReceiptEmail).to receive(:new).with(customer: customer, invoice: invoice)

      invoice_payer = Invoices::ProcessPayment.new(invoice: invoice, credit_card: invoice_credit_card).call
      expect(invoice_payer).to be_success
    end
  end

  context 'errors' do
    it 'cannot pay without an invoice' do
      invoice_payer = Invoices::ProcessPayment.new(invoice: nil, credit_card: invoice_credit_card).call

      expect(invoice_payer).to_not be_success
      expect(invoice_payer.errors).to include('Cannot pay without an invoice')
    end

    it 'cannot pay without a credit card' do
      invoice_payer = Invoices::ProcessPayment.new(invoice: invoice, credit_card: nil).call

      expect(invoice_payer).to_not be_success
      expect(invoice_payer.errors).to include('Cannot pay without a credit card')
    end

    it 'cannot pay an already paid invoice' do
      invoice.update_column(:payment_status, 'paid')
      invoice_payer = Invoices::ProcessPayment.new(invoice: invoice, credit_card: invoice_credit_card).call

      expect(invoice_payer).to_not be_success
      expect(invoice_payer.errors).to include('Invoice is already paid for')
    end
  end

end
