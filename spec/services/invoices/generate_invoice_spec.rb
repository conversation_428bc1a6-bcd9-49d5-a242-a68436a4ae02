require 'rails_helper'

RSpec.describe Invoices::GenerateInvoice, type: :service, invoices: true, orders: true do

  let(:customer) { create(:customer_profile, :random) }
  let(:order1) { create(:order, :delivered, customer_profile: customer, customer_total: rand(100.01..199.99)) }
  let(:order2) { create(:order, :delivered, customer_profile: customer, customer_total: rand(100.01..199.99)) }
  let(:order3) { create(:order, :delivered, customer_profile: customer, customer_total: rand(100.01..199.99)) }
  let(:order4) { create(:order, :delivered, customer_profile: customer, customer_total: rand(100.01..199.99)) }
  let!(:all_orders) { [order1, order2, order3, order4] }
  let!(:documents_generator) { double(Invoices::GenerateDocuments) }
  let!(:payment_processor) { double(Invoices::ProcessPayment) }

  before do
    # mock document generator
    order_detials_pdf_generator = double(Documents::Generate::CustomerOrderDetails)
    allow(Documents::Generate::CustomerOrderDetails).to receive(:new).and_return(order_detials_pdf_generator)
    allow(order_detials_pdf_generator).to receive(:call).and_return(true)

    # mock tax invoice pdf generator
    invoice_pdf_generator = double(Documents::Generate::TaxInvoice)
    allow(Documents::Generate::TaxInvoice).to receive(:new).and_return(invoice_pdf_generator)
    allow(invoice_pdf_generator).to receive(:call).and_return('invoice_document')

    # mock email sender
    email_sender = double(Customers::Emails::SendOrderInvoiceEmail)
    allow(Customers::Emails::SendOrderInvoiceEmail).to receive(:new).and_return(email_sender)
    sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email')
    allow(email_sender).to receive(:call).and_return(sender_response)

    # mock payment processor
    allow(Invoices::ProcessPayment).to receive(:new).and_return(payment_processor)
    payment_response = OpenStruct.new(success?: true, payments: ['payment'], errors: [])
    allow(payment_processor).to receive(:call).and_return(payment_response)
  end

  it 'creates a confirmed invoice for the customers and attaches to the orders' do
    invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call

    expect(invoice_generator).to be_success
    generated_invoice = invoice_generator.generated_invoice
    expect(generated_invoice).to be_present
    expect(generated_invoice).to be_a(Invoice)
    expect(generated_invoice.status).to eq('confirmed')
    expect(generated_invoice.customer_profile).to eq(customer)
    expect(generated_invoice.orders).to match_array(all_orders)
  end

  it 'creates an invoice with calculated values' do
    invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call

    expect(invoice_generator).to be_success
    generated_invoice = invoice_generator.generated_invoice

    orders_total = all_orders.map(&:customer_total).compact.sum
    expect(generated_invoice.amount_price.round(2).to_s).to eq(orders_total.round(2).to_s)
    expect(generated_invoice.uuid).to be_present # SecureRandom.uuid

    invoice_count = Invoice.where('created_at >= ?', Time.zone.now.beginning_of_day).count + 1 - 1 # -1 because invoice is now generated
    expected_invoice_number = "#{Time.zone.now.strftime('%y%m%d')}#{format('%03d', invoice_count)}"
    expect(generated_invoice.number).to eq(expected_invoice_number)
  end

  context 'invoice dates' do
    let!(:from) { Time.zone.now - 1.month }
    let!(:to) { Time.zone.now - 1.day }
    let!(:due) { Time.zone.now + 1.month }

    it 'creates an invoice with default from and to dates (based on order dates)' do
      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders, invoice_dates: [nil, {}].sample).call

      expect(invoice_generator).to be_success
      generated_invoice = invoice_generator.generated_invoice

      sorted_orders = all_orders.sort_by(&:delivery_at)
      expect(generated_invoice.from_at.to_s).to eq(sorted_orders.first.delivery_at.beginning_of_day.to_s)
      expect(generated_invoice.to_at.to_s).to eq(sorted_orders.last.delivery_at.end_of_day.to_s)
    end

    it 'creates an invoice with the passed from and to dates' do
      invoice_dates = {
        from: from,
        to: to,
      }
      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders, invoice_dates: invoice_dates).call

      expect(invoice_generator).to be_success
      generated_invoice = invoice_generator.generated_invoice

      expect(generated_invoice.from_at.to_s).to eq(invoice_dates[:from].beginning_of_day.to_s)
      expect(generated_invoice.to_at.to_s).to eq(invoice_dates[:to].end_of_day.to_s)
    end

    context 'due dates' do
      it 'sets the due date as the invoice to day + default term days' do
        invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call

        expect(invoice_generator).to be_success
        generated_invoice = invoice_generator.generated_invoice

        expected_due_date = generated_invoice.to_at.end_of_day + Invoice::DEFAULT_PAYMENT_DAYS.days
        expect(generated_invoice.due_at.to_s).to eq(expected_due_date.to_s)
      end

      it 'calculates the due date as the invoice to day + the cutomer\'s companies payment term days' do
        company = create(:company, :random, payment_term_days: Company::VALID_PAYMENT_TERM_DAYS.sample)
        customer.update_column(:company_id, company.id)

        invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call

        expect(invoice_generator).to be_success
        generated_invoice = invoice_generator.generated_invoice

        expected_due_date = generated_invoice.to_at.end_of_day + company.payment_term_days.days
        expect(generated_invoice.due_at.to_s).to eq(expected_due_date.to_s)
      end
    end
  end

  context 'with a customer purchase order' do
    let!(:customer_purchase_order) { create(:customer_purchase_order, :random, customer_profile: customer) }

    it 'creates an invoice with passed in purchase order' do
      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders, purchase_order: customer_purchase_order).call

      expect(invoice_generator).to be_success
      generated_invoice = invoice_generator.generated_invoice

      expect(generated_invoice.customer_purchase_order).to be_present
      expect(generated_invoice.customer_purchase_order).to eq(customer_purchase_order)
    end
  end

  it 'attaches the orders to the Invoice' do
    invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call

    expect(invoice_generator).to be_success
    generated_invoice = invoice_generator.generated_invoice
    expect(generated_invoice.orders).to match_array(all_orders)
    all_orders.each do |order|
      expect(order.reload.invoice_id).to eq(generated_invoice.id)
    end
  end

  context 'recalculating totals' do
    before do
      total_calculator = double(Orders::CalculateCustomerTotals)
      allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(total_calculator)
      order_totals = Orders::CalculateCustomerTotals::Totals.new(order_line_count: 2)
      allow(total_calculator).to receive(:call).and_return(order_totals)
    end

    it 're-calculates the order totals before generating the invoice' do
      all_orders.each do |order|
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, save_totals: true)
      end
      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call

      expect(invoice_generator).to be_success
    end
  end

  context 'document(s) generation' do
    it 'generates the Order details documents and the Tax invoice documents for the invoice' do
      expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order1)
      expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order2)
      expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order3)
      expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order4)

      expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: anything, document_type: 'tax_invoice') # invoice generated in SO

      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call
      expect(invoice_generator).to be_success
    end

    it 'Send the customer an email', notifications: true do
      expect(Customers::Emails::SendOrderInvoiceEmail).to receive(:new).with(customer: customer, invoice: anything, documents: ['invoice_document'], is_regenerate: false) # invoice generated in SO

      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders, notify_customer: true).call
      expect(invoice_generator).to be_success
    end

    it 'doesn\'t request processing payment' do
      expect(Invoices::ProcessPayment).to_not receive(:new)

      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call
      expect(invoice_generator).to be_success
    end
  end

  context 'with a nominated credit card' do
    let!(:invoice_payble_credit_card) { create(:credit_card, :valid_payment, auto_pay_invoice: true) }

    before do
      customer.credit_cards << invoice_payble_credit_card
    end

    it 'processes the payment for the generated invoice using the nominated card' do
      expect(Invoices::ProcessPayment).to receive(:new).with(invoice: anything, credit_card: invoice_payble_credit_card).and_return(payment_processor)

      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call
      expect(invoice_generator).to be_success

      generated_invoice = invoice_generator.generated_invoice
      expect(generated_invoice).to be_present
      expect(generated_invoice.status).to eq('confirmed')
    end

    it 'doesn\'t generate invoice documents' do
      expect(Invoices::GenerateDocuments).to_not receive(:new)

      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call
      expect(invoice_generator).to be_success
    end

    context 'with payment errors' do
      before do
        payment_response = OpenStruct.new(success?: false, errors: ['payment-error'])
        allow(payment_processor).to receive(:call).and_return(payment_response)
      end

      it 'returns with the payment errors' do
        invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call

        expect(invoice_generator).to_not be_success
        # expect(invoice_generator.errors).to include("Failed auto payment for invoice id: #{invoice.id} - ##{invoice.number}") # invoice generated in SO
        expect(invoice_generator.errors).to include('payment-error')
      end

      it 'generates the relevant invoice document(s), but does not notify the customer', notifications: true do
        expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order1)
        expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order2)
        expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order3)
        expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order4)

        expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: anything, document_type: 'tax_invoice') # invoice generated in SO
        expect(Customers::Emails::SendOrderInvoiceEmail).to_not receive(:new)

        invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: all_orders).call
        expect(invoice_generator).to_not be_success
      end
    end
  end

  context 'when generating invoice for a order with attached credit card' do
    let!(:attached_credit_card) { create(:credit_card, :valid_payment, auto_pay_invoice: false) }

    before do
      customer.credit_cards << attached_credit_card
      order1.update_column(:credit_card_id, attached_credit_card.id)
    end

    it 'processes the payment for the generated invoice using the attached credit card' do
      expect(Invoices::ProcessPayment).to receive(:new).with(invoice: anything, credit_card: attached_credit_card).and_return(payment_processor)

      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: [order1]).call
      expect(invoice_generator).to be_success

      generated_invoice = invoice_generator.generated_invoice
      expect(generated_invoice).to be_present
      expect(generated_invoice.status).to eq('confirmed')
    end

    it 'doesn\'t generate invoice documents' do
      expect(Invoices::GenerateDocuments).to_not receive(:new)

      invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: [order1]).call
      expect(invoice_generator).to be_success
    end

    context 'when the customer has an active nominated card' do
      let!(:invoice_payble_credit_card) { create(:credit_card, :valid_payment, auto_pay_invoice: true) }

      before do
        customer.credit_cards << invoice_payble_credit_card
      end

      it 'processes the payment for the generated invoice using the attached credit card' do
        expect(Invoices::ProcessPayment).to receive(:new).with(invoice: anything, credit_card: attached_credit_card).and_return(payment_processor)

        invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: [order1]).call
        expect(invoice_generator).to be_success
      end

      it 'doesn\'t generate invoice documents' do
        expect(Invoices::GenerateDocuments).to_not receive(:new)

        invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: [order1]).call
        expect(invoice_generator).to be_success
      end
    end

    context 'with payment errors' do
      before do
        payment_response = OpenStruct.new(success?: false, errors: ['card-payment-error'])
        allow(payment_processor).to receive(:call).and_return(payment_response)
      end

      it 'returns with the payment errors' do
        invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: [order1]).call

        expect(invoice_generator).to_not be_success
        # expect(invoice_generator.errors).to include("Failed auto payment for invoice id: #{invoice.id} - ##{invoice.number}") # invoice generated in SO
        expect(invoice_generator.errors).to include('card-payment-error')
      end

      it 'generates the relevant invoice document(s), but does not notify the customer', notifications: true do
        expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order1)
        expect(Documents::Generate::TaxInvoice).to receive(:new).with(invoice: anything, document_type: 'tax_invoice') # invoice generated in SO
        expect(Customers::Emails::SendOrderInvoiceEmail).to_not receive(:new)

        invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: [order1]).call
        expect(invoice_generator).to_not be_success
      end
    end
  end
end
