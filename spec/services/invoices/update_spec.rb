require 'rails_helper'

RSpec.describe Invoices::Update, type: :service, invoices: true do

  let!(:invoice) { create(:invoice, :random) }
  let!(:invoice_params) do
    {
      payment_status: Invoice::VALID_PAYMENT_STATUSES.sample,
      do_not_notify: [true, false].sample,
      amount_price: rand(1000.01..2000.67),
      due_at: Time.zone.now + (rand(1..20).to_i).days
    }
  end

  it 'updates the invoice with the passed in data' do
    invoice_updator = Invoices::Update.new(invoice: invoice, invoice_params: invoice_params).call

    expect(invoice_updator).to be_success
    updated_invoice = invoice_updator.invoice

    expect(updated_invoice.payment_status).to eq(invoice_params[:payment_status])
    expect(updated_invoice.do_not_notify).to eq(invoice_params[:do_not_notify])
    expect(updated_invoice.amount_price.round(2)).to eq(invoice_params[:amount_price].round(2))
    expect(updated_invoice.due_at).to eq(invoice_params[:due_at])
  end

  context 'errors' do
    it 'returns with errors for a missing invoice' do
      invoice_updator = Invoices::Update.new(invoice: nil, invoice_params: invoice_params).call

      expect(invoice_updator).to_not be_success
      expect(invoice_updator.errors).to include('Invoice is missing')
    end

    it 'returns with errors for a missing invoice' do
      invoice_updator = Invoices::Update.new(invoice: invoice, invoice_params: [nil, {}].sample).call

      expect(invoice_updator).to_not be_success
      expect(invoice_updator.errors).to include('Cannot update invoice without valid params')
    end

    it 'returns with errors for invalid params' do
      invalid_invoice_params = { payment_status: 'INVALID-PAYMENT-STATUS'}
      invoice_updator = Invoices::Update.new(invoice: invoice, invoice_params: invalid_invoice_params).call

      expect(invoice_updator).to_not be_success
      expect(invoice_updator.errors).to include('Payment status is not included in the list') # error for invalid payment status
    end
  end # errors
end
