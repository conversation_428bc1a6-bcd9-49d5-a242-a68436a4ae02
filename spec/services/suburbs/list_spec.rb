require 'rails_helper'

RSpec.describe Suburbs::List, type: :service, suburbs: true do

  let!(:pyrmont) { create(:suburb, :pyrmont) }
  let!(:woolloomooloo) { create(:suburb, :woolloomooloo) }
  let!(:haymarket) { create(:suburb, :haymarket) }

  it 'lists all suburbs by default' do
    suburbs = Suburbs::List.new.call

    expect(suburbs.count).to eq(3)
    expect(suburbs).to include(pyrmont, woolloomooloo, haymarket)
  end

  it 'filters by passed in postcode' do
    searchabe_suburb = [pyrmont, woolloomooloo, haymarket].sample
    lister_options = { postcode: searchabe_suburb.postcode }
    suburbs = Suburbs::List.new(options: lister_options).call

    expect(suburbs.count).to eq(1)
    expect(suburbs).to include(searchabe_suburb)
  end

  it 'filters by passed in name' do
    searchabe_suburb = [pyrmont, woolloomooloo, haymarket].sample
    lister_options = { name: searchabe_suburb.name }
    suburbs = Suburbs::List.new(options: lister_options).call

    expect(suburbs.count).to eq(1)
    expect(suburbs).to include(searchabe_suburb)
  end

  context 'filter by term' do
    it 'filters the suburb by passed in postcode' do
      lister_options = { term: pyrmont.postcode }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs.count).to eq(1)
      expect(suburbs).to include(pyrmont)
    end

    it 'filters the suburb by passed in suburb name' do
      lister_options = { term: woolloomooloo.name }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs.count).to eq(1)
      expect(suburbs).to include(woolloomooloo)
    end
  end

  context 'filter by country code' do
    # create NZ suburbs
    let!(:auckland_central) { create(:suburb, :auckland_central) }
    let!(:ponsonby) { create(:suburb, :ponsonby) }
    let!(:parnell) { create(:suburb, :parnell) }

    it 'filters the suburb by passed in country code' do
      lister_options = { country_code: 'AU' }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs).to include(woolloomooloo, pyrmont, haymarket)
      expect(suburbs).to_not include(auckland_central, ponsonby, parnell)

      lister_options = { country_code: 'NZ' }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs).to include(auckland_central, ponsonby, parnell)
      expect(suburbs).to_not include(woolloomooloo, pyrmont, haymarket)
    end

    it 'filters the suburbs by country code of `AU` by default' do
      lister_options = {}
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs).to include(woolloomooloo, pyrmont, haymarket)
      expect(suburbs).to_not include(auckland_central, ponsonby, parnell)
    end

    it 'filters the suburb by a non-case sensitive country code' do
      lister_options = { country_code: %w[nZ Nz nz].sample }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs).to include(auckland_central, ponsonby, parnell)
      expect(suburbs).to_not include(woolloomooloo, pyrmont, haymarket)
    end
  end

  it 'orders the list by passed in config' do
    lister_options = { order_by: 'postcode DESC' }
    suburbs = Suburbs::List.new(options: lister_options).call

    expect(suburbs).to eq([woolloomooloo, pyrmont, haymarket]) # 2011 - 2009 - 2000
  end

  it 'limits the results by the passed in limit' do
    lister_options = { limit: 2 }
    suburbs = Suburbs::List.new(options: lister_options).call

    expect(suburbs.count).to eq(2)
  end

  context 'best matched suburb' do
    let!(:same_postcode_different_name_pyrmont) { create(:suburb, :pyrmont, name: 'NOT-PYRMONT') }
    let!(:same_name_different_postcode_haymarket) { create(:suburb, :haymarket, postcode: (haymarket.postcode.to_i + rand(1..10)).to_s) }

    it 'lists all non macthed suburbs by default' do
      suburbs = Suburbs::List.new.call

      expect(suburbs.count).to be > 1
      expect(suburbs).to include(same_postcode_different_name_pyrmont, same_name_different_postcode_haymarket)
    end

    it 'only lists the suburb with an exact name and postcode match' do
      lister_options = {
        best_matched_to: {
          name: pyrmont.name,
          postcode: pyrmont.postcode,
        }
      }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs.count).to eq(1)
      expect(suburbs).to include(pyrmont)
      expect(suburbs).to_not include(same_postcode_different_name_pyrmont)

      lister_options = {
        best_matched_to: {
          name: haymarket.name,
          postcode: haymarket.postcode,
        }
      }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs.count).to eq(1)
      expect(suburbs).to include(haymarket)
      expect(suburbs).to_not include(same_name_different_postcode_haymarket)
    end

    it 'lists the suburbs with an exact name match if postcode is not matched' do
      lister_options = {
        best_matched_to: {
          name: same_postcode_different_name_pyrmont.name,
          postcode: 'random-postcode',
        }
      }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs.count).to eq(1)
      expect(suburbs).to include(same_postcode_different_name_pyrmont)
      expect(suburbs).to_not include(pyrmont)
    end

    it 'defaults and lists the first suburb record' do
      lister_options = {
        postcode: pyrmont.postcode,
        best_matched_to: {
          name: 'random-name',
          postcode: 'random-postcode',
        }
      }
      suburbs = Suburbs::List.new(options: lister_options).call

      expect(suburbs.count).to eq(1)
      expect(suburbs).to include(pyrmont)
      expect(suburbs).to_not include(same_postcode_different_name_pyrmont) # created after the pyrmont suburb
    end
  end

  it 'adds passed in includes to the returned suburbs collection', skip: 'cannot test Active Record includes' do
    suburb = Suburb::List.new(includes: [:delivery_zones]).call

    expect(suburb).to have_included(:delivery_zones)
  end
end
