require 'rails_helper'

RSpec.describe Suburbs::FetchForSearch, type: :service do
  subject { Suburbs::FetchForSearch }

  let!(:suburb1) { create(:suburb, :random, name: 'Sydney', state: 'NSW', postcode: '2000') }
  let!(:suburb2) { create(:suburb, :random, name: 'Melbourne', state: 'VIC', postcode: '3000') }
  let!(:suburb3) { create(:suburb, :random, name: 'Brisbane', state: 'QLD', postcode: '4000') }

  context 'fetch by params' do
    it 'fetches the suburb by name' do
      suburb_params = { suburb: suburb1.name }
      fetched_suburb = subject.new(suburb_params: suburb_params).call

      expect(fetched_suburb).to eq(suburb1)
      expect(fetched_suburb).to_not eq(suburb2)
      expect(fetched_suburb).to_not eq(suburb3)
    end

    it 'fetches by case-insensitive suburb name' do
      suburb_params = { suburb: suburb2.name.split('').map{|x| [true, false].sample ? x.upcase : x.downcase }.join }
      fetched_suburb = subject.new(suburb_params: suburb_params).call

      expect(fetched_suburb).to eq(suburb2)
      expect(fetched_suburb).to_not eq(suburb1)
      expect(fetched_suburb).to_not eq(suburb3)
    end

    it 'fetches the suburb by state' do
      suburb_params = { state: suburb2.state }
      fetched_suburb = subject.new(suburb_params: suburb_params).call

      expect(fetched_suburb).to eq(suburb2)
      expect(fetched_suburb).to_not eq(suburb1)
      expect(fetched_suburb).to_not eq(suburb3)
    end

    it 'fetches by case-insensitive suburb state' do
      suburb_params = { state: suburb3.state.split('').map{|x| [true, false].sample ? x.upcase : x.downcase }.join }
      fetched_suburb = subject.new(suburb_params: suburb_params).call
      
      expect(fetched_suburb).to eq(suburb3)
      expect(fetched_suburb).to_not eq(suburb1)
      expect(fetched_suburb).to_not eq(suburb2)
    end

    it 'fetches the suburb by postcode' do
      suburb_params = { postcode: suburb3.postcode }
      fetched_suburb = subject.new(suburb_params: suburb_params).call

      expect(fetched_suburb).to eq(suburb3)
      expect(fetched_suburb).to_not eq(suburb1)
      expect(fetched_suburb).to_not eq(suburb2)
    end

    it 'fetches the suburb by id' do
      suburb_params = { suburb_id: suburb1.id }
      fetched_suburb = subject.new(suburb_params: suburb_params).call

      expect(fetched_suburb).to eq(suburb1)
      expect(fetched_suburb).to_not eq(suburb2)
      expect(fetched_suburb).to_not eq(suburb3)
    end

    it 'returns nil if params are not passed' do
      suburb_params = {}
      fetched_suburb = subject.new(suburb_params: suburb_params).call

      expect(fetched_suburb).to be_blank
    end
  end

  context 'with cookies', cookies: true do
    it 'return nil if params are not passed and cookies don\'t contain suburb id' do
      suburb_params = {}
      suburb_cookies = {}
      fetched_suburb = subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call

      expect(fetched_suburb).to be_blank
    end

    it 'returns suburb from cookies if the yordar_suburb_id cookie is set' do
      suburb_params = {}
      suburb_cookies = { yordar_suburb_id: suburb2.id }
      fetched_suburb = subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call

      expect(fetched_suburb).to eq(suburb2)
      expect(fetched_suburb).to_not eq(suburb1)
      expect(fetched_suburb).to_not eq(suburb3)
    end

    context 'if suburb is found' do
      let(:suburb_cookies) { { yordar_suburb_id: suburb2.id, yordar_suburb_label: suburb3.label } }

      it 'resets cookie with key :yordar_suburb_id if suburb params are passed' do
        suburb_params = { suburb: suburb1.name }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb_id] }.from(suburb2.id).to({ value: suburb1.id, domain: nil })
      end

      it 'resets cookie with key :yordar_suburb_label if suburb params are passed' do
        suburb_params = { suburb: suburb1.name }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb_label] }.from(suburb3.label).to({ value: suburb1.label, domain: nil })
      end

      it 'sets cookie with key :yordar_suburb if params[:suburb] is passed' do
        suburb_params = { suburb: suburb1.name }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb] }.from(nil).to({ value: suburb1.name, domain: nil })
      end

      it 'sets cookie with key :state if params[:state] is passed' do
        suburb_params = { state: suburb1.state }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_state] }.from(nil).to({ value: suburb1.state, domain: nil })
      end

      it 'sets cookie with key :yordar_postcode if params[:postcode] is passed' do
        suburb_params = { postcode: suburb1.postcode }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_postcode] }.from(nil).to({ value: suburb1.postcode, domain: nil })
      end

      it 'sets cookie with key :yordar_street_address if params[:street_address] is passed' do
        suburb_params = { suburb: suburb1.name, street_address: 'some address' }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_street_address] }.from(nil).to({ value: suburb_params[:street_address], domain: nil })
      end

      context 'with valid cookie with key :yordar_suburb_id' do
        let(:suburb_params) { {} }

        it 'sets cookie with key :yordar_suburb_label if cookies contain :yordar_suburb_id' do
          expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb_label] }.from(suburb3.label).to({ value: suburb2.label, domain: nil })
        end

        it 'sets cookie with key :yordar_suburb if cookies contain yordar_suburb_id' do
          expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb] }.from(nil).to({ value: suburb2.name, domain: nil })
        end

        it 'doesn\'t reset the street_address if suburb hasn\'t changed' do
          suburb_cookies[:yordar_street_address] = 'some street_address'
          expect(suburb_cookies[:yordar_street_address]).to be_present

          expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to_not change { suburb_cookies[:yordar_street_address] }.from('some street_address')
        end
      end
    end

    context 'if suburb is not found', cookies: true do
      let(:suburb_cookies) { { yordar_suburb_id: suburb2.id, yordar_suburb_label: suburb2.label } }

      it 'deletes cookie with key :yordar_suburb_id if suburb params are passed' do
        suburb_params = { suburb: 'random suburb name' }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb_id] }.from(suburb2.id).to(nil)
      end

      it 'deletes cookie with key :yordar_suburb_label if suburb params are passed' do
        suburb_params = { suburb: 'random suburb name' }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb_id] }.from(suburb2.id).to(nil)
      end

      it 'does not set cookie with key :yordar_suburb even if suburb params are passed' do
        suburb_params = { suburb: 'random suburb name' }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to_not change { suburb_cookies[:yordar_suburb] }.from(nil)
      end

      it 'does not set cookie with key :state even if params[:state] is passed' do
        suburb_params = { state: 'random state' }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to_not change { suburb_cookies[:yordar_state] }.from(nil)
      end

      it 'does not set cookie with key :postcode even if params[:postcode] is passed' do
        suburb_params = { postcode: 'random postcode' }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to_not change { suburb_cookies[:yordar_postcode] }.from(nil)
      end

      it 'does not set cookie with key :yordar_street_address even if params[:street_address] is passed' do
        suburb_params = { suburb: 'random name', street_address: 'some address' }
        expect { subject.new(suburb_params: suburb_params, suburb_cookies: suburb_cookies).call }.to_not change { suburb_cookies[:yordar_street_address] }.from(nil)
      end

      context 'with invalid yordar_suburb_id cookie' do
        let(:blank_suburb_params) { {} }
        let(:invalid_suburb_id) { 99_999 }

        before do
          suburb_cookies[:yordar_suburb_id] = invalid_suburb_id
        end

        it 'deletes cookie with with key :yordar_suburb_id' do
          expect { subject.new(suburb_params: blank_suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb_id] }.from(invalid_suburb_id).to(nil)
        end

        it 'deletes cookie with with key :yordar_suburb_label' do
          expect { subject.new(suburb_params: blank_suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb_label] }.from(suburb2.label).to(nil)
        end

        it 'deletes cookies with key :yordar_suburb' do
          suburb_cookies[:yordar_suburb] = 'suburb name'
          expect { subject.new(suburb_params: blank_suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_suburb] }.from('suburb name').to(nil)
        end

        it 'deletes cookies with key :yordar_state' do
          suburb_cookies[:yordar_state] = 'state name'
          expect { subject.new(suburb_params: blank_suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_state] }.from('state name').to(nil)
        end

        it 'deletes cookies with key :yordar_postcode' do
          suburb_cookies[:yordar_postcode] = 'some postcode'
          expect { subject.new(suburb_params: blank_suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_postcode] }.from('some postcode').to(nil)
        end

        it 'deletes cookies with key :yordar_street_address' do
          suburb_cookies[:yordar_street_address] = 'some street address'
          expect { subject.new(suburb_params: blank_suburb_params, suburb_cookies: suburb_cookies).call }.to change { suburb_cookies[:yordar_street_address] }.from('some street address').to(nil)
        end
      end
    end
  end

end
