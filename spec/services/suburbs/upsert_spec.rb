require 'rails_helper'

RSpec.describe Suburbs::Upsert, type: :service, suburb: true do

  let!(:suburb_params) do
    {
      name: Faker::Name.name,
      state: Faker::Name.initials,
      postcode: (2000..6000).to_a.sample.to_s,
      latitude: "-#{rand(34.001..36.990).round(10)}",
      longitude: rand(154.001..158.990).round(10).to_s,
      country_code: Suburb::VALID_COUNTRY_CODES.sample,
    }
  end

  before do
    # mock save deliverable suburbs
    save_deliverable_suburbs = delayed_save_deliverable_suburb = double(Suburbs::SaveDeliverableSuburbs)
    allow(Suburbs::SaveDeliverableSuburbs).to receive(:new).and_return(save_deliverable_suburbs)
    allow(save_deliverable_suburbs).to receive(:delay).and_return(delayed_save_deliverable_suburb)
    allow(delayed_save_deliverable_suburb).to receive(:call).and_return(true)
  end

  it 'creates a new suburb with the passed in params' do
    suburb_creator = Suburbs::Upsert.new(suburb_params: suburb_params).call

    expect(suburb_creator).to be_success

    created_suburb = suburb_creator.suburb
    expect(created_suburb).to be_present
    expect(created_suburb).to be_persisted
    expect(created_suburb.name).to eq(suburb_params[:name])
    expect(created_suburb.state).to eq(suburb_params[:state])
    expect(created_suburb.postcode).to eq(suburb_params[:postcode])
    expect(created_suburb.latitude.to_s).to eq(suburb_params[:latitude].to_s)
    expect(created_suburb.longitude.to_s).to eq(suburb_params[:longitude].to_s)
  end

  it 'makes a request to save deliverable suburbs' do
    expect(Suburbs::SaveDeliverableSuburbs).to receive(:new).with(suburb: anything) # passes created suburb

    suburb_creator = Suburbs::Upsert.new(suburb_params: suburb_params).call
    expect(suburb_creator).to be_success
  end

  context 'with an existing suburb' do
    let!(:suburb) { create(:suburb, :random) }

    it 'updates the passed in suburb with the passed in suburb params' do
      suburb_updator = Suburbs::Upsert.new(suburb: suburb, suburb_params: suburb_params).call

      expect(suburb_updator).to be_success

      updated_suburb = suburb_updator.suburb
      expect(updated_suburb).to be_present
      expect(updated_suburb.id).to eq(suburb.id) # same suburb
      expect(updated_suburb.name).to eq(suburb_params[:name])
      expect(updated_suburb.state).to eq(suburb_params[:state])
      expect(updated_suburb.postcode).to eq(suburb_params[:postcode])
      expect(updated_suburb.latitude.to_s).to eq(suburb_params[:latitude].to_s)
      expect(updated_suburb.longitude.to_s).to eq(suburb_params[:longitude].to_s)
    end

    it 'makes a request to (re-)save deliverable suburbs' do
      expect(Suburbs::SaveDeliverableSuburbs).to receive(:new).with(suburb: suburb)

      suburb_updator = Suburbs::Upsert.new(suburb: suburb, suburb_params: suburb_params).call
      expect(suburb_updator).to be_success
    end
  end

end