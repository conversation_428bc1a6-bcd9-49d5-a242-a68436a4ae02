require 'rails_helper'

RSpec.describe Suburbs::SaveDeliverableSuburbs, type: :service, deliverable_suburbs: true do

  subject { Suburbs::SaveDeliverableSuburbs.new(suburb: upserted_suburb).call }

  let!(:upserted_suburb) { create(:suburb, :random, longitude: 151.2078, latitude: -33.9031 ) }

  before do
    # mock saving delivery zone deliverable suburbs
    delivery_zone_deliverable_suburbs_saver = double(DeliveryZones::SaveDeliverableSuburbs)
    allow(DeliveryZones::SaveDeliverableSuburbs).to receive(:new).and_return(delivery_zone_deliverable_suburbs_saver)
    allow(delivery_zone_deliverable_suburbs_saver).to receive(:call).and_return(true)
  end


  context 'with delivery zones for the upserted suburb' do
    let!(:delivery_zone) { create(:delivery_zone, :random, suburb: upserted_suburb) }
    let!(:deliverable_suburb) { create(:deliverable_suburb, :random, delivery_zone: delivery_zone, suburb: upserted_suburb, distance: 0) }

    it 'makes a request to save the deliverable suburb for the delivery zone attached to the upserted_suburb' do
      expect(DeliveryZones::SaveDeliverableSuburbs).to receive(:new).with(delivery_zone: delivery_zone, refresh: true)

      subject
    end
  end

  context 'with delivery zones in the same postcode' do
    let!(:postcode_suburb1) { create(:suburb, :random, postcode: upserted_suburb.postcode) }
    let!(:delivery_zone1) { create(:delivery_zone, :random, suburb: postcode_suburb1) }
    let!(:deliverable_suburb1) { create(:deliverable_suburb, :random, delivery_zone: delivery_zone1, suburb: postcode_suburb1, distance: 0) }

    let!(:postcode_suburb2) { create(:suburb, :random, postcode: upserted_suburb.postcode) }
    let!(:delivery_zone2) { create(:delivery_zone, :random, suburb: postcode_suburb2) }
    let!(:deliverable_suburb2) { create(:deliverable_suburb, :random, delivery_zone: delivery_zone2, suburb: postcode_suburb2, distance: 0) }

    it 'makes a request to save the deliverable suburb for the delivery zone attached to the suburb in the same postcode' do
      expect(DeliveryZones::SaveDeliverableSuburbs).to receive(:new).with(delivery_zone: delivery_zone1, refresh: true)
      expect(DeliveryZones::SaveDeliverableSuburbs).to receive(:new).with(delivery_zone: delivery_zone2, refresh: true)

      subject
    end
  end

  context 'with delivery zones in the same state' do
    let!(:state_suburb_1) { create(:suburb, :random, state: upserted_suburb.state, longitude: 151.2079, latitude: -33.9032) }
    let!(:delivery_zone1) { create(:delivery_zone, :random, suburb: state_suburb_1, radius: 5) }
    let!(:deliverable_suburb1) { create(:deliverable_suburb, :random, delivery_zone: delivery_zone1, suburb: state_suburb_1, distance: 0) }

    let!(:state_suburb2) { create(:suburb, :random, state: upserted_suburb.state, longitude: 141.2078, latitude: -39.9031) }
    let!(:delivery_zone2) { create(:delivery_zone, :random, suburb: state_suburb2, radius: 10) }
    let!(:deliverable_suburb2) { create(:deliverable_suburb, :random, delivery_zone: delivery_zone2, suburb: state_suburb2, distance: 0) }

    let!(:state_suburb3) { create(:suburb, :random, state: 'NNN', longitude: 151.2080, latitude: -33.9033) }
    let!(:delivery_zone3) { create(:delivery_zone, :random, suburb: state_suburb3, radius: 8) }
    let!(:deliverable_suburb3) { create(:deliverable_suburb, :random, delivery_zone: delivery_zone3, suburb: state_suburb3, distance: 0) }

    it 'makes a request to save the deliverable suburb for the delivery zone attached to the suburb within state within the maximum delivery zone radius' do
      expect(DeliveryZones::SaveDeliverableSuburbs).to receive(:new).with(delivery_zone: delivery_zone1, refresh: true) # suburb in state and radius
      expect(DeliveryZones::SaveDeliverableSuburbs).to_not receive(:new).with(delivery_zone: delivery_zone2, refresh: true) # suburb in state but not in radius
      expect(DeliveryZones::SaveDeliverableSuburbs).to_not receive(:new).with(delivery_zone: delivery_zone3, refresh: true) # suburb not in state

      subject
    end
  end

end
