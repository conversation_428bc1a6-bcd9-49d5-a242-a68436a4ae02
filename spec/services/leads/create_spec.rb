require 'rails_helper'

RSpec.describe Leads::Create, type: :service do
  let!(:lead_syncer) { double(Hubspot::SyncLead) }

  before do
    delayed_syncer = lead_syncer
    allow(Hubspot::SyncLead).to receive(:new).and_return(lead_syncer)
    allow(lead_syncer).to receive(:delay).and_return(delayed_syncer)
    allow(delayed_syncer).to receive(:call).and_return(true)
  end

  context 'with valid params' do
    subject { Leads::Create.new(lead_params: lead_params).call }

    let(:lead_params) { { email: Faker::Internet.email, lead_type: 'started_registration' }}

    it 'create a new lead' do
      lead_creator = subject

      expect(lead_creator).to be_success
      expect(lead_creator.lead).to be_present
      expect(lead_creator.lead).to be_a(Lead)
      expect(lead_creator.lead.email).to eq(lead_params[:email])
    end

    it 'returns a valid user' do
      lead_creator = subject

      expect(lead_creator).to be_success
      expect(lead_creator.user).to be_present
      expect(lead_creator.user).to be_a(User)
      expect(lead_creator.user.email).to eq(lead_params[:email])
    end

    it 'syncs the lead with <PERSON><PERSON><PERSON>', hubspot: true do
      expect(<PERSON><PERSON><PERSON>::SyncLead).to receive(:new).with(lead: anything) # sends created lead

      subject
    end
  end

  context 'with exiting user / lead' do
    it 'returns an error for an existing user' do
      user = create(:user, :random, email: Faker::Internet.email)
      lead_params = { email: user.email, lead_type: 'started_registration' }
      lead_creator = Leads::Create.new(lead_params: lead_params).call

      expect(lead_creator).to_not be_success
      expect(lead_creator.errors).to be_present
      expect(lead_creator.errors).to include('A user with that email already exists!')
      expect(Hubspot::SyncLead).to_not receive(:new)
    end

    it 'return an error for a new user but with incorrect lead type' do
      lead_params = { email: Faker::Internet.email, lead_type: 'random type' }
      lead_creator = Leads::Create.new(lead_params: lead_params).call

      expect(lead_creator).to_not be_success
      expect(lead_creator.errors).to be_present
      expect(Hubspot::SyncLead).to_not receive(:new)
    end

    it 'return an error for a malformed email' do
      lead_params = { email: 'invalid.email.com.au', lead_type: 'started_registration' }
      lead_creator = Leads::Create.new(lead_params: lead_params).call

      expect(lead_creator).to_not be_success
      expect(lead_creator.errors).to be_present
      expect(lead_creator.errors).to include('Email is invalid')
      expect(Hubspot::SyncLead).to_not receive(:new)
    end

    it 'does not error for a new user but with existing lead' do
      user_email = Faker::Internet.email
      lead = create(:lead, :random, email: user_email)
      lead_params = { email: user_email, lead_type: 'started_registration' }

      lead_creator = Leads::Create.new(lead_params: lead_params).call

      expect(lead_creator).to be_success
      expect(lead_creator.lead).to eq(lead)
      expect(lead_creator.errors).to be_blank
    end
  end
end
