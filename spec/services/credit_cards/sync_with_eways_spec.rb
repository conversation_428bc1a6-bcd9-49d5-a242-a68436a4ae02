require 'rails_helper'

RSpec.describe CreditCards::SyncWithEway, type: :service, credit_cards: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:visa_card_params) { { number: '4111 1111 1111 1111', cvv: '111', expiry_month: Time.zone.now.strftime('%m'), expiry_year: (Time.zone.now + 1.year).strftime('%Y'), name: 'test me' } }

  it 'expects Active Merchant mode to be test (as we are testing)' do
    CreditCards::SyncWithEway.new(customer: customer, card_params: visa_card_params)

    expect(ActiveMerchant::Billing::Base.mode).to eq(:test)
  end

  context 'with valid eway response' do
    let(:token) { rand(10_000_000) }
    let(:eway_response) do
      OpenStruct.new(
        success?: true,
        params: {
          'CreateCustomerResult' => token
        }
      )
    end

    before do
      base_gateway = eway_gateway = double(ActiveMerchant::Billing::Base)
      allow(ActiveMerchant::Billing::Base).to receive(:gateway).with(:eway_managed).and_return(base_gateway)
      allow(base_gateway).to receive(:new).and_return(eway_gateway)
      allow(eway_gateway).to receive(:store).and_return(eway_response)
    end

    it 'returns the token' do
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: visa_card_params).call

      expect(card_syncer).to be_success
      expect(card_syncer.token).to eq(token)
    end

    it 'returns the the display number' do
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: visa_card_params).call

      expect(card_syncer).to be_success
      expect(card_syncer.display_number).to eq('XXXX-XXXX-XXXX-1111')
    end

    it 'returns the the correct card brand' do
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: visa_card_params).call
      expect(card_syncer).to be_success
      expect(card_syncer.brand).to eq('Visa')

      mastercard_card_params = visa_card_params.merge({ number: '5555 5555 5555 4444' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: mastercard_card_params).call
      expect(card_syncer).to be_success
      expect(card_syncer.brand).to eq('MasterCard')

      amex_card_params = visa_card_params.merge({ number: '3782 822 4631 0005', cvv: '1111' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: amex_card_params).call
      expect(card_syncer).to be_success
      expect(card_syncer.brand).to eq('American Express')

      diners_card_params = visa_card_params.merge({ number: '3852 0000 0232 37' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: diners_card_params).call
      expect(card_syncer).to be_success
      expect(card_syncer.brand).to eq('Diners Club')

      unknown_card_params = visa_card_params.merge({ number: '1111 2222 3333 4444' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: unknown_card_params).call
      expect(card_syncer).to be_success
      expect(card_syncer.brand).to eq('Unknown')
    end

    it 'returns the token without a valid customer' do
      card_syncer = CreditCards::SyncWithEway.new(customer: nil, card_params: visa_card_params).call

      expect(card_syncer).to be_success
    end
  end

  context 'invalid eway response' do
    let(:eway_response) { OpenStruct.new(success?: false, message: 'Credit Card is not valid') }

    before do
      base_gateway = eway_gateway = double(ActiveMerchant::Billing::Base)
      allow(ActiveMerchant::Billing::Base).to receive(:gateway).with(:eway_managed).and_return(base_gateway)
      allow(base_gateway).to receive(:new).and_return(eway_gateway)
      allow(eway_gateway).to receive(:store).and_return(eway_response)
    end

    it 'returns with an error' do
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: visa_card_params).call

      expect(card_syncer).to_not be_success
      expect(card_syncer.errors).to include(eway_response[:message])
    end
  end

  context 'with card errors' do
    it 'errors if the card number is wrong' do
      error_card_params = visa_card_params.merge({ number: '4111' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: error_card_params).call

      expect(card_syncer).to_not be_success
      expect(card_syncer.errors).to include('Number is not a valid credit card number')
    end

    it 'errors if the cvv is wrong' do
      error_card_params = visa_card_params.merge({ cvv: '2013010' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: error_card_params).call

      expect(card_syncer).to_not be_success
      expect(card_syncer.errors).to include('Verification value should be 3 digits') # 3 digits for a visa card
    end

    it 'errors if the month is wrong' do
      error_card_params = visa_card_params.merge({ expiry_month: '15' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: error_card_params).call

      expect(card_syncer).to_not be_success
      expect(card_syncer.errors).to include('Month is not a valid month')
    end

    it 'errors if the year is wrong' do
      error_card_params = visa_card_params.merge({ expiry_year: '9999' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: error_card_params).call

      expect(card_syncer).to_not be_success
      expect(card_syncer.errors).to include('Year is not a valid year')
    end

    it 'errors if the year is in the past' do
      error_card_params = visa_card_params.merge({ expiry_year: '1899' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: error_card_params).call

      expect(card_syncer).to_not be_success
      expect(card_syncer.errors).to include('Year expired')
    end

    it 'errors if the name does not consist of a first and last name' do
      error_card_params = visa_card_params.merge({ name: 'test' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: error_card_params).call

      expect(card_syncer).to_not be_success
      expect(card_syncer.errors).to include('Last name cannot be empty')
    end

    it 'does not call Active Merchant Gateway on error' do
      error_card_params = visa_card_params.merge({ expiry_year: '1899' })
      card_syncer = CreditCards::SyncWithEway.new(customer: customer, card_params: error_card_params).call

      expect(card_syncer).to_not be_success
      expect(ActiveMerchant::Billing::Base).to_not receive(:gateway)
    end
  end

end
