require 'rails_helper'

RSpec.describe CreditCards::FetchSurcharge, type: :service, credit_cards: true do

  context 'for a card saved in eWay', eway: true do
    let!(:eway_card) { create(:credit_card, :valid_eway_payment) }

    it 'returns the surcharge precent for a visa card' do
      surcharge = CreditCards::FetchSurcharge.new(credit_card: eway_card).call

      expect(surcharge.percent).to eq(CreditCards::FetchSurcharge::EWAY_PERCENT)
    end

    it 'returns the surcharge precent for a diners card' do
      eway_card.update_column(:brand, 'diners')
      surcharge = CreditCards::FetchSurcharge.new(credit_card: eway_card).call

      expect(surcharge.percent).to eq(CreditCards::FetchSurcharge::EWAY_DINERS_PERCENT)
    end

    it 'returns the surcharge fee for an eway card' do
      surcharge = CreditCards::FetchSurcharge.new(credit_card: eway_card).call

      expect(surcharge.fee).to eq(CreditCards::FetchSurcharge::EWAY_FEE)
    end
  end

  context 'for a card saved in Stripe', stripe: true do
    let!(:stripe_card) { create(:credit_card, :valid_stripe_payment) }

    it 'returns the surcharge precent for a domestic card' do
      stripe_card.update_column(:country_code, %w[au AU].sample)
      surcharge = CreditCards::FetchSurcharge.new(credit_card: stripe_card).call

      expect(surcharge.percent).to eq(CreditCards::FetchSurcharge::STRIPE_DOMESTIC_PERCENT)
    end

    it 'returns the surcharge precent for an intenational card' do
      stripe_card.update_column(:country_code, %w[nz us].sample)
      surcharge = CreditCards::FetchSurcharge.new(credit_card: stripe_card).call

      expect(surcharge.percent).to eq(CreditCards::FetchSurcharge::STRIPE_INTERNATIONAL_PERCENT)
    end

    it 'returns the surcharge fee for an eway card' do
      surcharge = CreditCards::FetchSurcharge.new(credit_card: stripe_card).call

      expect(surcharge.fee).to eq(CreditCards::FetchSurcharge::STRIPE_FEE)
    end
  end

  it 'return a precent and fee of 0 from an on account card' do
    on_account_card = create(:credit_card, :on_account_card)
    surcharge = CreditCards::FetchSurcharge.new(credit_card: on_account_card).call

    expect(surcharge.percent).to eq(0)
    expect(surcharge.fee).to eq(0)
  end

  it 'return a precent and fee of 0 for a missing card' do
    surcharge = CreditCards::FetchSurcharge.new(credit_card: nil).call

    expect(surcharge.percent).to eq(0)
    expect(surcharge.fee).to eq(0)
  end

end
