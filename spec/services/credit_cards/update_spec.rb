require 'rails_helper'

RSpec.describe CreditCards::Update, type: :service, credit_cards: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:credit_card) { create(:credit_card, :random) }
  before do
    customer.credit_cards << credit_card
  end

  context 'with a valid card' do
    let!(:visa_card_params) { { number: '4111 1111 1111 1111', cvv: '111', expiry_month: Time.zone.now.strftime('%m'), expiry_year: (Time.zone.now + 1.year).strftime('%Y'), name: 'test me' } }

    let(:eway_syncer) { double(CreditCards::SyncWithEway) }
    let(:valid_eway_syncer_result) do
      result = CreditCards::SyncWithEway::Result.new
      result.token = rand(10_000_000).to_s
      result.display_number = 'XXXX-XXXX-XXXX-1111'
      result.brand = 'Visa'
      result
    end

    before do
      allow(CreditCards::SyncWithEway).to receive(:new).and_return(eway_syncer)
      allow(eway_syncer).to receive(:call).and_return(valid_eway_syncer_result)
    end

    it 'updates the existing credit card' do
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: visa_card_params).call

      expect(card_updater).to be_success
      updated_card = card_updater.card
      expect(updated_card).to be_present
      expect(updated_card.id).to eq(credit_card.id)
      expect(updated_card.name).to eq(visa_card_params[:name])
      expect(updated_card.expiry_month).to eq(visa_card_params[:expiry_month].to_i)
      expect(updated_card.expiry_year).to eq(visa_card_params[:expiry_year].to_i)
    end

    it 'updates a new credit card and keeps it attached to the customer' do
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: visa_card_params).call

      expect(card_updater).to be_success
      updated_card = card_updater.card
      expect(customer.reload.credit_cards).to include(updated_card)
    end

    it 'updates the correct (disguised) card number' do
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: visa_card_params).call

      expect(card_updater).to be_success
      expect(card_updater.card.number).to eq(valid_eway_syncer_result.display_number)
    end

    it 'updates the label card number' do
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: visa_card_params).call

      expect(card_updater).to be_success
      expect(card_updater.card.label).to eq("#{valid_eway_syncer_result.brand} (#{valid_eway_syncer_result.display_number.gsub('X', '#')})")
    end

    it 'does not save the CVV' do
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: visa_card_params).call

      expect(card_updater).to be_success
      expect(card_updater.card.cvv).to be_blank
    end

    it 'updates the gateway token as passed by eway' do
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: visa_card_params).call

      expect(card_updater).to be_success
      expect(card_updater.card.gateway_token).to eq(valid_eway_syncer_result.token)
    end
  end

  context 'with no card details' do
    it 'hides (not saved for future) the credit card' do
      card_params = { saved_for_future: false }
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: card_params).call

      expect(card_updater).to be_success
      updated_card = card_updater.card
      expect(updated_card.saved_for_future).to be_falsey
    end

    it 'disables the credit card' do
      card_params = { enabled: false }
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: card_params).call

      expect(card_updater).to be_success
      updated_card = card_updater.card
      expect(updated_card.enabled).to be_falsey
    end

    it 'does not sync with eway' do
      expect(CreditCards::SyncWithEway).to_not receive(:new)
      card_params = [{ enabled: false }, { saved_for_future: false }].sample
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: card_params).call

      expect(card_updater).to be_success
    end
  end

  context 'pay invoice nomination' do
    let!(:existing_nominated_credit_card) { create(:credit_card, :valid_payment, auto_pay_invoice: true) }

    before do
      customer.credit_cards << existing_nominated_credit_card
    end

    it 'updates the nomination of the credit card' do
      card_params = { auto_pay_invoice: [true, false].sample }
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: card_params).call

      updated_card = card_updater.card
      expect(updated_card.auto_pay_invoice).to eq(card_params[:auto_pay_invoice])
    end

    it 'revokes nomination of other credit cards belonging to the customer if a new one is nominated' do
      card_params = { auto_pay_invoice: true }
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: card_params).call

      updated_card = card_updater.card
      expect(updated_card.auto_pay_invoice).to be_truthy
      expect(existing_nominated_credit_card.reload.auto_pay_invoice).to be_falsey
    end

    it 'doesn\'t revoke nomination of credit cards not belonging to the customer' do
      other_nominated_credit_card = create(:credit_card, :valid_payment, auto_pay_invoice: true)
      card_params = { auto_pay_invoice: true }
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer, card_params: card_params).call

      updated_card = card_updater.card
      expect(updated_card.auto_pay_invoice).to be_truthy
      expect(other_nominated_credit_card.reload.auto_pay_invoice).to_not be_falsey
    end
  end

  context 'errors' do
    it 'cannot update a missing credit card' do
      card_params = { enabled: true }
      card_updater = CreditCards::Update.new(credit_card: nil, customer: customer, card_params: card_params).call

      expect(card_updater).to_not be_success
      expect(card_updater.errors).to include('Cannot update a missing card')
    end

    it 'cannot update a credit card without a customer' do
      card_params = { enabled: true }
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: nil, card_params: card_params).call

      expect(card_updater).to_not be_success
      expect(card_updater.errors).to include('You don\'t have permissions to change this card')
    end

    it 'cannot update a credit card not belonging to the customer' do
      customer2 = create(:customer_profile, :random)
      card_params = { enabled: true }
      card_updater = CreditCards::Update.new(credit_card: credit_card, customer: customer2, card_params: card_params).call

      expect(card_updater).to_not be_success
      expect(card_updater.errors).to include('You don\'t have permissions to change this card')
    end
  end
end
