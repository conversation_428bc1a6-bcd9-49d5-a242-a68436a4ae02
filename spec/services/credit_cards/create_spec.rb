require 'rails_helper'

RSpec.describe CreditCards::Create, type: :service, credit_cards: true do

  let!(:customer) { create(:customer_profile, :random) }

  context 'with a valid card' do
    let!(:visa_card_params) { { number: '4111 1111 1111 1111', cvv: '111', expiry_month: Time.zone.now.strftime('%m'), expiry_year: (Time.zone.now + 1.year).strftime('%Y'), name: 'test me' } }

    let(:eway_syncer) { double(CreditCards::SyncWithEway) }
    let(:valid_eway_syncer_result) do
      result = CreditCards::SyncWithEway::Result.new
      result.token = rand(100_000_000).to_s
      result.display_number = 'XXXX-XXXX-XXXX-1111'
      result.brand = 'Visa'
      result
    end

    before do
      allow(CreditCards::SyncWithEway).to receive(:new).and_return(eway_syncer)
      allow(eway_syncer).to receive(:call).and_return(valid_eway_syncer_result)
    end

    it 'creates a new credit card' do
      card_creator = CreditCards::Create.new(customer: customer, card_params: visa_card_params).call

      expect(card_creator).to be_success
      created_card = card_creator.card
      expect(created_card).to be_present
      expect(created_card).to be_persisted
    end

    it 'creates a new credit card and attaches it to the customer' do
      card_creator = CreditCards::Create.new(customer: customer, card_params: visa_card_params).call

      expect(card_creator).to be_success
      created_card = card_creator.card
      expect(customer.reload.credit_cards).to include(created_card)
    end

    it 'creates a new credit card without a customer (invoice only card)' do
      card_creator = CreditCards::Create.new(customer: nil, card_params: visa_card_params).call

      expect(card_creator).to be_success
      created_card = card_creator.card
      expect(customer.reload.credit_cards).to_not include(created_card)
    end

    it 'saves the correct (disguised) card number' do
      card_creator = CreditCards::Create.new(customer: customer, card_params: visa_card_params).call

      expect(card_creator).to be_success
      expect(card_creator.card.number).to eq(valid_eway_syncer_result.display_number)
    end

    it 'saves the label card number' do
      card_creator = CreditCards::Create.new(customer: customer, card_params: visa_card_params).call

      expect(card_creator).to be_success
      expect(card_creator.card.label).to eq("#{valid_eway_syncer_result.brand} (#{valid_eway_syncer_result.display_number.gsub('X', '#')})")
    end

    it 'does not save the CVV' do
      card_creator = CreditCards::Create.new(customer: customer, card_params: visa_card_params).call

      expect(card_creator).to be_success
      expect(card_creator.card.cvv).to be_blank
    end

    it 'saves the gateway token as passed by eway' do
      card_creator = CreditCards::Create.new(customer: customer, card_params: visa_card_params).call

      expect(card_creator).to be_success
      expect(card_creator.card.gateway_token).to eq(valid_eway_syncer_result.token)
    end
  end
end
