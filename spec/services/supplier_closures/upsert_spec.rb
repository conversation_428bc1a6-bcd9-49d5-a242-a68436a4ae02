require 'rails_helper'

RSpec.describe SupplierClosures::Upsert, type: :service, suppliers: true, closure_dates: true do

  let!(:today) { Time.zone.now }

  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:closure_params) do
    {
      starts_at: today + 10.days,
      ends_at: today + 20.days,
      reason: Faker::Name.name
    }
  end

  it 'creates a new supplier closure for the passed in supplier' do
    closure_creator = SupplierClosures::Upsert.new(supplier: supplier, closure_params: closure_params).call

    expect(closure_creator).to be_success

    created_supplier_closure = closure_creator.supplier_closure
    expect(created_supplier_closure).to be_present
    expect(created_supplier_closure).to be_persisted
    expect(created_supplier_closure).to be_a(SupplierClosure)

    expect(created_supplier_closure.supplier_profile).to eq(supplier)
    expect(created_supplier_closure.reason).to eq(closure_params[:reason])
  end

  it 'saves the closure start at as beginning of day' do
    starts_at = today + 5.days
    closure_params_with_starts_at = closure_params.merge({
      starts_at: [starts_at, starts_at.to_s].sample
    })
    closure_creator = SupplierClosures::Upsert.new(supplier: supplier, closure_params: closure_params_with_starts_at).call

    expect(closure_creator).to be_success
    created_supplier_closure = closure_creator.supplier_closure
    expect(created_supplier_closure.starts_at).to eq(starts_at.beginning_of_day)
  end

  it 'saves the closure start at as end of day' do
    ends_at = today + 30.days
    closure_params_with_ends_at = closure_params.merge({
      ends_at: [ends_at, ends_at.to_s].sample
    })

    closure_creator = SupplierClosures::Upsert.new(supplier: supplier, closure_params: closure_params_with_ends_at).call

    expect(closure_creator).to be_success
    created_supplier_closure = closure_creator.supplier_closure
    expect(created_supplier_closure.ends_at).to eq(ends_at.end_of_day)
  end

  context 'with an existing supplier closure' do
    let!(:supplier_closure) { create(:supplier_closure, :random, supplier_profile: supplier) }

    it 'updates the record' do
      closure_updator = SupplierClosures::Upsert.new(supplier: supplier, supplier_closure: supplier_closure, closure_params: closure_params).call

      expect(closure_updator).to be_success

      updated_supplier_closure = closure_updator.supplier_closure
      expect(updated_supplier_closure.id).to eq(supplier_closure.id)

      expect(updated_supplier_closure.reason).to eq(closure_params[:reason])
      expect(updated_supplier_closure.starts_at).to eq(closure_params[:starts_at].beginning_of_day)
      expect(updated_supplier_closure.ends_at).to eq(closure_params[:ends_at].end_of_day)
    end

    it 'errors out when the passed in supplier closure does not belong to the supplier' do
      supplier2 = create(:supplier_profile, :random)
      closure_updator = SupplierClosures::Upsert.new(supplier: supplier2, supplier_closure: supplier_closure, closure_params: closure_params).call

      expect(closure_updator).to_not be_success
      expect(closure_updator.errors).to include('You do not have access to this closure date')
    end
  end

  context 'errors' do
    it 'cannot create/update a supplier closure without a supplier' do
      closure_upserter = SupplierClosures::Upsert.new(supplier: nil, closure_params: closure_params).call

      expect(closure_upserter).to_not be_success
      expect(closure_upserter.errors).to include('Cannot create/update closure date without a supplier')
    end

    it 'errors out when erronously upserting a supplier closure (model validations)' do
      invalid_closure_params = {
        starts_at: today + 10.days,
        ends_at: today + 5.days,
        reason: ['', nil].sample
      }
      closure_upserter = SupplierClosures::Upsert.new(supplier: nil, closure_params: invalid_closure_params).call

      expect(closure_upserter).to_not be_success
      expect(closure_upserter.errors).to be_present
    end
  end

end
