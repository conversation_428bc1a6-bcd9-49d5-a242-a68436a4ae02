require 'rails_helper'

RSpec.describe Hubspot::BuildCustomerDetails, type: :service, hubspot: true do

  let!(:time) { Time.zone.now }
  let!(:customer) { create(:customer_profile, :random, :with_user, contact_phone: nil, mobile: nil) }
  let!(:contact) { customer.user }
  let!(:suburb) { create(:suburb, :random) }

  before do
    allow(Hubspot::Contact).to receive(:find_by_email).and_return(nil)
  end

  it 'returns default information' do
    customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

    expect(customer_details.keys).to include(:firstname, :lastname, :lifecyclestage)
    expect(customer_details[:firstname]).to eq(contact.firstname)
    expect(customer_details[:lastname]).to eq(contact.lastname)
    expect(customer_details[:lifecyclestage]).to eq('marketingqualifiedlead')
  end

  it 'returns customer specific info' do
    customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

    expect(customer_details.keys).to include(:custom_lifecyclestage, :contact_type)
    expect(customer_details[:custom_lifecyclestage]).to eq('customer')
    expect(customer_details[:contact_type]).to eq('Customer')
  end

  it 'returns the the last sign in value for the user if present' do
    last_sign_in = time - 22.days
    contact.update_column(:last_sign_in_at, last_sign_in)
    last_sign_in_utc = Time.utc(last_sign_in.year, last_sign_in.month, last_sign_in.day).midnight.to_i * 1000

    customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

    expect(customer_details.keys).to include(:last_sign_in)
    expect(customer_details[:last_sign_in]).to eq(last_sign_in_utc)
  end

  context 'with existing Hubspot contact' do
    let!(:hubspot_customer) { OpenStruct.new(vid: rand(11_000...223_300)) }

    before do
      contact.update_column(:hs_contact_id, hubspot_customer.vid.to_s)
      allow(Hubspot::Contact).to receive(:find_by_email).and_return(hubspot_customer)
    end

    it 'makes a request to get the hubspot contact via email' do
      expect(Hubspot::Contact).to receive(:find_by_email).with(contact.email)

      Hubspot::BuildCustomerDetails.new(contact: contact).call
    end

    it 'does not return creation details' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to_not include(:firstname, :lastname, :lifecyclestage, :custom_lifecyclestage, :contact_type)
    end

    it 'returns creation details if set to refresh' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact, refresh: true).call

      expect(customer_details.keys).to include(:firstname, :lastname, :lifecyclestage, :custom_lifecyclestage, :contact_type)
    end
  end

  context 'status details' do
    it 'returns the status as active by default' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:hs_content_membership_status)
      expect(customer_details[:hs_content_membership_status]).to eq('active')
    end

    it 'returns the status as in-active if the contact is in-active' do
      contact.update_column(:is_active, false)
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:hs_content_membership_status)
      expect(customer_details[:hs_content_membership_status]).to eq('inactive')
    end
  end

  context 'company details' do
    let!(:company_name) { Faker::Name.name }

    before do
      customer.update_column(:company_name, company_name)
    end

    it 'returns the company name as stored in the customer record' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:company)
      expect(customer_details[:company]).to eq(company_name)
    end

    context 'with associated company record' do
      let!(:company) { create(:company, :random, customer_profiles: [customer]) }

      it 'returns the name of the associated company' do
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to include(:company)
        expect(customer_details[:company]).to eq(company.name)
      end
    end

    context 'with existing Hubspot company info' do
      let!(:hubspot_customer) { OpenStruct.new(vid: rand(11_000...223_300), properties: { company: company_name }) }

      before do
        contact.update_column(:hs_contact_id, hubspot_customer.vid.to_s)
        allow(Hubspot::Contact).to receive(:find_by_email).and_return(hubspot_customer)
      end

      it 'does not return company info if existing hubspot has the same company name as customer' do
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to_not include(:company)
      end

      it 'returns updated company info if existing hubspot has a different company name to customer' do
        updated_company_name = Faker::Name.name
        customer.update_column(:company_name, updated_company_name)
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to include(:company)
        expect(customer_details[:company]).to eq(updated_company_name)
      end
    end
  end # Company details

  context 'Job title' do
    before do
      customer.update_column(:role, CustomerProfile::VALID_ROLES.sample)
    end

    it 'returns customer\'s role as the jobtitle' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:jobtitle)
      expect(customer_details[:jobtitle]).to eq(customer.role)
    end

    it 'returns the jobtitle as `Company Team Admin` if the company team admin is set to true' do
      customer.update_column(:company_team_admin, true)
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:jobtitle)
      expect(customer_details[:jobtitle]).to eq('Company Team Admin')
    end

    context 'with existing Hubspot contact with job title info' do
      let!(:hubspot_customer) { OpenStruct.new(vid: rand(11_000...223_300), properties: { jobtitle: customer.role }) }

      before do
        contact.update_column(:hs_contact_id, hubspot_customer.vid.to_s)
        allow(Hubspot::Contact).to receive(:find_by_email).and_return(hubspot_customer)
      end

      it 'does not return jobtitle info if existing hubspot has the same jobtitle as customer' do
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to_not include(:jobtitle)
      end

      it 'returns updated jobtitle info if existing hubspot contact has a different jobtitle to customer' do
        updated_role = (CustomerProfile::VALID_ROLES - [customer.role]).sample
        customer.update_column(:role, updated_role)
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to include(:jobtitle)
        expect(customer_details[:jobtitle]).to eq(updated_role)
      end
    end
  end

  context 'Phone details' do
    let!(:phone) { Faker::PhoneNumber.phone_number }
    let!(:mobile) { Faker::PhoneNumber.phone_number }

    before do
      customer.update_columns(contact_phone: phone, mobile: mobile)
    end

    it 'returns customer\'s contact_phone if present' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:phone)
      expect(customer_details[:phone]).to eq(phone)
    end

    it 'returns customer\'s mobile if present and contact_phone is blank' do
      customer.update_column(:contact_phone, nil)

      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:phone)
      expect(customer_details[:phone]).to eq(mobile)
    end

    context 'with existing Hubspot contact with phone info' do
      let!(:hubspot_customer) { OpenStruct.new(vid: rand(11_000...223_300), properties: { phone: phone }) }

      before do
        contact.update_column(:hs_contact_id, hubspot_customer.vid.to_s)
        allow(Hubspot::Contact).to receive(:find_by_email).and_return(hubspot_customer)
      end

      it 'does not return contact info if existing hubspot has the same phone as customer' do
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to_not include(:phone)
      end

      it 'returns updated contact info if existing hubspot has a different phone as customer' do
        updated_phone = Faker::PhoneNumber.phone_number
        customer.update_column(:contact_phone, updated_phone)
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to include(:phone)
        expect(customer_details[:phone]).to eq(updated_phone)
      end
    end
  end # Phone details

  context 'Location data' do
    before do
      contact.update_column(:suburb_id, suburb.id)
    end

    it 'returns default location details' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:country, :hs_timezone)
      expect(customer_details[:country]).to eq('Australia')
      expect(customer_details[:hs_timezone]).to eq('australia_slash_nsw')
    end

    it 'returns user attached suburb details if present' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:zip, :city, :state)
      expect(customer_details[:zip]).to eq(suburb.postcode)
      expect(customer_details[:city]).to eq(suburb.name)
      expect(customer_details[:state]).to eq(suburb.state)
    end

    context 'with company address suburb' do
      let!(:billing_suburb) { create(:suburb, :random) }
      let!(:billing_details) { create(:billing_details, :random, customer_profile: customer, suburb: billing_suburb) }

      it 'returns company address suburb ' do
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to include(:zip, :city, :state)
        expect(customer_details[:zip]).to eq(billing_suburb.postcode)
        expect(customer_details[:city]).to eq(billing_suburb.name)
        expect(customer_details[:state]).to eq(billing_suburb.state)
      end
    end

    context 'with existing Hubspot contact with suburb info' do
      let!(:hubspot_customer) { OpenStruct.new(vid: rand(11_000...223_300), properties: { city: suburb.name }) }

      before do
        contact.update_column(:hs_contact_id, hubspot_customer.vid.to_s)
        allow(Hubspot::Contact).to receive(:find_by_email).and_return(hubspot_customer)
      end

      it 'does not return location info if existing hubspot customer city is the same as the contact suburb' do
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to_not include(:zip, :city, :state)
      end

      it 'return updated location info if existing hubspot customer city is different from the the contact suburb' do
        updated_suburb = create(:suburb, :random)
        contact.update_column(:suburb_id, updated_suburb.id)

        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to include(:zip, :city, :state)
        expect(customer_details[:zip]).to eq(updated_suburb.postcode)
        expect(customer_details[:city]).to eq(updated_suburb.name)
        expect(customer_details[:state]).to eq(updated_suburb.state)
      end
    end
  end # Location details

  context 'orders data' do
    let!(:order1) { create(:order, :new, customer_profile: customer, created_at: time - 10.days) }
    let!(:order2) { create(:order, :new, customer_profile: customer, created_at: time - 3.days) }
    let!(:order3) { create(:order, :new, customer_profile: customer, created_at: time - 22.days) }

    it 'returns the last ordered (creation) date' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:date_last_ordered)
      last_order_date = order2.created_at
      expected_last_order_date = Time.utc(last_order_date.year, last_order_date.month, last_order_date.day).midnight.to_i * 1000
      expect(customer_details[:date_last_ordered]).to eq(expected_last_order_date)
    end

    it 'returns the (Hubspot) lifecyclestage to customer' do
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:lifecyclestage)
      expect(customer_details[:lifecyclestage]).to eq('customer')
    end

    it 'returns the last ordered (creation) date from customer\'s orders only' do
      customer2 = create(:customer_profile, :random)
      order2.update_column(:customer_profile_id, customer2.id)
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:date_last_ordered)
      last_order_date = order1.created_at
      expected_last_order_date = Time.utc(last_order_date.year, last_order_date.month, last_order_date.day).midnight.to_i * 1000
      expect(customer_details[:date_last_ordered]).to eq(expected_last_order_date)
    end

    it 'returns the last ordered (creation) date from non- draft/cancelled/on-hold/rejected/voided orders only' do
      order2.update_column(:status, %w[draft on-hold cancelled rejected].sample)
      customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

      expect(customer_details.keys).to include(:date_last_ordered)
      last_order_date = order1.created_at
      expected_last_order_date = Time.utc(last_order_date.year, last_order_date.month, last_order_date.day).midnight.to_i * 1000
      expect(customer_details[:date_last_ordered]).to eq(expected_last_order_date)
    end

    context 'with order line category data' do
      let!(:category1) { create(:category, :random, group: 'kitchen-supplies') }
      let!(:category2) { create(:category, :random, group: 'kitchen-supplies') }
      let!(:category3) { create(:category, :random, group: 'kitchen-supplies') }
      let!(:category4) { create(:category, :random, group: 'kitchen-supplies') }

      let!(:order_line11) { create(:order_line, :random, order: order1, category: category1) }

      let!(:order_line21) { create(:order_line, :random, order: order2, category: category1) }
      let!(:order_line22) { create(:order_line, :random, order: order2, category: category2) }

      let!(:order_line31) { create(:order_line, :random, order: order3, category: category3) }
      let!(:order_line32) { create(:order_line, :random, order: order3, category: category4) }

      it 'returns ordered categories data' do
        expected_order_categories = [category1, category2, category3, category4].map(&:slug).uniq.sort.join(';')
        customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

        expect(customer_details.keys).to include(:order_categories)
        expect(customer_details[:order_categories]).to eq(expected_order_categories)
      end

      context 'with ordered categories or type(group) catering-services' do
        before do
          [category2, category3].each do |category|
            category.update_column(:group, 'catering-services')
          end
        end

        it 'groups the catering services ordered categories as catering-services' do
          expected_order_categories = ([category1, category4].map(&:slug).uniq + ['catering-services']).sort.join(';')
          customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

          expect(customer_details.keys).to include(:order_categories)
          expect(customer_details[:order_categories]).to eq(expected_order_categories)
        end
      end

      context 'with existing Hubspot Contact with order categories' do
        let!(:hs_contact_id) { rand(10_000...2_132_000) }
        let!(:existing_hs_categories) { %w[existing_hs_category1 existing_hs_category2] }
        let!(:existing_hs_contact) { OpenStruct.new(vid: hs_contact_id, properties: { order_categories: existing_hs_categories.join(';') }) }

        before do
          contact.update_column(:hs_contact_id, hs_contact_id.to_s)
          allow(Hubspot::Contact).to receive(:find_by_email).and_return(existing_hs_contact)
        end

        it 'send cumulative ordered categories if the Hubspot Contact exists with order categories' do
          expected_order_categories = ([category1, category2, category3, category4].map(&:slug) + existing_hs_categories).uniq.sort.join(';')
          customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

          expect(customer_details.keys).to include(:order_categories)
          expect(customer_details[:order_categories]).to eq(expected_order_categories)
        end

        it 'only sends new ordered categories if exisitng hs contact does not have any order categories' do
          existing_hs_contact_without_categories = OpenStruct.new(vid: hs_contact_id, properties: { order_categories: nil })
          allow(Hubspot::Contact).to receive(:find_by_email).and_return(existing_hs_contact_without_categories)

          expected_order_categories = [category1, category2, category3, category4].map(&:slug).uniq.sort.join(';') # - existing_hs_categories
          customer_details = Hubspot::BuildCustomerDetails.new(contact: contact).call

          expect(customer_details.keys).to include(:order_categories)
          expect(customer_details[:order_categories]).to eq(expected_order_categories)
        end
      end # with existing Hubspot contact
    end # order categories
  end # orders data

end
