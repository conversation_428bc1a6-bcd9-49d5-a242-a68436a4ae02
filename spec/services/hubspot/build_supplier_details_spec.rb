require 'rails_helper'

RSpec.describe Hubspot::BuildSupplierDetails, type: :service, hubspot: true do

  let!(:time) { Time.zone.now }
  let!(:supplier) { create(:supplier_profile, :random, :with_user, phone: nil, mobile: nil) }
  let!(:contact) { supplier.user }
  let!(:suburb) { create(:suburb, :random) }

  before do
    allow(Hubspot::Contact).to receive(:find_by_email).and_return(nil)
  end

  it 'returns default information' do
    supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

    expect(supplier_details.keys).to include(:firstname, :lastname, :lifecyclestage)
    expect(supplier_details[:firstname]).to eq(contact.firstname)
    expect(supplier_details[:lastname]).to eq(contact.lastname)
    expect(supplier_details[:lifecyclestage]).to eq('marketingqualifiedlead')
  end

  it 'returns supplier specific info' do
    supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

    expect(supplier_details.keys).to include(:custom_lifecyclestage)
    expect(supplier_details[:custom_lifecyclestage]).to eq('supplier')
    expect(supplier_details[:contact_type]).to eq('Supplier')
  end

  it 'returns the the last sign in value for the user if present' do
    last_sign_in = time - 22.days
    contact.update_column(:last_sign_in_at, last_sign_in)
    last_sign_in_utc = Time.utc(last_sign_in.year, last_sign_in.month, last_sign_in.day).midnight.to_i * 1000

    supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

    expect(supplier_details.keys).to include(:last_sign_in)
    expect(supplier_details[:last_sign_in]).to eq(last_sign_in_utc)
  end

  context 'with existing Hubspot contact' do
    let!(:hubspot_supplier) { OpenStruct.new(vid: rand(11_000...223_300)) }

    before do
      contact.update_column(:hs_contact_id, hubspot_supplier.vid.to_s)
      allow(Hubspot::Contact).to receive(:find_by_email).and_return(hubspot_supplier)
    end

    it 'makes a request to get the hubspot contact via email' do
      expect(Hubspot::Contact).to receive(:find_by_email).with(contact.email)

      Hubspot::BuildSupplierDetails.new(contact: contact).call
    end

    it 'does not return creation details' do
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to_not include(:firstname, :lastname, :lifecyclestage, :custom_lifecyclestage, :contact_type)
    end

    it 'returns creation details if set to refresh' do
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact, refresh: true).call

      expect(supplier_details.keys).to include(:firstname, :lastname, :lifecyclestage, :custom_lifecyclestage, :contact_type)
    end
  end

  context 'status details' do
    it 'returns the status as active by default' do
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:hs_content_membership_status)
      expect(supplier_details[:hs_content_membership_status]).to eq('active')
    end

    it 'returns the status as in-active if the contact is in-active' do
      contact.update_column(:is_active, false)
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:hs_content_membership_status)
      expect(supplier_details[:hs_content_membership_status]).to eq('inactive')
    end

    it 'returns the status as in-active if the supplier is not searchable' do
      supplier.update_column(:is_searchable, false)
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:hs_content_membership_status)
      expect(supplier_details[:hs_content_membership_status]).to eq('inactive')
    end
  end

  context 'Phone details' do
    let!(:phone) { Faker::PhoneNumber.phone_number }
    let!(:mobile) { Faker::PhoneNumber.phone_number }

    before do
      supplier.update_columns(phone: phone, mobile: mobile)
    end

    it 'returns supplier\'s contact_phone if present' do
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:phone)
      expect(supplier_details[:phone]).to eq(phone)
    end

    it 'returns supplier\'s mobile if present and contact_phone is blank' do
      supplier.update_column(:phone, nil)

      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:phone)
      expect(supplier_details[:phone]).to eq(mobile)
    end

    context 'with existing Hubspot contact with phone info' do
      let!(:hubspot_supplier) { OpenStruct.new(vid: rand(11_000...223_300), properties: { phone: phone }) }

      before do
        contact.update_column(:hs_contact_id, hubspot_supplier.vid.to_s)
        allow(Hubspot::Contact).to receive(:find_by_email).and_return(hubspot_supplier)
      end

      it 'does not return contact info if existing hubspot has the same phone as supplier' do
        supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

        expect(supplier_details.keys).to_not include(:phone)
      end

      it 'returns updated contact info if existing hubspot has different phone as supplier' do
        updated_phone = Faker::PhoneNumber.phone_number
        supplier.update_column(:phone, updated_phone)
        supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

        expect(supplier_details.keys).to include(:phone)
        expect(supplier_details[:phone]).to eq(updated_phone)
      end
    end
  end # phone details

  context 'Location data' do
    before do
      contact.update_column(:suburb_id, suburb.id)
    end

    it 'returns default location details' do
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:country, :hs_timezone)
      expect(supplier_details[:country]).to eq('Australia')
      expect(supplier_details[:hs_timezone]).to eq('australia_slash_nsw')
    end

    it 'returns user attached suburb details if present' do
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:zip, :city, :state)
      expect(supplier_details[:zip]).to eq(suburb.postcode)
      expect(supplier_details[:city]).to eq(suburb.name)
      expect(supplier_details[:state]).to eq(suburb.state)
    end

    context 'with company address suburb' do
      let!(:address_suburb) { create(:suburb, :random) }

      before do
        supplier.update_column(:company_address_suburb_id, address_suburb.id)
      end

      it 'returns company address suburb ' do
        supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

        expect(supplier_details.keys).to include(:zip, :city, :state)
        expect(supplier_details[:zip]).to eq(address_suburb.postcode)
        expect(supplier_details[:city]).to eq(address_suburb.name)
        expect(supplier_details[:state]).to eq(address_suburb.state)
      end
    end

    context 'with existing Hubspot contact with suburb info' do
      let!(:hubspot_supplier) { OpenStruct.new(vid: rand(11_000...223_300), properties: { city: suburb.name }) }

      before do
        contact.update_column(:hs_contact_id, hubspot_supplier.vid.to_s)
        allow(Hubspot::Contact).to receive(:find_by_email).and_return(hubspot_supplier)
      end

      it 'does not return location info if existing hubspot supplier city is the same as the contact suburb' do
        supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

        expect(supplier_details.keys).to_not include(:zip, :city, :state)
      end

      it 'return updated location info if existing hubspot supplier city is different from the the contact suburb' do
        updated_suburb = create(:suburb, :random)
        contact.update_column(:suburb_id, updated_suburb.id)

        supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

        expect(supplier_details.keys).to include(:zip, :city, :state)
        expect(supplier_details[:zip]).to eq(updated_suburb.postcode)
        expect(supplier_details[:city]).to eq(updated_suburb.name)
        expect(supplier_details[:state]).to eq(updated_suburb.state)
      end
    end
  end # location details

  context 'orders data' do
    let!(:order1) { create(:order, :new, delivery_at: time + 10.days) }
    let!(:order_line1) { create(:order_line, :random, order: order1, supplier_profile: supplier) }

    let!(:order2) { create(:order, :new, delivery_at: time + 3.days) }
    let!(:order_line2) { create(:order_line, :random, order: order2, supplier_profile: supplier) }

    let!(:order3) { create(:order, :new, delivery_at: time + 22.days) }
    let!(:order_line3) { create(:order_line, :random, order: order3, supplier_profile: supplier) }

    it 'returns the last ordered (by delivery) date' do
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:date_last_ordered)
      last_order_date = order3.delivery_at
      expected_last_order_date = Time.utc(last_order_date.year, last_order_date.month, last_order_date.day).midnight.to_i * 1000
      expect(supplier_details[:date_last_ordered]).to eq(expected_last_order_date)
    end

    it 'returns the Hubspot lifecycle stage as other' do
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:lifecyclestage)
      expect(supplier_details[:lifecyclestage]).to eq('other')
    end

    it 'returns the last ordered (by delivery) date from supplier\'s orders only' do
      supplier2 = create(:supplier_profile, :random)
      order_line3.update_column(:supplier_profile_id, supplier2.id)
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:date_last_ordered)
      last_order_date = order1.delivery_at
      expected_last_order_date = Time.utc(last_order_date.year, last_order_date.month, last_order_date.day).midnight.to_i * 1000
      expect(supplier_details[:date_last_ordered]).to eq(expected_last_order_date)
    end

    it 'returns the last ordered (by delivery) date from non- draft/cancelled/on-hold/rejected orders only' do
      order3.update_column(:status, %w[draft on-hold cancelled rejected].sample)
      supplier_details = Hubspot::BuildSupplierDetails.new(contact: contact).call

      expect(supplier_details.keys).to include(:date_last_ordered)
      last_order_date = order1.delivery_at
      expected_last_order_date = Time.utc(last_order_date.year, last_order_date.month, last_order_date.day).midnight.to_i * 1000
      expect(supplier_details[:date_last_ordered]).to eq(expected_last_order_date)
    end
  end # orders details
end
