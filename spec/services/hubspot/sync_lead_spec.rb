require 'rails_helper'

RSpec.describe Hubspot::SyncLead, type: :service, hubspot: true do

  let!(:time) { Time.zone.now }
  let!(:lead) { create(:lead, :random, email: Faker::Internet.email, firstname: Faker::Name.first_name, lastname: Faker::Name.last_name) }
  let!(:hubspot_contact) { OpenStruct.new(vid: rand(11_000...223_300), properties: { email: lead.email, firstname: lead.firstname, lastname: lead.lastname }) }

  before do
    allow(Hubspot::Contact).to receive(:createOrUpdate).and_return(hubspot_contact)
  end

  it 'makes a request to create (or update) a Hubspot Contact with the passed in lead and saves the returned hubspot contact id' do
    sync_body = {
      firstname: lead.firstname,
      lastname: lead.lastname,
      lifecyclestage: 'marketingqualifiedlead',
      custom_lifecyclestage: lead.lead_type,

      country: 'Australia',
      hs_timezone: 'australia_slash_nsw',
    }
    expect(Hubspot::Contact).to receive(:createOrUpdate).with(lead.email, sync_body)

    syncer = Hubspot::SyncLead.new(lead: lead).call
    expect(syncer).to be_success

    updated_lead = syncer.lead.reload
    expect(updated_lead.hs_contact_id).to eq(hubspot_contact.vid.to_s)
  end

  context 'with hub spot error response' do
    let!(:exception) { RuntimeError.new('runtime-error') }

    before do
      allow(Hubspot::Contact).to receive(:createOrUpdate).and_raise(exception)
    end

    it 'makes a request to create (or update) a Hubspot Contact with the passed in lead and does not save the hubspot contact id' do
      expect(Hubspot::Contact).to receive(:createOrUpdate).with(lead.email, anything)

      syncer = Hubspot::SyncLead.new(lead: lead).call
      expect(syncer).to_not be_success
      expect(syncer.errors).to include("Sync for Lead ##{lead.id} failed due to: #{exception}")

      updated_lead = syncer.lead.reload
      expect(updated_lead.hs_contact_id).to be_blank
    end
  end

end
