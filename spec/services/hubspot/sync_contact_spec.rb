require 'rails_helper'

RSpec.describe Hubspot::SyncContact, type: :service, hubspot: true do

  let!(:time) { Time.zone.now }
  let!(:customer) { create(:customer_profile, :random, :with_user, contact_phone: nil, mobile: nil) }
  let!(:supplier) { create(:supplier_profile, :random, :with_user, phone: nil, mobile: nil) }
  let!(:contact) { [customer, supplier].sample.user }
  let!(:suburb) { create(:suburb, :random) }

  let!(:hubspot_contact) { OpenStruct.new(vid: rand(11_000...223_300), properties: { email: contact.email, firstname: contact.firstname, lastname: contact.lastname }) }

  # generic sync body
  let!(:customer_sync_body) do
    {
      firstname: customer.user.firstname,
      lastname: customer.user.lastname,
      jobtitle: Faker::Name.name,
      country: 'Australia',
      last_sign_in: nil,
      lifecyclestage: 'marketingqualifiedlead',
      custom_lifecyclestage: 'customer',
      hs_timezone: 'australia_slash_nsw',
      # ... other details
    }
  end

  let!(:supplier_sync_body) do
    {
      firstname: customer.user.firstname,
      lastname: customer.user.lastname,
      country: 'Australia',
      last_sign_in: nil,
      lifecyclestage: 'marketingqualifiedlead',
      custom_lifecyclestage: 'supplier',
      hs_timezone: 'australia_slash_nsw',
      # ... other details
    }
  end

  before do
    allow(Hubspot::Contact).to receive(:createOrUpdate).and_return(hubspot_contact)

    customer_details_builder = double(Hubspot::BuildCustomerDetails)
    allow(Hubspot::BuildCustomerDetails).to receive(:new).and_return(customer_details_builder)
    allow(customer_details_builder).to receive(:call).and_return(customer_sync_body)

    supplier_details_builder = double(Hubspot::BuildSupplierDetails)
    allow(Hubspot::BuildSupplierDetails).to receive(:new).and_return(supplier_details_builder)
    allow(supplier_details_builder).to receive(:call).and_return(supplier_sync_body)
  end

  it 'makes a request to create (or update) a Hubspot Contact for the passed in contact and saves the returned hubspot contact id' do
    expect(Hubspot::Contact).to receive(:createOrUpdate).with(contact.email, anything) # sync_body will be setup as per below tests

    syncer = Hubspot::SyncContact.new(contact: contact).call
    expect(syncer).to be_success

    updated_contact = syncer.contact.reload
    expect(updated_contact.hs_contact_id).to eq(hubspot_contact.vid.to_s)
  end

  context 'Syncing Customer Contact' do
    let!(:contact) { customer.user }

    it 'expects to build customer details' do
      expect(Hubspot::BuildCustomerDetails).to receive(:new).with(contact: contact, refresh: anything)

      syncer = Hubspot::SyncContact.new(contact: contact).call
      expect(syncer).to be_success
    end

    it 'syncs the contact with customer details' do
      expect(Hubspot::Contact).to receive(:createOrUpdate).with(contact.email, customer_sync_body)

      syncer = Hubspot::SyncContact.new(contact: contact).call
      expect(syncer).to be_success
    end
  end # customer sync

  context 'Syncing Supplier Contact' do
    let!(:contact) { supplier.user }

    it 'expects to build supplier details' do
      expect(Hubspot::BuildSupplierDetails).to receive(:new).with(contact: contact, refresh: anything)

      syncer = Hubspot::SyncContact.new(contact: contact).call
      expect(syncer).to be_success
    end

    it 'syncs the contact with supplier details' do
      expect(Hubspot::Contact).to receive(:createOrUpdate).with(contact.email, supplier_sync_body)

      syncer = Hubspot::SyncContact.new(contact: contact).call
      expect(syncer).to be_success
    end
  end # supplier sync

  context 'with hub spot error response' do
    let!(:exception) { RuntimeError.new('runtime-error') }

    before do
      allow(Hubspot::Contact).to receive(:createOrUpdate).and_raise(exception)
    end

    it 'makes a request to create (or update) a Hubspot Contact with the passed in contact and does not save the hubspot contact id' do
      expect(Hubspot::Contact).to receive(:createOrUpdate).with(contact.email, anything)

      syncer = Hubspot::SyncContact.new(contact: contact).call
      expect(syncer).to_not be_success
      expect(syncer.errors).to include("Sync for User ##{contact.id} failed due to: #{exception}")

      updated_contact = syncer.contact.reload
      expect(updated_contact.hs_contact_id).to be_blank
    end
  end

end
