require 'rails_helper'

RSpec.describe OrderLines::Fetch, type: :service, order_lines: true do
  let(:supplier) { create(:supplier_profile, :random, commission_rate: 0.0) }
  let(:order) { create(:order, :random, status: 'confirmed') }
  let(:location) { create(:location, :random, order: order) }
  let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }
  let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
  let(:menu_extra) { create(:menu_extra, :random, menu_item: menu_item) }
  let(:team_order_attendee) { create(:team_order_attendee, :random, order: order) }

  context 'exiting order line' do
    let!(:order_line_with_notes) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5, selected_menu_extras: [menu_extra.id], note: Faker::Lorem.sentence) }
    let!(:order_line_with_extras) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5, selected_menu_extras: [menu_extra.id]) }
    let!(:serving_size_order_line) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, serving_size: serving_size, supplier_profile: supplier, quantity: 5) }
    let!(:menu_item_order_line) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5) }
    let!(:attendee_order_line) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5, team_order_attendee: team_order_attendee) }

    it 'fetches the order line by ID' do
      order_line_to_be_fetched_by_id = [menu_item_order_line, serving_size_order_line, order_line_with_extras].sample
      order_line_params = { id: order_line_to_be_fetched_by_id.id }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to eq(order_line_to_be_fetched_by_id)
    end

    it 'fetches the order line by location and menu item' do
      order_line_params = { item_id: menu_item.id }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to eq(menu_item_order_line)
    end

    it 'fetches the order line by location and menu item and serving_size' do
      order_line_params = { item_id: menu_item.id, serving_size_id: serving_size.id }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to eq(serving_size_order_line)
    end

    it 'fetches the order line by location and menu item and menu extras' do
      order_line_params = { item_id: menu_item.id, selected_menu_extra_ids: [menu_extra.id] }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to eq(order_line_with_extras)
    end

    it 'generates a new order line when fetching with partial menu extras' do
      menu_extra2 = create(:menu_extra, :random, menu_item: menu_item)
      order_line_with_extras.update(selected_menu_extras: [menu_extra.id, menu_extra2.id])
      order_line_params = { item_id: menu_item.id, selected_menu_extra_ids: [menu_extra.id] }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to_not eq(order_line_with_extras)
      expect(fetched_order_line).to be_new_record
      expect(fetched_order_line.location).to eq(location)
      expect(fetched_order_line.selected_menu_extras).to match_array([menu_extra.id])
    end

    it 'fetches the order line by location and menu item and menu extras and note' do
      order_line_params = { item_id: menu_item.id, selected_menu_extra_ids: [menu_extra.id], note: order_line_with_notes.note }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to eq(order_line_with_notes)
    end

    it 'fetches the order line by attendee id', team_orders: true do
      order_line_params = { item_id: menu_item.id, attendee_id: team_order_attendee.id }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to eq(attendee_order_line)
    end
  end

  context 'new order line' do
    it 'generates a new order line for the specified location and menu item' do
      order_line_params = { item_id: menu_item.id }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to be_new_record
      expect(fetched_order_line.location).to eq(location)
      expect(fetched_order_line.menu_item).to eq(menu_item)
    end

    it 'generates a new order line for the specified location and menu item and serving_size' do
      order_line_params = { item_id: menu_item.id, serving_size_id: serving_size.id }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to be_new_record
      expect(fetched_order_line.location).to eq(location)
      expect(fetched_order_line.menu_item).to eq(menu_item)
      expect(fetched_order_line.serving_size).to eq(serving_size)
    end

    it 'generates a new order line for the specified location and menu item and menu extras' do
      order_line_params = { item_id: menu_item.id, selected_menu_extra_ids: [menu_extra.id] }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to be_new_record
      expect(fetched_order_line.location).to eq(location)
      expect(fetched_order_line.selected_menu_extras).to match_array([menu_extra.id])
    end

    it 'generates a new order line for the specified location and menu item and note' do
      order_line_params = { item_id: menu_item.id, note: Faker::Lorem.sentence }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to be_new_record
      expect(fetched_order_line.location).to eq(location)
      expect(fetched_order_line.note).to eq(order_line_params[:note])
    end

    it 'generates a new order line for the specified location, menu item and attendee', team_orders: true do
      order_line_params = { item_id: menu_item.id, attendee_id: team_order_attendee.id }
      fetched_order_line = OrderLines::Fetch.new(location: location, order_line_params: order_line_params).call

      expect(fetched_order_line).to be_new_record
      expect(fetched_order_line.location).to eq(location)
      expect(fetched_order_line.attendee_id).to eq(team_order_attendee.id)
    end
  end
end
