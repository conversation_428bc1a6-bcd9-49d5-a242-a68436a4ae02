require 'rails_helper'

RSpec.describe OrderLines::CalculatePrice, type: :service, order_lines: true do

  let(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }
  let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
  let(:menu_extra1) { create(:menu_extra, :random, menu_item: menu_item) }
  let(:menu_extra2) { create(:menu_extra, :random, menu_item: menu_item) }

  let(:menu_item_order_line) { build(:order_line, menu_item: menu_item, quantity: 20) }
  let(:serving_size_order_line) { build(:order_line, menu_item: menu_item, serving_size: serving_size, quantity: 20) }
  let(:order_line_with_extras) { build(:order_line, menu_item: menu_item, selected_menu_extras: [menu_extra1, menu_extra2].map(&:id), quantity: 20) }

  let(:company) { create(:company, :random) }
  let!(:customer) { create(:customer_profile, :random, company: company) }

  let!(:markup_override_fetcher) { double(Suppliers::FetchMarkupOverride) }

  before do
    # mock markup fetcher
    allow(Suppliers::FetchMarkupOverride).to receive(:new).and_return(markup_override_fetcher)
    allow(markup_override_fetcher).to receive(:call).and_return(nil)
  end

  context 'without a supplier markup' do
    let(:supplier) { create(:supplier_profile, :random, markup: 0.0) }

    it 'calculates the price of the order line from the menu item' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: menu_item_order_line).call

      expect(calculated_price).to eq(menu_item.price)
    end

    it 'calculates the price of the order line from the menu item\'s promo price if present', promotional_item: true do
      menu_item.update_column(:promo_price, rand(5.22...10.98))
      calculated_price = OrderLines::CalculatePrice.new(order_line: menu_item_order_line).call

      expect(calculated_price.round(2)).to eq(menu_item.promo_price.round(2))
    end

    it 'calculates the price of the order line from the serving size' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: serving_size_order_line).call

      expect(calculated_price.round(2)).to eq(serving_size.price.round(2))
    end

    it 'calculates the price of the order line from the menu item + any menu extras' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_extras).call
      expect(calculated_price).to eq(menu_item.price + menu_extra1.price.to_f + menu_extra2.price.to_f)
    end

    it 'calculates the price of the order line from the menu item\'s promotional price (if present) + any menu extras', promotional_item: true do
      menu_item.update_column(:promo_price, rand(5.22...10.98))
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_extras).call

      expect(calculated_price.round(2)).to eq((menu_item.promo_price + menu_extra1.price.to_f + menu_extra2.price.to_f).round(2))
    end
  end # no markup

  context 'with a suppler markup' do
    let(:supplier) { create(:supplier_profile, :random, markup: 15.0) }

    it 'calculates the price of the order line from the menu item including supplier markup' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: menu_item_order_line).call
      expected_price = menu_item.price.to_f * (1 + (supplier.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end

    it 'calculates the price of the order line from the menu item\'s promo price including supplier markup', promotional_item: true do
      menu_item.update_column(:promo_price, rand(5.22...10.98))
      calculated_price = OrderLines::CalculatePrice.new(order_line: menu_item_order_line).call

      expected_price = menu_item.promo_price.to_f * (1 + (supplier.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end

    it 'calculates the price of the order line from the serving size including supplier markup' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: serving_size_order_line).call

      expected_price = serving_size.price.to_f * (1 + (supplier.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end

    it 'calculates the price of the order line from the menu item + any menu extras (including supplier markup)' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_extras).call

      expected_price = menu_item.price.to_f * (1 + (supplier.markup / 100))
      expected_price += menu_extra1.price.to_f * (1 + (supplier.markup / 100))
      expected_price += menu_extra2.price.to_f * (1 + (supplier.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end

    it 'calculates the price of the order line from the menu item\'s promo price + any menu extras (including supplier markup)', promotional_item: true do
      menu_item.update_column(:promo_price, rand(5.22...10.98))
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_extras).call

      expected_price = menu_item.promo_price.to_f * (1 + (supplier.markup / 100))
      expected_price += menu_extra1.price.to_f * (1 + (supplier.markup / 100))
      expected_price += menu_extra2.price.to_f * (1 + (supplier.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end
  end # supplier markup

  context 'with a suppler markup override', markup_overrides: true do
    let!(:supplier) { create(:supplier_profile, :random, markup: 15.0) }
    let!(:markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: company, markup: 20) }

    before do
      allow(markup_override_fetcher).to receive(:call).and_return(markup_override)
    end

    it 'calculates the price of the order line from the menu item including overriden markup' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: menu_item_order_line, company: company).call
      expected_price = menu_item.price.to_f * (1 + (markup_override.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end

    context 'markup_override with no override value for `markup`' do
      before do
        markup_override.update_column(:markup, nil) # only symbolical as we mock the return of fetch markup override
        allow(markup_override_fetcher).to receive(:call).and_return(nil)
      end

      it 'calculates the price of the order line from the menu item using the default supplier markup' do
        calculated_price = OrderLines::CalculatePrice.new(order_line: menu_item_order_line, company: company).call
        expected_price = menu_item.price.to_f * (1 + (supplier.markup / 100))
        expect(calculated_price).to eq(expected_price)
      end
    end

    it 'calculates the price of the order line from the menu item\' promo price including overriden markup', promotional_item: true do
      menu_item.update_column(:promo_price, rand(5.22...10.98))
      calculated_price = OrderLines::CalculatePrice.new(order_line: menu_item_order_line, company: company).call

      expected_price = menu_item.promo_price.to_f * (1 + (markup_override.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end

    it 'calculates the price of the order line from the serving size including overriden markup' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: serving_size_order_line, company: company).call

      expected_price = serving_size.price.to_f * (1 + (markup_override.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end

    it 'calculates the price of the order line from the menu item + any menu extras (including overriden markup)' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_extras, company: company).call

      expected_price = menu_item.price.to_f * (1 + (markup_override.markup / 100))
      expected_price += menu_extra1.price.to_f * (1 + (markup_override.markup / 100))
      expected_price += menu_extra2.price.to_f * (1 + (markup_override.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end

    it 'calculates the price of the order line from the menu item\' promo_price + any menu extras (including overriden markup)', promotional_item: true do
      menu_item.update_column(:promo_price, rand(5.22...10.98))
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_extras, company: company).call

      expected_price = menu_item.promo_price.to_f * (1 + (markup_override.markup / 100))
      expected_price += menu_extra1.price.to_f * (1 + (markup_override.markup / 100))
      expected_price += menu_extra2.price.to_f * (1 + (markup_override.markup / 100))
      expect(calculated_price).to eq(expected_price)
    end
  end # markup override

  context 'with menu item rate cards irrespective of markup or overrides' do
    let!(:supplier) { create(:supplier_profile, :random, markup: [0.0, 15.0].sample) }

    let(:menu_item_with_rate_card) { create(:menu_item, :random, supplier_profile: supplier) }
    let!(:menu_item_rate_card) { create(:rate_card, :random, menu_item: menu_item_with_rate_card, company: company) }
    let(:order_line_with_menu_item_rate_card) { build(:order_line, menu_item: menu_item_with_rate_card, quantity: 20) }

    it 'calculates the price based on the passed in menu_items rate card' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_menu_item_rate_card, rate_card: menu_item_rate_card).call

      expect(calculated_price).to eq(menu_item_rate_card.price)
    end

    it 'calculates the price based on the menu_items rate card found using passed in company' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_menu_item_rate_card, company: company).call

      expect(calculated_price).to eq(menu_item_rate_card.price)
    end

    it 'calculates the price based on the menu_items rate card found using passed in customer\'s company' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_menu_item_rate_card, customer: customer).call

      expect(calculated_price).to eq(menu_item_rate_card.price)
    end

    it 'calculates the price based on the menu_items rate card (found using passed in company) even with promo price', promotional_item: true do
      menu_item_with_rate_card.update_column(:promo_price, rand(5.22...10.98))
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_menu_item_rate_card, company: company).call

      expect(calculated_price).to eq(menu_item_rate_card.price)
    end
  end

  context 'with serving size rate cards irrespective of markup or overrides' do
    let!(:supplier) { create(:supplier_profile, :random, markup: [0.0, 15.0].sample) }

    let(:menu_item_with_rate_card) { create(:menu_item, :random, supplier_profile: supplier) }
    let(:serving_size_with_rate_card) { create(:serving_size, :random, menu_item: menu_item_with_rate_card) }

    let(:order_line_with_serving_size_rate_card) { build(:order_line, menu_item: menu_item_with_rate_card, serving_size: serving_size_with_rate_card, quantity: 20) }
    let!(:serving_size_rate_card) { create(:rate_card, :random, serving_size: serving_size_with_rate_card, company: company) }

    it 'calculates the price based on the passed in serving_sizes rate card' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_serving_size_rate_card, rate_card: serving_size_rate_card).call

      expect(calculated_price).to eq(serving_size_rate_card.price)
    end

    it 'calculates the price based on the serving_sizes rate card found using passed in company' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_serving_size_rate_card, company: company).call

      expect(calculated_price).to eq(serving_size_rate_card.price)
    end

    it 'calculates the price based on the serving_sizes rate card found using passed in customer\'s company' do
      calculated_price = OrderLines::CalculatePrice.new(order_line: order_line_with_serving_size_rate_card, customer: customer).call

      expect(calculated_price).to eq(serving_size_rate_card.price)
    end
  end
end
