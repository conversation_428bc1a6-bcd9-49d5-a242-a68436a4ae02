require 'rails_helper'

RSpec.describe OrderLines::CalculateOrderTotals, type: :service, orders: true, order_lines: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:order) { create(:order, :new, customer_profile: customer) }
  let!(:order_line) { create(:order_line, :random, order: order) }

  before do
    order_total_calculator = double(Orders::CalculateCustomerTotals)
    allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(order_total_calculator)
    allow(order_total_calculator).to receive(:call).and_return(true)
  end

  it 'calculates and saves the totals for the order' do
    expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, save_totals: true)
    total_calculator = OrderLines::CalculateOrderTotals.new(order_line: order_line).call

    expect(total_calculator.order_totals).to be_present
    expect(total_calculator.team_order_spends).to_not be_present
  end

  context 'for a team order', team_orders: true do
    let!(:team_order_detail) { create(:team_order_detail, :random, order: order) }

    before do
      order.update_columns(status: 'pending', order_variant: 'team_order', unique_event_id: SecureRandom.hex(7))
    end

    it 'calculates and saves the totals for the order' do
      expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, save_totals: true)
      total_calculator = OrderLines::CalculateOrderTotals.new(order_line: order_line).call

      expect(total_calculator.order_totals).to be_present
      expect(total_calculator.team_order_spends).to_not be_present
    end

    context 'when ordering as team order attendees' do
      let!(:team_order_attendee) { create(:team_order_attendee, :random, order: order) }
      let!(:attendee_order_line) { create(:order_line, :random, order: order, team_order_attendee: team_order_attendee) }
      it 'calculates and saves the totals for the attendee' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, attendee: team_order_attendee)
        total_calculator = OrderLines::CalculateOrderTotals.new(order_line: attendee_order_line).call

        expect(total_calculator.order_totals).to be_present
      end

      it 'does not return supplier spends' do
        expect(Orders::GetSupplierSpends).to_not receive(:new)
        total_calculator = OrderLines::CalculateOrderTotals.new(order_line: attendee_order_line).call

        expect(total_calculator.team_order_spends).to_not be_present
      end
    end

    context 'when ordering as team admin' do
      let!(:admin_as_attendee) { build(:team_order_attendee, :random, order: order, uniq_code: order.unique_event_id) }

      before do
        attendee_fetcher = double(TeamOrderAttendees::Fetch)
        allow(TeamOrderAttendees::Fetch).to receive(:new).and_return(attendee_fetcher)
        allow(attendee_fetcher).to receive(:call).and_return(admin_as_attendee)

        spend_calculator = double(Orders::GetSupplierSpends)
        allow(Orders::GetSupplierSpends).to receive(:new).and_return(spend_calculator)
        allow(spend_calculator).to receive(:call).and_return(true)
      end

      it 'fetches the team admin as a team order attendee' do
        expect(TeamOrderAttendees::Fetch).to receive(:new).with(attendee_code: order.unique_event_id, profile: customer)

        OrderLines::CalculateOrderTotals.new(order_line: order_line, profile: customer).call
      end

      it 'calculates the order totals for the team admin order' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, attendee: admin_as_attendee)
        total_calculator = OrderLines::CalculateOrderTotals.new(order_line: order_line, profile: customer).call

        expect(total_calculator.order_totals).to be_present
      end

      it 'returns the supplier spends' do
        expect(Orders::GetSupplierSpends).to receive(:new).with(order: order, exclude_surcharge: true)
        total_calculator = OrderLines::CalculateOrderTotals.new(order_line: order_line, profile: customer).call

        expect(total_calculator.team_order_spends).to be_present
      end
    end

    context 'when not in pending state' do
      before do
        supplier = create(:supplier_profile, :random)
        order_line.update_column(:supplier_profile_id, supplier.id)
        order.update_column(:status, 'new')

        supplier_spends = OpenStruct.new({ supplier_spends: [supplier] })
        spend_calculator = double(Orders::GetSupplierSpends)
        allow(Orders::GetSupplierSpends).to receive(:new).and_return(spend_calculator)
        allow(spend_calculator).to receive(:call).and_return(supplier_spends)

        minimum_charger = double(Orders::ChargeToSupplierMinimums)
        allow(Orders::ChargeToSupplierMinimums).to receive(:new).and_return(minimum_charger)
        allow(minimum_charger).to receive(:call).and_return(true)
      end

      it 're-calculates the supplier surcharge (top-up) if team order cutoff option is set to charge_to_minimum' do
        team_order_detail.update_column(:cutoff_option, 'charge_to_minimum')
        expect(Orders::ChargeToSupplierMinimums).to receive(:new) # .with(team_order: order)
        if [true, false].sample
          total_calculator = OrderLines::CalculateOrderTotals.new(order_line: order_line, profile: customer).call
        else
          team_order_attendee = create(:team_order_attendee, :random, order: order)
          order_line.update_column(:attendee_id, team_order_attendee.id)
          total_calculator = OrderLines::CalculateOrderTotals.new(order_line: order_line).call
        end

        expect(total_calculator.order_totals).to be_present
      end

      it 'does not re-calculate the supplier surcharge (top-up) if team order cutoff option is not set to charge_to_minimum' do
        team_order_detail.update_column(:cutoff_option, [nil, 'cancel_order'].sample)
        expect(Orders::ChargeToSupplierMinimums).to_not receive(:new)
        if [true, false].sample
          total_calculator = OrderLines::CalculateOrderTotals.new(order_line: order_line, profile: customer).call
        else
          team_order_attendee = create(:team_order_attendee, :random, order: order)
          order_line.update_column(:attendee_id, team_order_attendee.id)
          total_calculator = OrderLines::CalculateOrderTotals.new(order_line: order_line).call
        end

        expect(total_calculator.order_totals).to be_present
      end
    end
  end

end
