require 'rails_helper'

RSpec.describe OrderLines::CreateMultiple, type: :service, order_lines: true, orders: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :draft, customer_profile: customer) }

  let(:supplier) { create(:supplier_profile, :random) }
  let(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier) }
  let(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier) }
  let(:menu_item3) { create(:menu_item, :random, supplier_profile: supplier) }

  let(:order_line_params1) { { item_id: menu_item1, quantity: 10 } }
  let(:order_line_params2) { { item_id: menu_item2, quantity: 15 } }
  let(:order_line_params3) { { item_id: menu_item3, quantity: 13 } }

  let(:order_lines_params) { [order_line_params1, order_line_params2, order_line_params3] }

  it 'returns the passed in order' do
    multiple_creator = OrderLines::CreateMultiple.new(order: order, order_lines_params: order_lines_params).call
    expect(multiple_creator).to be_success

    expect(multiple_creator.order).to be_present
    expect(multiple_creator.order).to eq(order)
  end

  it 'creates a new location within the order' do
    multiple_creator = OrderLines::CreateMultiple.new(order: order).call

    expect(multiple_creator).to be_success
    expect(multiple_creator.location).to be_present
    expect(multiple_creator.location).to be_persisted
    expect(multiple_creator.location.order).to eq(order)
  end

  it 'does not create any order lines if not passed' do
    multiple_creator = OrderLines::CreateMultiple.new(order: order).call

    expect(multiple_creator).to be_success
    expect(multiple_creator.order).to be_present
    expect(multiple_creator.created_order_lines).to be_blank
    expect(multiple_creator.order.order_lines).to be_blank
  end

  it 'creates new order lines passed' do
    multiple_creator = OrderLines::CreateMultiple.new(order: order, order_lines_params: order_lines_params).call

    expect(multiple_creator).to be_success
    expect(multiple_creator.order).to be_present
    created_order_lines = multiple_creator.created_order_lines
    expect(created_order_lines.size).to eq(3)
    expect(created_order_lines.map(&:menu_item)).to include(menu_item1, menu_item2, menu_item3)
    expect(created_order_lines.map(&:quantity)).to include(10, 15, 13)
  end

  it 'returns the order based on ID passed in the order params' do
    multiple_creator = OrderLines::CreateMultiple.new(order: nil, order_params: { order_id: order.id }, order_lines_params: order_lines_params).call
    expect(multiple_creator).to be_success
    expect(multiple_creator.order).to be_present
    expect(multiple_creator.order).to eq(order)
  end

  it 'creates new order lines within the order' do
    multiple_creator = OrderLines::CreateMultiple.new(order: order, order_lines_params: order_lines_params).call

    expect(multiple_creator).to be_success
    expect(multiple_creator.order).to be_present
    created_order_lines = multiple_creator.created_order_lines
    expect(created_order_lines.size).to eq(3)
    expect(created_order_lines.map(&:order)).to include(order)
    expect(created_order_lines.map(&:menu_item)).to include(menu_item1, menu_item2, menu_item3)
    expect(created_order_lines.map(&:quantity)).to include(10, 15, 13)
  end

  it 'creates a new location within the order' do
    multiple_creator = OrderLines::CreateMultiple.new(order: order).call

    expect(multiple_creator).to be_success
    expect(multiple_creator.location).to be_present
    expect(multiple_creator.location).to be_persisted
    expect(multiple_creator.location.order).to eq(order)
  end

  it 'creates new order lines within the order\'s location' do
    multiple_creator = OrderLines::CreateMultiple.new(order: order, order_lines_params: order_lines_params).call

    expect(multiple_creator).to be_success

    created_order_lines = multiple_creator.created_order_lines
    order_line_locations = created_order_lines.map(&:location)
    expect(order_line_locations.map(&:order).uniq).to eq([order])
  end

  context 'for an existing location' do
    let!(:location) { create(:location, :random, order: order) }

    it 'does not create a duplicate location' do
      multiple_creator = OrderLines::CreateMultiple.new(order: order, order_params: { location_id: location.id }).call

      expect(multiple_creator).to be_success
      expect(multiple_creator.location).to eq(location)
    end

    it 'creates new order lines within the location' do
      multiple_creator = OrderLines::CreateMultiple.new(order: order, order_params: { location_id: location.id }, order_lines_params: order_lines_params).call

      expect(multiple_creator).to be_success

      created_order_lines = multiple_creator.created_order_lines
      expect(created_order_lines.size).to eq(3)
      expect(created_order_lines.map(&:order)).to include(order)
      expect(created_order_lines.map(&:location)).to include(location)
    end
  end

  context 'Locations Fetch' do
    let!(:location_fetcher) { double(Locations::Fetch) }
    let!(:location) { create(:location, :random, order: order) }

    before do
      # mock location fetcher
      allow(Locations::Fetch).to receive(:new).and_return(location_fetcher)
      allow(location_fetcher).to receive(:call).and_return(location)
    end

    it 'makes a request the fetch the location if no location id passed' do
      expect(Locations::Fetch).to receive(:new).with(order: order, location_params: { id: nil })
      multiple_creator = OrderLines::CreateMultiple.new(order: order).call

      expect(multiple_creator).to be_success
      expect(multiple_creator.location).to eq(location)
    end

    it 'makes a request the fetch the location with passed in location' do
      expect(Locations::Fetch).to receive(:new).with(order: order, location_params: { id: location.id })
      multiple_creator = OrderLines::CreateMultiple.new(order: order, order_params: { location_id: location.id }).call

      expect(multiple_creator).to be_success
      expect(multiple_creator.location).to eq(location)
    end

    it 'errors if location fetcher returns nil' do
      allow(location_fetcher).to receive(:call).and_return(nil)

      multiple_creator = OrderLines::CreateMultiple.new(order: order, order_params: { location_id: location.id }).call

      expect(multiple_creator).to_not be_success
      expect(multiple_creator.errors).to include('Cannot create order lines without a location')
    end
  end

  context 'with existing order line and locations (Renewing orders)' do
    let(:location) { create(:location, :random, order: order) }
    let!(:order_line1) { create(:order_line, :random, order: order, location: location, menu_item: menu_item1, quantity: 10) }
    let!(:order_line2) { create(:order_line, :random, order: order, location: location, menu_item: menu_item2, quantity: 10) }
    let!(:order_line3) { create(:order_line, :random, order: order, location: location, menu_item: menu_item3, quantity: 10) }

    it 'creates a new order lines in a location that does not even belong to the order' do
      other_order = create(:order, :draft, customer_profile: customer)

      multiple_creator = OrderLines::CreateMultiple.new(order: other_order, order_params: { location_id: location.id }, order_lines_params: order_lines_params).call

      expect(multiple_creator).to be_success
      expect(multiple_creator.location).to eq(location)
      expect(multiple_creator.order).to eq(other_order)
      created_order_lines = multiple_creator.created_order_lines
      expect(created_order_lines.size).to eq(3)
      expect(created_order_lines.map(&:order)).to include(other_order)
      expect(created_order_lines.map(&:location)).to include(location)
    end
  end

  context 'OrderLine Upsert' do
    let!(:order_line_upserter) { double(OrderLines::Upsert) }

    before do
      # mock order_line upsert
      allow(OrderLines::Upsert).to receive(:new).and_return(order_line_upserter)
      order_line_upsert_response = OpenStruct.new(success?: true, order_line: 'order-line')
      allow(order_line_upserter).to receive(:call).and_return(order_line_upsert_response)
    end

    it 'makes a request to upsert each of the order lines' do
      expect(OrderLines::Upsert).to receive(:new).with(order: order, customer: order.customer_profile, location: anything, order_line_params: order_line_params1)
      expect(OrderLines::Upsert).to receive(:new).with(order: order, customer: order.customer_profile, location: anything, order_line_params: order_line_params2)
      expect(OrderLines::Upsert).to receive(:new).with(order: order, customer: order.customer_profile, location: anything, order_line_params: order_line_params3)

      multiple_creator = OrderLines::CreateMultiple.new(order: order, order_lines_params: order_lines_params).call
      expect(multiple_creator).to be_success
    end

    it 'errors if upsert order line fails/errors' do
      order_line_upsert_failed_response = OpenStruct.new(success?: false, errors: ['Order Line Upsert error'])
      allow(order_line_upserter).to receive(:call).and_return(order_line_upsert_failed_response)

      multiple_creator = OrderLines::CreateMultiple.new(order: order, order_lines_params: order_lines_params).call
      expect(multiple_creator).to_not be_success
      expect(multiple_creator.errors).to eq(['Order Line Upsert error', 'Order Line Upsert error', 'Order Line Upsert error']) # error for each of the 3 order lines
    end
  end

  context 'errors' do
    it 'errors if order is missing' do
      multiple_creator = OrderLines::CreateMultiple.new(order: nil).call

      expect(multiple_creator).to_not be_success
      expect(multiple_creator.errors).to include('Cannot create order lines without an order')
    end

    it 'errors if location is missing - invalid id' do
      multiple_creator = OrderLines::CreateMultiple.new(order: order, order_params: { location_id: rand(1..123).to_i }).call

      expect(multiple_creator).to_not be_success
      expect(multiple_creator.errors).to include('Cannot create order lines without a location')
    end
  end
end
