require 'rails_helper'

RSpec.describe OrderLines::List, type: :service, order_lines: true, orders: true do

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:supplier3) { create(:supplier_profile, :random) }

  let!(:order1) { create(:order, :random) }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
  let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier2) }

  let!(:order2) { create(:order, :random) }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier3) }

  let!(:order3) { create(:order, :random) }
  let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier3) }
  let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }

  it 'lists all order lines with no options' do
    lister_options = {}
    order_lines = OrderLines::List.new(options: lister_options).call

    expect(order_lines).to include(order_line11, order_line12, order_line21, order_line22, order_line31, order_line32)
  end

  it 'lists order lines for a given order' do
    lister_options = { order: order2 }
    order_lines = OrderLines::List.new(options: lister_options).call

    expect(order_lines).to include(order_line21, order_line22)
    expect(order_lines).to_not include(order_line11, order_line12, order_line31, order_line32)
  end

  it 'lists order lines for a given set if orders' do
    lister_options = { orders: [order2, order3] }
    order_lines = OrderLines::List.new(options: lister_options).call

    expect(order_lines).to include(order_line21, order_line22, order_line31, order_line32)
    expect(order_lines).to_not include(order_line11, order_line12)
  end

  it 'lists order lines for a given supplier' do
    lister_options = { supplier: supplier3 }
    order_lines = OrderLines::List.new(options: lister_options).call

    expect(order_lines).to include(order_line22, order_line31)
    expect(order_lines).to_not include(order_line11, order_line12, order_line21, order_line32)
  end

  it 'lists order lines for a given set of suppliers' do
    lister_options = { suppliers: [supplier1, supplier3] }
    order_lines = OrderLines::List.new(options: lister_options).call

    expect(order_lines).to include(order_line11, order_line22, order_line31, order_line32)
    expect(order_lines).to_not include(order_line12, order_line21)
  end

  context 'Filter by GST config' do
    before do
      [order_line11, order_line12, order_line32].each do |order_line|
        order_line.update_column(:is_gst_free, true)
      end

      [order_line21, order_line22, order_line31].each do |order_line|
        order_line.update_column(:is_gst_free, false)
      end
    end

    it 'filter order lines that are GST free' do
      lister_options = { gst_split: 'GST-FREE' }

      order_lines = OrderLines::List.new(options: lister_options).call
      expect(order_lines).to include(order_line11, order_line12, order_line32)
      expect(order_lines).to_not include(order_line21, order_line22, order_line31)
    end

    it 'filter order lines that have a GST' do
      lister_options = { gst_split: 'GST' }

      order_lines = OrderLines::List.new(options: lister_options).call
      expect(order_lines).to include(order_line21, order_line22, order_line31)
      expect(order_lines).to_not include(order_line11, order_line12, order_line32)
    end

    it 'does not filter order lines by any GST config if gst_split if blank' do
      lister_options = [{ gst_split: [nil, ''].sample }, {}].sample

      order_lines = OrderLines::List.new(options: lister_options).call
      expect(order_lines).to include(order_line11, order_line12, order_line21, order_line22, order_line31, order_line32)
    end
  end

  context 'team order', team_orders: true do
    let!(:team_order_attendee1) { create(:team_order_attendee, :random, order: order1, status: 'invited') }
    let!(:team_order_attendee2) { create(:team_order_attendee, :random, order: order1, status: 'pending') }
    let!(:team_order_attendee3) { create(:team_order_attendee, :random, order: order1, status: 'ordered') }

    before :each do
      [order1, order3].each do |order|
        order.update_column(:order_variant, %w[team_order recurring_team_order].sample)
      end
      order_line11.update_column(:attendee_id, team_order_attendee1.id)
      order_line21.update_column(:attendee_id, team_order_attendee2.id)
      order_line31.update_column(:attendee_id, team_order_attendee3.id)

      order_line32.update_column(:payment_status, 'paid')
    end

    it 'returns the order lines for the specified team order attendee' do
      lister_options = { for_attendee: team_order_attendee2 }
      order_lines = OrderLines::List.new(options: lister_options).call

      expect(order_lines).to include(order_line21)
      expect(order_lines).to_not include(order_line11, order_line12, order_line22, order_line31, order_line32)
    end

    it 'returns admin only order lines' do
      lister_options = { for_attendee: nil } # team order order lines with attendee id = nil are admin only order lines
      order_lines = OrderLines::List.new(options: lister_options).call

      expect(order_lines).to include(order_line12, order_line22, order_line32)
      expect(order_lines).to_not include(order_line11, order_line21, order_line31)
    end

    it 'returns order lines of only confirmed attendees and the ones without any attendees' do
      lister_options = { orders: [order1, order2, order3], confirmed_attendees_only: true }
      order_lines = OrderLines::List.new(options: lister_options).call

      expect(order_lines).to include(order_line12, order_line22, order_line31, order_line32)
      expect(order_lines).to_not include(order_line11, order_line21)
    end

    it 'returns order lines of anonymous attendees also when requesting or confirmed_attendees_only' do
      team_order_attendee3.update_column(:anonymous, true)
      lister_options = { orders: [order1, order2, order3], confirmed_attendees_only: true }
      order_lines = OrderLines::List.new(options: lister_options).call

      expect(order_lines).to include(order_line12, order_line22, order_line31, order_line32)
      expect(order_lines).to_not include(order_line11, order_line21)
    end

    it 'returns order line which are paid' do
      lister_options = { paid_only: true }
      order_lines = OrderLines::List.new(options: lister_options).call

      expect(order_lines).to include(order_line32)
      expect(order_lines).to_not include(order_line11, order_line12, order_line21, order_line22, order_line31)
    end
  end

end
