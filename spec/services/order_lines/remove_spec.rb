require 'rails_helper'

RSpec.describe OrderLines::Remove, type: :service, order_lines: true do

  let(:order) { create(:order, :draft) }
  let(:location) { create(:location, :random, order: order) }
  let!(:order_line) { create(:order_line, :random, location: location, order: order) }
  let!(:order_line2) { create(:order_line, :random, location: location, order: order) }

  it 'destroys the order line' do
    order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call

    expect(order_line_remover).to be_success
    expect{ order_line.reload }.to raise_error(ActiveRecord::RecordNotFound)
  end

  it 'resets the status of the team order attendee if no attendee order lines are present', team_orders: true do
    team_order_attendee = create(:team_order_attendee, :random, order: order, status: 'pending') # marked as pending because of order line
    order_line.update_column(:attendee_id, team_order_attendee.id)

    team_order_attendee2 = create(:team_order_attendee, :random, order: order, status: 'pending')
    create(:order_line, :random, location: location, order: order, attendee_id: team_order_attendee2.id) # create order line for team_order_attendee2

    order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call

    expect(order_line_remover).to be_success
    expect(team_order_attendee.reload.status).to eq('invited')
  end

  it 'does not resets the status of the team order attendee if attendee order lines are present', team_orders: true do
    team_order_attendee = create(:team_order_attendee, :random, order: order, status: 'pending') # marked as pending because of order line
    order_line.update_column(:attendee_id, team_order_attendee.id)
    create(:order_line, :random, location: location, order: order, attendee_id: team_order_attendee.id) # create order line for team_order_attendee

    order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call

    expect(order_line_remover).to be_success
    expect(team_order_attendee.reload.status).to_not eq('invited')
  end

  it 'errors out if location isn\'t found' do
    order_line_remover = OrderLines::Remove.new(location: nil, order_line_params: { id: order_line.id }).call

    expect(order_line_remover).to_not be_success
    expect(order_line_remover.errors).to include('Cannot remove an order line without a location')
  end

  # doesn't accidentally remove location for a different order
  it 'errors out if order line is not found' do
    order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: nil }).call

    expect(order_line_remover).to_not be_success
    expect(order_line_remover.errors).to include('Order line could not be found')
  end

  it 'lets you remove the all order lines from a draft/pending order' do
    order_line1_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call
    expect(order_line1_remover).to be_success
    expect{ order_line.reload }.to raise_error(ActiveRecord::RecordNotFound)

    order_line2_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line2.id }).call
    expect(order_line2_remover).to be_success
    expect{ order_line2.reload }.to raise_error(ActiveRecord::RecordNotFound)
  end

  context 'for a woolworths product', woolworths: true do
    let(:woolworths_supplier) { create(:supplier_profile, :random, commission_rate: 0.0) }
    let!(:woolworths_order_line) { create(:order_line, :random, order: order, location: location, supplier_profile: woolworths_supplier) }

    before do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(woolworths_supplier.id)
      order_line_processor = double(Woolworths::ProcessOrderLine)
      allow(Woolworths::ProcessOrderLine).to receive(:new).and_return(order_line_processor)
      allow(order_line_processor).to receive(:call).and_return(true)
    end

    it 'destroys the order line and syncs with woolworths' do
      expect(Woolworths::ProcessOrderLine).to receive(:new).with(order_line: woolworths_order_line)

      order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: woolworths_order_line.id }).call
      expect(order_line_remover).to be_success
      expect{ woolworths_order_line.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end

  context 'status update' do
    it 'doesn\'t let you remove the last order line from a non-draft/pending order' do
      order.update_column(:status, %w[new amended confirmed quoted].sample)
      order_line1_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call
      expect(order_line1_remover).to be_success
      expect{ order_line.reload }.to raise_error(ActiveRecord::RecordNotFound) # allows 2nd to last orderline to be deleted

      order_line2_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line2.id }).call
      expect(order_line2_remover).to_not be_success
      expect(order_line2_remover.errors).to include('Cannot remove the last item within a submitted order')
      expect{ order_line2.reload }.to_not raise_error(ActiveRecord::RecordNotFound)
    end

    it 'sets the order status to amended if order line is removed from a non-draft order' do
      order.update_column(:status, 'confirmed')
      order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call

      expect(order_line_remover).to be_success
      expect(order_line_remover.order_line.order.status).to eq('amended')
    end

    it 'updates the order\'s version ref(^erence) if order line is removed from a non-draft order (even if status is amended)' do
      saved_version_ref = SecureRandom.hex(7)
      order.update_column(:status, 'amended')
      order.update_column(:version_ref, saved_version_ref)

      order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call
      expect(order_line_remover).to be_success

      order.reload

      updated_order = order_line_remover.order_line.order
      expect(updated_order.version_ref).to eq(order.current_version_ref)
      expect(updated_order.version_ref).to_not eq(saved_version_ref)
    end

    it 'does not update the status for removal of an order line within a draft/pending/quoted order' do
      draft_status = %w[draft pending quoted].sample
      order.update_column(:status, draft_status)
      order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call

      expect(order_line_remover).to be_success
      expect(order_line_remover.order_line.order.status).to eq(draft_status)
    end

    context 'for a team order atteendee order line', team_orders: true do
      it 'does not update the status for the order, if removed from a pending team order' do
        order.update_column(:status, 'pending')
        team_order_attendee = create(:team_order_attendee, :random, order: order, status: 'ordered')
        order_line.update_column(:attendee_id, team_order_attendee.id)
        order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call

        expect(order_line_remover).to be_success
        expect(order_line_remover.order_line.order.status).to_not eq('amended')
      end

      it 'sets the order status as amended if removed from a non-pending team order' do
        order.update_column(:status, %w[new confirmed].sample)
        team_order_attendee = create(:team_order_attendee, :random, order: order, status: 'ordered')
        order_line.update_column(:attendee_id, team_order_attendee.id)
        order_line_remover = OrderLines::Remove.new(location: location, order_line_params: { id: order_line.id }).call

        expect(order_line_remover).to be_success
        expect(order_line_remover.order_line.order.status).to eq('amended')
      end
    end
  end

end
