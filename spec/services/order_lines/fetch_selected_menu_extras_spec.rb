require 'rails_helper'

RSpec.describe OrderLines::FetchSelectedMenuExtras, type: :service, order_lines: true, menu_extras: true do

  let!(:menu_item) { create(:menu_item, :random) }

  let!(:menu_extra_section1) { create(:menu_extra_section, :random, menu_item: menu_item, weight: 10) }
  let!(:menu_extra11) { create(:menu_extra, :random, menu_extra_section: menu_extra_section1) }
  let!(:menu_extra12) { create(:menu_extra, :random, menu_extra_section: menu_extra_section1) }

  let!(:menu_extra_section2) { create(:menu_extra_section, :random, menu_item: menu_item, weight: 20) }
  let!(:menu_extra2) { create(:menu_extra, :random, menu_extra_section: menu_extra_section2) }

  let!(:menu_extra_section3) { create(:menu_extra_section, :random, menu_item: menu_item, weight: 30) }
  let!(:menu_extra3) { create(:menu_extra, :random, menu_extra_section: menu_extra_section3) }

  let!(:menu_extra_section4) { create(:menu_extra_section, :random, menu_item: menu_item, weight: 40) }
  let!(:menu_extra4) { create(:menu_extra, :random, menu_extra_section: menu_extra_section4) }

  let!(:order_line) { create(:order_line, :random, menu_item: menu_item, selected_menu_extras: [menu_extra11, menu_extra12, menu_extra2, menu_extra3, menu_extra4].map(&:id)) }

  it 'returns selected menu sections grouped by the items section' do
    grouped_menu_extras = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call.to_h

    expect(grouped_menu_extras.keys.map(&:name)).to include(menu_extra_section1.name, menu_extra_section2.name, menu_extra_section3.name, menu_extra_section4.name)
    expect(grouped_menu_extras.values.flatten).to include(menu_extra11, menu_extra12, menu_extra2, menu_extra3, menu_extra4)

    section1_extras = grouped_menu_extras.detect{|menu_extra_section, _| menu_extra_section.name == menu_extra_section1.name }
    expect(section1_extras[1]).to include(menu_extra11, menu_extra12)

    section2_extras = grouped_menu_extras.detect{|menu_extra_section, _| menu_extra_section.name == menu_extra_section2.name }
    expect(section2_extras[1]).to include(menu_extra2)

    section3_extras = grouped_menu_extras.detect{|menu_extra_section, _| menu_extra_section.name == menu_extra_section3.name }
    expect(section3_extras[1]).to include(menu_extra3)

    section4_extras = grouped_menu_extras.detect{|menu_extra_section, _| menu_extra_section.name == menu_extra_section4.name }
    expect(section4_extras[1]).to include(menu_extra4)
  end

  it 'sorts the selected menu section by section weight' do
    grouped_menu_extras = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call.to_h

    expect(grouped_menu_extras.keys.map(&:weight)).to eq([menu_extra_section1, menu_extra_section2, menu_extra_section3, menu_extra_section4].map(&:weight))
  end

  it 'return blank if order_line does not have any selected_menu_extras' do
    order_line.update_column(:selected_menu_extras, nil)

    grouped_menu_extras = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call.to_h

    expect(grouped_menu_extras).to be_blank
  end

  context 'sanitzed section names' do
    let!(:section_names_with_sanity) do
      [
        {
          name: 'Choose your sides',
          sanitized: 'sides',
        },
        {
          name: 'Choose meats',
          sanitized: 'meats',
        },
        {
          name: 'Add meals',
          sanitized: 'meals',
        },
        {
          name: 'Select dips',
          sanitized: 'dips',
        },
        {
          name: 'drink of your choice',
          sanitized: 'drink',
        },
        {
          name: 'Your burgers',
          sanitized: 'burgers',
        },
        {
          name: 'Choose 2 salads from the below options',
          sanitized: '2 salads',
        },
        {
          name: '5 sauce options',
          sanitized: '5 sauce',
        },
        {
          name: 'Meal options',
          sanitized: 'Meal',
        },
        {
          name: 'Amount of dips',
          sanitized: 'dips',
        },
        {
          name: 'Indicate type of salad',
          sanitized: 'type of salad',
        },
        {
          name: 'BBQ choice',
          sanitized: 'BBQ',
        }
      ]
    end

    it 'sanitizes the names of the section' do
      6.times do # covers almost all names in the above list
        section1_name = section_names_with_sanity.sample
        menu_extra_section1.update_column(:name, section1_name[:name])

        section2_name = section_names_with_sanity.sample
        menu_extra_section2.update_column(:name, section2_name[:name])

        section3_name = section_names_with_sanity.sample
        menu_extra_section3.update_column(:name, section3_name[:name])

        section4_name = section_names_with_sanity.sample
        menu_extra_section4.update_column(:name, section4_name[:name])

        grouped_menu_extras = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call.to_h
        expect(grouped_menu_extras.keys.map(&:name)).to include(section1_name[:sanitized], section2_name[:sanitized], section3_name[:sanitized], section4_name[:sanitized])
      end
    end

    it 'groups the menu extra within `extras` if the section name is missing' do
      menu_extra_section4.update_column(:name, [nil, ''].sample)

      grouped_menu_extras = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call.to_h

      extras_section = grouped_menu_extras.detect{|menu_extra_section, _| menu_extra_section.name == 'extras' }
      expect(extras_section[1]).to include(menu_extra4)
    end
  end
end
