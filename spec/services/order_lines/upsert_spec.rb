require 'rails_helper'

RSpec.describe OrderLines::Upsert, type: :service, order_lines: true do

  let(:supplier) { create(:supplier_profile, :random, commission_rate: 10, markup: 12) }
  let!(:customer) { create(:customer_profile, :random) }
  let(:order) { create(:order, :confirmed, customer_profile: customer) }
  let(:location) { create(:location, :random, order: order) }
  let(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }

  context 'creating a new order line' do
    it 'creates a new order line' do
      order_line_params = { item_id: menu_item.id, quantity: 20, price: 2.2 }
      order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

      expect(order_line_creator).to be_success
      created_order_line = order_line_creator.order_line
      expect(created_order_line).to be_present
      expect(created_order_line).to be_persisted
      expect(created_order_line.order).to eq(order)

      expect(order.reload.order_lines).to include(created_order_line)
    end

    context 'ATTRIBUTE(s) ALLOCATION' do
      it 'sets the menu item as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20 }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.menu_item).to eq(menu_item)
      end

      it 'sets the location as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20 }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.location).to eq(location)
      end

      it 'sets the order as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20 }
        order_line_creator = OrderLines::Upsert.new(order: order, location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.order).to eq(order)
        expect(order_line_creator.order_line.location).to eq(location)
      end

      it 'sets the order (if not passed) as per specified location' do
        order_line_params = { item_id: menu_item.id, quantity: 20 }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.order).to eq(location.order)
      end

      it 'sets the supplier profile as per specified menu item\'s supplier profile' do
        order_line_params = { item_id: menu_item.id, quantity: 20 }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.supplier_profile).to eq(menu_item.supplier_profile)
      end

      it 'sets the quantity as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20 }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.quantity).to eq(20)
      end

      it 'sets the note as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20, note: 'BLAH BLAH' }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.note).to eq('BLAH BLAH')
      end

      context 'with categories' do
        let!(:catering_category) { create(:category, :random, weight: 20) }
        let!(:catering_category2) { create(:category, :random, weight: 10) }
        let!(:home_delivery_category) { create(:category, :random, group: 'home-deliveries', weight: 30) }
        let!(:menu_section) { create(:menu_section, :random, categories: [catering_category, catering_category2, home_delivery_category]) }

        before do
          menu_item.update_column(:menu_section_id, menu_section.id)
        end

        context 'for normal orders' do
          it 'sets the first non-home_delivery category (by weight) for the selected menu item' do
            order_line_params = { item_id: menu_item.id, quantity: 10 }
            order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

            expect(order_line_creator).to be_success
            expect(order_line_creator.order_line.category).to eq(catering_category2)
          end
        end

        context 'for a custom order' do
          let!(:generic_category) { create(:category, :random, is_generic: true) }

          before do
            order.update_columns(order_variant: 'event_order', major_category_id: generic_category.id)
            menu_section.update(categories: []) # custom order menu sections generally do not have any categories
          end

          it 'saves the order line category as the custom orders major category' do
            order_line_params = { item_id: menu_item.id, quantity: 10 }
            order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

            expect(order_line_creator).to be_success
            expect(order_line_creator.order_line.category).to eq(generic_category)
          end
        end

        context 'for home delivery orders' do
          before do
            order.update_column(:order_variant, 'home_delivery')
          end

          it 'set the category as the first home delivery category' do
            order_line_params = { item_id: menu_item.id, quantity: 10 }
            order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

            expect(order_line_creator).to be_success
            expect(order_line_creator.order_line.category).to eq(home_delivery_category)
          end

          it 'sets the first known category for a home delivery order if no home-delivery categories exists' do
            home_delivery_category.update_columns(group: 'catering-services', weight: 10)
            order_line_params = { item_id: menu_item.id, quantity: 10 }
            order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

            expect(order_line_creator).to be_success
            expect(order_line_creator.order_line.category).to eq(catering_category2)
          end
        end
      end

      it 'sets is gst free as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20, is_gst_free: true }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.is_gst_free).to be_truthy
      end

      it 'sets is gst inc as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20, is_gst_inc: true }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.is_gst_inc).to be_truthy
      end

      it 'sets the price as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20, price: '2.2' }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.price).to eq(2.2)
      end

      it 'sets the cost as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20, cost: '2.3' }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.cost).to eq(2.3)
      end

      it 'sets the baseline (cost) as specified' do
        order_line_params = { item_id: menu_item.id, quantity: 20, baseline: '2.4' }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success
        expect(order_line_creator.order_line.baseline).to eq(2.4)
      end

      context 'for a team order atteendee order line', team_orders: true do
        let(:team_order_attendee) { create(:team_order_attendee, :random, order: order, status: 'invited') }

        it 'sets the (team_order) attendee as specified' do
          order_line_params = { item_id: menu_item.id, quantity: 20, attendee_id: team_order_attendee.id }
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.team_order_attendee).to eq(team_order_attendee)
        end

        it 'sets the team order attendee\'s status as pending' do
          order_line_params = { item_id: menu_item.id, quantity: 20, attendee_id: team_order_attendee.id }
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(team_order_attendee.reload.status).to eq('pending')
        end

        it 'does not update the status of the team order attendee if its a consequence of an item update' do
          team_order_attendee.update_column(:status, 'ordered')
          order_line_params = { item_id: menu_item.id, quantity: 20, attendee_id: team_order_attendee.id }
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params, update_item: true).call

          expect(order_line_creator).to be_success
          expect(team_order_attendee.reload.status).to_not eq('pending')
          expect(team_order_attendee.reload.status).to eq('ordered')
        end
      end

      context 'status update' do
        it 'sets the order line status to be amended for a non-draft order' do
          order_line_params = { item_id: menu_item.id, quantity: 20 }
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
          expect(order_line_creator).to be_success

          expect(order_line_creator.order_line.status).to eq('amended')
        end

        it 'sets the order status to amended if order line is amended in a non-draft order' do
          order_line_params = { item_id: menu_item.id, quantity: 20 }
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
          expect(order_line_creator).to be_success

          expect(order_line_creator.order_line.order.status).to eq('amended')
        end

        it 'updates the version ref(^erence) of the order (even when order status is amended)' do
          saved_version_ref = SecureRandom.hex(7)
          order.update_column(:status, 'amended')
          order.update_column(:version_ref, saved_version_ref)

          order_line_params = { item_id: menu_item.id, quantity: 20 }
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
          expect(order_line_creator).to be_success

          order.reload
          updated_order = order_line_creator.order_line.order

          expect(updated_order.version_ref).to eq(order.current_version_ref)
          expect(updated_order.version_ref).to_not eq(saved_version_ref)
        end

        context 'within a draft/pending/quoted orders' do
          let!(:order_status) { %w[draft pending quoted].sample }

          before do
            order.update_column(:status, order_status)
          end

          it 'sets the order line status to be pending (default)' do
            order_line_params = { item_id: menu_item.id, quantity: 20 }
            order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

            expect(order_line_creator.order_line.status).to eq('pending')
          end

          it 'does not update the status of the order' do
            order_line_params = { item_id: menu_item.id, quantity: 20 }
            order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

            expect(order_line_creator.order_line.order.status).to eq(order_status)
          end
        end

        it 'does not update the status for an order line if its a consequence of an item update' do
          order.update_column(:status, 'confirmed')
          order_line_params = { item_id: menu_item.id, quantity: 20 }
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params, update_item: true).call

          expect(order_line_creator.order_line.order.status).to eq('confirmed')
        end

        context 'for a team order atteendee order line', team_orders: true do
          let(:team_order_attendee) { create(:team_order_attendee, :random, order: order, status: 'invited') }

          it 'does not update the order/orderline status if upserted within a pending order' do
            order.update_column(:status, 'pending')
            order_line_params = { item_id: menu_item.id, quantity: 20, attendee_id: team_order_attendee.id }
            order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

            expect(order_line_creator).to be_success
            expect(order_line_creator.order_line.status).to_not eq('amended')
            expect(order_line_creator.order_line.order.status).to_not eq('amended')
          end

          it 'sets the orderline and order status as amended if upserted within a non-pending order' do
            order.update_column(:status, %w[new confirmed].sample)
            order_line_params = { item_id: menu_item.id, quantity: 20, attendee_id: team_order_attendee.id }
            order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

            expect(order_line_creator).to be_success

            expect(order_line_creator.order_line.status).to eq('amended')
            expect(order_line_creator.order_line.order.status).to eq('amended')
          end
        end
      end

      context 'order_line for a simple menu item' do
        let(:order_line_params) { { item_id: menu_item.id, quantity: 20 } }

        it 'sets is gst free as per menu item' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.is_gst_free).to eq(menu_item.is_gst_free)
        end

        it 'sets the name as menu item name' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.name).to eq(menu_item.name)
        end

        it 'sets the price as per the menu items price (inc markup)' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expected_price = menu_item.price.to_f * (1 + (supplier.markup / 100))
          expect(order_line_creator.order_line.price).to eq(expected_price.round(2))
        end

        it 'sets the cost as per the menu item price (minus markdown)' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expected_cost = menu_item.price.to_f * (1 - (supplier.commission_rate / 100))
          expect(order_line_creator.order_line.cost).to eq(expected_cost.round(2))
        end

        it 'sets the baseline (cost) as per the menu item price' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.baseline.round(2)).to eq(menu_item.price.to_f.round(2))
        end
      end

      context 'order_line for a menu_item\'s serving size' do
        let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
        let(:order_line_params) { { item_id: menu_item.id, serving_size_id: serving_size.id, quantity: 20 } }

        it 'sets the serving size for the order_line' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.serving_size).to eq(serving_size)
        end

        it 'adds the serving size name to the order_line name' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.name).to include("- #{serving_size.name}")
        end

        it 'sets the price as per the serving_size price (inc markup)' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expected_price = serving_size.price.to_f * (1 + (supplier.markup / 100))
          expect(order_line_creator.order_line.price.round(2)).to eq(expected_price.round(2))
        end

        it 'sets the cost as per the serving_size price (minus commission)' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expected_cost = serving_size.price.to_f * (1 - (supplier.commission_rate / 100))
          expect(order_line_creator.order_line.cost.round(2)).to eq(expected_cost.round(2))
        end

        it 'sets the baseline (cost) as per the serving_size price' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.baseline.round(2)).to eq(serving_size.price.to_f.round(2))
        end
      end

      context 'order_line for a menu_item with extras', menu_extras: true do
        let!(:menu_extra_section) { create(:menu_extra_section, :random, menu_item: menu_item) }
        let(:menu_extra) { create(:menu_extra, :random, menu_extra_section: menu_extra_section) }
        let(:order_line_params) { { item_id: menu_item.id, selected_menu_extra_ids: [menu_extra.id], quantity: 20 } }

        it 'sets the serving size for the order_line' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.selected_menu_extras).to match_array([menu_extra.id])
        end

        it 'adds (with extras) to the order_line name if selected extras' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.name).to include('(with extras)')
        end

        it 'sets the price as per the menu item price and the extras (inc markup)' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expected_price = (menu_item.price.to_f + menu_extra.price.to_f) * (1 + (supplier.markup / 100))
          expect(order_line_creator.order_line.price).to eq(expected_price.round(2))
        end

        it 'sets the cost as per the menu item price and the extras (minus commission)' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expected_price = menu_item.price.to_f + menu_extra.price.to_f
          expected_cost = expected_price * (1 - (supplier.commission_rate / 100))
          expect(order_line_creator.order_line.cost).to eq(expected_cost.round(2))
        end

        it 'sets the baseline (cost) as per the menu item price and the extras (minus commission)' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expected_cost = menu_item.price.to_f + menu_extra.price.to_f
          expect(order_line_creator.order_line.baseline.round(2).to_s).to eq(expected_cost.round(2).to_s)
        end
      end

      context 'order_line with a menu_item/serving_size with attached rate card' do
        let(:company) { create(:company, :random) }
        let!(:menu_item_rate_card) { create(:rate_card, :random, menu_item: menu_item, company: company) }
        let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
        let!(:serving_size_rate_card) { create(:rate_card, :random, serving_size: serving_size, company: company) }
        let(:order_line_params) { { item_id: menu_item.id, quantity: 20 } }

        before do
          customer.update_column(:company_id, company.id)
        end

        it 'sets the price and cost as per the menu item\'s rate card price (exc markup)' do
          order_line_creator = OrderLines::Upsert.new(order: order, location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.price).to eq(menu_item_rate_card.price)
          expect(order_line_creator.order_line.cost).to eq(menu_item_rate_card.cost)
        end

        it 'sets the price and cost as per the serving size\'s rate card price (exc commission)' do
          params = order_line_params.merge({ serving_size_id: serving_size.id })
          order_line_creator = OrderLines::Upsert.new(order: order, location: location, order_line_params: params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.price).to eq(serving_size_rate_card.price)
          expect(order_line_creator.order_line.cost).to eq(serving_size_rate_card.cost)
        end

        it 'sets the baseline (cost) as per the rate card price (exc markup)' do
          order_line_creator = OrderLines::Upsert.new(order: order, location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          expect(order_line_creator.order_line.price).to eq(menu_item_rate_card.price)
          expect(order_line_creator.order_line.cost).to eq(menu_item_rate_card.cost)
        end
      end
    end

    context 'ERRORS' do
      it 'errors if location is not passed' do
        order_line_params = { item_id: menu_item.id, quantity: 20, price: 2.2 }
        order_line_creator = OrderLines::Upsert.new(location: nil, order_line_params: order_line_params).call

        expect(order_line_creator).to_not be_success
        expect(order_line_creator.errors).to include('Cannot create an order line without a location')
      end

      it 'errors if quantity is missing' do
        order_line_params = { item_id: menu_item.id, price: 2.2 }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to_not be_success
        expect(order_line_creator.errors).to include('Quantity can\'t be blank')
      end

      it 'errors if menu item is missing' do
        skip 'Need to re-evaluate the model validations'
        order_line_params = { quantity: 20, price: 2.2 }
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to_not be_success
        expect(order_line_creator.errors).to be_present
      end
    end
  end

  context 'updating an existing order line' do
    context 'when updating quantity of an existing item (order_line id passed)' do
      let!(:order_line) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5) }
      let(:order_line_params) { { id: order_line.id, item_id: menu_item.id, quantity: 20 } }

      it 'updates the quantity of the order' do
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success

        updated_order_line = order_line_creator.order_line
        expect(updated_order_line.id).to eq(order_line.id) # updates existing record
        expect(updated_order_line.quantity).to eq(20)
      end

      it 'does not call methods to recalculate price and cost' do
        expect(OrderLines::CalculatePrice).to_not receive(:new)
        expect(OrderLines::CalculateCost).to_not receive(:new)

        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
        expect(order_line_creator).to be_success
      end

      it 'removes any existing order line errors' do
        order_line.update_column(:last_errors, ['Item un-availability error'])
        order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

        expect(order_line_creator).to be_success

        updated_order_line = order_line_creator.order_line
        expect(updated_order_line.id).to eq(order_line.id) # updates existing record
        expect(updated_order_line.last_errors).to be_empty
      end
    end

    context 'when another order line of the same flavour is requested to be added (order_line id not passed)' do
      context 'with and existing order line of a menu item' do
        let!(:order_line) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5) }
        let(:order_line_params) { { item_id: menu_item.id, quantity: 20 } }

        it 'adds to the quantity of existing record' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          created_order_line = order_line_creator.order_line
          expect(created_order_line.id).to eq(order_line.id) # updates existing record
          expect(created_order_line.menu_item).to eq(order_line.menu_item)
          expect(created_order_line.quantity).to eq(25)
        end

        it 'creates a new order line for a different location but same serving size' do
          location2 = create(:location, :random, order: order)
          order_line_creator = OrderLines::Upsert.new(location: location2, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          created_order_line = order_line_creator.order_line
          expect(created_order_line.id).to_not eq(order_line.id)
          expect(created_order_line.location).to eq(location2)
          expect(created_order_line.menu_item).to eq(order_line.menu_item)
          expect(created_order_line.quantity).to eq(20)
        end
      end

      context 'with an exiting order line of a serving size' do
        let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
        let!(:order_line_no_serving_size) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5) }
        let!(:order_line) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, serving_size: serving_size, supplier_profile: supplier, quantity: 5) }
        let(:order_line_params) { { item_id: menu_item.id, serving_size_id: serving_size.id, quantity: 20 } }

        it 'adds to the quantity of existing order line with serving size' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          created_order_line = order_line_creator.order_line
          expect(created_order_line.id).to eq(order_line.id) # updates existing record
          expect(created_order_line.location).to eq(location)
          expect(created_order_line.menu_item).to eq(order_line.menu_item)
          expect(created_order_line.serving_size).to eq(order_line.serving_size)
          expect(created_order_line.quantity).to eq(25)
        end

        it 'creates a new order line for a different location but same serving size' do
          location2 = create(:location, :random, order: order)
          order_line_creator = OrderLines::Upsert.new(location: location2, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          created_order_line = order_line_creator.order_line
          expect(created_order_line.id).to_not eq(order_line.id)
          expect(created_order_line.location).to eq(location2)
          expect(created_order_line.menu_item).to eq(order_line.menu_item)
          expect(created_order_line.serving_size).to eq(order_line.serving_size)
          expect(created_order_line.quantity).to eq(20)
        end

        it 'creates a new order for the same menu item but a different serving size' do
          serving_size2 = create(:serving_size, :random, menu_item: menu_item)
          order_line_params = { item_id: menu_item.id, serving_size_id: serving_size2.id, quantity: 20 }
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          created_order_line = order_line_creator.order_line
          expect(created_order_line.id).to_not eq(order_line.id)
          expect(created_order_line.menu_item).to eq(order_line.menu_item)
          expect(created_order_line.serving_size).to eq(serving_size2)
          expect(created_order_line.quantity).to eq(20)
        end
      end

      context 'with an exiting order line of a menu item with menu extras', menu_extras: true do
        let!(:menu_extra_section) { create(:menu_extra_section, :random, menu_item: menu_item) }
        let(:menu_extra) { create(:menu_extra, :random, menu_extra_section: menu_extra_section) }
        let!(:order_line_no_extras) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5) }
        let!(:order_line) { create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: supplier, quantity: 5, selected_menu_extras: [menu_extra.id]) }
        let(:order_line_params) { { item_id: menu_item.id, selected_menu_extra_ids: [menu_extra.id], quantity: 20 } }

        it 'adds to the quantity of existing order line for a menu item with extras' do
          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          created_order_line = order_line_creator.order_line
          expect(created_order_line.id).to eq(order_line.id) # updates existing record
          expect(created_order_line.location).to eq(order_line.location)
          expect(created_order_line.menu_item).to eq(order_line.menu_item)
          expect(created_order_line.selected_menu_extras).to match_array(order_line.selected_menu_extras)
          expect(created_order_line.quantity).to eq(25)
        end

        it 'creates a new order line for a different location' do
          location2 = create(:location, :random, order: order)
          order_line_creator = OrderLines::Upsert.new(location: location2, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          created_order_line = order_line_creator.order_line
          expect(created_order_line.id).to_not eq(order_line.id)
          expect(created_order_line.location).to eq(location2)
          expect(created_order_line.menu_item).to eq(order_line.menu_item)
          expect(created_order_line.selected_menu_extras).to match_array([menu_extra.id])
          expect(created_order_line.quantity).to eq(20)
        end

        it 'creates a new order line if the menu extras are different' do
          menu_extra2 = create(:menu_extra, :random, menu_extra_section: menu_extra_section)
          order_line_params = { item_id: menu_item.id, selected_menu_extra_ids: [menu_extra.id, menu_extra2.id], quantity: 20 }

          order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call

          expect(order_line_creator).to be_success
          created_order_line = order_line_creator.order_line
          expect(created_order_line.id).to_not eq(order_line.id)
          expect(created_order_line.selected_menu_extras).to match_array([menu_extra.id, menu_extra2.id])
          expect(created_order_line.quantity).to eq(20)
        end

      end
    end
  end

  context 'woolworths order', woolworths: true do
    let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }
    let(:woolworths_supplier) { create(:supplier_profile, :random, commission_rate: 0.0, markup: 0.0) }
    let(:menu_item) { create(:menu_item, :random, supplier_profile: woolworths_supplier) }

    before do
      order.update_column(:status, 'draft')
      # stub Woolworths supplier
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(woolworths_supplier.id)

      # stubs for supplier creation
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

      order_line_syncer = double(Woolworths::Order::SyncTrolleyProduct)
      allow(Woolworths::Order::SyncTrolleyProduct).to receive(:new).and_return(order_line_syncer)
      allow(order_line_syncer).to receive(:call).and_return(true)

      order_line_processor = delayed_order_line_processor =  double(Woolworths::ProcessOrderLine)
      allow(Woolworths::ProcessOrderLine).to receive(:new).and_return(order_line_processor)
      allow(order_line_processor).to receive(:delay).and_return(delayed_order_line_processor)
      allow(delayed_order_line_processor).to receive(:call).and_return(true)
    end

    let!(:order_line_params) do
      {
        item_id: menu_item.id,
        quantity: 20,
        price: 2.2
      }
    end

    it 'creates a new order line and syncs with Woolworths' do
      expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new)
      expect(Woolworths::ProcessOrderLine).to receive(:new)

      order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
      expect(order_line_creator).to be_success

      created_order_line = order_line_creator.order_line
      expect(created_order_line).to be_present
      expect(created_order_line).to be_persisted
      expect(created_order_line.order).to eq(order)

      expect(order.reload.order_lines).to include(created_order_line)
    end

    it 'updates the quantity of the order and syncs with Woolworths' do
      order_line = create(:order_line, :random, order: order, location: location, menu_item: menu_item, supplier_profile: woolworths_supplier, quantity: 5)

      expect(Woolworths::Order::SyncTrolleyProduct).to receive(:new).with(order: order, order_line: order_line)
      expect(Woolworths::ProcessOrderLine).to receive(:new).with(order_line: order_line)

      order_line_params = { id: order_line.id, item_id: menu_item.id, quantity: 20 }
      order_line_updater = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
      expect(order_line_updater).to be_success

      updated_order_line = order_line_updater.order_line
      expect(updated_order_line.id).to eq(order_line.id) # updates existing record
      expect(updated_order_line.quantity).to eq(20)
    end

    it 'does not sync if order is not attached to a woolworths order' do
      woolworths_order.destroy
      order.reload

      expect(Woolworths::Order::SyncTrolleyProduct).to_not receive(:new)
      expect(Woolworths::ProcessOrderLine).to_not receive(:new)

      order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
      expect(order_line_creator).to be_success
    end

    it 'does not sync if order line does not belong to Woolworths' do
      menu_item.update_column(:supplier_profile_id, supplier.id) # non-woolworths supplier

      expect(Woolworths::Order::SyncTrolleyProduct).to_not receive(:new)
      expect(Woolworths::ProcessOrderLine).to_not receive(:new)

      order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
      expect(order_line_creator).to be_success
    end

    it 'does not sync with woolworths if the order is not in draft' do
      order.update_column(:status, %w[new amended confirmed delivered].sample)
      expect(Woolworths::Order::SyncTrolleyProduct).to_not receive(:new)
      expect(Woolworths::ProcessOrderLine).to_not receive(:new)

      order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
      expect(order_line_creator).to be_success
    end

    it 'does not sync with woolworths if the order is a custom order' do
      order.update_column(:order_variant, 'event_order')
      expect(Woolworths::Order::SyncTrolleyProduct).to_not receive(:new)
      expect(Woolworths::ProcessOrderLine).to_not receive(:new)

      order_line_creator = OrderLines::Upsert.new(location: location, order_line_params: order_line_params).call
      expect(order_line_creator).to be_success
    end
  end
end
