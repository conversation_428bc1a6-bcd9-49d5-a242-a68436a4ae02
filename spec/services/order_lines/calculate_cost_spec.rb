require 'rails_helper'

RSpec.describe OrderLines::CalculateCost, type: :service, order_lines: true do
  let(:supplier) { create(:supplier_profile, :random, markup: rand(1.1..20.1)) }
  let(:menu_item) { create(:menu_item, :random, supplier_profile: supplier, price: 20) }
  let(:menu_item_order_line) { build(:order_line, menu_item: menu_item, supplier_profile: supplier, quantity: 20) }

  let(:serving_size) { create(:serving_size, :random, menu_item: menu_item, price: 30) }
  let(:serving_size_order_line) { build(:order_line, menu_item: menu_item, serving_size: serving_size, supplier_profile: supplier, quantity: 20) }

  let!(:menu_extra_section) { create(:menu_extra_section, :random, menu_item: menu_item) }
  let(:menu_extra1) { create(:menu_extra, :random, menu_extra_section: menu_extra_section, price: 15) }
  let(:menu_extra2) { create(:menu_extra, :random, menu_extra_section: menu_extra_section, price: 10) }
  let(:order_line_with_extras) { build(:order_line, menu_item: menu_item, selected_menu_extras: [menu_extra1, menu_extra2].map(&:id), supplier_profile: supplier, quantity: 20) }

  let(:company) { create(:company, :random) }
  let(:customer) { create(:customer_profile, :random, company: company) }

  let!(:markup_override_fetcher) { double(Suppliers::FetchMarkupOverride) }

  before do
    # mock markup fetcher
    allow(Suppliers::FetchMarkupOverride).to receive(:new).and_return(markup_override_fetcher)
    allow(markup_override_fetcher).to receive(:call).and_return(nil)
  end

  context 'for a supplier with no commission (irrespective of markup)' do
    before do
      supplier.update_column(:commission_rate, 0)
    end

    it 'returns the cost based on the menu items price' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line).call

      expected_cost = menu_item.price.to_f
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(20)
    end

    it 'returns the cost based on the menu items promo price', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line).call

      expected_cost = menu_item.promo_price.to_f
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(15)
    end

    it 'returns the cost based on the serving size price' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: serving_size_order_line).call

      expected_cost = serving_size.price.to_f
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(30)
    end

    it 'returns the cost based on the menu item and its extras price' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: order_line_with_extras).call

      expected_cost = menu_item.price.to_f + menu_extra1.price.to_f + menu_extra2.price.to_f
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(45) # item(20) + extra(15) + extra(10)
    end

    it 'returns the cost based on the menu item\'s promo price and its extras price', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: order_line_with_extras).call

      expected_cost = menu_item.promo_price.to_f + menu_extra1.price.to_f + menu_extra2.price.to_f
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(40) # item(15) + extra(15) + extra(10)
    end
  end

  context 'for a supplier with commission' do
    before do
      supplier.update_column(:commission_rate, 10)
    end

    it 'returns the cost based on the menu items price minus commission' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line).call

      expected_cost = menu_item.price.to_f * (1 - (supplier.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(18) # item(20) - commission(2)
    end

    it 'returns the cost based on the menu items promo price minus commission', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line).call

      expected_cost = menu_item.promo_price.to_f * (1 - (supplier.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(13.5) # item(15) - commission(2)
    end

    it 'returns the cost based on the serving size price minus commission' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: serving_size_order_line).call

      expected_cost = serving_size.price.to_f * (1 - (supplier.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(27) # serving_size(30) - commission(3)
    end

    it 'returns the cost based on the menu item and its extras price minus commission' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: order_line_with_extras).call

      expected_cost = (menu_item.price.to_f + menu_extra1.price.to_f + menu_extra2.price.to_f) * (1 - (supplier.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(40.5) # item(20) + extra(15) + extra(10) - commission(4.5)
    end

    it 'returns the cost based on the menu item\'s promo price and its extras price minus commission', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: order_line_with_extras).call

      expected_cost = (menu_item.promo_price.to_f + menu_extra1.price.to_f + menu_extra2.price.to_f) * (1 - (supplier.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(36) # item(15) + extra(15) + extra(10) - commission(4)
    end
  end

  context 'with markup/commission override', markup_overrides: true do
    before do
      supplier.update_column(:commission_rate, rand(1.1..20.1))
    end
    let!(:markup_override) { create(:supplier_markup_override, :random, commission_rate: 20, supplier_profile: supplier, overridable: company) }

    before do
      allow(markup_override_fetcher).to receive(:call).and_return(markup_override)
    end

    it 'returns the cost based on the menu items price minus overridden commission' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line, company: company).call

      expected_cost = menu_item.price.to_f * (1 - (markup_override.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(16) # item(20) - commission(4)
    end

    context 'markup_override with no override value for `commission_rate`' do
      before do
        markup_override.update_column(:commission_rate, nil) # only symbolical as we mock the return of fetch markup override
        allow(markup_override_fetcher).to receive(:call).and_return(nil)
      end

      it 'returns the cost based on the menu item using the default supplier commission_rate' do
        calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line, company: company).call

        expected_cost = menu_item.price.to_f * (1 - (supplier.commission_rate / 100))
        expect(calculated_cost).to eq(expected_cost)
      end
    end

    it 'returns the cost based on the menu items promo price minus overridden commission', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line, company: company).call

      expected_cost = menu_item.promo_price.to_f * (1 - (markup_override.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(12) # item(15) - commission(3)
    end

    it 'returns the cost based on the serving size price minus overridden commission' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: serving_size_order_line, company: company).call

      expected_cost = serving_size.price.to_f * (1 - (markup_override.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(24) # serving_size(30) - commission(6)
    end

    it 'returns the cost based on the menu item and its extras price minus overridden commission' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: order_line_with_extras, company: company).call

      expected_cost = (menu_item.price.to_f + menu_extra1.price.to_f + menu_extra2.price.to_f) * (1 - (markup_override.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(36) # item(20) + extra(15) + extra(10) - commission(9)
    end

    it 'returns the cost based on the menu item\'s promo price and its extras price minus overridden commission', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: order_line_with_extras, company: company).call

      expected_cost = (menu_item.promo_price.to_f + menu_extra1.price.to_f + menu_extra2.price.to_f) * (1 - (markup_override.commission_rate / 100))
      expect(calculated_cost).to eq(expected_cost)
      expect(calculated_cost).to eq(32) # item(15) + extra(15) + extra(10) - commission(8)
    end
  end

  context 'baseline pricing irrespective of supplier commission (irrespective of markup or commission)' do
    before do
      supplier.update_column(:commission_rate, 10)
    end

    it 'returns the baseline (cost) based on the menu items' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line, baseline: true).call

      expect(calculated_cost).to eq(20)
    end

    it 'returns the baseline (cost) based on the menu item\'s promo price', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line, baseline: true).call

      expect(calculated_cost).to eq(15)
    end

    it 'returns the baseline (cost) based on the serving size price' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: serving_size_order_line, baseline: true).call

      expect(calculated_cost).to eq(30)
    end

    it 'returns the baseline (cost) based on the menu item and its extras price' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: order_line_with_extras, baseline: true).call

      expect(calculated_cost).to eq(45) # item(20) + extra(15) + extra(10)
    end

    it 'returns the baseline (cost) based on the menu item\'s promo price and its extras price', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: order_line_with_extras, baseline: true).call

      expect(calculated_cost).to eq(40) # item(15) + extra(15) + extra(10)
    end

    it 'returns the baseline (cost) based on the menu items (even with a markup/commission override)' do
      create(:supplier_markup_override, :random, commission_rate: 20, supplier_profile: supplier, overridable: company) # create markup override
      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line, company: company, baseline: true).call

      expect(calculated_cost).to eq(20)
    end

    it 'returns the baseline (cost) based on the menu item\'s promo price (even with a markup/commission override)', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      create(:supplier_markup_override, :random, commission_rate: 20, supplier_profile: supplier, overridable: company)

      calculated_cost = OrderLines::CalculateCost.new(order_line: menu_item_order_line, company: company, baseline: true).call

      expect(calculated_cost).to eq(15)
    end
  end

  context 'for an order line connected to a rate card (irrespective of markup or commission)' do
    let!(:rate_card) { create(:rate_card, :random, menu_item: menu_item, company: company, price: 22.2, cost: 22.2) }
    let(:rate_card_order_line) { build(:order_line, :random, menu_item: menu_item, supplier_profile: supplier) }

    before do
      supplier.update_column(:commission_rate, rand(1.1..20.1))
    end

    it 'returns the cost of the passed in rate card' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: rate_card_order_line, rate_card: rate_card).call

      expect(calculated_cost).to eq(rate_card.cost)
    end

    it 'returns the cost of the passed in rate card (irrespective of promo_price)', promotional_item: true do
      menu_item.update_column(:promo_price, 15)
      calculated_cost = OrderLines::CalculateCost.new(order_line: rate_card_order_line, rate_card: rate_card).call

      expect(calculated_cost).to eq(rate_card.cost)
    end

    it 'returns the cost of the rate card found using passed in company' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: rate_card_order_line, company: company).call

      expect(calculated_cost).to eq(rate_card.cost)
    end

    it 'returns the cost of the rate card found using passed in customer\'s company' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: rate_card_order_line, customer: customer).call

      expect(calculated_cost).to eq(rate_card.cost)
    end

    it 'returns the rate card cost as the baseline cost' do
      calculated_cost = OrderLines::CalculateCost.new(order_line: rate_card_order_line, company: company, baseline: true).call

      expect(calculated_cost).to eq(rate_card.cost)
    end

    it 'returns the cost of the passed in rate card (even with a markup/commission override)' do
      create(:supplier_markup_override, :random, commission_rate: 20, supplier_profile: supplier, overridable: company) # create markup override
      calculated_cost = OrderLines::CalculateCost.new(order_line: rate_card_order_line, rate_card: rate_card, company: company).call

      expect(calculated_cost).to eq(rate_card.cost)
    end
  end # rate cards
end
