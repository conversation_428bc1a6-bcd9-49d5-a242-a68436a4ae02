require 'rails_helper'

RSpec.describe OrderLines::GetSupplierItemChanges, type: :servive, order_lines: true, orders: true, order_amended_email: true do

  let!(:now) { Time.zone.now }
  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order) { create(:order, :amended) }

  let!(:order_line1) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }
  let!(:order_line2) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }
  let!(:order_line3) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }
  let!(:order_line4) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }

  before do
    last_order_line = order.reload.order_lines.order(updated_at: :desc).first
    order.update(suppliers_notified_at: last_order_line.updated_at)
  end

  it 'returns a blank array an order with no item changes (order line versions)' do
    item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

    expect(item_changes).to be_blank
  end

  context 'updates' do
    before do
      order_line1.update(quantity: order_line1.quantity + 10)
      order_line3.update(quantity: order_line3.quantity - 3, note: 'new note')
    end

    it 'returns only updated order lines' do
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      expect(item_changes.map(&:id)).to include(*[order_line1, order_line3].map(&:id))
      expect(item_changes.map(&:change_type)).to include('updated')
      expect(item_changes.map(&:id)).to_not include(*[order_line2, order_line4].map(&:id))
    end

    it 'returns updated order lines with changes to quantity field' do
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      order_line1_change = item_changes.detect{|change| change.id == order_line1.id }
      expect(order_line1_change).to be_present
      expect(order_line1_change.changes.map(&:field)).to include('quantity')
      expect(order_line1_change.changes.map(&:old_value)).to include(order_line1.quantity - 10)
      expect(order_line1_change.changes.map(&:new_value)).to include(order_line1.quantity)

      order_line3_change = item_changes.detect{|change| change.id == order_line3.id }
      expect(order_line3_change).to be_present
      expect(order_line3_change.changes.map(&:field)).to include('quantity')
      expect(order_line3_change.changes.map(&:old_value)).to include(order_line3.quantity + 3)
      expect(order_line3_change.changes.map(&:new_value)).to include(order_line3.quantity)
    end

    it 'returns updated order lines with changes to note field' do
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      order_line1_change = item_changes.detect{|change| change.id == order_line1.id }
      expect(order_line1_change).to be_present
      expect(order_line1_change.changes.map(&:field)).to_not include('note')

      order_line3_change = item_changes.detect{|change| change.id == order_line3.id }
      expect(order_line3_change).to be_present
      expect(order_line3_change.changes.map(&:field)).to include('note')
      expect(order_line3_change.changes.map(&:old_value)).to include('initial-note')
      expect(order_line3_change.changes.map(&:new_value)).to include(order_line3.note)
    end

    it 'returns updated order lines with changes of values which are different' do
      order_line2.update(quantity: order_line2.quantity, note: 'new note')
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      order_line2_change = item_changes.detect{|change| change.id == order_line2.id }
      expect(order_line2_change).to be_present
      expect(order_line2_change.changes.map(&:field)).to include('note')
      expect(order_line2_change.changes.map(&:field)).to_not include('quantity')
    end
  end

  context 'removal' do
    before do
      order_line2.destroy
    end

    it 'returns removed order lines' do
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      expect(item_changes.map(&:id)).to include(order_line2.id)
      expect(item_changes.map(&:change_type)).to include('removed')

      expect(item_changes.map(&:id)).to_not include(*[order_line1, order_line3, order_line4].map(&:id))
    end

    it 'returns removed order lines with blank changes' do
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      order_line2_change = item_changes.detect{|change| change.id == order_line2.id }
      expect(order_line2_change.changes).to be_blank
    end
  end

  context 'creation/addition' do
    let!(:order_line5) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }

    it 'returns removed order lines' do
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      expect(item_changes.map(&:id)).to include(order_line5.id)
      expect(item_changes.map(&:change_type)).to include('created')

      expect(item_changes.map(&:id)).to_not include(*[order_line1, order_line3, order_line4].map(&:id))
    end

    it 'returns removed order lines with only new values' do
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      order_line5_change = item_changes.detect{|change| change.id == order_line5.id }
      expect(order_line5_change.changes.map(&:field)).to include('quantity', 'note')
      expect(order_line5_change.changes.map(&:old_value)).to include(nil)
      expect(order_line5_change.changes.map(&:new_value)).to include(order_line5.quantity, order_line5.note)
    end
  end

  context 'for any order line updates/creates/removes' do
    let!(:order_line5) { create(:order_line, :random, order: order, quantity: 100, note: 'initial-note', supplier_profile: supplier) }
    before do
      order_line1.update(quantity: order_line1.quantity + 10)
      order_line2.destroy
      order_line3.update(quantity: order_line3.quantity - 3, note: 'new note')
    end

    it 'returns any updated/created/removed order lines' do
      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      expect(item_changes.map(&:id)).to include(*[order_line1, order_line2, order_line3, order_line5].map(&:id))
      expect(item_changes.map(&:change_type)).to include('updated', 'created', 'removed')
      expect(item_changes.map(&:id)).to_not include(order_line4.id)
    end

    it 'only returns updated order lines beloging to the supplier' do
      supplier2 = create(:supplier_profile, :random)
      order_line3.update_column(:supplier_profile_id, supplier2.id)

      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call

      expect(item_changes.map(&:id)).to include(*[order_line1, order_line2, order_line5].map(&:id))
      expect(item_changes.map(&:id)).to_not include(order_line3.id) # order_line3 belongs to a different supplier
    end

    it 'returns a blank array if order suppliers are already notified' do
      order.update(suppliers_notified_at: now + 1.hour)

      item_changes = OrderLines::GetSupplierItemChanges.new(order: order, supplier: supplier).call
      expect(item_changes).to be_blank
    end
  end

end
