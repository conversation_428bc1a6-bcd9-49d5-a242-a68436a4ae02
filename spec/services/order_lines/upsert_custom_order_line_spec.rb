require 'rails_helper'

RSpec.describe OrderLines::UpsertCustomOrderLine, type: :service, orders: true, order_lines: true do

  let(:supplier) { create(:supplier_profile, :random) }
  let(:order) { create(:order, :draft, :custom_order) }
  let(:location) { create(:location, :random, order: order) }
  let(:order_line_params) { { menu_item_description: Faker::Name.name, baseline: rand(19.1), cost: rand(18.1), price: rand(20.1), supplier_id: supplier.id, quantity: rand(10), note: Faker::Lorem.sentence, gst_option: 'gst_free' } }

  it 'creates a custom menu section for the supplier' do
    expect(supplier.menu_sections).to be_blank
    custom_order_line_creator = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call

    expect(custom_order_line_creator).to be_success
    supplier_menu_sections = supplier.reload.menu_sections

    expect(supplier_menu_sections).to be_present
    expect(supplier_menu_sections.map(&:name)).to include('custom')
  end

  it 'creates custom menu items for the supplier' do
    expect(supplier.menu_items).to be_blank

    custom_order_line_creator = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call
    expect(custom_order_line_creator).to be_success

    supplier_menu_sections = supplier.reload.menu_sections
    supplier_menu_items = supplier.menu_items
    expect(supplier_menu_items.map(&:supplier_profile)).to include(supplier)
    expect(supplier_menu_items.map(&:menu_section)).to include(supplier_menu_sections.first)
    expect(supplier_menu_items.map(&:name)).to include(order_line_params[:menu_item_description])
    expect(supplier_menu_items.map(&:price)).to include(order_line_params[:baseline])
    expect(supplier_menu_items.map(&:is_gst_free)).to include(true)
  end

  it 'creates a custom order lines for the custom menu item' do
    expect(order.order_lines).to be_blank

    custom_order_line_creator = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call
    expect(custom_order_line_creator).to be_success

    custom_order_line = custom_order_line_creator.custom_order_line
    expect(custom_order_line).to be_present
    expect(custom_order_line).to be_persisted
    expect(custom_order_line.order).to eq(order)
    expect(custom_order_line.location).to eq(location)
    expect(custom_order_line.quantity).to eq(order_line_params[:quantity])
    expect(custom_order_line.note).to eq(order_line_params[:note])
    expect(custom_order_line.name).to eq(order_line_params[:menu_item_description]) # name of the custom menu item
    expect(custom_order_line.baseline).to eq(order_line_params[:baseline])
    expect(custom_order_line.cost).to eq(order_line_params[:cost]) # cost of the custom menu item
    expect(custom_order_line.price).to eq(order_line_params[:price]) # price of the custom menu item
    expect(custom_order_line.is_gst_free).to eq(true) # gst free confif of menu item
    expect(custom_order_line.category).to eq(order.major_category) # same as order's major category
  end

  context 'menu item errors' do
    let(:malformed_order_line_params) do
      {
        menu_item_description: Faker::Lorem.characters(number: 256),
        note: Faker::Lorem.sentence,
        baseline: rand(19.1),
        cost: rand(18.1),
        price: rand(20.1),
        supplier_id: supplier.id,
        quantity: rand(10),
        gst_option: 'gst_free'
      }
    end

    it 'errors out if the menu item has malformed data' do
      custom_order_line_creator = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: malformed_order_line_params).call

      expect(custom_order_line_creator).to_not be_success
      expect(custom_order_line_creator.errors).to include('Name - Item name is too long (max 255)')
    end
  end

  context 'with existing custom menu section' do

    let!(:custom_menu_section) { create(:menu_section, :random, supplier_profile: supplier, name: 'custom') }

    it 'does not create a duplicate custom menu section for the supplier' do
      custom_order_line_creator = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call

      expect(custom_order_line_creator).to be_success
      supplier_custom_menu_sections = supplier.reload.menu_sections.where(name: 'custom')

      expect(supplier_custom_menu_sections).to be_present
      expect(supplier_custom_menu_sections.size).to eq(1)
      expect(supplier_custom_menu_sections.map(&:id)).to include(custom_menu_section.id)
    end

    context 'with an existing menu item' do

      let!(:custom_menu_item) { create(:menu_item, :random, menu_section: custom_menu_section, supplier_profile: supplier, name: order_line_params[:menu_item_description], description: order_line_params[:menu_item_description], price: order_line_params[:baseline], is_gst_free: true) }

      it 'create a new (duplicate) custom menu item for the supplier' do
        custom_order_line_creator = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call

        expect(custom_order_line_creator).to be_success
        created_order_line = custom_order_line_creator.custom_order_line
        expect(created_order_line.menu_item).to_not eq(custom_menu_item)
      end

      it 'creates a new custom menu item for the supplier with a different price and/or gst config' do
        order_line_params[:gst_option] = 'WITH GST'
        order_line_params[:baseline] = 999.99
        order_line_params[:price] = 1999.99

        custom_order_line_creator = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call

        expect(custom_order_line_creator).to be_success
        supplier_custom_menu_item = supplier.reload.menu_items

        expect(supplier_custom_menu_item).to be_present
        expect(supplier_custom_menu_item.size).to_not eq(1) # created a new one
        expect(supplier_custom_menu_item.map(&:id)).to include(custom_menu_item.id)
        expect(supplier_custom_menu_item.map(&:price)).to include(custom_menu_item.price, 999.99)
        expect(supplier_custom_menu_item.map(&:is_gst_free)).to include(custom_menu_item.is_gst_free, false)
      end

      context 'with an existing order line' do
        let!(:custom_order_line) { create(:order_line, :random, order: order, location: location, supplier_profile: supplier, menu_item: custom_menu_item, note: order_line_params[:note]) }

        it 'create a new (duplicate) order line' do
          custom_order_line_upserter = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call

          expect(custom_order_line_upserter).to be_success
          upserted_custom_order_line = custom_order_line_upserter.custom_order_line
          expect(upserted_custom_order_line.id).to_not eq(custom_order_line.id)
        end

        it 'updates the quantity of an exiting order line' do
          order_line_params[:quantity] = 999
          order_line_params[:id] = custom_order_line.id # need to pass ID else it'll add quantity
          custom_order_line_upserter = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call

          expect(custom_order_line_upserter).to be_success
          upserted_custom_order_line = custom_order_line_upserter.custom_order_line
          expect(upserted_custom_order_line.id).to eq(custom_order_line.id)
          expect(upserted_custom_order_line.quantity).to eq(999)
        end

        it 'does not create a duplicate menu item' do
          supplier_menu_items = supplier.reload.menu_items
          expect(supplier_menu_items.size).to eq(1) # initial check

          order_line_params[:id] = custom_order_line.id # need to pass ID else it'll add quantity
          custom_order_line_upserter = OrderLines::UpsertCustomOrderLine.new(location: location, order_line_params: order_line_params).call

          expect(custom_order_line_upserter).to be_success
          supplier_menu_items = supplier.reload.menu_items
          expect(supplier_menu_items.size).to eq(1) # did not change menu item count
          expect(supplier_menu_items.map(&:id)).to include(custom_menu_item.id)
        end
      end # existing order line
    end # exiting menu item
  end # existing menu section
end
