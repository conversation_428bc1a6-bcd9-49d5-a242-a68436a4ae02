require 'rails_helper'

RSpec.describe OrderLines::FutureUpdateExists, type: :service, order_lines: true, delayed_jobs: true do

  let!(:menu_item) { create(:menu_item, :random) }

  before do
    # do not run the delayed jobs inline, instead create a Delayed::Job record
    Delayed::Worker.delay_jobs = true
  end

  after do
    # revert back to running delayed jobs in line
    Delayed::Worker.delay_jobs = false
  end

  context 'for menu items' do
    it 'return false by default' do
      update_exists = OrderLines::FutureUpdateExists.new(item: menu_item).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn true if a menu item order line update exists' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).delay(queue: :data_integrity).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: menu_item).call
      expect(update_exists).to be_truthy
    end

    it 'reutrn false if a menu item order line update exists but is locked' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).delay(queue: :data_integrity).call
      expect(Delayed::Job.count).to eq(1)

      Delayed::Job.last.update_column(:locked_at, Time.zone.now)

      update_exists = OrderLines::FutureUpdateExists.new(item: menu_item).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn false if a menu item order line update exists but is in a different queue' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).delay(queue: :instant).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: menu_item).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn false if a menu item order line update exists but for a different menu item' do
      menu_item2 = create(:menu_item, :random)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item2).delay(queue: :instant).call
      expect(Delayed::Job.all).to be_present

      update_exists = OrderLines::FutureUpdateExists.new(item: menu_item).call
      expect(update_exists).to be_falsey
    end
  end

  context 'for serving sizes' do
    let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }

    it 'return false by default' do
      update_exists = OrderLines::FutureUpdateExists.new(item: serving_size).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn true if a serving size order line update exists' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size).delay(queue: :data_integrity).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: serving_size).call
      expect(update_exists).to be_truthy
    end

    it 'reutrn false if a serving size order line update exists but is locked' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size).delay(queue: :data_integrity).call
      expect(Delayed::Job.count).to eq(1)

      Delayed::Job.last.update_column(:locked_at, Time.zone.now)

      update_exists = OrderLines::FutureUpdateExists.new(item: serving_size).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn false if a serving size order line update exists but is in a different queue' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size).delay(queue: :instant).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: serving_size).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn false if a serving size order line update exists but for a different serving size' do
      serving_size2 = create(:serving_size, :random, menu_item: menu_item)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size2).delay(queue: :instant).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: serving_size).call
      expect(update_exists).to be_falsey
    end
  end

  context 'for menu item rate cards' do
    let!(:rate_card) { create(:rate_card, :random, menu_item: menu_item) }

    it 'return false by default' do
      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn true if a rate card order line update exists' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: rate_card).delay(queue: :data_integrity).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_truthy
    end

    it 'reutrn false if a rate card order line update exists but is locked' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: rate_card).delay(queue: :data_integrity).call
      expect(Delayed::Job.count).to eq(1)

      Delayed::Job.last.update_column(:locked_at, Time.zone.now)

      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn false if a rate card order line update exists but is in a different queue' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: rate_card).delay(queue: :instant).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn false if a rate card order line update exists but for a different rate_card' do
      rate_card2 = create(:rate_card, :random, menu_item: menu_item)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: rate_card2).delay(queue: :instant).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_falsey
    end

    context 'rate card archive' do
      it 'reutrn true if a rate card\'s menu item\'s order line update exists' do
        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).delay(queue: :data_integrity).call
        expect(Delayed::Job.count).to eq(1)

        update_exists = OrderLines::FutureUpdateExists.new(item: rate_card, is_archived: true).call
        expect(update_exists).to be_truthy
      end
    end
  end

  context 'for serving size rate cards' do
    let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
    let!(:rate_card) { create(:rate_card, :random, menu_item: menu_item, serving_size: serving_size) }

    it 'return false by default' do
      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn true if a (serving_size) rate card order line update exists' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size, rate_card: rate_card).delay(queue: :data_integrity).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_truthy
    end

    it 'reutrn false if a (serving_size) rate card order line update exists but is locked' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size, rate_card: rate_card).delay(queue: :data_integrity).call
      expect(Delayed::Job.count).to eq(1)

      Delayed::Job.last.update_column(:locked_at, Time.zone.now)

      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn false if a (serving_size) rate card order line update exists but is in a different queue' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size, rate_card: rate_card).delay(queue: :instant).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_falsey
    end

    it 'reutrn false if a (serving_size) rate card order line update exists but for a different rate_card' do
      rate_card2 = create(:rate_card, :random, menu_item: menu_item)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size, rate_card: rate_card2).delay(queue: :instant).call
      expect(Delayed::Job.count).to eq(1)

      update_exists = OrderLines::FutureUpdateExists.new(item: rate_card).call
      expect(update_exists).to be_falsey
    end

    context 'rate card archive' do
      it 'reutrn true if a rate card\'s menu item\'s and serving sizes\'s order line update exists' do
        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size).delay(queue: :data_integrity).call
        expect(Delayed::Job.count).to eq(1)

        update_exists = OrderLines::FutureUpdateExists.new(item: rate_card, is_archived: true).call
        expect(update_exists).to be_truthy
      end
    end
  end

end