require 'rails_helper'

RSpec.describe Suppliers::Emails::SendRecurringOrderReminderEmail, type: :service, emails: true, suppliers: true, orders: true do
  include Rails.application.routes.url_helpers

  subject { Suppliers::Emails::SendRecurringOrderReminderEmail.new(order: order, supplier: supplier).call }

  let!(:supplier) { create(:supplier_profile, :random, :with_user) }
  let!(:order_customer) { create(:customer_profile, :random, :with_user) }
  let!(:order) { create(:order, :confirmed, customer_profile: order_customer) }

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
  end

  it 'returns the sent email' do
    sent_email = subject

    expect(sent_email).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Suppliers::Emails::SendRecurringOrderReminderEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )
    subject
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "YORDAR: You have an upcoming recurring order - ##{order.id}",
        cc: anything,
        email_options: anything,
        email_variables: anything,
        attachments: anything
      )

    subject
  end

  it 'sends the email to the correct recipients the supplier (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: supplier.email_recipient,
      subject: anything,
      cc: 'orders-email',
      email_options: anything,
      email_variables: anything,
      attachments: anything
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{Suppliers::Emails::SendRecurringOrderReminderEmail::EMAIL_TEMPLATE}-#{order.id}-#{supplier.id}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: supplier.id, ref: email_ref },
        email_variables: anything,
        attachments: anything
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the email data' do
      expected_email_variables = {
        firstname: supplier.email_salutation,        
        has_commission: supplier.commission_rate > 0.0,
        header_color: :cream,

        order: anything,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    it 'sends email with the correct order data' do
      expected_order_data = {
        id: order.id,
        customer_name: order_customer.customer_or_company_name,
        delivery_at: order.delivery_at.to_s(:full_verbose),
        view_url: supplier_order_show_url(order, host: yordar_credentials(:default_host)),
        pdf_url: nil
      }
      expected_email_variables = {
        order: deep_struct(expected_order_data),

        firstname: anything,        
        has_commission: anything,
        header_color: anything,
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables,
          attachments: anything
        )

      subject
    end

    context 'with supplier order details documents' do
      let!(:order_supplier) { create(:order_supplier, :random, order: order, supplier_profile: supplier) }
      let!(:document1) { create(:document, :random, kind: 'supplier_order_details', documentable: order_supplier, version: 3) }
      let!(:document2) { create(:document, :random, kind: 'supplier_order_details', documentable: order_supplier, version: 1) }

      it 'sends email with the latest supplier order details PDF (even as attachment)' do
        expected_order_data = {
          id: order.id,
          customer_name: order_customer.customer_or_company_name,
          delivery_at: order.delivery_at.to_s(:full_verbose),
          view_url: supplier_order_show_url(order, host: yordar_credentials(:default_host)),
          pdf_url: document1.url
        }
        expected_email_variables = {
          order: deep_struct(expected_order_data),

          firstname: anything,        
          has_commission: anything,
          header_color: anything,
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            cc: anything,
            email_options: anything,
            email_variables: expected_email_variables,
            attachments: [document1]
          )

        subject
      end
    end
  end

  context 'errors' do
    it 'doesn\'t send email if order is missing' do
      expect(Emails::Send).to_not receive(:new)

      Suppliers::Emails::SendRecurringOrderReminderEmail.new(order: nil, supplier: supplier).call
    end

    it 'doesn\'t send email if supplier is missing' do
      expect(Emails::Send).to_not receive(:new)

      Suppliers::Emails::SendRecurringOrderReminderEmail.new(order: order, supplier: nil).call
    end

    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Suppliers::Emails::SendRecurringOrderReminderEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        failed_email_sender = Suppliers::Emails::SendRecurringOrderReminderEmail.new(order: order, supplier: supplier)

        expected_error_message = "Failed to send recurring order reminder email to supplier #{supplier.id} - #{order.id}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(failed_email_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

        failed_email_sender.call
      end
    end # email sender error
  end

end