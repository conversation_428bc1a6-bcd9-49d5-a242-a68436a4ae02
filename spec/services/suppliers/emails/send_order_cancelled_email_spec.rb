require 'rails_helper'

RSpec.describe Suppliers::Emails::SendOrderCancelledEmail, type: :service, emails: true, suppliers: true, orders: true do
  include Rails.application.routes.url_helpers

  subject { Suppliers::Emails::SendOrderCancelledEmail.new(supplier: supplier, orders: [order], mode: cancel_mode).call }

  let!(:supplier) { create(:supplier_profile, :random, :with_user) }
  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:order) { create(:order, :confirmed, customer_profile: customer) }

  let!(:email_sender) { double(::Emails::Send) }

  let!(:cancel_mode) { %w[one-off on-hold subsequent related].sample }

  let(:order_status) do
    {
      'one-off' => 'cancelled',
      'on-hold' => 'put on hold',
      # 'subsequent' => 'cancelled permanently',
      # 'related' => 'cancelled permanently',
    }[cancel_mode] || 'cancelled permanently'
  end

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
  end

  it 'returns the sent email' do
    email_sender = subject

    expect(email_sender).to be_success
    expect(email_sender.sent_notification).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Suppliers::Emails::SendOrderCancelledEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
      )
    subject
  end

  it 'send the email with the appropriate subject based on cancel_mode' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "YORDAR: An order has been #{order_status} - ##{order.id}",
        cc: anything,
        email_options: anything,
        email_variables: anything
      )

    subject
  end

  it 'sends the email to the correct recipients the supplier (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: supplier.email_recipient,
      subject: anything,
      cc: 'orders-email',
      email_options: anything,
      email_variables: anything,
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{Suppliers::Emails::SendOrderCancelledEmail::EMAIL_TEMPLATE}-#{order.id}"
    expect(Emails::Send).to receive(:new).with(
      template_name: anything,
      recipient: anything,
      subject: anything,
      cc: anything,
      email_options: { fk_id: supplier.id, ref: email_ref },
      email_variables: anything
    )

    subject
  end

  context 'email variables' do
    it 'sends email with the email data' do
      expected_email_variables = {        
        order: anything,
        profile_url: anything,        

        firstname: supplier.email_salutation,
        cancel_mode: cancel_mode,
        header_color: :cream,
      }

      expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: expected_email_variables
      )

      subject
    end

    context 'order data based on cancel_mode and order type' do
      let!(:delivery_date_format) do
        {
          'one-off' => '%A, %d-%b-%Y, %I:%M%P',
        }[cancel_mode] || '%d-%b-%Y, %I:%M%P'
      end

      let!(:order_type_map) do
        {
          'team_order' => 'team',
          'recurring' => 'standing',
          'normal' => 'one off',
        }
      end

      let!(:order_type) { order_type_map.keys.sample }

      before do
        case order_type
        when 'team_order'
          order.update_column(:order_variant, 'team_order')
        when 'recurring'
          order.update_column(:recurrent_id, rand(102..206))
        else # normal
          # do nothing
        end
      end

      it 'sends email with the correct order data' do
        expected_order_days = cancel_mode == 'one-off' ? nil : order.delivery_at.strftime('%As')
        expected_order_data = {
          id: order.id,
          type: order_type_map[order_type],
          status: order_status,
          customer_name: customer.customer_or_company_name,
          date: order.delivery_at.strftime(delivery_date_format),
          days: expected_order_days,
          pdf_url: nil
        }

        expected_email_variables = {        
          firstname: anything,
          cancel_mode: anything,
          header_color: anything,
          profile_url: anything,
          
          order: deep_struct(expected_order_data),
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            cc: anything,
            email_options: anything,
            email_variables: expected_email_variables
          )

        subject
      end

      context 'with supplier order details documents' do
        let!(:order_supplier) { create(:order_supplier, :random, order: order, supplier_profile: supplier) }
        let!(:document1) { create(:document, :random, kind: 'supplier_order_details', documentable: order_supplier, version: 1) }
        let!(:document2) { create(:document, :random, kind: 'supplier_order_details', documentable: order_supplier, version: 3) }

        it 'sends email with the latest supplier order details PDF' do
          expected_order_days = cancel_mode == 'one-off' ? nil : order.delivery_at.strftime('%As')
          expected_order_data = {
            id: order.id,
            type: order_type_map[order_type],
            status: order_status,
            customer_name: customer.customer_or_company_name,
            date: order.delivery_at.strftime(delivery_date_format),
            days: expected_order_days,
            pdf_url: document2.url
          }

          expected_email_variables = {
            firstname: anything,
            cancel_mode: anything,
            header_color: anything,
            profile_url: anything,
            
            order: deep_struct(expected_order_data),
          }

          expect(Emails::Send).to receive(:new).with(
              template_name: anything,
              recipient: anything,
              subject: anything,
              cc: anything,
              email_options: anything,
              email_variables: expected_email_variables
            )

          subject
        end
      end
    end # order data

    it 'sends email with the correct (supplier) profile url' do
      expected_email_variables = {
        firstname: anything,
        cancel_mode: anything,
        header_color: anything,
        order: anything,

        profile_url: supplier_profile_url(host: yordar_credentials(:default_host)),        
      }

      expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: expected_email_variables
      )

      subject
    end
  end # email variables

  context 'errors' do
    it 'doesn\'t send email if supplier is missing' do
      expect(Emails::Send).to_not receive(:new)

      email_sender = Suppliers::Emails::SendOrderCancelledEmail.new(supplier: nil, orders: [order], mode: cancel_mode).call
      expect(email_sender).to_not be_success
      expect(email_sender.errors).to include('Cannot notify without a supplier')
    end

    it 'doesn\'t send email if (cancelled) order(s) are missing' do
      expect(Emails::Send).to_not receive(:new)

      email_sender = Suppliers::Emails::SendOrderCancelledEmail.new(supplier: supplier, orders: nil, mode: cancel_mode).call
      expect(email_sender).to_not be_success
      expect(email_sender.errors).to include('Cannot notify without (cancelled) orders')
    end

    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Suppliers::Emails::SendOrderCancelledEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        failed_email_sender = Suppliers::Emails::SendOrderCancelledEmail.new(supplier: supplier, orders: [order], mode: cancel_mode)

        expected_error_message = "Failed to send recurring order reminder email to supplier #{supplier.id} - #{order.id}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(failed_email_sender).to receive(:log_errors)#.with(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id, order_ids: orders.map(&:id) })

        failed_email_sender.call
      end
    end # email sender error
  end

end