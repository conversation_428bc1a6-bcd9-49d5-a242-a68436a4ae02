require 'rails_helper'

RSpec.describe Suppliers::FetchOperatingDays, type: :service, suppliers: true, delivery_zones: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:suburb) { create(:suburb, :random) }
  let!(:delivery_zone) { create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb, operating_wdays: '1111111') }

  it 'returns the calculated operating days for the passed in delivery zone' do
    fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_days).to eq('7 days a week')
  end

  it 'returns nil if supplier if delivery_zone is missing' do
    fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: nil).call
    expect(fetched_operating_days).to eq(nil)
  end

  it 'returns blank of operating_wdays for delivery is missing' do
    delivery_zone.update_column(:operating_wdays, nil)
    fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_days).to be_blank
  end

  it 'returns the calculated weekdays for the passed in delivery zone' do
    delivery_zone.update_column(:operating_wdays, '0111110')

    fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_days).to eq('Mon-Fri')
  end

  it 'returns the calculated split weekdays for the passed in delivery zone' do
    delivery_zone.update_column(:operating_wdays, '0110110')
    fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_days).to eq('Mon-Tue, Thur-Fri')
  end

  it 'returns the calculated split individual weekdays for the passed in delivery zone' do
    delivery_zone.update_column(:operating_wdays, '0101010')

    fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_days).to eq('Mon, Wed, Fri')
  end

  it 'returns the calculated split individual and ranged weekdays for the passed in delivery zone' do
    delivery_zone.update_column(:operating_wdays, '0101110')

    fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_days).to eq('Mon, Wed-Fri')
  end

  context 'with saved operating days' do
    before do
      supplier.update_column(:operating_days, 'saved-operating-days')
    end

    it 'returns the saved operating hours for the supplier' do
      fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone).call
      expect(fetched_operating_days).to eq('saved-operating-days')
    end

    it 'returns the calculated delivery zone based operating hours if supplier is missing' do
      fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: nil, delivery_zone: delivery_zone).call
      expect(fetched_operating_days).to_not eq('saved-operating-days')
      expect(fetched_operating_days).to eq('7 days a week')
    end

    it 'returns the calculated delivery zone based operating hours if set to caculate' do
      fetched_operating_days = Suppliers::FetchOperatingDays.new(supplier: supplier, delivery_zone: delivery_zone, calculate: true).call
      expect(fetched_operating_days).to_not eq('saved-operating-days')
      expect(fetched_operating_days).to eq('7 days a week')
    end
  end

end
