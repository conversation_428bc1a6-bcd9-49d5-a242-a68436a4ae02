require 'rails_helper'

RSpec.describe Suppliers::FetchMarkupOverride, type: :service, suppliers: true, markup_overrides: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:customer1) { create(:customer_profile, :random) }
  let!(:customer2) { create(:customer_profile, :random) }

  let!(:customer_markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: customer1) }

  it 'returns the supplier x customer specific markup override' do
    fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer1).call

    expect(fetched_markup_override).to eq(customer_markup_override)
  end

  context 'with required override (field)' do
    Suppliers::FetchMarkupOverride::VALID_OVERRIDE_FIELDS.each do |override_field|
      it "only returns override if required override (#{override_field}) value is present" do
        customer_markup_override.update_column(override_field, nil)

        fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer1, required_override: override_field).call
        expect(fetched_markup_override).to be_nil
      end
    end

    it 'returns no override if passed in required override (field) is not a valid field name' do
      fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer1, required_override: 'override_field').call
      expect(fetched_markup_override).to be_nil
    end
  end

  context 'with company overrides' do
    let!(:company) { create(:company, :random) }
    let!(:company_markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: company) }

    before do
      # both customers belong to the company
      [customer1, customer2].each {|customer| customer.update_column(:company_id, company.id) }
    end

    it 'still returns the customer specific override if present' do
      fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer1).call

      expect(fetched_markup_override).to eq(customer_markup_override)
    end

    it 'returns the company specific override if present' do
      fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer2).call

      expect(fetched_markup_override).to eq(company_markup_override)
    end

    it 'returns the company specific override if a company is passed instead of the customer' do
      fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, company: company).call

      expect(fetched_markup_override).to eq(company_markup_override)
    end

    context 'with required override (field)' do
      it 'returns the (customer\'s) company specific override if present customer specific override does not have the required override field value' do
        override_field = Suppliers::FetchMarkupOverride::VALID_OVERRIDE_FIELDS.sample
        customer_markup_override.update_column(override_field, nil)

        fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer1, required_override: override_field).call

        expect(fetched_markup_override).to_not eq(customer_markup_override)
        expect(fetched_markup_override).to eq(company_markup_override)
      end

      Suppliers::FetchMarkupOverride::VALID_OVERRIDE_FIELDS.each do |override_field|
        it "only returns override if required override (#{override_field}) value is present" do
          company_markup_override.update_column(override_field, nil)

          fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, company: company, required_override: override_field).call
          expect(fetched_markup_override).to be_nil
        end
      end # override fields
    end
  end # company override

  it 'returns nil if supplier is missing' do
    fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: nil, customer: customer1).call

    expect(fetched_markup_override).to be_nil
  end

  it 'returns nil if markup belongs to a different customer' do
    fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: customer2).call

    expect(fetched_markup_override).to be_nil
  end

  it 'returns nil if customer and company is missing' do
    fetched_markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: nil, company: nil).call

    expect(fetched_markup_override).to be_nil
  end

end