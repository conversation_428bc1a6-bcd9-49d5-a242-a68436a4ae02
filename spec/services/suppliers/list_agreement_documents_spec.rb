require 'rails_helper'

RSpec.describe Suppliers::ListAgreementDocuments, type: :service, suppliers: true do

  let!(:now) { Time.zone.now }

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:document1) { create(:supplier_agreement_document, :random, supplier_profile: supplier, status: 'completed', created_at: now + 1.hour) }
  let!(:document2) { create(:supplier_agreement_document, :random, supplier_profile: supplier, status: 'completed', created_at: now + 2.hour) }
  let!(:document3) { create(:supplier_agreement_document, :random, supplier_profile: supplier, status: 'sent', created_at: now + 3.hour) }
  let!(:document4) { create(:supplier_agreement_document, :random, supplier_profile: supplier, status: 'voided', created_at: now + 4.hour) }

  it 'lists all the documents for the supplier' do
    documents = Suppliers::ListAgreementDocuments.new(supplier: supplier).call

    expect(documents.size).to eq(4)
    expect(documents).to include(document1, document2, document3, document4)
  end

  it 'doesn\'t list documents not belonging to the passed in supplier' do
    supplier2 = create(:supplier_profile, :random)
    document3.update_column(:supplier_profile_id, supplier2.id)
    documents = Suppliers::ListAgreementDocuments.new(supplier: supplier).call

    expect(documents).to_not include(document3)
    expect(documents).to include(document1, document2, document4)
  end

  it 'only lists documents with the passed in status' do
    lister_options = {
      status: 'completed'
    }

    documents = Suppliers::ListAgreementDocuments.new(supplier: supplier, options: lister_options).call
    expect(documents).to include(document1, document2)
    expect(documents).to_not include(document3, document4)
  end

  it 'only lists latest document' do
    lister_options = {
      latest: true
    }

    documents = Suppliers::ListAgreementDocuments.new(supplier: supplier, options: lister_options).call
    expect(documents.size).to eq(1)
    expect(documents).to include(document4)
    expect(documents).to_not include(document1, document2, document3)
  end

  it 'only lists latest document with the passed in status' do
    lister_options = {
      status: 'completed',
      latest: true
    }

    documents = Suppliers::ListAgreementDocuments.new(supplier: supplier, options: lister_options).call
    expect(documents).to include(document2)
    expect(documents).to_not include(document1, document3, document4)
  end

end
