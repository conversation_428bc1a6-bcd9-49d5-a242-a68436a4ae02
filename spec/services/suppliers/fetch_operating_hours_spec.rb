require 'rails_helper'

RSpec.describe Suppliers::FetchOperatingHours, type: :service, suppliers: true, delivery_zones: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:suburb) { create(:suburb, :random) }
  let!(:delivery_zone) { create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb, operating_hours_start: 18_000, operating_hours_end: 61_200) }

  it 'returns the calculated operating hours for the passed in delivery zone' do
    fetched_operating_hours = Suppliers::FetchOperatingHours.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_hours).to eq('05:00am - 05:00pm')
  end

  it 'only returns the calculated operating end hours if end is missing' do
    delivery_zone.update_column(:operating_hours_end, nil)
    fetched_operating_hours = Suppliers::FetchOperatingHours.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_hours).to eq('05:00am')
  end

  it 'returns the calculated operating start hours if end is missing' do
    delivery_zone.update_column(:operating_hours_start, nil)
    fetched_operating_hours = Suppliers::FetchOperatingHours.new(supplier: supplier, delivery_zone: delivery_zone).call
    expect(fetched_operating_hours).to eq('05:00pm')
  end

  it 'returns nil if supplier if delivery_zone is missing' do
    fetched_operating_hours = Suppliers::FetchOperatingHours.new(supplier: supplier, delivery_zone: nil).call
    expect(fetched_operating_hours).to be_nil
  end

  context 'with saved operating hours' do
    before do
      supplier.update_column(:operating_hours, 'saved-operating-hours')
    end

    it 'returns the saved operating hours for the supplier' do
      fetched_operating_hours = Suppliers::FetchOperatingHours.new(supplier: supplier, delivery_zone: delivery_zone).call
      expect(fetched_operating_hours).to eq('saved-operating-hours')
    end

    it 'returns the calculated delivery zone based operating hours if supplier is missing' do
      fetched_operating_hours = Suppliers::FetchOperatingHours.new(supplier: nil, delivery_zone: delivery_zone).call
      expect(fetched_operating_hours).to eq('05:00am - 05:00pm')
    end

    it 'returns the calculated delivery zone based operating hours if set to caculate' do
      fetched_operating_hours = Suppliers::FetchOperatingHours.new(supplier: supplier, delivery_zone: delivery_zone, calculate: true).call
      expect(fetched_operating_hours).to eq('05:00am - 05:00pm')
    end
  end

end
