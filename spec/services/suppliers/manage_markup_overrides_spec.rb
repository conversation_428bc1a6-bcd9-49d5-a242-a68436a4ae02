require 'rails_helper'

RSpec.describe Suppliers::ManageMarkupOverrides, type: :service, suppliers: true, markup_overrides: true do

  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:customer) { create(:customer_profile, :random) }
  let!(:company) { create(:company, :random) }

  let!(:customer_override) do
    {
      overridable_type: 'CustomerProfile',
      overridable_id: customer.id,
      markup: rand(10.3..20.9).round(2),
      commission_rate: rand(10.3..20.9).round(2),
      active: true
    }
  end

  let!(:company_override) do
    {
      overridable_type: 'Company',
      overridable_id: company.id,
      markup: rand(10.3..20.9).round(2),
      commission_rate: rand(10.3..20.9).round(2),
      active: true
    }
  end

  let!(:markup_overrides) { [customer_override, company_override] }

  it 'adds the markup overrides for passed in customer / company' do
    overrides_manager = Suppliers::ManageMarkupOverrides.new(supplier: supplier, markup_overrides: markup_overrides).call

    expect(overrides_manager).to be_success

    created_markup_overrides = overrides_manager.markup_overrides
    expect(created_markup_overrides.size).to eq(2)

    created_customer_override = created_markup_overrides.detect{|override| override.overridable == customer }
    expect(created_customer_override.supplier_profile).to eq(supplier)
    expect(created_customer_override.markup.round(2).to_s).to eq(customer_override[:markup].round(2).to_s)
    expect(created_customer_override.commission_rate.round(2).to_s).to eq(customer_override[:commission_rate].round(2).to_s)

    created_company_override = created_markup_overrides.detect{|override| override.overridable == company }
    expect(created_company_override.supplier_profile).to eq(supplier)
    expect(created_company_override.markup.round(2).to_s).to eq(company_override[:markup].round(2).to_s)
    expect(created_company_override.commission_rate.round(2).to_s).to eq(company_override[:commission_rate].round(2).to_s)
  end

  context 'with an existing markup overrides' do
    let!(:existing_customer_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: customer, active: true) }
    let!(:existing_company_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: company, active: true) }

    let!(:customer_override) do
      {
        id: existing_customer_override.id,
        overridable_type: existing_customer_override.overridable_type,
        overridable_id: existing_customer_override.overridable_id,
        markup: rand(10.3..20.9).round(2),
        commission_rate: rand(10.3..20.9).round(2),
        active: true
      }
    end

    let!(:company_override) do
      {
        id: existing_company_override.id,
        overridable_type: existing_company_override.overridable_type,
        overridable_id: existing_company_override.overridable_id,
        markup: rand(10.3..20.9).round(2),
        commission_rate: rand(10.3..20.9).round(2),
        active: true
      }
    end

    let!(:markup_overrides) { [customer_override, company_override] }

    it 'updates the existing delivery overrides (using passed in ID)' do
      overrides_manager = Suppliers::ManageMarkupOverrides.new(supplier: supplier, markup_overrides: markup_overrides).call

      expect(overrides_manager).to be_success

      updated_markup_overrides = overrides_manager.markup_overrides
      expect(updated_markup_overrides.size).to eq(2)

      updated_customer_override = updated_markup_overrides.detect{|override| override.overridable == customer }
      expect(updated_customer_override.id).to eq(existing_customer_override.id)
      expect(updated_customer_override.markup.round(2).to_s).to eq(customer_override[:markup].round(2).to_s)
      expect(updated_customer_override.commission_rate.round(2).to_s).to eq(customer_override[:commission_rate].round(2).to_s)
      expect(updated_customer_override.active).to eq(customer_override[:active])

      updated_company_override = updated_markup_overrides.detect{|override| override.overridable == company }
      expect(updated_company_override.id).to eq(existing_company_override.id)
      expect(updated_company_override.markup.round(2).to_s).to eq(company_override[:markup].round(2).to_s)
      expect(updated_company_override.commission_rate.round(2).to_s).to eq(company_override[:commission_rate].round(2).to_s)
      expect(updated_company_override.active).to eq(company_override[:active])
    end

    it 'updates the existing delivery overrides even without ID (matched on overridable id - does not create duplicates)' do
      markup_overrides_without_ids = markup_overrides.map{|override| override.except(:id) }
      overrides_manager = Suppliers::ManageMarkupOverrides.new(supplier: supplier, markup_overrides: markup_overrides_without_ids).call

      expect(overrides_manager).to be_success

      updated_markup_overrides = overrides_manager.markup_overrides
      expect(updated_markup_overrides.size).to eq(2)

      updated_customer_override = updated_markup_overrides.detect{|override| override.overridable == customer }
      expect(updated_customer_override.id).to eq(existing_customer_override.id)
      expect(updated_customer_override.markup.round(2).to_s).to eq(customer_override[:markup].round(2).to_s)
      expect(updated_customer_override.commission_rate.round(2).to_s).to eq(customer_override[:commission_rate].round(2).to_s)
      expect(updated_customer_override.active).to eq(customer_override[:active])

      updated_company_override = updated_markup_overrides.detect{|override| override.overridable == company }
      expect(updated_company_override.id).to eq(existing_company_override.id)
      expect(updated_company_override.markup.round(2).to_s).to eq(company_override[:markup].round(2).to_s)
      expect(updated_company_override.commission_rate.round(2).to_s).to eq(company_override[:commission_rate].round(2).to_s)
      expect(updated_company_override.active).to eq(company_override[:active])
    end

    it 'removes the existing delivery overrides if `_delete` is passed' do
      markup_overrides_with_deletes = [
        customer_override.merge({ _delete: true }),
        company_override
      ]
      overrides_manager = Suppliers::ManageMarkupOverrides.new(supplier: supplier, markup_overrides: markup_overrides_with_deletes).call

      expect(overrides_manager).to be_success

      updated_markup_overrides = overrides_manager.markup_overrides
      expect(updated_markup_overrides.size).to eq(1)

      updated_company_override = updated_markup_overrides.detect{|override| override.overridable == company }
      expect(updated_company_override.id).to eq(existing_company_override.id)
      expect(updated_company_override.markup.round(2).to_s).to eq(company_override[:markup].round(2).to_s)
      expect(updated_company_override.commission_rate.round(2).to_s).to eq(company_override[:commission_rate].round(2).to_s)
      expect(updated_company_override.active).to eq(company_override[:active])

      expect{ existing_customer_override.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end
end
