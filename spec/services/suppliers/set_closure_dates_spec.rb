require 'rails_helper'

RSpec.describe Suppliers::SetClosureDates, type: :service, suppliers: true do

  let!(:yordar_close_from) { Time.zone.now.beginning_of_month }
  let!(:yordar_close_to) { yordar_close_from.end_of_month }

  let!(:close_from) { yordar_close_from - 1.day }
  let!(:close_to) { yordar_close_to + 1.day }

  let!(:supplier1) { create(:supplier_profile, :random, is_searchable: true) }
  let!(:supplier2) { create(:supplier_profile, :random, is_searchable: true) }

  it 'sets the passed in closure dates for the searchable suppliers' do
    date_setter = Suppliers::SetClosureDates.new(close_from: close_from, close_to: close_to).call

    expect(date_setter).to be_success
    closure_suppliers = date_setter.closure_suppliers
    expect(closure_suppliers).to include(supplier1, supplier2)
    expect(closure_suppliers.map(&:close_from).uniq).to eq([close_from])
    expect(closure_suppliers.map(&:close_to).uniq).to eq([close_to])
  end

  it 'does not set the passed in closure dates for non-searchable suppliers' do
    supplier1.update_column(:is_searchable, false)

    date_setter = Suppliers::SetClosureDates.new(close_from: close_from, close_to: close_to).call
    expect(date_setter).to be_success

    closure_suppliers = date_setter.closure_suppliers

    expect(closure_suppliers).to_not include(supplier1)
    expect(supplier1.reload.close_from.to_s).to_not eq(close_from.to_s)
    expect(supplier1.reload.close_to.to_s).to_not eq(close_to)

    expect(closure_suppliers).to include(supplier2)
    expect(supplier2.reload.close_from.to_s).to eq(close_from.to_s)
    expect(supplier2.reload.close_to.to_s).to eq(close_to.to_s)
  end

  it 'only sets the closure dates for passed in suppliers' do
    date_setter = Suppliers::SetClosureDates.new(close_from: close_from, close_to: close_to, suppliers: [supplier1]).call
    expect(date_setter).to be_success

    closure_suppliers = date_setter.closure_suppliers
    expect(closure_suppliers).to_not include(supplier2)

    expect(supplier2.reload.close_from.to_s).to_not eq(close_from.to_s)
    expect(supplier2.reload.close_to.to_s).to_not eq(close_to.to_s)

    expect(closure_suppliers).to include(supplier1)
    expect(supplier1.reload.close_from.to_s).to eq(close_from.to_s)
    expect(supplier1.reload.close_to.to_s).to eq(close_to.to_s)
  end

  it 'cannot set closure dates within Yordar Closure dates', skip: 'validation put on hold for 2020' do
    if [true, false].sample
      close_from = yordar_close_from + 1.day
    else
      close_to = yordar_close_to - 1.day
    end
    date_setter = Suppliers::SetClosureDates.new(close_from: close_from, close_to: close_to).call
    expect(date_setter).to_not be_success
    expect(date_setter.errors.size).to eq(2) # per supplier
  end

end
