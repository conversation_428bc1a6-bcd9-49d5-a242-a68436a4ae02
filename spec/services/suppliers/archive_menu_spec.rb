require 'rails_helper'

RSpec.describe Suppliers::ArchiveMenu, type: :services, suppliers: true do

  let!(:supplier) { create(:supplier_profile, :random, is_searchable: true) }

  let!(:menu_section1) { create(:menu_section, :random, supplier_profile: supplier) }
  let!(:menu_section2) { create(:menu_section, :random, supplier_profile: supplier) }
  let!(:menu_section3) { create(:menu_section, :random, supplier_profile: supplier, archived_at: Time.zone.now - 20.days) }

  it 'archives all the menu sections of the supplier' do
    menu_archiver = Suppliers::ArchiveMenu.new(supplier: supplier).call

    expect(menu_archiver).to be_success
    expect(menu_archiver.archived_menu_sections).to include(menu_section1.reload, menu_section2.reload)
    expect(menu_section1.archived_at).to be_present
    expect(menu_section2.archived_at).to be_present

    expect(menu_archiver.archived_menu_sections).to_not include(menu_section3.reload) # already archived
  end

  it 'does not archive any menu sections with name as `custom`' do
    menu_section1.update_column(:name, 'custom')
    menu_archiver = Suppliers::ArchiveMenu.new(supplier: supplier).call

    expect(menu_archiver).to be_success
    expect(menu_archiver.archived_menu_sections).to_not include(menu_section1.reload)
    expect(menu_section1.archived_at).to be_blank

    expect(menu_archiver.archived_menu_sections).to include(menu_section2.reload)
    expect(menu_section2.archived_at).to be_present
  end

  it 'does not archive any menu sections with customer restrictions' do
    company = create(:company, :random)
    menu_section2.companies << company
    menu_archiver = Suppliers::ArchiveMenu.new(supplier: supplier).call

    expect(menu_archiver).to be_success
    expect(menu_archiver.archived_menu_sections).to include(menu_section1.reload)
    expect(menu_section1.archived_at).to be_present

    expect(menu_archiver.archived_menu_sections).to_not include(menu_section2.reload)
    expect(menu_section2.archived_at).to be_blank
  end

  it 'marks the supplier\'s is_searchable flag as false' do
    menu_archiver = Suppliers::ArchiveMenu.new(supplier: supplier).call

    expect(menu_archiver).to be_success

    expect(supplier.reload.is_searchable).to be_falsey
  end

  context 'menu sections with menu items' do
    let!(:menu_item11) { create(:menu_item, :random, menu_section: menu_section1, supplier_profile: supplier) }
    let!(:menu_item12) { create(:menu_item, :random, menu_section: menu_section1, supplier_profile: supplier) }

    let!(:menu_item21) { create(:menu_item, :random, menu_section: menu_section2, supplier_profile: supplier) }
    let!(:menu_item22) { create(:menu_item, :random, menu_section: menu_section2, supplier_profile: supplier) }

    it 'archives the internal non-archived menu items' do
      menu_archiver = Suppliers::ArchiveMenu.new(supplier: supplier).call

      expect(menu_archiver).to be_success
      [menu_item11, menu_item12, menu_item21, menu_item22].each do |menu_item|
        expect(menu_item.reload.archived_at).to be_present
      end
    end
  end

  context 'errors' do
    it 'cannot archive menu of a missing suppliers' do
      menu_archiver = Suppliers::ArchiveMenu.new(supplier: nil).call

      expect(menu_archiver).to_not be_success
      expect(menu_archiver.errors).to include('Cannot archive menu of a missing supplier')
    end

    it 'cannot archive a menu if the supplier does not have any un-archived items' do
      [menu_section1, menu_section2, menu_section1].each do |menu_section|
        menu_section.update_column(:archived_at, Time.zone.now)
      end
      menu_archiver = Suppliers::ArchiveMenu.new(supplier: supplier).call

      expect(menu_archiver).to_not be_success
      expect(menu_archiver.errors).to include('The supplier does not have any active menu items')
      expect(supplier.reload.is_searchable).to be_truthy # still searchable
    end
  end

  context 'as a mocked call' do
    before do
      section_archiver = double(MenuSections::Archive)
      allow(MenuSections::Archive).to receive(:new).and_return(section_archiver)
      section_archive_response = OpenStruct.new(success?: true, menu_section: 'menu_section')
      allow(section_archiver).to receive(:call).and_return(section_archive_response)
    end

    it 'makes a request to archive the menu section' do
      expect(MenuSections::Archive).to receive(:new).with(menu_section: menu_section1, is_forced: true)
      expect(MenuSections::Archive).to receive(:new).with(menu_section: menu_section2, is_forced: true)

      menu_archiver = Suppliers::ArchiveMenu.new(supplier: supplier).call

      expect(menu_archiver).to be_success
    end
  end

end
