require 'rails_helper'

RSpec.describe Suppliers::ListClosureDates, type: :service, suppliers: true, closure_dates: true do

  let!(:today) { Time.zone.today }

  let!(:supplier) { create(:supplier_profile, :random) }

  context 'supplier with internally saved closure dates' do
    before do
      supplier.update_columns(close_from: today + 20.day, close_to: today + 30.days)
      supplier.reload
    end

    it 'lists the closure period as a Chistmas Closure Period' do
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier).call

      supplier_closures = closure_retriever.closures
      expect(supplier_closures.map(&:reason)).to include('Christmas Closure Period')
      expect(supplier_closures.map(&:starts_on)).to eq([supplier.close_from.to_date])
      expect(supplier_closures.map(&:ends_on)).to eq([supplier.close_to.to_date])
    end

    it 'lists the days in between the supplier saved closure period in the next 6 months' do
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier).call

      expect(closure_retriever.closure_dates).to include(supplier.close_from.to_date)
      expect(closure_retriever.closure_dates).to include((supplier.close_from + rand(1..9).days).to_date)
      expect(closure_retriever.closure_dates).to include(supplier.close_to.to_date)
    end

    it 'does not list supplier saved closure dates if they are outside the passed in scope' do
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier, until_date: (today + 10.days)).call # from_date = today

      expect(closure_retriever.closure_dates).to be_empty
      expect(closure_retriever.closures).to be_empty
      expect(closure_retriever.closures.map(&:reason)).to_not include('Christmas Closure Period')
    end

    it 'lists days between the supplier saved closure dates if they overlap passed in times (from_date and/or until_date)' do
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier, until_date: (today + 25.days)).call

      expect(closure_retriever.closure_dates).to include(supplier.close_from.to_date)
      expect(closure_retriever.closure_dates).to include((supplier.close_from + rand(1..5).days).to_date)

      expect(closure_retriever.closure_dates).to include((supplier.close_from + rand(6..10).days).to_date)
      expect(closure_retriever.closure_dates).to include(supplier.close_to.to_date)
    end

    it 'lists days constrained between the supplier saved closure dates until the passed in date' do
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier, until_date: (today + 25.days), constrained: true).call

      expect(closure_retriever.closure_dates).to include(supplier.close_from.to_date)
      expect(closure_retriever.closure_dates).to include((supplier.close_from + rand(1..5).days).to_date)

      expect(closure_retriever.closure_dates).to_not include((supplier.close_from + rand(6..10).days).to_date)
      expect(closure_retriever.closure_dates).to_not include(supplier.close_to.to_date)
    end
  end

  context 'suppliers with delivery zones' do
    let!(:delivery_zone) { create(:delivery_zone, :random, operating_wdays: '1111011', supplier_profile: supplier) }

    before do
      supplier.update_columns(close_from: nil, close_to: nil)
    end

    it 'lists days that the supplier is not operating' do
      until_date = (today + 25.days)
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier, until_date: until_date, delivery_zone: delivery_zone).call

      all_in_between_thursdays = (today..until_date).map{|date| date.strftime('%a').downcase == 'thu' ? date : nil }.compact
      expect(closure_retriever.closure_dates).to include(*all_in_between_thursdays)

      supplier_closures = closure_retriever.closures
      expect(supplier_closures.map(&:reason)).to include('Non Operational Days')
      expect(supplier_closures.map(&:starts_on).reject(&:blank?)).to be_empty # only contains days in between
      expect(supplier_closures.map(&:ends_on).reject(&:blank?)).to be_empty # only contains days in between
      expect(supplier_closures.map(&:days_in_between).flatten).to include(*all_in_between_thursdays)
    end
  end

  context 'suppliers with closure dates' do
    let!(:supplier_closure1) { create(:supplier_closure, :random, supplier_profile: supplier, starts_at: today - 10.days, ends_at: today + 10.days) }
    let!(:supplier_closure2) { create(:supplier_closure, :random, supplier_profile: supplier, starts_at: today + 11.days, ends_at: today + 20.days) }

    before do
      supplier.update_columns(close_from: nil, close_to: nil)
    end

    it 'lists the closures that overlap passed in days' do
      until_date = (today + 5.days)
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier, until_date: until_date).call

      supplier_closures = closure_retriever.closures
      expect(supplier_closures.map(&:reason)).to include(supplier_closure1.reason)
      expect(supplier_closures.map(&:reason)).to_not include(supplier_closure2.reason)

      expect(supplier_closures.map(&:starts_on)).to include(supplier_closure1.starts_at.to_date)
      expect(supplier_closures.map(&:starts_on)).to_not include(supplier_closure2.starts_at.to_date)

      expect(supplier_closures.map(&:ends_on)).to include(supplier_closure1.ends_at.to_date)
      expect(supplier_closures.map(&:ends_on)).to_not include(supplier_closure2.ends_at.to_date)
    end

    it 'lists days that the supplier has closures overlapping passed in date' do
      until_date = (today + 5.days)
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier, until_date: until_date).call

      expect(closure_retriever.closure_dates).to include(supplier_closure1.starts_at.to_date)
      expect(closure_retriever.closure_dates).to include((today + rand(1..9).days).to_date)
      expect(closure_retriever.closure_dates).to include(supplier_closure1.ends_at.to_date)

      expect(closure_retriever.closure_dates).to_not include(supplier_closure2.starts_at.to_date)
      expect(closure_retriever.closure_dates).to_not include(supplier_closure2.ends_at.to_date)
    end

    it 'lists days that the supplier has closures constrained to passed in dates' do
      from_date  = today + 1.day
      until_date = (today + 5.days)
      closure_retriever = Suppliers::ListClosureDates.new(supplier: supplier, from_date: from_date, until_date: until_date, constrained: true).call

      expect(closure_retriever.closure_dates).to_not include(supplier_closure1.starts_at.to_date)
      expect(closure_retriever.closure_dates).to include(from_date)
      expect(closure_retriever.closure_dates).to include((today + rand(1..5).days).to_date)
      expect(closure_retriever.closure_dates).to_not include(supplier_closure1.ends_at.to_date)
    end

  end

end

