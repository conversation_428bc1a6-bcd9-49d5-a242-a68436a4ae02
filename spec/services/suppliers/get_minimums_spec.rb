require 'rails_helper'

RSpec.describe Suppliers::GetMinimums, type: :service, suppliers: true, minimums: true do

  let(:supplier1) { create(:supplier_profile, :random) }
  let(:supplier2) { create(:supplier_profile, :random) }
  let(:supplier3) { create(:supplier_profile, :random) }

  it 'returns an empty hash for no suppliers' do
    supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: []).call
    expect(supplier_mapped_minimums).to be_blank
  end

  it 'returns an empty hash if no minimums are present' do
    supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3]).call
    expect(supplier_mapped_minimums).to be_blank
  end

  context 'with minimums' do

    let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, spend_price: 10, lead_time: 24, lead_time_day_before: nil) }
    let!(:minimum12) { create(:minimum, :random, supplier_profile: supplier1, spend_price: 20, lead_time: 48, lead_time_day_before: nil) }

    let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, spend_price: 10, lead_time: nil, lead_time_day_before: '14:00') }
    let!(:minimum22) { create(:minimum, :random, supplier_profile: supplier2, spend_price: 15, lead_time: 24,  lead_time_day_before: nil) }

    let!(:minimum31) { create(:minimum, :random, supplier_profile: supplier3, spend_price: 10, lead_time: nil, lead_time_day_before: '12:00') }
    let!(:minimum32) { create(:minimum, :random, supplier_profile: supplier3, spend_price: 30, lead_time: nil, lead_time_day_before: '16:00') }

    it 'returns a hash with suppliers as keys' do
      supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3]).call

      expect(supplier_mapped_minimums).to be_present
      expect(supplier_mapped_minimums.keys).to include(supplier1, supplier2, supplier3)
    end

    it 'returns the maximum spend price (per supplier) saved in minimum_spend' do
      supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3]).call
      expect(supplier_mapped_minimums[supplier1].minimum_spend).to eq(minimum12.spend_price)
      expect(supplier_mapped_minimums[supplier2].minimum_spend).to eq(minimum22.spend_price)
      expect(supplier_mapped_minimums[supplier3].minimum_spend).to eq(minimum32.spend_price)
    end

    context 'with suppliers with lead time by hour' do
      let(:supplier1) { create(:supplier_profile, :random, lead_mode: 'by_hour') }
      let(:supplier2) { create(:supplier_profile, :random, lead_mode: 'by_hour') }
      let(:supplier3) { create(:supplier_profile, :random, lead_mode: 'by_hour') }

      it 'returns the maximum lead time value (per supplier) saved in lead_time' do
        supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3]).call

        expect(supplier_mapped_minimums[supplier1].lead_time).to eq('48 hrs')
        expect(supplier_mapped_minimums[supplier2].lead_time).to eq('24 hrs')
        expect(supplier_mapped_minimums[supplier3].lead_time).to eq('Unknown')
      end
    end

    context 'with suppliers with lead time by_day_before' do
      let(:supplier1) { create(:supplier_profile, :random, lead_mode: 'by_day_before') }
      let(:supplier2) { create(:supplier_profile, :random, lead_mode: 'by_day_before') }
      let(:supplier3) { create(:supplier_profile, :random, lead_mode: 'by_day_before') }

      it 'returns the minimum lead time day value (per supplier) saved in lead_time' do
        supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3]).call

        expect(supplier_mapped_minimums[supplier1].lead_time).to eq('Unknown')
        expect(supplier_mapped_minimums[supplier2].lead_time).to eq('02:00pm (day prior)')
        expect(supplier_mapped_minimums[supplier3].lead_time).to eq('12:00pm (day prior)')
      end
    end

    context 'with categories' do
      let!(:catering_category1) { create(:category, :random, group: 'catering-services') }
      let!(:catering_category2) { create(:category, :random, group: 'catering-services') }
      let!(:kitchen_supplies_category) { create(:category, :random, group: 'kitchen-supplies') }
      let!(:home_delivery_category) { create(:category, :random, group: 'home-deliveries') }
      let!(:catering_category3) { create(:category, :random, group: 'catering-services') }

      before do
        minimum11.update_column(:category_id, catering_category1.id)
        minimum12.update_column(:category_id, kitchen_supplies_category.id)

        minimum21.update_column(:category_id, catering_category2.id)
        minimum22.update_column(:category_id, kitchen_supplies_category.id)

        minimum31.update_column(:category_id, home_delivery_category.id)
        minimum32.update_column(:category_id, catering_category2.id)
      end

      it 'gets minimum (spend) scoped to the category group' do
        catering_supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3], category_group: 'catering-services').call
        expect(catering_supplier_mapped_minimums[supplier1].minimum_spend).to eq(minimum11.spend_price)
        expect(catering_supplier_mapped_minimums[supplier2].minimum_spend).to eq(minimum21.spend_price)
        expect(catering_supplier_mapped_minimums[supplier3].minimum_spend).to eq(minimum32.spend_price)

        kitchen_supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3], category_group: 'kitchen-supplies').call
        expect(kitchen_supplier_mapped_minimums[supplier1].minimum_spend).to eq(minimum12.spend_price)
        expect(kitchen_supplier_mapped_minimums[supplier2].minimum_spend).to eq(minimum22.spend_price)
        expect(kitchen_supplier_mapped_minimums[supplier3]).to be_blank

        home_delivery_supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3], category_group: 'home-deliveries').call
        expect(home_delivery_supplier_mapped_minimums[supplier1]).to be_blank
        expect(home_delivery_supplier_mapped_minimums[supplier2]).to be_blank
        expect(home_delivery_supplier_mapped_minimums[supplier3].minimum_spend).to eq(minimum31.spend_price)
      end

      it 'gets minimum (spend) scoped to the category' do
        catering_category1_supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3], categories: [catering_category1]).call
        expect(catering_category1_supplier_mapped_minimums[supplier1].minimum_spend).to eq(minimum11.spend_price)
        expect(catering_category1_supplier_mapped_minimums[supplier2]).to be_blank
        expect(catering_category1_supplier_mapped_minimums[supplier3]).to be_blank

        catering_category2_supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3], categories: [catering_category2]).call
        expect(catering_category2_supplier_mapped_minimums[supplier1]).to be_blank
        expect(catering_category2_supplier_mapped_minimums[supplier2].minimum_spend).to eq(minimum21.spend_price)
        expect(catering_category2_supplier_mapped_minimums[supplier3].minimum_spend).to eq(minimum32.spend_price)

        kitchen_supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3], categories: [kitchen_supplies_category]).call
        expect(kitchen_supplier_mapped_minimums[supplier1].minimum_spend).to eq(minimum12.spend_price)
        expect(kitchen_supplier_mapped_minimums[supplier2].minimum_spend).to eq(minimum22.spend_price)
        expect(kitchen_supplier_mapped_minimums[supplier3]).to be_blank

        home_delivery_supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3], categories: [home_delivery_category]).call
        expect(home_delivery_supplier_mapped_minimums[supplier1]).to be_blank
        expect(home_delivery_supplier_mapped_minimums[supplier2]).to be_blank
        expect(home_delivery_supplier_mapped_minimums[supplier3].minimum_spend).to eq(minimum31.spend_price)
      end

      it 'gets minimum (spend) scoped to the categories\' group' do
        catering_category1_supplier_mapped_minimums = Suppliers::GetMinimums.new(suppliers: [supplier1, supplier2, supplier3], categories: [catering_category3]).call
        expect(catering_category1_supplier_mapped_minimums[supplier1].minimum_spend).to eq(minimum11.spend_price)
        expect(catering_category1_supplier_mapped_minimums[supplier2].minimum_spend).to eq(minimum21.spend_price)
        expect(catering_category1_supplier_mapped_minimums[supplier3].minimum_spend).to eq(minimum32.spend_price)
      end
    end
  end

end
