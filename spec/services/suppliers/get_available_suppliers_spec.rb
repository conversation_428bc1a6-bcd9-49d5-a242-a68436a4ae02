require 'rails_helper'

RSpec.describe Suppliers::GetAvailableSuppliers, type: :service, suppliers: true do

  let!(:delivery_date) { Time.zone.now.beginning_of_week + 1.week + 2.days + 12.hours }

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:supplier3) { create(:supplier_profile, :random) }
  let!(:supplier4) { create(:supplier_profile, :random) }
  let(:supplier_ids) { [supplier1, supplier2, supplier3, supplier4].map(&:id) }

  let!(:pyrmont) { create(:suburb, :pyrmont) }
  let!(:melbourne) { create(:suburb, :melbourne) }
  let!(:delivery_zone1) { create(:delivery_zone, :random, radius: 10, operating_wdays: '1111111', supplier_profile: supplier1, suburb: pyrmont) }
  let!(:delivery_zone2) { create(:delivery_zone, :random, radius: 10, operating_wdays: '1111111', supplier_profile: supplier2, suburb: pyrmont) }
  let!(:delivery_zone3) { create(:delivery_zone, :random, radius: 10, operating_wdays: '1111111', supplier_profile: supplier3, suburb: pyrmont) }
  let!(:delivery_zone4) { create(:delivery_zone, :random, radius: 10, operating_wdays: '1111111', supplier_profile: supplier4, suburb: melbourne) }

  before do
    DeliveryZone.all.each do |delivery_zone|
      DeliveryZones::SaveDeliverableSuburbs.new(delivery_zone: delivery_zone, refresh: true).call
    end
  end

  it 'returns suppliers available within the suburb' do
    supplier_retriever = Suppliers::GetAvailableSuppliers.new(supplier_ids: supplier_ids, delivery_date: delivery_date, suburb: pyrmont).call

    expect(supplier_retriever.available_suppliers).to include(supplier1, supplier2, supplier3)
    expect(supplier_retriever.available_suppliers).to_not include(supplier4)
  end

  it 'filters out suppliers not operating on the delivery_date\'s day' do
    non_delivery_day_operation = 7.times.map{|num| num == delivery_date.wday ? 0 : 1 }.join('')
    delivery_zone1.update_column(:operating_wdays, non_delivery_day_operation)

    specific_delivery_day_operation = 7.times.map{|num| num == delivery_date.wday ? 1 : 0 }.join('')
    delivery_zone2.update_column(:operating_wdays, specific_delivery_day_operation)
    supplier_retriever = Suppliers::GetAvailableSuppliers.new(supplier_ids: supplier_ids, delivery_date: delivery_date, suburb: pyrmont).call

    expect(supplier_retriever.available_suppliers).to_not include(supplier1) # not operating on delivery date wday
    expect(supplier_retriever.available_suppliers).to include(supplier2, supplier3)

    expect(supplier_retriever.available_suppliers).to_not include(supplier4) # not operating in suburb
  end

  it 'doesn\'t return suppliers that are not passed' do
    supplier_retriever = Suppliers::GetAvailableSuppliers.new(supplier_ids: [supplier2].map(&:id), delivery_date: delivery_date, suburb: pyrmont).call

    expect(supplier_retriever.available_suppliers).to include(supplier2)
    expect(supplier_retriever.available_suppliers).to_not include(supplier1, supplier3)
  end

  it 'returns blank'

  context 'with supplier minimums' do
    let!(:minimum1) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 22, lead_time_day_before: '') }
    let!(:minimum2) { create(:minimum, :random, supplier_profile: supplier2, lead_time: nil, lead_time_day_before: '10:00') }

    it 'returns suppliers available within the cutoff period' do
      supplier_retriever = Suppliers::GetAvailableSuppliers.new(supplier_ids: supplier_ids, delivery_date: delivery_date, suburb: pyrmont).call

      expect(supplier_retriever.available_suppliers).to include(supplier1, supplier2, supplier3)
      expect(supplier_retriever.available_suppliers).to_not include(supplier4)
    end

    it 'filters out supplier not within the cutoff lead time' do
      minimum1.update_column(:lead_time, 336) # 2 weeks lead time

      supplier_retriever = Suppliers::GetAvailableSuppliers.new(supplier_ids: supplier_ids, delivery_date: delivery_date, suburb: pyrmont).call

      expect(supplier_retriever.available_suppliers).to_not include(supplier1)
      expect(supplier_retriever.available_suppliers).to include(supplier2, supplier3)
    end
  end

end
