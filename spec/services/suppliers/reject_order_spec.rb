require 'rails_helper'

RSpec.describe Suppliers::RejectOrder, type: :service, orders: true, suppliers: true do

  let!(:order) { create(:order, :new) }

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier, status: 'pending') }

  before do
    # mock email sender
    email_sender = delayed_email_sender = double(Orders::Emails::SendOrderRejectedAdminEmail)
    allow(Orders::Emails::SendOrderRejectedAdminEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'rejects all the supplier order lines for the order' do
    order_rejecter = Suppliers::RejectOrder.new(supplier: supplier, order: order).call

    expect(order_rejecter).to be_success
    supplier_order_lines = order.reload.order_lines.where(supplier_profile: supplier)

    expect(supplier_order_lines.map(&:status).uniq).to match_array(['rejected'])
  end

  it 'cancels the order', skip: 'Needs further clarification' do
    order_rejecter = Suppliers::RejectOrder.new(supplier: supplier, order: order).call

    expect(order_rejecter).to be_success
    rejected_order = order_rejecter.order

    expect(rejected_order.id).to eq(order.id)
    expect(rejected_order.status).to eq('cancelled')
  end

  it 'sends a rejection email to the admin' do
    expect(Orders::Emails::SendOrderRejectedAdminEmail).to receive(:new).with(order: order, supplier: supplier)

    order_rejecter = Suppliers::RejectOrder.new(supplier: supplier, order: order).call
    expect(order_rejecter).to be_success
  end

  it 'logs a `Order Rejected` event (along with supplier info)', event_logs: true do
    expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'order-rejected', severity: 'warning', supplier: supplier.name)

    order_rejecter = Suppliers::RejectOrder.new(supplier: supplier, order: order).call
    expect(order_rejecter).to be_success
  end

  context 'with multiple order suppliers' do
    let!(:supplier2) { create(:supplier_profile, :random) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2, status: 'pending') }

    it 'only rejects the specified supplier\'s order lines for the order' do
      order_rejecter = Suppliers::RejectOrder.new(supplier: supplier, order: order).call

      expect(order_rejecter).to be_success
      supplier_order_lines = order.reload.order_lines.where(supplier_profile: supplier)
      expect(supplier_order_lines.map(&:status).uniq).to match_array(['rejected'])

      supplier2_order_lines = order.reload.order_lines.where(supplier_profile: supplier2)
      expect(supplier2_order_lines.map(&:status)).to_not include('rejected')
    end

    it 'doesn\'t cancel the order because of pending order lines from another supplier but returns as success' do
      order_rejecter = Suppliers::RejectOrder.new(supplier: supplier, order: order).call

      expect(order_rejecter).to be_success
      expect(order_rejecter.warnings).to include('Cannot cancel order because of pending order lines from other supplier(s) within the order')
      expect(order.reload.status).to_not eq('cancelled')
    end

    it 'cancels the order if all order lines are rejected', skip: 'Needs further clarification' do
      order_line2.update_column(:status, 'rejected')

      order_rejecter = Suppliers::RejectOrder.new(supplier: supplier, order: order).call

      expect(order_rejecter).to be_success
      rejected_order = order_rejecter.order

      expect(rejected_order.id).to eq(order.id)
      expect(rejected_order.status).to eq('cancelled')
    end
  end

  context 'errors' do
    it 'cannot reject a missing order' do
      order_rejecter = Suppliers::RejectOrder.new(supplier: supplier, order: nil).call

      expect(order_rejecter).to_not be_success
      expect(order_rejecter.errors).to include('Cannot reject a missing order')
    end

    it 'cannot reject an order without a supplier' do
      order_rejecter = Suppliers::RejectOrder.new(supplier: nil, order: order).call

      expect(order_rejecter).to_not be_success
      expect(order_rejecter.errors).to include('You don\'t have access to this order')
    end

    it 'cannot reject order not belonging to the supplier' do
      supplier2 = create(:supplier_profile, :random)
      order_rejecter = Suppliers::RejectOrder.new(supplier: supplier2, order: order).call

      expect(order_rejecter).to_not be_success
      expect(order_rejecter.errors).to include('You don\'t have access to this order')
    end
  end

end
