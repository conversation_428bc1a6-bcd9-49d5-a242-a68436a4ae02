require 'rails_helper'

RSpec.describe Suppliers::FetchOrderSummary, type: :service, document: true, orders: true, suppliers: true do

  let!(:today) { Time.zone.now.beginning_of_day + rand(1..20).hours }
  let!(:summary_day) { today + 2.days }

  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:order1) { create(:order, :confirmed, name: 'order1', delivery_at: summary_day) }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier, status: 'accepted') }

  let!(:order2) { create(:order, :confirmed, name: 'order2', delivery_at: summary_day) }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier, status: 'accepted') }

  let!(:order3) { create(:order, :confirmed, name: 'order3', delivery_at: summary_day) }
  let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier, status: 'accepted') }

  let!(:generated_document) { create(:document, :random, name: 'generated-document', documentable: create(:supplier_profile, :random), kind: 'supplier_order_summary') }

  before do
    document_generator = double(Documents::Generate::SupplierOrderSummary)
    allow(Documents::Generate::SupplierOrderSummary).to receive(:new).and_return(document_generator)
    allow(document_generator).to receive(:call).and_return(generated_document)
  end

  it 'request the supplier summary document to be generated for the passed in supplier and summary day' do
    expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: anything, summary_type: anything)

    summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
    expect(summary_fetcher).to be_success
  end

  it 'returns the generated document' do
    summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call

    expect(summary_fetcher).to be_success
    expect(summary_fetcher.document).to eq(generated_document)
  end

  it 'generates the morning summary for days in future' do
    expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, summary_type: 'morning', order_lines: anything)

    summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
    expect(summary_fetcher).to be_success
  end

  context 'order (lines) data' do
    it 'generates the summary with order lines for the passed in summary day' do
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line21, order_line31], summary_type: anything)

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end

    it 'does not pass order lines belonging to a different supplier' do
      supplier2 = create(:supplier_profile, :random)
      order_line21.update_column(:supplier_profile_id, supplier2.id)

      expect(Documents::Generate::SupplierOrderSummary).to_not receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line21, order_line31], summary_type: anything)
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line31], summary_type: anything) # not containing order_line21

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end

    it 'does not pass order lines for order delivered on a different date' do
      order3.update_column(:delivery_at, summary_day + 2.days)
      expect(Documents::Generate::SupplierOrderSummary).to_not receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line21, order_line31], summary_type: anything)
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line21], summary_type: anything)  # not containing order_lines for order 3

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end

    it 'does not pass data of potentially confirmed orders' do
      order2.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[new amended confirmed delivered]).sample)
      expect(Documents::Generate::SupplierOrderSummary).to_not receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line21, order_line31], summary_type: anything)
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, summary_type: 'morning', order_lines: [order_line11, order_line31]) # not containing order lines for order 2

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end

    it 'does not pass data of rejected order lines' do
      order_line11.update_column(:status, 'rejected')
      expect(Documents::Generate::SupplierOrderSummary).to_not receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line21, order_line31], summary_type: anything)
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, summary_type: 'morning', order_lines: [order_line21, order_line31]) # not containing order_line11

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end
  end # order (lines) data

  context 'daily summary' do
    let!(:summary_day) { [today, (today - 3.days)].sample }

    before do
      [order1, order2, order3].each do |order|
        order.update_column(:delivery_at, summary_day)
      end
    end

    it 'generates the daily summary for current or past days' do
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, summary_type: 'daily', order_lines: anything)

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end

    it 'only passes data confirmed or delivered orders' do
      order1.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[confirmed delivered]).sample)
      expect(Documents::Generate::SupplierOrderSummary).to_not receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line21, order_line31], summary_type: anything)
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, summary_type: 'daily', order_lines: [order_line21, order_line31]) # not containing order_lines for order1

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end

    it 'does not pass data of non-accepted order lines' do
      order_line21.update_column(:status, %w[pending amended notified].sample)
      expect(Documents::Generate::SupplierOrderSummary).to_not receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: [order_line11, order_line21, order_line31], summary_type: anything)
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, summary_type: 'daily', order_lines: [order_line11, order_line31]) # not containing order_line21

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end
  end # daily summary type

  context 'with an existing document' do
    let!(:summary_document1) { create(:document, :random, kind: 'supplier_order_summary', name: "supplier-orders-summary-#{(summary_day - 1.day).strftime('%Y%m%d')}-#{supplier.id}-morning", documentable: supplier, version: 5) }
    let!(:summary_document2) { create(:document, :random, kind: 'supplier_order_summary', name: "supplier-orders-summary-#{summary_day.strftime('%Y%m%d')}-#{supplier.id}-morning", documentable: supplier, version: 2) }
    let!(:summary_document3) { create(:document, :random, kind: 'supplier_order_summary', name: "supplier-orders-summary-#{summary_day.strftime('%Y%m%d')}-#{supplier.id}-morning", documentable: supplier, version: 1) }

    it 'returns the latest document with name containing the summary day' do
      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call

      expect(summary_fetcher).to be_success
      expect(summary_fetcher.document).to eq(summary_document2)
    end

    it 'does not request the summary document to be generated' do
      expect(Documents::Generate::SupplierOrderSummary).to_not receive(:new)

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call
      expect(summary_fetcher).to be_success
    end

    it 'request the supplier summary document to be generated if regenerate is true even if a document exists' do
      expect(Documents::Generate::SupplierOrderSummary).to receive(:new).with(supplier: supplier, summary_day: summary_day, order_lines: anything, summary_type: anything)

      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day, regenerate: true).call
      expect(summary_fetcher).to be_success
    end
  end # existing document

  context 'errors' do
    it 'returns with errors if the supplier is missing' do
      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: nil, summary_day: summary_day).call

      expect(summary_fetcher).to_not be_success
      expect(summary_fetcher.errors).to include('Cannot generate summary for a missing supplier')
    end

    it 'returns with errors if the supplier is missing' do
      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: nil).call

      expect(summary_fetcher).to_not be_success
      expect(summary_fetcher.errors).to include('Cannot generate summary for a missing day')
    end

    it 'returns with errors if the supplier does not have any orders for the summary day' do
      [order1, order2, order3].each{|order| order.update_column(:delivery_at, summary_day + 1.day) }
      summary_fetcher = Suppliers::FetchOrderSummary.new(supplier: supplier, summary_day: summary_day).call

      expect(summary_fetcher).to_not be_success
      expect(summary_fetcher.errors).to include('Supplier does not have any orders on the summary day')
    end
  end

end
