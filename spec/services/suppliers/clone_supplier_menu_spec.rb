require 'rails_helper'

RSpec.describe Suppliers::CloneSupplierMenu, type: :serice, suppliers: true do

  subject { Suppliers::CloneSupplierMenu.new(old_supplier: old_supplier, new_supplier: new_supplier).call }

  let!(:old_supplier) { create(:supplier_profile, :random) }
  let!(:new_supplier) { create(:supplier_profile, :random) }

  let!(:menu_section1) { create(:menu_section, :random, supplier_profile: old_supplier) }
  let!(:menu_section2) { create(:menu_section, :random, supplier_profile: old_supplier) }

  it 'clones the menu_sections' do
    subject

    cloned_menu_sections = new_supplier.menu_sections
    expect(cloned_menu_sections.size).to eq(2)
    expect(cloned_menu_sections.map(&:name)).to include(*[menu_section1, menu_section2].map(&:name))
  end

  it 'does not clone a menu section belonging to a different supplier' do
    supplier2 = create(:supplier_profile, :random)
    menu_section1.update_column(:supplier_profile_id, supplier2.id)
    subject

    cloned_menu_sections = new_supplier.menu_sections
    expect(cloned_menu_sections.map(&:name)).to_not include(menu_section1.name)
    expect(cloned_menu_sections.map(&:name)).to include(menu_section2.name)
  end

  it 'does not clone an archived menu sections' do
    menu_section2.update_column(:archived_at, Time.zone.now)
    subject

    cloned_menu_sections = new_supplier.menu_sections
    expect(cloned_menu_sections.map(&:name)).to include(menu_section1.name)
    expect(cloned_menu_sections.map(&:name)).to_not include(menu_section2.name)
  end

  it 'does not clone a custom menu sections' do
    menu_section1.update_column(:name, 'custom')
    subject

    cloned_menu_sections = new_supplier.menu_sections
    expect(cloned_menu_sections.map(&:name)).to_not include(menu_section1.name)
    expect(cloned_menu_sections.map(&:name)).to include(menu_section2.name)
  end

  context 'with category menu sections' do
    let!(:category_menu_section1) { create(:category_menu_section, :random, menu_section: menu_section1) }
    let!(:category_menu_section2) { create(:category_menu_section, :random, menu_section: menu_section2) }

    it 'clones the associated category menu sections' do
      subject

      cloned_category_sections = new_supplier.menu_sections.map(&:category_menu_sections).flatten

      expect(cloned_category_sections.size).to eq(2)
      expect(cloned_category_sections.map{|cms| cms.menu_section.name }).to include(menu_section1.name, menu_section2.name)
      expect(cloned_category_sections.map{|cms| cms.menu_section.id }).to_not include(menu_section1.id, menu_section2.id)
    end
  end

  context 'with menu items' do
    let!(:menu_item1) { create(:menu_item, :random, supplier_profile: old_supplier, menu_section: menu_section1) }
    let!(:menu_item2) { create(:menu_item, :random, supplier_profile: old_supplier, menu_section: menu_section2) }

    it 'clones the menu_items' do
      subject

      cloned_menu_items = new_supplier.menu_items
      expect(cloned_menu_items.size).to eq(2)
      expect(cloned_menu_items.map(&:name)).to include(*[menu_item1, menu_item2].map(&:name))
    end

    it 'does not clone a menu_items belonging to a different supplier' do
      supplier2 = create(:supplier_profile, :random)
      menu_item1.update_column(:supplier_profile_id, supplier2.id)
      subject

      cloned_menu_items = new_supplier.menu_items
      expect(cloned_menu_items.map(&:name)).to_not include(menu_item1.name)
      expect(cloned_menu_items.map(&:name)).to include(menu_item2.name)
    end

    it 'does not clone archived menu_items' do
      menu_item2.update_column(:archived_at, Time.zone.now)
      subject

      cloned_menu_items = new_supplier.menu_items
      expect(cloned_menu_items.map(&:name)).to include(menu_item1.name)
      expect(cloned_menu_items.map(&:name)).to_not include(menu_item2.name)
    end

    context 'with serving sizes' do
      let!(:serving_size1) { create(:serving_size, :random, menu_item: menu_item1) }
      let!(:serving_size2) { create(:serving_size, :random, menu_item: menu_item2) }

      it 'clones the serving_sizes' do
        subject

        cloned_serving_sizes = new_supplier.menu_items.map(&:serving_sizes).flatten
        expect(cloned_serving_sizes.size).to eq(2)
        expect(cloned_serving_sizes.map(&:name)).to include(*[serving_size1, serving_size2].map(&:name))
      end

      it 'does not clone archived serving_sizes' do
        serving_size2.update_column(:archived_at, Time.zone.now)
        subject

        cloned_serving_sizes = new_supplier.menu_items.map(&:serving_sizes).flatten
        expect(cloned_serving_sizes.map(&:name)).to include(serving_size1.name)
        expect(cloned_serving_sizes.map(&:name)).to_not include(serving_size2.name)
      end
    end

    context 'with menu extra sections and menu extras' do
      let!(:menu_extra_section1) { create(:menu_extra_section, :random, menu_item: menu_item1) }
      let!(:menu_extra11) { create(:menu_extra, :random, menu_extra_section: menu_extra_section1, menu_item: menu_item1) }
      let!(:menu_extra12) { create(:menu_extra, :random, menu_extra_section: menu_extra_section1, menu_item: menu_item1) }

      let!(:menu_extra_section2) { create(:menu_extra_section, :random, menu_item: menu_item2) }
      let!(:menu_extra21) { create(:menu_extra, :random, menu_extra_section: menu_extra_section2, menu_item: menu_item2) }
      let!(:menu_extra22) { create(:menu_extra, :random, menu_extra_section: menu_extra_section2, menu_item: menu_item2) }

      it 'clones the menu_extra section and underlying extras' do
        subject

        cloned_menu_extra_sections = new_supplier.menu_items.map(&:menu_extra_sections).flatten
        expect(cloned_menu_extra_sections.size).to eq(2)
        expect(cloned_menu_extra_sections.map(&:name)).to include(*[menu_extra_section1, menu_extra_section2].map(&:name))
        expect(cloned_menu_extra_sections.map(&:id)).to_not include(*[menu_extra_section1, menu_extra_section2].map(&:id))

        cloned_menu_extras = new_supplier.menu_items.map(&:menu_extras).flatten
        expect(cloned_menu_extras.size).to eq(4)
        expect(cloned_menu_extras.map(&:name)).to include(*[menu_extra11, menu_extra12, menu_extra21, menu_extra22].map(&:name))
        expect(cloned_menu_extras.map(&:id)).to_not include(*[menu_extra11, menu_extra12, menu_extra21, menu_extra22].map(&:id))
      end

      it 'does not clone archived menu_extra_sections and any archived menu extras' do
        menu_extra12.update_column(:archived_at, Time.zone.now)
        menu_extra_section2.update_column(:archived_at, Time.zone.now)

        subject

        cloned_menu_extra_sections = new_supplier.menu_items.map(&:menu_extra_sections).flatten
        expect(cloned_menu_extra_sections.map(&:name)).to_not include(menu_extra_section2.name)
        expect(cloned_menu_extra_sections.map(&:name)).to include(menu_extra_section1.name)

        cloned_menu_extras = new_supplier.menu_items.map(&:menu_extras).flatten
        expect(cloned_menu_extras.map(&:name)).to_not include(*[menu_extra12, menu_extra21, menu_extra22].map(&:name))
        expect(cloned_menu_extras.map(&:name)).to include(menu_extra11.name)
      end
    end # menu extras
  end # with menu items

end
