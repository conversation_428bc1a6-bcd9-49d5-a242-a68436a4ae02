require 'rails_helper'

RSpec.describe Suppliers::FetchPotentialDeliveryZone, type: :service, deliverable_suburb: true do

  let(:supplier) { create(:supplier_profile, :random) }

  let!(:suburb1) { create(:suburb, :random, name: 'suburb1') }
  let!(:suburb2) { create(:suburb, :random, name: 'suburb2') }
  let!(:suburb3) { create(:suburb, :random, name: 'suburb3') }
  let!(:suburb4) { create(:suburb, :random, name: 'suburb4') }

  let!(:delivery_zone1) { create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb1, delivery_fee: 10, radius: 20, operating_wdays: '1111111') }

  let!(:deliverable_suburb11) { create(:deliverable_suburb, supplier_profile: supplier, suburb: suburb1, delivery_zone: delivery_zone1, distance: 0) }
  let!(:deliverable_suburb12) { create(:deliverable_suburb, supplier_profile: supplier, suburb: suburb2, delivery_zone: delivery_zone1, distance: 25) }

  let!(:delivery_zone2) { create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb3, delivery_fee: 30, radius: 40, operating_wdays: '0111110') }

  let!(:deliverable_suburb21) { create(:deliverable_suburb, supplier_profile: supplier, suburb: suburb3, delivery_zone: delivery_zone2, distance: 0) }
  let!(:deliverable_suburb22) { create(:deliverable_suburb, supplier_profile: supplier, suburb: suburb2, delivery_zone: delivery_zone2, distance: 10) }

  context 'delivery zones with exact suburb match' do
    it 'returns the delivery zone with the exact suburb match' do
      delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb1).call

      expect(delivery_zone).to eq(delivery_zone1)
    end

    it 'return the delivery zone with the minimums radius and minimums fee' do
      delivery_zone3 = create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb1, delivery_fee: 30, radius: 10) # less radius than original + more delivery fee
      delivery_zone4 = create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb1, delivery_fee: 20, radius: 10) # less radius than original + less delivery fee
      delivery_zone5 = create(:delivery_zone, :random, supplier_profile: supplier, suburb: suburb1, delivery_fee: 5, radius: 20) # same radius as original + less delivery fee

      delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb1).call

      expect(delivery_zone).to eq(delivery_zone4)
      expect(delivery_zone).to_not eq(delivery_zone3)
      expect(delivery_zone).to_not eq(delivery_zone5)
    end
  end

  context 'delivery zone with exact suburb not found' do
    it 'returns the nearest delivery zone (based on distance)' do
      delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb2).call

      expect(delivery_zone).to eq(delivery_zone2)
    end

    context 'with multiple delivery_zones with same minimum distance from suburb' do
      before do
        deliverable_suburb12.update_column(:distance, 10)
      end

      it 'returns the nearest delivery zone (based on distance) with the least delivery fee' do
        delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb2).call

        expect(delivery_zone).to eq(delivery_zone1)
      end

      it 'returns the nearest delivery zone (based on distance and radius) with the least delivery fee' do
        delivery_zone2.update_column(:radius, 5) # delivery zone2 has a smaller radius
        delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb2).call

        expect(delivery_zone).to eq(delivery_zone2)
      end
    end
  end

  it 'returns the no delivery zones if the suburb does not have any deliverable suburbs' do
    delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb4).call

    expect(delivery_zone).to eq(nil)
  end

  context 'with passed in delivery date' do
    let!(:beginning_of_week) { Time.zone.now.beginning_of_week }
    let!(:weekend) { beginning_of_week + [5, 6].sample.days }
    let!(:weekday) { beginning_of_week + [0, 1, 2, 3, 4].sample.days }

    it 'returns the delivery zone which operates on the given delivery date\'s week day - weekend test' do
      delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb2, delivery_date: weekend).call

      expect(delivery_zone).to eq(delivery_zone1)
    end

    it 'returns the delivery zone with exact match which operates on the given delivery date\'s week day - weekday test' do
      delivery_zone2.update_column(:suburb_id, suburb2.id) # mark delivery zone with exact suburb match
      deliverable_suburb22.update_column(:distance, 100) # mark delivery zone as far away (for testing purposes)
      delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb2, delivery_date: weekday).call

      expect(delivery_zone).to eq(delivery_zone2)
    end

    it 'returns the nearest delivery zone which operates on the given delivery date\'s week day - weekday test' do
      delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb2, delivery_date: weekday).call

      expect(delivery_zone).to eq(delivery_zone2)
    end

    it 'returns the nearest delivery zone which operates on the given delivery date\'s week day with lowest delivery fee - weekday test' do
      deliverable_suburb12.update_column(:distance, 10)
      delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb2, delivery_date: weekday).call

      expect(delivery_zone).to eq(delivery_zone1)
    end

    context 'with delivery zones with no operating days' do
      before do
        [delivery_zone1, delivery_zone2].each do |delivery_zone|
          delivery_zone.update_column(:operating_wdays, '0000000')
        end
      end

      it 'returns exact match suburb even if it cannot find delivery zone with active operating days ' do
        delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb1, delivery_date: [weekday, weekend].sample).call

        expect(delivery_zone).to eq(delivery_zone1)
      end

      it 'returns nearest delivery zone if it cannot find delivery zone with active operating days or an exact match ' do
        delivery_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: suburb2, delivery_date: [weekday, weekend].sample).call

        expect(delivery_zone).to eq(delivery_zone2)
      end
    end # delivery zones with no operating days
  end # delivery date filter

end
