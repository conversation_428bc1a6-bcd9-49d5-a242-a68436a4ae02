require 'rails_helper'

RSpec.describe Suppliers::UpdateFutureOrders, type: :service, suppliers: true, orders: true do

  let!(:today) { Time.zone.now.beginning_of_day }

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }

  let!(:order1) { create(:order, :confirmed, delivery_at: today + 1.days + 10.hours) }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1)}
  let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier1)}

  let!(:order2) { create(:order, :confirmed, delivery_at: today + 3.days + 8.hours) }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1)}
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2)}

  before do
    # mock order line update
    order_line_updater = double(OrderLines::Upsert)
    allow(OrderLines::Upsert).to receive(:new).and_return(order_line_updater)
    allow(order_line_updater).to receive(:call).and_return(true)

    # mock customer totals calculator
    customer_totals_updater = double(Orders::CalculateCustomerTotals)
    allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(customer_totals_updater)
    allow(customer_totals_updater).to receive(:call).and_return(true)

    # mock supplier totals calculator
    supplier_totals_updater = double(Orders::CalculateSupplierTotals)
    allow(Orders::CalculateSupplierTotals).to receive(:new).and_return(supplier_totals_updater)
    allow(supplier_totals_updater).to receive(:call).and_return(true)
  end

  it 'makes a request to update the order lines of all future orders for the passed in supplier' do
    expect(OrderLines::Upsert).to receive(:new).with(order: order1, customer: order1.customer_profile, location: order_line11.location, order_line_params: { id: order_line11.id }, update_item: true)
    expect(OrderLines::Upsert).to receive(:new).with(order: order1, customer: order1.customer_profile, location: order_line12.location, order_line_params: { id: order_line12.id }, update_item: true)
    expect(OrderLines::Upsert).to receive(:new).with(order: order2, customer: order2.customer_profile, location: order_line21.location, order_line_params: { id: order_line21.id }, update_item: true)

    expect(OrderLines::Upsert).to_not receive(:new).with(order: order2, customer: anything, location: order_line22.location, order_line_params: { id: order_line22.id }, update_item: anything) # belongs to a different supplier

    supplier_orders_updater = Suppliers::UpdateFutureOrders.new(supplier: supplier1).call

    expect(supplier_orders_updater).to be_success
  end

  it 'makes a request to update the customer and supplier totals of future orders' do
    expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order1, save_totals: true)
    expect(Orders::CalculateSupplierTotals).to receive(:new).with(order: order1, supplier: supplier1, save_totals: true)

    expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order2, save_totals: true)
    expect(Orders::CalculateSupplierTotals).to receive(:new).with(order: order2, supplier: supplier1, save_totals: true)

    supplier_orders_updater = Suppliers::UpdateFutureOrders.new(supplier: supplier1).call

    expect(supplier_orders_updater).to be_success
  end

  it 'does not make a request to update order lines or totals for a non new|amended|confirmed|paused orders' do
    order1.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[new amended confirmed paused]).sample)

    expect(OrderLines::Upsert).to_not receive(:new).with(order: order1, customer: anything, location: anything, order_line_params: anything, update_item: anything)
    expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order1, save_totals: anything)
    expect(Orders::CalculateSupplierTotals).to_not receive(:new).with(order: order1, supplier: anything, save_totals: anything)

    supplier_orders_updater = Suppliers::UpdateFutureOrders.new(supplier: supplier1).call

    expect(supplier_orders_updater).to be_success
  end

  it 'does not make a request to update order lines or totals for a non old orders' do
    order2.update_column(:delivery_at, today - 2.days + 10.hours)

    expect(OrderLines::Upsert).to_not receive(:new).with(order: order2, customer: anything, location: anything, order_line_params: anything, update_item: anything)
    expect(Orders::CalculateCustomerTotals).to_not receive(:new).with(order: order2, save_totals: anything)
    expect(Orders::CalculateSupplierTotals).to_not receive(:new).with(order: order2, supplier: anything, save_totals: anything)

    supplier_orders_updater = Suppliers::UpdateFutureOrders.new(supplier: supplier1).call

    expect(supplier_orders_updater).to be_success
  end

end