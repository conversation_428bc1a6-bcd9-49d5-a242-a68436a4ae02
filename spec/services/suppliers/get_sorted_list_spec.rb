require 'rails_helper'

RSpec.describe Suppliers::GetSortedList, type: :service, suppliers: true do

  let!(:now) { Time.zone.now }
  let(:supplier1) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier1', created_at: now - 3.months - 10.days) }
  let(:supplier2) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier2', created_at: now - 3.months - 11.days) }
  let(:supplier3) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier3', created_at: now - 3.months - 12.days) }
  let(:supplier4) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier4', created_at: now - 3.months - 13.days) }
  let(:supplier5) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier5', created_at: now - 3.months - 14.days) }
  let(:supplier6) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier6', created_at: now - 3.months - 15.days) }

  let(:all_suppliers) { [supplier1, supplier2, supplier3, supplier4, supplier5, supplier6] }

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:company) { create(:company, :random, customer_profiles: [customer]) }

  let!(:random_session) { [customer, supplier1, nil].sample}
  let!(:non_customer_session) { [supplier1, nil].sample }

  it 'sorts the suppliers randomly by default' do
    supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: random_session).call
    sorted_suppliers = supplier_sorter.sorted_suppliers

    expect(sorted_suppliers.size).to eq(all_suppliers.size)

    all_suppliers.size.times do |num|
      expect(sorted_suppliers[num]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
    end
  end

  it 'sorts a featured supplier above the rest which are randomised' do
    supplier1.supplier_flags.update_column(:is_featured, true)

    supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: random_session).call
    sorted_suppliers = supplier_sorter.sorted_suppliers

    expect(sorted_suppliers[0]).to eq(supplier1)

    (all_suppliers.size - 1).times do |num|
      expect(sorted_suppliers[num + 1]).to eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
    end
  end

  it 'sorts a supplier tagged as new above the rest which are randomised' do
    supplier2.supplier_flags.update_column(:is_new_expires_at, Time.zone.now + 1.month)

    supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: random_session).call
    sorted_suppliers = supplier_sorter.sorted_suppliers

    expect(sorted_suppliers[0]).to eq(supplier2)

    (all_suppliers.size - 1).times do |num|
      expect(sorted_suppliers[num + 1]).to eq(supplier1).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
    end
  end

  it 'sorts a supplier created in the last 2 months above the rest which are randomised' do
    supplier3.update_column(:created_at, now - 2.months + 3.days)

    supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: random_session).call
    sorted_suppliers = supplier_sorter.sorted_suppliers

    expect(sorted_suppliers[0]).to eq(supplier3)

    (all_suppliers.size - 1).times do |num|
      expect(sorted_suppliers[num + 1]).to eq(supplier1).or eq(supplier2).or eq(supplier4).or eq(supplier5).or eq(supplier6)
    end
  end

  context 'with custom menu sections' do
    let!(:menu_section) { create(:menu_section, :random, supplier_profile: supplier4, companies: [company]) }

    it 'sorts a supplier with a custom menu section (for a customer) above the rest which are randomised' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers[0]).to eq(supplier4)

      (all_suppliers.size - 1).times do |num|
        expect(sorted_suppliers[num + 1]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier5).or eq(supplier6)
      end
    end

    it 'returns the ids of suppliers with customer specific menu sections' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call

      expect(supplier_sorter.custom_menu_supplier_ids).to include(supplier4.id)
    end

    it 'does not sorts a supplier with a custom menu section (for a non-customer) above the rest' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: non_customer_session).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      all_suppliers.size.times do |num|
        expect(sorted_suppliers[num]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
      end
      expect(supplier_sorter.custom_menu_supplier_ids).to be_blank
    end
  end

  context 'with rate cards' do
    let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier5) }
    let!(:rate_card) { create(:rate_card, :random, menu_item: menu_item, company: company) }

    it 'sorts a supplier with a rate card for a customer above the rest which are randomised' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers[0]).to eq(supplier5)

      (all_suppliers.size - 1).times do |num|
        expect(sorted_suppliers[num + 1]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier6)
      end
    end

    it 'returns the ids of suppliers with rate cards' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call

      expect(supplier_sorter.rate_card_supplier_ids).to include(supplier5.id)
    end

    it 'does not sorts a supplier with a rate card for a non-customer above the rest' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: non_customer_session).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      all_suppliers.size.times do |num|
        expect(sorted_suppliers[num]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
      end
      expect(supplier_sorter.rate_card_supplier_ids).to be_blank
    end
  end

  context 'with company based markup overrides' do
    let!(:saved_supplier_markup) { 7 } # more than the overrides
    let!(:markup_override4) { create(:supplier_markup_override, :random, supplier_profile: supplier4, overridable: company, markup: 5) }
    let!(:markup_override5) { create(:supplier_markup_override, :random, supplier_profile: supplier5, overridable: company, markup: 5) }

    before do
      [supplier4, supplier5].each do |supplier|
        supplier.update_column(:markup, saved_supplier_markup) # make supplier markup more than the override
      end
    end

    it 'sorts a supplier with a markup override for a customer above the rest which are randomised' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers[0]).to eq(supplier4).or eq(supplier5)
      expect(sorted_suppliers[1]).to eq(supplier5).or eq(supplier4)

      (all_suppliers.size - 2).times do |num|
        expect(sorted_suppliers[num + 2]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier6)
      end
    end

    it 'does not sort and return suppliers with markup override for a non-customer above the rest and' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: non_customer_session).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(supplier_sorter.markup_override_supplier_ids).to be_blank
      all_suppliers.size.times do |num|
        expect(sorted_suppliers[num]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
      end
    end

    it 'returns the ids of suppliers with markup overrides' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call

      expect(supplier_sorter.markup_override_supplier_ids).to include(supplier4.id, supplier5.id)
    end

    it 'only returns the ids of suppliers with available and discounted markups' do
      markup_override4.update_column(:markup, [10, nil].sample)
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call

      expect(supplier_sorter.markup_override_supplier_ids).to include(supplier5.id)
      expect(supplier_sorter.markup_override_supplier_ids).to_not include(supplier4.id)
    end

    context 'with customer based markup overrides' do
      before do
        markup_override5.update_columns(overridable_type: 'CustomerProfile', overridable_id: customer.id)
      end

      it 'returns the ids of suppliers with markup overrides either at company level and or customer level' do
        supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call

        expect(supplier_sorter.markup_override_supplier_ids).to include(supplier4.id, supplier5.id)
      end

      it 'returns the ids of suppliers with markup overrides at the customer level for a customer with no company' do
        company.update(customer_profiles: []) # remove customer from company
        supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer.reload).call

        expect(supplier_sorter.markup_override_supplier_ids).to include(supplier5.id)
        expect(supplier_sorter.markup_override_supplier_ids).to_not include(supplier4.id)
      end
    end
  end

  context 'with favourite suppliers' do
    let!(:favourite_supplier6) { create(:favourite_supplier, favouriter: customer, supplier_profile: supplier6, kind: 'normal') }

    it 'sorts a favourite supplier above the rest which are randomised' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers[0]).to eq(supplier6)

      (all_suppliers.size - 1).times do |num|
        expect(sorted_suppliers[num + 1]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5)
      end
    end

    it 'returns the favourite supplier id' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer).call

      expect(supplier_sorter.favourite_supplier_ids).to include(supplier6.id)
    end

    it 'does not sort any favourited supplier for a customer not having any favourite suppliers' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: non_customer_session).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      all_suppliers.size.times do |num|
        expect(sorted_suppliers[num]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
      end
      expect(supplier_sorter.favourite_supplier_ids).to be_blank
    end
  end

  it 'sorts suppliers by featured -> new -> new by date ( with custom menu -> with rate card -> favourite ) -> randomized others' do
    supplier6.supplier_flags.update_column(:is_featured, true)
    supplier5.supplier_flags.update_column(:is_new_expires_at, Time.zone.now + 2.days)
    supplier4.update_column(:created_at, now - 2.months + 3.days)

    supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: non_customer_session).call
    sorted_suppliers = supplier_sorter.sorted_suppliers

    expect(sorted_suppliers[0]).to eq(supplier6)
    expect(sorted_suppliers[1]).to eq(supplier5).or eq(supplier4)

    (all_suppliers.size - 3).times do |num|
      expect(sorted_suppliers[num + 3]).to eq(supplier1).or eq(supplier2).or eq(supplier3)
    end
  end

  context 'with a selected supplier' do
    let!(:selected_supplier) { supplier1 }

    it 'puts the selected supplier above the rest which are randomised' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: random_session, selected_supplier: selected_supplier).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers[0]).to eq(selected_supplier)

      (all_suppliers.size - 1).times do |num|
        expect(sorted_suppliers[num + 1]).to eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
      end
    end

    it 'does not duplicate the selected supplier in the list' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: random_session, selected_supplier: selected_supplier).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      selected_suppliers_in_sort = sorted_suppliers.select{|sorted_supplier| sorted_supplier == selected_supplier }
      expect(selected_suppliers_in_sort.size).to eq(1)
    end

    it 'does not add a selected supplier if the selected supplier is not within the passed in suppliers list' do
      other_supplier = create(:supplier_profile, :random)

      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: random_session, selected_supplier: other_supplier).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers).to_not include(other_supplier)
    end
  end

  context 'with ratings' do
    let!(:filter_options) do
      {
        other: ['by_rating']
      }
    end

    before do
      supplier1.update_columns(rating_score: 1, rating_count: 1)
      supplier2.update_columns(rating_score: 2, rating_count: 1)
      supplier3.update_columns(rating_score: 3, rating_count: 1)
      # supplier4.update_columns(rating_score: 4, rating_count: 1) # rating of 0
      supplier5.update_columns(rating_score: 5, rating_count: 1)
      supplier6.update_columns(rating_score: 6, rating_count: 1)
    end

    it 'sorts the supplier by their ratings (descending) if asked' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers.each(&:reload), session_profile: random_session, filter_options: filter_options).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers[0]).to eq(supplier6)
      expect(sorted_suppliers[1]).to eq(supplier5)
      expect(sorted_suppliers[2]).to eq(supplier3)
      expect(sorted_suppliers[3]).to eq(supplier2)
      expect(sorted_suppliers[4]).to eq(supplier1)
      expect(sorted_suppliers[5]).to eq(supplier4)
    end

    it 'sorts the supplier by their ratings (descending) if asked even with a selected supplier' do
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers.each(&:reload), session_profile: random_session, filter_options: filter_options, selected_supplier: supplier4).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers[0]).to eq(supplier6)
      expect(sorted_suppliers[1]).to eq(supplier5)
      expect(sorted_suppliers[2]).to eq(supplier3)
      expect(sorted_suppliers[3]).to eq(supplier2)
      expect(sorted_suppliers[4]).to eq(supplier1)
      expect(sorted_suppliers[5]).to eq(supplier4)
    end
  end

  context 'for team orders' do
    let!(:filter_options) do
      {
        team_suppliers: true
      }
    end

    it 'sorts supplier based on featured / custom menu / custom pricing (rate-cards/markup overrides) above the rest which are randomised' do
      skip 'Already tested for normal suppliers'
    end

    it 'puts the selected supplier above the rest which are randomised', skip: 'Already tested for normal suppliers' do
      selected_supplier = supplier1
      supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: random_session, filter_options: filter_options, selected_supplier: selected_supplier).call
      sorted_suppliers = supplier_sorter.sorted_suppliers

      expect(sorted_suppliers[0]).to eq(selected_supplier)

      (all_suppliers.size - 1).times do |num|
        expect(sorted_suppliers[num + 1]).to eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
      end
    end

    context 'with favourite team order suppliers' do
      let!(:favourite_supplier6) { create(:favourite_supplier, favouriter: customer, supplier_profile: supplier6, kind: 'team_order') }

      it 'sorts a supplier with a rate card (for a customer) above the rest which are randomised' do
        supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer, filter_options: filter_options).call
        sorted_suppliers = supplier_sorter.sorted_suppliers

        expect(sorted_suppliers[0]).to eq(supplier6)

        (all_suppliers.size - 1).times do |num|
          expect(sorted_suppliers[num + 1]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5)
        end
      end

      it 'returns the favourite supplier id' do
        supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer, filter_options: filter_options).call

        expect(supplier_sorter.favourite_supplier_ids).to include(supplier6.id)
      end

      it 'does not put a normal favourited supplier above the rest which are randomised' do
        favourite_supplier6.update_column(:kind, 'normal')

        supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer, filter_options: filter_options).call
        sorted_suppliers = supplier_sorter.sorted_suppliers

        all_suppliers.size.times do |num|
          expect(sorted_suppliers[num]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
        end
      end
    end # with team supplier favourites

    context 'with supplier minimum spends' do
      let!(:supplier_minimums) do
        # SupplierMinimum is a Struct of (:minimum_spend, lead_time)
        minimums = {}
        minimums[supplier6] = Suppliers::GetMinimums::SupplierMinimum.new(100, nil)
        minimums[supplier5] = Suppliers::GetMinimums::SupplierMinimum.new(200, nil)
        minimums[supplier3] = Suppliers::GetMinimums::SupplierMinimum.new(300, nil)
        minimums[supplier2] = Suppliers::GetMinimums::SupplierMinimum.new(400, nil)
        minimums[supplier1] = Suppliers::GetMinimums::SupplierMinimum.new(500, nil)
        minimums[supplier4] = Suppliers::GetMinimums::SupplierMinimum.new(600, nil)
        minimums
      end

      let!(:filter_options_with_minimums) { filter_options.merge({ minimums: supplier_minimums }) }

      it 'sorts the suppliers by passed in minimums (spend)' do
        supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers.each(&:reload), session_profile: random_session, filter_options: filter_options_with_minimums).call
        sorted_suppliers = supplier_sorter.sorted_suppliers

        expect(sorted_suppliers[0]).to eq(supplier6)
        expect(sorted_suppliers[1]).to eq(supplier5)
        expect(sorted_suppliers[2]).to eq(supplier3)
        expect(sorted_suppliers[3]).to eq(supplier2)
        expect(sorted_suppliers[4]).to eq(supplier1)
        expect(sorted_suppliers[5]).to eq(supplier4)
      end

      it 'sorts by passed in minimums only if filtering / sorting team suppliers' do
        non_team_order_filter_options_with_minimums = filter_options_with_minimums.merge({ team_suppliers: [false, nil].sample })

        supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers, session_profile: customer, filter_options: non_team_order_filter_options_with_minimums).call
        sorted_suppliers = supplier_sorter.sorted_suppliers

        all_suppliers.size.times do |num|
          expect(sorted_suppliers[num]).to eq(supplier1).or eq(supplier2).or eq(supplier3).or eq(supplier4).or eq(supplier5).or eq(supplier6)
        end
      end

      context 'with missing supplier minimums for some suppliers' do
        let!(:mismatched_supplier_minimums) { supplier_minimums.dup.except!(supplier2, supplier6) }

        let!(:filter_options_with_mismatched_minimums) { filter_options.merge({ minimums: mismatched_supplier_minimums }) }

        it 'sorts supplier with minimums (spend) before suppliers without minimums (spend) which are randomised' do
          supplier_sorter = Suppliers::GetSortedList.new(suppliers: all_suppliers.each(&:reload), session_profile: random_session, filter_options: filter_options_with_mismatched_minimums).call
          sorted_suppliers = supplier_sorter.sorted_suppliers

          expect(sorted_suppliers[0]).to eq(supplier5)
          expect(sorted_suppliers[1]).to eq(supplier3)
          expect(sorted_suppliers[2]).to eq(supplier1)
          expect(sorted_suppliers[3]).to eq(supplier4)

          (all_suppliers.size - 4).times do |num|
            expect(sorted_suppliers[num + 4]).to eq(supplier2).or eq(supplier6)
          end
        end
      end # with missing supplier minimums
    end # with supplier minimums
  end # filtering / sorting team suppliers

  it 'return with empty results if suppliers list is blank' do
    supplier_sorter = Suppliers::GetSortedList.new(suppliers: [nil, []].sample, session_profile: random_session).call

    expect(supplier_sorter.sorted_suppliers).to be_blank
    expect(supplier_sorter.custom_menu_supplier_ids).to be_blank
    expect(supplier_sorter.rate_card_supplier_ids).to be_blank
    expect(supplier_sorter.favourite_supplier_ids).to be_blank
  end

end
