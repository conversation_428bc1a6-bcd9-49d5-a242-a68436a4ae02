require 'rails_helper'

RSpec.describe Suppliers::GetCutoffHours, type: :service, suppliers: true do

  let!(:beginning_of_week) { Time.zone.parse('2022-09-10').beginning_of_week } # tied to a specific date cause sometimes the specs break in certain weeks/months
  let!(:delivery_datetime) { beginning_of_week + 3.days + 12.hours } # Thursday at 12pm

  let!(:suburb) { create(:suburb, :random) }
  let!(:supplier) { create(:supplier_profile, :random) }

  it 'returns 0 hours if the supplier doesn\'t have any minimums' do
    cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call
    expect(cutoff_retriever.cutoff_hours).to eq('0.0') # picks up lead time from lead_day_minimum2 + holiday hours + weekend hours
  end

  context 'with supplier minimums only with lead time' do
    let!(:lead_time_minimum1) { create(:minimum, :random, supplier_profile: supplier, lead_time: 22, lead_time_day_before: '') }
    let!(:lead_time_minimum2) { create(:minimum, :random, supplier_profile: supplier, lead_time: 11, lead_time_day_before: '') }

    it 'gets the cutoff hours based on maximum lead time' do
      cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call

      expect(cutoff_retriever.cutoff_hours).to eq('22.0') # picks the supplier_minimum with highest lead time
    end

    it 'gets the remaining hourse based on the maximum lead time' do
      cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call

      expected_remaining_hours = ((delivery_datetime - Time.zone.now - cutoff_retriever.cutoff_hours.to_i.hours) / 1.hours).round(1)
      expect(cutoff_retriever.hours_remaining).to eq(expected_remaining_hours)
    end
  end

  context 'with supplier minimums only with lead days' do
    let!(:lead_day_minimum1) { create(:minimum, :random, supplier_profile: supplier, lead_time: nil, lead_time_day_before: '15:00') }
    let!(:lead_day_minimum2) { create(:minimum, :random, supplier_profile: supplier, lead_time: nil, lead_time_day_before: '10:00') }

    it 'gets the cutoff hours based on minimum lead day' do
      cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call

      expect(cutoff_retriever.cutoff_hours).to eq('26.0') # picks the supplier_minimum with lowest lead by day hours
    end

    it 'gets the cutoff hours based on minimum lead day' do
      cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call

      expected_remaining_hours = ((delivery_datetime - Time.zone.now - cutoff_retriever.cutoff_hours.to_i.hours) / 1.hours).round(1)
      expect(cutoff_retriever.hours_remaining).to eq(expected_remaining_hours)
    end
  end

  context 'with mixed supplier minimums' do
    let!(:lead_time_minimum) { create(:minimum, :random, supplier_profile: supplier, lead_time: 22, lead_time_day_before: '') }
    let!(:lead_day_minimum) { create(:minimum, :random, supplier_profile: supplier, lead_time: nil, lead_time_day_before: '10:00') }

    it 'returns the highest cutoff hours based on lead time and lead by day' do
      cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call
      expect(cutoff_retriever.cutoff_hours).to eq('26.0') # picks up lead_day_minimum

      _lead_time_minimum2 = create(:minimum, :random, supplier_profile: supplier, lead_time: 30, lead_time_day_before: '')
      cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call
      expect(cutoff_retriever.cutoff_hours).to eq('30.0') # picks up _lead_time_minimum2
    end

    it 'returns hours to a prior weekday if the supplier cutoff ends up being a weekend' do
      lead_time = rand(85..108).to_i
      _lead_time_minimum2 = create(:minimum, :random, supplier_profile: supplier, lead_time: lead_time, lead_time_day_before: '')

      cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call
      expect(cutoff_retriever.cutoff_hours).to eq((lead_time + 48.0).to_s) # picks up _lead_time_minimum2 + weekend hours
    end

    context 'with a holiday' do
      let!(:holiday) { create(:holiday, :random, :public_holiday, state: [nil, suburb.state].sample, on_date: (delivery_datetime - 1.day).to_date) }

      it 'returns hours to a prior weekday if the supplier cutoff ends up being a holiday' do
        cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call
        expect(cutoff_retriever.cutoff_hours).to eq((26.0 + 24.0).to_s) # picks up lead time from lead_day_minimum + holiday day
      end

      it 'returns hours according to supplier cutoff if the holiday is for another state' do
        new_state = (%w[NSW ACT NT VIC] - [suburb.state]).sample
        holiday.update_column(:state, new_state)
        cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call
        expect(cutoff_retriever.cutoff_hours).to eq('26.0') # picks up lead time from lead_day_minimum2
      end

      it 'returns hours to a prior available weekday if the supplier cutoff ends up being a holiday' do
        create(:holiday, :random, :public_holiday, on_date: (delivery_datetime - 2.day).to_date)
        create(:holiday, :random, :public_holiday, on_date: (delivery_datetime - 3.day).to_date)

        cutoff_retriever = Suppliers::GetCutoffHours.new(supplier: supplier, delivery_at: delivery_datetime, suburb: suburb).call
        expect(cutoff_retriever.cutoff_hours).to eq((26.0 + 72.0 + 48.0).to_s) # picks up lead time from lead_day_minimum2 + holiday hours + weekend hours
      end
    end # with holiday
  end

end
