require 'rails_helper'

RSpec.describe Suppliers::ConfirmOrder, type: :service, orders: true, suppliers: true do

  let!(:order) { create(:order, :new) }

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier, status: 'pending') }

  before do
    confirm_email_sender = delayed_confirm_email_sender = double(Customers::Emails::SendOrderConfirmationEmail)
    allow(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).and_return(confirm_email_sender)
    allow(confirm_email_sender).to receive(:delay).and_return(delayed_confirm_email_sender)
    allow(delayed_confirm_email_sender).to receive(:call).and_return(true)
  end

  it 'accepts all the supplier order lines for the order' do
    order_confirmer = Suppliers::ConfirmOrder.new(supplier: supplier, order: order).call

    expect(order_confirmer).to be_success
    supplier_order_lines = order.reload.order_lines.where(supplier_profile: supplier)

    expect(supplier_order_lines.map(&:status).uniq).to match_array(['accepted'])
  end

  it 'confirms the order' do
    order_confirmer = Suppliers::ConfirmOrder.new(supplier: supplier, order: order).call

    expect(order_confirmer).to be_success
    confirmed_order = order_confirmer.order

    expect(confirmed_order.id).to eq(order.id)
    expect(confirmed_order.status).to eq('confirmed')
  end

  it 'sends a confirmation email to the customer', notifications: true do
    expect(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).with(customer: order.customer_profile, order: order)

    order_confirmer = Suppliers::ConfirmOrder.new(supplier: supplier, order: order).call
    expect(order_confirmer).to be_success
  end

  context 'with multiple order suppliers' do
    let!(:supplier2) { create(:supplier_profile, :random) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2, status: %w[pending notified].sample) }

    it 'only accepts the specified supplier\'s order lines for the order' do
      order_confirmer = Suppliers::ConfirmOrder.new(supplier: supplier, order: order).call

      expect(order_confirmer).to be_success
      supplier_order_lines = order.reload.order_lines.where(supplier_profile: supplier)
      expect(supplier_order_lines.map(&:status).uniq).to match_array(['accepted'])

      supplier2_order_lines = order.reload.order_lines.where(supplier_profile: supplier2)
      expect(supplier2_order_lines.map(&:status)).to_not include('accepted')
    end

    it 'doesn\'t confirm the order because of pending order lines from another supplier but returns as success' do
      order_confirmer = Suppliers::ConfirmOrder.new(supplier: supplier, order: order).call

      expect(order_confirmer).to be_success
      expect(order_confirmer.warnings).to include('Order has not been processed as being confirmed because of pending order lines from other supplier(s) within the order')

      supplier2_order_lines = order.reload.order_lines.where(supplier_profile: supplier2)
      expect(order_confirmer.errors).to_not include("#{supplier2_order_lines.size} order line(s) have not been accepted") # override error as above
      expect(order.reload.status).to_not eq('confirmed')
    end

    it 'confirms the order if all order lines are accepted' do
      order_line2.update_column(:status, 'accepted')

      order_confirmer = Suppliers::ConfirmOrder.new(supplier: supplier, order: order).call

      expect(order_confirmer).to be_success
      confirmed_order = order_confirmer.order

      expect(confirmed_order.id).to eq(order.id)
      expect(confirmed_order.status).to eq('confirmed')
    end
  end

  context 'errors' do
    it 'cannot confirm a missing order' do
      order_confirmer = Suppliers::ConfirmOrder.new(supplier: supplier, order: nil).call

      expect(order_confirmer).to_not be_success
      expect(order_confirmer.errors).to include('Cannot confirm a missing order')
    end

    it 'cannot confirm an order without a supplier' do
      order_confirmer = Suppliers::ConfirmOrder.new(supplier: nil, order: order).call

      expect(order_confirmer).to_not be_success
      expect(order_confirmer.errors).to include('You don\'t have access to this order')
    end

    it 'cannot confirm order not belonging to the supplier' do
      supplier2 = create(:supplier_profile, :random)
      order_confirmer = Suppliers::ConfirmOrder.new(supplier: supplier2, order: order).call

      expect(order_confirmer).to_not be_success
      expect(order_confirmer.errors).to include('You don\'t have access to this order')
    end
  end

end
