require 'rails_helper'

RSpec.describe Suppliers::Webhooks::FetchItem, type: :service, webhooks: true, suppliers: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier, sku: SecureRandom.hex(5), name: '<PERSON>u Item') }

  it 'fetches a menu item by its SKU' do
    webhook_params = { sku: menu_item.sku, name: menu_item.name }
    fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier, webhook_params: webhook_params).call

    expect(fetched_item).to be_present
    expect(fetched_item).to eq(menu_item)
  end

  it 'fetches a menu item by its SKU even with name mismatch' do
    webhook_params = { sku: menu_item.sku, name: 'RANDOMISED NAME' }
    fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier, webhook_params: webhook_params).call

    expect(fetched_item).to be_present
    expect(fetched_item).to eq(menu_item)
  end

  it 'fetches the menu item by (stripped) name' do
    item_name = [menu_item.name, " #{menu_item.name}", "#{menu_item.name} "].sample
    webhook_params = { sku: 'randomised-sku', name: item_name }
    fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier, webhook_params: webhook_params).call

    expect(fetched_item).to be_present
    expect(fetched_item).to eq(menu_item)
  end

  it 'does not fetch menu item via name or SKU of it does not belong to the supplier' do
    supplier2 = create(:supplier_profile, :random)
    webhook_params = { sku: menu_item.sku, name: menu_item.name }
    fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier2, webhook_params: webhook_params).call

    expect(fetched_item).to be_nil
  end

  context 'with serving sizes' do
    let!(:menu_item_with_serving_sizes) { create(:menu_item, :random, supplier_profile: supplier, sku: SecureRandom.hex(5), name: 'Menu Item With Serving') }
    let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item_with_serving_sizes, sku: SecureRandom.hex(5), name: 'Serving Size') }

    it 'fetches a serving size by its SKU' do
      webhook_params = { sku: serving_size.sku, name: serving_size.name }
      fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier, webhook_params: webhook_params).call

      expect(fetched_item).to be_present
      expect(fetched_item).to eq(serving_size)
    end

    it 'fetches a serving size by its SKU even with name mismatch' do
      webhook_params = { sku: serving_size.sku, name: 'RANDOMISED NAME' }
      fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier, webhook_params: webhook_params).call

      expect(fetched_item).to be_present
      expect(fetched_item).to eq(serving_size)
    end

    it 'fetches the serving size by (stripped) name' do
      item_name = [serving_size.name, " #{serving_size.name}", "#{serving_size.name} "].sample
      webhook_params = { sku: 'randomised-sku', name: item_name }
      fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier, webhook_params: webhook_params).call

      expect(fetched_item).to be_present
      expect(fetched_item).to eq(serving_size)
    end

    it 'does not fetch serving size via name or SKU of it does not belong to the supplier' do
      supplier2 = create(:supplier_profile, :random)
      webhook_params = { sku: serving_size.sku, name: serving_size.name }
      fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier2, webhook_params: webhook_params).call

      expect(fetched_item).to be_nil
    end

    it 'fetches the menu item with SKU and/or Name before fetching the serving size' do # menu item preferential over serving size
      new_menu_item = create(:menu_item, :random, supplier_profile: supplier, sku: serving_size.sku, name: serving_size.name)

      webhook_params = { sku: serving_size.sku, name: serving_size.name }
      fetched_item = Suppliers::Webhooks::FetchItem.new(supplier: supplier, webhook_params: webhook_params).call

      expect(fetched_item).to eq(new_menu_item)
    end
  end

end