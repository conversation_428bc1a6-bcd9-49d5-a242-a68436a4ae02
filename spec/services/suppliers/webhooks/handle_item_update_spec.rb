require 'rails_helper'

RSpec.describe Suppliers::Webhooks::HandleItemUpdate, type: :sevice, webhooks: true, suppliers: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:menu_item) { create(:menu_item, :random, name: 'Menu Item Name', supplier_profile: supplier, sku: SecureRandom.hex(7)) }

  let!(:item_fetcher) { double(Suppliers::Webhooks::FetchItem) }
  let!(:slack_notifier) { double(Suppliers::Webhooks::NotifyViaSlack) }

  before do
    # item fetcher
    allow(Suppliers::Webhooks::FetchItem).to receive(:new).and_return(item_fetcher)
    allow(item_fetcher).to receive(:call).and_return(menu_item)

    # slack notifier
    allow(Suppliers::Webhooks::NotifyViaSlack).to receive(:new).and_return(slack_notifier)
    allow(slack_notifier).to receive(:add_attachment).and_return(true)
    allow(slack_notifier).to receive(:notify).and_return(true)
  end

  let!(:webhook_params) do
    {
      sku: menu_item.sku,
      name: menu_item.name,
      stock_quantity: rand(20..30)
    }
  end

  it 'makes a request to fetch the item for the supplier based on the passed in params' do
    expect(Suppliers::Webhooks::FetchItem).to receive(:new).with(supplier: supplier, webhook_params: webhook_params)

    item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call
    expect(item_updater).to be_success
  end

  it 'updates the updatable fields (e.g. stock_quantity) for the item' do
    item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call

    expect(item_updater).to be_success
    updated_item = item_updater.item

    expect(updated_item.id).to eq(menu_item.id)
    expect(updated_item.stock_quantity).to eq(webhook_params[:stock_quantity])
  end

  context 'name change' do
    it "updates the name if the mismatch is less than #{Suppliers::Webhooks::HandleItemUpdate::NAME_CHANGE_THRESHOLD}%" do
      webhook_params_with_name = webhook_params.merge({ name: "#{menu_item.name}2" })
      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params_with_name).call

      expect(item_updater).to be_success
      updated_item = item_updater.item

      expect(updated_item.id).to eq(menu_item.id)
      expect(updated_item.name).to eq(webhook_params_with_name[:name])
    end

    it "doesn't update name if the name from request is missing or name mismatch is greater than #{Suppliers::Webhooks::HandleItemUpdate::NAME_CHANGE_THRESHOLD}%" do
      webhook_params_with_name = webhook_params.merge({ name: ["#{menu_item.name} - add more changes to then name", nil, ''].sample })
      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params_with_name).call

      expect(item_updater).to be_success
      updated_item = item_updater.item

      expect(updated_item.id).to eq(menu_item.id)
      expect(updated_item.name).to_not eq(webhook_params_with_name[:name])
    end
  end

  context 'Slack notifications' do
    it 'initiates a slack notifier with a message' do
      expect(Suppliers::Webhooks::NotifyViaSlack).to receive(:new).with(message: ':hook: *Yordar External Webhook*: An item was marked to be updated')

      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call
      expect(item_updater).to be_success
    end

    it 'adds an attachment to show the passed in params' do
      expect(slack_notifier).to receive(:add_attachment).with(
        text: "Update Params for *#{supplier.name}* => #{webhook_params.to_h.inspect}",
        kind: 'info'
      )

      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call
      expect(item_updater).to be_success
    end

    it 'adds an attachment with info about the found item' do
      expect(slack_notifier).to receive(:add_attachment).with(
        text: "Found #{menu_item.class.name} => ID: #{menu_item.id}",
        kind: 'info'
      )

      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call
      expect(item_updater).to be_success
    end

    it 'adds an attachment if the found item is archived' do
      menu_item.update_column(:archived_at, Time.zone.now)

      expect(slack_notifier).to receive(:add_attachment).with(
        text: 'The Item is Archived on Yordar\'s end',
        kind: 'warning'
      )

      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call
      expect(item_updater).to be_success
    end

    it 'adds an attachment if it finds a SKU mismatch' do
      menu_item.update_column(:sku, SecureRandom.hex(7))

      expect(slack_notifier).to receive(:add_attachment).with(
        text: "SKU Mismatch => Stored: '#{menu_item.sku}' vs Passed: '#{webhook_params[:sku]}'",
        kind: 'warning'
      )

      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call
      expect(item_updater).to be_success
    end

    context 'name mismatch' do
      it "adds an attachment if it finds a Name mismatch is more than equal to #{Suppliers::Webhooks::HandleItemUpdate::NAME_MISMATCH_THRESHOLD}%" do
        webhook_params_with_name = webhook_params.merge({ name: 'completly randomised descriptor' })

        expect(slack_notifier).to receive(:add_attachment).with(
          text: "Name Mismatch => Stored: '#{menu_item.name}' vs Passed: '#{webhook_params_with_name[:name]}'",
          kind: 'warning'
        )

        item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params_with_name).call
        expect(item_updater).to be_success
      end

      it "doesn't add an attachment if it finds a Name mismatch is less than #{Suppliers::Webhooks::HandleItemUpdate::NAME_MISMATCH_THRESHOLD}%" do
        webhook_params_with_name = webhook_params.merge({ name: "#{menu_item.name}2" })

        expect(slack_notifier).to_not receive(:add_attachment).with(
          text: "Name Mismatch => Stored: '#{menu_item.name}' vs Passed: '#{webhook_params_with_name[:name]}'",
          kind: 'warning'
        )

        item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params_with_name).call
        expect(item_updater).to be_success
      end
    end

    it 'adds an attachment if the stock quantity is updated' do
      expect(slack_notifier).to receive(:add_attachment).with(
        text: "Stock Quantity: #{webhook_params[:stock_quantity]}",
        kind: 'info'
      )

      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call
      expect(item_updater).to be_success
    end
  end # slack notifications

  context 'errors' do
    it 'errors out if the supplier is misssing' do
      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: nil, item_params: webhook_params).call

      expect(item_updater).to_not be_success
      expect(item_updater.errors).to include('Supplier is missing')
    end

    it 'errors out if the item (webhook) params is misssing + adds attachment for Slack Notification' do
      expect(slack_notifier).to receive(:add_attachment).with(
        text: "No params passed for #{supplier.name}'s' item",
        kind: 'error'
      )
      item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: [nil, {}].sample).call

      expect(item_updater).to_not be_success
      expect(item_updater.errors).to include('Cannot update without data')
    end

    context 'with item not found' do
      before do
        allow(item_fetcher).to receive(:call).and_return(nil) # fetcher does not return an item
      end

      it 'errors out if the item cannot be found' do
        allow(item_fetcher).to receive(:call).and_return(nil) # fetcher does not return an item
        item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call

        expect(item_updater).to_not be_success
        expect(item_updater.errors).to include('Could not find Item using SKU/Name')
      end

      it 'adds an attachment notifying that the item could not be found' do
        expect(slack_notifier).to receive(:add_attachment).with(
          text: "Could not find object with SKU: #{webhook_params[:sku]} or Name: #{webhook_params[:name]}",
          kind: 'error'
        )

        item_updater = Suppliers::Webhooks::HandleItemUpdate.new(supplier: supplier, item_params: webhook_params).call
        expect(item_updater).to_not be_success
      end
    end # Item Not Found
  end

end