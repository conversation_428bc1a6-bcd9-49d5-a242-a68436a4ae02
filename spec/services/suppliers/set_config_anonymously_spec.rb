require 'rails_helper'

RSpec.describe Suppliers::SetConfigAnonymously, type: :service, suppliers: true do
  let(:supplier) { create(:supplier_profile, :random, :with_flags) }

  it 'can set the supplier as being a contactless provider' do
    config_setter = Suppliers::SetConfigAnonymously.new(uuid: supplier.uuid, field: 'contactless_delivery').call

    expect(config_setter).to be_success
    expect(supplier.reload.provides_contactless_delivery).to be_truthy
  end

  it 'can set the supplier as not being a contactless provider' do
    config_setter = Suppliers::SetConfigAnonymously.new(uuid: supplier.uuid, field: 'contactless_delivery', value: false).call

    expect(config_setter).to be_success
    expect(supplier.reload.provides_contactless_delivery).to be_falsey
  end

  context 'errors' do
    it 'cannot set config for a missing / invalid supplier' do
      config_setter = Suppliers::SetConfigAnonymously.new(uuid: [nil, 'invalid-uuid'].sample, field: 'contactless_delivery').call

      expect(config_setter).to_not be_success
      expect(config_setter.errors).to include('Could not find supplier')
    end

    it 'cannot set config for a supplier with missing flags records' do
      supplier.supplier_flags.destroy
      config_setter = Suppliers::SetConfigAnonymously.new(uuid: supplier.uuid, field: 'contactless_delivery').call

      expect(config_setter).to_not be_success
      expect(config_setter.errors).to include('Could not find supplier with flags')
    end

    it 'cannot set config for a missing or invalid field' do
      config_setter = Suppliers::SetConfigAnonymously.new(uuid: supplier.uuid, field: [nil, 'invalid-field'].sample).call

      expect(config_setter).to_not be_success
      expect(config_setter.errors).to include('Cannot set this config')
    end
  end
end
