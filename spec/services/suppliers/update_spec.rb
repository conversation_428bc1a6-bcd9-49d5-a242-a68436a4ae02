require 'rails_helper'

RSpec.describe Suppliers::Update, type: :service, suppliers: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_flags) }
  let!(:supplier_params) do
    {
      is_searchable: !supplier.is_searchable,
      markup: supplier.markup + rand(10.2..20.3),
      commission_rate: supplier.commission_rate + rand(10.2..20.3),
      lead_mode: (SupplierProfile::VALID_LEAD_MODES - [supplier.lead_mode]).sample,
      minimum_delivery_fee: rand(100.01..200.02)
    }
  end

  let!(:supplier_flag_params) do
    {
      is_eco_friendly: !supplier.is_eco_friendly,
      is_socially_responsible: !supplier.is_socially_responsible,
      is_indigenous_owned: !supplier.is_indigenous_owned,
      # ...
    }
  end

  before do
    # mock future order update
    order_updater = delayed_order_updater = double(Suppliers::UpdateFutureOrders)
    allow(Suppliers::UpdateFutureOrders).to receive(:new).and_return(order_updater)
    allow(order_updater).to receive(:delay).and_return(delayed_order_updater)
    allow(order_updater).to receive(:call).and_return(true)

    # mock bank details change email
    email_sender = delayed_email_sender = double(Admin::Emails::SendBankDetailChangedEmail)
    allow(Admin::Emails::SendBankDetailChangedEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'updates the supplier related info' do
    supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call
    expect(supplier_updater).to be_success

    updated_supplier = supplier_updater.supplier
    expect(updated_supplier.is_searchable).to eq(supplier_params[:is_searchable])
    expect(updated_supplier.markup.round(2).to_s).to eq(supplier_params[:markup].round(2).to_s)
    expect(updated_supplier.commission_rate.round(2).to_s).to eq(supplier_params[:commission_rate].round(2).to_s)
    expect(updated_supplier.lead_mode).to eq(supplier_params[:lead_mode])
    expect(updated_supplier.minimum_delivery_fee.round(2).to_s).to eq(supplier_params[:minimum_delivery_fee].round(2).to_s)
  end

  it 'returns the updated fields' do
    supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call

    expect(supplier_updater).to be_success
    expect(supplier_updater.updated_fields).to include(*supplier_params.keys)
  end

  context 'with minimum' do
    let!(:minimum1) { create(:minimum, :random, supplier_profile: supplier) }
    let!(:minimum2) { create(:minimum, :random, supplier_profile: supplier) }

    it 'destroys all minimums when the lead mode is changed' do
      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call
      expect(supplier_updater).to be_success

      expect{ minimum1.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ minimum2.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'does not update/destroy minimums when the lead mode is NOT changed' do
      supplier_params_without_lead_mode = supplier_params.except(:lead_mode)
      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params_without_lead_mode).call
      expect(supplier_updater).to be_success

      expect{ minimum1.reload }.to_not raise_error(ActiveRecord::RecordNotFound)
      expect{ minimum2.reload }.to_not raise_error(ActiveRecord::RecordNotFound)
    end
  end

  context 'is_searchable update', event_logs: true do
    let!(:supplier_params) do
      {
        company_name: Faker::Name.name,
        is_searchable: !supplier.is_searchable,
      }
    end

    it 'logs a `Searchable Updated` event log' do
      expect(EventLogs::Create).to receive(:new).with(event_object: supplier, event: 'searchable-updated', is_searchable: supplier_params[:is_searchable])

      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call
      expect(supplier_updater).to be_success
    end

    it 'does not log a `Searchable Updated` event log if searchable is not updated' do
      expect(EventLogs::Create).to_not receive(:new).with(event_object: supplier, event: 'searchable-updated', is_searchable: anything)

      non_searchable_supplier_params = supplier_params.except(:is_searchable)
      unchanged_searchable_supplier_params = supplier_params.merge({ is_searchable: supplier.is_searchable })
      supplier_update_params = [non_searchable_supplier_params, unchanged_searchable_supplier_params].sample

      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_update_params).call
      expect(supplier_updater).to be_success
    end

    it 'updates the menu_last_updated_on (datetime) supplier flag' do
      supplier.update_column(:is_searchable, false)
      supplier_params = { is_searchable: true }
      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call

      expect(supplier_updater).to be_success
      expect(supplier.menu_last_updated_on).to be_present
      expect(supplier.reload.menu_last_updated_on).to be_within(2.seconds).of(Time.zone.now)
    end
  end # is_searchable

  context 'markup / commission updates' do
    let!(:supplier_params) do
      {
        company_name: Faker::Name.name,
        markup: supplier.markup + rand(10.2..20.3),
        commission_rate: supplier.commission_rate + rand(10.2..20.3),
      }
    end

    it 'requests to update future orders if either the markup or commission rate is changed' do
      expect(Suppliers::UpdateFutureOrders).to receive(:new).with(supplier: supplier)

      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call
      expect(supplier_updater).to be_success
    end

    it 'logs a `Margin Updated` event', event_logs: true do
      expect(EventLogs::Create).to receive(:new).with(event_object: supplier, event: 'margin-updated', markup: supplier_params[:markup]&.round(2).to_s, commission_rate: supplier_params[:commission_rate]&.round(2).to_s)

      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call
      expect(supplier_updater).to be_success
    end

    it 'does not request to update future orders if a delayed job already exists' do
      handler_data = "object:Suppliers::UpdateFutureOrders - value_before_type_cast: #{supplier.id} - method_name: :call"
      Delayed::Job.create(handler: handler_data, locked_at: nil, failed_at: nil)

      expect(Suppliers::UpdateFutureOrders).to_not receive(:new).with(supplier: supplier)

      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call
      expect(supplier_updater).to be_success
    end

    context 'when markup / commission rate is not changed' do
      let(:non_margin_supplier_params) { supplier_params.except(:markup, :commission_rate) }
      let(:unchanged_searchable_supplier_params) { supplier_params.merge({ markup: supplier.markup, commission_rate: supplier.commission_rate }) }
      let!(:supplier_update_params) { [non_margin_supplier_params, unchanged_searchable_supplier_params].sample }

      it 'does not request to update future orders if neither the markup or commission rate is changed' do
        expect(Suppliers::UpdateFutureOrders).to_not receive(:new).with(supplier: supplier)

        supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_update_params).call
        expect(supplier_updater).to be_success
      end

      it 'does not log a `Margin Updated` event', event_logs: true do
        expect(EventLogs::Create).to_not receive(:new).with(event_object: supplier, event: 'margin-updated', markup: supplier_params[:markup]&.round(2).to_s, commission_rate: supplier_params[:commission_rate]&.round(2).to_s)

        supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_update_params).call
        expect(supplier_updater).to be_success
      end
    end
  end

  context 'with bank details update' do
    let!(:previous_bank_details) do
      {
        bsb_number: Faker::Bank.swift_bic,
        bank_account_number: Faker::Bank.account_number
      }
    end

    let!(:supplier_params) do
      {
        company_name: Faker::Name.name,
        bsb_number: Faker::Bank.swift_bic,
        bank_account_number: Faker::Bank.account_number
      }
    end

    before do
      # pre-set supplier bank details
      supplier.update_columns(previous_bank_details)
    end

    it 'notifies admins about the bank detail changes' do
      expect(Admin::Emails::SendBankDetailChangedEmail).to receive(:new).with(supplier: supplier, previous_details: previous_bank_details)

      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params).call
      expect(supplier_updater).to be_success
    end

    it 'does not send admin an email if bank details are not changed' do
      expect(Admin::Emails::SendBankDetailChangedEmail).to_not receive(:new).with(supplier: supplier, previous_details: anything)

      supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_params: supplier_params.except(:bsb_number, :bank_account_number)).call
      expect(supplier_updater).to be_success
    end
  end

  it 'updates the supplier flags' do
    supplier_updater = Suppliers::Update.new(supplier: supplier, supplier_flag_params: supplier_flag_params).call
    expect(supplier_updater).to be_success

    updated_supplier_flags = supplier_updater.supplier.supplier_flags
    expect(updated_supplier_flags.is_eco_friendly).to eq(supplier_flag_params[:is_eco_friendly])
    expect(updated_supplier_flags.is_socially_responsible).to eq(supplier_flag_params[:is_socially_responsible])
    expect(updated_supplier_flags.is_indigenous_owned).to eq(supplier_flag_params[:is_indigenous_owned])
  end

  context 'errors' do
    it 'cannot update without a supplier' do
      supplier_updater = Suppliers::Update.new(supplier: nil, supplier_params: supplier_params).call

      expect(supplier_updater).to_not be_success
      expect(supplier_updater.errors).to include('Cannot update without a supplier')
    end

    it 'cannot update without either supplier or supplier flag params' do
      supplier_updater = Suppliers::Update.new(supplier: supplier).call

      expect(supplier_updater).to_not be_success
      expect(supplier_updater.errors).to include('Cannot update without update params')
    end

    it 'cannot update without either supplier flags if the supplier is missing supplier flags' do
      supplier.supplier_flags.destroy
      supplier_updater = Suppliers::Update.new(supplier: supplier.reload, supplier_flag_params: supplier_flag_params).call

      expect(supplier_updater).to_not be_success
      expect(supplier_updater.errors).to include('Supplier does not have any supplier flags')
    end
  end

end