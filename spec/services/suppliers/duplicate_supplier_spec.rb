require 'rails_helper'

RSpec.describe Suppliers::DuplicateSupplier, type: :service, suppliers: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_user) }

  it 'create a copy of the supplier' do
    supplier_duplicator = Suppliers::DuplicateSupplier.new(supplier: supplier).call
    expect(supplier_duplicator).to be_success

    duplicate_supplier = supplier_duplicator.duplicate_supplier
    expect(duplicate_supplier.id).to_not eq(supplier.id)
    expect(duplicate_supplier.name).to eq("Copy - #{supplier.name}")
  end

  it 'duplicate the supplier and marks it as not searchable' do
    supplier.update_column(:is_searchable, true)
    expect(supplier).to be_is_searchable
    supplier_duplicator = Suppliers::DuplicateSupplier.new(supplier: supplier).call
    expect(supplier_duplicator).to be_success

    duplicate_supplier = supplier_duplicator.duplicate_supplier
    expect(duplicate_supplier).to_not be_is_searchable
  end

  it 'generates a duplicate user (and profile) record' do
    supplier_duplicator = Suppliers::DuplicateSupplier.new(supplier: supplier).call
    expect(supplier_duplicator).to be_success

    duplicate_supplier = supplier_duplicator.duplicate_supplier
    duplicate_user = duplicate_supplier.reload.user
    original_user = supplier.reload.user

    expect(duplicate_user.id).to_not eq(original_user.id)
    expect(duplicate_user.firstname).to eq("Copy - #{original_user.firstname}")
    expect(duplicate_user.lastname).to eq("Copy - #{original_user.lastname}")
    expect(duplicate_user.email).to include('copy')
    expect(duplicate_user.email).to include(original_user.email.split('@').first)
    expect(duplicate_user.email).to include('@yordar.com.au')
  end

  it 'generates a duplicate non-admin user record' do
    supplier_user = supplier.reload.user
    supplier_user.update_column(:admin, true)
    supplier_duplicator = Suppliers::DuplicateSupplier.new(supplier: supplier).call
    expect(supplier_duplicator).to be_success

    duplicate_supplier = supplier_duplicator.duplicate_supplier
    duplicate_user = duplicate_supplier.reload.user
    expect(duplicate_user.admin).to be_falsey
  end

  context 'with menu sections', skip: 'Handled in Suppliers:CloneSupplierMenu\'s specs' do
    it 'duplicates the menu_sections'
    it 'does not duplicate a menu section belonging to a different supplier'
    it 'does not duplicate an archived menu sections'
    it 'does not duplicate a custom menu sections'

    context 'with category menu sections' do
      it 'duplicates the associated category menu sections'
    end

    context 'with menu items' do
      it 'duplicates the menu_items'
      it 'does not duplicate a menu_items belonging to a different supplier'
      it 'does not duplicate archived menu_items'

      context 'with serving sizes' do
        it 'duplicates the serving_sizes'
        it 'does not duplicate archived serving_sizes'
      end

      context 'with menu extra sections and menu extras' do
        it 'duplicates the menu_extra section and underlying extras'
        it 'does not duplicate archived menu_extra_sections and any archived menu extras'
      end # with menu extras
    end # with menu items
  end # with menu sections

  context 'with delivery zones' do
    let!(:delivery_zone1) { create(:delivery_zone, :random, supplier_profile: supplier) }
    let!(:delivery_zone2) { create(:delivery_zone, :random, supplier_profile: supplier) }

    it 'duplicates the delivery zones' do
      supplier_duplicator = Suppliers::DuplicateSupplier.new(supplier: supplier).call
      expect(supplier_duplicator).to be_success
      duplicate_supplier = supplier_duplicator.duplicate_supplier
      delivery_zones = duplicate_supplier.delivery_zones

      expect(delivery_zones.size).to eq(2)
      expect(delivery_zones.map(&:radius)).to include(*[delivery_zone1, delivery_zone2].map(&:radius))
      expect(delivery_zones.map(&:delivery_fee)).to include(*[delivery_zone1, delivery_zone2].map(&:delivery_fee))
      expect(delivery_zones.map(&:operating_wdays)).to include(*[delivery_zone1, delivery_zone2].map(&:operating_wdays))
    end

    it 'does not duplicate delivery zones not belonging to the supplier' do
      supplier2 = create(:supplier_profile, :random)
      delivery_zone1.update_column(:supplier_profile_id, supplier2.id)
      supplier_duplicator = Suppliers::DuplicateSupplier.new(supplier: supplier).call
      expect(supplier_duplicator).to be_success
      duplicate_supplier = supplier_duplicator.duplicate_supplier
      delivery_zones = duplicate_supplier.delivery_zones

      expect(delivery_zones.size).to eq(1)
      expect(delivery_zones.map(&:radius)).to_not include(delivery_zone1.radius)
      expect(delivery_zones.map(&:radius)).to include(delivery_zone2.radius)
    end
  end

end
