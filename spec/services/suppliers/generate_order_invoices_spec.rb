require 'rails_helper'

RSpec.describe Suppliers::GenerateOrderInvoices, type: :service, suppliers: true, rgi: true do

  let!(:now) { Time.zone.now }
  let!(:past_week) { now - 1.week }
  let!(:past_fornight) { now - 2.weeks }
  let!(:past_month) { now - 1.month }

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier1') }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier2') }

  let!(:order1) { create(:order, :delivered, name: 'order1') }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1, sent_as_rgi_to_xero: [nil, false].sample) }

  let!(:order2) { create(:order, :delivered, name: 'order2') }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier1, sent_as_rgi_to_xero: [nil, false].sample) }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2, sent_as_rgi_to_xero: [nil, false].sample) }

  let!(:order3) { create(:order, :delivered, name: 'order3') }
  let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: supplier2, sent_as_rgi_to_xero: [nil, false].sample) }

  let(:generated_supplier_invoice) { create(:supplier_invoice, :random) }
  let(:generated_rgi_document) { create(:document, :random, kind: 'recipient_generated_invoice', documentable: generated_supplier_invoice) }

  let(:invoice_generator) { double(Suppliers::GenerateInvoice) }

  before do
    # mock invoice generator
    allow(Suppliers::GenerateInvoice).to receive(:new).and_return(invoice_generator)
    invoice_generator_response = OpenStruct.new(success?: true, generated_invoice: generated_supplier_invoice, generated_pdf: generated_rgi_document)
    allow(invoice_generator).to receive(:call).and_return(invoice_generator_response)
  end

  context 'frequency of weekly' do
    let!(:delivery_time) { past_week }
    let!(:billing_frequency) { 'weekly' }

    before do
      [supplier1, supplier2].each do |supplier|
        supplier.supplier_flags.update_column(:billing_frequency, billing_frequency)
      end
      [order1, order2].each_with_index do |order, idx|
        order.update_column(:delivery_at, delivery_time + idx.seconds)
      end
      order3.update_column(:delivery_at, [delivery_time - 10.days, delivery_time + 10.days].sample)
    end

    it 'only generates invoices for orders delivered in the last week' do
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: anything)

      expect(Suppliers::GenerateInvoice).to_not receive(:new).with(supplier: supplier2, invoicable_orders: [order2, order3], invoice_dates: anything, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order2], invoice_dates: anything, notify_supplier: anything) # does not include order3

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end

    it 'passes the invoice dates as previous week' do
      previous_week = now - 1.week
      expected_invoice_dates = {
        from: previous_week.beginning_of_week,
        to: previous_week.end_of_week,
      }
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: anything, invoice_dates: expected_invoice_dates, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: anything, invoice_dates: expected_invoice_dates, notify_supplier: anything)

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end
  end

  context 'frequency of fortnightly' do
    let!(:delivery_time) { [past_week, past_fornight].sample }
    let!(:billing_frequency) { 'fortnightly' }

    before do
      [supplier1, supplier2].each do |supplier|
        supplier.supplier_flags.update_column(:billing_frequency, billing_frequency)
      end
      [order1, order2].each_with_index do |order, idx|
        order.update_column(:delivery_at, delivery_time + idx.seconds)
      end
      order3.update_column(:delivery_at, [delivery_time - 20.days, delivery_time + 20.days].sample)
    end

    it 'only generates invoices for orders delivered in the last fortnightly' do
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: anything)

      expect(Suppliers::GenerateInvoice).to_not receive(:new).with(supplier: supplier2, invoicable_orders: [order2, order3], invoice_dates: anything, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order2], invoice_dates: anything, notify_supplier: anything) # does not include order3

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end

    it 'passes the invoice dates as previous fornight' do
      previous_fortnight = now - 2.weeks
      previous_week = now - 1.week
      expected_invoice_dates = {
        from: previous_fortnight.beginning_of_week,
        to: previous_week.end_of_week,
      }
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: anything, invoice_dates: expected_invoice_dates, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: anything, invoice_dates: expected_invoice_dates, notify_supplier: anything)

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end
  end

  context 'frequency of monthly' do
    let!(:delivery_time) { past_month }
    let!(:billing_frequency) { 'monthly' }

    before do
      [supplier1, supplier2].each do |supplier|
        supplier.supplier_flags.update_column(:billing_frequency, billing_frequency)
      end
      [order1, order2].each_with_index do |order, idx|
        order.update_column(:delivery_at, delivery_time + idx.seconds)
      end
      order3.update_column(:delivery_at, [delivery_time - 40.days, delivery_time + 40.days].sample)
    end

    it 'only generates invoices for orders delivered in the last month' do
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: anything)

      expect(Suppliers::GenerateInvoice).to_not receive(:new).with(supplier: supplier2, invoicable_orders: [order2, order3], invoice_dates: anything, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order2], invoice_dates: anything, notify_supplier: anything) # does not include order3

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end

    it 'passes the invoice dates as previous month' do
      previous_month = now - 1.month
      expected_invoice_dates = {
        from: previous_month.beginning_of_month,
        to: previous_month.end_of_month,
      }
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: anything, invoice_dates: expected_invoice_dates, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: anything, invoice_dates: expected_invoice_dates, notify_supplier: anything)

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end
  end

  context 'for any preference' do
    let!(:billing_frequency) { %w[weekly fortnightly monthly].sample }

    before do
      delivery_time = case billing_frequency
      when 'weekly'
        past_week
      when 'fortnightly'
        [past_week, past_fornight].sample
      when 'monthly'
        past_month
      end
      [order1, order2, order3].each_with_index do |order, idx|
        order.update_column(:delivery_at, delivery_time + idx.seconds)
      end

      [supplier1, supplier2].each do |supplier|
        supplier.supplier_flags.update_column(:billing_frequency, billing_frequency)
      end
    end

    it 'generates supplier invoices for the supplier\'s orders' do
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order2, order3], invoice_dates: anything, notify_supplier: anything)

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.invoices).to include(generated_supplier_invoice)
    end

    context 'Supplier Notifications', notifications: true do
      it 'generates supplier invoices for the supplier\'s orders and notifies the supplier if notify_supplier is passed as true' do
        expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: true)
        expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order2, order3], invoice_dates: anything, notify_supplier: true)

        invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency, notify_supplier: true).call
        expect(invoices_generator).to be_success
      end

      it 'generates supplier invoices for the supplier\'s orders and does not notify the suppliers by default' do
        expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: false)
        expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order2, order3], invoice_dates: anything, notify_supplier: false)

        invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
        expect(invoices_generator).to be_success
      end
    end

    it 'does not generate invoices for supplier orders whose order lines are already pushed to Xero' do
      [order_line11, order_line32].each do |order_line|
        order_line.update_column(:sent_as_rgi_to_xero, true)
      end
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order2], invoice_dates: anything, notify_supplier: anything) # does not include order1
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order2], invoice_dates: anything, notify_supplier: anything) # does not include order3

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end

    it 'does not generate invoices for non-delivered or non-confirmed supplier orders' do
      [order1, order2].each do |order|
        order.update_column(:status, %w[new draft pending amended cancelled paused].sample)
      end

      expect(Suppliers::GenerateInvoice).to_not receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: anything) # does not include order1
      expect(Suppliers::GenerateInvoice).to_not receive(:new).with(supplier: supplier2, invoicable_orders: [order2, order3], invoice_dates: anything, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order3], invoice_dates: anything, notify_supplier: anything) # does not include order2

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end

    context 'with existing supplier invoices' do
      let!(:supplier_invoice23) { create(:supplier_invoice, :random, supplier_profile: supplier2) }
      let!(:order_supplier31) { create(:order_supplier, :random, supplier_profile: supplier2, order: order3, supplier_invoice: supplier_invoice23) }

      it 'does not generate invoices for orders with already generated invoices' do
        expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: anything)
        expect(Suppliers::GenerateInvoice).to_not receive(:new).with(supplier: supplier2, invoicable_orders: [order2, order3], invoice_dates: anything, notify_supplier: anything)
        expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier2, invoicable_orders: [order2], invoice_dates: anything, notify_supplier: anything) # does not include order3

        invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
        expect(invoices_generator).to be_success
      end
    end

    it 'does not generate invoices for suppliers with billing_frequency not equal to passed in frequency' do
      other_frequencies = SupplierFlags::VALID_BILLING_FREQUENCIES - [billing_frequency]
      supplier2.supplier_flags.update_column(:billing_frequency, ([nil, ''] + other_frequencies).sample)

      expect(Suppliers::GenerateInvoice).to receive(:new).with(supplier: supplier1, invoicable_orders: [order1, order2], invoice_dates: anything, notify_supplier: anything)
      expect(Suppliers::GenerateInvoice).to_not receive(:new).with(supplier: supplier2, invoicable_orders: anything, invoice_dates: anything, notify_supplier: anything)

      invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end

    context 'Invoice generation errors' do
      before do
        invoice_generator_response = OpenStruct.new(success?: false, errors: ['invoice-generation-errors'])
        allow(invoice_generator).to receive(:call).and_return(invoice_generator_response)
      end

      it 'returns with error the invoice generation errors' do
        invoices_generator = Suppliers::GenerateOrderInvoices.new(time: now, frequency: billing_frequency).call

        expect(invoices_generator).to_not be_success
        expect(invoices_generator.errors).to include('invoice-generation-errors')
      end
    end # Invoice generation errors
  end

end