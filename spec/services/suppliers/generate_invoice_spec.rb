require 'rails_helper'

RSpec.describe Suppliers::GenerateInvoice, type: :service, suppliers: true, rgi: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_flags) }

  let!(:order1) { create(:order, :delivered) }
  let!(:order2) { create(:order, :delivered) }
  let!(:invoicable_orders) { [order1, order2] }

  let!(:order3) { create(:order, :delivered) }

  let!(:order_line1) { create(:order_line, :random, order: order1, supplier_profile: supplier) }
  let!(:order_line2) { create(:order_line, :random, order: order2, supplier_profile: supplier) }

  let!(:order_supplier1) { create(:order_supplier, :random, order: order1, supplier_profile: supplier) }
  let!(:order_supplier2) { create(:order_supplier, :random, order: order2, supplier_profile: supplier) }

  let!(:generated_rgi_document) { create(:document, :random, kind: 'recipient_generated_invoice')}

  before do
    # mock pdf generator
    pdf_generator = double(Documents::Generate::RecipientGeneratedInvoice)
    allow(Documents::Generate::RecipientGeneratedInvoice).to receive(:new).and_return(pdf_generator)
    allow(pdf_generator).to receive(:call).and_return(generated_rgi_document)

    # mock email sender
    email_sender = double(Suppliers::Emails::SendPurchaseOrderSummaryEmail)
    allow(Suppliers::Emails::SendPurchaseOrderSummaryEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call).and_return(true)
  end

  it 'creates a supplier invoice for passed in orders' do
    invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders).call

    expect(invoice_generator).to be_success
    generated_invoice = invoice_generator.generated_invoice
    expect(generated_invoice).to be_present
    expect(generated_invoice).to be_persisted
    expect(generated_invoice).to be_a(SupplierInvoice)
    expect(generated_invoice.supplier_profile).to eq(supplier)
  end

  it 'attaches the invoice to the orders (via order suppliers)' do
    invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders).call

    expect(invoice_generator).to be_success
    generated_invoice = invoice_generator.generated_invoice
    expect(generated_invoice.order_suppliers).to include(order_supplier1, order_supplier2)
    expect(generated_invoice.orders).to include(order1, order2)
  end

  it 'creates an invoice with calculated values' do
    invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders).call

    expect(invoice_generator).to be_success
    generated_invoice = invoice_generator.generated_invoice

    orders_total = [order_supplier1, order_supplier2].each(&:reload).map(&:total).compact.sum
    expect(generated_invoice.amount).to eq(orders_total)
    expect(generated_invoice.uuid).to be_present # SecureRandom.uuid

    invoice_count = SupplierInvoice.where('created_at >= ?', Time.zone.now.beginning_of_day).count + 1 - 1 # -1 because invoice is now generated
    expected_invoice_number = "RGI-#{Time.zone.now.strftime('%y%m%d')}#{format('%03d', invoice_count)}"
    expect(generated_invoice.number).to eq(expected_invoice_number)
  end

  context 'invoice dates' do
    let!(:from) { Time.zone.now - 1.month }
    let!(:to) { Time.zone.now - 1.day }
    let!(:due) { Time.zone.now + 1.month }

    it 'creates an invoice with default from and to dates' do
      invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders, invoice_dates: [nil, {}].sample).call

      expect(invoice_generator).to be_success
      generated_invoice = invoice_generator.generated_invoice

      previous_day = (Time.zone.now - 1.day).beginning_of_day
      expect(generated_invoice.from_at).to eq(previous_day.beginning_of_day)
      expect(generated_invoice.to_at).to eq(previous_day.end_of_day)
    end

    it 'creates an invoice with the passed from and to dates' do
      invoice_dates = {
        from: from,
        to: to,
      }
      invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders, invoice_dates: invoice_dates).call

      expect(invoice_generator).to be_success
      generated_invoice = invoice_generator.generated_invoice

      expect(generated_invoice.from_at).to eq(invoice_dates[:from])
      expect(generated_invoice.to_at).to eq(invoice_dates[:to])
    end

    context 'due dates' do
      let!(:payment_term_days) { SupplierFlags::VALID_TERM_DAYS.sample }
      before do
        supplier.supplier_flags.update_column(:payment_term_days, payment_term_days)
      end

      it 'sets the due date as per the supplier payment term days' do
        invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders).call

        expect(invoice_generator).to be_success
        generated_invoice = invoice_generator.generated_invoice

        expected_due_date = generated_invoice.to_at.beginning_of_day + payment_term_days.days
        expect(generated_invoice.due_at).to eq(expected_due_date)
      end
    end # due dates
  end # invoice dates

  # done before generating the invoice
  context 'recalculating totals' do
    before do
      total_calculator = double(Orders::CalculateSupplierTotals)
      allow(Orders::CalculateSupplierTotals).to receive(:new).and_return(total_calculator)
      allow(total_calculator).to receive(:call).and_return(true)
    end

    it 'recalculates the supplier totals for each order' do
      expect(Orders::CalculateSupplierTotals).to receive(:new).with(order: order1, supplier: supplier, save_totals: true)
      expect(Orders::CalculateSupplierTotals).to receive(:new).with(order: order2, supplier: supplier, save_totals: true)

      invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders).call
      expect(invoice_generator).to be_success
    end
  end

  it 'generates the RGI pdf for the invoice' do
    expect(Documents::Generate::RecipientGeneratedInvoice).to receive(:new).with(invoice: anything) # invoice generated in SO

    invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders).call
    expect(invoice_generator).to be_success
    expect(invoice_generator.generated_pdf).to eq(generated_rgi_document)
  end

  it 'sends the generated RGI to the supplier' do
    allow(Suppliers::Emails::SendPurchaseOrderSummaryEmail).to receive(:new).with(supplier: supplier, invoice: anything, rgi_document: generated_rgi_document) # invoice generated in SO

    invoice_generator = Suppliers::GenerateInvoice.new(supplier: supplier, invoicable_orders: invoicable_orders).call
    expect(invoice_generator).to be_success
  end

end