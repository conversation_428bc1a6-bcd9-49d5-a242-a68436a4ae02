require 'rails_helper'

RSpec.describe Suppliers::Cache::CategoryGroups, type: :service, suppliers: true do

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags, is_searchable: true) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags, is_searchable: true) }

  let!(:catering_category1) { create(:category, :random, group: 'catering-services') }
  let!(:catering_category2) { create(:category, :random, group: 'catering-services') }

  let!(:pantry_category1) { create(:category, :random, group: 'kitchen-supplies') }
  let!(:pantry_category2) { create(:category, :random, group: 'kitchen-supplies') }

  let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1, categories: [catering_category1, catering_category2]) }
  let!(:menu_section12) { create(:menu_section, :random, supplier_profile: supplier1, categories: [catering_category2]) }

  let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2, categories: [catering_category1, pantry_category1]) }
  let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2, categories: [pantry_category2]) }

  it 'saves both has_<category_group> flags for all searchable suppliers' do
    Suppliers::Cache::CategoryGroups.new.call

    [supplier1, supplier2].each(&:reload)

    expect(supplier1.has_catering_services).to be_truthy
    expect(supplier1.has_kitchen_supplies).to be_falsey

    expect(supplier2.has_catering_services).to be_truthy
    expect(supplier2.has_kitchen_supplies).to be_truthy
  end

  it 'does not update has_<category_group> flags for non searchable suppliers' do
    supplier2.update_column(:is_searchable, false)
    Suppliers::Cache::CategoryGroups.new.call

    [supplier1, supplier2].each(&:reload)

    expect(supplier1.has_catering_services).to be_truthy
    expect(supplier1.has_kitchen_supplies).to be_falsey

    expect(supplier2.has_catering_services).to_not be_truthy
    expect(supplier2.has_kitchen_supplies).to_not be_truthy
  end

  it 'only updates has_<category_group> flags for passed in supplier(s)' do
    Suppliers::Cache::CategoryGroups.new(suppliers: [supplier2]).call

    [supplier1, supplier2].each(&:reload)

    expect(supplier1.has_catering_services).to_not be_truthy
    expect(supplier1.has_kitchen_supplies).to be_falsey

    expect(supplier2.has_catering_services).to be_truthy
    expect(supplier2.has_kitchen_supplies).to be_truthy
  end

  it 'does not take into consideration archived menu sections' do
    menu_section21.update_column(:archived_at, Time.zone.now)
    Suppliers::Cache::CategoryGroups.new(suppliers: [supplier2]).call

    [supplier1, supplier2].each(&:reload)

    # same as before
    expect(supplier1.has_catering_services).to_not be_truthy
    expect(supplier1.has_kitchen_supplies).to be_falsey

    expect(supplier2.has_catering_services).to_not be_truthy # menu section with category is archived
    expect(supplier2.has_kitchen_supplies).to be_truthy
  end

  it 'does not take into consideration a hidden menu sections' do
    menu_section21.update_column(:is_hidden, true)
    Suppliers::Cache::CategoryGroups.new(suppliers: [supplier2]).call

    [supplier1, supplier2].each(&:reload)

    # same as before
    expect(supplier1.has_catering_services).to_not be_truthy
    expect(supplier1.has_kitchen_supplies).to be_falsey

    expect(supplier2.has_catering_services).to_not be_truthy # menu section with category is hidden
    expect(supplier2.has_kitchen_supplies).to be_truthy
  end

end