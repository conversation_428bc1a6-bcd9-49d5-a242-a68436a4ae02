require 'rails_helper'

RSpec.describe Suppliers::Cache::DeliveryDetails, type: :service, supplier: true do

  let!(:suburb) { create(:suburb, :random) }
  let(:supplier) { create(:supplier_profile, :random) }
  let!(:delivery_zone1) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier, delivery_fee: 10, operating_wdays: '0111110', operating_hours_start: 10_000, operating_hours_end: 20_000) }
  let!(:delivery_zone2) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier, delivery_fee: 10, operating_wdays: '0111110', operating_hours_start: 10_000, operating_hours_end: 20_000) }
  let!(:delivery_zone3) { create(:delivery_zone, :random, suburb: suburb, supplier_profile: supplier, delivery_fee: 10, operating_wdays: '0111110', operating_hours_start: 10_000, operating_hours_end: 20_000) }

  it 'saves the delivery fee in supplier if all it\'s delivery zones have the same delivery fee' do
    details_saver = Suppliers::Cache::DeliveryDetails.new(supplier: supplier).call

    expect(details_saver.has_same_delivery_fee).to be_truthy
    supplier.reload
    expect(supplier.delivery_fee).to eq(delivery_zone1.delivery_fee)
  end

  it 'clears supplier delivery fee if at least 1 delivery fee is different' do
    supplier.update_column(:delivery_fee, 20)
    delivery_zone1.update_column(:delivery_fee, 100)
    details_saver = Suppliers::Cache::DeliveryDetails.new(supplier: supplier).call

    expect(details_saver.has_same_delivery_fee).to be_falsey
    supplier.reload
    expect(supplier.delivery_fee).to_not be_present
  end

  it 'saves the operating_days in supplier if all it\'s delivery zones have the same operating_wdays' do
    details_saver = Suppliers::Cache::DeliveryDetails.new(supplier: supplier).call

    expect(details_saver.has_same_operating_days).to be_truthy
    supplier.reload
    expect(supplier.operating_days).to be_present # calculated
  end

  it 'clears supplier operating_days if at least 1 operating wdays is different' do
    supplier.update_column(:operating_days, Faker::Name.name)
    delivery_zone2.update_column(:operating_wdays, '0000001')
    details_saver = Suppliers::Cache::DeliveryDetails.new(supplier: supplier).call

    expect(details_saver.has_same_operating_days).to be_falsey
    supplier.reload
    expect(supplier.operating_days).to_not be_present
  end

  it 'saves the operating_hours in supplier if all it\'s delivery zones have the same operating_hours (start and end)' do
    details_saver = Suppliers::Cache::DeliveryDetails.new(supplier: supplier).call

    expect(details_saver.has_same_operating_hours).to be_truthy
    supplier.reload
    expect(supplier.operating_hours).to be_present # calculated
  end

  it 'clears supplier operating_hours if at least 1 operating hours (start or end) is different' do
    supplier.update_column(:operating_hours, Faker::Name.name)
    delivery_zone3.update_column(:operating_hours_start, 300_000)
    details_saver = Suppliers::Cache::DeliveryDetails.new(supplier: supplier).call

    expect(details_saver.has_same_operating_hours).to be_falsey
    supplier.reload
    expect(supplier.operating_hours).to_not be_present
  end

end
