require 'rails_helper'

RSpec.describe Suppliers::Notifications::SendMenuReminders, type: :service, suppliers: true, notifications: true do
	subject { Suppliers::Notifications::SendMenuReminders.new(time: notification_time).call }

	let!(:notification_time) { Time.zone.now.end_of_month - 2.days }
	let!(:monthly_supplier) { create(:supplier_profile, :random, :with_flags, is_searchable: true, flags: { menu_reminder_frequency: 'monthly', menu_last_updated_on: notification_time - 1.month }) }
	let!(:supplier_3_months) { create(:supplier_profile, :random, :with_flags, is_searchable: true, flags: { menu_reminder_frequency: '3.months', menu_last_updated_on: notification_time - 3.months }) }
	let!(:supplier_6_months) { create(:supplier_profile, :random, :with_flags, is_searchable: true, flags: { menu_reminder_frequency: '6.months', menu_last_updated_on: notification_time - 6.months }) }

	before do
		email_sender = delayed_email_sender = double(Suppliers::Emails::SendMenuReminderEmail)
		allow(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).and_return(email_sender)
		allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
		allow(delayed_email_sender).to receive(:call).and_return(true)
	end

	it 'sends notification for each supplier with reminder frequency and menu last updated before frequency time' do
		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: monthly_supplier, time: notification_time)
		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: supplier_3_months, time: notification_time)
		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: supplier_6_months, time: notification_time)

		notified_suppliers = subject
		expect(notified_suppliers.size).to eq(3)
		expect(notified_suppliers).to include(monthly_supplier, supplier_3_months, supplier_6_months)
	end

	it 'does not send notifications to non-searchable suppliers' do
		supplier_3_months.update_column(:is_searchable, false)

		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: monthly_supplier, time: anything)
		expect(Suppliers::Emails::SendMenuReminderEmail).to_not receive(:new).with(supplier: supplier_3_months, time: anything)
		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: supplier_6_months, time: anything)

		notified_suppliers = subject
		expect(notified_suppliers.size).to eq(2)
		expect(notified_suppliers).to include(monthly_supplier, supplier_6_months)
		expect(notified_suppliers).to_not include(supplier_3_months)
	end

	it 'does not send notifications to suppliers with no menu reminder frequency' do
		monthly_supplier.supplier_flags.update_column(:menu_reminder_frequency, nil)

		expect(Suppliers::Emails::SendMenuReminderEmail).to_not receive(:new).with(supplier: monthly_supplier, time: anything)
		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: supplier_3_months, time: anything)
		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: supplier_6_months, time: anything)

		notified_suppliers = subject
		expect(notified_suppliers.size).to eq(2)
		expect(notified_suppliers).to include(supplier_3_months, supplier_6_months)
		expect(notified_suppliers).to_not include(monthly_supplier)
	end

	it 'does not send notifications to suppliers with no last_menu_updated_on' do
		supplier_6_months.supplier_flags.update_column(:menu_last_updated_on, nil)

		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: monthly_supplier, time: anything)
		expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: supplier_3_months, time: anything)
		expect(Suppliers::Emails::SendMenuReminderEmail).to_not receive(:new).with(supplier: supplier_6_months, time: anything)

		notified_suppliers = subject
		expect(notified_suppliers.size).to eq(2)
		expect(notified_suppliers).to include(monthly_supplier, supplier_3_months)
		expect(notified_suppliers).to_not include(supplier_6_months)
	end

	context 'monthly reminders' do
		let!(:notification_times) { (1..10).map{|num| Time.zone.now.end_of_month + 10.days + num.months }.sort }

		before do
			monthly_supplier.supplier_flags.update_column(:menu_last_updated_on, notification_times.first - 1.month)
		end

		it 'sends the reminder on the same day of the month as menu was last updated, every month' do
			notification_times.each do |notification_time|
				expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: monthly_supplier, time: notification_time)

				notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: notification_time).call
				expect(notified_suppliers).to include(monthly_supplier)
			end
		end

		it 'does not send a reminder on a day not the same as menu last updated on' do
			notification_times.each do |notification_time|
				non_day_notification_time = notification_time + rand(1..10).days
				expect(Suppliers::Emails::SendMenuReminderEmail).to_not receive(:new).with(supplier: monthly_supplier, time: non_day_notification_time)

				notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: non_day_notification_time).call
				expect(notified_suppliers).to_not include(monthly_supplier)
			end
		end
	end

	context '3 month reminders' do
		let!(:notification_times) { (1..10).map{|num| Time.zone.now.end_of_month + 10.days + (num * 3).months }.sort }

		before do
			supplier_3_months.supplier_flags.update_column(:menu_last_updated_on, notification_times.first - 3.months)
		end

		it 'sends the reminder on the same day of the month as menu was last updated, every 3 months' do
			notification_times.each do |notification_time|
				expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: supplier_3_months, time: notification_time)

				notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: notification_time).call
				expect(notified_suppliers).to include(supplier_3_months)
			end
		end

		it 'does not send a reminder on a day not the same as menu last updated on' do
			notification_times.each do |notification_time|
				non_day_notification_time = notification_time + rand(1..10).days
				expect(Suppliers::Emails::SendMenuReminderEmail).to_not receive(:new).with(supplier: supplier_3_months, time: non_day_notification_time)

				notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: non_day_notification_time).call
				expect(notified_suppliers).to_not include(supplier_3_months)
			end
		end

		it 'does not send a reminder months not in the 3 month cycle as menu last updated on' do
			notification_times.each do |notification_time|
				non_3_month_notification = notification_time - [1, 2, 4, 5].sample.months
				expect(Suppliers::Emails::SendMenuReminderEmail).to_not receive(:new).with(supplier: supplier_3_months, time: non_3_month_notification)

				notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: non_3_month_notification).call
				expect(notified_suppliers).to_not include(supplier_3_months)
			end
		end
	end

	context '6 month reminders' do
		let!(:notification_times) { (1..10).map{|num| Time.zone.now.end_of_month + 10.days + (num * 6).months }.sort }

		before do
			supplier_6_months.supplier_flags.update_column(:menu_last_updated_on, notification_times.first - 6.months)
		end

		it 'sends the reminder on the same day of the month as menu was last updated, every 6 months' do
			notification_times.each do |notification_time|
				expect(Suppliers::Emails::SendMenuReminderEmail).to receive(:new).with(supplier: supplier_6_months, time: notification_time)

				notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: notification_time).call
				expect(notified_suppliers).to include(supplier_6_months)
			end
		end

		it 'does not send a reminder on a day not the same as menu last updated on' do
			notification_times.each do |notification_time|
				non_day_notification_time = notification_time + rand(1..10).days
				expect(Suppliers::Emails::SendMenuReminderEmail).to_not receive(:new).with(supplier: supplier_6_months, time: non_day_notification_time)

				notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: non_day_notification_time).call
				expect(notified_suppliers).to_not include(supplier_6_months)
			end
		end

		it 'does not send a reminder months not in the 6 month cycle as menu last updated on' do
			notification_times.each do |notification_time|
				non_3_month_notification = notification_time - [1, 2, 4, 5].sample.months
				expect(Suppliers::Emails::SendMenuReminderEmail).to_not receive(:new).with(supplier: supplier_6_months, time: non_3_month_notification)

				notified_suppliers = Suppliers::Notifications::SendMenuReminders.new(time: non_3_month_notification).call
				expect(notified_suppliers).to_not include(supplier_6_months)
			end
		end
	end

end
