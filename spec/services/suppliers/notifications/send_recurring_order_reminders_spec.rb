require 'rails_helper'

RSpec.describe Suppliers::Notifications::SendRecurringOrderReminders, type: :service, suppliers: true, notifications: true do

  let!(:notification_time) { Time.zone.now.beginning_of_week + 2.days + 10.hours } # 10am Tuesday

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags) }

  let!(:order1) { create(:order, :new, name: 'order1', delivery_at: notification_time + 1.day) }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }

  let!(:order2) { create(:order, :new, name: 'order2', delivery_at: notification_time + 2.days) }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

  let!(:order3) { create(:order, :new, name: 'order3', delivery_at: notification_time + 3.days) }
  let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }
  let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }

  before do
    # make orders recurring
    [order1, order2, order3].each do |order|
      order.update_columns(order_type: 'recurrent', template_id: order.id, recurrent_id: order.id, pattern: '1.week')
    end

    # make suppliers eligible for notifications
    [supplier1, supplier2].each do |supplier|
      supplier.supplier_flags.update_column(:needs_recurring_reminder, true)
    end

    # mock lead time fetcher
    lead_time_fetcher = double(Orders::FetchLeadTime)
    allow(Orders::FetchLeadTime).to receive(:new).and_return(lead_time_fetcher)
    lead_time_response = OpenStruct.new(lead_time: notification_time) # same as notification time
    allow(lead_time_fetcher).to receive(:call).and_return(lead_time_response)

    # mock email sender
    email_sender = double(Suppliers::Emails::SendRecurringOrderReminderEmail)
    allow(Suppliers::Emails::SendRecurringOrderReminderEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call).and_return(true)
  end

  it 'sends a notification to all future pending orders that are cutoff in the last 1 hour' do
    expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to receive(:new).with(order: order1, supplier: supplier1)
    expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to receive(:new).with(order: order2, supplier: supplier2)
    expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to receive(:new).with(order: order3, supplier: supplier1)
    expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to receive(:new).with(order: order3, supplier: supplier2)

    notifications_sender = Suppliers::Notifications::SendRecurringOrderReminders.new(time: notification_time).call
  end

  it 'does not send notifications for non recurring orders' do
    order3.update_column(:order_type, 'one-off')
    expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to_not receive(:new).with(order: order3, supplier: anything)

    notifications_sender = Suppliers::Notifications::SendRecurringOrderReminders.new(time: notification_time).call
  end

  it 'does not send notifications for non-pending orders' do
    order2.update_column(:status, %w[draft cancelled quoted].sample)
    expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to_not receive(:new).with(order: order2, supplier: supplier2)

    notifications_sender = Suppliers::Notifications::SendRecurringOrderReminders.new(time: notification_time).call
  end

  it 'does not send notifications to supplier whose needs_recurring_reminder is set to false' do
    supplier1.supplier_flags.update_column(:needs_recurring_reminder, false)
    expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to_not receive(:new).with(order: order1, supplier: supplier1)
    expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to_not receive(:new).with(order: order3, supplier: supplier1)

    notifications_sender = Suppliers::Notifications::SendRecurringOrderReminders.new(time: notification_time).call
  end

  it 'makes a request to get lead time for all future pending orders' do
    expect(Orders::FetchLeadTime).to receive(:new).with(order: order1)
    expect(Orders::FetchLeadTime).to receive(:new).with(order: order2)
    expect(Orders::FetchLeadTime).to receive(:new).with(order: order3)

    notifications_sender = Suppliers::Notifications::SendRecurringOrderReminders.new(time: notification_time).call
  end

  context 'order lead time is not in current hour' do
    before do
      # mock lead time fetcher for order 1
      order_1_lead_time_fetcher = double(Orders::FetchLeadTime)
      allow(Orders::FetchLeadTime).to receive(:new).with(order: order1).and_return(order_1_lead_time_fetcher)
      order1_lead_time_response = OpenStruct.new(lead_time: notification_time + 1.hour + 10.minutes) # lead time is after notification time hour
      allow(order_1_lead_time_fetcher).to receive(:call).and_return(order1_lead_time_response)

      # mock lead time fetcher for order 2
      order_2_lead_time_fetcher = double(Orders::GetMaximumLeadTime)
      allow(Orders::FetchLeadTime).to receive(:new).with(order: order2).and_return(order_2_lead_time_fetcher)
      order2_lead_time_response = OpenStruct.new(lead_time: notification_time - 1.hour - 10.minutes) # lead time is before notification time hour
      allow(order_2_lead_time_fetcher).to receive(:call).and_return(order2_lead_time_response)
    end

    it 'does not send reminder emails for orders whose lead time is not the recent hour' do
      expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to_not receive(:new).with(order: order1, supplier: supplier1)
      expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to_not receive(:new).with(order: order2, supplier: supplier2)

      expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to receive(:new).with(order: order3, supplier: supplier1)
      expect(Suppliers::Emails::SendRecurringOrderReminderEmail).to receive(:new).with(order: order3, supplier: supplier2)

      notifications_sender = Suppliers::Notifications::SendRecurringOrderReminders.new(time: notification_time).call
    end
  end
end