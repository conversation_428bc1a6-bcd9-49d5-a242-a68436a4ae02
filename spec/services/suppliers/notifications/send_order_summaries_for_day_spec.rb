require 'rails_helper'

RSpec.describe Suppliers::Notifications::SendOrderSummariesForDay, type: :service, orders: true, suppliers: true, notifications: true do

  let!(:delivery_at) { Time.zone.parse('2020-09-15 00:00:00') } # Tuesday

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags) }

  let!(:order1) { create(:order, :confirmed, delivery_at: delivery_at) }
  let!(:order2) { create(:order, :confirmed, delivery_at: delivery_at) }

  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1, status: 'accepted') }
  let!(:order_line12) { create(:order_line, :random, order: order2, supplier_profile: supplier1, status: 'accepted') }

  let!(:order_line21) { create(:order_line, :random, order: order1, supplier_profile: supplier2, status: 'accepted') }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2, status: 'accepted') }

  before do
    email_sender = double(Suppliers::Emails::SendOrdersSummaryEmail)
    allow(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).and_return(email_sender)
    email_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
    allow(email_sender).to receive(:call).and_return(email_sender_response)
  end

  context 'Daily summaries' do
    let!(:summary_type) { 'daily' }

    it 'send an email to the suppliers with the order lines of confirmed orders' do
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line11, order_line12], summary_day: delivery_at, summary_type: summary_type)
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line21, order_line22], summary_day: delivery_at, summary_type: summary_type)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end

    it 'doesn\'t send emails for suppliers with un-accepted order lines' do
      [order_line11, order_line12, order_line21].each do |order_line|
        order_line.update_column(:status, %w[pending rejected amended notified].sample)
      end
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to_not receive(:new).with(supplier: supplier1, order_lines: [order_line11, order_line12], summary_day: delivery_at, summary_type: summary_type)
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line22], summary_day: delivery_at, summary_type: summary_type)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(1)
    end

    it 'doesn\'t send emails for suppliers order lines of unconfirmed orders' do
      order1.update_column(:status, %w[new draft pending amended cancelled].sample)

      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line12], summary_day: delivery_at, summary_type: summary_type)
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line22], summary_day: delivery_at, summary_type: summary_type)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end

    it 'doesn\'t send emails for suppliers order lines of orders not delivered on summary day' do
      order2.update_column(:delivery_at, delivery_at + rand(1..10).days)

      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line11], summary_day: delivery_at, summary_type: summary_type)
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line21], summary_day: delivery_at, summary_type: summary_type)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end
  end

  context 'Morning summaries' do
    let!(:summary_type) { 'morning' }

    it 'send an email to the suppliers with order lines of new/amended/confirmed orders' do
      order1.update_column(:status, %w[new amended confirmed].sample)
      order2.update_column(:status, %w[new amended confirmed].sample)

      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line11, order_line12], summary_day: delivery_at, summary_type: summary_type)
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line21, order_line22], summary_day: delivery_at, summary_type: summary_type)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end

    it 'doesn\'t send emails for suppliers with rejected order lines' do
      [order_line11, order_line12, order_line21].each do |order_line|
        order_line.update_column(:status, 'rejected')
      end

      expect(Suppliers::Emails::SendOrdersSummaryEmail).to_not receive(:new).with(supplier: supplier1, order_lines: [order_line11, order_line12], summary_day: delivery_at, summary_type: summary_type)
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line22], summary_day: delivery_at, summary_type: summary_type)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(1)
    end

    it 'doesn\'t send emails for suppliers order lines of unconfirmed orders' do
      order1.update_column(:status, %w[draft pending cancelled].sample)

      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line12], summary_day: delivery_at, summary_type: summary_type)
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line22], summary_day: delivery_at, summary_type: summary_type)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end

    it 'doesn\'t send emails for suppliers order lines of orders not delivered on summary day' do
      order2.update_column(:delivery_at, delivery_at + rand(1..10).days)

      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line11], summary_day: delivery_at, summary_type: summary_type)
      expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line21], summary_day: delivery_at, summary_type: summary_type)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end

    context 'multi-day summary (friday only)' do
      let!(:friday) { Time.zone.parse('2022-08-12 00:00:00') }
      let!(:monday) { Time.zone.parse('2022-08-15 00:00:00') }

      let!(:order4) { create(:order, status: %w[new amended confirmed].sample, delivery_at: friday) }
      let!(:order5) { create(:order, status: %w[new amended confirmed].sample, delivery_at: friday) }

      let!(:order_line41) { create(:order_line, :random, order: order4, supplier_profile: supplier1, status: 'accepted') }
      let!(:order_line51) { create(:order_line, :random, order: order5, supplier_profile: supplier2, status: 'accepted') }

      let!(:order6) { create(:order, status: %w[new amended confirmed].sample, delivery_at: monday) }
      let!(:order7) { create(:order, status: %w[new amended confirmed].sample, delivery_at: monday) }

      let!(:order_line61) { create(:order_line, :random, order: order6, supplier_profile: supplier1, status: 'accepted') }
      let!(:order_line71) { create(:order_line, :random, order: order7, supplier_profile: supplier2, status: 'accepted') }

      it 'send an email to the suppliers with order lines of new/amended/confirmed orders delivered on summary day' do # same as morning summary
        expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line41], summary_day: friday, summary_type: summary_type)
        expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line51], summary_day: friday, summary_type: summary_type)

        summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: friday, summary_type: summary_type).call
        expect(summary_sender).to be_success
        expect(summary_sender.sent_notifications.size).to eq(2)
      end

      it 'when supplier needs multi-day summary it send an email to the suppliers with order lines of new/amended/confirmed orders delivered on summary day and the next monday' do
        supplier1.supplier_flags.update_column(:needs_multi_day_summary, true)

        expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line41, order_line61], summary_day: friday, summary_type: summary_type)
        expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line51], summary_day: friday, summary_type: summary_type)

        summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: friday, summary_type: summary_type).call
        expect(summary_sender).to be_success
        expect(summary_sender.sent_notifications.size).to eq(2)
      end

      it 'doesn\'t send multi-day summary even if supplier needs multi-day summary on a non-friday summary day' do
        supplier2.supplier_flags.update_column(:needs_multi_day_summary, true)
        next_day_order = create(:order, :confirmed, delivery_at: delivery_at + 3.days)
        next_day_order_line = create(:order_line, :random, order: next_day_order, supplier_profile: supplier2, status: 'accepted')

        expect(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line21, order_line22], summary_day: delivery_at, summary_type: summary_type)
        expect(Suppliers::Emails::SendOrdersSummaryEmail).to_not receive(:new).with(supplier: supplier2, order_lines: [order_line21, order_line22, next_day_order_line], summary_day: delivery_at, summary_type: summary_type)

        summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
        expect(summary_sender).to be_success
      end
    end
  end

  context 'Order Reminders' do
    let!(:summary_type) { 'reminder' }

    before do
      [order1, order2].each do |order|
        order.update_column(:pattern, ['2.weeks', '1.month', '4.weeks'].sample)
      end
      reminder_email_sender = double(Suppliers::Emails::SendOrdersReminderEmail)
      allow(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).and_return(reminder_email_sender)
      reminder_email_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
      allow(reminder_email_sender).to receive(:call).and_return(reminder_email_sender_response)
    end

    it 'send an email to the suppliers of confirmed orders' do
      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line11, order_line12], summary_day: delivery_at)
      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line21, order_line22], summary_day: delivery_at)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end

    it 'doesn\'t send emails for suppliers with un-accepted order lines' do
      [order_line11, order_line12, order_line21].each do |order_line|
        order_line.update_column(:status, 'rejected')
      end

      expect(Suppliers::Emails::SendOrdersReminderEmail).to_not receive(:new).with(supplier: supplier1, order_lines: [order_line11, order_line12], summary_day: delivery_at)
      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line22], summary_day: delivery_at)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(1)
    end

    it 'doesn\'t send emails for suppliers order lines of unconfirmed orders' do
      order1.update_column(:status, %w[draft pending cancelled].sample)

      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line12], summary_day: delivery_at)
      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line22], summary_day: delivery_at)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end

    it 'doesn\'t generate pdf/send emails for suppliers order lines of orders not delivered on summary day' do
      order2.update_column(:delivery_at, delivery_at + rand(1..10).days)

      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line11], summary_day: delivery_at)
      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line21], summary_day: delivery_at)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end

    it 'doesn\'t generate pdf/send emails for suppliers order lines of orders without a pattern of 2.weeks or 1.month' do
      order1.update_column(:pattern, 'random-pattern')

      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier1, order_lines: [order_line12], summary_day: delivery_at)
      expect(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).with(supplier: supplier2, order_lines: [order_line22], summary_day: delivery_at)

      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: summary_type).call
      expect(summary_sender).to be_success
      expect(summary_sender.sent_notifications.size).to eq(2)
    end
  end

  context 'with email sending errors' do
    before do
      email_sender = double(Suppliers::Emails::SendOrdersSummaryEmail)
      allow(Suppliers::Emails::SendOrdersSummaryEmail).to receive(:new).and_return(email_sender)
      email_sender_response = OpenStruct.new(success?: false, sent_notification: nil, errors: ['Summary email Sending ERROR'])
      allow(email_sender).to receive(:call).and_return(email_sender_response)

      reminder_email_sender = double(Suppliers::Emails::SendOrdersReminderEmail)
      allow(Suppliers::Emails::SendOrdersReminderEmail).to receive(:new).and_return(reminder_email_sender)
      reminder_email_sender_response = OpenStruct.new(success?: false, sent_notification: nil, errors: ['Reminder email Sending ERROR'])
      allow(reminder_email_sender).to receive(:call).and_return(reminder_email_sender_response)
    end

    it 'returns with an error if the email sending fails for order summaries' do
      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: %w[daily morning].sample).call
      expect(summary_sender).to_not be_success
      expect(summary_sender.sent_notifications.size).to eq(0)
      expect(summary_sender.errors).to include('Summary email Sending ERROR')
    end

    it 'returns with an error if the email sending fails for order reminders' do
      [order1, order2].each do |order|
        order.update_column(:pattern, ['2.weeks', '1.month'].sample)
      end
      summary_sender = Suppliers::Notifications::SendOrderSummariesForDay.new(summary_day: delivery_at, summary_type: 'reminder').call
      expect(summary_sender).to_not be_success
      expect(summary_sender.sent_notifications.size).to eq(0)
      expect(summary_sender.errors).to include('Reminder email Sending ERROR')
    end
  end

end
