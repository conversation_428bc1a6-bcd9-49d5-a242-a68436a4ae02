require 'rails_helper'

RSpec.describe Suppliers::Notifications::SendFinalClosureEmails, type: :service, suppliers: true, orders: true do

  let!(:start_of_month) { Time.zone.now.beginning_of_month }

  let!(:supplier1) { create(:supplier_profile, :random, close_from: start_of_month, close_to: start_of_month.end_of_month) }
  let!(:supplier2) { create(:supplier_profile, :random, close_from: start_of_month, close_to: start_of_month.end_of_month) }

  let!(:order1) { create(:order, :new, delivery_at: start_of_month + rand(10).days, order_type: 'recurrent', pattern: '1.week') }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }

  let!(:order2) { create(:order, :new, delivery_at: start_of_month + rand(10).days, order_type: 'recurrent', pattern: '1.week') }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

  before do
    email_sender = double(Suppliers::Emails::SendFinalClosureEmail)
    allow(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).and_return(email_sender)
    successfull_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
    allow(email_sender).to receive(:call).and_return(successfull_sender_response)
  end

  it 'sends the notification to suppliers with orders within closure period' do
    expect(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).with(supplier: supplier1, orders: [order1])
    expect(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).with(supplier: supplier2, orders: [order2])

    notifications_sender = Suppliers::Notifications::SendFinalClosureEmails.new.call

    expect(notifications_sender).to be_success
    expect(notifications_sender.sent_notifications.size).to eq(2) # per supplier
  end

  it 'doesn\'t send notification to suppliers with orders not within closure period' do
    order1.update_column(:delivery_at, start_of_month - 1.day)

    expect(Suppliers::Emails::SendFinalClosureEmail).to_not receive(:new).with(supplier: supplier1, orders: [order1])
    expect(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).with(supplier: supplier2, orders: [order2])

    notifications_sender = Suppliers::Notifications::SendFinalClosureEmails.new.call

    expect(notifications_sender).to be_success
    expect(notifications_sender.sent_notifications.size).to eq(1)
  end

  it 'doesn\'t send notification to suppliers with orders within closure period but with status of draft/pending/rejected/cancelled' do
    order2.update_column(:status, %w[draft pending rejected cancelled paused].sample)

    expect(Suppliers::Emails::SendFinalClosureEmail).to_not receive(:new).with(supplier: supplier2, orders: [order2])

    notifications_sender = Suppliers::Notifications::SendFinalClosureEmails.new.call

    expect(notifications_sender).to be_success
    expect(notifications_sender.sent_notifications.size).to eq(1)
  end

  context 'with passed in closure orders' do
    let!(:order3) { create(:order, :new, delivery_at: start_of_month - 1.day) }
    let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }

    it 'sends notiifcation to all suppliers that are passed in' do
      expect(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).with(supplier: supplier1, orders: [order1, order3])
      expect(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).with(supplier: supplier2, orders: [order2])

      notifications_sender = Suppliers::Notifications::SendFinalClosureEmails.new(closure_orders: [order1, order2, order3]).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.sent_notifications.size).to eq(2) # per supplier
    end
  end

  context 'with email sending error' do
    before do
      email_sender = double(Suppliers::Emails::SendFinalClosureEmail)
      allow(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).and_return(email_sender)
      unsuccessfull_sender_response = OpenStruct.new(success?: false, sent_notification: nil, errors: ['sending-error'])
      allow(email_sender).to receive(:call).and_return(unsuccessfull_sender_response)
    end

    it 'returns with errors' do
      expect(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).with(supplier: supplier1, orders: [order1])
      expect(Suppliers::Emails::SendFinalClosureEmail).to receive(:new).with(supplier: supplier2, orders: [order2])

      notifications_sender = Suppliers::Notifications::SendFinalClosureEmails.new.call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.sent_notifications).to be_blank
      expect(notifications_sender.errors).to include('sending-error')
    end
  end

end
