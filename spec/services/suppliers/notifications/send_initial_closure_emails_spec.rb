require 'rails_helper'

RSpec.describe Suppliers::Notifications::SendInitialClosureEmails, type: :service, suppliers: true, orders: true do

  let!(:supplier1) { create(:supplier_profile, :random, is_searchable: true) }
  let!(:supplier2) { create(:supplier_profile, :random, is_searchable: true) }

  before do
    email_sender = double(Suppliers::Emails::SendInitialClosureEmail)
    allow(Suppliers::Emails::SendInitialClosureEmail).to receive(:new).and_return(email_sender)
    successfull_sender_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
    allow(email_sender).to receive(:call).and_return(successfull_sender_response)
  end

  it 'sends the notification to searchable suppliers' do
    expect(Suppliers::Emails::SendInitialClosureEmail).to receive(:new).with(supplier: supplier1)
    expect(Suppliers::Emails::SendInitialClosureEmail).to receive(:new).with(supplier: supplier2)

    notifications_sender = Suppliers::Notifications::SendInitialClosureEmails.new.call

    expect(notifications_sender).to be_success
    expect(notifications_sender.sent_notifications.size).to eq(2) # per supplier
  end

  it 'does not sent closure email to non-searchable suppliers' do
    supplier1.update_column(:is_searchable, false)
    expect(Suppliers::Emails::SendInitialClosureEmail).to_not receive(:new).with(supplier: supplier1)

    notifications_sender = Suppliers::Notifications::SendInitialClosureEmails.new.call

    expect(notifications_sender).to be_success
    expect(notifications_sender.sent_notifications.size).to eq(1)
  end

  context 'with email sending errors' do
    before do
      email_sender = double(Suppliers::Emails::SendInitialClosureEmail)
      allow(Suppliers::Emails::SendInitialClosureEmail).to receive(:new).and_return(email_sender)
      unsuccessfull_sender_response = OpenStruct.new(success?: false, sent_notification: nil, errors: ['sending-error'])
      allow(email_sender).to receive(:call).and_return(unsuccessfull_sender_response)
    end

    it 'returns with errors' do
      expect(Suppliers::Emails::SendInitialClosureEmail).to receive(:new).with(supplier: supplier1)
      expect(Suppliers::Emails::SendInitialClosureEmail).to receive(:new).with(supplier: supplier2)

      notifications_sender = Suppliers::Notifications::SendInitialClosureEmails.new.call

      expect(notifications_sender).to_not be_success
      expect(notifications_sender.sent_notifications).to be_blank
      expect(notifications_sender.errors).to include('sending-error')
    end
  end

end
