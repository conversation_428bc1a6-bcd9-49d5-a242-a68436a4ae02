require 'rails_helper'

RSpec.describe MealPlans::Create, type: :service, meal_plans: true, customers: true do
  
  let!(:customer) { create(:customer_profile, :random, :with_flags) }
  let!(:suburb) { create(:suburb, :random) }
  let!(:on_account_card) { create(:credit_card, :random, :on_account_card) }

  let!(:meal_plan_params) do
    {
      kind: MealPlan::VALID_KINDS.sample,
      name: Faker::Name.name,
      number_of_people: rand(20..30),
      delivery_time: '20:30',
      delivery_address_level: rand(1..10).to_s,
      delivery_address: Faker::Address.street_address,
      delivery_suburb_id: suburb.id,
      delivery_instruction: Faker::Lorem.sentence,
      credit_card_id: on_account_card.id,
      department_identity: Faker::Name.name
    }
  end

  it 'creates a new Meal plan for the passed in customer' do
    meal_plan_creator = MealPlans::Create.new(customer: customer, meal_plan_params: meal_plan_params).call

    expect(meal_plan_creator).to be_success
    created_meal_plan = meal_plan_creator.meal_plan

    expect(created_meal_plan).to be_present
    expect(created_meal_plan).to be_persisted
    expect(created_meal_plan).to be_a(MealPlan)
    expect(created_meal_plan.customer_profile).to eq(customer)
  end

  it 'creates a new Meal plan for with the passed in params' do
    meal_plan_creator = MealPlans::Create.new(customer: customer, meal_plan_params: meal_plan_params).call

    expect(meal_plan_creator).to be_success
    created_meal_plan = meal_plan_creator.meal_plan

    expect(created_meal_plan.kind).to eq(meal_plan_params[:kind])
    expect(created_meal_plan.name).to eq(meal_plan_params[:name])
    expect(created_meal_plan.number_of_people).to eq(meal_plan_params[:number_of_people])
    expect(created_meal_plan.delivery_address_level).to eq(meal_plan_params[:delivery_address_level])
    expect(created_meal_plan.delivery_address).to eq(meal_plan_params[:delivery_address])
    expect(created_meal_plan.delivery_suburb).to eq(suburb)
    expect(created_meal_plan.delivery_instruction).to eq(meal_plan_params[:delivery_instruction])
    expect(created_meal_plan.credit_card).to eq(on_account_card)
    expect(created_meal_plan.department_identity).to eq(meal_plan_params[:department_identity])
  end

  context 'with customer purchase orders' do
    let!(:gst_po) { create(:customer_purchase_order, :random, po_number: 'GST PO', customer_profile: customer) }
    let!(:gst_free_po) { create(:customer_purchase_order, :random, po_number: 'GST-Free PO', customer_profile: customer) }

    it 'saves the meal plan with the passed in PO (based on IDs)' do
      meal_plan_params_with_po = meal_plan_params.merge({
        cpo_id: gst_po.id,
        gst_free_cpo_id: gst_free_po.id,
      })

      meal_plan_creator = MealPlans::Create.new(customer: customer, meal_plan_params: meal_plan_params_with_po).call

      expect(meal_plan_creator).to be_success
      created_meal_plan = meal_plan_creator.meal_plan

      expect(created_meal_plan.customer_purchase_order).to eq(gst_po)
      expect(created_meal_plan.gst_free_customer_purchase_order).to eq(gst_free_po)
    end

    it 'saves the meal plan with the passed in PO (based on PO numbers)' do
      meal_plan_params_with_po = meal_plan_params.merge({
        cpo_id: gst_po.po_number,
        gst_free_cpo_id: gst_free_po.po_number,
      })

      meal_plan_creator = MealPlans::Create.new(customer: customer, meal_plan_params: meal_plan_params_with_po).call

      expect(meal_plan_creator).to be_success
      created_meal_plan = meal_plan_creator.meal_plan

      expect(created_meal_plan.customer_purchase_order).to eq(gst_po)
      expect(created_meal_plan.gst_free_customer_purchase_order).to eq(gst_free_po)
    end

    it 'saves the meal plan with new POs (based on PO numbers)' do
      meal_plan_params_with_po = meal_plan_params.merge({
        cpo_id: SecureRandom.hex(7),
        gst_free_cpo_id: SecureRandom.hex(7),
      })

      meal_plan_creator = MealPlans::Create.new(customer: customer, meal_plan_params: meal_plan_params_with_po).call

      expect(meal_plan_creator).to be_success
      created_meal_plan = meal_plan_creator.meal_plan

      expect(created_meal_plan.customer_purchase_order).to be_present
      expect(created_meal_plan.customer_purchase_order).to be_a(CustomerPurchaseOrder)
      expect(created_meal_plan.customer_purchase_order.customer_profile).to eq(customer)
      expect(created_meal_plan.po_number).to eq(meal_plan_params_with_po[:cpo_id])

      expect(created_meal_plan.gst_free_customer_purchase_order).to be_present
      expect(created_meal_plan.gst_free_customer_purchase_order).to be_a(CustomerPurchaseOrder)
      expect(created_meal_plan.gst_free_customer_purchase_order.customer_profile).to eq(customer)
      expect(created_meal_plan.gst_free_po_number).to eq(meal_plan_params_with_po[:gst_free_cpo_id])
    end
  end

  it 'creates the new meal plan with a UUID' do
    meal_plan_creator = MealPlans::Create.new(customer: customer, meal_plan_params: meal_plan_params).call

    expect(meal_plan_creator).to be_success
    created_meal_plan = meal_plan_creator.meal_plan

    expect(created_meal_plan.uuid).to be_present
  end

  context 'errors' do
    it 'returns with errors if the customer is not passed' do
      meal_plan_creator = MealPlans::Create.new(customer: nil, meal_plan_params: meal_plan_params).call

      expect(meal_plan_creator).to_not be_success
      expect(meal_plan_creator.errors).to include('Cannot create meal plan without a customer')
    end

    it 'returns with errors if the customer requires a PO and the params do not contain any PO info' do
      company = create(:company, :random, customer_profiles: [customer], requires_po: true)

      meal_plan_creator = MealPlans::Create.new(customer: customer.reload, meal_plan_params: meal_plan_params).call

      expect(meal_plan_creator).to_not be_success
      expect(meal_plan_creator.errors).to include('Your orders require a Purchase Order')
    end

    it 'returns with errors if the customer requires a Cost Centre ID and the params do not contain any info' do
      customer.customer_flags.update_column(:requires_department_identity, CustomerFlags::VALID_DEPARTMENT_ID_FORMATS.sample)

      meal_plan_creator = MealPlans::Create.new(customer: customer.reload, meal_plan_params: meal_plan_params.except(:department_identity)).call

      expect(meal_plan_creator).to_not be_success
      expect(meal_plan_creator.errors).to include('Your orders require a Cost Centre ID')
    end
  end

end
