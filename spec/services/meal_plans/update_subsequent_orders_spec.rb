require 'rails_helper'

RSpec.describe MealPlans::UpdateSubsequentOrders, type: :service, meal_plans: true, orders: true do

  let!(:time) { Time.zone.now.beginning_of_day + 1.day }

  let!(:customer) { create(:customer_profile, :random, :with_flags) }
  let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer) }

  let!(:order1) { create(:order, :confirmed, customer_profile: customer, meal_plan: meal_plan, delivery_at: time + 2.days + rand(10..15).hours) }
  let!(:order2) { create(:order, :confirmed, customer_profile: customer, meal_plan: meal_plan, delivery_at: time + 3.days + rand(10..15).hours) }
  let!(:order3) { create(:order, :confirmed, customer_profile: customer, meal_plan: meal_plan, delivery_at: time + 4.days + rand(10..15).hours) }

  let!(:meal_plan_orders) { [order1, order2, order3] }

  context 'mode: update' do
    let!(:mode) { 'update' }

    it 'updates all subsequent orders' do
      orders_updator = MealPlans::UpdateSubsequentOrders.new(meal_plan: meal_plan, mode: mode, since: time).call

      expect(orders_updator).to be_success
      expect(orders_updator.orders).to include(order1, order2, order3)

      updated_orders = orders_updator.orders
      updated_orders.each do |order|
        MealPlans::AttachToOrder::ORDER_FIELDS.each do |field|
          expect(order.send(field)).to eq(meal_plan.send(field))
        end

        MealPlans::AttachToOrder::DELIVERY_FIELDS.each do |field|
          expect(order.send(field)).to eq(meal_plan.send(field))
        end

        MealPlans::AttachToOrder::BILLING_FIELDS.each do |field|
          expect(order.send(field)).to eq(meal_plan.send(field))
        end
      end
    end

    it 'updates the delivery time of all subsequent orders (keep the same dates)' do
      cached_delivery_dates = meal_plan_orders.map do |order|
        [order.id,  order.delivery_at.to_s(:date).dup]
      end.to_h

      orders_updator = MealPlans::UpdateSubsequentOrders.new(meal_plan: meal_plan, mode: mode, since: time).call

      expect(orders_updator).to be_success
      expect(orders_updator.orders).to include(order1, order2, order3)

      updated_orders = orders_updator.orders
      updated_orders.each do |order|
        expect(order.delivery_at.to_s(:date)).to eq(cached_delivery_dates[order.id])
        expect(order.delivery_at.to_s(:time_only)).to eq(meal_plan.delivery_time.to_s(:time_only))
      end
    end
  end

  context 'mode: cancel' do
    let!(:mode) { 'cancel' }

    it 'cancels all subsequent orders' do
      orders_updator = MealPlans::UpdateSubsequentOrders.new(meal_plan: meal_plan, mode: mode, since: time).call

      expect(orders_updator).to be_success
      expect(orders_updator.orders).to include(order1, order2, order3)

      updated_orders = orders_updator.orders
      updated_orders.each do |order|
        expect(order.status).to eq('cancelled')
      end
    end
  end

  context 'mode: update/cancel' do
    let!(:mode) { ['update', 'cancel'].sample }

    it 'does not update/cancel orders which are before the time (defaults to Time.zone.now)' do
      order2.update_column(:delivery_at, time - 1.day)

      orders_updator = MealPlans::UpdateSubsequentOrders.new(meal_plan: meal_plan, mode: mode, since: time).call
      expect(orders_updator).to be_success

      expect(orders_updator.orders).to_not include(order2)
      expect(orders_updator.orders).to include(order1, order3)
    end

    it 'only updates/cancels passed in ORDER IDs if present' do
      orders_updator = MealPlans::UpdateSubsequentOrders.new(meal_plan: meal_plan, mode: mode, order_ids: [order2, order3].map(&:id), since: time).call
      expect(orders_updator).to be_success

      expect(orders_updator.orders).to include(order2, order3)
      expect(orders_updator.orders).to_not include(order1)
    end
  end


end