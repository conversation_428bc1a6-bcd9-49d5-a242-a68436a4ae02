require 'rails_helper'

RSpec.describe MealPlans::AttachToOrder, type: :service, meal_plans: true, orders: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :draft, customer_profile: customer) }
  let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer) }

  it 'attaches the meal plan to the order' do
    meal_plan_order_attacher = MealPlans::AttachToOrder.new(order: order, meal_plan: meal_plan).call

    expect(meal_plan_order_attacher).to be_success
    updated_order = meal_plan_order_attacher.order

    expect(updated_order.meal_plan).to be_present
    expect(updated_order.meal_plan).to eq(meal_plan)
  end

  it 'saves the meal plan order details, delivery details and billing fields against the order' do
    meal_plan_order_attacher = MealPlans::AttachToOrder.new(order: order, meal_plan: meal_plan).call

    expect(meal_plan_order_attacher).to be_success
    updated_order = meal_plan_order_attacher.order

    MealPlans::AttachToOrder::ORDER_FIELDS.each do |field|
      expect(updated_order.send(field)).to eq(meal_plan.send(field))
    end

    MealPlans::AttachToOrder::DELIVERY_FIELDS.each do |field|
      expect(updated_order.send(field)).to eq(meal_plan.send(field))
    end

    MealPlans::AttachToOrder::BILLING_FIELDS.each do |field|
      expect(updated_order.send(field)).to eq(meal_plan.send(field))
    end
  end

  context 'with purchase orders' do
    let!(:gst_po) { create(:customer_purchase_order, :random, customer_profile: customer, po_number: 'GST PO') }
    let!(:gst_free_po) { create(:customer_purchase_order, :random, customer_profile: customer, po_number: 'GST-Free PO') }

    before do
      meal_plan.update_columns(cpo_id: gst_po.id, gst_free_cpo_id: gst_free_po.id)
    end

    it 'attaches the PO to the orders' do
     meal_plan_order_attacher = MealPlans::AttachToOrder.new(order: order, meal_plan: meal_plan).call

     expect(meal_plan_order_attacher).to be_success
     updated_order = meal_plan_order_attacher.order

     expect(updated_order.customer_purchase_order).to eq(gst_po)
     expect(updated_order.gst_free_customer_purchase_order).to eq(gst_free_po)
    end
  end # with purchase orders

end