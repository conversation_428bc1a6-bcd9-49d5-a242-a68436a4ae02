require 'rails_helper'

RSpec.describe MealPlans::Emails::SendOrderReminderEmail, type: :service, emails: true, meal_plans: true, notifications: true do
  include Rails.application.routes.url_helpers

  subject { MealPlans::Emails::SendOrderReminderEmail.new(meal_plan: meal_plan, dates: reminder_dates, frequency: reminder_frequency).call }

  let!(:reminder_frequency) { MealPlan::VALID_REMINDER_FREQUENCIES.sample }

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer, reminder_frequency: reminder_frequency) }
  let!(:reminder_dates) do
    {
      from_date: Time.zone.now.beginning_of_week,
      # to_date: Time.zone.now.end_of_week,
    }
  end

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
  end

  it 'returns the sent email' do
    reminder_sender = subject

    expect(reminder_sender).to be_success
    expect(reminder_sender.sent_notification).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
      template_name: MealPlans::Emails::SendOrderReminderEmail::EMAIL_TEMPLATE,
      recipient: anything,
      subject: anything,
      cc: anything,
      email_options: anything,
      email_variables: anything
    )

    reminder_sender = subject
    expect(reminder_sender).to be_success
  end

  context 'weekly email subject' do
    let!(:reminder_frequency) { 'weekly' }

    it 'send the email with the appropriate subject for weekly reminders' do
      expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "Yordar - Meal Plan `#{meal_plan.name}` orders reminder for upcoming week starting #{reminder_dates[:from_date].to_s(:date_verbose)}",
        cc: anything,
        email_options: anything,
        email_variables: anything
      )

      subject
    end
  end

  context 'monthly email subject' do
    let!(:reminder_frequency) { 'monthly' }

    it 'send the email with the appropriate subject for monthly reminders' do
      expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "Yordar - Meal Plan `#{meal_plan.name}` orders reminder for upcoming month of #{reminder_dates[:from_date].strftime('%B')}",
        cc: anything,
        email_options: anything,
        email_variables: anything
      )

      subject
    end
  end

  it 'sends the email to the correct recipients (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: customer.email_recipient,
      subject: anything,
      cc: 'orders-email',
      email_options: anything,
      email_variables: anything
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{MealPlans::Emails::SendOrderReminderEmail::EMAIL_TEMPLATE}-#{meal_plan.id}-#{reminder_dates[:from_date].to_s(:date_spreadsheet)}"
    expect(Emails::Send).to receive(:new).with(
      template_name: anything,
      recipient: anything,
      subject: anything,
      cc: anything,
      email_options: { fk_id: meal_plan.id, ref: email_ref },
      email_variables: anything
    )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct email data' do
      expected_email_variables = {
        meal_plan: anything,
        account_managers: anything,

        first_name: customer.email_salutation,
        frequency: reminder_frequency,
        reminder_date: reminder_dates[:from_date],
        header_color: :pink
      }

      expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: expected_email_variables
      )

      subject
    end

    it 'sends email with the correct meal plan data' do
      expected_meal_plan = {
        id: meal_plan.id,
        name: meal_plan.name,
        customer_name: customer.name,
        link_url: customer_meal_plans_url(mealUUID: meal_plan.uuid, date: reminder_dates[:from_date].to_s(:date_spreadsheet), host: yordar_credentials(:default_host))
      }
      expected_email_variables = {
        first_name: anything,
        header_color: anything,
        frequency: anything,
        reminder_date: anything,
        account_managers: anything,

        meal_plan: deep_struct(expected_meal_plan)
      }

      expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: expected_email_variables
      )

      subject
    end
  end # email variables

  context 'errors' do
    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(MealPlans::Emails::SendOrderReminderEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        invite_email_sender = MealPlans::Emails::SendOrderReminderEmail.new(meal_plan: meal_plan, dates: reminder_dates, frequency: reminder_frequency)

        expected_error_message = "Failed to send meal plan reminders email for meal plan #{meal_plan.id} - customer #{customer.id}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(invite_email_sender).to receive(:log_errors) # .with(exception: expected_exception, message: expected_error_message, sentry: true)

        invite_email_sender.call
      end
    end # email sender error
  end

end