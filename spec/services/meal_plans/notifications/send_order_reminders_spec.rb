require 'rails_helper'

RSpec.describe MealPlans::Notifications::SendOrderReminders, type: :service, notifications: true, meal_plans: true do

  let!(:reminder_frequency) { MealPlan::VALID_REMINDER_FREQUENCIES.sample }
  let!(:notification_time) { Time.zone.now }
  let!(:delivery_date) do
    case reminder_frequency
    when 'weekly'
      notification_time.beginning_of_week + 1.day + 10.hours
    when 'monthly'
      notification_time.beginning_of_month + 1.day + 12.hours
    end
  end

  let!(:meal_plan1) { create(:meal_plan, :random, reminder_frequency: reminder_frequency) }
  let!(:order1) { create(:order, :confirmed, meal_plan: meal_plan1, delivery_at: delivery_date + 1.day) }

  let!(:meal_plan2) { create(:meal_plan, :random, reminder_frequency: reminder_frequency) }
  let!(:order2) { create(:order, :confirmed, meal_plan: meal_plan2, delivery_at: delivery_date + 2.days) }

  before do
    print reminder_frequency
    # mock email sender
    email_sender = double(MealPlans::Emails::SendOrderReminderEmail)
    allow(MealPlans::Emails::SendOrderReminderEmail).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, sent_email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)
  end

  it 'sends an email for each meal plan that has orders in the current frequency cycle but no orders in the folowing cycle' do
    expect(MealPlans::Emails::SendOrderReminderEmail).to receive(:new).with(meal_plan: meal_plan1, dates: anything, frequency: reminder_frequency)
    expect(MealPlans::Emails::SendOrderReminderEmail).to receive(:new).with(meal_plan: meal_plan2, dates: anything, frequency: reminder_frequency)

    reminder_sender = MealPlans::Notifications::SendOrderReminders.new(time: notification_time, frequency: reminder_frequency).call
    expect(reminder_sender).to be_success
    expect(reminder_sender.notified_meal_plans).to match_array([meal_plan1, meal_plan2])
  end

  it 'send notifications with future order dates' do
    future_dates = case reminder_frequency
    when 'weekly'
      reminder_week = notification_time + 1.week
      {
        from_date: reminder_week.beginning_of_week,
        to_date: reminder_week.end_of_week,
      }
    when 'monthly'
      reminder_month = notification_time + 1.month
      {
        from_date: reminder_month.beginning_of_month,
        to_date: reminder_month.end_of_month,
      }
    end
    expect(MealPlans::Emails::SendOrderReminderEmail).to receive(:new).with(meal_plan: anything, dates: future_dates, frequency: anything)
    expect(MealPlans::Emails::SendOrderReminderEmail).to receive(:new).with(meal_plan: anything, dates: future_dates, frequency: anything)

    reminder_sender = MealPlans::Notifications::SendOrderReminders.new(time: notification_time, frequency: reminder_frequency).call
    expect(reminder_sender).to be_success
  end

  it 'does not send notifications for archived meal plans' do
    meal_plan1.update_column(:archived_at, Time.zone.now) # archive meal plan

    expect(MealPlans::Emails::SendOrderReminderEmail).to_not receive(:new).with(meal_plan: meal_plan1, dates: anything, frequency: reminder_frequency)
    expect(MealPlans::Emails::SendOrderReminderEmail).to receive(:new).with(meal_plan: meal_plan2, dates: anything, frequency: reminder_frequency)

    reminder_sender = MealPlans::Notifications::SendOrderReminders.new(time: notification_time, frequency: reminder_frequency).call
    expect(reminder_sender).to be_success
  end

  it 'does not send notifications for meal plans with no active order in the current cycle' do
    case
    when [true, false].sample # outside_notification_time
      past_deliery_date = case reminder_frequency
      when 'weekly'
        notification_time - 1.week
      else # monthly
        notification_time - 1.month
      end
      order2.update_column(:delivery_at, past_deliery_date)
    else # invalid order status
      order2.update_column(:status, (Order::VALID_ORDER_STATUSES - MealPlans::Notifications::SendOrderReminders::CURRENT_ORDER_STATUSES).sample)
    end
    expect(MealPlans::Emails::SendOrderReminderEmail).to receive(:new).with(meal_plan: meal_plan1, dates: anything, frequency: reminder_frequency)
    expect(MealPlans::Emails::SendOrderReminderEmail).to_not receive(:new).with(meal_plan: meal_plan2, dates: anything, frequency: reminder_frequency)

    reminder_sender = MealPlans::Notifications::SendOrderReminders.new(time: notification_time, frequency: reminder_frequency).call
    expect(reminder_sender).to be_success
  end

  it 'does not send notifications for meal plans with order in the future cycle' do
    future_deliery_date = case reminder_frequency
    when 'weekly'
      notification_time + 1.week
    else # monthly
      notification_time + 1.month
    end
    create(:order, status: MealPlans::Notifications::SendOrderReminders::FUTURE_ORDER_STATUSES.sample, meal_plan: meal_plan1, delivery_at: future_deliery_date)

    expect(MealPlans::Emails::SendOrderReminderEmail).to_not receive(:new).with(meal_plan: meal_plan1, dates: anything, frequency: reminder_frequency)
    expect(MealPlans::Emails::SendOrderReminderEmail).to receive(:new).with(meal_plan: meal_plan2, dates: anything, frequency: reminder_frequency)

    reminder_sender = MealPlans::Notifications::SendOrderReminders.new(time: notification_time, frequency: reminder_frequency).call
    expect(reminder_sender).to be_success
  end

end