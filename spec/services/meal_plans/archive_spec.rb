require 'rails_helper'

RSpec.describe MealPlans::Archive, type: :service, meal_plans: true, customers: true do
  
  let!(:customer) { create(:customer_profile, :random, :with_flags) }
  let!(:suburb) { create(:suburb, :random) }
  let!(:on_account_card) { create(:credit_card, :random, :on_account_card) }

  let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer) }

  before do
    orders_updater = delayed_orders_updater = double(MealPlans::UpdateSubsequentOrders)
    allow(MealPlans::UpdateSubsequentOrders).to receive(:new).and_return(orders_updater)
    allow(orders_updater).to receive(:delay).and_return(delayed_orders_updater)
    allow(delayed_orders_updater).to receive(:call).and_return(true)
  end

  it 'archives the meal plan' do
    meal_plan_archiver = MealPlans::Archive.new(customer: customer, meal_plan: meal_plan).call

    expect(meal_plan_archiver).to be_success
    meal_plan = meal_plan_archiver.meal_plan

    expect(meal_plan.id).to eq(meal_plan.id) # same meal plan
    expect(meal_plan.archived_at).to be_present
    expect(meal_plan).to be_archived
  end

  context 'errors' do
    it 'returns with errors if the customer is not passed' do
      meal_plan_archiver = MealPlans::Archive.new(customer: nil, meal_plan: meal_plan).call

      expect(meal_plan_archiver).to_not be_success
      expect(meal_plan_archiver.errors).to include('Cannot archive meal plan without a customer')
    end

    it 'returns with errors if the meal plan is not passed' do
      meal_plan_archiver = MealPlans::Archive.new(customer: customer, meal_plan: nil).call

      expect(meal_plan_archiver).to_not be_success
      expect(meal_plan_archiver.errors).to include('Cannot archive a missing meal plan')
    end

    it 'returns with errors if the meal plan does not belong to the customer' do
      customer2 = create(:customer_profile, :random)
      meal_plan_archiver = MealPlans::Archive.new(customer: customer2, meal_plan: meal_plan).call

      expect(meal_plan_archiver).to_not be_success
      expect(meal_plan_archiver.errors).to include('You do not have access to this meal plan')
    end

    it 'returns with errors if the meal plan is already archvied' do
      meal_plan.update_column(:archived_at, Time.zone.now)
      meal_plan_archiver = MealPlans::Archive.new(customer: customer, meal_plan: meal_plan).call

      expect(meal_plan_archiver).to_not be_success
      expect(meal_plan_archiver.errors).to include('Meal plan is already archived')
    end
  end

  context 'with pending future orders' do
    let!(:delivery_time) { Time.zone.now + 1.day }
    let!(:pending_order_statuses) { %w[new amended confirmed pending] }

    let!(:order1) { create(:order, customer_profile: customer, meal_plan: meal_plan, status: pending_order_statuses.sample, delivery_at: delivery_time + rand(1..3).days) }
    let!(:order2) { create(:order, customer_profile: customer, meal_plan: meal_plan, status: pending_order_statuses.sample, delivery_at: delivery_time + rand(1..3).days) }
    let!(:order3) { create(:order, customer_profile: customer, meal_plan: meal_plan, status: pending_order_statuses.sample, delivery_at: delivery_time + rand(1..3).days) }

    context 'with mode: of `initial`' do
      let!(:remove_params) do
        {
          mode: 'initial'
        }
      end

      it 'errors out on updating meal plan with pending future orders' do
        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call

        expect(meal_plan_archiver).to_not be_success
        expect(meal_plan_archiver.errors).to include('Your have future order that might be affected by this change')
        expect(meal_plan_archiver.pending_orders).to include(order1, order2, order3)
      end

      it 'only returns pending future orders' do
        order1.update_column(:status, (Order::VALID_ORDER_STATUSES - pending_order_statuses).sample)
        order3.update_column(:delivery_at, delivery_time - 1.day)

        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call

        expect(meal_plan_archiver).to_not be_success
        expect(meal_plan_archiver.errors).to include('Your have future order that might be affected by this change')
        expect(meal_plan_archiver.pending_orders).to include(order2)
        expect(meal_plan_archiver.pending_orders).to_not include(order1, order3)
      end

      it 'does not cancel any future orders' do
        expect(MealPlans::UpdateSubsequentOrders).to_not receive(:new)

        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call
        expect(meal_plan_archiver).to_not be_success
      end
    end # mode = initial

    context 'with mode: of `one-off`' do
      let!(:remove_params) do
        {
          mode: 'one-off'
        }
      end
      it 'successully updates meal plan (if valid) and returns without any errors' do
        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call

        expect(meal_plan_archiver).to be_success
      end

      it 'does not return any pending future orders' do
        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call

        expect(meal_plan_archiver).to be_success
        expect(meal_plan_archiver.pending_orders).to be_blank
      end

      it 'does not cancel any future orders' do
        expect(MealPlans::UpdateSubsequentOrders).to_not receive(:new)

        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call
        expect(meal_plan_archiver).to be_success
      end
    end # mode = one-off

    context 'with mode: of `subsequent`' do
      let!(:remove_params) do
        {
          mode: 'subsequent'
        }
      end
      it 'successully updates meal plan and returns without any errors' do
        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call

        expect(meal_plan_archiver).to be_success
      end

      it 'does not return any pending future orders' do
        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call

        expect(meal_plan_archiver).to be_success
        expect(meal_plan_archiver.pending_orders).to be_blank
      end

      it 'makes a request to cancel meal plans future orders' do
        expect(MealPlans::UpdateSubsequentOrders).to receive(:new).with(meal_plan: meal_plan, mode: 'cancel', order_ids: [])

        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params).call
        expect(meal_plan_archiver).to be_success
      end

      it 'makes a request to cancel meal plans future orders only for passed in order ids' do
        updatable_orders = [order1, order2].map(&:id)
        remove_params_with_orders = remove_params.merge({ order_ids: updatable_orders })
        expect(MealPlans::UpdateSubsequentOrders).to receive(:new).with(meal_plan: meal_plan, mode: 'cancel', order_ids: updatable_orders)

        meal_plan_archiver = MealPlans::Archive.new(customer: customer.reload, meal_plan: meal_plan, remove_params: remove_params_with_orders).call
        expect(meal_plan_archiver).to be_success
      end
    end # mode = subsequent
  end

end
