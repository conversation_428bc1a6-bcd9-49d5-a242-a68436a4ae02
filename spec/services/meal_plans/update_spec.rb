require 'rails_helper'

RSpec.describe MealPlans::Update, type: :service, meal_plans: true, customers: true do
  
  let!(:customer) { create(:customer_profile, :random, :with_flags) }
  let!(:suburb) { create(:suburb, :random) }
  let!(:on_account_card) { create(:credit_card, :random, :on_account_card) }

  let!(:meal_plan) { create(:meal_plan, :random, customer_profile: customer) }

  let!(:meal_plan_params) do
    {
      kind: MealPlan::VALID_KINDS.sample,
      name: Faker::Name.name,
      number_of_people: rand(20..30),
      delivery_time: '20:30',
      delivery_address_level: rand(1..10).to_s,
      delivery_address: Faker::Address.street_address,
      delivery_suburb_id: suburb.id,
      delivery_instruction: Faker::Lorem.sentence,
      credit_card_id: on_account_card.id,
      department_identity: Faker::Name.name
    }
  end

  before do
    orders_updater = delayed_orders_updater = double(MealPlans::UpdateSubsequentOrders)
    allow(MealPlans::UpdateSubsequentOrders).to receive(:new).and_return(orders_updater)
    allow(orders_updater).to receive(:delay).and_return(delayed_orders_updater)
    allow(delayed_orders_updater).to receive(:call).and_return(true)
  end

  it 'updates the meal plan with the passed in params' do
    meal_plan_updater = MealPlans::Update.new(customer: customer, meal_plan: meal_plan, meal_plan_params: meal_plan_params).call

    expect(meal_plan_updater).to be_success
    updated_meal_plan = meal_plan_updater.meal_plan

    expect(updated_meal_plan.id).to eq(meal_plan.id) # same meal plan
    expect(updated_meal_plan.name).to eq(meal_plan_params[:name])
    expect(updated_meal_plan.number_of_people).to eq(meal_plan_params[:number_of_people])
    expect(updated_meal_plan.delivery_address_level).to eq(meal_plan_params[:delivery_address_level])
    expect(updated_meal_plan.delivery_address).to eq(meal_plan_params[:delivery_address])

    expect(updated_meal_plan.delivery_suburb).to eq(suburb)
    expect(updated_meal_plan.delivery_instruction).to eq(meal_plan_params[:delivery_instruction])
    expect(updated_meal_plan.credit_card).to eq(on_account_card) 
  end

  it 'does not update the UUID of the existing meal even if pass in as params' do
    existing_uuid = meal_plan.uuid.dup
    meal_plan_params_with_uuid = meal_plan_params.merge({ uuid: SecureRandom.uuid })
    meal_plan_updater = MealPlans::Update.new(customer: customer, meal_plan: meal_plan, meal_plan_params: meal_plan_params_with_uuid).call

    expect(meal_plan_updater).to be_success
    updated_meal_plan = meal_plan_updater.meal_plan

    expect(updated_meal_plan.id).to eq(meal_plan.id) # same meal plan
    expect(updated_meal_plan.uuid).to_not eq(meal_plan_params_with_uuid[:uuid])

    expect(updated_meal_plan.uuid).to eq(existing_uuid)
  end

  context 'with customer purchase orders' do
    let!(:gst_po) { create(:customer_purchase_order, :random, po_number: 'GST PO', customer_profile: customer) }
    let!(:gst_free_po) { create(:customer_purchase_order, :random, po_number: 'GST-Free PO', customer_profile: customer) }

    it 'saves the meal plan with the passed in PO (based on IDs)' do
      meal_plan_params_with_po = meal_plan_params.merge({
        cpo_id: gst_po.id,
        gst_free_cpo_id: gst_free_po.id,
      })

      meal_plan_updater = MealPlans::Update.new(customer: customer, meal_plan: meal_plan, meal_plan_params: meal_plan_params_with_po).call

      expect(meal_plan_updater).to be_success
      updated_meal_plan = meal_plan_updater.meal_plan

      expect(updated_meal_plan.customer_purchase_order).to eq(gst_po)
      expect(updated_meal_plan.gst_free_customer_purchase_order).to eq(gst_free_po)
    end

    it 'saves the meal plan with the passed in PO (based on PO numbers)' do
      meal_plan_params_with_po = meal_plan_params.merge({
        cpo_id: gst_po.po_number,
        gst_free_cpo_id: gst_free_po.po_number,
      })

      meal_plan_updater = MealPlans::Update.new(customer: customer, meal_plan: meal_plan, meal_plan_params: meal_plan_params_with_po).call

      expect(meal_plan_updater).to be_success
      updated_meal_plan = meal_plan_updater.meal_plan

      expect(updated_meal_plan.customer_purchase_order).to eq(gst_po)
      expect(updated_meal_plan.gst_free_customer_purchase_order).to eq(gst_free_po)
    end

    it 'saves the meal plan with new POs (based on PO numbers)' do
      meal_plan_params_with_po = meal_plan_params.merge({
        cpo_id: SecureRandom.hex(7),
        gst_free_cpo_id: SecureRandom.hex(7),
      })

      meal_plan_updater = MealPlans::Update.new(customer: customer, meal_plan: meal_plan, meal_plan_params: meal_plan_params_with_po).call

      expect(meal_plan_updater).to be_success
      updated_meal_plan = meal_plan_updater.meal_plan

      expect(updated_meal_plan.customer_purchase_order).to be_present
      expect(updated_meal_plan.customer_purchase_order).to be_a(CustomerPurchaseOrder)
      expect(updated_meal_plan.customer_purchase_order.customer_profile).to eq(customer)
      expect(updated_meal_plan.po_number).to eq(meal_plan_params_with_po[:cpo_id])

      expect(updated_meal_plan.gst_free_customer_purchase_order).to be_present
      expect(updated_meal_plan.gst_free_customer_purchase_order).to be_a(CustomerPurchaseOrder)
      expect(updated_meal_plan.gst_free_customer_purchase_order.customer_profile).to eq(customer)
      expect(updated_meal_plan.gst_free_po_number).to eq(meal_plan_params_with_po[:gst_free_cpo_id])
    end
  end

  context 'errors' do
    it 'returns with errors if the customer is not passed' do
      meal_plan_updater = MealPlans::Update.new(customer: nil, meal_plan: meal_plan, meal_plan_params: meal_plan_params).call

      expect(meal_plan_updater).to_not be_success
      expect(meal_plan_updater.errors).to include('Cannot update meal plan without a customer')
    end

    it 'returns with errors if the meal plan is not passed' do
      meal_plan_updater = MealPlans::Update.new(customer: customer, meal_plan: nil, meal_plan_params: meal_plan_params).call

      expect(meal_plan_updater).to_not be_success
      expect(meal_plan_updater.errors).to include('Cannot update a missing meal plan')
    end

    it 'returns with errors if the meal plan does not belong to the customer' do
      customer2 = create(:customer_profile, :random)
      meal_plan_updater = MealPlans::Update.new(customer: customer2, meal_plan: meal_plan, meal_plan_params: meal_plan_params).call

      expect(meal_plan_updater).to_not be_success
      expect(meal_plan_updater.errors).to include('You do not have access to this meal plan')
    end
      
    it 'returns with errors if the customer requires a PO and the params do not contain any PO info' do
      company = create(:company, :random, customer_profiles: [customer], requires_po: true)

      meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params).call

      expect(meal_plan_updater).to_not be_success
      expect(meal_plan_updater.errors).to include('Your orders require a Purchase Order')
    end

    it 'returns with errors if the customer requires a Cost Centre ID and the params do not contain any info' do
      customer.customer_flags.update_column(:requires_department_identity, CustomerFlags::VALID_DEPARTMENT_ID_FORMATS.sample)

      meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params.except(:department_identity)).call

      expect(meal_plan_updater).to_not be_success
      expect(meal_plan_updater.errors).to include('Your orders require a Cost Centre ID')
    end
  end

  context 'with pending future orders' do
    let!(:delivery_time) { Time.zone.now + 1.day }
    let!(:pending_order_statuses) { %w[new amended confirmed pending] }

    let!(:order1) { create(:order, customer_profile: customer, meal_plan: meal_plan, status: pending_order_statuses.sample, delivery_at: delivery_time + rand(1..3).days) }
    let!(:order2) { create(:order, customer_profile: customer, meal_plan: meal_plan, status: pending_order_statuses.sample, delivery_at: delivery_time + rand(1..3).days) }
    let!(:order3) { create(:order, customer_profile: customer, meal_plan: meal_plan, status: pending_order_statuses.sample, delivery_at: delivery_time + rand(1..3).days) }


    context 'with update mode: of `initial`' do
      let!(:update_params) do
        {
          mode: 'initial'
        }
      end

      it 'errors out on updating meal plan with pending future orders' do
        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call

        expect(meal_plan_updater).to_not be_success
        expect(meal_plan_updater.errors).to include('Your have future order that might be affected by this change')
        expect(meal_plan_updater.pending_orders).to include(order1, order2, order3)
      end

      it 'only returns pending future orders' do
        order1.update_column(:status, (Order::VALID_ORDER_STATUSES - pending_order_statuses).sample)
        order3.update_column(:delivery_at, delivery_time - 1.day)

        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call

        expect(meal_plan_updater).to_not be_success
        expect(meal_plan_updater.errors).to include('Your have future order that might be affected by this change')
        expect(meal_plan_updater.pending_orders).to include(order2)
        expect(meal_plan_updater.pending_orders).to_not include(order1, order3)
      end

      it 'does not update any future orders' do
        expect(MealPlans::UpdateSubsequentOrders).to_not receive(:new)

        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call
        expect(meal_plan_updater).to_not be_success
      end
    end # mode = initial

    context 'with update mode: of `one-off`' do
      let!(:update_params) do
        {
          mode: 'one-off'
        }
      end
      it 'successully updates meal plan (if valid) and returns without any errors' do
        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call

        expect(meal_plan_updater).to be_success
      end

      it 'does not return any pending future orders' do
        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call

        expect(meal_plan_updater).to be_success
        expect(meal_plan_updater.pending_orders).to be_blank
      end

      it 'does not update any future orders' do
        expect(MealPlans::UpdateSubsequentOrders).to_not receive(:new)

        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call
        expect(meal_plan_updater).to be_success
      end
    end # mode = one-off

    context 'with update mode: of `subsequent`' do
      let!(:update_params) do
        {
          mode: 'subsequent'
        }
      end
      it 'successully updates meal plan and returns without any errors' do
        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call

        expect(meal_plan_updater).to be_success
      end

      it 'does not return any pending future orders' do
        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call

        expect(meal_plan_updater).to be_success
        expect(meal_plan_updater.pending_orders).to be_blank
      end

      it 'makes a request to update meal plans future orders' do
        expect(MealPlans::UpdateSubsequentOrders).to receive(:new).with(meal_plan: meal_plan, mode: 'update', order_ids: [])

        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call
        expect(meal_plan_updater).to be_success
      end

      it 'makes a request to update meal plans future orders only for passed in order ids' do
        updatable_orders = [order1, order2].map(&:id)
        update_params_with_orders = update_params.merge({ order_ids: updatable_orders })
        expect(MealPlans::UpdateSubsequentOrders).to receive(:new).with(meal_plan: meal_plan, mode: 'update', order_ids: updatable_orders)

        meal_plan_updater = MealPlans::Update.new(customer: customer.reload, meal_plan: meal_plan, meal_plan_params: meal_plan_params, update_params: update_params_with_orders).call
        expect(meal_plan_updater).to be_success
      end
    end # mode = subsequent
  end

end
