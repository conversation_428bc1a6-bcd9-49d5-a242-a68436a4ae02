require 'rails_helper'

RSpec.describe MenuExtras::Clone, type: :service, menu_extras: true, menu_items: true, cloning: true do

  let(:menu_item) { create(:menu_item, :random) }
  let!(:menu_extra_section) { create(:menu_extra_section, :random, menu_item: menu_item) }
  let!(:menu_extra) { create(:menu_extra, :random, menu_extra_section: menu_extra_section, menu_item: menu_item) }

  it 'clones the menu extra with the same data' do
    extra_cloner = MenuExtras::Clone.new(menu_extra: menu_extra).call

    expect(extra_cloner).to be_success
    cloned_menu_extra = extra_cloner.cloned_menu_extra

    expect(cloned_menu_extra).to be_present
    expect(cloned_menu_extra.id).to_not eq(menu_extra.id) # new menu extra

    # different data
    expect(cloned_menu_extra.weight).to_not eq(menu_extra.weight)
    expect(cloned_menu_extra.name).to eq("#{menu_extra.name} - CLONED")

    # cloned data
    expect(cloned_menu_extra.price).to eq(menu_extra.price)
    expect(cloned_menu_extra.menu_extra_section).to eq(menu_extra.menu_extra_section)
    expect(cloned_menu_extra.menu_item).to eq(menu_extra.menu_item)
  end

  it 'clones the menu extra with the same data in a different menu extra section' do
    other_menu_extra_section = create(:menu_extra_section, :random)
    extra_cloner = MenuExtras::Clone.new(menu_extra: menu_extra, menu_extra_section: other_menu_extra_section).call

    expect(extra_cloner).to be_success
    cloned_menu_extra = extra_cloner.cloned_menu_extra

    expect(cloned_menu_extra).to be_present
    expect(cloned_menu_extra.id).to_not eq(menu_extra.id)

    # different data
    expect(cloned_menu_extra.weight).to_not eq(menu_extra.weight)
    expect(cloned_menu_extra.menu_item).to_not eq(menu_extra.menu_item)
    expect(cloned_menu_extra.menu_item).to eq(other_menu_extra_section.menu_item)

    # cloned data
    expect(cloned_menu_extra.name).to eq(menu_extra.name)
    expect(cloned_menu_extra.price).to eq(menu_extra.price)
  end

  it 'does not clone an archvied menu extra' do
    menu_extra.update_column(:archived_at, Time.zone.now)
    extra_cloner = MenuExtras::Clone.new(menu_extra: menu_extra).call

    expect(extra_cloner).to_not be_success
    expect(extra_cloner.errors).to include('Cannot clone an archived menu extra')
  end

end
