require 'rails_helper'

RSpec.describe MenuExtras::Upsert, type: :service, menu_extras: true do

  let(:menu_item) { create(:menu_item, :random) }
  let(:menu_extra_section) { create(:menu_extra_section, :random, menu_item: menu_item) }

  it 'creates a new menu extra within the passed menu extra section' do
    menu_extra_params = { name: Faker::Name.name, price: 10.2, menu_extra_section: menu_extra_section }
    extras_upserter = MenuExtras::Upsert.new(menu_extra_params: menu_extra_params).call

    expect(extras_upserter).to be_success
    created_menu_extra = extras_upserter.menu_extra
    expect(created_menu_extra.menu_extra_section).to eq(menu_extra_section)
    expect(created_menu_extra.menu_item).to eq(menu_extra_section.menu_item) # set menu item as menu extra sections' menu item (will be removed later)
  end

  it 'create a new menu extra with default params' do
    menu_extra_params = { name: Faker::Name.name, price: 10.2, menu_extra_section: menu_extra_section }
    extras_upserter = MenuExtras::Upsert.new(menu_extra_params: menu_extra_params).call

    expect(extras_upserter).to be_success
    created_menu_extra = extras_upserter.menu_extra
    expect(created_menu_extra.weight).to be_present
    expect(created_menu_extra.weight).to eq(MenuExtra.pluck(:weight).compact.max)
  end

  it 'create a new menu extra with passed in params' do
    menu_extra_params = { name: Faker::Name.name, price: 10.2, menu_extra_section: menu_extra_section, weight: 10 }
    extras_upserter = MenuExtras::Upsert.new(menu_extra_params: menu_extra_params).call

    expect(extras_upserter).to be_success
    created_menu_extra = extras_upserter.menu_extra
    expect(created_menu_extra.name).to eq(menu_extra_params[:name])
    expect(created_menu_extra.price).to eq(menu_extra_params[:price].to_s)
    expect(created_menu_extra.weight).to eq(menu_extra_params[:weight])
  end

  it 'sanitizes name (with spaces) when saving' do
    name_with_space = [" #{Faker::Name.name}", "#{Faker::Name.name} "].sample
    menu_extra_params = { name: name_with_space, price: 10.2, menu_extra_section: menu_extra_section }
    extras_upserter = MenuExtras::Upsert.new(menu_extra_params: menu_extra_params).call

    expect(extras_upserter).to be_success
    created_menu_extra = extras_upserter.menu_extra
    expect(created_menu_extra.name).to eq(name_with_space.strip)
  end

  context 'with existing menu extra' do
    let!(:menu_extra) { create(:menu_extra, :random, menu_extra_section: menu_extra_section, menu_item: menu_extra_section.menu_item) }

    it 'does not creates a new menu extra with the name of an existing menu extra (instead it updates) ' do
      extras_name = [menu_extra.name, " #{menu_extra.name}", "#{menu_extra.name} "].sample
      menu_extra_params = { name: extras_name, price: 10.2, menu_extra_section: menu_extra_section }
      extras_upserter = MenuExtras::Upsert.new(menu_extra_params: menu_extra_params).call

      expect(extras_upserter).to be_success
      created_menu_extra = extras_upserter.menu_extra
      expect(created_menu_extra.name).to eq(menu_extra.name)
      expect(created_menu_extra.id).to eq(menu_extra.id) # same menu extra
      expect(created_menu_extra.price).to eq(menu_extra_params[:price].to_s)
    end

    it 'creates a new menu extra with the name of an existing menu extra if it does not belong to the menu extra section' do
      menu_extra_section2 = create(:menu_extra_section, :random)
      menu_extra_params = { name: menu_extra.name, price: 10.2, menu_extra_section: menu_extra_section2 }
      extras_upserter = MenuExtras::Upsert.new(menu_extra_params: menu_extra_params).call

      expect(extras_upserter).to be_success
      created_menu_extra = extras_upserter.menu_extra
      expect(created_menu_extra.id).to_not eq(menu_extra.id) # not the same menu extra
      expect(created_menu_extra.name).to eq(menu_extra.name)
      expect(created_menu_extra.menu_extra_section).to eq(menu_extra_section2)
    end

    it 'creates a new menu extra with the name of an existing menu extra if forced' do
      menu_extra_params = { name: menu_extra.name, price: 10.2, menu_extra_section: menu_extra_section }
      extras_upserter = MenuExtras::Upsert.new(menu_extra_params: menu_extra_params, forced: true).call

      expect(extras_upserter).to be_success
      created_menu_extra = extras_upserter.menu_extra
      expect(created_menu_extra.name).to eq(menu_extra.name)
      expect(created_menu_extra.id).to_not eq(menu_extra.id) # not the same menu extra
    end

    it 'updates the menu extra with passed in params' do
      menu_extra_params = { name: Faker::Name.name, menu_extra_section: menu_extra_section, weight: 10 }
      serving_updator = MenuExtras::Upsert.new(menu_extra: menu_extra, menu_extra_params: menu_extra_params).call

      expect(serving_updator).to be_success
      updated_menu_extra = serving_updator.menu_extra
      expect(updated_menu_extra.id).to eq(menu_extra.id)
      expect(updated_menu_extra.name).to eq(menu_extra_params[:name])
      expect(updated_menu_extra.menu_extra_section).to eq(menu_extra_section)
      expect(updated_menu_extra.weight).to eq(menu_extra_params[:weight])
    end
  end # with existing menu extra
end
