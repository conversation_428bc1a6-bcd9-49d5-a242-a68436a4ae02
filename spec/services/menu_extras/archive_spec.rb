require 'rails_helper'

RSpec.describe MenuExtras::Archive, type: :service, menu_extras: true do

  let!(:menu_extra) { create(:menu_extra, :random) }

  it 'archives the menu extra' do
    archiver = MenuExtras::Archive.new(menu_extra: menu_extra).call

    expect(archiver).to be_success
    expect(menu_extra.reload.archived_at).to be_present
  end

  describe 'errors' do

    it 'cannot archive a missing menu extra' do
      archiver = MenuExtras::Archive.new(menu_extra: nil).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archive a missing menu extra')
    end

    it 'cannot archive already archived menu extra' do
      menu_extra.update_column(:archived_at, Time.zone.now)

      archiver = MenuExtras::Archive.new(menu_extra: menu_extra).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archvie an already archived menu extra')
    end

  end
end
