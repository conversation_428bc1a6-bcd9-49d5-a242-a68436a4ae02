require 'rails_helper'

RSpec.describe MenuItems::Upsert, type: :service, menu_items: true do

  let(:supplier) { create(:supplier_profile, :random, :with_flags, commission_rate: 10, markup: 0) }
  let(:menu_section) { create(:menu_section, :random, supplier_profile: supplier) }

  it 'lets you create a new menu item' do
    menu_item_params = { name: Faker::Name.name, menu_section: menu_section }
    item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

    expect(item_creator).to be_success
    created_menu_item = item_creator.menu_item
    expect(created_menu_item).to be_present
    expect(created_menu_item).to be_persisted
  end

  it 'creates a new menu item within the passed menu section' do
    menu_item_params = { name: Faker::Name.name, menu_section: menu_section }
    item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

    expect(item_creator).to be_success
    created_menu_item = item_creator.menu_item
    expect(created_menu_item.menu_section).to eq(menu_section)
    expect(created_menu_item.supplier_profile).to eq(menu_section.supplier_profile)
  end

  it 'create a new menu item with default params' do
    menu_item_params = { name: Faker::Name.name, menu_section: menu_section }
    item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

    expect(item_creator).to be_success
    created_menu_item = item_creator.menu_item
    expect(created_menu_item.is_hidden).to be_falsey
    expect(created_menu_item.weight).to be_present
    expect(created_menu_item.weight).to eq(MenuItem.pluck(:weight).compact.max)
  end

  it 'create a new menu item with passed in params' do
    supplier2 = create(:supplier_profile, :random, :with_flags)
    menu_item_params = { name: Faker::Name.name, menu_section: menu_section, supplier_profile_id: supplier2.id, weight: 10, is_hidden: true }
    item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

    expect(item_creator).to be_success
    created_menu_item = item_creator.menu_item
    expect(created_menu_item.name).to eq(menu_item_params[:name])
    expect(created_menu_item.supplier_profile).to eq(supplier2)
    expect(created_menu_item.weight).to eq(menu_item_params[:weight])
    expect(created_menu_item.is_hidden).to be_truthy
  end

  it 'sanitizes name (with spaces) when saving' do
    name_with_space = [" #{Faker::Name.name}", "#{Faker::Name.name} "].sample
    menu_item_params = { name: name_with_space, menu_section: menu_section }
    item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

    expect(item_creator).to be_success
    created_menu_item = item_creator.menu_item
    expect(created_menu_item.name).to eq(name_with_space.strip)
  end

  it 'sets the supplier\'s menu last updated datetime' do
    menu_item_params = { name: Faker::Name.name, menu_section: menu_section }
    item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call
    expect(item_creator).to be_success

    expect(supplier.reload.menu_last_updated_on).to be_present
    expect(supplier.reload.menu_last_updated_on).to be_within(2.seconds).of(Time.zone.now)
  end

  context 'with existing menu item' do
    let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }

    it 'does not creates a new menu item with the name of an existing menu item within a suppliers menu section' do
      item_name = [menu_item.name, " #{menu_item.name}", "#{menu_item.name} "].sample
      menu_item_params = { name: item_name, menu_section: menu_section }
      item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

      expect(item_creator).to be_success
      created_menu_item = item_creator.menu_item
      expect(created_menu_item.name).to eq(menu_item.name)
      expect(created_menu_item.id).to eq(menu_item.id) # same menu item
    end

    it 'does not creates a new menu item with the name of an existing menu item within a suppliers menu section' do
      menu_item_params = { name: menu_item.name, supplier_profile: supplier }
      item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

      expect(item_creator).to be_success
      created_menu_item = item_creator.menu_item
      expect(created_menu_item.name).to eq(menu_item.name)
      expect(created_menu_item.id).to eq(menu_item.id) # same menu item
    end

    it 'does not creates a new menu item with the item code of an existing menu item' do
      menu_item.update_column(:sku, SecureRandom.hex(7))
      menu_item_params = { sku: menu_item.sku, name: Faker::Name.name, price: 10.2 }
      item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

      expect(item_creator).to be_success
      created_menu_item = item_creator.menu_item
      expect(created_menu_item.id).to eq(menu_item.id) # same menu item
      expect(created_menu_item.name).to eq(menu_item_params[:name])
      expect(created_menu_item.price).to eq(menu_item_params[:price])
    end

    it 'creates a new menu item with the name of an existing menu item if it does not belong to the supplier' do
      new_supplier = create(:supplier_profile, :random, :with_flags)
      menu_item_params = { name: menu_item.name, menu_section: menu_section, supplier_profile: new_supplier }
      item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params).call

      expect(item_creator).to be_success
      created_menu_item = item_creator.menu_item
      expect(created_menu_item.id).to_not eq(menu_item.id) # not the same menu item
      expect(created_menu_item.name).to eq(menu_item.name)
      expect(created_menu_item.supplier_profile).to eq(new_supplier)
    end

    it 'creates a new menu item with the name of an existing menu item if forced' do
      menu_item_params = { name: menu_item.name, menu_section: menu_section }
      item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params, forced: true).call

      expect(item_creator).to be_success
      created_menu_item = item_creator.menu_item
      expect(created_menu_item.name).to eq(menu_item.name)
      expect(created_menu_item.id).to_not eq(menu_item.id) # not the same menu item
    end

    it 'updates the menu item with passed in params' do
      menu_item_params = { name: Faker::Name.name, menu_section: menu_section, weight: 10, is_hidden: true }
      item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call

      expect(item_updator).to be_success
      updated_menu_item = item_updator.menu_item
      expect(updated_menu_item.id).to eq(menu_item.id)
      expect(updated_menu_item.name).to eq(menu_item_params[:name])
      expect(updated_menu_item.menu_section).to eq(menu_section)
      expect(updated_menu_item.supplier_profile).to eq(supplier)
      expect(updated_menu_item.weight).to eq(menu_item_params[:weight])
      expect(updated_menu_item.is_hidden).to be_truthy
    end

    context 'stock quantity' do
      it 'saves the passed in stock quantity (number)' do
        stock_quantity = [0, 10, 3, '15', '2'].sample
        menu_item_params = { stock_quantity: stock_quantity }
        item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call

        expect(item_updator).to be_success
        updated_menu_item = item_updator.menu_item
        expect(updated_menu_item.stock_quantity).to eq(stock_quantity.to_i)
      end

      it 'sanitizes the stock quantity value as nil if not a number' do
        menu_item.update_column(:stock_quantity, rand(10..100))
        stock_quantity = ['', ' ', nil].sample
        menu_item_params = { stock_quantity: stock_quantity }
        item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call

        expect(item_updator).to be_success
        updated_menu_item = item_updator.menu_item
        expect(updated_menu_item.stock_quantity).to be_nil
      end
    end

    it 're-sets the supplier\'s menu last updated datetime' do
      menu_item_params = { name: Faker::Name.name, menu_section: menu_section, weight: 10, is_hidden: true }
      item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call
      expect(item_updator).to be_success

      expect(supplier.reload.menu_last_updated_on).to be_present
      expect(supplier.reload.menu_last_updated_on).to be_within(2.seconds).of(Time.zone.now)
    end

    context 'with exiting order lines containing serving size' do
      let(:future_order) { create(:order, :draft) }
      let(:location) { create(:location, :random, order: future_order) }
      let!(:item_order_line) { create(:order_line, :random, location: location, menu_item: menu_item, order: future_order, supplier_profile: supplier) }

      let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
      let!(:serving_size_order_line) { create(:order_line, :random, location: location, menu_item: menu_item, serving_size: serving_size, order: future_order, supplier_profile: supplier) }

      it 'updates the future order lines if the menu item name is updated' do
        menu_item_params = { name: Faker::Name.name }
        item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call

        expect(item_updator).to be_success
        item_order_line.reload
        expect(item_order_line.name).to eq(menu_item_params[:name])

        serving_size_order_line.reload
        expect(serving_size_order_line.name).to include(menu_item_params[:name])
      end

      it 'updates the future order lines if the serving size price is updated' do
        menu_item_params = { price: 15.89 }
        item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call

        expect(item_updator).to be_success
        item_order_line.reload
        expect(item_order_line.price.round(2)).to eq(menu_item_params[:price])
        expect(item_order_line.cost.round(2)).to eq(14.30)

        serving_size_order_line.reload
        expect(serving_size_order_line.price.round(2)).to_not eq(menu_item_params[:price])
        expect(serving_size_order_line.cost.round(2)).to_not eq(14.30)
      end

      context 'update as a Delayed Job', delayed_jobs: true do
        let!(:menu_item_params) { { price: 15.89 } }
        let!(:order_line_update_checker) { double(OrderLines::FutureUpdateExists) }

        before do
          # do not run the delayed jobs inline, instead create a Delayed::Job record
          Delayed::Worker.delay_jobs = true

          # mock if a future order line update exists
          allow(OrderLines::FutureUpdateExists).to receive(:new).and_return(order_line_update_checker)
          allow(order_line_update_checker).to receive(:call).and_return(false)
        end

        after do
          # revert back to running delayed jobs in line
          Delayed::Worker.delay_jobs = false
        end

        it 'creates a Delayed Job to update future order lines' do
          item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params)
          expect{ item_updator.call }.to change{ Delayed::Job.count }.from(0).to(1)

          delayed_job = Delayed::Job.last
          expect(delayed_job).to be_present
          expect(delayed_job.handler).to include('MenuItems::UpdateFutureOrderLines')

          job_handler = YAML.load(delayed_job.handler)
          expect(job_handler.menu_item).to eq(menu_item)
        end

        it 'doesn\'t create a second delayed job to update future order lines if one already exists' do
          # create a menu item update
          MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).delay(queue: :data_integrity).call
          expect(Delayed::Job.count).to eq(1)

          # mock an update exists
          allow(order_line_update_checker).to receive(:call).and_return(true)

          item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params)
          expect{ item_updator.call }.not_to change{ Delayed::Job.count }.from(1)
        end
      end # update as a Delayed Job
    end # with existing order lines
  end # with existing menu item
end
