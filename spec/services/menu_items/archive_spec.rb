require 'rails_helper'

RSpec.describe MenuItems::Archive, type: :service, menu_items: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_flags) }
  let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }

  it 'archives the menu item' do
    archiver = MenuItems::Archive.new(menu_item: menu_item).call

    expect(archiver).to be_success
    expect(menu_item.reload.archived_at).to be_present
  end

  it 'sets the supplier\'s menu last updated datetime' do
    archiver = MenuItems::Archive.new(menu_item: menu_item).call
    expect(archiver).to be_success

    expect(supplier.reload.menu_last_updated_on).to be_present
    expect(supplier.reload.menu_last_updated_on).to be_within(2.seconds).of(Time.zone.now)
  end

  context 'with order lines' do
    let!(:order) { create(:order, :confirmed) }
    let!(:order_line) { create(:order_line, :random, order: order, menu_item: menu_item) }

    it 'cannot archive menu item which is used in an order' do
      archiver = MenuItems::Archive.new(menu_item: menu_item).call
      expect(archiver).to_not be_success
      expect(archiver.warnings).to include('The item is still in use!')
    end

    it 'can archive the menu item if the used in non-active orders' do
      order.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[pending quoted new amended confirmed]).sample)

      archiver = MenuItems::Archive.new(menu_item: menu_item).call
      expect(archiver).to be_success
      expect(menu_item.reload.archived_at).to be_present
    end

    it 'can archive menu item with active order lines if forced' do
      archiver = MenuItems::Archive.new(menu_item: menu_item, forced: true).call
      expect(archiver).to be_success
      expect(menu_item.reload.archived_at).to be_present
    end
  end

  context 'with serving_sizes' do
    let!(:serving_size1) { create(:serving_size, :random, menu_item: menu_item) }
    let!(:serving_size2) { create(:serving_size, :random, menu_item: menu_item) }

    it 'can archive menu item with existing active serving size(s)' do
      archiver = MenuItems::Archive.new(menu_item: menu_item).call
      expect(archiver).to be_success
      expect(menu_item.reload.archived_at).to be_present
    end

    it 'archives the associated serving sizes when archiving menu item' do
      archiver = MenuItems::Archive.new(menu_item: menu_item).call

      expect(archiver).to be_success
      expect(serving_size1.reload.archived_at).to be_present
      expect(serving_size2.reload.archived_at).to be_present
    end

    it 'archives menu item with existing archived serving size(s)' do
      [serving_size1, serving_size2].each do |serving_size|
        serving_size.update_column(:archived_at, Time.zone.now)
      end

      archiver = MenuItems::Archive.new(menu_item: menu_item).call
      expect(archiver).to be_success
      expect(menu_item.reload.archived_at).to be_present
    end
  end

  context 'with menu extras sections' do
    let!(:menu_extra_section1) { create(:menu_extra_section, :random, menu_item: menu_item) }
    let!(:menu_extra_section2) { create(:menu_extra_section, :random, menu_item: menu_item) }

    let!(:menu_extra1) { create(:menu_extra, :random, menu_item: menu_item, menu_extra_section: menu_extra_section1) }
    let!(:menu_extra2) { create(:menu_extra, :random, menu_item: menu_item, menu_extra_section: menu_extra_section2) }

    it 'archives menu item even with existing non-archived menu extra sections' do
      archiver = MenuItems::Archive.new(menu_item: menu_item).call
      expect(archiver).to be_success
      expect(menu_item.reload.archived_at).to be_present
    end

    it 'archives associated menu extras when menu item is archived' do
      archiver = MenuItems::Archive.new(menu_item: menu_item).call

      expect(archiver).to be_success
      expect(menu_extra_section1.reload.archived_at).to be_present
      expect(menu_extra_section2.reload.archived_at).to be_present

      expect(menu_extra1.reload.archived_at).to be_present
      expect(menu_extra2.reload.archived_at).to be_present
    end
  end

  context 'with rate cards' do
    let!(:rate_card) { create(:rate_card, :random, menu_item: menu_item) }

    it 'cannot archive menu item with rate cards' do
      archiver = MenuItems::Archive.new(menu_item: menu_item).call
      expect(archiver).to_not be_success
      expect(archiver.warnings).to include('The item is still in use!')
    end

    it 'can archive menu item with rate cards if forced' do
      archiver = MenuItems::Archive.new(menu_item: menu_item, forced: true).call
      expect(archiver).to be_success
      expect(menu_item.reload.archived_at).to be_present
    end
  end

  describe 'errors' do
    it 'cannot archive a missing menu item' do
      archiver = MenuItems::Archive.new(menu_item: nil).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archive a missing menu item')
    end

    it 'cannot archive already archived menu item' do
      menu_item.update_column(:archived_at, Time.zone.now)

      archiver = MenuItems::Archive.new(menu_item: menu_item).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archvie an already archived menu item')
    end
  end
end
