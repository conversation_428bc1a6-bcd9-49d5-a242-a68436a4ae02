require 'rails_helper'

RSpec.describe MenuItems::ListUsageInOrders, type: :service, menu_items: true, orders: true do

  let!(:time) { Time.zone.now }

  let!(:menu_item) { create(:menu_item, :random) }

  let!(:order1) { create(:order, :confirmed, delivery_at: time + 10.days) }
  let!(:order_line1) { create(:order_line, :random, order: order1, menu_item: menu_item) }

  let!(:order2) { create(:order, :confirmed, delivery_at: time + 9.days) }
  let!(:order_line2) { create(:order_line, :random, order: order2, menu_item: menu_item) }

  it 'returned a hashed value of orders and their underlying order lines' do
    in_usage_orders = MenuItems::ListUsageInOrders.new(menu_item: menu_item, since: time).call

    expect(in_usage_orders).to be_present
    expect(in_usage_orders).to be_a(Hash)
    expect(in_usage_orders.keys.sample).to be_a(Order)
    expect(in_usage_orders.values.flatten.sample).to be_a(OrderLine)
  end

  it 'lists all the orders the item is used in since passed in time' do
    in_usage_orders = MenuItems::ListUsageInOrders.new(menu_item: menu_item, since: time).call

    expect(in_usage_orders.keys).to include(order1, order2)
  end

  it 'does not lists orders delivered before the passed in time' do
    order2.update_column(:delivery_at, time - 1.day)
    in_usage_orders = MenuItems::ListUsageInOrders.new(menu_item: menu_item, since: time).call

    expect(in_usage_orders.keys).to_not include(order2)
    expect(in_usage_orders.keys).to include(order1)
  end

  it 'does not lists non-active orders' do
    order1.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[pending quoted new amended confirmed]).sample)
    in_usage_orders = MenuItems::ListUsageInOrders.new(menu_item: menu_item, since: time).call

    expect(in_usage_orders.keys).to_not include(order1)
    expect(in_usage_orders.keys).to include(order2)
  end

  it 'does not list orders not containing the menu item' do
    menu_item2 = create(:menu_item, :random)
    order_line2.update_column(:menu_item_id, menu_item2.id)

    in_usage_orders = MenuItems::ListUsageInOrders.new(menu_item: menu_item, since: time).call

    expect(in_usage_orders.keys).to_not include(order2)
    expect(in_usage_orders.keys).to include(order1)
  end

  it 'lists order lines containing the menu item' do
    in_usage_orders = MenuItems::ListUsageInOrders.new(menu_item: menu_item, since: time).call

    expect(in_usage_orders.values.flatten).to include(order_line1, order_line2)
  end

end