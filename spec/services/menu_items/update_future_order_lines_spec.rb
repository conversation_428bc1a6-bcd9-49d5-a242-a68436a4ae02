require 'rails_helper'

RSpec.describe MenuItems::UpdateFutureOrderLines, type: :service, orders: true, order_lines: true do

  let(:supplier) { create(:supplier_profile, :random, commission_rate: 10, markup: 0) }
  let(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }

  before do
    # mock the emails
    item_email_sender = delayed_item_email_sender = double(Admin::Emails::SendMenuItemAmendedEmail)
    allow(Admin::Emails::SendMenuItemAmendedEmail).to receive(:new).and_return(item_email_sender)
    allow(item_email_sender).to receive(:delay).and_return(delayed_item_email_sender)
    allow(delayed_item_email_sender).to receive(:call).and_return(true)

    rate_card_email_sender = delayed_rate_card_email_sender = double(Admin::Emails::SendRateCardAmendedEmail)
    allow(Admin::Emails::SendRateCardAmendedEmail).to receive(:new).and_return(rate_card_email_sender)
    allow(rate_card_email_sender).to receive(:delay).and_return(delayed_rate_card_email_sender)
    allow(delayed_rate_card_email_sender).to receive(:call).and_return(true)
  end

  context 'for future orders (with status draft/new/amended/pending/confirmed/paused)' do
    let(:future_order) { create(:order, :draft) }
    let(:location) { create(:location, :random, order: future_order) }
    let!(:order_line) { create(:order_line, :random, location: location, menu_item: menu_item, order: future_order, supplier_profile: supplier) }

    it 'updates the price of the order line' do
      new_price = 30.22
      menu_item.update_column(:price, new_price)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.price.round(2)).to eq(30.22) # for 0 supplier markup percent
    end

    it 'updates the cost of the order line' do
      new_price = 15.28
      menu_item.update_column(:price, new_price)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.cost.round(2)).to eq(13.75) # cost for a 10% supplier comission
    end

    it 'does not update order line price if the menu item is a Woolworths product', woolworths: true do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier.id)
      new_price = 30.22
      menu_item.update_column(:price, new_price)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.price.round(2)).to_not eq(30.22)
    end

    it 'does not update order line price if the passed in menu item is blank' do
      new_price = 30.22
      menu_item.update_column(:price, new_price)
      MenuItems::UpdateFutureOrderLines.new(menu_item: nil).call

      order_line.reload
      expect(order_line.price.round(2)).to_not eq(30.22)
    end

    it 'updates the name of the order line' do
      new_name = Faker::Name.name
      menu_item.update_column(:name, new_name)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.name).to eq(new_name)
    end

    it 'updates the order lines on after_commit of menu item' do
      menu_item_params = { name: Faker::Name.name }
      item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call

      expect(item_updator).to be_success

      order_line.reload
      expect(order_line.name).to eq(menu_item_params[:name])

      menu_item_params = { price: 15.89 }
      item_updator = MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call

      expect(item_updator).to be_success

      order_line.reload
      expect(order_line.price.round(2)).to eq(menu_item_params[:price])
      expect(order_line.cost.round(2)).to eq(14.30)
    end

    it 'notifies admin about the changes to associated order lines / orders' do
      expect(Admin::Emails::SendMenuItemAmendedEmail).to receive(:new).with(menu_item: menu_item, serving_size: nil, updated_orders: anything)

      new_price = 30.22
      menu_item.update_column(:price, new_price)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call
    end

    context 'totals calculation' do
      before do
        customer_order_calculator = double(Orders::CalculateCustomerTotals)
        allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(customer_order_calculator)
        allow(customer_order_calculator).to receive(:call).and_return(true)

        supplier_order_calculator = double(Orders::CalculateSupplierTotals)
        allow(Orders::CalculateSupplierTotals).to receive(:new).and_return(supplier_order_calculator)
        allow(supplier_order_calculator).to receive(:call).and_return(true)

        new_price = 30.22
        menu_item.update_column(:price, new_price)
      end

      it 'calculates (and saves) the order totals for the customer' do
        expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: future_order, save_totals: true)

        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call
      end

      it 'calculates (and saves) the order totals for each order supplier' do
        expect(Orders::CalculateSupplierTotals).to receive(:new).with(order: future_order, supplier: supplier, save_totals: true)

        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call
      end
    end

    context 'with serving sizes' do
      let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
      let!(:serving_size_order_line) { create(:order_line, :random, location: location, menu_item: menu_item, serving_size: serving_size, order: future_order, supplier_profile: supplier) }

      it 'updates the price and cost of the order line' do
        new_price = 30.22
        serving_size.update_column(:price, new_price)
        MenuItems::UpdateFutureOrderLines.new(menu_item: serving_size.menu_item).call

        serving_size_order_line.reload
        expect(serving_size_order_line.price.round(2)).to eq(30.22) # for 0 supplier markup percent
        expect(serving_size_order_line.cost.round(2)).to eq(27.20) # cost for a 10% supplier comission
      end

      it 'updates the name of the order line' do
        new_name = Faker::Name.name
        serving_size.update_column(:name, new_name)
        MenuItems::UpdateFutureOrderLines.new(menu_item: serving_size.menu_item).call

        serving_size_order_line.reload
        expect(serving_size_order_line.name).to include(new_name)
      end

      it 'updates the order lines (for the serving_size) on update of serving size (using Upsert)' do
        serving_size_params = { name: Faker::Name.name }
        serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call

        expect(serving_updator).to be_success

        serving_size_order_line.reload
        order_line.reload
        expect(serving_size_order_line.name).to include(serving_size_params[:name])
        expect(order_line.name).to_not include(serving_size_params[:name])

        serving_size_params = { price: 15.89 }
        serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call

        expect(serving_updator).to be_success

        serving_size_order_line.reload
        expect(serving_size_order_line.price.round(2)).to eq(serving_size_params[:price])
        expect(serving_size_order_line.cost.round(2)).to eq(14.30)

        order_line.reload
        expect(order_line.price.round(2)).to_not eq(serving_size_params[:price])
        expect(order_line.cost.round(2)).to_not eq(14.30)
      end

      it 'notifies admin about the changes to associated order lines / orders' do
        expect(Admin::Emails::SendMenuItemAmendedEmail).to receive(:new).with(menu_item: menu_item, serving_size: serving_size, updated_orders: anything)

        new_name = Faker::Name.name
        serving_size.update_column(:name, new_name)
        MenuItems::UpdateFutureOrderLines.new(menu_item: serving_size.menu_item, serving_size: serving_size).call
      end
    end

    context 'with menu item rate cards and order belonging to the same company (via customer)' do
      let(:company) { create(:company, :random) }
      let(:customer) { create(:customer_profile, :random, company: company) }

      let(:rate_carded_future_order) { create(:order, :draft, customer_profile: customer, name: 'FUTURE ORDE RAT') }
      let(:rate_carded_location) { create(:location, :random, order: rate_carded_future_order) }

      let!(:menu_item_rate_card) { create(:rate_card, :random, menu_item: menu_item, company: company, price: 20.12, cost: 10.12) }
      let!(:menu_item_rate_card_order_line) { create(:order_line, :random, location: rate_carded_location, menu_item: menu_item, order: rate_carded_future_order, supplier_profile: supplier, price: 20.12, cost: 10.12) }

      it 'updates the price and cost of order lines' do
        new_price = 30.22
        new_cost = 20.22
        menu_item_rate_card.update_column(:price, new_price)
        menu_item_rate_card.update_column(:cost, new_cost)
        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: menu_item_rate_card).call

        menu_item_rate_card_order_line.reload
        expect(menu_item_rate_card_order_line.price.round(2)).to eq(new_price)
        expect(menu_item_rate_card_order_line.cost.round(2)).to eq(new_cost)
      end

      it 'updates the price and cost of the order line after the rate card has been updated' do
        new_price = 30.22
        new_cost = 20.22
        RateCards::Upsert.new(rate_card: menu_item_rate_card, rate_card_params: { price: new_price, cost: new_cost }).call

        menu_item_rate_card_order_line.reload
        expect(menu_item_rate_card_order_line.price.round(2)).to eq(new_price)
        expect(menu_item_rate_card_order_line.cost.round(2)).to eq(new_cost)
      end

      it 'reverts back price of order line to original menu item if rate card is deleted/archived' do
        RateCards::Archive.new(rate_card: menu_item_rate_card).call

        menu_item_rate_card_order_line.reload
        expect(menu_item_rate_card_order_line.price.round(2)).to eq(menu_item.price) # 0 % supplier markup
        expect(menu_item_rate_card_order_line.cost.round(2)).to eq(menu_item.cost.round(2))
      end

      it 'notifies admin about the changes to associated order lines / orders' do
        expect(Admin::Emails::SendRateCardAmendedEmail).to receive(:new).with(rate_card: menu_item_rate_card, updated_orders: anything)

        new_price = 30.22
        new_cost = 20.22
        menu_item_rate_card.update_column(:price, new_price)
        menu_item_rate_card.update_column(:cost, new_cost)
        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: menu_item_rate_card).call
      end

      it 'does not update the order line from an order not connected to same rate card company (via customer)' do
        if [true, false].sample
          rate_carded_future_order.update_column(:customer_profile_id, nil) # disconnect customer from order
        else
          other_company = create(:company, :random)
          menu_item_rate_card.update_column(:company_id, other_company.id) # attach rate card to a different company (using update_column to not trigger another after save callback)
        end

        new_price = 30.22
        new_cost = 20.22
        if [true, false].sample
          menu_item_rate_card.update_column(:price, new_price)
          menu_item_rate_card.update_column(:cost, new_cost)
          MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: menu_item_rate_card).call
        else
          RateCards::Upsert.new(rate_card: menu_item_rate_card.reload, rate_card_params: { price: new_price, cost: new_cost }).call
        end

        menu_item_rate_card_order_line.reload
        expect(menu_item_rate_card_order_line.price.round(2)).to_not eq(new_price)
        expect(menu_item_rate_card_order_line.cost.round(2)).to_not eq(new_price * 0.9)

        # order line keeps original values
        expect(menu_item_rate_card_order_line.price.round(2)).to eq(20.12)
        expect(menu_item_rate_card_order_line.cost.round(2)).to eq(10.12)
      end

      it 'does not update the order line price of an order line connected to a rate card on update of menu item' do
        new_price = 99.21
        if [true, false].sample
          menu_item.update_column(:price, new_price)
          MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call
        else
          menu_item_params = { price: new_price }
          MenuItems::Upsert.new(menu_item: menu_item, menu_item_params: menu_item_params).call
        end

        menu_item_rate_card_order_line.reload
        expect(menu_item_rate_card_order_line.price.round(2)).to_not eq(new_price)
        expect(menu_item_rate_card_order_line.cost.round(2)).to_not eq(new_price * 0.9)

        # order line keeps original values
        expect(menu_item_rate_card_order_line.price.round(2)).to eq(20.12)
        expect(menu_item_rate_card_order_line.cost.round(2)).to eq(10.12)
      end
    end

    context 'with a serving size rate cards and order belonging to the same company (via customer)' do
      let(:company) { create(:company, :random) }
      let(:customer) { create(:customer_profile, :random, company: company) }

      let(:rate_carded_future_order) { create(:order, :draft, customer_profile: customer, name: 'FUTURE ORDE RAT') }
      let(:rate_carded_location) { create(:location, :random, order: rate_carded_future_order) }
      let(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }

      let!(:serving_size_rate_card) { create(:rate_card, :random, menu_item: menu_item, serving_size: serving_size, company: company, price: 20.12, cost: 10.12) }
      let!(:serving_size_rate_card_order_line) { create(:order_line, :random, location: rate_carded_location, menu_item: menu_item, serving_size: serving_size, order: rate_carded_future_order, supplier_profile: supplier, price: 20.12, cost: 10.12) }

      it 'updates the price and cost of order lines' do
        new_price = 30.22
        new_cost = 20.22
        serving_size_rate_card.update_column(:price, new_price)
        serving_size_rate_card.update_column(:cost, new_cost)
        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: serving_size_rate_card).call

        serving_size_rate_card_order_line.reload
        expect(serving_size_rate_card_order_line.price.round(2)).to eq(new_price)
        expect(serving_size_rate_card_order_line.cost.round(2)).to eq(new_cost)
      end

      it 'updates the price and cost of the order line as an after save' do
        new_price = 30.22
        new_cost = 20.22
        RateCards::Upsert.new(rate_card: serving_size_rate_card, rate_card_params: { price: new_price, cost: new_cost }).call

        serving_size_rate_card_order_line.reload
        expect(serving_size_rate_card_order_line.price.round(2)).to eq(new_price)
        expect(serving_size_rate_card_order_line.cost.round(2)).to eq(new_cost)
      end

      it 'reverts back price of order line to original menu item if rate card is deleted' do
        RateCards::Archive.new(rate_card: serving_size_rate_card).call

        serving_size_rate_card_order_line.reload
        expect(serving_size_rate_card_order_line.price.round(2)).to eq(serving_size.price) # 0 % supplier markup
        expect(serving_size_rate_card_order_line.cost.round(2)).to eq(serving_size.cost.round(2))
      end

      it 'notifies admin about the changes to associated order lines / orders' do
        expect(Admin::Emails::SendRateCardAmendedEmail).to receive(:new).with(rate_card: serving_size_rate_card, updated_orders: anything)

        new_price = 30.22
        new_cost = 20.22
        serving_size_rate_card.update_column(:price, new_price)
        serving_size_rate_card.update_column(:cost, new_cost)
        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: serving_size_rate_card).call
      end

      it 'does not update the order line from an order not connected to same rate card company (via customer)' do
        if [true, false].sample
          rate_carded_future_order.update_column(:customer_profile_id, nil) # disconnect customer from order
        else
          other_company = create(:company, :random)
          serving_size_rate_card.update_column(:company_id, other_company.id) # attach rate card to a different company (using update_column to not trigger another after save callback)
        end

        new_price = 30.22
        new_cost = 20.22
        if [true, false].sample
          serving_size_rate_card.update_column(:price, new_price)
          serving_size_rate_card.update_column(:cost, new_cost)
          MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: serving_size_rate_card).call
        else
          RateCards::Upsert.new(rate_card: serving_size_rate_card.reload, rate_card_params: { price: new_price, cost: new_cost }).call
        end

        serving_size_rate_card_order_line.reload
        expect(serving_size_rate_card_order_line.price.round(2)).to_not eq(new_price)
        expect(serving_size_rate_card_order_line.cost.round(2)).to_not eq(new_price * 0.9)

        # order line keeps original values
        expect(serving_size_rate_card_order_line.price.round(2)).to eq(20.12)
        expect(serving_size_rate_card_order_line.cost.round(2)).to eq(10.12)
      end

      it 'does not update the order line price of an order line connected to a rate card on update of menu item' do
        new_price = 99.21
        if [true, false].sample
          serving_size.update_column(:price, new_price)
          MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size).call
        else
          serving_size_params = { price: new_price }
          serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call
          expect(serving_updator).to be_success
        end

        serving_size_rate_card_order_line.reload
        expect(serving_size_rate_card_order_line.price.round(2)).to_not eq(new_price)
        expect(serving_size_rate_card_order_line.cost.round(2)).to_not eq(new_price * 0.9)

        # order line keeps original values
        expect(serving_size_rate_card_order_line.price.round(2)).to eq(20.12)
        expect(serving_size_rate_card_order_line.cost.round(2)).to eq(10.12)
      end

      it 'does not update the order line price of an order line connected to a rate card on update of serving size' do
        new_price = 109.21
        if [true, false].sample
          serving_size.update_column(:price, new_price)
          MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, serving_size: serving_size).call
        else
          serving_size_params = { price: new_price }
          serving_updator = ServingSizes::Upsert.new(serving_size: serving_size, serving_size_params: serving_size_params).call
          expect(serving_updator).to be_success
        end

        serving_size_rate_card_order_line.reload
        expect(serving_size_rate_card_order_line.price.round(2)).to_not eq(new_price)
        expect(serving_size_rate_card_order_line.cost.round(2)).to_not eq(new_price * 0.9)

        # order line keeps original values
        expect(serving_size_rate_card_order_line.price.round(2)).to eq(20.12)
        expect(serving_size_rate_card_order_line.cost.round(2)).to eq(10.12)
      end
    end

    context 'for future recurring order (where order lines share location)' do
      let(:future_order2) { create(:order, :draft) }
      let!(:order_line) { create(:order_line, :random, location: location, menu_item: menu_item, order: future_order2, supplier_profile: supplier) }

      it 'updates the price of the order line' do
        new_price = 30.22
        menu_item.update_column(:price, new_price)
        MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

        order_line.reload
        expect(order_line.price.round(2)).to eq(30.22) # for 0 supplier markup percent
      end
    end

  end

  context 'for past orders (with status delivered)' do
    let(:past_order) { create(:order, :delivered) }
    let(:location) { create(:location, :random, order: past_order) }
    let!(:order_line) { create(:order_line, :random, location: location, menu_item: menu_item, order: past_order, supplier_profile: supplier) }

    it 'does not update the price of the order line' do
      new_price = 30.22
      menu_item.update_column(:price, new_price)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.price.round(2)).to_not eq(30.22) # for 0 supplier markup percent
    end

    it 'does not update the cost of the order line' do
      new_price = 15.28
      menu_item.update_column(:price, new_price)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.cost.round(2)).to_not eq(13.75) # cost for a 10% supplier comission
    end

    it 'does not update the name of the order line' do
      new_name = Faker::Name.name
      menu_item.update_column(:name, new_name)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.name).to_not eq(new_name)
    end
  end

  context 'for past orders (non-delivered)' do
    let!(:beginning_of_week) { Time.zone.now.beginning_of_week }
    let(:past_order) { create(:order, status: %w[draft new amended pending confirmed paused].sample, delivery_at: beginning_of_week + 2.days + 2.hours) }
    let(:location) { create(:location, :random, order: past_order) }
    let!(:order_line) { create(:order_line, :random, location: location, menu_item: menu_item, order: past_order, supplier_profile: supplier) }
    let!(:new_price) { 30.22 }

    before do
      menu_item.update_column(:price, new_price)
    end

    it 'updates the order line if the order it belongs to was delivered in the current week' do
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.price.round(2)).to eq(30.22) # for 0 supplier markup percent
      expect(order_line.cost.round(2)).to eq(27.2) # cost for a 10% supplier comission
    end

    it 'does not update the order line if the order it belongs to was delivered before the current week' do
      past_order.update_column(:delivery_at, beginning_of_week - 2.days + 2.hours)
      MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item).call

      order_line.reload
      expect(order_line.price.round(2)).to_not eq(30.22) # for 0 supplier markup percent
      expect(order_line.cost.round(2)).to_not eq(27.2) # cost for a 10% supplier comission
    end
  end

end
