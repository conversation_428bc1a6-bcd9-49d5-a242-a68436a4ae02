require 'rails_helper'

RSpec.describe MenuItems::Clone, type: :servive, menu_items: true, cloning: true do

  let(:supplier) { create(:supplier_profile, :random) }
  let(:menu_section) { create(:menu_section, :random, supplier_profile: supplier) }
  let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier, menu_section: menu_section) }

  it 'clones the menu item' do
    item_cloner = MenuItems::Clone.new(menu_item: menu_item).call

    expect(item_cloner).to be_success
    expect(item_cloner.cloned_item).to be_present
    expect(item_cloner.cloned_item).to be_a(MenuItem)
  end

  it 'creats a new menu item with cloned params' do
    item_cloner = MenuItems::Clone.new(menu_item: menu_item).call

    expect(item_cloner).to be_success
    cloned_item = item_cloner.cloned_item
    expect(cloned_item.id).to_not eq(menu_item.id) # a different menu item

    # new data
    expect(cloned_item.weight).to_not eq(menu_item.weight)
    expect(cloned_item.name).to eq("#{menu_item.name} - CLONED")

    # cloned data
    expect(cloned_item.menu_section).to eq(menu_item.menu_section)
    expect(cloned_item.supplier_profile).to eq(menu_item.supplier_profile)
    expect(cloned_item.price).to eq(menu_item.price)
    expect(cloned_item.is_vegan).to eq(menu_item.is_vegan)
    expect(cloned_item.is_vegetarian).to eq(menu_item.is_vegetarian)
    expect(cloned_item.is_gluten_free).to eq(menu_item.is_gluten_free)
    expect(cloned_item.is_hidden).to eq(menu_item.is_hidden)
    expect(cloned_item.is_gst_free).to eq(menu_item.is_gst_free)
  end

  context 'with serving sizes' do
    let!(:serving_size1) { create(:serving_size, :random, menu_item: menu_item) }
    let!(:serving_size2) { create(:serving_size, :random, menu_item: menu_item) }
    let!(:archived_serving_size) { create(:serving_size, :random, menu_item: menu_item, archived_at: Time.zone.now) }

    it 'clones any non-archived serving sizes' do
      item_cloner = MenuItems::Clone.new(menu_item: menu_item).call

      expect(item_cloner).to be_success
      cloned_item = item_cloner.cloned_item
      expect(cloned_item.id).to_not eq(menu_item.id) # a different menu item

      cloned_serving_sizes = cloned_item.serving_sizes
      expect(cloned_serving_sizes).to be_present

      expect(cloned_serving_sizes.map(&:id)).to_not include(serving_size1.name, serving_size2.name) # new serving sizes
      expect(cloned_serving_sizes.map(&:weight)).to_not include(serving_size1.weight, serving_size2.weight) # new serving size weights

      expect(cloned_serving_sizes.map(&:name)).to include(serving_size1.name, serving_size2.name)
      expect(cloned_serving_sizes.map(&:price)).to include(serving_size1.price, serving_size2.price)
      expect(cloned_serving_sizes.map(&:archived_at)).to match_array([nil, nil])
    end

    it 'does not clone archived serving sizes' do
      item_cloner = MenuItems::Clone.new(menu_item: menu_item).call

      expect(item_cloner).to be_success
      cloned_item = item_cloner.cloned_item
      expect(cloned_item.id).to_not eq(menu_item.id) # a different menu item

      cloned_serving_sizes = cloned_item.serving_sizes
      expect(cloned_serving_sizes).to be_present # clones non-archived serving sizes

      expect(cloned_serving_sizes.map(&:id)).to_not include(archived_serving_size.id)
      expect(cloned_serving_sizes.map(&:weight)).to_not include(archived_serving_size.weight) # new serving size weights

      expect(cloned_serving_sizes.map(&:name)).to_not include(archived_serving_size.name)
      expect(cloned_serving_sizes.map(&:price)).to_not include(archived_serving_size.price)
    end
  end

  context 'with menu extra sections' do
    let!(:menu_extra_section1) { create(:menu_extra_section, :random, menu_item: menu_item) }
    let!(:menu_extra_section2) { create(:menu_extra_section, :random, menu_item: menu_item) }
    let!(:archived_menu_extra_section) { create(:menu_extra_section, :random, menu_item: menu_item, min_limit: 5, max_limit: 10, archived_at: Time.zone.now) }

    let!(:menu_extra1) { create(:menu_extra, :random, menu_extra_section: menu_extra_section1, menu_item: menu_item) }
    let!(:menu_extra2) { create(:menu_extra, :random, menu_extra_section: menu_extra_section2, menu_item: menu_item) }
    let!(:archived_menu_extra) { create(:menu_extra, :random, menu_extra_section: archived_menu_extra_section, menu_item: menu_item, archived_at: Time.zone.now) }

    it 'clones any non-archived menu extra sections and its underlying menu extras' do
      item_cloner = MenuItems::Clone.new(menu_item: menu_item).call

      expect(item_cloner).to be_success
      cloned_item = item_cloner.cloned_item
      expect(cloned_item.id).to_not eq(menu_item.id) # a different menu item

      cloned_menu_extra_sections = cloned_item.menu_extra_sections
      expect(cloned_menu_extra_sections).to be_present

      expect(cloned_menu_extra_sections.map(&:id)).to_not include(menu_extra_section1.id, menu_extra_section2.id) # new menu extra sections
      expect(cloned_menu_extra_sections.map(&:weight)).to_not include(menu_extra_section1.weight, menu_extra_section2.weight) # new  menu extra section weights

      expect(cloned_menu_extra_sections.map(&:menu_item)).to include(cloned_item)
      expect(cloned_menu_extra_sections.map(&:name)).to include(menu_extra_section1.name, menu_extra_section2.name)
      expect(cloned_menu_extra_sections.map(&:min_limit)).to include(menu_extra_section1.min_limit, menu_extra_section2.min_limit)
      expect(cloned_menu_extra_sections.map(&:max_limit)).to include(menu_extra_section1.max_limit, menu_extra_section2.max_limit)
      expect(cloned_menu_extra_sections.map(&:archived_at)).to match_array([nil, nil])

      cloned_menu_extras = cloned_item.menu_extras
      expect(cloned_menu_extras).to be_present

      expect(cloned_menu_extras.map(&:id)).to_not include(menu_extra1.id, menu_extra2.id) # new menu extras
      expect(cloned_menu_extras.map(&:weight)).to_not include(menu_extra1.weight, menu_extra2.weight) # new menu extra weights

      expect(cloned_menu_extras.map(&:name)).to include(menu_extra1.name, menu_extra2.name)
      expect(cloned_menu_extras.map(&:price)).to include(menu_extra1.price, menu_extra2.price)
      expect(cloned_menu_extras.map(&:archived_at)).to match_array([nil, nil])
    end

    it 'does not clone archived menu extra sections and its underlying menu extras(even if not archived)' do
      archived_menu_extra.update_column(:archived_at, [Time.zone.now, nil].sample)

      item_cloner = MenuItems::Clone.new(menu_item: menu_item).call

      expect(item_cloner).to be_success
      cloned_item = item_cloner.cloned_item
      expect(cloned_item.id).to_not eq(menu_item.id) # a different menu item

      cloned_menu_extra_sections = cloned_item.menu_extra_sections
      expect(cloned_menu_extra_sections).to be_present # clones non-archived menu extra sections
      expect(cloned_menu_extra_sections.map(&:id)).to_not include(archived_menu_extra_section.id)
      expect(cloned_menu_extra_sections.map(&:weight)).to_not include(archived_menu_extra_section.weight)
      expect(cloned_menu_extra_sections.map(&:name)).to_not include(archived_menu_extra_section.name)
      expect(cloned_menu_extra_sections.map(&:min_limit)).to_not include(archived_menu_extra_section.min_limit)
      expect(cloned_menu_extra_sections.map(&:max_limit)).to_not include(archived_menu_extra_section.max_limit)

      cloned_menu_extras = cloned_item.menu_extras
      expect(cloned_menu_extras).to be_present # clones non-archived menu extras
      expect(cloned_menu_extras.map(&:id)).to_not include(archived_menu_extra.id)
      expect(cloned_menu_extras.map(&:weight)).to_not include(archived_menu_extra.weight)
      expect(cloned_menu_extras.map(&:name)).to_not include(archived_menu_extra.name)
      expect(cloned_menu_extras.map(&:price)).to_not include(archived_menu_extra.price)
    end
  end
end
