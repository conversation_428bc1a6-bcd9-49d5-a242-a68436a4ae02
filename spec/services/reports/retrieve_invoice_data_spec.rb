require 'rails_helper'

RSpec.describe Reports::RetrieveInvoiceData, type: :service, reports: true do

  let!(:customer) { create(:customer_profile, :random) }

  let!(:data_start) do
    start = Time.zone.parse('2022-03-01 00:00:00').to_date # different fequency test pass with start date of this month # Time.zone.now.beginning_of_month.to_date
    start += 1.day if start.wday == 0
    start
  end

  let!(:invoice1) { create(:invoice, :random, created_at: data_start + 1.day) }
  let!(:order11) { create(:order, :delivered, customer_profile: customer, invoice: invoice1, update_with_invoice: true) }

  let!(:invoice2) { create(:invoice, :random, created_at: data_start + 7.days) }
  let!(:order21) { create(:order, :delivered, customer_profile: customer, invoice: invoice2, update_with_invoice: true) }

  let!(:invoice3) { create(:invoice, :random, created_at: data_start + 8.days) }
  let!(:order31) { create(:order, :delivered, customer_profile: customer, invoice: invoice3, update_with_invoice: true) }

  let!(:invoice4) { create(:invoice, :random, created_at: data_start + 1.month + 1.day) }
  let!(:order41) { create(:order, :delivered, customer_profile: customer, invoice: invoice4, update_with_invoice: true) }

  let!(:invoice5) { create(:invoice, :random, created_at: data_start + 1.month + 7.days) }
  let!(:order51) { create(:order, :delivered, customer_profile: customer, invoice: invoice5, update_with_invoice: true) }

  context 'with defaults (report_type = monthly)' do
    it 'retrurns the filter options as passed in along with defaults' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        customer_id: customer.id,
      }

      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call

      report_options = report_retreiver.options
      expect(report_options[:start_date]).to eq(options[:start_date])
      expect(report_options[:end_date]).to eq(options[:end_date])
      expect(report_options[:report_type]).to eq('monthly')
      expect(report_options[:company_wide]).to eq(false)
    end

    it 'returns all the invoices grouped by creation date month' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        customer_id: customer.id,
      }

      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(2) # accross 2 months
      expect(report_data.map(&:report_data).flatten).to include(invoice1, invoice2, invoice3, invoice4, invoice5)
    end

    it 'does not return invoices not within the passed date range' do
      invoice3.update_column(:created_at, data_start + 3.months)
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(2) # accross 2 months
      expect(report_data.map(&:report_data).flatten).to_not include(invoice3)
      expect(report_data.map(&:report_data).flatten).to include(invoice1, invoice2, invoice4, invoice5)
    end
  end

  context 'with customer purchase order' do
    let!(:customer_purchase_order1) { create(:customer_purchase_order, :random, customer_profile: customer) }
    let!(:customer_purchase_order2) { create(:customer_purchase_order, :random, customer_profile: customer) }

    before do
      [order11, order21].each do |order|
        order.update_column(:cpo_id, customer_purchase_order1.id)
      end
      [order31, order41].each do |order|
        order.update_column(:cpo_id, customer_purchase_order2.id)
      end
    end

    it 'only returns data connected to the passed in purchase order id' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        customer_id: customer.id,
        purchase_order_id: customer_purchase_order2.id,
      }
      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call
      report_data = report_retreiver.data

      expect(report_data.size).to eq(2) # across 2 months
      expect(report_data.map(&:report_data).flatten).to include(invoice3, invoice4)
      expect(report_data.map(&:report_data).flatten).to_not include(invoice1, invoice2, invoice5)
    end

    it 'returns data connected to no purchase orders' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        customer_id: customer.id,
        purchase_order_id: 'no-po',
      }
      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call
      report_data = report_retreiver.data

      expect(report_data.size).to eq(1) # across 1 month
      expect(report_data.map(&:report_data).flatten).to include(invoice5)
      expect(report_data.map(&:report_data).flatten).to_not include(invoice1, invoice2, invoice3, invoice4)
    end
  end

  context 'monthly report' do
    let!(:report_type) { 'monthly' }
    it 'retrieves the invoices for the customer and groups them by month' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        report_type: report_type,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(2) # accross 2 months
      key_dates = report_data.map(&:key_date)
      expect(key_dates.map{|x| x[:label] }).to include(*[invoice1, invoice4].map{|invoice| invoice.created_at.strftime('%b %Y') })
    end
  end

  context 'weekly report' do
    let!(:report_type) { 'weekly' }

    it 'retrieves the invoices for the customer and groups them by week start' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        report_type: report_type,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(4) # accross 4 weeks
      key_dates = report_data.map(&:key_date)
      expect(key_dates.map{|x| x[:label] }).to include(*[invoice1, invoice2, invoice4, invoice5].map{|invoice| invoice.created_at.strftime('%Y-%m-%d') })
    end
  end

  context 'with multiple customers' do
    let!(:customer2) { create(:customer_profile, :random) }
    let!(:invoice21) { create(:invoice, :random, created_at: data_start + 1.day) }
    let!(:order211) { create(:order, :delivered, customer_profile: customer2, invoice: invoice21, update_with_invoice: true) }
    let!(:invoice22) { create(:invoice, :random, created_at: data_start + 7.days) }
    let!(:order221) { create(:order, :delivered, customer_profile: customer2, invoice: invoice22, update_with_invoice: true) }

    let!(:customer3) { create(:customer_profile, :random) }
    let!(:invoice31) { create(:invoice, :random, created_at: data_start + 1.day) }
    let!(:order311) { create(:order, :delivered, customer_profile: customer3, invoice: invoice31, update_with_invoice: true) }
    let!(:invoice32) { create(:invoice, :random, created_at: data_start + 7.days) }
    let!(:order321) { create(:order, :delivered, customer_profile: customer3, invoice: invoice32, update_with_invoice: true) }

    it 'doesn\'t return invoices from other customers' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call

      report_data = report_retreiver.data
      expect(report_data.map(&:report_data).flatten).to_not include(invoice21, invoice22, invoice31, invoice32)
    end

    it 'returns invoices from customers belonging to the passed in customer\'s company' do
      company = create(:company, :random)
      company.customer_profiles = [customer, customer2]
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        with_order_data: true,
        company_wide: true,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveInvoiceData.new(options: options).call
      report_data = report_retreiver.data

      expect(report_data.map(&:report_data).flatten).to include(invoice1, invoice2, invoice3, invoice4, invoice5) # customer 1 invoices
      expect(report_data.map(&:report_data).flatten).to include(invoice21, invoice22) # customer 2 invoices
      expect(report_data.map(&:report_data).flatten).to_not include(invoice31, invoice32)
    end
  end

end
