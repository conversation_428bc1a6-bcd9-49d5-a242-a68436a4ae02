require 'rails_helper'

RSpec.describe Reports::RetrieveData, type: :service, reports: true do

  let!(:customer) { create(:customer_profile, :random, :with_user) }

  let!(:data_start) do
    start = Time.zone.now.beginning_of_month.to_date
    start += 1.day if start.wday == 0
    start
  end

  let!(:report_source1) { create(:report_source, source: customer, key_date: data_start + 1.day) }
  let!(:order11) { create(:order, :delivered, customer_profile: customer, customer_total: 20.0) }
  let!(:report_data11) { create(:report_datum, report_source: report_source1, total_spend: 20.0, order_ids: [order11.id]) }

  let!(:order12) { create(:order, :delivered, customer_profile: customer, customer_total: 30.0) }
  let!(:report_data12) { create(:report_datum, report_source: report_source1, total_spend: 30.0, order_ids: [order12.id]) }

  let!(:report_source2) { create(:report_source, source: customer, key_date: data_start + 1.week) }

  let!(:order13) { create(:order, :delivered, customer_profile: customer, customer_total: 50.0) }
  let!(:report_data21) { create(:report_datum, report_source: report_source2, total_spend: 50.0, order_ids: [order13.id]) }

  let!(:order14) { create(:order, :delivered, customer_profile: customer, customer_total: 60.0) }
  let!(:report_data22) { create(:report_datum, report_source: report_source2, total_spend: 60.0, order_ids: [order14.id]) }

  let!(:report_source3) { create(:report_source, source: customer, key_date: data_start + 1.month) }

  let!(:order15) { create(:order, :delivered, customer_profile: customer, customer_total: 90.0) }
  let!(:report_data31) { create(:report_datum, report_source: report_source3, total_spend: 90.0, order_ids: [order15.id]) }

  let!(:order16) { create(:order, :delivered, customer_profile: customer, customer_total: 100.0) }
  let!(:report_data32) { create(:report_datum, report_source: report_source3, total_spend: 100.0, order_ids: [order16.id]) }

  context 'with defaults (report_type = monthly)' do
    it 'returns the filter options as passed in along with defaults' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        customer_id: customer.id,
      }

      report_retreiver = Reports::RetrieveData.new(options: options).call

      report_options = report_retreiver.options
      expect(report_options[:start_date]).to eq(options[:start_date])
      expect(report_options[:end_date]).to eq(options[:end_date])
      expect(report_options[:report_type]).to eq('monthly')
      expect(report_options[:purchase_order_id]).to eq(nil)
      expect(report_options[:category_group]).to eq(nil)
      expect(report_options[:company_wide]).to eq(false)
      expect(report_options[:with_order_data]).to eq(false)
    end

    it 'returns the total spend (cumulative) grouped by dates' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        with_order_data: true,
        customer_id: customer.id,
      }

      report_retreiver = Reports::RetrieveData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(2) # accross 2 months
      expect(report_data.map(&:total_spend)).to include([report_data11, report_data12, report_data21, report_data22].map(&:total_spend).sum, [report_data31, report_data32].map(&:total_spend).sum)
    end

    it 'returns order data if passed as option' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        with_order_data: true,
        customer_id: customer.id,
      }

      report_retreiver = Reports::RetrieveData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(2) # accross 2 months
      expect(report_data.map(&:report_sources)).to include([report_source1, report_source2], [report_source3])
      expect(report_data.map(&:report_data)).to include([report_data11, report_data12, report_data21, report_data22], [report_data31, report_data32])
      expect(report_data.map(&:order_ids)).to include([order11, order12, order13, order14].map(&:id), [order15, order16].map(&:id))
    end

    it 'does not return data not within the date range passed' do
      report_source3.update_column(:key_date, data_start + 3.months)
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        with_order_data: true,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(1)
      expect(report_data.map(&:report_sources)).to_not include([report_source3])
      expect(report_data.map(&:report_data)).to_not include([report_data31, report_data32])
      expect(report_data.map(&:order_ids)).to_not include([order15, order16].map(&:id))
      expect(report_data.map(&:total_spend)).to_not include([report_data31, report_data32].map(&:total_spend).sum)
    end

    context 'with customer purchase order' do
      let!(:customer_purchase_order) { create(:customer_purchase_order, :random, customer_profile: customer) }

      before do
        [report_data11, report_data31].each do |report_data|
          report_data.update_column(:customer_purchase_order_id, customer_purchase_order.id)
        end
      end

      it 'only returns data connected to the passed in purchase order id' do
        options = {
          start_date: data_start,
          end_date: data_start + 2.months,
          purchase_order_id: customer_purchase_order.id,
          with_order_data: true,
          customer_id: customer.id,
        }
        report_retreiver = Reports::RetrieveData.new(options: options).call
        report_data = report_retreiver.data

        expect(report_data.map(&:report_sources)).to include([report_source1, report_source2], [report_source3])
        expect(report_data.map(&:report_data)).to include([report_data11], [report_data31])
        expect(report_data.map(&:order_ids)).to include([order11].map(&:id), [order15].map(&:id))
      end

      it 'returns data connected no purchase orders' do
        options = {
          start_date: data_start,
          end_date: data_start + 2.months,
          purchase_order_id: 'no-po',
          with_order_data: true,
          customer_id: customer.id,
        }
        report_retreiver = Reports::RetrieveData.new(options: options).call
        report_data = report_retreiver.data

        expect(report_data.map(&:report_sources)).to include([report_source1, report_source2], [report_source3])
        expect(report_data.map(&:report_data)).to include([report_data12, report_data21, report_data22], [report_data32])
        expect(report_data.map(&:order_ids)).to include([order12, order13, order14].map(&:id), [order16].map(&:id))
      end
    end

    context 'with order category' do
      let!(:category_group) { 'catering-services' }

      before do
        [report_data12, report_data32].each do |report_data|
          report_data.update_column(:category, category_group)
        end
      end

      it 'only returns data with the passed in category' do
        options = {
          start_date: data_start,
          end_date: data_start + 2.months,
          category_group: category_group,
          with_order_data: true,
          customer_id: customer.id,
        }
        report_retreiver = Reports::RetrieveData.new(options: options).call
        report_data = report_retreiver.data

        expect(report_data.map(&:report_sources)).to include([report_source1, report_source2], [report_source3])
        expect(report_data.map(&:report_data)).to include([report_data12], [report_data32])
        expect(report_data.map(&:order_ids)).to include([order12].map(&:id), [order16].map(&:id))
      end
    end
  end

  context 'monthly report' do
    let!(:report_type) { 'monthly' }
    it 'retrieves the total spends for the customer reports groups them by month' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        report_type: report_type,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(2) # accross 2 months
      key_dates = report_data.map(&:key_date)
      expect(key_dates.map{|x| x[:label] }).to include(*[report_source1, report_source3].map.map{|rc| rc.key_date.strftime('%b %Y') }) # report_source2 data comes within the month of report_source1
    end
  end

  context 'weekly report' do
    let!(:report_type) { 'weekly' }

    it 'retrieves the total spends for the customer reports groups them by week start' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        report_type: report_type,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveData.new(options: options).call
      report_data = report_retreiver.data
      expect(report_data.size).to eq(3) # accross 3 weeks
      key_dates = report_data.map(&:key_date)
      expect(key_dates.map{|x| x[:label] }).to include(*[report_source1, report_source2, report_source3].map.map{|rc| rc.key_date.strftime('%Y-%m-%d') })
    end
  end

  context 'with multiple customers' do
    let!(:customer2) { create(:customer_profile, :random, :with_user) }
    let!(:report_source21) { create(:report_source, source: customer2, key_date: data_start + 1.day) }
    let!(:order211) { create(:order, :delivered, customer_profile: customer2, customer_total: 20.0) }
    let!(:report_data211) { create(:report_datum, report_source: report_source21, total_spend: 20.0, order_ids: [order211.id]) }
    let!(:order212) { create(:order, :delivered, customer_profile: customer2, customer_total: 30.0) }
    let!(:report_data212) { create(:report_datum, report_source: report_source21, total_spend: 30.0, order_ids: [order212.id]) }

    let!(:customer3) { create(:customer_profile, :random, :with_user) }
    let!(:report_source31) { create(:report_source, source: customer3, key_date: data_start + 1.day) }
    let!(:order311) { create(:order, :delivered, customer_profile: customer3, customer_total: 20.0) }
    let!(:report_data311) { create(:report_datum, report_source: report_source31, total_spend: 20.0, order_ids: [order311.id]) }
    let!(:order312) { create(:order, :delivered, customer_profile: customer3, customer_total: 30.0) }
    let!(:report_data312) { create(:report_datum, report_source: report_source31, total_spend: 30.0, order_ids: [order312.id]) }

    it 'doesn\'t return data from other customers' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        with_order_data: true,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveData.new(options: options).call
      report_data = report_retreiver.data

      expect(report_data.map(&:report_sources).flatten).to_not include(report_source21, report_source31)
      expect(report_data.map(&:report_data).flatten).to_not include(report_data211, report_data212, report_data311, report_data312)
      expect(report_data.map(&:order_ids).flatten).to_not include(*[order211, order212, order311, order312].map(&:id))
    end

    it 'returns data from customers belonging to the passed in customer\'s company' do
      company = create(:company, :random)
      company.customer_profiles = [customer, customer2]
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        with_order_data: true,
        company_wide: true,
        customer_id: customer.id,
      }
      report_retreiver = Reports::RetrieveData.new(options: options).call
      report_data = report_retreiver.data

      expect(report_data.map(&:report_sources).flatten).to include(report_source21)
      expect(report_data.map(&:report_sources).flatten).to_not include(report_source31)

      expect(report_data.map(&:report_data).flatten).to include(report_data211, report_data212)
      expect(report_data.map(&:report_data).flatten).to_not include(report_data311, report_data312)

      expect(report_data.map(&:order_ids).flatten).to include(*[order211, order212].map(&:id))
      expect(report_data.map(&:order_ids).flatten).to_not include(*[order311, order312].map(&:id))
    end

    context 'for an Admin Portal user (pantry manager, account manager)' do
      let!(:admin_customer) { create(:customer_profile, :random, :with_user, company_team_admin: true) }
      let!(:admin_report_source) { create(:report_source, source: admin_customer, key_date: data_start + 1.day) }
      let!(:admin_order) { create(:order, :delivered, customer_profile: admin_customer, customer_total: 20.0) }
      let!(:admin_report_data) { create(:report_datum, report_source: admin_report_source, total_spend: 20.0, order_ids: [admin_order.id]) }

      let!(:access_permission1) { create(:access_permission, :random, admin: admin_customer, customer_profile: customer) }
      let!(:access_permission2) { create(:access_permission, :random, admin: admin_customer, customer_profile: customer3) }

      it 'returns data only from customers the admin user has access to (including self)' do
        options = {
          start_date: data_start,
          end_date: data_start + 2.months,
          with_order_data: true,
          admin_user: admin_customer.reload.user,
          source_type: 'CustomerProfile',
        }
        report_retreiver = Reports::RetrieveData.new(options: options).call
        report_data = report_retreiver.data

        expect(report_data.map(&:report_sources).flatten).to include(report_source1, report_source2, report_source3, report_source31, admin_report_source)
        expect(report_data.map(&:report_sources).flatten).to_not include(report_source21)

        expect(report_data.map(&:report_data).flatten).to include(report_data11, report_data12, report_data21, report_data22, report_data31, report_data32, report_data311, report_data312, admin_report_data)
        expect(report_data.map(&:report_data).flatten).to_not include(report_data211, report_data212)

        expect(report_data.map(&:order_ids).flatten).to include(*[order11, order12, order13, order14, order15, order16, order311, order312, admin_order].map(&:id))
        expect(report_data.map(&:order_ids).flatten).to_not include(*[order211, order212].map(&:id))
      end
    end
  end

  context 'For Admin Portal Reports (as Yordar Admin)' do
    let!(:supplier) { create(:supplier_profile, :random) }

    let!(:supplier_report_source1) { create(:report_source, source: supplier, key_date: data_start + 1.day) }
    let!(:supplier_report_data11) { create(:report_datum, report_source: supplier_report_source1, total_spend: 15.0, order_ids: [order11.id]) }
    let!(:supplier_report_data12) { create(:report_datum, report_source: supplier_report_source1, total_spend: 25.0, order_ids: [order12.id]) }

    let!(:supplier_report_source2) { create(:report_source, source: supplier, key_date: data_start + 1.week) }
    let!(:supplier_report_data21) { create(:report_datum, report_source: supplier_report_source2, total_spend: 45.0, order_ids: [order13.id]) }
    let!(:supplier_report_data22) { create(:report_datum, report_source: supplier_report_source2, total_spend: 55.0, order_ids: [order14.id]) }

    let!(:supplier_report_source3) { create(:report_source, source: supplier, key_date: data_start + 1.month) }
    let!(:supplier_report_data31) { create(:report_datum, report_source: supplier_report_source3, total_spend: 85.0, order_ids: [order15.id]) }
    let!(:supplier_report_data32) { create(:report_datum, report_source: supplier_report_source3, total_spend: 95.0, order_ids: [order16.id]) }

    it 'returns the appropriate report source and data based on passed in source_type' do
      options = {
        start_date: data_start,
        end_date: data_start + 2.months,
        with_order_data: true,
        source_type: 'CustomerProfile',
      }
      report_retreiver = Reports::RetrieveData.new(options: options).call
      report_data = report_retreiver.data

      expect(report_data.map(&:report_sources).flatten).to include(report_source1, report_source2, report_source3)
      expect(report_data.map(&:report_sources).flatten).to_not include(supplier_report_source1, supplier_report_source2, supplier_report_source3)
      
      expect(report_data.map(&:report_data).flatten).to include(report_data11, report_data12, report_data21, report_data22, report_data31, report_data32)
      expect(report_data.map(&:report_data).flatten).to_not include(supplier_report_data11, supplier_report_data12, supplier_report_data21, supplier_report_data22, supplier_report_data31, supplier_report_data32)

      expect(report_data.map(&:order_ids).flatten).to include(*[order11, order12, order13, order14, order15, order16].map(&:id))

      expect(report_data.map(&:total_spend)).to include([report_data11, report_data12, report_data21, report_data22].map(&:total_spend).sum, [report_data31, report_data32].map(&:total_spend).sum)
      expect(report_data.map(&:total_spend)).to_not include([report_data11, supplier_report_data12, supplier_report_data21, supplier_report_data22].map(&:total_spend).sum, [supplier_report_data31, supplier_report_data32].map(&:total_spend).sum)
    end

    context 'with staffing data' do
      let!(:staffing_supplier) { create(:supplier_profile, :random) }

      let!(:staffing_supplier_report_source) { create(:report_source, source: staffing_supplier, key_date: data_start + 1.day) }
      let!(:staffing_supplier_report_data) { create(:report_datum, report_source: staffing_supplier_report_source, total_spend: 15.0, order_ids: [order11.id]) }

      before do
        # allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
        # allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :staffing_supplier_id).and_return(staffing_supplier.id)
      end

      it 'returns data for a staffing supplier by default' do
        options = {
          start_date: data_start,
          end_date: data_start + 2.months,
          with_order_data: true,
          source_types: ['SupplierProfile'],
        }
        report_retreiver = Reports::RetrieveData.new(options: options).call
        report_data = report_retreiver.data

        expect(report_data.map(&:report_sources).flatten).to include(staffing_supplier_report_source)
        expect(report_data.map(&:report_sources).flatten).to include(supplier_report_source1, supplier_report_source2, supplier_report_source3)
        expect(report_data.map(&:report_sources).flatten).to_not include(report_source1, report_source2, report_source3) # not requested source type

        expect(report_data.map(&:report_data).flatten).to include(staffing_supplier_report_data)
        expect(report_data.map(&:report_data).flatten).to include(supplier_report_data11, supplier_report_data12, supplier_report_data21, supplier_report_data22, supplier_report_data31, supplier_report_data32)
        expect(report_data.map(&:report_data).flatten).to_not include(report_data11, report_data12, report_data21, report_data22, report_data31, report_data32)
      end

      it 'returns data excluding staffing supplier data' do
        options = {
          start_date: data_start,
          end_date: data_start + 2.months,
          with_order_data: true,
          exclude_staffing: true,
          source_types: ['SupplierProfile'],
        }
        report_retreiver = Reports::RetrieveData.new(options: options).call
        report_data = report_retreiver.data

        expect(report_data.map(&:report_sources).flatten).to_not include(staffing_supplier_report_source) # staffing supplier
        expect(report_data.map(&:report_sources).flatten).to include(supplier_report_source1, supplier_report_source2, supplier_report_source3)
        expect(report_data.map(&:report_sources).flatten).to_not include(report_source1, report_source2, report_source3) # not requested source type

        expect(report_data.map(&:report_data).flatten).to_not include(staffing_supplier_report_data) # staffing supplier data
        expect(report_data.map(&:report_data).flatten).to include(supplier_report_data11, supplier_report_data12, supplier_report_data21, supplier_report_data22, supplier_report_data31, supplier_report_data32)
        expect(report_data.map(&:report_data).flatten).to_not include(report_data11, report_data12, report_data21, report_data22, report_data31, report_data32)
      end
    end

    # Used for Admin Supplier Reports
    context 'with source types' do
      it 'returns the report source and data based on passed in source_types' do
        options = {
          start_date: data_start,
          end_date: data_start + 2.months,
          with_order_data: true,
          source_types: %w[CustomerProfile SupplierProfile],
        }
        report_retreiver = Reports::RetrieveData.new(options: options).call
        report_data = report_retreiver.data

        expect(report_data.map(&:report_sources).flatten).to include(supplier_report_source1, supplier_report_source2, supplier_report_source3)
        expect(report_data.map(&:report_sources).flatten).to include(report_source1, report_source2, report_source3)      

        expect(report_data.map(&:report_data).flatten).to include(supplier_report_data11, supplier_report_data12, supplier_report_data21, supplier_report_data22, supplier_report_data31, supplier_report_data32)
        expect(report_data.map(&:report_data).flatten).to include(report_data11, report_data12, report_data21, report_data22, report_data31, report_data32)

        expect(report_data.map(&:order_ids).flatten).to include(*[order11, order12, order13, order14, order15, order16].map(&:id))
      end

      # Used for supplier specific Admin Supplier Reports
      it 'returns the customer report data along with supplier data even if supplier id is passed' do
        options = {
          start_date: data_start,
          end_date: data_start + 2.months,
          with_order_data: true,
          source_types: %w[CustomerProfile SupplierProfile],
          supplier_id: supplier.id,
        }
        report_retreiver = Reports::RetrieveData.new(options: options).call
        report_data = report_retreiver.data

        expect(report_data.map(&:report_sources).flatten).to include(supplier_report_source1, supplier_report_source2, supplier_report_source3)
        expect(report_data.map(&:report_sources).flatten).to include(report_source1, report_source2, report_source3)      

        expect(report_data.map(&:report_data).flatten).to include(supplier_report_data11, supplier_report_data12, supplier_report_data21, supplier_report_data22, supplier_report_data31, supplier_report_data32)
        expect(report_data.map(&:report_data).flatten).to include(report_data11, report_data12, report_data21, report_data22, report_data31, report_data32)

        expect(report_data.map(&:order_ids).flatten).to include(*[order11, order12, order13, order14, order15, order16].map(&:id))
      end
    end
  end

end
