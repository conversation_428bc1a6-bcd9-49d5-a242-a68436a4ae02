require 'rails_helper'

RSpec.describe Reports::CaptureSupplierRangedData, type: :service, reports: true do

  let!(:delivery_start) { (Time.zone.now - 2.weeks).beginning_of_week }

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags) }

  let!(:order1) { create(:order, :delivered, delivery_at: delivery_start + 1.day) }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
  let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }
  let!(:order_line13) { create(:order_line, :random, order: order1, supplier_profile: supplier1) }

  let!(:order2) { create(:order, :delivered, delivery_at: delivery_start + 2.day) }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }
  let!(:order_line23) { create(:order_line, :random, order: order2, supplier_profile: supplier2) }

  let!(:order3) { create(:order, :delivered, delivery_at: delivery_start + 3.days) }
  let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }
  let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: supplier2) }
  let!(:order_line33) { create(:order_line, :random, order: order3, supplier_profile: supplier1) }

  it 'creates a report source record for the passed in start date' do
    data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

    captured_report_suppliers = data_capturer.report_suppliers
    expect(captured_report_suppliers.map(&:source)).to include(supplier1, supplier2)
    expect(captured_report_suppliers.map(&:key_date).uniq).to eq([delivery_start.to_date])
  end

  it 'creates report data records per supplier containing the orders data for that date range' do
    # [order11, order12, order21, order22].each do |order|
    #   order.update_column(:customer_total, rand(1000..3000))
    # end
    data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

    captured_report_suppliers = data_capturer.report_suppliers

    supplier1_report = captured_report_suppliers.detect{|report| report.source == supplier1 }
    supplier1_report_data = supplier1_report.report_data
    expect(supplier1_report_data.size).to eq(1)
    # expect(supplier1_report_data.map(&:total_spend).uniq).to include(order11.customer_total + order12.customer_total)
    expect(supplier1_report_data.map(&:order_ids).uniq).to include([order1.id, order3.id])

    supplier2_report = captured_report_suppliers.detect{|report| report.source == supplier2 }
    supplier2_report_data = supplier2_report.report_data
    expect(supplier2_report_data.size).to eq(1)
    # expect(supplier2_report_data.map(&:total_spend).uniq).to include(order21.customer_total + order22.customer_total)
    expect(supplier2_report_data.map(&:order_ids).uniq).to include([order2.id, order3.id])
  end

  context 'time based order status check' do
    it 'does not collect data for orders not marked as delivered when collecting data in the past' do # capture date is today
      order1.update_column(:status, 'confirmed')
      data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

      supplier1_report = data_capturer.report_suppliers.detect{|report| report.source == supplier1 }
      supplier1_report_data = supplier1_report.report_data
      expect(supplier1_report_data.map(&:order_ids).uniq).to include([order3.id])
      expect(supplier1_report_data.map(&:order_ids).uniq).to_not include([order1.id, order3.id])
    end

    it 'includes non-delivered (yet active) orders if capture date overlaps caputring start and end dates' do
      order1.update_column(:status, 'confirmed')
      data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week, capture_date: (delivery_start + 1.day).to_date).call

      supplier1_report = data_capturer.report_suppliers.detect{|report| report.source == supplier1 }
      supplier1_report_data = supplier1_report.report_data
      expect(supplier1_report_data.map(&:order_ids).uniq).to include([order1.id, order3.id])
    end    

    it 'includes non-delivered (yet active) orders when capturing data for days ending a day before capturing date' do # as orders are still remaining to be confirmed
      order3.update_column(:status, 'confirmed')
      data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week, capture_date: (delivery_start.end_of_week + 1.day).to_date).call

      supplier1_report = data_capturer.report_suppliers.detect{|report| report.source == supplier1 }
      supplier1_report_data = supplier1_report.report_data
      expect(supplier1_report_data.map(&:order_ids).uniq).to include([order1.id, order3.id])
    end

    it 'does not collect data for orders not marked as delivered past yesterday' do
      order3.update_column(:status, 'confirmed')
      data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week, capture_date: (delivery_start.end_of_week + 2.days).to_date).call

      supplier1_report = data_capturer.report_suppliers.detect{|report| report.source == supplier1 }
      supplier1_report_data = supplier1_report.report_data
      expect(supplier1_report_data.map(&:order_ids).uniq).to include([order1.id])
      expect(supplier1_report_data.map(&:order_ids).uniq).to_not include([order1.id, order3.id])
    end
  end

  it 'does not collect data for orders not delivered within passed in range' do
    order3.update_column(:delivery_at, delivery_start + 1.month)
    data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

    supplier1_report = data_capturer.report_suppliers.detect{|report| report.source == supplier1 }
    supplier1_report_data = supplier1_report.report_data
    # expect(supplier1_report_data.map(&:total_spend).uniq).to include(order11.customer_total + order12.customer_total)
    expect(supplier1_report_data.map(&:order_ids).uniq).to include([order1.id])
    expect(supplier1_report_data.map(&:order_ids).uniq).to_not include([order1.id, order3.id])
  end

  context 'orders with categoriezed order lines' do
    let!(:category_catering) { create(:category, :random, group: 'catering-services') }
    let!(:generic_category_catering) { create(:category, :random, group: 'catering-services', is_generic: true) }
    let!(:kitchen_category) { create(:category, :random, group: 'kitchen-supplies') }
    let!(:generic_kitchen_category) { create(:category, :random, group: 'kitchen-supplies', is_generic: true) }

    before do
      [order_line11, order_line12, order_line21].each do |order_line|
        order_line.update_columns(category_id: category_catering.id, quantity: 10)
      end

      [order_line13, order_line22, order_line23].each do |order_line|
        order_line.update_columns(category_id: kitchen_category.id, quantity: 10)
      end
    end

    it 'splits the report data records by major category of the order (determined by category attached to the order_line)' do
      data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

      supplier1_report = data_capturer.report_suppliers.detect{|report| report.source == supplier1 }
      supplier1_report_data = supplier1_report.report_data
      expect(supplier1_report_data.map(&:category).uniq).to include('catering-services', nil)
      expect(supplier1_report_data.map(&:order_ids).uniq).to include([order1.id], [order3.id])

      supplier2_report = data_capturer.report_suppliers.detect{|report| report.source == supplier2 }
      supplier2_report_data = supplier2_report.report_data
      expect(supplier2_report_data.map(&:category).uniq).to include('kitchen-supplies', nil)
      expect(supplier2_report_data.map(&:order_ids).uniq).to include([order2.id], [order3.id])
    end

    it 'splits the report data records by major category of the order (determined by category attached order line => menu_item => menu_section)' do
      menu_section1 = create(:menu_section, :random, categories: [kitchen_category])
      menu_item1 = create(:menu_item, :random, menu_section: menu_section1)
      order_line31.update_column(:menu_item_id, menu_item1.id) # category attached to order line via menu item => menu section
      order_line33.update_column(:menu_item_id, menu_item1.id) # category attached to order line via menu item => menu section

      data_capturer = Reports::CaptureSupplierRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

      supplier1_report = data_capturer.report_suppliers.detect{|report| report.source == supplier1 }
      supplier1_report_data = supplier1_report.report_data
      expect(supplier1_report_data.map(&:category).uniq).to include('catering-services', 'kitchen-supplies')
      expect(supplier1_report_data.map(&:order_ids).uniq).to include([order1.id], [order3.id])
    end
  end

end
