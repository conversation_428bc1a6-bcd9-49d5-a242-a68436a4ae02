require 'rails_helper'

RSpec.describe Reports::CaptureData, type: :service, reports: true do

  before do
    # mock data capture
    ranged_data_capture = double(Reports::CaptureCustomerRangedData)
    allow(Reports::CaptureCustomerRangedData).to receive(:new).and_return(ranged_data_capture)
    data_capture_response = OpenStruct.new(report_data: [true])
    allow(ranged_data_capture).to receive(:call).and_return(data_capture_response)

    ranged_supplier_data_capture = double(Reports::CaptureSupplierRangedData)
    allow(Reports::CaptureSupplierRangedData).to receive(:new).and_return(ranged_supplier_data_capture)
    data_capture_response = OpenStruct.new(report_data: [true])
    allow(ranged_supplier_data_capture).to receive(:call).and_return(data_capture_response)
  end

  context 'for a given date range' do
    let!(:starts) { Time.zone.parse('2021-03-01') }
    let!(:ends) { Time.zone.parse('2021-03-28') }

    it 'capture data for each week within the date range' do
      week1_start = Time.zone.parse('2021-03-01') # - 2021-03-07
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week1_start.beginning_of_day, capture_end: week1_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week1_start.beginning_of_day, capture_end: week1_start.end_of_week, verbose: anything)

      week2_start = week1_start + 1.week # 2021-03-08 - 2021-03-14
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week2_start.beginning_of_day, capture_end: week2_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week2_start.beginning_of_day, capture_end: week2_start.end_of_week, verbose: anything)

      week3_start = week2_start + 1.week # 2021-03-15 - 2021-03-21
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week3_start.beginning_of_day, capture_end: week3_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week3_start.beginning_of_day, capture_end: week3_start.end_of_week, verbose: anything)

      week4_start = week3_start + 1.week # 2021-03-22 - 2021-03-28
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week4_start.beginning_of_day, capture_end: week4_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week4_start.beginning_of_day, capture_end: week4_start.end_of_week, verbose: anything)

      Reports::CaptureData.new(starts: starts, ends: ends).call
    end

    it 'splits the data capture for the week containing a month start/end' do
      ends = Time.zone.parse('2021-04-11')

      week1_start = Time.zone.parse('2021-03-01') # - 2021-03-07
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week1_start.beginning_of_day, capture_end: week1_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week1_start.beginning_of_day, capture_end: week1_start.end_of_week, verbose: anything)

      week2_start = week1_start + 1.week # 2021-03-08 - 2021-03-04
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week2_start.beginning_of_day, capture_end: week2_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week2_start.beginning_of_day, capture_end: week2_start.end_of_week, verbose: anything)

      week3_start = week2_start + 1.week # 2021-03-15 - 2021-03-21
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week3_start.beginning_of_day, capture_end: week3_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week3_start.beginning_of_day, capture_end: week3_start.end_of_week, verbose: anything)

      week4_start = week3_start + 1.week # 2021-03-22 - 2021-03-28
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week4_start.beginning_of_day, capture_end: week4_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week4_start.beginning_of_day, capture_end: week4_start.end_of_week, verbose: anything)

      # split weekly stats
      week5_start = week4_start + 1.week # 2021-03-29 - 2021-03-31
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week5_start.beginning_of_day, capture_end: week5_start.end_of_month, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week5_start.beginning_of_day, capture_end: week5_start.end_of_month, verbose: anything)

      start_of_next_month = (week5_start + 1.month).beginning_of_month # 2021-04-01 - 2021-04-04
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: start_of_next_month, capture_end: start_of_next_month.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: start_of_next_month, capture_end: start_of_next_month.end_of_week, verbose: anything)

      week6_start = week5_start + 1.week # 2021-04-05 - 2021-04-11
      expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week6_start.beginning_of_day, capture_end: week6_start.end_of_week, verbose: anything)
      expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week6_start.beginning_of_day, capture_end: week6_start.end_of_week, verbose: anything)

      Reports::CaptureData.new(starts: starts, ends: ends).call
    end

    context 'selective data capture' do
      it 'captures data only for customers if directed' do
        week1_start = Time.zone.parse('2021-03-01') # - 2021-03-07
        expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week1_start.beginning_of_day, capture_end: week1_start.end_of_week, verbose: anything)

        week2_start = week1_start + 1.week # 2021-03-08 - 2021-03-14
        expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week2_start.beginning_of_day, capture_end: week2_start.end_of_week, verbose: anything)

        week3_start = week2_start + 1.week # 2021-03-15 - 2021-03-21
        expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week3_start.beginning_of_day, capture_end: week3_start.end_of_week, verbose: anything)

        week4_start = week3_start + 1.week # 2021-03-22 - 2021-03-28
        expect(Reports::CaptureCustomerRangedData).to receive(:new).with(capture_start: week4_start.beginning_of_day, capture_end: week4_start.end_of_week, verbose: anything)

        expect(Reports::CaptureSupplierRangedData).to_not receive(:new)

        Reports::CaptureData.new(starts: starts, ends: ends, data_type: 'customer').call
      end

      it 'captures data only for suppliers if directed' do
        week1_start = Time.zone.parse('2021-03-01') # - 2021-03-07
        expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week1_start.beginning_of_day, capture_end: week1_start.end_of_week, verbose: anything)

        week2_start = week1_start + 1.week # 2021-03-08 - 2021-03-14
        expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week2_start.beginning_of_day, capture_end: week2_start.end_of_week, verbose: anything)

        week3_start = week2_start + 1.week # 2021-03-15 - 2021-03-21
        expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week3_start.beginning_of_day, capture_end: week3_start.end_of_week, verbose: anything)

        week4_start = week3_start + 1.week # 2021-03-22 - 2021-03-28
        expect(Reports::CaptureSupplierRangedData).to receive(:new).with(capture_start: week4_start.beginning_of_day, capture_end: week4_start.end_of_week, verbose: anything)

        expect(Reports::CaptureCustomerRangedData).to_not receive(:new)

        Reports::CaptureData.new(starts: starts, ends: ends, data_type: 'supplier').call
      end
    end
  end

end
