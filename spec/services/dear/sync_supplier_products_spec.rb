require 'rails_helper'

RSpec.describe Dear::SyncSupplierProducts, type: :service, dear: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier) }
  let!(:menu_section) { create(:menu_section, :random, supplier_profile: supplier) }
  let!(:menu_item) { create(:menu_item, :random, menu_section: menu_section, supplier_profile: supplier) }

  let!(:api_product) do # Snack Proud Product
    {
      ID: 'bfe60704-ff3f-4990-95f6-5ffd343c6843',
      SKU: 'ABB01',
      Name: 'Abbotts Farmhouse Whole Bread',
      Category: 'Breakfast & Pantry',
      Brand: 'Abbotts',
      Type: 'Stock',
      CostingMethod: 'FIFO',
      DropShipMode: 'Optional Drop Ship',
      DefaultLocation: 'Kingsgrove',
      PriceTier2: 6.5,
      # ...
      PriceTiers: {
        'Wholesale (PriceTier2)': 6.5,
        # ...
      },
      ShortDescription: 'This is the short description',
      Description: 'This is the long description',
      Status: 'Active',
      LastModifiedOn: '2022-09-06T14:04:39.38Z',
      # ...
    }
  end

  before do
    # mock supplier categories syncer
    supplier_categories_syncer = double(Dear::SyncSupplierCategories)
    allow(Dear::SyncSupplierCategories).to receive(:new).and_return(supplier_categories_syncer)
    allow(supplier_categories_syncer).to receive(:call).and_return(true)

    # mock supplier menu section syncer
    supplier_menu_archiver = double(Suppliers::ArchiveMenu)
    allow(Suppliers::ArchiveMenu).to receive(:new).and_return(supplier_menu_archiver)
    allow(supplier_menu_archiver).to receive(:call)

    # mock api product fetcher
    products_fetcher = double(Dear::API::FetchProducts)
    allow(Dear::API::FetchProducts).to receive(:new).and_return(products_fetcher)
    products_fetcher_response = OpenStruct.new(success?: true, products: [api_product])
    allow(products_fetcher).to receive(:call).and_return(products_fetcher_response)

    # mock menu section syncer
    menu_section_syncer = double(Dear::SyncMenuSection)
    allow(Dear::SyncMenuSection).to receive(:new).and_return(menu_section_syncer)
    section_syncer_response = OpenStruct.new(success?: true, menu_section: menu_section)
    allow(menu_section_syncer).to receive(:call).and_return(section_syncer_response)

    # mock product syncer
    menu_item_syncer = double(Dear::SyncProduct)
    allow(Dear::SyncProduct).to receive(:new).and_return(menu_item_syncer)
    item_syncer_response = OpenStruct.new(success?: true, menu_item: menu_item)
    allow(menu_item_syncer).to receive(:call).and_return(item_syncer_response)
  end

  context 'with dear supplier categories' do
    let!(:dear_category) { create(:dear_category, :random, dear_account: dear_account) }

    it 'requests the syncing of Dear Supplier Categories' do
      expect(Dear::SyncSupplierCategories).to receive(:new).with(supplier: supplier, verbose: anything)

      products_syncer = Dear::SyncSupplierProducts.new(supplier: supplier).call
      expect(products_syncer).to be_success
    end

    it 'does not requests the syncing of Dear Supplier Categories if dear supplier categories are blank' do
      dear_category.destroy

      expect(Dear::SyncSupplierCategories).to_not receive(:new)

      products_syncer = Dear::SyncSupplierProducts.new(supplier: supplier).call
      expect(products_syncer).to be_success
    end
  end

  it 'requests the fetching of the supplier products via Dear' do
    expect(Dear::API::FetchProducts).to receive(:new).with(supplier: supplier, verbose: anything)

    products_syncer = Dear::SyncSupplierProducts.new(supplier: supplier).call
    expect(products_syncer).to be_success
    expect(products_syncer.all_products).to include(api_product)
  end

  it 'requests the syncing of the menu section' do
    expect(Dear::SyncMenuSection).to receive(:new).with(category_name: api_product[:Category], supplier: supplier, verbose: anything)

    products_syncer = Dear::SyncSupplierProducts.new(supplier: supplier).call
    expect(products_syncer).to be_success
    expect(products_syncer.menu_sections).to include(menu_section)
  end

  it 'requests the syncing of the product (menu item)' do
    expect(Dear::SyncProduct).to receive(:new).with(api_product: api_product, menu_section: menu_section, supplier: supplier, verbose: anything)

    products_syncer = Dear::SyncSupplierProducts.new(supplier: supplier).call
    expect(products_syncer).to be_success
    expect(products_syncer.menu_items).to include(menu_item)
    expect(products_syncer.unsynced_products).to be_empty
  end

  context 'Fresh Start' do
    it 'requests the supplier\'s menu to be archived if it is a fresh start' do
      expect(Suppliers::ArchiveMenu).to receive(:new).with(supplier: supplier)

      products_syncer = Dear::SyncSupplierProducts.new(supplier: supplier, refresh: true).call
      expect(products_syncer).to be_success
    end

    it 'requests the supplier\'s menu to be archived if it is not a fresh start' do
      expect(Suppliers::ArchiveMenu).to_not receive(:new).with(supplier: supplier)

      products_syncer = Dear::SyncSupplierProducts.new(supplier: supplier, refresh: [nil, false].sample).call
      expect(products_syncer).to be_success
    end
  end # Fresh Start

end
