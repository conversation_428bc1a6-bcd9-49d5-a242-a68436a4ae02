require 'rails_helper'

RSpec.describe Dear::SyncMenuSection, type: :services, dear: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier) }

  let!(:category_name) { Faker::Name.name }

  it 'create a new menu section with the given category name' do
    section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

    expect(section_syncer).to be_success

    synced_menu_section = section_syncer.menu_section
    expect(synced_menu_section).to be_present
    expect(synced_menu_section).to be_persisted
    expect(synced_menu_section.name).to eq(category_name)
    expect(synced_menu_section.supplier_profile).to eq(supplier)
  end

  context 'with allowed categories within dear account' do
    let!(:category1) { create(:dear_category, :random, dear_account: dear_account, name: category_name) }
    let!(:category2) { create(:dear_category, :random, dear_account: dear_account) }

    it 'syncs the menu section if category name is included in allowed categories for the dear account' do
      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

      expect(section_syncer).to be_success

      synced_menu_section = section_syncer.menu_section
      expect(synced_menu_section.name).to eq(category_name)
    end

    it 'errors if category name is not included in allowed categories for the dear account' do
      category1.update_column(:name, 'another-category-name')
      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

      expect(section_syncer).to_not be_success
      expect(section_syncer.errors).to include("Not a valid/syncable category name - #{category_name}")
    end

    it 'sets the menu section name equal to override name if present' do
      category1.update_column(:override_name, 'Overridden Category Name')
      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

      expect(section_syncer).to be_success

      synced_menu_section = section_syncer.menu_section
      expect(synced_menu_section.name).to_not eq(category_name)
      expect(synced_menu_section.name).to eq('Overridden Category Name')
    end
  end

  context 'with an existing menu section (of the same name)' do
    let!(:menu_section) { create(:menu_section, :random, supplier_profile: supplier, name: category_name, group_name: nil) }

    it 'doesn\'t create a duplicate menu section' do
      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

      expect(section_syncer).to be_success
      synced_menu_section = section_syncer.menu_section
      expect(synced_menu_section.id).to eq(menu_section.id)
    end

    it 'creates a new menu section if existing has a group name' do
      menu_section.update_column(:group_name, Faker::Name.name)

      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

      expect(section_syncer).to be_success
      synced_menu_section = section_syncer.menu_section
      expect(synced_menu_section.id).to_not eq(menu_section.id)
      expect(synced_menu_section.name).to eq(category_name)
      expect(synced_menu_section.group_name).to be_blank
    end

    it 'un-archives an already archived menu section' do
      menu_section.update_column(:archived_at, Time.zone.now)

      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

      expect(section_syncer).to be_success
      synced_menu_section = section_syncer.menu_section
      expect(synced_menu_section.id).to eq(menu_section.id)
      expect(synced_menu_section.archived_at).to be_blank
    end

    it 'creates a new menu secion if the menu section of the same name exists within another supplier' do
      supplier2 = create(:supplier_profile, :random)
      menu_section.update_column(:supplier_profile_id, supplier2.id)

      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

      expect(section_syncer).to be_success
      creaed_menu_section = section_syncer.menu_section
      expect(creaed_menu_section.id).to_not eq(menu_section.id)
      expect(creaed_menu_section.name).to eq(category_name)
      expect(creaed_menu_section.supplier_profile).to eq(supplier)
    end
  end

  context 'errors' do
    it 'errors without a category name' do
      section_syncer = Dear::SyncMenuSection.new(category_name: nil, supplier: supplier).call

      expect(section_syncer).to_not be_success
      expect(section_syncer.errors).to include('Cannot sync a menu section without a name')
    end

    it 'errors without a supplier' do
      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: nil).call

      expect(section_syncer).to_not be_success
      expect(section_syncer.errors).to include('Cannot sync without a supplier')
    end

    it 'errors if the supplier does not have an active dear account' do
      if [true, false].sample
        supplier2 = create(:supplier_profile, :random)
        dear_account.update_column(:supplier_profile_id, supplier2.id)
      else
        dear_account.update_column(:active, false)
      end
      section_syncer = Dear::SyncMenuSection.new(category_name: category_name, supplier: supplier).call

      expect(section_syncer).to_not be_success
      expect(section_syncer.errors).to include('Cannot sync into non-dear supplier')
    end

    it 'errors if category name is `custom`' do
      section_syncer = Dear::SyncMenuSection.new(category_name: 'custom', supplier: supplier).call

      expect(section_syncer).to_not be_success
      expect(section_syncer.errors).to include('Cannot sync a custom menu section')
    end
  end

end
