require 'rails_helper'

RSpec.describe Dear::AttachSaleToOrder, type: :service, dear: true, orders: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order) { create(:order, :draft) }

  let!(:sale) do
    {
      ID: SecureRandom.uuid,
      Location: Faker::Name.name,
    }
  end

  it 'creates a new dear_sale and attaches to order' do
    expect(order.dear_sales).to be_blank # inital check

    sale_attacher = Dear::AttachSaleToOrder.new(sale: sale, order: order, supplier: supplier).call

    expect(sale_attacher).to be_success

    created_sale = sale_attacher.dear_sale
    expect(created_sale).to be_present
    expect(created_sale).to be_persisted
    expect(created_sale.order).to eq(order)
    expect(created_sale.supplier_profile).to eq(supplier)
  end

  it 'creates a new dear_sale with given details' do
    sale_attacher = Dear::AttachSaleToOrder.new(sale: sale, order: order, supplier: supplier).call

    expect(sale_attacher).to be_success

    created_sale = sale_attacher.dear_sale
    expect(created_sale.sale_id).to eq(sale[:ID])
    expect(created_sale.location).to eq(sale[:Location])
  end

  context 'with existing sale' do
    let!(:dear_sale) { create(:dear_sale, :random, order: order, supplier_profile: supplier) }

    it 'updates the existing dear sale record for the order/supplier combo' do
      sale_attacher = Dear::AttachSaleToOrder.new(sale: sale, order: order, supplier: supplier).call

      expect(sale_attacher).to be_success
      updated_sale = sale_attacher.dear_sale

      expect(updated_sale.id).to eq(dear_sale.id)
      expect(updated_sale.sale_id).to eq(sale[:ID])
      expect(updated_sale.location).to eq(sale[:Location])
    end

    it 'create a new dear sale record for a new order supplier' do
      supplier2 = create(:supplier_profile, :random)
      sale_attacher = Dear::AttachSaleToOrder.new(sale: sale, order: order, supplier: supplier2).call

      expect(sale_attacher).to be_success
      created_sale = sale_attacher.dear_sale

      expect(created_sale.id).to_not eq(dear_sale.id)

      expect(created_sale.order).to eq(order)
      expect(created_sale.supplier_profile).to eq(supplier2)
      expect(created_sale.sale_id).to eq(sale[:ID])
      expect(created_sale.location).to eq(sale[:Location])
    end
  end

  context 'errors' do
    it 'errors if sale is missing' do
      sale_attacher = Dear::AttachSaleToOrder.new(sale: nil, order: order, supplier: supplier).call

      expect(sale_attacher).to_not be_success
      expect(sale_attacher.errors).to include('Cannot attach to a missing sale')
    end

    it 'errors if order is missing' do
      sale_attacher = Dear::AttachSaleToOrder.new(sale: sale, order: nil, supplier: supplier).call

      expect(sale_attacher).to_not be_success
      expect(sale_attacher.errors).to include('Cannot attach to a missing order')
    end

    it 'errors if supplier is missing' do
      sale_attacher = Dear::AttachSaleToOrder.new(sale: sale, order: order, supplier: nil).call

      expect(sale_attacher).to_not be_success
      expect(sale_attacher.errors).to include('Cannot attach sale with a missing supplier')
    end

    it 'errors if dear sale is missing data' do
      sale_with_missing_data = sale.except(:ID)
      sale_attacher = Dear::AttachSaleToOrder.new(sale: sale_with_missing_data, order: order, supplier: supplier).call

      expect(sale_attacher).to_not be_success
      expect(sale_attacher.errors).to include('Sale can\'t be blank') # sale_id model validation (presence: true)
    end
  end

end
