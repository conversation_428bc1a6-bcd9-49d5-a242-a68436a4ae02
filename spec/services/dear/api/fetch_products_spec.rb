require 'rails_helper'

RSpec.describe Dear::API::FetchProducts, type: :servive, dear: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_dear_account) }

  let!(:api_response) do # modeled on Dear::API::Response
    OpenStruct.new(
      success?: true,
      status: 200,
      data: {
        Total: 150,
        Products: %w[product-1 product-2]
      }
    )
  end

  let!(:expected_params) do
    {
      limit: Dear::API::FetchProducts::PRODUCT_LIMIT,
      IncludeDeprecated: true,
      IncludeAttachments: true,
    }
  end

  before do
    # mock DEAR API requests
    allow_any_instance_of(Dear::API::FetchProducts).to receive(:dear_request).and_return(api_response)
  end

  it 'makes a GET request to get products from Dear API' do
    products_fetcher = Dear::API::FetchProducts.new(supplier: supplier)

    expect(products_fetcher).to receive(:dear_request).with(method: :get, url: Dear::API::PRODUCT_ENDPOINT, params: anything).and_return(api_response) # page 1
    expect(products_fetcher).to receive(:dear_request).with(method: :get, url: Dear::API::PRODUCT_ENDPOINT, params: anything).and_return(api_response) # page 2

    products_fetcher_response = products_fetcher.call
    expect(products_fetcher_response).to be_success
  end

  it 'makes a request to get multiple pages of products based on Total number of Products' do
    products_fetcher = Dear::API::FetchProducts.new(supplier: supplier)

    page_1_params = expected_params.merge({ page: 1 })
    expect(products_fetcher).to receive(:dear_request).with(method: anything, url: anything, params: page_1_params).and_return(api_response)

    page_2_params = expected_params.merge({ page: 2 })
    expect(products_fetcher).to receive(:dear_request).with(method: anything, url: anything, params: page_2_params).and_return(api_response)

    page_3_params = expected_params.merge({ page: 3 })
    expect(products_fetcher).to_not receive(:dear_request).with(method: anything, url: anything, params: page_3_params) # not page 3

    products_fetcher_response = products_fetcher.call
    expect(products_fetcher_response).to be_success
  end

  it 'returns the total number of products and products' do
    products_fetcher = Dear::API::FetchProducts.new(supplier: supplier).call

    expect(products_fetcher).to be_success
    expect(products_fetcher.total_products).to eq(api_response.data[:Total])
    expect(products_fetcher.products).to eq(api_response.data[:Products] + api_response.data[:Products]) # twice for 2 pages
  end

  context 'errors' do
    it 'errors if supplier is missing' do
      products_fetcher = Dear::API::FetchProducts.new(supplier: nil).call

      expect(products_fetcher).to_not be_success
      expect(products_fetcher.errors).to include('Supplier is missing')
    end

    it 'errors if supplier does not have an active dear account' do
      if [true, false].sample
        supplier.dear_account.update_column(:active, false)
      else
        supplier.dear_account.destroy
        supplier.reload
      end
      products_fetcher = Dear::API::FetchProducts.new(supplier: supplier).call

      expect(products_fetcher).to_not be_success
      expect(products_fetcher.errors).to include('Suppier does not have an active Dear Account')
    end

    context 'with an API errors' do
      let!(:invalid_api_response) do
        OpenStruct.new(
          success?: false,
          status: 400,
          errors: ['Dear API response error']
        )
      end

      before do
        allow_any_instance_of(Dear::API::FetchProducts).to receive(:dear_request).and_return(invalid_api_response)
      end

      it 'returns with an error' do
        products_fetcher = Dear::API::FetchProducts.new(supplier: supplier).call

        expect(products_fetcher).to_not be_success
        expect(products_fetcher.errors).to include('Dear API response error')
      end
    end # API errors
  end

end
