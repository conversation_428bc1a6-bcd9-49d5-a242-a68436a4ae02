require 'rails_helper'

RSpec.describe Dear::API::UpsertSaleOrder, type: :service, dear: true, api: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier) }

  let!(:order) { create(:order, :delivered) }
  let!(:dear_sale) { create(:dear_sale, :random, order: order, supplier_profile: supplier) }

  let!(:api_response) do # modeled on Dear::API::Response
    OpenStruct.new(
      success?: true,
      status: 200,
      data: {
        SaleID: SecureRandom.uuid,
        Order: {
          Lines: []
        }
      }
    )
  end

  let!(:expected_request_body) do
    {
      sale_id: dear_sale.sale_id,
      memo: '',
      status: 'DRAFT',
      lines: [],
      additional_charges: [],
    }
  end

  let!(:gst) { 0.1 } # 10%

  before do
    # mock DEAR API requests
    allow_any_instance_of(Dear::API::UpsertSaleOrder).to receive(:dear_request).and_return(api_response)

    # mock gst
    allow_any_instance_of(Dear::API::UpsertSaleOrder).to receive(:gst).and_return(gst)
  end

  it 'makes a request to upsert a Dear Sale Order' do
    sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier)

    expect(sale_order_upserter).to receive(:dear_request).with(method: :post, url: Dear::API::SALE_ORDER_ENDPOINT, body: anything).and_return(api_response)

    sale_order_upserter_response = sale_order_upserter.call
    expect(sale_order_upserter_response).to be_success
  end

  it 'makes a request with the relevant order information (including dear customer id)' do
    sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier)

    expect(sale_order_upserter).to receive(:dear_request).with(method: anything, url: anything, body: expected_request_body).and_return(api_response)

    sale_order_upserter_response = sale_order_upserter.call
    expect(sale_order_upserter_response).to be_success
  end

  it 'works with the passed in dear sale' do
    sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier, dear_sale: dear_sale).call

    expect(sale_order_upserter).to be_success
  end

  context 'with order_lines' do
    let!(:menu_item) { create(:menu_item, :random, sku: SecureRandom.hex(7), supplier_profile: supplier) }
    let!(:menu_item_order_line) { create(:order_line, :random, order: order, menu_item: menu_item, supplier_profile: supplier, is_gst_free: false) }

    let!(:menu_item_with_serving_size) { create(:menu_item, :random, price: nil, sku: nil, supplier_profile: supplier) }
    let!(:serving_size) { create(:serving_size, :random, sku: SecureRandom.hex(7), menu_item: menu_item_with_serving_size) }
    let!(:serving_size_order_line) { create(:order_line, :random, order: order, menu_item: serving_size.menu_item, serving_size: serving_size, supplier_profile: supplier, is_gst_free: true) }

    it 'adds lines to the request body' do
      lines_data = []
      item_line_total_cost = (menu_item_order_line.cost * menu_item_order_line.quantity)
      item_line_tax = (item_line_total_cost * gst).round(2)
      lines_data << {
        sku: menu_item.sku, # attached menu items SKU
        name: menu_item_order_line.name,
        quantity: menu_item_order_line.quantity,
        price: menu_item_order_line.cost,
        tax: item_line_tax,
        tax_rule: Dear::API::SALE_TAX_RULE,
        total: item_line_total_cost,
      }
      lines_data << {
        sku: serving_size.sku, # attached serving size's SKU
        name: serving_size_order_line.name,
        quantity: serving_size_order_line.quantity,
        price: serving_size_order_line.cost,
        tax: 0,
        tax_rule: Dear::API::SALE_TAX_RULE,
        total: (serving_size_order_line.cost * serving_size_order_line.quantity)
      }
      expected_request_body_with_lines = expected_request_body.merge({ lines: lines_data })

      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier)

      expect(sale_order_upserter).to receive(:dear_request).with(method: anything, url: anything, body: expected_request_body_with_lines).and_return(api_response)

      sale_order_upserter_response = sale_order_upserter.call
      expect(sale_order_upserter_response).to be_success
    end
  end # with order lines

  context 'with a supplier delivery fee' do
    let!(:delivery_fee) { rand(10..20) }
    let!(:delivery_calculator) { double(Orders::CalculateDelivery) }

    before do
      # mock delivery_fee_calculation
      allow(Orders::CalculateDelivery).to receive(:new).and_return(delivery_calculator)
      allow(delivery_calculator).to receive(:call).and_return(delivery_fee)
    end

    it 'requests the delivery fee to be calculated' do
      expect(Orders::CalculateDelivery).to receive(:new).with(order: order, profile: supplier, order_lines: anything)

      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier).call
      expect(sale_order_upserter).to be_success
    end

    it 'adds the delivery fee as additional charges' do
      delivery_data = {
        description: 'Delivery Fee',
        price: delivery_fee,
        quantity: 1,
        tax:  (delivery_fee * gst).round(2),
        tax_rule: Dear::API::SALE_TAX_RULE,
        total: delivery_fee,
      }

      expected_request_body_with_delivery = expected_request_body.merge({ additional_charges: [delivery_data] })

      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier)

      expect(sale_order_upserter).to receive(:dear_request).with(method: anything, url: anything, body: expected_request_body_with_delivery).and_return(api_response)

      sale_order_upserter_response = sale_order_upserter.call
      expect(sale_order_upserter_response).to be_success
    end

    it 'does not add the delivery fee as additional charges if it is 0.0' do
      allow(delivery_calculator).to receive(:call).and_return(0.0)

      expected_request_body_with_delivery = expected_request_body.merge({ additional_charges: [] })

      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier)

      expect(sale_order_upserter).to receive(:dear_request).with(method: anything, url: anything, body: expected_request_body_with_delivery).and_return(api_response)

      sale_order_upserter_response = sale_order_upserter.call
      expect(sale_order_upserter_response).to be_success
    end

    context 'with an order supplier record' do
      let!(:order_supplier) { create(:order_supplier, :random, order: order, supplier_profile: supplier, delivery: rand(10..20)) }

      before do
        supplier.reload
      end

      it 'does not requests the delivery fee to be calculated' do
        expect(Orders::CalculateDelivery).to_not receive(:new)

        sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier).call
        expect(sale_order_upserter).to be_success
      end

      it 'adds the saved delivery fee as additional charges' do
        saved_delivery_fee = order_supplier.delivery
        delivery_data = {
          description: 'Delivery Fee',
          price: saved_delivery_fee,
          quantity: 1,
          tax:  (saved_delivery_fee * gst).round(2),
          tax_rule: Dear::API::SALE_TAX_RULE,
          total: saved_delivery_fee,
        }

        expected_request_body_with_delivery = expected_request_body.merge({ additional_charges: [delivery_data] })

        sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier)

        expect(sale_order_upserter).to receive(:dear_request).with(method: anything, url: anything, body: expected_request_body_with_delivery).and_return(api_response)

        sale_order_upserter_response = sale_order_upserter.call
        expect(sale_order_upserter_response).to be_success
      end

      it 'does not add the saved delivery fee as additional charges if it is 0.0' do
        order_supplier.update_column(:delivery, 0.0)
        expected_request_body_with_delivery = expected_request_body.merge({ additional_charges: [] })

        sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier)

        expect(sale_order_upserter).to receive(:dear_request).with(method: anything, url: anything, body: expected_request_body_with_delivery).and_return(api_response)

        sale_order_upserter_response = sale_order_upserter.call
        expect(sale_order_upserter_response).to be_success
      end

      it 'requests the delivery fee to be calculated if saved delivery fee is blank' do
        order_supplier.update_column(:delivery, nil)
        expect(Orders::CalculateDelivery).to receive(:new).with(order: order, profile: supplier, order_lines: anything)

        sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier).call
        expect(sale_order_upserter).to be_success
      end
    end # with saved order_supplier record
  end

  context 'with an API errors' do
    let!(:invalid_api_response) do
      OpenStruct.new(
        success?: false,
        status: 400,
        errors: ['Dear API response error']
      )
    end

    before do
      # mock sending back invalid api response
      allow_any_instance_of(Dear::API::UpsertSaleOrder).to receive(:dear_request).and_return(invalid_api_response)
    end

    it 'returns with an error' do
      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier).call

      expect(sale_order_upserter).to_not be_success
      expect(sale_order_upserter.errors).to include('Dear API response error')
    end
  end # API errors

  context 'errors' do
    it 'errors with a missing supplier' do
      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: nil).call

      expect(sale_order_upserter).to_not be_success
      expect(sale_order_upserter.errors).to include('Cannot upsert without a supplier')
    end

    it 'errors with a supplier with in-active Dear account' do
      if [true, false].sample
        dear_account.update_column(:active, false)
      else
        dear_account.destroy
      end
      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier).call

      expect(sale_order_upserter).to_not be_success
      expect(sale_order_upserter.errors).to include('Suppier does not have an active Dear Account')
    end

    it 'errors with a missing supplier' do
      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: nil, supplier: supplier).call

      expect(sale_order_upserter).to_not be_success
      expect(sale_order_upserter.errors).to include('Cannot upsert without an order')
    end

    it 'errors if the order is missing a dear sale' do
      dear_sale.destroy
      sale_order_upserter = Dear::API::UpsertSaleOrder.new(order: order, supplier: supplier).call

      expect(sale_order_upserter).to_not be_success
      expect(sale_order_upserter.errors).to include('Need a Dear Sale before upserting Sale Order')
    end
  end

end
