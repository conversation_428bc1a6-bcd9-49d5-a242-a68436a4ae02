require 'rails_helper'

RSpec.describe Dear::API::UpsertSale, type: :service, dear: true, api: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier) }
  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:order) { create(:order, :draft, customer_profile: customer) }

  let!(:api_sale) do
    {
      ID: SecureRandom.uuid,
      Location: Faker::Address.city,
    }
  end

  let!(:api_response) do # modeled on Dear::API::Response
    OpenStruct.new(
      success?: true,
      status: 200,
      data: api_sale
    )
  end

  let!(:expected_request_body) do
    {
      customer_id: dear_account.customer_id,
      external_ID: order.id,
      customer_reference: "#{order.name} | Order #{order.id}",
      contact: order.contact_name,
      email: order.contact_email,
      phone: order.phone,
      ship_by: order.delivery_at.strftime('%FT%T%:z'),
      shipping_address: {
        line1: order.delivery_address,
        line2: '',
        city: order.delivery_suburb.name,
        state: order.delivery_suburb.state,
        postcode: order.delivery_suburb.postcode,
        country: 'AUS',
        company: customer.customer_or_company_name,
        contact: customer.email_salutation,
        ship_to_other: false
      },
      shipping_notes: order.delivery_instruction,
      tax_rule: Dear::API::SALE_TAX_RULE,
      skip_quote: true,
    }
  end

  before do
    # mock DEAR API requests
    allow_any_instance_of(Dear::API::UpsertSale).to receive(:dear_request).and_return(api_response)
  end

  context 'Order with no Dear Sales' do
    it 'makes a request to create the new Dear Sale' do
      sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier)

      expect(sale_upserter).to receive(:dear_request).with(method: :post, url: Dear::API::SALE_ENDPOINT, body: anything).and_return(api_response)

      sale_upserter_response = sale_upserter.call
      expect(sale_upserter_response).to be_success
    end

    it 'makes a request with the relevant order information (including dear customer id)' do
      sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier)

      expect(sale_upserter).to receive(:dear_request).with(method: anything, url: anything, body: expected_request_body).and_return(api_response)

      sale_upserter_response = sale_upserter.call
      expect(sale_upserter_response).to be_success
    end

    context 'with a order delivery address level' do
      before do
        order.update_column(:delivery_address_level, "Level #{rand(1..10)}")
      end

      it 'makes a request with updated shipping_address' do
        sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier)

        updated_shipping_address = {
          line1: order.formatted_delivery_address_level,
          line2: order.delivery_address,
          city: order.delivery_suburb.name,
          state: order.delivery_suburb.state,
          postcode: order.delivery_suburb.postcode,
          country: 'AUS',
          company: customer.customer_or_company_name,
          contact: customer.email_salutation,
          ship_to_other: false
        }
        updated_expected_request_body = expected_request_body.merge({ shipping_address: updated_shipping_address })
        expect(sale_upserter).to receive(:dear_request).with(method: anything, url: anything, body: updated_expected_request_body).and_return(api_response)

        sale_upserter_response = sale_upserter.call
        expect(sale_upserter_response).to be_success
      end
    end # with delivery address level

    it 'create and attaches new sale to the order' do
      sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier).call

      expect(sale_upserter).to be_success
      created_dear_sale = sale_upserter.dear_sale

      expect(created_dear_sale).to be_present
      expect(created_dear_sale).to be_persisted

      expect(created_dear_sale.order).to eq(order)
      expect(created_dear_sale.supplier_profile).to eq(supplier)
      expect(created_dear_sale.sale_id).to eq(api_sale[:ID])
      expect(created_dear_sale.location).to eq(api_sale[:Location])
    end
  end # new sale

  context 'with an existing dear sale' do
    let!(:dear_sale) { create(:dear_sale, :random, order: order, supplier_profile: supplier) }

    it 'makes a request to update the new Dear Sale' do
      sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier)

      expect(sale_upserter).to receive(:dear_request).with(method: :put, url: Dear::API::SALE_ENDPOINT, body: anything).and_return(api_response)

      sale_upserter_response = sale_upserter.call
      expect(sale_upserter_response).to be_success
    end

    it 'makes a request with order details and Dear Sale Id and Location' do
      sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier)

      expected_request_body_with_sale_data = expected_request_body.merge({ id: dear_sale.sale_id, location: dear_sale.location })
      expect(sale_upserter).to receive(:dear_request).with(method: anything, url: anything, body: expected_request_body_with_sale_data).and_return(api_response)

      sale_upserter_response = sale_upserter.call
      expect(sale_upserter_response).to be_success
    end

    it 'updates the already attached Dear sale' do
      sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier).call

      expect(sale_upserter).to be_success
      updated_dear_sale = sale_upserter.dear_sale

      expect(updated_dear_sale.id).to eq(dear_sale.id)
      expect(updated_dear_sale.sale_id).to eq(api_sale[:ID])
      expect(updated_dear_sale.location).to eq(api_sale[:Location])
    end
  end # with existing dear sale

  context 'errors' do
    it 'errors with a missing supplier' do
      sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: nil).call

      expect(sale_upserter).to_not be_success
      expect(sale_upserter.errors).to include('Cannot upsert without a supplier')
    end

    it 'errors with a supplier with in-active Dear account' do
      if [true, false].sample
        dear_account.update_column(:active, false)
      else
        dear_account.destroy
      end
      sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier).call

      expect(sale_upserter).to_not be_success
      expect(sale_upserter.errors).to include('Suppier does not have an active Dear Account')
    end

    it 'errors with a missing supplier' do
      sale_upserter = Dear::API::UpsertSale.new(order: nil, supplier: supplier).call

      expect(sale_upserter).to_not be_success
      expect(sale_upserter.errors).to include('Cannot upsert without an order')
    end

    context 'with an API errors' do
      let!(:invalid_api_response) do
        OpenStruct.new(
          success?: false,
          status: 400,
          errors: ['Dear API response error']
        )
      end

      before do
        allow_any_instance_of(Dear::API::UpsertSale).to receive(:dear_request).and_return(invalid_api_response)
      end

      it 'returns with an error' do
        sale_upserter = Dear::API::UpsertSale.new(order: order, supplier: supplier).call

        expect(sale_upserter).to_not be_success
        expect(sale_upserter.errors).to include('Dear API response error')
      end
    end # API errors
  end

end
