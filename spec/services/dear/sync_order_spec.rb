require 'rails_helper'

RSpec.describe Dear::SyncOrder, type: :service, dear: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier) }

  let!(:order) { create(:order, :draft) }
  let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier) }

  let(:dear_sale) { double(Dear::Sale) }

  before do
    # mock sale upserter
    sale_upserter = double(Dear::API::UpsertSale)
    allow(Dear::API::UpsertSale).to receive(:new).and_return(sale_upserter)
    sale_upserter_response = OpenStruct.new(success?: true, dear_sale: dear_sale)
    allow(sale_upserter).to receive(:call).and_return(sale_upserter_response)

    # mock sale order upserter
    sale_order_upserter = double(Dear::API::UpsertSaleOrder)
    allow(Dear::API::UpsertSaleOrder).to receive(:new).and_return(sale_order_upserter)
    sale_order_upserter_response = OpenStruct.new(success?: true, sale_order_synced: true)
    allow(sale_order_upserter).to receive(:call).and_return(sale_order_upserter_response)
  end

  it 'makes a request to upsert a Dear Sale' do
    expect(Dear::API::UpsertSale).to receive(:new).with(order: order, supplier: supplier)

    order_syncer = Dear::SyncOrder.new(supplier: supplier, order: order).call

    expect(order_syncer).to be_success
    expect(order_syncer.dear_sale).to eq(dear_sale)
  end

  it 'makes a request to upsert a Dear Sale Order' do
    expect(Dear::API::UpsertSaleOrder).to receive(:new).with(order: order, supplier: supplier, dear_sale: dear_sale)

    order_syncer = Dear::SyncOrder.new(supplier: supplier, order: order).call
    expect(order_syncer).to be_success
  end

  context 'errors' do
    it 'errors if the supplier is missing' do
      order_syncer = Dear::SyncOrder.new(supplier: nil, order: order).call

      expect(order_syncer).to_not be_success
      expect(order_syncer.errors).to include('Cannot sync order for a missing supplier')
    end

    it 'errors if the supplier does not have an active Dear Account' do
      if [true, false].sample
        dear_account.update_column(:active, false)
      else
        dear_account.destroy
      end
      order_syncer = Dear::SyncOrder.new(supplier: supplier, order: order).call

      expect(order_syncer).to_not be_success
      expect(order_syncer.errors).to include('Supplier does not have an active Dear Account')
    end

    it 'errors if the order is missing' do
      order_syncer = Dear::SyncOrder.new(supplier: supplier, order: nil).call

      expect(order_syncer).to_not be_success
      expect(order_syncer.errors).to include('Cannot sync a missing order')
    end

    it 'errors if the supplier does not have items within the order' do
      order_line.destroy
      order_syncer = Dear::SyncOrder.new(supplier: supplier, order: order).call

      expect(order_syncer).to_not be_success
      expect(order_syncer.errors).to include('Order does not contain items from supplier')
    end
  end

end
