require 'rails_helper'

RSpec.describe Dear::SyncSupplierOrders, type: :service, orders: true, dear: true do

  let!(:time) { Time.zone.now.beginning_of_day + 10.hours}
  let!(:dear_supplier) { create(:supplier_profile, :random) }
  let!(:dear_account) { create(:dear_account, :random, supplier_profile: dear_supplier) }

  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:order1) { create(:order, :confirmed, delivery_at: time + 1.day) }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: dear_supplier) }

  let!(:order2) { create(:order, :confirmed, delivery_at: time + 2.days) }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: dear_supplier) }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier) }

  before do
    # mock order syncer
    dear_order_syncer = double(Dear::SyncOrder)
    allow(Dear::SyncOrder).to receive(:new).and_return(dear_order_syncer)
    dear_order_syncer_response = OpenStruct.new(success?: true, dear_sale: 'dear-sale')
    allow(dear_order_syncer).to receive(:call).and_return(dear_order_syncer_response)
  end

  it 'syncs all future orders for the passed in supplier based on order lines' do
    expect(Dear::SyncOrder).to receive(:new).with(supplier: dear_supplier, order: order1)
    expect(Dear::SyncOrder).to receive(:new).with(supplier: dear_supplier, order: order2)

    orders_syncer = Dear::SyncSupplierOrders.new(supplier: dear_supplier, time: time).call

    expect(orders_syncer).to be_success
    expect(orders_syncer.synced_orders).to include(order1, order2)
  end

  it 'does not sync an order which is delivered before passed in time' do
    order1.update_column(:delivery_at, time - 1.day)
    expect(Dear::SyncOrder).to_not receive(:new).with(supplier: dear_supplier, order: order1)

    orders_syncer = Dear::SyncSupplierOrders.new(supplier: dear_supplier, time: time).call

    expect(orders_syncer).to be_success
    expect(orders_syncer.synced_orders).to_not include(order1)
  end

  it 'does not sync an order marked as delivered' do
    order2.update_column(:status, 'delivered')
    expect(Dear::SyncOrder).to_not receive(:new).with(supplier: dear_supplier, order: order2)

    orders_syncer = Dear::SyncSupplierOrders.new(supplier: dear_supplier, time: time).call

    expect(orders_syncer).to be_success
    expect(orders_syncer.synced_orders).to_not include(order2)
  end

  context 'with an attached dear sale' do
    let!(:dear_sale) { create(:dear_sale, :random, order: order2, supplier_profile: dear_supplier) }

    it 'only syncs an order with order lines and dear sale once' do
      expect(Dear::SyncOrder).to receive(:new).with(supplier: dear_supplier, order: order1)
      expect(Dear::SyncOrder).to receive(:new).with(supplier: dear_supplier, order: order2)
      expect(Dear::SyncOrder).to_not receive(:new).with(supplier: dear_supplier, order: order2) # not the second time

      orders_syncer = Dear::SyncSupplierOrders.new(supplier: dear_supplier, time: time).call

      expect(orders_syncer).to be_success
      expect(orders_syncer.synced_orders).to include(order2)
    end

    it 'syncs an order even if it does not have any supplier order_lines' do
      order_line21.destroy
      expect(Dear::SyncOrder).to receive(:new).with(supplier: dear_supplier, order: order2)

      orders_syncer = Dear::SyncSupplierOrders.new(supplier: dear_supplier, time: time).call

      expect(orders_syncer).to be_success
      expect(orders_syncer.synced_orders).to include(order2)
    end
  end

  context 'errors' do
    it 'errors with a missing supplier' do
      orders_syncer = Dear::SyncSupplierOrders.new(supplier: nil, time: time).call

      expect(orders_syncer).to_not be_success
      expect(orders_syncer.errors).to include('Cannot sync without a supplier')
    end

    it 'errors if supplier does not have an active Dear Account' do
      if [true, false].sample
        dear_account.update_column(:active, false)
      else
        dear_account.destroy
      end
      orders_syncer = Dear::SyncSupplierOrders.new(supplier: [dear_supplier, supplier].sample, time: time).call

      expect(orders_syncer).to_not be_success
      expect(orders_syncer.errors).to include('Supplier does not have an active Dear Account')
    end
  end

end
