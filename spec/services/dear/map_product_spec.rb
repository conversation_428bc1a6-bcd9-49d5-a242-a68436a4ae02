require 'rails_helper'

RSpec.describe Dear::MapProduct, type: :service, dear: true do

  let!(:api_product) do # Snack Proud Product
    {
      ID: '0852cdb5-b3f1-4787-8f10-dd2a847e0545',
      SKU: 'ABB01',
      Name: 'Abbotts Farmhouse Whole Bread',
      Category: 'Bulk Foods and Pantry',
      Brand: 'Abbotts',
      Type: 'Stock',
      CostingMethod: 'FIFO',
      DropShipMode: 'Optional Drop Ship',
      DefaultLocation: 'Kingsgrove',
      Length: 0.0000,
      Width: 0.0000,
      Height: 0.0000,
      Weight: 0.0000,
      UOM: 'Bottle',
      WeightUnits: nil,
      DimensionsUnits: nil,
      Barcode: nil,
      MinimumBeforeReorder: 0.0000,
      ReorderQuantity: 0.0000,
      PriceTier1: 6.5000,
      PriceTier2: 10.5000,
      PriceTier3: 10.0000,
      PriceTier4: 20.0000,
      PriceTier5: 30.0000,
      PriceTier6: 40.0000,
      PriceTier7: 50.0000,
      PriceTier8: 60.0000,
      PriceTier9: 70.0000,
      PriceTier10: 80.0000,
      PriceTiers: {
          'Corp Volume (Tier 1)': 6.5000,
          'Wholesale (PriceTier2)': 10.5000,
          'Retail + 30% - 32% (PriceTier 3)': 10.0000,
          'WooCo  (PriceTier 4)': 20.0000,
          'Trade Price List': 30.0000,
          'Tier 6': 40.0000,
          'Tier 7': 50.0000,
          'Tier 8': 60.0000,
          'Tier 9': 70.0000,
          'Tier 10': 80.0000
      },
      AverageCost: 4.9500,
      ShortDescription: '',
      InternalNote: '',
      Description: '',
      AdditionalAttribute1: nil,
      AdditionalAttribute2: nil,
      AdditionalAttribute3: nil,
      AdditionalAttribute4: nil,
      AdditionalAttribute5: nil,
      AdditionalAttribute6: 'GF, V, DF, Keto',
      AdditionalAttribute7: nil,
      AdditionalAttribute8: nil,
      AdditionalAttribute9: nil,
      AdditionalAttribute10: nil,
      AttributeSet: 'Additional_Properties',
      DiscountRule: nil,
      Tags: nil,
      Status: 'Active',
      StockLocator: nil,
      COGSAccount: '310',
      RevenueAccount: '200',
      ExpenseAccount: nil,
      InventoryAccount: '631',
      PurchaseTaxRule: nil,
      SaleTaxRule: nil,
      LastModifiedOn: '2022-09-15T03:32:54.33Z',
      Sellable: true,
      PickZones: nil,
      BillOfMaterial: false,
      AutoAssembly: false,
      AutoDisassembly: false,
      QuantityToProduce: 1.**********,
      AlwaysShowQuantity: 0.0000,
      AssemblyInstructionURL: '',
      AssemblyCostEstimationMethod: 'Average Cost',
      Suppliers: [],
      ReorderLevels: [],
      BillOfMaterialsProducts: [],
      BillOfMaterialsServices: [],
      Movements: [],
      Attachments: [
        {
          ID: '3d1a2f87-c819-4f46-9bc9-eb883114e6ba',
          ContentType: 'image/png',
          FileName: 'Abbotts+-+Farmhouse+Wholemeal.png',
          IsDefault: false,
          DownloadUrl: 'https://inventory.dearsystems.com/Attachment/Download?ID=3d1a2f87-c819-4f46-9bc9-eb883114e6ba&ContentType=image/png&FileName=Abbotts+-+Farmhouse+Wholemeal.png&isPublic=True'
        }
      ],
      BOMType: 'None',
      WarrantyName: nil,
      CustomPrices: [],
      CartonHeight: 0.0000,
      CartonWidth: 0.0000,
      CartonLength: 0.0000,
      CartonQuantity: 0.0000,
      CartonInnerQuantity: 0.0000
  }
  end

  it 'maps the SKU' do
    mapped_product = Dear::MapProduct.new(api_product: api_product).call
    expect(mapped_product.sku).to eq(api_product[:SKU])
  end

  it 'maps the Name' do
    mapped_product = Dear::MapProduct.new(api_product: api_product).call
    expect(mapped_product.name).to eq(api_product[:Name])
  end

  context 'description' do
    it 'maps the (long) Description if present' do
      mapped_product = Dear::MapProduct.new(api_product: api_product).call
      expect(mapped_product.description).to eq(api_product[:Description])
    end

    it 'maps the Short Description if present and only if long description is absent' do
      mapped_product = Dear::MapProduct.new(api_product: api_product.except(:Description)).call
      expect(mapped_product.description).to eq(api_product[:ShortDescription])
    end
  end

  context 'price map' do
    let!(:price_tier) { "PriceTier#{rand(1..10)}" }
    let!(:supplier) { create(:supplier_profile, :random) }
    let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier, price_tier: price_tier) }

    it 'maps the pricing to the price tier saved against the Dear Account of the passed in supplier' do
      mapped_product = Dear::MapProduct.new(api_product: api_product, supplier: supplier).call
      expect(mapped_product.price).to eq(api_product[price_tier.to_sym])
    end

    it 'maps the pricing as Tier 1 by default' do
      dear_account.update_column(:price_tier, nil)
      mapped_product = Dear::MapProduct.new(api_product: api_product, supplier: [supplier, nil].sample).call
      expect(mapped_product.price).to eq(api_product[:PriceTier1])
    end
  end

  context 'with dietary attribute' do
    let!(:dietary_attribute) { 'AdditionalAttribute6' }
    let!(:supplier) { create(:supplier_profile, :random) }
    let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier, dietary_attribute: dietary_attribute) }

    it 'maps the dietary fields for the product' do
      mapped_product = Dear::MapProduct.new(api_product: api_product, supplier: supplier).call

      expect(mapped_product.is_vegan).to be_truthy
      expect(mapped_product.is_vegetarian).to be_falsey
      expect(mapped_product.is_gluten_free).to be_truthy
      expect(mapped_product.is_dairy_free).to be_truthy
    end

    it 'returns dietary fields as false if dear account does not contain dietary_attribute' do
      dear_account.update_column(:dietary_attribute, nil)
      mapped_product = Dear::MapProduct.new(api_product: api_product, supplier: [supplier, nil].sample).call

      expect(mapped_product.is_vegan).to be_falsey
      expect(mapped_product.is_vegetarian).to be_falsey
      expect(mapped_product.is_gluten_free).to be_falsey
      expect(mapped_product.is_dairy_free).to be_falsey
    end

    it 'returns dietary fields as false if dear api does not contain any info in the passed in dietary_attribute' do
      dear_account.update_column(:dietary_attribute, 'AdditionalAttribute7')
      mapped_product = Dear::MapProduct.new(api_product: api_product, supplier: supplier).call

      expect(mapped_product.is_vegan).to be_falsey
      expect(mapped_product.is_vegetarian).to be_falsey
      expect(mapped_product.is_gluten_free).to be_falsey
      expect(mapped_product.is_dairy_free).to be_falsey
    end
  end

  it 'maps the attachments' do
    mapped_product = Dear::MapProduct.new(api_product: api_product).call

    expect(mapped_product.attachments.size).to eq(1)

    mapped_attachment = mapped_product.attachments.first
    api_attachment = api_product[:Attachments].first

    expect(mapped_attachment.default).to eq(api_attachment[:IsDefault])
    expect(mapped_attachment.url).to eq(api_attachment[:DownloadUrl])
    expect(mapped_attachment.type).to eq(api_attachment[:ContentType])
    expect(mapped_attachment.name).to eq(api_attachment[:FileName])
  end

end
