require 'rails_helper'

RSpec.describe Dear::SyncProduct, type: :serive, dear: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:menu_section) { create(:menu_section, :random, supplier_profile: supplier) }

  let!(:api_product) do # Snack Proud Product
    {
      ID: 'bfe60704-ff3f-4990-95f6-5ffd343c6843',
      SKU: 'ABB01',
      Name: 'Abbotts Farmhouse Whole Bread',
      Category: 'Breakfast & Pantry',
      Brand: 'Abbotts',
      Type: 'Stock',
      CostingMethod: 'FIFO',
      DropShipMode: 'Optional Drop Ship',
      DefaultLocation: 'Kingsgrove',
      PriceTier1: 4.5,
      PriceTier2: 6.5,
      # ...
      PriceTiers: {
        'Corp Volume (Tier 1)': 4.5,
        'Wholesale (PriceTier2)': 6.5,
        # ...
      },
      ShortDescription: 'This is the short description',
      Description: 'This is the long description',
      Status: 'Active',
      LastModifiedOn: '2022-09-06T14:04:39.38Z',
      AdditionalAttribute6: 'GF, V, DF, Keto',
      # ...
    }
  end

  let!(:mapped_product) do
    OpenStruct.new(
      sku: api_product[:SKU],
      name: api_product[:Name],
      description: api_product[:Description],
      price: api_product[:PriceTier1],
      is_vegan: true,
      is_vegetarian: false,
      is_gluten_free: false,
      is_dairy_free: true
    )
  end

  let!(:product_mapper) { double(Dear::MapProduct) }

  before do
    # mock product mapping
    allow(Dear::MapProduct).to receive(:new).and_return(product_mapper)
    allow(product_mapper).to receive(:call).and_return(mapped_product)
  end

  it 'makes a request to map the Dear product' do
    expect(Dear::MapProduct).to receive(:new).with(api_product: api_product, supplier: supplier)

    product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call
    expect(product_syncer).to be_success
  end

  it 'create a new menu item according to passed in api/mapped product' do
    product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call

    expect(product_syncer).to be_success

    synced_menu_item = product_syncer.menu_item
    expect(synced_menu_item).to be_present
    expect(synced_menu_item).to be_persisted
    expect(synced_menu_item.sku).to eq(api_product[:SKU]) # mapped_product.sku
    expect(synced_menu_item.name).to eq(api_product[:Name]) # mapped_product.name
    expect(synced_menu_item.description).to eq(api_product[:Description]) # mapped_product.description
    expect(synced_menu_item.price).to eq(api_product[:PriceTier1]) # mapped_product.price # default price tier

    expect(synced_menu_item.is_vegan).to be_truthy
    expect(synced_menu_item.is_vegetarian).to be_falsey
    expect(synced_menu_item.is_gluten_free).to be_falsey
    expect(synced_menu_item.is_dairy_free).to be_truthy
  end

  it 'create a new menu item within the passed in menu section (and supplier)' do
    product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section, supplier: [menu_section.supplier_profile, supplier, nil].sample).call

    expect(product_syncer).to be_success

    synced_menu_item = product_syncer.menu_item
    expect(synced_menu_item.menu_section).to eq(menu_section)
    expect(synced_menu_item.supplier_profile).to eq(supplier)
  end

  it 'does not import an api product with status not equal to `Active`' do
    inactive_api_product = api_product.merge({ Status: 'INACTIVE' })
    product_syncer = Dear::SyncProduct.new(api_product: inactive_api_product, menu_section: menu_section).call

    expect(product_syncer).to be_success
    expect(product_syncer.menu_item).to be_blank
  end

  it 'does not import an api product with mapped pricing as 0.0' do
    mapped_product_with_zero_pricing = mapped_product
    mapped_product_with_zero_pricing.price = 0.0
    allow(product_mapper).to receive(:call).and_return(mapped_product_with_zero_pricing)

    product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call

    expect(product_syncer).to be_success
    expect(product_syncer.menu_item).to be_blank
  end

  context 'with Attachments' do
    let!(:mapped_product_with_attachments) do
      product = mapped_product
      product.attachments = [
        OpenStruct.new(
          default: false,
          url: Faker::Internet.url,
          type: 'image/png',
          name: Faker::Name.name
        )
      ]
      product
    end

    before do
      # mock the mapper returns with attachments
      allow(product_mapper).to receive(:call).and_return(mapped_product_with_attachments)

      # mock product image syncer
      product_image_syncer = delayed_product_image_syncer = double(Dear::SyncProductImage)
      allow(Dear::SyncProductImage).to receive(:new).and_return(product_image_syncer)
      allow(product_image_syncer).to receive(:delay).and_return(delayed_product_image_syncer)
      allow(delayed_product_image_syncer).to receive(:call).and_return(true)
    end

    it 'requests the product image to be synced' do
      expect(Dear::SyncProductImage).to receive(:new).with(menu_item: anything, attachments: mapped_product_with_attachments.attachments)

      product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call
      expect(product_syncer).to be_success
    end
  end

  context 'with an existing menu item' do # testing MenuItems::Upsert
    let!(:menu_item) { create(:menu_item, :random, sku: api_product[:SKU], menu_section: menu_section, supplier_profile: supplier) }

    it 'does not create a duplicate item' do
      product_syncer =  Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call

      expect(product_syncer).to be_success
      synced_menu_item = product_syncer.menu_item

      expect(synced_menu_item.id).to eq(menu_item.id)
    end

    it 'updates the name, description and pricing of the existing menu item' do
      product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call

      expect(product_syncer).to be_success
      synced_menu_item = product_syncer.menu_item

      expect(synced_menu_item.id).to eq(menu_item.id)
      expect(synced_menu_item.name).to eq(api_product[:Name])
      expect(synced_menu_item.description).to eq(api_product[:Description])
      expect(synced_menu_item.price).to eq(api_product[:PriceTier1])
    end

    it 'updates any existing dietary flags' do
      item_flags = %i[is_vegan is_vegetarian is_gluten_free is_dairy_free]
      item_flags.each do |field|
        menu_item.update_column(field, true) # set fields to true
      end
      product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call

      expect(product_syncer).to be_success
      synced_menu_item = product_syncer.menu_item

      expect(synced_menu_item.id).to eq(menu_item.id)
      item_flags.each do |field|
        expect(synced_menu_item.send(field)).to eq(mapped_product.send(field))
      end
    end

    context 'archiving' do
      it 'un-archives an already archived item if api product status is active' do
        menu_item.update_column(:archived_at, Time.zone.now)
        product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call

        expect(product_syncer).to be_success
        synced_menu_item = product_syncer.menu_item

        expect(synced_menu_item.id).to eq(menu_item.id)
        expect(synced_menu_item.archived_at).to be_nil
      end

      it 'archives an existing item if the api_product is inactive' do
        inactive_api_product = api_product.merge({ Status: 'INACTIVE' })
        product_syncer = Dear::SyncProduct.new(api_product: inactive_api_product, menu_section: menu_section).call

        expect(product_syncer).to be_success
        synced_menu_item = product_syncer.menu_item

        expect(synced_menu_item.id).to eq(menu_item.id)
        expect(synced_menu_item.archived_at).to be_present
      end

      it 'archives an existing item if api_product price is 0.0' do
        mapped_product_with_zero_pricing = mapped_product
        mapped_product_with_zero_pricing.price = 0.0
        allow(product_mapper).to receive(:call).and_return(mapped_product_with_zero_pricing)

        product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call

        expect(product_syncer).to be_success
        synced_menu_item = product_syncer.menu_item

        expect(synced_menu_item.id).to eq(menu_item.id)
        expect(synced_menu_item.archived_at).to be_present
      end

      it 'archives an existing item if the api_product is inactive' do
        inactive_api_product = api_product.merge({ Status: 'INACTIVE' })
        product_syncer = Dear::SyncProduct.new(api_product: inactive_api_product, menu_section: menu_section).call

        expect(product_syncer).to be_success
        synced_menu_item = product_syncer.menu_item

        expect(synced_menu_item.id).to eq(menu_item.id)
        expect(synced_menu_item.archived_at).to be_present
      end
    end # archiving
  end # existing menu item

  context 'errors' do
    it 'does not sync product if the menu_section is missing' do
      product_syncer = Dear::SyncProduct.new(api_product: nil, menu_section: menu_section).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.errors).to include('Cannot sync product without an api product')
    end

    it 'does not sync product if the menu_section is missing' do
      product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: nil).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.errors).to include('Cannot sync product without a menu section')
    end

    it 'does not sync product with incorrect mapping' do
      if [true, false].sample
        invalid_mapped_products = nil
      else
        invalid_mapped_products = mapped_product
        invalid_mapped_products.sku = nil
      end
      allow(product_mapper).to receive(:call).and_return(invalid_mapped_products)

      product_syncer = Dear::SyncProduct.new(api_product: api_product, menu_section: menu_section).call

      expect(product_syncer).to_not be_success
      expect(product_syncer.errors).to include('Cannot sync product wihtout the correct mapping')
    end
  end # errors

end
