require 'rails_helper'

RSpec.describe Dear::SyncProductImage, type: :service, dear: true do

  let!(:menu_item) { create(:menu_item, :random) }
  let!(:mapped_attachments) do
    [
      {
        default: false,
        url: Faker::Internet.url,
        name: Faker::Name.name,
        type: 'image/png',
      },
      {
        default: false,
        url: Faker::Internet.url,
        name: Faker::Name.name,
        type: 'document/pdf',
      },
      {
        default: true,
        url: Faker::Internet.url,
        name: Faker::Name.name,
        type: 'image/png',
      }
    ].map{|attachment| OpenStruct.new(attachment) }
  end

  let!(:cloudinary_image) do
    {
      'url' => Faker::Internet.url,
      'secure_url' => Faker::Internet.url,
    }
  end

  before do
    cloudinary_uploader = double(Cloudinary::Uploader)
    allow(Cloudinary::Uploader).to receive(:upload).and_return(cloudinary_uploader).and_return(cloudinary_image)
  end

  it 'makes a request to upload the default image to cloudinary' do
    expect(Cloudinary::Uploader).to receive(:upload).with(mapped_attachments.detect(&:default).url)
    expect(Cloudinary::Uploader).to_not receive(:upload).with(mapped_attachments[0].url)
    expect(Cloudinary::Uploader).to_not receive(:upload).with(mapped_attachments[1].url)

    image_syncer = Dear::SyncProductImage.new(menu_item: menu_item, attachments: mapped_attachments).call
    expect(image_syncer).to be_success
  end

  it 'makes a request to upload the first non-default image to cloudinary if not default images are present' do
    non_default_image_attachments = mapped_attachments.dup
    non_default_image_attachments[1].default = true # make the supplier/pdf deafault
    non_default_image_attachments[2].default = false # make all image attachments non-default

    expect(Cloudinary::Uploader).to receive(:upload).with(non_default_image_attachments[0].url) # first image
    expect(Cloudinary::Uploader).to_not receive(:upload).with(non_default_image_attachments.detect(&:default).url) # default non-image
    expect(Cloudinary::Uploader).to_not receive(:upload).with(non_default_image_attachments[2].url)

    image_syncer = Dear::SyncProductImage.new(menu_item: menu_item, attachments: non_default_image_attachments).call
    expect(image_syncer).to be_success
  end

  it 'attaches the uploaded cloudinary image to the menu item' do
    image_syncer = Dear::SyncProductImage.new(menu_item: menu_item, attachments: mapped_attachments).call

    expect(image_syncer).to be_success
    expect(image_syncer.image).to eq(cloudinary_image['secure_url'])

    synced_menu_item = image_syncer.menu_item
    expect(synced_menu_item.id).to eq(menu_item.id)
    expect(synced_menu_item.image).to eq(cloudinary_image['secure_url'])
  end

  context 'menu item with an existing cloudinary image' do
    before do
      menu_item.update(image: 'http://cloudinary.com/some-image.png')
    end

    it 'does not upload new image' do
      expect(Cloudinary::Uploader).to_not receive(:upload)
      image_syncer = Dear::SyncProductImage.new(menu_item: menu_item, attachments: mapped_attachments).call
      expect(image_syncer).to_not be_success
      expect(image_syncer.errors).to include('Item already has an uploaded image')
    end

    it 'upload new image if set to refresh' do
      expect(Cloudinary::Uploader).to receive(:upload).with(mapped_attachments.detect(&:default).url)

      image_syncer = Dear::SyncProductImage.new(menu_item: menu_item, attachments: mapped_attachments, refresh: true).call

      expect(image_syncer).to be_success
      expect(image_syncer.image).to eq(cloudinary_image['secure_url'])

      synced_menu_item = image_syncer.menu_item
      expect(synced_menu_item.id).to eq(menu_item.id)
      expect(synced_menu_item.image).to eq(cloudinary_image['secure_url'])
    end
  end # existing cloudinary image

  context 'errors' do
    it 'does not upload image if the menu item is missing' do
      expect(Cloudinary::Uploader).to_not receive(:upload)

      image_syncer = Dear::SyncProductImage.new(menu_item: nil, attachments: mapped_attachments).call
      expect(image_syncer).to_not be_success
      expect(image_syncer.errors).to include('Cannot sync image to a missing menu item')
    end

    it 'does not upload if it is missing image attachments' do
      non_image_mapped_attachments = mapped_attachments.dup
      non_image_mapped_attachments[0].type = 'document/pdf'
      non_image_mapped_attachments[2].type = 'document/pdf'

      expect(Cloudinary::Uploader).to_not receive(:upload)

      image_syncer = Dear::SyncProductImage.new(menu_item: menu_item, attachments: non_image_mapped_attachments).call
      expect(image_syncer).to_not be_success
      expect(image_syncer.errors).to include('Cannot sync without an image')
    end

    it 'errors if the Cloudinary Uploader failed' do
      allow(Cloudinary::Uploader).to receive(:upload).and_raise(CloudinaryException.new)

      image_syncer = Dear::SyncProductImage.new(menu_item: menu_item, attachments: mapped_attachments).call
      expect(image_syncer).to_not be_success
      expect(image_syncer.errors).to include("Image upload to cloudinary failed for menu item - ##{menu_item.id} => #{mapped_attachments.detect(&:default).url}")
    end
  end # errors

end
