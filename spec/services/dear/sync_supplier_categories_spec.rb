require 'rails_helper'

RSpec.describe Dear::SyncSupplierCategories, type: :service, dear: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:dear_account) { create(:dear_account, :random, supplier_profile: supplier) }

  let!(:category1) { create(:dear_category, :random, dear_account: dear_account) }
  let!(:category2) { create(:dear_category, :random, dear_account: dear_account) }
  let!(:category3) { create(:dear_category, :random, dear_account: dear_account) }

  let!(:categories_fetcher) { double(Dear::API::FetchProductCategories) }

  let!(:api_categories) do
    {
      category1.category_id => Faker::Name.name,
      category2.category_id => Faker::Name.name,
      category3.category_id => Faker::Name.name,
    }
  end

  before do
    # mock categories fetcher
    allow(Dear::API::FetchProductCategories).to receive(:new).and_return(categories_fetcher)
    categories_fetcher_response = OpenStruct.new(
      success?: true,
      categories_hash: api_categories
    )
    allow(categories_fetcher).to receive(:call).and_return(categories_fetcher_response)
  end

  it 'makes a request to fetch Dear Product Categories' do
    expect(Dear::API::FetchProductCategories).to receive(:new).with(supplier: supplier, verbose: anything)

    categories_syncer = Dear::SyncSupplierCategories.new(supplier: supplier).call
    expect(categories_syncer).to be_success
  end

  it 'updates the name of a categories from the API' do
    categories_syncer = Dear::SyncSupplierCategories.new(supplier: supplier).call
    expect(categories_syncer).to be_success

    dear_account.reload.categories
    expect(category1.reload.name).to eq(api_categories[category1.category_id])
    expect(category2.reload.name).to eq(api_categories[category2.category_id])
    expect(category3.reload.name).to eq(api_categories[category3.category_id])
  end

  it 'removes a category from the saved ones if missing in the API' do
    category4 = create(:dear_category, :random, dear_account: dear_account)

    categories_syncer = Dear::SyncSupplierCategories.new(supplier: supplier).call
    expect(categories_syncer).to be_success

    expect{ category4.reload }.to raise_error(ActiveRecord::RecordNotFound)
  end

  context 'errors' do
    it 'errors if supplier is missing' do
      categories_syncer = Dear::SyncSupplierCategories.new(supplier: nil).call

      expect(categories_syncer).to_not be_success
      expect(categories_syncer.errors).to include('Cannot sync categories for a missing supplier')
    end

    it 'errors if supplier is not an active Dear supplier' do
      if [true, false].sample
        supplier2 = create(:supplier_profile, :random)
        dear_account.update_column(:supplier_profile_id, supplier2) # attach dear account to different supplier
      else
        dear_account.update_column(:active, false)
      end
      categories_syncer = Dear::SyncSupplierCategories.new(supplier: supplier).call

      expect(categories_syncer).to_not be_success
      expect(categories_syncer.errors).to include('Cannot sync categories for an missing/in-active Dear supplier')
    end

    it 'errors if supplier\'s dear account does not contain any categories' do
      [category1, category2, category3].each(&:destroy)

      categories_syncer = Dear::SyncSupplierCategories.new(supplier: supplier).call

      expect(categories_syncer).to_not be_success
      expect(categories_syncer.errors).to include('No need to sync categories')
    end

    it 'errors if supplier\'s returned api categories are blank or un-successfull' do
      if [true, false].sample
        missing_categories_response = OpenStruct.new(success?: true, categories_hash: [{}, nil].sample)
        allow(categories_fetcher).to receive(:call).and_return(missing_categories_response)
      else
        unsuccessful_categories_response = OpenStruct.new(success?: false)
        allow(categories_fetcher).to receive(:call).and_return(unsuccessful_categories_response)
      end

      categories_syncer = Dear::SyncSupplierCategories.new(supplier: supplier).call

      expect(categories_syncer).to_not be_success
      expect(categories_syncer.errors).to include('Cannot sync missing categories from API')
    end
  end

end
