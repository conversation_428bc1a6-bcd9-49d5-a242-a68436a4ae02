require 'rails_helper'

RSpec.describe EmployeeSurveys::Upsert, type: :service, customers: true, employee_surveys: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:category_group) { EmployeeSurvey::VALID_CATEGORY_GROUPS.sample }

  let!(:survey_params) do
    {
      category_group: category_group,
      active: true,
    }
  end

  before do
    question_prefiller = double(EmployeeSurveys::PrefillQuestions)
    allow(EmployeeSurveys::PrefillQuestions).to receive(:new).and_return(question_prefiller)
    allow(question_prefiller).to receive(:call).and_return(true)
  end

  it 'creates a new employee survey record for the passed in customer' do
    survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, survey_params: survey_params).call

    expect(survey_upserter).to be_success

    created_survey = survey_upserter.employee_survey
    expect(created_survey).to be_present
    expect(created_survey).to be_persisted
    expect(created_survey.customer_profile).to eq(customer)
    expect(created_survey.category_group).to eq(survey_params[:category_group])
  end

  it 'creates an employee survey with default values' do
    survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, survey_params: survey_params).call

    expect(survey_upserter).to be_success

    created_survey = survey_upserter.employee_survey
    expect(created_survey).to be_active
    expect(created_survey.uuid).to be_present
    expect(created_survey.name).to eq("#{EmployeeSurveys::Upsert::CATEGORY_NAME_MAP[survey_params[:category_group]]} Survey")
  end

  context 'prefill questions' do
    it 'doesn\'t request the questions to be prefilled by default' do
      expect(EmployeeSurveys::PrefillQuestions).to_not receive(:new)

      survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, survey_params: survey_params).call
      expect(survey_upserter).to be_success
    end

    it 'requests the questions to be prefilled if passed as a config' do
      expect(EmployeeSurveys::PrefillQuestions).to receive(:new).with(employee_survey: anything) # created employee survey

      survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, survey_params: survey_params, prefill: true).call
      expect(survey_upserter).to be_success
    end
  end

  context 'with existing survey' do
    let!(:employee_survey) { create(:employee_survey, :random, customer_profile: customer, category_group: category_group, active: false) }

    it 'updates the existing employee survey for the passed in category_group params' do
      survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, survey_params: survey_params).call

      expect(survey_upserter).to be_success
      updated_survey = survey_upserter.employee_survey

      expect(updated_survey.id).to eq(employee_survey.id)
      expect(updated_survey).to be_active # as passed in survey params
    end

    it 'updates the existing passed in employee survey' do
      survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, employee_survey: employee_survey, survey_params: survey_params).call

      expect(survey_upserter).to be_success
      updated_survey = survey_upserter.employee_survey

      expect(updated_survey.id).to eq(employee_survey.id)
      expect(updated_survey).to be_active # as passed in survey params
    end

    it 'updates the existing passed-in employee survey, without updating the category group' do
      different_category_group = (EmployeeSurvey::VALID_CATEGORY_GROUPS - [category_group]).sample
      survey_params_with_different_category_group = survey_params.merge({ category_group: different_category_group })
      survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, employee_survey: employee_survey, survey_params: survey_params_with_different_category_group).call

      expect(survey_upserter).to be_success
      updated_survey = survey_upserter.employee_survey

      expect(updated_survey.id).to eq(employee_survey.id)
      expect(updated_survey.category_group).to_not eq(survey_params_with_different_category_group[:category_group])
      expect(updated_survey.category_group).to eq(category_group)
      expect(updated_survey).to be_active # as passed in survey params
    end
  end

  context 'errors' do
    it 'errors if the customer is missing' do
      survey_upserter = EmployeeSurveys::Upsert.new(customer: nil, survey_params: survey_params).call

      expect(survey_upserter).to_not be_success
      expect(survey_upserter.errors).to include('Customer is missing')
    end

    it 'errors if the survey params is missing category_group' do
      survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, survey_params: survey_params.except(:category_group)).call

      expect(survey_upserter).to_not be_success
      expect(survey_upserter.errors).to include('Survey does not belong to the customer')
    end

    it 'errors with invalid survey params' do
      invalid_survey_params = { category_group: 'invalid-category-group' }
      survey_upserter = EmployeeSurveys::Upsert.new(customer: customer, survey_params: invalid_survey_params).call

      expect(survey_upserter).to_not be_success
      expect(survey_upserter.errors).to include('Category group is not included in the list')
    end
  end

end