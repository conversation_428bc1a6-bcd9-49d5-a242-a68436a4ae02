require 'rails_helper'

RSpec.describe EmployeeSurveys::SubmitAnswer, type: :service, employee_surveys: true do

  let!(:employee_survey) { create(:employee_survey, :random) }
  let!(:survey_question) { create(:survey_question, :random, employee_survey: employee_survey) }

  let!(:employee_survey_submission) { create(:employee_survey_submission, :random, employee_survey: employee_survey) }

  let!(:answer_params) do
    {
      survey_question_id: survey_question.id,
      value: Faker::Lorem.sentence,
    }
  end

  it 'saves the passed in answer for the survey submission' do
    answer_submitter = EmployeeSurveys::SubmitAnswer.new(employee_survey_submission: employee_survey_submission, answer_params: answer_params).call

    expect(answer_submitter).to be_success

    submitted_answer = answer_submitter.survey_answer
    expect(submitted_answer).to be_present
    expect(submitted_answer).to be_persisted
    expect(submitted_answer.employee_survey_submission).to eq(employee_survey_submission)
    expect(submitted_answer.survey_question).to eq(survey_question)
    expect(submitted_answer.value).to eq(answer_params[:value])
  end

  it 'saves the label of the question on submission' do
    answer_submitter = EmployeeSurveys::SubmitAnswer.new(employee_survey_submission: employee_survey_submission, answer_params: answer_params).call

    expect(answer_submitter).to be_success

    submitted_answer = answer_submitter.survey_answer
    expect(submitted_answer.question_label).to eq(survey_question.label)
  end

  context 'errors' do
    it 'errors out without an employee survey submission' do
      answer_submitter = EmployeeSurveys::SubmitAnswer.new(employee_survey_submission: nil, answer_params: answer_params).call

      expect(answer_submitter).to_not be_success
      expect(answer_submitter.errors).to include('Cannot submit an answer without a submission')
    end

    it 'errors out if the answer params are missing' do
      invalid_answer_params = [nil, {}, answer_params.merge({ value: nil })].sample
      answer_submitter = EmployeeSurveys::SubmitAnswer.new(employee_survey_submission: employee_survey_submission, answer_params: invalid_answer_params).call

      expect(answer_submitter).to_not be_success
      expect(answer_submitter.errors).to include('Answer is missing')
    end

    it 'errors out if the question is missing' do
      missing_question_answer_params = case
      when [true, false].sample
        answer_params.merge({ survey_question_id: nil })
      else
        non_survey_question = create(:survey_question, :random)
        answer_params.merge({ survey_question_id: non_survey_question.id })
      end
      answer_submitter = EmployeeSurveys::SubmitAnswer.new(employee_survey_submission: employee_survey_submission, answer_params: missing_question_answer_params).call

      expect(answer_submitter).to_not be_success
      expect(answer_submitter.errors).to include('Could not find the question')
    end
  end

end
