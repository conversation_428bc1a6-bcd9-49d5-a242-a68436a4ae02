require 'rails_helper'

RSpec.describe EmployeeSurveys::Submit, type: :service, employee_surveys: true do

  let!(:employee_survey) { create(:employee_survey, :random) }

  let!(:survey_question1) { create(:survey_question, :random, employee_survey: employee_survey) }
  let!(:survey_question2) { create(:survey_question, :random, employee_survey: employee_survey) }
  let!(:survey_question3) { create(:survey_question, :random, employee_survey: employee_survey) }

  let!(:submission_answers) do
    [survey_question1, survey_question2, survey_question3].map do |question|
      {
        survey_question_id: question.id,
        value: "answer #{question.id}",
      }
    end
  end

  let!(:submission_params) do
    {
      name: Faker::Name.name,
      survey_answers: submission_answers
    }
  end

  it 'creates a new employee survey submission' do
    survey_submitter = EmployeeSurveys::Submit.new(employee_survey: employee_survey, submission_params: submission_params).call

    expect(survey_submitter).to be_success
    survey_submssion = survey_submitter.submission

    expect(survey_submssion).to be_present
    expect(survey_submssion).to be_a(EmployeeSurveySubmission)
    expect(survey_submssion).to be_persisted
    expect(survey_submssion.employee_survey).to eq(employee_survey)
  end

  it 'attaches the answers to the submission' do
    survey_submitter = EmployeeSurveys::Submit.new(employee_survey: employee_survey, submission_params: submission_params).call

    expect(survey_submitter).to be_success
    survey_submssion = survey_submitter.submission

    expect(survey_submssion).to be_present

    survey_answers = survey_submssion.survey_answers
    expect(survey_answers).to be_present
    expect(survey_answers.size).to eq(submission_answers.size)
    expect(survey_answers.map(&:survey_question)).to include(survey_question1, survey_question2, survey_question3)
    expect(survey_answers.map(&:value)).to include(*submission_answers.map{|answer| answer[:value] })
  end

  it 'creates a new employe_submission with just the overall ratings' do
    ratings_only_submission_params = { overall_rating: rand(0..10), survey_answers: [] }
    survey_submitter = EmployeeSurveys::Submit.new(employee_survey: employee_survey, submission_params: ratings_only_submission_params).call

    expect(survey_submitter).to be_success
    survey_submssion = survey_submitter.submission

    expect(survey_submssion).to be_present
    expect(survey_submssion.overall_rating).to eq(ratings_only_submission_params[:overall_rating])
  end

  context 'errors' do
    it 'errors if the employee_survey is missing' do
      survey_submitter = EmployeeSurveys::Submit.new(employee_survey: nil, submission_params: submission_params).call

      expect(survey_submitter).to_not be_success
      expect(survey_submitter.errors).to include('Missing Survey')
    end

    it 'errors if the submission does not contain any answers and any overall rating' do
      submission_params_with_missing_answers = submission_params.merge({ overall_rating: nil, survey_answers: [] })
      survey_submitter = EmployeeSurveys::Submit.new(employee_survey: employee_survey, submission_params: submission_params_with_missing_answers).call

      expect(survey_submitter).to_not be_success
      expect(survey_submitter.errors).to include('Cannot submit survey without any answers')
    end
  end

end
