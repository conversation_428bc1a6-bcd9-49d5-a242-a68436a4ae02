require 'rails_helper'

RSpec.describe EmployeeSurveys::ListSubmissions, type: :service, employee_surveys: true do

  let!(:employee_survey1) { create(:employee_survey, :random) }
  let!(:employee_survey2) { create(:employee_survey, :random) }

  let!(:submission11) { create(:employee_survey_submission, :random, employee_survey: employee_survey1) }
  let!(:submission12) { create(:employee_survey_submission, :random, employee_survey: employee_survey1) }
  let!(:submission13) { create(:employee_survey_submission, :random, employee_survey: employee_survey1) }

  let!(:submission21) { create(:employee_survey_submission, :random, employee_survey: employee_survey2) }

  it 'only lists the submissions of the passed in employee survey' do
    listed_submissions = EmployeeSurveys::ListSubmissions.new(employee_survey: employee_survey1).call

    expect(listed_submissions).to include(submission11, submission12, submission13)
    expect(listed_submissions).to_not include(submission21)
  end

  context 'filter by date' do
    before do
      submission11.update_column(:created_at, Time.zone.now - 7.months)
      submission12.update_column(:created_at, Time.zone.now - 5.months)
      submission13.update_column(:created_at, Time.zone.now - 1.month)
    end

    it 'lists the submissions in the last 6 months by default' do
      listed_submissions = EmployeeSurveys::ListSubmissions.new(employee_survey: employee_survey1).call
      
      expect(listed_submissions).to include(submission12, submission13)
      expect(listed_submissions).to_not include(submission11) # before 6 months

      expect(listed_submissions).to_not include(submission21) # does not belong to passed in employee survey
    end

    it 'lists the submissions between the passed in start and end dates' do
      lister_options = { starts_on: (Time.zone.now - 8.months), ends_on: (Time.zone.now - 3.months) }
      listed_submissions = EmployeeSurveys::ListSubmissions.new(employee_survey: employee_survey1, options: lister_options).call
      
      expect(listed_submissions).to include(submission11, submission12)
      expect(listed_submissions).to_not include(submission13) # outside range

      expect(listed_submissions).to_not include(submission21) # does not belong to passed in employee survey
    end
  end

  it 'returns blank if the employee survey is missing' do
    listed_submissions = EmployeeSurveys::ListSubmissions.new(employee_survey: nil).call

    expect(listed_submissions).to be_empty
  end

end