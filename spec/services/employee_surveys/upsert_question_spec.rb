require 'rails_helper'

RSpec.describe EmployeeSurveys::UpsertQuestion, type: :service, customers: true, employee_surveys: true do

  let!(:employee_survey) { create(:employee_survey, :random) }
  let!(:question_params) do
    {
      label: Faker::Lorem.sentence,
      input_type: SurveyQuestion::VALID_INPUT_TYPES.sample,
      active: true,
    }
  end

  it 'creates a new survey question record for the passed in employee_survey' do
    question_upserter = EmployeeSurveys::UpsertQuestion.new(employee_survey: employee_survey, question_params: question_params).call

    expect(question_upserter).to be_success

    created_question = question_upserter.survey_question
    expect(created_question).to be_present
    expect(created_question).to be_persisted
    expect(created_question.employee_survey).to eq(employee_survey)
  end

  it 'creates an survey question with default and passed in values' do
    question_upserter = EmployeeSurveys::UpsertQuestion.new(employee_survey: employee_survey, question_params: question_params).call

    expect(question_upserter).to be_success

    created_question = question_upserter.survey_question
    expect(created_question.label).to eq(question_params[:label])
    expect(created_question.input_type).to eq(question_params[:input_type])
    expect(created_question).to be_active
  end

  context 'with existing survey' do
    let!(:survey_question) { create(:survey_question, :random, employee_survey: employee_survey, active: false) }

    it 'updates the existing survey question for the passed in params' do
      question_upserter = EmployeeSurveys::UpsertQuestion.new(employee_survey: employee_survey, question: survey_question, question_params: question_params).call

      expect(question_upserter).to be_success
      updated_question = question_upserter.survey_question

      expect(updated_question.id).to eq(survey_question.id)
      expect(updated_question.label).to eq(question_params[:label])
      expect(updated_question.input_type).to eq(question_params[:input_type])
      expect(updated_question).to be_active
    end
  end

  context 'question position' do
    it 'create survey question with a default position' do
      question_upserter = EmployeeSurveys::UpsertQuestion.new(employee_survey: employee_survey, question_params: question_params).call

      expect(question_upserter).to be_success

      created_question = question_upserter.survey_question
      expect(created_question.position).to eq(1)
    end

    context 'with existing surveys' do
      let!(:survey_question1) { create(:survey_question, :random, employee_survey: employee_survey, active: true, position: 10) }
      let!(:survey_question2) { create(:survey_question, :random, employee_survey: employee_survey, active: false, position: 7) }

      it 'creates a new survey question with next highest position (based on active survey questions' do
        question_upserter = EmployeeSurveys::UpsertQuestion.new(employee_survey: employee_survey, question_params: question_params).call

        expect(question_upserter).to be_success

        created_question = question_upserter.survey_question
        expect(created_question.position).to eq(survey_question1.position + 1)
      end
    end
  end # question position

  context 'errors' do
    it 'errors if the employee_survey is missing' do
      question_upserter = EmployeeSurveys::UpsertQuestion.new(employee_survey: nil, question_params: question_params).call

      expect(question_upserter).to_not be_success
      expect(question_upserter.errors).to include('Missing Survey to attach question')
    end

    it 'errors with invalid question params' do
      invalid_question_params = { label: nil, input_type: 'invalid-input-type' }
      question_upserter = EmployeeSurveys::UpsertQuestion.new(employee_survey: employee_survey, question_params: invalid_question_params).call

      expect(question_upserter).to_not be_success
      expect(question_upserter.errors).to include('Label can\'t be blank', 'Input type is not included in the list')
    end
  end

end