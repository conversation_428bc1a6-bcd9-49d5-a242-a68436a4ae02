require 'rails_helper'

RSpec.describe EmployeeSurveys::PrefillQuestions, type: :service, employee_surveys: true do

  let!(:catering_survey) { create(:employee_survey, :random, category_group: 'catering-services') }
  let!(:pantry_survey) { create(:employee_survey, :random, category_group: 'kitchen-supplies') }

  it 'adds the relevant prefillable questions to the catering survey' do
    question_prefiller = EmployeeSurveys::PrefillQuestions.new(employee_survey: catering_survey).call

    expect(question_prefiller).to be_success
    prefilled_questions = question_prefiller.questions

    expect(prefilled_questions.size).to eq(7)
    expect(prefilled_questions.map(&:label)).to include('Is there anything about our meals you would improve, and if so, what?')

    toggle_question = prefilled_questions.detect{|question| question.label == 'If you have a dietary restriction please rate how accommodated you are in the meals offered?'}
    expect(toggle_question.input_type).to eq('ratings')
  end

  it 'adds the relevant prefillable questions to the pantry survey with options' do
    question_prefiller = EmployeeSurveys::PrefillQuestions.new(employee_survey: pantry_survey).call

    expect(question_prefiller).to be_success
    prefilled_questions = question_prefiller.questions

    expect(prefilled_questions.size).to eq(5)
    expect(prefilled_questions.map(&:label)).to include('How would you rate the variety of products?')

    toggle_question = prefilled_questions.detect{|question| question.label == 'Are there enough healthy products on offer?'}
    expect(toggle_question.input_type).to eq('toggle')
    expect(toggle_question.options).to include('Yes', 'No')
  end

  context 'errors' do
    it 'errors if the employee survey is missing' do
      question_prefiller = EmployeeSurveys::PrefillQuestions.new(employee_survey: nil).call

      expect(question_prefiller).to_not be_success
      expect(question_prefiller.errors).to include('Missing Employee Survey')
    end

    it 'errors if the employee survey already has existing survey questions' do
      create(:survey_question, :random, employee_survey: catering_survey)
      question_prefiller = EmployeeSurveys::PrefillQuestions.new(employee_survey: catering_survey).call

      expect(question_prefiller).to_not be_success
      expect(question_prefiller.errors).to include('Survey Already has questions')
    end

    it 'errors if there are no questions for the (unknown) employee survey category group' do
      catering_survey.update_column(:category_group, 'unkown-category-group') # update columns to avoid model validation
      question_prefiller = EmployeeSurveys::PrefillQuestions.new(employee_survey: catering_survey).call

      expect(question_prefiller).to_not be_success
      expect(question_prefiller.errors).to include("Could not find questions for #{catering_survey.category_group}")
    end
  end # errors

end