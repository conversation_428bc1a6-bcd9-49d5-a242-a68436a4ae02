require 'rails_helper'

RSpec.describe Admin::FavouriteCustomer, type: :service, admin: true, customers: true do

  let!(:customer1) { create(:customer_profile, :random, :with_user) }
  let!(:customer2) { create(:customer_profile, :random, :with_user) }
  let!(:customer3) { create(:customer_profile, :random, :with_user) }

  let(:super_admin) { create(:user, :random, super_admin: true, admin: false,) }
  let(:admin) { create(:user, :random, super_admin: false, admin: true) }
  let(:yordar_admin) { [super_admin, admin].sample }

  it 'lets yordar admins favourite customers' do
    customer_favouriter = Admin::FavouriteCustomer.new(user: yordar_admin, customer: customer1).call

    expect(customer_favouriter).to be_success
    expect(yordar_admin.reload.favourite_customer_profiles).to include(customer1)
    expect(customer_favouriter.favourite_mode).to eq('favourited')
  end

  context 'with existing favourited customers' do
    let!(:favourite_customer1) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer1 )}
    let!(:favourite_customer2) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer2 )}

    it 'adds a new customer to the favourites list' do
      customer_favouriter = Admin::FavouriteCustomer.new(user: yordar_admin, customer: customer3).call

      expect(customer_favouriter).to be_success
      expect(yordar_admin.reload.favourite_customer_profiles).to include(customer3, customer1, customer2)
      expect(customer_favouriter.favourite_mode).to eq('favourited')
    end

    it 'removes a new customer form the users favourite list' do
      customer_favouriter = Admin::FavouriteCustomer.new(user: yordar_admin, customer: customer2).call

      expect(customer_favouriter).to be_success
      expect(yordar_admin.reload.favourite_customer_profiles).to_not include(customer2)
      expect(yordar_admin.reload.favourite_customer_profiles).to_not include(customer3) # was not favourited
      expect(yordar_admin.reload.favourite_customer_profiles).to include(customer1)
      expect(customer_favouriter.favourite_mode).to eq('un-favourited')
    end
  end

  context 'errors' do
    it 'cannot favourite without a user' do
      customer_favouriter = Admin::FavouriteCustomer.new(user: nil, customer: customer1).call

      expect(customer_favouriter).to_not be_success
      expect(customer_favouriter.errors).to include('Cannot favourite customer without a user')
    end

    it 'cannot favourite without a customer' do
      customer_favouriter = Admin::FavouriteCustomer.new(user: yordar_admin, customer: nil).call

      expect(customer_favouriter).to_not be_success
      expect(customer_favouriter.errors).to include('Cannot favourite without a customer')
    end

    context 'access error' do
      context 'with all customers access' do
        let!(:all_customer_admin) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: true) }

        it 'user who can access all customer can only favourite active customers' do        
          customer3.user.update_column(:is_active, false)

          customer1_favouriter = Admin::FavouriteCustomer.new(user: all_customer_admin, customer: customer1).call

          expect(customer1_favouriter).to be_success
          expect(all_customer_admin.reload.favourite_customer_profiles).to include(customer1)

          customer3_favouriter = Admin::FavouriteCustomer.new(user: all_customer_admin, customer: customer3).call

          expect(customer3_favouriter).to_not be_success
          expect(customer3_favouriter.errors).to include('You do not have access to this customer')
          expect(all_customer_admin.reload.favourite_customer_profiles).to_not include(customer3)
        end
      end

      context 'as company team admin' do
        let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }
        let!(:customer_team_admin_user) { create(:user, :random, super_admin: false, admin: false) }

        let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer1) }
        let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3) }

        before do
          customer_team_admin.profile.update_column(:user_id, customer_team_admin_user.id) # attach non-admin-user record to company team admin profile
          customer_team_admin_user.reload
        end

        it 'only lets them favourite customers they have access to' do
          customer1_favouriter = Admin::FavouriteCustomer.new(user: customer_team_admin_user, customer: customer1).call

          expect(customer1_favouriter).to be_success
          expect(customer_team_admin_user.reload.favourite_customer_profiles).to include(customer1)

          customer2_favouriter = Admin::FavouriteCustomer.new(user: customer_team_admin_user, customer: customer2).call

          expect(customer2_favouriter).to_not be_success
          expect(customer2_favouriter.errors).to include('You do not have access to this customer')
          expect(customer_team_admin_user.reload.favourite_customer_profiles).to_not include(customer2)
        end
      end
    end
  end

end