require 'rails_helper'

RSpec.describe Admin::ListCoupons, type: :service, admin: true, users: true, coupons: true do

  let!(:coupon1) { create(:coupon, :random, description: 'coupon1') }
  let!(:coupon2) { create(:coupon, :random, description: 'coupon2') }
  let!(:coupon3) { create(:coupon, :random, description: 'coupon3') }

  it 'lists all coupons by default' do
    coupons = Admin::ListCoupons.new.call

    expect(coupons).to include(coupon1, coupon2, coupon3)
  end

  context 'filter by query' do
    it 'filters coupons by their code' do
      lister_options = { query: coupon2.name }
      coupons = Admin::ListCoupons.new(options: lister_options).call

      expect(coupons).to include(coupon2)
      expect(coupons).to_not include(coupon1, coupon1)
    end

    it 'filters coupons by their description' do
      lister_options = { query: coupon3.description }
      coupons = Admin::ListCoupons.new(options: lister_options).call

      expect(coupons).to include(coupon3)
      expect(coupons).to_not include(coupon1, coupon2)
    end

    context 'with coupons connected to orders' do
      let!(:customer1) { create(:customer_profile, :random, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
      let!(:order11) { create(:order, :confirmed, customer_profile: customer1, coupon: coupon1) }
      let!(:order12) { create(:order, :confirmed, customer_profile: customer1, coupon: coupon2) }

      let!(:customer2) { create(:customer_profile, :random, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
      let!(:order21) { create(:order, :confirmed, customer_profile: customer2, coupon: coupon2) }

      let!(:customer3) { create(:customer_profile, :random, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
      let!(:order31) { create(:order, :confirmed, customer_profile: customer3, coupon: coupon2) }
      let!(:order32) { create(:order, :confirmed, customer_profile: customer3, coupon: coupon3) }

      it 'filters coupons by their attached order ID' do
        lister_options = { query: order32.id }
        coupons = Admin::ListCoupons.new(options: lister_options).call
        
        expect(coupons).to include(coupon3)
        expect(coupons).to_not include(coupon1, coupon2)
      end

      it 'filters coupons by their attached order\'s customer name' do
        lister_options = { query: customer1.customer_name }
        coupons = Admin::ListCoupons.new(options: lister_options).call
        
        expect(coupons).to include(coupon1, coupon2)
        expect(coupons).to_not include(coupon3)
      end

      it 'filters coupons by their attached order\'s customer\'s company name' do
        lister_options = { query: customer3.company_name }
        coupons = Admin::ListCoupons.new(options: lister_options).call
        
        expect(coupons).to include(coupon2, coupon3)
        expect(coupons).to_not include(coupon1)
      end

      context 'with attached customer company' do
        let!(:company) { create(:company, :random, customer_profiles: [customer1, customer2]) }

        it 'filters coupons by their attached order\'s customer\'s attached company name' do
          lister_options = { query: company.name }
          coupons = Admin::ListCoupons.new(options: lister_options).call
          
          expect(coupons).to include(coupon1, coupon2)
          expect(coupons).to_not include(coupon3)
        end
      end # with attached company
    end # with orders  
  end # filter by query

  context 'filter by Coupon' do
    it 'returns the passed in coupon if accessible' do
      lister_options = { coupon: coupon2 }
      coupons = Admin::ListCoupons.new(options: lister_options).call

      expect(coupons).to include(coupon2)
      expect(coupons).to_not include(coupon1, coupon3)
    end

    it 'returns an empty list for non-coupon object' do
      supplier = create(:supplier_profile, :random, :with_user)
      lister_options = { coupon: supplier }
      coupons = Admin::ListCoupons.new(options: lister_options).call

      expect(coupons).to be_empty
    end
  end

  it 'paginates coupons according to page and limit (default sort of created_at and then ID)' do
    lister_options = { page: 2, limit: 1 }
    coupons = Admin::ListCoupons.new(options: lister_options).call

    expect(coupons).to include(coupon2)
    expect(coupons).to_not include(coupon1, coupon3)
  end

end