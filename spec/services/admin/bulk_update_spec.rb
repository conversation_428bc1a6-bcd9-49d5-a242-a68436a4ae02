require 'rails_helper'

RSpec.describe Admin::BulkUpdate, type: :service, rate_cards: true do

  context 'Rate Cards' do
    let!(:rate_card1) { create(:rate_card, :random) }
    let!(:rate_card2) { create(:rate_card, :random) }
    let!(:rate_card3) { create(:rate_card, :random) }
    let!(:rate_card4) { create(:rate_card, :random) }
    let!(:all_rate_cards) { [rate_card1, rate_card2, rate_card3, rate_card4] }

    let(:model_name) { 'rate_card' }
    let!(:update_params) do
      {
        rate_card1.id.to_s => {
          price: '10',
          cost: '11',
        },
        rate_card2.id.to_s => {
          price: '12',
          cost: '13',
        },
        rate_card3.id.to_s => {
          price: '14',
          cost: '15',
        },
        rate_card4.id.to_s => {
          price: '16',
          cost: '17',
        },
      }
    end

    it 'bulk updates based on the params passed' do
      Admin::BulkUpdate.new(model_name: model_name, bulk_ids: all_rate_cards.map(&:id), update_params: update_params).call

      all_rate_cards.each(&:reload)
      expect(rate_card1.price).to eq(10)
      expect(rate_card1.cost).to eq(11)

      expect(rate_card2.price).to eq(12)
      expect(rate_card2.cost).to eq(13)

      expect(rate_card3.price).to eq(14)
      expect(rate_card3.cost).to eq(15)

      expect(rate_card4.price).to eq(16)
      expect(rate_card4.cost).to eq(17)
    end

    it 'does not update item if item bulk ids don\'t include them' do
      Admin::BulkUpdate.new(model_name: model_name, bulk_ids: [rate_card1, rate_card4].map(&:id), update_params: update_params).call

      all_rate_cards.each(&:reload)
      expect(rate_card1.price).to eq(10)
      expect(rate_card1.cost).to eq(11)

      expect(rate_card2.price).to_not eq(12) # does not update rate_card2
      expect(rate_card2.cost).to_not eq(13)

      expect(rate_card3.price).to_not eq(14) # does not update rate_card3
      expect(rate_card3.cost).to_not eq(15)

      expect(rate_card4.price).to eq(16)
      expect(rate_card4.cost).to eq(17)
    end
  end

end
