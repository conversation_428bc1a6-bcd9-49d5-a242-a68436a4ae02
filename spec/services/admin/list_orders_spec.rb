require 'rails_helper'

RSpec.describe Admin::ListOrders, type: :service, admin: true, users: true, orders: true do
  
  let!(:customer1) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
  let!(:customer2) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
  let!(:customer3) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }

  let(:super_admin) { create(:user, :random, super_admin: true, admin: false, allow_all_customer_access: false) }
  let(:admin) { create(:user, :random, super_admin: false, admin: true, allow_all_customer_access: false) }
  let(:all_order_admin) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: true) }

  let(:yordar_admin) { [super_admin, admin].sample }
  let(:non_admin_user) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: false) }

  let!(:delivery_day) { (Time.zone.now + 1.week).beginning_of_week + 1.day }

  let!(:order1) { create(:order, :delivered, customer_profile: customer1, delivery_at: delivery_day + 2.hours) }
  let!(:order2) { create(:order, :delivered, customer_profile: customer2, delivery_at: delivery_day + 1.days + 2.hours) }
  let!(:order3) { create(:order, :delivered, customer_profile: customer3, delivery_at: delivery_day + 2.days + 2.hours) }

  context 'Access based filtering' do
    it 'does not lists any orders for missing user' do
      lister_options =  { for_user: nil, query: [true, false].sample ? order1.name : '' }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to be_blank
    end

    it 'super admins, Yordar Admins can list all orders' do
      lister_options = { for_user: yordar_admin }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order1, order2, order3)
    end

    it 'User who can access all customer can only list orders from active orders' do
      customer2.user.update_column(:is_active, false)

      lister_options = { for_user: all_order_admin }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order1, order3)
      expect(orders).to_not include(order2)
    end

    it 'Non admin users cannot list any orders' do
      lister_options = { for_user: non_admin_user }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to be_blank
    end

    context 'as Customer Team Admin' do
      let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

      let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer2) }
      let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3) }

      before do
        customer_team_admin.profile.update_column(:user_id, non_admin_user.id) # attach non-admin-user record to company team admin profile
        non_admin_user.reload
      end

      it 'only lists orders of customers the team admin has active access permission to' do
        lister_options = { for_user: non_admin_user }
        orders = Admin::ListOrders.new(options: lister_options).call

        expect(orders).to include(order2, order3)
        expect(orders).to_not include(order1)
      end

      it 'does not list orders of customers with non-active access permissions' do
        admin_access_permission1.update_column(:active, false)
        lister_options = { for_user: non_admin_user }
        orders = Admin::ListOrders.new(options: lister_options).call

        expect(orders).to include(order3)
        expect(orders).to_not include(order1, order2)
      end

      it 'also lists its own orders' do
        customer_admin_order = create(:order, :delivered, customer_profile: customer_team_admin)
        lister_options = { for_user: non_admin_user }
        orders = Admin::ListOrders.new(options: lister_options).call

        expect(orders).to include(customer_admin_order)
        # expect(orders).to include(order2, order3)
        # expect(orders).to_not include(order1)
      end

      context 'with customer favourites' do
        let!(:favourite_customer2) { create(:favourite_customer, :random, favouriter: non_admin_user, customer_profile: customer2) }

        it 'only lists the orders for the favourited customers' do
          lister_options = { for_user: non_admin_user, favourites_only: true }
          orders = Admin::ListOrders.new(options: lister_options).call

          expect(orders).to include(order2)
          expect(orders).to_not include(order1, order3)
        end
      end # with favoutire orders (more like prioritized customers)
    end # As Company Team Admin    
  end # Access based filtering

  context 'filter by query' do
    it 'filters orders by their id' do
      lister_options = { for_user: yordar_admin, query: order2.id }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order2)
      expect(orders).to_not include(order1, order3)
    end

    it 'filters orders by their name' do
      lister_options = { for_user: yordar_admin, query: order3.name }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order3)
      expect(orders).to_not include(order1, order2)
    end

    it 'filters orders by customer name' do
      lister_options = { for_user: yordar_admin, query: order2.customer_profile.customer_name }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order2)
      expect(orders).to_not include(order1, order3)
    end

    it 'filters orders by the customer\'s company name' do
      lister_options = { for_user: yordar_admin, query: order1.customer_profile.company_name }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order1)
      expect(orders).to_not include(order2, order3)
    end

    context 'with associated companies' do
      let!(:company1) { create(:company, :random, customer_profiles: [customer1]) }
      let!(:company3) { create(:company, :random, customer_profiles: [customer3]) }

      it 'filters orders by the associated company name' do
        lister_options = { for_user: yordar_admin, query: company3.name }
        orders = Admin::ListOrders.new(options: lister_options).call

        expect(orders).to include(order3)
        expect(orders).to_not include(order1, order2)
      end
    end

    context 'with associated woolworths order' do
      let!(:woolworths_order) { create(:woolworths_order, :random, order: order2, woolworths_order_id: '*********')}

      it 'filters orders by the associated Woolworths Order ID' do
        lister_options = { for_user: yordar_admin, query: '*********' }
        orders = Admin::ListOrders.new(options: lister_options).call

        expect(orders).to include(order2)
        expect(orders).to_not include(order1, order3)
      end
    end
  end

  context 'filter by status' do
    before do
      order3.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[quoted pending new amended confirmed delivered paused skipped cancelled]).sample)
    end

    it 'filters orders that are either quoted/pending/new/amended/confirmed/delivered/paused/skipped/cancelled' do
      lister_options = { for_user: yordar_admin }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order1, order2)
      expect(orders).to_not include(order3)
    end
  end

  context 'filter by custom orders' do
    before do
      [order1, order3].each do |order|
        order.update_column(:order_variant, 'event_order')
      end
    end

    it 'only returns custom orders' do
      order1.update_column(:order_variant, 'event_order')
      lister_options = { for_user: yordar_admin, custom_orders_only: true }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order1, order3)
      expect(orders).to_not include(order2)
    end

    it 'also returns draft custom orders' do
      order1.update_column(:status, 'draft')
      lister_options = { for_user: yordar_admin, custom_orders_only: true }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order1, order3) # still includes a draft order1
      expect(orders).to_not include(order2)
    end
  end

  context 'filter by Order' do
    it 'returns the passed in order if accessible' do
      lister_options = { for_user: yordar_admin, order: order2 }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order2)
      expect(orders).to_not include(order1, order3)
    end

    it 'returns an empty list for non-order object' do
      supplier = create(:supplier_profile, :random, :with_user)
      lister_options = { for_user: yordar_admin, order: supplier }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to be_empty
    end
  end

  context 'with customer favourites' do
    let!(:favourite_customer1) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer1) }
    let!(:favourite_customer3) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer3) }

    it 'only lists the orders for the favourited orders only' do
      lister_options = { for_user: yordar_admin, favourites_only: true }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order1, order3)
      expect(orders).to_not include(order2)
    end
  end

  it 'paginates orders according to page and limit (default sort of delivery at ASC and then ID)' do
    lister_options = { for_user: yordar_admin, page: 2, limit: 1 }
    orders = Admin::ListOrders.new(options: lister_options).call

    expect(orders).to include(order2)
    expect(orders).to_not include(order1, order3)
  end

  context 'filter by delivery dates' do
    before do
      order2.update_column(:delivery_at, Time.zone.now - 2.days)
    end

    it 'filters upcoming orders by default' do
      lister_options = { for_user: yordar_admin }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order1, order3)
      expect(orders).to_not include(order2)
    end

    it 'filters past orders if passed as filter options' do
      lister_options = { for_user: yordar_admin, show_past: true }
      orders = Admin::ListOrders.new(options: lister_options).call

      expect(orders).to include(order2)
      expect(orders).to_not include(order1, order3)
    end

    it 'filters the orders according to the passed in from and to dates' do
     lister_options = { for_user: yordar_admin, from_date: order2.delivery_at.beginning_of_day, to_date: order1.delivery_at.end_of_day }
     orders = Admin::ListOrders.new(options: lister_options).call

     expect(orders).to include(order1, order2)
     expect(orders).to_not include(order3)
    end
  end

end