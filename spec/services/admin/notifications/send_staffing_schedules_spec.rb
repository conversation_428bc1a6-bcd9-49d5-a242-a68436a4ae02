require 'rails_helper'

# is highly reliant on Admin::Reports::FetchPantryManagerSpends
RSpec.describe Admin::Notifications::SendStaffingSchedules, type: :service, notifications: true, pantry_managers: true do

  let!(:notification_time) { Time.zone.now.beginning_of_week + 2.days } # Tuesday This week
  let!(:staffing_supplier) { create(:supplier_profile, :random) }
  let!(:adminable_customer) { create(:customer_profile, :random) }

  let!(:pantry_manager1) { create(:customer_profile, :random, :with_user, company_team_admin: true, customer_name: 'pantry_manager1') }
  let!(:access_permission1) { create(:access_permission, admin: pantry_manager1, customer_profile: adminable_customer, scope: 'pantry_manager') }
  let!(:order11) { create(:order, :confirmed, pantry_manager: pantry_manager1, delivery_at: notification_time + 1.week + 1.days, name: 'order1') }
  let!(:order_line11) { create(:order_line, :random, order: order11, supplier_profile: staffing_supplier) }
  let!(:order12) { create(:order, :confirmed, pantry_manager: pantry_manager1, delivery_at: notification_time + 1.week + 3.days, name: 'order1') }
  let!(:order_line12) { create(:order_line, :random, order: order12, supplier_profile: staffing_supplier) }

  let!(:pantry_manager2) { create(:customer_profile, :random, :with_user, company_team_admin: true, customer_name: 'pantry_manager2') }
  let!(:access_permission2) { create(:access_permission, admin: pantry_manager2, customer_profile: adminable_customer, scope: 'pantry_manager') }
  let!(:order21) { create(:order, :confirmed, pantry_manager: pantry_manager2, delivery_at: notification_time + 1.week + 2.days, name: 'order2') }
  let!(:order_line21) { create(:order_line, :random, order: order21, supplier_profile: staffing_supplier) }
  let!(:order22) { create(:order, :confirmed, pantry_manager: pantry_manager2, delivery_at: notification_time + 1.week + 4.days, name: 'order2') }
  let!(:order_line22) { create(:order_line, :random, order: order22, supplier_profile: staffing_supplier) }

  let!(:pantry_manager3) { create(:customer_profile, :random, :with_user, company_team_admin: true, customer_name: 'pantry_manager3') }
  let!(:access_permission3) { create(:access_permission, admin: pantry_manager3, customer_profile: adminable_customer, scope: 'pantry_manager') }
  let!(:order31) { create(:order, :confirmed, pantry_manager: pantry_manager3, delivery_at: notification_time + 1.week + 3.days, name: 'order3') }
  let!(:order_line31) { create(:order_line, :random, order: order31, supplier_profile: staffing_supplier) }
  let!(:order32) { create(:order, :confirmed, pantry_manager: pantry_manager3, delivery_at: notification_time + 1.week + 1.days, name: 'order3') }
  let!(:order_line32) { create(:order_line, :random, order: order32, supplier_profile: staffing_supplier) }

  let!(:email_sender) { double(Admin::Emails::SendStaffingScheduleEmail) }

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :staffing_supplier_id).and_return(staffing_supplier.id)

    # mock email sender    
    allow(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, errors: [], sent_email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)
  end

  it 'sends a notifcation for all pantry managers who have orders in the next week' do
    expect(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).with(pantry_manager: pantry_manager1, orders: [order11, order12], week: anything)
    expect(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).with(pantry_manager: pantry_manager2, orders: [order21, order22], week: anything)
    expect(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).with(pantry_manager: pantry_manager3, orders: [order32, order31], week: anything)

    notifications_sender = Admin::Notifications::SendStaffingSchedules.new(time: notification_time).call

    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_pantry_managers).to include(pantry_manager1, pantry_manager2, pantry_manager3)
  end

  it 'doesn\'t send notification for pantry managers who do not have active orders in next week' do
    non_notifiable_orders = [order12, order21, order22]
    case
    when [true, false].sample
      non_notifiable_orders.each{|order| order.update_column(:status, (Order::VALID_ORDER_STATUSES - Admin::Reports::FetchPantryManagerSpends::VALID_ACTIVE_ORDER_STATUSES).sample) }
    else
      non_notifiable_orders.each{|order| order.update_column(:delivery_at, [notification_time, notification_time - 1.week, notification_time + 2.weeks].sample) }
    end
    expect(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).with(pantry_manager: pantry_manager1, orders: [order11], week: anything)
    expect(Admin::Emails::SendStaffingScheduleEmail).to_not receive(:new).with(pantry_manager: pantry_manager2, orders: anything, week: anything)
    expect(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).with(pantry_manager: pantry_manager3, orders: [order32, order31], week: anything)

    notifications_sender = Admin::Notifications::SendStaffingSchedules.new(time: notification_time).call

    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_pantry_managers).to_not include(pantry_manager2)
    expect(notifications_sender.notified_pantry_managers).to include(pantry_manager1, pantry_manager3)
  end

  it 'send the notifications along with next weeks info' do
    next_week = notification_time + 1.week
    notification_week = {
      from_date: next_week.beginning_of_week,
      to_date: next_week.end_of_week,
    }
    expect(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).with(pantry_manager: anything, orders: anything, week: notification_week)
    expect(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).with(pantry_manager: anything, orders: anything, week: notification_week)
    expect(Admin::Emails::SendStaffingScheduleEmail).to receive(:new).with(pantry_manager: anything, orders: anything, week: notification_week)

    notifications_sender = Admin::Notifications::SendStaffingSchedules.new(time: notification_time).call
    expect(notifications_sender).to be_success
  end

  context 'errors' do
    it 'returns with errors if the Staffing Log (per pantry manager) email fails' do
      unsuccessfull_response = OpenStruct.new(success?: false, errors: ['pantry-manager-email-error'])
      allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

      notifications_sender = Admin::Notifications::SendStaffingSchedules.new(time: notification_time).call
      expect(notifications_sender).to_not be_success
      expect(notifications_sender.errors).to eq(%w[pantry-manager-email-error pantry-manager-email-error pantry-manager-email-error]) # per pantry manager
    end
  end

end