require 'rails_helper'

RSpec.describe Admin::Notifications::BroadcastCount, type: :service, notifications: true, event_logs: true do

  subject { Admin::Notifications::BroadcastCount.new.call }

  let!(:user1) { create(:user) }
  let!(:user2) { create(:user) }

  let!(:event_log1) { create(:event_log, :random) }
  let!(:event_log2) { create(:event_log, :random) }
  let!(:listed_events) { [event_log1, event_log2] }

  let!(:actioncable_server) { double(ActionCable.server) }
  let!(:actioncable_connections) do
    [
      {
        connection_identifier: 'identifier-1',
        current_user: user1
      },
      {
        connection_identifier: 'identifier-2',
        current_user: user2
      },
      {
        connection_identifier: 'identifier-1',
        current_user: user1
      }
    ].map{|connection| OpenStruct.new(connection) }
  end

  let!(:redis_server) { double(Redis) }
  let!(:pubsub_channels) do
    [
      "yordar_staging:my_notifications_#{user1.id}",
      "yordar_staging:my_notifications_#{user2.id}",
      "yordar_staging:action_cable/#{SecureRandom.hex(12)}",
      "_action_cable_internal"
    ]
  end

  before do
    # mock Redis channels
    allow(Redis).to receive(:new).and_return(redis_server)
    allow(redis_server).to receive(:pubsub).and_return(pubsub_channels)

    # mock ActionCable connections
    allow(ActionCable).to receive(:server).and_return(actioncable_server)
    allow(actioncable_server).to receive(:connections).and_return(actioncable_connections)

    # mock ActionCable broadcast
    allow(actioncable_server).to receive(:broadcast).and_return(true)

    # mock notifications lister
    notifications_lister = double(Admin::ListNotifications)
    allow(Admin::ListNotifications).to receive(:new).and_return(notifications_lister)
    allow(notifications_lister).to receive(:call).and_return(listed_events)
  end

  it 'makes request to get all ActionCable server connections', skip: 'in development only' do
    expect(actioncable_server).to receive(:connections)

    subject
  end

  it 'makes a request to get all Redis channels' do
    expect(redis_server).to receive(:pubsub).with('channels')

    subject
  end

  it 'makes a request to list unviewed notifications, per user' do
    expected_lister_options = { unviewed_only: true, favourites_only: false, limit: 1000 }
    expect(Admin::ListNotifications).to receive(:new).with(options: expected_lister_options.merge({ for_user: user1 }))
    expect(Admin::ListNotifications).to receive(:new).with(options: expected_lister_options.merge({ for_user: user2 }))

    subject
  end

  context 'with user favourites' do
    let!(:customer) { create(:customer_profile, :random) }
    let!(:favourite_customer1) { create(:favourite_customer, :random, favouriter: user2, customer_profile: customer) }

    it 'makes a request to list unviewed notifications for favourite customers only, per user' do
      expected_lister_options = { unviewed_only: true, limit: 1000 }
      expect(Admin::ListNotifications).to receive(:new).with(options: expected_lister_options.merge({ for_user: user1, favourites_only: false }))
      expect(Admin::ListNotifications).to receive(:new).with(options: expected_lister_options.merge({ for_user: user2, favourites_only: true }))

      subject
    end
  end

  it 'broadcasts the notifications count to each users channel' do
    expect(actioncable_server).to receive(:broadcast).with("my_notifications_#{user1.id}", { count: listed_events.size })
    expect(actioncable_server).to receive(:broadcast).with("my_notifications_#{user2.id}", { count: listed_events.size })

    notifications_broadcaster = subject
    expect(notifications_broadcaster).to be_success
    expect(notifications_broadcaster.notifed_users.map(&:id)).to include(*[user1, user2].map(&:id))
  end

  it 'only broadcasts to the passed in user\'s channel if present' do
    expect(actioncable_server).to_not receive(:broadcast).with("my_notifications_#{user1.id}", { count: listed_events.size })
    expect(actioncable_server).to receive(:broadcast).with("my_notifications_#{user2.id}", { count: listed_events.size })

    notifications_broadcaster = Admin::Notifications::BroadcastCount.new(user: user2).call
    expect(notifications_broadcaster).to be_success
    expect(notifications_broadcaster.notifed_users.map(&:id)).to include(user2.id)
    expect(notifications_broadcaster.notifed_users.map(&:id)).to_not include(user1.id)
  end

end