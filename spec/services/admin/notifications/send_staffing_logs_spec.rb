require 'rails_helper'

# is highly reliant on Admin::Reports::FetchPantryManagerSpends
RSpec.describe Admin::Notifications::SendStaffingLogs, type: :service, notifications: true, pantry_managers: true do

  let!(:notification_time) { Time.zone.now.beginning_of_week + 2.days } # Tuesday This week
  let!(:staffing_supplier) { create(:supplier_profile, :random) }
  let!(:adminable_customer) { create(:customer_profile, :random) }

  let!(:pantry_manager1) { create(:customer_profile, :random, :with_user, company_team_admin: true, customer_name: 'pantry_manager1') }
  let!(:access_permission1) { create(:access_permission, admin: pantry_manager1, customer_profile: adminable_customer, scope: 'pantry_manager') }
  let!(:order11) { create(:order, :confirmed, pantry_manager: pantry_manager1, delivery_at: notification_time - 1.week + 1.days, name: 'order1') }
  let!(:order_line11) { create(:order_line, :random, order: order11, supplier_profile: staffing_supplier) }
  let!(:order12) { create(:order, :confirmed, pantry_manager: pantry_manager1, delivery_at: notification_time + 3.days, name: 'order1') }
  let!(:order_line12) { create(:order_line, :random, order: order12, supplier_profile: staffing_supplier) }

  let!(:pantry_manager2) { create(:customer_profile, :random, :with_user, company_team_admin: true, customer_name: 'pantry_manager2') }
  let!(:access_permission2) { create(:access_permission, admin: pantry_manager2, customer_profile: adminable_customer, scope: 'pantry_manager') }
  let!(:order21) { create(:order, :confirmed, pantry_manager: pantry_manager2, delivery_at: notification_time - 1.week + 2.days, name: 'order2') }
  let!(:order_line21) { create(:order_line, :random, order: order21, supplier_profile: staffing_supplier) }
  let!(:order22) { create(:order, :confirmed, pantry_manager: pantry_manager2, delivery_at: notification_time + 4.days, name: 'order2') }
  let!(:order_line22) { create(:order_line, :random, order: order22, supplier_profile: staffing_supplier) }

  let!(:pantry_manager3) { create(:customer_profile, :random, :with_user, company_team_admin: true, customer_name: 'pantry_manager3') }
  let!(:access_permission3) { create(:access_permission, admin: pantry_manager3, customer_profile: adminable_customer, scope: 'pantry_manager') }
  let!(:order31) { create(:order, :confirmed, pantry_manager: pantry_manager3, delivery_at: notification_time + 3.days, name: 'order3') }
  let!(:order_line31) { create(:order_line, :random, order: order31, supplier_profile: staffing_supplier) }
  let!(:order32) { create(:order, :confirmed, pantry_manager: pantry_manager3, delivery_at: notification_time - 1.week + 1.days, name: 'order3') }
  let!(:order_line32) { create(:order_line, :random, order: order32, supplier_profile: staffing_supplier) }

  let(:pantry_manager_spend_class) { Admin::Reports::FetchPantryManagerSpends::PantryManager }
  let!(:accounts_email_sender) { double(Admin::Emails::SendStaffingLogAccountsEmail) }
  let!(:email_sender) { double(Admin::Emails::SendStaffingLogEmail) }

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :staffing_supplier_id).and_return(staffing_supplier.id)

    # mock accounts email sensder
    allow(Admin::Emails::SendStaffingLogAccountsEmail).to receive(:new).and_return(accounts_email_sender)
    successfull_response = OpenStruct.new(success?: true, errors: [], sent_email: 'sent-accounts-email')
    allow(accounts_email_sender).to receive(:call).and_return(successfull_response)

    # mock email sender    
    allow(Admin::Emails::SendStaffingLogEmail).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, errors: [], sent_email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)
  end

  it 'sends a notification to account team with the staffing spends' do
    expect(Admin::Emails::SendStaffingLogAccountsEmail).to receive(:new).with(staffing_spends: 3.times.map{|_| instance_of(pantry_manager_spend_class) }, fortnight: anything)

    notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time).call

    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_accounts_team).to be_truthy
  end

  it 'sends a notifcation for each pantry manager who have staffing orders in current forntnight' do
    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: pantry_manager1, orders: [order11, order12], fortnight: anything)
    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: pantry_manager2, orders: [order21, order22], fortnight: anything)
    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: pantry_manager3, orders: [order32, order31], fortnight: anything)

    notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time).call

    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_pantry_managers).to include(pantry_manager1, pantry_manager2, pantry_manager3)
  end

  it 'only sends the email to accounts if type is passed in as accounts' do
    expect(Admin::Emails::SendStaffingLogAccountsEmail).to receive(:new).with(staffing_spends: 3.times.map{|_| instance_of(pantry_manager_spend_class) }, fortnight: anything)

    expect(Admin::Emails::SendStaffingLogEmail).to_not receive(:new)

    notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time, type: 'accounts').call
    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_accounts_team).to be_truthy
    expect(notifications_sender.notified_pantry_managers).to be_blank
  end

  it 'only sends the email to pantry_managers if type is passed in as pantry-managers' do
    expect(Admin::Emails::SendStaffingLogAccountsEmail).to_not receive(:new)

    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: pantry_manager1, orders: [order11, order12], fortnight: anything)
    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: pantry_manager2, orders: [order21, order22], fortnight: anything)
    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: pantry_manager3, orders: [order32, order31], fortnight: anything)

    notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time, type: 'pantry-managers').call
    expect(notifications_sender).to be_success
    expect(notifications_sender.notified_accounts_team).to be_falsey
    expect(notifications_sender.notified_pantry_managers).to include(pantry_manager1, pantry_manager2, pantry_manager3)
  end

  context 'with in-active orders' do
    before do
      non_notifiable_orders = [order12, order21, order22]
      case
      when [true, false].sample
        non_notifiable_orders.each{|order| order.update_column(:status, (Order::VALID_ORDER_STATUSES - Admin::Reports::FetchPantryManagerSpends::VALID_ACTIVE_ORDER_STATUSES).sample) }
      else
        non_notifiable_orders.each{|order| order.update_column(:delivery_at, [notification_time - 2.weeks, notification_time + 1.weeks].sample) }
      end
    end

    it 'sends a notification to account team with the active staffing spends for the current forntnight' do
      expect(Admin::Emails::SendStaffingLogAccountsEmail).to receive(:new).with(staffing_spends: 2.times.map{|_| instance_of(pantry_manager_spend_class) }, fortnight: anything) # pantry_manager1 and pantry_manager3

      notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.notified_accounts_team).to be_truthy
    end

    it 'doesn\'t send notification for pantry managers who do not have active orders in current forntnight' do    
      expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: pantry_manager1, orders: [order11], fortnight: anything)
      expect(Admin::Emails::SendStaffingLogEmail).to_not receive(:new).with(pantry_manager: pantry_manager2, orders: anything, fortnight: anything)
      expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: pantry_manager3, orders: [order32, order31], fortnight: anything)

      notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time).call

      expect(notifications_sender).to be_success
      expect(notifications_sender.notified_pantry_managers).to_not include(pantry_manager2)
      expect(notifications_sender.notified_pantry_managers).to include(pantry_manager1, pantry_manager3)
    end
  end

  it 'send the notifications along with current fortnight info' do
    last_week = notification_time - 1.week
    notification_fortnight = {
      from_date: last_week.beginning_of_week,
      to_date: notification_time.end_of_week,
    }

    expect(Admin::Emails::SendStaffingLogAccountsEmail).to receive(:new).with(staffing_spends: anything, fortnight: notification_fortnight)

    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: anything, orders: anything, fortnight: notification_fortnight)
    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: anything, orders: anything, fortnight: notification_fortnight)
    expect(Admin::Emails::SendStaffingLogEmail).to receive(:new).with(pantry_manager: anything, orders: anything, fortnight: notification_fortnight)

    notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time).call
    expect(notifications_sender).to be_success
  end

  context 'errors' do
    it 'returns with errors if the Staffing Log Accounts email fails' do
      unsuccessfull_response = OpenStruct.new(success?: false, errors: ['accounts-email-error'])
      allow(accounts_email_sender).to receive(:call).and_return(unsuccessfull_response)

      notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time).call
      expect(notifications_sender).to_not be_success
      expect(notifications_sender.errors).to eq(['accounts-email-error'])
    end

    it 'returns with errors if the Staffing Log (per pantry manager) email fails' do
      unsuccessfull_response = OpenStruct.new(success?: false, errors: ['pantry-manager-email-error'])
      allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

      notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: notification_time).call
      expect(notifications_sender).to_not be_success
      expect(notifications_sender.errors).to eq(%w[pantry-manager-email-error pantry-manager-email-error pantry-manager-email-error]) # per pantry manager
    end
  end

end