require 'rails_helper'

RSpec.describe Admin::DeprecateUser, type: :service, admin: true, users: true do

  let!(:super_admin) { create(:user, :confirmed, super_admin: true) }
  let!(:user) { create(:user, :confirmed) }

  it 'adds depracated to the user email address' do
    expect(user.email).to_not include('@deprecated-')
    user_deprecater = Admin::DeprecateUser.new(user: user, admin: super_admin).call

    expect(user_deprecater).to be_success

    deprecated_user = user_deprecater.user
    expect(deprecated_user.id).to eq(user.id)
    expect(deprecated_user.email).to include('@deprecated-')
    expect(user.email).to include('@deprecated-')

    expect(user.unconfirmed_email).to be_blank
  end

  it 'adds depracated to the user\'s last name' do
    user_deprecater = Admin::DeprecateUser.new(user: user, admin: super_admin).call

    expect(user_deprecater).to be_success

    deprecated_user = user_deprecater.user
    expect(deprecated_user.lastname).to include('-deprecated')
  end

  it 'deactivates the passed in user' do
    user_deprecater = Admin::DeprecateUser.new(user: user, admin: super_admin).call

    expect(user_deprecater).to be_success

    deprecated_user = user_deprecater.user
    expect(deprecated_user).to_not be_is_active
  end

  context 'errors' do
    it 'fails without a passed in user' do
      user_deprecater = Admin::DeprecateUser.new(user: nil, admin: super_admin).call

      expect(user_deprecater).to_not be_success
      expect(user_deprecater.errors).to include('Cannot deprecate a missing user')
    end

    it 'only super admins can deprecate a user' do
      non_super_admin = create(:user, :confirmed, super_admin: false, admin: [true, false].sample)
      user_deprecater = Admin::DeprecateUser.new(user: user, admin: non_super_admin).call

      expect(user_deprecater).to_not be_success
      expect(user_deprecater.errors).to include('Only super admins can deprecate users')
    end

    it 'cannot deprecate an already deprecated user' do
      user.update_columns(email: user.email.sub('@', '@deprecated-'), lastname: "#{user.lastname || ''}-deprecated", is_active: false)
      user_deprecater = Admin::DeprecateUser.new(user: user, admin: super_admin).call

      expect(user_deprecater).to_not be_success
      expect(user_deprecater.errors).to include('User is already deprecated')
    end
  end

end
