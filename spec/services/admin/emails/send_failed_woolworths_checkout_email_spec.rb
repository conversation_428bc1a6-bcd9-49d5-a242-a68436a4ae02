require 'rails_helper'

RSpec.describe Admin::Emails::SendFailedWoolworthsCheckoutEmail, type: :service, emails: true, admin: true, orders: true, woolworths: true do
  include Rails.application.routes.url_helpers
  include ActionView::Helpers::NumberHelper

  subject { Admin::Emails::SendFailedWoolworthsCheckoutEmail.new(order: order).call }


  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:order) { create(:order, :new, customer_profile: customer) }

  let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :developer_email).and_return('developer-email')
  end

  it 'returns the sent email' do
    sent_email = subject

    expect(sent_email).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Admin::Emails::SendFailedWoolworthsCheckoutEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything,
      )

    subject
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "YORDAR: FAILED Woolworths Checkout - ##{order.id}",
        cc: anything,
        email_options: anything,
        email_variables: anything
      )

    subject
  end

  it 'sends the email to the correct recipients (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: 'orders-email',
      subject: anything,
      cc: 'developer-email',
      email_options: anything,
      email_variables: anything
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{Admin::Emails::SendFailedWoolworthsCheckoutEmail::EMAIL_TEMPLATE}-#{order.id}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: order.id, ref: email_ref },
        email_variables: anything
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct order data' do
      expected_order_data = {
      id: order.id,
      name: order.name,
      customer_name: customer.name,
      delivery_at: order.delivery_at.to_s(:full_verbose),
      total: number_to_currency(order.customer_total),
      order_line_count: order.order_lines.count,
      address: order.delivery_address_arr.join(', '),
      account_name: woolworths_order.account.short_name,
      delivery_window_text: woolworths_order.delivery_window_text,
      link: order_show_url(order, host: yordar_credentials(:default_host)),
    }

      expected_email_variables = {
        order: deep_struct(expected_order_data),        
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end
  end

  context 'errors' do
    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Admin::Emails::SendFailedWoolworthsCheckoutEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        failed_order_email_sender = Admin::Emails::SendFailedWoolworthsCheckoutEmail.new(order: order)

        expected_error_message = "Failed to send failed Woolworths Order Checkout notification to admin - #{order.id}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(failed_order_email_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

        failed_order_email_sender.call
      end
    end # email sender error
  end

end