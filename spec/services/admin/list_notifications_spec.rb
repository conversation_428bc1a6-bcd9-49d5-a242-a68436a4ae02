require 'rails_helper'

RSpec.describe Admin::ListNotifications, type: :service, admin: true, users: true, notifications: true, event_logs: true do

  let!(:customer1) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
  let!(:customer2) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
  let!(:customer3) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }

  let(:super_admin) { create(:user, :random, super_admin: true, admin: false) }
  let(:admin) { create(:user, :random, super_admin: false, admin: true) }

  let(:yordar_admin) { [super_admin, admin].sample }
  let(:non_admin_user) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: false) }

  let!(:notification1) { create(:event_log, event: 'order-canceled-permanently', scopable: customer1) }
  let!(:notification2) { create(:event_log, event: 'order-rejected', scopable: customer2) }
  let!(:notification3) { create(:event_log, event: 'woolworths-checkout-failed', scopable: customer3) }
  let!(:all_notifications) { [notification1, notification2, notification3] }

  context 'Event & User Based filtering' do
    it 'does not lists any notifications for missing user' do
      lister_options = { for_user: nil }
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to be_blank
    end

    context 'as a super admin' do
      let!(:super_admin_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:super_admin].sample(2) }
      let!(:non_super_admin_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:super_admin]).sample }

      before do
        notification1.update_column(:event, super_admin_events[0])
        notification2.update_column(:event, non_super_admin_event)
        notification3.update_column(:event, super_admin_events[1])
      end

      it 'can only list allowed events' do
        lister_options = { for_user: super_admin }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification3)
        expect(notifications).to_not include(notification2)
      end

      it 'can list all events - if show_all_events is passed' do
        lister_options = { for_user: super_admin, show_all_events: true }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification2, notification3)
      end
    end

    context 'as a Yordar/Orders admin' do
      let!(:yordar_admin_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:yordar_admin].sample(2) }
      let!(:non_yordar_admin_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:yordar_admin]).sample }

      before do
        notification1.update_column(:event, non_yordar_admin_event)
        notification2.update_column(:event, yordar_admin_events[0])
        notification3.update_column(:event, yordar_admin_events[1])
      end

      it 'can only list allowed events' do
        lister_options = { for_user: admin }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification2, notification3)
        expect(notifications).to_not include(notification1)
      end

      it 'still only lists allowed events - even if show_all_events is passed' do
        lister_options = { for_user: admin, show_all_events: true }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification2, notification3)
        expect(notifications).to_not include(notification1)
      end
    end

    context 'as an Account Manager' do # admin user with account manager permissions
      let(:account_manager_user) { admin }
      let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

      let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer2, scope: 'account_manager') }
      let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3, scope: 'account_manager') }

      let!(:account_manager_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:account_manager].sample(2) }
      let!(:non_account_manager_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:account_manager]).sample }

      before do
        customer_team_admin.profile.update_column(:user_id, account_manager_user.id) # attach customer team admin customer profile to user
        account_manager_user.reload

        notification1.update_column(:event, account_manager_events[0])
        notification2.update_column(:event, account_manager_events[1])
        notification3.update_column(:event, non_account_manager_event)
      end

      it 'can only list allowed events' do
        lister_options = { for_user: account_manager_user }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification2)
        expect(notifications).to_not include(notification3)
      end

      it 'still only lists allowed events - even if show_all_events is passed' do
        lister_options = { for_user: account_manager_user, show_all_events: true }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification2)
        expect(notifications).to_not include(notification3)
      end
    end

    context 'as a Pantry Manager' do # non-admin user with pantry manager permissions
      let(:pantry_manager_user) { non_admin_user }
      let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

      let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer1, scope: 'pantry_manager') }
      let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer2, scope: 'pantry_manager') }
      let!(:admin_access_permission3) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3, scope: 'pantry_manager') }

      let!(:pantry_manager_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:pantry_manager].sample(2) }
      let!(:non_pantry_manager_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:pantry_manager]).sample }

      before do
        customer_team_admin.profile.update_column(:user_id, pantry_manager_user.id) # attach customer team admin customer profile to user
        pantry_manager_user.reload

        notification1.update_column(:event, pantry_manager_events[0])
        notification2.update_column(:event, non_pantry_manager_event)
        notification3.update_column(:event, pantry_manager_events[1])
      end

      it 'can only list allowed events' do
        lister_options = { for_user: pantry_manager_user }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification3)
        expect(notifications).to_not include(notification2)
      end

      it 'still only lists allowed events - even if show_all_events is passed' do
        lister_options = { for_user: pantry_manager_user, show_all_events: true }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification3)
        expect(notifications).to_not include(notification2)
      end
    end

    context 'as a Company Team Admin' do # non-admin user with full_access or blank scope permissions
      let(:company_team_admin_user) { non_admin_user }
      let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

      let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer1, scope: [nil, 'full_access', 'company_team_admin'].sample) }
      let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer2, scope: [nil, 'full_access', 'company_team_admin'].sample) }
      let!(:admin_access_permission3) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3, scope: [nil, 'full_access', 'company_team_admin'].sample) }

      let!(:customer_team_admin_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:customer_team_admin].sample(2) }
      let!(:non_customer_team_admin_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:customer_team_admin]).sample }

      before do
        customer_team_admin.profile.update_column(:user_id, company_team_admin_user.id) # attach customer team admin customer profile to user
        company_team_admin_user.reload

        notification1.update_column(:event, customer_team_admin_events[0])
        notification2.update_column(:event, non_customer_team_admin_event)
        notification3.update_column(:event, customer_team_admin_events[1])
      end

      it 'can only list allowed events' do
        lister_options = { for_user: company_team_admin_user }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification3)
        expect(notifications).to_not include(notification2)
      end

      it 'still only lists allowed events - even if show_all_events is passed' do
        lister_options = { for_user: company_team_admin_user, show_all_events: true }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification3)
        expect(notifications).to_not include(notification2)
      end
    end

    context 'as a Supplier Admin' do # user who can_access_suppliers
      let(:supplier_admin_user) { create(:user, :random, super_admin: false, admin: false, can_access_suppliers: true) }

      let!(:supplier_admin_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:supplier_admin].sample(2) }
      let!(:non_supplier_admin_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:supplier_admin]).sample }

      before do
        notification1.update_column(:event, non_supplier_admin_event)
        notification2.update_column(:event, supplier_admin_events[0])
        notification3.update_column(:event, supplier_admin_events[1])
      end

      it 'can only list allowed events' do
        lister_options = { for_user: supplier_admin_user }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification2, notification3)
        expect(notifications).to_not include(notification1)
      end

      it 'still only lists allowed events - even if show_all_events is passed' do
        lister_options = { for_user: supplier_admin_user, show_all_events: true }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification2, notification3)
        expect(notifications).to_not include(notification1)
      end
    end
  end

  context 'Scopable based filtering' do
    it 'does not lists any notifications for missing user' do
      lister_options = { for_user: nil }
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to be_blank
    end

    it 'Super admins, Yordar Admins can list notifications for all customers' do
      lister_options = { for_user: super_admin }
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to include(notification1, notification2, notification3)
    end

    it 'Non admin users cannot list any notifications' do
      lister_options = { for_user: non_admin_user }
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to be_blank
    end

    context 'as Pantry Manager or Customer Team Admin' do
      let!(:is_pantry_manager) { [true, false].sample }
      let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

      let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer2, scope: (is_pantry_manager ? 'pantry_manager' : 'full_access')) }
      let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3, scope: (is_pantry_manager ? 'pantry_manager' : 'full_access')) }

      before do
        customer_team_admin.profile.update_column(:user_id, non_admin_user.id) # attach non-admin-user record to company team admin profile
        non_admin_user.reload

        all_notifications.each do |notification|
          user_kind = is_pantry_manager ? :pantry_manager : :customer_team_admin
          notification.update_column(:event, EventLogs::FilterByUser::EVENT_BASED_ACCESS[user_kind].sample)
        end
      end

      it 'only lists notifications of customers the team admin has active access permission to' do
        lister_options = { for_user: non_admin_user }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification2, notification3)
        expect(notifications).to_not include(notification1)
      end

      it 'does not list notifications of customers with non-active access permissions' do
        admin_access_permission1.update_column(:active, false)
        lister_options = { for_user: non_admin_user }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification3)
        expect(notifications).to_not include(notification1, notification2)
      end

      it 'also lists its own notifications' do
        customer_admin_notification = create(:event_log, event: 'new-order-submitted', scopable: customer_team_admin)

        lister_options = { for_user: non_admin_user }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(customer_admin_notification)
        # expect(notifications).to include(notification2, notification3)
        # expect(notifications).to_not include(notification1)
      end

      context 'with customer favourites' do
        let!(:favourite_customer2) { create(:favourite_customer, :random, favouriter: non_admin_user, customer_profile: customer2) }

        it 'only lists the notifications for the favourited customers' do
          lister_options = { for_user: non_admin_user, favourites_only: true }
          notifications = Admin::ListNotifications.new(options: lister_options).call

          expect(notifications).to include(notification2)
          expect(notifications).to_not include(notification1, notification3)
        end
      end # with favoutire notification customers (more like prioritized customers)
    end # As Company Team Admin
  end # Access based filtering

  context 'filter by favourites' do
    let!(:favourite_customer1) { create(:favourite_customer, :random, favouriter: super_admin, customer_profile: customer1) }
    let!(:favourite_customer3) { create(:favourite_customer, :random, favouriter: super_admin, customer_profile: customer3) }

    it 'filters notifications of favourited customers only' do
      lister_options = { for_user: super_admin.reload, show_all_events: true, favourites_only: true }
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to include(notification1, notification3)
      expect(notifications).to_not include(notification2)
    end

    context 'with non-scoped notifications' do
      let!(:notification4) { create(:event_log, event: 'new-customer-registration', scopable: nil) }
      let!(:notification5) { create(:event_log, event: 'new-supplier-registration', scopable: nil) }
      let!(:notification6) { create(:event_log, event: 'new-quote-submitted', scopable: nil) }

      it 'filters notifications of favourited customers and non-scoped events' do
        lister_options = { for_user: super_admin.reload, show_all_events: true, favourites_only: true }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification3, notification4, notification5, notification6)
        expect(notifications).to_not include(notification2)
      end

      it 'filters notifications of all customer and non-scoped events with no favourites' do
        [favourite_customer1, favourite_customer3].each(&:destroy)
        lister_options = { for_user: super_admin.reload, favourites_only: true }
        notifications = Admin::ListNotifications.new(options: lister_options).call

        expect(notifications).to include(notification1, notification2, notification3, notification4, notification5, notification6)
      end
    end
  end

  context 'filter by views' do
    let!(:view1) { create(:event_log_view, event_log: notification1, user: super_admin) }
    let!(:view2) { create(:event_log_view, event_log: notification1, user: admin) }
    let!(:view3) { create(:event_log_view, event_log: notification3, user: admin) }

    it 'filters notifications not viewed by the passed in user' do
      lister_options = { for_user: super_admin.reload, show_all_events: true, unviewed_only: true }
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to include(notification2, notification3)
      expect(notifications).to_not include(notification1)
    end
  end

  context 'filter by severity' do
    let!(:filterable_severity) { EventLog::VALID_SEVERITIES.sample }
    before do
      [notification1, notification3].each do |notification|
        notification.update_column(:severity, filterable_severity)
      end
      notification2.update_column(:severity, (EventLog::VALID_SEVERITIES - [filterable_severity]).sample)
    end
    it 'filters notifications by severity' do
      lister_options = { for_user: super_admin.reload, show_all_events: true, severity: filterable_severity }
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to include(notification1, notification3)
      expect(notifications).to_not include(notification2)
    end
  end

  context 'filter by specific event type' do
    let!(:specific_notification) { all_notifications.sample }
    let!(:event_type) { specific_notification.event }

    it 'filters notifications of passed in event type' do
      lister_options = { for_user: super_admin, show_all_events: true, event_type: event_type }
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to include(specific_notification)
      expect(notifications).to_not include(*(all_notifications - [specific_notification]))
    end
  end

  context 'filter by query (customer specific)' do
    it 'filters notifications for the customer with name/company_name passed as query' do
      lister_options = { for_user: super_admin, show_all_events: true, query: [customer2.customer_name, customer2.company_name].sample}
      notifications = Admin::ListNotifications.new(options: lister_options).call

      expect(notifications).to include(notification2)
      expect(notifications).to_not include(notification1, notification3)
    end
  end

end
