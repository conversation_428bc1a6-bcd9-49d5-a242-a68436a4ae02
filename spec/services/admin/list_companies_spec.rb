require 'rails_helper'

RSpec.describe Admin::ListCompanies, type: :service, admin: true, users: true, companies: true do
  
  let!(:customer1) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name) }
  let!(:company1) { create(:company, :random, customer_profiles: [customer1]) }

  let!(:customer2) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name) }
  let!(:company2) { create(:company, :random, customer_profiles: [customer2]) }

  let!(:customer3) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name) }
  let!(:company3) { create(:company, :random, customer_profiles: [customer3]) }

  let(:super_admin) { create(:user, :random, super_admin: true, admin: false, allow_all_customer_access: false) }
  let(:admin) { create(:user, :random, super_admin: false, admin: true, allow_all_customer_access: false) }
  let(:all_customer_admin) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: true) }

  let(:yordar_admin) { [super_admin, admin].sample }
  let(:non_admin_user) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: false) }

  context 'Access based filtering' do
    it 'does not lists any companies for missing user' do
      lister_options =  { for_user: nil, query: [true, false].sample ? company1.name : '' }
      companies = Admin::ListCompanies.new(options: lister_options).call

      expect(companies).to be_blank
    end

    it 'Super admins, Yordar Admins can list all companies' do
      lister_options = { for_user: yordar_admin }
      companies = Admin::ListCompanies.new(options: lister_options).call

      expect(companies).to include(company1, company2, company3)
    end

    it 'User who can access all customers only list companies of active customers' do
      customer2.user.update_column(:is_active, false)

      lister_options = { for_user: all_customer_admin }
      companies = Admin::ListCompanies.new(options: lister_options).call

      expect(companies).to include(company1, company3)
      expect(companies).to_not include(company2)
    end

    it 'Non admin users cannot list any companies' do
      lister_options = { for_user: non_admin_user }
      companies = Admin::ListCompanies.new(options: lister_options).call

      expect(companies).to be_blank
    end

    context 'as Customer Team Admin' do
      let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

      let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer2) }
      let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3) }

      before do
        customer_team_admin.profile.update_column(:user_id, non_admin_user.id) # attach non-admin-user record to company team admin profile
        non_admin_user.reload
      end

      it 'only lists companies of customers the team admin has active access permission to' do
        lister_options = { for_user: non_admin_user }
        companies = Admin::ListCompanies.new(options: lister_options).call

        expect(companies).to include(company2, company3)
        expect(companies).to_not include(company1)
      end

      it 'does not list companies of customers with non-active access permissions' do
        admin_access_permission1.update_column(:active, false)
        lister_options = { for_user: non_admin_user }
        companies = Admin::ListCompanies.new(options: lister_options).call

        expect(companies).to include(company3)
        expect(companies).to_not include(company1, company2)
      end

      it 'also lists its own company' do
        customer_admin_company = create(:company, :random, customer_profiles: [customer_team_admin])

        lister_options = { for_user: non_admin_user }
        companies = Admin::ListCompanies.new(options: lister_options).call

        expect(companies).to include(customer_admin_company)
        # expect(companies).to include(company2, company3)
        # expect(companies).to_not include(company1)
      end

      context 'with customer favourites' do
        let!(:favourite_customer2) { create(:favourite_customer, :random, favouriter: non_admin_user, customer_profile: customer2) }

        it 'only lists the invoices for the favourited customers' do
          lister_options = { for_user: non_admin_user, favourites_only: true }
          companies = Admin::ListCompanies.new(options: lister_options).call

          expect(companies).to include(company2)
          expect(companies).to_not include(company1, company3)
        end
      end # with favoutire company customers (more like prioritized customers)
    end # as Company Team Admin
  end # Filter by Access 

  context 'filter by query' do
    it 'filters companies by their name' do
      lister_options = { for_user: yordar_admin, query: company3.name }
      companies = Admin::ListCompanies.new(options: lister_options).call

      expect(companies).to include(company3)
      expect(companies).to_not include(company1, company2)
    end

    it 'filters companies by their customer\'s name' do
      lister_options = { for_user: yordar_admin, query: customer2.customer_name }
      companies = Admin::ListCompanies.new(options: lister_options).call
      
      expect(companies).to include(company2)
      expect(companies).to_not include(company1, company3)
    end
  end

  context 'filter by Company' do
    it 'returns the passed in company if accessible' do
      lister_options = { for_user: yordar_admin, company: company2 }
      companies = Admin::ListCompanies.new(options: lister_options).call

      expect(companies).to include(company2)
      expect(companies).to_not include(company1, company3)
    end

    it 'returns an empty list for non-company object' do
      supplier = create(:supplier_profile, :random, :with_user)
      lister_options = { for_user: yordar_admin, company: supplier }
      companies = Admin::ListCompanies.new(options: lister_options).call

      expect(companies).to be_empty
    end
  end

  context 'filter by favourites' do
    let!(:favourite_customer1) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer1) }
    let!(:favourite_customer3) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer3) }

    it 'filters companies of favourited customers only' do
      lister_options = { for_user: yordar_admin.reload, favourites_only: true }
      companies = Admin::ListCompanies.new(options: lister_options).call

      expect(companies).to include(company1, company3)
      expect(companies).to_not include(company2)
    end
  end

  it 'paginates companies according to page and limit (default sort of ID)' do
    lister_options = { for_user: yordar_admin, page: 2, limit: 1 }
    companies = Admin::ListCompanies.new(options: lister_options).call

    expect(companies).to include(company2)
    expect(companies).to_not include(company1, company3)
  end

end