require 'rails_helper'

RSpec.describe Admin::ListAdmins, type: :service, admin: true, users: true do

  let!(:user1) { create(:user, :random) }
  let!(:user2) { create(:user, :random) }

  let!(:customer1) { create(:customer_profile, :random, :with_user, company_team_admin: true) }
  let!(:customer2) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

  before do
    # reload to map customers to their respective user
    [customer1, customer2].each(&:reload)
  end

  it 'returns blank if not kind is passed' do
    lister_options = [true, false].sample ? { kind: [nil, '', 'invalid-kind'].sample } : {}

    listed_admins = Admin::ListAdmins.new(options: lister_options).call
    expect(listed_admins).to be_blank
  end

  context 'Yordar Admins' do
    let!(:super_admin) { create(:user, super_admin: true, admin: false, can_access_suppliers: false, firstname: '<PERSON><PERSON>', lastname: '<PERSON><PERSON>')}
    let!(:yordar_admin) { create(:user, super_admin: true, admin: false, can_access_suppliers: false, firstname: 'CCC', lastname: 'DDD') }
    let!(:supplier_admin) { create(:user, super_admin: false, admin: false, can_access_suppliers: true, firstname: 'AAA', lastname: 'FFF' ) }

    let!(:lister_options) do
      { kind: 'yordar_admin' }
    end

    it 'returns all admins/super admins or supplier admins' do
      listed_admins = Admin::ListAdmins.new(options: lister_options).call

      expect(listed_admins).to include(super_admin, yordar_admin, supplier_admin)
      expect(listed_admins).to_not include(user1, user2, customer1.user, customer2.user)
    end

    it 'only lists active users by default' do
      yordar_admin.update_column(:is_active, false)
      listed_admins = Admin::ListAdmins.new(options: lister_options).call

      expect(listed_admins).to include(super_admin, supplier_admin)
      expect(listed_admins).to_not include(yordar_admin, user1, user2, customer1.user, customer2.user)
    end

    context 'filter by query' do
      it 'filters users by the name' do
        lister_options_with_query = lister_options.merge({ query: yordar_admin.firstname })
        listed_admins = Admin::ListAdmins.new(options: lister_options_with_query).call

        expect(listed_admins).to include(yordar_admin)
        expect(listed_admins).to_not include(super_admin, supplier_admin, user1, user2, customer1.user, customer2.user)
      end

      it 'filters users by the email' do
        lister_options_with_query = lister_options.merge({ query: supplier_admin.email })
        listed_admins = Admin::ListAdmins.new(options: lister_options_with_query).call

        expect(listed_admins).to include(supplier_admin)
        expect(listed_admins).to_not include(super_admin, yordar_admin, user1, user2, customer1.user, customer2.user)
      end
    end

    it 'sorts the result by name (firstname and lastname)' do
      listed_admins = Admin::ListAdmins.new(options: lister_options).call

      expect(listed_admins[0]).to eq(supplier_admin)
      expect(listed_admins[1]).to eq(super_admin)
      expect(listed_admins[2]).to eq(yordar_admin)        
    end

    it 'paginates the result' do
      paginated_lister_options = lister_options.merge({ page: 2, limit: 1 })
      listed_admins = Admin::ListAdmins.new(options: paginated_lister_options).call

      expect(listed_admins.size).to eq(1)
      expect(listed_admins[0]).to eq(super_admin)
    end
  end

  context 'Team Admins' do
    let!(:customer1) { create(:customer_profile, :random, :with_user, team_admin: true) }
    let!(:customer2) { create(:customer_profile, :random, :with_user, team_admin: false) }

    let!(:lister_options) do
      { kind: 'team_admin' }
    end

    it 'returns all users with with customer accounts with team admin set to true' do
      listed_admins = Admin::ListAdmins.new(options: lister_options).call

      expect(listed_admins).to include(customer1.user)
      expect(listed_admins).to_not include(customer2.user, user1, user2)
    end
  end

  context 'Customer Acesss Permission Based' do
    ['account_manager', 'pantry_manager', 'company_team_admin'].each do |manager_kind|
      context "#{manager_kind.humanize.titleize.pluralize} (kind = #{manager_kind})" do
        let!(:access_permission1) { create(:access_permission, :random, admin: customer1, scope: manager_kind) }
        let!(:access_permission2) { create(:access_permission, :random, admin: customer2, scope: manager_kind) }

        let!(:lister_options) do
          { kind: manager_kind }
        end

        it "returns all users with customer accounts with company team admin access permissions with scope of #{manager_kind}" do
          listed_admins = Admin::ListAdmins.new(options: lister_options).call

          expect(listed_admins).to include(customer1.user, customer2.user)
          expect(listed_admins).to_not include(user1, user2)
        end

        it "only returns users with customer accounts with `active` admin accesss permissions" do
          access_permission1.update_column(:active, false)
          listed_admins = Admin::ListAdmins.new(options: lister_options).call

          expect(listed_admins).to include(customer2.user)
          expect(listed_admins).to_not include(customer1.user, user1, user2)
        end

        it "only returns users with customer accounts tagged as company team admins" do
          customer2.update_column(:company_team_admin, false)
          listed_admins = Admin::ListAdmins.new(options: lister_options).call

          expect(listed_admins).to include(customer1.user)
          expect(listed_admins).to_not include(customer2.user, user1, user2)
        end

        if manager_kind == 'company_team_admin'

          it "also returns users with customer accounts with admin accesss permissions with scope blank, nil or full access" do
            access_permission1.update_column(:scope, [nil, '', 'full_access'].sample)
            access_permission2.update_column(:scope, ['account_manager', 'pantry_manager'].sample)
            listed_admins = Admin::ListAdmins.new(options: lister_options).call

            expect(listed_admins).to include(customer1.user)
            expect(listed_admins).to_not include(customer2.user, user1, user2)
          end

        else

          it "only returns users with customer accounts with admin accesss permissions with scope #{manager_kind}" do
            access_permission2.update_column(:scope, (AccessPermission::VALID_SCOPES - [manager_kind]).sample)
            listed_admins = Admin::ListAdmins.new(options: lister_options).call

            expect(listed_admins).to include(customer1.user)
            expect(listed_admins).to_not include(customer2.user, user1, user2)
          end

        end # Company Team Admin variation
      end # Manager Kind Context
    end # Manager Kind Loop
  end# Customer Acesss Permission Based

end
