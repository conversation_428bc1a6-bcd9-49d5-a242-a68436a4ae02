require 'rails_helper'

RSpec.describe Admin::Reports::FetchPantryManagerSpends, type: :service, reports: true, pantry_managers: true do

  let!(:now) { Time.zone.now.beginning_of_week + 2.days + 10.hours } # Tuesday 10am
  let(:non_staffing_supplier) { create(:supplier_profile, :random) }
  let!(:staffing_supplier) { create(:supplier_profile, :random) }
  let!(:hourly_item) { create(:menu_item, :random, supplier_profile: staffing_supplier, name: '<PERSON>rda<PERSON> Pantry Manager') }
  let!(:weekly_item) { create(:menu_item, :random, supplier_profile: staffing_supplier, name: 'Yordar Weekly Manager') }
  let!(:half_hourly_item) { create(:menu_item, :random, supplier_profile: staffing_supplier, name: 'Yordar Pantry Manager - 30 min') }

  let!(:pantry_manager1) { create(:customer_profile, :random, :with_user, company_team_admin: true) }
  let!(:pantry_manager2) { create(:customer_profile, :random, :with_user, company_team_admin: true) }
  let!(:pantry_manager3) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:access_permission1) { create(:access_permission, :random, admin: pantry_manager1, customer_profile: customer1, scope: 'pantry_manager') }
  let!(:order1) { create(:order, :confirmed, customer_profile: customer1, pantry_manager: pantry_manager1, delivery_at: now + 1.day, name: 'order1') }
  let!(:order_line1) { create(:order_line, :random, order: order1, supplier_profile: staffing_supplier, menu_item: hourly_item, quantity: 4) } # 4x 1hr

  let!(:customer2) { create(:customer_profile, :random) }
  let!(:access_permission2) { create(:access_permission, :random, admin: pantry_manager2, customer_profile: customer2, scope: 'pantry_manager') }
  let!(:order2) { create(:order, :confirmed, customer_profile: customer2, pantry_manager: pantry_manager2, delivery_at: now + 2.days, name: 'order2') }
  let!(:order_line2) { create(:order_line, :random, order: order2, supplier_profile: staffing_supplier, menu_item: weekly_item, quantity: 1) } # 1x 37.5hrs

  let!(:order3) { create(:order, :confirmed, customer_profile: customer2, pantry_manager: pantry_manager1, delivery_at: now + 3.days, name: 'order3') }
  let!(:order_line31) { create(:order_line, :random, order: order3, supplier_profile: staffing_supplier, menu_item: hourly_item, quantity: 5) } # 5x 1hr
  let!(:order_line32) { create(:order_line, :random, order: order3, supplier_profile: staffing_supplier, menu_item: half_hourly_item, quantity: 1) } # 1x 0.5hrs

  let!(:customer3) { create(:customer_profile, :random) }
  let!(:access_permission3) { create(:access_permission, :random, admin: pantry_manager3, customer_profile: customer3, scope: 'pantry_manager') }

  let!(:report_options) do
    {
      from_date: now.beginning_of_week,
      to_date: now.end_of_week,
    }
  end

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :staffing_supplier_id).and_return(staffing_supplier.id)
  end

  it 'returns all pantry managers irresepective of assigned orders' do
    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    expect(pantry_manager_spends.size).to eq(3)
    expect(pantry_manager_spends.sample).to be_a(Admin::Reports::FetchPantryManagerSpends::PantryManager)
    expect(pantry_manager_spends.map(&:manager)).to include(pantry_manager1, pantry_manager2, pantry_manager3)
  end

  it 'returns pantry managers with staffing orders they are assigned to' do
    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager1 }
    expect(pantry_manager1_spends).to be_present
    expect(pantry_manager1_spends.orders).to include(order1, order3)
    expect(pantry_manager1_spends.orders).to_not include(order2)

    pantry_manager2_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager2 }
    expect(pantry_manager2_spends).to be_present
    expect(pantry_manager2_spends.orders).to include(order2)
    expect(pantry_manager2_spends.orders).to_not include(order1, order3)

    pantry_manager3_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager3 }
    expect(pantry_manager3_spends).to be_present
    expect(pantry_manager3_spends.orders).to be_blank
  end

  it 'only returns orders which are new/amended/confirmed/delivered/pending/cancelled/skipped' do
    [order1, order2].each{|order| order.update_column(:status, (Order::VALID_ORDER_STATUSES - Admin::Reports::FetchPantryManagerSpends::VALID_STAFFING_ORDER_STATUSES).sample) }

    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager1 }
    expect(pantry_manager1_spends).to be_present
    expect(pantry_manager1_spends.orders).to include(order3)
    expect(pantry_manager1_spends.orders).to_not include(order1, order2)

    pantry_manager2_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager2 }
    expect(pantry_manager2_spends).to be_present
    expect(pantry_manager2_spends.orders).to be_blank
    expect(pantry_manager2_spends.orders).to_not include(order1, order2, order3)

    pantry_manager3_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager3 }
    expect(pantry_manager3_spends).to be_present
    expect(pantry_manager3_spends.orders).to be_blank
  end

  it 'only returns active orders which are new/amended/confirmed/delivered/pending (NOT cancelled/skipped) when sent with active_orders_only' do
    [order1, order2].each{|order| order.update_column(:status, (Order::VALID_ORDER_STATUSES - Admin::Reports::FetchPantryManagerSpends::VALID_ACTIVE_ORDER_STATUSES).sample) }
    active_order_report_options = report_options.merge({ active_orders_only: true })
    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: active_order_report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager1 }
    expect(pantry_manager1_spends).to be_present
    expect(pantry_manager1_spends.orders).to include(order3)
    expect(pantry_manager1_spends.orders).to_not include(order1, order2)

    pantry_manager2_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager2 }
    expect(pantry_manager2_spends).to be_present
    expect(pantry_manager2_spends.orders).to be_blank
    expect(pantry_manager2_spends.orders).to_not include(order1, order2, order3)

    pantry_manager3_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager3 }
    expect(pantry_manager3_spends).to be_present
    expect(pantry_manager3_spends.orders).to be_blank
  end

  it 'only returns orders with staffing supplier order lines' do
    [order_line1, order_line2, order_line32].each{|order_line| order_line.update_column(:supplier_profile_id, non_staffing_supplier.id) }

    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager1 }
    expect(pantry_manager1_spends).to be_present
    expect(pantry_manager1_spends.orders).to include(order3) # order 3 has at lease 1 staffing supplier order lines
    expect(pantry_manager1_spends.orders).to_not include(order1, order2) # order 1 does not have staffing supplier order lines

    pantry_manager2_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager2 }
    expect(pantry_manager2_spends).to be_present
    expect(pantry_manager2_spends.orders).to be_blank # order 2 does not have staffing supplier order lines

    pantry_manager3_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager3 }
    expect(pantry_manager3_spends).to be_present
    expect(pantry_manager3_spends.orders).to be_blank
  end

  context 'with un-assigned staffing orders' do
    before do
      [order2, order3].each{|order| order.update_column(:pantry_manager_id, nil) }
    end

    it 'returns a blank pantry manager if staffing orders are unassigned' do
      pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

      expect(pantry_manager_spends).to be_present
      expect(pantry_manager_spends.size).to eq(4)
      expect(pantry_manager_spends.sample).to be_a(Admin::Reports::FetchPantryManagerSpends::PantryManager)
      expect(pantry_manager_spends.map(&:manager)).to include(pantry_manager1, pantry_manager2, pantry_manager3, nil)

      unassigned_pantry_manager_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager.nil? }
      expect(unassigned_pantry_manager_spends).to be_present
      expect(unassigned_pantry_manager_spends.orders).to include(order2, order3)
    end

    it 'only returns active unassigned staffing orders' do
      order3.update_column(:status, (Order::VALID_ORDER_STATUSES - Admin::Reports::FetchPantryManagerSpends::VALID_ACTIVE_ORDER_STATUSES).sample)
      pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

      expect(pantry_manager_spends).to be_present
      expect(pantry_manager_spends.size).to eq(4)
      expect(pantry_manager_spends.sample).to be_a(Admin::Reports::FetchPantryManagerSpends::PantryManager)
      expect(pantry_manager_spends.map(&:manager)).to include(pantry_manager1, pantry_manager2, pantry_manager3, nil)

      unassigned_pantry_manager_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager.nil? }
      expect(unassigned_pantry_manager_spends).to be_present
      expect(unassigned_pantry_manager_spends.orders).to include(order2)
      expect(unassigned_pantry_manager_spends.orders).to_not include(order3)
    end
  end

  it 'retuns the per customer spends per pantry manager' do
    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_customer_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager1 }&.customer_spends
    expect(pantry_manager1_customer_spends).to be_present
    expect(pantry_manager1_customer_spends.size).to eq(2)
    expect(pantry_manager1_customer_spends.sample).to be_a(Admin::Reports::FetchPantryManagerSpends::CustomerSpend)
    expect(pantry_manager1_customer_spends.map(&:customer)).to include(customer1, customer2)

    pantry_manager2_customer_spends = pantry_manager_spends.detect{|manager_spend| manager_spend.manager == pantry_manager2 }&.customer_spends
    expect(pantry_manager2_customer_spends).to be_present
    expect(pantry_manager2_customer_spends.size).to eq(1)
    expect(pantry_manager2_customer_spends.sample).to be_a(Admin::Reports::FetchPantryManagerSpends::CustomerSpend)
    expect(pantry_manager2_customer_spends.map(&:customer)).to include(customer2)
    expect(pantry_manager2_customer_spends.map(&:customer)).to_not include(customer1)
  end

  it 'returns the item spends per customer per pantry manager customer' do
    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_customer_spends = pantry_manager_spends.detect do |manager_spend|
      manager_spend.manager == pantry_manager1
    end&.customer_spends
    expect(pantry_manager1_customer_spends).to be_present
    pantry_manager1_customer1_item_spends = pantry_manager1_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer1
    end&.item_spends
    expect(pantry_manager1_customer1_item_spends).to be_present
    expect(pantry_manager1_customer1_item_spends.sample).to be_a(Admin::Reports::FetchPantryManagerSpends::ItemSpend)
    expect(pantry_manager1_customer1_item_spends.map(&:item)).to include(hourly_item)
    expect(pantry_manager1_customer1_item_spends.map(&:quantity)).to include(order_line1.quantity)
    expect(pantry_manager1_customer1_item_spends.map(&:hours)).to include(4)

    pantry_manager1_customer2_item_spends = pantry_manager1_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer2
    end&.item_spends
    expect(pantry_manager1_customer2_item_spends).to be_present
    expect(pantry_manager1_customer2_item_spends.sample).to be_a(Admin::Reports::FetchPantryManagerSpends::ItemSpend)
    expect(pantry_manager1_customer2_item_spends.map(&:item)).to include(hourly_item, half_hourly_item)
    expect(pantry_manager1_customer2_item_spends.map(&:quantity)).to include(order_line31.quantity, order_line32.quantity)
    expect(pantry_manager1_customer2_item_spends.map(&:hours)).to include(5, 0.5)

    pantry_manager2_customer_spends = pantry_manager_spends.detect do |manager_spend|
      manager_spend.manager == pantry_manager2
    end&.customer_spends
    expect(pantry_manager2_customer_spends).to be_present
    pantry_manager2_customer2_item_spends = pantry_manager2_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer2
    end&.item_spends
    expect(pantry_manager2_customer2_item_spends).to be_present
    expect(pantry_manager2_customer2_item_spends.sample).to be_a(Admin::Reports::FetchPantryManagerSpends::ItemSpend)
    expect(pantry_manager2_customer2_item_spends.map(&:item)).to include(weekly_item)
    expect(pantry_manager2_customer2_item_spends.map(&:quantity)).to include(order_line2.quantity)
    expect(pantry_manager2_customer2_item_spends.map(&:hours)).to include(37.5)
  end

  it 'returns the cumulative item based hours per pantry manager customer' do
    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_customer_spends = pantry_manager_spends.detect do |manager_spend|
      manager_spend.manager == pantry_manager1
    end&.customer_spends
    expect(pantry_manager1_customer_spends).to be_present
    pantry_manager1_customer1_spends = pantry_manager1_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer1
    end
    expect(pantry_manager1_customer1_spends).to be_present
    expect(pantry_manager1_customer1_spends.hours).to eq(4) # 1x hourly item

    pantry_manager1_customer2_spends = pantry_manager1_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer2
    end
    expect(pantry_manager1_customer2_spends).to be_present
    expect(pantry_manager1_customer2_spends.hours).to eq(5.5) # 5x hourly item + 1x half hour item

    pantry_manager2_customer_spends = pantry_manager_spends.detect do |manager_spend|
      manager_spend.manager == pantry_manager2
    end&.customer_spends
    expect(pantry_manager2_customer_spends).to be_present
    pantry_manager2_customer2_spends = pantry_manager2_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer2
    end
    expect(pantry_manager2_customer2_spends).to be_present
    expect(pantry_manager2_customer2_spends.hours).to eq(37.5) # 1x weekly item
  end

  it 'returns the cumulative customer based hours per pantry manager' do
    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_spends = pantry_manager_spends.detect do |manager_spend|
      manager_spend.manager == pantry_manager1
    end
    expect(pantry_manager1_spends).to be_present
    expect(pantry_manager1_spends.hours).to eq(9.5) # 4 + 5 + 0.5

    pantry_manager2_spends = pantry_manager_spends.detect do |manager_spend|
      manager_spend.manager == pantry_manager2
    end
    expect(pantry_manager2_spends).to be_present
    expect(pantry_manager2_spends.hours).to eq(37.5)
  end

  it 'only returns spends for active orders' do
    order3.update_column(:status, (Order::VALID_ORDER_STATUSES - Admin::Reports::FetchPantryManagerSpends::VALID_ACTIVE_ORDER_STATUSES).sample)

    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_spends = pantry_manager_spends.detect do |manager_spend|
      manager_spend.manager == pantry_manager1
    end
    expect(pantry_manager1_spends).to be_present
    expect(pantry_manager1_spends.hours).to eq(4)

    pantry_manager1_customer_spends = pantry_manager1_spends.customer_spends
    expect(pantry_manager1_customer_spends).to be_present
    pantry_manager1_customer1_spends = pantry_manager1_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer1
    end
    expect(pantry_manager1_customer1_spends).to be_present
    expect(pantry_manager1_customer1_spends.hours).to eq(4) # 1x hourly item

    pantry_manager1_customer2_spends = pantry_manager1_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer2
    end
    expect(pantry_manager1_customer2_spends).to be_blank
  end

  it 'only returns spends from order lines of Staffing Suppliers' do
    order_line31.update_column(:supplier_profile_id, non_staffing_supplier.id)

    pantry_manager_spends = Admin::Reports::FetchPantryManagerSpends.new(options: report_options).call

    expect(pantry_manager_spends).to be_present
    pantry_manager1_spends = pantry_manager_spends.detect do |manager_spend|
      manager_spend.manager == pantry_manager1
    end
    expect(pantry_manager1_spends).to be_present
    expect(pantry_manager1_spends.hours).to eq(4.5)

    pantry_manager1_customer_spends = pantry_manager1_spends.customer_spends
    expect(pantry_manager1_customer_spends).to be_present
    pantry_manager1_customer1_spends = pantry_manager1_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer1
    end
    expect(pantry_manager1_customer1_spends).to be_present
    expect(pantry_manager1_customer1_spends.hours).to eq(4) # 1x hourly item

    pantry_manager1_customer2_spends = pantry_manager1_customer_spends.detect do |customer_spend|
      customer_spend.customer == customer2
    end
    expect(pantry_manager1_customer2_spends).to be_present
    expect(pantry_manager1_customer2_spends.hours).to eq(0.5) # 1x half hour item
  end

  context 'Order Spends' do
    before do
      order3.reload
    end

    it 'returns the staffing spends for the passed in order' do
      order_spends = Admin::Reports::FetchPantryManagerSpends::OrderSpend.new(order: order3)

      expect(order_spends.hours).to eq(5.5) # 5.0 + 0.5
    end

    it 'returns the spends for the staffing order lines of the passed in order' do
      order_line31.update_column(:supplier_profile_id, non_staffing_supplier.id)
      order_spends = Admin::Reports::FetchPantryManagerSpends::OrderSpend.new(order: order3)

      expect(order_spends.hours).to eq(0.5)
    end
  end

end