require 'rails_helper'

RSpec.describe Admin::ListSuppliers, type: :service, admin: true, users: true, suppliers: true do
  
  let!(:supplier1) { create(:supplier_profile, :random, :with_user) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_user) }
  let!(:supplier3) { create(:supplier_profile, :random, :with_user) }

  let(:super_admin) { create(:user, :random, super_admin: true, admin: false, allow_all_supplier_access: false, can_access_suppliers: false) }
  let(:admin) { create(:user, :random, super_admin: false, admin: true, allow_all_supplier_access: false, can_access_suppliers: false) }
  let(:all_supplier_admin) { create(:user, :random, super_admin: false, admin: false, allow_all_supplier_access: true, can_access_suppliers: false) }
  let(:some_suppliers_admin) { create(:user, :random, super_admin: false, admin: false, allow_all_supplier_access: false, can_access_suppliers: true, supplier_profiles: [supplier1, supplier3]) }

  let(:yordar_admin) { [super_admin, admin].sample }
  let(:non_admin_user) { create(:user, :random, super_admin: false, admin: false, allow_all_supplier_access: false, can_access_suppliers: false) }

  context 'Access based filtering' do
    it 'does not lists any suppliers for missing user' do
      lister_options =  { for_user: nil, query: [true, false].sample ? supplier1.name : '' }
      suppliers = Admin::ListSuppliers.new(options: lister_options).call

      expect(suppliers).to be_blank
    end

    it 'super admins, Yordar Admins can list all suppliers' do
      lister_options = { for_user: yordar_admin }
      suppliers = Admin::ListSuppliers.new(options: lister_options).call

      expect(suppliers).to include(supplier1, supplier2, supplier3)
    end

    it 'User who can access all suppliers only list suppliers that are active' do
      supplier2.user.update_column(:is_active, false)

      lister_options = { for_user: all_supplier_admin }
      suppliers = Admin::ListSuppliers.new(options: lister_options).call

      expect(suppliers).to include(supplier1, supplier3)
      expect(suppliers).to_not include(supplier2)
    end

    it 'User who can access (some) suppliers only list suppliers they have access to' do
      lister_options = { for_user: some_suppliers_admin }
      suppliers = Admin::ListSuppliers.new(options: lister_options).call

      expect(suppliers).to include(supplier1, supplier3)
      expect(suppliers).to_not include(supplier2)
    end

    it 'Non admin users cannot list any suppliers' do
      lister_options = { for_user: non_admin_user }
      suppliers = Admin::ListSuppliers.new(options: lister_options).call

      expect(suppliers).to be_blank
    end
  end

  context 'filter by query' do
    it 'filters suppliers by their name' do
      lister_options = { for_user: yordar_admin, query: supplier3.company_name }
      suppliers = Admin::ListSuppliers.new(options: lister_options).call

      expect(suppliers).to include(supplier3)
      expect(suppliers).to_not include(supplier1, supplier2)
    end

    it 'filters suppliers by their email' do
      lister_options = { for_user: yordar_admin, query: supplier2.user.email }
      suppliers = Admin::ListSuppliers.new(options: lister_options).call

      expect(suppliers).to include(supplier2)
      expect(suppliers).to_not include(supplier1, supplier3)
    end
  end

  it 'paginates suppliers according to page and limit (default sort of ID)' do
    lister_options = { for_user: yordar_admin, page: 2, limit: 1 }
    suppliers = Admin::ListSuppliers.new(options: lister_options).call

    expect(suppliers).to include(supplier2)
    expect(suppliers).to_not include(supplier1, supplier3)
  end

end