require 'rails_helper'

RSpec.describe Admin::ListPromotions, type: :service, admin: true, users: true, promotions: true do

  let!(:promotion1) { create(:promotion, :random, description: 'promotion1') }
  let!(:promotion2) { create(:promotion, :random, description: 'promotion2') }
  let!(:promotion3) { create(:promotion, :random, description: 'promotion3') }

  it 'lists all promotions by default' do
    promotions = Admin::ListPromotions.new.call

    expect(promotions).to include(promotion1, promotion2, promotion3)
  end

  context 'filter by Promotion' do
    it 'returns the passed in promotion if accessible' do
      lister_options = { promotion: promotion2 }
      promotions = Admin::ListPromotions.new(options: lister_options).call

      expect(promotions).to include(promotion2)
      expect(promotions).to_not include(promotion1, promotion3)
    end

    it 'returns an empty list for non-promotion object' do
      supplier = create(:supplier_profile, :random, :with_user)
      lister_options = { promotion: supplier }
      promotions = Admin::ListPromotions.new(options: lister_options).call

      expect(promotions).to be_empty
    end
  end

  it 'paginates promotions according to page and limit (default sort of created_at and then ID)' do
    lister_options = { page: 2, limit: 1 }
    promotions = Admin::ListPromotions.new(options: lister_options).call

    expect(promotions).to include(promotion2)
    expect(promotions).to_not include(promotion1, promotion3)
  end

end