require 'rails_helper'

RSpec.describe RateCards::CheckUsage, type: :service, rate_cards: true do

  let!(:time) { Time.zone.now.beginning_of_day + 3.hours }
  let!(:menu_item) { create(:menu_item, :random) }
  let!(:rate_card) { create(:rate_card, :random, menu_item: menu_item) }

  it 'return false if there are not future orders containing the rate card item' do
    is_in_use = RateCards::CheckUsage.new(rate_card: rate_card, since: time).call

    expect(is_in_use).to be_falsey
  end

  context 'with future orders containing the item' do
    let!(:order) { create(:order, :confirmed, delivery_at: time) }
    let!(:order_line) { create(:order_line, :random, order: order, menu_item: menu_item) }

    it 'return true if there exist future orders containing the rate card item' do
      is_in_use = RateCards::CheckUsage.new(rate_card: rate_card, since: time).call

      expect(is_in_use).to be_truthy
    end

    it 'return false if there exist future orders containing the rate card item, but not active' do
      order.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[pending quoted new amended confirmed]).sample)
      is_in_use = RateCards::CheckUsage.new(rate_card: rate_card, since: time).call

      expect(is_in_use).to be_falsey
    end

    it 'return false if orders containing the rate card item are to be delivered in the past' do
      order.update_column(:delivery_at, time - 10.minutes)
      is_in_use = RateCards::CheckUsage.new(rate_card: rate_card, since: time).call

      expect(is_in_use).to be_falsey
    end
  end

  context 'with a serving size' do
    let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
    let!(:serving_rate_card) { create(:rate_card, :random, menu_item: menu_item, serving_size: serving_size) }

    it 'return false if there are not future orders containing the rate card item' do
      is_in_use = RateCards::CheckUsage.new(rate_card: serving_rate_card, since: time).call

      expect(is_in_use).to be_falsey
    end

    context 'with future orders contianing the item and serving size' do
      let!(:order) { create(:order, :confirmed, delivery_at: time) }
      let!(:serving_order_line) { create(:order_line, :random, order: order, menu_item: menu_item, serving_size: serving_size) }

      it 'return true if there exist future orders containing the rate card item and serving size' do
        is_in_use = RateCards::CheckUsage.new(rate_card: serving_rate_card, since: time).call

        expect(is_in_use).to be_truthy
      end

      it 'return false if there exist future orders containing the rate card item but not the rate card serving size' do
        serving_size2 = create(:serving_size, :random, menu_item: menu_item)
        serving_order_line.update_column(:serving_size_id, serving_size2.id)
        is_in_use = RateCards::CheckUsage.new(rate_card: serving_rate_card, since: time).call

        expect(is_in_use).to be_falsey
      end
    end

  end

end