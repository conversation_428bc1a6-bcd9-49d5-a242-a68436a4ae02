require 'rails_helper'

RSpec.describe RateCards::Upsert, type: :service, rate_cards: true do

  let!(:company) { create(:company, :random) }
  let(:supplier) { create(:supplier_profile, :random, commission_rate: 10, markup: 0) }
  let(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }

  let!(:rate_card_params) do
    {
      menu_item: menu_item,
      serving_size: nil,
      company: company,
      price: 10.2,
      cost: 9.2
    }
  end

  it 'creates a new rate card associated with the passed menu item' do
    rate_card_creator = RateCards::Upsert.new(rate_card_params: rate_card_params).call

    expect(rate_card_creator).to be_success
    created_rate_card = rate_card_creator.rate_card
    expect(created_rate_card.menu_item).to eq(menu_item)
  end

  it 'creates a new rate card with passed in params' do
    rate_card_creator = RateCards::Upsert.new(rate_card_params: rate_card_params).call

    expect(rate_card_creator).to be_success
    created_rate_card = rate_card_creator.rate_card
    expect(created_rate_card.company).to eq(rate_card_params[:company])
    expect(created_rate_card.price.to_s).to eq(rate_card_params[:price].to_s)
    expect(created_rate_card.cost.to_s).to eq(rate_card_params[:cost].to_s)
  end

  it 'doesn\'t let u creates a rate card for an missing menu item' do
    missing_item_params = rate_card_params.merge({ menu_item: nil })
    rate_card_creator = RateCards::Upsert.new(rate_card_params: missing_item_params).call

    expect(rate_card_creator).to_not be_success
    expect(rate_card_creator.errors).to include('Cannot create a rate card without a menu item')
  end

  it 'doesn\'t let u creates a rate card for an inactive menu item' do
    menu_item.update_column(:archived_at, Time.zone.now)
    rate_card_creator = RateCards::Upsert.new(rate_card_params: rate_card_params).call

    expect(rate_card_creator).to_not be_success
    expect(rate_card_creator.errors).to include('Cannot create a rate card for an archived menu item')
  end

  context 'serving size rate cards' do
    let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item) }
    let!(:serving_size_rate_card_params) do
      rate_card_params.merge({
        serving_size: serving_size
      })
    end

    it 'creates a new rate card associated with the passed serving size' do
      rate_card_creator = RateCards::Upsert.new(rate_card_params: serving_size_rate_card_params).call

      expect(rate_card_creator).to be_success
      created_rate_card = rate_card_creator.rate_card
      expect(created_rate_card.menu_item).to eq(menu_item)
      expect(created_rate_card.serving_size).to eq(serving_size)
    end

    it 'doesn\'t let u creates a rate card for an inactive serving size' do
      serving_size.update_column(:archived_at, Time.zone.now)
      rate_card_creator = RateCards::Upsert.new(rate_card_params: serving_size_rate_card_params).call

      expect(rate_card_creator).to_not be_success
      expect(rate_card_creator.errors).to include('Cannot create a rate card for an archived serving size')
    end

    it 'doesn\'t let u creates a serving size rate card without a menu item' do
      missing_item_card_params = serving_size_rate_card_params.merge({ menu_item: nil })
      rate_card_creator = RateCards::Upsert.new(rate_card_params: missing_item_card_params).call

      expect(rate_card_creator).to_not be_success
      expect(rate_card_creator.errors).to include('Serving Size does not belong to the Menu Item')
    end

    it 'doesn\'t let u creates a serving size rate card for a menu item not belonging to the serving size' do
      menu_item2 = create(:menu_item, :random)
      mismatch_item_card_params = serving_size_rate_card_params.merge({ menu_item: menu_item2 })
      rate_card_creator = RateCards::Upsert.new(rate_card_params: mismatch_item_card_params).call

      expect(rate_card_creator).to_not be_success
      expect(rate_card_creator.errors).to include('Serving Size does not belong to the Menu Item')
    end

    it 'doesn\'t let u creates a non serving size rate card for a menu item with active serving sizes' do
      non_serving_card_params = serving_size_rate_card_params.merge({ serving_size: nil })
      rate_card_creator = RateCards::Upsert.new(rate_card_params: non_serving_card_params).call

      expect(rate_card_creator).to_not be_success
      # expect(rate_card_creator.errors).to include('Serving Size does not belong to the Menu Item')
    end
  end

  context 'with exiting order lines containing rate card item', order: true, order_lines: true do
    let!(:customer) { create(:customer_profile, :random) }
    let!(:company_customer) { create(:customer_profile, :random, company: company) }

    let(:future_order1) { create(:order, :draft, customer_profile: customer) }
    let(:location1) { create(:location, :random, order: future_order1) }
    let!(:order_line1) { create(:order_line, :random, location: location1, menu_item: menu_item, order: future_order1, supplier_profile: supplier) }

    let(:future_order2) { create(:order, :draft, customer_profile: company_customer) }
    let(:location2) { create(:location, :random, order: future_order2) }
    let!(:order_line2) { create(:order_line, :random, location: location2, menu_item: menu_item, order: future_order2, supplier_profile: supplier) }

    it 'updates the future order lines to use the rate card price/cost' do
      rate_card_updator = RateCards::Upsert.new(rate_card_params: rate_card_params).call

      expect(rate_card_updator).to be_success

      order_line1.reload
      expect(order_line1.price.to_s).to_not eq(rate_card_params[:price].to_s)
      expect(order_line1.cost.to_s).to_not eq(rate_card_params[:cost].to_s)

      order_line2.reload
      expect(order_line2.price.to_s).to eq(rate_card_params[:price].to_s)
      expect(order_line2.cost.to_s).to eq(rate_card_params[:cost].to_s)
    end
  end # with existing order lines

  context 'with existing rate card' do
    let!(:rate_card) { create(:rate_card, :random, menu_item: menu_item, company: company) }
    let!(:update_rate_card_params) { rate_card_params.except(:company, :menu_item, :serving_size) }

    it 'updates the passed in rate card' do
      rate_card_creator = RateCards::Upsert.new(rate_card: rate_card, rate_card_params: update_rate_card_params).call

      expect(rate_card_creator).to be_success
      created_rate_card = rate_card_creator.rate_card
      expect(created_rate_card.id).to eq(rate_card.id) # same rate card
      expect(created_rate_card.company).to eq(rate_card.company)
      expect(created_rate_card.price.to_s).to eq(update_rate_card_params[:price].to_s)
      expect(created_rate_card.cost.to_s).to eq(update_rate_card_params[:cost].to_s)
    end

    it 'does not creates a new rate card for the same menu item and company conbination (instead it updates) ' do
      rate_card_creator = RateCards::Upsert.new(rate_card_params: rate_card_params).call

      expect(rate_card_creator).to be_success
      created_rate_card = rate_card_creator.rate_card
      expect(created_rate_card.id).to eq(rate_card.id) # same rate card
      expect(created_rate_card.company).to eq(rate_card.company)
      expect(created_rate_card.price.to_s).to eq(rate_card_params[:price].to_s)
      expect(created_rate_card.cost.to_s).to eq(rate_card_params[:cost].to_s)
    end

    context 'with exiting order lines containing rate card', order: true, order_lines: true do
      let!(:customer) { create(:customer_profile, :random) }
      let!(:company_customer) { create(:customer_profile, :random, company: company) }

      let(:future_order1) { create(:order, :draft, customer_profile: customer) }
      let(:location1) { create(:location, :random, order: future_order1) }
      let!(:order_line1) { create(:order_line, :random, location: location1, menu_item: menu_item, order: future_order1, supplier_profile: supplier) }

      let(:future_order2) { create(:order, :draft, customer_profile: company_customer) }
      let(:location2) { create(:location, :random, order: future_order2) }
      let!(:order_line2) { create(:order_line, :random, location: location2, menu_item: menu_item, order: future_order2, supplier_profile: supplier) }

      it 'updates the future order lines if the rate card price/cost is updated' do
        rate_card_updator = RateCards::Upsert.new(rate_card: rate_card, rate_card_params: update_rate_card_params).call

        expect(rate_card_updator).to be_success

        order_line1.reload
        expect(order_line1.price.to_s).to_not eq(update_rate_card_params[:price].to_s)
        expect(order_line1.cost.to_s).to_not eq(update_rate_card_params[:cost].to_s)

        order_line2.reload
        expect(order_line2.price.to_s).to eq(update_rate_card_params[:price].to_s)
        expect(order_line2.cost.to_s).to eq(update_rate_card_params[:cost].to_s)
      end

      context 'mock future order line update' do
        before do
          order_lines_updater = delayed_order_lines_updater = double(MenuItems::UpdateFutureOrderLines)
          allow(MenuItems::UpdateFutureOrderLines).to receive(:new).and_return(order_lines_updater)
          allow(order_lines_updater).to receive(:delay).and_return(delayed_order_lines_updater)
          allow(delayed_order_lines_updater).to receive(:call).and_return(true)
        end

        it 'calls future order lines to be updated' do
          expect(MenuItems::UpdateFutureOrderLines).to receive(:new).with(menu_item: menu_item, serving_size: nil, rate_card: anything) # newly created rate card

          rate_card_updator = RateCards::Upsert.new(rate_card_params: rate_card_params).call
          expect(rate_card_updator).to be_success
        end
      end

      context 'update order lines as a Delayed Job', delayed_jobs: true do
        let!(:order_line_update_checker) { double(OrderLines::FutureUpdateExists) }

        before do
          # do not run the delayed jobs inline, instead create a Delayed::Job record
          Delayed::Worker.delay_jobs = true

          # mock if a future order line update exists
          allow(OrderLines::FutureUpdateExists).to receive(:new).and_return(order_line_update_checker)
          allow(order_line_update_checker).to receive(:call).and_return(false)
        end

        after do
          # revert back to running delayed jobs in line
          Delayed::Worker.delay_jobs = false
        end

        it 'creates a Delayed Job to update future order lines' do
          rate_card_updator = RateCards::Upsert.new(rate_card_params: rate_card_params)
          expect{ rate_card_updator.call }.to change{ Delayed::Job.count }.from(0).to(1)

          delayed_job = Delayed::Job.last
          expect(delayed_job).to be_present
          expect(delayed_job.handler).to include('MenuItems::UpdateFutureOrderLines')

          job_handler = YAML.load(delayed_job.handler)
          expect(job_handler.menu_item).to eq(menu_item)
          expect(job_handler.rate_card).to be_present
        end

        it 'doesn\'t create a second delayed job to update future order lines if one already exists' do
          # create a rate card update
          MenuItems::UpdateFutureOrderLines.new(menu_item: menu_item, rate_card: rate_card).delay(queue: :data_integrity).call
          expect(Delayed::Job.count).to eq(1)

          # mock an update exists
          allow(order_line_update_checker).to receive(:call).and_return(true)

          rate_card_updator = RateCards::Upsert.new(rate_card_params: rate_card_params)
          expect{ rate_card_updator.call }.not_to change{ Delayed::Job.count }.from(1)
        end
      end # update as a Delayed Job
    end # with existing order lines

  end # with existing rate card
end
