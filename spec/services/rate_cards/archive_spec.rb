require 'rails_helper'

RSpec.describe RateCards::Archive, type: :service, rate_cards: true do

  let!(:rate_card) { create(:rate_card, :random) }

  it 'removes the rate card' do
    archiver = RateCards::Archive.new(rate_card: rate_card).call

    expect(archiver).to be_success
    expect{ rate_card.reload }.to raise_error(ActiveRecord::RecordNotFound)
  end

  context 'with order lines' do
    let!(:order) { create(:order, :confirmed, delivery_at: Time.zone.now + 20.days) }
    let!(:order_line) { create(:order_line, :random, order: order, menu_item: rate_card.menu_item) }

    it 'cannot archive rate card which is used in a future order' do
      archiver = RateCards::Archive.new(rate_card: rate_card).call
      expect(archiver).to_not be_success
      expect(archiver.warnings).to include('The item is still in use!')
    end

    it 'can archive the rate card if the order its used in is in is not active' do
      order.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[pending quoted new amended confirmed]).sample)

      archiver = RateCards::Archive.new(rate_card: rate_card).call
      expect(archiver).to be_success
      expect{ rate_card.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'can archive rate card with active order lines if forced' do
      archiver = RateCards::Archive.new(rate_card: rate_card, forced: true).call
      expect(archiver).to be_success
      expect{ rate_card.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    context 'mock future order line update' do
      before do
        order_lines_updater = delayed_order_lines_updater = double(MenuItems::UpdateFutureOrderLines)
        allow(MenuItems::UpdateFutureOrderLines).to receive(:new).and_return(order_lines_updater)
        allow(order_lines_updater).to receive(:delay).and_return(delayed_order_lines_updater)
        allow(delayed_order_lines_updater).to receive(:call).and_return(true)
      end

      it 'calls future order lines to be updated' do
        expect(MenuItems::UpdateFutureOrderLines).to receive(:new).with(menu_item: rate_card.menu_item, serving_size: rate_card.serving_size)

        archiver = RateCards::Archive.new(rate_card: rate_card, forced: true).call
        expect(archiver).to be_success
      end
    end

    context 'update order lines as a Delayed Job', delayed_jobs: true do
      let!(:order_line_update_checker) { double(OrderLines::FutureUpdateExists) }

      before do
        # do not run the delayed jobs inline, instead create a Delayed::Job record
        Delayed::Worker.delay_jobs = true

        # mock if a future order line update exists
        allow(OrderLines::FutureUpdateExists).to receive(:new).and_return(order_line_update_checker)
        allow(order_line_update_checker).to receive(:call).and_return(false)
      end

      after do
        # revert back to running delayed jobs in line
        Delayed::Worker.delay_jobs = false
      end

      it 'creates a Delayed Job to update future order lines' do
        archiver = RateCards::Archive.new(rate_card: rate_card, forced: true)
        expect{ archiver.call }.to change{ Delayed::Job.count }.from(0).to(1)

        delayed_job = Delayed::Job.last
        expect(delayed_job).to be_present
        expect(delayed_job.handler).to include('MenuItems::UpdateFutureOrderLines')

        job_handler = YAML.load(delayed_job.handler)
        expect(job_handler.menu_item).to eq(rate_card.menu_item)
      end

      it 'doesn\'t create a second delayed job to update future order lines if one already exists' do
        # create a rate card item update
        MenuItems::UpdateFutureOrderLines.new(menu_item: rate_card.menu_item).delay(queue: :data_integrity).call
        expect(Delayed::Job.count).to eq(1)

        # mock an update exists
        allow(order_line_update_checker).to receive(:call).and_return(true)

        archiver = RateCards::Archive.new(rate_card: rate_card, forced: true)
        expect{ archiver.call }.not_to change{ Delayed::Job.count }.from(1)
      end
    end # update as a Delayed Job
  end

  describe 'errors' do
    it 'cannot archive a missing rate card' do
      archiver = RateCards::Archive.new(rate_card: nil).call
      expect(archiver).to_not be_success
      expect(archiver.errors).to include('Cannot archive a missing rate card')
    end
  end
end
