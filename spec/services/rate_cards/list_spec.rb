require 'rails_helper'

RSpec.describe RateCards::List, type: :service, rate_cards: true do

  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:supplier2) { create(:supplier_profile, :random) }

  let!(:menu_item1) { create(:menu_item, :random, supplier_profile: supplier1) }
  let!(:menu_item2) { create(:menu_item, :random, supplier_profile: supplier2) }

  let!(:serving_size) { create(:serving_size, :random, menu_item: menu_item1) }

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:customer2) { create(:customer_profile, :random) }

  let!(:company) { create(:company, :random, customer_profiles: [customer1]) }

  let!(:rate_card1) { create(:rate_card, :random, company: company, menu_item: menu_item1, serving_size: serving_size) }
  let!(:rate_card2) { create(:rate_card, :random, company: company, menu_item: menu_item2) }

  it 'returns the rate cards for a given customer' do
    rate_cards = RateCards::List.new(customer: customer1).call

    expect(rate_cards).to include(rate_card1, rate_card2)
  end

  it 'filters the rate_cards by passed in supplier' do
    rate_cards = RateCards::List.new(customer: customer1, options: { supplier: supplier1 }).call

    expect(rate_cards).to include(rate_card1)
    expect(rate_cards).to_not include(rate_card2)
  end

  it 'filters the rate_cards by passed in menu_item' do
    rate_cards = RateCards::List.new(customer: customer1, options: { menu_item: menu_item2 }).call

    expect(rate_cards).to include(rate_card2)
    expect(rate_cards).to_not include(rate_card1)
  end

  it 'filters the rate_cards by passed in serving_size' do
    rate_cards = RateCards::List.new(customer: customer1, options: { serving_size: serving_size }).call

    expect(rate_cards).to include(rate_card1)
    expect(rate_cards).to_not include(rate_card2)
  end

  it 'filters the rate card by the active menu item' do
    menu_item1.update_column(:archived_at, Time.zone.now)
    rate_cards = RateCards::List.new(customer: customer1, options: { active_only: true }).call

    expect(rate_cards).to include(rate_card2)
    expect(rate_cards).to_not include(rate_card1)
  end

  context 'with conflicting multiple filters' do
    it 'filters rate cards by passed in menu item over passed in supplier profile' do
      rate_cards = RateCards::List.new(customer: customer1, options: { supplier: supplier1, menu_item: menu_item2 }).call

      expect(rate_cards).to include(rate_card2)
      expect(rate_cards).to_not include(rate_card1)
    end

    it 'filters rate cards by passed in serving_size over passed in supplier profile' do
      rate_cards = RateCards::List.new(customer: customer1, options: { supplier: supplier2, serving_size: serving_size }).call

      expect(rate_cards).to include(rate_card1)
      expect(rate_cards).to_not include(rate_card2)
    end

    it 'filters rate cards by passed in serving_size over passed in menu item' do
      rate_cards = RateCards::List.new(customer: customer1, options: { menu_item: menu_item2, serving_size: serving_size }).call

      expect(rate_cards).to include(rate_card1)
      expect(rate_cards).to_not include(rate_card2)
    end
  end

  it 'returns blank if the customer is blank' do
    rate_cards = RateCards::List.new(customer: nil).call

    expect(rate_cards).to be_empty
  end

  it 'returns blank if the customer is not associated with a company' do
    rate_cards = RateCards::List.new(customer: customer2).call

    expect(rate_cards).to be_empty
  end

  it 'returns blank if the customer company does not have any rate_cards' do
    create(:company, :random, customer_profiles: [customer2]) # create customer company
    customer2.reload

    rate_cards = RateCards::List.new(customer: customer2).call
    expect(rate_cards).to be_empty
  end

end
