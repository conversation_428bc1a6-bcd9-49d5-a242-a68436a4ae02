require 'rails_helper'

RSpec.describe TeamOrderAttendees::Checkout, type: :servive, team_orders: true do
  include ActionView::Helpers::NumberHelper

  let!(:team_admin) { create(:customer_profile, :random, :with_user) }
  let!(:team_order) { create(:order, :team_order, customer_profile: team_admin) }
  let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }
  let!(:invited_attendee) { create(:team_order_attendee, :random, order: team_order, event_attendee: event_attendee, status: 'pending') }

  before do
    checkout_emailer = delayed_emailer = double(TeamOrderAttendees::Emails::SendCheckoutEmail)
    allow(TeamOrderAttendees::Emails::SendCheckoutEmail).to receive(:new).and_return(checkout_emailer)
    allow(checkout_emailer).to receive(:delay).and_return(delayed_emailer)
    allow(delayed_emailer).to receive(:call).and_return(true)
  end

  it 'checks out the attendee order successfully' do
    order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code).call

    expect(order_checkout).to be_success

    expect(order_checkout.team_order_attendee.id).to eq(invited_attendee.id)
    expect(order_checkout.team_order_attendee.status).to eq('ordered')

    expect(order_checkout.team_order_attendee.event_attendee.id).to eq(event_attendee.id)
    expect(order_checkout.team_order.id).to eq(team_order.id)
  end

  it 'notifies the attendee after successful checkout' do
    expect(TeamOrderAttendees::Emails::SendCheckoutEmail).to receive(:new).with(team_order_attendee: invited_attendee)
    order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code).call

    expect(order_checkout).to be_success
  end

  it 'can checkout an already confirmed attendee' do
    invited_attendee.update_column(:status, 'ordered')
    order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code).call

    expect(order_checkout).to be_success
    expect(order_checkout.warnings).to include('Your order is already confirmed')
    expect(order_checkout.is_redirected).to be_truthy
  end

  context 'with a budget' do
    let!(:attendee_order_line) { create(:order_line, :random, :with_supplier, order: team_order, team_order_attendee: invited_attendee, is_gst_free: true, price: 10, quantity: 3) }

    before do
      team_order.team_order_detail.update_column(:budget, 20)
    end

    it 'successfully checkout when attendee order total is less than (or equal to) budget' do
      attendee_order_line.update_column(:quantity, 2)
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code).call

      expect(order_checkout).to be_success
    end

    it 'cannot checkout if attendee order total is more than team order budget' do
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code).call

      expect(order_checkout).to_not be_success
      expect(order_checkout.errors).to include("You have exceeded your budget of #{number_to_currency(team_order.team_order_budget)}")
    end

    it 'cannot checkout if attendee order total is more than team order budget and does not include budget value if set to hide' do
      team_order.team_order_detail.update_column(:hide_budget, true)

      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code).call

      expect(order_checkout).to_not be_success
      expect(order_checkout.errors).to include('You have exceeded your budget')
    end

    it 'checks out an over budget order successful when ordering as (Team) Admin' do
      attendee_order_line.update_column(:attendee_id, nil) # make order line a team admin order line

      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: team_order.unique_event_id, profile: team_admin).call
      expect(order_checkout).to be_success
    end

    it 'checks out and over budget attendee order successful when logged in as a team admin' do
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code, profile: team_admin).call

      expect(order_checkout).to be_success
    end
  end

  context 'checkout as team admin (send order unique id as attendee id)' do
    it 'checks out the order successful as a team admin' do
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: team_order.unique_event_id, profile: team_admin).call

      expect(order_checkout).to be_success
    end

    it 'doesn\'t send an attendee email' do
      expect(TeamOrderAttendees::Emails::SendCheckoutEmail).to_not receive(:new)
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: team_order.unique_event_id, profile: team_admin).call

      expect(order_checkout).to be_success
    end

    it 'cannot checkout as admin if profile isn\'t passed' do
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: team_order.unique_event_id, profile: nil).call

      expect(order_checkout).to_not be_success
      expect(order_checkout.errors).to include('Could not find a matching team order')
    end

    it 'cannot checkout as admin if team order does not belong to passed in profile' do
      if [true, false].sample
        other_team_admin = create(:customer_profile, :random)
        order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: team_order.unique_event_id, profile: other_team_admin).call
      else
        other_team_order = create(:order, :draft, order_variant: 'team_order', unique_event_id: SecureRandom.hex(7))
        order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: other_team_order.unique_event_id, profile: team_admin).call
      end

      expect(order_checkout).to_not be_success
      expect(order_checkout.errors).to include('Could not find a matching team order')
    end
  end

  context 'with team order levels', team_order_levels: true do
    let!(:team_order_level) { create(:team_order_level, :random, team_order_detail: team_order.team_order_detail) }
    let!(:checkout_params) do
      {
        team_order_level_id: team_order_level.id
      }
    end

    it 'set the team order attendee level if passed as checkout params' do
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code, checkout_params: checkout_params).call

      expect(order_checkout).to be_success

      checkout_team_order_attendee = order_checkout.team_order_attendee
      expect(checkout_team_order_attendee.level).to eq(team_order_level)
    end

    it 'can checkout an already confirmed attendee along with updates to the level if passed in' do
      invited_attendee.update_column(:status, 'ordered')
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code, checkout_params: checkout_params).call

      expect(order_checkout).to be_success
      expect(order_checkout.warnings).to include('Your order is already confirmed')
      checkout_team_order_attendee = order_checkout.team_order_attendee
      expect(checkout_team_order_attendee.level).to eq(team_order_level)
    end
  end

  describe 'errors' do
    it 'cannot checkout if the team order attendee cannot be found' do
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: nil).call

      expect(order_checkout).to_not be_success
      expect(order_checkout.errors).to include('Could not find a matching team order')
      expect(order_checkout.is_redirected).to be_truthy
    end

    it 'cannot checkout if the attendee hasn\'t ordered yet' do
      invited_attendee.update_column(:status, 'invited')
      order_checkout = TeamOrderAttendees::Checkout.new(attendee_code: invited_attendee.uniq_code).call

      expect(order_checkout).to_not be_success
      expect(order_checkout.errors).to include('Your order does not have any selections')
      expect(order_checkout.is_redirected).to be_falsey
    end
  end
end
