require 'rails_helper'

RSpec.describe TeamOrderAttendees::Unsubscribe, type: :service, team_order: true do

  let!(:team_order) { create(:order, :draft, order_variant: 'team_order') }
  let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order, status: 'invited') }

  it 'successfully unsubscribes an invited team order attendee' do
    attendee_unsubscriber = TeamOrderAttendees::Unsubscribe.new(attendee_code: team_order_attendee.uniq_code).call

    expect(attendee_unsubscriber).to be_success

    unsubscribed_team_order_attendee = attendee_unsubscriber.team_order_attendee
    expect(unsubscribed_team_order_attendee.id).to eq(team_order_attendee.id)
    expect(unsubscribed_team_order_attendee.status).to eq('declined')
  end

  it 'successfully unsubscribes a team order attendee with a confirmed order' do
    team_order_attendee.update_column(:status, 'ordered')
    attendee_unsubscriber = TeamOrderAttendees::Unsubscribe.new(attendee_code: team_order_attendee.uniq_code).call

    expect(attendee_unsubscriber).to be_success

    unsubscribed_team_order_attendee = attendee_unsubscriber.team_order_attendee
    expect(unsubscribed_team_order_attendee.id).to eq(team_order_attendee.id)
    expect(unsubscribed_team_order_attendee.status).to eq('declined')
  end

  context 'errors' do
    it 'cannot un-subscribe a missing team order attendee' do
     attendee_unsubscriber = TeamOrderAttendees::Unsubscribe.new(attendee_code: SecureRandom.hex(7)).call

     expect(attendee_unsubscriber).to_not be_success
     expect(attendee_unsubscriber.errors).to include('Could not find a matching team order')

     expect(team_order_attendee.reload.status).to_not eq('declined')
    end

    it 'cannot un-subscribe a team order attendee with confirmed order and the order is already past cutoff' do
     team_order_attendee.update_column(:status, 'ordered')
     team_order.update_column(:status, %w[confirmed delivered].sample)
     attendee_unsubscriber = TeamOrderAttendees::Unsubscribe.new(attendee_code: team_order_attendee.uniq_code).call

     expect(attendee_unsubscriber).to_not be_success
     expect(attendee_unsubscriber.errors).to include('Cannot decline your invitation now as your order has been passed on to the suppliers')

     expect(team_order_attendee.reload.status).to_not eq('declined')
    end

    it 'cannot un-subscribe the attendee past the team order\'s cutoff period - 1 hour (threshold)' do
      team_order.update_column(:delivery_at, Time.zone.now) # force to past cutoff
      attendee_unsubscriber = TeamOrderAttendees::Unsubscribe.new(attendee_code: team_order_attendee.uniq_code).call

      expect(attendee_unsubscriber).to_not be_success
      expect(attendee_unsubscriber.errors).to include('Cannot decline your invitation now as its past the cutoff period')
    end
  end

end
