require 'rails_helper'

RSpec.describe TeamOrderAttendees::UnsubscribeWithinPackage, type: :service, team_order: true do

  let!(:package_id) { SecureRandom.uuid }
  let!(:team_order1) { create(:order, :team_order, name: 'team order1', delivery_at: Time.zone.now + 1.days) }
  let!(:team_order2) { create(:order, :team_order, name: 'team order2', delivery_at: Time.zone.now + 2.days) }
  let!(:team_order3) { create(:order, :team_order, name: 'team order3', delivery_at: Time.zone.now + 3.days) }

  let!(:team_order_attendee11) { create(:team_order_attendee, :random, order: team_order1, status: 'invited') }
  let!(:team_order_attendee12) { create(:team_order_attendee, :random, order: team_order2, event_attendee: team_order_attendee11.event_attendee, status: 'invited') }
  let!(:team_order_attendee13) { create(:team_order_attendee, :random, order: team_order3, event_attendee: team_order_attendee11.event_attendee, status: 'invited') }

  before do
    [team_order1, team_order2, team_order3].each do |team_order|
      team_order.team_order_detail.update_column(:package_id, package_id)
    end
  end

  it 'successfully unsubscribes a team order attendee from all invited team orders' do
    attendee_unsubscriber = TeamOrderAttendees::UnsubscribeWithinPackage.new(attendee_code: team_order_attendee11.uniq_code).call

    expect(attendee_unsubscriber).to be_success
    unsubscribed_attendees = attendee_unsubscriber.unsubscribed_attendees

    expect(unsubscribed_attendees).to include(team_order_attendee11, team_order_attendee12, team_order_attendee13)
    expect(unsubscribed_attendees.each(&:reload).map(&:status).uniq).to eq(['declined'])
  end

  it 'does not unsubscribe a team order attendee from any inactive package team orders' do
    team_order2.update_column(:status, %w[delivered cancelled rejected skipped].sample)
    attendee_unsubscriber = TeamOrderAttendees::UnsubscribeWithinPackage.new(attendee_code: team_order_attendee11.uniq_code).call

    expect(attendee_unsubscriber).to be_success
    unsubscribed_attendees = attendee_unsubscriber.unsubscribed_attendees

    expect(unsubscribed_attendees).to_not include(team_order_attendee12)
    expect(unsubscribed_attendees).to include(team_order_attendee11, team_order_attendee13)
  end

  it 'only unsubscribe a team order attendee from any future package team orders' do
    team_order3.update_column(:delivery_at, team_order1.delivery_at - 1.day)
    attendee_unsubscriber = TeamOrderAttendees::UnsubscribeWithinPackage.new(attendee_code: team_order_attendee11.uniq_code).call

    expect(attendee_unsubscriber).to be_success
    unsubscribed_attendees = attendee_unsubscriber.unsubscribed_attendees

    expect(unsubscribed_attendees).to_not include(team_order_attendee13)
    expect(unsubscribed_attendees).to include(team_order_attendee11, team_order_attendee12)
  end

  it 'does not unsubscribe a team order attendee from package team orders, they\'ve already declined or were removed from' do
    team_order_attendee13.update_column(:status, %w[declined cancelled].sample)
    attendee_unsubscriber = TeamOrderAttendees::UnsubscribeWithinPackage.new(attendee_code: team_order_attendee11.uniq_code).call

    expect(attendee_unsubscriber).to be_success
    unsubscribed_attendees = attendee_unsubscriber.unsubscribed_attendees

    expect(unsubscribed_attendees).to_not include(team_order_attendee13)
    expect(unsubscribed_attendees).to include(team_order_attendee11, team_order_attendee12)
  end

  context 'errors' do
    it 'cannot un-subscribe a missing team order attendee' do
     attendee_unsubscriber = TeamOrderAttendees::UnsubscribeWithinPackage.new(attendee_code: SecureRandom.hex(7)).call

     expect(attendee_unsubscriber).to_not be_success
     expect(attendee_unsubscriber.errors).to include('Could not find a matching team order')

     expect(team_order_attendee11.reload.status).to_not eq('declined')
     expect(team_order_attendee12.reload.status).to_not eq('declined')
     expect(team_order_attendee13.reload.status).to_not eq('declined')
    end

    # also tested in TeamOrderAttendees::Unsubscribe
    it 'cannot un-subscribe a team order attendee with confirmed order and the order is already past cutoff' do
     team_order_attendee11.update_column(:status, 'ordered')
     team_order1.update_column(:status, %w[confirmed delivered].sample)
     attendee_unsubscriber = TeamOrderAttendees::UnsubscribeWithinPackage.new(attendee_code: team_order_attendee11.uniq_code).call

     expect(attendee_unsubscriber).to_not be_success
     expect(attendee_unsubscriber.errors).to include('Cannot decline your invitation now as your order has been passed on to the suppliers')

     expect(team_order_attendee11.reload.status).to_not eq('declined')
    end

    # also tested in TeamOrderAttendees::Unsubscribe
    it 'cannot un-subscribe the attendee past the team order\'s cutoff period - 1 hour (threshold)' do
      team_order1.update_column(:delivery_at, Time.zone.now + 3.minutes) # force to past cutoff
      attendee_unsubscriber = TeamOrderAttendees::UnsubscribeWithinPackage.new(attendee_code: team_order_attendee11.uniq_code).call

      expect(attendee_unsubscriber).to_not be_success
      expect(attendee_unsubscriber.errors).to include('Cannot decline your invitation now as its past the cutoff period')
    end
  end

end
