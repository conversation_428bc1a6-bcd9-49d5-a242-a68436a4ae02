require 'rails_helper'

RSpec.describe TeamOrderAttendees::Fetch, type: :service, orders: true, team_orders: true do

  let!(:team_admin) { create(:customer_profile, :random, :with_user) }
  let!(:team_order) { create(:order, :draft, order_variant: 'team_order', customer_profile: team_admin, unique_event_id: SecureRandom.hex(7)) }
  let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }
  let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order, event_attendee: event_attendee, status: 'pending') }

  it 'returns the team order attendee as per passed in attendee code' do
    fetched_attendee = TeamOrderAttendees::Fetch.new(attendee_code: team_order_attendee.uniq_code).call

    expect(fetched_attendee).to eq(team_order_attendee)
    expect(fetched_attendee.event_attendee).to eq(event_attendee)
  end

  it 'returns the team order attendee as per passed in attendee code even if profile is passed' do
    fetched_attendee = TeamOrderAttendees::Fetch.new(attendee_code: team_order_attendee.uniq_code, profile: team_admin).call

    expect(fetched_attendee).to eq(team_order_attendee)
    expect(fetched_attendee.event_attendee).to eq(event_attendee)
  end

  it 'returns the attendee even if it does not belong to the passed in profile - attendee code takes precedence' do
    other_team_admin = create(:customer_profile, :random)
    fetched_attendee = TeamOrderAttendees::Fetch.new(attendee_code: team_order_attendee.uniq_code, profile: other_team_admin).call

    expect(fetched_attendee).to eq(team_order_attendee)
    expect(fetched_attendee.event_attendee).to eq(event_attendee)
  end

  context 'as an admin (passed in attendee code is the order unique id' do
    it 'returns the mock team order attendee the team admin' do
      fetched_attendee = TeamOrderAttendees::Fetch.new(attendee_code: team_order.unique_event_id, profile: team_admin).call

      expect(fetched_attendee).to be_present
      expect(fetched_attendee).to be_a(TeamOrderAttendee)
      expect(fetched_attendee).to_not be_persisted
      expect(fetched_attendee.order).to eq(team_order)
      expect(fetched_attendee.uniq_code).to eq(team_order.unique_event_id)
      expect(fetched_attendee).to be_is_team_admin

      expect(fetched_attendee.status).to eq('pending') # used for checkout
    end

    it 'returns the mock team order attendee with a mock event attendee the team admin' do
      fetched_attendee = TeamOrderAttendees::Fetch.new(attendee_code: team_order.unique_event_id, profile: team_admin).call

      expect(fetched_attendee).to be_present
      admin_event_attendee = fetched_attendee.event_attendee

      expect(admin_event_attendee).to be_a(EventAttendee)
      expect(admin_event_attendee).to_not be_persisted
      expect(admin_event_attendee.first_name).to eq(team_admin.name)
      expect(admin_event_attendee.last_name).to eq('(team admin)')
      expect(admin_event_attendee.email).to eq(team_admin.user.email)
    end

    it 'does not return an attendee there is a team order and profile mismatch' do
      if [true, false].sample
        other_team_admin = create(:customer_profile, :random)
        fetched_attendee = TeamOrderAttendees::Fetch.new(attendee_code: team_order.unique_event_id, profile: other_team_admin).call
      else
        other_team_order = create(:order, :draft, order_variant: 'team_order', unique_event_id: SecureRandom.hex(7))
        fetched_attendee = TeamOrderAttendees::Fetch.new(attendee_code: other_team_order.unique_event_id, profile: team_admin).call
      end

      expect(fetched_attendee).to_not be_present
    end
  end

end
