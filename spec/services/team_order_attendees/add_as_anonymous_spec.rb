require 'rails_helper'

RSpec.describe TeamOrderAttendees::AddAsAnonymous, type: :service, team_order: true do

  let!(:team_admin) { create(:customer_profile, :random, :with_user) }
  let!(:team_order) { create(:order, :team_order, customer_profile: team_admin) }

  let(:attendee_params) { { first_name: Faker::Name.first_name, last_name: Faker::Name.last_name, email: Faker::Internet.email } }

  before do
    # mock attendee invite email
    email_sender = delayed_email_sender = double(TeamOrderAttendees::Emails::SendInviteEmail)
    allow(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock team admin registration email
    admin_email_sender = delayed_admin_email_sender = double(TeamOrders::Emails::SendAdminRegistrationEmail)
    allow(TeamOrders::Emails::SendAdminRegistrationEmail).to receive(:new).and_return(admin_email_sender)
    allow(admin_email_sender).to receive(:delay).and_return(delayed_admin_email_sender)
    allow(delayed_admin_email_sender).to receive(:call).and_return(true)
  end

  it 'creates a new event attendee for team admin of the supplier order' do
    attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params).call

    expect(attendee_attacher).to be_success
    created_event_attendee = attendee_attacher.event_attendee
    expect(created_event_attendee).to be_present
    expect(created_event_attendee.first_name).to eq(attendee_params[:first_name])
    expect(created_event_attendee.last_name).to eq(attendee_params[:last_name])
    expect(created_event_attendee.email).to eq(attendee_params[:email])
    expect(created_event_attendee.team_admin).to eq(team_admin)
  end

  it 'attaches the new event attendee for team admin to the team order' do
    attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params).call

    expect(attendee_attacher).to be_success
    created_team_order_attendee = attendee_attacher.team_order_attendee
    expect(created_team_order_attendee).to be_present
    expect(created_team_order_attendee.name).to eq("#{attendee_params[:first_name]} #{attendee_params[:last_name]}")
    expect(created_team_order_attendee.email).to eq(attendee_params[:email])
    expect(created_team_order_attendee).to be_anonymous
    expect(created_team_order_attendee.order).to eq(team_order)
  end

  it 'sends an invite email to the newly attached attendee' do
    expect(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).with(team_order_attendee: anything)

    attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params).call
    expect(attendee_attacher).to be_success
  end

  it 'doesn\'t sends an invite email to the newly attached attendee if specified' do
    expect(TeamOrderAttendees::Emails::SendInviteEmail).to_not receive(:new)

    attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params, notify_attendee: false).call
    expect(attendee_attacher).to be_success
  end

  context 'with team order attendee levels', team_order_levels: true do
    let!(:team_order_level) { create(:team_order_level, :random, team_order_detail: team_order.team_order_detail) }

    let!(:attendee_params_with_levels) { attendee_params.merge({ level_id: team_order_level.id }) }

    it 'attaches the event attendee to the team order with the passed in team order level' do
      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params_with_levels).call

      expect(attendee_attacher).to be_success
      created_team_order_attendee = attendee_attacher.team_order_attendee
      expect(created_team_order_attendee.level).to eq(team_order_level)
    end
  end

  context 'registering with the team admin email' do
    let!(:admin_attendee_params) { attendee_params.merge({ email: team_admin.email }) }

    it 'doesn\'t create a new event attendee for team admin and gives a warning' do
      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: admin_attendee_params).call

      expect(attendee_attacher).to_not be_success
      expect(attendee_attacher.warnings).to include('You are already registered as an attendee for this event, please check your email for an order invite link')
    end

    it 'does not create a new event attendee for the team admin' do
      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: admin_attendee_params).call

      expect(attendee_attacher).to_not be_success

      expect(team_admin.reload.event_attendees.map(&:email)).to_not include(admin_attendee_params[:email])
    end

    it 'sends an team admin registration email' do
      expect(TeamOrders::Emails::SendAdminRegistrationEmail).to receive(:new).with(team_order: team_order, team_admin: team_admin)

      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: admin_attendee_params).call
      expect(attendee_attacher).to_not be_success
    end
  end

  context 'with existing event attendee with same email' do
    let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin, email: attendee_params[:email]) }

    it 'does not create a new event attendee record or update the name' do
      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params).call

      expect(attendee_attacher).to be_success
      attacher_event_attendee = attendee_attacher.event_attendee
      expect(attacher_event_attendee).to be_present
      expect(attacher_event_attendee.id).to eq(event_attendee.id)
      expect(attacher_event_attendee.first_name).to_not eq(attendee_params[:first_name])
      expect(attacher_event_attendee.last_name).to_not eq(attendee_params[:last_name])
      expect(attacher_event_attendee.email).to eq(attendee_params[:email])
      expect(attacher_event_attendee.team_admin).to eq(team_admin)
    end

    it 'it attaches the exiting event attendee to the team order' do
      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params).call

      expect(attendee_attacher).to be_success
      created_team_order_attendee = attendee_attacher.team_order_attendee
      expect(created_team_order_attendee).to be_present
      expect(created_team_order_attendee.order).to eq(team_order)
      expect(created_team_order_attendee.event_attendee).to eq(event_attendee)
      expect(created_team_order_attendee).to be_anonymous
    end

    it 'attaches the existing event attendee with same case-insensitive email to team order' do
      random_case_email = attendee_params[:email].split('').map{|char| [char.upcase, char.downcase].sample }.join
      event_attendee.update_column(:email, random_case_email)

      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params).call

      expect(attendee_attacher).to be_success
      created_team_order_attendee = attendee_attacher.team_order_attendee
      expect(created_team_order_attendee).to be_present
      expect(created_team_order_attendee.order).to eq(team_order)
      expect(created_team_order_attendee.event_attendee).to eq(event_attendee)
      expect(created_team_order_attendee).to be_anonymous
    end

    context 'with an already attached event attendee (team order attendee)' do
      let!(:team_order_attendee) { create(:team_order_attendee, :random, event_attendee: event_attendee, order: team_order) }

      it 'does not create a new team attendee record' do
        attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params).call

        expect(attendee_attacher).to_not be_success
        expect(attendee_attacher.warnings).to include('You are already registered as an attendee for this event, please check your email for an order invite link or contact your team admin')
      end

      it 'still sends an invite email to the already attached attendee' do
        expect(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).with(team_order_attendee: team_order_attendee)

        attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: attendee_params).call
        expect(attendee_attacher).to_not be_success
      end
    end
  end

  context 'for a package order (including a recurring team order)', package_orders: true do
    let!(:package_id) { SecureRandom.uuid }
    let!(:team_order2) { create(:order, :team_order, customer_profile: team_admin) }
    let!(:team_order3) { create(:order, :team_order, customer_profile: team_admin) }
    let!(:package_attendee_params) { attendee_params.merge({ package_id: package_id }) }

    let!(:is_recurring_team_order) { [true, false].sample }

    before do
      # convert to package orders
      [team_order, team_order2, team_order3].each do |order|
        order.team_order_detail.update_column(:package_id, package_id)
        if is_recurring_team_order
          order.update_column(:order_variant, 'recurring_team_order')
        end
        order.reload

        package_email_sender = package_delayed_email_sender = double(TeamOrderAttendees::Emails::SendPackageInviteEmail)
        allow(TeamOrderAttendees::Emails::SendPackageInviteEmail).to receive(:new).and_return(package_email_sender)
        allow(package_email_sender).to receive(:delay).and_return(package_delayed_email_sender)
        allow(package_delayed_email_sender).to receive(:call).and_return(true)
      end
    end

    it 'only attaches the current team order if package id does not match the team order package id' do
      random_package_id = SecureRandom.uuid
      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: package_attendee_params.merge({ package_id: random_package_id })).call

      expect(attendee_attacher).to be_success

      created_event_attendee = attendee_attacher.event_attendee
      created_team_order_attendees = created_event_attendee.team_order_attendees

      expect(created_team_order_attendees).to be_present
      expect(created_team_order_attendees.map(&:order)).to include(team_order)
      expect(created_team_order_attendees.map(&:order)).to_not include(team_order2, team_order3)
    end

    it 'does not the attach anonymous attendee to all linked team orders' do
      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: package_attendee_params).call

      expect(attendee_attacher).to be_success

      created_event_attendee = attendee_attacher.event_attendee
      created_team_order_attendees = created_event_attendee.team_order_attendees

      expect(created_team_order_attendees).to be_present
      expect(created_team_order_attendees.size).to eq(1)
      expect(created_team_order_attendees.map(&:order)).to include(team_order)
      expect(created_team_order_attendees.map(&:order)).to_not include(team_order2, team_order3)
    end

    it 'does not send an individual invite email to the newly attached attendee' do
      expect(TeamOrderAttendees::Emails::SendInviteEmail).to_not receive(:new)

      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: package_attendee_params).call
      expect(attendee_attacher).to be_success
    end

    it 'sends a package invite email to the newly attached attendee and lists all existing package orders' do
      expect(TeamOrderAttendees::Emails::SendPackageInviteEmail).to receive(:new).with(package_order_attendee: anything, package_orders: nil)

      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: package_attendee_params).call
      expect(attendee_attacher).to be_success
    end

    it 'fails attachment but still sends a package invite email to the an already attached attendee' do
      event_attendee = create(:event_attendee, :random, team_admin: team_admin, email: attendee_params[:email])
      create(:team_order_attendee, :random, event_attendee: event_attendee, order: team_order) # create team order attendee for event attendee

      expect(TeamOrderAttendees::Emails::SendPackageInviteEmail).to receive(:new).with(package_order_attendee: anything)

      attendee_attacher = TeamOrderAttendees::AddAsAnonymous.new(team_order: team_order, attendee_params: package_attendee_params).call
      expect(attendee_attacher).to_not be_success
      expect(attendee_attacher.warnings).to include('You are already registered as an attendee for this event, please check your email for an order invite link or contact your team admin')
    end
  end

end
