require 'rails_helper'

RSpec.describe TeamOrderAttendees::FetchWithinPackage, type: :service do
  let!(:package_id) { SecureRandom.hex(7) }
  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }

  let!(:team_order1) { create(:order, :package_team_order, customer_profile: team_admin, package_id: package_id) }
  let!(:team_order_attendee1) { create(:team_order_attendee, :random, event_attendee: event_attendee, order: team_order1) }

  let!(:team_order2) { create(:order, :package_team_order, customer_profile: team_admin, package_id: package_id) }

  let!(:team_order_params) { { code: team_order_attendee1.uniq_code, event_id: team_order2.unique_event_id } }

  let!(:is_recurring_team_order) { [true, false].sample }

  before do
    if is_recurring_team_order
      [team_order1, team_order2].each do |team_order|
        team_order.update_column(:order_variant, 'recurring_team_order')
      end
    end
  end

  it 'returns a newly created authorized team order attendee attached to the subsequent team order within the package' do
    team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

    expect(team_order_attendee).to be_present
    expect(team_order_attendee).to be_persisted
    expect(team_order_attendee.event_attendee).to eq(team_order_attendee1.event_attendee)
    expect(team_order_attendee.order).to eq(team_order2)
    expect(team_order_attendee).to_not be_anonymous
  end

  it 'doesn\'t send attendee invite email to the newly created team order attendee' do
    expect(TeamOrderAttendees::Emails::SendInviteEmail).to_not receive(:new)

    team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call
    expect(team_order_attendee).to be_present
  end

  it 'returns team order attendee even if team order does not belong to team admin' do
    team_admin2 = create(:customer_profile, :random)
    [team_order1, team_order2].sample.update_column(:customer_profile_id, team_admin2.id)
    team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: team_admin).call

    expect(team_order_attendee).to be_present
  end

  context 'with team order levels', team_order_levels: true do
    let!(:team_order_level11) { create(:team_order_level, :random, team_order_detail: team_order1.team_order_detail) }
    let!(:team_order_level12) { create(:team_order_level, :random, team_order_detail: team_order1.team_order_detail) }

    let!(:team_order_level21) { create(:team_order_level, :random, name: team_order_level12.name, team_order_detail: team_order2.team_order_detail) }
    let!(:team_order_level22) { create(:team_order_level, :random, name: team_order_level11.name, team_order_detail: team_order2.team_order_detail) }

    before do
      team_order_attendee1.update_column(:team_order_level_id, team_order_level11.id)
    end

    it 'set the level for the new team order attendee with the same named level in the last updated attendee record within the package' do
      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_present
      expect(team_order_attendee.level).to eq(team_order_level22)
    end

    it 'only sets the level for the new team order attendee equal to same contacts\'s selected team order attendee level' do
      event_attendee2 = create(:event_attendee, :random, team_admin: team_admin)
      create(:team_order_attendee, :random, event_attendee: event_attendee2, order: team_order1, level: team_order_level12) # create team order attendee with level

      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_present
      expect(team_order_attendee.level).to_not eq(team_order_level21)
      expect(team_order_attendee.level).to eq(team_order_level22)
    end

    context 'with a completely different package with the same event attendee' do
      let!(:team_order3) { create(:order, :package_team_order, customer_profile: team_admin, package_id: SecureRandom.hex(7), order_variant: (is_recurring_team_order ? 'recurring_team_order' : 'team_order')) }
      let!(:team_order_level31) { create(:team_order_level, :random, name: team_order_level12.name, team_order_detail: team_order3.team_order_detail) }
      let!(:team_order_level32) { create(:team_order_level, :random, name: team_order_level11.name, team_order_detail: team_order3.team_order_detail) }
      let!(:team_order_attendee31) { create(:team_order_attendee, :random, event_attendee: event_attendee, order: team_order3, level: team_order_level31) }

      it 'only sets the level for the new team order attendee equal to same contacts\'s selected team order attendee level within the current package' do
        team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

        expect(team_order_attendee).to be_present
        expect(team_order_attendee.level).to_not eq(team_order_level21)
        expect(team_order_attendee.level).to eq(team_order_level22)
      end
    end

    it 'doesn\'t set the level for the new team order attendee if the existing team order attendee does not have a level selected' do
      team_order_attendee1.update_column(:team_order_level_id, nil)
      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_present
      expect(team_order_attendee.level).to be_blank
    end

    it 'doesn\'t set the level for the new team order attendee if the existing team order does not have any levels' do
      team_order2.team_order_levels.each(&:destroy)
      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_present
      expect(team_order_attendee.level).to be_blank
    end

    it 'doesn\'t set the level for the new team order attendee if the existing team order does not have a similarly named level' do
      team_order_level22.update_column(:name, Faker::Name.name)
      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_present
      expect(team_order_attendee.level).to be_blank
    end
  end

  context 'missing attendee/order' do
    it 'returns nil if the params are missing' do
      mangled_order_params = team_order_params.except!(%i[code event_id].sample)
      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: mangled_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_nil
    end

    it 'returns nil if the package attendee does not exist' do
      team_order_params_with_wrong_code =  team_order_params.merge({ code: SecureRandom.hex(7) })

      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params_with_wrong_code, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_nil
    end

    it 'returns nil if the package attendee\'s event attendee has been deactivated' do
      team_order_attendee1.event_attendee.update_column(:active, false)
      
      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_nil
    end

    it 'returns nil if team orders are not a package team orders' do
      [team_order1, team_order2].sample.team_order_detail.update_column(:package_id, nil) # convert either to a normal team order
      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_nil
    end

    it 'returns nil if team order package ids are a mismatch' do
      [team_order1, team_order2].sample.team_order_detail.update_column(:package_id, SecureRandom.hex(7))
      team_order_attendee = TeamOrderAttendees::FetchWithinPackage.new(team_order_params: team_order_params, profile: [team_admin, nil].sample).call

      expect(team_order_attendee).to be_nil
    end
  end

end
