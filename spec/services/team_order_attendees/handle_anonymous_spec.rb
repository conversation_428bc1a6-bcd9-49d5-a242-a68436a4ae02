require 'rails_helper'

RSpec.describe TeamOrderAttendees::HandleAnonymous, type: :service, team_order: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:team_order) { create(:order, :team_order, customer_profile: team_admin) }

  let!(:event_attendee) { create(:event_attendee, :random) }
  let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order, event_attendee: event_attendee, status: 'invited', anonymous: true) }

  before do
    totals_calculator = double(Orders::CalculateCustomerTotals)
    allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(totals_calculator)
    allow(totals_calculator).to receive(:call).and_return(true)
  end

  context 'approval' do
    let!(:anonymous_status) { %w[approve auto_approve].sample }

    it 'can approve an anonymous attendee' do
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to be_success
      expect(team_order_attendee.reload).to_not be_anonymous
      expect(team_order_attendee.status).to eq('invited') # same status as before
    end

    it 'recalculates the customer totals if team order attendee status is ordered' do
      team_order_attendee.update_column(:status, 'ordered')
      expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: team_order, save_totals: true)
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to be_success
    end

    it 'does not recalculate order customer totals if attendee status is not ordered' do
      team_order_attendee.update_column(:status, 'pending')
      expect(Orders::CalculateCustomerTotals).to_not receive(:new)
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to be_success
    end
  end

  context 'reject' do
    let!(:anonymous_status) { 'reject' }

    before do
      attendee_detacher = double(TeamOrderAttendees::Detach)
      allow(TeamOrderAttendees::Detach).to receive(:new).and_return(attendee_detacher)
      allow(attendee_detacher).to receive(:call).and_return(true)
    end

    it 'can reject an anonymous attendee' do
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to be_success
      expect(team_order_attendee.reload).to_not be_anonymous
    end

    it 'detaches the attendee from the team order' do
      expect(TeamOrderAttendees::Detach).to receive(:new).with(team_order_attendee: team_order_attendee, team_admin: team_admin)
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to be_success
      # expect(team_order_attendee.status).to eq('cancelled') # taken care in the detach attendee service object
    end

    it 'does not recalculate order customer totals if attendee status is not confirmed' do
      expect(Orders::CalculateCustomerTotals).to_not receive(:new)
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to be_success
    end
  end

  context 'errors' do
    let!(:anonymous_status) { %w[approve auto_approve reject].sample }

    it 'cannot handle a missing team order attendee' do
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: nil, team_admin: team_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to_not be_success
      expect(attendee_handler.errors).to include('Cannot handle a missing team order attendee')
    end

    it 'cannot handle an attendee without a team admin' do
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: nil, anonymous_status: anonymous_status).call

      expect(attendee_handler).to_not be_success
      expect(attendee_handler.errors).to include('Cannot handle a missing team order attendee')
    end

    it 'cannot handle an attendee which is not anonymous' do
      team_order_attendee.update_column(:anonymous, false)
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to_not be_success
      expect(attendee_handler.errors).to include('Attendee is not an anonymous attendee')
    end

    it 'cannot handle an attendee not belonging to the team admin' do
      another_admin = create(:customer_profile, :random)
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: another_admin, anonymous_status: anonymous_status).call

      expect(attendee_handler).to_not be_success
      expect(attendee_handler.errors).to include('You do not have access to this attendee')
    end
  end

  context 'within a package order' do
    let!(:team_order2) { create(:order, :team_order, customer_profile: team_admin) }
    let!(:team_order3) { create(:order, :team_order, customer_profile: team_admin) }

    let!(:team_order_attendee2) { create(:team_order_attendee, :random, order: team_order2, event_attendee: event_attendee, status: 'invited', anonymous: true) }
    let!(:team_order_attendee3) { create(:team_order_attendee, :random, order: team_order2, event_attendee: event_attendee, status: 'invited', anonymous: true) }

    before do
      # convert to package orders
      package_id = SecureRandom.uuid
      [team_order, team_order2, team_order3].each do |order|
        order.team_order_detail.update_column(:package_id, package_id)
        order.reload
      end
    end

    it 'approves the team order attendee in each order within the package' do
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: 'approve').call

      expect(attendee_handler).to be_success

      expect(team_order_attendee.reload).to_not be_anonymous
      expect(team_order_attendee.status).to eq('invited') # same status as before

      expect(team_order_attendee2.reload).to_not be_anonymous
      expect(team_order_attendee2.status).to eq('invited') # same status as before

      expect(team_order_attendee3.reload).to_not be_anonymous
      expect(team_order_attendee3.status).to eq('invited') # same status as before
    end

    it 'rejects the team order attendee in each order within the package' do
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: 'reject').call

      expect(attendee_handler).to be_success

      expect(team_order_attendee.reload).to_not be_anonymous
      expect(team_order_attendee.status).to eq('cancelled')

      expect(team_order_attendee2.reload).to_not be_anonymous
      expect(team_order_attendee2.status).to eq('cancelled')

      expect(team_order_attendee3.reload).to_not be_anonymous
      expect(team_order_attendee3.status).to eq('cancelled')
    end

    it 'doesn\'t approve the team order attendee in each order within the package if auto approved' do
      attendee_handler = TeamOrderAttendees::HandleAnonymous.new(team_order_attendee: team_order_attendee, team_admin: team_admin, anonymous_status: 'auto_approve').call

      expect(attendee_handler).to be_success

      expect(team_order_attendee.reload).to_not be_anonymous
      expect(team_order_attendee.status).to eq('invited') # same status as before

      expect(team_order_attendee2.reload).to be_anonymous # still anonymous
      expect(team_order_attendee3.reload).to be_anonymous # still anonymous
    end
  end

end
