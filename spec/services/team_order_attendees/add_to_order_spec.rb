require 'rails_helper'

RSpec.describe TeamOrderAttendees::AddToOrder, type: :service, team_orders: true do

  let!(:team_admin) { create(:customer_profile, :random, :with_user) }
  let!(:team_order) { create(:order, :team_order, customer_profile: team_admin) }

  let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }

  before do
    team_order.update_columns({
      order_variant: 'team_order',
      status: 'pending',
    })
    email_sender = delayed_email_sender = double(TeamOrderAttendees::Emails::SendInviteEmail)
    allow(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  it 'attaches a event attendee to the specified team order' do
    attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee).call

    expect(attendee_attacher).to be_success
    attached_team_order_attendee = attendee_attacher.team_order_attendee

    expect(attached_team_order_attendee).to be_present
    expect(attached_team_order_attendee.order).to eq(team_order)
    expect(attached_team_order_attendee.event_attendee).to eq(event_attendee)
  end

  it 'attaches a new event attendee with default values' do
    attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee).call

    expect(attendee_attacher).to be_success
    attached_team_order_attendee = attendee_attacher.team_order_attendee

    expect(attached_team_order_attendee).to be_present
    expect(attached_team_order_attendee.status).to eq('invited')
    expect(attached_team_order_attendee.uniq_code).to be_present
  end

  it 'sends an invite email when inviting a new event attendee' do
    expect(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new)

    attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee).call

    expect(attendee_attacher).to be_success
  end

  context 'with an already attached attendee' do
    let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order, event_attendee: event_attendee, status: 'invited') }

    it 'does nothing to an already invited attendee' do
      expect(TeamOrderAttendees::Emails::SendInviteEmail).to_not receive(:new) # does not send invite email again

      attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee).call

      expect(attendee_attacher).to be_success
      attached_team_order_attendee = attendee_attacher.team_order_attendee

      expect(attached_team_order_attendee).to be_present
      expect(attached_team_order_attendee.id).to eq(team_order_attendee.id)
      expect(attached_team_order_attendee.status).to eq('invited')
    end

    it 're-invites a cancelled attendee' do
      team_order_attendee.update_column(:status, 'cancelled')
      saved_uniq_code = team_order_attendee.uniq_code.dup
      expect(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new) # send invitation email again

      attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee).call

      expect(attendee_attacher).to be_success
      attached_team_order_attendee = attendee_attacher.team_order_attendee

      expect(attached_team_order_attendee).to be_present
      expect(attached_team_order_attendee.id).to eq(team_order_attendee.id)
      expect(attached_team_order_attendee.status).to eq('invited')
      expect(attached_team_order_attendee.uniq_code).to_not eq(saved_uniq_code) # generates a new code
    end
  end

  context 'with team order levels', team_order_levels: true do
    let!(:team_order_level) { create(:team_order_level, :random, team_order_detail: team_order.team_order_detail) }

    it 'attaches the event attendee to the specified team order with the passed in level' do
      attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee, level_id: team_order_level.id).call

      expect(attendee_attacher).to be_success
      attached_team_order_attendee = attendee_attacher.team_order_attendee
      expect(attached_team_order_attendee.level).to eq(team_order_level)
    end

    it 'does not attach the level if the passed in level does not belong to the team order' do
      team_order_level2 = create(:team_order_level, :random)

      attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee, level_id: team_order_level2.id).call

      expect(attendee_attacher).to be_success
      attached_team_order_attendee = attendee_attacher.team_order_attendee
      expect(attached_team_order_attendee.level).to be_blank
    end

    it 'attaches the level if the passed in level does not belong to the passed in team order, but belongs to a team order within the package', skip: 'will need to test for an existing team order package with no levels' do
      team_order2 = create(:order, :package_team_order, package_id: SecureRandom.hex(7), customer_profile: team_admin)
      team_order_level2 = create(:team_order_level, :random, team_order_detail: team_order2.team_order_detail)
      team_order.team_order_detail.update_column(:package_id, team_order2.package_id)

      attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee, level_id: team_order_level2.id).call

      expect(attendee_attacher).to be_success
      attached_team_order_attendee = attendee_attacher.team_order_attendee
      expect(attached_team_order_attendee.level).to eq(team_order_level2)
    end
  end

  context 'errors' do
    it 'does not attach a missing attendee' do
      attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: nil).call

      expect(attendee_attacher).to_not be_success
      expect(attendee_attacher.errors).to include('Cannot attached a missing contact')
    end

    it 'does not attach an attendee which has the same email as the team admin' do
      event_attendee.update_column(:email, team_admin.user.email)
      attendee_attacher = TeamOrderAttendees::AddToOrder.new(team_order: team_order, event_attendee: event_attendee).call

      expect(attendee_attacher).to_not be_success
      expect(attendee_attacher.errors).to include('Cannot attach the attendee with same email as the team admin')
    end
  end
end
