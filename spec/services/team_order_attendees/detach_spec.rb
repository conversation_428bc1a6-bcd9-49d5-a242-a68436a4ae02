require 'rails_helper'

RSpec.describe TeamOrderAttendees::Detach, type: :service, team_orders: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:team_order) { create(:order, :draft, customer_profile: team_admin) }

  let!(:event_attendee) { create(:event_attendee, :random) }
  let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order, event_attendee: event_attendee, status: 'invited') }

  before do
    team_order.update_columns({
      order_variant: 'team_order',
      status: 'pending',
    })
    email_sender = delayed_email_sender = double(TeamOrderAttendees::Emails::SendRemovedFromOrderEmail)
    allow(TeamOrderAttendees::Emails::SendRemovedFromOrderEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  it 'detaches an existing team order attendee from a team_order' do
    attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: team_order_attendee, team_admin: team_admin).call

    expect(attendee_detacher).to be_success
    detached_team_order_attendee = attendee_detacher.team_order_attendee

    expect(detached_team_order_attendee.id).to eq(team_order_attendee.id)
    expect(detached_team_order_attendee.status).to eq('cancelled')
  end

  it 'reset the uniq code for the team attendee' do
    saved_uniq_code = team_order_attendee.uniq_code.dup

    attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: team_order_attendee, team_admin: team_admin).call

    expect(attendee_detacher).to be_success
    detached_team_order_attendee = attendee_detacher.team_order_attendee
    expect(detached_team_order_attendee.uniq_code).to_not eq(saved_uniq_code)
  end

  context 'attendee removal email' do
    it 'does not send a removal email by default' do
      expect(TeamOrderAttendees::Emails::SendRemovedFromOrderEmail).to_not receive(:new)

      attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: team_order_attendee, team_admin: team_admin).call

      expect(attendee_detacher).to be_success
    end

    it 'sends a removal email to the attendee only if specified' do
      expect(TeamOrderAttendees::Emails::SendRemovedFromOrderEmail).to receive(:new).with(team_order_attendee: team_order_attendee)

      attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: team_order_attendee, team_admin: team_admin, notify_attendee: true).call

      expect(attendee_detacher).to be_success
    end
  end

  context 'errors' do
    it 'does not detach a missing team order attendee' do
      attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: nil, team_admin: team_admin).call

      expect(attendee_detacher).to_not be_success
      expect(attendee_detacher.errors).to include('Cannot detach missing attendee')
    end

    it 'does not detach a missing team admin' do
      attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: team_order_attendee, team_admin: nil).call

      expect(attendee_detacher).to_not be_success
      expect(attendee_detacher.errors).to include('You do not have access to this team order')
    end

    it 'does not detach if the team order does not belong to the passed in team admin' do
      another_team_admin = create(:customer_profile, :random)
      attendee_detacher = TeamOrderAttendees::Detach.new(team_order_attendee: team_order_attendee, team_admin: another_team_admin).call

      expect(attendee_detacher).to_not be_success
      expect(attendee_detacher.errors).to include('You do not have access to this team order')
    end
  end
end
