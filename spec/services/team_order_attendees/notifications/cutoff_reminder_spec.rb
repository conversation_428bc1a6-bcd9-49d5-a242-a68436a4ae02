require 'rails_helper'

RSpec.describe TeamOrderAttendees::Notifications::CutoffReminder, type: :service, team_orders: true do

  subject { TeamOrderAttendees::Notifications::CutoffReminder.new(cutoff_time: cutoff_time, time: notification_time).call }

  # No Suppliers / Minimums means the cutoff is same as delivery time
  let!(:delivery_start) { Time.zone.now.beginning_of_day }

  let!(:team_order1) { create(:order, :team_order, name: 'team_order1', delivery_at: delivery_start + 10.hours) }
  let!(:event_attendee1) { create(:event_attendee, :random, first_name: 'Attendee', last_name: '1') }
  let!(:team_order_attendee11) { create(:team_order_attendee, :random, order: team_order1, event_attendee: event_attendee1, status: %w[invited pending].sample) }

  let!(:team_order2) { create(:order, :team_order, name: 'team_order2', delivery_at: delivery_start + 10.hours) }
  let!(:event_attendee2) { create(:event_attendee, :random, first_name: 'Attendee', last_name: '2') }
  let!(:team_order_attendee21) { create(:team_order_attendee, :random, order: team_order2, event_attendee: event_attendee2, status: %w[invited pending].sample) }

  let!(:team_order3) { create(:order, :team_order, name: 'team_order3', delivery_at: delivery_start + 10.hours) }
  let!(:event_attendee3) { create(:event_attendee, :random, first_name: 'Attendee', last_name: '3') }
  let!(:team_order_attendee31) { create(:team_order_attendee, :random, order: team_order3, event_attendee: event_attendee3, status: %w[invited pending].sample) }

  before do
    email_sender = double(TeamOrderAttendees::Emails::SendCutOffEmail)
    allow(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call).and_return(true)
  end

  let!(:cutoff_time) { %w[4hr 2hr 30m].sample }
  let!(:notification_time) do
    number_of_hours = {
      '4hr' => 6,
      '2hr' => 8,
      '30m' => 9,
    }[cutoff_time]
    delivery_start + number_of_hours.hours
  end

  before do
    puts "Notifying for #{cutoff_time} @ #{notification_time}" if ENV['VERBOSE']
  end

  it 'send (4hr/2hr/30m) cutoff emails to invited and pending (non-anonymous) attendees of a team order within cutoff' do
    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)
    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)

    subject
  end

  it 'does not send cutoff emails attendees of a team order delivered before notification time' do
    delivery_time = [(delivery_start - 1.day), (delivery_start + 5.hours)].sample
    team_order1.update_column(:delivery_at, delivery_time)

    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
    subject
  end

  it 'does not send cutoff emails attendees of a team order not within cutoff timeframe' do
    delivery_time = [(delivery_start + 1.day), (delivery_start + 12.hours), (team_order2.delivery_at + 1.hour)].sample
    team_order2.update_column(:delivery_at, delivery_time)

    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)
    subject
  end

  it 'does not send cutoff emails attendees of a team order not within attendee threshold of the cutoff timeframe' do
    team_order3.update_column(:delivery_at, (team_order3.delivery_at + 31.minutes)) # 30 minutes threshold from actual cutoff

    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)
    subject
  end

  it 'does not send cutoff emails to already ordered attendees of a team order within cutoff' do
    team_order_attendee21.update_column(:status, 'ordered')

    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)
    subject
  end

  it 'does not send cutoff emails to already notified attendees of a team order within cutoff' do
    notification_attribute = "cutoff_#{cutoff_time}_reminder"
    team_order_attendee31.update_column(notification_attribute.to_sym, Time.zone.now)

    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)
    subject
  end

  it 'sends (4hr/2hr/30m) cutoff emails to pending anonymous attendees also' do
    team_order_attendee11.update_column(:anonymous, true)

    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
    subject
  end

  context 'for a recurring team orders' do
    let!(:package_id) { SecureRandom.hex(7) }

    let!(:cutoff_time) { %w[4hr 30m].sample }
    let!(:notification_time) do
      number_of_hours = {
        '4hr' => 6,
        '30m' => 9,
      }[cutoff_time]
      delivery_start + number_of_hours.hours
    end

    before do
      ####
      ### * When AttendeeCutoff calls SendAttendeeCutOffEmail for a recurring team order attendee from a different team order
      ### * it creates a new team order attendee using TeamOrderAttendees::FetchWithinPackage
      ### * We skip this using the SKIP_ATTENDEE_CREATION_FOR_TEST ENV variable as we couldn't write expect statements for sending email to a not-yet created team order attendee
      ####
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:skip_attendee_creation_for_test).and_return(true)

      [team_order1, team_order2, team_order3].each do |order|
        order.update_column(:order_variant, 'recurring_team_order')
        order.team_order_detail.update_column(:package_id, package_id)
      end
    end

    it 'sends (4hr/30m) cutoff emails to pending attendees of a recurring team order' do
      expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
      expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)
      expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)

      subject
    end

    context 'for a 24hr cutoff notification' do
      let!(:delivery_start) do
        today = Time.zone.now.beginning_of_day
        if today.wday == 5 # is Friday
          today + 3.days # team order delivered on Monday
        else
          today + 1.day
        end
      end
      let!(:cutoff_time) { '24hr' }
      let!(:notification_time) { delivery_start - 22.hours }

      it 'sends a 24hr cutoff emails to pending attendees of a recurring team order' do
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)

        subject
      end
    end

    it 'does not send 2hr cutoff emails to pending attendees of a recurring team order' do
      non_compliant_cutoff_time = '2hr'
      expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: non_compliant_cutoff_time)
      expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: non_compliant_cutoff_time)
      expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: non_compliant_cutoff_time)

      subject
    end

    context 'with ordered package attendees' do
      before do
        [team_order_attendee21, team_order_attendee31].each do |attendee|
          attendee.update_column(:status, 'ordered')
        end
      end

      it 'sends cutoff emails to non-attached pending attendees of a recurring team order, only if it is marked as ordered in a previous package order' do
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)

        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)

        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)

        subject
      end

      it 'doesn\'t sends cutoff emails to non-attached package team order attendees but are in-active' do
        team_order_attendee31.event_attendee.update_column(:active, false)

        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)

        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time)

        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time)
        expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time)

        subject
      end

      context 'as the same event attendee' do
        let!(:event_attendee) { create(:event_attendee, :random) }
        before do
          [team_order_attendee11, team_order_attendee21].each do |team_order_attendee|
            team_order_attendee.update_column(:event_attendee_id, event_attendee.id)
          end
        end

        it 'doesn\'t sent multiple cutoff emails to the same team order attendee' do
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # duplicate but belonging to current team order and ordered
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # duplicate event attendee but not belonging to current order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # ordered attendee from another package order

          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # duplicate event attendee not belonging to current order, but not ordered in another order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # duplicate but belonging to current team order and ordered
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # ordered attendee from another package order

          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # duplicate event attendee not belonging to current order, but not ordered in another order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # duplicate event attendee but not belonging to current order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # ordered in current order

          subject
        end

        it 'doesn\'t send a cutoff email to an attendee who is already marked as declined/cancelled in the current team order' do
          team_order_attendee11.update_column(:status, %w[declined cancelled].sample)
          team_order_attendee31.update_column(:status, %w[declined cancelled].sample)

          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # duplicate but belonging to current team order and ordered
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # duplicate event attendee but not belonging to current order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # declined attendee from another order

          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # declined attendee from another order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # duplicate but belonging to current team_order2 and marked as ordered
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # declined attendee from another order

          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # declined attendee from another order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # ordered attendee from another package order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # declined attendee from another order

          subject
        end

        it 'doesn\'t send a cutoff email to an attendee who is already marked as notified in the current team order' do
          notification_attribute = "cutoff_#{cutoff_time}_reminder"
          team_order_attendee11.update_column(notification_attribute.to_sym, Time.zone.now)
          team_order_attendee31.update_column(notification_attribute.to_sym, Time.zone.now)

          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # duplicate but belonging to current team order notified
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # duplicate ordered attendee from another package order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order1, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # ordered attendee from another package order

          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # duplicate ordered attendee from another package order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # duplicate but belonging to current team_order2 but already notified
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order2, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # ordered attendee from another package order

          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee11, cutoff_time: cutoff_time) # duplicate ordered attendee from another package order but not ordered
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee21, cutoff_time: cutoff_time) # duplicate ordered attendee from another package order
          expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, team_order_attendee: team_order_attendee31, cutoff_time: cutoff_time) # attendee belonging to current attendee but already notified

          subject
        end
      end # as the same team order attendee

      context 'attendee creation mockup' do
        before do
          allow_any_instance_of(Object).to receive(:yordar_credentials).with(:skip_attendee_creation_for_test).and_return(false)

          attendee_fetcher = double(TeamOrderAttendees::FetchWithinPackage)
          allow(TeamOrderAttendees::FetchWithinPackage).to receive(:new).and_return(attendee_fetcher)
          allow(attendee_fetcher).to receive(:call).and_return(true)
        end

        it 'it generates team order attendee records for package team orders not containing the team order attendee record, only if marked as ordered in previous orders' do
          # team_order_attendee11
          team_order_params = { code: team_order_attendee11.uniq_code, event_id: team_order2.unique_event_id }
          expect(TeamOrderAttendees::FetchWithinPackage).to_not receive(:new).with(team_order_params: team_order_params)
          team_order_params = { code: team_order_attendee11.uniq_code, event_id: team_order3.unique_event_id }
          expect(TeamOrderAttendees::FetchWithinPackage).to_not receive(:new).with(team_order_params: team_order_params)

          # team_order_attendee21
          team_order_params = { code: team_order_attendee21.uniq_code, event_id: team_order1.unique_event_id }
          expect(TeamOrderAttendees::FetchWithinPackage).to receive(:new).with(team_order_params: team_order_params)
          team_order_params = { code: team_order_attendee21.uniq_code, event_id: team_order3.unique_event_id }
          expect(TeamOrderAttendees::FetchWithinPackage).to receive(:new).with(team_order_params: team_order_params)

          # team_order_attendee31
          team_order_params = { code: team_order_attendee31.uniq_code, event_id: team_order1.unique_event_id }
          expect(TeamOrderAttendees::FetchWithinPackage).to receive(:new).with(team_order_params: team_order_params)
          team_order_params = { code: team_order_attendee31.uniq_code, event_id: team_order2.unique_event_id }
          expect(TeamOrderAttendees::FetchWithinPackage).to receive(:new).with(team_order_params: team_order_params)

          subject
        end
      end # attendee creation mock

    end # as ordered package attendees

  end # for a recurring team order

end
