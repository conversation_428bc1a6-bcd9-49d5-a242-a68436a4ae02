require 'rails_helper'

RSpec.describe Documents::GetNextVersion, type: :service, documents: true do

  # possible documentables
  let!(:order) { create(:order, :delivered) }
  let!(:invoice) { create(:invoice, :random) }
  let!(:customer) { create(:customer_profile, :random) }
  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:documentable) { [order, invoice, customer, supplier].sample }

  it 'returns the version as 1 for a documentable with no documents' do
    next_version = Documents::GetNextVersion.new(documentable: documentable, kind: Document::VALID_KINDS.sample).call

    expect(next_version).to eq(1)
  end

  context 'with an existing document' do
    let!(:document) { create(:document, :random, documentable: documentable, version: rand(2..10)) }

    it 'returns the next version for the same kind of document for the documentable' do
      next_version = Documents::GetNextVersion.new(documentable: document.documentable, kind: document.kind).call

      expect(next_version).to eq(document.version + 1)
    end

    it 'returns version as 1 if the same kind of document does not exist' do
      new_kind = (Document::VALID_KINDS - [document.kind]).sample
      next_version = Documents::GetNextVersion.new(documentable: document.documentable, kind: new_kind).call

      expect(next_version).to eq(1)
    end

    it 'returns version as 1 if same kind of document does not exist for the documentable' do
      other_documentable = ([order, invoice, customer, supplier] - [documentable]).sample
      next_version = Documents::GetNextVersion.new(documentable: other_documentable, kind: document.kind).call

      expect(next_version).to eq(1)
    end
  end

  context 'for a supplier order details document (or supplier order delivery details ) of a recurring order' do
    let!(:supplier) { create(:supplier_profile, :random) }
    let!(:document_kind) { Documents::GetNextVersion::RECURRING_ORDER_DOCUMENT_KINDS.sample }

    let!(:order1) { create(:order, :delivered) }
    let!(:order_supplier1) { create(:order_supplier, :random, order: order1, supplier_profile: supplier) }
    let!(:document1) { create(:document, :random, documentable: order_supplier1, kind: document_kind, version: 3) }

    let!(:order2) { create(:order, :delivered) }
    let!(:order_supplier2) { create(:order_supplier, :random, order: order2, supplier_profile: supplier) }

    let!(:order3) { create(:order, :delivered) }
    let!(:order_supplier3) { create(:order_supplier, :random, order: order3, supplier_profile: supplier) }

    before do
      # make orders recurring
      [order1, order2].each do |recurring_order|
        recurring_order.update_columns(order_type: 'recurring', recurrent_id: order1.id, template_id: order1.id)
      end
    end

    it 'returns the next incremental version for an OrderSupplier with existing Document' do
      next_version = Documents::GetNextVersion.new(documentable: order_supplier1, kind: document_kind).call

      expect(next_version).to eq(document1.version + 1)
    end

    it 'returns the version of 2 for OrderSupplier of an amended (non-new) recurring order, without any existing Document' do
      next_version = Documents::GetNextVersion.new(documentable: order_supplier2, kind: document_kind).call

      expect(next_version).to eq(2)
    end

    it 'returns the version of 1 for an OrderSupplier of new recurring order, without any existing Document' do
      order2.update_column(:status, 'new')
      next_version = Documents::GetNextVersion.new(documentable: order_supplier2, kind: document_kind).call

      expect(next_version).to eq(1)
    end

    it 'returns the version of 1 for an OrderSupplier of a non-recurring order without any existing Document' do
      next_version = Documents::GetNextVersion.new(documentable: order_supplier3, kind: document_kind).call

      expect(next_version).to eq(1)
    end
  end

end
