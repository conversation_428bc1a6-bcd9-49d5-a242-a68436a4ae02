require 'rails_helper'

RSpec.describe Documents::Generate::RecipientGeneratedInvoice, type: :service, documents: true, orders: true, notifications: true, rgi: true do

  let!(:supplier_invoice) { create(:supplier_invoice, :random) }

  let!(:document_url) { 'https://rgi-document.com' }

  before do
    # mock pdf generators
    rgi_generator = double(Invoices::RecipientGeneratedInvoice)
    allow(Invoices::RecipientGeneratedInvoice).to receive(:new).and_return(rgi_generator)
    allow(rgi_generator).to receive(:generate).and_return(true)
    allow(rgi_generator).to receive(:render_file).and_return(true)
    allow(rgi_generator).to receive(:report_path).and_return('report-path')
    allow(rgi_generator).to receive(:file_path).and_return('file-path')

    # mock Cloudinary uploader
    cloudinary_reponse = { 'secure_url' => document_url }
    allow(Cloudinary::Uploader).to receive(:upload).and_return(cloudinary_reponse)

    # mock Document Upserter
    mock_document = create(:document, :random)
    document_upserter = double(Documents::Upsert)
    allow(Documents::Upsert).to receive(:new).and_return(document_upserter)
    upserter_response = OpenStruct.new(
      success?: true,
      document: mock_document
    )
    allow(document_upserter).to receive(:call).and_return(upserter_response) # return mock document
  end

  it 'makes a request to generate the RGI document' do
    expect(Invoices::RecipientGeneratedInvoice).to receive(:new).with(invoice: supplier_invoice)

    document = Documents::Generate::RecipientGeneratedInvoice.new(invoice: supplier_invoice).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record and attach it to the supplier invoice' do
    expect(Documents::Upsert).to receive(:new).with(documentable: supplier_invoice, document_params: anything)

    document = Documents::Generate::RecipientGeneratedInvoice.new(invoice: supplier_invoice).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
    expected_document_params = {
      name: 'Recipient Generated Invoice',
      kind: 'recipient_generated_invoice',
      url: document_url,
      version: 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::RecipientGeneratedInvoice.new(invoice: supplier_invoice).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the the next version' do
    existing_document = create(:document, :random, documentable: supplier_invoice, version: 2, kind: 'recipient_generated_invoice')
    expected_document_params = {
      name: 'Recipient Generated Invoice',
      kind: 'recipient_generated_invoice',
      url: document_url,
      version: existing_document.version + 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::RecipientGeneratedInvoice.new(invoice: supplier_invoice).call
    expect(document).to be_present
  end

  it 'makes a request to upload the document via Cloudinary', skip: 'already tested in other generator spec'
  it 'returns the generated document', skip: 'already tested in other generator spec'

end
