require 'rails_helper'

RSpec.describe Documents::Generate::CustomerOrderSummary, type: :service, documents: true, orders: true, notifications: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order1) { create(:order, :delivered, customer_profile: customer) }
  let!(:order2) { create(:order, :delivered, customer_profile: customer) }

  let!(:customer_orders) { [order1, order2] }

  let!(:summary_day) { customer_orders.sample.delivery_at }

  let!(:document_url) { 'https://order-details-document.com' }

  before do
    # mock pdf generators
    summary_pdf_generator = double(Customers::OrderSummary)
    allow(Customers::OrderSummary).to receive(:new).and_return(summary_pdf_generator)
    allow(summary_pdf_generator).to receive(:generate).and_return(true)
    allow(summary_pdf_generator).to receive(:render_file).and_return(true)
    allow(summary_pdf_generator).to receive(:report_path).and_return('report-path')
    allow(summary_pdf_generator).to receive(:file_path).and_return('file-path')

    # mock Cloudinary uploader
    cloudinary_reponse = { 'secure_url' => document_url }
    allow(Cloudinary::Uploader).to receive(:upload).and_return(cloudinary_reponse)

    # mock Document Upserter
    mock_document = create(:document, :random)
    document_upserter = double(Documents::Upsert)
    allow(Documents::Upsert).to receive(:new).and_return(document_upserter)
    upserter_response = OpenStruct.new(
      success?: true,
      document: mock_document
    )
    allow(document_upserter).to receive(:call).and_return(upserter_response) # return mock document
  end

  it 'makes a request to generate the Customer Order Summary document' do
    expect(Customers::OrderSummary).to receive(:new).with(customer: customer, orders: customer_orders, summary_day: summary_day)

    document = Documents::Generate::CustomerOrderSummary.new(customer: customer, orders: customer_orders, summary_day: summary_day).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record and attach it to customer' do
    expect(Documents::Upsert).to receive(:new).with(documentable: customer, document_params: anything)

    document = Documents::Generate::CustomerOrderSummary.new(customer: customer, orders: customer_orders, summary_day: summary_day).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
    expected_document_params = {
      name: "order-summary-#{summary_day.strftime('%Y%m%d')}",
      kind: 'customer_daily_summary',
      url: document_url,
      version: 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::CustomerOrderSummary.new(customer: customer, orders: customer_orders, summary_day: summary_day).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the the next version' do
    existing_document = create(:document, :random, documentable: customer, version: 2, kind: 'customer_daily_summary')
    expected_document_params = {
      name: "order-summary-#{summary_day.strftime('%Y%m%d')}",
      kind: 'customer_daily_summary',
      url: document_url,
      version: existing_document.version + 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::CustomerOrderSummary.new(customer: customer, orders: customer_orders, summary_day: summary_day).call
    expect(document).to be_present
  end

  it 'makes a request to upload the document via Cloudinary', skip: 'already tested in other generator spec'
  it 'returns the generated document', skip: 'already tested in other generator spec'

end
