require 'rails_helper'

RSpec.describe Documents::Generate::TaxInvoice, type: :service, documents: true, orders: true, notifications: true do

  let!(:invoice) { create(:invoice, :random) }

  let!(:document_type) { %w[tax_invoice tax_invoice_spreadsheet tax_invoice_receipt].sample }

  let!(:document_url) { 'https://tax-invoice-document.com' }

  before do
    # mock pdf generators
    invoice_pdf_generator = double(Invoices::TaxInvoice)
    allow(Invoices::TaxInvoice).to receive(:new).and_return(invoice_pdf_generator)
    allow(invoice_pdf_generator).to receive(:generate).and_return(true)
    allow(invoice_pdf_generator).to receive(:render_file).and_return(true)
    allow(invoice_pdf_generator).to receive(:report_path).and_return('report-path')
    allow(invoice_pdf_generator).to receive(:file_path).and_return('file-path')

    invoice_csv_generator = double(Invoices::TaxInvoiceSpreadsheet)
    allow(Invoices::TaxInvoiceSpreadsheet).to receive(:new).and_return(invoice_csv_generator)
    allow(invoice_csv_generator).to receive(:generate).and_return(true)
    allow(invoice_csv_generator).to receive(:render_file).and_return(true)
    allow(invoice_csv_generator).to receive(:report_path).and_return('report-path')
    allow(invoice_csv_generator).to receive(:file_path).and_return('file-path')

    receipt_pdf_generator = double(Invoices::TaxInvoiceReceipt)
    allow(Invoices::TaxInvoiceReceipt).to receive(:new).and_return(receipt_pdf_generator)
    allow(receipt_pdf_generator).to receive(:generate).and_return(true)
    allow(receipt_pdf_generator).to receive(:render_file).and_return(true)
    allow(receipt_pdf_generator).to receive(:report_path).and_return('report-path')
    allow(receipt_pdf_generator).to receive(:file_path).and_return('file-path')

    # mock Cloudinary uploader
    cloudinary_reponse = { 'secure_url' => document_url }
    allow(Cloudinary::Uploader).to receive(:upload).and_return(cloudinary_reponse)

    # mock Document Upserter
    mock_document = create(:document, :random)
    document_upserter = double(Documents::Upsert)
    allow(Documents::Upsert).to receive(:new).and_return(document_upserter)
    upserter_response = OpenStruct.new(
      success?: true,
      document: mock_document
    )
    allow(document_upserter).to receive(:call).and_return(upserter_response) # return mock document
  end

  it 'makes a request to create a document record and attach it to invoice' do
    expect(Documents::Upsert).to receive(:new).with(documentable: invoice, document_params: anything)

    document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
    expect(document).to be_present
  end

  context 'document_type of tax_invoice' do
    let!(:document_type) { 'tax_invoice' }

    it 'makes a request to generate the Tax Invoice document' do
      expect(Invoices::TaxInvoice).to receive(:new).with(invoice: invoice)

      document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
      expected_document_params = {
        name: "Invoice-#{invoice.number}",
        kind: document_type,
        url: document_url,
        version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the the next version' do
      existing_document = create(:document, :random, documentable: invoice, version: 5, kind: document_type)
      expected_document_params = {
        name: "Invoice-#{invoice.number}",
        kind: document_type,
        url: document_url,
        version: existing_document.version + 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      expect(document).to be_present
    end
  end

  context 'document_type of tax_invoice_spreadsheet' do
    let!(:document_type) { 'tax_invoice_spreadsheet' }

    it 'makes a request to generate the Tax Invoice Spreadsheet' do
      expect(Invoices::TaxInvoiceSpreadsheet).to receive(:new).with(invoice: invoice)

      document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
      expected_document_params = {
        name: "Invoice-#{invoice.number}",
        kind: document_type,
        url: document_url,
        version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the the next version' do
      existing_document = create(:document, :random, documentable: invoice, version: 5, kind: document_type)
      expected_document_params = {
        name: "Invoice-#{invoice.number}",
        kind: document_type,
        url: document_url,
        version: existing_document.version + 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      expect(document).to be_present
    end
  end

  context 'for a tax invoice receipt document' do
    let!(:document_type) { 'tax_invoice_receipt' }

    it 'makes a request to generate the Tax Invoice Receipt document' do
      expect(Invoices::TaxInvoiceReceipt).to receive(:new).with(invoice: invoice)

      document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
      expected_document_params = {
        name: "Tax-Invoice-Receipt-#{invoice.number}",
        kind: document_type,
        url: document_url,
        version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::TaxInvoice.new(invoice: invoice, document_type: document_type).call
      expect(document).to be_present
    end
  end

  it 'makes a request to upload the document via Cloudinary', skip: 'already tested in other generator spec'
  it 'returns the generated document', skip: 'already tested in other generator spec'

end
