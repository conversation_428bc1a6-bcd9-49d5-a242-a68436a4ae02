require 'rails_helper'

RSpec.describe Documents::Generate::EmployeeSurveyPromo, type: :service, documents: true, employee_surveys: true do

  let!(:employee_survey) { create(:employee_survey, :random) }

  let!(:document_url) { 'https://order-details-document.com' }

  before do
    # mock pdf generator
    survey_pdf_generator = double(Customers::EmployeeSurveyPromo)
    allow(Customers::EmployeeSurveyPromo).to receive(:new).and_return(survey_pdf_generator)
    allow(survey_pdf_generator).to receive(:generate).and_return(true)
    allow(survey_pdf_generator).to receive(:render_file).and_return(true)
    allow(survey_pdf_generator).to receive(:report_path).and_return('report-path')
    allow(survey_pdf_generator).to receive(:file_path).and_return('file-path')

    # mock Cloudinary uploader
    cloudinary_reponse = { 'secure_url' => document_url }
    allow(Cloudinary::Uploader).to receive(:upload).and_return(cloudinary_reponse)

    # mock Document Upserter
    mock_document = create(:document, :random)
    document_upserter = double(Documents::Upsert)
    allow(Documents::Upsert).to receive(:new).and_return(document_upserter)
    upserter_response = OpenStruct.new(
      success?: true,
      document: mock_document
    )
    allow(document_upserter).to receive(:call).and_return(upserter_response) # return mock document
  end

  it 'makes a request to generate the Supplier Order Summary document' do
    expect(Customers::EmployeeSurveyPromo).to receive(:new).with(employee_survey: employee_survey)

    document = Documents::Generate::EmployeeSurveyPromo.new(employee_survey: employee_survey).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record and attach it to the employee_survey' do
    expect(Documents::Upsert).to receive(:new).with(documentable: employee_survey, document_params: anything)

    document = Documents::Generate::EmployeeSurveyPromo.new(employee_survey: employee_survey).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
    expected_document_params = {
      name: "Employee Survey Promo - #{employee_survey.category_group_name}",
      kind: 'employee_survey_promo',
      url: document_url,
      version: 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::EmployeeSurveyPromo.new(employee_survey: employee_survey).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the the next version' do
    existing_document = create(:document, :random, documentable: employee_survey, version: 2, kind: 'employee_survey_promo')
    expected_document_params = {
      name: "Employee Survey Promo - #{employee_survey.category_group_name}",
      kind: 'employee_survey_promo',
      url: document_url,
      version: existing_document.version + 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::EmployeeSurveyPromo.new(employee_survey: employee_survey).call
    expect(document).to be_present
  end

  context 'orders reminder' do
    let!(:summary_type) { 'reminder' }

    it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
      expected_document_params = {
      name: "Employee Survey Promo - #{employee_survey.category_group_name}",
      kind: 'employee_survey_promo',
      url: document_url,
      version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::EmployeeSurveyPromo.new(employee_survey: employee_survey).call
      expect(document).to be_present
    end
  end

  it 'makes a request to upload the document via Cloudinary', skip: 'already tested in other generator spec'
  it 'returns the generated document', skip: 'already tested in other generator spec'

end
