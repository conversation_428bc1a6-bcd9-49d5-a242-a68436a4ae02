require 'rails_helper'

RSpec.describe Documents::Generate::SupplierOrderDetails, type: :service, documents: true, orders: true, notifications: true do

  let!(:order) { create(:order, :delivered) }
  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:order_supplier) { create(:order_supplier, :random, order: order, supplier_profile: supplier) }

  let!(:reference) { SecureRandom.hex(7) }

  let!(:document_url) { 'https://order-details-document.com' }

  before do
    # mock pdf generators
    order_detials_pdf_generator = double(Suppliers::OrderDetails)
    allow(Suppliers::OrderDetails).to receive(:new).and_return(order_detials_pdf_generator)
    allow(order_detials_pdf_generator).to receive(:generate).and_return(true)
    allow(order_detials_pdf_generator).to receive(:render_file).and_return(true)
    allow(order_detials_pdf_generator).to receive(:report_path).and_return('report-path')
    allow(order_detials_pdf_generator).to receive(:file_path).and_return('file-path')

    team_order_detials_pdf_generator = double(Suppliers::OrderDetails)
    allow(Suppliers::TeamOrderDetails).to receive(:new).and_return(team_order_detials_pdf_generator)
    allow(team_order_detials_pdf_generator).to receive(:generate).and_return(true)
    allow(team_order_detials_pdf_generator).to receive(:render_file).and_return(true)
    allow(team_order_detials_pdf_generator).to receive(:report_path).and_return('report-path')
    allow(team_order_detials_pdf_generator).to receive(:file_path).and_return('file-path')

    # mock Cloudinary uploader
    cloudinary_reponse = { 'secure_url' => document_url }
    allow(Cloudinary::Uploader).to receive(:upload).and_return(cloudinary_reponse)

    # mock Document Upserter
    mock_document = create(:document, :random)
    document_upserter = double(Documents::Upsert)
    allow(Documents::Upsert).to receive(:new).and_return(document_upserter)
    upserter_response = OpenStruct.new(
      success?: true,
      document: mock_document
    )
    allow(document_upserter).to receive(:call).and_return(upserter_response) # return mock document
  end

  it 'makes a request to generate the Supplier Order Details document' do
    expect(Suppliers::OrderDetails).to receive(:new).with(order: order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: 'normal', version: anything)

    document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record and attach it to order supplier' do
    expect(Documents::Upsert).to receive(:new).with(documentable: order_supplier, document_params: anything)

    document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record and attach it to order, if order_supplier record is missing' do
    order_supplier.destroy
    expect(Documents::Upsert).to receive(:new).with(documentable: order, document_params: anything)

    document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
    expected_document_params = {
      name: "supplier-order-details-#{order.id}-#{supplier.id}-v1",
      kind: 'supplier_order_details',
      url: document_url,
      version: 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the the next version (including within name)' do
    existing_document = create(:document, :random, documentable: order_supplier, version: 2, kind: 'supplier_order_details')
    expected_document_params = {
      name: "supplier-order-details-#{order.id}-#{supplier.id}-v#{existing_document.version + 1}",
      kind: 'supplier_order_details',
      url: document_url,
      version: existing_document.version + 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference).call
    expect(document).to be_present
  end

  context 'with a version override' do
    let!(:version_override) { rand(100..200) }

    it 'makes a request to generate the Supplier Order Details document with the passed in version' do
      expect(Suppliers::OrderDetails).to receive(:new).with(order: order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: 'normal', version: version_override)

      document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference, version_override: version_override).call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the the passed in version' do
      create(:document, :random, documentable: order_supplier, version: 2, kind: 'supplier_order_details') # create existing document

      expected_document_params = {
        name: "supplier-order-details-#{order.id}-#{supplier.id}-v#{version_override}",
        kind: 'supplier_order_details',
        url: document_url,
        version: version_override,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference, version_override: version_override).call
      expect(document).to be_present
    end
  end

  context 'as heads up details' do
    it 'makes a request to generate the Supplier Order Delivery Details document' do
      expect(Suppliers::OrderDetails).to receive(:new).with(order: order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: 'heads_up', version: anything)

      document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference, variation: 'heads_up').call
      expect(document).to be_present
    end

    it 'makes a request to create a delivery document record passing the correct name, kind, version and document url as params' do
      expected_document_params = {
        name: "supplier-heads-up-order-details-#{order.id}-#{supplier.id}-v1",
        kind: 'supplier_heads_up_order_details',
        url: document_url,
        version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference, variation: 'heads_up').call
      expect(document).to be_present
    end
  end

  context 'as a delivery docket' do
    it 'makes a request to generate the Supplier Order Delivery Details document' do
      expect(Suppliers::OrderDetails).to receive(:new).with(order: order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: 'delivery_docket', version: anything)

      document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference, variation: 'delivery_docket').call
      expect(document).to be_present
    end

    it 'makes a request to create a delivery document record passing the correct name, kind, version and document url as params' do
      expected_document_params = {
        name: "supplier-order-delivery-details-#{order.id}-#{supplier.id}-v1",
        kind: 'supplier_order_delivery_details',
        url: document_url,
        version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: reference, variation: 'delivery_docket').call
      expect(document).to be_present
    end
  end

  context 'for a team order' do
    let!(:team_order) { create(:order, :team_order) }

    it 'makes a request to generate the Team Order Supplier Order Details document' do
      expect(Suppliers::TeamOrderDetails).to receive(:new).with(team_order: team_order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: 'normal', version: anything)

      document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: reference).call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
      expected_document_params = {
        name: "supplier-team-order-details-#{team_order.id}-v1",
        kind: 'supplier_order_details',
        url: document_url,
        version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: reference).call
      expect(document).to be_present
    end

    context 'as heads up details' do
      it 'makes a request to generate the Supplier Order Delivery Details document (not Team Order details)' do
        expect(Suppliers::TeamOrderDetails).to_not receive(:new)
        expect(Suppliers::OrderDetails).to receive(:new).with(order: team_order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: 'heads_up', version: anything)

        document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: reference, variation: 'heads_up').call
        expect(document).to be_present
      end

      it 'makes a request to create a delivery document record passing the correct name, kind, version and document url as params' do
        expected_document_params = {
          name: "supplier-heads-up-team-order-details-#{team_order.id}-v1",
          kind: 'supplier_heads_up_order_details',
          url: document_url,
          version: 1,
        }
        expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

        document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: reference, variation: 'heads_up').call
        expect(document).to be_present
      end
    end

    context 'as a delivery docket' do
      it 'makes a request to generate the Team Order Supplier Order Delivery Details document' do
        expect(Suppliers::TeamOrderDetails).to receive(:new).with(team_order: team_order, supplier: supplier, reference: "#{reference}-#{supplier.id}", variation: 'delivery_docket', version: anything)

        document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: reference, variation: 'delivery_docket').call
        expect(document).to be_present
      end

      it 'makes a request to create a delivery document record passing the correct name, kind, version and document url as params' do
        expected_document_params = {
          name: "supplier-team-order-delivery-details-#{team_order.id}-v1",
          kind: 'supplier_order_delivery_details',
          url: document_url,
          version: 1,
        }
        expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

        document = Documents::Generate::SupplierOrderDetails.new(order: team_order, supplier: supplier, reference: reference, variation: 'delivery_docket').call
        expect(document).to be_present
      end
    end
  end

  it 'makes a request to upload the document via Cloudinary', skip: 'already tested in other generator spec'
  it 'returns the generated document', skip: 'already tested in other generator spec'

end
