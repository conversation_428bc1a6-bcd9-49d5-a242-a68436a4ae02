require 'rails_helper'

RSpec.describe Documents::Generate::CustomerOrderDetails, type: :service, documents: true, orders: true, notifications: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :delivered, customer_profile: customer) }

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order_supplier) { create(:order_supplier, :random, order: order, supplier_profile: supplier) }

  let!(:reference) { SecureRandom.hex(7) }

  let!(:report_path) { 'report-path' }
  let!(:file_path) { 'rendered-file-path.pdf' }
  let!(:document_url) { 'https://order-details-document.com' }
  let!(:mock_document) { create(:document, :random) }

  before do
    # mock order details pdf generator
    order_detials_pdf_generator = double(Customers::OrderDetails)
    allow(Customers::OrderDetails).to receive(:new).and_return(order_detials_pdf_generator)
    allow(order_detials_pdf_generator).to receive(:generate).and_return(true)
    allow(order_detials_pdf_generator).to receive(:render_file).and_return(true)
    allow(order_detials_pdf_generator).to receive(:report_path).and_return(report_path)
    allow(order_detials_pdf_generator).to receive(:file_path).and_return(file_path)

    # mock team order details pdf generator
    team_order_detials_pdf_generator = double(Customers::TeamOrderDetails)
    allow(Customers::TeamOrderDetails).to receive(:new).and_return(team_order_detials_pdf_generator)
    allow(team_order_detials_pdf_generator).to receive(:generate).and_return(true)
    allow(team_order_detials_pdf_generator).to receive(:render_file).and_return(true)
    allow(team_order_detials_pdf_generator).to receive(:report_path).and_return(report_path)
    allow(team_order_detials_pdf_generator).to receive(:file_path).and_return(file_path)

    # mock Cloudinary uploader
    cloudinary_reponse = { 'secure_url' => document_url }
    allow(Cloudinary::Uploader).to receive(:upload).and_return(cloudinary_reponse)

    # mock Document Upserter
    document_upserter = double(Documents::Upsert)
    allow(Documents::Upsert).to receive(:new).and_return(document_upserter)
    upserter_response = OpenStruct.new(
      success?: true,
      document: mock_document
    )
    allow(document_upserter).to receive(:call).and_return(upserter_response) # return mock document
  end

  it 'makes a request to generate the Customer Order Details document' do
    expect(Customers::OrderDetails).to receive(:new).with(order: order, reference: reference, version: anything, is_quote: false)

    document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: reference).call
    expect(document).to be_present
  end

  it 'makes a request to upload the document via Cloudinary' do
    expect(Cloudinary::Uploader).to receive(:upload).with(file_path, public_id: report_path, use_filename: true, unique_filename: false)

    document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: reference).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record and attach it to order' do
    expect(Documents::Upsert).to receive(:new).with(documentable: order, document_params: anything)

    document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: reference).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
    expected_document_params = {
      name: "order-details-#{order.id}-v1",
      kind: Documents::Generate::CustomerOrderDetails::ORDER_DOCUMENT_KIND,
      url: document_url,
      version: 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: reference).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the the next version (including within name)' do
    existing_document = create(:document, :random, documentable: order, version: 2, kind: 'customer_order_details')
    expected_document_params = {
      name: "order-details-#{order.id}-v#{existing_document.version + 1}",
      kind: Documents::Generate::CustomerOrderDetails::ORDER_DOCUMENT_KIND,
      url: document_url,
      version: existing_document.version + 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: reference).call
    expect(document).to be_present
  end

  it 'returns the generated document' do
    document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: reference).call
    expect(document).to be_present
    expect(document).to eq(mock_document)
  end

  context 'for an order quote' do
    it 'makes a request to generate the Customer Order Details document as quote' do
      expect(Customers::OrderDetails).to receive(:new).with(order: order, reference: reference, version: anything, is_quote: true)

      document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: reference, variation: 'quote').call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the correct kind, version and document url as params' do
      expected_document_params = {
        name: "order-quote-#{order.id}-v1",
        kind: Documents::Generate::CustomerOrderDetails::QUOTE_DOCUMENT_KIND,
        url: document_url,
        version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::CustomerOrderDetails.new(order: order, reference: reference, variation: 'quote').call
      expect(document).to be_present
    end
  end

  context 'for a team order manifest' do
    let!(:team_order) { create(:order, :team_order) }

    it 'makes a request to generate the Customer Team Order Details document' do
      expect(Customers::TeamOrderDetails).to receive(:new).with(team_order: team_order, reference: reference, version: anything)

      document = Documents::Generate::CustomerOrderDetails.new(order: team_order, reference: reference, variation: 'team_order_manifest').call
      expect(document).to be_present
    end

    it 'makes a request to create a document record passing the correct kind, version and document url as params' do
      expected_document_params = {
        name: "team-order-details-#{team_order.id}-v1",
        kind: Documents::Generate::CustomerOrderDetails::TEAM_ORDER_DOCUMENT_KIND,
        url: document_url,
        version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::CustomerOrderDetails.new(order: team_order, reference: reference, variation: 'team_order_manifest').call
      expect(document).to be_present
    end
  end

end
