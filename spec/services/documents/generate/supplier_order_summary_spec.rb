require 'rails_helper'

RSpec.describe Documents::Generate::SupplierOrderSummary, type: :service, documents: true, orders: true, notifications: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:order) { create(:order, :delivered) }
  let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier) }
  let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier) }

  let!(:order_lines) { [order_line1, order_line2] }

  let!(:summary_day) { order.delivery_at }
  let!(:summary_type) { %w[daily morning].sample }

  let!(:document_url) { 'https://order-details-document.com' }

  before do
    # mock pdf generator
    summary_pdf_generator = double(Suppliers::OrderSummary)
    allow(Suppliers::OrderSummary).to receive(:new).and_return(summary_pdf_generator)
    allow(summary_pdf_generator).to receive(:generate).and_return(true)
    allow(summary_pdf_generator).to receive(:render_file).and_return(true)
    allow(summary_pdf_generator).to receive(:report_path).and_return('report-path')
    allow(summary_pdf_generator).to receive(:file_path).and_return('file-path')

    # mock Cloudinary uploader
    cloudinary_reponse = { 'secure_url' => document_url }
    allow(Cloudinary::Uploader).to receive(:upload).and_return(cloudinary_reponse)

    # mock Document Upserter
    mock_document = create(:document, :random)
    document_upserter = double(Documents::Upsert)
    allow(Documents::Upsert).to receive(:new).and_return(document_upserter)
    upserter_response = OpenStruct.new(
      success?: true,
      document: mock_document
    )
    allow(document_upserter).to receive(:call).and_return(upserter_response) # return mock document
  end

  it 'makes a request to generate the Supplier Order Summary document' do
    expect(Suppliers::OrderSummary).to receive(:new).with(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type)

    document = Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record and attach it to supplier' do
    expect(Documents::Upsert).to receive(:new).with(documentable: supplier, document_params: anything)

    document = Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
    expected_document_params = {
      name: "supplier-orders-summary-#{summary_day.strftime('%Y%m%d')}-#{supplier.id}-#{summary_type}",
      kind: 'supplier_order_summary',
      url: document_url,
      version: 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type).call
    expect(document).to be_present
  end

  it 'makes a request to create a document record passing the the next version' do
    existing_document = create(:document, :random, documentable: supplier, version: 2, kind: 'supplier_order_summary')
    expected_document_params = {
      name: "supplier-orders-summary-#{summary_day.strftime('%Y%m%d')}-#{supplier.id}-#{summary_type}",
      kind: 'supplier_order_summary',
      url: document_url,
      version: existing_document.version + 1,
    }
    expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

    document = Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type).call
    expect(document).to be_present
  end

  context 'orders reminder' do
    let!(:summary_type) { 'reminder' }

    it 'makes a request to create a document record passing the correct name, kind, version and document url as params' do
      expected_document_params = {
      name: "supplier-orders-reminder-#{summary_day.strftime('%Y%m%d')}-#{supplier.id}",
      kind: 'supplier_order_summary',
      url: document_url,
      version: 1,
      }
      expect(Documents::Upsert).to receive(:new).with(documentable: anything, document_params: expected_document_params)

      document = Documents::Generate::SupplierOrderSummary.new(supplier: supplier, order_lines: order_lines, summary_day: summary_day, summary_type: summary_type).call
      expect(document).to be_present
    end
  end

  it 'makes a request to upload the document via Cloudinary', skip: 'already tested in other generator spec'
  it 'returns the generated document', skip: 'already tested in other generator spec'

end
