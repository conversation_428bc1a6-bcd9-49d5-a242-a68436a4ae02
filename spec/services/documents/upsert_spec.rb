require 'rails_helper'

RSpec.describe Documents::Upsert, type: :service, documents: true do

  let!(:order) { create(:order, :delivered) }
  let!(:invoice) { create(:invoice, :random) }
  let!(:document_params) do
    {
      kind: Document::VALID_KINDS.sample,
      url: 'http://test-url.com',
      version: rand(1..10),
    }
  end

  let!(:documentable) { order } # [order, invoice].sample }

  it 'create a new document belonging to the attachable' do
    document_creator = Documents::Upsert.new(documentable: documentable, document_params: document_params).call

    expect(document_creator).to be_success
    created_document = document_creator.document

    expect(created_document).to be_present
    expect(created_document).to be_a(Document)
    expect(created_document).to be_persisted
    expect(created_document.documentable).to eq(documentable)
  end

  it 'create a new document with the passed in params' do
    document_creator = Documents::Upsert.new(documentable: documentable, document_params: document_params).call

    expect(document_creator).to be_success
    created_document = document_creator.document

    expect(created_document.kind).to eq(document_params[:kind])
    expect(created_document.url).to eq(document_params[:url])
    expect(created_document.version).to eq(document_params[:version])
  end

  context 'with an existing document' do
    let!(:document) { create(:document, :random, documentable: documentable, kind: document_params[:kind], version: document_params[:version]) }

    it 'updates an existing document for the same kind and version' do
      saved_url = document.url.dup
      document_updator = Documents::Upsert.new(documentable: documentable, document_params: document_params).call

      expect(document_updator).to be_success
      updated_document = document_updator.document

      expect(updated_document.id).to eq(document.id)
      expect(updated_document.url).to_not eq(saved_url)
      expect(updated_document.url).to eq(document_params[:url])
    end

    it 'creates a new document for a different version and/or kind' do
      new_document_params = case
      when [true, false].sample
        document_params.merge({ version: (document.version + 1) })
      else
        document_params.merge({ kind: (Document::VALID_KINDS - [document.kind]).sample })
      end
      document_upserter = Documents::Upsert.new(documentable: documentable, document_params: new_document_params).call

      expect(document_upserter).to be_success
      upserted_document = document_upserter.document

      expect(upserted_document.id).to_not eq(document.id) # create a new one
      expect(upserted_document.documentable).to eq(documentable)
      expect(upserted_document.kind).to eq(new_document_params[:kind])
      expect(upserted_document.url).to eq(new_document_params[:url])
      expect(upserted_document.version).to eq(new_document_params[:version])
    end
  end

  context 'errors' do
    it 'cannot create a document without a documentable' do
      document_creator = Documents::Upsert.new(documentable: nil, document_params: document_params).call

      expect(document_creator).to_not be_success
      expect(document_creator.errors).to include('Cannot create a document without a documentable')
    end

    it 'cannot create a document without a document kind' do
      non_kind_document_params = document_params.merge({ kind: nil })
      document_creator = Documents::Upsert.new(documentable: documentable, document_params: non_kind_document_params).call

      expect(document_creator).to_not be_success
      expect(document_creator.errors).to include('Cannot create a document without a document kind')
    end

    it 'cannot create a document with an invalid document kind' do
      invalid_kind_document_params = document_params.merge({ kind: 'invalid-kind' })
      document_creator = Documents::Upsert.new(documentable: documentable, document_params: invalid_kind_document_params).call

      expect(document_creator).to_not be_success
      expect(document_creator.errors).to include('Kind is not included in the list')
    end
  end

end
