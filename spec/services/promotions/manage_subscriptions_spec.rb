require 'rails_helper'

RSpec.describe Promotions::ManageSubscriptions, type: :service, promotions: true do
    
  let!(:promotion) { create(:promotion, :random) }

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:customer2) { create(:customer_profile, :random) }

  it 'subscribes customers to the promotion' do
    subscriptions = [
      {
        subscriber_type: customer1.class.name,
        subscriber_id: customer1.id,
        active: true
      },
      {
        subscriber_type: customer2.class.name,
        subscriber_id: customer2.id,
        active: true
      }
    ]
    subscriptions_manager = Promotions::ManageSubscriptions.new(promotion: promotion, subscriptions: subscriptions).call

    expect(subscriptions_manager).to be_success

    created_subscriptions = subscriptions_manager.subscriptions
    expect(created_subscriptions.size).to eq(2)
    expect(created_subscriptions.map(&:promotion)).to include(promotion)
    expect(created_subscriptions.map(&:subscriber)).to include(customer1, customer2)
    expect(created_subscriptions.map(&:subscriber_type)).to include('CustomerProfile')
  end

  context 'company subscriptions' do
    let!(:company1) { create(:company, :random) }

    it 'subscribes companies to the promotion' do
      subscriptions = [
        {
          subscriber_type: customer1.class.name,
          subscriber_id: customer1.id,
          active: true
        },
        {
          subscriber_type: company1.class.name,
          subscriber_id: company1.id,
          active: true
        }
      ]
      subscriptions_manager = Promotions::ManageSubscriptions.new(promotion: promotion, subscriptions: subscriptions).call

      expect(subscriptions_manager).to be_success

      created_subscriptions = subscriptions_manager.subscriptions
      expect(created_subscriptions.size).to eq(2)
      expect(created_subscriptions.map(&:promotion)).to include(promotion)
      expect(created_subscriptions.map(&:subscriber)).to include(customer1, company1)
      expect(created_subscriptions.map(&:subscriber_type)).to include('CustomerProfile', 'Company')
    end
  end

  context 'with an existing access permissions' do
    let!(:subscription1) { create(:promotion_subscription, :random, promotion: promotion, subscriber: customer1, active: true) }

    it 'updates the existing subscription (using passed in ID)' do
      subscriptions = [
        {
          id: subscription1.id,
          subscriber_type: customer1.class.name,
          subscriber_id: customer1.id,
          active: false
        }
      ]
      subscriptions_manager = Promotions::ManageSubscriptions.new(promotion: promotion, subscriptions: subscriptions).call

      expect(subscriptions_manager).to be_success

      updated_subscriptions = subscriptions_manager.subscriptions
      expect(updated_subscriptions.size).to eq(1)

      updated_subscription = updated_subscriptions.first
      expect(updated_subscription.id).to eq(subscription1.id)
      expect(updated_subscription.active).to eq(subscriptions[0][:active])
    end

    it 'updates the existing access permissions even without ID (matched on subscriber - does not create duplicates)' do
      subscriptions = [
        {
          # id: subscription1.id, # no ID sent
          subscriber_type: customer1.class.name,
          subscriber_id: customer1.id,
          active: false
        }
      ]
      subscriptions_manager = Promotions::ManageSubscriptions.new(promotion: promotion, subscriptions: subscriptions).call

      expect(subscriptions_manager).to be_success

      updated_subscriptions = subscriptions_manager.subscriptions
      expect(updated_subscriptions.size).to eq(1)

      updated_subscription = updated_subscriptions.first
      expect(updated_subscription.id).to eq(subscription1.id)
      expect(updated_subscription.active).to eq(subscriptions[0][:active])
    end

    it 'removes the existing permission if `_delete` is passed' do
      subscriptions = [
        {
          id: subscription1.id,
          subscriber_type: customer1.class.name,
          subscriber_id: customer1.id,
          active: false,
          _delete: true
        }
      ]
      subscriptions_manager = Promotions::ManageSubscriptions.new(promotion: promotion, subscriptions: subscriptions).call

      expect(subscriptions_manager).to be_success

      updated_subscriptions = subscriptions_manager.subscriptions
      expect(updated_subscriptions).to be_blank

      expect{ subscription1.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end

  context 'errors' do
    it 'errors if the promotion is missing' do
      subscriptions_manager = Promotions::ManageSubscriptions.new(promotion: nil).call

      expect(subscriptions_manager).to_not be_success
      expect(subscriptions_manager.errors).to include('Cannot manage subscriptions without a promotion')
    end
  end
end
