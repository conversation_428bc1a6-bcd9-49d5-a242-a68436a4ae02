require 'rails_helper'

RSpec.describe Promotions::SyncWithOrder, type: :service, orders: true, promotions: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :random, customer_profile: customer) }

  let!(:promotion) { create(:promotion, :random) }
  let!(:promotion_fetcher) { double(Promotions::FetchForOrderCustomer) }

  before do
    # mock fetching promotion
    allow(Promotions::FetchForOrderCustomer).to receive(:new).and_return(promotion_fetcher)
    allow(promotion_fetcher).to receive(:call).and_return(promotion)

    # mock totals calculator
    customer_total_calculator = double(Orders::CalculateCustomerTotals)
    allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(customer_total_calculator)
    allow(customer_total_calculator).to receive(:call).and_return(true)
  end

  it 'makes a request to fetch a promotion for the order\'s customer' do
    expect(Promotions::FetchForOrderCustomer).to receive(:new).with(order: order)

    promotions_syncer = Promotions::SyncWithOrder.new(order: order).call
    expect(promotions_syncer).to be_success
  end

  it 'attaches the fetched promotion to the order' do
    promotions_syncer = Promotions::SyncWithOrder.new(order: order).call

    expect(promotions_syncer).to be_success

    updated_order = promotions_syncer.order
    expect(updated_order.promotion).to be_present
    expect(updated_order.promotion).to eq(promotion)
  end

  it 'makes a request to recalculate the order totals' do
    expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, save_totals: true)

    promotions_syncer = Promotions::SyncWithOrder.new(order: order).call
    expect(promotions_syncer).to be_success
  end

  context 'with an already attached promotion' do
    let!(:existing_promotion) { create(:promotion, :random) }
    before do
      order.update_column(:promotion_id, existing_promotion.id)
    end

    it 'attaches the newly fetched promotion if it is different to the already attached promotion' do
      promotions_syncer = Promotions::SyncWithOrder.new(order: order, customer: nil).call

      expect(promotions_syncer).to be_success
      updated_order = promotions_syncer.order
      expect(updated_order.promotion).to be_present
      expect(updated_order.promotion).to_not eq(existing_promotion)
      expect(updated_order.promotion).to eq(promotion) # fetched promotion
    end

    it 'return with errors if fetched promotion is already attached to order' do
      order.update_column(:promotion_id, promotion.id) # same as the one fetched
      promotions_syncer = Promotions::SyncWithOrder.new(order: order, customer: nil).call

      expect(promotions_syncer).to_not be_success
      expect(promotions_syncer.errors).to include('Order is already attached to a promotion')
    end
  end

  context 'errors' do
    it 'errors without an order' do
      promotions_syncer = Promotions::SyncWithOrder.new(order: nil).call

      expect(promotions_syncer).to_not be_success
      expect(promotions_syncer.errors).to include('Cannot attach promotion to a missing order')
    end

    it 'errors without a customer' do
      order.update_column(:customer_profile_id, nil)
      promotions_syncer = Promotions::SyncWithOrder.new(order: order, customer: nil).call

      expect(promotions_syncer).to_not be_success
      expect(promotions_syncer.errors).to include('Cannot attach promotion without knowing the customer')
    end
  end

end