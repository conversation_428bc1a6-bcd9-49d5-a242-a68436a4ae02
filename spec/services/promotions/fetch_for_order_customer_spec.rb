require 'rails_helper'

RSpec.describe Promotions::FetchForOrderCustomer, type: :service, orders: true, promotions: true do

  let!(:today) { (Time.zone.now.beginning_of_day + 10.hours).to_date }

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :draft, customer_profile: customer, delivery_at: today.beginning_of_day + 10.hours) }
  let!(:customer_promotion) { create(:promotion, :random, valid_from: today - 3.days, valid_until: today + 3.days) }
  let!(:customer_subscription) { create(:promotion_subscription, promotion: customer_promotion, subscriber: customer) }

  it 'returns the active and valid customer promotion' do
    fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

    expect(fetched_promotion).to be_present
    expect(fetched_promotion).to eq(customer_promotion)
  end

  context 'with a category (group) based promotion' do
    let!(:restricted_category_group) { Category::VALID_CATEGORY_GROUPS.sample }
    let!(:customer_category_promotion) { create(:promotion, :random, valid_from: today - 3.days, valid_until: today + 3.days, category_restriction: restricted_category_group) }
    let!(:customer_category_subscription) { create(:promotion_subscription, promotion: customer_category_promotion, subscriber: customer) }

    let!(:category1) { create(:category, :random, group: restricted_category_group) }
    let!(:order_line11) { create(:order_line, :random, order: order, category: category1, quantity: 10) }
    let!(:order_line12) { create(:order_line, :random, order: order, category: category1, quantity: 10) }

    let!(:category2) { create(:category, :random, group: (Category::VALID_CATEGORY_GROUPS - [restricted_category_group]).sample) }
    let!(:order_line21) { create(:order_line, :random, order: order, category: category2, quantity: 10) }

    it 'returns the active and valid category restricted customer promotion for the order category' do
      fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

      expect(fetched_promotion).to be_present
      expect(fetched_promotion).to eq(customer_category_promotion)
    end

    it 'reverts to non-category restricted customer if category restricted promotion is not found' do
      order_line12.update_column(:category_id, category2.id) # major order category is now category 2 (which does not have a promotion)
      fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

      expect(fetched_promotion).to be_present
      expect(fetched_promotion).to eq(customer_promotion)
    end

    it 'reverts to non-category restricted customer promotion if order category_group could not be determined' do
      [order_line11, order_line12, order_line21].each do |order_line|
        order_line.update_column(:category_id, nil)
      end
      fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

      expect(fetched_promotion).to be_present
      expect(fetched_promotion).to eq(customer_promotion)
    end

    context 'for a custom order' do
      let!(:custom_order) { create(:order, :draft, :custom_order, customer_profile: customer, major_category: category1 )}
      
      it 'returns the active and valid category restricted customer promotion for the custom order\'s major category' do
        fetched_promotion = Promotions::FetchForOrderCustomer.new(order: custom_order).call

        expect(fetched_promotion).to be_present
        expect(fetched_promotion).to eq(customer_category_promotion)
      end
    end
  end

  it 'returns with nothing if an active customer promotion does not exist' do
    customer_promotion.update_column(:active, false)
    fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

    expect(fetched_promotion).to be_blank
  end

  it 'returns with nothing if a customer promotion with valid dates does not exist' do
    customer_promotion.update_column(:active, false)
    fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

    expect(fetched_promotion).to be_blank
  end

  it 'returns with nothing if a customer promotion subscription is inactive' do
    customer_subscription.update_column(:active, false)
    fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

    expect(fetched_promotion).to be_blank
  end

  context 'with multiple promotions' do
    let!(:customer_promotion2) { create(:promotion, :random, valid_from: today - 2.days, valid_until: today + 2.days) }
    let!(:customer_subscription2) { create(:promotion_subscription, promotion: customer_promotion2, subscriber: customer) }

    it 'returns the latest promotion' do
      fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

      expect(fetched_promotion).to be_present
      expect(fetched_promotion).to eq(customer_promotion2)
    end
  end

  context 'with attached company' do
    let!(:company) { create(:company, :random) }

    let!(:company_promotion) { create(:promotion, :random, valid_from: today - 3.days, valid_until: today + 3.days) }
    let!(:company_subscription) { create(:promotion_subscription, promotion: company_promotion, subscriber: company) }

    before do
      customer.update_column(:company_id, company.id) # attach company to customer
    end

    it 'still retrieves the existing (active and valid) customer promotion' do
      fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

      expect(fetched_promotion).to be_present
      expect(fetched_promotion).to eq(customer_promotion)
    end

    context 'if customer_promotion is in-active/invalid/missing' do
      before do
        is_inactive_customer_promotion = [true, false].sample
        is_invalid_customer_promotion = [true, false].sample
        is_inactive_customer_subscription = [true, false].sample
        is_missing_customer_subscription = [true, false].sample
        case
        when is_inactive_customer_promotion
          customer_promotion.update_column(:active, false)
        when is_invalid_customer_promotion
          customer_promotion.update_column(:valid_from, today + 1.day)
        when is_inactive_customer_subscription
          customer_subscription.update_column(:active, false)
        when is_missing_customer_subscription
          customer_subscription.destroy
        else # missing customer promotion
          customer_promotion.destroy
        end
      end

      it 'returns the company promotion' do
        fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

        expect(fetched_promotion).to be_present
        expect(fetched_promotion).to eq(company_promotion)
      end

      context 'with a category (group) based promotion' do
        let!(:restricted_category_group) { Category::VALID_CATEGORY_GROUPS.sample }
        let!(:company_category_promotion) { create(:promotion, :random, valid_from: today - 3.days, valid_until: today + 3.days, category_restriction: restricted_category_group) }
        let!(:company_category_subscription) { create(:promotion_subscription, promotion: company_category_promotion, subscriber: company) }

        let!(:category1) { create(:category, :random, group: restricted_category_group) }
        let!(:order_line11) { create(:order_line, :random, order: order, category: category1, quantity: 10) }
        let!(:order_line12) { create(:order_line, :random, order: order, category: category1, quantity: 10) }

        let!(:category2) { create(:category, :random, group: (Category::VALID_CATEGORY_GROUPS - [restricted_category_group]).sample) }
        let!(:order_line21) { create(:order_line, :random, order: order, category: category2, quantity: 10) }

        it 'returns the active and valid category restricted company promotion for the order category' do
          fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

          expect(fetched_promotion).to be_present
          expect(fetched_promotion).to eq(company_category_promotion)
        end

        it 'reverts to non-category restricted company promotion if category restricted promotion is not found' do
          order_line12.update_column(:category_id, category2.id) # major order category is now category 2 (which does not have a promotion)
          fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

          expect(fetched_promotion).to be_present
          expect(fetched_promotion).to eq(company_promotion)
        end

        it 'reverts to non-category restricted company promotion if order category_group could not be determined' do
          [order_line11, order_line12, order_line21].each do |order_line|
            order_line.update_column(:category_id, nil)
          end
          fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

          expect(fetched_promotion).to be_present
          expect(fetched_promotion).to eq(company_promotion)
        end
      end # with category based promotion
    end # no customer promotions
  end # with only company promotions

  it 'returns with nothing if the order customer is missing' do
    order.update_column(:customer_profile_id, nil)
    fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

    expect(fetched_promotion).to be_blank
  end

  it 'returns with nothing if the order delivery date(time) is missing' do
    order.update_column(:delivery_at, nil)
    fetched_promotion = Promotions::FetchForOrderCustomer.new(order: order).call

    expect(fetched_promotion).to be_blank
  end

end