require 'rails_helper'

RSpec.describe Promotions::CalculateDiscount, kind: :service, promotions: true do

  let!(:promotion) { create(:promotion, :random) }
  let!(:redeemable_amount) { 1000 }

  it 'returns the reedeemable_amount, discount and discount total as keys' do
    promotion_values = Promotions::CalculateDiscount.new(promotion: promotion, amount: redeemable_amount).call

    expect(promotion_values.keys).to include(:amount, :discount, :total)
    expect(promotion_values[:amount]).to eq(redeemable_amount)
  end

  context 'an amount kind of promotion' do
    let!(:amount_promotion) { create(:promotion, :random, amount: 50, kind: 'amount') }

    it 'returns the discount as promotion amount' do
      promotion_values = Promotions::CalculateDiscount.new(promotion: amount_promotion, amount: redeemable_amount).call

      expect(promotion_values[:discount]).to eq(amount_promotion.amount)
    end

    it 'returns the discounted total' do
      promotion_values = Promotions::CalculateDiscount.new(promotion: amount_promotion, amount: redeemable_amount).call

      expect(promotion_values[:total]).to eq(redeemable_amount - amount_promotion.amount)
    end
  end

  context 'a pecentage kind of promotion' do
    let!(:precent_promotion) { create(:promotion, :random, amount: 50, kind: 'percentage') }

    it 'returns the discount as promotion amount percent of redeemable_amount' do
      promotion_values = Promotions::CalculateDiscount.new(promotion: precent_promotion, amount: redeemable_amount).call

      expect(promotion_values[:discount]).to eq(redeemable_amount * precent_promotion.amount / 100)
    end

    it 'returns the discounted total' do
      promotion_values = Promotions::CalculateDiscount.new(promotion: precent_promotion, amount: redeemable_amount).call

      expect(promotion_values[:total]).to eq(redeemable_amount - (redeemable_amount * precent_promotion.amount / 100))
    end
  end

end
