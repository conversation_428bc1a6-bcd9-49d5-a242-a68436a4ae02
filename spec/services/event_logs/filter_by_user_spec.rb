require 'rails_helper'

RSpec.describe EventLogs::FilterByUser, type: :service, event_logs: true do

  let(:super_admin) { create(:user, :random, super_admin: true, admin: false) }
  let(:admin) { create(:user, :random, super_admin: false, admin: true) }
  let(:non_admin_user) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: false) }

  let!(:event_log1) { create(:event_log, event: 'new-quote-submitted') }
  let!(:event_log2) { create(:event_log, event: 'order-rejected') }
  let!(:event_log3) { create(:event_log, event: 'woolworths-checkout-failed') }

  let!(:existing_logs) { EventLog.all }

  it 'return blank for a missing user' do
    event_logs = EventLogs::FilterByUser.new(user: nil, existing_logs: existing_logs).call

    expect(event_logs).to be_blank
  end

  context 'as a super admin' do
    let!(:super_admin_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:super_admin].sample(2) }
    let!(:non_super_admin_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:super_admin]).sample }

    before do
      event_log1.update_column(:event, super_admin_events[0])
      event_log2.update_column(:event, non_super_admin_event)
      event_log3.update_column(:event, super_admin_events[1])
    end

    it 'can only list allowed events' do
      event_logs = EventLogs::FilterByUser.new(user: super_admin, existing_logs: existing_logs).call

      expect(event_logs).to include(event_log1, event_log3)
      expect(event_logs).to_not include(event_log2)
    end

    it 'only filter within passed in existing event logs' do
      scoped_logs = EventLog.where(event: event_log1.event)
      event_logs = EventLogs::FilterByUser.new(user: super_admin, existing_logs: scoped_logs).call

      expect(event_logs).to include(event_log1)
      expect(event_logs).to_not include(event_log2, event_log3)
    end
  end

  context 'as a Yordar/Orders admin' do
    let!(:yordar_admin_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:yordar_admin].sample(2) }
    let!(:non_yordar_admin_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:yordar_admin]).sample }

    before do
      event_log1.update_column(:event, non_yordar_admin_event)
      event_log2.update_column(:event, yordar_admin_events[0])
      event_log3.update_column(:event, yordar_admin_events[1])
    end

    it 'can only list allowed events' do
      event_logs = EventLogs::FilterByUser.new(user: admin, existing_logs: existing_logs).call

      expect(event_logs).to include(event_log2, event_log3)
      expect(event_logs).to_not include(event_log1)
    end
  end

  context 'as an Account Manager' do # admin user with account manager permissions
    let(:account_manager_user) { admin }
    let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

    let!(:admin_access_permission1) { create(:access_permission, :random, admin: customer_team_admin, scope: 'account_manager') }

    let!(:account_manager_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:account_manager].sample(2) }
    let!(:non_account_manager_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:account_manager]).sample }

    before do
      customer_team_admin.profile.update_column(:user_id, account_manager_user.id) # attach customer team admin customer profile to user
      account_manager_user.reload

      event_log1.update_column(:event, account_manager_events[0])
      event_log2.update_column(:event, account_manager_events[1])
      event_log3.update_column(:event, non_account_manager_event)
    end

    it 'can only list allowed events' do
      event_logs = EventLogs::FilterByUser.new(user: account_manager_user, existing_logs: existing_logs).call

      expect(event_logs).to include(event_log1, event_log2)
      expect(event_logs).to_not include(event_log3)
    end
  end

  context 'as a Pantry Manager' do # non-admin user with pantry manager permissions
    let(:pantry_manager_user) { non_admin_user }
    let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

    let!(:admin_access_permission) { create(:access_permission, :random, admin: customer_team_admin, scope: 'pantry_manager') }

    let!(:pantry_manager_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:pantry_manager].sample(2) }
    let!(:non_pantry_manager_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:pantry_manager]).sample }

    before do
      customer_team_admin.profile.update_column(:user_id, pantry_manager_user.id) # attach customer team admin customer profile to user
      pantry_manager_user.reload

      event_log1.update_column(:event, pantry_manager_events[0])
      event_log2.update_column(:event, non_pantry_manager_event)
      event_log3.update_column(:event, pantry_manager_events[1])
    end

    it 'can only list allowed events' do
      event_logs = EventLogs::FilterByUser.new(user: pantry_manager_user, existing_logs: existing_logs).call

      expect(event_logs).to include(event_log1, event_log3)
      expect(event_logs).to_not include(event_log2)
    end
  end

  context 'as a Company Team Admin' do # non-admin user with company_team_admin, full_access or blank scope permissions
    let(:company_team_admin_user) { non_admin_user }
    let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

    let!(:admin_access_permission) { create(:access_permission, :random, admin: customer_team_admin, scope: [nil, 'full_access', 'company_team_admin'].sample) }

    let!(:customer_team_admin_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:customer_team_admin].sample(2) }
    let!(:non_customer_team_admin_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:customer_team_admin]).sample }

    before do
      customer_team_admin.profile.update_column(:user_id, company_team_admin_user.id) # attach customer team admin customer profile to user
      company_team_admin_user.reload

      event_log1.update_column(:event, customer_team_admin_events[0])
      event_log2.update_column(:event, non_customer_team_admin_event)
      event_log3.update_column(:event, customer_team_admin_events[1])
    end

    it 'can only list allowed events' do
      event_logs = EventLogs::FilterByUser.new(user: company_team_admin_user, existing_logs: existing_logs).call

      expect(event_logs).to include(event_log1, event_log3)
      expect(event_logs).to_not include(event_log2)
    end
  end

  context 'as a Supplier Admin' do # user who can_access_suppliers
    let(:supplier_admin_user) { create(:user, :random, super_admin: false, admin: false, can_access_suppliers: true) }

    let!(:supplier_admin_events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:supplier_admin].sample(2) }
    let!(:non_supplier_admin_event) { (EventLog::VALID_EVENTS - EventLogs::FilterByUser::EVENT_BASED_ACCESS[:supplier_admin]).sample }

    before do
      event_log1.update_column(:event, non_supplier_admin_event)
      event_log2.update_column(:event, supplier_admin_events[0])
      event_log3.update_column(:event, supplier_admin_events[1])
    end

    it 'can only list allowed events' do
      event_logs = EventLogs::FilterByUser.new(user: supplier_admin_user, existing_logs: existing_logs).call

      expect(event_logs).to include(event_log2, event_log3)
      expect(event_logs).to_not include(event_log1)
    end
  end

end