require 'rails_helper'

RSpec.describe EventLogs::Create, type: :service, event_logs: true do

  before do
    # mock notification broadcaster
    notifications_broadcaster = double(Admin::Notifications::BroadcastCount)
    allow(Admin::Notifications::BroadcastCount).to receive(:new).and_return(notifications_broadcaster)
    allow(notifications_broadcaster).to receive(:call).and_return(true)
  end

  context 'Order events' do
    subject { EventLogs::Create.new(event_object: order, event: event_type).call }

    let!(:event_type) { EventLog::VALID_ORDER_EVENTS.sample }
    let!(:customer) { create(:customer_profile, :random) }
    let!(:order) { create(:order, :new, customer_profile: customer) }

    it 'creates a new event log for the order object' do
      event_logger = subject
      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log).to be_present
      expect(created_log).to be_a(EventLog)
      expect(created_log).to be_persisted

      expect(created_log.loggable).to eq(order)
      expect(created_log.event).to eq(event_type)
    end

    it 'saves the order customer as the scopable object' do
      event_logger = subject
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.scopable).to be_present
      expect(created_log.scopable).to be_a(CustomerProfile)
      expect(created_log.scopable).to eq(customer) # order.customer_profile
    end

    it 'does not create a duplicate event for the order object' do
      existing_log = create(:event_log, loggable: order, scopable: customer, event: event_type, info: { type: 'general', is_recurrent: false })
      event_logger = subject

      expect(event_logger).to be_success
      returned_log = event_logger.event_log

      expect(returned_log.id).to eq(existing_log.id)
    end

    context 'info' do
      it 'saves the order type and if it is recurrent as event info' do
        event_logger = subject
        expect(event_logger).to be_success
        created_log = event_logger.event_log

        expect(created_log.info).to be_present
        expect(created_log.info.keys).to include('type', 'is_recurrent')
      end

      it 'saves the appropriate info for a one-off order' do
        order.update_columns(order_type: 'one-off', order_variant: 'general')
        event_logger = subject
        expect(event_logger).to be_success
        created_log = event_logger.event_log

        expect(created_log.info).to be_present
        expect(created_log.info['type']).to eq('general')
        expect(created_log.info['is_recurrent']).to eq(false)
      end

      it 'saves the appropriate info for a recurring order' do
        order.update_columns(order_type: 'recurrent', order_variant: 'general')
        event_logger = subject
        expect(event_logger).to be_success
        created_log = event_logger.event_log

        expect(created_log.info).to be_present
        expect(created_log.info['type']).to eq('general')
        expect(created_log.info['is_recurrent']).to eq(true)
      end

      it 'saves the appropriate info for a team orders' do
        order.update_columns(order_type: 'one-off', order_variant: 'team_order')
        event_logger = subject
        expect(event_logger).to be_success
        created_log = event_logger.event_log

        expect(created_log.info).to be_present
        expect(created_log.info['type']).to eq('team_order')
        expect(created_log.info['is_recurrent']).to eq(false)
      end

      it 'saves the appropriate info for a recurring team orders' do
        order.update_columns(order_type: 'one-off', order_variant: 'recurring_team_order')
        event_logger = subject
        expect(event_logger).to be_success
        created_log = event_logger.event_log

        expect(created_log.info).to be_present
        expect(created_log.info['type']).to eq('team_order')
        expect(created_log.info['is_recurrent']).to eq(true)
      end

      it 'saves the appropriate info for a custom order' do
        order.update_columns(order_type: 'one-off', order_variant: 'event_order')
        event_logger = subject
        expect(event_logger).to be_success
        created_log = event_logger.event_log

        expect(created_log.info).to be_present
        expect(created_log.info['type']).to eq('event_order')
        expect(created_log.info['is_recurrent']).to eq(false)
      end
    end
  end

  context 'Customer Events' do
    subject { EventLogs::Create.new(event_object: customer, event: event_type).call }

    let!(:customer) { create(:customer_profile, :random) }
    let!(:event_type) { 'new-customer-registration' }

    it 'creates a new event log for the customer object' do
      event_logger = subject
      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log).to be_present
      expect(created_log).to be_a(EventLog)
      expect(created_log).to be_persisted

      expect(created_log.loggable).to eq(customer)
      expect(created_log.event).to eq(event_type)
    end

    it 'does not save a scopable' do
      event_logger = subject
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.scopable).to be_blank
    end
  end

  context 'Customer Quote Events' do
    subject { EventLogs::Create.new(event_object: customer_quote, event: event_type).call }

    let!(:customer) { create(:customer_profile, :random) }
    let!(:customer_quote) { create(:customer_quote, :random, customer_profile: customer) }
    let!(:event_type) { 'new-quote-submitted' }

    it 'creates a new event log for the customer quote object' do
      event_logger = subject
      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log).to be_present
      expect(created_log).to be_a(EventLog)
      expect(created_log).to be_persisted

      expect(created_log.loggable).to eq(customer_quote)
      expect(created_log.event).to eq(event_type)
    end

    it 'does not save a scopable' do
      event_logger = subject
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.scopable).to be_blank
    end
  end

  context 'Invoice events' do
    subject { EventLogs::Create.new(event_object: invoice, event: event_type).call }

    let!(:event_type) { 'invoice-overdue' }
    let!(:invoice) { create(:invoice, :random) }

    let!(:customer) { create(:customer_profile, :random) }
    let!(:order) { create(:order, :new, customer_profile: customer, update_with_invoice: true, invoice: invoice) }

    it 'creates a new event log for the invoice object' do
      event_logger = subject
      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log).to be_present
      expect(created_log).to be_a(EventLog)
      expect(created_log).to be_persisted

      expect(created_log.loggable).to eq(invoice)
      expect(created_log.event).to eq(event_type)
    end

    it 'saves the invoice order\'s customer as the scopable object' do
      event_logger = subject
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.scopable).to be_present
      expect(created_log.scopable).to be_a(CustomerProfile)
      expect(created_log.scopable).to eq(customer) # invoice.orders.sample.customer_profile
    end

    it 'saves log with passed in info' do
      invoice_info = { overdue_by: '3 days', payment_term_days: '20 days' }
      event_logger = EventLogs::Create.new(event_object: invoice, event: event_type, **invoice_info).call
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.info).to be_present
      expect(created_log.info.keys).to include('overdue_by', 'payment_term_days')
      expect(created_log.info['overdue_by']).to eq(invoice_info[:overdue_by])
      expect(created_log.info['payment_term_days']).to eq(invoice_info[:payment_term_days])
    end
  end

  context 'Supplier events' do
    subject { EventLogs::Create.new(event_object: supplier, event: event_type).call }

    let!(:event_type) { EventLog::VALID_SUPPLIER_EVENTS.sample }
    let!(:supplier) { create(:supplier_profile, :random) }

    it 'creates a new event log for the supplier object' do
      event_logger = subject
      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log).to be_present
      expect(created_log).to be_a(EventLog)
      expect(created_log).to be_persisted

      expect(created_log.loggable).to eq(supplier)
      expect(created_log.event).to eq(event_type)
    end

    it 'saves the scopable as blank' do
      event_logger = subject
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.scopable).to be_blank
    end

    it 'can create duplicate events for the supplier' do
      existing_log = create(:event_log, loggable: supplier, scopable: false, event: event_type)

      event_logger = subject
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.id).to_not eq(existing_log.id)
    end
  end

  context 'Holiday Events' do
    subject { EventLogs::Create.new(event_object: holiday, event: event_type).call }

    let!(:holiday) { create(:holiday, :random) }
    let!(:event_type) { ['new-quote-submitted', 'upcoming-public-holiday'].sample }

    it 'creates a new event log for the holiday object' do
      event_logger = subject
      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log).to be_present
      expect(created_log).to be_a(EventLog)
      expect(created_log).to be_persisted

      expect(created_log.loggable).to eq(holiday)
      expect(created_log.event).to eq(event_type)
    end

    it 'does not save a scopable' do
      event_logger = subject
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.scopable).to be_blank
    end

    it 'does not create a duplicate event for the holiday object' do
      existing_log = create(:event_log, loggable: holiday, event: event_type)
      event_logger = subject

      expect(event_logger).to be_success
      returned_log = event_logger.event_log

      expect(returned_log.id).to eq(existing_log.id)
    end
  end

  context 'CustomerBudget events' do
    subject { EventLogs::Create.new(event_object: customer_budget, event: event_type).call }

    let!(:event_type) { EventLog::VALID_BUDGET_EVENTS.sample }
    let!(:customer) { create(:customer_profile, :random) }
    let!(:customer_budget) { create(:customer_budget, :random, customer_profile: customer) }

    it 'creates a new event log for the customer budget object' do
      event_logger = subject
      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log).to be_present
      expect(created_log).to be_a(EventLog)
      expect(created_log).to be_persisted

      expect(created_log.loggable).to eq(customer_budget)
      expect(created_log.event).to eq(event_type)
    end

    it 'saves the customer budget\'s customer as the scopable object' do
      event_logger = subject
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.scopable).to be_present
      expect(created_log.scopable).to be_a(CustomerProfile)
      expect(created_log.scopable).to eq(customer) # customer_budget.customer_profile
    end

    it 'saves log with passed in info' do
      budget_info = {
        number_of_orders: rand(10..20),
        budget: 1000.00,
        spend: 600.00,
        spend_percentage: 60.00,
        po_number: nil,
        remaining: 400.00
      }
      event_logger = EventLogs::Create.new(event_object: customer_budget, event: event_type, **budget_info).call
      expect(event_logger).to be_success
      created_log = event_logger.event_log

      expect(created_log.info).to be_present
      expect(created_log.info.keys).to include(*budget_info.keys.map(&:to_s))
      budget_info.keys.each do |key|
        expect(created_log.info[key.to_s]).to eq(budget_info[key])
      end
    end
  end

  context 'Time based Events' do
    it 'creates a new event log with given info' do
      event_logger = EventLogs::Create.new(event: 'orders-auto-confirmed', order_ids: [1, 2, 3]).call
      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log).to be_present
      expect(created_log).to be_a(EventLog)
      expect(created_log).to be_persisted

      expect(created_log.loggable).to be_blank
      expect(created_log.scopable).to be_blank

      expect(created_log.event).to eq('orders-auto-confirmed')
      expect(created_log.info).to be_present
      expect(created_log.info.keys).to include('order_ids')
      expect(created_log.info['order_ids']).to match_array([1, 2, 3])
    end
  end

  it 'makes a request to broadcast the notifications count' do
    expect(Admin::Notifications::BroadcastCount).to receive(:new)

    event_logger = EventLogs::Create.new(event: EventLog::VALID_EVENTS.sample).call
    expect(event_logger).to be_success
  end

  context 'Severity' do    
    it 'saves an event log with the default severity of `info`' do
      event_logger = EventLogs::Create.new(event: EventLog::VALID_EVENTS.sample).call

      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log.severity).to eq('info')
    end

    it 'saves an event log with the passed in severity (and additional info if present)' do
      event_severity = EventLog::VALID_SEVERITIES.sample
      additional_info = [true, false].sample ? { extra_info: true, some_more_info: true } : {}
      event_logger = EventLogs::Create.new(event: EventLog::VALID_EVENTS.sample, severity: event_severity, **additional_info).call

      expect(event_logger).to be_success

      created_log = event_logger.event_log
      expect(created_log.severity).to eq(event_severity)

      expect(created_log.info.keys).to include(*additional_info.keys.map(&:to_s))
    end
  end

end