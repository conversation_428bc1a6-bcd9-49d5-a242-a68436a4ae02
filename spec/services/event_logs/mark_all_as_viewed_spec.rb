require 'rails_helper'

RSpec.describe EventLogs::MarkAllAsViewed, type: :service, event_logs: true do

  let!(:events) { EventLogs::FilterByUser::EVENT_BASED_ACCESS[:yordar_admin] }

  let!(:event_log1) { create(:event_log, :random, event: events.sample, severity: 'info') }
  let!(:event_log2) { create(:event_log, :random, event: events.sample, severity: 'info') }
  let!(:event_log3) { create(:event_log, :random, event: events.sample, severity: 'info') }

  let!(:user) { create(:user, :random, admin: true) }

  before do
    # mock notification broadcaster
    notifications_broadcaster = double(Admin::Notifications::BroadcastCount)
    allow(Admin::Notifications::BroadcastCount).to receive(:new).and_return(notifications_broadcaster)
    allow(notifications_broadcaster).to receive(:call).and_return(true)
  end

  it 'marks all info based notificaions as (bulk) viewed (by default)' do
    event_logs_viewer = EventLogs::MarkAllAsViewed.new(user: user).call

    expect(event_logs_viewer).to be_success
    expect(event_logs_viewer.viewed_notifications).to include(event_log1, event_log2, event_log3)
    expect(event_logs_viewer.pending_notifications).to be_blank

    event_log_views = event_logs_viewer.viewed_notifications.map(&:views).flatten(1)
    expect(event_log_views.map(&:in_bulk).uniq).to eq([true])
  end

  it 'broadcasts the notifications update' do
    expect(Admin::Notifications::BroadcastCount).to receive(:new).with(user: user)
    expect(Admin::Notifications::BroadcastCount).to_not receive(:new).with(user: user) # only once

    event_logs_viewer = EventLogs::MarkAllAsViewed.new(user: user).call
    expect(event_logs_viewer).to be_success
  end

  context 'with high severity events' do
    before do
      [event_log1, event_log3].each do |log|
        log.update_column(:severity, %w[warning error].sample)
      end
    end

    it 'marks low severity notifications as (bulk) viewed and returns high-severity notifications as pending' do
      event_logs_viewer = EventLogs::MarkAllAsViewed.new(user: user).call

      expect(event_logs_viewer).to_not be_success
      expect(event_logs_viewer.viewed_notifications).to include(event_log2)
      expect(event_logs_viewer.pending_notifications).to include(event_log1, event_log3)
    end

    it 'marks all (including high) severity notifications as (bulk) viewed if passed' do
      event_logs_viewer = EventLogs::MarkAllAsViewed.new(user: user, include_high_severity: true).call

      expect(event_logs_viewer).to be_success
      expect(event_logs_viewer.viewed_notifications).to include(event_log1, event_log2, event_log3)
      expect(event_logs_viewer.pending_notifications).to be_blank
    end

    it 'broadcasts the notifications update' do
      expect(Admin::Notifications::BroadcastCount).to receive(:new).with(user: user)
      expect(Admin::Notifications::BroadcastCount).to_not receive(:new).with(user: user) # only once

      event_logs_viewer = EventLogs::MarkAllAsViewed.new(user: user).call
      expect(event_logs_viewer).to_not be_success
    end

    context 'with all high severity notifications' do
      before do
        [event_log1, event_log2, event_log3].each do |log|
          log.update_column(:severity, %w[warning error].sample)
        end
      end

      it 'returns all high-severity notifications as pending and does not broadcast notifications viewing update' do
        expect(Admin::Notifications::BroadcastCount).to_not receive(:new)

        event_logs_viewer = EventLogs::MarkAllAsViewed.new(user: user).call

        expect(event_logs_viewer).to_not be_success
        expect(event_logs_viewer.viewed_notifications).to be_blank
        expect(event_logs_viewer.pending_notifications).to include(event_log1, event_log3)
      end
    end
  end

  context 'with passed in lister options (filters)' do
    let!(:customer) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
    let!(:favourite_customer) { create(:favourite_customer, :random, favouriter: user, customer_profile: customer) }

    before do
      event_log2.update_columns(scopable_type: 'CustomerProfile', scopable_id: customer.id)
    end

    it 'only marks filtered notifications as (bulk) viewed' do
      lister_options = { query: customer.customer_name }
      event_logs_viewer = EventLogs::MarkAllAsViewed.new(user: user, lister_options: lister_options).call

      expect(event_logs_viewer).to be_success
      expect(event_logs_viewer.viewed_notifications).to include(event_log2)
      expect(event_logs_viewer.viewed_notifications).to_not include(event_log1, event_log3)
      expect(event_logs_viewer.pending_notifications).to be_blank
    end
  end
end
