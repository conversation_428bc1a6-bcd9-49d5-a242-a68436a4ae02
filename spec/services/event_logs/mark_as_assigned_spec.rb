require 'rails_helper'

RSpec.describe EventLogs::MarkAsAssigned, type: :service, event_logs: true do

  let!(:event_log) { create(:event_log, :random, event: EventLog::ASSIGNABLE_EVENTS.sample) }
  let!(:user) { create(:user, :random) }

  before do
    # mock event log viewer
    event_log_viewer = double(EventLogs::Mark<PERSON>Viewed)
    allow(EventLogs::MarkAsViewed).to receive(:new).and_return(event_log_viewer)
    allow(event_log_viewer).to receive(:call).and_return(true)
  end

  it 'saves an event log view for passed in the event log and user' do
    event_log_assigner = EventLogs::MarkAsAssigned.new(event_log: event_log, user: user).call

    expect(event_log_assigner).to be_success
    assigned_event_log = event_log_assigner.event_log

    expect(assigned_event_log.assigned_to).to be_present
    expect(assigned_event_log.assigned_to).to eq(user)
  end

  it 'makes a request to mark the event as viewed' do
    expect(EventLogs::<PERSON><PERSON><PERSON>iewed).to receive(:new).with(event_log: event_log, user: user)

    event_log_assigner = EventLogs::MarkAsAssigned.new(event_log: event_log, user: user).call
    expect(event_log_assigner).to be_success
  end

  context 'with already assigned event log' do
    before do
      event_log.update_column(:assigned_user_id, user.id)
    end

    it 'un-assigns the event log' do
      event_log_assigner = EventLogs::MarkAsAssigned.new(event_log: event_log, user: user).call

      expect(event_log_assigner).to be_success

      updated_event_log = event_log_assigner.event_log
      expect(updated_event_log.assigned_to).to be_blank
    end

    it 'errors when a different user is trying to assign the event log' do
      user2 = create(:user, :random)
      event_log_assigner = EventLogs::MarkAsAssigned.new(event_log: event_log, user: user2).call

      expect(event_log_assigner).to_not be_success
      expect(event_log_assigner.errors).to include('Event log is aready assigned by someone')
    end
  end

  context 'errors' do
    it 'returns with errors if event_log is missing' do
      event_log_assigner = EventLogs::MarkAsAssigned.new(event_log: nil, user: user).call

      expect(event_log_assigner).to_not be_success
      expect(event_log_assigner.errors).to include('Cannot mark as assigned without an event log')
    end

    it 'returns with errors if event_log is missing' do
      event_log_assigner = EventLogs::MarkAsAssigned.new(event_log: event_log, user: nil).call

      expect(event_log_assigner).to_not be_success
      expect(event_log_assigner.errors).to include('Cannot mark as assigned without user')
    end

    it 'returns with errors if the event log event is not an assignable one' do
      event_log.update_column(:event, (EventLog::VALID_EVENTS - EventLog::ASSIGNABLE_EVENTS).sample)
      event_log_assigner = EventLogs::MarkAsAssigned.new(event_log: event_log, user: user).call

      expect(event_log_assigner).to_not be_success
      expect(event_log_assigner.errors).to include('This event is not assignable')
    end
  end

end
