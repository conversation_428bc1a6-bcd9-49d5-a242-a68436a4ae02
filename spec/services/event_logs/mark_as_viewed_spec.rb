require 'rails_helper'

RSpec.describe EventLogs::<PERSON><PERSON><PERSON><PERSON><PERSON>, type: :service, event_logs: true do

  let!(:event_log1) { create(:event_log, :random) }
  let!(:event_log2) { create(:event_log, :random) }

  let!(:user1) { create(:user, :random) }
  let!(:user2) { create(:user, :random) }

  before do
    # mock notification broadcaster
    notifications_broadcaster = double(Admin::Notifications::BroadcastCount)
    allow(Admin::Notifications::BroadcastCount).to receive(:new).and_return(notifications_broadcaster)
    allow(notifications_broadcaster).to receive(:call).and_return(true)
  end

  it 'saves an event log view for passed in the event log and user' do
    event_log_viewer = EventLogs::MarkAsViewed.new(event_log: event_log2, user: user1).call

    expect(event_log_viewer).to be_success

    create_event_log_view = event_log_viewer.event_log_view
    expect(create_event_log_view).to be_present
    expect(create_event_log_view).to be_persisted
    expect(create_event_log_view).to be_a(EventLog::View)
    expect(create_event_log_view.event_log).to eq(event_log2)
    expect(create_event_log_view.user).to eq(user1)
    expect(create_event_log_view.in_bulk).to be_falsey
  end

  it 'makes a request to broadcast the notifications count for the viewing user' do
    expect(Admin::Notifications::BroadcastCount).to receive(:new).with(user: user1)

    event_log_viewer = EventLogs::MarkAsViewed.new(event_log: event_log2, user: user1).call
    expect(event_log_viewer).to be_success
  end

  context 'with existing event log view' do
    let!(:existing_event_log_view) { create(:event_log_view, event_log: event_log1, user: user2) }

    it 'returns the already create event log' do
      event_log_viewer = EventLogs::MarkAsViewed.new(event_log: event_log1, user: user2).call

      expect(event_log_viewer).to be_success

      returned_event_log_view = event_log_viewer.event_log_view
      expect(returned_event_log_view.id).to eq(existing_event_log_view.id)
    end
  end

  context 'Viewing in Bulk' do
    it 'saves the view with in_bulk set to true' do
      event_log_viewer = EventLogs::MarkAsViewed.new(event_log: event_log2, user: user1, in_bulk: true).call

      expect(event_log_viewer).to be_success

      create_event_log_view = event_log_viewer.event_log_view
      expect(create_event_log_view.in_bulk).to be_truthy
    end

    it 'does not makes a request to broadcast the notifications count for the viewing user with viewing in bulk' do
      expect(Admin::Notifications::BroadcastCount).to_not receive(:new).with(user: user1)

      event_log_viewer = EventLogs::MarkAsViewed.new(event_log: event_log2, user: user1, in_bulk: true).call
      expect(event_log_viewer).to be_success
    end
  end

  context 'errors' do
    it 'returns with errors if event_log is missing' do
      event_log_viewer = EventLogs::MarkAsViewed.new(event_log: nil, user: user1).call

      expect(event_log_viewer).to_not be_success
      expect(event_log_viewer.errors).to include('Cannot mark as viewed without an event log')
    end

    it 'returns with errors if event_log is missing' do
      event_log_viewer = EventLogs::MarkAsViewed.new(event_log: event_log1, user: nil).call

      expect(event_log_viewer).to_not be_success
      expect(event_log_viewer.errors).to include('Cannot mark as viewed without user')
    end
  end

end
