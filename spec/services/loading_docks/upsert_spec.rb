require 'rails_helper'

RSpec.describe LoadingDocks::Upsert, type: :service, loading_docks: true do

  let!(:customer) { create(:customer_profile, :random) }

  let!(:loading_dock_params) do
    {
      customer_profile_id: customer.id,
      code: SecureRandom.hex(10),
      file_url: Faker::Internet.url
    }
  end

  before do
    # mock supplier email
    email_sender = delayed_email_sender = double(Suppliers::Emails::SendLoadingDockEmail)
    allow(Suppliers::Emails::SendLoadingDockEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)
  end

  it 'creates a new loading dock for the passed in customer' do
    loading_dock_creator = LoadingDocks::Upsert.new(loading_dock_params: loading_dock_params).call

    expect(loading_dock_creator).to be_success

    created_loading_dock = loading_dock_creator.loading_dock
    expect(created_loading_dock).to be_present
    expect(created_loading_dock).to be_persisted
    expect(created_loading_dock).to be_a(LoadingDock)
    expect(created_loading_dock.customer_profile).to eq(customer)
    expect(created_loading_dock.code).to eq(loading_dock_params[:code])
    expect(created_loading_dock.file_url).to eq(loading_dock_params[:file_url])
  end

  context 'with a passed in order' do
    let!(:order) { create(:order, :random, customer_profile: customer) }
    let!(:order_line1) { create(:order_line, :random, :with_supplier, order: order) }
    let!(:order_line2) { create(:order_line, :random, :with_supplier, order: order) }

    it 'attaches the newly created loading dock to the order' do
      loading_dock_creator = LoadingDocks::Upsert.new(loading_dock_params: loading_dock_params, order: order).call

      expect(loading_dock_creator).to be_success

      created_loading_dock = loading_dock_creator.loading_dock
      expect(created_loading_dock.order_ids).to include(order.id)

      order.reload
      expect(order.loading_dock_id).to eq(created_loading_dock.id)
      expect(order.loading_dock.code).to eq(loading_dock_params[:code])
    end

    it 'notfies the suppliers of the orders' do
      expect(Suppliers::Emails::SendLoadingDockEmail).to receive(:new).with(order: order, supplier: order_line1.supplier_profile)
      expect(Suppliers::Emails::SendLoadingDockEmail).to receive(:new).with(order: order, supplier: order_line2.supplier_profile)

      loading_dock_creator = LoadingDocks::Upsert.new(loading_dock_params: loading_dock_params, order: order).call
      expect(loading_dock_creator).to be_success
    end
  end

  context 'with an existing loading dock' do
    let!(:loading_dock) { create(:loading_dock, :random, customer_profile: customer) }

    let!(:loading_dock_params) do
      {
        id: loading_dock.id,
        customer_profile_id: loading_dock.customer_profile_id,
        code: SecureRandom.hex(10),
        file_url: Faker::Internet.url
      }
    end

    it 'updates the existing loading dock' do
      loading_dock_updator = LoadingDocks::Upsert.new(loading_dock: loading_dock, loading_dock_params: loading_dock_params).call

      expect(loading_dock_updator).to be_success

      updated_loading_dock = loading_dock_updator.loading_dock
      expect(updated_loading_dock).to be_present
      expect(updated_loading_dock.id).to eq(loading_dock.id)
      expect(updated_loading_dock.code).to eq(loading_dock_params[:code])
      expect(updated_loading_dock.file_url).to eq(loading_dock_params[:file_url])
    end

    context 'with a passed in order' do
      let!(:order) { create(:order, :random, customer_profile: customer) }
      let!(:order_line1) { create(:order_line, :random, :with_supplier, order: order) }
      let!(:order_line2) { create(:order_line, :random, :with_supplier, order: order) }

      it 'attaches the updated loading dock to the order' do
        loading_dock_updator = LoadingDocks::Upsert.new(loading_dock: loading_dock, loading_dock_params: loading_dock_params, order: order).call

        expect(loading_dock_updator).to be_success

        updated_loading_dock = loading_dock_updator.loading_dock
        expect(updated_loading_dock.orders).to include(order)

        order.reload
        expect(order.loading_dock_id).to eq(loading_dock.id)
        expect(order.loading_dock.code).to eq(loading_dock_params[:code])
        expect(order.loading_dock.code).to eq(updated_loading_dock.code)
      end

      it 'notfies the suppliers of the orders' do
        expect(Suppliers::Emails::SendLoadingDockEmail).to receive(:new).with(order: order, supplier: order_line1.supplier_profile)
        expect(Suppliers::Emails::SendLoadingDockEmail).to receive(:new).with(order: order, supplier: order_line2.supplier_profile)

        loading_dock_creator = LoadingDocks::Upsert.new(loading_dock_params: loading_dock_params, order: order).call
        expect(loading_dock_creator).to be_success
      end
    end
  end

  context 'errors' do
    it 'returns with errors if customer is not passed in (via loading dock params)' do
      malformed_loading_dock_params = loading_dock_params.except(:customer_profile_id)

      loading_dock_upserter = LoadingDocks::Upsert.new(loading_dock_params: malformed_loading_dock_params).call

      expect(loading_dock_upserter).to_not be_success
      expect(loading_dock_upserter.errors).to include('Cannot save loading dock without a customer')
    end

    it 'returns with errors if passed in loading dock customer is not the same as passed in customer (via loading_dock_params)' do
      loading_dock = create(:loading_dock, :random)

      loading_dock_upserter = LoadingDocks::Upsert.new(loading_dock: loading_dock, loading_dock_params: loading_dock_params).call

      expect(loading_dock_upserter).to_not be_success
      expect(loading_dock_upserter.errors).to include('You do not have access to this loading dock')
    end

    it 'returns with errors if passed in order does not belong to passed in customer (via loading_dock_params)' do
      order = create(:order, :random)

      loading_dock_upserter = LoadingDocks::Upsert.new(loading_dock_params: loading_dock_params, order: order).call

      expect(loading_dock_upserter).to_not be_success
      expect(loading_dock_upserter.errors).to include('You do not have access to this order')
    end

    it 'returns with errors if loading dock cannot be successfully created' do
      malformed_loading_dock_params = loading_dock_params.except(:code, :file_url)

      loading_dock_upserter = LoadingDocks::Upsert.new(loading_dock_params: malformed_loading_dock_params).call

      expect(loading_dock_upserter).to_not be_success
      expect(loading_dock_upserter.errors).to include('Must have at least a code or a file')
    end
  end

end