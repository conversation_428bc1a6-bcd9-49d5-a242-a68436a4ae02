require 'rails_helper'

RSpec.describe LoadingDocks::HandleOrderRequest, type: :service, orders: true, loading_docks: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :random, customer_profile: customer) }
  let!(:random_salt) { SecureRandom.hex(7) }

  let!(:request_uuid) do
    Digest::MD5.hexdigest(order.id.to_s + customer.id.to_s + random_salt)
  end

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:random_salt).and_return(random_salt)
  end

  it 'returns successfully with the order and customer for a params' do
    request_handler = LoadingDocks::HandleOrderRequest.new(order: order, request_uuid: request_uuid).call

    expect(request_handler).to be_success
    expect(request_handler.order).to eq(order)
    expect(request_handler.customer).to eq(customer)
  end

  context 'errors' do
    it 'returns with errors if order is missing' do
      request_handler = LoadingDocks::HandleOrderRequest.new(order: nil, request_uuid: request_uuid).call

      expect(request_handler).to_not be_success
      expect(request_handler.errors).to include('Could not find order')
    end

    it 'returns with errors if request UUID is malformed' do
      wrong_order_request = [true, false].sample
      malformed_request_uuid = case
      when wrong_order_request
        order2 = create(:order, :random)
        Digest::MD5.hexdigest(order2.id.to_s + order2.customer_profile_id.to_s + yordar_credentials(:random_salt))
      else # random request_uuid
        SecureRandom.hex(7)
      end
      request_handler = LoadingDocks::HandleOrderRequest.new(order: order, request_uuid: malformed_request_uuid).call

      expect(request_handler).to_not be_success
      expect(request_handler.errors).to include('Invalid Request')
    end
  end

end