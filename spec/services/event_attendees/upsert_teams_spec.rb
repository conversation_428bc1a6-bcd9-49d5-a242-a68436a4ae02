require 'rails_helper'

RSpec.describe EventAttendees::UpsertTeams, type: :service, team_orders: true, event_attendees: true, event_teams: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }

  it 'creates new event teams (using names) for the team admins and attaches them to the event attendee' do
    team_names = [Faker::Name.name, Faker::Name.name]
    team_upserter = EventAttendees::UpsertTeams.new(event_attendee: event_attendee, team_params: team_names, team_admin: team_admin).call

    expect(team_upserter).to be_success
    expect(team_admin.reload.event_teams).to be_present # creates team events at the team admin level

    attendee_teams = event_attendee.reload.event_teams
    expect(attendee_teams).to be_present
    expect(attendee_teams.map(&:name)).to match_array(team_names)
  end

  context 'with exiting team admin event teams' do
    let!(:event_team1) { create(:event_team, :random, customer_profile: team_admin) }
    let!(:event_team2) { create(:event_team, :random, customer_profile: team_admin) }

    it 'attaches existing event attendees if IDs are passed' do
      team_ids = [event_team1, event_team2].map(&:id)
      team_upserter = EventAttendees::UpsertTeams.new(event_attendee: event_attendee, team_params: team_ids, team_admin: team_admin).call

      expect(team_upserter).to be_success

      attendee_teams = event_attendee.reload.event_teams
      expect(attendee_teams).to be_present
      expect(attendee_teams).to match_array([event_team1, event_team2])
    end

    it 'attaches existing event attendees if same names are passed' do
      team_names = [event_team1, event_team2].map(&:name)
      team_upserter = EventAttendees::UpsertTeams.new(event_attendee: event_attendee, team_params: team_names, team_admin: team_admin).call

      expect(team_upserter).to be_success

      attendee_teams = event_attendee.reload.event_teams
      expect(attendee_teams).to be_present
      expect(attendee_teams).to match_array([event_team1, event_team2])
    end

    it 'creates new event teams if new names are passed' do
      new_name = Faker::Name.name
      team_names = [event_team1, event_team2].map(&:name) + [new_name]
      team_upserter = EventAttendees::UpsertTeams.new(event_attendee: event_attendee, team_params: team_names, team_admin: team_admin).call

      expect(team_upserter).to be_success

      attendee_teams = event_attendee.reload.event_teams
      expect(attendee_teams).to be_present
      expect(attendee_teams.size).to eq(3)
      expect(attendee_teams.map(&:name)).to match_array([event_team1, event_team2].map(&:name) + [new_name])
    end

    it 'removed currently attached teams if not passed' do
      team_ids = [event_team1].map(&:id)
      team_upserter = EventAttendees::UpsertTeams.new(event_attendee: event_attendee, team_params: team_ids, team_admin: team_admin).call

      expect(team_upserter).to be_success

      attendee_teams = event_attendee.reload.event_teams
      expect(attendee_teams).to be_present
      expect(attendee_teams).to include(event_team1)
      expect(attendee_teams).to_not include(event_team2)
    end
  end

end
