require 'rails_helper'

RSpec.describe EventAttendees::Remove, type: :service, event_attendees: true, team_orders: true do

  let(:team_admin) { create(:customer_profile, :random) }
  let(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }

  it 'removes the specified event attendee' do
    attendee_remover = EventAttendees::Remove.new(event_attendee: event_attendee, team_admin: team_admin).call

    expect(attendee_remover).to be_success
    removed_attendee = attendee_remover.event_attendee
    expect(removed_attendee.id).to eq(event_attendee.id)
    expect(removed_attendee).to_not be_active
  end

  context 'errors' do
    it 'cannot remove without a specified event_attendee' do
      attendee_remover = EventAttendees::Remove.new(event_attendee: nil, team_admin: team_admin).call

      expect(attendee_remover).to_not be_success
      expect(attendee_remover.errors).to include('Could not find a matching contact')
    end

    it 'cannot remove without a specified team admin' do
      attendee_remover = EventAttendees::Remove.new(event_attendee: event_attendee, team_admin: nil).call

      expect(attendee_remover).to_not be_success
      expect(attendee_remover.errors).to include('Could not find a matching contact')
    end

    it 'cannot remove not belonging to the specified team_admin' do
      other_team_admin = create(:customer_profile, :random)
      event_attendee.update_column(:team_admin_id, other_team_admin.id)
      attendee_remover = EventAttendees::Remove.new(event_attendee: event_attendee, team_admin: team_admin).call

      expect(attendee_remover).to_not be_success
      expect(attendee_remover.errors).to include('This contact does not belong to you')
    end

    it 'cannot remove an already in-active event attendee' do
      event_attendee.update_column(:active, false)
      attendee_remover = EventAttendees::Remove.new(event_attendee: event_attendee, team_admin: team_admin).call

      expect(attendee_remover).to_not be_success
      expect(attendee_remover.errors).to include('Cannot remove an already removed contact')
    end
  end

end
