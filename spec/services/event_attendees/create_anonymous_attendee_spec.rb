require 'rails_helper'

RSpec.describe EventAttendees::CreateAnonymousAttendee, type: :service, team_orders: true, customer: true do

  let!(:team_admin) { create(:customer_profile, :random) }

  let(:attendee_params) { { first_name: Faker::Name.first_name, last_name: Faker::Name.last_name, email: Faker::Internet.email } }

  it 'create a new contact (event_attendee) for a team admin' do
    attendee_creator = EventAttendees::CreateAnonymousAttendee.new(team_admin: team_admin, attendee_params: attendee_params).call

    expect(attendee_creator).to be_success
    created_event_attendee = attendee_creator.event_attendee
    expect(created_event_attendee).to be_present
    expect(created_event_attendee.first_name).to eq(attendee_params[:first_name])
    expect(created_event_attendee.last_name).to eq(attendee_params[:last_name])
    expect(created_event_attendee.email).to eq(attendee_params[:email])
    expect(created_event_attendee.team_admin).to eq(team_admin)
  end

  context 'with existing event attendee' do
    let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin, email: attendee_params[:email]) }

    it 'does not create a new event attendee and does not update existing' do
      attendee_creator = EventAttendees::CreateAnonymousAttendee.new(team_admin: team_admin, attendee_params: attendee_params).call

      expect(attendee_creator).to_not be_success
      expect(attendee_creator.errors).to include('You are already registered as a contact')
    end
  end
end
