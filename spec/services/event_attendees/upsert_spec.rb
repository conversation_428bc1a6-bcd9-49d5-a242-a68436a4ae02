require 'rails_helper'

RSpec.describe EventAttendees::Upsert, type: :service, team_orders: true, event_attendees: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:event_attendee_params) { { first_name: Faker::Name.first_name, last_name: Faker::Name.last_name, email: Faker::Internet.email } }

  it 'creates an event attendee' do
    attendee_creator = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params, team_admin: team_admin).call

    expect(attendee_creator).to be_success
    created_attendee = attendee_creator.event_attendee

    expect(created_attendee).to be_present
    expect(created_attendee).to be_a(EventAttendee)
  end

  it 'creates an event attendee with default params' do
    attendee_creator = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params, team_admin: team_admin).call

    expect(attendee_creator).to be_success
    created_attendee = attendee_creator.event_attendee

    expect(created_attendee).to be_present
    expect(created_attendee).to be_a(EventAttendee)
    expect(created_attendee).to be_active
  end

  it 'creates an event attendee attached to the team admin' do
    attendee_creator = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params, team_admin: team_admin).call

    expect(attendee_creator).to be_success
    created_attendee = attendee_creator.event_attendee

    expect(created_attendee.team_admin).to eq(team_admin)
  end

  it 'creates a new event attendee and associates them to teams' do
    event_attendee_params_with_teams = event_attendee_params.merge({ teams: [Faker::Name.name, Faker::Name.name] })
    attendee_creator = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params_with_teams, team_admin: team_admin).call

    expect(attendee_creator).to be_success
    created_attendee = attendee_creator.event_attendee
    attendee_teams = created_attendee.event_teams

    expect(attendee_teams).to be_present
    expect(attendee_teams.map(&:name)).to match_array(event_attendee_params_with_teams[:teams])
  end

  context 'with existing event attendee' do
    let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }
    let!(:event_attendee_params) { { first_name: Faker::Name.first_name, last_name: Faker::Name.last_name, email: event_attendee.email } }

    it 'updates the existing event attendee record' do
      new_email = Faker::Internet.email
      updated_event_attendee_params = event_attendee_params.merge({ email: new_email })
      attendee_updater = EventAttendees::Upsert.new(event_attendee: event_attendee, event_attendee_params: updated_event_attendee_params, team_admin: team_admin).call

      expect(attendee_updater).to be_success
      updated_attendee = attendee_updater.event_attendee
      expect(updated_attendee.id).to eq(event_attendee.id)
      expect(updated_attendee.first_name).to eq(event_attendee_params[:first_name])
      expect(updated_attendee.last_name).to eq(event_attendee_params[:last_name])
      expect(updated_attendee.email).to eq(new_email)
    end

    it 'updates the existing event attendee record based on passed in email' do
      attendee_updater = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params, team_admin: team_admin).call

      expect(attendee_updater).to be_success
      updated_attendee = attendee_updater.event_attendee
      expect(updated_attendee.id).to eq(event_attendee.id)
      expect(updated_attendee.first_name).to eq(event_attendee_params[:first_name])
      expect(updated_attendee.last_name).to eq(event_attendee_params[:last_name])
      expect(updated_attendee.email).to eq(event_attendee.email)
    end

    it 'updates existing event attendee with passed in teams' do
      event_attendee_params_with_teams = event_attendee_params.merge({ teams: [Faker::Name.name, Faker::Name.name] })
      attendee_updater = EventAttendees::Upsert.new(event_attendee: event_attendee, event_attendee_params: event_attendee_params_with_teams, team_admin: team_admin).call

      expect(attendee_updater).to be_success
      updated_attendee = attendee_updater.event_attendee
      attendee_teams = updated_attendee.event_teams
      expect(attendee_teams).to be_present

      expect(updated_attendee.event_teams.map(&:name)).to match_array(event_attendee_params_with_teams[:teams])
    end

    it 're-activates an in-active attendee' do
      event_attendee.update_column(:active, false)
      expect(event_attendee).to_not be_active
      attendee_updater = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params, team_admin: team_admin).call

      expect(attendee_updater).to be_success
      updated_attendee = attendee_updater.event_attendee
      expect(updated_attendee.id).to eq(event_attendee.id)
      expect(updated_attendee).to be_active
    end

    it 'recreates a new attendee for another team admin with same email' do
      other_team_admin = create(:customer_profile, :random)
      attendee_creator = EventAttendees::Upsert.new(event_attendee_params: event_attendee_params, team_admin: other_team_admin).call

      expect(attendee_creator).to be_success
      created_attendee = attendee_creator.event_attendee
      expect(created_attendee.id).to_not eq(event_attendee.id)
      expect(created_attendee.team_admin).to eq(other_team_admin)
      expect(created_attendee.email).to eq(event_attendee.email)
    end
  end
end
