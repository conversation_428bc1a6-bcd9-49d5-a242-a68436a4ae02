require 'rails_helper'

RSpec.describe EventAttendees::RemoveAttendeeFromTeam, type: :service, event_attendees: true, event_teams: true, team_orders: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:event_team) { create(:event_team, :random, customer_profile: team_admin) }
  let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }

  before do
    event_attendee.event_teams << event_team
  end

  it 'removes the attendee from the team' do
    attendee_remover = EventAttendees::RemoveAttendeeFromTeam.new(event_attendee: event_attendee, event_team: event_team).call

    expect(attendee_remover).to be_success
    expect(event_attendee.reload.event_teams).to_not include(event_team)
  end

  context 'errors' do
    it 'cannot remove from a missing team' do
      attendee_remover = EventAttendees::RemoveAttendeeFromTeam.new(event_attendee: event_attendee, event_team: nil).call

      expect(attendee_remover).to_not be_success
      expect(attendee_remover.errors).to include('Cannot remove from missing team')
    end

    it 'cannot remove a missing attendee' do
      attendee_remover = EventAttendees::RemoveAttendeeFromTeam.new(event_attendee: nil, event_team: event_team).call

      expect(attendee_remover).to_not be_success
      expect(attendee_remover.errors).to include('Cannot remove a missing attendee')
    end

    it 'cannot remove an attendee not belonging to the team' do
      event_attendee.event_teams -= [event_team]
      attendee_remover = EventAttendees::RemoveAttendeeFromTeam.new(event_attendee: event_attendee, event_team: event_team).call

      expect(attendee_remover).to_not be_success
      expect(attendee_remover.errors).to include('Attendee not part of the team')
    end

    it 'cannot remove an attendee not belonging to the same team admin as the event team' do
      another_event_team = create(:event_team, :random)
      attendee_remover = EventAttendees::RemoveAttendeeFromTeam.new(event_attendee: event_attendee, event_team: another_event_team).call

      expect(attendee_remover).to_not be_success
      expect(attendee_remover.errors).to include('Cannot remove an attendee not belonging to the same team admin as the team')
    end
  end

end
