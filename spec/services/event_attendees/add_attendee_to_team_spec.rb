require 'rails_helper'

RSpec.describe EventAttendees::AddAttendeeToTeam, type: :service, event_attendees: true, event_teams: true, team_orders: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:event_team) { create(:event_team, :random, customer_profile: team_admin) }
  let!(:event_attendee) { create(:event_attendee, :random, team_admin: team_admin) }

  it 'adds the attendee to the team' do
    attendee_adder = EventAttendees::AddAttendeeToTeam.new(event_attendee: event_attendee, event_team: event_team).call

    expect(attendee_adder).to be_success
    expect(event_attendee.reload.event_teams).to include(event_team)
  end

  context 'errors' do
    it 'cannot add to a missing team' do
      attendee_adder = EventAttendees::AddAttendeeToTeam.new(event_attendee: event_attendee, event_team: nil).call

      expect(attendee_adder).to_not be_success
      expect(attendee_adder.errors).to include('Cannot add to missing team')
    end

    it 'cannot add to a missing attendee' do
      attendee_adder = EventAttendees::AddAttendeeToTeam.new(event_attendee: nil, event_team: event_team).call

      expect(attendee_adder).to_not be_success
      expect(attendee_adder.errors).to include('Cannot add a missing attendee')
    end

    it 'cannot add to an attendee already belonging to the team' do
      event_attendee.event_teams << event_team
      attendee_adder = EventAttendees::AddAttendeeToTeam.new(event_attendee: event_attendee, event_team: event_team).call

      expect(attendee_adder).to_not be_success
      expect(attendee_adder.errors).to include('Attendee already part of the team')
    end

    it 'cannot add to an attendee not belonging to the same team admin as the event team' do
      another_event_team = create(:event_team, :random)
      attendee_adder = EventAttendees::AddAttendeeToTeam.new(event_attendee: event_attendee, event_team: another_event_team).call

      expect(attendee_adder).to_not be_success
      expect(attendee_adder.errors).to include('Cannot add an attendee not belonging to the same team admin as the team')
    end
  end

end
