require 'rails_helper'

RSpec.describe EventTeams::Upsert, type: :service, event_teams: true, event_attendees: true, team_orders: true do

  let!(:team_admin) { create(:customer_profile, :random) }

  it 'creates a new event team for a team admin' do
    team_params = { name: Faker::Name.name }
    team_creator = EventTeams::Upsert.new(team_admin: team_admin, team_params: team_params).call

    expect(team_creator).to be_success
    created_event_team = team_creator.event_team
    expect(created_event_team).to be_present
    expect(created_event_team).to be_is_a(EventTeam)
    expect(created_event_team.name).to eq(team_params[:name])
    expect(created_event_team.customer_profile).to eq(team_admin)
    expect(created_event_team).to be_active
  end

  it 'cannot upsert without a team admin' do
    team_params = { name: Faker::Name.name }
    team_updator = EventTeams::Upsert.new(team_admin: nil, team_params: team_params).call

    expect(team_updator).to_not be_success
    expect(team_updator.errors).to include('Cannot upsert a team without a team admin')
  end

  context 'with existing event team' do
    let!(:event_team) { create(:event_team, :random, customer_profile: team_admin) }

    it 'updates the event team' do
      team_params = { name: Faker::Name.name }
      team_updator = EventTeams::Upsert.new(team_admin: team_admin, team_params: team_params, event_team: event_team).call

      expect(team_updator).to be_success
      updated_event_team = team_updator.event_team
      expect(updated_event_team.id).to eq(event_team.id)
      expect(updated_event_team.name).to eq(team_params[:name])
    end

    it 'does create a duplicate team with the same name (case-insensitive)' do
      team_params = { name: event_team.name.split('').map{|char| [true, false].sample ? char.upcase : char.downcase }.join('') }
      team_updator = EventTeams::Upsert.new(team_admin: team_admin, team_params: team_params).call

      expect(team_updator).to be_success
      updated_event_team = team_updator.event_team
      expect(updated_event_team.id).to eq(event_team.id)
      expect(updated_event_team.name).to eq(team_params[:name])
    end

    it 'cannot update an event team not belonging to the team admin' do
      other_team_admin = create(:customer_profile, :random)
      team_params = { name: Faker::Name.name }
      team_updator = EventTeams::Upsert.new(team_admin: other_team_admin, team_params: team_params, event_team: event_team).call

      expect(team_updator).to_not be_success
      expect(team_updator.errors).to include('You don\'t have access to this team')
    end
  end

end
