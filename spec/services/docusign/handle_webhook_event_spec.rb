require 'rails_helper'

RSpec.describe Docusign::HandleWebhookEvent, type: :service, docusign: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:supplier_agreement_document) { create(:supplier_agreement_document, :random, supplier_profile: supplier, status: 'sent') }

  let!(:updated_status) { (SupplierAgreementDocument::KNOWN_STATUSES - ['sent']).sample }

  before do
    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  let!(:webhook_event) do
    {
      'DocuSignEnvelopeInformation' => {
        'EnvelopeStatus' => {
          'EnvelopeID' => supplier_agreement_document.docusign_envelope_id,
          'Status' => updated_status
        }
      }
    }
  end

  it 'updates the status of the docusign document' do
    webhook_handler = Docusign::HandleWebhookEvent.new(event: webhook_event).call

    expect(webhook_handler).to be_success
    expect(supplier_agreement_document.reload.status).to eq(updated_status)
  end

  it 'logs a `Supplier Agreement Signed` event if status is `completed`', event_logs: true do
    completed_webhook_event = {
      'DocuSignEnvelopeInformation' => {
        'EnvelopeStatus' => {
          'EnvelopeID' => supplier_agreement_document.docusign_envelope_id,
          'Status' => 'completed'
        }
      }
    }
    expect(EventLogs::Create).to receive(:new).with(event_object: supplier, event: 'supplier-agreement-signed')

    webhook_handler = Docusign::HandleWebhookEvent.new(event: completed_webhook_event).call
    expect(webhook_handler).to be_success
  end

  context 'errors' do
    it 'returns with errors if the webhook event is missing' do
      webhook_handler = Docusign::HandleWebhookEvent.new(event: [nil, {}].sample).call

      expect(webhook_handler).to_not be_success
      expect(webhook_handler.errors).to include('Missing webhook event')
    end

    it 'returns with errors if the webhook event refers to a missing document' do
      invalid_webhook_event = {
        'DocuSignEnvelopeInformation' => {
          'EnvelopeStatus' => {
            'EnvelopeID' => SecureRandom.uuid,
            'Status' => updated_status
          }
        }
      }
      webhook_handler = Docusign::HandleWebhookEvent.new(event: invalid_webhook_event).call

      expect(webhook_handler).to_not be_success
      expect(webhook_handler.errors).to include('Could not find document')
    end
  end

end