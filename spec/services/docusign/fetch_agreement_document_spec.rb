require 'rails_helper'

RSpec.describe Docusign::FetchAgreementDocument, type: :service, docusign: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:agreement_document) { create(:supplier_agreement_document, :random, supplier_profile: supplier) }

  let!(:api_instance) { double(DocuSign_eSign::EnvelopesApi) }

  # mock Docusign API calls
  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :rest_host).and_return('DOCUSIGN_REST_HOST')
    allow_any_instance_of(Docusign::FetchAgreementDocument).to receive(:login).and_return(true) # mock login

    allow_any_instance_of(Docusign::FetchAgreementDocument).to receive(:api_instance).and_return(api_instance)
    allow(api_instance).to receive(:get_document).and_return("fetched-document-with-id-#{agreement_document.docusign_envelope_id}")
  end

  it 'expects to login to the DocuSignDocument' do
    document_fetcher = Docusign::FetchAgreementDocument.new(envelope_id: agreement_document.docusign_envelope_id)

    expect(document_fetcher).to receive(:login)

    fetcher_response = document_fetcher.call

    expect(fetcher_response).to be_success
  end

  it 'expects to get document from DocuSign_eSign' do
    expect(api_instance).to receive(:get_document).with(anything, 'COMBINED', agreement_document.docusign_envelope_id)

    document_fetcher = Docusign::FetchAgreementDocument.new(envelope_id: agreement_document.docusign_envelope_id).call
    expect(document_fetcher).to be_success
  end

  it 'returns the fetched document based on passed ID' do
    document_fetcher = Docusign::FetchAgreementDocument.new(envelope_id: agreement_document.docusign_envelope_id).call

    expect(document_fetcher).to be_success
    expect(document_fetcher.docusign_document).to eq("fetched-document-with-id-#{agreement_document.docusign_envelope_id}")
  end

  context 'errors' do
    it 'returns error if docusign (host) isn\'t configured' do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :rest_host).and_return(nil)

      document_fetcher = Docusign::FetchAgreementDocument.new(envelope_id: agreement_document.docusign_envelope_id).call

      expect(document_fetcher).to_not be_success
      expect(document_fetcher.errors).to include('Cannot access the document server')
    end

    it 'returns an error for a missing envelope_id' do
      document_fetcher = Docusign::FetchAgreementDocument.new(envelope_id: nil).call

      expect(document_fetcher).to_not be_success
      expect(document_fetcher.errors).to include('Cannot fetch a document without an id')
    end

    it 'returns an error if the login failed' do
      allow_any_instance_of(Docusign::FetchAgreementDocument).to receive(:login).and_raise(DocuSign_eSign::ApiError.new)

      document_fetcher = Docusign::FetchAgreementDocument.new(envelope_id: agreement_document.docusign_envelope_id).call

      expect(document_fetcher).to_not be_success
      expect(document_fetcher.errors).to include("Exception when calling EnvelopesApi->get_document for envelope_id = #{agreement_document.docusign_envelope_id}")
    end

    it 'returns an error if getting the document failed' do
      allow(api_instance).to receive(:get_document).and_raise(DocuSign_eSign::ApiError.new)

      document_fetcher = Docusign::FetchAgreementDocument.new(envelope_id: agreement_document.docusign_envelope_id).call

      expect(document_fetcher).to_not be_success
      expect(document_fetcher.errors).to include("Exception when calling EnvelopesApi->get_document for envelope_id = #{agreement_document.docusign_envelope_id}")
    end
  end

end
