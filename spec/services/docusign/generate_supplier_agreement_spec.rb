require 'rails_helper'

RSpec.describe Docusign::GenerateSupplierAgreement, type: :service, docusign: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:agreement_document) { create(:supplier_agreement_document, :random, supplier_profile: supplier) }

  let!(:api_instance) { double(DocuSign_eSign::EnvelopesApi) }
  let!(:docusign_envelope) { OpenStruct.new(envelope_id: SecureRandom.uuid, status: 'generated') }
  let!(:docusign_fields) { %w[company_name company_address abn_acn commission_rate] }

  # mock Docusign API calls
  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :rest_host).and_return('DOCUSIGN_REST_HOST')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :template_id).and_return('template-id')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :supplier_role_name).and_return('supplier-role-name')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :yordar_email).and_return('yordar-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :yordar_name).and_return('yordar-name')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :yordar_role_name).and_return('yordar-role-name')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :field_list).and_return(docusign_fields.join('|'))
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :webhook_url).and_return('webhook-url')

    allow_any_instance_of(Docusign::GenerateSupplierAgreement).to receive(:login).and_return(true) # mock login

    allow_any_instance_of(Docusign::GenerateSupplierAgreement).to receive(:api_instance).and_return(api_instance)
    allow(api_instance).to receive(:create_envelope).and_return(docusign_envelope)
  end

  it 'expects to login to the DocuSign API' do
    document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier)

    expect(document_generator).to receive(:login)

    fetcher_response = document_generator.call
    expect(fetcher_response).to be_success
  end

  it 'expects to create docusign document (envelope)' do
    expect(api_instance).to receive(:create_envelope).with(anything, anything) # (account_id (from login), envelope_definition)

    document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call
    expect(document_generator).to be_success
  end

  context 'envelope definition' do
    it 'generates the yordar role' do
      text_tabs = docusign_fields.map do |field|
        DocuSign_eSign::Text.new(tabLabel: field, value: supplier.public_send(field))
      end.compact
      docusign_tabs = DocuSign_eSign::Tabs.new(textTabs: text_tabs)
      expect(DocuSign_eSign::TemplateRole).to receive(:new).with(email: 'yordar-email', name: 'yordar-name', roleName: 'yordar-role-name', tabs: docusign_tabs)
      expect(DocuSign_eSign::TemplateRole).to receive(:new).with(email: anything, name: anything, roleName: anything) # for supplier role

      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call
      expect(document_generator).to be_success
    end

    it 'generates the supplier role' do
      expect(DocuSign_eSign::TemplateRole).to receive(:new) # for yordar role
      expect(DocuSign_eSign::TemplateRole).to receive(:new).with(email: supplier.email, name: supplier.name, roleName: 'supplier-role-name')

      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call
      expect(document_generator).to be_success
    end

    it 'generates the supplier role only with 1 email address - even if supplier has multiple email addresses' do
      separator = [' ; ', ';', ',', "\r", "\n"].sample
      supplier_emails = 3.times.map{|_| Faker::Internet.email }
      supplier.update_column(:email, supplier_emails.join(separator))

      expect(DocuSign_eSign::TemplateRole).to receive(:new).with(email: anything, name: anything, roleName: anything, tabs: anything) # for yordar role
      expect(DocuSign_eSign::TemplateRole).to receive(:new).with(email: supplier_emails.first.strip, name: supplier.name, roleName: 'supplier-role-name')

      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call
      expect(document_generator).to be_success
    end

    it 'generates a docusign event notification' do
      envelope_events = Docusign::GenerateSupplierAgreement::ENVELOPE_STATUSES.map do |envelope_status|
        DocuSign_eSign::EnvelopeEvent.new(envelopeEventStatusCode: envelope_status)
      end
      expect(DocuSign_eSign::EventNotification).to receive(:new).with(logging: true, envelopeEvents: envelope_events, url: 'webhook-url')

      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call
      expect(document_generator).to be_success
    end

    it 'generates a docusign envelope definition' do
      text_tabs = docusign_fields.map do |field|
        DocuSign_eSign::Text.new(tabLabel: field, value: supplier.public_send(field))
      end.compact
      docusign_tabs = DocuSign_eSign::Tabs.new(textTabs: text_tabs)
      yordar_role = DocuSign_eSign::TemplateRole.new(email: 'yordar-email', name: 'yordar-name', roleName: 'yordar-role-name', tabs: docusign_tabs)

      supplier_role = DocuSign_eSign::TemplateRole.new(email: supplier.email, name: supplier.name, roleName: 'supplier-role-name')

      envelope_events = Docusign::GenerateSupplierAgreement::ENVELOPE_STATUSES.map do |envelope_status|
        DocuSign_eSign::EnvelopeEvent.new(envelopeEventStatusCode: envelope_status)
      end
      event_notification = DocuSign_eSign::EventNotification.new(logging: true, envelopeEvents: envelope_events, url: 'webhook-url')

      expect(DocuSign_eSign::EnvelopeDefinition).to receive(:new).with(emailSubject: "Yordar - Supplier Agreement #{Time.zone.now.year}", templateId: 'template-id', templateRoles: [yordar_role, supplier_role], eventNotification: event_notification, status: 'sent')

      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call
      expect(document_generator).to be_success
    end
  end

  it 'returns the generated docusign document (envelope)' do
    document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call

    expect(document_generator).to be_success
    generated_docusign_document = document_generator.docusign_document
    expect(generated_docusign_document.envelope_id).to eq(docusign_envelope.envelope_id)
    expect(generated_docusign_document.status).to eq(docusign_envelope.status)
  end

  it 'saves and returns the supplier aggreement document' do
    document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call

    expect(document_generator).to be_success
    generated_agreement_document = document_generator.agreement_document
    expect(generated_agreement_document).to be_present
    expect(generated_agreement_document.supplier_profile).to eq(supplier)
    expect(generated_agreement_document.docusign_envelope_id).to eq(docusign_envelope.envelope_id)
    expect(generated_agreement_document.status).to eq(docusign_envelope.status)
    expect(generated_agreement_document).to be_persisted
  end

  context 'errors' do
    it 'returns error if docusign (host) isn\'t configured' do
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:docusign, :rest_host).and_return(nil)

      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call

      expect(document_generator).to_not be_success
      expect(document_generator.errors).to include('Cannot access the document server')
    end

    it 'returns an error for a missing supplier' do
      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: nil).call

      expect(document_generator).to_not be_success
      expect(document_generator.errors).to include('Cannot generate a document wihtout a supplier')
    end

    it 'returns an error if the login failed' do
      allow_any_instance_of(Docusign::GenerateSupplierAgreement).to receive(:login).and_raise(DocuSign_eSign::ApiError.new)

      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call

      expect(document_generator).to_not be_success
      expect(document_generator.errors).to include("Exception when calling EnvelopesApi->create_envelope for supplier with ID #{supplier.id}")
    end

    it 'returns an error if generating the docusign document (envelope) failed' do
      allow(api_instance).to receive(:create_envelope).and_raise(DocuSign_eSign::ApiError.new)

      document_generator = Docusign::GenerateSupplierAgreement.new(supplier: supplier).call

      expect(document_generator).to_not be_success
      expect(document_generator.errors).to include("Exception when calling EnvelopesApi->create_envelope for supplier with ID #{supplier.id}")
    end
  end

end
