require 'rails_helper'

RSpec.describe Utility::MatchArrays, type: :service, utility: true do

  let(:arr_1) { [1, 2, 3] }
  let(:arr_2) { [1, 2, 3] }
  let(:arr_3) { [1, 2, 3] }
  let(:arr_4) { [1, 2, 3] }
  let(:arr_5) { [1, 2, 3] }

  context 'matching arrays' do
    it 'returns true if 2 arrays have the same elements' do
      array_matcher = Utility::MatchArrays.new(arr_1, arr_2).call
      expect(array_matcher).to be_truthy
    end

    it 'returns true if multiple arrays have the same elements' do
      array_matcher = Utility::MatchArrays.new(arr_1, arr_2, arr_3, arr_4, arr_5).call
      expect(array_matcher).to be_truthy
    end

    it 'returns true if arrays have the same elements in any order' do
      arr_2 = [3, 1, 2]
      array_matcher = Utility::MatchArrays.new(arr_1, arr_2).call
      expect(array_matcher).to be_truthy
    end

    it 'returns true for empty arrays also' do
      arr_1 = []
      arr_2 = []
      array_matcher = Utility::MatchArrays.new(arr_1, arr_2).call
      expect(array_matcher).to be_truthy
    end

    it 'returns true when matching the same array' do
      array_matcher = Utility::MatchArrays.new(arr_1, arr_1).call
      expect(array_matcher).to be_truthy
    end

    it 'returns true when matching just 1 array' do
      array_matcher = Utility::MatchArrays.new(arr_1).call
      expect(array_matcher).to be_truthy
    end
  end

  context 'non-matching arrays' do
    it 'returns false even if one array has the wrong no. of elemnents' do
      arr_3 = [1, 2]
      array_matcher = Utility::MatchArrays.new(arr_1, arr_2, arr_3).call
      expect(array_matcher).to be_falsey
    end

    it 'returns false even if one array has the wrong elemnents' do
      arr_4 = [4, 3, 1]
      array_matcher = Utility::MatchArrays.new(arr_1, arr_2, arr_4).call
      expect(array_matcher).to be_falsey
    end

    it 'returns false when nothing is passed' do
      array_matcher = Utility::MatchArrays.new.call
      expect(array_matcher).to be_falsey
    end
  end

end
