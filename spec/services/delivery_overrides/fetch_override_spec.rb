require 'rails_helper'

RSpec.describe DeliveryOverrides::FetchOverride, type: :service do

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:customer2) { create(:customer_profile, :random) }

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags) }

  let!(:delivery_override1) { create(:delivery_override, :random, supplier_kind: 'all', customer_profile: customer1) }
  let!(:delivery_override2) { create(:delivery_override, :random, supplier_kind: 'all', customer_profile: customer2) }

  let!(:catering_category) { create(:category, :random, group: 'catering-services') }
  let!(:pantry_category) { create(:category, :random, group: 'kitchen-supplies') }

  let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1, categories: [catering_category]) }
  let!(:menu_section12) { create(:menu_section, :random, supplier_profile: supplier1, categories: [catering_category]) }

  let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2, categories: [catering_category]) }
  let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2, categories: [pantry_category]) }

  before do
    if [true, false].sample # explicitly set supplier category group flags
      [supplier1, supplier2].each do |supplier|
        supplier.supplier_flags.update_column(:has_catering_services, true)
      end
      [supplier2].each do |supplier|
        supplier.supplier_flags.update_column(:has_kitchen_supplies, true)
      end
    else # cache category groups
      Suppliers::Cache::CategoryGroups.new(suppliers: [supplier1, supplier2]).call
    end
  end

  it 'fetches the `all` delivery overide pertaining only to the customer' do
    fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: [supplier1, supplier2].sample).call

    expect(fetched_override).to eq(delivery_override2)
  end

  context 'with supplier specific override' do
    let!(:supplier_specific_delivery_override) { create(:delivery_override, :random, supplier_kind: 'specific', customer_profile: customer2, supplier_profile: supplier2) }

    it 'fetches the delivery_override with the specific supplier x customer combination' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier2).call

      expect(fetched_override).to eq(supplier_specific_delivery_override)
    end

    it 'fetches (reverts back to) `all` delivery override if customer does not have any specific supplier override for the passed in supplier' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier1).call

      expect(fetched_override).to eq(delivery_override2)
    end

    it 'fetches nothing if the customer does not have a reverting `all` overrride' do
      delivery_override2.destroy
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier1).call

      expect(fetched_override).to be_nil
    end

    it 'fetches `all` delivery override if customer does not have any specific supplier overrides' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer1, supplier: [supplier1, supplier2].sample).call

      expect(fetched_override).to eq(delivery_override1)
    end
  end

  context 'with catering supplier overrides' do
    let!(:catering_supplier_override) { create(:delivery_override, :random, supplier_kind: 'catering', customer_profile: customer2) }

    it 'fetches the catering override for a supplier with catering menu sections' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: [supplier1, supplier2].sample).call

      expect(fetched_override).to eq(catering_supplier_override)
    end

    it 'fetches a supplier specific override if present' do
      supplier_specific_delivery_override = create(:delivery_override, :random, supplier_kind: 'specific', customer_profile: customer2, supplier_profile: supplier2)

      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier2).call
      expect(fetched_override).to eq(supplier_specific_delivery_override)
    end

    it 'fetches (reverts back to) `all` delivery override if customer does not have any catering supplier override' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer1, supplier: [supplier1, supplier2].sample).call

      expect(fetched_override).to eq(delivery_override1)
    end

    it 'fetches (reverts back to) `all` delivery override if supplier does not have active catering menu sections', skip: 'Already tested in Suppliers::Cache::CategoryGroups' do
      is_archived = [true, false].sample
      is_hidden = [true, false].sample
      case
      when is_archived
        menu_section21.update_column(:archived_at, Time.zone.now)
      when is_hidden
        menu_section21.update_column(:is_hidden, true)
      else
        menu_section21.update(categories: [pantry_category]) # menu section is categoriesed as a pantry section
      end
      Suppliers::Cache::CategoryGroups.new(suppliers: [supplier1, supplier2]).call

      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier2).call

      expect(fetched_override).to eq(delivery_override2)
    end

    it 'fetches nothing if the customer does not have a (fall-back) `all` overrride' do
      delivery_override1.destroy
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer1, supplier: [supplier1, supplier2].sample).call

      expect(fetched_override).to be_nil
    end
  end

  context 'with pantry supplier overrides' do
    let!(:pantry_supplier_override) { create(:delivery_override, :random, supplier_kind: 'pantry', customer_profile: customer2) }

    it 'fetches the pantry override for a supplier with pantry menu sections' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier2).call

      expect(fetched_override).to eq(pantry_supplier_override)
    end

    it 'fetches a supplier specific override if present' do
      supplier_specific_delivery_override = create(:delivery_override, :random, supplier_kind: 'specific', customer_profile: customer2, supplier_profile: supplier2)

      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier2).call
      expect(fetched_override).to eq(supplier_specific_delivery_override)
    end

    it 'fetches (reverts back to) `all` delivery override if supplier is not a pantry supplier' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier1).call

      expect(fetched_override).to eq(delivery_override2)
    end

    it 'fetches (reverts back to) `all` delivery override if supplier does not have active pantry menu sections', skip: 'Already tested in Suppliers::Cache::CategoryGroups' do
      is_archived = [true, false].sample
      is_hidden = [true, false].sample
      case
      when is_archived
        menu_section22.update_column(:archived_at, Time.zone.now)
      when is_hidden
        menu_section22.update_column(:is_hidden, true)
      else
        menu_section22.update(categories: [catering_category]) # menu section is categoriesed as a catering section
      end
      Suppliers::Cache::CategoryGroups.new(suppliers: [supplier1, supplier2]).call
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer2, supplier: supplier2).call

      expect(fetched_override).to eq(delivery_override2)
    end

    it 'fetches (reverts back to) `all` delivery override if customer does not have any pantry supplier overrides' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer1, supplier: [supplier1, supplier2].sample).call

      expect(fetched_override).to eq(delivery_override1)
    end

    it 'fetches nothing if the customer does not have a (fall-back) `all` overrride' do
      delivery_override1.destroy
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer1, supplier: [supplier1, supplier2].sample).call

      expect(fetched_override).to be_nil
    end
  end

  context 'no overrides' do
    it 'returns nil for missing customer' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: nil, supplier: [supplier1, supplier2].sample).call

      expect(fetched_override).to be_nil
    end

    it 'returns nil for missing supplier' do
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: [customer1, customer2].sample, supplier: nil).call

      expect(fetched_override).to be_nil
    end

    it 'returns nil for customer with no overrides' do
      customer3 = create(:customer_profile, :random)
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: customer3, supplier: [supplier1, supplier2].sample).call

      expect(fetched_override).to be_nil
    end

    it 'returns nil if customer does not have any supplier specific overrides' do
      [delivery_override1, delivery_override2].each do |override|
        override.update_column(:supplier_kind, 'pantry')
      end
      fetched_override = DeliveryOverrides::FetchOverride.new(customer: [customer1, customer2].sample, supplier: supplier1).call # supplier 1 is not a pantry supplier

      expect(fetched_override).to be_nil
    end
  end

end

