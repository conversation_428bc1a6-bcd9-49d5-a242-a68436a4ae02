require 'rails_helper'

RSpec.describe TeamOrders::ListExtendableOrders, type: :service, team_orders: true do

  let!(:beginning_of_week) { Time.zone.now.beginning_of_week }
  let!(:package_id1) { SecureRandom.hex(7) }
  let!(:team_admin1) { create(:customer_profile, :random) }

  let!(:package_id2) { SecureRandom.hex(7) }
  let!(:team_admin2) { create(:customer_profile, :random) }

  # recurring team orders
  let!(:team_order11) { create(:order, :recurring_team_order, package_id: package_id1, delivery_at: beginning_of_week + 2.days) }
  let!(:team_order12) { create(:order, :recurring_team_order, package_id: package_id1, delivery_at: beginning_of_week + 3.days) }
  let!(:team_order13) { create(:order, :recurring_team_order, package_id: package_id1, delivery_at: beginning_of_week + 1.week + 2.days) }
  let!(:team_order14) { create(:order, :recurring_team_order, package_id: package_id1, delivery_at: beginning_of_week + 1.week + 3.days) }

  let!(:team_order21) { create(:order, :recurring_team_order, package_id: package_id2, delivery_at: beginning_of_week + 4.days) }
  let!(:team_order22) { create(:order, :recurring_team_order, package_id: package_id2, delivery_at: beginning_of_week + 1.week + 4.days) }

  let!(:time_in_future) { beginning_of_week + rand(1..4).days + rand(1..10).hours }

  it 'lists (last) orders within each package with no orders in the 2nd to next week' do
    order_lister = TeamOrders::ListExtendableOrders.new(time: time_in_future).call

    expect(order_lister.extendable_team_orders).to include(team_order14, team_order22)
    expect(order_lister.extendable_team_orders.map(&:package_id)).to include(package_id1, package_id2)
  end

  it 'doesn\'t list package orders with team orders in the 2nd to next week' do # REMINDER_THRESHOLD = 2.weeks
    team_order23 = create(:order, :recurring_team_order, package_id: package_id2, delivery_at: beginning_of_week + 2.weeks + 4.days)

    order_lister = TeamOrders::ListExtendableOrders.new(time: time_in_future).call
    expect(order_lister.extendable_team_orders.map(&:package_id)).to include(package_id1)

    expect(order_lister.extendable_team_orders).to_not include(team_order21, team_order22, team_order23)
    expect(order_lister.extendable_team_orders.map(&:package_id)).to_not include(package_id2)
  end

  # case of the recurring order being cancelled in the last week)
  it 'doesn\'t list package orders with no orders in the next week' do
    team_order22.destroy

    order_lister = TeamOrders::ListExtendableOrders.new(time: time_in_future).call
    expect(order_lister.extendable_team_orders).to include(team_order14) # still lists team order to extend
    expect(order_lister.extendable_team_orders).to_not include(team_order22) # o'course the order does not exist
    expect(order_lister.extendable_team_orders).to_not include(team_order21) # does not list current team order either

    expect(order_lister.extendable_team_orders.map(&:package_id)).to include(package_id1)
    expect(order_lister.extendable_team_orders.map(&:package_id)).to_not include(package_id2)
  end

  context 'with cancelled/voided orders' do
    let!(:order_status) { %w[voided cancelled].sample }

    it 'lists team orders within a package even if some orders in the last week are cancelled/voided' do
      team_order14.update_column(:status, order_status)
      order_lister = TeamOrders::ListExtendableOrders.new(time: time_in_future).call

      expect(order_lister.extendable_team_orders).to include(team_order14) # still extends package for the week
    end

    it 'lists team orders within a package even if all orders are skipped' do
      [team_order21, team_order22].each do |team_order|
        team_order.update_column(:status, 'skipped')
      end
      order_lister = TeamOrders::ListExtendableOrders.new(time: time_in_future).call

      expect(order_lister.extendable_team_orders).to include(team_order22) # still extends package for the week
      expect(order_lister.extendable_team_orders.map(&:package_id)).to include(package_id2)
    end

    it 'doesn\'t list team orders within a package in which all orders in the last week are cancelled/voided' do
      [team_order13, team_order14].each do |team_order|
        team_order.update_column(:status, order_status)
      end
      order_lister = TeamOrders::ListExtendableOrders.new(time: time_in_future).call

      expect(order_lister.extendable_team_orders).to_not include(team_order14) # does not extends package for the week
      expect(order_lister.extendable_team_orders.map(&:package_id)).to_not include(package_id1)

      expect(order_lister.extendable_team_orders).to include(team_order22) # still extends package for the week
      expect(order_lister.extendable_team_orders.map(&:package_id)).to include(package_id2)
    end
  end

  it 'also returns the beginning of the 2nd to next week as the extension week' do
    order_lister = TeamOrders::ListExtendableOrders.new(time: time_in_future).call

    expect(order_lister.extension_week).to eq((time_in_future + TeamOrder::REMINDER_THRESHOLD).beginning_of_week)
  end

end
