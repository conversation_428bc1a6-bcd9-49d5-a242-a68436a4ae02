require 'rails_helper'

RSpec.describe TeamOrders::AutoExtend, type: :service, team_orders: true do

  let!(:beginning_of_week) { Time.zone.now.beginning_of_week }
  let!(:package_id1) { SecureRandom.hex(7) }
  let!(:team_admin1) { create(:customer_profile, :random) }

  let!(:package_id2) { SecureRandom.hex(7) }
  let!(:team_admin2) { create(:customer_profile, :random) }

  # recurring team orders
  let!(:team_order11) { create(:order, :recurring_team_order, package_id: package_id1, delivery_at: beginning_of_week + 2.days) }
  let!(:team_order12) { create(:order, :recurring_team_order, package_id: package_id1, delivery_at: beginning_of_week + 3.days) }
  let!(:team_order13) { create(:order, :recurring_team_order, package_id: package_id1, delivery_at: beginning_of_week + 1.week + 2.days) }
  let!(:team_order14) { create(:order, :recurring_team_order, package_id: package_id1, delivery_at: beginning_of_week + 1.week + 3.days) }

  let!(:team_order21) { create(:order, :recurring_team_order, package_id: package_id2, delivery_at: beginning_of_week + 4.days) }
  let!(:team_order22) { create(:order, :recurring_team_order, package_id: package_id2, delivery_at: beginning_of_week + 1.week + 4.days) }

  let!(:time_in_future) { beginning_of_week + rand(1..4).days + rand(1..10).hours }

  before do
    order_extender = double(TeamOrders::ExtendOrder)
    allow(TeamOrders::ExtendOrder).to receive(:new).and_return(order_extender)
    extender_response = OpenStruct.new(success?: true, extended_orders: [true], errors: [])
    allow(order_extender).to receive(:call).and_return(extender_response)
  end

  it 'extends the last orders from the package with no orders in the extension week' do
    expect(TeamOrders::ExtendOrder).to receive(:new).with(team_order: team_order14, extension_week: anything)
    expect(TeamOrders::ExtendOrder).to receive(:new).with(team_order: team_order22, extension_week: anything)

    orders_extender = TeamOrders::AutoExtend.new(time: time_in_future).call
    expect(orders_extender).to be_success
    expect(orders_extender.extended_orders.size).to eq(2) # mock only sends back 1 extended order per package
  end

  it 'doesn\'t extend package with all cancelled orders in the last week' do
    [team_order13, team_order14].each do |team_order|
      team_order.update_column(:status, 'cancelled')
    end

    expect(TeamOrders::ExtendOrder).to_not receive(:new).with(team_order: team_order14, extension_week: anything)
    expect(TeamOrders::ExtendOrder).to receive(:new).with(team_order: team_order22, extension_week: anything)

    orders_extender = TeamOrders::AutoExtend.new(time: time_in_future).call
    expect(orders_extender).to be_success
    expect(orders_extender.extended_orders.size).to eq(1) # mock only sends back 1 extended order per package
  end

  it 'doesn\'t list package orders with team orders in the 2nd to next week' do # REMINDER_THRESHOLD = 2.weeks
    create(:order, :recurring_team_order, package_id: package_id2, delivery_at: beginning_of_week + 2.weeks + 4.days)

    expect(TeamOrders::ExtendOrder).to receive(:new).with(team_order: team_order14, extension_week: anything)
    expect(TeamOrders::ExtendOrder).to_not receive(:new).with(team_order: team_order22, extension_week: anything)

    orders_extender = TeamOrders::AutoExtend.new(time: time_in_future).call
    expect(orders_extender).to be_success
    expect(orders_extender.extended_orders.size).to eq(1)
  end

end
