require 'rails_helper'

RSpec.describe TeamOrders::Clone, type: :service, team_orders: true do

  let!(:team_admin) { create(:customer_profile, :random, :with_user) }
  let!(:team_order) { create(:order, :team_order, unique_event_id: SecureRandom.hex(7)) }

  it 'clones the team order into a draft order' do
    team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
    expect(team_order_cloner).to be_success

    cloned_team_order = team_order_cloner.cloned_team_order
    expect(cloned_team_order).to_not be_persisted # does not actually create the team order
    expect(cloned_team_order.name).to eq(team_order.name)
    expect(cloned_team_order.customer_profile).to eq(team_order.customer_profile)
    expect(cloned_team_order.status).to eq('draft')
    expect(cloned_team_order.delivery_at).to be_blank
    expect(cloned_team_order.unique_event_id).to be_blank
    expect(cloned_team_order.invoice_id).to be_blank
  end

  it 'adds a default team order detail if missing in original team order' do
    team_order.team_order_detail.destroy
    team_order_cloner = TeamOrders::Clone.new(team_order: team_order.reload).call
    expect(team_order_cloner).to be_success

    cloned_team_order_detail = team_order_cloner.cloned_team_order.team_order_detail
    expect(cloned_team_order_detail).to_not be_persisted
    expect(cloned_team_order_detail.cutoff_option).to eq('charge_to_minimum')
    expect(cloned_team_order_detail.attendee_pays).to eq(false)
  end

  context 'with team order detail' do
    let!(:team_order_detail) { team_order.reload.team_order_detail }

    it 'clones the team order detail' do
      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_detail = team_order_cloner.cloned_team_order.team_order_detail
      expect(cloned_team_order_detail).to_not be_persisted
      expect(cloned_team_order_detail.budget).to eq(team_order_detail.budget)
      expect(cloned_team_order_detail.hide_budget).to eq(team_order_detail.hide_budget)
      expect(cloned_team_order_detail.cutoff_option).to eq(team_order_detail.cutoff_option)
      expect(cloned_team_order_detail.attendee_pays).to eq(team_order_detail.attendee_pays)
    end

    it 'does not clone any reminder times within the team order detail' do
      now = Time.zone.now
      team_order_detail.update_columns(cutoff_2hr_reminder: now, cutoff_30m_reminder: now, anonymous_attendees_reminder: now)

      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_detail = team_order_cloner.cloned_team_order.team_order_detail
      expect(cloned_team_order_detail).to_not be_persisted
      expect(cloned_team_order_detail.cutoff_2hr_reminder).to be_blank
      expect(cloned_team_order_detail.cutoff_30m_reminder).to be_blank
      expect(cloned_team_order_detail.anonymous_attendees_reminder).to be_blank
    end

    context 'with team order detail levels', team_order_levels: true do
      let!(:team_order_level1) { create(:team_order_level, :random, team_order_detail: team_order.team_order_detail) }
      let!(:team_order_level2) { create(:team_order_level, :random, team_order_detail: team_order.team_order_detail) }

      it 'clones the team order levels against the cloned team order detail' do
        team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
        expect(team_order_cloner).to be_success

        cloned_team_order_detail = team_order_cloner.cloned_team_order.team_order_detail
        cloned_levels = cloned_team_order_detail.levels
        expect(cloned_levels).to be_present
        expect(cloned_levels.size).to eq(team_order.team_order_levels.size)
        expect(cloned_levels.map(&:persisted?).uniq).to eq([false])
        expect(cloned_levels.map(&:name)).to include(team_order_level1.name, team_order_level2.name)
      end
    end
  end

  context 'with team order attendees' do
    let!(:team_order_attendee1) { create(:team_order_attendee, :random, order: team_order, status: %w[invited pending ordered].sample) }
    let!(:team_order_attendee2) { create(:team_order_attendee, :random, order: team_order, status: %w[invited pending ordered].sample) }
    let!(:team_order_attendee3) { create(:team_order_attendee, :random, order: team_order, status: %w[invited pending ordered].sample) }

    it 'clones all active attendees' do
      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_attendees = team_order_cloner.cloned_team_order.team_order_attendees
      expect(cloned_team_order_attendees).to be_present
      expect(cloned_team_order_attendees.map(&:persisted?).uniq).to eq([false]) # does not create records
      expect(cloned_team_order_attendees.map(&:id)).to_not include(team_order_attendee1.id, team_order_attendee2.id, team_order_attendee3.id)
      expect(cloned_team_order_attendees.map(&:event_attendee)).to include(team_order_attendee1.event_attendee, team_order_attendee2.event_attendee, team_order_attendee3.event_attendee)
    end

    it 'resets all the data for the attendees' do
      now = Time.zone.now
      [team_order_attendee1, team_order_attendee2, team_order_attendee3].each do |attendee|
        attendee.update_columns(
          cutoff_4hr_reminder: now,
          cutoff_2hr_reminder: now,
          cutoff_30m_reminder: now,
          delivery_30m_reminder: now
        )
      end
      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_attendees = team_order_cloner.cloned_team_order.team_order_attendees
      expect(cloned_team_order_attendees.map(&:status).uniq).to eq(['invited'])
      expect(cloned_team_order_attendees.map(&:anonymous).uniq).to eq([false])
      expect(cloned_team_order_attendees.map(&:uniq_code).compact).to be_blank
      expect(cloned_team_order_attendees.map(&:cutoff_4hr_reminder).compact).to be_blank
      expect(cloned_team_order_attendees.map(&:cutoff_2hr_reminder).compact).to be_blank
      expect(cloned_team_order_attendees.map(&:cutoff_30m_reminder).compact).to be_blank
      expect(cloned_team_order_attendees.map(&:delivery_30m_reminder).compact).to be_blank
    end

    it 'does not clone cancelled or declined attendees' do
      [team_order_attendee1, team_order_attendee2].each do |attendee|
        attendee.update_column(:status, %w[declined cancelled].sample)
      end

      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_attendees = team_order_cloner.cloned_team_order.team_order_attendees
      expect(cloned_team_order_attendees.map(&:id)).to_not include(team_order_attendee1.id, team_order_attendee2.id, team_order_attendee3.id)
      expect(cloned_team_order_attendees.map(&:event_attendee)).to_not include(team_order_attendee1.event_attendee, team_order_attendee2.event_attendee)
      expect(cloned_team_order_attendees.map(&:event_attendee)).to include(team_order_attendee3.event_attendee)
    end

    it 'does not clone anonymous attendees' do
      [team_order_attendee1, team_order_attendee3].each do |attendee|
        attendee.update_column(:anonymous, true)
      end

      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_attendees = team_order_cloner.cloned_team_order.team_order_attendees
      expect(cloned_team_order_attendees.map(&:id)).to_not include(team_order_attendee1.id, team_order_attendee2.id, team_order_attendee3.id)
      expect(cloned_team_order_attendees.map(&:event_attendee)).to_not include(team_order_attendee1.event_attendee, team_order_attendee3.event_attendee)
      expect(cloned_team_order_attendees.map(&:event_attendee)).to include(team_order_attendee2.event_attendee)
    end
  end

  context 'with team order supplier' do
    let!(:order_supplier1) { create(:order_supplier, :random, order: team_order) }
    let!(:order_supplier2) { create(:order_supplier, :random, order: team_order) }

    it 'clones the order suppliers' do
      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_suppliers = team_order_cloner.cloned_team_order.order_suppliers
      expect(cloned_team_order_suppliers).to be_present
      expect(cloned_team_order_suppliers.map(&:persisted?).uniq).to eq([false])
      expect(cloned_team_order_suppliers.map(&:id)).to_not include(order_supplier1.id, order_supplier2.id)
      expect(cloned_team_order_suppliers.map(&:supplier_profile)).to include(order_supplier1.supplier_profile, order_supplier2.supplier_profile)
    end

    it 'resets all the data for the order suppliers' do
      [order_supplier1, order_supplier2].each do |order_supplier|
        order_supplier.update_columns(
          surcharge: rand(20),
          cutoff_4hr_reminder: Time.zone.now,
          cutoff_day_reminder: Time.zone.now
        )
      end
      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_suppliers = team_order_cloner.cloned_team_order.order_suppliers
      expect(cloned_team_order_suppliers.map(&:surcharge).compact).to be_blank
      expect(cloned_team_order_suppliers.map(&:cutoff_4hr_reminder).compact).to be_blank
      expect(cloned_team_order_suppliers.map(&:cutoff_day_reminder).compact).to be_blank
    end

    it 'clones over any menu selected order suppliers' do
      selected_menu_sections = [rand(10), rand(20), rand(30)]
      order_supplier2.update_column(:selected_menu_sections, selected_menu_sections)
      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_suppliers = team_order_cloner.cloned_team_order.order_suppliers
      cloned_supplier2_order_supplier = cloned_team_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == order_supplier2.supplier_profile }
      expect(cloned_supplier2_order_supplier.selected_menu_sections).to eq(selected_menu_sections)
    end
  end

  context 'as a package order' do
    let!(:package_id) { SecureRandom.uuid }

    before do
      team_order.team_order_detail.update_column(:package_id, package_id)
    end

    it 'clones the order_variant and package id' do
      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order = team_order_cloner.cloned_team_order
      cloned_team_order_detail = cloned_team_order.team_order_detail
      expect(cloned_team_order_detail.package_id).to eq(team_order.package_id)

      expect(cloned_team_order.package_id).to eq(team_order.package_id)
      expect(cloned_team_order).to be_is_package_order
      expect(cloned_team_order.order_variant).to eq('team_order') # non-recurring package order
    end

    it 'sets up the next delivery dates' do
      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order = team_order_cloner.cloned_team_order
      expect(cloned_team_order.delivery_dates.map(&:to_s)).to match_array([team_order.delivery_at + 1.week].map(&:to_s))
    end

    it 'does not clone team order attendees' do
      create(:team_order_attendee, :random, order: team_order, status: %w[invited pending ordered].sample)
      create(:team_order_attendee, :random, order: team_order, status: %w[invited pending ordered].sample)

      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_attendees = team_order_cloner.cloned_team_order.team_order_attendees
      expect(cloned_team_order_attendees).to be_blank
    end

    it 'does not clone order suppliers' do
      create(:order_supplier, :random, order: team_order)
      create(:order_supplier, :random, order: team_order)

      team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
      expect(team_order_cloner).to be_success

      cloned_team_order_suppliers = team_order_cloner.cloned_team_order.order_suppliers
      expect(cloned_team_order_suppliers).to be_blank
    end

    context 'as a recurring team order' do
      before do
        team_order.update_column(:order_variant, 'recurring_team_order')
      end

      it 'clones the order_variant and package id when cloning a recurring team order' do
        team_order_cloner = TeamOrders::Clone.new(team_order: team_order).call
        expect(team_order_cloner).to be_success

        cloned_team_order = team_order_cloner.cloned_team_order
        cloned_team_order_detail = cloned_team_order.team_order_detail
        expect(cloned_team_order_detail.package_id).to eq(team_order.package_id)

        expect(cloned_team_order.package_id).to eq(team_order.package_id)
        expect(cloned_team_order).to be_is_package_order
        expect(cloned_team_order).to be_is_recurring_team_order # order_variant = 'recurring_team_order'
      end
    end # recurring team order
  end # package_order order

end
