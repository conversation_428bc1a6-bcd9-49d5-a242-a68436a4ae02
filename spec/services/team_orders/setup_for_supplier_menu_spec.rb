require 'rails_helper'

RSpec.describe TeamOrders::SetupForSupplierMenu, type: :service, orders: true, team_orders: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:suburb) { create(:suburb, :random) }

  let!(:now) { Time.zone.now }

  let!(:team_admin) { create(:customer_profile, :random, :with_user) }
  let!(:event_attendee) { create(:event_attendee, :random) }
  let!(:team_order) { create(:order, :team_order, customer_profile: team_admin, delivery_suburb: suburb) }
  let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order, event_attendee: event_attendee, status: 'invited') }
  let!(:team_order_supplier) { create(:order_supplier, order: team_order, supplier_profile: supplier) }

  let!(:team_order_params) { { code: team_order_attendee.uniq_code } }
  let!(:team_order_admin_params) { { code: team_order.unique_event_id } }

  context 'ordering as a team order attendee' do
    it 'sets up the team order for an attendee from the passed in attendee code' do
      order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

      expect(order_setup).to be_success
      expect(order_setup.order).to eq(team_order)
      expect(order_setup.team_order_attendee).to eq(team_order_attendee)
    end

    it 'returns the supplier for the team order if found' do
      order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

      expect(order_setup).to be_success
      expect(order_setup.supplier).to eq(supplier)
    end
  end

  context 'ordering as a team admin' do
    it 'sets up the team order for the team admin as an attendee (attendee_code = order\'s unique id)' do
      order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_admin_params, profile: team_admin).call

      expect(order_setup).to be_success
      expect(order_setup.order).to eq(team_order)
      admin_attendee = order_setup.team_order_attendee
      expect(admin_attendee).to be_present
      expect(admin_attendee.uniq_code).to eq(team_order.unique_event_id)
      expect(admin_attendee).to be_is_team_admin
    end
  end

  context 'errors / warnings' do
    it 'return with `missing_order_attendee` if team order attendee could not be found' do
      team_order_params = { code: 'random attendee code' }
      order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

      expect(order_setup).to_not be_success
      expect(order_setup.error).to eq(:missing_order_attendee)
      expect(order_setup.warnings).to be_blank
      expect(order_setup.order).to be_blank
      expect(order_setup.supplier).to be_blank
    end

    context 'when ordering as a team order attendee' do
      it 'returns with an error if team order attendee does not have an active event attendee' do
        event_attendee.update(active: false)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:missing_order_attendee)
        expect(order_setup.team_order_attendee).to be_blank
        expect(order_setup.order).to eq(team_order)
      end

      it 'returns with an error if order is cancelled' do
        team_order.update_column(:status, %w[paused cancelled].sample)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

        expect(order_setup.error).to eq(:cancelled_event)
        expect(order_setup.team_order_attendee).to eq(team_order_attendee)
        expect(order_setup.order).to eq(team_order)
      end

      it 'returns with an error if order no longer pending' do
        team_order.update_column(:status, %w[new amended confirmed delivered].sample)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:order_closed)
        expect(order_setup.team_order_attendee).to eq(team_order_attendee)
        expect(order_setup.order).to eq(team_order)
      end

      it 'returns successfully but with a warning if order is 30mins before its cut off time (attendee soft-cutoff)' do
        team_order.update_column(:delivery_at, now + 20.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

        expect(order_setup).to be_success
        expect(order_setup.warnings).to include(:cutoff_almost_exceeded)
        expect(order_setup.error).to be_blank
        expect(order_setup.order).to eq(team_order)
        expect(order_setup.team_order_attendee).to eq(team_order_attendee)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'returns successfully but with a warning if the team order attendee order is pending' do
        team_order_attendee.update_column(:status, 'pending')
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

        expect(order_setup).to be_success
        expect(order_setup.warnings).to include(:pending_order_confirmation)
        expect(order_setup.error).to be_blank
        expect(order_setup.order).to eq(team_order)
        expect(order_setup.team_order_attendee).to eq(team_order_attendee)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'does not return with an error/warning if order is 30mins before its cut off time (attendee soft-cutoff) and if team admin is ordering on behalf of a team order attendee' do
        team_order.update_column(:delivery_at, now + 20.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params, profile: team_admin).call

        expect(order_setup).to be_success
        expect(order_setup.warnings).to be_blank
        expect(order_setup.error).to be_blank

        expect(order_setup.order).to eq(team_order)
        expect(order_setup.team_order_attendee).to eq(team_order_attendee)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'returns with an error if order is past its cut off time but within grace period' do
        team_order.update_column(:delivery_at, now - 30.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:cutoff_exceeded)
        expect(order_setup.warnings).to be_blank
        expect(order_setup.order).to eq(team_order)
        expect(order_setup.team_order_attendee).to eq(team_order_attendee)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'returns with an error if order is past its cut off time and grace period' do
        team_order.update_column(:delivery_at, now - 61.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:order_closed)
        expect(order_setup.warnings).to be_blank
        expect(order_setup.order).to eq(team_order)
        expect(order_setup.team_order_attendee).to eq(team_order_attendee)
        expect(order_setup.supplier).to eq(supplier)
      end
    end

    context 'when ordering as a team order admin' do
      it 'returns with an error if order is cancelled' do
        team_order.update_column(:status, %w[paused cancelled].sample)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_admin_params, profile: team_admin).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:cancelled_event)
        expect(order_setup.warnings).to be_blank
      end

      it 'returns with an error if order no longer pending' do
        team_order.update_column(:status, %w[new amended confirmed delivered].sample)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_admin_params, profile: team_admin).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:order_closed)
        expect(order_setup.warnings).to be_blank
      end

      it 'does not returns with error/warning if order is 30mins before its cut off time' do
        team_order.update_column(:delivery_at, now + 20.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_admin_params, profile: team_admin).call

        expect(order_setup).to be_success
        expect(order_setup.error).to be_blank
        expect(order_setup.warnings).to be_blank
        expect(order_setup.order).to eq(team_order)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'returns with an error if order is past its cut off time but within grace period' do
        team_order.update_column(:delivery_at, now - 30.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_admin_params, profile: team_admin).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:cutoff_exceeded)
        expect(order_setup.warnings).to be_blank
      end

      it 'returns with an error if order is past its cut off time and grace period' do
        team_order.update_column(:delivery_at, now - 61.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_admin_params, profile: team_admin).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:order_closed)
        expect(order_setup.warnings).to be_blank
      end
    end

    context 'when ordering as a Yordar admin' do
      it 'returns with an error if order is cancelled' do
        team_order.update_column(:status, %w[paused cancelled].sample)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params, is_admin: true).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:cancelled_event)
        expect(order_setup.warnings).to be_blank
      end

      it 'returns with an error if order already delivered' do
        team_order.update_column(:status, 'delivered')
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params, is_admin: true).call

        expect(order_setup).to_not be_success
        expect(order_setup.error).to eq(:order_closed)
        expect(order_setup.warnings).to be_blank
      end

      it 'does not returns with an error if order id either submitted, amended or confirmed' do
        team_order.update_column(:status, %w[new amended confirmed].sample)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params, is_admin: true).call

        expect(order_setup).to be_success
        expect(order_setup.error).to be_blank
        expect(order_setup.warnings).to be_blank

        expect(order_setup.order).to eq(team_order)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'does not returns with any error/warning if order is 30mins before its cut off time' do
        team_order.update_column(:delivery_at, now + 20.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params, is_admin: true).call

        expect(order_setup).to be_success
        expect(order_setup.warnings).to be_blank
        expect(order_setup.error).to be_blank
        expect(order_setup.order).to eq(team_order)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'does not return with an error/warning if order is past its cut off time but within grace period' do
        team_order.update_column(:delivery_at, now - 30.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params, is_admin: true).call

        expect(order_setup).to be_success
        expect(order_setup.error).to be_blank
        expect(order_setup.warnings).to be_blank
        expect(order_setup.order).to eq(team_order)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'does not return with an error/warning even if order is past its cut off time and grace period' do
        team_order.update_column(:delivery_at, now - 61.minutes)
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_params, is_admin: true).call

        expect(order_setup).to be_success
        expect(order_setup.error).to be_blank
        expect(order_setup.warnings).to be_blank
        expect(order_setup.order).to eq(team_order)
        expect(order_setup.supplier).to eq(supplier)
      end

      it 'returns with error if the team order does not belong to the passed in profile' do
        with_a_different_admin = [true, false].sample
        with_no_admin = [true, false].sample
        case
        when with_a_different_admin
          admin = create(:customer_profile, :random)
          team_order2 = team_order
        when with_no_admin
          admin = nil
          team_order2 = team_order
        else
          admin = team_admin
          team_order2 = create(:order, :team_order)
        end
        team_order_admin_params = { code: team_order2.unique_event_id }
        order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: team_order_admin_params, profile: admin).call

        expect(order_setup).to_not be_success
        expect(order_setup.order).to be_blank
        expect(order_setup.error).to eq(:missing_order_attendee)
        expect(order_setup.warnings).to be_blank
        expect(order_setup.supplier).to be_blank # supplier controller renders missing order page
      end
    end # ordering as Yordar admin
  end # errors / warnings

  context 'ordering as a package team order attendee' do
    let!(:package_id) { SecureRandom.hex(7) }
    let!(:team_order2) { create(:order, :team_order, customer_profile: team_admin, delivery_suburb: suburb) }

    before do
      # convert order to a package order
      [team_order, team_order2].each do |order|
        order.team_order_detail.update_column(:package_id, package_id)
        order.update_column(:order_variant, %w[team_order recurring_team_order].sample)
        order.reload
      end
    end

    let!(:recurring_order_params) { { code: team_order_attendee.uniq_code, event_id: team_order2.unique_event_id, recurring_team_order: [true, false].sample } }

    it 'sets up the team order attendee of the new team order and returns true' do
      order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: recurring_order_params, profile: team_admin).call

      expect(order_setup.order).to eq(team_order2)

      # also tested in TeamOrders::FetchPackageOrderAttendee
      new_team_order_attendee = order_setup.team_order_attendee
      expect(new_team_order_attendee).to be_present
      expect(new_team_order_attendee.event_attendee).to eq(team_order_attendee.event_attendee)
      expect(new_team_order_attendee).to_not be_is_team_admin
    end

    it 'returns  with an error if team order attendee could not be found (missing package team order)' do
      [team_order, team_order2].sample.team_order_detail.update_column(:package_id, nil) # convert to non-package order
      order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: recurring_order_params).call

      expect(order_setup).to_not be_success
      expect(order_setup.order).to be_blank
      expect(order_setup.error).to eq(:missing_order_attendee)
      expect(order_setup.warnings).to be_blank
      expect(order_setup.supplier).to be_blank
    end

    it 'returns with an error if team order attendee could not be found (mismatch package_id)' do
      [team_order, team_order2].sample.team_order_detail.update_column(:package_id, SecureRandom.hex(7))
      order_setup = TeamOrders::SetupForSupplierMenu.new(team_order_params: recurring_order_params).call

      expect(order_setup).to_not be_success
      expect(order_setup.order).to be_blank
      expect(order_setup.error).to eq(:missing_order_attendee)
      expect(order_setup.warnings).to be_blank
      expect(order_setup.supplier).to be_blank
    end
  end

end
