require 'rails_helper'

RSpec.describe TeamOrders::FetchCutoffDayHour, type: :service, team_orders: true do

  let!(:now) { Time.zone.now}
  let!(:team_order) { create(:order, :team_order, delivery_at: now + 3.days) }

  before do
    # mock lead time fetcher
    lead_time_fetcher = double(Orders::FetchLeadTime)
    allow(Orders::FetchLeadTime).to receive(:new).and_return(lead_time_fetcher)
    allow(lead_time_fetcher).to receive(:call).and_return(lead_time_response)
  end

  context 'cutoff time x days in future' do
    let!(:lead_time_response) { OpenStruct.new(lead_time: now + 1.day + 10.hours) }

    it 'returns the remaing days and hours' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher.days_left).to eq(1)
      expect(cutoff_fetcher.hours_left).to eq(10)
    end

    it 'returns that the cutoff has NOT expired' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher).to_not be_has_expired
    end

    it 'returns that the cutoff is NOT today' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher).to_not be_is_today
    end
  end

  context 'cutoff time is 0 days in future' do
    let!(:lead_time_response) { OpenStruct.new(lead_time: now + 4.hours) }

    it 'returns the remaing days and hours' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher.days_left).to eq(0)
      expect(cutoff_fetcher.hours_left).to eq(4)
    end

    it 'returns that the cutoff has NOT expired' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher).to_not be_has_expired
    end

    it 'returns that the cutoff is today' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher).to be_is_today
    end
  end

  context 'cutoff time is before now' do
    let!(:lead_time_response) { OpenStruct.new(lead_time: now - 4.hours) }

    it 'returns the remaing days and hours' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher.days_left).to eq(0)
      expect(cutoff_fetcher.hours_left).to eq(0)
    end

    it 'returns that the cutoff has expired' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher).to be_has_expired
    end

    it 'returns that the cutoff is NOT today' do
      cutoff_fetcher = TeamOrders::FetchCutoffDayHour.new(team_order: team_order, compared_to: now).call

      expect(cutoff_fetcher).to_not be_is_today
    end
  end
end
