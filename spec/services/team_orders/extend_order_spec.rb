require 'rails_helper'

RSpec.describe TeamOrders::ExtendOrder, type: :service, team_orders: true do

  let!(:beginning_of_week) { Time.zone.now.beginning_of_week }
  let!(:package_id) { SecureRandom.hex(7) }

  let!(:team_order1) { create(:order, :recurring_team_order, package_id: package_id, delivery_at: beginning_of_week + 2.days + 3.hours) }
  let!(:order_supplier1) { create(:order_supplier, :random, order: team_order1) }

  let!(:supplier2) { create(:supplier_profile, :random) }
  let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2) }
  let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2) }
  let!(:menu_section23) { create(:menu_section, :random, supplier_profile: supplier2) }

  let!(:team_order2) { create(:order, :recurring_team_order, package_id: package_id, delivery_at: beginning_of_week + 3.days + 1.hours) }
  let!(:order_supplier2) { create(:order_supplier, :random, supplier_profile: supplier2, order: team_order2, selected_menu_sections: [menu_section21, menu_section22, menu_section23].map(&:id)) }

  let!(:team_order3) { create(:order, :recurring_team_order, package_id: package_id, delivery_at: beginning_of_week + 4.days + 2.hours) }
  let!(:order_supplier3) { create(:order_supplier, :random, order: team_order3) }

  let!(:team_order_package) { [team_order1, team_order2, team_order3] }

  let!(:next_week) { 1.week }
  let!(:extension_week) { beginning_of_week + next_week }

  before do
    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  context 'as a mock call' do
    before do
      order_creator = double(TeamOrders::Create)
      allow(TeamOrders::Create).to receive(:new).and_return(order_creator)
      creator_response = OpenStruct.new(success?: true, team_order: team_order_package.sample, errors: [])
      allow(order_creator).to receive(:call).and_return(creator_response)
    end

    it 'makes a call to generate package team orders by passing in the team order admin and package_id' do
      team_order_package.each do |team_order|
        expect(TeamOrders::Create).to receive(:new).with(team_order_params: anything, team_admin: team_order.customer_profile, package_id: team_order.package_id)
      end

      order_extender = TeamOrders::ExtendOrder.new(team_order: team_order1, extension_week: extension_week).call
      expect(order_extender).to be_success
    end

    it 'makes a call to generate package team orders by passing in the team order params' do
      team_order_package.sort_by(&:delivery_at).each do |team_order|
        delivery_date = team_order.delivery_at + next_week

        expected_team_order_params = team_order.attributes.symbolize_keys.slice(*TeamOrders::ExtendOrder::TEAM_ORDER_FIELDS)
        expected_team_order_params[:delivery_at] = delivery_date

        expected_team_order_params[:team_order_detail_attributes] = team_order.team_order_detail.attributes.symbolize_keys.slice(*TeamOrders::ExtendOrder::TEAM_ORDER_DETAIL_FIELDS)
        order_supplier = team_order.order_suppliers.first
        delivery_suppliers = {}
        delivery_suppliers[delivery_date.strftime('%Y-%m-%d')] = {
          order_supplier.supplier_profile_id.to_s => order_supplier.selected_menu_sections
        }
        expected_team_order_params[:delivery_suppliers] = delivery_suppliers

        expect(TeamOrders::Create).to receive(:new).with(team_order_params: expected_team_order_params, team_admin: anything, package_id: anything)
      end

      order_extender = TeamOrders::ExtendOrder.new(team_order: team_order1, extension_week: extension_week).call
      expect(order_extender).to be_success
    end
  end

  it 'creates team orders in the extended week' do
    order_extender = TeamOrders::ExtendOrder.new(team_order: team_order1, extension_week: extension_week).call

    expect(order_extender).to be_success
    extended_orders = order_extender.extended_orders

    expect(extended_orders.size).to eq(3)

    expected_delivery_dates = team_order_package.map{|order| order.delivery_at + next_week }
    expect(extended_orders.map(&:delivery_at).map(&:to_s)).to match_array(expected_delivery_dates.map(&:to_s))
  end

  it 'creates team orders in the extended week with the same details as the original team order' do
    order_extender = TeamOrders::ExtendOrder.new(team_order: team_order1, extension_week: extension_week).call

    expect(order_extender).to be_success
    extended_orders = order_extender.extended_orders

    extended_orders.each do |extended_order|
      team_order = team_order_package.detect{|order| order.delivery_at.strftime('%a') == extended_order.delivery_at.strftime('%a') }

      (TeamOrders::ExtendOrder::TEAM_ORDER_FIELDS - [:delivery_at]).each do |order_field|
        expect(extended_order.send(order_field)).to eq(team_order.send(order_field))
      end
    end
  end

  it 'creates team orders in the extended week with the same team order details as the original team order' do
    order_extender = TeamOrders::ExtendOrder.new(team_order: team_order1, extension_week: extension_week).call

    expect(order_extender).to be_success
    extended_orders = order_extender.extended_orders

    extended_orders.each do |extended_order|
      team_order = team_order_package.detect{|order| order.delivery_at.strftime('%a') == extended_order.delivery_at.strftime('%a') }

      TeamOrders::ExtendOrder::TEAM_ORDER_DETAIL_FIELDS.each do |detail_field|
        expect(extended_order.team_order_detail.send(detail_field)).to eq(team_order.team_order_detail.send(detail_field))
      end
    end
  end

  it 'creates team orders in the extended week with the same suppliers config as orders in the previous (5-1) week' do
    order_extender = TeamOrders::ExtendOrder.new(team_order: team_order1, extension_week: extension_week).call

    expect(order_extender).to be_success
    extended_orders = order_extender.extended_orders

    extended_orders.each do |extended_order|
      team_order = team_order_package.detect{|order| order.delivery_at.strftime('%a') == extended_order.delivery_at.strftime('%a') }
      team_order_supplier = team_order.order_suppliers.first
      extended_oprder_supplier = extended_order.order_suppliers.first

      expect(extended_oprder_supplier.supplier_profile).to eq(team_order_supplier.supplier_profile)
      expect(extended_oprder_supplier.selected_menu_sections).to eq(team_order_supplier.selected_menu_sections)
    end
  end

end
