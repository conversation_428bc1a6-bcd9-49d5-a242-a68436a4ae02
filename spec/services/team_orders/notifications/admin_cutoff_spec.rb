require 'rails_helper'

RSpec.describe TeamOrders::Notifications::<PERSON><PERSON><PERSON><PERSON><PERSON>, type: :service, team_orders: true do

  subject { TeamOrders::Notifications::AdminCutoff.new(cutoff_time: cutoff_time, time: notification_time).call }

  # No Suppliers / Minimums means the cutoff is same as delivery time
  let!(:delivery_start) { Time.zone.now.beginning_of_day }

  let!(:team_order1) { create(:order, :team_order, name: 'team_order1', delivery_at: delivery_start + 10.hours) }
  let!(:team_order2) { create(:order, :team_order, name: 'team_order2', delivery_at: delivery_start + 10.hours) }
  let!(:team_order3) { create(:order, :team_order, name: 'team_order3', delivery_at: delivery_start + 10.hours) }

  let!(:supplier_spends_fetcher) { double(Orders::GetSupplierSpends) }

  before do
    # mock email sender
    email_sender = double(TeamOrders::Emails::SendAdminCutOffEmail)
    allow(TeamOrders::Emails::SendAdminCutOffEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call).and_return(true)

    # mock supplier spends
    allow(Orders::GetSupplierSpends).to receive(:new).and_return(supplier_spends_fetcher)
    valid_supplier_spend_response = OpenStruct.new(is_under?: false)
    allow(supplier_spends_fetcher).to receive(:call).and_return(valid_supplier_spend_response)

    # mock event log creator
    event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:call).and_return(true)
  end

  let!(:cutoff_time) { %w[2hr 30m].sample }
  let!(:notification_time) do
    number_of_hours = {
      '2hr' => 8,
      '30m' => 9.5,
    }[cutoff_time]
    delivery_start + number_of_hours.hours
  end

  before do
    puts "Notifying for #{cutoff_time} @ #{notification_time}" if ENV['VERBOSE']
  end

  it 'send (2hr/30m) cutoff emails to team admins of a team order within cutoff' do
    expect(TeamOrders::Emails::SendAdminCutOffEmail).to receive(:new).with(team_order: team_order1, cutoff_time: cutoff_time)
    expect(TeamOrders::Emails::SendAdminCutOffEmail).to receive(:new).with(team_order: team_order2, cutoff_time: cutoff_time)
    expect(TeamOrders::Emails::SendAdminCutOffEmail).to receive(:new).with(team_order: team_order3, cutoff_time: cutoff_time)

    subject
  end

  it 'does not send cutoff emails to team admins of non-pending team orders' do
    team_order3.update_column(:status, %w[new confirmed amended delivered].sample)

    expect(TeamOrders::Emails::SendAdminCutOffEmail).to_not receive(:new).with(team_order: team_order3, cutoff_time: cutoff_time)
    subject
  end

  it 'does not send cutoff emails to team admins of a team order delivered before notification time' do
    delivery_time = [(delivery_start - 1.day), (delivery_start + 5.hours)].sample
    team_order1.update_column(:delivery_at, delivery_time)

    expect(TeamOrders::Emails::SendAdminCutOffEmail).to_not receive(:new).with(team_order: team_order1, cutoff_time: cutoff_time)
    subject
  end

  it 'does not send cutoff emails to team admins of a team order not within cutoff timeframe' do
    delivery_time = [(delivery_start + 1.day), (delivery_start + 12.hours), (team_order2.delivery_at + 1.hour)].sample
    team_order2.update_column(:delivery_at, delivery_time)

    expect(TeamOrders::Emails::SendAdminCutOffEmail).to_not receive(:new).with(team_order: team_order2, cutoff_time: cutoff_time)
    subject
  end

  it 'does not send cutoff emails to already notified team admins of a team order' do
    notification_attribute = "cutoff_#{cutoff_time}_reminder"
    team_order3.team_order_detail.update_column(notification_attribute.to_sym, Time.zone.now)

    expect(TeamOrderAttendees::Emails::SendCutOffEmail).to_not receive(:new).with(team_order: team_order3, cutoff_time: cutoff_time)
    subject
  end

  context 'Event Logs', event_logs: true do
    it 'logs an `Approaching Cutoff` events for each notifiable team order' do
      expect(EventLogs::Create).to receive(:new).with(event_object: team_order1, event: 'approaching-cutoff', cutoff: cutoff_time)
      expect(EventLogs::Create).to receive(:new).with(event_object: team_order2, event: 'approaching-cutoff', cutoff: cutoff_time)
      expect(EventLogs::Create).to receive(:new).with(event_object: team_order3, event: 'approaching-cutoff', cutoff: cutoff_time)

      subject
    end

    context 'with the team order spends under supplier minimums' do
      let!(:supplier_spend) { SupplierSpend.new(supplier: 'supplier', total_spend: 100, minimum_spend: 200) }

      before do
        invalid_spend_response = OpenStruct.new(is_under?: true, supplier_spends: [supplier_spend])
        allow(supplier_spends_fetcher).to receive(:call).and_return(invalid_spend_response)
      end

      it 'logs an `Approaching Cutoff Below Minimum` events for each notifiable team orders along with cutoff and spends info' do
        expect(EventLogs::Create).to receive(:new).with(event_object: team_order1, event: 'approaching-cutoff-below-minimum', severity: 'warning', cutoff: cutoff_time, cutoff_option: team_order1.cutoff_option, remaining_spend: supplier_spend.remaining_spend.round(2))
        expect(EventLogs::Create).to receive(:new).with(event_object: team_order2, event: 'approaching-cutoff-below-minimum', severity: 'warning', cutoff: cutoff_time, cutoff_option: team_order2.cutoff_option, remaining_spend: supplier_spend.remaining_spend.round(2))
        expect(EventLogs::Create).to receive(:new).with(event_object: team_order3, event: 'approaching-cutoff-below-minimum', severity: 'warning', cutoff: cutoff_time, cutoff_option: team_order3.cutoff_option, remaining_spend: supplier_spend.remaining_spend.round(2))

        subject
      end
    end
  end

  context 'for a recurring team orders' do
    let!(:package_id) { SecureRandom.hex(7) }
    before do
      [team_order1, team_order2, team_order3].each do |order|
        order.update_column(:order_variant, 'recurring_team_order')
        order.team_order_detail.update_column(:package_id, package_id)
      end
    end

    it 'sends (2hr/30m) cutoff emails to team admins of a recurring team order' do
      expect(TeamOrders::Emails::SendAdminCutOffEmail).to receive(:new).with(team_order: team_order1, cutoff_time: cutoff_time)
      expect(TeamOrders::Emails::SendAdminCutOffEmail).to receive(:new).with(team_order: team_order2, cutoff_time: cutoff_time)
      expect(TeamOrders::Emails::SendAdminCutOffEmail).to receive(:new).with(team_order: team_order3, cutoff_time: cutoff_time)

      subject
    end
  end # for a recurring team order

end
