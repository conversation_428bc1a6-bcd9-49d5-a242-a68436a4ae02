require 'rails_helper'

RSpec.describe TeamOrders::Notifications::AdminRecurringTeamOrderExtension, type: :service, team_orders: true do

  let!(:beginning_of_week) { Time.zone.now.beginning_of_week }
  let!(:package_id1) { SecureRandom.hex(7) }
  let!(:team_admin1) { create(:customer_profile, :random) }

  let!(:package_id2) { SecureRandom.hex(7) }
  let!(:team_admin2) { create(:customer_profile, :random) }

  # recurring team orders
  let!(:team_order11) { create(:order, :recurring_team_order, package_id: package_id1, customer_profile: team_admin1, delivery_at: beginning_of_week + 2.days) }
  let!(:team_order12) { create(:order, :recurring_team_order, package_id: package_id1, customer_profile: team_admin1, delivery_at: beginning_of_week + 3.days) }
  let!(:team_order13) { create(:order, :recurring_team_order, package_id: package_id1, customer_profile: team_admin1, delivery_at: beginning_of_week + 1.week + 2.days) }
  let!(:team_order14) { create(:order, :recurring_team_order, package_id: package_id1, customer_profile: team_admin1, delivery_at: beginning_of_week + 1.week + 3.days) }

  let!(:team_order21) { create(:order, :recurring_team_order, package_id: package_id2, customer_profile: team_admin2, delivery_at: beginning_of_week + 4.days) }
  let!(:team_order22) { create(:order, :recurring_team_order, package_id: package_id2, customer_profile: team_admin2, delivery_at: beginning_of_week + 1.week + 4.days) }

  before do
    email_sender = double(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail)
    allow(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call).and_return(true)
  end

  let!(:notification_time) { beginning_of_week + rand(1..4).days + rand(1..10).hours }

  it 'sends emails for each package with no orders in the next 2 weeks' do
    expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).with(team_order: team_order14, extension_week: anything, final_reminder: anything)
    expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).with(team_order: team_order22, extension_week: anything, final_reminder: anything)

    TeamOrders::Notifications::AdminRecurringTeamOrderExtension.new(time: notification_time).call
  end

  it 'doesn\'t send notifications for package orders with team orders in the 2nd to next week' do # REMINDER_THRESHOLD = 2.weeks
    create(:order, :recurring_team_order, package_id: package_id2, customer_profile: team_admin2, delivery_at: beginning_of_week + 2.weeks + 4.days)

    expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to_not receive(:new).with(team_order: team_order22, extension_week: anything, final_reminder: anything)
    TeamOrders::Notifications::AdminRecurringTeamOrderExtension.new(time: notification_time).call
  end

  it 'send the beginning of the 2nd to next week as the exntension week' do
    beginning_of_extension_week = beginning_of_week + 2.weeks
    expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).with(team_order: anything, extension_week: beginning_of_extension_week, final_reminder: anything)
    expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).with(team_order: anything, extension_week: beginning_of_extension_week, final_reminder: anything)
    TeamOrders::Notifications::AdminRecurringTeamOrderExtension.new(time: notification_time).call
  end

  context 'as a final reminder' do
    it 'sends the emails as a final reminder' do
      expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).with(team_order: team_order14, extension_week: anything, final_reminder: true)
      expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).with(team_order: team_order22, extension_week: anything, final_reminder: true)

      TeamOrders::Notifications::AdminRecurringTeamOrderExtension.new(time: notification_time, final_reminder: true).call
    end

    it 'sends the emails as not a final reminder' do
      expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).with(team_order: team_order14, extension_week: anything, final_reminder: false)
      expect(TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail).to receive(:new).with(team_order: team_order22, extension_week: anything, final_reminder: false)

      TeamOrders::Notifications::AdminRecurringTeamOrderExtension.new(time: notification_time, final_reminder: false).call
    end
  end

end
