require 'rails_helper'

RSpec.describe TeamOrders::Notifications::Supplier<PERSON>utoff, type: :service, notifications: true, team_orders: true, suppliers: true do

  subject { TeamOrders::Notifications::SupplierCutoff.new(cutoff_time: cutoff_time, time: notification_time).call }

  let!(:category1) { create(:category, :random, group: 'catering-services') }

  let!(:delivery_time) { Time.zone.parse('2020-08-05 13:00:00') } # Wednesday

  let!(:team_order) { create(:order, :draft, status: 'pending', order_variant: 'team_order', delivery_at: delivery_time) }
  let!(:supplier1) { create(:supplier_profile, :random) }
  let!(:team_order_supplier1) { create(:order_supplier, :random, order: team_order, supplier_profile: supplier1) }
  let!(:minimum11) { create(:minimum, :random, supplier_profile: supplier1, lead_time: 24.0, lead_time_day_before: nil, category: category1) }
  let!(:cutoff_datetime) { Time.zone.parse('2020-08-04 13:00:00') } # Tuesday # 24hrs before delivery

  let!(:cutoff_time) { %w[4hr day].sample }
  let!(:notification_time) do
    number_of_hours = {
      '4hr' => 4,
      'day' => 24,
    }[cutoff_time]
    cutoff_datetime - number_of_hours.hours
  end

  before do
    puts "Notifying for #{cutoff_time} @ #{notification_time}" if ENV['VERBOSE']

    # mock email sender
    email_sender = double(Suppliers::Emails::SendTeamOrderCutoffEmail)
    allow(Suppliers::Emails::SendTeamOrderCutoffEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call).and_return(true)
  end

  it 'sends notification to notifiable suppliers 4hrs/a day before lead time' do
    expect(Suppliers::Emails::SendTeamOrderCutoffEmail).to receive(:new).with(team_order: team_order, supplier: supplier1, cutoff_time: cutoff_time)

    subject
  end

  it 'does not send notification to suppliers already notified' do
    notification_attribute = "cutoff_#{cutoff_time}_reminder"
    team_order_supplier1.update_column(notification_attribute.to_sym, Time.zone.now)

    expect(Suppliers::Emails::SendTeamOrderCutoffEmail).to_not receive(:new).with(team_order: team_order, supplier: supplier1, cutoff_time: cutoff_time)

    subject
  end

  it 'does not send supplier cutoff emails for a team order not within cutoff timeframe' do
    if cutoff_time == '4hr'
      updated_delivery_time = [(delivery_time - 1.days), (delivery_time + 2.days), (delivery_time + 1.hours)].sample
    else
      updated_delivery_time = [(delivery_time - 1.days), (delivery_time + 2.days), (delivery_time + 1.day)].sample
    end
    team_order.update_column(:delivery_at, updated_delivery_time)

    expect(Suppliers::Emails::SendTeamOrderCutoffEmail).to_not receive(:new).with(team_order: team_order, supplier: supplier1, cutoff_time: cutoff_time)

    subject
  end

  context 'with another team order' do
    let!(:team_order2) { create(:order, :draft, status: 'pending', order_variant: 'team_order', delivery_at: delivery_time) }
    let!(:supplier2) { create(:supplier_profile, :random) }
    let!(:team_order_supplier2) { create(:order_supplier, :random, order: team_order2, supplier_profile: supplier2) }
    let!(:minimum21) { create(:minimum, :random, supplier_profile: supplier2, lead_time: 24.0, lead_time_day_before: nil, category: category1) }
    let!(:cutoff_datetime) { Time.zone.parse('2020-08-04 13:00:00') } # Tuesday # 24hrs before delivery

    it 'only sends the cutoff notification to the supplier of the team order' do
      expect(Suppliers::Emails::SendTeamOrderCutoffEmail).to_not receive(:new).with(team_order: team_order, supplier: supplier2, cutoff_time: cutoff_time)
      expect(Suppliers::Emails::SendTeamOrderCutoffEmail).to receive(:new).with(team_order: team_order, supplier: supplier1, cutoff_time: cutoff_time)

      expect(Suppliers::Emails::SendTeamOrderCutoffEmail).to_not receive(:new).with(team_order: team_order2, supplier: supplier1, cutoff_time: cutoff_time)
      expect(Suppliers::Emails::SendTeamOrderCutoffEmail).to receive(:new).with(team_order: team_order2, supplier: supplier2, cutoff_time: cutoff_time)

      subject
    end
  end

  context 'For an order with cutoff on Monday' do
    let!(:delivery_time) { Time.zone.parse('2020-08-04 13:00:00') } # Tuesday
    let!(:cutoff_datetime) { Time.zone.parse('2020-08-03 13:00:00') } # Monday # 24hrs before delivery
    let!(:cutoff_time) { 'day' }

    it 'sends notification on Friday' do
      # notification_time => cutoff_datetime - 3.days
      expect(Suppliers::Emails::SendTeamOrderCutoffEmail).to receive(:new).with(team_order: team_order, supplier: supplier1, cutoff_time: cutoff_time)

      subject
    end
  end

end
