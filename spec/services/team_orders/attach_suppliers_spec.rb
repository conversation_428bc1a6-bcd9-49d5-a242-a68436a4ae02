require 'rails_helper'

RSpec.describe TeamOrders::AttachSuppliers, type: :service, orders: true, team_orders: true, suppliers: true do

  let!(:team_order) { create(:order, :team_order) }
  let!(:supplier1) { create(:supplier_profile, :random, company_name: 'supplier1') }
  let!(:supplier2) { create(:supplier_profile, :random, company_name: 'supplier2') }

  let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1) }
  let!(:menu_section12) { create(:menu_section, :random, supplier_profile: supplier1) }

  let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2) }
  let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2) }

  let!(:delivery_suppliers) do
    {
      supplier1.id.to_s => [],
      supplier2.id.to_s => [],
    }
  end

  before do
    heads_up_email_sender = delayed_heads_up_email_sender = double(Suppliers::Emails::SendTeamOrderHeadsUpEmail)
    allow(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).and_return(heads_up_email_sender)
    allow(heads_up_email_sender).to receive(:delay).and_return(delayed_heads_up_email_sender)
    allow(delayed_heads_up_email_sender).to receive(:call).and_return(true)

    cancelled_email_sender = delayed_cancelled_email_sender = double(Suppliers::Emails::SendOrderCancelledEmail)
    allow(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).and_return(cancelled_email_sender)
    allow(cancelled_email_sender).to receive(:delay).and_return(delayed_cancelled_email_sender)
    allow(delayed_cancelled_email_sender).to receive(:call).and_return(true)
  end

  it 'attaches the suppliers to the team order' do
    supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: delivery_suppliers).call

    expect(supplier_attacher).to be_success
    expect(supplier_attacher.attached_suppliers.map(&:supplier_profile)).to include(supplier1, supplier2)
  end

  it 'sends a heads up email to the attached suppliers' do
    expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).with(team_order: team_order, supplier: supplier1)
    expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).with(team_order: team_order, supplier: supplier2)

    supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: delivery_suppliers).call

    expect(supplier_attacher).to be_success
  end

  context 'with menu sections config' do
    let!(:delivery_suppliers_with_menu_sections) do
      {
        supplier1.id.to_s => [menu_section11, menu_section12].map(&:id),
        supplier2.id.to_s => [menu_section21, menu_section22].map(&:id),
      }
    end

    it 'attaches suppliers with the passed in menu section config' do
      supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: delivery_suppliers_with_menu_sections).call

      expect(supplier_attacher).to be_success

      supplier1_team_order_supplier = supplier_attacher.attached_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier1 }
      expect(supplier1_team_order_supplier.selected_menu_sections).to match_array(delivery_suppliers_with_menu_sections[supplier1.id.to_s])

      supplier2_team_order_supplier = supplier_attacher.attached_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier2 }
      expect(supplier2_team_order_supplier.selected_menu_sections).to match_array(delivery_suppliers_with_menu_sections[supplier2.id.to_s])
    end
  end

  context 'with existing team suppliers' do
    let!(:supplier3) { create(:supplier_profile, :random, company_name: 'supplier3') }

    let!(:order_supplier1) { create(:order_supplier, :random, order: team_order, supplier_profile: supplier1, selected_menu_sections: [menu_section11, menu_section12].map(&:id)) }
    let!(:order_supplier2) { create(:order_supplier, :random, order: team_order, supplier_profile: supplier2, selected_menu_sections: [menu_section21, menu_section22].map(&:id)) }

    let!(:updated_delivery_suppliers) do
      {
        supplier1.id.to_s => [[menu_section12].map(&:id), []].sample,
        supplier3.id.to_s => [],
      }
    end

    it 'attaches the new supplier to the team order' do
      supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: updated_delivery_suppliers).call

      expect(supplier_attacher).to be_success
      expect(supplier_attacher.attached_suppliers.map(&:supplier_profile)).to include(supplier3)
      expect(team_order.reload.team_supplier_profiles).to include(supplier1, supplier3)
    end

    context 'supplier removal' do
      it 'removes any existing supplier not passed in' do
        supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: updated_delivery_suppliers).call

        expect(supplier_attacher).to be_success
        expect(supplier_attacher.attached_suppliers.map(&:supplier_profile)).to_not include(supplier2)
        expect(team_order.reload.team_supplier_profiles).to_not include(supplier2)
      end

      context 'with supplier order lines' do
        let!(:attendee1) { create(:team_order_attendee, :random, order: team_order, status: 'ordered') }
        let!(:attendee2) { create(:team_order_attendee, :random, order: team_order, status: 'declined') }
        let!(:attendee3) { create(:team_order_attendee, :random, order: team_order, status: 'invited') }
        let!(:attendee4) { create(:team_order_attendee, :random, order: team_order, status: 'ordered') }
        let!(:attendee5) { create(:team_order_attendee, :random, order: team_order, status: 'cancelled') }

        let!(:order_line11) { create(:order_line, :random, order: team_order, supplier_profile: supplier2, team_order_attendee: attendee1) }
        let!(:order_line41) { create(:order_line, :random, order: team_order, supplier_profile: supplier2, team_order_attendee: attendee4) }

        before do
          Orders::CalculateCustomerTotals.new(order: team_order.reload, save_totals: true).call # update totals with order lines
        end

        it 'removes the order lines from the previous supplier' do
          supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: updated_delivery_suppliers).call
          expect(supplier_attacher).to be_success

          expect(team_order.reload.order_lines).to be_blank
          expect{ order_line11.reload }.to raise_error(ActiveRecord::RecordNotFound)
          expect{ order_line41.reload }.to raise_error(ActiveRecord::RecordNotFound)
        end

        it 're-sets the total of the order' do
          supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: updated_delivery_suppliers).call
          expect(supplier_attacher).to be_success

          expect(team_order.reload.customer_total).to eq(0.0)
        end

        it 're-sets the status of the ordered attendees back to invited' do
          supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: updated_delivery_suppliers).call
          expect(supplier_attacher).to be_success

          expect(attendee1.reload.status).to eq('invited')
          expect(attendee4.reload.status).to eq('invited')
        end
      end # with existing order lines

      it 'notifies the removed team order supplier about the cancellation' do
        expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'one-off', supplier: supplier2, orders: [team_order])

        supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: updated_delivery_suppliers).call
        expect(supplier_attacher).to be_success
      end
    end

    it 'updates the selected menu sections config of the existing supplier' do
      supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: updated_delivery_suppliers).call

      expect(supplier_attacher).to be_success
      supplier1_team_order_supplier = team_order.reload.order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier1 }

      expect(supplier1_team_order_supplier.selected_menu_sections).to match_array(updated_delivery_suppliers[supplier1.id.to_s])
    end

    it 'only sends a heads up email to the newly attached suppliers' do
      expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).with(team_order: team_order, supplier: supplier3) # new supplier

      expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to_not receive(:new).with(team_order: team_order, supplier: supplier1) # existing supplier
      expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to_not receive(:new).with(team_order: team_order, supplier: supplier2) # removed supplier

      supplier_attacher = TeamOrders::AttachSuppliers.new(team_order: team_order, delivery_suppliers: updated_delivery_suppliers).call

      expect(supplier_attacher).to be_success
    end
  end

end
