require 'rails_helper'

RSpec.describe TeamOrders::UpsertDetail, type: :service, team_order: true do

  let!(:team_order) { create(:order, :random, status: 'pending', order_variant: 'team_order') }
  let!(:team_order_detail_params) { { cutoff_option: TeamOrder::Detail::VALID_CUTOFF_OPTIONS.sample, attendee_pays: true } }

  it 'creates a new team order detail with passed in params' do
    detail_creator = TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: team_order_detail_params).call

    expect(detail_creator).to be_success
    created_team_order_detail = detail_creator.team_order_detail
    expect(created_team_order_detail).to be_present
    expect(created_team_order_detail).to be_persisted

    expect(created_team_order_detail.order).to eq(team_order)
    expect(created_team_order_detail.cutoff_option).to eq(team_order_detail_params[:cutoff_option])
    expect(created_team_order_detail.attendee_pays).to eq(team_order_detail_params[:attendee_pays])
  end

  it 'creates a new (default) team order detail if no params are passed' do
    detail_creator = TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: {}).call

    expect(detail_creator).to be_success
    created_team_order_detail = detail_creator.team_order_detail
    expect(created_team_order_detail).to be_present
    expect(created_team_order_detail).to be_persisted

    expect(created_team_order_detail.order).to eq(team_order)
    expect(created_team_order_detail.cutoff_option).to eq('charge_to_minimum')
  end

  context 'with existing team order detail' do
    let!(:team_order_detail) { create(:team_order_detail, :random, order: team_order) }

    it 'updates the existing detail with passed in params' do
      detail_updator = TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: team_order_detail_params).call

      expect(detail_updator).to be_success
      updated_team_order_detail = detail_updator.team_order_detail

      expect(updated_team_order_detail.id).to eq(team_order_detail.id)
      expect(updated_team_order_detail.cutoff_option).to eq(team_order_detail_params[:cutoff_option])
      expect(updated_team_order_detail.attendee_pays).to eq(team_order_detail_params[:attendee_pays])
    end

    it 'does not reset the cutoff_option if passed in details are missing' do
      detail_updator = TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: team_order_detail_params.except(:cutoff_option)).call

      expect(detail_updator).to be_success
      updated_team_order_detail = detail_updator.team_order_detail

      expect(updated_team_order_detail.id).to eq(team_order_detail.id)
      expect(updated_team_order_detail.cutoff_option).to eq(team_order_detail.cutoff_option)
    end
  end

  context 'with team order levels' do
    let!(:detail_params_with_levels) do
      team_order_detail_params.merge({
        levels: {
          names: [Faker::Name.name, Faker::Name.name]
        }
      })
    end

    before do
      # mock team order levels upserter
      levels_upserter = double(TeamOrders::UpsertLevels)
      allow(TeamOrders::UpsertLevels).to receive(:new).and_return(levels_upserter)
      allow(levels_upserter).to receive(:call).and_return(true)
    end

    it 'passes through the level names to upsert the team order levels' do
      expect(TeamOrders::UpsertLevels).to receive(:new).with(team_order_detail: anything, names: detail_params_with_levels[:levels][:names])

      detail_upserter = TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: detail_params_with_levels).call
      expect(detail_upserter).to be_success
    end
  end

  context 'as a package order' do
    let!(:package_id) { SecureRandom.uuid }

    it 'sets the package order for the team order details as the passed in order' do
      package_team_order_detail_params = team_order_detail_params.merge({ package_id: package_id })
      detail_creator = TeamOrders::UpsertDetail.new(team_order: team_order, team_order_detail_params: package_team_order_detail_params).call

      expect(detail_creator).to be_success
      created_team_order_detail = detail_creator.team_order_detail
      expect(created_team_order_detail.package_id).to eq(package_id)
      expect(team_order.reload.is_package_order?).to be_truthy
    end
  end

end
