require 'rails_helper'

RSpec.describe TeamOrders::Cancel, type: :service, team_order: true, orders: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:team_order) { create(:order, :random, customer_profile: team_admin, status: 'pending', order_variant: 'team_order', delivery_at: Time.zone.now - 1.day - 2.hours) }

  before do
    email_sender = delayed_email_sender = double(TeamOrderAttendees::Emails::SendOrderCancelledEmail)
    allow(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    supplier_email_sender = delayed_supplier_email_sender = double(Suppliers::Emails::SendOrderCancelledEmail)
    allow(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).and_return(supplier_email_sender)
    allow(supplier_email_sender).to receive(:delay).and_return(delayed_supplier_email_sender)
    allow(delayed_supplier_email_sender).to receive(:call)
  end

  it 'successfully cancels the team order' do
    order_canceller = TeamOrders::Cancel.new(team_order: team_order, team_admin: team_admin).call

    expect(order_canceller).to be_success
    expect(team_order.reload.status).to eq('cancelled')
  end

  context 'with team supplier profiles' do
    let!(:supplier) { create(:supplier_profile, :random) }
    let!(:team_order_supplier) { create(:order_supplier, order: team_order, supplier_profile: supplier) }

    before do
      team_order.reload
    end

    it 'sends an order cancellation email to the supplier', notifications: true do
      expect(Suppliers::Emails::SendOrderCancelledEmail).to receive(:new).with(mode: 'one-off', supplier: supplier, orders: [team_order])
      order_canceller = TeamOrders::Cancel.new(team_order: team_order, team_admin: team_admin).call

      expect(order_canceller).to be_success
      expect(team_order.reload.status).to eq('cancelled')
    end
  end

  context 'with team order attendees' do
    let!(:attendee1) { create(:team_order_attendee, :random, order: team_order, status: %w[invited pending ordered].sample) }
    let!(:attendee2) { create(:team_order_attendee, :random, order: team_order, status: %w[invited pending ordered].sample) }

    it 'sends a cancellation email to all the invited team order attendees' do
      expect(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to receive(:new).with(team_order_attendee: attendee1, previous_order_status: 'pending')
      expect(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to receive(:new).with(team_order_attendee: attendee2, previous_order_status: 'pending')

      order_canceller = TeamOrders::Cancel.new(team_order: team_order.reload, team_admin: team_admin).call

      expect(order_canceller).to be_success
    end

    it 'does not send cancellation email to all any team order attendees if notify_attendees is passed as false' do
      expect(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to_not receive(:new)
      order_canceller = TeamOrders::Cancel.new(team_order: team_order.reload, team_admin: team_admin, notify_attendees: false).call

      expect(order_canceller).to be_success
    end

    it 'does not send cancellation email to cancelled or declined team order attendees' do
      attendee2.update_column(:status, %w[declined cancelled].sample)

      expect(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to receive(:new).with(team_order_attendee: attendee1, previous_order_status: 'pending')
      expect(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to_not receive(:new).with(team_order_attendee: attendee2, previous_order_status: anything)
      order_canceller = TeamOrders::Cancel.new(team_order: team_order.reload, team_admin: team_admin).call

      expect(order_canceller).to be_success
    end

    it 'does not send cancellation to invited attendees if the the order was cancelled after submission' do
      team_order_status = %w[new confirmed amended].sample
      team_order.update_column(:status, team_order_status)
      attendee1.update_column(:status, 'invited')
      attendee2.update_column(:status, %w[pending ordered].sample)

      expect(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to_not receive(:new).with(team_order_attendee: attendee1, previous_order_status: anything)
      expect(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to receive(:new).with(team_order_attendee: attendee2, previous_order_status: team_order_status)
      order_canceller = TeamOrders::Cancel.new(team_order: team_order.reload, team_admin: team_admin).call

      expect(order_canceller).to be_success
    end
  end

  context 'errors' do
    it 'cannot cancel an already cancelled team order' do
      team_order.update_column(:status, 'cancelled')
      order_canceller = TeamOrders::Cancel.new(team_order: team_order, team_admin: team_admin).call

      expect(order_canceller).to_not be_success
      expect(order_canceller.errors).to include('Order is already cancelled')
    end

    it 'cannot cancel an already delivered team order' do
      team_order.update_column(:status, 'delivered')
      order_canceller = TeamOrders::Cancel.new(team_order: team_order, team_admin: team_admin).call

      expect(order_canceller).to_not be_success
      expect(order_canceller.errors).to include('Cannot cancel a delivered order')
    end

    it 'a team admin cannot cancel an order not belonging to them' do
      another_team_admin = create(:customer_profile, :random)
      order_canceller = TeamOrders::Cancel.new(team_order: team_order, team_admin: another_team_admin).call

      expect(order_canceller).to_not be_success
      expect(order_canceller.errors).to include('You don\'t have access to this team order')
    end
  end

end
