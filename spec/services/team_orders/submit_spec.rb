require 'rails_helper'

RSpec.describe TeamOrders::Submit, type: :service, team_orders: true, orders: true do

  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:team_order) { create(:order, :team_order, customer_profile: team_admin, delivery_at: Time.zone.now - 1.day - 2.hours) }
  let!(:team_order_supplier) { create(:order_supplier, :random, supplier_profile: supplier, order: team_order) }

  let!(:team_order_attendee) { create(:team_order_attendee, :random, order: team_order, status: 'ordered') }
  let!(:order_line) { create(:order_line, :random, supplier_profile: supplier, order: team_order, team_order_attendee: team_order_attendee, price: 11, quantity: 3) }

  before do
    supplier_email_sender = delayed_supplier_email_sender = Suppliers::Emails::SendNewTeamOrderEmail
    allow(Suppliers::Emails::SendNewTeamOrderEmail).to receive(:new).and_return(supplier_email_sender)
    allow(supplier_email_sender).to receive(:delay).and_return(delayed_supplier_email_sender)
    allow(delayed_supplier_email_sender).to receive(:call).and_return(true)

    customer_email_sender = delayed_customer_email_sender = Suppliers::Emails::SendNewTeamOrderEmail
    allow(TeamOrders::Emails::SendAdminOrderSubmissionEmail).to receive(:new).and_return(customer_email_sender)
    allow(customer_email_sender).to receive(:delay).and_return(delayed_customer_email_sender)
    allow(delayed_customer_email_sender).to receive(:call).and_return(true)
  end

  it 'successfully submits the team order' do
    order_submitter = TeamOrders::Submit.new(team_order: team_order).call

    expect(order_submitter).to be_success
    submitted_team_order = order_submitter.team_order

    expect(submitted_team_order.status).to eq('new')
  end

  it 'sends an email to the supplier' do
    expect(Suppliers::Emails::SendNewTeamOrderEmail).to receive(:new).with(supplier: supplier, team_order: team_order)

    order_submitter = TeamOrders::Submit.new(team_order: team_order).call
    expect(order_submitter).to be_success
    expect(team_order.suppliers_notified_at).to be_present
  end

  it 'sends an email to the team admin' do
    expect(TeamOrders::Emails::SendAdminOrderSubmissionEmail).to receive(:new).with(team_order: team_order)

    order_submitter = TeamOrders::Submit.new(team_order: team_order).call
    expect(order_submitter).to be_success
  end

  context 'with cutoff options' do
    let!(:category) { create(:category, :random, group: 'catering-services') }
    let!(:supplier_minimum) { create(:minimum, :random, supplier_profile: supplier, spend_price: 100, category: category) }

    context 'cutoff_option == cancel_order' do
      before do
        team_order.team_order_detail.update_column(:cutoff_option, 'cancel_order')
      end

      it 'cancels the order if order is below supplier minimum' do
        order_submitter = TeamOrders::Submit.new(team_order: team_order).call

        expect(order_submitter).to_not be_success
        submitted_team_order = order_submitter.team_order

        expect(submitted_team_order.status).to eq('cancelled')
        expect(order_submitter.errors).to include('Order is cancelled as it did not meet supplier minimums')
      end

      it 'does not send any cancellation emails to the team order attendees' do
        expect(TeamOrderAttendees::Emails::SendOrderCancelledEmail).to_not receive(:new)

        order_submitter = TeamOrders::Submit.new(team_order: team_order).call

        expect(order_submitter).to_not be_success
        submitted_team_order = order_submitter.team_order

        expect(submitted_team_order.status).to eq('cancelled')
      end

      it 'sends an email to the team admin even on cancellation' do
        expect(TeamOrders::Emails::SendAdminOrderSubmissionEmail).to receive(:new).with(team_order: team_order)

        order_submitter = TeamOrders::Submit.new(team_order: team_order).call
        expect(order_submitter).to_not be_success
      end
    end

    it 'adds a surcharge of the remaining amount of the supplier(s) minimum and submits the order' do
      team_order.team_order_detail.update_column(:cutoff_option, 'charge_to_minimum')
      order_submitter = TeamOrders::Submit.new(team_order: team_order).call

      expect(order_submitter).to be_success
      submitted_team_order = order_submitter.team_order

      expect(submitted_team_order.status).to eq('new')
      expect(submitted_team_order.customer_topup).to eq(67) # remaining value = 100 - 33 + no-credit card surcharge
    end
  end

  context 'with anonymous attendees' do
    before do
      team_order_attendee.update_column(:anonymous, true)
    end

    it 'auto approves any anonymous team order attendees and submits the team order' do
      expect(team_order_attendee).to be_anonymous
      order_submitter = TeamOrders::Submit.new(team_order: team_order).call

      expect(order_submitter).to be_success
      submitted_team_order = order_submitter.team_order

      expect(submitted_team_order.status).to eq('new')
      expect(team_order_attendee.reload).to_not be_anonymous
    end
  end

  context 'recurring team orders' do
    before do
      team_order.update_column(:order_variant, 'recurring_team_order')
      team_order.team_order_detail.update_column(:package_id, SecureRandom.hex(7))
    end

    it 'submits a valid recurring team order by default' do
      order_submitter = TeamOrders::Submit.new(team_order: team_order).call

      expect(order_submitter).to be_success

      submitted_team_order = order_submitter.team_order
      expect(submitted_team_order.status).to eq('new')
    end

    it 'cannot submit a recurring team order that is not past its cutoff time' do
      team_order.update_column(:delivery_at, Time.zone.now + 5.days)

      order_submitter = TeamOrders::Submit.new(team_order: team_order).call

      expect(order_submitter).to_not be_success
      expect(order_submitter.errors).to include('Order is not past its cutoff time')
    end

    it 'skips a recurring team order past its cutoff but with no confirmed order lines' do
      order_line.destroy
      order_submitter = TeamOrders::Submit.new(team_order: team_order.reload).call

      expect(order_submitter).to_not be_success
      expect(order_submitter.errors).to include('Order is skipped as it does not have confirmed order lines')

      submitted_team_order = order_submitter.team_order
      expect(submitted_team_order.status).to eq('skipped')
    end
  end

  context 'errors' do
    it 'cannot submit a non-pending order' do
      team_order.update_column(:status, %w[draft amended confirmed delivered].sample)

      order_submitter = TeamOrders::Submit.new(team_order: team_order).call

      expect(order_submitter).to_not be_success
      expect(order_submitter.errors).to include("Cannot submit a #{team_order.status} order")
    end

    it 'cannot submit an order that is not past its cutoff time' do
      team_order.update_column(:delivery_at, Time.zone.now + 5.days)

      order_submitter = TeamOrders::Submit.new(team_order: team_order).call

      expect(order_submitter).to_not be_success
      expect(order_submitter.errors).to include('Order is not past its cutoff time')
    end

    it 'cannot submit an order without any confirmed order lines' do
      if [true, false].sample
      order_line.destroy # remove the order line
      else
      team_order_attendee.update_column(:status, 'pending') # mark attendee as not-confirmed
      end
      order_submitter = TeamOrders::Submit.new(team_order: team_order).call

      expect(order_submitter).to_not be_success
      expect(order_submitter.errors).to include('Order does not have any confirmed order lines')
    end

    it 'does not sends an emails to the supplier or team admin on error' do
      is_non_pending_order = [true, false].sample
      not_after_cutoff = [true, false].sample
      with_removed_order_line = [true, false].sample
      case
      when is_non_pending_order
        team_order.update_column(:status, %w[draft confirmed delivered].sample) # not pending / amended
      when not_after_cutoff
        team_order.update_column(:delivery_at, Time.zone.now + 5.days) # not after cut off
      when with_removed_order_line
        order_line.destroy # remove the order line
      else
        team_order_attendee.update_column(:status, 'pending') # mark attendee as not-confirmed
      end
      expect(Suppliers::Emails::SendNewTeamOrderEmail).to_not receive(:new)
      expect(TeamOrders::Emails::SendAdminOrderSubmissionEmail).to_not receive(:new)

      order_submitter = TeamOrders::Submit.new(team_order: team_order).call
      expect(order_submitter).to_not be_success
    end
  end
end
