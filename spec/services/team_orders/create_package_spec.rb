require 'rails_helper'

RSpec.describe TeamOrders::CreatePackage, type: :service, orders: true, team_orders: true do

  let!(:now) { Time.zone.now }
  let!(:package_delivery_dates) { [(now + 2.days), (now + 4.days), (now + 6.days)].map(&:to_date) }
  let!(:package_delivery_time) { (now + 2.days) }
  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:suburb) { create(:suburb, :random) }

  let!(:team_order_package_params) { { name: Faker::Name.name, delivery_at: package_delivery_time, delivery_dates: package_delivery_dates, delivery_suburb_id: suburb.id, delivery_address: Faker::Address.street_address } }

  before do
    # mock admin email sender
    admin_email_sender = delayed_admin_email_sender = double(TeamOrders::Emails::SendAdminNewPackageOrderEmail)
    allow(TeamOrders::Emails::SendAdminNewPackageOrderEmail).to receive(:new).and_return(admin_email_sender)
    allow(admin_email_sender).to receive(:delay).and_return(delayed_admin_email_sender)
    allow(delayed_admin_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  context 'with team order creation mocks' do
    before do
      # mock team order creator
      team_package_creator = double(TeamOrders::Create)
      allow(TeamOrders::Create).to receive(:new).and_return(team_package_creator)
      creator_response = OpenStruct.new(success?: true, errors: [], team_order: create(:order, :random))
      allow(team_package_creator).to receive(:call).and_return(creator_response)
    end

    it 'calls team order create to create the team orders for individual dates' do
      package_delivery_dates.sort.each do |delivery_date|
        delivery_time = delivery_date.to_time.in_time_zone.change(hour: package_delivery_time.hour, min: package_delivery_time.min)
        team_order_params = team_order_package_params.except(:delivery_dates).merge({ delivery_at: delivery_time, delivery_suppliers: nil })
        expect(TeamOrders::Create).to receive(:new).with(team_order_params: team_order_params, team_admin: team_admin, package_id: anything)
      end

      package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_package_params, team_admin: team_admin).call
      expect(package_creator).to be_success
    end
  end

  before do
    # mock supplier email sender
    supplier_email_sender = delayed_supplier_email_sender = double(Suppliers::Emails::SendTeamOrderHeadsUpEmail)
    allow(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).and_return(supplier_email_sender)
    allow(supplier_email_sender).to receive(:delay).and_return(delayed_supplier_email_sender)
    allow(delayed_supplier_email_sender).to receive(:call).and_return(true)

    # send team order admin email sender
    admin_email_sender = delayed_admin_email_sender = double(Suppliers::Emails::SendTeamOrderHeadsUpEmail)
    allow(TeamOrders::Emails::SendAdminNewOrderEmail).to receive(:new).and_return(admin_email_sender)
    allow(admin_email_sender).to receive(:delay).and_return(delayed_admin_email_sender)
    allow(delayed_admin_email_sender).to receive(:call).and_return(true)
  end

  it 'creates the team orders for each delivery date' do
    package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_package_params, team_admin: team_admin).call
    expect(package_creator).to be_success

    created_package_id = package_creator.package_id
    package_team_orders = package_creator.package_team_orders

    expect(package_team_orders.size).to eq(package_delivery_dates.size)
    delivery_times = package_delivery_dates.map do |date|
      date.to_time.in_time_zone.change(hour: package_delivery_time.hour, min: package_delivery_time.min)
    end
    expect(package_team_orders.map(&:delivery_at).uniq.sort).to include(*delivery_times.sort)
    expect(package_team_orders.map(&:is_package_order?).uniq).to match_array([true])
    expect(package_team_orders.map(&:package_id).uniq).to match_array([created_package_id])
  end

  it 'sends new team order package email to team admin' do
    expect(TeamOrders::Emails::SendAdminNewPackageOrderEmail).to receive(:new).with(package_orders: anything, is_extension: false)

    package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_package_params, team_admin: team_admin).call
    expect(package_creator).to be_success
  end

  it 'does not send any new team order emails for individual orders' do
    package_delivery_dates.each do |_|
      expect(TeamOrders::Emails::SendAdminNewOrderEmail).to_not receive(:new).with(team_order: anything)
    end

    package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_package_params, team_admin: team_admin).call
    expect(package_creator).to be_success
  end

  it 'logs a `New Package Created` event', event_logs: true do
    expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'new-package-created') # event_objecct is created team order

    package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_package_params, team_admin: team_admin).call
    expect(package_creator).to be_success
  end

  context 'with team order attendees' do
    let!(:event_attendee1) { create(:event_attendee, :random, team_admin: team_admin) }
    let!(:event_attendee2) { create(:event_attendee, :random) }
    let!(:event_attendee3) { create(:event_attendee, :random, team_admin: team_admin) }

    before do
      package_invite_sender = delayed_package_invite_sender = double(TeamOrderAttendees::Emails::SendInviteEmail)
      allow(TeamOrderAttendees::Emails::SendPackageInviteEmail).to receive(:new).and_return(package_invite_sender)
      allow(package_invite_sender).to receive(:delay).and_return(delayed_package_invite_sender)
      allow(delayed_package_invite_sender).to receive(:call).and_return(true)

      invite_sender = delayed_invite_sender = double(TeamOrderAttendees::Emails::SendInviteEmail)
      allow(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).and_return(invite_sender)
      allow(invite_sender).to receive(:delay).and_return(delayed_invite_sender)
      allow(delayed_invite_sender).to receive(:call).and_return(true)
    end

    it 'attaches attendees to the each package team order' do
      package_params_with_attendees = team_order_package_params.merge({ attendee_ids: [event_attendee1, event_attendee3].map(&:id) })

      package_creator = TeamOrders::CreatePackage.new(team_order_params: package_params_with_attendees, team_admin: team_admin).call
      expect(package_creator).to be_success

      package_team_orders = package_creator.package_team_orders

      package_delivery_dates.map(&:to_date).each do |delivery_date|
        delivery_time = delivery_date.to_time.in_time_zone.change(hour: package_delivery_time.hour, min: package_delivery_time.min)
        package_team_order = package_team_orders.detect{|order| order.delivery_at == delivery_time }
        expect(package_team_order).to be_present
        expect(package_team_order.team_order_attendees.map(&:event_attendee)).to include(event_attendee1, event_attendee3)
      end
    end

    it 'sends a package invitation emails to the attendees of the package team order' do
      event_attendees = [event_attendee1, event_attendee3]

      event_attendees.each do |_|
        expect(TeamOrderAttendees::Emails::SendPackageInviteEmail).to receive(:new).with(package_order_attendee: anything, package_orders: anything)
      end

      package_params_with_attendees = team_order_package_params.merge({ attendee_ids: event_attendees.map(&:id) })

      package_creator = TeamOrders::CreatePackage.new(team_order_params: package_params_with_attendees, team_admin: team_admin).call
      expect(package_creator).to be_success
    end

    it 'does not send attendee invitation emails for attendees attached to undelying individual orders' do
      event_attendees = [event_attendee1, event_attendee3]

      package_delivery_dates.each do |_|
        event_attendees.each do |_|
          expect(TeamOrderAttendees::Emails::SendInviteEmail).to_not receive(:new).with(team_order_attendee: anything)
        end
      end

      package_params_with_attendees = team_order_package_params.merge({ attendee_ids: event_attendees.map(&:id) })

      package_creator = TeamOrders::CreatePackage.new(team_order_params: package_params_with_attendees, team_admin: team_admin).call
      expect(package_creator).to be_success
    end
  end

  context 'with suppliers' do
    let!(:supplier1) { create(:supplier_profile, :random, company_name: 'supplier1') }
    let!(:supplier2) { create(:supplier_profile, :random, company_name: 'supplier2') }
    let!(:supplier3) { create(:supplier_profile, :random, company_name: 'supplier3') }
    let!(:all_suppliers) { [supplier1, supplier2, supplier3] }
    let!(:delivery_suppliers) do
      delivery_suppliers = {}
      package_delivery_dates.each_with_index do |delivery_date, idx|
        delivery_suppliers[delivery_date.strftime('%Y-%m-%d')] = { all_suppliers[idx].id.to_s => [] }
      end
      delivery_suppliers
    end

    it 'attaches the supplier to the undelying team orders respectively' do
      package_params_with_suppliers = team_order_package_params.merge({ delivery_suppliers: delivery_suppliers })
      package_creator = TeamOrders::CreatePackage.new(team_order_params: package_params_with_suppliers, team_admin: team_admin).call
      expect(package_creator).to be_success

      package_team_orders = package_creator.package_team_orders

      package_delivery_dates.each_with_index do |delivery_date, idx|
        team_order = package_team_orders.detect{|order| order.delivery_at.to_date == delivery_date.to_date }
        expect(team_order).to be_present
        expect(team_order.reload.team_supplier_profiles).to include(all_suppliers[idx])
      end
    end
  end

  context 'with team order details' do
    let!(:team_order_params_with_details) do
      team_order_details = { cutoff_option: TeamOrder::Detail::VALID_CUTOFF_OPTIONS.sample, budget: rand(20..50), hide_budget: [true, false].sample, attendee_pays: [true, false].sample }
      team_order_package_params.merge({ team_order_detail_attributes: team_order_details })
    end

    it 'creates the team orders along with the passed in team order details' do
      package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_params_with_details, team_admin: team_admin).call
      expect(package_creator).to be_success

      package_team_order_details = package_creator.package_team_orders.map(&:team_order_detail)

      expect(package_team_order_details.map(&:cutoff_option).uniq.sort).to include(team_order_params_with_details[:team_order_detail_attributes][:cutoff_option])
      expect(package_team_order_details.map(&:budget).uniq.sort).to include(team_order_params_with_details[:team_order_detail_attributes][:budget])
      expect(package_team_order_details.map(&:hide_budget).uniq.sort).to include(team_order_params_with_details[:team_order_detail_attributes][:hide_budget])
      expect(package_team_order_details.map(&:attendee_pays).uniq.sort).to include(team_order_params_with_details[:team_order_detail_attributes][:attendee_pays])
    end
  end

  context 'as a recurring team order' do
    let!(:team_order_recurring_package_params) do
      team_order_details = { cutoff_option: TeamOrder::Detail::VALID_CUTOFF_OPTIONS.sample, budget: rand(20..50), hide_budget: [true, false].sample, attendee_pays: [true, false].sample }
      team_order_package_params.merge({ order_variant: 'recurring_team_order', team_order_detail_attributes: team_order_details })
    end

    it 'creates the team orders for each delivery date' do # initial creation
      package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_recurring_package_params, team_admin: team_admin).call
      expect(package_creator).to be_success

      created_package_id = package_creator.package_id
      package_team_orders = package_creator.package_team_orders

      expect(package_team_orders.size).to eq(package_delivery_dates.size)
      delivery_times = package_delivery_dates.map do |date|
        date.to_time.in_time_zone.change(hour: package_delivery_time.hour, min: package_delivery_time.min)
      end
      expect(package_team_orders.map(&:delivery_at).uniq.sort).to include(*delivery_times.sort)
      expect(package_team_orders.map(&:is_team_order?).uniq).to match_array([true])
      expect(package_team_orders.map(&:is_package_order?).uniq).to match_array([true])
      expect(package_team_orders.map(&:is_recurring_team_order?).uniq).to match_array([true])
      expect(package_team_orders.map(&:package_id).uniq).to match_array([created_package_id])
    end

    context 'extending a recurring team order' do # via the frontend
      let!(:package_id) { SecureRandom.hex(7) }
      let!(:team_order_recurring_package_extension_params) do
        params = team_order_recurring_package_params.dup
        params[:team_order_detail_attributes][:package_id] = package_id
        params
      end

      it 'creates team orders when extending a team order package' do # when package id is passed
        package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_recurring_package_extension_params, team_admin: team_admin).call
        expect(package_creator).to be_success

        created_package_id = package_creator.package_id
        package_team_orders = package_creator.package_team_orders

        expect(created_package_id).to eq(package_id)
        expect(package_team_orders.map(&:package_id).uniq).to eq([package_id])
      end

      it 'sends new team order package email with extension to team admin' do
        expect(TeamOrders::Emails::SendAdminNewPackageOrderEmail).to receive(:new).with(package_orders: anything, is_extension: true)

        package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_recurring_package_extension_params, team_admin: team_admin).call
        expect(package_creator).to be_success
      end

      it 'logs a `Package Extended` event', event_logs: true do
        expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'package-extended') # event_objecct is created extension team order

        package_creator = TeamOrders::CreatePackage.new(team_order_params: team_order_recurring_package_extension_params, team_admin: team_admin).call
        expect(package_creator).to be_success
      end
    end
  end

end
