require 'rails_helper'

RSpec.describe TeamOrders::Create, type: :service, team_orders: true, orders: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:suburb) { create(:suburb, :random) }

  let!(:team_order_params) { { name: Faker::Name.name, delivery_at: Time.zone.now + 10.days, delivery_suburb_id: suburb.id, delivery_address: Faker::Address.street_address } }

  before do
    # mock supplier email sender
    supplier_email_sender = delayed_supplier_email_sender = double(Suppliers::Emails::SendTeamOrderHeadsUpEmail)
    allow(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).and_return(supplier_email_sender)
    allow(supplier_email_sender).to receive(:delay).and_return(delayed_supplier_email_sender)
    allow(delayed_supplier_email_sender).to receive(:call).and_return(true)

    # mock team admin email sender
    admin_email_sender = delayed_admin_email_sender = double(TeamOrders::Emails::SendAdminNewOrderEmail)
    allow(TeamOrders::Emails::SendAdminNewOrderEmail).to receive(:new).and_return(admin_email_sender)
    allow(admin_email_sender).to receive(:delay).and_return(delayed_admin_email_sender)
    allow(delayed_admin_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'creates a new team order' do
    order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin).call

    expect(order_creator).to be_success
    created_team_order = order_creator.team_order
    expect(created_team_order).to be_present
    expect(created_team_order).to be_a(Order)
  end

  it 'creates the order with default params' do
    order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin).call

    expect(order_creator).to be_success
    created_team_order = order_creator.team_order
    expect(created_team_order.customer_profile).to eq(team_admin)
    expect(created_team_order.status).to eq('pending')
    expect(created_team_order.order_variant).to eq('team_order')
    expect(created_team_order).to be_is_team_order
    expect(created_team_order.credit_card_id).to eq(1) # pay on account
    expect(created_team_order.unique_event_id).to be_present
    expect(created_team_order.uuid).to be_present
  end

  it 'creates the order with passed in params' do
    order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin).call

    expect(order_creator).to be_success
    created_team_order = order_creator.team_order
    expect(created_team_order.customer_profile).to eq(team_admin)
    expect(created_team_order.name).to eq(team_order_params[:name])
    expect(created_team_order.delivery_at).to eq(team_order_params[:delivery_at])
    expect(created_team_order.delivery_suburb_id).to eq(team_order_params[:delivery_suburb_id])
    expect(created_team_order.delivery_address).to eq(team_order_params[:delivery_address])
  end

  it 'creates the new order and sets the whodunnit' do
    user = create(:user, :random)
    team_order_params_wtih_whodunnit = team_order_params.merge({ whodunnit_id: user.id })

    order_creator = TeamOrders::Create.new(team_order_params: team_order_params_wtih_whodunnit, team_admin: team_admin).call

    expect(order_creator).to be_success
    created_team_order = order_creator.team_order
    expect(created_team_order.whodunnit_id).to eq(user.id)
    expect(created_team_order.original_creator).to eq(user)
  end

  it 'sends an email to the team admin' do
    expect(TeamOrders::Emails::SendAdminNewOrderEmail).to receive(:new).with(team_order: anything)

    order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin).call
    expect(order_creator).to be_success
  end

  it 'logs a `New Order Created` event', event_logs: true do
    expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'new-team-order-created') # event object is created team order

    order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin).call
    expect(order_creator).to be_success
  end

  context 'with team order details' do
    let!(:team_order_detail_attributes) { { budget: rand(20), hide_budget: true, cutoff_option: TeamOrder::Detail::VALID_CUTOFF_OPTIONS.sample, attendee_pays: true } }
    let!(:team_order_params_with_details) { team_order_params.merge({ team_order_detail_attributes: team_order_detail_attributes }) }

    it 'creates a detail record with passed in team order detail params' do
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_details, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order
      team_order_detail = created_team_order.team_order_detail
      expect(team_order_detail).to be_present
      expect(team_order_detail.budget).to eq(team_order_detail_attributes[:budget])
      expect(team_order_detail.hide_budget).to eq(team_order_detail_attributes[:hide_budget])
      expect(team_order_detail.cutoff_option).to eq(team_order_detail_attributes[:cutoff_option])
      expect(team_order_detail.attendee_pays).to eq(team_order_detail_attributes[:attendee_pays])

      expect(created_team_order.team_order_budget).to eq(team_order_detail_attributes[:budget])
      expect(created_team_order.hide_budget).to eq(team_order_detail_attributes[:hide_budget])
      expect(created_team_order.cutoff_option).to eq(team_order_detail_attributes[:cutoff_option])
      expect(created_team_order.attendee_pays).to eq(team_order_detail_attributes[:attendee_pays])
    end

    it 'creates a detail record even if detail params are not passed' do
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order
      team_order_detail = created_team_order.team_order_detail
      expect(team_order_detail).to be_present
      expect(team_order_detail.cutoff_option).to eq('charge_to_minimum')
    end
  end

  context 'with purchase order details', purchase_orders: true do
    let!(:customer_purchase_order) { create(:customer_purchase_order, customer_profile: team_admin, po_number: SecureRandom.hex(7)) }

    it 'saves the purchase order based on passed in ID' do
      team_order_params_with_cpo = team_order_params.merge({ cpo_id: customer_purchase_order.id })
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_cpo, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order
      expect(created_team_order.cpo_id).to eq(customer_purchase_order.id)
      expect(created_team_order.po_number).to eq(customer_purchase_order.po_number)
    end

    it 'saves the purchase order based on passed in PO number' do
      team_order_params_with_cpo = team_order_params.merge({ cpo_id: customer_purchase_order.po_number })
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_cpo, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order
      expect(created_team_order.cpo_id).to eq(customer_purchase_order.id)
      expect(created_team_order.po_number).to eq(customer_purchase_order.po_number)
    end

    it 'creates and saves a new customer purchase order based on passed in PO number' do
      team_order_params_with_cpo = team_order_params.merge({ cpo_id: SecureRandom.hex(7) })
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_cpo, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order
      expect(created_team_order.customer_purchase_order).to be_present
      expect(created_team_order.customer_purchase_order.customer_profile).to eq(created_team_order.customer_profile)
      expect(created_team_order.customer_purchase_order.customer_profile).to eq(team_admin)
      expect(created_team_order.po_number).to eq(team_order_params_with_cpo[:cpo_id])
    end
  end

  context 'with suppliers' do
    let!(:supplier1) { create(:supplier_profile, :random) }
    let!(:supplier2) { create(:supplier_profile, :random) }
    let!(:delivery_date) { team_order_params[:delivery_at].strftime('%Y-%m-%d') }

    let!(:delivery_suppliers) do
      {
        delivery_date => {
          supplier1.id.to_s => [],
          supplier2.id.to_s => [],
        }
      }
    end

    it 'attaches suppliers to the team order' do
      team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order

      expect(created_team_order.order_suppliers.map(&:supplier_profile)).to include(supplier1, supplier2)
    end

    it 'sends a heads up email to the attached supplier(s)' do
      expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).with(team_order: anything, supplier: supplier1)
      expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).with(team_order: anything, supplier: supplier2)

      team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

      expect(order_creator).to be_success
    end

    context 'with menu sections' do
      let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1) }
      let!(:menu_section12) { create(:menu_section, :random, supplier_profile: supplier1) }
      let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2) }
      let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2) }

      let!(:delivery_suppliers) do
        {
          delivery_date => {
            supplier1.id.to_s => [menu_section11, menu_section12].map(&:id),
            supplier2.id.to_s => [menu_section22].map(&:id),
          }
        }
      end

      it 'attaches suppliers to the team order with selected menu sections' do
        team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
        order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

        expect(order_creator).to be_success
        created_team_order = order_creator.team_order

        team_order_suppliers = created_team_order.order_suppliers
        expect(team_order_suppliers.map(&:supplier_profile)).to include(supplier1, supplier2)

        supplier1_team_order_supplier = team_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier1 }
        expect(supplier1_team_order_supplier.selected_menu_sections).to be_present
        expect(supplier1_team_order_supplier.selected_menu_sections.map(&:to_i)).to include(menu_section11.id, menu_section12.id)

        supplier2_team_order_supplier = team_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier2 }
        expect(supplier2_team_order_supplier.selected_menu_sections).to be_present
        expect(supplier2_team_order_supplier.selected_menu_sections.map(&:to_i)).to include(menu_section22.id)
        expect(supplier2_team_order_supplier.selected_menu_sections.map(&:to_i)).to_not include(menu_section21.id)
      end

      it 'attaches suppliers to the team order but doesn\'t set the selected menu sections if not passed' do
        delivery_suppliers[delivery_date][supplier1.id.to_s] = [menu_section12].map(&:id)
        delivery_suppliers[delivery_date][supplier2.id.to_s] = []
        team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
        order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

        expect(order_creator).to be_success
        created_team_order = order_creator.team_order

        team_order_suppliers = created_team_order.order_suppliers
        expect(team_order_suppliers.map(&:supplier_profile)).to include(supplier1, supplier2)

        supplier1_team_order_supplier = team_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier1 }
        expect(supplier1_team_order_supplier.selected_menu_sections).to be_present
        expect(supplier1_team_order_supplier.selected_menu_sections.map(&:to_i)).to include(menu_section12.id)
        expect(supplier1_team_order_supplier.selected_menu_sections.map(&:to_i)).to_not include(menu_section11.id)

        supplier2_team_order_supplier = team_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier2 }
        expect(supplier2_team_order_supplier.selected_menu_sections).to_not be_present
      end
    end
  end

  context 'with team order attendees' do
    let!(:event_attendee1) { create(:event_attendee, :random, team_admin: team_admin) }
    let!(:event_attendee2) { create(:event_attendee, :random) }
    let!(:event_attendee3) { create(:event_attendee, :random, team_admin: team_admin) }

    before do
      invite_sender = delayed_invite_sender = double(TeamOrderAttendees::Emails::SendInviteEmail)
      allow(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).and_return(invite_sender)
      allow(invite_sender).to receive(:delay).and_return(delayed_invite_sender)
      allow(delayed_invite_sender).to receive(:call).and_return(true)
    end

    it 'attaches attendees to the team order' do
      team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee3].map(&:id) })
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order.reload

      expect(created_team_order.team_order_attendees.map(&:event_attendee)).to include(event_attendee1, event_attendee3)
    end

    it 'does not attach an event attendee not belonging to the team order admin' do
      team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee2].map(&:id) })
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order.reload

      expect(created_team_order.team_order_attendees.map(&:event_attendee)).to_not include(event_attendee2)
    end

    # also tested in AttachAttendee spec
    it 'sends an invitation email to each attached attendee' do
      event_attendees = [event_attendee1, event_attendee2, event_attendee3]

      event_attendees.each do |attendee|
        next if attendee == event_attendee2 # event_attendee2 is not attached

        expect(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).with(team_order_attendee: anything)
      end

      team_order_params_with_attendees = team_order_params.merge({ attendee_ids: event_attendees.map(&:id) })
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

      expect(order_creator).to be_success
    end
  end

  context 'with a passed in package id' do
    let!(:package_id) { SecureRandom.uuid }

    it 'creates a team order details with the passed in package_id' do
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin, package_id: package_id).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order
      team_order_detail = created_team_order.team_order_detail

      expect(team_order_detail.package_id).to eq(package_id)
    end

    it 'doesn\'t send the team admin an email if the created team order is a package order' do
      expect(TeamOrders::Emails::SendAdminNewOrderEmail).to_not receive(:new)
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin, package_id: package_id).call

      expect(order_creator).to be_success
    end
  end

  context 'no package order' do
    it 'creates a team order detail with no package_id' do
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin).call

      expect(order_creator).to be_success
      created_team_order = order_creator.team_order
      team_order_detail = created_team_order.team_order_detail

      expect(team_order_detail.package_id).to be_blank
    end

    it 'send the team admin an email even if the created team order is not the packaged order' do
      expect(TeamOrders::Emails::SendAdminNewOrderEmail).to receive(:new)
      order_creator = TeamOrders::Create.new(team_order_params: team_order_params, team_admin: team_admin).call

      expect(order_creator).to be_success
    end
  end
end
