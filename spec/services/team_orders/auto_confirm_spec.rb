require 'rails_helper'

RSpec.describe TeamOrders::AutoConfirm, type: :service, team_orders: true, orders: true do

  let!(:category) { create(:category, :random, group: 'catering-services') }
  let!(:supplier) { create(:supplier_profile, :random) }
  let!(:supplier_minimum) { create(:minimum, supplier_profile: supplier, category: category, lead_time: 25) }

  let!(:now) { Time.zone.parse('2020-07-22 13:00:00') } # Wednesday, 22 Jul 2020 13:00:00 AEST +10:00
  let!(:team_order) { create(:order, :new, order_variant: 'team_order', delivery_at: now + 1.day) }
  let!(:team_order_supplier) { create(:order_supplier, order: team_order, supplier_profile: supplier) }

  before do
    email_sender = delayed_email_sender = double(Orders::Emails::SendConfirmationCheckEmail)
    allow(Orders::Emails::SendConfirmationCheckEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    confirm_email_sender = delayed_confirm_email_sender = double(Customers::Emails::SendOrderConfirmationEmail)
    allow(Customers::Emails::SendOrderConfirmationEmail).to receive(:new).and_return(confirm_email_sender)
    allow(confirm_email_sender).to receive(:delay).and_return(delayed_confirm_email_sender)
    allow(delayed_confirm_email_sender).to receive(:call).and_return(true)
  end

  it 'auto confirms new/amended team orders past their cutoff and grace period' do
    auto_confirmer = TeamOrders::AutoConfirm.new(time: now).call

    expect(auto_confirmer).to be_success
    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders

    expect(auto_confirmed_orders.map(&:id)).to include(team_order.id)
    expect(auto_confirmed_orders.map(&:status).uniq).to match_array(['confirmed'])

    expect(team_order.reload.status).to eq('confirmed')
  end

  it 'does not auto confirms team orders past not past their cutoff and grace period' do
    supplier_minimum.update_column(:lead_time, [24, 23].sample)
    auto_confirmer = TeamOrders::AutoConfirm.new(time: now).call

    expect(auto_confirmer).to be_success
    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders

    expect(auto_confirmed_orders).to be_blank
    expect(team_order.reload.status).to_not eq('confirmed')
  end

  it 'does not auto confirms team orders past their cutoff and grace period but not new or amended' do
    team_order.update_column(:status, %w[pending cancelled paused].sample)
    auto_confirmer = TeamOrders::AutoConfirm.new(time: now).call

    expect(auto_confirmer).to be_success
    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders

    expect(auto_confirmed_orders).to be_blank
    expect(team_order.reload.status).to_not eq('confirmed')
  end

  it 'does not auto confirms non-team orders past' do
    team_order.update_column(:order_variant, %w[general event_order home_delivery].sample)
    auto_confirmer = TeamOrders::AutoConfirm.new(time: now).call

    expect(auto_confirmer).to be_success
    auto_confirmed_orders = auto_confirmer.auto_confirmed_orders

    expect(auto_confirmed_orders).to be_blank
    expect(team_order.reload.status).to_not eq('confirmed')
  end

  it 'sends an email to the admin about the auto order confirmation' do
    expect(Orders::Emails::SendConfirmationCheckEmail).to receive(:new)

    auto_confirmer = TeamOrders::AutoConfirm.new(time: now).call
    expect(auto_confirmer).to be_success
  end

  it 'does not send a confirmation email for the auto-confirmed orders', notifications: true do # from Orders::Confirm
    expect(Customers::Emails::SendOrderConfirmationEmail).to_not receive(:new).with(customer: team_order.customer_profile, order: team_order)

    auto_confirmer = TeamOrders::AutoConfirm.new(time: now).call
    expect(auto_confirmer).to be_success
  end
end
