require 'rails_helper'

RSpec.describe TeamOrders::UpsertLevels, type: :service, team_orders: true, team_order_levels: true do

  let!(:team_order_detail) { create(:team_order_detail, :random) }
  let!(:level_names) do
    3.times.map do |num|
      "Level #{num}"
    end.to_a
  end

  it 'creates the new levels for the given team order based on the names' do
    level_upserter = TeamOrders::UpsertLevels.new(team_order_detail: team_order_detail, names: level_names).call

    expect(level_upserter).to be_success
    created_levels = level_upserter.levels
    expect(created_levels.size).to eq(level_names.size)
    expect(created_levels.sample.team_order_detail).to eq(team_order_detail)
    expect(created_levels.sample).to be_a(TeamOrder::Level)
    expect(created_levels.map(&:name)).to include(*level_names)

    team_order_levels = team_order_detail.reload.levels
    expect(team_order_levels.map(&:name)).to include(*level_names)
  end

  it 'only creates levels with valid (non-blank) names' do
    level_name_with_blanks = level_names + ['', nil]
    level_upserter = TeamOrders::UpsertLevels.new(team_order_detail: team_order_detail, names: level_name_with_blanks).call

    expect(level_upserter).to be_success
    created_levels = level_upserter.levels
    expect(created_levels.size).to eq(level_names.size)
    expect(created_levels.map(&:name)).to include(*level_names)
  end

  it 'only creates uniq names' do
    duplicate_level_names = ['Level 1', 'level 1', 'Level 2']
    level_upserter = TeamOrders::UpsertLevels.new(team_order_detail: team_order_detail, names: duplicate_level_names).call

    expect(level_upserter).to be_success
    created_levels = level_upserter.levels
    expect(created_levels.size).to eq(2)
    expect(created_levels.map(&:name)).to include('Level 1', 'Level 2')
  end

  context 'with existing levels' do
    let!(:level1) { create(:team_order_level, :random, team_order_detail: team_order_detail) }
    let!(:level2) { create(:team_order_level, :random, team_order_detail: team_order_detail) }
    let!(:level3) { create(:team_order_level, :random, team_order_detail: team_order_detail) }

    let!(:level_names) do
      3.times.map do |num|
        "Level #{num}"
      end.to_a
    end

    it 'creates only new levels' do
      level_upserter = TeamOrders::UpsertLevels.new(team_order_detail: team_order_detail, names: (level_names + [level1, level2].map(&:name))).call

      updated_levels = level_upserter.levels
      expect(updated_levels.map(&:name)).to include(*level_names)
      expect(updated_levels.map(&:name)).to_not include(*[level1, level2].map(&:name)) # does not pass

      team_order_levels = team_order_detail.reload.levels
      expect(team_order_levels.size).to eq(5) # 3 new ones + level1 and level2
      expect(team_order_levels.map(&:name)).to include(*level_names)
      expect(team_order_levels.map(&:id)).to include(*[level1, level2].map(&:id))
      expect(team_order_levels.map(&:name)).to include(*[level1, level2].map(&:name))
    end

    it 'doesn\'t create levels with existing names (case-insensitive)' do
      randomized_level2_name = level2.name.gsub(/./){|s| s.send(%i[upcase downcase].sample) }
      duplicate_level_names = level_names + [randomized_level2_name, level3.name]
      level_upserter = TeamOrders::UpsertLevels.new(team_order_detail: team_order_detail, names: duplicate_level_names).call

      expect(level_upserter).to be_success
      team_order_levels = team_order_detail.reload.levels
      expect(team_order_levels.size).to eq(5) # 3 new ones + level2 and level3
      expect(team_order_levels.map(&:name)).to include(*level_names)
      expect(team_order_levels.map(&:id)).to include(*[level2, level3].map(&:id))
      expect(team_order_levels.map(&:name)).to include(*[level2, level3].map(&:name))
    end

    it 'deletes levels which don\'t match any passed names' do
      level_upserter = TeamOrders::UpsertLevels.new(team_order_detail: team_order_detail, names: (level_names + [level2.name])).call

      expect(level_upserter).to be_success
      team_order_levels = team_order_detail.reload.levels
      expect(team_order_levels.size).to eq(4) # 3 new ones + level 2
      expect(team_order_levels.map(&:id)).to_not include(level1.id, level3.id)
      expect(team_order_levels.map(&:name)).to_not include(level1.name, level3.name)
      expect{ level1.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ level3.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'removes all existing levels if passed in level names is empty' do
      level_upserter = TeamOrders::UpsertLevels.new(team_order_detail: team_order_detail, names: [[], nil].sample).call

      expect(level_upserter).to be_success
      expect(team_order_detail.reload.levels).to be_blank
      expect{ level1.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ level2.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ level3.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end

end
