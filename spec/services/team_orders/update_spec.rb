require 'rails_helper'

RSpec.describe TeamOrders::Update, type: :service, team_orders: true, orders: true do

  let!(:team_admin) { create(:customer_profile, :random) }
  let!(:suburb) { create(:suburb, :random) }
  let!(:team_order) { create(:order, :draft, customer_profile: team_admin, order_variant: 'team_order', status: 'pending') }
  let!(:team_order_params) { { name: Faker::Name.name, delivery_at: Time.zone.now + 10.days, delivery_suburb_id: suburb.id, delivery_address: Faker::Address.street_address } }

  before do
    email_sender = delayed_email_sender = double(Suppliers::Emails::SendTeamOrderHeadsUpEmail)
    allow(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock attendee invite email
    invite_sender = delayed_invite_sender = double(TeamOrderAttendees::Emails::SendInviteEmail)
    allow(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).and_return(invite_sender)
    allow(invite_sender).to receive(:delay).and_return(delayed_invite_sender)
    allow(delayed_invite_sender).to receive(:call).and_return(true)

    detached_sender = delayed_detached_sender = double(TeamOrderAttendees::Emails::SendInviteEmail)
    allow(TeamOrderAttendees::Emails::SendRemovedFromOrderEmail).to receive(:new).and_return(detached_sender)
    allow(detached_sender).to receive(:delay).and_return(delayed_detached_sender)
    allow(delayed_detached_sender).to receive(:call).and_return(true)
  end

  it 'updates the order with passed in params' do
    order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params, team_admin: team_admin).call

    expect(order_updater).to be_success
    updated_team_order = order_updater.team_order
    expect(updated_team_order.id).to eq(team_order.id)
    expect(updated_team_order.name).to eq(team_order_params[:name])
    expect(updated_team_order.delivery_at).to eq(team_order_params[:delivery_at])
    expect(updated_team_order.delivery_suburb_id).to eq(team_order_params[:delivery_suburb_id])
    expect(updated_team_order.delivery_address).to eq(team_order_params[:delivery_address])
  end

  context 'errors' do
    it 'cannot update a missing team order' do
      order_updater = TeamOrders::Update.new(team_order: nil, team_order_params: team_order_params, team_admin: team_admin).call

      expect(order_updater).to_not be_success
      expect(order_updater.errors).to include('Cannot update a missing team order')
    end

    it 'cannot update an order which is not a team order' do
      team_order.update_column(:order_variant, 'general')
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params, team_admin: team_admin).call

      expect(order_updater).to_not be_success
      expect(order_updater.errors).to include('This order is not a team order')
    end

    it 'cannot update a team order if it does not belong to the passed in team admin' do
      another_team_admin = create(:customer_profile, :random)
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params, team_admin: another_team_admin).call

      expect(order_updater).to_not be_success
      expect(order_updater.errors).to include('You don\'t have access to this team order')
    end
  end

  context 'with team details' do
    let!(:team_order_detail) { create(:team_order_detail, :random, order: team_order) }
    let!(:team_order_detail_attributes) { { budget: rand(20), hide_budget: true, cutoff_option: TeamOrder::Detail::VALID_CUTOFF_OPTIONS.sample, attendee_pays: true }}
    let!(:team_order_params_with_details) { team_order_params.merge({ team_order_detail_attributes: team_order_detail_attributes }) }

    it 'updates the details with passed in detail params' do
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_details, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      team_order_detail = updated_team_order.team_order_detail
      expect(team_order_detail).to be_present
      expect(team_order_detail.id).to eq(team_order_detail.id)
      expect(team_order_detail.budget).to eq(team_order_detail_attributes[:budget])
      expect(team_order_detail.hide_budget).to eq(team_order_detail_attributes[:hide_budget])
      expect(team_order_detail.cutoff_option).to eq(team_order_detail_attributes[:cutoff_option])
      expect(team_order_detail.attendee_pays).to eq(team_order_detail_attributes[:attendee_pays])

      expect(updated_team_order.team_order_budget).to eq(team_order_detail_attributes[:budget])
      expect(updated_team_order.hide_budget).to eq(team_order_detail_attributes[:hide_budget])
      expect(updated_team_order.cutoff_option).to eq(team_order_detail_attributes[:cutoff_option])
      expect(updated_team_order.attendee_pays).to eq(team_order_detail_attributes[:attendee_pays])
    end

    it 'creates a new details record if missing' do
      team_order_detail.destroy

      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_details, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      team_order_detail = updated_team_order.team_order_detail
      expect(team_order_detail).to be_present
      expect(team_order_detail.budget).to eq(team_order_detail_attributes[:budget])
      expect(team_order_detail.hide_budget).to eq(team_order_detail_attributes[:hide_budget])
      expect(team_order_detail.cutoff_option).to eq(team_order_detail_attributes[:cutoff_option])
      expect(team_order_detail.attendee_pays).to eq(team_order_detail_attributes[:attendee_pays])
    end

    it 'creates a new details record if missing with default params' do
      team_order_detail.destroy

      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      team_order_detail = updated_team_order.team_order_detail
      expect(team_order_detail).to be_present
      expect(team_order_detail.cutoff_option).to eq('charge_to_minimum')
      expect(team_order_detail.attendee_pays).to be_falsey
      expect(team_order_detail.hide_budget).to be_falsey
    end
  end

  context 'with purchase order details', purchase_orders: true do
    let!(:customer_purchase_order) { create(:customer_purchase_order, customer_profile: team_admin, po_number: SecureRandom.hex(7)) }

    it 'saves the purchase order based on passed in ID' do
      team_order_params_with_cpo = team_order_params.merge({ cpo_id: customer_purchase_order.id })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_cpo, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      expect(updated_team_order.cpo_id).to eq(customer_purchase_order.id)
      expect(updated_team_order.po_number).to eq(customer_purchase_order.po_number)
    end

    it 'saves the purchase order based on passed in PO number' do
      team_order_params_with_cpo = team_order_params.merge({ cpo_id: customer_purchase_order.po_number })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_cpo, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      expect(updated_team_order.cpo_id).to eq(customer_purchase_order.id)
      expect(updated_team_order.po_number).to eq(customer_purchase_order.po_number)
    end

    it 'creates and saves a new customer purchase order based on passed in PO number' do
      team_order_params_with_cpo = team_order_params.merge({ cpo_id: SecureRandom.hex(7) })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_cpo, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      expect(updated_team_order.customer_purchase_order).to be_present
      expect(updated_team_order.customer_purchase_order.customer_profile).to eq(updated_team_order.customer_profile)
      expect(updated_team_order.customer_purchase_order.customer_profile).to eq(team_admin)
      expect(updated_team_order.po_number).to eq(team_order_params_with_cpo[:cpo_id])
    end
  end

  context 'with suppliers' do
    let!(:supplier1) { create(:supplier_profile, :random) }
    let!(:supplier2) { create(:supplier_profile, :random) }
    let!(:supplier3) { create(:supplier_profile, :random) }

    before do
      team_order.team_supplier_profiles << supplier1
      team_order.team_supplier_profiles << supplier2
      team_order.reload
    end

    let!(:delivery_suppliers) do
      {
        team_order_params[:delivery_at].strftime('%Y-%m-%d') => {
          supplier1.id.to_s => [],
          supplier3.id.to_s => [],
        }
      }
    end

    it 'attaches passed in new supplier(s) to the team order' do
      team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order

      expect(updated_team_order.order_suppliers.map(&:supplier_profile)).to include(supplier1, supplier3)
    end

    it 'detaches existing suppliers that are not passed in' do
      expect(team_order.reload.team_supplier_profiles).to include(supplier1, supplier2)

      team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order

      expect(updated_team_order.team_supplier_profiles).to_not include(supplier2)
    end

    it 'sends a heads up email to a newly attached supplier(s)' do
      expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to receive(:new) # email sent to supplier3

      team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

      expect(order_updater).to be_success
    end

    it 'does not send a heads up email if no new suppliers are added' do
      expect(Suppliers::Emails::SendTeamOrderHeadsUpEmail).to_not receive(:new)

      delivery_suppliers = {
        team_order_params[:delivery_at].strftime('%Y-%m-%d') => {
          supplier2.id.to_s => [],
        }
      }

      team_order_params_with_no_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_no_suppliers, team_admin: team_admin).call

      expect(order_updater).to be_success
    end

    context 'with menu sections' do
      let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1) }
      let!(:menu_section12) { create(:menu_section, :random, supplier_profile: supplier1) }
      let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2) }
      let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2) }
      let!(:menu_section31) { create(:menu_section, :random, supplier_profile: supplier3) }
      let!(:menu_section32) { create(:menu_section, :random, supplier_profile: supplier3) }

      it 'attaches new suppliers to the team order with selected menu sections' do
        delivery_suppliers = {
          team_order_params[:delivery_at].strftime('%Y-%m-%d') => {
            supplier1.id.to_s => [menu_section11, menu_section12].map(&:id),
            supplier3.id.to_s => [menu_section31].map(&:id),
          }
        }

        team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
        order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

        expect(order_updater).to be_success
        updated_team_order = order_updater.team_order

        team_order_suppliers = updated_team_order.order_suppliers
        expect(team_order_suppliers.map(&:supplier_profile)).to include(supplier1, supplier3)

        supplier3_team_order_supplier = team_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier3 }
        expect(supplier3_team_order_supplier.selected_menu_sections).to be_present
        expect(supplier3_team_order_supplier.selected_menu_sections.map(&:to_i)).to include(menu_section31.id)
        expect(supplier3_team_order_supplier.selected_menu_sections.map(&:to_i)).to_not include(menu_section32.id)
      end

      context 'with existing supplier selected menu sections' do
        before do
          supplier1_team_order_supplier = team_order.order_suppliers.where(supplier_profile: supplier1).first
          supplier1_team_order_supplier.update_column(:selected_menu_sections, [menu_section11, menu_section12].map(&:id))
          expect(supplier1_team_order_supplier.selected_menu_sections.map(&:to_i)).to include(menu_section11.id, menu_section12.id)
        end

        it 'updates the selected menu sections config of already attached suppliers' do
          delivery_suppliers = {
            team_order_params[:delivery_at].strftime('%Y-%m-%d') => {
              supplier1.id.to_s => [menu_section12].map(&:id),
              supplier3.id.to_s => [menu_section31].map(&:id),
            }
          }

          team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
          order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

          expect(order_updater).to be_success
          updated_team_order = order_updater.team_order

          team_order_suppliers = updated_team_order.order_suppliers
          expect(team_order_suppliers.map(&:supplier_profile)).to include(supplier1)

          supplier1_team_order_supplier = team_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier1 }
          expect(supplier1_team_order_supplier.selected_menu_sections).to be_present
          expect(supplier1_team_order_supplier.selected_menu_sections.map(&:to_i)).to include(menu_section12.id)
          expect(supplier1_team_order_supplier.selected_menu_sections.map(&:to_i)).to_not include(menu_section11.id)
        end

        it 'clears out the selected menu sections config of already attached suppliers, if menu sections are not passed' do
          delivery_suppliers = {
            team_order_params[:delivery_at].strftime('%Y-%m-%d') => {
              supplier1.id.to_s => [],
              supplier3.id.to_s => [menu_section31].map(&:id),
            }
          }

          team_order_params_with_suppliers = team_order_params.merge({ delivery_suppliers: delivery_suppliers })
          order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_suppliers, team_admin: team_admin).call

          expect(order_updater).to be_success
          updated_team_order = order_updater.team_order

          team_order_suppliers = updated_team_order.order_suppliers
          expect(team_order_suppliers.map(&:supplier_profile)).to include(supplier1)

          supplier1_team_order_supplier = team_order_suppliers.detect{|order_supplier| order_supplier.supplier_profile == supplier1 }
          expect(supplier1_team_order_supplier.selected_menu_sections).to be_blank
        end
      end
    end
  end

  context 'with team order attendees' do
    let!(:event_attendee1) { create(:event_attendee, :random, team_admin: team_admin) }
    let!(:team_order_attendees1) { create(:team_order_attendee, :random, event_attendee: event_attendee1, order: team_order, status: 'invited') }

    let!(:event_attendee2) { create(:event_attendee, :random) }

    let!(:event_attendee3) { create(:event_attendee, :random, team_admin: team_admin) }
    let!(:team_order_attendees3) { create(:team_order_attendee, :random, event_attendee: event_attendee3, order: team_order, status: 'invited') }

    let!(:event_attendee4) { create(:event_attendee, :random, team_admin: team_admin) }

    it 'attaches attendees to the team order' do
      team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id) })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order.reload
      invited_team_order_attendees = updated_team_order.team_order_attendees.where(status: 'invited')
      expect(invited_team_order_attendees.map(&:event_attendee)).to include(event_attendee1, event_attendee4)
    end

    it 'does not attach an event attendee not belonging to the team order admin' do
      team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee2].map(&:id) })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order.reload

      expect(updated_team_order.team_order_attendees.map(&:event_attendee)).to_not include(event_attendee2)
    end

    it 'sends invite emails to newly attached attendees' do
      expect(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).with(team_order_attendee: anything) # only for event_attendee4

      team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id) })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call
      expect(order_updater).to be_success
    end

    it 'detaches attendees from the team order not passed in' do
      team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id) })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order.reload
      cancelled_team_order_attendees = updated_team_order.team_order_attendees.where(status: 'cancelled')
      expect(cancelled_team_order_attendees).to include(team_order_attendees3)
      expect(team_order_attendees3.reload.status).to eq('cancelled')

      invited_team_order_attendees = updated_team_order.team_order_attendees.where(status: 'invited')
      expect(invited_team_order_attendees.map(&:event_attendee)).to_not include(event_attendee3)
    end

    it 'sends cancellation emails to dettached attendees' do
      expect(TeamOrderAttendees::Emails::SendRemovedFromOrderEmail).to receive(:new).with(team_order_attendee: team_order_attendees3)

      team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id) })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

      expect(order_updater).to be_success
    end
  end

  context 'as a package order' do
    let!(:package_id) { SecureRandom.hex(7) }
    let!(:team_order2) { create(:order, :draft, customer_profile: team_admin, name: 'team_order2', order_variant: 'team_order', status: 'pending', delivery_at: team_order.delivery_at + 2.days) }
    let!(:team_order3) { create(:order, :draft, customer_profile: team_admin, name: 'team_order3', order_variant: 'team_order', status: 'pending', delivery_at: team_order.delivery_at + 3.days) }
    let!(:team_order4) { create(:order, :draft, customer_profile: team_admin, name: 'team_order4', order_variant: 'team_order', status: 'pending', delivery_at: team_order.delivery_at + 4.days) }

    let!(:team_order_detail) { create(:team_order_detail, :random, order: team_order, package_id: package_id) }
    let!(:team_order_detail2) { create(:team_order_detail, :random, order: team_order2, package_id: package_id) }
    let!(:team_order_detail3) { create(:team_order_detail, :random, order: team_order3, package_id: package_id) }
    let!(:team_order_detail4) { create(:team_order_detail, :random, order: team_order4, package_id: package_id) }

    before do
      package_invite_sender = delayed_package_invite_sender = double(TeamOrderAttendees::Emails::SendPackageInviteEmail)
      allow(TeamOrderAttendees::Emails::SendPackageInviteEmail).to receive(:new).and_return(package_invite_sender)
      allow(package_invite_sender).to receive(:delay).and_return(delayed_package_invite_sender)
      allow(delayed_package_invite_sender).to receive(:call).and_return(true)
    end

    it 'updates the order and its linked orders with passed in params' do
      team_order_params_with_mode = team_order_params.merge({ mode: 'linked' })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_mode, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      expect(updated_team_order.id).to eq(team_order.id)
      expect(updated_team_order.name).to eq(team_order_params[:name])
      expect(updated_team_order.delivery_suburb_id).to eq(team_order_params[:delivery_suburb_id])
      expect(updated_team_order.delivery_address).to eq(team_order_params[:delivery_address])

      future_orders = [team_order2, team_order3, team_order4]
      future_orders.each do |future_order|
        future_order.reload
        expect(future_order.name).to eq(team_order_params[:name])
        expect(future_order.delivery_suburb_id).to eq(team_order_params[:delivery_suburb_id])
        expect(future_order.delivery_address).to eq(team_order_params[:delivery_address])
      end
    end

    it 'does not update the delivery at of linked orders only the initial order' do
      team_order_params_with_mode = team_order_params.merge({ mode: 'linked' })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_mode, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      expect(updated_team_order.id).to eq(team_order.id)
      expect(updated_team_order.delivery_at).to eq(team_order_params[:delivery_at])

      future_orders = [team_order2, team_order3, team_order4]
      future_orders.each do |future_order|
        future_order.reload
        expect(future_order.delivery_at).to_not eq(team_order_params[:delivery_at])
      end
    end

    it 'does not update an order which is before the current time' do
      team_order2.update_column(:delivery_at, Time.zone.now - 1.day)
      team_order_params_with_mode = team_order_params.merge({ mode: 'linked' })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_mode, team_admin: team_admin).call

      expect(order_updater).to be_success
      team_order2.reload
      expect(team_order2.package_id).to eq(team_order.package_id)
      expect(team_order2.name).to_not eq(team_order_params[:name])
      expect(team_order2.delivery_at).to_not eq(team_order_params[:delivery_at])
      expect(team_order2.delivery_suburb_id).to_not eq(team_order_params[:delivery_suburb_id])
      expect(team_order2.delivery_address).to_not eq(team_order_params[:delivery_address])
    end

    it 'does not update an in-active future order' do
      team_order3.update_column(:status, %w[confirmed delivered cancelled].sample)
      team_order_params_with_mode = team_order_params.merge({ mode: 'linked' })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_mode, team_admin: team_admin).call

      expect(order_updater).to be_success
      team_order3.reload
      expect(team_order3.package_id).to eq(team_order.package_id)
      expect(team_order3.name).to_not eq(team_order_params[:name])
      expect(team_order3.delivery_at).to_not eq(team_order_params[:delivery_at])
      expect(team_order3.delivery_suburb_id).to_not eq(team_order_params[:delivery_suburb_id])
      expect(team_order3.delivery_address).to_not eq(team_order_params[:delivery_address])
    end

    it 'only updates the current team order and doesn\'t update its linked orders if the passed in mode is not `linked`' do
      team_order_params_with_mode = team_order_params.merge({ mode: [nil, '', 'not-linked', 'one-off'].sample })
      order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_mode, team_admin: team_admin).call

      expect(order_updater).to be_success
      updated_team_order = order_updater.team_order
      expect(updated_team_order.id).to eq(team_order.id)
      expect(updated_team_order.name).to eq(team_order_params[:name])
      expect(updated_team_order.delivery_at).to eq(team_order_params[:delivery_at])
      expect(updated_team_order.delivery_suburb_id).to eq(team_order_params[:delivery_suburb_id])
      expect(updated_team_order.delivery_address).to eq(team_order_params[:delivery_address])

      future_orders = [team_order2, team_order3, team_order4]
      future_orders.each do |future_order|
        future_order.reload
        expect(future_order.name).to_not eq(team_order_params[:name])
        expect(future_order.delivery_at).to_not eq(team_order_params[:delivery_at])
        expect(future_order.delivery_suburb_id).to_not eq(team_order_params[:delivery_suburb_id])
        expect(future_order.delivery_address).to_not eq(team_order_params[:delivery_address])
      end
    end

    context 'with team order attendees' do
      let!(:event_attendee1) { create(:event_attendee, :random, team_admin: team_admin) }
      let!(:team_order_attendees1) { create(:team_order_attendee, :random, event_attendee: event_attendee1, order: team_order, status: 'invited') }

      let!(:event_attendee2) { create(:event_attendee, :random) }
      let!(:event_attendee3) { create(:event_attendee, :random, team_admin: team_admin) }
      let!(:team_order_attendees3) { create(:team_order_attendee, :random, event_attendee: event_attendee3, order: team_order, status: 'invited') }
      let!(:event_attendee4) { create(:event_attendee, :random, team_admin: team_admin) }

      it 'updates the team order attendees of the linked orders' do
        team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id), mode: 'linked' })
        order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

        expect(order_updater).to be_success
        updated_team_order = order_updater.team_order.reload
        invited_team_order_attendees = updated_team_order.team_order_attendees.where(status: 'invited')
        expect(invited_team_order_attendees.map(&:event_attendee)).to include(event_attendee1, event_attendee4)
        expect(invited_team_order_attendees.map(&:event_attendee)).to_not include(event_attendee3)

        future_orders = [team_order2, team_order3, team_order4]
        future_orders.each do |future_order|
          invited_team_order_attendees = future_order.reload.team_order_attendees.where(status: 'invited')
          expect(invited_team_order_attendees.map(&:event_attendee)).to include(event_attendee1, event_attendee4)
          expect(invited_team_order_attendees.map(&:event_attendee)).to_not include(event_attendee3)
        end
      end

      it 'does not update team order attendees of a recurring team order (as attendees are managed using magic link)' do
        [team_order, team_order2, team_order3, team_order4].each do |order|
          order.update_column(:order_variant, 'recurring_team_order')
        end

        team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id), mode: 'linked' })
        order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

        expect(order_updater).to be_success
        updated_team_order = order_updater.team_order.reload
        invited_team_order_attendees = updated_team_order.team_order_attendees.where(status: 'invited')
        expect(invited_team_order_attendees.map(&:event_attendee)).to_not include(event_attendee4)
        expect(invited_team_order_attendees.map(&:event_attendee)).to include(event_attendee1, event_attendee3) # same as before

        future_orders = [team_order2, team_order3, team_order4]
        future_orders.each do |future_order|
          invited_team_order_attendees = future_order.reload.team_order_attendees.where(status: 'invited')
          expect(invited_team_order_attendees.map(&:event_attendee)).to_not include(event_attendee4)
          expect(invited_team_order_attendees.map(&:event_attendee)).to be_blank # same as before
        end
      end

      context 'attendee invitation emails' do
        it 'sends a new attendee package invite email if updating a package team order along with linked orders' do
          expect(TeamOrderAttendees::Emails::SendPackageInviteEmail).to receive(:new).with(package_order_attendee: anything, package_orders: anything) # only sent for event_attendee4

          team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id), mode: 'linked' })
          order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

          expect(order_updater).to be_success
        end

        it 'does not send a new attendee invite email if updating a package team order along with linked orders' do
          expect(TeamOrderAttendees::Emails::SendInviteEmail).to_not receive(:new).with(team_order_attendee: anything)

          team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id), mode: 'linked' })
          order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

          expect(order_updater).to be_success
        end

        it 'sends a new attendee invite email if updating a package team order as one-off' do
          expect(TeamOrderAttendees::Emails::SendInviteEmail).to receive(:new).with(team_order_attendee: anything) # only for event_attendee4 for team order
          expect(TeamOrderAttendees::Emails::SendInviteEmail).to_not receive(:new).with(team_order_attendee: anything) # only sent once

          team_order_params_with_attendees = team_order_params.merge({ attendee_ids: [event_attendee1, event_attendee4].map(&:id), mode: ['one-off', nil].sample })
          order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_attendees, team_admin: team_admin).call

          expect(order_updater).to be_success
        end
      end
    end

    context 'with supplier config' do
      let!(:supplier1) { create(:supplier_profile, :random) }
      let!(:supplier2) { create(:supplier_profile, :random) }
      let!(:supplier3) { create(:supplier_profile, :random) }

      let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1) }
      let!(:menu_section12) { create(:menu_section, :random, supplier_profile: supplier1) }
      let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2) }
      let!(:menu_section22) { create(:menu_section, :random, supplier_profile: supplier2) }
      let!(:menu_section31) { create(:menu_section, :random, supplier_profile: supplier3) }
      let!(:menu_section32) { create(:menu_section, :random, supplier_profile: supplier3) }

      before do
        order_supplier = team_order.order_suppliers.where(supplier_profile: supplier1).first_or_create
        order_supplier.update_attribute(:selected_menu_sections, [menu_section11, menu_section12].map(&:id))

        order_supplier = team_order2.order_suppliers.where(supplier_profile: supplier2).first_or_create
        order_supplier.update_attribute(:selected_menu_sections, [menu_section22].map(&:id))

        order_supplier = team_order3.order_suppliers.where(supplier_profile: supplier3).first_or_create
        order_supplier.update_attribute(:selected_menu_sections, [menu_section31, menu_section32].map(&:id))

        order_supplier = team_order4.order_suppliers.where(supplier_profile: supplier1).first_or_create
        order_supplier.update_attribute(:selected_menu_sections, [])

        [team_order, team_order2, team_order3, team_order4].each(&:reload)
      end

      let!(:delivery_suppliers) do
        {
          team_order_params[:delivery_at].strftime('%Y-%m-%d') => {
            supplier1.id.to_s => [menu_section11].map(&:id),
          }
        }
      end

      it 'only updates the supplier config of the updated order and not linked ones' do
        team_order_params_with_mode = team_order_params.merge({ mode: 'linked', delivery_suppliers: delivery_suppliers })
        order_updater = TeamOrders::Update.new(team_order: team_order, team_order_params: team_order_params_with_mode, team_admin: team_admin).call

        expect(order_updater).to be_success
        team_order_supplier = team_order.order_suppliers.first
        expect(team_order_supplier.supplier_profile).to eq(supplier1)
        expect(team_order_supplier.selected_menu_sections).to match_array([menu_section11].map(&:id)) # changed

        team_order2_supplier = team_order2.order_suppliers.first
        expect(team_order2_supplier.supplier_profile).to eq(supplier2)
        expect(team_order2_supplier.selected_menu_sections).to match_array([menu_section22].map(&:id)) # no change

        team_order3_supplier = team_order3.order_suppliers.first
        expect(team_order3_supplier.supplier_profile).to eq(supplier3)
        expect(team_order3_supplier.selected_menu_sections).to match_array([menu_section31, menu_section32].map(&:id)) # no change

        team_order4_supplier = team_order4.order_suppliers.first
        expect(team_order4_supplier.supplier_profile).to eq(supplier1)
        expect(team_order4_supplier.selected_menu_sections).to be_blank # no change
      end
    end

  end
end
