require 'rails_helper'

RSpec.describe TeamOrders::ListPotentialFutureDeliveryOrders, type: :service, team_orders: true do
  let!(:beginning_of_week) { Time.zone.now.beginning_of_week }

  let!(:is_recurring_team_order) { true } # [true, false].sample }
  let!(:order_variant) { (is_recurring_team_order ? 'team_order' : 'recurring_team_order') }

  let!(:week_starting) { Time.zone.now.beginning_of_week }
  let!(:next_week) { 1.week }
  let!(:package_id) { SecureRandom.hex(7) }

  let!(:team_order1) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 1.week + 2.days + 3.hours) }
  let!(:team_order2) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 1.week + 3.days + 1.hours) }
  let!(:team_order3) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 1.week + 4.days + 2.hours) }

  let!(:team_order1_supplier) { create(:order_supplier, :random, order: team_order1) }
  let!(:team_order2_supplier) { create(:order_supplier, :random, order: team_order2) }
  let!(:team_order3_supplier) { create(:order_supplier, :random, order: team_order3) }

  let!(:package_order) { [team_order1, team_order2, team_order3].sample }

  it 'lists package team orders in the previous week of the passed in starting week' do
    future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

    expect(future_delivery_orders.map(&:team_order)).to match_array([team_order1, team_order2, team_order3])
  end

  it 'lists package team order suppliers in the previous week of the passed in starting week' do
    future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

    expect(future_delivery_orders.map(&:order_suppliers)).to match_array([team_order1.order_suppliers, team_order2.order_suppliers, team_order3.order_suppliers])
  end

  it 'list the potential delivery dates for the future week' do
    future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

    expect_delivery_dates = [team_order1, team_order2, team_order3].map do |order|
      order.delivery_at + next_week
    end
    expect(future_delivery_orders.map(&:delivery_at)).to match_array(expect_delivery_dates)
  end

  it 'does not list dates for team orders not in the package' do
    non_package_order = ([team_order1, team_order2, team_order3] - [package_order]).sample
    non_package_order.team_order_detail.update_column(:package_id, 'random package id')
    future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

    expect(future_delivery_orders.map(&:team_order)).to_not include(non_package_order)
    expect(future_delivery_orders.map(&:delivery_at)).to_not include(non_package_order.delivery_at + next_week)
  end

  it 'returns blank dates for a non-package team order' do
    package_order.team_order_detail.update_column(:package_id, nil)
    future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call
    expect(future_delivery_orders).to be_blank
  end

  context 'with more than 1 week of previous orders' do
    let!(:team_order51) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 5.weeks + 2.days + 3.hours) }
    let!(:team_order52) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 5.weeks + 3.days + 1.hours) }
    let!(:team_order53) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 5.weeks + 4.days + 2.hours) }

    let!(:team_order51_supplier) { create(:order_supplier, :random, order: team_order51) }
    let!(:team_order52_supplier) { create(:order_supplier, :random, order: team_order52) }
    let!(:team_order53_supplier) { create(:order_supplier, :random, order: team_order53) }

    let!(:team_order41) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 4.weeks + 2.days + 3.hours) }
    let!(:team_order42) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 4.weeks + 3.days + 1.hours) }
    let!(:team_order43) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 4.weeks + 4.days + 2.hours) }

    let!(:team_order41_supplier) { create(:order_supplier, :random, order: team_order41) }
    let!(:team_order42_supplier) { create(:order_supplier, :random, order: team_order42) }
    let!(:team_order43_supplier) { create(:order_supplier, :random, order: team_order43) }

    let!(:team_order31) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 3.weeks + 2.days + 3.hours) }
    let!(:team_order32) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 3.weeks + 3.days + 1.hours) }
    let!(:team_order33) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 3.weeks + 4.days + 2.hours) }

    let!(:team_order31_supplier) { create(:order_supplier, :random, order: team_order31) }
    let!(:team_order32_supplier) { create(:order_supplier, :random, order: team_order32) }
    let!(:team_order33_supplier) { create(:order_supplier, :random, order: team_order33) }

    let!(:team_order21) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 2.weeks + 2.days + 3.hours) }
    let!(:team_order22) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 2.weeks + 3.days + 1.hours) }
    let!(:team_order23) { create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 2.weeks + 4.days + 2.hours) }

    let!(:team_order21_supplier) { create(:order_supplier, :random, order: team_order21) }
    let!(:team_order22_supplier) { create(:order_supplier, :random, order: team_order22) }
    let!(:team_order23_supplier) { create(:order_supplier, :random, order: team_order23) }

    it 'lists package team orders of last week but the order suppliers of at least 5 weeks old previous to the passed in starting week if present' do
      future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

      expect(future_delivery_orders.map(&:team_order)).to match_array([team_order1, team_order2, team_order3])
      expect(future_delivery_orders.map(&:order_suppliers)).to_not include(*[team_order1, team_order2, team_order3].map(&:order_suppliers))
      expect(future_delivery_orders.map(&:order_suppliers)).to match_array([team_order51, team_order52, team_order53].map(&:order_suppliers))
    end

    it 'lists 4 week old package team orders, if there are no 5 weeks old orders' do
      [team_order51, team_order52, team_order53].each(&:destroy) # destory 5 week old orders

      future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

      expect(future_delivery_orders.map(&:team_order)).to match_array([team_order1, team_order2, team_order3])
      expect(future_delivery_orders.map(&:order_suppliers)).to_not include(*[team_order1, team_order2, team_order3].map(&:order_suppliers))
      expect(future_delivery_orders.map(&:order_suppliers)).to match_array([team_order41, team_order42, team_order43].map(&:order_suppliers))
    end

    context 'last week order count check' do
      it 'lists 4 week old package team orders, if 5 weeks old orders count (2) is less than last week orders count (3)' do
        [team_order51, team_order52, team_order53].sample.destroy # destory at-least one 5 week old order

        future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

        expect(future_delivery_orders.map(&:team_order)).to match_array([team_order1, team_order2, team_order3])
        expect(future_delivery_orders.map(&:order_suppliers)).to_not include(*[team_order1, team_order2, team_order3].map(&:order_suppliers))
        expect(future_delivery_orders.map(&:order_suppliers)).to match_array([team_order41, team_order42, team_order43].map(&:order_suppliers))
      end

      it 'lists 5 week old package team orders, if 5 weeks old orders count (4) is greater than last week orders count (3)' do
        team_order54 = create(:order, :package_team_order, order_variant: order_variant, package_id: package_id, delivery_at: week_starting - 5.weeks + 1.days + 4.hours)

        future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

        expect(future_delivery_orders.map(&:team_order)).to match_array([team_order1, team_order2, team_order3, team_order54])
        expect(future_delivery_orders.map(&:order_suppliers)).to_not include(*[team_order1, team_order2, team_order3].map(&:order_suppliers))
        expect(future_delivery_orders.map(&:order_suppliers)).to match_array([team_order51, team_order52, team_order53, team_order54].map(&:order_suppliers))
      end
    end

    it 'lists 3 week old package team orders, if there are orders older than 4 weeks (inclusive)' do
      [team_order51, team_order52, team_order53, team_order41, team_order42, team_order43].each(&:destroy) # destory 5 and 4 week old orders

      future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

      expect(future_delivery_orders.map(&:team_order)).to match_array([team_order1, team_order2, team_order3])
      expect(future_delivery_orders.map(&:order_suppliers)).to_not include(*[team_order1, team_order2, team_order3].map(&:order_suppliers))
      expect(future_delivery_orders.map(&:order_suppliers)).to match_array([team_order31, team_order32, team_order33].map(&:order_suppliers))
    end

    it 'lists 2 week old package team orders, if there are orders older than 3 weeks (inclusive)' do
      [team_order51, team_order52, team_order53, team_order41, team_order42, team_order43, team_order31, team_order32, team_order33].each(&:destroy) # destory 5, 4 and 3 week old orders

      future_delivery_orders = TeamOrders::ListPotentialFutureDeliveryOrders.new(team_order: package_order, week_starting: week_starting).call

      expect(future_delivery_orders.map(&:team_order)).to match_array([team_order1, team_order2, team_order3])
      expect(future_delivery_orders.map(&:order_suppliers)).to_not include(*[team_order1, team_order2, team_order3].map(&:order_suppliers))
      expect(future_delivery_orders.map(&:order_suppliers)).to match_array([team_order21, team_order22, team_order23].map(&:order_suppliers))
    end
  end

end
