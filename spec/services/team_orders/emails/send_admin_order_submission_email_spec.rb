require 'rails_helper'

RSpec.describe TeamOrders::Emails::SendAdminOrderSubmissionEmail, type: :service, emails: true, stripe: true, orders: true do
  include Rails.application.routes.url_helpers
  include ActionView::Helpers::NumberHelper

  subject { TeamOrders::Emails::SendAdminOrderSubmissionEmail.new(team_order: team_order).call }

  let!(:customer) { create(:customer_profile, :random, :with_user) }
  let!(:team_order) { create(:order, :new, customer_profile: customer) }
  let!(:team_order_detail) { create(:team_order_detail, :random, order: team_order, cutoff_option: 'charge_to_minimum') }
  let!(:supplier) { create(:supplier_profile, :random, :with_user) }
  let!(:order_supplier) { create(:order_supplier, :random, order: team_order, supplier_profile: supplier, surcharge: nil) }

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    # stubs for supplier creation
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
  end

  it 'returns the sent email' do
    sent_email = subject

    expect(sent_email).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: TeamOrders::Emails::SendAdminOrderSubmissionEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything
      )
    subject
  end

  context 'email subject' do
    it 'sends the email with the appropriate subject on submission' do
      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: "Yordar: Team Order Submitted - ##{team_order.id}",
          cc: anything,
          email_options: anything,
          email_variables: anything
        )

      subject
    end

    it 'sends the email with the appropriate subject on cancellation' do
      team_order.update_column(:status, 'cancelled')
      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: "Yordar: Team Order Cancelled - ##{team_order.id}",
          cc: anything,
          email_options: anything,
          email_variables: anything
        )

      subject
    end
  end

  it 'sends the email to the correct recipients (and cc)' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: customer.email_recipient,
      subject: anything,
      cc: 'orders-email',
      email_options: anything,
      email_variables: anything
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = "#{TeamOrders::Emails::SendAdminOrderSubmissionEmail::EMAIL_TEMPLATE}-#{team_order.id}-#{team_order.version_ref}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: customer.id, ref: email_ref },
        email_variables: anything
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the email specific data' do
      expected_email_variables = {
        firstname: customer.email_salutation,
        header_color: :pink,

        team_order: anything,
        order_spend: anything,
        minimum_spend: anything,
        supplier_surcharge: anything,
        account_managers: anything
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end

    it 'sends email with the correct team order data' do
      expected_supplier_data = {
        name: supplier.name,
        image: nil, # wasn't working with anything
      }
      expected_team_order_data = {
        id: team_order.id,
        name: team_order.name,
        link: team_order_url(team_order.id, host: yordar_credentials(:default_host)),
        cutoff_option: team_order.cutoff_option,
        status: team_order.status,
        budget: number_to_currency(team_order.team_order_budget),
        hide_budget: team_order.hide_budget,
        delivery_at: team_order.delivery_at.to_s(:full),
        delivery_address: team_order.delivery_address_arr.join(',<br/>'),
        supplier: expected_supplier_data
      }
      expected_email_variables = {
        team_order: deep_struct(expected_team_order_data),

        firstname: anything,
        order_spend: anything,
        minimum_spend: anything,
        supplier_surcharge: anything,
        header_color: anything,
        account_managers: anything
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end

    it 'sends email with the correct team order supplier data' do
      expected_supplier_data = {
        name: supplier.name,
        image: nil, # wasn't working with anything
      }
      expected_team_order_data = {
        id: anything,
        name: anything,
        link: anything,
        cutoff_option: anything,
        status: anything,
        budget: anything,
        hide_budget: anything,
        delivery_at: anything,
        delivery_address: anything,
        supplier: expected_supplier_data
      }
      expected_email_variables = {
        team_order: deep_struct(expected_team_order_data),
        supplier_surcharge: nil,

        firstname: anything,
        team_order: anything,        
        order_spend: anything,
        minimum_spend: anything,
        header_color: anything,
        account_managers: anything
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end

    context 'with supplier spends' do
      let!(:supplier_spends) do
        OpenStruct.new(
          total_spend: 20,
          minimum_spend: 100,
        )
      end

      before do
        # mock supplier spends fetcher
        supplier_spend_fetcher = double(Orders::GetSupplierSpends)
        allow(Orders::GetSupplierSpends).to receive(:new).and_return(supplier_spend_fetcher)
        allow(supplier_spend_fetcher).to receive(:call).and_return(supplier_spends)
        Orders::GetSupplierSpends.new(order: team_order, exclude_surcharge: true).call
      end

      it 'requests the supplier spends to be fetched' do
        expect(Orders::GetSupplierSpends).to receive(:new).with(order: team_order, exclude_surcharge: true)

        subject
      end

      it 'sends email with the correct spend data' do
        expected_email_variables = {
          order_spend: number_to_currency(supplier_spends.total_spend, precision: 2),
          minimum_spend: number_to_currency(supplier_spends.minimum_spend, precision: 2),

          firstname: anything,
          team_order: anything,
          supplier_surcharge: anything,        
          header_color: anything,
          account_managers: anything
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            cc: anything,
            email_options: anything,
            email_variables: expected_email_variables
          )

        subject
      end
    end
  end

  context 'with a Supplier surcharge (Team Order Topup)' do
    before do
      order_supplier.update_column(:surcharge, rand(20.02...30.53))
    end

    it 'sends the email with the appropriate subject on submission with topup' do
      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: "Yordar: Team Order Submitted - ##{team_order.id} - Top-Up Added",
          cc: anything,
          email_options: anything,
          email_variables: anything
        )

      subject
    end

    it 'sends email with the correct supplier surcharge' do
      expected_email_variables = {
        supplier_surcharge: number_to_currency(order_supplier.surcharge, precision: 2),

        firstname: anything,
        team_order: anything,        
        order_spend: anything,
        minimum_spend: anything,
        header_color: anything,
        account_managers: anything
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end
  end

  context 'with Notification Preferences' do
    let!(:notification_preference) { create(:notification_preference, account: customer, template_name: TeamOrders::Emails::SendAdminOrderSubmissionEmail::EMAIL_TEMPLATE) }

    it 'send email to the prefered email recipients' do
      notification_preference.update_column(:email_recipients, 'prefered-email-recipients')

      expect(::Emails::Send).to receive(:new).with(template_name: anything,
        recipient: 'prefered-email-recipients',
        subject: anything,
        cc: 'orders-email',
        email_options: anything,
        email_variables: anything
      )

      subject
    end

    it 'send email with the prefered email salutation' do
      notification_preference.update_column(:salutation, 'prefered-email-salutation')

      expected_email_variables = {
        firstname: 'prefered-email-salutation',

        team_order: anything,
        order_spend: anything,
        minimum_spend: anything,
        supplier_surcharge: anything,
        header_color: anything,
        account_managers: anything
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end

    it 'doesn\'t send email if the preference is set to be inactive' do
      notification_preference.update_column(:active, false)

      expect(Emails::Send).to_not receive(:new)
      subject
    end
  end

  context 'errors' do
    it 'doesn\'t send email if order is missing' do
      expect(Emails::Send).to_not receive(:new)

      TeamOrders::Emails::SendAdminOrderSubmissionEmail.new(team_order: nil).call
    end

    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(TeamOrders::Emails::SendAdminOrderSubmissionEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        failed_order_charge_email_sender = TeamOrders::Emails::SendAdminOrderSubmissionEmail.new(team_order: team_order)

        expected_error_message = "Failed to send team order submission email to customer #{customer.id} - ##{team_order.id}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(failed_order_charge_email_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

        failed_order_charge_email_sender.call
      end
    end # email sender error
  end

end