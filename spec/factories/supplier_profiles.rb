FactoryBot.define do

	factory :supplier_profile do

		trait :random do
			uuid { SecureRandom.uuid }
			company_name { Faker::Company.name }

			commission_rate { rand(0.1..11.25) }
			description { Faker::Lorem.sentence(word_count: 5) }
			company_address { Faker::Address.street_address }

			email { Faker::Internet.email }
			phone { Faker::PhoneNumber.phone_number }
			mobile { Faker::PhoneNumber.cell_phone }
		end

		trait :with_user do
			after(:create) do |supplier_profile|
				user = create(:user, :random)
				create(:profile, user: user, profileable: supplier_profile)
			end
		end

		trait :with_flags do
			transient do
				flags { {} }
			end
			after(:create) do |supplier_profile, evaluator|
				create(:supplier_flags, supplier_profile: supplier_profile, **evaluator.flags)
			end
		end

		trait :with_dear_account do
			after(:create) do |supplier_profile|
				create(:dear_account, :random, supplier_profile: supplier_profile)
			end
		end

	end

	factory :customer_profiles_supplier_profile do
		supplier_profile { create(:supplier_profile, :random) }
		customer_profile { create(:customer_profile, :random) }
	end

end
