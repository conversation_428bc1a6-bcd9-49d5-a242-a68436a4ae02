FactoryBot.define do

	factory :credit_card do

		trait :random do
			name { Faker::Name.name }
			# randomly pick a valid credit card number
			number { %w[**************** **************** *************** **************].sample }
			expiry_month { rand 1..12 }
			expiry_year { rand 2015..2020 }
			cvv { rand 100..999 }
			gateway_token { }
		end

		trait :on_account_card do
			name { Faker::Name.name }
			number { '****************' }
			pay_on_account { true }
		end

		trait :valid_payment do # only used in non-service specs
			name { Faker::Name.name }
			number { '****************' }
			expiry_month { rand 1..12 }
			expiry_year { rand 2020..2025 }
			cvv { 123 }
			gateway_token { SecureRandom.hex(7) }
		end

		trait :valid_eway_payment do
			name { Faker::Name.name }
			number { '****************' }
			expiry_month { rand 1..12 }
			expiry_year { rand 2020..2025 }
			cvv { 123 }
			gateway_token { SecureRandom.hex(7) }
		end

		trait :valid_stripe_payment do
			name { Faker::Name.name }
			number { '****************' }
			expiry_month { rand 1..12 }
			expiry_year { (Time.zone.now + rand(1..10).years).year }
			brand { 'visa' }
			country_code { 'au' }
			cvv { 123 }
			stripe_token { SecureRandom.hex(7) }
		end

		trait :expired do
			expiry_year { 1990 }
		end

	end

end

