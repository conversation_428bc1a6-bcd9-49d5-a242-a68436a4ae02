FactoryBot.define do

  factory :delivery_override do

    trait :random do
      customer_profile { create(:customer_profile, :random) }
      supplier_kind { 'all' }
      customer_override { rand(2.03..20.9) }
      supplier_override { rand(2.03..20.9) }
    end

    trait :for_specific_supplier do
      customer_profile { create(:customer_profile, :random) }
      supplier_kind { 'specific' }
      supplier_profile { create(:supplier_profile, :random) }
      customer_override { rand(2.03..20.9) }
      supplier_override { rand(2.03..20.9) }
    end

  end

end
