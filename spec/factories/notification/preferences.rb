FactoryBot.define do

  factory :notification_preference, class: 'Notification::Preference' do

    trait :random do
      account { create(:customer_profile, :random) }
      template_name { EmailTemplate::VALID_TEMPLATE_NAMES.sample }
    end

    trait :random_customer do
      account { create(:customer_profile, :random) }
      template_name {EmailTemplate::VALID_TEMPLATE_NAMES.sample }
    end

  end

end
