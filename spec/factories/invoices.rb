FactoryBot.define do

	factory :invoice do

		trait :random do
			customer_profile { create(:customer_profile, :random) }
			number { SecureRandom.hex(7) }
			from_at { Time.zone.now.beginning_of_week }
			to_at { Time.zone.now.end_of_week }
			due_at { Time.zone.now.end_of_week + Invoice::DEFAULT_PAYMENT_DAYS.days }
			amount_price { rand(0.01..5_000) }
			uuid { SecureRandom.uuid }
			status { 'confirmed' }
		end

	end

end
