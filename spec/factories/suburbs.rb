FactoryBot.define do

	factory :suburb do

		trait :eliza<PERSON><PERSON> do
			postcode { 2011 }
			name { 'Elizabeth Bay' }
			state { 'NSW' }
			latitude { -0.338702E2 }
			longitude { 0.151222E3 }
			country_code { 'AU' }
		end

		trait :pyrmont do
			name { 'Pyrmont' }
			postcode { 2009 }
			state { 'NSW' }
			latitude { -33.8694 }
			longitude { 151.1939 }
			country_code { 'AU' }
		end

		trait :woolloomooloo do
			name { 'Woolloomooloo' }
			postcode { 2011 }
			state { 'NSW' }
			latitude { -33.8702 }
			longitude { 151.222 }
			country_code { 'AU' }
		end

		trait :granville do
			name { 'Granville' }
			postcode { 2142 }
			state { 'NSW' }
			latitude { -33.8332 }
			longitude { 151.0198 }
			country_code { 'AU' }
		end

		trait :cronulla do
			name { 'Cronulla' }
			postcode { 2230 }
			state { 'NSW' }
			latitude { -34.05 }
			longitude { 151.15 }
			country_code { 'AU' }
		end

		trait :haymarket do
			name { 'Haymarket' }
			postcode { 2000 }
			state { 'NSW' }
			latitude { -33.8808 }
			longitude { 151.2028 }
			country_code { 'AU' }
		end

		trait :darlinghurst do
			name { 'Darlinghurst' }
			postcode { 2010 }
			state { 'NSW' }
			latitude { -33.8821 }
			longitude { 151.2151 }
			country_code { 'AU' }
		end

		trait :rosebay do
			name { 'Rose Bay' }
			postcode { 2029 }
			state { 'NSW' }
			latitude { -33.8747 }
			longitude { 151.2633 }
			country_code { 'AU' }
		end

		trait :sutherland do
			name { 'Sutherland' }
			postcode { 2232 }
			state { 'NSW' }
			latitude { -34.106 }
			longitude { 151.0832 }
			country_code { 'AU' }
		end

		trait :melbourne do
			name { 'Melbourne' }
			postcode { 3000 }
			state { 'VIC' }
			latitude { -37.814 }
			longitude { 144.96332 }
			country_code { 'AU' }
		end

		# NZ based suburbs
		trait :auckland_central do
			name { 'Auckland Central' }
			postcode { 1010 }
			state { 'Auckland' }
			latitude { -36.8052 }
			longitude { 174.9811 }
			country_code { 'NZ' }
		end

		trait :ponsonby do
			name { 'Ponsonby' }
			postcode { 1011 }
			state { 'Auckland' }
			latitude { -36.8447 }
			longitude { 174.7336 }
			country_code { 'NZ' }
		end

		trait :parnell do
			name { 'Parnell' }
			postcode { 1052 }
			state { 'Auckland' }
			latitude { -36.857 }
			longitude { 174.7914 }
			country_code { 'NZ' }
		end		

		trait :random do
			postcode { rand(1000...9999) }
			name { Faker::Address.city }
			state{ %w[NSW ACT NT VIC].sample }
			latitude { rand(-90.000000000...90.000000000) }
			longitude { rand(-180.000000000...180.000000000) }
			country_code { 'AU' }
		end

	end

end
