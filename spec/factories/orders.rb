FactoryBot.define do

	factory :order do

		name { Faker::Company.name }
		delivery_at { Time.now + 3.days }
		customer_profile { create(:customer_profile, :random) }
		credit_card { create(:credit_card, :on_account_card) }
		delivery_address { Faker::Address.street_address }
		delivery_suburb { create(:suburb, :random) }
		uuid { SecureRandom.uuid }

		trait :new do
			status { 'new' }
		end

		trait :amended do
			status { 'amended' }
		end

		trait :confirmed do
			status { 'confirmed' }
		end

		trait :draft do
			status { 'draft' }
		end

		trait :delivered do
			status { 'delivered' }
		end

		trait :quoted do
			status { 'quoted' }
		end

		trait :random do
			status { %w[new pending delivered amended confirmed].sample }
		end

		trait :custom_order do
			order_type { 'one-off' }
			order_variant { 'event_order' }
			major_category { create(:category, :random, is_generic: true) }
		end

		trait :team_order do
			order_variant { 'team_order' }
			status { 'pending' }
			unique_event_id { SecureRandom.hex(7) }

			after(:build) do |team_order|
				team_order.team_order_detail = create(:team_order_detail, :random, order: team_order)
			end
		end

		trait :package_team_order do
			order_variant { 'team_order' }
			status { 'pending' }
			unique_event_id { SecureRandom.hex(7) }

			transient do
				package_id { '' }
			end
			after(:build) do |team_order, evaluator|
				team_order.team_order_detail = create(:team_order_detail, :random, order: team_order, package_id: evaluator.package_id || SecureRandom.hex(7))
			end
		end

		trait :recurring_team_order do
			order_variant { 'recurring_team_order' }
			status { 'pending' }
			unique_event_id { SecureRandom.hex(7) }

			transient do
				package_id { '' }
			end
			after(:build) do |team_order, evaluator|
				team_order.team_order_detail = create(:team_order_detail, :random, order: team_order, package_id: evaluator.package_id || SecureRandom.hex(7))
			end
		end

	end

end
