FactoryBot.define do

	factory :customer_profile do

		trait :random do
			contact_phone { Faker::PhoneNumber.cell_phone }
		end

		trait :dummy do
			contact_phone { '0425203193' }
		end

		trait :with_user do
			after(:create) do |customer_profile|
				user = create(:user, :random)
				create(:profile, user: user, profileable: customer_profile)
			end
		end

		trait :with_flags do
			transient do
				flags { {} }
			end
			after(:create) do |customer_profile, evaluator|
				create(:customer_flags, customer_profile: customer_profile, **evaluator.flags)
			end
		end

	end

end
