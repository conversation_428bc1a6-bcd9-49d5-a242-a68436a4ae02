FactoryBot.define do

  factory :order_review do

    trait :random do
      supplier_profile { create(:supplier_profile, :random) }
      order { create(:order, :random) }
      comment { Faker::Lorem.sentence }
    end

    trait :random_scores do
      product_quality_score { rand(1..5) }
      delivery_punctuality_score { rand(1..5) }
      food_taste_score { rand(1..5) }
      presentation_score { rand(1..5) }
    end

  end

end
