FactoryBot.define do

  factory :supplier_markup_override, class: 'Supplier::MarkupOverride' do

    trait :random do
      supplier_profile { create(:supplier_profile, :random) }
      overridable { [true, false].sample ? create(:customer_profile, :random) : create(:company, :random) }
      markup { rand(10.2..15.9) }
      commission_rate { rand(10.2..15.9) }
    end

    trait :company_override do
      supplier_profile { create(:supplier_profile, :random) }
      overridable { create(:company, :random) }
      markup { rand(10.2..15.9) }
      commission_rate { rand(10.2..15.9) }
    end

    trait :customer_override do
      supplier_profile { create(:supplier_profile, :random) }
      overridable { create(:customer_profile, :random) }
      markup { rand(10.2..15.9) }
      commission_rate { rand(10.2..15.9) }
    end

  end

end
