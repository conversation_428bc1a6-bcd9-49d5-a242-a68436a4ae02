require 'rails_helper'

RSpec.describe <PERSON><PERSON><PERSON>roller, type: :controller do

	before :each do
		request.env['devise.mapping'] = Devise.mappings[:user]
	end

	describe 'GET #new' do
		it 'should be successful' do
			get :new
			expect(response).to be_successful
		end
	end

	describe 'POST #create' do # LOGIN USER
		let!(:password) { SecureRandom.hex(7) }
		let!(:user) { create(:user, :random, password: password) }

		context 'with valid login details' do
			let!(:valid_details) { { email: user.email, password: password } }

			it 'signs the user in (sets up session)' do
				post :create, params: { user: valid_details }
				expect(session['warden.user.user.key'].first.first).to eq(user.id)
			end

			it 'notifies a confirmed user of successful sign in' do
				post :create, params: { user: valid_details }
				expect(flash[:notice]).to include('Signed in successfully')
			end

			it 'notifies an unconfirmed user of successful sign in (confirmation check alert comes later)' do
				user.update_column(:confirmed_at, nil)
				post :create, params: { user: valid_details }
				expect(flash[:notice]).to include('Signed in successfully')
			end

			context 'as a customer' do
				let!(:customer_user) { create(:user, :as_customer, password: password) }
				let!(:login_details) { { email: customer_user.email, password: password } }

				it 'redirects the suplier to its homepage on sign in' do
					post :create, params: { user: login_details }

					expect(response).to redirect_to(Rails.application.routes.url_helpers.customer_profile_path)
				end
			end

			context 'as a supplier' do
				let!(:supplier_user) { create(:user, :as_supplier, password: password) }
				let!(:login_details) { { email: supplier_user.email, password: password } }

				it 'redirects the suplier to its homepage on sign in' do
					post :create, params: { user: login_details }

					expect(response).to redirect_to(Rails.application.routes.url_helpers.supplier_profile_path)
				end
			end

			context 'with admin access' do
				let!(:admin_user) { create(:user, admin: true, password: password) }
				let!(:user_with_supplier_access) { create(:user, can_access_suppliers: true, password: password) }

				let!(:login_user) { [admin_user, user_with_supplier_access].sample }
				let!(:login_details) { { email: login_user.email, password: password } }

				it 'redirects to rails admin dashboard' do
					post :create, params: { user: login_details }

					expect(response).to redirect_to(Rails.application.routes.url_helpers.admin_path)
				end
			end

			context 'as a normal user' do
				let!(:normal_user) { create(:user, admin: false, password: password) }
				let!(:login_details) { { email: normal_user.email, password: password } }

				it 'redirects to rails admin dashboard' do
					post :create, params: { user: login_details }

					expect(response).to redirect_to(Rails.application.routes.url_helpers.prismic_root_path)
				end
			end
		end

		context 'with invalid login details' do
			let!(:invalid_details) { { email: user.email, password: 'invalid-password' } }

			it 'should not sign user if password was wrong' do
				post :create, params: { user: invalid_details }
				expect(session['warden.user.user.key']).to be_blank
				expect(flash[:alert]).to include('Invalid Email or password')
			end
		end
	end
end
