require 'rails_helper'

RSpec.describe OrderReviewsController, type: :controller do

  describe '#new' do
    let!(:order) { create(:order, :delivered) }

    let(:supplier1) { create(:supplier_profile, :random) }
    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }

    let(:supplier2) { create(:supplier_profile, :random) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

    it 'renders new template' do
      get :new, params: { supplier_profile_id: supplier1.id, order_id: order.id }

      expect(response).to render_template(:new)
    end

    it 'instantiates the order and the supplier' do
      get :new, params: { supplier_profile_id: supplier1.id, order_id: order.id }

      expect(assigns(:order)).to eq(order)
      expect(assigns(:supplier)).to eq(supplier1)
    end

    it 'instantiates order\'s order lines belonging to the supplier' do
      get :new, params: { supplier_profile_id: supplier1.id, order_id: order.id }

      expect(assigns(:order_lines)).to include(order_line1)
      expect(assigns(:order_lines)).to_not include(order_line2)
    end

    context 'errors' do
      it 'renders missing review if order_id is not passed' do
        get :new, params: { supplier_profile_id: supplier1.id, order_id: nil }

        expect(response).to render_template('order_reviews/missing_review')
      end

      it 'renders missing review if supplier_id is not passed' do
        get :new, params: { supplier_profile_id: nil, order_id: order.id }

        expect(response).to render_template('order_reviews/missing_review')
      end

      it 'renders missing review if supplier does not belong to the order' do
        non_order_supplier = create(:supplier_profile, :random)
        get :new, params: { supplier_profile_id: non_order_supplier.id, order_id: order.id }

        expect(response).to render_template('order_reviews/missing_review')
      end
    end

  end
end
