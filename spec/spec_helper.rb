require 'simplecov'
require 'capybara'

SimpleCov.start 'rails' do
	add_filter 'vendor/'
	add_filter 'config/'
	add_filter 'spec/'
	add_filter 'app/templates/'
	add_filter 'app/uploaders/'
	add_filter 'app/controllers/'
	add_filter 'app/channels/'
	add_filter 'app/views/'
	add_filter 'lib/'
	# minimum_coverage 95

	add_group 'Models', 'app/models'
	add_group 'Services', 'app/services'
	add_group 'Helpers', 'app/helpers'
	add_group 'Mailers', 'app/mailers'
	add_group 'Libraries', 'lib'
end

# This file is copied to spec/ when you run 'rails generate rspec:install'
ENV['RAILS_ENV'] ||= 'test'
require File.expand_path('../config/environment', __dir__)
require 'rspec/rails'

# Requires supporting ruby files with custom matchers and macros, etc,
# in spec/support/ and its subdirectories.
Dir[Rails.root.join('spec/support/**/*.rb')].each { |f| require f }
include FactoryBot::Syntax::Methods

RSpec.configure do |config|
	# ## Mock Framework
	#
	# If you prefer to use mocha, flexmock or RR, uncomment the appropriate line:
	#
	# config.mock_with :mocha
	# config.mock_with :flexmock
	# config.mock_with :rr

	# Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
	config.fixture_path = "#{::Rails.root}/spec/fixtures"

	# If you're not using ActiveRecord, or you'd prefer not to run each of your
	# examples within a transaction, remove the following line or assign false
	# instead of true.
	config.use_transactional_fixtures = true

	# If true, the base class of anonymous controllers will be inferred
	# automatically. This will be the default behavior in future versions of
	# rspec-rails.
	config.infer_base_class_for_anonymous_controllers = false

	Delayed::Worker.delay_jobs = false

	# Run specs in random order to surface order dependencies. If you find an
	# order dependency and want to debug it, you can fix the order by providing
	# the seed, which is printed after each run.
	#     --seed 1234
	config.order = 'random'
	Kernel.srand config.seed

	# https://github.com/plataformatec/devise/wiki/How-To:-Controllers-tests-with-Rails-3-%28and-rspec%29
	# config.include Devise::TestHelpers, :type => :controller  # this is done in support/devise.rb

	config.infer_spec_type_from_file_location!
end
