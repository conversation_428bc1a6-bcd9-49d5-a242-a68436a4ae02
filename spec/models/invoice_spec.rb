require 'rails_helper'

RSpec.describe Invoice, type: :model do

	describe 'validations' do
		it { is_expected.to validate_inclusion_of(:status).in_array(Invoice::VALID_STATUSES) }
		it { is_expected.to_not validate_presence_of(:status) }
		it { is_expected.to validate_inclusion_of(:payment_status).in_array(Invoice::VALID_PAYMENT_STATUSES) }
		it { is_expected.to validate_presence_of(:number) }
		it { is_expected.to validate_presence_of(:customer_profile) }
		it { is_expected.to validate_presence_of(:from_at) }
		it { is_expected.to validate_presence_of(:to_at) }
		it { is_expected.to validate_presence_of(:due_at) }
		it { is_expected.to validate_presence_of(:uuid) }

		it 'has default values' do
			invoice = Invoice.new

			expect(invoice.payment_status).to eq('unpaid')
			expect(invoice.do_not_notify).to be_falsey
		end

		it 'has a valid factory' do
			factory_invoice = build(:invoice, :random)

			expect(factory_invoice).to be_valid
		end
	end

	describe 'associations' do
		it { is_expected.to have_many(:orders) }
		it { is_expected.to have_many(:gst_free_orders) }
		it { is_expected.to have_many(:order_lines) }
		it { is_expected.to have_one(:payment) }
		it { is_expected.to belong_to(:customer_profile) }
		it { is_expected.to belong_to(:customer_purchase_order) }
		it { is_expected.to have_many(:documents) }
	end

	context 'methods' do
		describe '.invoice_orders' do
			let!(:invoice) { create(:invoice, :random) }
			let!(:order1) { create(:order, :delivered, update_with_invoice: true, invoice: invoice) }
			let!(:order2) { create(:order, :delivered, update_with_invoice: true, invoice: invoice) }

			it 'list all orders connected to the invoice', gst_split_invoicing: true do
				expect(invoice.invoice_orders).to include(order1, order2)
			end

			context 'with gst free orders', gst_split_invoicing: true do
				let!(:order3) { create(:order, :delivered, update_with_invoice: true, gst_free_invoice: invoice) }
				let!(:order4) { create(:order, :delivered, update_with_invoice: true, gst_free_invoice: invoice) }

				it 'list all orders connected to the invoice (GST or GST-Free)', gst_split_invoicing: true do
					expect(invoice.invoice_orders).to include(order1, order2, order3, order4)
				end
			end
		end

		context 'delegated methods' do
			describe '.po_number' do
				let(:customer_purchase_order) { create(:customer_purchase_order, :random) }
				let!(:invoice) { create(:invoice, :random, customer_purchase_order: customer_purchase_order) }

				it 'delegates the po number to the attached customer_purchase_order' do
					expect(invoice.po_number).to eq(customer_purchase_order.po_number)
				end

				it 'returns nil if not attached to any customer_purchase_order' do
					invoice.update_column(:cpo_id, nil)
					expect(invoice.po_number).to be_nil
				end
			end
		end

		describe '.latest_document' do
			let!(:invoice) { create(:invoice, :random, payment_status: 'unpaid') }
			let!(:tax_invoice_document1) { create(:document, :random, documentable: invoice, kind: 'tax_invoice', version: 1) }
			let!(:tax_invoice_document2) { create(:document, :random, documentable: invoice, kind: 'tax_invoice', version: 2) }
			let!(:tax_invoice_receipt_document1) { create(:document, :random, documentable: invoice, kind: 'tax_invoice_receipt', version: 1) }
			let!(:tax_invoice_receipt_document2) { create(:document, :random, documentable: invoice, kind: 'tax_invoice_receipt', version: 2) }

			it 'returns the latest tax_invoice_document' do
				expect(invoice.latest_document).to eq(tax_invoice_document2)
			end

			it 'returns the latest document based on kind' do
				expect(invoice.latest_document(kind: 'tax_invoice_receipt')).to eq(tax_invoice_receipt_document2)
			end

			context 'for a paid invoice' do
				before do
					invoice.update_column(:payment_status, 'paid')
				end

				it 'returns the latest tax_invoice_receipt document if the invoice is paid' do
					expect(invoice.latest_document).to eq(tax_invoice_receipt_document2)
				end

				it 'returns the latest tax invoice document if the invoice is paid and the tax_invoice_receipt document is missing' do
					[tax_invoice_receipt_document1, tax_invoice_receipt_document2].each(&:destroy)
					expect(invoice.latest_document).to eq(tax_invoice_document2)
				end
			end # paid invoice
		end # .latest_document

		describe '.paid?' do
			it 'returns true only for invoices with payment_status as `paid`' do
				paid_invoice = build(:invoice, :random, payment_status: 'paid')
				expect(paid_invoice).to be_paid

				unpaid_invoice = build(:invoice, :random, payment_status: (Invoice::VALID_PAYMENT_STATUSES - ['paid']).sample)
				expect(unpaid_invoice).to_not be_paid
			end
		end

		describe '.overdue?' do
			let!(:invoice) { build(:invoice, :random, due_at: Time.zone.now - 1.day) }

			it 'returns true if the due_at is before today' do
				expect(invoice).to be_overdue
			end

			it 'returns true if the due_at is less than start of today' do
				invoice.due_at = Time.zone.now.beginning_of_day - 1.minute
				expect(invoice).to be_overdue
			end

			it 'returns false if the due_at is greater than start of today' do
				invoice.due_at = [Time.zone.now.beginning_of_day, Time.zone.now, Time.zone.now + 1.day].sample
				expect(invoice).to_not be_overdue
			end

			it 'returns false if due_at is missing' do
				invoice.due_at = nil
				expect(invoice).to_not be_overdue
			end

			it 'returns false if invoice is marked as paid' do
				invoice.payment_status = 'paid'
				expect(invoice).to_not be_overdue
			end
		end # .overdue?

	end # methods

end
