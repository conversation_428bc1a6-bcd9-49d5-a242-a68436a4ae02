require 'rails_helper'

RSpec.describe Coupon, type: :model do

  describe 'validations' do
    it { is_expected.to validate_presence_of(:code) }
    it { is_expected.to validate_presence_of(:valid_from) }
    it { is_expected.to validate_presence_of(:redemption_limit) }
    it { is_expected.to validate_presence_of(:amount) }
    it { is_expected.to validate_presence_of(:type) }
    it { is_expected.to validate_inclusion_of(:type).in_array(Coupon::VALID_TYPES) }

    it 'has a valid factory' do
      factory_company = build(:coupon, :random)
      expect(factory_company).to be_valid
    end
  end

  describe 'associations' do
    it { is_expected.to have_many(:orders) }
    it { is_expected.to have_many(:redemptions) }
  end

  describe 'callbacks', callbacks: true do
    it 'sanitizes by stripping the code before save' do
      coupon = create(:coupon, :random, code: ' with-spaces ')

      expect(coupon.code).to eq('with-spaces')
    end
  end

end
