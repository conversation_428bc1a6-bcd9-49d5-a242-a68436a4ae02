require 'rails_helper'

RSpec.describe Dear::Sale, type: :model, dear: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:sale_id) }
    it { is_expected.to validate_presence_of(:location) }

    it 'has a valid factory' do
      factory_dear_sale = build(:dear_sale, :random)
      expect(factory_dear_sale).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:order) }
    it { is_expected.to belong_to(:supplier_profile) }
  end

end
