require 'rails_helper'

RSpec.describe Dear::Account, type: :model, dear: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:account_id) }
    it { is_expected.to validate_presence_of(:api_key) }
    it { is_expected.to validate_presence_of(:customer_id) }
    it { is_expected.to validate_inclusion_of(:price_tier).in_array(Dear::Account::VALID_PRICE_TIERS).allow_blank }
    it { is_expected.to validate_inclusion_of(:dietary_attribute).in_array(Dear::Account::VALID_ADDITIONAL_ATTRIBUTES).allow_blank }

    it 'has a valid factory' do
      factory_dear_account = build(:dear_account, :random)
      expect(factory_dear_account).to be_valid
    end

    it 'has factory with default values' do
      factory_dear_account = build(:dear_account, :random)
      expect(factory_dear_account).to be_active
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:supplier_profile) }
    it { is_expected.to have_many(:categories) }
  end

end
