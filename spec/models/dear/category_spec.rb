require 'rails_helper'

RSpec.describe Dear::Category, type: :model, dear: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:dear_account_id) }
    it { is_expected.to validate_presence_of(:category_id) }
    it { is_expected.to validate_presence_of(:name) }

    it 'has a valid factory' do
      factory_category = build(:dear_category, :random)
      expect(factory_category).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:dear_account) }
    it { is_expected.to have_one(:supplier_profile) }
  end

end
