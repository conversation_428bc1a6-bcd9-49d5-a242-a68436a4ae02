require 'rails_helper'

RSpec.describe Report::Datum, type: :model, reports: true do

  context 'validations' do
    it 'has a valid factory' do
      factory_datum = build(:report_datum, :random)

      expect(factory_datum).to be_valid
    end
  end

  context 'association' do
    it { is_expected.to belong_to(:report_source) }
    it { is_expected.to belong_to(:customer_purchase_order) }
    it { is_expected.to have_many(:order_data) }
  end

end
