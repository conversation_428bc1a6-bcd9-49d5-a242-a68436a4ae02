require 'rails_helper'

RSpec.describe Report::OrderDatum, type: :model, reports: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:data_kind) }
    it { is_expected.to validate_inclusion_of(:data_kind).in_array(Report::OrderDatum::VALID_DATA_KINDS) }

    it 'has a valid factory' do
      factory_order_datum = build(:report_order_datum, :random)

      expect(factory_order_datum).to be_valid
    end
  end

  context 'association' do
    it { is_expected.to belong_to(:report_datum) }
  end

end
