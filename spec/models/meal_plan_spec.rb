require 'rails_helper'

RSpec.describe MealPlan, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }
    it { is_expected.to validate_presence_of(:kind) }
    it { is_expected.to validate_inclusion_of(:kind).in_array(MealPlan::VALID_KINDS) }
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:delivery_time) }
    it { is_expected.to validate_presence_of(:delivery_address) }
    it { is_expected.to validate_presence_of(:delivery_suburb) }
    it { is_expected.to validate_presence_of(:credit_card) }
    it { is_expected.to validate_presence_of(:uuid) }
    it { is_expected.to validate_inclusion_of(:reminder_frequency).in_array(MealPlan::VALID_REMINDER_FREQUENCIES).allow_blank }

    it 'has a valid factory' do
      factory_meal_plan = build(:meal_plan, :random)

      expect(factory_meal_plan).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to belong_to(:delivery_suburb) }
    it { is_expected.to belong_to(:customer_purchase_order) }
    it { is_expected.to belong_to(:gst_free_customer_purchase_order) }
    it { is_expected.to have_many(:orders) }
  end

  context 'delegated methods' do
    describe '.po_number' do
      let(:customer_purchase_order) { create(:customer_purchase_order, :random) }
      let!(:meal_plan) { create(:meal_plan, :random, customer_purchase_order: customer_purchase_order) }

      it 'delegates the po number to the attached customer_purchase_order' do
        expect(meal_plan.po_number).to eq(customer_purchase_order.po_number)
      end

      it 'returns nil if not attached to any customer_purchase_order' do
        meal_plan.update_column(:cpo_id, nil)
        expect(meal_plan.po_number).to be_nil
      end
    end

    describe '.gst_free_po_number' do
      let(:gst_free_customer_purchase_order) { create(:customer_purchase_order, :random) }
      let!(:meal_plan) { create(:meal_plan, :random, gst_free_customer_purchase_order: gst_free_customer_purchase_order) }

      it 'delegates the po number to the attached gst_free_customer_purchase_order' do
        expect(meal_plan.gst_free_po_number).to eq(gst_free_customer_purchase_order.po_number)
      end

      it 'returns nil if not attached to any gst_free_customer_purchase_order' do
        meal_plan.update_column(:gst_free_cpo_id, nil)
        expect(meal_plan.gst_free_po_number).to be_nil
      end
    end
  end

  context 'instance methods' do
    describe '.archived?' do
      let!(:meal_plan) { create(:meal_plan, :random) }

      it 'returns true if the meal plan has an archived datetime' do
        meal_plan.update_column(:archived_at, Time.zone.now)

        expect(meal_plan).to be_archived
      end

      it 'returns false if the meal plan has an archived datetime' do
        meal_plan.update_column(:archived_at, nil)

        expect(meal_plan).to_not be_archived
      end
    end
  end

end
