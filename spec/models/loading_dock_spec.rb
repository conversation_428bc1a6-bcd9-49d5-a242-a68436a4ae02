require 'rails_helper'

RSpec.describe LoadingDock, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }

    it 'has a valid factory' do
      factory_loading_dock = build(:loading_dock, :random)

      expect(factory_loading_dock).to be_valid
    end

    it 'validates that it has at least have one info' do
      no_info_loading_dock = build(:loading_dock, :random, code: nil, file_url: nil)
      expect(no_info_loading_dock).to_not be_valid
      expect(no_info_loading_dock.errors.full_messages).to include('Must have at least a code or a file')

      code_loading_dock = build(:loading_dock, :random, code: Faker::Lorem.sentence, file_url: nil)
      expect(code_loading_dock).to be_valid

      file_loading_dock = build(:loading_dock, :random, code: nil, file_url: Faker::Internet.url)
      expect(file_loading_dock).to be_valid

      combined_loading_dock = build(:loading_dock, :random, code: Faker::Lorem.sentence, file_url: Faker::Internet.url)
      expect(combined_loading_dock).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to have_many(:orders) }
  end

end
