require 'rails_helper'

RSpec.describe SurveyAnswer, type: :model, employee_surveys: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:employee_survey_submission) }
    it { is_expected.to validate_presence_of(:survey_question) }
    it { is_expected.to validate_presence_of(:value) }

    it 'has a valid factory' do
      factory_answer = build(:survey_answer, :random)
      expect(factory_answer).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:employee_survey_submission) }
    it { is_expected.to belong_to(:survey_question) }
  end

end
