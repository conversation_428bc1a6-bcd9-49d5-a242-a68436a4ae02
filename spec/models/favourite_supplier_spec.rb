require 'rails_helper'

RSpec.describe FavouriteSupplier, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:kind) }
    it { is_expected.to validate_inclusion_of(:kind).in_array(FavouriteSupplier::VALID_KINDS) }
    it { is_expected.to validate_presence_of(:favouriter) }
    it { is_expected.to validate_presence_of(:supplier_profile) }

    it 'has a valid factory' do
      factory_favourite_supplier = build(:favourite_supplier, :random)

      expect(factory_favourite_supplier).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:favouriter) }
    it { is_expected.to belong_to(:supplier_profile) }
  end
end
