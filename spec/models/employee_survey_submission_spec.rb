require 'rails_helper'

RSpec.describe EmployeeSurveySubmission, type: :model, employee_surveys: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:employee_survey) }

    it 'has a valid factory' do
      factory_submission = build(:employee_survey_submission, :random)
      expect(factory_submission).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:employee_survey) }
    it { is_expected.to have_many(:survey_answers) }
  end

end
