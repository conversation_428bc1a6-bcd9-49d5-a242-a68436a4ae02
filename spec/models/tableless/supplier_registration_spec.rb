require 'rails_helper'

RSpec.describe SupplierRegistration, type: :model, tableless: true do

	describe 'validations' do
		let!(:registration) { build(:supplier_registration, :random) }

		it { is_expected.to validate_presence_of(:firstname) }
		it { is_expected.to validate_presence_of(:company_name) }

		it { is_expected.to validate_presence_of(:email) }

		it 'validates the email format' do
			['<EMAIL>', '<EMAIL>'].each do |valid_email|
				registration.email = registration.email_confirmation = valid_email
				expect(registration).to be_valid
			end

			['aa', 'aa@', '@bb'].each do |invalid_email|
				registration.email = registration.email_confirmation = invalid_email
				expect(registration).to_not be_valid
			end
		end

		it { is_expected.to validate_presence_of(:password) }

		it 'valiates length of password' do
			registration.password = registration.password_confirmation = 123_456_789
			expect(registration).to be_valid

			registration.password = registration.password_confirmation = 1_234_567
			expect(registration).to_not be_valid
		end

		it 'has a valid factory' do
			expect(registration).to be_valid
		end
	end

end


