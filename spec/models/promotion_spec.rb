require 'rails_helper'

RSpec.describe Promotion, type: :model, promotions: true do
  
  context 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:amount) }
    it { is_expected.to validate_presence_of(:kind) }
    it { is_expected.to validate_inclusion_of(:kind).in_array(Promotion::VALID_KINDS) }
    it { is_expected.to validate_inclusion_of(:category_restriction).in_array(Category::VALID_CATEGORY_GROUPS) }
    it { is_expected.to_not validate_presence_of(:category_restriction) }
    it { is_expected.to validate_presence_of(:valid_from) }

    it 'has a valid factory' do
      factory_promotion = build(:promotion, :random)

      expect(factory_promotion).to be_valid
    end

    it 'has default values' do
      promotion = Promotion.new

      expect(promotion.active).to be_truthy
    end

    it 'validates valid from and to dates' do
      promotion = create(:promotion, :random)

      promotion.valid_until = promotion.valid_from + 2.days
      expect(promotion).to be_valid

      promotion.valid_until = promotion.valid_from - 2.days
      expect(promotion).to_not be_valid
      
      expect(promotion.errors[:valid_until]).to be_present
      expect(promotion.errors[:valid_until]).to include('Must end after it starts')
    end
  end

  context 'associations' do
    it { is_expected.to have_many(:subscriptions) }
    it { is_expected.to have_many(:active_subscriptions) }
    it { is_expected.to have_many(:orders) }
  end

  context 'instance methods' do
    describe '.discount_note' do
      it 'shows the dolar based note for a amount kind of promotion' do
        promotion = create(:promotion, :random, kind: 'amount')

        expect(promotion.discount_note).to eq("$#{promotion.amount} OFF")
      end

      it 'shows the percent based note for a percentage kind of promotion' do
        promotion = create(:promotion, :random, kind: 'percentage')

        expect(promotion.discount_note).to eq("#{promotion.amount}% OFF")
      end
    end
  end

end
