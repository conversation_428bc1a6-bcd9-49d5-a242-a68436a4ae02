require 'rails_helper'

RSpec.describe Order, type: :model, orderxs: true do

	context 'validations' do
		it { is_expected.to validate_inclusion_of(:status).in_array(Order::VALID_ORDER_STATUSES) }
		it { is_expected.to validate_inclusion_of(:pattern).in_array(Order::VALID_PATTERNS).allow_blank }
		it { is_expected.to validate_inclusion_of(:payment_status).in_array(Order::VALID_PAYMENT_STATUSES).allow_blank }
		it { is_expected.to validate_inclusion_of(:order_type).in_array(Order::VALID_ORDER_TYPES).allow_blank }
		it { is_expected.to validate_inclusion_of(:order_variant).in_array(Order::VALID_ORDER_VARIANTS).allow_blank }
		it { is_expected.to validate_inclusion_of(:delivery_type).in_array(Order::VALID_DELIVERY_TYPES).allow_blank }
		it { is_expected.to validate_presence_of(:uuid) }

		it 'has a valid factory' do
			order = build(:order, :random)
			expect(order).to be_valid
		end

		describe 'Update with Invoice' do
			let!(:invoice) { create(:invoice, :random) }
			let!(:order) { create(:order, :random) }

			it 'prohibits editing an order which was invoiced.' do
				order.invoice = invoice
				order.name = 'something new'
				expect{ order.save! }.to raise_error(ActiveRecord::RecordInvalid)
				expect(order.reload.name).to_not eq('something new')
			end

			it 'allows an order which was invoiced to be edited if update_with_invoice is true' do
				order.invoice = invoice
				order.name = 'something new'
				order.update_with_invoice = true
				expect{ order.save! }.to_not raise_error(ActiveRecord::RecordInvalid)
				expect(order.reload.name).to eq('something new')
			end
		end

		it 'validates presence of delivery at for order with status not in [draft or cancelled]' do
			order = build(:order, :random)

			order.status = %w[new amended confirmed delivered].sample
			expect(order).to be_valid

			order.delivery_at = nil
			expect(order).to_not be_valid
		end

		it 'requires fields to be set for all order with non-draft status' do
			validation_fields = %i[name credit_card_id customer_profile_id delivery_address delivery_suburb_id]

 			order = create(:order, :draft)
			expect(order).to be_valid
			validation_fields.sample(2).each do |field|
				order.send("#{field}=", nil)
			end
			expect(order).to be_valid

			order = create(:order, :draft)
			expect(order).to be_valid
			order.update_column(:status, %w[new amended confirmed delivered skipped pending paused].sample)

			validation_fields.sample(2).each do |field|
				order.send("#{field}=", nil)
			end
			expect(order).to_not be_valid
		end

		it 'requires all order lines to be accepted when order is confirmed' do
			order = create(:order, :new)
			# create pending orders lines
			create(:order_line, :random, status: 'pending', order: order)
			create(:order_line, :random, status: 'pending', order: order)
			expect(order.reload).to be_valid

			order.status = 'confirmed'
			expect(order).to_not be_valid
		end

		it 'validates presence of order major category if order is an event_order' do
			order = build(:order, :random)
			order.order_variant = 'event_order'
			order.major_category = nil
			expect(order).to_not be_valid

			order.major_category = create(:category, :random, is_generic: true)
			expect(order).to be_valid
		end
	end

	context 'associations' do
		it { is_expected.to belong_to(:customer_profile) }
		it { is_expected.to belong_to(:pantry_manager) }
		it { is_expected.to belong_to(:delivery_suburb) }
		it { is_expected.to belong_to(:invoice) }
		it { is_expected.to belong_to(:gst_free_invoice) }
		it { is_expected.to belong_to(:credit_card) }
		it { is_expected.to belong_to(:customer_purchase_order) }
		it { is_expected.to belong_to(:gst_free_customer_purchase_order) }
		it { is_expected.to belong_to(:coupon) }
		it { is_expected.to belong_to(:promotion) }
		it { is_expected.to belong_to(:major_category) }
		it { is_expected.to belong_to(:loading_dock) }
		it { is_expected.to have_many(:order_lines) }
		it { is_expected.to have_many(:ordered_menu_items) }
		it { is_expected.to have_many(:locations) }
		it { is_expected.to have_many(:supplier_profiles) }
		it { is_expected.to have_many(:order_suppliers) }
		it { is_expected.to have_many(:custom_order_suppliers) }
		it { is_expected.to have_many(:documents) }
		it { is_expected.to belong_to(:meal_plan) }
		it { is_expected.to belong_to(:customer_quote) }
		it { is_expected.to have_many(:external_documents) }
		it { is_expected.to have_many(:on_hold_charges) }
		it { is_expected.to have_one(:team_order_detail) }
		it { is_expected.to have_many(:team_order_levels) }
		it { is_expected.to have_many(:team_order_attendees) }
		it { is_expected.to have_many(:event_attendees) }
		it { is_expected.to have_many(:team_supplier_profiles) }

		it { is_expected.to have_one(:associated_woolworths_order) }
		it { is_expected.to have_one(:woolworths_order) }
		it { is_expected.to have_one(:woolworths_account) }
		it { is_expected.to have_one(:attached_woolworths_account) }
		it { is_expected.to have_many(:woolworths_products) }
		it { is_expected.to have_many(:synced_woolworths_products) }
		it { is_expected.to have_many(:synced_woolworths_order_lines) }

		it { is_expected.to have_many(:dear_sales) }
	end

	context 'delegated methods' do
		describe '.po_number' do
			let(:customer_purchase_order) { create(:customer_purchase_order, :random) }
			let!(:order) { create(:order, :random, customer_purchase_order: customer_purchase_order) }

			it 'delegates the po number to the attached customer_purchase_order' do
				expect(order.po_number).to eq(customer_purchase_order.po_number)
			end

			it 'returns nil if not attached to any customer_purchase_order' do
				order.update_column(:cpo_id, nil)
				expect(order.po_number).to be_nil
			end
		end

		describe '.gst_free_po_number' do
			let(:gst_free_customer_purchase_order) { create(:customer_purchase_order, :random) }
			let!(:order) { create(:order, :random, gst_free_customer_purchase_order: gst_free_customer_purchase_order) }

			it 'delegates the po number to the attached gst_free_customer_purchase_order' do
				expect(order.gst_free_po_number).to eq(gst_free_customer_purchase_order.po_number)
			end

			it 'returns nil if not attached to any gst_free_customer_purchase_order' do
				order.update_column(:gst_free_cpo_id, nil)
				expect(order.gst_free_po_number).to be_nil
			end
		end

		describe '.symbolized_country_code' do
			let(:suburb) { create(:suburb, :random) }
			let!(:order) { create(:order, :random, delivery_suburb: suburb) }

			it 'delegates the symbolized_country_code to the attached delivery suburb' do
				expect(order.symbolized_country_code).to eq(suburb.symbolized_country_code)
			end

			it 'returns nil if not attached to any delivery suburb' do
				order.update_column(:delivery_suburb_id, nil)
				expect(order.symbolized_country_code).to be_nil
			end
		end
	end

	context 'order variant based boolean instance method' do
		let!(:order) { build(:order, :random) }

		describe '.is_team_order?' do
			it 'recognises a team order' do
				order.order_variant = %w[team_order recurring_team_order].sample
				expect(order.is_team_order?).to be_truthy

				order.order_variant = (Order::VALID_ORDER_VARIANTS - %w[team_order recurring_team_order]).sample
				expect(order.is_team_order?).to be_falsey
			end
		end

		describe '.is_recurring_team_order?' do
			it 'recognises a recurring team order' do
				order.order_variant = 'recurring_team_order'
				expect(order.is_recurring_team_order?).to be_truthy

				order.order_variant = (Order::VALID_ORDER_VARIANTS - ['recurring_team_order']).sample
				expect(order.is_recurring_team_order?).to be_falsey
			end
		end

		describe '.is_event_order?', custom_orders: true do
			it 'recognises an event (custom) order' do
				order.order_variant = 'event_order'
				expect(order.is_event_order?).to be_truthy

				order.order_variant = (Order::VALID_ORDER_VARIANTS - ['event_order']).sample
				expect(order.is_event_order?).to be_falsey
			end
		end

		describe '.is_home_delivery?' do
			it 'recognises a home delivery order' do
				order.order_variant = 'home_delivery'
				expect(order.is_home_delivery?).to be_truthy

				order.order_variant = (Order::VALID_ORDER_VARIANTS - ['home_delivery']).sample
				expect(order.is_home_delivery?).to be_falsey
			end
		end
	end

	context 'order type based boolean instance method' do
		let!(:order) { build(:order, :random) }

		describe '.is_recurrent?' do
			it 'recognises a recurrent order' do
				order.order_type = 'recurrent'
				expect(order.is_recurrent?).to be_truthy

				order.order_type = (Order::VALID_ORDER_TYPES - ['recurrent']).sample
				expect(order.is_recurrent?).to be_falsey
			end
		end
	end

	context 'delivery type based boolean instance method' do
		let!(:order) { build(:order, :random) }

		describe '.is_contactless_delivery?' do
			it 'recognises a contactless delivery order' do
				order.delivery_type = 'contactless'
				expect(order.is_contactless_delivery?).to be_truthy

				order.delivery_type = (Order::VALID_DELIVERY_TYPES - ['contactless']).sample
				expect(order.is_contactless_delivery?).to be_falsey
			end
		end
	end

	describe '.has_gst_split_pos?' do
		let!(:order) { create(:order, :random) }

		let!(:gst_po) { create(:customer_purchase_order, :random) }
		let!(:gst_free_po) { create(:customer_purchase_order, :random) }

		it 'returns false if order does not have any POs attached' do
			expect(order.has_gst_split_pos?).to be_falsey
		end

		it 'returns true if the order has 2 POs attached' do
			order.update_columns(cpo_id: gst_po.id, gst_free_cpo_id: gst_free_po.id)

			expect(order.has_gst_split_pos?).to be_truthy
		end

		it 'returns false if order only has one PO' do
			if [true, false].sample
				order.update_column(:cpo_id, [gst_po.id, gst_free_po.id].sample)
			else
				order.update_column(:gst_free_cpo_id, [gst_po.id, gst_free_po.id].sample)
			end

			expect(order.has_gst_split_pos?).to be_falsey
		end
	end

	context 'Woolworths related instance methods', woolworths: true do
		let!(:order) { create(:order, :confirmed) }
		let(:woolworths_supplier) { create(:supplier_profile, :random) }
		before do
			# stubs for supplier creation
			allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(Time.zone.now.beginning_of_month)
			allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(Time.zone.now.end_of_month)

			allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(woolworths_supplier.id)
		end

		describe '.is_woolworths_order?' do
			context 'with an associated woolworths_order record' do
				let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }

				it 'recognises order as Woolworths order' do
					expect(order.is_woolworths_order?).to be_truthy
				end
			end

			context 'without an associated woolworths_order record' do
				it 'doesn\'t recognies the order as Woolworths Order' do
					expect(order.is_woolworths_order?).to_not be_truthy
				end

				it 'recognises an order has a Woolworths Order if has items (order_lines) belonging to Woolworths' do
					create(:order_line, :random, order: order, supplier_profile: woolworths_supplier) # create woolworths order line
					order.reload
					expect(order.is_woolworths_order?).to be_truthy
				end
			end
		end

		describe '.has_woolworths_items?' do
			let!(:normal_supplier) { create(:supplier_profile, :random) }
			let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: normal_supplier) }
			let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: woolworths_supplier) }

			before do
				order.reload
			end

			it 'recognises an order has Woolworths Items if any order_lines belong to Woolworths' do
				expect(order.has_woolworths_items?).to be_truthy
			end

			it 'recognises an order as NOT having Woolworths Items if no order_lines belong to Woolworths' do
				order_line2.update_column(:supplier_profile_id, normal_supplier.id)
				expect(order.has_woolworths_items?).to_not be_truthy
			end
		end

		describe '.woolworths_order_name' do
			it 'returns the orders name by default' do
				expect(order.woolworths_order_name).to eq(order.name)
			end

			context 'with an associated woolworths order' do
				let!(:woolworths_account) { create(:woolworths_account, :random, email: "woolworths#{rand(1..16).to_i}@woolworths.com.au") }
				let!(:woolworths_order) { create(:woolworths_order, :random, order: order, account: woolworths_account) }

				it 'adds the woolworths accounts number to the name' do
					expect(order.woolworths_order_name).to include(" - #{woolworths_account.short_name}")
				end

				it 'adds the woolworths order ID to the name' do
					woolworths_order.update_column(:woolworths_order_id, SecureRandom.hex(7))
					expect(order.woolworths_order_name).to include(" - Woolworths ID: ##{woolworths_order.woolworths_order_id}")
				end

				it 'returns the updated Woolworths Order ID if the name already contains Woolworths ID' do
					order.update_column(:name, "#{order.name} (Woolworths ID: #EXISTING ORDER NAME)")

					expect(order.woolworths_order_name).to_not eq(order.name)
					expect(order.woolworths_order_name).to include("Woolworths ID: ##{woolworths_order.woolworths_order_id}")
				end
			end
		end
	end

	context 'Dear Supplier Order', dear: true do
		let!(:order) { create(:order, :confirmed) }
		let!(:dear_supplier) { create(:supplier_profile, :random, :with_dear_account) }
		let!(:normal_supplier) { create(:supplier_profile, :random) }

		let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: normal_supplier) }
		let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: dear_supplier) }

		describe '.dear_suppliers' do
			it 'returns list of supplies within the order with an active dear account' do
				expect(order.dear_suppliers).to include(dear_supplier)
			end

			it 'returns empty if none of the the order\'s order lines belong to a Supplier with an active Dear Account' do
				if [true, false].sample
					order_line2.update_column(:supplier_profile_id, normal_supplier.id)
				else
					dear_supplier.dear_account.update_column(:active, false)
				end
				expect(order.dear_suppliers).to be_empty
			end
		end

		describe '.is_dear_order?' do
			it 'returns true if the order has order lines belonging to a Supplier with a Dear Account' do
				expect(order.is_dear_order?).to be_truthy
			end

			it 'return true even if no Dear Supplier order lines are present but a dear sale is attached to the order' do
				order_line2.update_column(:supplier_profile_id, normal_supplier.id)
				create(:dear_sale, :random, order: order) # create dear sale

				expect(order.is_dear_order?).to be_truthy
			end
		end
	end

	context 'invoice order grouping instance methods', invoices: true do
		let!(:order) { create(:order, :confirmed) }

		describe '.address_label' do
			let!(:suburb) { create(:suburb, :darlinghurst) }

			before do
				order.update_columns(delivery_suburb_id: suburb.id, delivery_address: 'East street')
			end

			it 'is formatted correctly when there is a delivery address' do
				expect(order.address_label).to eq('East street - Darlinghurst, NSW 2010')
			end

			it 'returns delivery address when there is no suburb' do
				order.update_column(:delivery_suburb_id, nil)
				expect(order.address_label).to eq('East street')
			end
		end

		describe '.address_po_label' do
			let!(:suburb) { create(:suburb, :darlinghurst) }
			before do
				order.update_columns(delivery_suburb_id: suburb.id)
			end

			it 'returns address label by default' do
				expect(order.address_po_label).to eq(order.address_label)
			end

			it 'returns with PO label if PO is present' do
				customer_purchase_order = create(:customer_purchase_order, :random, customer_profile: order.customer_profile)
				order.update_column(:cpo_id, customer_purchase_order.id)

				expect(order.address_po_label).to eq(order.address_label + " | PO ##{order.po_number}")
			end
		end

		describe '.address_po_label' do
			let!(:suburb) { create(:suburb, :darlinghurst) }
			before do
				order.update_columns(delivery_suburb_id: suburb.id)
			end

			it 'returns address label by default' do
				expect(order.address_po_label).to eq(order.address_label)
			end

			it 'returns with PO label if PO is present' do
				customer_purchase_order = create(:customer_purchase_order, :random, customer_profile: order.customer_profile)
				order.update_column(:cpo_id, customer_purchase_order.id)

				expect(order.address_po_label).to eq(order.address_label + " | PO ##{order.po_number}")
			end
		end

		describe '.delivery_date' do
			it 'returns delivery_at as a date' do
				expect(order.delivery_date.to_s).to eq(order.delivery_at.to_date.to_s)
			end
		end
	end

	describe '.template_delivery_at' do
		let!(:delivery_at) { Time.zone.now + 2.weeks }
		let!(:one_off_order) { create(:order, :new, delivery_at: delivery_at) }

		let!(:template_order) { create(:order, :new, delivery_at: delivery_at) }
		let!(:recurrent_order) { create(:order, :new, delivery_at: delivery_at + 1.week) }

		before do
			[template_order, recurrent_order].each do |order|
				order.update_columns(order_type: 'recurrent', pattern: '1.week', recurrent_id: template_order.id, template_id: template_order.id)
			end
		end

		it 'returns nil for one off order' do
			expect(one_off_order.template_delivery_at).to be_blank
		end

		it 'returns parent template\'s delivery_at for recurrent order' do
			expect(recurrent_order.template_delivery_at.to_s).to eq(template_order.delivery_at.to_s)
		end

		it 'returns falls back to current order\'s date for recurrent order' do
			recurrent_order.update_column(:template_id, nil)
			expect(recurrent_order.template_delivery_at.to_s).to eq(recurrent_order.delivery_at.to_s)
		end
	end

	context 'as a team order' do
		let!(:team_order) { create(:order, :team_order) }

		it 'has a valid team order detail record' do
			expect(team_order.team_order_detail).to be_present
			expect(team_order.team_order_detail).to be_valid
		end

		it 'is marked as a team order' do
			expect(team_order).to be_is_team_order
		end

		%i[hide_budget cutoff_option attendee_pays package_id is_package_order?].each do |field|
			it "delegates #{field} to team order details" do
				expect(team_order.send(field)).to eq(team_order.team_order_detail.send(field))
			end
		end

		it 'is marked as a recurring team order if order variant is recurring_team_order' do
			team_order.order_variant = 'recurring_team_order'
			expect(team_order).to be_is_recurring_team_order
			expect(team_order).to be_is_team_order # still a team order
		end
	end

end
