require 'rails_helper'

RSpec.describe Payment, type: :model do

	describe 'validations' do
		it { is_expected.to validate_presence_of :order_id }
		it { is_expected.to validate_presence_of :amount }
		it { is_expected.to validate_presence_of :credit_card_id }

		it 'validates presence of user_id if attendee_id is blank' do
			payment = build(:payment, :random, attendee_id: nil)
			expect(payment).to be_valid

			payment.user_id = nil
			expect(payment).to_not be_valid
		end

		it 'validates presence of invoice id if user_id is present' do
			payment = build(:payment, :random)
			expect(payment).to be_valid

			payment.invoice_id = nil
			expect(payment).to_not be_valid
		end

		it 'validates presence of attendee_id if user_id is blank' do
			team_order_attendee = create(:team_order_attendee, :random)
			payment = build(:payment, :random, user_id: nil, attendee_id: team_order_attendee.id)
			expect(payment).to be_valid

			payment.attendee_id = nil
			expect(payment).to_not be_valid
		end
	end

	describe 'associations' do
		it { is_expected.to belong_to(:invoice) }
		it { is_expected.to belong_to(:order) }
		it { is_expected.to belong_to(:credit_card) }
  	it { is_expected.to belong_to(:user) }
	end

end
