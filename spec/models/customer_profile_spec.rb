require 'rails_helper'

RSpec.describe CustomerProfile, type: :model, customers: true do

	context 'validations' do
		it { is_expected.to validate_inclusion_of(:role).in_array(CustomerProfile::VALID_ROLES) }

		it 'has default values' do # from database migrations
			customer = CustomerProfile.new

			default_falsey_fields = %i[team_admin company_team_admin]
			default_falsey_fields.each do |falsey_field|
				expect(customer.send(falsey_field)).to be_falsey
			end
		end

		it 'has a valid factory' do
			factory_customer = build(:customer_profile, :random)

			expect(factory_customer).to be_valid
		end
	end

	context 'associations' do
		it { is_expected.to have_one(:profile) }
		it { is_expected.to have_one(:user) }
		it { is_expected.to have_one(:billing_details) }
		it { is_expected.to have_one(:customer_flags) }
		it { is_expected.to have_one(:staff_details) }
		it { is_expected.to have_many(:customer_purchase_orders) }
		it { is_expected.to have_many(:saved_addresses) }
		it { is_expected.to have_many(:employee_surveys) }
		it { is_expected.to have_many(:loading_docks) }
		it { is_expected.to have_many(:meal_plans) }
		it { is_expected.to have_many(:orders) }
		it { is_expected.to have_many(:team_orders) }
		it { is_expected.to have_many(:team_order_attendees) }
		it { is_expected.to have_many(:pantry_manager_orders) }
		it { is_expected.to have_many(:invoices) }
		it { is_expected.to belong_to(:company) }
		it { is_expected.to have_and_belong_to_many(:credit_cards) }

		it { is_expected.to have_many(:admin_access_permissions) }
		it { is_expected.to have_many(:active_admin_access_permissions) }
		it { is_expected.to have_many(:active_adminable_customer_profiles) }
		it { is_expected.to have_many(:access_permissions_as_customer) }

		it { is_expected.to have_many(:notification_preferences) }
		it { is_expected.to have_many(:report_sources) }
		it { is_expected.to have_many(:budgets) }

		it { is_expected.to have_many(:documents) }
		it { is_expected.to have_many(:quotes) }
		it { is_expected.to have_many(:delivery_overrides) }
		it { is_expected.to have_many(:supplier_markup_overrides) }
		it { is_expected.to have_many(:promotion_subscriptions) }

		it { is_expected.to have_many(:favourite_supplier_records) }
		it { is_expected.to have_many(:favourite_suppliers) }

		it { is_expected.to have_many(:favourite_team_supplier_records) }
		it { is_expected.to have_many(:favourite_team_suppliers) }
	end

	context 'with billing details' do
		let!(:customer) { create(:customer_profile, :random) }
		let!(:billing_details) { create(:billing_details, :random, customer_profile: customer) }

		it 'delegates some of the billing detail methods to customer profile' do
			expect(customer.billing_frequency).to eq(billing_details.frequency)

			CustomerProfile::BILLING_NEEDS_FIELDS.each do |field|
				expect(customer.send("needs_#{field}")).to eq(billing_details.send(field))
			end

			CustomerProfile::BILLING_FLAG_FIELDS.each do |field|
				expect(customer.send(field)).to eq(billing_details.send(field))
			end
		end

		it 'returns nil for delegated billing detail methods for a customer without any billing_details' do
			customer2 = create(:customer_profile, :random)

			expect(customer2.billing_details).to be_blank
			expect(customer2.billing_frequency).to be_nil

			CustomerProfile::BILLING_NEEDS_FIELDS.each do |field|
				expect(customer2.send("needs_#{field}")).to be_nil
			end

			CustomerProfile::BILLING_FLAG_FIELDS.each do |field|
				expect(customer2.send(field)).to be_nil
			end
		end
	end

	context 'delegated customer_flags methods' do
		let!(:customer) { create(:customer_profile, :random) }
		let!(:customer_flags) { create(:customer_flags, customer_profile: customer) }

		CustomerProfile::CUSTOMER_FLAG_FIELDS.each do |field|
			it "delegates `#{field}` to customer flags" do
				field_value = [true, false].sample

				customer_flags.update_column(field, field_value)
				expect(customer.send(field)).to eq(customer_flags.send(field))
			end
		end
	end # delegated customer_flags

	context 'with attached user' do
		let!(:customer) { create(:customer_profile, :random, :with_user) }
		let!(:user) { customer.reload.user }

		describe '.name' do
			it 'returns customer\'s user name when there is one' do
				expect(customer.name).to eq(user.name)
			end

			it 'does not throw error when customers don\'t have a user name' do
				user.destroy
				customer.reload

				expect { customer.name }.to_not raise_error
				expect(customer.name).to eq('A customer')
			end
		end

		context 'delegated methods' do
			it 'returns the email of the attached user' do
				expect(customer.email).to eq(user.email)
			end

			it 'returns the suburb of the attached user' do
				suburb = create(:suburb, :random)
				user.update_column(:suburb_id, suburb.id)
				expect(customer.suburb).to eq(user.suburb)
				expect(customer.suburb).to eq(suburb)
			end
		end

		describe '.country_of_origin' do
			let!(:suburb) { create(:suburb, :random, country_code: 'NZ') }
			before do
				user.update_column(:suburb_id, suburb.id)
			end

			it 'returns the country_of_origin of the attached user' do
				expect(customer.country_of_origin).to eq(user.country_of_origin)
			end

			it 'defaults country of origin to `AU` for a customer without an attached user' do
				user.destroy
				customer.reload

				expect { customer.country_of_origin }.to_not raise_error
				expect(customer.country_of_origin).to eq('AU')
			end
		end
	end

	describe '.email_recipient' do
		it 'returns customer\'s user email address if it exists' do
			customer = create(:customer_profile, :random, :with_user)
			user = customer.reload.user

			expect(customer.email_recipient).to eq(user.email_recipient)
		end

		it 'returns "admin" as email recipient if user doesn\'t exist' do
			allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :admin_email).and_return('<EMAIL>')
			customer = create(:customer_profile, :random)
			expect(customer.email_recipient).to eq('<EMAIL>')
		end

		describe '.customer_or_company_name' do
			let!(:customer) { create(:customer_profile, :random, :with_user) }
			let!(:user) { customer.reload.user }
			let!(:company) { create(:company, :random) }

			it 'returns customer\'s name when not belong to a company or has no company_name' do
				expect(customer.customer_or_company_name).to eq(user.name)
			end
			it 'returns customer\'s company name when not belong to a company' do
				customer.update_column(:company_name, 'saved company name')
				expect(customer.customer_or_company_name).to eq('saved company name')
			end

			it 'returns the name of the company that the customer belongs to' do
				customer.company = company
				customer.save!
				expect(customer.customer_or_company_name).to eq(company.name)
			end
		end
	end

	describe '.email_salutation' do
		it 'returns customer\'s first name as salutation' do
			user = create(:user, :confirmed, :as_customer, firstname: 'Rabbit')
			customer = user.reload.profile.profileable

			# should derive name from the user
			expect(customer.email_salutation).to eq('Rabbit')
		end

		it 'doesn\'t throw err, if customer doesn\'t have an app user' do
			customer = create(:customer_profile, :random)

			expect { customer.email_salutation }.to_not raise_error
			expect(customer.email_salutation).to eq('there')
		end
	end

	describe '.invoice_account_software' do
		let!(:customer_accounting_software) { CustomerFlags::VALID_ACCOUNTING_SOFTWARES.sample }
		let!(:customer) { create(:customer_profile, :random, :with_flags) }

		before do
			customer.customer_flags.update_column(:accounting_software, customer_accounting_software)
		end

		it 'returns the accounting software of the customer' do
			expect(customer.invoice_accounting_software).to eq(customer_accounting_software)
		end

		context 'with Company accounting software' do
		  let!(:company_accounting_software) { (CustomerFlags::VALID_ACCOUNTING_SOFTWARES - [customer_accounting_software]).sample }
		  let!(:company) { create(:company, :random, customer_profiles: [customer], accounting_software: company_accounting_software) }

		  before do
		    customer.reload
		  end

		  it 'it still returns customer accounting software' do
		  	expect(customer.invoice_accounting_software).to eq(customer_accounting_software)
		  end

		  it 'returns company accounting software if customer accounting software is blank' do
		  	customer.customer_flags.update_column(:accounting_software, [nil, ''].sample)
		  	expect(customer.invoice_accounting_software).to eq(company_accounting_software)
		  end

		  it 'returns blank if customer accounting software is set to none' do
		  	customer.customer_flags.update_column(:accounting_software, 'none')
		  	expect(customer.invoice_accounting_software).to be_blank
		  end
		end
	end # invoice_accounting_software

	context 'callbacks', callbacks: true do
		let!(:customer) { create(:customer_profile, :random) }

		context 'after_save: welcome_team_admin' do
			before do
				email_sender = delayed_email_sender = double(Customers::Emails::SendTeamAdminWelcomeEmail)
				allow(Customers::Emails::SendTeamAdminWelcomeEmail).to receive(:new).and_return(email_sender)
				allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
				allow(delayed_email_sender).to receive(:call).and_return(true)
			end

			it 'sends a Welcome Team Admin email if the customer is set as a team admin' do
				expect(Customers::Emails::SendTeamAdminWelcomeEmail).to receive(:new).with(customer: customer)

				customer.update(team_admin: true)
			end

			it 'sends email only if the team_admin flag is changed' do
				customer.update_column(:team_admin, true) # set flag to true in db avoiding callback

				expect(Customers::Emails::SendTeamAdminWelcomeEmail).to_not receive(:new).with(customer: customer)

				customer.update(role: CustomerProfile::VALID_ROLES.sample)
			end

			it 'does\'t send a Welcome Team Admin email if the customer is un-set as a team admin' do
				expect(Customers::Emails::SendTeamAdminWelcomeEmail).to_not receive(:new)

				customer.update(team_admin: false)
			end

			it 'doesn\'t send a Welcome email if the email is already sent' do
				create(:email, :random, fk_id: customer.id, ref: "customer-team_admin_welcome-#{customer.id}") # create existing email
				expect(Customers::Emails::SendTeamAdminWelcomeEmail).to_not receive(:new)

				customer.update(team_admin: true)
			end

			it 'doesn\'t send Welcome email if customer is un-set to be a team admin' do
				customer.update_column(:team_admin, true) # set flag to true in db avoiding callback

				expect(Customers::Emails::SendTeamAdminWelcomeEmail).to_not receive(:new)

				customer.update(team_admin: false)
			end
		end

		context 'after_save: welcome_company_team_admin' do
			before do
				email_sender = delayed_email_sender = double(Customers::Emails::SendCompanyTeamAdminWelcomeEmail)
				allow(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to receive(:new).and_return(email_sender)
				allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
				allow(delayed_email_sender).to receive(:call).and_return(true)
			end

			it 'sends a Welcome Company Team Admin email if the customer is set as a team admin' do
				expect(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to receive(:new).with(customer: customer)

				customer.update(company_team_admin: true)
			end

			it 'sends email only if the company_team_admin flag is changed' do
				customer.update_column(:company_team_admin, true) # set flag to true in db avoiding callback

				expect(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to_not receive(:new).with(customer: customer)

				customer.update(role: CustomerProfile::VALID_ROLES.sample)
			end

			it 'does\'t send a Welcome Company Team Admin email if the customer is un-set as a team admin' do
				expect(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to_not receive(:new)

				customer.update(company_team_admin: false)
			end

			it 'doesn\'t send a Welcome email if the email is already sent' do
				create(:email, :random, fk_id: customer.id, ref: "customer-company_team_admin_welcome-#{customer.id}") # create existing email
				expect(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to_not receive(:new)

				customer.update(company_team_admin: true)
			end

			it 'doesn\'t send a Welcome email if customer is setup with pantry manager or account manager admin access permissions' do
				create(:access_permission, :random, admin: customer, scope: %w[pantry_manager account_manager].sample) # create a pantry manager/account manager access permission
				expect(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to_not receive(:new)

				customer.update(company_team_admin: true)
			end

			it 'doesn\'t send Welcome email if customer is un-set to be a company team admin' do
				customer.update_column(:company_team_admin, true) # set flag to true in db avoiding callback

				expect(Customers::Emails::SendCompanyTeamAdminWelcomeEmail).to_not receive(:new)

				customer.update(company_team_admin: false)
			end
		end

		context 'after_save: sync_contact_to_hubspot' do
			let!(:customer) { create(:customer_profile, :random, :with_user, company_team_admin: false) }

			before do
				hubspot_syncer = delayed_hubspot_syncer = double(Hubspot::SyncContact)
				allow(Hubspot::SyncContact).to receive(:new).and_return(hubspot_syncer)
				allow(hubspot_syncer).to receive(:delay).and_return(delayed_hubspot_syncer)
				allow(delayed_hubspot_syncer).to receive(:call).and_return(true)
			end

			it 'requests to sync customer\'s contact (user) with Hubspot if company_team_admin is changed' do
				expect(Hubspot::SyncContact).to receive(:new).with(contact: customer.user)

				customer.update(company_team_admin: true)
			end

			it 'does not sync customer\'s contact (user) with Hubspot if the company team admin is not changed' do
				expect(Customers::Emails::SendTeamAdminWelcomeEmail).to_not receive(:new).with(contact: customer.user)

				customer.update(customer_name: Faker::Name.name)
			end

			it 'does not sync customer\'s contact (user) with Hubspot if the contact is missing' do
				customer.user.destroy

				expect(Customers::Emails::SendTeamAdminWelcomeEmail).to_not receive(:new).with(contact: customer.user)

				customer.update(company_team_admin: true)
			end

			it 'does not sync customer\'s contact (user) with Hubspot if a delayed job already exists' do
				handler_data = "object:Hubspot::SyncContact - value_before_type_cast: #{customer.user.id} - method_name: :call"
				Delayed::Job.create(handler: handler_data, locked_at: nil, failed_at: nil)

				expect(Hubspot::SyncContact).to_not receive(:new).with(contact: customer.user)

				customer.update(company_team_admin: true)
			end
		end
	end

end
