require 'rails_helper'

RSpec.describe CreditCard, type: :model do

	describe 'validations' do
		it { is_expected.to validate_presence_of(:name) }
	end

	describe 'associations' do
		it { is_expected.to have_and_belong_to_many(:customer_profiles) }
		it { is_expected.to belong_to(:event_attendee) }
	end

	describe '.expiry_label' do
		let!(:credit_card) { create(:credit_card, :valid_payment, expiry_year: '2015', expiry_month: '12') }

		it 'concatenates the month and year separated by a slash' do
			expect(credit_card.expiry_label).to eq('12/2015')
		end
	end

end
