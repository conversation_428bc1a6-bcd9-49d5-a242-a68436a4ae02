require 'rails_helper'

RSpec.describe <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, type: :model, employee_surveys: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:customer_profile) }
    it { is_expected.to validate_presence_of(:category_group) }
    it { is_expected.to validate_inclusion_of(:category_group).in_array(EmployeeSurvey::VALID_CATEGORY_GROUPS) }
    it { is_expected.to validate_uniqueness_of(:category_group).scoped_to(:customer_profile_id) }
    it { is_expected.to validate_presence_of(:uuid) }

    it 'has a valid factory' do
      factory_survey = build(:employee_survey, :random)
      expect(factory_survey).to be_valid
    end

    it 'has default values' do
      employee_survey = EmployeeSurvey.new

      expect(employee_survey.active).to be_truthy
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to have_many(:survey_questions) }
    it { is_expected.to have_many(:submissions) }
    it { is_expected.to have_many(:documents) }
  end

  context 'instance methods' do
    describe '.category_group_name' do
      it 'returns Catering if the category_group is catering-services' do
        factory_survey = build(:employee_survey, :random, category_group: 'catering-services')

        expect(factory_survey.category_group_name).to eq('Catering')
      end
      it 'returns Pantry if the category_group is kitchen-supplies' do
        factory_survey = build(:employee_survey, :random, category_group: 'kitchen-supplies')

        expect(factory_survey.category_group_name).to eq('Pantry')
      end
    end # .category_group_name
  end # instance methods

end
