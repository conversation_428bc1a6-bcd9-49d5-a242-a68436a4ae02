require 'rails_helper'

RSpec.describe SupplierClosure, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:supplier_profile_id) }
    it { is_expected.to validate_presence_of(:reason) }
    it { is_expected.to validate_presence_of(:starts_at) }
    it { is_expected.to validate_presence_of(:ends_at) }

    it 'has a valid factory' do
      factory_supplier_closure = build(:supplier_closure, :random)
      expect(factory_supplier_closure).to be_valid
    end

    it 'validates the closure ends only after it starts' do
      now = Time.zone.now

      invalid_supplier_closure = build(:supplier_closure, :random, starts_at: now, ends_at: now - rand(1..10).minutes)
      expect(invalid_supplier_closure).to_not be_valid

      valid_supplier_closure = build(:supplier_closure, :random, starts_at: now, ends_at: now + rand(1..10).minutes)
      expect(valid_supplier_closure).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:supplier_profile) }
  end

end
