require 'rails_helper'

RSpec.describe SupplierAgreementDocument, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to validate_presence_of(:supplier_profile) }
    it { is_expected.to validate_presence_of(:docusign_envelope_id) }

    it 'has a valid factory' do
      factory_document = build(:supplier_agreement_document, :random)
      expect(factory_document).to be_valid
    end

    it 'validates the uniqueness of the docusign_envelope_id' do
      existing_document = create(:supplier_agreement_document, :random)

      new_document = build(:supplier_agreement_document, :random, docusign_envelope_id: existing_document.docusign_envelope_id)
      expect(new_document).to_not be_valid
    end
  end

  context 'assocations' do
    it { is_expected.to belong_to(:supplier_profile) }
  end

end
