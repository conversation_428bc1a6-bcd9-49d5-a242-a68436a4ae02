require 'rails_helper'

RSpec.describe Category, type: :model do

	describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
		it { is_expected.to validate_inclusion_of(:group).in_array(Category::VALID_CATEGORY_GROUPS) }

    it 'has a valid factory' do
      factory_category = build(:category, :random)

      expect(factory_category).to be_valid
    end

    it 'has default values' do
      new_category = Category.new

      expect(new_category.is_generic).to be_falsey
    end
	end

	describe 'associations' do
		it { is_expected.to have_many(:category_menu_sections) }
		it { is_expected.to have_many(:menu_sections) }
    it { is_expected.to have_many(:orders) }
	end

  context 'callbacks', callbacks: true do
    it 'sets the group weight according to the group name' do
      category_group = Category::VALID_CATEGORY_GROUPS.sample
      category = create(:category, :random, group: category_group)

      expect(category.group_weight).to be_present
      expect(category.group_weight).to eq(Category::GROUP_WEIGHTS[category_group].presence || 99)
    end

    it 'sets the slug for the category based on its name' do
      category_name = Faker::Name.name
      category = create(:category, :random, name: category_name)

      expect(category.slug).to be_present
      expect(category.slug).to eq(category_name.downcase.gsub(/[^a-z1-9]+/, '-').chomp('-'))
    end
  end

  context 'scopes' do
    describe '.generic_category_for' do
      let!(:group_name) { Category::VALID_CATEGORY_GROUPS.sample }
      let!(:generic_category) { create(:category, :random, group: group_name, is_generic: true) }
      let!(:non_generic_category) { create(:category, :random, group: group_name, is_generic: false) }

      let!(:other_generic_category) { create(:category, :random, group: (Category::VALID_CATEGORY_GROUPS - [group_name]).sample, is_generic: true) }

      it 'returns the generic group within the group of categories' do
        returned_category = Category.generic_category_for(group_name: group_name)

        expect(returned_category).to eq(generic_category)
        expect(returned_category).to_not eq(non_generic_category)
        expect(returned_category).to_not eq(other_generic_category)
      end
    end
  end # scopes

end
