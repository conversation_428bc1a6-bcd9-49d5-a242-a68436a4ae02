require 'rails_helper'

RSpec.describe AccessPermission, type: :model do

  context 'validation' do
    it { is_expected.to validate_presence_of(:admin) }
    it { is_expected.to validate_presence_of(:customer_profile) }
    it { is_expected.to validate_inclusion_of(:scope).in_array(AccessPermission::VALID_SCOPES).allow_nil }

    it 'has a valid factory' do
      factory_access_permission = build(:access_permission, :random)
      expect(factory_access_permission).to be_valid
    end

    it 'is active by default' do
      access_permission = AccessPermission.new
      expect(access_permission).to be_active
    end

    # it { is_expected.to validate_uniqueness_of(:customer_profile).scoped_to(:admin) }
    it 'has a unique admin vs customer profile combination' do
      existing_access_permissions = create(:access_permission, :random)

      new_access_permissions = build(:access_permission, :random, admin: existing_access_permissions.admin, customer_profile: existing_access_permissions.customer_profile)
      expect(new_access_permissions).to_not be_valid
    end
  end

  context 'association' do
    it { is_expected.to belong_to(:admin) }
    it { is_expected.to belong_to(:customer_profile) }
  end

end
