require 'rails_helper'

RSpec.describe DeliverableSuburb, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:delivery_zone) }
    it { is_expected.to validate_presence_of(:supplier_profile) }
    it { is_expected.to validate_presence_of(:suburb) }

    it 'has a valid factoryy' do
      factory_deliverable_suburb = build(:deliverable_suburb, :random)
      expect(factory_deliverable_suburb).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:delivery_zone) }
    it { is_expected.to belong_to(:supplier_profile) }
    it { is_expected.to belong_to(:suburb) }
  end

end
