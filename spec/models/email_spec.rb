require 'rails_helper'

RSpec.describe Email, type: :model, notifications: true do

  describe 'validations' do
    it { is_expected.to validate_presence_of(:fk_id) }
    it { is_expected.to validate_presence_of(:template_name) }
    it { is_expected.to validate_inclusion_of(:template_name).in_array(EmailTemplate::VALID_TEMPLATE_NAMES) }

    it 'has a valid factory' do
      factory_email = build(:email, :random)
      expect(factory_email).to be_valid
    end
  end

  context 'class methods' do
    describe '.view_template' do

      it 'returns the view template name (with folder path)' do
        template_name = EmailTemplate::VALID_TEMPLATE_NAMES.select{|valid_name| valid_name.include?('customer-') }.sample
        view_template = Email.view_template(template_name)

        name_split = template_name.split('-')
        expect(view_template).to eq("emails/#{name_split[0]}/#{name_split[1..].join('_')}")
      end

      it 'returns the view template name (with folder path) for a team order (admin/attendee) template' do
        template_name = EmailTemplate::VALID_TEMPLATE_NAMES.select{|valid_name| valid_name.include?('team-order-admin') || valid_name.include?('team-order-attendee') }.sample
        view_template = Email.view_template(template_name)

        sanitized_template_name = template_name.gsub('team-order-', 'team_order-')
        name_split = sanitized_template_name.split('-')
        expect(view_template).to eq("emails/#{name_split[0]}/#{name_split[1..].join('_')}")
      end

      it 'returns nil if template_name is not a valid email template' do
        view_template = Email.view_template('invalid-template-name')

        expect(view_template).to be_nil
      end

    end
  end

  context 'instance methods' do
    describe '.subject' do
      it 'retrieves the subject from the details' do
        email = create(:email, :random, :with_details)

        expect(email.subject).to eq(email.details['subject'])
      end
    end
  end
end
