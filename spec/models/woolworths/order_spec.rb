require 'rails_helper'

RSpec.describe Woolworths::Order, type: :model, woolworths: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:order) }

    it 'has a valid factory' do
      factory_woolworths_order = build(:woolworths_order, :random)
      expect(factory_woolworths_order).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:order) }
    it { is_expected.to belong_to(:account) }
  end

end
