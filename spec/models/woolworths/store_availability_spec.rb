require 'rails_helper'

RSpec.describe Woolworths::StoreAvailability, type: :model, woolworths: true do

  context 'validations' do
    %i[menu_item store_id stock_quantity].each do |field|
      it { is_expected.to validate_presence_of(field) }
    end

    it { is_expected.to validate_inclusion_of(:store_id).in_array(Rails.configuration.woolworths.fulfilment_stores.mapped_stores.keys).with_message('is not a recognized Woolworths store ID') }
    it { is_expected.to validate_numericality_of(:stock_quantity).only_integer.is_greater_than(0) }

    # it { is_expected.to validate_uniqueness_of(:menu_item).scoped_to(:store_id).with_message('can only have one availability record per Woolworths store') }
    it 'cannot have duplicate store availabilty for a menu_item scope within a store id' do
      existing_store_availability = create(:woolworths_store_availability, :random)
      new_store_availability = build(:woolworths_store_availability, menu_item: existing_store_availability.menu_item, store_id: existing_store_availability.store_id)

      expect(new_store_availability).to_not be_valid
    end

    it 'has a valid factory' do
      factory_store_availability = build(:woolworths_store_availability, :random)
      expect(factory_store_availability).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:menu_item) }
    it { is_expected.to have_one(:menu_section) }
  end

end
