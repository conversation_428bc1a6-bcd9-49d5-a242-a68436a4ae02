require 'rails_helper'

RSpec.describe Woolworths::TrolleyProduct, type: :model, woolworths: true do

  context 'validations' do
    it 'has a valid factory' do
      factory_trolley_product = build(:woolworths_trolley_product, :random)
      expect(factory_trolley_product).to be_valid
    end
  end

  context 'assocations' do
    it { is_expected.to belong_to(:woolworths_order) }
    it { is_expected.to belong_to(:order_line) }
  end

end
