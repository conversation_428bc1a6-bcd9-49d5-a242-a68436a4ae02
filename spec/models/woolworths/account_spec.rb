require 'rails_helper'

RSpec.describe Woolworths::Account, type: :model, woolworths: true do

  describe 'validations' do

    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_presence_of(:password) }

    it 'has a valid factory' do
      factory_woolworths_account = build(:woolworths_account, :random)
      expect(factory_woolworths_account).to be_valid
    end
  end

  describe 'associations' do
    it { is_expected.to have_many(:woolworths_orders) }
    it { is_expected.to have_many(:orders) }
  end

  context 'instance methods' do
    let!(:woolworths_account) { create(:woolworths_account, :random) }

    describe '.short_name' do
      it 'returns the numbered email account' do
        account_number = rand(1..15)
        woolworths_account.email = "woolworths#{account_number}@email.com"
        expect(woolworths_account.short_name).to eq("W#{account_number}")
      end

      it 'return the actual email address for non-numbered email' do
        woolworths_account.email = '<EMAIL>'
        expect(woolworths_account.short_name).to eq('non-numbered')
      end
    end
  end

end
