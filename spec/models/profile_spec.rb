require 'rails_helper'

RSpec.describe Profile, type: :model do

	describe 'validations' do
		it { is_expected.to validate_presence_of :user_id }
		it { is_expected.to validate_uniqueness_of :user_id }
	end

	describe 'associations' do
		it { is_expected.to belong_to(:user) }
		it { is_expected.to belong_to(:profileable) }
	end

	context 'profileable type' do
		let!(:customer) { create(:user, :confirmed, :as_customer) }
		let!(:supplier) { create(:user, :confirmed, :as_supplier) }

		before do
			# need to reload for the attached profiles to be refreshed
			customer.reload
			supplier.reload
		end

		describe '.is_customer?' do
			it 'true if the profile belongs to a customer' do
				expect(customer.profile.is_customer?).to be_truthy
			end

			it 'false if the profile doesn\'t belong to a customer' do
				expect(supplier.profile.is_customer?).to be_falsey
			end
		end

		describe '.is_supplier?' do
			it 'returns true if the profile belongs to a customer' do
				expect(supplier.profile.is_supplier?).to be_truthy
			end

			it 'false if the profile doesn\'t belong to a supplier' do
				expect(customer.profile.is_supplier?).to be_falsey
			end
		end
	end

end
