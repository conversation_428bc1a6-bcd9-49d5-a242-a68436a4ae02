require 'rails_helper'

RSpec.describe User, type: :model do

	context 'validations' do
		it { is_expected.to validate_inclusion_of(:gender).in_array(User::VALID_GENDERS).allow_nil }
		it { is_expected.to validate_presence_of(:firstname) }
		it { is_expected.to validate_inclusion_of(:business).in_array(User::VALID_BUSINESS_TYPES) }

		it 'has a valid factory' do
			factory_user = build(:user, :random)

			expect(factory_user).to be_valid
		end
	end

	context 'associations' do
		it { is_expected.to have_one(:profile) }
		it { is_expected.to have_many(:leads) }
		it { is_expected.to have_one(:customer_profile) }
		it { is_expected.to have_one(:supplier_profile) }
		it { is_expected.to belong_to(:suburb) }

		it { is_expected.to have_many(:favourite_customers) }
		it { is_expected.to have_many(:favourite_customer_profiles) }

		it { is_expected.to have_many(:event_log_views) }
		it { is_expected.to have_many(:assigned_event_logs) }

		# has_and_belongs_to_many :customer_profiles
		# has_and_belongs_to_many :supplier_profiles
	end

	context 'instance methods' do
		describe '.name' do
			let!(:user) { create(:user, :confirmed, firstname: 'first', lastname: 'last') }

			it 'returns the combination of firstname and lastname' do
				expect(user.name).to eq('first last')
			end
		end

		describe '.country_of_origin' do
			let!(:suburb) { create(:suburb, :random) }
			let!(:user) { create(:user, :confirmed, suburb: suburb) }

			it 'returns the country code of the attached suburb' do
				expect(user.country_of_origin).to eq(suburb.country_code)
			end

			it 'returns `AU` as the default country code if a suburb is not attached' do
				user.update(suburb: nil)

				expect(user.country_of_origin).to eq('AU')
			end
		end

		describe '.email_recipient' do
			let!(:user) { create(:user, :confirmed, firstname: 'first', lastname: 'last', email: '<EMAIL>') }

			it 'returns the combination of firstname, lastname and email' do
				expect(user.email_recipient).to eq('first last <<EMAIL>>')
			end
		end

		describe '.send_devise_notification' do
			before do
				reset_password_email_sender = double(Users::Emails::SendResetPasswordEmail)
				allow(Users::Emails::SendResetPasswordEmail).to receive(:new).and_return(reset_password_email_sender)
				allow(reset_password_email_sender).to receive(:call).and_return(true)

				confirmation_email_sender = double(Users::Emails::SendAccountConfirmationEmail)
				allow(Users::Emails::SendAccountConfirmationEmail).to receive(:new).and_return(confirmation_email_sender)
				allow(confirmation_email_sender).to receive(:call).and_return(true)
			end
			let!(:user) { create(:user, :confirmed) }

			it 'sends customised email when sending `password reset` is required' do
				expect(Users::Emails::SendResetPasswordEmail).to receive(:new).with(user: anything, token: anything)

				user.send_devise_notification(:reset_password_instructions)
			end

			it 'sends customised email when sending `confirmation instructions` is required' do
				expect(Users::Emails::SendAccountConfirmationEmail).to receive(:new).with(user: anything)

				user.send_devise_notification(:confirmation_instructions)
			end
		end
	end

	context 'model callbacks', callbacks: true do
		describe 'after_save: sync_user_with_lead' do
			let!(:lead) { create(:lead, lead_type: 'started_registration', email: Faker::Internet.email) }

			it 'syncs the lead for the associated email' do
				expect(lead.user_id).to be_blank

				user = build(:user, :random, email: lead.email)
				user.save

				expect(lead.reload.user_id).to eq(user.id)
			end
		end

		describe 'after_save: sync_contact_to_hubspot', hubspot: true do
			before do
				hubspot_syncer = delayed_hubspot_syncer = double(Hubspot::SyncContact)
				allow(Hubspot::SyncContact).to receive(:new).and_return(hubspot_syncer)
				allow(hubspot_syncer).to receive(:delay).and_return(delayed_hubspot_syncer)
				allow(delayed_hubspot_syncer).to receive(:call).and_return(true)
			end

			let(:user) { create(:user, :random) }

			it 'requests to sync contact with Hubspot on save' do
				expect(Hubspot::SyncContact).to receive(:new).with(contact: user)

				user.save
			end

			it 'does not sync contact with Hubspot if is just registered' do
				expect(Hubspot::SyncContact).to_not receive(:new).with(contact: user)

				user.just_registered = true
				user.save
			end

			it 'does not sync contact with Hubspot if a delayed job already exists' do
				handler_data = "object:Hubspot::SyncContact - value_before_type_cast: #{user.id} - method_name: :call"
				Delayed::Job.create(handler: handler_data, locked_at: nil, failed_at: nil)

				expect(Hubspot::SyncContact).to_not receive(:new).with(contact: user)

				user.save
			end
		end

		describe 'after_save: copy_name_to_customer' do
			let!(:customer) { create(:customer_profile, :random, :with_user) }
			let!(:user) { create(:user, :random) }

			before do
				customer.update(user: user) # attach user to customer
			end

			it 'syncs any changed customer name' do
				user.update(firstname: 'New First Name', lastname: 'New Last Name')

				customer.reload
				expect(customer.customer_name).to eq('New First Name New Last Name')
				expect(customer.name).to eq('New First Name New Last Name')
			end
		end
	end
end

