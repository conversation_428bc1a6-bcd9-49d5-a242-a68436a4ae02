require 'rails_helper'

RSpec.describe Location, type: :model do

	describe 'associations' do
		it { is_expected.to have_many(:order_lines) }
		it { is_expected.to belong_to(:order) }
	end

	describe '.label' do
		let!(:location) { create(:location, :random) }

		it 'returns the combination of details and notes when they both exist' do
			expect(location.label).to eq("#{location.details} - #{location.note}")
		end

		it 'only returns note if details are blank' do
			location.update_columns(details: nil, note: 'note')
			expect(location.label).to eq('note')
		end
	end

end
