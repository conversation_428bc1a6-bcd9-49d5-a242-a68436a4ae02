require 'rails_helper'

RSpec.describe SupplierProfile, type: :model, suppliers: true do

	describe '.validations' do
		it { is_expected.to validate_presence_of :company_name }
		it { is_expected.to validate_presence_of :commission_rate }

		it 'validates the email format' do
			['<EMAIL>', '<EMAIL>'].each do |valid_email|
				supplier = SupplierProfile.new(company_name: Faker::Name.name, email: valid_email)
				expect(supplier).to be_valid
			end

			['aa', 'aa@', '@bb'].each do |invalid_email|
				supplier = SupplierProfile.new(company_name: Faker::Name.name, email: invalid_email)
				expect(supplier).to_not be_valid
			end
		end

		it 'validates the abn_acn format', skip: 'commented validation' do
			['', '123', '1 2 3'].each do |abn_acn|
				supplier = SupplierProfile.new(company_name: Faker::Name.name, abn_acn: abn_acn)
				expect(supplier).to be_valid
			end

			['a', '@', '123a', '123 a 456'].each do |abn_acn|
				supplier = SupplierProfile.new(company_name: Faker::Name.name, abn_acn: abn_acn)
				expect(supplier).to_not be_valid
			end
		end
		it { is_expected.to validate_inclusion_of(:lead_mode).in_array(SupplierProfile::VALID_LEAD_MODES) }

		it 'has a valid factory' do
			factory_supplier = build(:supplier_profile, :random)

			expect(factory_supplier).to be_valid
		end

		context 'setting is_searchable' do
			let!(:supplier) { create(:supplier_profile, :random) }
			it 'lets supplier\'s is_searchable flag be set' do
				supplier.is_searchable = [true, false].sample

				expect(supplier).to be_valid
			end

			context 'with pending orders' do
				let!(:now) { Time.zone.now.beginning_of_week + 1.day + 10.hours }
				let!(:order) { create(:order, :random, delivery_at: now + 20.minutes, status: %w[pending quoted new amended confirmed].sample) }
				let!(:order_line) { create(:order_line, :random, order: order, supplier_profile: supplier) } # attach supplier to order

				it 'lets supplier\'s is_searchable flag be to true' do
					supplier.is_searchable = true

					expect(supplier).to be_valid
				end

				it 'does not let supplier\'s is_searchable flag be to false with pending future orders' do
					supplier.is_searchable = false

					expect(supplier).to_not be_valid

					is_searchable_errors = supplier.errors[:is_searchable]
					expect(is_searchable_errors).to be_present
					expect(is_searchable_errors).to include('cannot be set to false as the supplier still has pending future orders')
				end

				it 'lets supplier\'s is_searchable flag be to false if no pending future orders are present' do
					order.update_column(:delivery_at, now - 1.month)
					supplier.is_searchable = false

					expect(supplier).to be_valid
				end
			end
		end
	end # setting is_searchable

	context 'closure validation', skip: 'removed date validation' do
		it 'validates the closure dates to be between Yordar closure dates' do
			closure_start = Time.zone.now.beginning_of_month
			closure_end = Time.zone.now.end_of_month
			allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(closure_start)
			allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(closure_end)
			supplier = SupplierProfile.new(company_name: Faker::Name.name, close_from: closure_start, close_to: closure_end)
			expect(supplier).to be_valid

			supplier = SupplierProfile.new(company_name: Faker::Name.name, close_from: closure_start - 1.day, close_to: closure_end)
			expect(supplier).to be_valid

			supplier = SupplierProfile.new(company_name: Faker::Name.name, close_from: closure_start, close_to: closure_end + 1.day)
			expect(supplier).to be_valid

			supplier = SupplierProfile.new(company_name: Faker::Name.name, close_from: closure_start - 1.day, close_to: closure_end + 1.day)
			expect(supplier).to be_valid

			supplier = SupplierProfile.new(company_name: Faker::Name.name, close_from: closure_start + 1.day, close_to: closure_end)
			expect(supplier).to_not be_valid
			# expect(supplier.errors.full_messages).to include('asdas')

			supplier = SupplierProfile.new(company_name: Faker::Name.name, close_from: closure_start, close_to: closure_end - 1.day)
			expect(supplier).to_not be_valid

			supplier = SupplierProfile.new(company_name: Faker::Name.name, close_from: closure_start + 1.day, close_to: closure_end - 1.day)
			expect(supplier).to_not be_valid
		end
	end

	describe 'associations' do
		it { is_expected.to belong_to(:company_address_suburb) }
		it { is_expected.to have_one(:profile) }
		it { is_expected.to have_one(:user) }
		it { is_expected.to have_one(:supplier_flags) }
		it { is_expected.to have_one(:dear_account) }
		it { is_expected.to have_many(:minimums) }
		it { is_expected.to have_many(:order_suppliers) }
		it { is_expected.to have_many(:custom_order_suppliers) }
		it { is_expected.to have_many(:supplier_invoices) }
		it { is_expected.to have_many(:delivery_zones) }
		it { is_expected.to have_many(:suburbs) }
		it { is_expected.to have_many(:deliverable_suburbs) }
		it { is_expected.to have_many(:menu_sections) }
		it { is_expected.to have_many(:categories) }
		it { is_expected.to have_many(:menu_items) }
		it { is_expected.to have_many(:markup_overrides) }
		it { is_expected.to have_many(:order_lines) }
		it { is_expected.to have_many(:order_reviews) }
		it { is_expected.to have_many(:supplier_agreement_documents) }
		it { is_expected.to have_many(:report_sources) }
		it { is_expected.to have_many(:orders) }
		it { is_expected.to have_many(:closure_dates) }
	end

	# Model Callbacks
	context 'model callbacks', callbacks: true do
		let!(:supplier) { create(:supplier_profile, :random) }

		it 'sets the defaults for the supplier profile on save' do
			closure_starts = Time.zone.now + 2.days
			closure_ends = Time.zone.now + 3.days
			allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_start_date).and_return(closure_starts)
			allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :closure_end_date).and_return(closure_ends)

			supplier.close_from = nil
			supplier.close_to = nil
			supplier.markup = nil
			supplier.save

			expect(supplier.close_from).to eq(closure_starts)
			expect(supplier.close_to).to eq(closure_ends)
			expect(supplier.markup).to eq(0)
		end

		it 'changes the slug if the company name is changed' do
			expect(supplier.slug).to be_present

			supplier.update(company_name: 'New Supplier Name')
			expect(supplier.slug).to eq('new-supplier-name')

			supplier.update(is_searchable: !supplier.is_searchable)
			expect(supplier.slug).to eq('new-supplier-name') # still the same slug
		end
	end

	#
	# Methods:
	#
	context 'methods' do
		context 'delegated supplier_flags methods' do
			let!(:supplier) { create(:supplier_profile, :random) }
			let!(:supplier_flags) { create(:supplier_flags, supplier_profile: supplier) }

			SupplierProfile::SUPPLIER_FLAG_FIELDS.each do |field|
				it "delegates `#{field}` to supplier flags" do
					field_value = case field
					when :is_new_expires_at, :menu_last_updated_on
						Time.zone.now
					when :billing_frequency
						SupplierFlags::VALID_BILLING_FREQUENCIES.sample
					when :payment_term_days
						SupplierFlags::VALID_TERM_DAYS.sample
					else
						[true, false].sample
					end
					supplier_flags.update_column(field, field_value)
					expect(supplier.send(field)).to eq(supplier_flags.send(field))
				end
			end
		end # delegated supplier_flags

		describe '.name' do
			let!(:supplier) { create(:supplier_profile, :random) }

			it 'returns supplier\'s company name' do
				expect(supplier.name).to eq(supplier.company_name)
			end
		end

		describe '.email_recipient' do
			let!(:supplier) { create(:supplier_profile, :random, :with_user) }

			it 'returns the saved email value from the supplier record' do
				expect(supplier.email_recipient).to eq(supplier.email)
			end

			context 'with missing email value' do
				before do
					supplier.update_column(:email, nil)
				end

				it 'if supplier doesn\'t have an app user, returns YORDAR ADMIN EMAIL' do
					allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :admin_email).and_return('<EMAIL>')
					supplier.user.destroy
					supplier.reload
					expect(supplier.email_recipient).to eq('<EMAIL>')
				end

				it 'returns supplier user\'s email as the recipient' do
					user = supplier.user

					expect(supplier.email_recipient).to eq(user.email_recipient)
				end
			end
		end

		describe '.email_salutation' do
			it 'returns there if supplier does not have a user records' do
				supplier = create(:supplier_profile, :random)

				expect(supplier.email_salutation).to eq('there')
			end

			it 'returns the app users first name' do
				supplier = create(:supplier_profile, :random, :with_user)
				user = supplier.user

				expect(supplier.email_salutation).to eq(user.firstname)
			end
		end

		describe '.is_new?' do
			let!(:supplier) { create(:supplier_profile, :random, :with_flags) }

			context 'based on created at field' do
				it 'returns true if the supplier (record) is created in the last 2 months' do
					supplier.update_column(:created_at, [Time.zone.now, (Time.zone.now - 1.month), (Time.zone.now - 2.months + 1.day)].sample)

					expect(supplier).to be_is_new
				end

				it 'returns false if the supplier (record) is created at least 2 months ago' do
					supplier.update_column(:created_at, [(Time.zone.now - 2.months), (Time.zone.now - 2.months - 10.days)].sample)

					expect(supplier).to_not be_is_new
				end
			end

			context 'based on `is_new_expires_at` supplier flag' do
				before do
					supplier.update_column(:created_at, Time.zone.now - 3.months) # make supplier (record) older than 2 months
				end

				it 'defaults to false with no value in the `is_new_expires_at` flag' do
					supplier.supplier_flags.update_column(:is_new_expires_at, nil)

					expect(supplier).to_not be_is_new
				end

				it 'return true if the not-null `is_new_expires_at` value is greater than current time' do
					supplier.supplier_flags.update_column(:is_new_expires_at, [(Time.zone.now + 1.day), (Time.zone.now + 1.month)].sample)

					expect(supplier).to be_is_new
				end

				it 'returns false if the non-null `is_new_expires_at` value is less than current time' do
					supplier.supplier_flags.update_column(:is_new_expires_at, [(Time.zone.now - 1.day), (Time.zone.now - 1.month)].sample)

					expect(supplier).to_not be_is_new
				end
			end
		end

		context 'with attached user' do
			let!(:supplier) { create(:supplier_profile, :random, :with_user) }
			let!(:user) { supplier.reload.user }

			describe '.country_of_origin' do
				let!(:suburb) { create(:suburb, :random, country_code: 'NZ') }
				before do
					user.update_column(:suburb_id, suburb.id)
				end

				it 'returns the country_of_origin of the attached user' do
					expect(supplier.country_of_origin).to eq(user.country_of_origin)
				end

				it 'defaults country of origin to `AU` for a supplier without an attached user' do
					user.destroy
					supplier.reload

					expect { supplier.country_of_origin }.to_not raise_error
					expect(supplier.country_of_origin).to eq('AU')
				end
			end
		end

		describe '.import_menu_from_csv', skip: 'needs more info - deprecated feature for now' do
			before :each do
				@supplier = create :supplier_profile, :random
				category = create :category, :random, name: 'Brunch', group: 'catering-services'
				menu_section = create :menu_section, :random, supplier_profile: @supplier, name: 'yummy brunch'
				create :category_menu_section, :random, category: category, menu_section: menu_section, menu_section_id: menu_section.id
				create :category_menu_section, :random, category: category, menu_section: menu_section, menu_section_id: menu_section.id
				create :menu_item, :random, supplier_profile: @supplier, menu_section: menu_section
				create :menu_item, :random, supplier_profile: @supplier, menu_section: menu_section
				create :serving_size, :random, menu_item: @supplier.menu_items.first, price: 14.01
				create :serving_size, :random, menu_item: @supplier.menu_items.first
			end
			it 'return error message as `success` when menu is successfully updated' do
				csv_arrays = [['MenuSection.id', 'MenuItem.id', 'ServingSize.id', 'MenuSection.name', 'MenuItem.name', 'MenuItem.description', 'MenuItem.price', 'MenuItem.is_gst_free', 'MenuItem.minimum_quantity', 'MenuItem.is_vegan', 'MenuItem.is_vegetarian', 'MenuItem.is_gluten_free', 'ServingSize.name', 'ServingSize.price', 'Brunch'], [nil, nil, nil, 'lalala', 'abbab', nil, '3', nil, nil, nil, nil, nil, nil, nil, 'TRUE']]

				expect(@supplier.import_menu_from_csv(csv_arrays)).to eq('Your menu has been successfully imported.')

			end

			it 'returns errors when menu fails updating' do
				csv_arrays = [['MenuSection.id', 'MenuItem.id', 'ServingSize.id', 'MenuSection.name', 'MenuItem.name', 'MenuItem.description', 'MenuItem.price', 'MenuItem.is_gst_free', 'MenuItem.minimum_quantity', 'MenuItem.is_vegan', 'MenuItem.is_vegetarian', 'MenuItem.is_gluten_free', 'ServingSize.name', 'ServingSize.price', 'Brunch'], [nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, 'lalal', nil, 'TRUE']]

				expect(@supplier.import_menu_from_csv(csv_arrays)).to_not eq('Your menu has been successfully imported.')
			end

			it 'returns error message as `success` when csv file is empty' do
				csv_arrays = []

				expect(@supplier.import_menu_from_csv(csv_arrays)).to eq('Your menu has been successfully imported.')
			end
		end
	end

end
