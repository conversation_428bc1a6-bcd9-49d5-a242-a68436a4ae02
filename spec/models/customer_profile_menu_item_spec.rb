require 'rails_helper'

RSpec.describe CustomerProfileMenuItem, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }
    it { is_expected.to validate_presence_of(:menu_item) }

    it 'has a valid factory' do
      factory_customer_profile_menu_item = build(:customer_profile_menu_item, :random)

      expect(factory_customer_profile_menu_item).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to belong_to(:menu_item) }
  end

end
