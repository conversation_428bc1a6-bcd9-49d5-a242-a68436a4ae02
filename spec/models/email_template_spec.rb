require 'rails_helper'

RSpec.describe EmailTemplate, type: :model, notifications: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_inclusion_of(:name).in_array(EmailTemplate::VALID_TEMPLATE_NAMES) }
    describe 'uniqueness' do
      subject { build(:email_template, :random) }
      it { is_expected.to validate_uniqueness_of(:name).scoped_to(:account_type) }
    end
    it { is_expected.to validate_presence_of(:account_type) }
    it { is_expected.to validate_inclusion_of(:account_type).in_array(EmailTemplate::VALID_ACCOUNT_TYPES) }
    it { is_expected.to validate_inclusion_of(:kind).in_array(EmailTemplate::VALID_KINDS).allow_nil }

    it 'has a valid factory' do
      factory_template = build(:email_template, :random)
      expect(factory_template).to be_valid
    end

    it 'has default values' do
       template = EmailTemplate.new

       expect(template.can_override).to be_falsey
       expect(template.variations).to be_empty
    end
  end # validations

end
