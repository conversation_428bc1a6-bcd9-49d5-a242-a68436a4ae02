require 'rails_helper'

RSpec.describe Document, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:documentable) }
    it { is_expected.to validate_presence_of(:url) }
    it { is_expected.to validate_presence_of(:kind) }
    it { is_expected.to validate_inclusion_of(:kind).in_array(Document::VALID_KINDS) }

    it 'has a valid factory' do
      factory_document = build(:document, :random)
      expect(factory_document).to be_valid
    end

    context 'uniqueness' do
      # it { is_expected.to validate_uniqueness_of(:kind).scoped_to([:documentable, :version])  }
      it 'cannot have duplicate documents for the same documentable with the same kind and version' do
        order1 = create(:order, :delivered)
        exiting_document = create(:document, :random, documentable: order1, version: 2)

        same_documentable_kind_version = build(:document, :random, documentable: order1, version: exiting_document.version, kind: exiting_document.kind)
        expect(same_documentable_kind_version).to_not be_valid

        same_documentable_kind_diff_version = build(:document, :random, documentable: order1, version: exiting_document.version + 1, kind: exiting_document.kind)
        expect(same_documentable_kind_diff_version).to be_valid

        order2 = create(:order, :delivered)
        diff_documentable_same_kind_version = build(:document, :random, documentable: order2, version: exiting_document.version, kind: exiting_document.kind)
        expect(diff_documentable_same_kind_version).to be_valid
      end
    end
  end

  context 'association' do
    it { is_expected.to belong_to(:documentable) }
    it { is_expected.to belong_to(:order) }
    it { is_expected.to belong_to(:order_supplier) }
  end

  context 'instance methods' do
    describe '.file_extension' do
      it 'returns the file extension of `csv` for all CSV reports' do
        document = build(:document, :random, kind: 'team_order_avery_labels')
        expect(document.file_extension).to eq('csv')
      end

      it 'returns the file extension of `json` for all JSON reports' do
        document = build(:document, :random, kind: 'supplier_json_order_details')
        expect(document.file_extension).to eq('json')
      end

      it 'returns the file extentions of `pdf` for all other reports with a .pdf in url' do
        document = build(:document, :random, kind: (Document::VALID_KINDS - %w[team_order_avery_labels supplier_json_order_details]).sample, url: 'https://filename.pdf')
        expect(document.file_extension).to eq('pdf')
      end

      it 'returns file extention as per the document url' do
        document = build(:document, :random, kind: (Document::VALID_KINDS - %w[team_order_avery_labels supplier_json_order_details]).sample, url: 'https://filename.randon-extension')
        expect(document.file_extension).to eq('randon-extension')
      end
    end

    describe '.fallback_name' do
      it 'returns the name saved against the record' do
        document = build(:document, :random, name: 'saved-document-name')
        expect(document.fallback_name).to eq('saved-document-name')
      end

      context 'document without saved name' do
        context 'invoice documents' do
          invoice = build(:invoice, :random)
          it 'returns the report name for a tax invoice document' do
            document = build(:document, :random, name: nil, documentable: invoice, kind: 'tax_invoice', version: 1)
            expect(document.fallback_name).to eq("invoice-#{document.documentable.number}")

            # with version
            document = build(:document, :random, name: nil, documentable: invoice, kind: 'tax_invoice', version: rand(2..10))
            expect(document.fallback_name).to eq("invoice-#{document.documentable.number}-v#{document.version}")
          end

          it 'returns the report name for a invoice report documents' do
            document = build(:document, :random, name: nil, documentable: invoice, kind: %w[invoice_location_report invoice_detailed_report].sample)
            expect(document.fallback_name).to eq("invoice-#{document.documentable.number}-#{document.kind.gsub('invoice_', '').gsub('_', '-')}-v#{document.version}")
          end

          it 'returns the report name for a invoice tax receipt documents' do
            document = build(:document, :random, name: nil, documentable: invoice, kind: 'tax_invoice_receipt')
            expect(document.fallback_name).to eq("invoice-tax-receipt-#{document.documentable.number}")
          end
        end

        it 'returns the generic name based on kind documentable_id and version for non-invoice documents' do
          document = build(:document, :random, name: nil, kind: (Document::VALID_KINDS - %w[tax_invoice invoice_location_report invoice_detailed_report tax_invoice_receipt]).sample)
          expect(document.fallback_name).to eq("#{document.kind.gsub('_', '-')}-v#{document.version}")
        end
      end
    end

    describe '.kind_based_name' do
      it 'returns the humanized and titleize (with version) of the kind' do
        document_kind = Document::VALID_KINDS.sample
        document_version = rand(1..10).to_i
        document = build(:document, :random, kind: document_kind, version: document_version)

        expect(document.kind_based_name).to eq("#{document_kind.humanize.titleize} (v#{document_version})")
      end
    end
  end

end
