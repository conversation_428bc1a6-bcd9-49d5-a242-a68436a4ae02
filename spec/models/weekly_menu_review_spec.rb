require 'rails_helper'

RSpec.describe WeeklyMenuReview, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }
    it { is_expected.to validate_presence_of(:day) }
    it { is_expected.to validate_presence_of(:taste) }
    it { is_expected.to validate_presence_of(:presentation) }
    it { is_expected.to validate_presence_of(:quantity) }

    # https://stackoverflow.com/questions/25396297/how-to-make-validates-inclusion-true-or-false-work-in-testing-rails#answer-42938116
    it { is_expected.to allow_value(%w[true false]).for(:see_again) }

    it 'has a valid factory' do
      factory_review = build(:weekly_menu_review, :random)
      expect(factory_review).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:weekly_menu) }
  end

  context 'methods' do
    describe '.name' do
      let!(:weekly_menu_review) { create(:weekly_menu_review, :random) }

      it 'return the name as a combination of first name and last name' do
        expect(weekly_menu_review.name).to eq("#{weekly_menu_review.first_name} #{weekly_menu_review.last_name}")
      end
    end
  end

end
