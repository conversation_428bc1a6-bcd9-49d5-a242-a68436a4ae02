require 'rails_helper'

RSpec.describe CustomerQuote, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:kind) }
    it { is_expected.to validate_inclusion_of(:kind).in_array(CustomerQuote::VALID_QUOTE_KINDS) }
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to validate_inclusion_of(:status).in_array(CustomerQuote::VALID_STATUSES) }
    it { is_expected.to validate_presence_of(:form_data) }
    it { is_expected.to validate_presence_of(:uuid) }
    it { is_expected.to validate_uniqueness_of(:uuid) }

    it 'has a valid factory' do
      factory_quote = build(:customer_quote, :random)
      expect(factory_quote).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to have_many(:orders) }
  end
end
