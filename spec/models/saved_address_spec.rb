require 'rails_helper'

RSpec.describe SavedAddress, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:street_address) }
    it { is_expected.to validate_presence_of(:suburb_id) }
    it { is_expected.to validate_presence_of(:instructions) }

    it 'has a valid factory' do
      factory_address = build(:saved_address, :random)

      expect(factory_address).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to belong_to(:suburb) }
  end

  context 'instance methods' do

    describe '.address_arr' do
      it 'returns the address and suburb (label) and no level info' do
        saved_address = create(:saved_address, :random, level: nil)
        expect(saved_address.address_arr).to eq([saved_address.street_address, saved_address.suburb.label])
      end

      it 'returns the delivery address level (formatted), address and suburb (label)' do
        saved_address = create(:saved_address, :random, level: rand(1..20))
        expect(saved_address.address_arr).to eq([saved_address.formatted_level, saved_address.street_address, saved_address.suburb.label])
      end
    end

    describe '.formatted_level' do
      it 'returns blank string (``) for a missing delivery address level' do
        saved_address = create(:saved_address, :random, level: [nil, ''].sample)

        expect(saved_address.formatted_level).to eq('')
      end

      it 'returns numerical level value with a `Level` prefix' do
        saved_address = create(:saved_address, :random, level: rand(1..10))

        expect(saved_address.formatted_level).to eq("Level #{saved_address.level}")
      end

      it 'returns level value as is, if it contains Level or starts with L (case-insensitive)' do
        level_number = rand(1..10)
        saved_address = create(:saved_address, :random, level: ["level #{level_number}", "Level #{level_number}", "Tower 3, Level #{level_number}", "l #{level_number}", "L#{level_number}"].sample)

        expect(saved_address.formatted_level).to eq(saved_address.level)
      end
    end

  end

end
