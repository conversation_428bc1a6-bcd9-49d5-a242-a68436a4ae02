require 'rails_helper'

RSpec.describe CustomOrderSupplier, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:order_id) }
    it { is_expected.to validate_presence_of(:supplier_profile_id) }

    it 'has a valid factory' do
      factory_order_supplier = build(:custom_order_supplier, :random)
      expect(factory_order_supplier).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:order) }
    it { is_expected.to belong_to(:supplier_profile) }
  end

end
