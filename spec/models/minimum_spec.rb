require 'rails_helper'

RSpec.describe Minimum, type: :model do
	describe 'validations' do
		it { is_expected.to validate_presence_of(:category_id) }
		it { is_expected.to validate_presence_of(:spend_price) }
		it { is_expected.to validate_numericality_of(:spend_price).is_greater_than_or_equal_to(0) }

		it { is_expected.to validate_numericality_of(:lead_time) }

		it 'is not valid if the lead time isn\'t greater than 0 if lead_time_day_before is blank' do
			minimum = build(:minimum, :random, lead_time: [nil, 0, -1], lead_time_day_before: nil)
			expect(minimum).to_not be_valid
		end

		it 'is not valid if the lead_time_day_before is blank if lead_time is blank' do
			minimum = build(:minimum, :random, lead_time: nil, lead_time_day_before: nil)
			expect(minimum).to_not be_valid
		end
	end

	describe 'associations' do
		it { is_expected.to belong_to(:category) }
		it { is_expected.to belong_to(:supplier_profile) }
	end

	context 'instance methods' do

		describe '.lead_time_day_in_numbers' do
			let!(:minimum) { create(:minimum, :random, lead_time_day_before: '20:30') }

			it 'returns the integer value of the string without the colon (:)' do
				expect(minimum.lead_time_day_in_numbers).to eq(2030)
			end

			it 'returns nil if minimum\'s lead_time_day_before is nil or blank' do
				minimum.update_column(:lead_time_day_before, [nil, ''].sample)
				expect(minimum.lead_time_day_in_numbers).to be_nil
			end
		end

		describe '.formatted_lead_time' do
			let!(:day_before_minimum) { create(:minimum, :random, lead_time_day_before: '9:45', lead_time: nil) }
			let!(:hours_minimum) { create(:minimum, :random, lead_time: 24, lead_time_day_before: nil) }

			it 'returns the time of day before for minimum with only set lead_time_day_before' do
				expect(day_before_minimum.formatted_lead_time).to eq('09:45am day before')
			end

			it 'returns the number of hours for the minimum with only set lead_time' do
				expect(hours_minimum.formatted_lead_time).to eq('24 hrs')
			end

			it 'returns the time of day before for minimum with both lead_time and lead_time_day_before set' do
				hours_minimum.update_column(:lead_time_day_before, '15:20')
				expect(hours_minimum.formatted_lead_time).to eq('03:20pm day before')
			end
		end

	end # instance methods

end
