require 'rails_helper'

RSpec.describe DeliveryOverride, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }
    it { is_expected.to validate_presence_of(:supplier_kind) }
    it { is_expected.to validate_inclusion_of(:supplier_kind).in_array(DeliveryOverride::VALID_KINDS) }

    it 'is active by default' do
      new_delivery_override = DeliveryOverride.new
      expect(new_delivery_override).to be_active
    end

    it 'has a valid factory' do
      factory_delivery_override = build(:delivery_override, :random)
      expect(factory_delivery_override).to be_valid
    end

    it 'vaidates that the delivery override has a supplier profile if supplier_kind is `specific`' do
      specific_supplier_delivery_override = build(:delivery_override, :for_specific_supplier)
      expect(specific_supplier_delivery_override).to be_valid

      specific_supplier_delivery_override.supplier_profile = nil
      expect(specific_supplier_delivery_override).to_not be_valid
    end

    it 'validates that it has at least have one override value' do
      no_delivery_override = build(:delivery_override, :random, customer_override: nil, supplier_override: nil)
      expect(no_delivery_override).to_not be_valid
      expect(no_delivery_override.errors.full_messages).to include('Must have at least one override value')

      delivery_supplier_override = build(:delivery_override, :random, customer_override: nil, supplier_override: rand(2.03..20.9))
      expect(delivery_supplier_override).to be_valid

      delivery_customer_override = build(:delivery_override, :random, customer_override: rand(2.03..20.9), supplier_override: nil)
      expect(delivery_customer_override).to be_valid

      combined_supplier_override = build(:delivery_override, :random, customer_override: rand(2.03..20.9), supplier_override: rand(2.03..20.9))
      expect(combined_supplier_override).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to belong_to(:supplier_profile) }
  end

end
