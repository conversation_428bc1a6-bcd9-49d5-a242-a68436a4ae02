require 'rails_helper'

RSpec.describe FavouriteCustomer, type: :model do

  context 'validation' do
    it 'has a valid factory' do
      factory_favourite_customer = build(:favourite_customer, :random)

      expect(factory_favourite_customer).to be_valid
    end
  end

  context 'association' do
    it { is_expected.to belong_to(:favouriter) }
    it { is_expected.to belong_to(:customer_profile) }
  end

end
