require 'rails_helper'

RSpec.describe PromotionSubscription, type: :model, promotions: true do

  context 'valiadations' do
    it { is_expected.to validate_presence_of(:promotion) }
    it { is_expected.to validate_presence_of(:subscriber) }

    it 'has a valid factory' do
      factory_subscription = build(:promotion_subscription, :random)

      expect(factory_subscription).to be_valid
    end

    it 'has default values' do
      subscription = PromotionSubscription.new

      expect(subscription.active).to be_truthy
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:promotion) }
    it { is_expected.to belong_to(:subscriber) }
  end
end
