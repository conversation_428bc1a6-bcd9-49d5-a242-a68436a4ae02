require 'rails_helper'

RSpec.describe WeeklyMenuClient, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:title) }
    it { is_expected.to validate_presence_of(:navigation_title) }
    it { is_expected.to validate_presence_of(:slug) }
    it { is_expected.to validate_presence_of(:header_content) }
    it { is_expected.to validate_presence_of(:abbreviations) }
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile).required }
    it { is_expected.to have_many(:weekly_menus) }
  end

end
