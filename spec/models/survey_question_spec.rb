require 'rails_helper'

RSpec.describe SurveyQuestion, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:employee_survey) }
    it { is_expected.to validate_presence_of(:label) }
    it { is_expected.to validate_presence_of(:input_type) }
    it { is_expected.to validate_inclusion_of(:input_type).in_array(SurveyQuestion::VALID_INPUT_TYPES) }
    it { is_expected.to validate_presence_of(:position) }

    it 'has a valid factory' do
      factory_question = build(:survey_question, :random)
      expect(factory_question).to be_valid
    end

    it 'has default values' do
      factory_question = SurveyQuestion.new

      expect(factory_question.active).to be_truthy
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:employee_survey) }
  end

end
