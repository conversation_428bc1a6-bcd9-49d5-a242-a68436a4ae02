require 'rails_helper'

RSpec.describe Suburb, type: :model do

	describe 'validations' do
		it { is_expected.to validate_presence_of(:name) }
		it { is_expected.to validate_presence_of(:postcode) }
		it { is_expected.to validate_numericality_of(:postcode).only_integer.is_greater_than(1).is_less_than_or_equal_to(9999) }
		it { is_expected.to validate_presence_of(:country_code) }
		it { is_expected.to validate_inclusion_of(:country_code).in_array(Suburb::VALID_COUNTRY_CODES) }

		it 'has a valid factory' do
			factory_suburb = build(:suburb, :random)

			expect(factory_suburb).to be_valid
		end
	end

	describe '.associations' do
		it { is_expected.to have_many(:delivery_zones) }
		it { is_expected.to have_many(:deliverable_suburbs) }
	end

	context 'methods' do
		describe '.label' do
			let!(:suburb) { create(:suburb, :random, postcode: '2010', name: 'Darlinghurst', state: 'NSW') }
			it 'concatenates name, state and postcode if present' do
				expect(suburb.label).to eq("#{suburb.name}, #{suburb.state} #{suburb.postcode}")
			end
		end

		describe '.suburb_state' do
			let!(:suburb) { create(:suburb, :woolloomooloo) }
			it 'returns the combination of name and state' do
				expect(suburb.suburb_state).to eq('Woolloomooloo, NSW')
			end
		end

		describe '.has_coordinates?' do
			let!(:suburb) { create(:suburb, :woolloomooloo, latitude: rand(1..10), longitude: rand(1..20)) }

			it 'returns true if both latitude and longitude exist' do
				expect(suburb.has_coordinates?).to be_truthy
			end

			it 'returns false if either latitude and longitude does not exist' do
				field = [true, false].sample ? :longitude : :latitude
				suburb.update_column(field, nil)
				expect(suburb.has_coordinates?).to be_falsey
			end
		end		

		describe '.symbolized_country_code' do
			let!(:suburb) { create(:suburb, :random, country_code: Suburb::VALID_COUNTRY_CODES.sample) }
			it 'returns the downcased symbolized version of the country code' do
				expect(suburb.symbolized_country_code).to eq(suburb.country_code.downcase.to_sym)
			end
		end

		describe '.as_json' do
			let!(:suburb) { create(:suburb, :woolloomooloo) }

			it 'does stuff' do
				json_suburb = suburb.as_json
				expect(json_suburb.keys).to include(:label)

				expect(json_suburb[:label]).to include(suburb.label)
			end
		end
	end

end
