require 'rails_helper'

RSpec.describe Supplier::MarkupOverride, type: :model, suppliers: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:supplier_profile) }
    it { is_expected.to validate_presence_of(:overridable) }

    it 'has a valid factory' do
      factory_override = build(:supplier_markup_override, :random)
      expect(factory_override).to be_valid
    end

    it 'has default values for markup and commisson' do
      factory_override = Supplier::MarkupOverride.new
      expect(factory_override.markup).to be_nil
      expect(factory_override.commission_rate).to be_nil
    end

    it 'is active by default' do
      factory_override = build(:supplier_markup_override, :random)
      expect(factory_override).to be_active
    end

    it 'can have negative markup and/or commission rate values' do
      factory_override = build(:supplier_markup_override, :random)
      negative_value = -10.0
      if [true, false].sample
        factory_override.markup = negative_value
        expect(factory_override).to be_valid
        expect(factory_override.markup).to eq(negative_value)
      else
        factory_override.commission_rate = negative_value
        expect(factory_override).to be_valid
        expect(factory_override.commission_rate).to eq(negative_value)
      end
    end

    # needed a custom validation method, as AR uniquness validation for polymophic field fails
    context 'supplier - overridable combination uniqueness validation' do
      let!(:existing_override) { create(:supplier_markup_override, :random) }

      it 'validates uniqueness of supplier profile and overridable for new record' do
        new_override = build(:supplier_markup_override, :random, overridable: existing_override.overridable, supplier_profile: existing_override.supplier_profile)

        expect(new_override).to_not be_valid
      end

      it 'does not update existing override to another existing override' do
        existing_override2 = create(:supplier_markup_override, :random)
        expect(existing_override2.update(supplier_profile: existing_override.supplier_profile, overridable: existing_override.overridable)).to be_falsey

        existing_override2.reload
        expect(existing_override2.supplier_profile).to_not eq(existing_override.supplier_profile)
        expect(existing_override2.overridable).to_not eq(existing_override.overridable)
      end

      it 'lets you update value of existing override' do
        update_attributes = { markup: rand(10.9..20.9).round(2), commission_rate: rand(10.9..20.9).round(2) }
        expect(existing_override.update(update_attributes)).to be_truthy

        expect(existing_override.markup).to eq(update_attributes[:markup])
        expect(existing_override.commission_rate).to eq(update_attributes[:commission_rate])
      end
    end

    it 'validates that it has at least have one override value' do
      no_delivery_override = build(:supplier_markup_override, :random, markup: nil, commission_rate: nil)
      expect(no_delivery_override).to_not be_valid
      expect(no_delivery_override.errors.full_messages).to include('Must have at least one override value')

      override_with_markup_only = build(:supplier_markup_override, :random, markup: rand(10.2..15.9), commission_rate: nil)
      expect(override_with_markup_only).to be_valid

      override_with_commission_only = build(:supplier_markup_override, :random, markup: nil, commission_rate: rand(10.2..15.9))
      expect(override_with_commission_only).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:supplier_profile) }
    it { is_expected.to belong_to(:overridable) }
  end

  context 'instance methods' do
    describe '.yordar_commission' do
      let!(:supplier) { create(:supplier_profile, :random, markup: rand(15.2..20.9), commission_rate: rand(15.2..20.9)) }
      let!(:markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, markup: rand(10.2..14.9), commission_rate: rand(10.2..14.9)) }

      it 'returns value based on markup override values' do
        expected_override_commission = 1 - (1 - (markup_override.commission_rate / 100)) / (1 + (markup_override.markup / 100))
        expect(markup_override.yordar_commission).to eq(expected_override_commission)
      end

      it 'returns value based on default supplier markup value if blank within override' do
        markup_override.update_column(:markup, nil)

        expected_override_commission = 1 - (1 - (markup_override.commission_rate / 100)) / (1 + (supplier.markup / 100))
        expect(markup_override.yordar_commission).to eq(expected_override_commission)
      end

      it 'returns value based on default supplier commission rate value if blank within override' do
        markup_override.update_column(:commission_rate, nil)

        expected_override_commission = 1 - (1 - (supplier.commission_rate / 100)) / (1 + (markup_override.markup / 100))
        expect(markup_override.yordar_commission).to eq(expected_override_commission)
      end
    end
  end

end
