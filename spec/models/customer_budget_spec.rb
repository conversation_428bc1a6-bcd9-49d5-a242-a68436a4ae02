require 'rails_helper'

RSpec.describe CustomerBudget, type: :model, budgets: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }
    it { is_expected.to validate_presence_of(:value) }
    it { is_expected.to validate_presence_of(:starts_on) }
    it { is_expected.to validate_presence_of(:frequency) }
    it { is_expected.to validate_inclusion_of(:frequency).in_array(CustomerBudget::VALID_BUDGET_FREQUENCIES) }

    it 'has a valid factory' do
      factory_budget = build(:customer_budget, :random)
      expect(factory_budget).to be_valid
    end

    it 'validates the budget ends only after it starts if it ever ends' do
      today = Date.today

      invalid_budget = build(:customer_budget, :random, starts_on: today, ends_on: today - rand(1..10).days)
      expect(invalid_budget).to_not be_valid

      valid_budget = build(:customer_budget, :random, starts_on: today, ends_on: [today, (today + rand(1..10).days), nil].sample)
      expect(valid_budget).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to belong_to(:customer_purchase_order) }
  end

  context 'delegated methods' do
    describe '.po_number' do
      let(:customer_purchase_order) { create(:customer_purchase_order, :random) }
      let!(:customer_budget) { create(:customer_budget, :random, customer_purchase_order: customer_purchase_order) }

      it 'delegates the po number to the attached customer_purchase_order' do
        expect(customer_budget.po_number).to eq(customer_purchase_order.po_number)
      end

      it 'returns nil if not attached to any customer_purchase_order' do
        customer_budget.update_column(:customer_purchase_order_id, nil)
        expect(customer_budget.po_number).to be_nil
      end
    end
  end

end
