require 'rails_helper'

RSpec.describe MenuExtra, type: :model, menu_extras: true do

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:price) }
    it { is_expected.to validate_presence_of(:menu_extra_section) }

    it 'has a valid factory' do
      factory_menu_extra = build(:menu_extra, :random)
      expect(factory_menu_extra).to be_valid
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:menu_extra_section) }
    it { is_expected.to belong_to(:menu_item) }
    it { is_expected.to have_one(:supplier_profile) }
  end

  context 'instance methods' do

    context 'Price / Cost' do
      let!(:price) { rand(5.22...10.98) }
      let!(:supplier) { create(:supplier_profile, :random, commission_rate: 0.0, markup: 0.0) }
      let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }
      let!(:menu_extra) { build(:menu_extra, :random, menu_item: menu_item, price: price) }

      before do
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(0.1)
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(0.15)
      end

      describe '.markup_price' do
        it 'returns the price as saved in the record' do
          expect(menu_extra.markup_price.round(2)).to be_within(0.2).of(price.round(2))
        end

        it 'returns the price as saved in the record even when asking promotional pricing', promotional_item: true do
          expect(menu_extra.markup_price(promotional: true).round(2)).to be_within(0.2).of(price.round(2))
        end

        it 'returns the gst inclusive pricing per country' do
          expect(menu_extra.markup_price(gst_country: 'AU').round(2)).to be_within(0.2).of((price * 1.1).round(2))
          expect(menu_extra.markup_price(gst_country: 'NZ').round(2)).to be_within(0.2).of((price * 1.15).round(2))
        end

        it 'returns the price for a gst inclusive pricing if item is gst free' do
          menu_item.update_column(:is_gst_free, true)

          expect(menu_extra.markup_price(gst_country: %i[au nz].sample).round(2)).to be_within(0.2).of(price.round(2))
        end

        context 'with supplier markup' do
          let!(:markup) { [10, 20, 30].sample }

          before do
            supplier.update_column(:markup, markup)
          end

          it 'returns the price including supplier markup' do
            expect(menu_extra.markup_price.round(2)).to be_within(0.2).of((price.to_f * (1 + markup.to_f / 100)).round(2))
          end

          it 'returns the gst inclusive pricing (based on country) including supplier markup' do
            expect(menu_extra.markup_price(gst_country: 'au').round(2)).to be_within(0.2).of(((price.to_f * (1 + markup.to_f / 100)) * 1.1).round(2))
            expect(menu_extra.markup_price(gst_country: 'nz').round(2)).to be_within(0.2).of(((price.to_f * (1 + markup.to_f / 100)) * 1.15).round(2))
          end

          context 'with supplier markup override' do
            let!(:markup_override) { [15, 25, 35].sample }
            let!(:supplier_markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, markup: markup_override) }

            it 'returns the price including supplier\'s overriden markup' do
              expect(menu_extra.markup_price(override: supplier_markup_override).round(2)).to be_within(0.2).of((price.to_f * (1 + markup_override.to_f / 100)).round(2))
            end

            it 'returns the price including supplier\'s default markup if override is not passed' do
              markup_price = menu_extra.markup_price(override: nil).round(2)
              expect(markup_price).to_not be_within(0.2).of((price.to_f * (1 + markup_override.to_f / 100)).round(2))
              expect(markup_price).to be_within(0.2).of((price.to_f * (1 + markup.to_f / 100)).round(2))
            end

            it 'returns the gst inclusive pricing (based on country) including supplier markup' do
              expect(menu_extra.markup_price(gst_country: 'AU', override: supplier_markup_override).round(2)).to be_within(0.2).of(((price.to_f * (1 + markup_override.to_f / 100)) * 1.1).round(2))
              expect(menu_extra.markup_price(gst_country: "NZ", override: supplier_markup_override).round(2)).to be_within(0.2).of(((price.to_f * (1 + markup_override.to_f / 100)) * 1.15).round(2))
            end
          end # with supplier markup override
        end # with supplier markup
      end # .markup_price

      describe '.cost' do
        it 'returns the price for a menu item of a supplier with no commission (rate)' do
          expect(menu_extra.cost.round(2)).to be_within(0.2).of(price.to_f.round(2))
        end

        it 'returns the price - commission for a menu item of a supplier with commission (rate)' do
          commission = [10, 15, 20].sample
          supplier.update_column(:commission_rate, commission)

          expect(menu_extra.cost.round(2)).to be_within(0.2).of((price.to_f * (1 - commission.to_f / 100)).round(2))
        end
      end # .cost
    end # Price / Cost

  end # instance methods

end
