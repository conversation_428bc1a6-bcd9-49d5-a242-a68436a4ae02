require 'rails_helper'

RSpec.describe RateCard, type: :model do

	context 'validations' do
		it { is_expected.to validate_presence_of(:company_id) }
		it { is_expected.to validate_presence_of(:menu_item_id) }
		it { is_expected.to validate_presence_of(:cost) }
		it { is_expected.to validate_presence_of(:price) }

		it 'has a valid factory' do
			factory_rate_card = build(:rate_card, :random)
			expect(factory_rate_card).to be_valid
		end
	end

	context 'associations' do
		it { is_expected.to belong_to(:company) }
		it { is_expected.to belong_to(:menu_item) }
		it { is_expected.to belong_to(:serving_size) }
		it { is_expected.to have_one(:supplier_profile) }
	end

	context 'instance methods' do
		describe '.price_inc_gst' do
			let!(:price) { rand(5.22...10.98) }
			let!(:menu_item) { create(:menu_item, :random, is_gst_free: false) }
			let!(:rate_card) { create(:rate_card, :random, menu_item: menu_item, price: price) }

			before do
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(0.1)
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(0.15)
			end

			it 'returns the gst inclusive pricing (based on country)' do
				expect(rate_card.price_inc_gst(gst_country: 'au').round(2)).to be_within(0.2).of((price * 1.1).round(2))
				expect(rate_card.price_inc_gst(gst_country: 'nz').round(2)).to be_within(0.2).of((price * 1.15).round(2))
			end

			it 'reutrns only the price for a gst free items rate card' do
				menu_item.update_column(:is_gst_free, true)

				expect(rate_card.price_inc_gst(gst_country: %i[au nz].sample).round(2)).to eq(price.round(2))
			end
		end
	end

end
