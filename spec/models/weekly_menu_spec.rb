require 'rails_helper'

RSpec.describe WeeklyMenu, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:week_of) }
  end

  context 'associations' do
    it { is_expected.to have_many(:weekly_menu_reviews) }
    it { is_expected.to belong_to(:weekly_menu_client).required }
    it { is_expected.to belong_to(:monday_supplier) }
    it { is_expected.to belong_to(:tuesday_supplier) }
    it { is_expected.to belong_to(:wednesday_supplier) }
    it { is_expected.to belong_to(:thursday_supplier) }
    it { is_expected.to belong_to(:friday_supplier) }
    it { is_expected.to belong_to(:saturday_supplier) }
    it { is_expected.to belong_to(:sunday_supplier) }
  end

end
