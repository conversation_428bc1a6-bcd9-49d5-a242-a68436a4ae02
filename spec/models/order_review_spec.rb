require 'rails_helper'

RSpec.describe OrderReview, type: :model do

  describe 'validations' do
    it { is_expected.to validate_presence_of(:order) }
    it { is_expected.to validate_presence_of(:supplier_profile) }

    # it { is_expected.to validate_uniqueness_of(:supplier_profile).scoped_to(:order) }
    it 'validates uniqueness of supplier profile in the scope of an order' do
      supplier = create(:supplier_profile, :random)
      order = create(:order, :random)
      create(:order_review, order: order, supplier_profile: supplier) # create existing order review

      new_order_review = build(:order_review, order: order, supplier_profile: supplier)
      expect(new_order_review).to_not be_valid
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:supplier_profile) }
    it { is_expected.to belong_to(:order) }
  end

end
