require 'rails_helper'

RSpec.describe TeamOrder::Detail, type: :model, team_orders: true do

  context 'validations' do
    it { is_expected.to validate_inclusion_of(:cutoff_option).in_array(TeamOrder::Detail::VALID_CUTOFF_OPTIONS) }
    it 'has a valid factory' do
      factory_detail = build(:team_order_detail, :random)
      expect(factory_detail).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:order) }
    it { is_expected.to have_many(:levels) }
  end

  context 'instance methods' do
    let!(:team_order_detail) { create(:team_order_detail, :random) }

    it 'returns is_package_order? as true if package_id is present' do
      expect(team_order_detail).to_not be_is_package_order # default

      team_order_detail.package_id = SecureRandom.uuid
      expect(team_order_detail).to be_is_package_order
    end
  end

end
