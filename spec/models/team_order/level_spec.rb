require 'rails_helper'

RSpec.describe TeamOrder::Level, type: :model, team_orders: true, team_order_levels: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:team_order_detail) }
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name).scoped_to(:team_order_detail_id).case_insensitive }

    it 'has a valid factory' do
      factory_level = build(:team_order_level, :random)
      expect(factory_level).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:team_order_detail) }
  end

end
