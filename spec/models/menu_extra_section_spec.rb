require 'rails_helper'

RSpec.describe MenuExtraSection, type: :model do

  describe 'valiadations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:menu_item) }

    it 'has a valid factory' do
      factory_menu_extra_section = build(:menu_extra_section, :random)

      expect(factory_menu_extra_section).to be_valid
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:menu_item) }
    it { is_expected.to have_many(:menu_extras) }
  end

end
