require 'rails_helper'

RSpec.describe SupplierInvoice, type: :model, suppliers: true, rgi: true do

  describe 'validations' do
    it { is_expected.to validate_inclusion_of(:payment_status).in_array(SupplierInvoice::VALID_PAYMENT_STATUSES) }
    it { is_expected.to validate_presence_of(:number) }
    it { is_expected.to validate_presence_of(:uuid) }

    it 'has default values' do
      invoice = SupplierInvoice.new

      expect(invoice.payment_status).to eq('unpaid')
    end

    it 'has a valid factory' do
      factory_invoice = build(:supplier_invoice, :random)

      expect(factory_invoice).to be_valid
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:supplier_profile) }
    it { is_expected.to have_many(:order_suppliers) }
    it { is_expected.to have_many(:orders) }
  end

end
