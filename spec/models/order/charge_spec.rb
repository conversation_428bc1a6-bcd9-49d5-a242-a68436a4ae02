require 'rails_helper'

RSpec.describe Order::Charge, type: :model, orders: true, stripe: true do

  context 'validations' do
    it { is_expected.to validate_inclusion_of(:status).in_array(Order::Charge::VALID_STATUSES) }

    it 'has a valid factory' do
      factory_charge = build(:order_charge, :random)
      expect(factory_charge).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:order) }
  end

  context 'instance methods' do
    describe '.refunded?' do
      it 'returns true if the charge consists of a refund token and a status of refunded' do
        refunded_charge = create(:order_charge, :random, status: 'refunded', refund_token: SecureRandom.hex(7))
        expect(refunded_charge).to be_refunded
      end
    end
  end

end