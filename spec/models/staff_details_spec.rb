require 'rails_helper'

RSpec.describe StaffDetails, type: :model, customers: true, staff: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }

    it 'has a valid factory' do
      staff_details = build(:staff_details, :random)

      expect(staff_details).to be_valid
    end

    it 'has default nil values set from DB', rgi: true do
      staff_details = build(:staff_details)

      expect(staff_details.personal).to be_nil
      expect(staff_details.emergency_contact).to be_nil
      expect(staff_details.bank).to be_nil
      expect(staff_details.tax).to be_nil
      expect(staff_details.documents).to be_nil
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
  end

  context 'instance methods' do
    let!(:staff_details) do
      create(:staff_details,
        :random,
        personal: { key1: 'value1', key2: 'value2' },
        emergency_contact: { key1: true, key2: 'value2' },
        bank: { key1: 'value1', key2: 'value2' },
        tax: { key1: 'value1', key2: true },
        documents: { key1: 'value1', key2: 'value2' }
      )
    end

    describe '.complete?' do
      it 'returns as true if all values within each field are present' do
        expect(staff_details).to be_complete
      end

      it 'returns as false if at the fiel itself is blank' do
        staff_details.update_column(:bank, [nil, {}].sample)

        expect(staff_details).to_not be_complete        
      end

      it 'returns as false if at least one of the values of each field is false' do
        staff_details.update_column(:emergency_contact, { key1: false, key2: 'value2' })

        expect(staff_details).to_not be_complete        
      end

      it 'returns as false if at least one of the values of each field is blank' do
        staff_details.update_column(:bank,  { key1: 'value1', key2: ['', nil].sample })

        expect(staff_details).to_not be_complete        
      end
    end # .complete?

    describe '.current_version_ref' do

      it 'builds the reference based on the values of the version ref fields' do
        reference = StaffDetails::VERSION_REF_FIELDS.map do |field|
          value = staff_details.send(field)
          next if value.blank?

          value.to_json
        end.reject(&:blank?).join('')
        expected_version_ref = Digest::MD5.hexdigest(reference)

        expect(staff_details.current_version_ref).to eq(expected_version_ref)
      end

    end # .current_version_ref
  end # instance methods

end
