require 'rails_helper'

RSpec.describe StaffDetails, type: :model, customers: true, staff: true do

  describe 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }

    it 'has a valid factory' do
      staff_details = build(:staff_details, :random)

      expect(staff_details).to be_valid
    end

    it 'has default nil values set from DB', rgi: true do
      staff_details = build(:staff_details)

      expect(staff_details.personal).to be_nil
      expect(staff_details.emergency_contact).to be_nil
      expect(staff_details.bank).to be_nil
      expect(staff_details.tax).to be_nil
      expect(staff_details.documents).to be_nil
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
  end

end
