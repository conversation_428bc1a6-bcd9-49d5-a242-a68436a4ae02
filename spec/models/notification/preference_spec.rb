require 'rails_helper'

RSpec.describe Notification::Preference, type: :model, notifications: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:account) }
    it { is_expected.to validate_presence_of(:template_name) }

    it 'has a valid factory' do
      factory_preference = build(:notification_preference, :random)
      expect(factory_preference).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:account) }
  end

end
