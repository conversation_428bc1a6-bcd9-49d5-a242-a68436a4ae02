require 'rails_helper'

RSpec.describe EventAttendee, type: :model, team_orders: true do

  context 'validations' do
    let(:valid_email) { Faker::Internet.email }
    let(:invalid_email) { 'invalid-email' }

    it { is_expected.to validate_presence_of :first_name }
    it { is_expected.to validate_presence_of :email }

    it { is_expected.to allow_value(valid_email).for(:email) }
    it { is_expected.to_not allow_value(invalid_email).for(:email) }
    it { is_expected.to validate_uniqueness_of(:email).scoped_to(:team_admin_id).case_insensitive }
  end

  context 'associations' do
    it { is_expected.to have_many(:team_order_attendees) }
    it { is_expected.to have_many(:orders) }
    it { is_expected.to have_many(:credit_cards) }
    it { is_expected.to belong_to(:team_admin) }
  end

end
