require 'rails_helper'

RSpec.describe BillingDetails, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_presence_of(:address) }
    it { is_expected.to validate_presence_of(:suburb) }
    it { is_expected.to validate_presence_of(:phone) }

    it { is_expected.to validate_presence_of(:frequency) }
    it { is_expected.to validate_inclusion_of(:frequency).in_array(BillingDetails::VALID_FREQUENCIES) }

    it { is_expected.to validate_presence_of(:invoice_order_grouping) }
    it { is_expected.to validate_inclusion_of(:invoice_order_grouping).in_array(BillingDetails::VALID_INVOICE_ORDER_GROUPINGS) }

    it 'has a valid factory' do
      factory_billing_details = build(:billing_details, :random)
      expect(factory_billing_details).to be_valid
    end

    it 'has default values' do # from database migrations
      billing_details = BillingDetails.new
      expect(billing_details.frequency).to eq('instantly')
      expect(billing_details.order_summaries).to be_falsey
      expect(billing_details.summary_report).to be_falsey
      expect(billing_details.hide_delivery_pricing).to be_falsey
      expect(billing_details.invoice_order_grouping).to eq('address_po')
      expect(billing_details.invoice_spreadsheet).to be_falsey
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
    it { is_expected.to belong_to(:suburb) }
  end
end
