require 'rails_helper'

RSpec.describe DeliveryZone, type: :model, suppliers: true do

	context 'validations' do
		it { is_expected.to validate_presence_of :suburb_id }
		it { is_expected.to validate_presence_of :operating_wdays }
		it { is_expected.to validate_numericality_of(:delivery_fee).is_greater_than_or_equal_to(0).with_message('must be >= 0') }
		it { is_expected.to validate_numericality_of(:radius).is_greater_than_or_equal_to(0).with_message('must be >= 0') }

		it 'has a valid factory' do
			delivery_zone = build(:delivery_zone, :random)
			expect(delivery_zone).to be_valid
		end

		context 'operating_hours not set' do
			before { allow(subject).to receive(:at_least_one_time_present?).and_return(false) }
			it { is_expected.to_not validate_presence_of(:operating_hours_start) }
			it { is_expected.to_not validate_presence_of(:operating_hours_end) }
		end

		context 'operating_hours set' do
			before { allow(subject).to receive(:at_least_one_time_present?).and_return(true) }
			it { is_expected.to validate_numericality_of(:operating_hours_start).is_greater_than_or_equal_to(0).with_message('invalid time. HH:MM') }
			it { is_expected.to validate_numericality_of(:operating_hours_end).is_greater_than_or_equal_to(0).with_message('invalid time. HH:MM') }
		end

		context 'operating_wdays validations' do
			let!(:delivery_zone) { build(:delivery_zone, :random, operating_wdays: '0000000') }

			it 'should have at least one day' do
				expect(delivery_zone).to_not be_valid
			end
		end
	end

	context 'associations' do
		it { is_expected.to belong_to(:suburb) }
		it { is_expected.to belong_to(:supplier_profile) }
		it { is_expected.to have_many(:deliverable_suburbs) }
	end

end
