require 'rails_helper'

RSpec.describe Company, type: :model do

	describe 'validations' do
		it { is_expected.to validate_presence_of(:name) }
		it { is_expected.to validate_inclusion_of(:payment_term_days).in_array(Company::VALID_PAYMENT_TERM_DAYS) }
		it { is_expected.to validate_inclusion_of(:accounting_software).in_array(CustomerFlags::VALID_ACCOUNTING_SOFTWARES).allow_blank }

		it 'has default values' do # from database migrations
		  company = Company.new

		  # boolean fields - false
		  %i[can_pay_on_account invoice_by_po requires_po].each do |field|
		  	expect(company.send(field)).to be_falsey
		  end

		  # boolean fields - true
		  %i[can_pay_by_credit_card].each do |field|
		  	expect(company.send(field)).to be_truthy
		  end

		  # default payment term days
		  expect(company.payment_term_days).to eq(Company::DEFAULT_PAYMENT_TERM_DAYS)
		end

		it 'has a valid factory' do
			factory_company = build(:company, :random)
			expect(factory_company).to be_valid
		end

		it 'fails for an invalid email address' do
			invalid_emails = %w[user@foo,com user_at_foo.org example.user@foo.]
			invalid_emails.each do |email|
				company = build(:company, :random, email: email)
				expect(company).to_not be_valid
			end
		end

		it 'validates that at least 1 payment option is available' do
			pay_on_account_company = build(:company, :random, can_pay_on_account: true, can_pay_by_credit_card: false)
			expect(pay_on_account_company).to be_valid

			pay_on_credit_card_company = build(:company, :random, can_pay_on_account: false, can_pay_by_credit_card: true)
			expect(pay_on_credit_card_company).to be_valid

			no_pay_company = build(:company, :random, can_pay_on_account: false, can_pay_by_credit_card: false)
			expect(no_pay_company).to_not be_valid
		end

	end

	describe 'associations' do
		it { is_expected.to have_many(:customer_profiles) }
		it { is_expected.to have_many(:rate_cards) }
		it { is_expected.to have_and_belong_to_many(:menu_sections) }
		it { is_expected.to have_many(:supplier_markup_overrides) }
		it { is_expected.to have_many(:promotion_subscriptions) }
	end

	describe 'callbacks', callbacks: true do
		context 'company with attached customers' do
			let!(:non_instant_billing_frequency) { (BillingDetails::VALID_FREQUENCIES - ['instantly']).sample }
			let!(:company) { create(:company, :random, can_pay_on_account: true) }
			let!(:customer1) { create(:customer_profile, :random, company: company) }
			let!(:billing_details1) { create(:billing_details, :random, customer_profile: customer1, frequency: non_instant_billing_frequency) }
			let!(:customer2) { create(:customer_profile, :random, company: company) }
			let!(:billing_details2) { create(:billing_details, :random, customer_profile: customer2, frequency: non_instant_billing_frequency) }

			it 'sets attached customers billing preference to `instantly` if can_pay_on_account is set to false' do
				company.update(can_pay_on_account: false)

				expect(customer1.reload.billing_frequency).to eq('instantly')
				expect(customer2.reload.billing_frequency).to eq('instantly')
			end
		end # company customer
	end # callbacks

end
