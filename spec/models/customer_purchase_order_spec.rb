require 'rails_helper'

RSpec.describe CustomerPurchaseOrder, type: :model, customers: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:po_number) }
    it { is_expected.to validate_presence_of(:customer_profile_id) }
    it { is_expected.to validate_uniqueness_of(:po_number).scoped_to(:customer_profile_id).with_message('should have one po number') }
    it 'has a valid factory' do
      factory_cpo = build(:customer_purchase_order, :random)
      expect(factory_cpo).to be_valid
    end
    it 'is active by default' do
      cpo = CustomerPurchaseOrder.new
      expect(cpo).to be_active
    end
  end

  context 'associations' do
    it { is_expected.to have_many(:orders) }
    it { is_expected.to have_many(:invoices) }
    it { is_expected.to belong_to(:customer_profile) }
  end

  context 'instance methods'
  describe '.info' do
    let!(:customer_purchase_order) { create(:customer_purchase_order, :random) }

    it 'sends back po number within the info' do
      expect(customer_purchase_order.info).to eq(customer_purchase_order.po_number)
    end

    it 'sends back description within the info only if present' do
      customer_purchase_order.update_column(:description, Faker::Name.name)
      expected_info = "#{customer_purchase_order.po_number} - #{customer_purchase_order.description}"
      expect(customer_purchase_order.info).to eq(expected_info)
    end
  end

  context 'with associated report_data' do
    let!(:customer_purchase_order) { create(:customer_purchase_order, :random) }
    let!(:report_datum) { create(:report_datum, :random, customer_purchase_order: customer_purchase_order) }

    it 'destroys the report data when destroying purchase order' do
      customer_purchase_order.destroy

      expect{ customer_purchase_order.reload }.to raise_error(ActiveRecord::RecordNotFound)
      expect{ report_datum.reload }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end

end
