require 'rails_helper'

RSpec.describe EventLog, type: :model, event_logs: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:event) }
    it { is_expected.to validate_inclusion_of(:event).in_array(EventLog::VALID_EVENTS) }
    it { is_expected.to validate_presence_of(:severity) }
    it { is_expected.to validate_inclusion_of(:severity).in_array(EventLog::VALID_SEVERITIES) }

    it 'has a valid factory' do
      factory_event_log = build(:event_log, :random)
      expect(factory_event_log).to be_valid
    end

    it 'has defaults' do
      new_event_log = EventLog.new
      expect(new_event_log.severity).to eq('info')
      expect(new_event_log.info).to eq({})
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:loggable) }
    it { is_expected.to belong_to(:scopable) }
    it { is_expected.to have_many(:views) }
    it { is_expected.to belong_to(:assigned_to) }
  end
end
