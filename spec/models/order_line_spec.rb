require 'rails_helper'

RSpec.describe OrderLine, type: :model do

	context 'validations' do
		it { is_expected.to validate_presence_of(:quantity) }
		it { is_expected.to validate_presence_of(:location_id) }
		it { is_expected.to validate_presence_of(:name).on(:update) }
		it { is_expected.to validate_presence_of(:price).on(:update) }
		it { is_expected.to validate_presence_of(:cost).on(:update) }
		it { is_expected.to validate_inclusion_of(:status).in_array(OrderLine::VALID_ORDER_LINE_STATUSES) }
		it { is_expected.to validate_inclusion_of(:payment_status).in_array(OrderLine::VALID_ORDER_LINE_PAYMENT_STATUSES) }

		it 'has a valid factory' do
			factory_order_lines = build(:order_line, :random)
			expect(factory_order_lines).to be_valid
		end

		it 'has default values' do
			new_order_line = OrderLine.new

			expect(new_order_line.status).to eq('pending')
			expect(new_order_line.last_errors).to be_empty
		end
	end

	context 'associations' do
		it { is_expected.to belong_to(:order) }
		it { is_expected.to belong_to(:location) }
		it { is_expected.to belong_to(:supplier_profile) }
		it { is_expected.to belong_to(:category) }
		it { is_expected.to belong_to(:menu_item) }
		it { is_expected.to have_one(:menu_section) }
		it { is_expected.to belong_to(:serving_size) }
		it { is_expected.to belong_to(:team_order_attendee) }
	end

	context 'instance methods' do
		describe '.price_inc_gst' do
			let!(:price) { rand(5.22...10.98) }
			let!(:order_line) { create(:order_line, :random, price: price, is_gst_free: false, is_gst_inc: false) }

			before do
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(0.1)
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(0.15)
			end

			it 'returns the gst inclusive pricing (based on country' do
				expect(order_line.price_inc_gst(gst_country: 'au').round(2)).to be_within(0.2).of((price * 1.1).round(2))
				expect(order_line.price_inc_gst(gst_country: 'nz').round(2)).to be_within(0.2).of((price * 1.15).round(2))
			end

			it 'reutrns only the price for a gst free or gst inc order line ' do
				if[true, false].sample
					order_line.update_column(:is_gst_free, true)
				else
					order_line.update_column(:is_gst_inc, true)
				end

				expect(order_line.price_inc_gst(gst_country: %i[au nz].sample).round(2)).to eq(price.round(2))
			end
		end

		describe '.total_price' do
			let!(:order_line) { create(:order_line, :random, quantity: 10, price: 5.33, is_gst_free: false, is_gst_inc: false) }

			before do
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(0.1)
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(0.15)
			end

			it 'returns multiple of price and quantity for non-gst free and gst free items' do
				order_line.update_column(:is_gst_free, [true, false].sample)

				expect(order_line.total_price(gst_country: :au)).to eq(53.3) # price * quantity
				expect(order_line.total_price(gst_country: :nz)).to eq(53.3) # price * quantity
			end

			context 'with gst inclusive pricing' do
				before do
					order_line.update_column(:is_gst_inc, true) # make order line pricing gst inclusive
				end

				it 'returns multiple of gst exc price and quantity for order lines with is_gst_inc as true' do
					expect(order_line.total_price(gst_country: :au)).to eq(48.45) # price exc gst * quantity
					expect(order_line.total_price(gst_country: :nz)).to eq(46.35) # price exc gst * quantity
				end

				context 'with order delivery suburb' do
					let!(:country_code) { %w[AU NZ].sample }
					let!(:delivery_suburb) { create(:suburb, :random, country_code: country_code)}
					let!(:order) { create(:order, :confirmed, delivery_suburb: delivery_suburb)}

					before do
						order_line.update_column(:order_id, order.id) # connect order line to order with delivery suburb
					end

					it 'returns multiple of gst exc price and quantity for order lines with is_gst_inc as true' do
						expected_total_price = (order_line.price / (1 + yordar_credentials(:yordar, :gst_percent, country_code.downcase.to_sym)) * order_line.quantity).round(2)
						expect(order_line.total_price).to eq(expected_total_price)

						case country_code
						when 'AU'
							expect(order_line.total_price).to eq(48.45) # price exc gst * quantity
						when 'NZ'
							expect(order_line.total_price).to eq(46.35) # price exc gst * quantity
						end
					end
				end
			end			
		end

		describe '.total_cost' do
			let!(:order_line) { create(:order_line, :random, quantity: 10, cost: 4.33, is_gst_free: false, is_gst_inc: false) }

			before do
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(0.1)
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(0.15)
			end

			it 'returns multiples of cost and quantity' do
				order_line.update_column(:is_gst_free, [true, false].sample)

				expect(order_line.total_cost(gst_country: :au)).to eq(43.3) # price * quantity
				expect(order_line.total_cost(gst_country: :nz)).to eq(43.3) # price * quantity
			end

			context 'with gst inclusive pricing' do
				before do
					order_line.update_column(:is_gst_inc, true) # make order line pricing gst inclusive
				end

				it 'returns multiple of gst exc price and quantity for order lines with is_gst_inc as true' do
					expect(order_line.total_cost(gst_country: :au)).to eq(39.36) # price exc gst * quantity
					expect(order_line.total_cost(gst_country: :nz)).to eq(37.65) # price exc gst * quantity
				end

				context 'with order delivery suburb' do
					let!(:country_code) { %w[AU NZ].sample }
					let!(:delivery_suburb) { create(:suburb, :random, country_code: country_code)}
					let!(:order) { create(:order, :confirmed, delivery_suburb: delivery_suburb)}

					before do
						order_line.update_column(:order_id, order.id) # connect order line to order with delivery suburb
					end

					it 'returns multiple of gst exc price and quantity for order lines with is_gst_inc as true' do
						expected_total_cost = (order_line.cost / (1 + yordar_credentials(:yordar, :gst_percent, country_code.downcase.to_sym)) * order_line.quantity).round(2)
						expect(order_line.total_cost).to eq(expected_total_cost)
						
						case country_code
						when 'AU'
							expect(order_line.total_cost).to eq(39.36) # price exc gst * quantity
						when 'NZ'
							expect(order_line.total_cost).to eq(37.65) # price exc gst * quantity
						end
					end
				end
			end
		end
	end

end


