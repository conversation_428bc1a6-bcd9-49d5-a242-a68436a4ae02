require 'rails_helper'

RSpec.describe CustomerFlags, type: :model, customers: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:customer_profile) }
    it { is_expected.to validate_inclusion_of(:default_orders_view).in_array(CustomerFlags::VALID_ORDER_VIEWS) }
    it { is_expected.to validate_inclusion_of(:requires_department_identity).in_array(CustomerFlags::VALID_DEPARTMENT_ID_FORMATS).allow_blank }
    it { is_expected.to validate_inclusion_of(:accounting_software).in_array((CustomerFlags::VALID_ACCOUNTING_SOFTWARES + ['none'])).allow_blank }

    it 'has a valid factory' do
      customer_flags = build(:customer_flags)

      expect(customer_flags).to be_valid
    end

    it 'has default boolean values of false' do
      customer_flags = build(:customer_flags)

      boolean_fields = (
        CustomerProfile::CUSTOMER_REVIEW_FLAGS +
        CustomerProfile::CUSTOMER_HAS_FLAGS
      )

      boolean_fields.each do |field|
        expect(customer_flags.send(field)).to be_falsey
      end
    end

    it 'has default (non-boolean) values set from DB', rgi: true do
      customer_flags = build(:customer_flags)
      expect(customer_flags.default_orders_view).to eq('list')
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:customer_profile) }
  end
end
