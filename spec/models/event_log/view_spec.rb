require 'rails_helper'

RSpec.describe EventLog::View, type: :model, event_logs: true do

  context 'validations' do
    it { is_expected.to validate_presence_of(:event_log) }
    it { is_expected.to validate_presence_of(:user) }

    it { is_expected.to validate_uniqueness_of(:event_log_id).scoped_to(:user_id) }

    it 'has a valid factory' do
      factory_read = build(:event_log_view, :random)
      expect(factory_read).to be_valid
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:event_log) }
    it { is_expected.to belong_to(:user) }
  end

end
