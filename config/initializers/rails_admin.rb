require 'rails_admin/config/actions/sign_in_as'
require 'rails_admin/config/actions/deprecate_user'
require 'rails_admin/config/actions/favourite_customer'
require 'rails_admin/config/actions/my_favourite_customers'
require 'rails_admin/config/actions/custom_order'
require 'rails_admin/config/actions/clone'
require 'rails_admin/config/actions/bulk_update'
require 'rails_admin/config/actions/duplicate_supplier'
require 'rails_admin/config/where_builder_extention'

RailsAdmin.config do |config|

  config.main_app_name = 'Yordar Dashboard'
  config.authenticate_with do
    warden.authenticate! scope: :user
  end
  config.current_user_method { current_user }
  config.authorize_with :cancancan
  # config.excluded_models << Rich::RichFile
  # config.attr_accessible_role { :admin }
  config.total_columns_width = 1000
  config.included_models = %w[
    CustomerProfile
    AccessPermission
    CustomerQuote
    DeliveryOverride
    SupplierProfile
    Supplier::MarkupOverride
    EventSupplier
    Order
    CustomOrder
    User
    Category
    WeeklyMenuClient
    WeeklyMenu
    EmailTemplate
    Doorkeeper::Application
    Dear::Account
    Dear::Category
  ]

  config.actions do
    #
    # ==== root actions ====
    #
    root :new_admin do
      link_icon 'icon-home'
    end

    dashboard do # mandatory
      statistics false
    end

    root :reports do
      link_icon 'icon-book'
      visible do
        user = bindings[:controller].current_user
        user.super_admin?
      end
    end

    root :accounting do
      link_icon 'icon-book'
      visible do
        user = bindings[:controller].current_user
        user.super_admin?
      end
    end

    root :clear_cache do
      link_icon 'icon-trash'
      visible do
        user = bindings[:controller].current_user
        user.super_admin?
      end
    end

    #
    # ==== collection actions ====
    #
    index # mandatory
    new do
      only %w[AccessPermission DeliveryOverride Category EmailTemplate Dear::Account Dear::Category Supplier::MarkupOverride]
    end
    #
    # ==== member actions ====
    #
    show do
      only %w[CustomerQuote]
    end
    edit do
      except %w[CustomerQuote EventSupplier Order CustomOrder]
    end
    delete do
      only %w[AccessPermission DeliveryOverride]
    end
    sign_in_as do
      only %w[CustomerProfile SupplierProfile EventSupplier]
    end
    custom_order do
      only %w[CustomerProfile CustomOrder]
    end
    clone do
      only %w[CustomOrder]
    end
    duplicate_supplier do
      only %w[SupplierProfile]
    end
    bulk_update do
      only %w[SupplierProfile]
    end
    my_favourite_customers do
      only %w[CustomerProfile]
    end
    deprecate_user do
      only %w[User]
    end
  end

  config.actions do
    favourite_customer do
      visible do
        %w[CustomerProfile].include?(bindings[:abstract_model].model.to_s)
      end
    end
  end

  config.model 'CustomerProfile' do

    weight 10
    label 'Customers'
    object_label_method :customer_name

    list do

      field :customer_name do
        label 'User Name'
      end

      # in-visible field to only let admins filter by user email
      field :user do
        label 'Attached User'
        visible false
        filterable true
        queryable true
        searchable [:email]
      end

      field :company_name

      field :company do
        label 'Linked company Name'
        searchable [:name]
        queryable true
      end

      field :team_admin
      field :company_team_admin
    end

    update do

      group :default do
        field :email do
          label 'Login Email'
          read_only true
          visible do
            logged_user = bindings[:controller].current_user
            bindings[:object].user.present? && (logged_user.admin? || logged_user.super_admin?)
          end
        end

        field :supplier_profiles
        field :company_name
        field :contact_phone do
          label 'Phone'
        end
        field :mobile
        field :role, :enum do
          enum do
            [''] + CustomerProfile::VALID_ROLES
          end
        end
        field :team_admin
        field :company_team_admin

        field :active_adminable_customer_profiles do
          label 'Accessible Customers'
          visible do
            bindings[:object].company_team_admin?
          end
        end

        field :admin_notes do
          label 'Notes for customer requirements'
          help 'Will be displayed when signed in as customer'
        end
      end

      group :user do
        field :linked_user do
          pretty_value do
            if bindings[:object].user.present?
              bindings[:view].link_to(bindings[:object].user.name, bindings[:view].edit_path(model_name: 'user', id: bindings[:object].user.id), target: :blank)
            else
              'Not Linked'
            end
          end
          read_only true
        end
      end

    end
  end

  config.model 'AccessPermission' do
    parent CustomerProfile
    weight 15

    list do
      field :admin
      field :customer_profile
      field :active
      field :scope
    end

    edit do
      field :admin do
        inline_edit false
        associated_collection_cache_all true
        associated_collection_scope do
          proc do |scope|
            scope.joins(:user).where(users: { is_active: true }).order('users.firstname, users.lastname')
          end
        end
      end
      field :customer_profile do
        inline_edit false
        associated_collection_cache_all true
        associated_collection_scope do
          proc do |scope|
            scope.joins(:user).where(users: { is_active: true }).order('users.firstname, users.lastname')
          end
        end
      end
      field :active
      field :scope, :enum do
        enum do
          AccessPermission::VALID_SCOPES.map{|scope| [scope.humanize, scope] }
        end
      end
    end

  end

  config.model 'CustomerQuote' do
    parent CustomerProfile
    weight 17
    label 'Quotes'

    list do
      field :customer_profile
      field :kind do
        pretty_value do
          bindings[:object].kind.humanize.titleize
        end
      end
      field :status
    end
  end

  config.model 'DeliveryOverride' do
    parent CustomerProfile
    weight 19

    list do
      field :customer_profile
      field :supplier_kind
      field :supplier_profile
      field :customer_override
      field :supplier_override
      field :active
    end

    edit do
      field :customer_profile
      field :supplier_kind, :enum do
        enum do
          DeliveryOverride::VALID_KINDS.map{|kind| [kind.humanize, kind] }
        end
      end
      field :supplier_profile do
        help 'Keep blank if supplier kind is not set to `specific`'
      end
      field :customer_override
      field :supplier_override
      field :active
    end
  end

  config.model 'SupplierProfile' do
    weight 30
    label 'Supplier'
    label_plural 'Suppliers'

    # calculated field
    configure :calculated_yordar_commission do
      label 'Yordar commission'
      formatted_value do
        supplier = bindings[:object]
        (supplier.yordar_commission * 100).round(2)
      end
      read_only true # won't be editable in forms (alternatively, hide it in edit section)
    end

    list do
      field :user do
        label 'User Name'
        filterable true
        queryable true
        searchable [:email, :firstname, :lastname]
      end
      field :company_name
      field :is_searchable
      field :commission_rate do
        label 'Markdown'
        visible do
          user = bindings[:controller].current_user
          user.super_admin? || user.admin?
        end
        pretty_value do
          bindings[:view].render partial: 'rails_admin/supplier_profiles/commission_rate_input', locals: { field: self, object: bindings[:object] }
        end
      end
      field :markup do
        visible do
          user = bindings[:controller].current_user
          user.super_admin? || user.admin?
        end
        pretty_value do
          bindings[:view].render partial: 'rails_admin/supplier_profiles/markup_input', locals: { field: self, object: bindings[:object] }
        end
      end
      field :calculated_yordar_commission do
        visible do
          user = bindings[:controller].current_user
          user.super_admin? || user.admin? || user.allow_all_supplier_access?
        end
      end
      field :team_supplier
      field :lead_mode do
        pretty_value do
          # result from here will be passed to a view
          case bindings[:object].try(:lead_mode)
          when 'by_hour'
            bindings[:view].content_tag(:span, 'Hrs', { class: 'badge badge-inverse' })
          else
            bindings[:view].content_tag(:span, 'Day', { class: 'badge badge-warning' })
          end
        end
      end
    end

    edit do

      group :default do
        field :login_email do
          label 'Login Email'
          pretty_value do
            bindings[:object].user.email
          end
          read_only true
          visible do
            bindings[:object].user.present? && bindings[:controller].current_user.super_admin?
          end
        end
      end

      group :customers do
        field :customer_profiles
      end

      group :company do
        field :company_name
        field :abn_acn
        field :liquor_license_no
        field :email
        field :phone
        field :mobile
        field :company_address do
          label 'Address'
        end
        field :company_address_suburb_id do
          label 'Suburb / Postcode'
          def render
            bindings[:view].render partial: 'supplier_profile_suburb', locals: { profile: bindings[:object] }
          end
        end
        field :team_supplier
        field :lead_mode, :enum do
          label 'Lead Mode'
          enum do
            SupplierProfile::VALID_LEAD_MODES.map{|mode| [mode.humanize, mode] }
          end
        end
      end

      group :user do
        field :linked_user do
          pretty_value do
            if bindings[:object].user.present?
              bindings[:view].link_to(bindings[:object].user.name, bindings[:view].edit_path(model_name: 'user', id: bindings[:object].user.id), target: :blank)
            else
              'Not Linked'
            end
          end
          read_only true
        end
      end

      group :settings do
        field :commission_rate do
          label 'Supplier discount markdown'
          visible do
            user = bindings[:controller].current_user
            user.super_admin? || user.business != 'cs'
          end
        end
        field :markup do
          visible do
            user = bindings[:controller].current_user
            user.super_admin? || user.business != 'cs'
          end
        end
        field :calculated_yordar_commission
        field :price_indication do
          visible do
            user = bindings[:controller].current_user
            user.super_admin? || user.business != 'cs'
          end
        end
        field :minimum_delivery_fee
        field :close_from do
          help "Date must be on or before #{Time.zone.parse(Rails.application.credentials.yordar[:closure_start_date]).to_s(:date_verbose)}"
          visible do
            user = bindings[:controller].current_user
            user.super_admin? || user.business != 'cs'
          end
        end
        field :close_to do
          help "Date must be on or after #{Time.zone.parse(Rails.application.credentials.yordar[:closure_end_date]).to_s(:date_verbose)}"
          visible do
            user = bindings[:controller].current_user
            user.super_admin? || user.business != 'cs'
          end
        end
        field :bank_account_number do
          visible do
            user = bindings[:controller].current_user
            user.super_admin? || user.business != 'cs'
          end
        end
        field :bsb_number do
          visible do
            user = bindings[:controller].current_user
            user.super_admin? || user.business != 'cs'
          end
        end
      end

    end
  end

  config.model 'Supplier::MarkupOverride' do
    parent SupplierProfile
    weight 37
    label 'Markup Overrides'
    visible do
      user = bindings[:controller].current_user
      user.super_admin? || user.admin? || user.allow_all_supplier_access?
    end

    # calculated field
    configure :calculated_yordar_commission do
      label 'Yordar commission'
      formatted_value do
        override = bindings[:object]
        (override.yordar_commission * 100).round(2)
      end
      read_only true # won't be editable in forms (alternatively, hide it in edit section)
    end

    list do
      field :supplier_profile do
        label 'Supplier'
      end
      field :company
      field :customer_profile do
        label 'Customer'
      end
      field :markup do
        pretty_value do
          bindings[:object].markup || "(default: #{bindings[:object].supplier_profile.markup})"
        end
      end
      field :commission_rate do
        pretty_value do
          bindings[:object].commission_rate || "(default: #{bindings[:object].supplier_profile.commission_rate})"
        end
      end
      field :calculated_yordar_commission
      field :active do
        label 'Is Active'
      end
    end

    edit do
      field :supplier_profile do
        label 'Supplier'
      end
      field :company
      field :customer_profile do
        label 'Targeted to a particular Customer'
      end
      field :markup do
        html_attributes do
          { placeholder: bindings[:object].markup.blank? ? "defaults to #{bindings[:object]&.supplier_profile&.markup}%" : '' }
        end
        help do
          "defaults to #{bindings[:object]&.supplier_profile&.markup}%"
        end
      end
      field :commission_rate do
        html_attributes do
          { placeholder: bindings[:object].commission_rate.blank? ? "defaults to #{bindings[:object]&.supplier_profile&.commission_rate}%" : '' }
        end
        help do
          "defaults to #{bindings[:object]&.supplier_profile&.commission_rate}%"
        end
      end
      field :active
    end
  end

  config.model 'Order' do
    weight 40

    list do
      filters [:delivery_at]
      field :id
      field :name
      field :order_type
      field :order_variant
      field :customer_name do
        label 'Customer - Company'
        def value
          if (customer = bindings[:object].customer_profile.presence)
            customer_names = []
            customer_names << "#{customer.name} ##{customer.id}"
            if (customer_company = customer.company.presence)
              customer_names << customer_company.name
            end
            customer_names.compact.join(' - ')
          else
            'N/A'
          end
        end
      end
      field :status do
        def value
          if bindings[:object].status == 'new' && !bindings[:object].is_event_order?
            accepted_sup = bindings[:object].order_lines.where(status: 'accepted').count
            total_sup = bindings[:object].supplier_profiles.distinct.count(:id)
            "#{accepted_sup} out of #{total_sup} confirmed this order"
          else
            bindings[:object].status
          end
        end
      end
      field :delivery_at  do
        strftime_format '%d-%m-%Y %H:%M'
        sort_reverse false
      end
      field :delivery_suburb
      field :customer_total
    end
  end

  config.model 'CustomOrder' do
    label 'Custom Orders'
    weight 42
    list do
      field :id
      field :name
      field :order_variant
      field :customer_profile
      field :company_name
      field :delivery_at  do
        strftime_format '%d-%m-%Y %H:%M'
        sort_reverse false
      end

      field :delivery_suburb

      field :status
    end
  end

  config.model 'User' do
    # rails_admin do
    weight 50
    label 'User'

    list do
      field :firstname
      field :lastname
      field :email
      field :admin do
        label 'Is Yordar Admin?'
      end
      field :mailchimp_subscribed
      field :is_active
    end

    update do
      group :details do
        field :email
        field :secondary_email
        field :firstname
        field :lastname
        field :gender, :enum do
          enum do
            User::VALID_GENDERS
          end
        end
        field :password
        field :password_confirmation
      end

      group :advanced do
        field :super_admin do
          visible do
            logged_in_user = bindings[:controller].current_user
            logged_in_user.super_admin?
          end
        end

        field :admin do
          label 'Yordar Admin'
          help 'Optional. if user is not an admin, they are assigned in customer category'
          visible do
            logged_in_user = bindings[:controller].current_user
            logged_in_user.super_admin?
          end
        end

        field :can_access_suppliers do
          label 'Supplier Admin'
          help 'Optional. A supplier admin can access suppliers from the admin panel'
          visible do
            logged_in_user = bindings[:controller].current_user
            logged_in_user.super_admin?
          end
        end

        field :supplier_profiles do
          visible do
            logged_in_user = bindings[:controller].current_user
            logged_in_user.super_admin? && bindings[:object].can_access_suppliers && !bindings[:object].allow_all_supplier_access
          end
          help 'The suppliers this user can access'
        end

        field :allow_all_supplier_access do
          visible do
            logged_in_user = bindings[:controller].current_user
            logged_in_user.super_admin?
          end
          help 'Allow this user access to all suppliers'
        end

        # Admin has all rights(controlled by cancan)
        # Super admin is rails_admin out-of-the-box-user
        # displays all, and not easily customized with CanCan
        #
        # field :super_admin
        field :is_active
        field :mailchimp_subscribed
        field :xero_push_fault
        field :business, :enum do
          enum do
            User::VALID_BUSINESS_TYPES.map{|type| [(type == 'yr' ? 'Yordar' : 'Category Solutions'), type] }
          end
        end
      end

      group :information do
        field :confirmation_sent_at
        field :unconfirmed_email
        field :confirmation_token
        field :user_confirmation_url do
          visible do
            logged_in_user = bindings[:controller].current_user
            user = bindings[:object]
            (logged_in_user.super_admin? || logged_in_user.admin?) && (!user.confirmed? || user.unconfirmed_email.present?)
          end
          formatted_value do
            sleep 0.5
            confirmation_url = Rails.application.routes.url_helpers.user_confirmation_url(confirmation_token: bindings[:object].confirmation_token, host: ActionMailer::Base.default_url_options[:host])
            bindings[:view].link_to(confirmation_url, confirmation_url, target: '_blank')
          end
          read_only true
        end
        field :last_sign_in_ip
        field :current_sign_in_ip
        field :last_sign_in_at
        field :current_sign_in_at
        field :sign_in_count
        field :remember_created_at
        field :reset_password_sent_at
        field :profile do
          read_only true
        end
      end
    end

    create do
      field :email
      field :firstname
      field :lastname
      field :gender, :enum do
        enum do
          User::VALID_BUSINESS_TYPES.map{|type| [(type == 'yr' ? 'Yordar' : 'Category Solutions'), type] }
        end
      end
      field :password
      field :password_confirmation
      field :admin do
        label 'Yordar Admin'
        help 'Optional. if user is not an admin, they are assigned in customer category'
      end
      field :can_access_suppliers do
        label 'Supplier Admin'
        help 'Optional. A supplier admin can access suppliers from the admin panel'
      end
      field :mailchimp_subscribed
      field :business, :enum do
        enum do
          User::VALID_BUSINESS_TYPES.map{|type| [(type == 'yr' ? 'Yordar' : 'Category Solutions'), type] }
        end
      end
    end
  end

  config.model 'WeeklyMenuClient' do
    weight 137
    label 'Weekly Menu Clients'
    label_plural 'Weekly Menu Clients'
    visible do
      user = bindings[:controller].current_user
      user.super_admin?
    end

    list do
      field :id
      field :title do
        label 'Name'
      end
      field :slug do
        formatted_value do
          bindings[:view].link_to(bindings[:object].slug, "/menus/#{bindings[:object].slug}")
        end
      end
      field :navigation_title do
        label 'Nav Name'
      end
    end

    create do
      field :title
      field :navigation_title
      field :slug

      field :customer_profile do
        label 'Client\'s User Account'

        associated_collection_cache_all true
        associated_collection_scope do
          proc do |scope|
            scope.with_a_company
          end
        end
      end

      group :content do
        field :header_image, :carrierwave
        field :logo, :carrierwave
        field :header_content do
          css_class 'tinymce'
        end

        field :abbreviations, :text
      end

      group :settings do
        field :monday, :boolean
        field :tuesday, :boolean
        field :wednesday, :boolean
        field :thursday, :boolean
        field :friday, :boolean
        field :saturday, :boolean
        field :sunday, :boolean
      end
    end

    update do
      field :title
      field :navigation_title
      field :slug

      field :customer_profile do
        label 'Client\'s User Account'

        associated_collection_cache_all true
        associated_collection_scope do
          proc do |scope|
            scope.with_a_company
          end
        end
      end

      group :content do
        field :header_image, :carrierwave
        field :logo, :carrierwave
        field :header_content do
          css_class 'tinymce'
        end
        field :abbreviations, :text
      end

      group :settings do
        field :monday, :boolean
        field :tuesday, :boolean
        field :wednesday, :boolean
        field :thursday, :boolean
        field :friday, :boolean
        field :saturday, :boolean
        field :sunday, :boolean
      end
    end
  end

  config.model 'WeeklyMenu' do
    weight 138
    label 'Weekly Menus'
    label_plural 'Weekly Menus'
    visible do
      user = bindings[:controller].current_user
      user.super_admin?
    end

    list do
      field :id
      field :week_of do
        formatted_value do
          value.strftime('%e %b %y')
        end
      end
      field :weekly_menu_client do
        label 'Client Name'
      end
    end

    create do
      field :weekly_menu_client, :belongs_to_association do
        label 'Client'
        inline_add false
        inline_edit false
      end

      field :week_of

      group :content do
        field :monday do
          css_class 'tinymce'
        end

        field :monday_supplier, :belongs_to_association do
          label 'Monday Supplier'
          inline_add false
          inline_edit false
        end

        field :tuesday do
          css_class 'tinymce'
        end

        field :tuesday_supplier, :belongs_to_association do
          label 'Tuesday Supplier'
          inline_add false
          inline_edit false
        end

        field :wednesday do
          css_class 'tinymce'
        end

        field :wednesday_supplier, :belongs_to_association do
          label 'Wednesday Supplier'
          inline_add false
          inline_edit false
        end

        field :thursday do
          css_class 'tinymce'
        end

        field :thursday_supplier, :belongs_to_association do
          label 'Thursday Supplier'
          inline_add false
          inline_edit false
        end

        field :friday do
          css_class 'tinymce'
        end

        field :friday_supplier, :belongs_to_association do
          label 'Friday Supplier'
          inline_add false
          inline_edit false
        end

        field :saturday do
          css_class 'tinymce'
        end

        field :saturday_supplier, :belongs_to_association do
          label 'Saturday Supplier'
          inline_add false
          inline_edit false
        end

        field :sunday do
          css_class 'tinymce'
        end

        field :sunday_supplier, :belongs_to_association do
          label 'Sunday Supplier'
          inline_add false
          inline_edit false
        end
      end
    end

    update do
      field :weekly_menu_client, :belongs_to_association do
        label 'Client'
        inline_add false
        inline_edit false
      end

      field :week_of

      group :content do
        field :monday do
          css_class 'tinymce'
        end

        field :monday_supplier, :belongs_to_association do
          label 'Monday Supplier'
          inline_add false
          inline_edit false
        end

        field :tuesday do
          css_class 'tinymce'
        end

        field :tuesday_supplier, :belongs_to_association do
          label 'Tuesday Supplier'
          inline_add false
          inline_edit false
        end

        field :wednesday do
          css_class 'tinymce'
        end

        field :wednesday_supplier, :belongs_to_association do
          label 'Wednesday Supplier'
          inline_add false
          inline_edit false
        end

        field :thursday do
          css_class 'tinymce'
        end

        field :thursday_supplier, :belongs_to_association do
          label 'Thursday Supplier'
          inline_add false
          inline_edit false
        end

        field :friday do
          css_class 'tinymce'
        end

        field :friday_supplier, :belongs_to_association do
          label 'Friday Supplier'
          inline_add false
          inline_edit false
        end

        field :saturday do
          css_class 'tinymce'
        end

        field :saturday_supplier, :belongs_to_association do
          label 'Saturday Supplier'
          inline_add false
          inline_edit false
        end

        field :sunday do
          css_class 'tinymce'
        end

        field :sunday_supplier, :belongs_to_association do
          label 'Sunday Supplier'
          inline_add false
          inline_edit false
        end
      end
    end
  end

  config.model 'Category' do
    weight 45
    visible do
      user = bindings[:controller].current_user
      user.super_admin?
    end

    list do
      field :id
      field :name
      field :group
      field :weight
      field :show_in_menu
      field :show_in_homepage
    end
    edit do
      field :name
      field :group, :enum do
        enum do
          Category::SUPPLIER_CATEGORIES
        end
      end
      field :weight do
        html_attributes min: 1
      end
      field :show_in_menu do
        label 'Show in supplier menu export/import'
      end
      field :show_in_homepage
    end

  end

  config.model EmailTemplate do
    weight 155
    label 'Email Templates'
    visible do
      user = bindings[:controller].current_user
      user.super_admin?
    end
    list do
      sort_by :position

      field :name
      field :position
      field :account_type
      field :kind
      field :can_override
      field :variations
    end

    edit do
      field :name, :enum do
        enum do
          [''] + EmailTemplate::VALID_TEMPLATE_NAMES
        end
      end
      field :account_type, :enum do
        enum do
          EmailTemplate::VALID_ACCOUNT_TYPES
        end
      end
      field :kind, :enum do
        enum do
          [''] + EmailTemplate::VALID_KINDS
        end
      end
      field :position
      field :variations, :pg_string_array do
        html_attributes do
          { maxlength: 100 }
        end
      end
      field :can_override
    end
  end

  config.model 'Doorkeeper::Application' do
    weight 160
    label 'OAuth Applications'
    visible do
      # Always show for super admins
      user = bindings[:controller].current_user
      user.super_admin?
    end

    list do
      field :name
      field :uid
    end

    edit do
      field :name
      field :uid
      field :secret do
        read_only true
      end
      field :owner
    end
  end

  config.model 'Dear::Account' do
    weight 170
    label 'Supplier Account'
    visible do
      user = bindings[:controller].current_user
      user.super_admin?
    end

    list do
      field :supplier_profile do
        label 'Supplier'
        pretty_value do
          supplier = bindings[:object].supplier_profile
          "#{supplier.name} - ##{supplier.id}"
        end
      end
      field :account_id do
        label 'Account ID'
      end
      field :active
    end

    edit do
      field :supplier_profile
      field :account_id do
        label 'Account ID'
      end
      field :api_key
      field :customer_id do
        label 'Yordar Customer ID'
      end
      field :active
      field :price_tier
      field :dietary_attribute
    end
  end

  config.model 'Dear::Category' do
    weight 175
    label 'Supplier Categories'
    visible do
      user = bindings[:controller].current_user
      user.super_admin?
    end

    list do
      field :supplier_profile do
        label 'Supplier'
        pretty_value do
          supplier = bindings[:object].supplier_profile
          "#{supplier.name} - ##{supplier.id}"
        end
      end
      field :account_id do
        pretty_value do
          dear_account = bindings[:object].dear_account
          dear_account.account_id
        end
      end
      field :name
      field :override_name
    end

    edit do
      field :dear_account do
        label 'Dear Account'
      end
      field :category_id do
        label 'Category UUID'
      end
      field :name
      field :override_name
    end
  end

end
