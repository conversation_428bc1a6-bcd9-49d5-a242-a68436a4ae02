# By default, it will delete failed jobs (and it always deletes successful jobs). If you want to keep failed jobs, set Delayed::Worker.destroy_failed_jobs = false. The failed jobs will be marked with non-null failed_at.
Delayed::Worker.destroy_failed_jobs = false

Delayed::Backend::ActiveRecord::Job.logger.level = 1

# You should set this to the longest time you think the job could take.
Delayed::Worker.max_run_time = 15.minutes

Delayed::Worker.raise_signal_exceptions = :term

# Delay jobs in both development, staging and prod, but not in test
Delayed::Worker.delay_jobs = !Rails.env.test?

Delayed::Worker.queue_attributes = {
  instant: { priority: 1 }, # e.g. Woolworths
  developer: { priority: 10 }, # e.g. developer based notification (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>)
  data_integrity: { priority: 20 }, # e.g. Extra data steps ( Future Order Lines)
  notifications: { priority: 30 }, # e.g. notifications / emails
  low_priority: { priority: 40 } # e.g. cache based / hubspot / everything else # default
}

Delayed::Worker.default_queue_name = 'low_priority'
