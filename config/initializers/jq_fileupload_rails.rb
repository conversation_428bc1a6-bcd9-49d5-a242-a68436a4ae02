# The jquery.iframe-transport fallback transport has some special caveats 
# regarding the response data type, http status, and character encodings. 
# jquery-fileupload-rails includes a middleware that handles these inconsistencies seamlessly. 
# If you decide to use it, create an initializer that adds the middleware 
# to your application's middleware stack.
# https://github.com/tors/jquery-fileupload-rails

# Rails.application.config.middleware.use JQuery::FileUpload::Rails::Middleware