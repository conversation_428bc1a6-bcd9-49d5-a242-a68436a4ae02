client_id = Rails.application.credentials.send(Rails.env).dig(:hubspot, :client_id) rescue nil
client_secret = Rails.application.credentials.send(Rails.env).dig(:hubspot, :client_secret) rescue nil
redirect_uri = Rails.application.credentials.send('hubspot').dig(:redirect_uri) rescue nil
access_token = Rails.application.credentials.send(Rails.env).dig(:hubspot, :access_token) rescue nil

Hubspot.configure(
    client_id: client_id,
    client_secret: client_secret,
    redirect_uri: redirect_uri,
    access_token: access_token)
