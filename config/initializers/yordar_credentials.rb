## helper method for Rails credentials
def yordar_credentials(field, *key)
  environment = Rails.env
  environment = 'staging' if ENV['IS_STAGING']
  if key.present?
    credentials = Rails.application.credentials.send(environment).dig(*([field] + key.flatten)) rescue nil
    credentials = !credentials.nil? ? credentials : Rails.application.credentials.send(field).dig(*key.flatten) rescue nil
  else
    credentials = Rails.application.credentials.send(environment).dig(field) rescue nil
    credentials = !credentials.nil? ? credentials : Rails.application.credentials.send(field) rescue nil
  end
end
