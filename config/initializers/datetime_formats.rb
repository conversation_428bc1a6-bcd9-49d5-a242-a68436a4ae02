datetime_formats = {
  full: '%A, %d-%b-%Y, %I:%M%P',
  full_verbose: '%a, %d %b %Y at %I:%M%P',
  full_date: '%A, %d %b %Y',
  short_week_full_date: '%a, %d %b %Y',

  filename: '%Y-%m-%d-%H-%M-%S-%L',
  invoice_number: '%y%m%d',

  weekday: '%A',
  short_weekday: '%a',

  date: '%d-%m-%Y',
  date_compact: '%Y%m%d',
  date_spreadsheet: '%Y-%m-%d',
  date_verbose: '%d %B %Y',

  datetime: '%Y-%m-%d %H:%M',

  month_year: '%b %Y',
  year_week: '%Y%W',
  day_month: '%d/%m',

  time_only: '%I:%M%P',
  hour_only: '%H',
}

Time::DATE_FORMATS.merge! datetime_formats
Date::DATE_FORMATS.merge! datetime_formats