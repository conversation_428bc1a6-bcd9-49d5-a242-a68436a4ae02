# Be sure to restart your server when you modify this file.

# Your secret key is used for verifying the integrity of signed cookies.
# If you change this key, all old signed cookies will become invalid!

# Make sure the secret is at least 30 characters and all random,
# no regular words or you'll be exposed to dictionary attacks.
# You can use `rake secret` to generate a secure secret key.

# Make sure the secrets in this file are kept private
# if you're sharing your code publicly.

development:
  secret_key_base: ab7c02e0688fa8576aa33cbd4f59563d58e00b715f13aad5eb124039952eda0cd1c94e33d21d184cde383f06dfa9e8479a844ac48b5aca7b479ca72f164abed9

test:
  secret_key_base: fb30ea9210f83981c29bc8fd4325dd30c5251ac045ef21cc402ae2e0624fe681f231118e82bfb49df5ef0076ff7d9a89d3e4203d8d43932b6b5e937e6ed3cdd1

staging:
  secret_key_base: <%= Rails.application.credentials.staging[:secret_key_base] %>

# Do not keep production secrets in the repository,
# instead read values from the environment.
production:
  secret_key_base: <%= Rails.application.credentials.production[:secret_key_base] %>
