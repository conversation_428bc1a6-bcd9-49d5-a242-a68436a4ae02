Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # In the development environment your application's code is reloaded on
  # every request. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.cache_classes = false

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports.
  config.consider_all_requests_local = true

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.
  if Rails.root.join('tmp', 'caching-dev.txt').exist?
    config.action_controller.perform_caching = true

    config.cache_store = :file_store, '/tmp/file-cache'
    config.public_file_server.headers = {
      'Cache-Control' => "public, max-age=#{2.days.to_i}"
    }
  else
    config.action_controller.perform_caching = false

    config.cache_store = :null_store
  end

  # Store uploaded files on the local file system (see config/storage.yml for options)
  config.active_storage.service = :local

  # Don't care if the mailer can't send.
  config.action_mailer.raise_delivery_errors = false

  config.action_mailer.perform_caching = false

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Prevent Development Log File
  config.logger = ActiveSupport::Logger.new(nil)

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Debug mode disables concatenation and preprocessing of assets.
  # This option may cause significant delays in view rendering with a large
  # number of complex assets.
  config.assets.debug = true


  # Adds additional error checking when serving assets at runtime.
  # Checks for improperly declared sprockets dependencies.
  # Raises helpful error messages.
  config.assets.raise_runtime_errors = true
  # Suppress logger output for asset requests.
  config.assets.quiet = true

  # Raises error for missing translations
  # config.action_view.raise_on_missing_translations = true

  config.action_mailer.delivery_method = :test
  config.action_mailer.default_url_options = { host: 'localhost', port: 3000, protocol: 'http' }
  config.action_mailer.delivery_method = :smtp
  config.action_mailer.perform_deliveries = true
  config.action_mailer.raise_delivery_errors = true

  # mailhog settings
  config.action_mailer.smtp_settings = {
    address: 'localhost',
    port: 1025,
  }

  # sendrid smtp settings (like production)
  # config.action_mailer.smtp_settings = {
  #   :address        => 'smtp.sendgrid.net',
  #   :port           => '587',
  #   :authentication => :plain,
  #   :user_name      => Rails.application.credentials.production.dig(:sendgrid, :username),
  #   :password       => Rails.application.credentials.production.dig(:sendgrid, :api_key),
  #   :domain         => 'yordar.com.au',
  #   :enable_starttls_auto => true
  # }

  # config.action_dispatch.tld_length = 2
  config.middleware.use DynamicTldLength, 'yordar-dev', 2
  config.middleware.use CustomCookieDomain, 'yordar-dev'

  # ============ Application specific configs ===================


  # adding e-way support using active_marchent
  config.after_initialize do
    ActiveMerchant::Billing::Base.mode = :test
  end

  config.after_initialize do
    Bullet.enable = true
    Bullet.bullet_logger = true
    Bullet.rails_logger = true
  end

  config.hosts += ['app.yordar-dev.com.au', 'app.yordar-dev.nz']
  
  config.middleware.insert_before 0, Rack::Cors do
    allow do
      origins (GATSBY_APP_DOMAINS + NEXT_APP_DOMAINS).map{|x| '\Ahttp[s]?:\/\/' + x.gsub('.', '\.') + '\Z' }.map{|x| Regexp.new(x) }
      [NEXT_APP_ROUTES, GATSBY_APP_ROUTES].inject(&:merge).each do |path, options|
        resource path, **options
      end
    end
  end
  # Use an evented file watcher to asynchronously detect changes in source code,
  # routes, locales, etc. This feature depends on the listen gem.
  config.file_watcher = ActiveSupport::EventedFileUpdateChecker

  # Automatically update js-routes file
  # when routes.rb is changed
  config.middleware.use(JsRoutes::Middleware)
end
