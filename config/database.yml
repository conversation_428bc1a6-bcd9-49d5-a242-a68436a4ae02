defaults: &defaults
  adapter: postgresql
  encoding: unicode
  host: localhost
production:
  # Actual production database is currently setup by <PERSON><PERSON> using the DATABASE_URL ENV variable
  database: yordar_development
  <<: *defaults
staging:
  # Actual staging database is currently setup by cloud66 using the DATABASE_URL ENV variable
  database: yordar_development
  <<: *defaults
development:
  database: yordar_development
  <<: *defaults
test:
  database: yordar_test
  <<: *defaults
