--- 
sandbox: &sandbox
  from_mail: ~
  address: ~
  domain: ~
  password: ~
  port: ~
  user_name: ~
  default_url_options: http://localhost:3000
heroku_sendgid: &heroku_sendgrid
  delivery_method: !ruby/symbol smtp
  from_mail: <EMAIL>
  address: smtp.sendgrid.net
  password: 0dbffxeg0184
  port: 587
  user_name: <EMAIL>
  domain: heroku.com
  default_url_options: https://www.yordar.com.au
development:
  <<: *sandbox
  delivery_method: !ruby/symbol letter_opener
  from_mail: <EMAIL>
test:
  <<: *sandbox
  delivery_method: !ruby/symbol test
production:
  <<: *heroku_sendgrid
staging:
  <<: *heroku_sendgrid
