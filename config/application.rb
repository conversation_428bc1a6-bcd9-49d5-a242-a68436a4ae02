require_relative 'boot'

require 'csv'
require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, :staging or :production.
Bundler.require(*Rails.groups)

module Yordar
  class Application < Rails::Application
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.

    # Set Time.zone default to the specified zone and make Active Record auto-convert to this zone.
    # Run "rake -D time" for a list of tasks for finding time zone names. Default is UTC.
    config.time_zone = 'Sydney'

    # The default locale is :en and all translations from config/locales/*.rb,yml are auto loaded.
    # config.i18n.load_path += Dir[Rails.root.join('my', 'locales', '*.{rb,yml}').to_s]
    # config.i18n.default_locale = :de

	unless Rails.env.test? || Rails.env.development?
		config.logger = Logger.new(STDOUT)
		log_level = String(ENV['LOG_LEVEL'] || 'info').upcase
		config.logger.level = Logger.const_get(log_level)
		config.log_level = log_level
	end

    # https://devcenter.heroku.com/articles/rails-asset-pipeline#troubleshooting
    config.assets.initialize_on_precompile = false

    # Do not swallow errors in after_commit/after_rollback callbacks.
    # config.active_record.raise_in_transactional_callbacks = true

    config.active_job.queue_adapter = :delayed_job

		# ========== imported from old config ==============
		# added tableless models that
		config.autoload_paths += Dir["#{config.root}/app/models/**/"]

		# autoload document templates
		config.autoload_paths += Dir["#{config.root}/app/templates/**/"]

		# autoload uploader
		config.autoload_paths += Dir["#{config.root}/app/uploaders/**/"]

		# Pre-loading all lib classes to ensure all apis and wrappers are loaded only once (not at each request)
		config.autoload_paths << Rails.root.join('lib')

		Dir["#{Rails.root.join('lib/middleware/*.rb')}"].each do |file|
		  require file
		end

		config.middleware.insert_after ActionDispatch::Static, Rack::Deflater
		
		config.active_record.yaml_column_permitted_classes = [Matrix, OpenStruct, Symbol, Hash, Array, Date, Time, BigDecimal, ActiveSupport::TimeWithZone, ActiveSupport::TimeZone, ActiveSupport::HashWithIndifferentAccess]

		# ===== application (business) specific configs ============
		config.woolworths = ActiveSupport::OrderedOptions.new
		config.woolworths.fulfilment_stores = OpenStruct.new(config_for(:woolworths_fulfilment_stores))
		config.woolworths.category_mappings = OpenStruct.new(config_for(:woolworths_category_mappings))
		# ===== application (business) specific configs [END]=======

		# Use routes for error handling
		config.exceptions_app = self.routes

		Raven.configure do |config|
			config.dsn = Rails.application.credentials.send(Rails.env)[:sentry_dns] rescue nil
		end

  end
end
