common: &common
  mapped_stores:
    1800: # Mascot, Sydney.
      street_1: 545-549 Kent St
      suburb: Sydney
      postcode: 2000
      state: NSW
    3800: # Melbourne West, Melbourne.
      street_1: 28 Melon Street
      suburb: Braybrook
      postcode: 3019
      state: VIC
    2755: # Lutwyche, Brisbane.
      street_1: 95 Adelaide Street
      suburb: Brisbane City
      postcode: 4000
      state: QLD
    5687: # Findon, Adelaide.
      street_1: 1 Rundle Mall
      suburb: Adelaide
      postcode: 5000
      state: SA
    1419: # Majura, Canberra Airport, Canberra.
      street_1: 59 Blackall Street
      suburb: Barton
      postcode: 2600
      state: ACT
    4366: # Dianella, Perth.
      street_1: 88 St Georges Terrace
      suburb: Perth
      postcode: 6000
      state: WA

development:
  <<: *common

staging:
  <<: *common

test:
  <<: *common

production:
  <<: *common

# to check store ids for a particular address run the folloing code
# connection = Woolworths::API::Connection.new(use_importer_account: true)
# # then run this separately
# address_data = {
#   street_1: '95 Adelaide Street',
#   suburb: 'Brisbane City',
#   postcode: '4000',
# }
# a = Woolworths::API::AddAddress.new({ connection: connection }.merge(address_data)).call
# # then run this separately
# connection.authenticate
# connection.fulfilment_store_id
# check store id by going to
# https://www.woolworths.com.au/shop/storelocator

