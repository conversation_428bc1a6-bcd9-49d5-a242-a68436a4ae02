import { render } from 'react-dom';

import BecomeTeamOrderAdminApp from 'components/customer/team_orders/admin_request/BecomeTeamOrderAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function becomeTeamAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <BecomeTeamOrderAdminApp />
    </appContext.Provider>,
    el
  );
}
