import { render } from 'react-dom';
import { Provider } from 'react-redux';

import CheckoutApp from 'components/checkout/CheckoutApp';
import store from 'store/configureOrderCheckoutStore';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

import 'react-responsive-modal/styles.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function orderCheckout($el, props) {
  const { isAdmin, ...checkoutProps } = props;
  render(
    <Provider store={store}>
      <adminContext.Provider value={{ isAdmin }}>
        <appContext.Provider value={checkoutProps}>
          <CheckoutApp />
        </appContext.Provider>
      </adminContext.Provider>
    </Provider>,
    $el
  );
}
