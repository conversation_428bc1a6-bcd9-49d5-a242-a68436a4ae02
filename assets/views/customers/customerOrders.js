import { render } from 'react-dom';

import { Provider } from 'react-redux';
import OrderApp from 'components/customer/orders/OrderApp';
import store from 'store/configureCustomerOrdersStore';
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';

export default function customerOrders($el, props) {
  const { isAdmin, ...orderProps } = props;
  render(
    <Provider store={store}>
      <adminContext.Provider value={{ isAdmin }}>
        <appContext.Provider value={orderProps}>
          <OrderApp />
        </appContext.Provider>
      </adminContext.Provider>
    </Provider>,
    $el
  );
}
