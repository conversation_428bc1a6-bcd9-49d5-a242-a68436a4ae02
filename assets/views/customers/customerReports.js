import { render } from 'react-dom';

import ReportsApp from 'components/customer/reports/ReportsApp';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

import 'react-responsive-modal/styles.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function customerReports($el, props) {
  const { isAdmin, isTeamAdmin, ...reportProps } = props;
  render(
    <adminContext.Provider value={{ isAdmin, isTeamAdmin }}>
      <appContext.Provider value={reportProps}>
        <ReportsApp />
      </appContext.Provider>
    </adminContext.Provider>,
    $el
  );
}
