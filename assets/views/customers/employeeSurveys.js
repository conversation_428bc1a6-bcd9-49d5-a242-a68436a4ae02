import { render } from 'react-dom';

import SurveyApp from 'components/customer/employee_surveys/SurveyApp';
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function employeeSurveys($el, props) {
  const { isAdmin, ...surveyProps } = props;
  render(
    <adminContext.Provider value={{ isAdmin }}>
      <appContext.Provider value={surveyProps}>
        <SurveyApp />
      </appContext.Provider>
    </adminContext.Provider>,
    $el
  );
}
