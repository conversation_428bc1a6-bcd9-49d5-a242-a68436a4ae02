import { render } from 'react-dom';

import MealPlanApp from 'components/customer/meal_plans/MealPlanApp';
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function customerMealPlans($el, props) {
  const { isAdmin, ...invoiceProps } = props;
  render(
    <adminContext.Provider value={{ isAdmin }}>
      <appContext.Provider value={invoiceProps}>
        <MealPlanApp />
      </appContext.Provider>
    </adminContext.Provider>,
    $el
  );
}
