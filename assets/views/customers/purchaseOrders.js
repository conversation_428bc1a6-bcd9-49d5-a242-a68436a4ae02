import { render } from 'react-dom';
import { Provider } from 'react-redux';

import PurchaseOrderApp from 'components/customer/purchase_orders/PurchaseOrderApp';
import store from 'store/configurePurchaseOrderStore';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

import 'react-responsive-modal/styles.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function customerPurchaseOrders($el, props) {
  const { isAdmin, ...purchaseOrderProps } = props;
  render(
    <Provider store={store}>
      <adminContext.Provider value={{ isAdmin }}>
        <appContext.Provider value={purchaseOrderProps}>
          <PurchaseOrderApp />
        </appContext.Provider>
      </adminContext.Provider>
    </Provider>,
    $el
  );
}
