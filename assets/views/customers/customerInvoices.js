import { render } from 'react-dom';

import InvoiceApp from 'components/customer/invoices/InvoiceApp';
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function customerInvoices($el, props) {
  const { isAdmin, ...invoiceProps } = props;
  render(
    <adminContext.Provider value={{ isAdmin }}>
      <appContext.Provider value={invoiceProps}>
        <InvoiceApp />
      </appContext.Provider>
    </adminContext.Provider>,
    $el
  );
}
