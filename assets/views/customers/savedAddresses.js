import { render } from 'react-dom';
import { Provider } from 'react-redux';

import App from 'components/customer/saved_addresses/SavedAddressesApp';
import store from 'store/configureSavedAddressStore';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

export default function customerSavedAddresses($el, props) {
  const { isAdmin, ...savedAddressProps } = props;
  render(
    <Provider store={store}>
      <adminContext.Provider value={{ isAdmin }}>
        <appContext.Provider value={savedAddressProps}>
          <App />
        </appContext.Provider>
      </adminContext.Provider>
    </Provider>,
    $el
  );
}
