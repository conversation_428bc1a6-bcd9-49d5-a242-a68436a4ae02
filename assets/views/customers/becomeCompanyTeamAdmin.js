import { render } from 'react-dom';

import BecomeCompanyTeamAdminApp from 'components/customer/company_team_admin/admin_request/BecomeCompanyTeamAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function becomeCompanyTeamAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <BecomeCompanyTeamAdminApp />
    </appContext.Provider>,
    el
  );
}
