import { render } from 'react-dom';

import { Provider } from 'react-redux';
import QuotesApp from 'components/customer/quotes/QuotesApp';
import store from 'store/configureCustomerOrdersStore';
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function customerQuotes($el, props) {
  const { isAdmin, ...quoteProps } = props;
  render(
    <Provider store={store}>
      <adminContext.Provider value={{ isAdmin }}>
        <appContext.Provider value={quoteProps}>
          <QuotesApp />
        </appContext.Provider>
      </adminContext.Provider>
    </Provider>,
    $el
  );
}
