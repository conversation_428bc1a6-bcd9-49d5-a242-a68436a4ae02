import { render } from 'react-dom';

import HolidayAdminApp from 'components/admin/holiday/HolidayAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function holidayAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <HolidayAdminApp />
    </appContext.Provider>,
    el
  );
}
