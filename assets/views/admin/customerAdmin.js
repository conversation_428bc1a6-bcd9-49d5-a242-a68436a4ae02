import { render } from 'react-dom';

import CustomerAdminApp from 'components/admin/customer/CustomerAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function customerAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <CustomerAdminApp />
    </appContext.Provider>,
    el
  );
}
