import { render } from 'react-dom';

import YordarAdminApp from 'components/admin/yordar_admin/YordarAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function yordarAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <YordarAdminApp />
    </appContext.Provider>,
    el
  );
}
