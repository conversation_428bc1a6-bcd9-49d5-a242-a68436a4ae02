import { render } from 'react-dom';

import SupplierAdminApp from 'components/admin/supplier/SupplierAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function supplierAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <SupplierAdminApp />
    </appContext.Provider>,
    el
  );
}
