import { render } from 'react-dom';

import AdminNotificationsApp from 'components/admin/notifications/AdminNotificationsApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function adminNotifications(el, props) {
  render(
    <appContext.Provider value={props}>
      <AdminNotificationsApp />
    </appContext.Provider>,
    el
  );
}
