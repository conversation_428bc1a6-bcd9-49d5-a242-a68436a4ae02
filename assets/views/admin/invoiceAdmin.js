import { render } from 'react-dom';

import InvoiceAdminApp from 'components/admin/invoice/InvoiceAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function customerInvoiceAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <InvoiceAdminApp />
    </appContext.Provider>,
    el
  );
}
