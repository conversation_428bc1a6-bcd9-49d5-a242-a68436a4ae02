import { render } from 'react-dom';

import PantryManagerAdminApp from 'components/admin/pantry_manager/PantryManagerAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function pantryManagerAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <PantryManagerAdminApp />
    </appContext.Provider>,
    el
  );
}
