import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiPurchaseOrdersPath, apiPurchaseOrderPath, ordersAPIPurchaseOrdersPath, updateOrderAPIPurchaseOrdersPath } from 'routes';
import {
  FETCH_PURCHASE_ORDERS_REQUEST,
  FETCH_PURCHASE_ORDERS_SUCCESS,
  FETCH_PURCHASE_ORDERS_FAILURE,
  FETCH_MORE_PURCHASE_ORDERS_REQUEST,
  FETCH_MORE_PURCHASE_ORDERS_SUCCESS,
  FETCH_MORE_PURCHASE_ORDERS_FAILURE,
  CREATE_PURCHASE_ORDER_REQUEST,
  CREATE_PURCHASE_ORDER_SUCCESS,
  CREATE_PURCHASE_ORDER_FAILURE,
  UPDATE_PURCHASE_ORDER_REQUEST,
  UPDATE_PURCHASE_ORDER_SUCCESS,
  UPDATE_PURCHASE_ORDER_FAILURE,
  REMOVE_PURCHASE_ORDER_REQUEST,
  REMOVE_PURCHASE_ORDER_SUCCESS,
  REMOVE_PURCHASE_ORDER_FAILURE,
  FETCH_ORDERS_REQUEST,
  FETCH_ORDERS_SUCCESS,
  FETCH_ORDERS_FAILURE,
  UPDATE_ORDER_REQUEST,
  UPDATE_ORDER_SUCCESS,
  UPDATE_ORDER_FAILURE,
} from './purhaseOrderActionTypes';

const MAXIMUM_RETURNED_DATES = 10;

export const fetchPurchaseOrders =
  ({ page, infiniteLoading }) =>
  (dispatch) => {
    dispatch({
      type: infiniteLoading ? FETCH_MORE_PURCHASE_ORDERS_REQUEST : FETCH_PURCHASE_ORDERS_REQUEST,
    });
    const request = axios({
      method: 'GET',
      url: apiPurchaseOrdersPath({ format: 'json' }),
      params: { page },
    })
      .then(({ data: purchase_orders, status }) => {
        if (status === 200) {
          const hasMore = purchase_orders.length === MAXIMUM_RETURNED_DATES;
          dispatch({
            type: infiniteLoading ? FETCH_MORE_PURCHASE_ORDERS_SUCCESS : FETCH_PURCHASE_ORDERS_SUCCESS,
            payload: {
              purchase_orders,
              hasMore,
            },
          });
        }
      })
      .catch((error) => {
        dispatch({
          type: infiniteLoading ? FETCH_MORE_PURCHASE_ORDERS_FAILURE : FETCH_PURCHASE_ORDERS_FAILURE,
          payload: error,
        });
      });
  };

export const createPurchaseOrder = async ({ purchaseOrder, dispatch }) => {
  dispatch({ type: CREATE_PURCHASE_ORDER_REQUEST, payload: purchaseOrder });

  const request = axios({
    method: 'POST',
    url: apiPurchaseOrdersPath({ format: 'json' }),
    data: { purchase_order: purchaseOrder },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: CREATE_PURCHASE_ORDER_SUCCESS, payload: response.data });
      return response.data;
    })
    .catch((error) => {
      let error_messages;
      if (error.response.status === 422) {
        error_messages = error.response.data.errors;
        alert(error_messages.join('. '));
      } else {
        alert('Something went wrong when creating a new Purchase Order');
      }
      dispatch({ type: CREATE_PURCHASE_ORDER_FAILURE, payload: purchaseOrder });
      return Promise.reject(error);
    });
  return request;
};

export const updatePurchaseOrder = async ({ purchaseOrder, dispatch }) => {
  dispatch({ type: UPDATE_PURCHASE_ORDER_REQUEST, payload: purchaseOrder });

  const request = axios({
    method: 'PUT',
    url: apiPurchaseOrderPath(purchaseOrder, { format: 'json' }),
    data: { purchase_order: purchaseOrder },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: UPDATE_PURCHASE_ORDER_SUCCESS, payload: response.data });
      return response.data;
    })
    .catch((error) => {
      let error_messages;
      if (error.response.status === 422) {
        error_messages = error.response.data.errors;
        alert(error_messages.join('. '));
      } else {
        alert('Something went wrong when updating the Purchase Order');
      }
      dispatch({ type: UPDATE_PURCHASE_ORDER_FAILURE, payload: purchaseOrder });
      return Promise.reject(error);
    });
  return await request;
};

export const removePurchaseOrder =
  ({ purchaseOrder }) =>
  (dispatch) => {
    dispatch({ type: REMOVE_PURCHASE_ORDER_REQUEST });
    const request = axios({
      method: 'DELETE',
      url: apiPurchaseOrderPath(purchaseOrder, { format: 'json' }),
      headers: csrfHeaders(),
    })
      .then((response) => {
        dispatch({
          type: REMOVE_PURCHASE_ORDER_SUCCESS,
          payload: purchaseOrder,
        });
      })
      .catch((error) => {
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          alert(error_messages.join('. '));
        } else {
          alert('Oops! Something went wrong when trying to remove the purchase order.');
        }
        dispatch({
          type: REMOVE_PURCHASE_ORDER_FAILURE,
          payload: error,
        });
      });
  };

export const fetchOrders = () => (dispatch) => {
  dispatch({
    type: FETCH_ORDERS_REQUEST,
  });
  const request = axios({
    method: 'GET',
    url: ordersAPIPurchaseOrdersPath({ format: 'json' }),
  })
    .then(({ data: orders, status }) => {
      if (status === 200) {
        dispatch({
          type: FETCH_ORDERS_SUCCESS,
          payload: orders,
        });
      }
    })
    .catch((error) => {
      dispatch({
        type: FETCH_ORDERS_FAILURE,
        payload: error,
      });
    });
};

export const updateOrder =
  ({ order, purchaseOrder }) =>
  (dispatch) => {
    dispatch({
      type: UPDATE_ORDER_REQUEST,
    });
    const request = axios({
      method: 'PUT',
      url: updateOrderAPIPurchaseOrdersPath(order, { format: 'json' }),
      data: { cpo_id: order.attached ? purchaseOrder.id : null },
      headers: csrfHeaders(),
    })
      .then(({ data: updatedOrder, status }) => {
        if (status === 200) {
          dispatch({
            type: UPDATE_ORDER_SUCCESS,
            purchaseOrder,
            payload: updatedOrder,
          });
        }
      })
      .catch((error) => {
        dispatch({
          type: UPDATE_ORDER_FAILURE,
          payload: error,
        });
      });
  };
