import axios from 'axios';
import { apiSupplierMenuPath, apiCompaniesPath } from 'routes';
import {
  FETCH_MENU_REQUEST,
  FETCH_MENU_SUCCESS,
  FETCH_MENU_FAILURE,
  FETCH_COMPANIES_REQUEST,
  FETCH_COMPANIES_SUCCESS,
  FETCH_COMPANIES_FAILURE,
} from './menuActionTypes';

export const fetchMenu = async ({ supplierID, showArchived, menuItem, dispatch }) => {
  dispatch({ type: FETCH_MENU_REQUEST });
  const request = await axios({
    method: 'GET',
    url: apiSupplierMenuPath(supplierID, { format: 'json' }),
    params: {
      ...(showArchived && { show_archived: true }),
      ...(menuItem && { menu_item_id: menuItem.id, query: menuItem.query })
    },
  })
    .then((response) => {
      if (response.status === 200) {
        dispatch({
          type: <PERSON><PERSON><PERSON>_MENU_SUCCESS,
          payload: response.data,
        });
      }
    })
    .catch((error) => {
      dispatch({
        type: FETCH_MENU_FAILURE,
        payload: error,
      });
    });
};

export const fetchCompanies = ({ dispatch }) => {
  dispatch({ type: FETCH_COMPANIES_REQUEST });
  const request = axios({
    method: 'GET',
    url: apiCompaniesPath({ format: 'json' }),
  })
    .then((response) => {
      if (response.status === 200) {
        dispatch({
          type: FETCH_COMPANIES_SUCCESS,
          payload: response.data,
        });
      }
    })
    .catch((error) => {
      dispatch({
        type: FETCH_COMPANIES_FAILURE,
        payload: error,
      });
    });
};
