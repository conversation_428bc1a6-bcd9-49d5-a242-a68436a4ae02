import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiStripeCreditCardsPath } from 'routes';
import {
  SAVE_STRIPE_CARD_REQUEST,
  SAVE_STRIPE_CARD_SUCCESS,
  SAVE_STRIPE_CARD_FAILURE,
  SAVE_YORDAR_CARD_REQUEST,
  SAVE_YORDAR_CARD_SUCCESS,
  SAVE_YORDAR_CARD_FAILURE,
  UPDATE_ORDER_PAY_BY_CARD,
} from './checkoutActionTypes';

export const saveStripeCard =
  ({ stripe, stripeElements, creditCard, setCreditCard }) =>
  (dispatch) => {
    dispatch({ type: SAVE_STRIPE_CARD_REQUEST });
    const stripe_payment = stripe
      .createPaymentMethod({
        type: 'card',
        card: stripeElements._elements[0],
        billing_details: {
          name: creditCard.name,
          email: creditCard.email,
        },
      })
      .then((response) => {
        const { error, paymentMethod } = response;
        if (error) {
          dispatch({ type: SAVE_STRIPE_CARD_FAILURE, payload: error.message });
        } else {
          dispatch({ type: SAVE_STRIPE_CARD_SUCCESS });
          const { card: stripeCard } = paymentMethod;
          setCreditCard({
            ...creditCard,
            last4: stripeCard.last4,
            brand: stripeCard.brand,
            country_code: stripeCard.country,
            expiry_month: stripeCard.exp_month,
            expiry_year: stripeCard.exp_year,
            stripe_token: paymentMethod.id,
          });
        }
      })
      .catch((e) => {
        dispatch({ type: SAVE_STRIPE_CARD_FAILURE, payload: 'We were unable to connect to our Credit Card provider.' });
      });
  };

export const saveYordarCard =
  ({ creditCard, setCreditCard }) =>
  (dispatch) => {
    dispatch({ type: SAVE_YORDAR_CARD_REQUEST });

    axios({
      method: 'POST',
      url: apiStripeCreditCardsPath({ format: 'json' }),
      data: { credit_card: creditCard },
      headers: csrfHeaders(),
    })
      .then((response) => {
        const yordarCard = { ...response.data, is_new: true };
        setCreditCard(yordarCard);
        dispatch({ type: SAVE_YORDAR_CARD_SUCCESS });
        dispatch({ type: UPDATE_ORDER_PAY_BY_CARD, payload: yordarCard.id });
      })
      .catch((error) => {
        dispatch({ type: SAVE_YORDAR_CARD_FAILURE });
      });
  };
