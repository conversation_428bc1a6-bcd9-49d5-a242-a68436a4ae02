import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiRateCardsPath, apiRateCardPath } from 'routes';
import {
  CREATE_RATE_CARD_REQUEST,
  CREATE_RATE_CARD_SUCCESS,
  CREATE_RATE_CARD_FAILURE,
  UPDATE_RATE_CARD_REQUEST,
  UPDATE_RATE_CARD_SUCCESS,
  UPDATE_RATE_CARD_FAILURE,
  REMOVE_RATE_CARD_REQUEST,
  REMOVE_RATE_CARD_SUCCESS,
  REMOVE_RATE_CARD_FAILURE,
} from './menuActionTypes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

export const createRateCard =
  ({ rateCard }) =>
    (dispatch) => {
      dispatch({ type: CREATE_RATE_CARD_REQUEST });
      const request = axios({
        method: 'POST',
      url: apiRateCardsPath({ format: 'json' }),
      data: { rate_card: rateCard },
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({ type: CREATE_RATE_CARD_SUCCESS, payload: response.data });
          toast.success(`Created rate-card for '${response.data.company_name}'`, { ...defaultToastOptions, autoClose: 5000 });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          } else {
            toast.error('Oops! Something went wrong when trying to create a new rate card. Please try again!', { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({ type: CREATE_RATE_CARD_FAILURE });
        });
    };

export const updateRateCard =
  ({ rateCard }) =>
    (dispatch) => {
    dispatch({ type: UPDATE_RATE_CARD_REQUEST, payload: rateCard });
    const request = axios({
      method: 'PUT',
      url: apiRateCardPath(rateCard, { format: 'json' }),
      data: { rate_card: rateCard },
      headers: csrfHeaders(),
    })
      .then((response) => {
        dispatch({ type: UPDATE_RATE_CARD_SUCCESS, payload: response.data });
        toast.success(`Updated rate-card '${response.data.company_name}'`, { ...defaultToastOptions, autoClose: 5000 });
      })
      .catch((error) => {
        let error_messages;
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
        }
        dispatch({ type: UPDATE_RATE_CARD_FAILURE, payload: rateCard });
      });
    };

export const removeRateCard = async ({ rateCard, isForced, dispatch }) => {
  dispatch({ type: REMOVE_RATE_CARD_REQUEST, payload: rateCard });
  const request = axios({
    method: 'DELETE',
    url: apiRateCardPath(rateCard, { format: 'json' }),
    data: { forced: isForced },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: REMOVE_RATE_CARD_SUCCESS, payload: rateCard });
      toast.success(`Removed rate-card '${response.data.company_name}'`, { ...defaultToastOptions, autoClose: 5000 });
      return response.data;
    })
    .catch((error) => {
      dispatch({ type: REMOVE_RATE_CARD_FAILURE, payload: rateCard });
      return Promise.reject(error);
    });
  return await request;
};
