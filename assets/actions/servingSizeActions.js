import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiServingSizesPath, apiServingSizePath } from 'routes';
import {
  CREATE_SERVING_SIZE_REQUEST,
  CREATE_SERVING_SIZE_SUCCESS,
  CREATE_SERVING_SIZE_FAILURE,
  UPDATE_SERVING_SIZE_REQUEST,
  UPDATE_SERVING_SIZE_SUCCESS,
  UPDATE_SERVING_SIZE_FAILURE,
  REMOVE_SERVING_SIZE_REQUEST,
  REMOVE_SERVING_SIZE_SUCCESS,
  REMOVE_SERVING_SIZE_FAILURE,
} from './menuActionTypes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

export const createServingSize =
  ({ servingSize }) =>
    (dispatch) => {
      dispatch({ type: CREATE_SERVING_SIZE_REQUEST });
      const request = axios({
        method: 'POST',
      url: apiServingSizesPath({ format: 'json' }),
      data: { serving_size: servingSize },
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({ type: CREATE_SERVING_SIZE_SUCCESS, payload: response.data });
          toast.success(`Created '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          } else {
            toast.error('Oops! Something went wrong when trying to create a new serving size. Please try again!', { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({ type: CREATE_SERVING_SIZE_FAILURE });
        });
    };

export const updateServingSize =
  ({ servingSize }) =>
    (dispatch) => {
    dispatch({ type: UPDATE_SERVING_SIZE_REQUEST, payload: servingSize });
    const request = axios({
      method: 'PUT',
      url: apiServingSizePath(servingSize, { format: 'json' }),
      data: { serving_size: servingSize },
      headers: csrfHeaders(),
    })
      .then((response) => {
        dispatch({ type: UPDATE_SERVING_SIZE_SUCCESS, payload: response.data });
        toast.success(`Update '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      })
      .catch((error) => {
        let error_messages;
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
        }
        dispatch({ type: UPDATE_SERVING_SIZE_FAILURE, payload: servingSize });
      });
    };

export const removeServingSize =
  ({ servingSize }) =>
    (dispatch) => {
      dispatch({ type: REMOVE_SERVING_SIZE_REQUEST, payload: servingSize });
    const request = axios({
      method: 'DELETE',
      url: apiServingSizePath(servingSize, { format: 'json' }),
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({ type: REMOVE_SERVING_SIZE_SUCCESS, payload: servingSize });
          toast.success(`Removed '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({ type: REMOVE_SERVING_SIZE_FAILURE, payload: servingSize });
        });
    };
