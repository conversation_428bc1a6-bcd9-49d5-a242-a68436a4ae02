import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiSavedAddressesPath, apiSavedAddressPath } from 'routes';
import {
  FETCH_SAVED_ADDRESSS_REQUEST,
  FETCH_SAVED_ADDRESSS_SUCCESS,
  FETCH_SAVED_ADDRESSS_FAILURE,
  FETCH_MORE_SAVED_ADDRESSS_REQUEST,
  FETCH_MORE_SAVED_ADDRESSS_SUCCESS,
  FETCH_MORE_SAVED_ADDRESSS_FAILURE,
  CREATE_SAVED_ADDRESS_REQUEST,
  CREATE_SAVED_ADDRESS_SUCCESS,
  CREATE_SAVED_ADDRESS_FAILURE,
  UPDATE_SAVED_ADDRESS_REQUEST,
  UPDATE_SAVED_ADDRESS_SUCCESS,
  UPDATE_SAVED_ADDRESS_FAILURE,
  REMOVE_SAVED_ADDRESS_REQUEST,
  REMOVE_SAVED_ADDRESS_SUCCESS,
  REMOVE_SAVED_ADDRESS_FAILURE,
} from './savedAddressActionTypes';

const MAXIMUM_RETURNED_DATES = 10;

export const fetchSavedAddresss =
  ({ page, infiniteLoading }) =>
    (dispatch) => {
      dispatch({
        type: infiniteLoading ? FETCH_MORE_SAVED_ADDRESSS_REQUEST : FETCH_SAVED_ADDRESSS_REQUEST,
      });
      const request = axios({
        method: 'GET',
      url: apiSavedAddressesPath({ format: 'json' }),
      params: { page },
    })
        .then(({ data: saved_addresses, status }) => {
          if (status === 200) {
            const hasMore = saved_addresses.length === MAXIMUM_RETURNED_DATES;
            dispatch({
              type: infiniteLoading ? FETCH_MORE_SAVED_ADDRESSS_SUCCESS : FETCH_SAVED_ADDRESSS_SUCCESS,
              payload: {
                saved_addresses,
                hasMore,
              },
            });
          }
        })
        .catch((error) => {
          dispatch({
            type: infiniteLoading ? FETCH_MORE_SAVED_ADDRESSS_FAILURE : FETCH_SAVED_ADDRESSS_FAILURE,
            payload: error,
          });
        });
    };

export const createSavedAddress = async ({ savedAddress, dispatch }) => {
  dispatch({ type: CREATE_SAVED_ADDRESS_REQUEST, payload: savedAddress });

  const request = axios({
    method: 'POST',
    url: apiSavedAddressesPath({ format: 'json' }),
    data: { saved_address: savedAddress },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: CREATE_SAVED_ADDRESS_SUCCESS, payload: response.data });
      return response.data;
    })
    .catch((error) => {
      let error_messages;
      if (error.response.status === 422) {
        error_messages = error.response.data.errors;
        alert(error_messages.join('. '));
      } else {
        alert('Something went wrong when saving a new Address');
      }
      dispatch({ type: CREATE_SAVED_ADDRESS_FAILURE, payload: savedAddress });
      return Promise.reject(error);
    });
  return await request;
};

export const updateSavedAddress = async ({ savedAddress, dispatch }) => {
  dispatch({ type: UPDATE_SAVED_ADDRESS_REQUEST, payload: savedAddress });

  const request = axios({
    method: 'PUT',
    url: apiSavedAddressPath(savedAddress, { format: 'json' }),
    data: { saved_address: savedAddress },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: UPDATE_SAVED_ADDRESS_SUCCESS, payload: response.data });
      return response.data;
    })
    .catch((error) => {
      let error_messages;
      if (error.response.status === 422) {
        error_messages = error.response.data.errors;
        alert(error_messages.join('. '));
      } else {
        alert('Something went wrong when updating the Address');
      }
      dispatch({ type: UPDATE_SAVED_ADDRESS_FAILURE, payload: savedAddress });
      return Promise.reject(error);
    });
  return await request;
};

export const removeSavedAddress =
  ({ savedAddress }) =>
    (dispatch) => {
      dispatch({ type: REMOVE_SAVED_ADDRESS_REQUEST });
      const request = axios({
        method: 'DELETE',
      url: apiSavedAddressPath(savedAddress, { format: 'json' }),
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({
            type: REMOVE_SAVED_ADDRESS_SUCCESS,
            payload: savedAddress,
          });
        })
        .catch((error) => {
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            alert(error_messages.join('. '));
          } else {
            alert('Oops! Something went wrong when trying to remove the address.');
          }
          dispatch({
            type: REMOVE_SAVED_ADDRESS_FAILURE,
            payload: error,
          });
        });
    };
