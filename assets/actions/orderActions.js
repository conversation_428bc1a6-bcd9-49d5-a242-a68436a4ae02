import axios from 'axios';
import { sanitizeParamsForKey } from 'utilities/orderKeySanitizing';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiOrdersPath } from 'routes';
import {
  FETCH_ORDERS_REQUEST,
  <PERSON>ETCH_ORDERS_SUCCESS,
  <PERSON>ETCH_ORDERS_FAILURE,
  FETCH_ORDERS_FROM_CACHE,
  FETCH_MORE_ORDERS_REQUEST,
  FETCH_MORE_ORDERS_SUCCESS,
  FETCH_MORE_ORDERS_FAILURE,
  UPDATE_ORDER_REQUEST,
  UPDATE_ORDER_SUCCESS,
  UPDATE_ORDER_FAILURE,
} from './actionTypes';

const MAXIMUM_RETURNED_ORDERS = 20;

const handleRequestError = (dispatch, infiniteLoading) => {
  const actionType = infiniteLoading ? FETCH_MORE_ORDERS_FAILURE : FETCH_ORDERS_FAILURE;
  dispatch({ type: actionType });
};

const fetchOrdersSuccess = (dispatch, orders, params, isCalendarView, infiniteLoading) => {
  const actionType = infiniteLoading ? FETCH_MORE_ORDERS_SUCCESS : FETCH_ORDERS_SUCCESS;
  const hasMoreOrders = orders.length === MAXIMUM_RETURNED_ORDERS;

  dispatch({
    type: actionType,
    payload: {
      orders,
      key: sanitizeParamsForKey(params, isCalendarView),
      hasMoreOrders,
    },
  });
};

export const fetchOrders =
  ({ params, cachedKey, isCalendarView, infiniteLoading }) =>
    (dispatch) => {
    if (cachedKey) {
      dispatch({
        type: FETCH_ORDERS_FROM_CACHE,
        payload: { cachedKey },
      });

      // Return a resolved Promise
      return Promise.resolve();
    }

    const actionType = infiniteLoading ? FETCH_MORE_ORDERS_REQUEST : FETCH_ORDERS_REQUEST;
    dispatch({ type: actionType });

    const { from_date, to_date, ...nonDateParams } = params;
    const requestParams = isCalendarView ? { from_date, to_date } : nonDateParams;

    // Return the Promise created by axios
    return axios
      .get(apiOrdersPath({ format: 'json' }), {
        params: requestParams,
      })
      .then(({ data: orders, status }) => {
        if (status === 200) {
          fetchOrdersSuccess(dispatch, orders, params, isCalendarView, infiniteLoading);
        }
      })
        .catch(() => handleRequestError(dispatch, infiniteLoading));
    };

export const changeOrderStatus =
  ({ apiEndpoint, reactivate }) =>
    (dispatch) => {
      const method = reactivate ? 'put' : 'delete';

    dispatch({ type: UPDATE_ORDER_REQUEST });

      axios(apiEndpoint, { method, headers: csrfHeaders() })
        .then(({ data: order }) => {
          dispatch({ type: UPDATE_ORDER_SUCCESS, payload: order });
        })
        .catch((error) => {
          console.log(error);
          dispatch({ type: UPDATE_ORDER_FAILURE });
        });
    };
