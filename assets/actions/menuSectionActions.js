import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiMenuSectionsPath, apiMenuSectionPath } from 'routes';
import {
  CREATE_MENU_SECTION_REQUEST,
  CREATE_MENU_SECTION_SUCCESS,
  CREATE_MENU_SECTION_FAILURE,
  UPDATE_MENU_SECTION_REQUEST,
  UPDATE_MENU_SECTION_SUCCESS,
  UPDATE_MENU_SECTION_FAILURE,
  REMOVE_MENU_SECTION_REQUEST,
  REMOVE_MENU_SECTION_SUCCESS,
  REMOVE_MENU_SECTION_FAILURE,
} from './menuActionTypes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

// async means there are on-success view changes
export const createMenuSection = async ({ menuSection, dispatch }) => {
  dispatch({ type: CREATE_MENU_SECTION_REQUEST });

  const request = axios({
    method: 'POST',
    url: apiMenuSectionsPath({ format: 'json' }),
    data: { menu_section: menuSection },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: CREATE_MENU_SECTION_SUCCESS, payload: response.data });
      return response.data;
    })
    .catch((error) => {
      let error_messages;
      if (error.response.status === 422) {
        const error_messages = error.response.data.errors;
        toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
      } else {
        toast.error('Oops! Something went wrong when trying to create a new menu section. Please try again!', { ...defaultToastOptions, autoClose: 10000 });
      }
      dispatch({ type: CREATE_MENU_SECTION_FAILURE });
      return Promise.reject(error);
    });
  return await request;
};

export const updateMenuSection = async ({ menuSection, dispatch }) => {
  const { menu_items, categories, restricted_companies, ...sanitizedMenuSection } = menuSection;
  dispatch({ type: UPDATE_MENU_SECTION_REQUEST, payload: menuSection });

  const request = axios({
    method: 'PUT',
    url: apiMenuSectionPath(menuSection, { format: 'json' }),
    data: { menu_section: sanitizedMenuSection },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: UPDATE_MENU_SECTION_SUCCESS, payload: response.data });
      toast.success(`Updated section named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      return response.data;
    })
    .catch((error) => {
      let error_messages;
      if (error.response.status === 422) {
        const error_messages = error.response.data.errors;
        toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
      }
      dispatch({ type: UPDATE_MENU_SECTION_FAILURE, payload: menuSection });
      return Promise.reject(error);
    });
  return request;
};

export const removeMenuSection = async ({ menuSection, isForced, dispatch }) => {
  dispatch({ type: REMOVE_MENU_SECTION_REQUEST, payload: menuSection });
  const request = axios({
    method: 'DELETE',
    url: apiMenuSectionPath(menuSection, { format: 'json' }),
    data: { is_forced: isForced },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: REMOVE_MENU_SECTION_SUCCESS, payload: menuSection });
      toast.success(`Removed section named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      return response.data;
    })
    .catch((error) => {
      dispatch({ type: REMOVE_MENU_SECTION_FAILURE, payload: menuSection });
      toast.error(`Could not remove section named '${menuSection.name}. Please try again!`, { ...defaultToastOptions, autoClose: 10000 });
      return Promise.reject(error);
    });
  return request;
};
