import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiMenuExtrasPath, apiMenuExtraPath } from 'routes';
import {
  CREATE_MENU_EXTRA_REQUEST,
  CREATE_MENU_EXTRA_SUCCESS,
  CREATE_MENU_EXTRA_FAILURE,
  UPDATE_MENU_EXTRA_REQUEST,
  UPDATE_MENU_EXTRA_SUCCESS,
  UPDATE_MENU_EXTRA_FAILURE,
  REMOVE_MENU_EXTRA_REQUEST,
  REMOVE_MENU_EXTRA_SUCCESS,
  REMOVE_MENU_EXTRA_FAILURE,
} from './menuActionTypes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

export const createMenuExtra =
  ({ menuExtra }) =>
  (dispatch) => {
    dispatch({ type: CREATE_MENU_EXTRA_REQUEST });
    const request = axios({
      method: 'POST',
      url: apiMenuExtrasPath({ format: 'json' }),
      data: { menu_extra: menuExtra },
      headers: csrfHeaders(),
    })
      .then((response) => {
        dispatch({ type: CREATE_MENU_EXTRA_SUCCESS, payload: response.data });
        toast.success(`Created extra named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      })
      .catch((error) => {
        let error_messages;
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
        } else {
          toast.error('Oops! Something went wrong when trying to create a new menu extra. Please try again!', { ...defaultToastOptions, autoClose: 10000 });
        }
        dispatch({ type: CREATE_MENU_EXTRA_FAILURE });
      });
  };

export const updateMenuExtra =
  ({ menuExtra }) =>
  (dispatch) => {
    dispatch({ type: UPDATE_MENU_EXTRA_REQUEST, payload: menuExtra });
    const request = axios({
      method: 'PUT',
      url: apiMenuExtraPath(menuExtra, { format: 'json' }),
      data: { menu_extra: menuExtra },
      headers: csrfHeaders(),
    })
      .then((response) => {
        dispatch({ type: UPDATE_MENU_EXTRA_SUCCESS, payload: response.data });
        toast.success(`Updated extra named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      })
      .catch((error) => {
        let error_messages;
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
        }
        dispatch({ type: UPDATE_MENU_EXTRA_FAILURE, payload: menuExtra });
      });
  };

export const removeMenuExtra =
  ({ menuExtra }) =>
  (dispatch) => {
    dispatch({ type: REMOVE_MENU_EXTRA_REQUEST, payload: menuExtra });
    const request = axios({
      method: 'DELETE',
      url: apiMenuExtraPath(menuExtra, { format: 'json' }),
      headers: csrfHeaders(),
    })
      .then((response) => {
        dispatch({ type: REMOVE_MENU_EXTRA_SUCCESS, payload: menuExtra });
        toast.success(`Removed extra named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      })
      .catch((error) => {
        let error_messages;
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
        }
        dispatch({ type: REMOVE_MENU_EXTRA_FAILURE, payload: menuExtra });
      });
  };
