import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiMenuItemsPath, apiMenuItemPath, apiMenuItemClonePath, apiMenuItemCheckUsagePath } from 'routes';
import {
  CREATE_MENU_ITEM_REQUEST,
  CREATE_MENU_ITEM_SUCCESS,
  CREATE_MENU_ITEM_FAILURE,
  CLONE_MENU_ITEM_REQUEST,
  CLONE_MENU_ITEM_SUCCESS,
  CLONE_MENU_ITEM_FAILURE,
  UPDATE_MENU_ITEM_REQUEST,
  UPDATE_MENU_ITEM_SUCCESS,
  UPDATE_MENU_ITEM_FAILURE,
  REMOVE_MENU_ITEM_REQUEST,
  REMOVE_MENU_ITEM_SUCCESS,
  REMOVE_MENU_ITEM_FAILURE,
  MENU_ITEM_ORDERS_REQUEST,
  MENU_ITEM_ORDERS_SUCCESS,
  MENU_ITEM_ORDERS_FAILURE,
} from './menuActionTypes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

// async means there are on-success view changes
export const createMenuItem = async ({ menuItem, dispatch }) => {
  dispatch({ type: CREATE_MENU_ITEM_REQUEST });

  const request = axios({
    method: 'POST',
    url: apiMenuItemsPath({ format: 'json' }),
    data: { menu_item: menuItem },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: CREATE_MENU_ITEM_SUCCESS, payload: response.data });
      return response.data;
    })
    .catch((error) => {
      let error_messages;
      if (error.response.status === 422) {
        const error_messages = error.response.data.errors;
        toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
      } else {
        toast.error('Oops! Something went wrong when trying to create a new menu item. Please try again!', { ...defaultToastOptions, autoClose: 10000 });
      }
      dispatch({ type: CREATE_MENU_ITEM_FAILURE });
      return Promise.reject(error);
    });
  return await request;
};

export const cloneMenuItem =
  ({ menuItem }) =>
    (dispatch) => {
      dispatch({ type: CLONE_MENU_ITEM_REQUEST, payload: menuItem });
    const request = axios({
      method: 'POST',
      url: apiMenuItemClonePath(menuItem, { format: 'json' }),
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({ type: CLONE_MENU_ITEM_SUCCESS, payload: menuItem, cloned_payload: response.data });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({ type: CLONE_MENU_ITEM_FAILURE, payload: menuItem });
        });
    };

export const updateMenuItem =
  ({ menuItem }) =>
    (dispatch) => {
      dispatch({ type: UPDATE_MENU_ITEM_REQUEST, payload: menuItem });

    const { menu_extras, serving_sizes, rate_cards, dietary_preferences, ...sanitizedMenuItem } = menuItem;

    const request = axios({
      method: 'PUT',
      url: apiMenuItemPath(menuItem, { format: 'json' }),
      data: { menu_item: sanitizedMenuItem },
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({ type: UPDATE_MENU_ITEM_SUCCESS, payload: response.data });
          toast.success(`Updated item named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({ type: UPDATE_MENU_ITEM_FAILURE, payload: menuItem });
        });
    };

export const removeMenuItem = async ({ menuItem, isForced, dispatch }) => {
  dispatch({ type: REMOVE_MENU_ITEM_REQUEST, payload: menuItem });
  const request = axios({
    method: 'DELETE',
    url: apiMenuItemPath(menuItem, { format: 'json' }),
    data: { forced: isForced },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({ type: REMOVE_MENU_ITEM_SUCCESS, payload: menuItem });
      toast.success(`Removed item named '${menuItem.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      return response.data;
    })
    .catch((error) => {
      dispatch({ type: REMOVE_MENU_ITEM_FAILURE, payload: menuItem });
      if (isForced) {
        toast.error(`Could not remove item named '${menuItem.name}'`, { ...defaultToastOptions, autoClose: 10000 });
      }
      return Promise.reject(error);
    });
  return await request;
};

export const checkMenuItemUsage =
  ({ menuItem }) =>
    (dispatch) => {
      dispatch({ type: MENU_ITEM_ORDERS_REQUEST, payload: menuItem });

    const request = axios({
      method: 'GET',
      url: apiMenuItemCheckUsagePath(menuItem, { format: 'json' }),
    })
        .then((response) => {
          dispatch({ type: MENU_ITEM_ORDERS_SUCCESS, payload: menuItem, orders: response.data });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({ type: MENU_ITEM_ORDERS_FAILURE, payload: menuItem });
        });
    };
