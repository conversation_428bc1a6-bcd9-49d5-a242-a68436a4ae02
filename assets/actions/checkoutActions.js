import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import {
  apiOrderPath,
  apiOrderCheckSwipeCardAccessPath,
  apiOrderAmendPath,
  apiOrderSubmitPath,
  editAPIOrderPath,
  customerCheckoutDetailsAPICustomersPath,
} from 'routes';
import {
  FETCH_ORDER_DETAILS_REQUEST,
  FETCH_ORDER_DETAILS_SUCCESS,
  FETCH_ORDER_DETAILS_FAILURE,
  FETCH_BILLING_DETAILS_SUCCESS,
  FETCH_CUSTOMER_DETAILS_REQUEST,
  FETCH_CUSTOMER_DETAILS_SUCCESS,
  FETCH_CUSTOMER_DETAILS_FAILURE,
  UPDATE_SUBMIT_ORDER,
  UPDATE_AS_QUOTE_ORDER,
  SUBMIT_ORDER_REQUEST,
  SUBMIT_ORDER_SUCCESS,
  SUBMIT_ORDER_FAILURE,
  CANCEL_ORDER_REQUEST,
  CANCEL_ORDER_SUCCESS,
  CANCEL_ORDER_FAILURE,
  CHECK_SWIPE_CARD_ACCESS_REQUEST,
  CHECK_SWIPE_CARD_ACCESS_SUCCESS,
  CHECK_SWIPE_CARD_ACCESS_FAILURE,
} from './checkoutActionTypes';

export const fetchOrder = async ({ dispatch, orderID }) => {
  dispatch({ type: FETCH_ORDER_DETAILS_REQUEST });
  try {
    const { data: payloadOrder } = await axios({
      method: 'GET',
      url: editAPIOrderPath(orderID, { format: 'json' }),
    });

    if (payloadOrder) {
      dispatch({ type: FETCH_ORDER_DETAILS_SUCCESS, payload: payloadOrder });
    }
  } catch {
    dispatch({ type: FETCH_ORDER_DETAILS_FAILURE });
  }
};

export const fetchCustomer = async ({ orderID, dispatch }) => {
  dispatch({ type: FETCH_CUSTOMER_DETAILS_REQUEST });
  try {
    const {
      data: { billing_details, ...customer },
    } = await axios({
      method: 'GET',
      url: customerCheckoutDetailsAPICustomersPath({ format: 'json', order_id: orderID }),
    });

    if (billing_details) {
      dispatch({ type: FETCH_BILLING_DETAILS_SUCCESS, payload: billing_details });
    }

    if (customer) {
      dispatch({ type: FETCH_CUSTOMER_DETAILS_SUCCESS, payload: customer });
    }
  } catch {
    dispatch({ type: FETCH_CUSTOMER_DETAILS_FAILURE });
  }
};

export const submitOrder = async ({ order, billingDetails, mode, quoteDetails, dispatch }) => {
  dispatch({ type: SUBMIT_ORDER_REQUEST, payload: order });
  try {
    const { data: submittedOrder } = await axios({
      method: 'POST',
      url:
        order.status === 'quoted'
          ? apiOrderSubmitPath(order, { format: 'json' })
          : apiOrderAmendPath(order, { format: 'json' }),
      data: { 
        order,
        billing_details: billingDetails,
        mode,
        ...(quoteDetails?.emails && { quote_emails: quoteDetails.emails, quote_message: quoteDetails.message }),
      },
      headers: csrfHeaders(),
    });

    if (order.status !== 'quoted') {
      dispatch({ type: SUBMIT_ORDER_SUCCESS, payload: submittedOrder });
    }

    if (mode === 'quote') dispatch({ type: UPDATE_AS_QUOTE_ORDER, payload: false });

    return { submittedOrder };
  } catch (error) {
    dispatch({ type: UPDATE_SUBMIT_ORDER, payload: '' });
    const {
      data: { errors },
    } = error.response;
    dispatch({ type: SUBMIT_ORDER_FAILURE, payload: order, errors });
    return Promise.reject(errors);
  }
};

export const cancelOrder = async ({ order, mode, dispatch }) => {
  dispatch({ type: CANCEL_ORDER_REQUEST, payload: order });
  try {
    const { data: cancelledOrder } = await axios({
      method: 'DELETE',
      url: apiOrderPath(order, { format: 'json' }),
      data: { cancel_mode: mode },
      headers: csrfHeaders(),
    });
    dispatch({ type: CANCEL_ORDER_SUCCESS, payload: cancelledOrder });
  } catch (error) {
    const {
      data: { errors },
    } = error.response;
    dispatch({ type: CANCEL_ORDER_FAILURE, payload: order, errors });
    return Promise.reject(errors);
  }
};

export const checkSwipeAccess = async ({ orderID, deliveryAt, dispatch }) => {
  if (!deliveryAt) return false;

  dispatch({ type: CHECK_SWIPE_CARD_ACCESS_REQUEST });
  try {
    const {
      data: { suppliers },
    } = await axios({
      method: 'GET',
      url: apiOrderCheckSwipeCardAccessPath(orderID, { format: 'json' }),
      params: { order_delivery_at: deliveryAt },
    });

    dispatch({ type: CHECK_SWIPE_CARD_ACCESS_SUCCESS, payload: suppliers });
  } catch {
    dispatch({ type: CHECK_SWIPE_CARD_ACCESS_FAILURE });
  }
};
