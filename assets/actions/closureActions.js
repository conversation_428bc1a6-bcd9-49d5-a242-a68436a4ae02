import axios from 'axios';

import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiSupplierClosuresPath, apiSupplierClosurePath } from 'routes';
import {
  FETCH_CLOSURE_DATES_REQUEST,
  FETCH_CLOSURE_DATES_SUCCESS,
  FETCH_CLOSURE_DATES_FAILURE,
  FETCH_MORE_CLOSURE_DATES_REQUEST,
  FETCH_MORE_CLOSURE_DATES_SUCCESS,
  FETCH_MORE_CLOSURE_DATES_FAILURE,
  CREATE_CLOSURE_DATE_REQUEST,
  CREATE_CLOSURE_DATE_SUCCESS,
  CREATE_CLOSURE_DATE_FAILURE,
  UPDATE_CLOSURE_DATE_REQUEST,
  UPDATE_CLOSURE_DATE_SUCCESS,
  UPDATE_CLOSURE_DATE_FAILURE,
  REMOVE_CLOSURE_DATE_REQUEST,
  REMOVE_CLOSURE_DATE_SUCCESS,
  REMOVE_CLOSURE_DATE_FAILURE,
} from './closureActionTypes';

const MAXIMUM_RETURNED_DATES = 10;

export const fetchClosureDates =
  ({ page, infiniteLoading }) =>
    async (dispatch) => {
      dispatch({
        type: infiniteLoading ? FETCH_MORE_CLOSURE_DATES_REQUEST : FETCH_CLOSURE_DATES_REQUEST,
      });
      try {
        const { data: closure_dates } = await axios({
          method: 'GET',
        url: apiSupplierClosuresPath({ format: 'json' }),
        params: { page },
      });
        const hasMore = closure_dates.length === MAXIMUM_RETURNED_DATES;
        dispatch({
          type: infiniteLoading ? FETCH_MORE_CLOSURE_DATES_SUCCESS : FETCH_CLOSURE_DATES_SUCCESS,
          payload: {
            closure_dates,
            hasMore,
          },
        });
      } catch (error) {
        dispatch({
          type: infiniteLoading ? FETCH_MORE_CLOSURE_DATES_FAILURE : FETCH_CLOSURE_DATES_FAILURE,
          payload: error,
        });
      }
    };

export const createClosureDate =
  ({ closureDate }) =>
    async (dispatch) => {
      dispatch({ type: CREATE_CLOSURE_DATE_REQUEST, payload: closureDate });
      try {
        const { data: payload } = await axios({
          method: 'POST',
        url: apiSupplierClosuresPath({ format: 'json' }),
        data: { supplier_closure: closureDate },
        headers: csrfHeaders(),
      });
        dispatch({ type: CREATE_CLOSURE_DATE_SUCCESS, payload });
      } catch (error) {
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          alert(error_messages.join('. '));
        }
        dispatch({ type: CREATE_CLOSURE_DATE_FAILURE, payload: error });
        throw new Error(error);
      }
    };

export const updateClosureDate =
  ({ closureDate }) =>
    async (dispatch) => {
      dispatch({ type: UPDATE_CLOSURE_DATE_REQUEST, payload: closureDate });
      try {
        const { data: payload } = await axios({
          method: 'PUT',
        url: apiSupplierClosurePath(closureDate, { format: 'json' }),
        data: { supplier_closure: closureDate },
        headers: csrfHeaders(),
      });
        dispatch({ type: UPDATE_CLOSURE_DATE_SUCCESS, payload });
      } catch (error) {
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          alert(error_messages.join('. '));
        }
        dispatch({ type: UPDATE_CLOSURE_DATE_FAILURE, payload: error });
        throw new Error(error);
      }
    };

export const removeClosureDate =
  ({ closureDate }) =>
    async (dispatch) => {
      dispatch({ type: REMOVE_CLOSURE_DATE_REQUEST });
      try {
        await axios({
          method: 'DELETE',
        url: apiSupplierClosurePath(closureDate, { format: 'json' }),
        headers: csrfHeaders(),
      });
        dispatch({
          type: REMOVE_CLOSURE_DATE_SUCCESS,
          payload: closureDate,
        });
      } catch (error) {
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          alert(error_messages.join('. '));
        } else {
          alert('Oops! Something went wrong when trying to create a remove the closure date.');
        }
        dispatch({
          type: REMOVE_CLOSURE_DATE_FAILURE,
          payload: error,
        });
        throw new Error(error);
      }
    };
