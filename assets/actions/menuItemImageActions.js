import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiMenuItemPath } from 'routes';
import {
  UPLOAD_MENU_ITEM_IMAGE_REQUEST,
  UPLOAD_MENU_ITEM_IMAGE_SUCCESS,
  UPLOAD_MENU_ITEM_IMAGE_FAILURE,
  REMOVE_MENU_ITEM_IMAGE_REQUEST,
  REMOVE_MENU_ITEM_IMAGE_SUCCESS,
  REMOVE_MENU_ITEM_IMAGE_FAILURE,
} from './menuActionTypes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

// upload image to cloudinary
export const uploadImage =
  ({ uploadUrl, uploadPreset, menuItem, image }) =>
    async (dispatch) => {
      dispatch({ type: UPLOAD_MENU_ITEM_IMAGE_REQUEST });
      const data = new FormData();
      data.append('file', image);
      data.append('upload_preset', uploadPreset);

    try {
      const response = await axios.post(uploadUrl, data);
      dispatch(
        saveImageToMenuItem({
          menuItem,
          imageUrl: response.data.secure_url,
        })
      );
        dispatch({ type: UPLOAD_MENU_ITEM_IMAGE_SUCCESS });
        return response.data.secure_url;
      } catch (error) {
        dispatch({
          type: UPLOAD_MENU_ITEM_IMAGE_FAILURE,
          payload: error,
        });
        throw error;
      }
    };

// send uploaded image url against menu item
const saveImageToMenuItem =
  ({ menuItem, imageUrl }) =>
    (dispatch) => {
    const menuItemWithImage = {
      id: menuItem.id,
      menu_section_id: menuItem.menu_section_id,
      image: imageUrl,
    };
    const request = axios({
      method: 'PUT',
      url: apiMenuItemPath(menuItem, { format: 'json' }),
      data: { menu_item: menuItemWithImage },
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({
            type: UPLOAD_MENU_ITEM_IMAGE_SUCCESS,
            payload: response.data,
          });
          toast.success(`Saved image for item named '${menuItem.name}'`, { ...defaultToastOptions, autoClose: 5000 });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({
            type: UPLOAD_MENU_ITEM_IMAGE_FAILURE,
            payload: error_messages,
          });
        });
    };

export const removeImage =
  ({ menuItem }) =>
    async (dispatch) => {
      dispatch({ type: REMOVE_MENU_ITEM_IMAGE_REQUEST });
    const menuItemWithOutImage = {
      id: menuItem.id,
      menu_section_id: menuItem.menu_section_id,
      image: null,
    };
    try {
      const response = await axios({
        method: 'PUT',
        url: apiMenuItemPath(menuItem, { format: 'json' }),
        data: { menu_item: menuItemWithOutImage },
        headers: csrfHeaders(),
      });
        dispatch({
          type: REMOVE_MENU_ITEM_IMAGE_SUCCESS,
          payload: response.data,
        });
        toast.success(`Removed image for item named '${menuItem.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      } catch (error) {
        let error_messages;
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
        }
        dispatch({
          type: REMOVE_MENU_ITEM_IMAGE_FAILURE,
          payload: error_messages,
        });
      }
    };
