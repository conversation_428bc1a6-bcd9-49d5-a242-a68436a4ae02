import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiSupplierPath } from 'routes';
import { UPDATE_SUPPLIER_REQUEST, UPDATE_SUPPLIER_SUCCESS, UPDATE_SUPPLIER_FAILURE } from './menuActionTypes';

export const updateSupplier = async ({ supplier, dispatch }) => {
  dispatch({ type: UPDATE_SUPPLIER_REQUEST });
  const request = await axios({
    method: 'PUT',
    url: apiSupplierPath(supplier, { format: 'json' }),
    data: { supplier_profile: supplier },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({
        type: UPDATE_SUPPLIER_SUCCESS,
        payload: response.data,
      });
      return response.data;
    })
    .catch((error) => {
      dispatch({
        type: UPDATE_SUPPLIER_FAILURE,
        payload: error,
      });
      return Promise.reject(error);
    });
};

export const updateSupplierFlags = async ({ supplier, dispatch }) => {
  dispatch({ type: UPDATE_SUPPLIER_REQUEST });
  const request = await axios({
    method: 'PUT',
    url: apiSupplierPath(supplier, { format: 'json' }),
    data: { supplier_flags: supplier },
    headers: csrfHeaders(),
  })
    .then((response) => {
      dispatch({
        type: UPDATE_SUPPLIER_SUCCESS,
        payload: response.data,
      });
      return response.data;
    })
    .catch((error) => {
      dispatch({
        type: UPDATE_SUPPLIER_FAILURE,
        payload: error,
      });
      return Promise.reject(error);
    });
};
