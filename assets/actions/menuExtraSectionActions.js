import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiMenuExtraSectionsPath, apiMenuExtraSectionPath } from 'routes';
import {
  CREATE_MENU_EXTRA_SECTION_REQUEST,
  CREATE_MENU_EXTRA_SECTION_SUCCESS,
  CREATE_MENU_EXTRA_SECTION_FAILURE,
  UPDATE_MENU_EXTRA_SECTION_REQUEST,
  UPDATE_MENU_EXTRA_SECTION_SUCCESS,
  UPDATE_MENU_EXTRA_SECTION_FAILURE,
  REMOVE_MENU_EXTRA_SECTION_REQUEST,
  REMOVE_MENU_EXTRA_SECTION_SUCCESS,
  REMOVE_MENU_EXTRA_SECTION_FAILURE,
} from './menuActionTypes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

export const createMenuExtraSection =
  ({ menuExtraSection }) =>
    (dispatch) => {
      dispatch({ type: CREATE_MENU_EXTRA_SECTION_REQUEST });
      const request = axios({
        method: 'POST',
      url: apiMenuExtraSectionsPath({ format: 'json' }),
      data: { menu_extra_section: menuExtraSection },
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({ type: CREATE_MENU_EXTRA_SECTION_SUCCESS, payload: response.data });
          toast.success(`Created extras-section named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          } else {
            toast.error('Oops! Something went wrong when trying to create a new extras section. Please try again!', { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({ type: CREATE_MENU_EXTRA_SECTION_FAILURE });
        });
    };

export const updateMenuExtraSection =
  ({ menuExtraSection }) =>
    (dispatch) => {
    dispatch({ type: UPDATE_MENU_EXTRA_SECTION_REQUEST, payload: menuExtraSection });
    const request = axios({
      method: 'PUT',
      url: apiMenuExtraSectionPath(menuExtraSection, { format: 'json' }),
      data: { menu_extra_section: menuExtraSection },
      headers: csrfHeaders(),
    })
      .then((response) => {
        dispatch({ type: UPDATE_MENU_EXTRA_SECTION_SUCCESS, payload: response.data });
        toast.success(`Updated extras-section named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
      })
      .catch((error) => {
        let error_messages;
        if (error.response.status === 422) {
          const error_messages = error.response.data.errors;
          toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
        }
        dispatch({ type: UPDATE_MENU_EXTRA_SECTION_FAILURE, payload: menuExtraSection });
      });
    };

export const removeMenuExtraSection =
  ({ menuExtraSection }) =>
    (dispatch) => {
      dispatch({ type: REMOVE_MENU_EXTRA_SECTION_REQUEST, payload: menuExtraSection });
    const request = axios({
      method: 'DELETE',
      url: apiMenuExtraSectionPath(menuExtraSection, { format: 'json' }),
      headers: csrfHeaders(),
    })
        .then((response) => {
          dispatch({ type: REMOVE_MENU_EXTRA_SECTION_SUCCESS, payload: menuExtraSection });
          toast.success(`Removed extras-section named '${response.data.name}'`, { ...defaultToastOptions, autoClose: 5000 });
        })
        .catch((error) => {
          let error_messages;
          if (error.response.status === 422) {
            const error_messages = error.response.data.errors;
            toast.error(error_messages.join('.'), { ...defaultToastOptions, autoClose: 10000 });
          }
          dispatch({ type: REMOVE_MENU_EXTRA_SECTION_FAILURE, payload: menuExtraSection });
        });
    };
