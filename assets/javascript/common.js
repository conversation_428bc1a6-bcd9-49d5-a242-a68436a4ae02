export function checkClosureTimes(callback, $button, $wrapper) {
  const delivery_at = $('#order_delivery_at').val();
  const buttonText = $button.text();
  const $form = $wrapper.find('.order-details-form');
  const lead_time_request = $.ajax({
    url: $form.data('check-closure-dates'),
    type: 'GET',
    data: { order_delivery_at: delivery_at },
  });
  $button.html(spinner_html);
  lead_time_request.done(function (response) {
    if (response.can_process) {
      $button.text(buttonText);
      checkLeadTime(callback, $button, $wrapper);
    } else {
      let display_msg = ''; // "Yordar will be closed from " + response.yordar_close_from + " to " + response.yordar_close_to +".</br>"
      const closure_suppliers = response.supplier_closure_dates;
      for (let i = 0; i < closure_suppliers.length; i++) {
        const supplier = closure_suppliers[i];
        display_msg += `${supplier.name} will be closed from ${supplier.close_from} to ${supplier.close_to}.</br>`;
      }
      display_msg += 'Please place your order before or after these dates.';
      // display_msg += "Recurring orders can't be placed during the closure period. <br>Only one-off orders are allowed as an admin."
      $(this).yordarPOP({
        container: '#yordarPopUp',
        title: 'Supplier Closures',
        innerContent: display_msg,
        submit: 'Change Date',
        submitHandler(event) {
          // select date again
          $wrapper.find('#order_delivery_at').focus();
        },
        alternate: 'Find other suppliers',
        alternateHandler(event) {
          window.location = `/search?order_date=${delivery_at.split(' ')[0]}`;
        },
      });
    }
  });
  lead_time_request.always(function () {
    $button.text(buttonText);
  });
}

function checkLeadTime(callback, $button, $wrapper) {
  const delivery_at = $('#order_delivery_at').val();
  const buttonText = $button.text();
  const $form = $wrapper.find('.order-details-form');
  const lead_time_request = $.ajax({
    url: $form.data('check-lead-time'),
    type: 'GET',
    data: { order_delivery_at: delivery_at },
  });
  $button.html(spinner_html);

  lead_time_request.done(function (response) {
    if (response.can_process) {
      callback($button, $wrapper);
    } else {
      const min_delivery_at = response.formatted_minimum_delivery_at;
      let display_msg = 'You are placing an order outside of the ';
      display_msg += `<span title='${min_delivery_at}'>supplier lead time</span>.</br>`;
      display_msg += ' If you place this order it might get rejected.';
      $(this).yordarPOP({
        container: '#yordarPopUp',
        title: 'Supplier Lead Times',
        innerContent: display_msg,
        submit: 'Continue',
        cancel: 'Change',
        submitHandler(event) {
          callback($button, $wrapper);
        },
        cancelHandler(event) {
          // select date again
          $wrapper.find('#order_delivery_at').focus();
        },
      });
    }
  });

  lead_time_request.always(function () {
    $button.text(buttonText);
  });
}
