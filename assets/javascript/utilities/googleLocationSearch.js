const googleAutoCompleteOptions = (countryCode) => (
  {
    componentRestrictions: {
      country: countryCode || 'au'
    }
  }
);

const googleAutoCompleteFields = [
  'place_id',
  'types',
  'formatted_address',
  'address_components'
]

export default function googleLocationSearch (el, props) {
  var $el = $(el);
  var $wrapper = $el.parents(".suppliers-cta-form")
  bindEvents($el, $wrapper, props);
  setupGoogleAutoComplete($el, $wrapper, props);
}

function bindEvents ($input, $wrapper, props) {
  $input.focusin(function() {
    // This is to prevent the selected suburb from being cleared if read only.
    if ($(this).attr('readonly') == 'readonly') {
      return;
    }
    // Always assume the value is invalid.
    $(this).data('valid', 'false');
  })

  $input.focusout(function() {
    $(this)
      .siblings('span')
      .text('')
      .delay(2000)
      .fadeTo('slow', 0.6);
  });

  $wrapper.on('click', '.search-submit', function(e) { submitForm(e, $input, $wrapper, props) });
}

function submitForm(event, $input, $wrapper, props) {
  event.preventDefault();
  if ($input.val() !== "") {
    var selected_location = {
      suburb: props.suburb,
      state: props.state,
      postcode: props.postcode,
      street_address: props.street_address,
      suburb_id: props.subub_id,
    }
    searchSuppliers(selected_location, $input, $wrapper, props);
  }
}

function setupGoogleAutoComplete ($input, $wrapper, props) {
  var google_maps_autocomplete = new google.maps.places.Autocomplete(
    $input[0],
    googleAutoCompleteOptions(props.countryCode),
  );

  google_maps_autocomplete.setFields(googleAutoCompleteFields);

  google_maps_autocomplete.addListener('place_changed', function() {
    onLocationChange(google_maps_autocomplete, $input, $wrapper, props);
  });
}

function onLocationChange (google_maps_search_autocomplete, $input, $wrapper, props) {
  var selected_place = google_maps_search_autocomplete.getPlace();

  var selected_location = {
    suburb: '',
    state: '',
    postcode: '',
    street_address: '',
  }

  if (selected_place.address_components) {

    // Get the postcode of the selected place.
    var selected_postcode_address_component = $.grep(
      selected_place.address_components, function(component, index) {
        return $.inArray("postal_code", component.types) !== -1;
      }
    )[0];

    if (selected_postcode_address_component) {
      selected_location.postcode = selected_postcode_address_component.short_name;
    }

    // Get the sub-locality of the selected place (city, metropolitan area, etc).
    var selected_sublocality_address_component = $.grep(
      selected_place.address_components, function(component, index) {
        return $.inArray("sublocality", component.types) !== -1;
      }
    )[0];

    if (selected_sublocality_address_component) {
      selected_location.suburb = selected_sublocality_address_component.long_name;
    }

    // Get the locality of the selected place (city, metropolitan area, etc).
    var selected_locality_address_component = $.grep(
      selected_place.address_components, function(component, index) {
        return $.inArray("locality", component.types) !== -1;
      }
    )[0];

    if (selected_locality_address_component) {
      selected_location.suburb ||= selected_locality_address_component.long_name;
    }

    var selected_state_address_component = $.grep(
      selected_place.address_components, function(component, index) {
        return $.inArray("administrative_area_level_1", component.types) !== -1;
      }
    )[0];

    if (selected_state_address_component) {
      selected_location.state = selected_state_address_component.short_name;
    }

    // If Google didn't return either a postcode or locality, but did return
    // a political place type, then try setting the locality to be searched
    // for to the first political component. This is to handle results like
    // Sydney CBD, NSW, Australia".
    if (!selected_location.postcode && !selected_location.suburb) {
      var selected_political_address_component = $.grep(
        selected_place.address_components, function(component, index) {
          return $.inArray("political", component.types) !== -1;
        }
      )[0];

      if (selected_political_address_component) {
        selected_location.suburb = selected_political_address_component.long_name.replace(
          " CBD",
          ""
        );
      }
    }

    // Get the street address of the selected place.
    if (
      $.inArray("street_address", selected_place.types) !== -1 ||
      $.inArray("premise", selected_place.types) !== -1
    ) {
      selected_location.street_address = $.grep(
        selected_place.formatted_address.split(", "),
        function(address_component) {
          return !address_component.startsWith("Level");
        }
      )[0];
    }
    searchSuppliers(selected_location, $input, $wrapper, props);
  }
  return false;
}

function searchSuppliers(selected_location, $input, $wrapper, props) {
  var { suburb, state, postcode, street_address, suburb_id } = selected_location;
  var { is_next_app, category_group, search_url } = props;

  var searchUrl = '';
  var location_queries = new Array;
  var queries = new Array;

  var location_query = window.location.search.substring(1);
  if (location_query) {
    location_queries = location_query.split("&");
  }

  if (is_next_app) {
    searchUrl = search_url;
    if ($input.data('category-group')) {
      category_group = $input.data('category-group');
    }
    searchUrl = searchUrl.replace('_category_group_', category_group)
    searchUrl = searchUrl.replace('_state_', state)
    searchUrl = searchUrl.replace('_suburb_', suburb)

    var possible_filters = ["category[]", "category%5B%5D", "search_keywords", "team_suppliers"];
    for(var i=0; i<location_queries.length; i++) {
      var query = location_queries[i];
      var query_field = query.split("=")[0];
      if (possible_filters.includes(query_field)) {
        queries.push(query);
      }
    }
    if (postcode) {
     queries.push("postcode=" + postcode);
    }
    if (street_address) {
     queries.push("street_address=" + street_address);
    }
    if (queries.length) {
      searchUrl += "?" + queries.join("&");
    }
  } else {
    if (suburb) {
      queries.push("suburb=" + suburb);
    }
    if (state) {
     queries.push("state=" + state);
    }
    if (postcode) {
     queries.push("postcode=" + postcode);
    }
    if (street_address) {
     queries.push("street_address=" + street_address);
    }

    if (!suburb && suburb_id != undefined) {
     queries.push("suburb_id=" + suburb_id);
    }

    var possible_filters = ["category_group", "category[]", "category%5B%5D", "search_keywords", "team_suppliers"];
    for(var i=0; i<location_queries.length; i++) {
      var query = location_queries[i];
      var query_field = query.split("=")[0];
      if (possible_filters.includes(query_field)) {
        queries.push(query);
      }
    }
    if (queries.length > 0) {
      searchUrl = search_url + "?" + queries.join("&");
    }
  }

  if (searchUrl != '') {
    $wrapper.find(".search-submit").hide();
    $wrapper.find("#postcode-search-spinner").show()
    window.location = searchUrl
  }
}
