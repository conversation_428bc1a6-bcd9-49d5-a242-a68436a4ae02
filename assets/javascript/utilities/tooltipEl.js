require('jquery-ui/ui/widgets/tooltip');

export default function tooltipEl (el, props) {
  var $el = $(el);
  var contentOptions = {};
  if (props && props.allow_html) {
    contentOptions = {
      content: function () {
        return $el.prop('title');
      }
    }
  }
  const mergedOPtions = Object.assign({}, contentOptions, props);
  const { allow_html, ...tooltipOptions  } = mergedOPtions;

  $el.tooltip(tooltipOptions);
}

