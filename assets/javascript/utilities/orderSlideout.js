export default function orderSlideout(el, props) {
  const $el = $(el)
  bindSlideoutEvents($el);
}

function bindSlideoutEvents ($wrapper) {
  $wrapper.on("click", "a[data-modal-view]", function (e) { modalView(e) });
  $wrapper.on('click', '.view-slider-link-calendar', function (e) {modalView(e)});
  $(document).on("click", ".collapsible", function (e) { collapseContent(e) });
}

function modalView(event) {
  event.preventDefault()
  var $link = $(event.currentTarget);
  var $orderModal = $(document).find('#modal-order-show');

  $orderModal.foundation('open');
  $orderModal.html('<div class="lds-ring-container"><div class="lds-ring"><div></div><div></div><div></div><div></div></div></div>');
  var menu_request = $.ajax({
    url: $link.attr('href'),
    type: 'GET',
    dataType: 'JSON',
    data: { wants_html: true },
  });

  menu_request.done(function (response) {
    $orderModal.html(response.html);
    showFirstLocation($orderModal);
  });

  menu_request.fail(function (err) {
    $(this).yordarPOP({
      title: "Order",
      innerContent: "Sorry, we couldn't load the order details right now, please try again later.",
      cancel: false
    });
  });
}

function showFirstLocation($orderModal) {
  var $firstLocation = $orderModal.find('.first-location')
  $firstLocation.prev('.collapsible').click();
}

function collapseContent (event) {
  var $header = $(event.currentTarget);
  var $content = $header.next('.content');
  var maxHeight = $content.css('maxHeight');
  if (maxHeight === null || maxHeight == '0px') {
    $content.css('maxHeight', $content.prop('scrollHeight'))
  } else {
    $content.css('maxHeight', '0px')
  }
}
