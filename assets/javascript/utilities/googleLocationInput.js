const { google } = window;
const API_SUBURB_ENDPOINT = '/api/suburbs.json';

const googleAutoCompleteOptions = (countryCode) => (
  {
    componentRestrictions: {
      country: countryCode || 'au'
    }
  }
);

const googleAutoCompleteFields = [
  'place_id',
  'types',
  'formatted_address',
  'address_components'
]

const defaultOptions = {
  restricted_suburb_id: null, // 'google-autocomplete-restrict-to-suburb-id'
  restricted_suburb_label: null, // 'google-autocomplete-restrict-to-suburb-label'
  as_street_address: false, // 'google-autocomplete-only-set-street-address'
  set_suburb: false, // 'google-autocomplete-set-suburb-into-form'
  set_street_address: false, // 'google-autocomplete-copy-street-address-to-selector'
  auto_submit_form: false, // $form.hasClass('suppliers-cta-form')
}

export default function googleLocationInput (el, props) {
  var $el = $(el);
  var options = Object.assign({}, defaultOptions, props);

  bindEvents($el);
  setupGoogleAutoComplete($el, options);
}

function bindEvents ($el) {
  $el.focusin(function() {
    // This is to prevent the selected suburb from being cleared if read only.
    if ($(this).attr('readonly') == 'readonly') {
      return;
    }
    // Always assume the value is invalid.
    $(this).data('valid', 'false');
  })

  $el.focusout(function() {
    $(this)
      .siblings('span')
      .text('')
      .delay(2000)
      .fadeTo('slow', 0.6);
  });
}

function setupGoogleAutoComplete ($input, options) {
  var google_maps_autocomplete = new google.maps.places.Autocomplete(
    $input[0],
    googleAutoCompleteOptions(options.countryCode)
  );

  google_maps_autocomplete.setFields(googleAutoCompleteFields);

  google_maps_autocomplete.addListener('place_changed', function() {
    onLocationChange(google_maps_autocomplete, $input, options);
  });
}

function onLocationChange (google_maps_autocomplete, $input, options) {
  var selected_postcode = '';
  var selected_locality = '';
  var selected_street_address = '';

  var selected_place = google_maps_autocomplete.getPlace();

  // Get the postcode of the selected place.
  var selected_postcode_address_component = $.grep(
    selected_place.address_components,
    function(component, index) {
      if ($.inArray('postal_code', component.types) !== -1) {
        return true;
      }
    }
  )[0];
  if (selected_postcode_address_component) {
    selected_postcode = selected_postcode_address_component.short_name;
  }

  // Get the locality of the selected place (city, metropolitan area, etc).
  var selected_locality_address_component = $.grep(
    selected_place.address_components,
    function(component, index) {
      if ($.inArray('locality', component.types) !== -1) {
        return true;
      }
    }
  )[0];
  if (selected_locality_address_component) {
    selected_locality = selected_locality_address_component.long_name;
  }

  // If Google didn't return either a postcode or locality, but did return
  // a political place type, then try setting the locality to be searched
  // for to the first political component. This is to handle results like
  // Sydney CBD, NSW, Australia'.
  if (!selected_postcode && !selected_locality) {
    var selected_political_address_component = $.grep(
      selected_place.address_components,
      function(component, index) {
        if ($.inArray('political', component.types) !== -1) {
          return true;
        }
      }
    )[0];

    if (selected_political_address_component) {
      selected_locality = selected_political_address_component.long_name.replace(
        ' CBD',
        ''
      );
    }
  }

  // Get the street address of the selected place.
  var selected_street_address_components = [];
  if (
    $.inArray('street_address', selected_place.types) !== -1 ||
    $.inArray('premise', selected_place.types) !== -1 ||
    $.inArray('subpremise', selected_place.types) !== -1
  ) {
    selected_street_address_components = $.grep(
      selected_place.formatted_address.split(', '),
      function(address_component) {
        return !address_component.startsWith('Level');
      }
    )
    selected_street_address = selected_street_address_components.slice(0, (selected_street_address_components.length - 2)).join(', ');
  }

  setSelectedLocation($input, options, selected_postcode, selected_locality, selected_street_address);
}

function setSelectedLocation ($input, options, selected_postcode, selected_locality, selected_street_address) {
  var $form = $input.parents('form');

  // Use the Yordar API to map the selected postcode or locality to the internal Suburb record.
  $.getJSON(
    API_SUBURB_ENDPOINT,
    { term: selected_postcode || selected_locality },
    function(suburbs) {
      // If we got a postcode from Google, match the suburb based on both postcode and locality name.
      if (selected_postcode) {
        var suburb = $.grep(suburbs, function(suburb, index) {
          if (
            suburb.postcode == selected_postcode &&
            suburb.name == selected_locality
          ) {
            return true;
          }
        })[0];
      }
      // Next, try and match the suburb based on locality name.
      if (suburb == undefined) {
        var suburb = $.grep(suburbs, function(suburb, index) {
          if (suburb.name == selected_locality) {
            return true;
          }
        })[0];

        // Edge case: There is a suburb in Tasmania called 'Perth'. If searching by locality name for 'Perth', make sure we use the WA city, not TAS suburb.
        if (selected_locality == 'Perth') {
          var suburb = $.grep(suburbs, function(
            suburb,
            index
          ) {
            if (suburb.postcode == 6000) {
              return true;
            }
          })[0];
        }
      }

      // As a fallback, use the first suburb.
      if (suburb == undefined) {
        var suburb = suburbs[0];
      }

      if (options.as_street_address) {
        $input.val(selected_street_address);
      }

      if (options.set_street_address) {
        $form.find(options.set_street_address).val(selected_street_address);
      }

      if (options.restricted_suburb_id && suburb.id != options.restricted_suburb_id) {
        $input.val('');
        $(document).yordarPOP({
          title: 'Delivery street address',
          innerContent:
            'Please select an address within ' +
            options.restricted_suburb_label +
            ' (the delivery suburb).',
          cancel: false,
          submit: 'OK'
        });
        return;
      }

      if (options.set_suburb) {
        $form.find('#suburb_id').val(suburb.id).trigger('change');
        $form.find('input#order_suburb').val(suburb.label);
        $form.find(options.set_suburb).val(suburb.id);
      }

      $input.data('valid', 'true');

      // Automatically submit the form on the home page and top banner.
      if (options.auto_submit_form) {
        $form.submit();
      }
    }
  );
}
