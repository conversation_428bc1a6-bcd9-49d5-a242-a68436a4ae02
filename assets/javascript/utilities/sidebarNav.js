const {$} = window

export default function sidebarNav (el, props) {
  const $el = $(el)
  bindEvents($el, props);
}

function bindEvents ($el, props) {
  $el.on('click', '.customer-sidebar-link__nested', function(e) { toggleNestedNav(e) });
}

function toggleNestedNav (event) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  var $nestedList = $link.parents('.nested-list');
  if (!$nestedList.hasClass('active')) {    
    var $nestedMenu = $nestedList.find('ul.nested.menu')
    $nestedList.toggleClass('open');
    $nestedMenu.toggleClass('hidden');  
  }
}
