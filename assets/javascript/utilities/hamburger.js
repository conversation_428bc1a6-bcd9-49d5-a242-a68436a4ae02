export default function hamburger(el, props) {
  const $el = $(el)
  if (props.kind == 'dash' ) {
    bindDashHamburgerEvents($el);
  } else {
    bindNavHamburgerEvents($el);
  }
}

function bindDashHamburgerEvents ($el) {
  var $sidebar = $(document).find('.customer-sticky-sidebar');
  $(window).resize(function () {
    if (window.innerWidth > 900) {
      $sidebar.show();
    }
  });
  $el.on('click', function (e) { openDashNav(e, $el, $sidebar) });
}

function openDashNav (event, $el, $sidebar) {
  $el.toggleClass('open');
  $sidebar.toggle();
  $('body').toggleClass('prevent-scroll')
}

function bindNavHamburgerEvents($el) {
  $el.on('click', function (e) { openMainNav(e, $el) });
}

function openMainNav (event, $el) {
  var $mainNav = $(document).find('#main-navigation');
  $el.toggleClass('open');
  $mainNav.toggleClass('active');
  $('body').toggleClass('lock');
}

