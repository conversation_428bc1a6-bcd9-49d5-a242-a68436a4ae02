export default function adminBanner(el, props) {
  const $el = $(el)
  bindEvents($el);
}

function bindEvents ($wrapper) {
  $wrapper.on('click', '#customer-admin-notes', function (e) { toggleCustomerAdminNotes(e, $wrapper) });
}

// For admins signed in as customers
function toggleCustomerAdminNotes (event, $wrapper) {
  var $link = $(event.currentTarget);
  var link_text;
  var $notes = $wrapper.find('#customer-notes-text');
  $notes.toggleClass('hidden');
  if ($notes.hasClass('hidden')) {
    link_text = "Click here to view customer notes";
  } else {
    link_text = "Click here to hide customer notes";
  }
  $link.text(link_text);
}
