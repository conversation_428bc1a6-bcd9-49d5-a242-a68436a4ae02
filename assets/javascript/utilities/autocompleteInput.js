require('jquery-ui/ui/widgets/autocomplete');

export default function autocompleteInput (el, props) {
  var $el = $(el);
  setupAutocompletion($el, props);
}

function setupAutocompletion($el, props) {
  var $targetfield = $('input[name="' + props.target_name + '"]');

  $el.autocomplete({
    source: function (request, response) {
      var autocomplete_request = $.ajax({
        url: props.path,
        type: 'GET',
        dataType: 'JSON',
        data: { term: request.term, country_code: (props.countryCode || 'au') }
      });

      autocomplete_request.done(function (suburbs) {
        response(suburbs);
      });
    },
    minLength: 1,
    select: function (event, ui) {
      $targetfield.val(ui.item.id);
      $el.attr('data-valid', 'true');
    }
  });
  $el.attr('autocomplete', 'nope');
}
