require('jquery-ui/ui/widgets/datepicker');

export default function datepickerInput (el, props) {
  var $el = $(el);
  setupDatepicker($el, props);
}

function setupDatepicker($el, props) {
  var options = Object.assign({}, defaultConfig, props.options)

  if (props.is_reports) {
    options = Object.assign({}, options, reportsConfig)
  }

  $el.datepicker(options);
}

const defaultConfig = {
  dateFormat: 'dd-mm-yy',
  showWeek: true,
  firstDay: 1,
}

const reportsConfig = {
  beforeShowDay: function(date){
    var y = date.getFullYear()
    var m = date.getMonth();
    var firstDay = new Date(y, m, 1);
    var lastDay = new Date(y, m + 1, 0);
    return [(date.getDay() === 0 || date.getDay() == 1 || date.getDate() === firstDay.getDate() || date.getDate() === lastDay.getDate()), ''];
  }
}


