const {$} = window

export default function newOrderDropdown (el, props) {
  const $el = $(el)
  bindOrderDropdownEvents($el, props);
}

function bindOrderDropdownEvents ($el, props) {
  $el.on('click', '.create-new-link', function(e) { clearDeliveryDateCookie(e, props) });
}

// Clear the 'delivery_date_filter' cookie (only to be set when creating new order from calendar view)
function clearDeliveryDateCookie (event, props) {
  const domainAttribute = props.cookie_domain ? `; domain=${props.cookie_domain}` : '';
  const expires = 'expires=Thu, 01 Jan 1970 00:00:00 UTC';
  document.cookie = `delivery_date_filter=; ${expires}${domainAttribute}; path=/;`;
}