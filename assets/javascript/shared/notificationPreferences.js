export default function notificationPreferences (el, props) {
  const $el = $(el)
  bindEvents($el);
}

function bindEvents($wrapper) {
  $wrapper.on("click", ".toggle-overrides", function(e) { toggleOverrides(e) });
  $wrapper.on("change", "form.notification-preference input", function(e) { triggerUpdate(e) });
  $wrapper.on("submit", "form.notification-preference", function(e) { updatePreference(e) });
}

function toggleOverrides(event) {
  var $button = $(event.currentTarget);
  var $overrides = $button
    .parents(".notification-preference")
    .find(".preference-overrides");
  $button.toggleClass("hidden");
  $overrides.toggleClass("hidden");
}

function triggerUpdate(event) {
  var $form = $(event.currentTarget);
  $form.submit();
}

function updatePreference(event) {
  event.preventDefault();
  var $form = $(event.currentTarget);
  $form.find(".notification-pref__loading").removeClass("hidden");
  var preferenceData = $form.serialize()
  $form.find('input').attr('disabled', true)
  var preference_request = $.ajax({
    url: $form.attr("action"),
    type: $form.data("method"),
    data: preferenceData + "&" + $.param({ wants_html: true }),
    dataType: "JSON",
  });

  preference_request.done(function(response) {
    $form.replaceWith(response.html);
  });

  preference_request.always(function(response) {
    $(".notification-pref__loading").addClass("hidden");
    $form.find('input').attr('disabled', false)
  });

  preference_request.fail(function(response) {
    $form.yordarPOP({
      title: "Preference Error",
      message: "We were unable to save the preference, please try again!",
      // innerContent: "All the items under this location will be deleted as well.",
      cancel: false,
    });
  });
}
