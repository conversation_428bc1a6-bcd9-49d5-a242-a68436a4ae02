export default function userFlags (el, props) {
  const $el = $(el);
  bindFlagEvents($el);
}

function bindFlagEvents ($wrapper) {
  $wrapper.on('change', '.user-tag', function (e) { updateFlag(e, $wrapper) });
}

function updateFlag (event, $wrapper) {
  var $input = $(event.currentTarget);
  var $form = $input.parents('form.tag-form');

  var update_request = $.ajax({
    url: $form.attr('action'),
    dataType: 'JSON',
    type: 'POST',
    data: $form.serialize()
  });

  update_request.done(function(response) {
    // alert('done');
  })

  update_request.fail(function(response) {
    // alert('fail');
  })
}
