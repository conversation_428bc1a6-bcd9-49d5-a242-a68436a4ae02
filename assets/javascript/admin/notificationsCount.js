import * as ActionCable from '@rails/actioncable';

export default function notificationsCount(el, props) {
  const $el = $(el);
  if (props.count === 0) {
    $el.addClass('hidden');
  }
  createNotificaitonsConsumer($el, props);
  subscribeToNotifications($el, props);
}

const createNotificaitonsConsumer = ($wrapper, props) => {
  window.App || (window.App = {});
  window.App.cable = ActionCable.createConsumer();
};

const subscribeToNotifications = ($el, props) => {
  App.cable.subscriptions.create(
    { channel: 'NotificationsChannel' },
    {
      connected() {
        console.log('Connected to the channel:', this);
      },
      disconnected() {
        console.log('Disconnected from channel');
      },
      received(data) {
        if (data.count === 0) {
          $el.addClass('hidden');
        } else {
          $el.removeClass('hidden');
        }
        $el.text(data.count);
      },
    }
  );
};
