export default function supplierOrder (el, props) {
  const $el = $(el);
  bindEvents($el);
}

function bindEvents ($wrapper) {
  $(document).on('click', '.order-confirm-btn', function (e) { confirmOrder(e, $wrapper) });
  $(document).on('click', '.order-reject-btn', function (e) { rejectOrder(e, $wrapper) });
}
function confirmOrder (event, $wrapper) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  var orderId = $link.data('order-id');
  var $order = $wrapper.find(`.order-item[data-order-id=${orderId}]`);

  var confirm_request = $.ajax({
    url: $link.data('url'),
    type: 'PUT',
    dataType: 'JSON',
    data: { wants_html: true }
  });

  confirm_request.done(function(response) {
    $order.yordarPOP({
      title: 'Yordar : Confirmed order',
      innerContent: 'Order has been confirmed successfully',
      cancel: false,
      submitHandler: function () {
        if ($link.data('reload')) {
          window.location.reload();
        } else {
          $order.replaceWith(response.order_html);  
        }
      }
    });   
  });

  confirm_request.fail(function (xhr, status) {
    $order.yordarPOP({
      title: 'Yordar : Order confirmation',
      innerContent: 'Sorry! we were unable to confirm the order',
      cancel: false,
    });
  });
}

function rejectOrder (event, $wrapper) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  $link.yordarPOP({
    innerContent: 'Are you sure you want to reject this order?',
    submit: 'Reject',
    submitHandler: function () {
      rejectSupplierOrder($link, $wrapper);
    }
  });
}

function rejectSupplierOrder ($link, $wrapper) {
  var orderId = $link.data('order-id');
  var $order = $wrapper.find(`.order-item[data-order-id=${orderId}]`);
  var reject_request = $.ajax({
    url: $link.data('url'),
    type: 'PUT',
    dataType: 'JSON',
    data: { wants_html: true }
  });

  reject_request.done(function(response) {
    $order.yordarPOP({
      title: 'Yordar : Rejected order',
      innerContent: 'Order has been rejected successfully',
      cancel: false,
      submitHandler: function () {
        if ($link.data('reload')) {
          window.location.reload();
        } else {
          $order.replaceWith(response.order_html);
        }
      }
    });      
  });

  reject_request.fail(function (xhr, status) {
    $order.yordarPOP({
      title: 'Yordar : Order Rejection',
      innerContent: 'Sorry! we were unable to mark the order as rejected',
      cancel: false,
    });
  });
}
