export default function supplierOrders (el, props) {
  const $el = $(el);
  bindEvents($el);
}

function bindEvents ($wrapper) {
  $wrapper.on('change', '.filter-by-order-days', function(e) { filterOrdersByDays(e, $wrapper) });
}

function filterOrdersByDays (event, $wrapper) {
  var $input = $(event.currentTarget);
  var $orderList = $input.parents('.order-list');
  window.location = `${$orderList.data('url')}&for_duration=${$input.val()}`;
}
