export default function supplierInvoices (el, props) {
  const $el = $(el)
  bindEvents($el);
}

function bindEvents ($wrapper) {
  $wrapper.on('keyup', '.search-invoices', function (e) { filterInvoices(e, $wrapper) });
}

function filterInvoices (event, $wrapper) {
  var $invoices = $wrapper.find('.supplier-invoice');
  $invoices.removeClass('hidden');

  var $searchInput = $(event.currentTarget);
  var query = $.trim($searchInput.val()).replace(/ +/g, ' ').toLowerCase();
  if (query != '' && query.length >= 3) {
    for(var i=0; i < $invoices.length; i++) {
      var $invoice = $($invoices[i]);
      var $searchables = $invoice.find('.searchable');
      var invoice_filter = '';
      for(var s=0; s < $searchables.length; s++) {
        var $searchable = $($searchables[s]);
        invoice_filter += $searchable.text();
        if ($searchable.data('searchable')) {
          invoice_filter += $searchable.data('searchable');
        }
      }
      if (invoice_filter.toLowerCase().indexOf(query) == -1) {
        $invoice.addClass('hidden');
      }
    }
  }
}
