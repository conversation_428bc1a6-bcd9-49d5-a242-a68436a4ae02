export default function supplierOrderSummary (el, props) {
  const $el = $(el);
  bindEvents($el);
}

function bindEvents ($wrapper) {
  $wrapper.on('click', '.summary-document-handle', function(e) { openSummaryDocumentModal(e) });
  $(document).on('change', '.summary-date', function(e) { summaryDateChanged(e) });
  $(document).on('click', '.generate-summary', function(e) { generateDocument(e) });
}

function openSummaryDocumentModal (event) {
  var $modal = $(document).find('#modal-summary');
  $modal.foundation('open');
}

function summaryDateChanged (event) {
  var $datePicker = $(event.currentTarget);
  var $modal = $datePicker.parents('#modal-summary');
  var $documentHandler = $modal.find('.document-handler');
  var $generateButton = $modal.find('.generate-summary');

  // hide any downloaded document
  $documentHandler.addClass('hidden');

  // reset document generate to not regenerate
  if ($generateButton.data('regenerate')) {
    $generateButton.data('regenerate', false);
    $generateButton.text('Generate');
  }  
}

function generateDocument (event) {
  var $button = $(event.currentTarget);
  var $modal = $button.parents('#modal-summary');
  var $documentHandler = $modal.find('.document-handler');
  var $errors = $modal.find('.summary-errors');

  var url = $button.data('url');
  var regenerate = !!$button.data('regenerate');
  var $datePicker = $modal.find('input.datepicker');
  var summaryDay = $datePicker.val();

  $button.attr('disabled', true);
  $button.text('Generating...');
  $documentHandler.addClass('hidden');
  $errors.hide();
  
  var save_request = $.ajax({
    url: url,
    type: 'GET',
    data: { summary_day: summaryDay, regenerate: regenerate },
    dataType: 'JSON',
  });

  save_request.fail(function(response) {
    var errorMessage = 'Sorry we could not generate the order summary! Please try again.';

    var jsonErrors = response.responseJSON;
    if (jsonErrors && jsonErrors.errors) {
      errorMessage = jsonErrors.errors.join('. ');
    }
    $errors.text(errorMessage);
    $errors.show();
    $button.text('Generate');
    $button.data('regenerate', false);
  });

  save_request.then(function (response) {
    var document = response
    $documentHandler.find('.document-link').attr('href', document.url);
    $documentHandler.find('.filename').text(document.name);
    $documentHandler.find('.version').text(`v${document.version}`);
    $documentHandler.removeClass('hidden');

    $button.data('regenerate', true);
    $button.text('Re-generate');
  })

  save_request.always(function () {
    $button.attr('disabled', false);
  })
}
