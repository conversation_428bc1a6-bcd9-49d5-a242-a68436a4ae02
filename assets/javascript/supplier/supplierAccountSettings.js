import axios from 'axios';

export default function supplierAccountSettings (el, props) {
  const $el = $(el);
  bindAccountPageEvents($el);
}

function bindAccountPageEvents($wrapper) {
  $wrapper.on('click', '.supplier-account-submit-btn', function(e) { submitSupplier(e, $wrapper) });
  $wrapper.on('click', '.new-supplier_agreement', function(e) { sendNewSupplierAgreement(e) });
  $wrapper.on('change', 'input[name="avatar_uploader"]', function(e) { uploadSupplierImage(e, $wrapper) });
};

function submitSupplier (event, $wrapper) {
  $wrapper.find('.update_supplier_profile').submit();
}

function sendNewSupplierAgreement (event) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  if ($link.attr('disabled')) {
    return;
  }
  $link.yordarPOP({
    container: '#yordarPopUp',
    title: 'Supplier Agreement - via DocuSign',
    innerContent: 'Are you sure, you want to send a new supplier agreement document to the Supplier?',
    submit: 'Confirm',
    submitHandler: function (event) {
      $link.attr('disabled', true);
      $link.text('Sending...');
      window.location = $link.attr('href');
    },
  });
}


function uploadSupplierImage (event, $wrapper) {
  var $input = $(event.currentTarget);
  var $button = $wrapper.find('.avatar-upload-button');
  var newFile = event.target.files[0];

  if (!newFile)
    return

  var uploadUrl = $input.data('upload-url');
  var uploadPreset = $input.data('upload-preset');

  const imageData = new FormData();
  imageData.append('file', newFile);
  imageData.append('upload_preset', uploadPreset);

  $button.text('Uploading...');
  $button.attr('disabled', true);

  let request = axios({
    method: 'POST',
    url: uploadUrl,
    data: imageData,
  })
  .then((response) => {
    var imageUrl = response.data.secure_url;
    if (imageUrl) {
      var filename = imageUrl.split('upload/')[1];
      var $avatarField = $wrapper.find(`input[name="${$input.data('target-name')}"]`);
      $avatarField.val(filename);

      // show image
      var $imageContainer = $wrapper.find('.supplier-avatar-container');
      var img = document.createElement('img');
      img.src = imageUrl;
      $imageContainer.html(img);
    } else {
      showUploadError($wrapper);
    }
    $button.text($button.data('button-text'));
    $button.attr('disabled', false);
  })
  .catch((error) => {
    showUploadError($wrapper);
    $button.text($button.data('button-text'));
    $button.attr('disabled', false);
  })
}

function showUploadError ($wrapper) {
  $wrapper.yordarPOP({
    container: '#yordarPopUp',
    title: 'Supplier Avatar',
    innerContent: 'We couldn\'t upload the image. Please try again!',
    submit: 'Ok',
    cancel: false,
  });
}
