import renderSpendChart from 'utilities/plugins/highcharts_spend_chart'

export default function supplierReports (el, props) {
  var $el = $(el)
  bindEvents($el);  
}

function bindEvents($wrapper) {
  $wrapper.on('change', '.datepicker.budget', function (e) { updateSpendChart($wrapper) });
  $wrapper.on('change', 'select[name=report_type]', function (e) { updateSpendChart($wrapper) });

  $wrapper.on('click', '.export-toggle', function(e) { toggleExport(e, $wrapper) });
  $wrapper.on('click', '.export-report-csv', function(e) { exportCsv(e, $wrapper) });

  $wrapper.find('select[name=report_type]').trigger('change');
}

function updateSpendChart ($wrapper) {
  var $spend_container = $('#order-spend-container');
  var $filterForm = $wrapper.find('form.filter-form');

  $spend_container.html(spinner_html);

  var report_request = $.ajax({
    url: $filterForm.prop('action'),
    type: 'GET',
    dataType: 'JSON',
    data: $filterForm.serialize(),
  });

  report_request.done(function(response){
    renderSpendChart(response.report_type, response.source_type, response.report_data, 'order-spend-container'); // defined in components/reports/chart.js
  });

  report_request.fail(function(){
    $spend_container.html('');
    $spend_container.yordarPOP({
      title: 'Report data error!',
      innerContent: 'Sorry we were unable to gather data for the filter parameters. Please try again!',
      cancel: 'close',
      submit: false,
    });
  });
}

function toggleExport (event, $wrapper) {
  var $toggleButtons = $('.export-toggle');
  var $exportPanel = $wrapper.find('.export-panel');
  $toggleButtons.toggleClass('hidden');
  $exportPanel.slideToggle();
}

function exportCsv (event, $wrapper) {
  event.preventDefault();
  var $button = $(event.currentTarget);
  var $filterForm = $wrapper.find('form.filter-form');
  var $exportForm = $wrapper.find('form.export-form');

  var exportLocation = `${$exportForm.prop('action')}?${$filterForm.serialize()}&${$exportForm.serialize()}`;
  window.location = exportLocation;
}

