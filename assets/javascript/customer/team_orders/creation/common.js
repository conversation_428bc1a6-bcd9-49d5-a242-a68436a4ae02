// datetime format should match Date::DATE_FORMATS[:date_spreadsheet] => '%Y-%m-%d'
export const PICKER_DATE_FORMAT = 'yy-mm-dd';

export function isPackageOrder ($form) {
  var $activeOrderType = $form.find('.team-order-type-selection button.active')
  return $activeOrderType.data('order-type') == 'package'
}

export function isRecurringOrder ($form) {
 var $activeOrderType = $form.find('.team-order-type-selection button.active')
 return $activeOrderType.data('order-type') == 'open'
}

export function isRecurringOrderExtension ($form) {
 return $form.hasClass('recurring-team-order-extend-form');
}

export function isOrderEdit ($form) {
  return $form.hasClass('team-order-edit-form')
}

export function toggleMultipleDaysLabel($form, daysSelected) {
  var $multipleDaysLabel = $('.multiple-days-label');
  var $tooltip = $('.whats-this__multiple');
  if (daysSelected.length > 1) {
    $tooltip.addClass('hidden');
    var dateCount = daysSelected.length;
    if (dateCount == 15) {
      dateCount += ' (max)';
    }
    $multipleDaysLabel.find('.date-count').text(dateCount);
    $multipleDaysLabel.removeClass('hidden');
  } else {
    $tooltip.removeClass('hidden');
    $multipleDaysLabel.addClass('hidden');
  }
}

// const WEEKDAYS = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday' , 'Friday', 'Saturday']
export function setupMultiDaySelector ($form) {
  var $datesList = $form.find('ul.multi-day-supplier-selector')

  var $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  var teamOrderDates = JSON.parse($teamOrderDates.val());

  $datesList.html('');
  teamOrderDates.sort().map(function(date, idx) {
    createDayListItem($form, $datesList, date);
  });
  $datesList.find('.supplier-day:first-of-type').trigger('click');

  if (isPackageOrder($form) || isRecurringOrder($form)) {
    $datesList.removeClass('hidden');
    $form.find('.multi-supplier-selection-header').removeClass('hidden');
  } else {
    $datesList.addClass('hidden');
    $form.find('.multi-supplier-selection-header').addClass('hidden')
  }
}

function createDayListItem ($form, $datesList, date) {
  var $teamOrderSuppliers = $form.find('input[name="order[delivery_suppliers]"]');
  var deliverySuppliers = JSON.parse($teamOrderSuppliers.val());
  var recurringTeamOrder = isRecurringOrder($form);
  const formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date));
  const intlDate = new Intl.DateTimeFormat("en-AU", {weekday: 'short', day: 'numeric', month: 'short', }).format(new Date(date)).replace(/,/g, '');

  // create tab
  var li = document.createElement('li')
  li.dataset.date = formattedDate

  var hasSupplierForDay = deliverySuppliers[formattedDate] && Object.keys(deliverySuppliers[formattedDate]).length
  li.className =`supplier-day${hasSupplierForDay ? ' selected-supplier' : ''}`

  li.appendChild(document.createTextNode(intlDate));

  if (!recurringTeamOrder) {
    var removeButton = document.createElement('a');
    removeButton.className = 'remove-date float-right';
    removeButton.href = 'javascript:void(0)';
    li.appendChild(removeButton);
  }

  $datesList.append(li);
}

export function refreshSupplierList($form) {
  setupMultiDaySelector($form); // setup in suppliers.js
  var data = supplierFilterData()
  if (!data.suburb_id) return;

  var $supplierSelection = $form.find('.team-order-panel.supplier-selection');
  var $supplierList = $supplierSelection.find('.team-suppliers-list')
  data.budget = $form.find('input[name="order[team_order_detail_attributes][budget]"]').val();
  data.wants_html = true;

  $supplierList.html('<li><h3 class="mt-2 text-center">Loading Suppliers...</h3></li>');

  var supplier_refresh_request = $.ajax({
    url: $supplierSelection.data('refresh-url'),
    type: 'GET',
    dataType: 'JSON',
    data: data,
  });

  supplier_refresh_request.done(function (response) {
    $supplierList.html(response.suppliers_html);
    checkSupplierAvailability($form);
  });

  supplier_refresh_request.fail(function(response) {
    $supplierList.html('<h3 class="mt-2 text-center">We were unable to retrieve suppliers for the selected criteria!</h3>')
  });
}

function supplierFilterData () {
  const DROPDOWN_FILTERS = ["category", "dietary", "delivery", "other", "team_suppliers"];
  var data = new Object();
  data.search_keywords = localStorage.getItem("search_keywords");
  data.suburb_id = localStorage.getItem("suburb_id");
  if (localStorage.getItem("supplier_filters") !== undefined) {
    var filters = JSON.parse(localStorage.getItem("supplier_filters"));
    if (filters !== null) {
      for (var i = 0; i < DROPDOWN_FILTERS.length; i++) {
        var filter = DROPDOWN_FILTERS[i];
        data[filter] = filters
          .filter(function (option) {
            return option.name == filter;
          })
          .map(function (option) {
            return option.value;
          });
      }
    }
  }
  return data
}

export function checkSupplierAvailability ($form) {
  // grey out suppliers if the cutoff time has passed
  var $supplierSelection = $form.find('.team-order-panel.supplier-selection');
  var $suppliers = $supplierSelection.find('.team-supplier');
  var supplier_ids = $suppliers.map(function (i, el) {
    return $(el).data('supplier-id');
  }).get();
  if (supplier_ids.length < 1) return;
  var delivery_date = $form.find('input.datepicker').val();

  // cannot check availability if date is not selected
  if (!delivery_date) {
    return;
  }

  if (isPackageOrder($form) || isRecurringOrder($form)) {
    var selected_delivery_date = delivery_date;
    var activeDate = $form.find('.supplier-day.active').data('date');
    var delivery_date = activeDate + ' ' + selected_delivery_date.split(' ')[1]; // add the delivery time from the datepicker
  }

  var suburb_id = $form.find('#suburb_id').val()

  var cutoff_hours_request = $.ajax({
    url: $supplierSelection.data('suppliers-availability-url'),
    dataType: 'JSON',
    type: 'GET',
    data: {
      supplier_ids: supplier_ids,
      delivery_date: delivery_date,
      suburb_id: suburb_id
    }
  });

  var unavailableNotice = 'The supplier is not available for selected date and/or needs more notice.';

  cutoff_hours_request.done(function (unavailableSupplierIds) {
    $suppliers.each((idx, el) => {
      var $supplier = $(el);
      if(unavailableSupplierIds.length && unavailableSupplierIds.includes($supplier.data('supplier-id'))) {
        $supplier.addClass('unavailable');
        $supplier.prop('title', unavailableNotice);
        $supplier.find('.choose-supplier-btn').prop('disabled', true);        
      } else {
        $supplier.removeClass('unavailable');
        $supplier.prop('title', '');
        $supplier.find('.choose-supplier-btn').prop('disabled', false);        
      }
    });
    // orderAvailableSuppliers($form);
    var $unavailableDisclaimer = $form.find('.unavailble-suppliers-disclaimer');
    if (unavailableSupplierIds.length) {
      $unavailableDisclaimer.removeClass('hidden')
    } else {
      $unavailableDisclaimer.addClass('hidden')
    }
  });
}

// splits un-available and available supplier
// commented out because it messes up the set supplier sorting
// function orderAvailableSuppliers ($form) {
//   var $teamSuppliersList = $form.find(".team-suppliers-list");

//   if ($form.find(".unavailable-notice").length) $form.find(".unavailable-notice").remove();

//   var $unavailableSuppliers = $form.find(".unavailable");
//   if ($unavailableSuppliers.length > 0) {
//     var notice = 'The following suppliers are not available and/or need more notice.'
//     var $notice = $(`<li class="team-order-list__row row unavailable-notice"><div class='small-12 columns'><h3>${notice}</h3></div></li>`);

//     $teamSuppliersList.append($notice);
//     $notice.after($unavailableSuppliers);
//   }
// }

export function updateSupplierSelection ($form, delivery_date, supplier_id, selected_menu_sections) {
  var formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(delivery_date))
  var $deliveryDaySelector = $form.find('.supplier-day[data-date="' + formattedDate + '"]')
  var $teamOrderSuppliers = $form.find('input[name="order[delivery_suppliers]"]');
  var deliverySuppliers = JSON.parse( $teamOrderSuppliers.val())
  deliverySuppliers[formattedDate] = new Object
  if (supplier_id) {
    if (selected_menu_sections) {
      deliverySuppliers[formattedDate][supplier_id] = selected_menu_sections;
    } else {
      deliverySuppliers[formattedDate][supplier_id] = new Array;
    }
    $deliveryDaySelector.addClass('selected-supplier');
  } else {
    delete deliverySuppliers[formattedDate][supplier_id]
    $deliveryDaySelector.removeClass('selected-supplier');
  }
  $teamOrderSuppliers.val(JSON.stringify(deliverySuppliers));
}

export function setInviteAllSwitch($wrapper) {
  var $check = $wrapper.find(".invite-all-attendees");
  var $attendees = $wrapper.find(".team-contacts .team-contact:visible");
  var $invitedAttendees = $attendees.find(".uninvite-btn");
  if ($attendees.length > 0 && $invitedAttendees.length == $attendees.length) {
    $check.prop("checked", true);
  } else {
    $check.prop("checked", false);
  }
}

