const MODAL_SELECTOR = '#modal-payment-details'

export default function teamOrderPaymentSlideout (el, props) {
  const $el = $(el);
  const $modal = $(document).find(MODAL_SELECTOR);
  bindEvents($el, $modal);
}

function bindEvents ($wrapper, $modal) {
  $wrapper.on('click', 'a[data-modal-view]', function (e) { modalView(e, $modal) });
  $modal.on('click', '.update-payment', function(e) { updatePayment(e, $modal) });
}

function modalView(event, $modal) {
  event.preventDefault();
  $modal.foundation('open');
}

function updatePayment (event, $modal) {
  var $submitButton = $(event.currentTarget);
  var $newCardForm = $modal.find('form.new-credit-card');

  if ($newCardForm.is(':visible') && !$newCardForm.data('submitting')) {
    $('#yordarPopUp').data('multiple-opened', 'true');
    $submitButton.html(spinner_html);
    $newCardForm.submit();
  } else {
    var $modal = $(event.currentTarget).parents(MODAL_SELECTOR);
    $modal.foundation('close');
  }
}
