import { PICKER_DATE_FORMAT, isPackageOrder, isRecurringOrder, isOrderEdit, checkSupplierAvailability, toggleMultipleDaysLabel, updateSupplierSelection } from './common.js'

export default function teamOrderSupplierForm (el, props) {
  const $el = $(el);
  const $form = $el.parents('form.team-order-form');
  bindEvents($el, $form);
}

function bindEvents ($wrapper, $form) {
  $wrapper.on('click', '.supplier-day .remove-date', function(e) { removeDeliveryDay(e, $form) });
  $(document).on('click', '.choose-supplier-btn', function (e) { supplierSelection(e, $wrapper, $form) });
  $wrapper.on('click', '.add-fav-supp, .remove-fav-supp', function (e) { saveTeamFavourite(e) });
}

function removeDeliveryDay(event, $form) {
  event.preventDefault();
  event.stopPropagation();
  var $deliveryDay = $(event.currentTarget).parents('.supplier-day');
  var delivery_date = $deliveryDay.data('date');

  var $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  var teamOrderDates = JSON.parse($teamOrderDates.val());
  var formattedDates = teamOrderDates.map(function(date, idx) { return $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date)) });
  var index = jQuery.inArray(delivery_date, formattedDates);

  if (teamOrderDates.length > 1 && index != -1) {
    teamOrderDates.splice(index, 1);
    $teamOrderDates.val(JSON.stringify(teamOrderDates));
    toggleMultipleDaysLabel($form, teamOrderDates);
    if ($deliveryDay.hasClass('active')) {
      $deliveryDay.prev().hasClass('supplier-day') ? $deliveryDay.prev().click() : $deliveryDay.next().click();
    }
    $deliveryDay.remove();
  }
}

function supplierSelection (event, $wrapper, $form) {
  var $button = $(event.currentTarget);
  var isAlreadySelected = $button.hasClass('selected-supplier');
  var delivery_date = $form.find('input.datepicker').val();

  if (isAlreadySelected) {
    unselectSupplier(event, $wrapper, $form);
  } else if (!delivery_date) {
    var display_msg = 'Delivery date has to be entered.';
    $(this).yordarPOP({
      title: 'New team order',
      innerContent: display_msg,
      cancel: false
    });
  } else if ($button.data('menu-selected')) { // select supplier if selected from menu form
    $button.data('menu-selected', null);
    selectSupplier(event, $wrapper, $form);
  } else if (isPackageOrder($form) || isRecurringOrder($form) || isOrderEdit($form)) {
    $button.parents('.team-supplier').find('.supplier-menu').click();
  } else {
    selectSupplier(event, $wrapper, $form);
  }
}

function selectSupplier (event, $wrapper, $form) {
  var $button = $(event.currentTarget);

  var supplierStore = localStorage.getItem('deliverySuppliers') || '{}';
  var deliverySuppliers = JSON.parse(supplierStore);

  if (isPackageOrder($form) || isRecurringOrder($form)) {
    var $activeDeliveryDay = $wrapper.find('.supplier-day.active');
    var delivery_date = $activeDeliveryDay.data('date');
  } else {
    var delivery_date = $form.find('input.datepicker').val();
  }
  var delivery_date_time = $form.find('input.datepicker').val();
  var activeDate = $wrapper.find('.supplier-day.active').data('date');

  if (isPackageOrder($form) || isRecurringOrder($form)) {
    var selected_delivery_date = delivery_date_time;
    var delivery_date_time = `${activeDate} ${selected_delivery_date.split(' ')[1]}`; // add the delivery time from the datepicker
  }

  var suburb_id = $form.find('input[name=suburb_id]').val();

  var $supplier = $button.parents('.team-supplier');
  var supplier_id = $supplier.data('supplier-id');

  $wrapper.find('.team-supplier .choose-supplier-btn').removeClass('selected-supplier');

  var $supplierSelection = $form.find('.supplier-selection');

  var cutoff_hours_request = $.ajax({
    url: $supplierSelection.data('supplier-cutoff-hours-remaining-url'),
    dataType: 'JSON',
    type: 'GET',
    data: { supplier_id: supplier_id, delivery_date: delivery_date_time, suburb_id: suburb_id }
  })
  cutoff_hours_request.done(function (response) {
    var display_msg, title;
    if (response.hours_remaining > 0) {
      title = `You've selected ${response.supplier_name}`;
      display_msg = `You have ${response.hours_remaining_message} to get the team order in, continue OR choose another supplier.`;

      $supplier.find('.choose-supplier-btn').addClass('selected-supplier');
      updateSupplierSelection($form, activeDate, supplier_id, $button.data('selected-menu-sections'));
      $button.data('selected-menu-sections', '');
    } else {
      title = 'Supplier Selection';
      display_msg = 'Team order lead time has passed for the supplier, choose another supplier.';
    }

    $(this).yordarPOP({
      title: title,
      innerContent: display_msg,
      cancel: false
    });
  });

  // Cannot select delivery postcode if a supplier is selected.
  $form.find('#postcode').prop('disabled', true);
}

function unselectSupplier (event, $wrapper, $form) {
  var $button = $(event.currentTarget);
  $wrapper.find('.team-supplier .choose-supplier-btn').removeClass('selected-supplier');

  var delivery_date = $wrapper.find('.supplier-day.active').data('date');

  updateSupplierSelection($form, delivery_date, '');

  $form.find('#postcode').prop('disabled', false);
}

function isDateThisWeek(date) {
  const convertedDate = new Date(date);

  const today = new Date();
  const todayDate = today.getDate();
  const todayDay = today.getDay();

  // get first date of week
  const firstDayOfWeek = new Date(today.setDate(todayDate - todayDay));

  // get last date of week
  const lastDayOfWeek = new Date(firstDayOfWeek);
  lastDayOfWeek.setDate(lastDayOfWeek.getDate() + 6);

  // if date is equal or within the first and last dates of the week
  return convertedDate >= firstDayOfWeek && convertedDate <= lastDayOfWeek;
}

function isDateNextWeek(date) {
  const convertedDate = new Date(date);

  const nextWeek = new Date();
  nextWeek.setDate(nextWeek.getDate() + 7);

  const nextWeekDate = nextWeek.getDate();
  const nextWeekDay = nextWeek.getDay();

  // get first date of next week
  const firstDayOfNextWeek = new Date(nextWeek.setDate(nextWeekDate - nextWeekDay));

  // get last date of next week
  const lastDayOfNextWeek = new Date(firstDayOfNextWeek);
  lastDayOfNextWeek.setDate(lastDayOfNextWeek.getDate() + 6);

  return convertedDate >= firstDayOfNextWeek && convertedDate <= lastDayOfNextWeek;
}

function saveTeamFavourite(event) {
  var $favButton = $(event.currentTarget);
  var addFav = $favButton.hasClass('add-fav-supp');

  var favouriteRequest = $.ajax({
    type: addFav ? 'PUT' : 'DELETE',
    dataType: 'JSON',
    url: $favButton.data('url'),
  });
  
  favouriteRequest.done(function () {
    $favButton.toggleClass('add-fav-supp remove-fav-supp');
  });
  
  favouriteRequest.fail(function () {
      $favButton.yordarPOP({
        title: 'Favouriting Failed',
        innerContent: 'Sorry, something has gone wrong and your favouriting did not work',
        cancel: false
      });
  });
}
