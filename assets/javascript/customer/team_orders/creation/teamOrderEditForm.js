// Team Order Edit page JS
// only used to trigger supplier change function

export default function teamOrderEditForm (el, props) {
  const $el = $(el)
  const $form = $el.find('.team-order-form');
  bindEvents($el, $form);
}

function bindEvents ($wrapper, $form) {
  $(document).on('click', '.change-supplier', function (e) { showSupplierList(e, $form) });
}

function showSupplierList(event, $form) {
  var $link = $(event.currentTarget);
  var $supplier = $link.parents('.team-supplier');
  var $modal = $(document).find('#modal-supplier-list');
  var $modalList = $modal.find('.team-suppliers-list')

  $modalList.html('<li class="lds-ring-container"><div class="lds-ring"><div></div><div></div><div></div><div></div></div></li>');

  var data = {}
  data.suburb_id = $form.find('input[name="order[delivery_suburb_id]"]').val()  
  if (!data.suburb_id) return;
  data.budget = $form.find('input[name="order[team_order_detail_attributes][budget]"]').val();
  data.wants_html = true;
  data.for_modal = true

  var supplier_refresh_request = $.ajax({
    url: $link.data('url'),
    type: 'GET',
    dataType: 'JSON',
    data: data,
  });

  supplier_refresh_request.done(function (response) {
    $modalList.html(response.suppliers_html);
  });

  supplier_refresh_request.fail(function(response) {
    $modalList.html('<li><h3 class="mt-2 text-center">We were unable to retrieve suppliers for the selected criteria!</h3></li>')
  });
}
