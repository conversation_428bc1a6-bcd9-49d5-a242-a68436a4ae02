import { isPackageOrder, isRecurringOrder, isOrderEdit, updateSupplierSelection } from './common.js'

export default function teamOrderSupplierMenu (el, props) {
  const $el = $(el);
  const $form = $el.parents('form.team-order-form');
  bindEvents($el, $form);
}

function bindEvents ($wrapper, $form) {
  $(document).on('click', '.supplier-menu', function (e) { showSupplierMenu(e, $form) });
  $(document).on('click', '#modal-supplier-menu .preview-menu', function (e) { previewMenu(e) });
  $(document).on('click', '#modal-supplier-menu .select-supplier', function(e) { selectMenuSupplier(e, $wrapper, $form) });
  $(document).on('change', '#modal-supplier-menu .delivery-day', function (e) { handleActiveDeliveryDay(e) });
}

function showSupplierMenu(event, $form) {
  var $link = $(event.currentTarget);
  var $supplier = $link.parents('.team-supplier');
  var $menuModal = $(document).find('#modal-supplier-menu');
  var packageTeamOrder = isPackageOrder($form) || isRecurringOrder($form);

  var $menuContainer = $menuModal.find('.team-supplier-menu');
  var supplierMenuLoaded = $menuContainer.length > 0 && $menuContainer.data('supplier-id') === $supplier.data('supplier-id') && !packageTeamOrder;
  if (supplierMenuLoaded) return;

  var budget = $form.find('input[name="order[team_order_detail_attributes][budget]"]').val();
  var menuData = {
    wants_html: true,
    budget: budget
  };

  menuData.suburb_id = localStorage.getItem('suburb_id');

  if (packageTeamOrder) {
    menuData['delivery_dates'] = $form.find('input[name="order[delivery_dates]"]').val();
    menuData['current_date'] = $form.find('.supplier-day.active').data('date');
    menuData['delivery_suppliers'] = $form.find('input[name="order[delivery_suppliers]"]').val();
  }

  $menuModal.html('<div class="lds-ring-container"><div class="lds-ring"><div></div><div></div><div></div><div></div></div></div>');
  var menu_request = $.ajax({
    url: $link.data('url'),
    type: 'GET',
    dataType: 'JSON',
    data: menuData,
  });

  menu_request.done(function (response) {
    var $supplierMenu = $(response.html);
    $menuModal.html($supplierMenu);
    collapsibleSection('team-menu-section__heading', 'menu-open');
    collapsibleSection('serving-options', 'options-open', true);
  });

  menu_request.fail(function (err) {
    alert('failed');
  });
}

function previewMenu(event) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  var $menuModal = $link.parents('#modal-supplier-menu');

  var $menuSections = $menuModal.find('.menu-section-selection');
  var $selectedMenuSections = $menuModal.find('.menu-section-selection:checked');
  var menuSectionModified = $menuSections.length > 0 && $menuSections.length !== $selectedMenuSections.length;
  var previewLocation = $link.prop('href');
  if (menuSectionModified) {
    previewLocation += previewLocation.indexOf('?') >= 0 ? '&' : '?';
    previewLocation += $selectedMenuSections.map(function (idx, el) { return `selected_menu_sections[]=${$(el).val()}` }).get().join('&');
  }
  window.open(previewLocation);
}

function selectMenuSupplier (event, $wrapper, $form) {
  var $button = $(event.currentTarget);
  var $menuModal = $button.parents('#modal-supplier-menu');
  var supplierId = $menuModal.find('.team-supplier-menu').data('supplier-id');

  var $supplierSelectionButtons = $form.find('.team-supplier .choose-supplier-btn');
  var $supplierSelectionButton = $supplierSelectionButtons.first();
  
  if (isOrderEdit($form)) {
    var $teamSupplier = $supplierSelectionButton.parents('.team-supplier');
    if ($teamSupplier.data('supplier-id') != supplierId) {
      var newSupplierName = $menuModal.find('.team-supplier-banner__title .supplier-name').text().replace(/\n/g,'');
      $form.find('.supplier-name').text(newSupplierName);
      $teamSupplier.data('supplier-id', supplierId);
    }
  } else {
    $supplierSelectionButton = $form.find(`.team-supplier[data-supplier-id=${supplierId}]`).find('.choose-supplier-btn'); 
  }

  $supplierSelectionButton.data('selected-menu-sections', null);

  var $menuSections = $menuModal.find('.menu-section-selection');
  var $selectedMenuSections = $menuModal.find('.menu-section-selection:checked');
  var menuSectionModified = $menuSections.length > 0 && $menuSections.length !== $selectedMenuSections.length;
  var selectedMenuSections;

  if (menuSectionModified) {
    selectedMenuSections = $selectedMenuSections.map(function(idx, el) { return $(el).val() }).toArray();
    $supplierSelectionButton.data('selected-menu-sections', selectedMenuSections);
  }

  var $deliveryDates = $menuModal.find('input.delivery-day:checked:not(:disabled)');

  if ($deliveryDates.length) {
    $deliveryDates.each(function(idx, el) {
      var delivery_date = $(el).val();
      updateSupplierSelection($form, delivery_date, supplierId, selectedMenuSections);
    })
  }

  $supplierSelectionButton.removeClass('selected-supplier'); // mark supplier as unselected
  $supplierSelectionButton.data('menu-selected', true); // tag supplier as being selected from menu
  $supplierSelectionButton.click(); // trigger supplier selection
}

function collapsibleSection(target, chevronClass, nested) {
  var collapsible = document.getElementsByClassName(target);
  for (var i = 0; i < collapsible.length; i++) {
    collapsible[i].addEventListener('click', function () {
      this.classList.toggle(chevronClass)
      var content = this.nextElementSibling;
      if (content.style.maxHeight) {
        content.style.maxHeight = null;
      } else {
        content.style.maxHeight = `${content.scrollHeight}px`;
        // We need to increase max height of collapsing parent as well
        if (nested) {
          var currentMenuSection = content.closest('.team-menu-section__items');
          currentMenuSection.style.maxHeight = parseInt(currentMenuSection.style.maxHeight) + content.scrollHeight
        }
      }
    });
  }
}

function handleActiveDeliveryDay (event) {
  $(event.currentTarget).parents('.menu-date-selector').toggleClass('active');
}
