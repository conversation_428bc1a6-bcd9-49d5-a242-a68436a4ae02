import { submitStripeCard } from 'javascript/stripe/handleStripeForm';

export default function teamOrderPaymentForm(el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents($wrapper, props) {
  $wrapper.on('click', '.payment-options__toggle', function (e) {
    changePaymentPanel(e, $wrapper);
  });

  $wrapper.on('change', 'select#credit-card-select', function (e) {
    selectCard(e, $wrapper);
    changeSelectCardIcon($wrapper);
  });
  $wrapper.on('change', 'select[name=invoice_order_individually]', function (e) {
    setInvoiceOverride(e);
  });

  $wrapper.on('click', '.add-new-credit-card-btn', function (e) {
    toggleNewCardForm(e, $wrapper);
  });
  $wrapper.on('submit', 'form.new-credit-card', function (e) {
    submitNewCreditCard(e, $wrapper);
  });
}

function changePaymentPanel(event, $wrapper) {
  const toggle = event.currentTarget;
  const selectedPaymentType = toggle.dataset.type;
  $wrapper.find('.payment-options__toggle').removeClass('active');
  toggle.classList.add('active');
  $wrapper.find('.payment-options__panel').addClass('hidden');
  $wrapper.find(`.payment-options__panel--${selectedPaymentType}`).removeClass('hidden');

  const $orderCard = $(document).find('input[name="order[credit_card_id]"]');
  const $cardSelector = $wrapper.find('select#credit-card-select');

  if (selectedPaymentType == 'invoice') {
    $orderCard.val(1);
    $wrapper.find('select[name=invoice_order_individually]').trigger('change');
  } else {
    $orderCard.val('');
    if (!$cardSelector.hasClass('hidden')) {
      $cardSelector.trigger('change');
    }
  }
}

function selectCard(event, $wrapper) {
  const $cardSelector = $(event.currentTarget);
  const $orderCard = $(document).find('input[name="order[credit_card_id]"]');
  $orderCard.val($cardSelector.val());
  const $selectedCard = $cardSelector.find('option:selected');
  const card_type = $selectedCard.text().split(' (')[0].trim();
  const card_charge = $selectedCard.data('charge');
  displaySelectedCardChargeNote($wrapper, $cardSelector);
}

function displaySelectedCardChargeNote($wrapper, $cardSelector) {
  const $chargeNote = $wrapper.find('#credit-card-charge-note');
  if ($cardSelector.val() == 1) {
    // pay on account
    $chargeNote.addClass('hidden');
  } else {
    const $selectedCard = $cardSelector.find('option:selected');
    const card_type = $selectedCard.text().split(' (')[0].trim();
    const card_charge = $selectedCard.data('percent');
    const card_fee = $selectedCard.data('fee');
    $chargeNote.find('.card-type').text(card_type);
    $chargeNote.find('.card-percent').text(`${card_charge}%`);
    if (card_fee && $chargeNote.find('.card-fee').length > 0) {
      $chargeNote.find('.card-fee').text(`+ ${parseInt(parseFloat(card_fee) * 100)}c`);
    }
    if ($selectedCard.data('is-old')) {
      $chargeNote.find('.card-deprecation').removeClass('hidden');
    } else {
      $chargeNote.find('.card-deprecation').addClass('hidden');
    }
    $chargeNote.removeClass('hidden');
  }
}

function changeSelectCardIcon($wrapper) {
  const $select = $('select#credit-card-select');
  const brand = $select.find('option:selected').data('brand');
  $select.removeClass('amex visa mastercard diners_club');
  $select.addClass(brand);
}

function setInvoiceOverride(event) {
  const $orderOverride = $(document).find('input[name="order[invoice_individually]"]');
  if ($orderOverride.length) {
    const isOverridden = $(event.currentTarget).val();
    $orderOverride.val(isOverridden);
  }
}

function toggleNewCardForm(event, $wrapper) {
  // update card selection
  const $cardSelect = $wrapper.find('#credit-card-select');
  $cardSelect.prop('disabled', !$('#credit-card-select').prop('disabled')).toggleClass('hidden');

  const $title = $wrapper.find('.payment-options__panel--card-heading h5');

  const $button = $(event.currentTarget);
  if ($cardSelect.hasClass('hidden')) {
    $wrapper.find('.new-credit-card-container').slideToggle(400);
    $title.text('Add Credit Card');
    $button.text('Saved Cards');
    // hide all notes / overrides
    $wrapper.find('#credit-card-charge-note').addClass('hidden');
  } else {
    $wrapper.find('.new-credit-card-container').hide();
    $title.text('Select Credit Card');
    $button.text('Add New Card');
  }
}

function submitNewCreditCard(event, $wrapper) {
  event.preventDefault();
  const $form = $(event.currentTarget);
  const $placeOrderButton = $(document).find('.place-order-btn');
  if ($placeOrderButton.length) {
    $placeOrderButton.html(spinner_html);
  }

  submitStripeCard($form, $wrapper, selectStripeCardAndSubmit);
}

function selectStripeCardAndSubmit(creditCard, $form, $wrapper) {
  // select new card
  const $creditCardSelector = $wrapper.find('select#credit-card-select');

  const $cardOption = $('<option>');
  $cardOption.val(creditCard.id);
  $cardOption.text(creditCard.label);
  $cardOption.data('percent', creditCard.surcharge_percent);
  $cardOption.data('fee', creditCard.surcharge_fee);
  $creditCardSelector.append($cardOption);
  $creditCardSelector.val(creditCard.id).trigger('change');

  // close cc form
  const $addCardButton = $wrapper.find('.add-new-credit-card-btn');
  if ($addCardButton.length) {
    $addCardButton.click();
  }

  // submit form
  const $orderDetailForm = $(document).find('form.order-details-form');
  const $placeOrderButton = $(document).find('.place-order-btn');

  if ($orderDetailForm.length) {
    $orderDetailForm.submit();
  } else if ($placeOrderButton.length) {
    $placeOrderButton.html(spinner_html);
    $placeOrderButton.click();
  }
}
