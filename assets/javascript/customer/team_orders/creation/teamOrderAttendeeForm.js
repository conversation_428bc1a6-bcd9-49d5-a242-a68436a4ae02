import { setInviteAllSwitch } from './common';

export default function teamOrderAttendeeForm (el, props) {
  const $el = $(el);
  bindEvents($el);
  setInviteAllSwitch($el);
}

function bindEvents ($wrapper) {
  $wrapper.on('click', '.invite-btn', function (e) { inviteContact(e, $wrapper) });
  $wrapper.on('click', '.uninvite-btn', function (e) { removeContact(e, $wrapper) });
  $wrapper.on('change', '.invite-all-attendees', function (e) { toggleInviteAll(e, $wrapper) });
}

function inviteContact(event, $wrapper) {
  var $button = $(event.currentTarget);
  var $eventAttendee = $button.parents('.team-contact')
  $eventAttendee.find('input[name="order[attendee_ids][]"]').val($eventAttendee.data('attendee-id'));
  $button.addClass('uninvite-btn').removeClass('invite-btn')
  updateAttendeeCount($wrapper);
  setInviteAllSwitch($wrapper);
}

function removeContact(event, $wrapper) {
  var $button = $(event.currentTarget);
  var $eventAttendee = $button.parents('.team-contact')
  $eventAttendee.find('input[name="order[attendee_ids][]"]').val('');
  $button.addClass('invite-btn').removeClass('uninvite-btn')
  updateAttendeeCount($wrapper);
  setInviteAllSwitch($wrapper);
}

function toggleInviteAll(event, $wrapper) {
  var $input = $(event.currentTarget);
  if ($input.is(':checked')) {
    $wrapper.find('.team-contacts .invite-btn:visible').click();
  } else {
    $wrapper.find('.team-contacts .uninvite-btn:visible').click();
  }
}

function updateAttendeeCount($wrapper) {
  var $countEl = $wrapper.find('.invited-attendee-count');
  var attendeeCount = $('.team-contacts .uninvite-btn').length;
  $countEl.text(attendeeCount);
}
