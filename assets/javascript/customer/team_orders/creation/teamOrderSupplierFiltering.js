import { refreshSupplierList } from './common.js'

var previousSearch;
var keywordTimer = 0;
var filterTimer = 0;


export default function teamOrderSupplierFiltering (el, props) {
  const $el = $(el);
  const $form = $el.parents('form.team-order-form');
  bindEvents($el, $form);
  setDefaultFilters ();
}

function setDefaultFilters () {
  localStorage.removeItem('search_keywords');
  localStorage.removeItem('suburb');
  localStorage.removeItem('supplier_filters');
}

function bindEvents ($wrapper, $form) {
  $wrapper.on('click', '.dropdown-filter-button', function (e) { showHideFilterDropdowns(e) });
  $(document).on('click', '.overlay', function (e) { hideFilterDropdowns(e) });

  $form.on('change', '#suburb_id', function (e) { filterBySuburb(e, $form) });

  $wrapper.on('keyup', '.keyword-search-filter', function (e) {
    if (e.which == 13 || e.keyCode == 13) {
      filterByKeywords(e, $form);
    }
  });

  $wrapper.on('change',
    'input[name="category[]"], input[name="dietary[]"], input[name="delivery[]"], input[name="other[]"], input[name="team_suppliers"]',
    function (e) { filterBySelectedOptions(e, $form) });
}

function showHideFilterDropdowns (event) {
  event.preventDefault();
  var clickedButton = event.currentTarget;
  var currentFilterContent = clickedButton.parentNode.querySelector(
    '.filter-content'
  );
  var filterContents = document.querySelectorAll(
    '.dropdown-bubble .filter-content'
  );
  var overlay = document.querySelector('.overlay');
  var showOverlay = false;
  for (var i = 0; i < filterContents.length; i++) {
    var filterContent = filterContents[i];
    if (filterContent == currentFilterContent) {
      $(filterContent).toggleClass('hidden');
      $(clickedButton).toggleClass('spin');
    } else {
      $(filterContent).addClass('hidden');
    }
    if (!$(filterContent).hasClass('hidden')) {
      showOverlay = true;
    }
  }
  // show Hide Overlay
  if (showOverlay) {
    $(overlay)
      .removeClass('hidden')
      .addClass('transparent');
  } else {
    $(overlay)
      .addClass('hidden')
      .removeClass('transparent');
  }
}

function hideFilterDropdowns() {
  var $overlay = $('.overlay');
  var $filterContent = $('.dropdown-bubble .filter-content');
  $filterContent.addClass('hidden');
  $filterContent.siblings('.dropdown-filter-button').removeClass('spin');
  $overlay.addClass('hidden').removeClass('transparent');
}

function filterBySuburb(event, $form) {
  var $input = $(event.currentTarget);
  var suburbId = $input.val();
  localStorage.setItem('suburb_id', suburbId);
  refreshSupplierList($form);
}

function filterByKeywords(event, $form) {
  var $input = $(event.currentTarget);
  var search_words = $input.val();
  var differentSearch = previousSearch !== search_words;
  var validQueryLength = search_words.length == 0 || search_words.length >= 3;
  if (differentSearch && validQueryLength) {
    previousSearch = search_words;
    localStorage.setItem('search_keywords', search_words);
    clearTimeout(keywordTimer);
    keywordTimer = setTimeout(function () {
      refreshSupplierList($form);
    }, 400);
  }
}

function filterBySelectedOptions(event, $form) {
  var $el = $(event.currentTarget);
  var inputName = $el.attr('name').replace('[]', '');
  var selectedValue = $el.val();
  var selectedLabel = $el.data('label'); //parents('label').text();
  var addRemove = $el.is(':checked') ? 'add' : 'remove';
  setLocalFilters(
    { name: inputName, value: selectedValue, label: selectedLabel, is_fixed: $el.attr('disabled') },
    addRemove
  );
  if ($el.data('do-not-search') !== undefined && $el.data('do-not-search')) {
    $el.data('do-not-search', false);
  } else {
    clearTimeout(filterTimer);
    filterTimer = setTimeout(function () {
      refreshSupplierList($form);
    }, 400);
  }
}

function setLocalFilters(filter, addRemove) {
  var currentStore = [];
  if (localStorage.getItem('supplier_filters') !== null) {
    currentStore = JSON.parse(localStorage.getItem('supplier_filters'));
  }
  if (addRemove == 'add') {
    currentStore.push(filter);
  } else {
    currentStore = currentStore.filter(function (filterOption) {
      return !(
        filterOption.name == filter.name && filterOption.value == filter.value
      );
    });
  }
  localStorage.setItem('supplier_filters', JSON.stringify(currentStore));
}
