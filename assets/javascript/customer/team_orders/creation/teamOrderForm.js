require('jquery-ui/ui/widgets/datepicker');
require('jquery-ui-timepicker-addon/dist/jquery-ui-timepicker-addon');

import { PICKER_DATE_FORMAT, isPackageOrder, isOrderEdit, isRecurringOrder, isRecurringOrderExtension, toggleMultipleDaysLabel, setupMultiDaySelector, refreshSupplierList, checkSupplierAvailability } from './common.js'
import select2 from 'select2';

const MULTI_DAY_LIMIT = 15;

const STEPS = {
  'step-1': 'order-details',
  'step-2': 'attendees-selection',
  'step-3': 'supplier-selection',
  'step-4': 'payment-details',
}

export default function teamOrderForm (el, props) {
  const $el = $(el);
  const $form = $el.find('.team-order-form');
  bindEvents($el, $form);
  setupDeliveryDatePicker($form);
  setupPOSelect($form);
  setupLevelSelect($form);
  autoProceedForOrderExtension($form);
}

function bindEvents ($wrapper, $form) {
  $wrapper.on('click', '.team-order-step-btn', function (e) { changePanel(e, $wrapper, $form) });
  $form.on('click', '.team-order-type-selection button', function (e) { changeTeamOrderType(e, $form) });
  $form.on('click', '.team-order-levels-handle', function (e) { toggleAttendeeLevels(e, $form) });
  $wrapper.on('click', '.place-order-btn', function (e) { submitTeamOrder(e, $wrapper, $form) });
  $wrapper.on('click', '.extend-order-btn', function (e) { extendTeamOrder(e, $wrapper, $form) });
  $(document).on('click', '#package-update-modal .place-order-btn', function (e) { submitTeamOrder(e, $wrapper, $form) });

  $wrapper.on('click', '.supplier-day', function(e) { chooseDeliveryDay(e, $wrapper, $form) });
}

function chooseDeliveryDay (event, $wrapper, $form) {
  var $deliveryDay = $(event.currentTarget);
  var $deliveryDays = $wrapper.find('.supplier-day');
  $deliveryDays.removeClass('active');
  $deliveryDay.addClass('active');

  checkSupplierAvailability($form);

  $wrapper.find('.team-supplier .choose-supplier-btn').removeClass('selected-supplier');
  var delivery_date = $deliveryDay.data('date');

  // mark already selected supplier for date as selected
  var deliverySuppliers = JSON.parse($form.find('input[name="order[delivery_suppliers]"]').val());
  var hasSupplierForDay = deliverySuppliers[delivery_date] && Object.keys(deliverySuppliers[delivery_date]).length !== 0
  if (hasSupplierForDay) {
    var supplier_id = Object.keys(deliverySuppliers[delivery_date])[0]
    var $selectedSupplier = $(`.team-supplier[data-supplier-id=${supplier_id}]`);
    if ($selectedSupplier.length) {
      $selectedSupplier.find('.choose-supplier-btn').addClass('selected-supplier')
    }
  }
}

function setupLevelSelect($form) {
  let $levelSelect = $form.find('select[name="order[team_order_detail_attributes][levels][names][]"]')
  $levelSelect.select2({
    tags: true,
    tokenSeparators: [','],
    dropdownParent: $form,
    multiple: true,
  });
}

function setupPOSelect($form) {
  $form.find('select#order_cpo_id').select2({
    tags: true,
    tokenSeparators: [',', ' '],
    dropdownParent: $form,
  });
}

function autoProceedForOrderExtension ($form) {
  if (isRecurringOrderExtension($form)) {
    $form.find('.order-details .team-order-step-btn').click(); // go directly to supplier selection
  }
}

function changeDatepickerInfo($form) {
  if(isPackageOrder($form) || isRecurringOrder($form)) {
    showMultipleDatesTooltip(true);
    changeDatepickerPlaceholder('multiple');
  }
  else {
    showMultipleDatesTooltip(false);
    changeDatepickerPlaceholder();
    $('.multiple-days-label').addClass('hidden');
  }
}

function changeDatepickerPlaceholder(type) {
  var $datepicker = $('.datepicker');
  if(type === 'multiple') {
    return $datepicker.attr('placeholder', 'Select Multiple Dates');
  }
  $datepicker.attr('placeholder', 'Select a Date and Time');
}

function showMultipleDatesTooltip(show) {
  var $tooltip = $('.whats-this__multiple');
  if(show) {
    return $tooltip.removeClass('hidden');
  }
  $tooltip.addClass('hidden');
}

function setupDeliveryDatePicker($form) {
  var min_date_time = new Date(new Date().setMinutes(15 * (Math.ceil((new Date()).getMinutes() / 15))));
  var $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');

  // https://stackoverflow.com/questions/1452066/jquery-ui-datepicker-multiple-date-selections
  function addOrRemoveDate(date, dateTimePicker) {
    var formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date));
    var dateWithTime = `${formattedDate} ${date.split(' ')[1]}`;

    var teamOrderDates = JSON.parse($teamOrderDates.val());
    var formattedDates = teamOrderDates.map(function(date, idx) { return $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date)) });
    var index = jQuery.inArray(formattedDate, formattedDates);

    if (isPackageOrder($form)) {
      if (index >= 0 && date == teamOrderDates[index]) {
        teamOrderDates.splice(index, 1);
        if (teamOrderDates.length > 0) {
          var newValue = teamOrderDates[teamOrderDates.length-1];
        } else {
          var newValue = '';
        }
        dateTimePicker = $(dateTimePicker)[0]
        setTimeout(function() { $(dateTimePicker.input).val(newValue)  }, 200); // delay to override value set by datepicker
      } else if (index >= 0) {
        // time update
        teamOrderDates.splice(index, 1);
        teamOrderDates.push(dateWithTime);
      } else if (teamOrderDates.length < MULTI_DAY_LIMIT) {
        teamOrderDates.push(dateWithTime);
      }
      toggleMultipleDaysLabel($form, teamOrderDates);
    } else {
      if (index == -1) {
        teamOrderDates.splice(index, 1);
        teamOrderDates.push(dateWithTime);
      }
    }
    $teamOrderDates.val(JSON.stringify(teamOrderDates));
  }

  $('input.datepicker').datetimepicker({
    monthNames: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    controlType: 'select',
    dateFormat: PICKER_DATE_FORMAT,
    timeFormat: 'HH:mm',
    stepMinute: 15,
    minDateTime: min_date_time,
    onSelect: function (dateText, inst) {
      addOrRemoveDate(dateText, inst);
    },
    beforeShowDay: function (date) {
      var teamOrderDates = JSON.parse($teamOrderDates.val());
      var formattedDates = teamOrderDates.map(function(date, idx) { return $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date)) })
      var formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, date)
      var gotDate = $.inArray(formattedDate, formattedDates);
      if (isOrderEdit($form)) {
        return [false, 'ui-state-multi-unselect'];
      } else if (isRecurringOrder($form)) {
        var $selectedDays = $form.find('.open-team-days input[type=checkbox]:checked');
        var selected_days = $selectedDays.map(function(idx, el) { return parseInt($(el).val()) }).get();
        return [(selected_days.indexOf(date.getDay()) != -1), '']
      } else if (gotDate >= 0) {
        return [true, 'ui-state-multi-select'];
      } else {
        return [true, 'ui-state-multi-unselect'];
      }
    }
  });
}

function changeTeamOrderType(event, $form) {
  event.preventDefault(); // prevent validation;
  var $button = $(event.currentTarget);
  if ($button.hasClass('active')) return;

  $form.find('.team-order-type-selection button').removeClass('active');
  $button.addClass('active');
  changeDatepickerInfo($form);

  var $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  var $teamOrderSuppliers = $form.find('input[name="order[delivery_suppliers]"]');
  $teamOrderDates.val('[]');
  $teamOrderSuppliers.val('{}');

  $('input.datepicker').val('');
}

function changePanel(event, $wrapper, $form) {
  var $button = $(event.currentTarget);
  var step = $button.data('step');
  var currentStep = $button.data('current-step');
  var panel = STEPS[step];
  var currentPanel = STEPS[currentStep];
  var validPanel = validatePanel(currentPanel, $form);
  if (validPanel) {
    toggleActivePanel(panel, $wrapper, $form)
  }
}

function validatePanel(currentPanel, $form) {
  var validForm = true;
  switch (currentPanel) {
    case 'order-details':
      validForm = isValidForm($form)
      if (validForm) {
        validForm = hasDeliveryAddress($form);
      }
      break;
    case 'attendees-selection':
      validForm = hasAttendees($form);
      break;
    case 'supplier-selection':
      validForm = hasSuppliers($form);
      break;
    default:
      break;
  };
  return validForm;
}

function hasAttendees($form) {
  var attendee_ids = $form.find('input[name="order[attendee_ids][]"]').map(function (idx, el) {
    var $attendee = $(el);
    return $attendee.val() ? $attendee.val() : null
  }).get();
  if (attendee_ids.length <= 0) {
    $form.yordarPOP({
      title: 'Attendee Selection',
      innerContent: 'You need to select at least 1 attendee for the event',
      cancel: false
    });
  }
  return attendee_ids.length > 0
}

function hasSuppliers($form) {
  var hasSelectedSuppliers = false;
  var $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  var $teamOrderSuppliers = $form.find('input[name="order[delivery_suppliers]"]');
  var deliverySuppliers = JSON.parse($teamOrderSuppliers.val());
  var deliveryDates = JSON.parse($teamOrderDates.val());
  var supplier_ids = new Array();

  deliveryDates.map(function(date) {
    var formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, new Date(date))
    if (deliverySuppliers[formattedDate] && Object.keys(deliverySuppliers[formattedDate]).length !== 0) {
      supplier_ids.push(Object.keys(deliverySuppliers[formattedDate])[0]);
    }
  });
  hasSelectedSuppliers = supplier_ids.length == deliveryDates.length

  if (!hasSelectedSuppliers) {
    if (isPackageOrder($form) || isRecurringOrder($form)) {
      var message = 'You need to select a Supplier for each day of the event';
    } else {
      var message = 'You need to select a Supplier for the event';
    }
    $form.yordarPOP({
      title: 'Supplier Selection',
      innerContent: message,
      cancel: false
    });
  }
  return hasSelectedSuppliers
}

function toggleActivePanel(panel, $wrapper, $form) {
  $wrapper.find('.team-order-panel').addClass('hidden');
  $wrapper.find(`.${panel}`).removeClass('hidden');
  window.scrollTo(0, 0);

  // load suppliers if not already loaded
  if (['attendees-selection', 'supplier-selection'].indexOf != -1 && $form.find('.team-supplier').length == 0) {
    var suburbId = $form.find('#suburb_id').val();
    localStorage.setItem('suburb_id', suburbId);
    refreshSupplierList($form);
  }

  if (panel == 'supplier-selection') {
    if (isRecurringOrder($form)) {
      setupRecurringDeliveryDates($form);
    } else {
      setupMultiDaySelector ($form);
    };

    if (!isPackageOrder($form) || isRecurringOrder($form)) {
      checkSupplierAvailability($form);
    }

    updateSupplierBestForTeamCount($form);
  }
}

// setup delivery dates based on the order details form's day and date inputs
function setupRecurringDeliveryDates ($form) {
  var $selectedDays = $form.find('.open-team-days input[type=checkbox]:checked');
  var selected_days = $selectedDays.map(function(idx, el) { return parseInt($(el).val()) }).get();

  var delivery_date = $form.find('input.datepicker').val();
  var current_date = new Date(delivery_date);

  var number_of_days = isRecurringOrderExtension($form) ? 7 : 14;
  if (current_date.getDay() > selected_days[0]) { // if the selected date's week-day is greater the the first selected week day
    number_of_days += 6 - current_date.getDay();
  }
  var dayms = number_of_days * 24 * 60 * 60 * 1000;
  var end_date = new Date(current_date.getTime() + dayms);

  var selected_days = $selectedDays.map(function(idx, el) { return parseInt($(el).val()) }).get();
  var teamOrderDates = [];
  while (current_date < end_date ) {
    if (selected_days.indexOf(current_date.getDay()) != -1) {
      var formattedDate = $.datepicker.formatDate(PICKER_DATE_FORMAT, current_date);
      var dateWithTime = `${formattedDate} ${delivery_date.split(' ')[1]}`;
      teamOrderDates.push(dateWithTime);
    }
    current_date.setDate(current_date.getDate() + 1);
  }
  var $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  $teamOrderDates.val(JSON.stringify(teamOrderDates));
  setupMultiDaySelector ($form);

  if(!isRecurringOrderExtension($form) && !$teamOrderDates.data('recurring_supplier_popup')) {
    $form.yordarPOP({
      container: '#yordarPopUp',
      title: 'Recurring Team Order Suppliers',
      innerContent: 'For initial setup we require you to select suppliers for 2 complete weeks.' ,
      submit: 'Ok',
      cancel: false,
    });
    $teamOrderDates.data('recurring_supplier_popup', true);
  }
}

function updateSupplierBestForTeamCount($form) {
  var $suppliers = $form.find('.team-supplier');
  var budget = $form.find('input[name="order[team_order_detail_attributes][budget]"]').val();  
  if (budget) {
    var $budgetHeader = $form.find('.best-for-header')
    $budgetHeader.attr('title', `<span class='text-center'>Based on budget of:<br /> $${budget} (per head)</span>`);

    $suppliers.each(function (idx, el) {
      var $supplier = $(el);
      var bestFor = parseInt($supplier.data('minimum-spend') / budget)
      if (bestFor < 1) bestFor = 1;
      $supplier.find('.best-for-teams').text(bestFor);
    })
  }
}

// checks the hidden field for delivery address
function hasDeliveryAddress($form) {
  var deliveryAddress = $form.find('input[name="order[delivery_address]"]').val();
  if (!deliveryAddress) {
    $form.yordarPOP({
      title: 'Team Order Delivery',
      innerContent: 'We need a valid street address to find the right suppliers',
      cancel: false
    });
  }
  return deliveryAddress ? true : false
}

function isValidForm($form) {
  sanitizeDeliveryDates($form);
  $form.foundation('validateForm');
  //subsection is for partially validating a single form
  var order_details_errors;
  order_details_errors = $form.find('[data-invalid]').length > 0;
  if (order_details_errors) {
    scrollToValidationErrors($form);
  } else {
    return true
  }
}

function sanitizeDeliveryDates($form) {
  if (!isPackageOrder($form) && !isRecurringOrder($form)) {
    return false;
  }
  var $teamOrderDates = $form.find('input[name="order[delivery_dates]"]');
  var teamOrderDates = JSON.parse($teamOrderDates.val());
  if (teamOrderDates.length == 0) {
    $form.find('input[name="order[delivery_at]"]').val('').trigger('change');
  }
}

function scrollToValidationErrors($form) {
  var invalidFields = $form.find('[data-invalid]');
  var scrollTo = $(`#${invalidFields[0].id}`).offset().top - 40;
  $('html, body').animate({ scrollTop: scrollTo }, 400);
}

function toggleAttendeeLevels (event, $form) {
  var $link = $(event.currentTarget);
  var $levelsForm = $form.find('.team-order-levels');
  $link.toggleClass('hidden');
  $levelsForm.toggleClass('hidden');
}

function submitTeamOrder(event, $wrapper, $form) {
  var $button = $(event.currentTarget);

  // set delivery suburb
  var suburb_id = $form.find('#suburb_id').val();
  $form.find('input[name="order[delivery_suburb_id]"]').val(suburb_id);

  var $newCardForm = $wrapper.find('form.new-credit-card');
  var order_card = $form.find('input[name="order[credit_card_id]"]').val();

  if ($newCardForm.is(':visible') && !$newCardForm.data('submitting')) {
    $newCardForm.submit(); // handled in customerOrderPaymentForm.js // returns back to here on success
  } else if (order_card) {
    submitOrderWithMode ($button, $form);
  } else {
    $(this).yordarPOP({
      container: '#yordarPopUp',
      title: 'Team Order Error!',
      innerContent: 'Please select a Payment method',
      cancel: false,
    });
  }
}

// extend order button on supplier selection panel
function extendTeamOrder(event, $wrapper, $form) {
  event.preventDefault();

  var $button = $(event.currentTarget);  
  var hasOrderCard = $form.find('input[name="order[credit_card_id]"]').val();
  var isValidExtension = hasSuppliers($form) && hasOrderCard;
  if (isValidExtension) {
    submitOrderWithMode($button, $form);
  }
}

function submitOrderWithMode ($button, $form) {
  var mode = $button.data('mode');
  if (mode) {
    var $modeInput = $form.find('input[name="order[mode]"]');
    $modeInput.val(mode);
    $form.append($modeInput);
  }
  $form.foundation('validateForm');
  if ($form.find('[data-invalid]').length === 0) {
    $button.html(spinner_html);
    $form.submit();
  } else {
    $form.yordarPOP({
      container: '#yordarPopUp',
      title: 'Team Order Error!',
      innerContent: 'Please check form for invalid fields',
      cancel: false,
    });
  }
}
