export default function customerTeamOrderManifest (el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents ($wrapper, props) {
  $wrapper.on('click', '.generate-manifest', function(e) { generateDocument(e, props) });
}

function generateDocument (event, props) {

  var $button = $(event.currentTarget);
  $button.attr('disabled', true);
  $button.text('Generating...');
  
  var generate_request = $.ajax({
    url: props.url,
    type: 'GET',
    dataType: 'JSON',
  });

  generate_request.fail(function(response) {
    $button.yordarPOP({
      title: 'Team Order Manifest',
      innerContent: 'Sorry we could not generate the Team Order Manifest! Please try again',
      cancel: 'close',
      submit: false,
    });
  });

  generate_request.then(function (response) {
    $button.yordarPOP({
      title: 'Team Order Manifest',
      innerContent: `<a href='${response.url}'>Click to open <strong>Generated Team Order Manifest</strong></a>`,
      cancel: 'close',
      submit: false,
    });
  })

  generate_request.always(function () {
    $button.attr('disabled', false);
    $button.text('Generate Team Order Manifest');
  })
}
