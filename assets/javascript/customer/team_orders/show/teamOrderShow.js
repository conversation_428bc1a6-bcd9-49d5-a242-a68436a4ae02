export default function teamOrderShow (el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents ($wrapper, props) {
  $(document).on('click', '.attendee-invite-link', function (e) { copyInviteLink(e, $wrapper) });
  $wrapper.on('click', '.remove-attendee', function(e) { handleRemoveAttendee(e) });
  $wrapper.on('click', '.team-order-attendees__show-orders', function (e) { showAttendeeOrders(e, $wrapper) });
  $wrapper.on('click', '.team-order-attendees__show-orders--hollow', function (e) { hideAttendeeOrders(e, $wrapper) });
  $(document).on('click', '.collapsible-totals', function(e) { collapsibleTotals(e, $wrapper) });
}

function copyInviteLink(event, $wrapper) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  if ($link.prop('tagName') == 'A') {
    var copyText = $link.attr('href');
  } else {
    var copyText = $link.text();
  }
  var input = document.createElement('input');
  var $mockInput = $(input);
  $mockInput.prop('type', 'text');
  $mockInput.val(copyText);
  $link.after($mockInput);
  $mockInput.select();
  document.execCommand('copy');
  $mockInput.addClass('hidden');
  $('.invite-link-info--copied').removeClass('hidden');
  $('.show-package__magic-link').text('Link Copied!').css('color', 'green');
}

function handleRemoveAttendee (event) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  var $attendee = $link.parents('.team-contact');
  $attendee.yordarPOP({
    title: 'Remove/Detach Attendee',
    innerContent: `Are you sure you want to detach '${$attendee.data('name')}'?`,
    submitHandler: function () {
      detachAttendee($link);
    }
  });
}

function detachAttendee ($link) {
  var $attendee = $link.parents('.team-contact');
  var attendee_request = $.ajax({
    url: $link.attr('href'),
    type: 'DELETE',
    dataType: 'JSON',
    data: { wants_html: true },
  });

  attendee_request.done(function (response) {
    var $updatedAttendee = $(response.attendee_html);
    $attendee.replaceWith($updatedAttendee);
  });

  attendee_request.fail(function (response) {
    var errors = response.responseJSON.errors;
    var display_msg = errors.join('<br/>');
    $attendee.yordarPOP({
      title: 'Attendee - Error!',
      innerContent: display_msg,
      cancel: false
    });
  });
}

function showAttendeeOrders(e) {
  var $activeAttendeeList = $('.team-contacts--active');
  var $anonAttendeeList = $('.team-contacts--anon');

  var $orderedActiveAttendees = $activeAttendeeList.find('.team-contact[data-order-status="ordered"]');
  var $orderedAnonAttendees = $anonAttendeeList.find('.team-contact[data-order-status="ordered"]');

  var $button = $(e.currentTarget);
  $button.text('Hide orders');
  $button.addClass('team-order-attendees__show-orders--hollow');

  $activeAttendeeList.prepend($orderedActiveAttendees);
  $anonAttendeeList.prepend($orderedAnonAttendees);
  $('.team-attendee-order-lines').removeClass('hidden');
}

function hideAttendeeOrders(e) {
  var $activeAttendeeList = $('.team-contacts--active');
  var $anonAttendeeList = $('.team-contacts--anon');

  var $button = $(e.currentTarget);
  $button.removeClass('team-order-attendees__show-orders--hollow');
  $button.text('Show orders');
  $('.team-attendee-order-lines').addClass('hidden');
  resortToInitialOrder($activeAttendeeList);
  resortToInitialOrder($anonAttendeeList);
}

function resortToInitialOrder($list) {
  var $attendees = $list.children('li');

  var sortList = Array.prototype.sort.bind($attendees);

  sortList(function (a, b) {

    var aId = parseInt(a.getAttribute('data-sort-id'));
    var bId = parseInt(b.getAttribute('data-sort-id'));

    if (aId < bId) {
      return -1;
    }

    if (aId > bId) {
      return 1;
    }
    return 0;
  });

  $list.append($attendees);
}

function collapsibleTotals() {
  var content = document.querySelector('.content');
  var seeMore = document.querySelector('.team-order-details__see-more');
  if (content.style.maxHeight) {
    content.style.maxHeight = null;
    seeMore.textContent = 'See More';
  } else {
    content.style.maxHeight = `${content.scrollHeight}px`;
    seeMore.textContent = 'See Less';
  }
}
