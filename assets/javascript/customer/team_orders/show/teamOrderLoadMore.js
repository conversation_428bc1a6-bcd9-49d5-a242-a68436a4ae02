export default function teamOrderLoadMore (el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents ($el) {
  $el.on('click', '.load-more', function(e) { loadMore(e) });
}

function loadMore (event) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  var direction = $link.data('direction');
  var $linkContainer = $link.parents('.show-package__row');
  $link.html(spinner_html);

  var more_request = $.ajax({
    url: $link.attr('href'),
    dataType: 'JSON',
    type: 'GET',
    data: { wants_html: true, direction: direction}
  });

  more_request.done(function(response) {
    var $updatedHtml = $(response.html);
    $linkContainer.replaceWith($updatedHtml);
  });

  more_request.fail(function(response) {
    $linkContainer.yordarPOP({
      container: '#yordarPopUp',
      title: 'Load More',
      innerContent: `Sorry we were unable to load more orders for the ${direction} week. Please try again.`,
      submit: 'Ok',
      cancel: false,
    });
  });
}
