export default function teamOrderAttendeeSlideout(el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents($wrapper) {
  $wrapper.on('click', 'a[data-modal-view]', function (e) {
    modalView(e, $wrapper);
  });
}

function modalView(event, $wrapper) {
  event.preventDefault();
  const $link = $(event.currentTarget);
  const $orderModal = $(document).find('#modal-order-show');

  $orderModal.foundation('open');
  $orderModal.html(
    '<div class="lds-ring-container"><div class="lds-ring"><div></div><div></div><div></div><div></div></div></div>'
  );
  const menu_request = $.ajax({
    url: $link.attr('href'),
    type: 'GET',
    dataType: 'JSON',
    data: { wants_html: true },
  });

  menu_request.done(function (response) {
    $orderModal.html(response.order_html);
  });

  menu_request.fail(function (err) {
    $(this).yordarPOP({
      title: 'Order',
      innerContent: 'Sorry, we couldn\'t load your order details right now, please try again later.',
      cancel: false,
    });
  });
}
