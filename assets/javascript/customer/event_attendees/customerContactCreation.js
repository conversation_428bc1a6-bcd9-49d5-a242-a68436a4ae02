import { setInviteAllSwitch } from '../team_orders/creation/common';

export default function customerContactCreation(el, props) {
  const $el = $(el);
  bindEvents($el);
  setupTeamSelect($el);
}

function bindEvents($wrapper) {
  $wrapper.on('click', '.change-panel', function (e) {
    changeDrawerPanel(e, $wrapper);
  });
  $wrapper.on('click', 'form.new-team-contact-form button.add-contact', function (e) {
    startAddingNewContact(e, $wrapper);
  });
  $wrapper.on('submit', 'form.new-team-contact-form', function (e) {
    addNewContact(e, $wrapper);
  });
  $wrapper.on('submit', 'form.new-team-contact-csv-form', function (e) {
    uploadContactsCSV(e, $wrapper);
  });
  $wrapper.on('click', '.attendee-invite-link', function (e) {
    copyInviteLink(e, $wrapper);
  });
}

function setupTeamSelect($wrapper) {
  $wrapper.find('select[name="event_attendee[teams][]"]').select2({
    tags: true,
    dropdownParent: $wrapper,
  });
}

function changeDrawerPanel(event, $wrapper) {
  const $currentTab = $(event.currentTarget);
  $('.change-panel').removeClass('active');
  $currentTab.addClass('active');
  $('.add-contact-panel').addClass('hidden');

  const panelClass = $currentTab.data('panel');
  $(`.add-contact-panel.${panelClass}`).removeClass('hidden');
}

function startAddingNewContact(event, $wrapper) {
  event.preventDefault();
  const $submitBtn = $(event.currentTarget);
  $submitBtn.data('active', true);
  $submitBtn.parents('form').submit();
}

function addNewContact(event, $wrapper) {
  event.preventDefault();
  const $form = $(event.currentTarget);
  const attendeeData = `${$form.serialize()}&${$.param({ wants_html: true, context: $form.data('context') })}`;
  const $buttons = $form.find('button.add-contact');
  let $submitBtn;
  for (let i = 0; i < $buttons.length; i++) {
    const $button = $($buttons[i]);
    if ($button.data('active')) {
      $submitBtn = $button;
    }
  }

  if (!$submitBtn) return;

  $submitBtn.text('Uploading...');

  const new_contact_request = $.ajax({
    url: $form.attr('action'),
    type: 'POST',
    dataType: 'JSON',
    data: attendeeData,
  });

  new_contact_request.done(function (response) {
    const $container = $(document);
    const $eventAttendeesList = $container.find('.team-contacts');
    const $newAttendee = $(response.attendee_html);
    $eventAttendeesList.append($newAttendee);
    $form[0].reset();
    $form.find('select[name="event_attendee[teams][]"]').val('').trigger('change');
    if (!$submitBtn.data('add-more')) {
      $form.parents('#modal-add-new-contact').foundation('close');
    }
    $eventAttendeesList.animate({ scrollTop: $eventAttendeesList[0].scrollHeight }, 500);

    setInviteAllSwitch($container);
    showHideNoContactsPanel($container);
  });

  new_contact_request.fail(function (response) {
    if (response.responseJSON && response.responseJSON.errors) {
      const { errors } = response.responseJSON;
      $wrapper.find('#yordarPopUp').data('multiple-opened', 'true');
      $form.yordarPOP({
        container: '#yordarPopUp',
        innerContent: errors.join('<br>'),
        title: 'Contact Error',
        cancel: false,
      });
    } else {
      alert('Sorry we were not able to save the contact. Please try again.');
    }
  });

  new_contact_request.always(function () {
    $submitBtn.data('active', false);
    $submitBtn.text($submitBtn.data('button-text'));
  });
}

function uploadContactsCSV(event, $wrapper) {
  event.preventDefault();
  const $form = $(event.currentTarget);
  const $submitBtn = $form.find('button[type=submit]');
  const $fileInput = $form.find('input[type=file]');
  const uploadedFile = $fileInput[0].files[0];

  if (!uploadedFile) {
    return;
  }
  $submitBtn.text('Uploading...');
  const formData = new FormData();
  formData.append('wants_html', 'true');
  formData.append('context', $form.data('context'));
  formData.append($fileInput.attr('name'), uploadedFile);

  const upload_contacts_request = $.ajax({
    url: $form.attr('action'),
    type: 'POST',
    dataType: 'JSON',
    processData: false,
    contentType: false,
    enctype: 'multipart/form-data',
    data: formData,
  });

  upload_contacts_request.done(function (response) {
    const $eventAttendeesList = $wrapper.find('.team-contacts');
    const uploaded_event_attendees = response.event_attendees;

    for (let i = 0; i < uploaded_event_attendees.length; i++) {
      const uploaded_attendee = uploaded_event_attendees[i];
      const $existingAttendee = $eventAttendeesList.find(`.team-contact[data-attendee-id=${uploaded_attendee.id}]`);
      if ($existingAttendee.length > 0) {
        var $newAttendee = $(uploaded_attendee.attendee_html);
        $existingAttendee.replaceWith($newAttendee);
      } else {
        var $newAttendee = $(uploaded_attendee.attendee_html);
        $eventAttendeesList.append($newAttendee);
      }
    }

    setInviteAllSwitch($wrapper);
    $form.parents('#modal-add-new-contact').foundation('close');
    $submitBtn.text('Upload Contacts');
    showHideNoContactsPanel($wrapper);
  });

  upload_contacts_request.fail(function (response) {
    if (response.responseJSON && response.responseJSON.errors) {
      const { errors } = response.responseJSON;
      $wrapper.find('#yordarPopUp').data('multiple-opened', 'true');
      $form.yordarPOP({
        container: '#yordarPopUp',
        title: 'Contacts Upload Error',
        innerContent: errors.join('<br>'),
        cancel: false,
      });
    } else {
      alert('Sorry we were not able to upload the contacts. Please try again.');
    }
  });

  upload_contacts_request.always(function () {
    $submitBtn.text('Upload Contacts');
  });
}

function showHideNoContactsPanel($wrapper) {
  const $contacts = $wrapper.find('.team-order-list__row');
  if ($contacts.length) {
    $wrapper.find('.attendees-selection').removeClass('hidden');
    $(document).find('.no-team-contacts').addClass('hidden');
  }
}

function copyInviteLink(event, $wrapper) {
  event.preventDefault();
  const $link = $(event.currentTarget);
  const copyText = $link.prop('href');
  const input = document.createElement('input');
  const $mockInput = $(input);
  $mockInput.prop('type', 'text');
  $mockInput.val(copyText);
  $link.after($mockInput);
  $mockInput.select();
  document.execCommand('copy');
  $mockInput.addClass('hidden');
  $('.invite-link-info--copied').removeClass('hidden');
}
