export default function customerContactTeam (el, props) {
  const $el = $(el);
  bindEvents($el);
}

function bindEvents ($wrapper) {
  // add new team
  $wrapper.on('submit', 'form#new_event_team', function (e) { addNewTeam(e, $wrapper) });

  // edit existing team
  $wrapper.on('click', '.edit-team-btn', function (e) { editTeam(e, $wrapper) });
  $wrapper.on('focus', '.team-name[contenteditable]', function(e) { selectEditableContent(e) });
  $wrapper.on('focusout', '.team-name[contenteditable]', function(e) { teamNotEditable(e) });
  $wrapper.on('keydown', '.team-name[contenteditable]', function (e) { handleEditTeam(e, $wrapper) });

  // remove team
  $wrapper.on('click', '.delete-team-btn', function (e) { deleteTeam(e, $wrapper) });

  // add contacts to team
  $wrapper.on('change', '.add-edit-teams input', function (e) { addOrEditToTeams(e, $wrapper) });
  $wrapper.on('click', '.team-contact .edit-contact', function (e) { editContactInTeam(e, $wrapper) });
}

function addOrEditToTeams(e, $wrapper) {
  var $input = $(e.currentTarget);
  var team = parseInt($input.val());
  var teamName = $input.prop('name');
  var activeTeamFilter = $(e.currentTarget).prop('checked');


  // Uncheck any filters that aren't the one just clicked
  $wrapper.find('.team-filter').not($(e.currentTarget)).prop('checked', false);
  $wrapper.find('.team-order-list__btn').removeClass('delete-contact');
  $wrapper.find('.team-order-list__btn').addClass('edit-contact');

  resortToInitialOrder($wrapper);
  indicateTeamSelection(teamName, $wrapper);

  var $contacts = $wrapper.find('.team-contacts .team-contact');
  $contacts.each(function (idx, el) {
    var $contact = $(el);
    var contactTeams = $contact.data('team-ids');
    var $button = $contact.find('.team-order-list__btn');
    if (contactTeams.indexOf(team) >= 0 && activeTeamFilter) {
      $('.team-contacts').prepend($contact);
      $button.addClass('contact-selected');
      $button.text('-');
    }
    else {
      $button.removeClass('contact-selected');
      $button.text('+');
    }
  })

  if (!activeTeamFilter) {
    resetToDefaultNoTeamsSelected($wrapper);
  }
}


function resortToInitialOrder($wrapper) {
  var $contactList = $wrapper.find('.team-contacts');
  var $contacts = $contactList.children('li');
  var sortList = Array.prototype.sort.bind($contacts);

  sortList(function (a, b) {
    var aId = parseInt(a.getAttribute('data-list-order-id'));
    var bId = parseInt(b.getAttribute('data-list-order-id'));
    if (aId < bId) {
      return -1;
    }
    if (aId > bId) {
      return 1;
    }
    return 0;
  });
  $contactList.append($contacts);
}

function indicateTeamSelection (teamName, $wrapper) {
  var $dropdownButtonContainer = $wrapper.find('.team-order-filter--teams');
  var $dropdownButton = $dropdownButtonContainer.find('.dropdown-filter-button');
  $dropdownButtonContainer.addClass('active-team');
  $dropdownButton.text(teamName.slice(0, 12));
  $wrapper.find('.team-order-list__heading--highlight').text(`Add/Remove from ${teamName}`);
}

function resetToDefaultNoTeamsSelected ($wrapper) {
  var $dropdownButtonContainer = $wrapper.find('.team-order-filter--teams');
  var $dropdownButton = $dropdownButtonContainer.find('.dropdown-filter-button');
  $dropdownButtonContainer.removeClass('active-team');
  $dropdownButton.text('Teams');
  $wrapper.find('.team-order-list__heading--highlight').text('remove contact');
  $wrapper.find('.team-order-list__btn').text('X');
  $wrapper.find('.team-order-list__btn').addClass('delete-contact');
  $wrapper.find('.team-order-list__btn').removeClass('edit-contact');
}

function addNewTeam (event, $wrapper) {
  event.preventDefault();
  var $form = $(event.currentTarget);
  var $input = $('.add-new-team__input');
  var $newTeam = $input.val();
  var new_team_request = $.ajax({
    url: $form.attr('action'),
    type: 'POST',
    dataType: 'JSON',
    data: { event_team: { name: $newTeam }, wants_html: true },
  });

  new_team_request.done(function (response) {
    var $newTeam = $(response.team_html);
    var $teamsDropdown = $('.team-order-filter--teams .filter-content ul');
    var $existingTeam = $teamsDropdown.find(`.event-team[data-team-id=${response.id}]`);
    if ($existingTeam.length > 0) {
      $existingTeam.replaceWith($newTeam);
    } else {
      $teamsDropdown.append($newTeam);
    }
    $newTeam.find('input').prop('checked', true).trigger('change');
    $input.val('');
  });

  new_team_request.fail(function (response) {
    if (response.responseJSON && response.responseJSON.errors) {
      var errors = response.responseJSON.errors;
      $form.yordarPOP({
        container: '#yordarPopUp',
        innerContent: errors.join('<br>'),
        title: 'Team Error',
        cancel: false,
      });
    } else {
      alert('Sorry we were not able to add this team. Please try again.');
    }
  });
}

function editTeam(e, $wrapper) {
  event.preventDefault();
  var $teamRow = $(e.currentTarget).parent('.event-team');
  var $team = $teamRow.find('.team-name');
  var $originalTeamName = $team.text().trim();
  var $teamID = $(e.currentTarget).parents('.event-team').data('team-id');
  $team.prop('contenteditable', true);
  // Issue focusing on contenteditable, solved with setTimeout
  // https://stackoverflow.com/questions/2388164/set-focus-on-div-contenteditable-element
  setTimeout(function () { $team.focus() }, 0);
}

// select all text in contenteditable - http://stackoverflow.com/a/6150060/145346
function selectEditableContent (event) {
  var cell = event.currentTarget;
  var range, selection;
  if (document.body.createTextRange) {
    range = document.body.createTextRange();
    range.moveToElementText(cell);
    range.select();
  } else if (window.getSelection) {
    selection = window.getSelection();
    range = document.createRange();
    range.selectNodeContents(cell);
    selection.removeAllRanges();
    selection.addRange(range);
  }
}

function teamNotEditable(event) {
  $(event.currentTarget).prop('contenteditable', false);
}

function handleEditTeam(event, $wrapper) {
  var $team = $(event.currentTarget).parents('.event-team');
  var originalTeamName = $team.find('.team-filter').prop('name');
  var $teamName = $team.find('.team-name');
  if (event.keyCode === 13) {
    event.preventDefault();
    var newTeamName = $teamName.text().trim();
    var edit_team_request = $.ajax({
      url: $team.data('url'),
      type: 'PUT',
      dataType: 'JSON',
      data: { event_team: { name: newTeamName }, wants_html: true },
    });

    edit_team_request.done(function (response) {
      $team.replaceWith(response.team_html);
      var $contactTeams = $wrapper.find('.team-order-list__cell--teams');
      $contactTeams.each(function (idx, el) {
        var teams = $(el).text().replace(originalTeamName, newTeamName);
        $(el).text(teams);
      });
    })

    edit_team_request.fail(function (response) {
      $teamName.text(originalTeamName);
      if (response.responseJSON && response.responseJSON.errors) {
        var errors = response.responseJSON.errors;
        $team.yordarPOP({
          container: '#yordarPopUp',
          innerContent: errors.join('<br>'),
          title: 'Team Error',
          cancel: false,
        });
      } else {
        alert('Sorry we were not able to edit this team. Please try again.');
      }
    });

    edit_team_request.always(function () {
      $team.prop('contenteditable', false);
    });
  }
}

function deleteTeam (event) {
  event.preventDefault();
  var $team = $(event.currentTarget).parents('.event-team');
  var teamName = $team.find('.team-name').text().trim();

  $team.yordarPOP({
    container: '#yordarPopUp',
    innerContent: `Are you sure you wish to remove <b>${teamName}</b>?`,
    title: 'Remove Team',
    submit: 'Yes',
    cancel: 'No',
    submitHandler: function (event) {
      handleDeleteTeam($team);
    }
  });
}

function handleDeleteTeam($team) {
  var delete_team_request = $.ajax({
    url: $team.data('url'),
    type: 'DELETE',
    dataType: 'JSON'
  });

  delete_team_request.done(function (response) {
    $team.remove();
  });

  delete_team_request.fail(function (response) {
    if (response.responseJSON && response.responseJSON.errors) {
      var errors = response.responseJSON.errors;
      $team.yordarPOP({
        container: '#yordarPopUp',
        innerContent: errors.join('<br>'),
        title: 'Team Error',
        cancel: false
      });
    } else {
      alert('Sorry we were not able to delete this team. Please try again.');
    }
  });
}

function editContactInTeam(event, $wrapper) {
  event.preventDefault();
  var $button = $(event.currentTarget);
  var $eventAttendee = $button.parents('.team-contact');
  var rowId = $eventAttendee.data('list-order-id');
  var currentTeamId = parseInt($('.team-filter:checked').data('team-id'));
  var action = $button.hasClass('contact-selected') ? 'remove' : 'add';

  var edit_contact_team_request = $.ajax({
    url: $button.data('edit-team-path'),
    type: 'POST',
    dataType: 'JSON',
    data: { event_team_id: currentTeamId, attendee_action: action, wants_html: true, row_id: rowId }
  });

  edit_contact_team_request.done(function (response) {
    var $responseHTML = response.attendee_html;
    $eventAttendee.replaceWith($responseHTML);
  });

  edit_contact_team_request.fail(function (response) {
    if (response.responseJSON && response.responseJSON.errors) {
      var errors = response.responseJSON.errors;
      $eventAttendee.yordarPOP({
        container: '#yordarPopUp',
        innerContent: errors.join('<br>'),
        title: 'Contact Team Error',
        cancel: false,
      });
    } else {
      var message = `Sorry we were not able to ${action} the contact ${(action == 'remove' ? 'from' : 'to')} the team. Please try again.`;
      $eventAttendee.yordarPOP({
        container: '#yordarPopUp',
        innerContent: message,
        title: 'Contact Team Error',
        cancel: false,
      });
    }
  });
}

