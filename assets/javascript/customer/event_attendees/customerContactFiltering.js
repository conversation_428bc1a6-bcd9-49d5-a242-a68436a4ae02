import { setInviteAllSwitch } from '../team_orders/creation/common';

export default function customerContactFiltering(el, props) {
  const $el = $(el);
  bindEvents($el);
}

function bindEvents($wrapper) {
  // teams dropdown
  $wrapper.on('click', '.dropdown-filter-button', function (e) {
    showHideFilterDropdowns(e);
  });
  $(document).on('click', '.overlay', function (e) {
    hideFilterDropdowns(e);
  });

  $wrapper.on('change', '.filter-by-teams input', function (e) {
    filterByTeams(e, $wrapper);
  });
  $wrapper.on('keyup', 'input[name=filter-contacts]', function (e) {
    filterByQuery(e, $wrapper);
  });
}

function showHideFilterDropdowns(event) {
  event.preventDefault();
  const clickedButton = event.currentTarget;
  const currentFilterContent = clickedButton.parentNode.querySelector('.filter-content');
  const filterContents = document.querySelectorAll('.dropdown-bubble .filter-content');
  const overlay = document.querySelector('.overlay');
  let showOverlay = false;
  for (let i = 0; i < filterContents.length; i++) {
    const filterContent = filterContents[i];
    if (filterContent == currentFilterContent) {
      $(filterContent).toggleClass('hidden');
      $(clickedButton).toggleClass('spin');
    } else {
      $(filterContent).addClass('hidden');
    }
    if (!$(filterContent).hasClass('hidden')) {
      showOverlay = true;
    }
  }
  // show Hide Overlay
  if (showOverlay) {
    $(overlay).removeClass('hidden').addClass('transparent');
  } else {
    $(overlay).addClass('hidden').removeClass('transparent');
  }
}

function hideFilterDropdowns() {
  const $overlay = $('.overlay');
  const $filterContent = $('.dropdown-bubble .filter-content');
  $filterContent.addClass('hidden');
  $filterContent.siblings('.dropdown-filter-button').removeClass('spin');
  $overlay.addClass('hidden').removeClass('transparent');
}

function filterByTeams(e, $wrapper) {
  if ($(e.currentTarget).hasClass('team-filter--everyone')) {
    $('.team-filter').prop('checked', false);
  } else {
    $('.team-filter--everyone').prop('checked', false);
  }

  const teams = [];
  $('.team-filter:checked').each(function () {
    teams.push(parseInt($(this).val()));
  });

  if (teams.length === 0) $('.team-filter--everyone').prop('checked', true);

  const $contacts = $wrapper.find('.team-contacts .team-contact');
  $contacts.removeClass('hidden');

  if (teams.length) {
    $contacts.each(function (idx, el) {
      const $contact = $(el);
      const contactTeams = $contact.data('team-ids');
      if (contactTeams.length < 0 || $(contactTeams).filter(teams).length === 0) {
        $contact.addClass('hidden');
      }
    });
  }

  setInviteAllSwitch($wrapper);
}

function filterByQuery(event, $wrapper) {
  const $input = $(event.currentTarget);
  const query = $input.val().toLocaleLowerCase();

  const $contacts = $wrapper.find('.team-contacts .team-contact');
  $contacts.removeClass('hidden');

  if (query.length >= 3) {
    $contacts.each(function (idx, el) {
      const $contact = $(el);
      const searchableText = $contact.text().toLocaleLowerCase();
      if (searchableText.indexOf(query) < 0) {
        $contact.addClass('hidden');
      }
    });
  }

  setInviteAllSwitch($wrapper);
}
