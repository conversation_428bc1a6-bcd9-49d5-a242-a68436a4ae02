import './dragDrop.js';

export default function customerContactList (el, props) {
  const $el = $(el);
  bindEvents($el);
}

function bindEvents ($wrapper) {
  $wrapper.on('click', '.team-contact .delete-contact', function (e) { removeContact(e, $wrapper) });
  $wrapper.on('DOMSubtreeModified', '.team-contacts', function(e) { toggleNoContacts(e) });
}

function removeContact(event, $wrapper) {
  event.preventDefault();
  var $button = $(event.currentTarget);
  var $eventAttendee = $button.parents('.team-contact');
  var remove_contact_request = $.ajax({
    url: $button.attr('href'),
    type: 'DELETE',
    dataType: 'JSON',
  });

  remove_contact_request.done(function (response) {
    $eventAttendee.remove();
  });

  remove_contact_request.fail(function (response) {
    if (response.responseJSON && response.responseJSON.errors) {
      var errors = response.responseJSON.errors;
      $eventAttendee.yordarPOP({
        container: '#yordarPopUp',
        innerContent: errors.join('<br>'),
        title: 'Contact Error',
        cancel: false,
      });
    } else {
      alert('Sorry we were not able to remove the contact. Please try again.');
    }
  });
}

function toggleNoContacts(e) {
  var $contacts = $(document).find('.team-order-list__row');
  if($contacts.length) {
    $('.contact-list').removeClass('hidden');
    $('.no-team-contacts').addClass('hidden');
  } else {
    $('.contact-list').addClass('hidden');
    $('.no-team-contacts').removeClass('hidden');
  }
}
