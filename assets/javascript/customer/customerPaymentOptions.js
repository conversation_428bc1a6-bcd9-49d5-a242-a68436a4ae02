import { submitStripeCard, bindStripeCardToForm } from 'javascript/stripe/handleStripeForm';

export default function customerPaymentOptions (el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents ($wrapper, props) {
  $wrapper.on('click', '.add-new-credit-card-btn', function (e) { toggleNewCardForm(e, $wrapper) });
  $wrapper.on('click', 'a.edit-credit-card', function(e) { toggleEditCardForm(e, $wrapper) });
  $wrapper.on('click', '.button.cancel', function(e) { hideCreditCardForm(e, $wrapper) });

  $wrapper.on('click', 'a.hide-credit-card', function (e) { hideCard(e) });
  $wrapper.on('click', 'a.delete-credit-card', function (e) { deleteCard(e) });

  $wrapper.on('click', '.submit-card', function(e) { $(e.currentTarget).parents('form').submit(); });
  $wrapper.on('submit', 'form.new-credit-card', function(e) { submitNewCreditCard(e, $wrapper) });
  $wrapper.on('submit', 'form.edit-credit-card', function(e) { updateCreditCard(e, $wrapper) });

  $wrapper.on('click', 'a.nominate-credit-card', function(e) { nominateCard(e, $wrapper) });
}

function toggleNewCardForm (event, $wrapper) {
  $wrapper.find('.new-credit-card-container').toggleClass('hidden');
}

function toggleEditCardForm (event, $wrapper) {
  var $link = $(event.currentTarget);
  var $creditCard = $link.parents('.credit-card-item');
  $(`.edit-credit-card-form-container[data-credit-card-id='${$creditCard.data('credit-card-id')}']`).toggleClass('hidden');
}

function hideCreditCardForm (event, $wrapper) {
  var $formContainer = $(event.currentTarget).parents('.credit-card-form-container');
  $formContainer.addClass('hidden');
}

function initTooltips ($wrapper) {
  $wrapper.find('tr.credit-card-item').tooltip();
}

function addStripeCardTolist(response, $form, $wrapper) {
  var $creditCardList = $wrapper.find('#credit-cards-listing')
  var $createdCard = $(response.html);

  if ($creditCardList.find('.new-cards').length > 0) {
    $creditCardList.find('.new-cards').after($createdCard);
  } else {
    $creditCardList.prepend($createdCard);
  }

  var $newform = $createdCard.find('.stripe-card-form');
  bindStripeCardToForm($newform, $wrapper);

  $form.parents('.credit-card-form-container').addClass('hidden');
  var scrollTo = $createdCard.offset().top;
  $('html, body').animate({ scrollTop: scrollTo }, 400);
}

function submitNewCreditCard (event, $wrapper) {
  event.preventDefault();
  var $form = $(event.currentTarget);

  submitStripeCard($form, $wrapper, addStripeCardTolist);
}

function updateStripeCardInList (response, $form, $wrapper) {
  $form.parents('.credit-card-form-container').remove(); // remove current form
  var $creditCard = $wrapper.find(`.credit-card-item[data-credit-card-id=${response.id}]`);
  var $responseHtml = $(response.html);
  $creditCard.replaceWith($responseHtml); // replace credit card row with incoming card row and form row
  var $form = $responseHtml.find('.stripe-card-form');
  bindStripeCardToForm($form, $wrapper);
}

function updateCreditCard (event, $wrapper) {
  event.preventDefault();
  var $form = $(event.currentTarget);

  submitStripeCard($form, $wrapper, updateStripeCardInList);
}

function hideCard (event) {
  var $link = $(event.currentTarget);
  var $creditCard = $link.parents('.credit-card-item');
  var display_msg = 'Are you sure you want to hide this credit card?'
  display_msg += '<br/> It won\'t appear in the drop down for future orders'
  $(this).yordarPOP({
    container: '#yordarPopUp',
    innerContent: display_msg,
    submit: 'Hide',
    submitHandler: function(e) {
      updateCreditCardVisibility('saved_for_future', $creditCard);
    }
  });
}

function deleteCard (event) {
  var $link = $(event.currentTarget);
  var $creditCard = $link.parents('.credit-card-item');
  $(this).yordarPOP({
    container: '#yordarPopUp',
    innerContent: 'Are you sure you want to delete this credit card?',
    submit: 'Delete',
    submitHandler: function(e) {
      updateCreditCardVisibility('enabled', $creditCard);
    }
  });
}

function nominateCard (event, $wrapper) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  var $creditCard = $link.parents('.credit-card-item');
  var is_nominated = !$creditCard.data('nominated');

  var nominate_request = $.ajax({
    url: $creditCard.data('url'),
    type: 'PUT',
    dataType: 'JSON',
    data: { credit_card: { auto_pay_invoice: is_nominated } },
  });

  nominate_request.done(function (response) {
    var $existingCards = $wrapper.find('.credit-card-item');
    if ($existingCards.length > 0) {
      $existingCards.each(function(idx, el) {
        var $card = $(el);
        if ($card.data('nominated')) {
          $card.find('.nominate-credit-card').text('Nominate');
          $card.data('nominated', false);
        }
      });
    }
    if (response.auto_pay_invoice) {
      $creditCard.data('nominated', true);
      $link.text('NOMINATED');
    }
  });

  nominate_request.fail(function (response) {
    $creditCard.yordarPOP({
      title: 'Card Error',
      innerContent: 'Sorry couldn\'t nominate the card. Please try again!',
      cancel: false
    });
  });
}

function updateCreditCardVisibility(field, $creditCard) {
  var data = {};
  data.credit_card = {};
  data.credit_card[field] = false;
  $.ajax({
    type: 'PUT',
    url: $creditCard.data('url'),
    data: data,
    error: function (xhr, status) {
      console.log(status);
      console.log(xhr);
    },
    success: function (xhr, result) {
      $creditCard.remove();
    }
  });
}

