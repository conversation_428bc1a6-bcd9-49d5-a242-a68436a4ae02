const customerFavSuppliers = () => {
  if (document.querySelector('#search-box-catering'))
    document.querySelector('#search-box-catering').addEventListener('keyup', function(e) {searchFavourites(e)});

  if (document.querySelector('#search-box-snacks'))
    document.querySelector('#search-box-snacks').addEventListener('keyup', function(e) {searchFavourites(e)});
}

function searchFavourites(e, category){
  var category = e.target.getAttribute('data-category');
  var searchString = e.target.value.toLowerCase();
  var suppliers = document.querySelectorAll(`.supplier-card__${category}`);
  suppliers.forEach(function(supplier) {
    var supplierName = supplier.querySelector('.supplier-title').innerText.toLowerCase();
    if (supplierName.indexOf(searchString) === -1) {
      supplier.classList.add('hidden');
    } else {
      supplier.classList.remove('hidden');
    }
  });

  // if all supplier cards have been hidden, show no results message and hide scroll bar
  if (suppliers.length === Array.from(suppliers).filter(function(supplier) {
    return supplier.classList.contains('hidden');
  }).length) {
    document.querySelector('.no-results-message').classList.remove('hidden');
  } else {
    document.querySelector('.no-results-message').classList.add('hidden');
  }
}

export default customerFavSuppliers;
