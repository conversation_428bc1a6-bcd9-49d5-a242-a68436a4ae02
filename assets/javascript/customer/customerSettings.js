import axios from 'axios';

export default function customerSettings (el, props) {
  var $el = $(el);
  bindCustomerSettingEvents($el);
}

function bindCustomerSettingEvents($wrapper) {
  $wrapper.on('change', 'input[name="avatar_uploader"]', function(e) { uploadCustomerImage(e, $wrapper) });
};

function uploadCustomerImage (event, $wrapper) {
  var $input = $(event.currentTarget);
  var $button = $wrapper.find('.avatar-upload-button');
  var newFile = event.target.files[0];

  if (!newFile)
    return

  var uploadUrl = $input.data('upload-url');
  var uploadPreset = $input.data('upload-preset');

  const imageData = new FormData();
  imageData.append('file', newFile);
  imageData.append('upload_preset', uploadPreset);

  $button.text('Uploading...');
  $button.attr('disabled', true);

  let request = axios({
    method: 'POST',
    url: uploadUrl,
    data: imageData,
  })
  .then((response) => {
    var imageUrl = response.data.secure_url;
    if (imageUrl) {
      var filename = imageUrl.split('upload/')[1];
      var $avatarField = $wrapper.find(`input[name="${$input.data('target-name')}"]`);
      $avatarField.val(filename);

      // show image
      var $imageContainer = $wrapper.find('.supplier-avatar-container');
      var img = document.createElement('img');
      img.src = imageUrl;
      $imageContainer.html(img);
    } else {
      showUploadError($wrapper);
    }
    $button.text($button.data('button-text'));
    $button.attr('disabled', false);
  })
  .catch((error) => {
    showUploadError($wrapper);
    $button.text($button.data('button-text'));
    $button.attr('disabled', false);
  })
}
