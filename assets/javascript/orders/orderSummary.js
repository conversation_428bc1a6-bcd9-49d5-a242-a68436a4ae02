var MAX_QUANTITY = 9999; // overridden according to the menu item
var BASE_ORDER_LINE_URL = '/api/order_lines';
var BASE_ORDER_URL = '/api/orders';

var delay = (function(){
  var timer = 0;
  return function(callback, ms){
    clearTimeout (timer);
    timer = setTimeout(callback, ms);
  };
})();

export default function customerOrderSummary (el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents ($wrapper, props) {
  $wrapper.on('focus', '.order-line-item .item-qty', function (e) { selectQuantity(e) });
  $wrapper.on('click', '.order-line-item .toggle-quantity', function (e) { toggleQuantity(e) });
  $wrapper.on('change', '.order-line-item .item-qty', function (e) { orderLineQuantityChanged(e) });
  $wrapper.on('change', '.order-line-item .order-line-note', function (e) { orderLineNoteChanged(e) });
  $wrapper.on('click', '.order-line-item .delete-icon', function (e) { removeOrderLine(e) });

  $wrapper.on('click', '.apply-coupon', function(e) { applyCoupon(e) });
  $wrapper.on('click', '.delivery-window-day', function(e)  { toggleDeliveryWindowDay(e) })
  $wrapper.on('click', '.woolworths-delivery-window', function (e) { selectDeliveryWindow(e) });

  $wrapper.on('click', '.checkout-btn', function (e) { checkoutOrder(e, $wrapper) });
}

function selectQuantity (event) {
  var $quantityField = $(event.currentTarget);
  $quantityField.select();
}

function toggleQuantity(event) {
  var $toggleEl = $(event.currentTarget);
  var toggleState = $toggleEl.data('toggle');
  var $quantityInput = $toggleEl.siblings().closest('.item-qty');

  var currentVal = parseInt($quantityInput.val());
  if (isNaN(currentVal)) {
    $quantityInput.val(0);
    currentVal = 0;
  }
  var maxQuantity = $quantityInput.prop('max') || MAX_QUANTITY;
  if (toggleState == 'increase' && currentVal < maxQuantity) {
    $quantityInput.val(currentVal + 1);
  } else if (toggleState == 'decrease' && currentVal > 0) {
    $quantityInput.val(currentVal - 1);
  }
  delay(function () {
    $quantityInput.trigger('change');
  }, 300);
}

function validateQuantity ($quantityInput) {
  var quantity = parseInt($quantityInput.val());
  var maxQuantity = $quantityInput.prop('max') || MAX_QUANTITY;
  if (isNaN(quantity) || quantity < 0) {
    $quantityInput.val(0);
  } else if (quantity > maxQuantity) {
    alert(`Cannot add more than ${maxQuantity}`);
    $quantityInput.val(maxQuantity);
  }
}

function orderLineQuantityChanged (event) {
  var $quantityEl = $(event.currentTarget);
  validateQuantity($quantityEl);
  var quantity = $quantityEl.val();
  var $orderLine = $quantityEl.parents('.order-line-item');
  $orderLine.addClass('processing');
  $quantityEl.attr('disabled', true);
  if (quantity == 0 || quantity == '') {
    deleteOrderLine($orderLine, $quantityEl);
  } else {
    updateOrderLine($orderLine, $quantityEl);
  }
}

function orderLineNoteChanged (event) {
  var $noteEl = $(event.currentTarget);
  var note = $noteEl.val();
  if (note != $noteEl.data('prev-value')) {
    var $orderLine = $noteEl.parents('.order-line-item');
    $orderLine.addClass('processing');
    $noteEl.attr('disabled', true);
    updateOrderLine($orderLine, $noteEl);
  }
}

function updateOrderLine($orderLine, $input) {
  var saveData = {};
  saveData.location_id = $orderLine.parents('.table-order-location').data('location-id');
  saveData.order_id = $orderLine.parents('.table-order').data('order-id');
  saveData.id = $orderLine.data('order-line-id');
  var field = $input.attr('name');
  saveData[field] = $input.val();

  var save_request = $.ajax({
    url: BASE_ORDER_LINE_URL + '/' + saveData.id,
    type: 'PUT',
    data: saveData,
    dataType: 'JSON',
  })

  save_request.fail(function (err) {
    alert("Sorry we couldn't update the item in cart");
    $input.val($input.data('prev-value'));
  })

  save_request.then(function (response) {
    if (field == 'quantity') {
      $orderLine.find('.order-line-total').text(response.order_line.line_total);
      updateTotals(response);
      updateHeader(response);
    }
    $input.data('prev-value', saveData[field]);
  })

  save_request.always(function () {
    $orderLine.removeClass('processing');
    $input.attr('disabled', false);
  })
}

function removeOrderLine (event) {
  var $orderLine = $(event.currentTarget).parents('.order-line-item');
  deleteOrderLine($orderLine);
}

function deleteOrderLine($orderLine, $quantityEl) {
  var removeData = {};
  removeData.location_id = $orderLine.parents('.table-order-location').data('location-id');
  removeData.order_id = $orderLine.parents('.table-order').data('order-id');
  removeData.id = $orderLine.data('order-line-id');
  $orderLine.addClass('processing');
  if ($quantityEl) {
    $quantityEl.attr('disabled', true);
  }

  var save_request = $.ajax({
    url: `${BASE_ORDER_LINE_URL}/${removeData.id}`,
    type: 'DELETE',
    data: removeData,
    dataType: 'JSON',
  });

  save_request.fail(function (response) {
    if (response.responseJSON && response.responseJSON.errors) {
      var errors = response.responseJSON.errors;
      $orderLine.yordarPOP({
        container: '#yordarPopUp',
        title: 'Removal Error',
        innerContent: errors.join('<br>'),
        cancel: false,
        submit: 'Ok',
      });
    } else {
      alert("Sorry we couldn't remove the item from cart");
    }
    $orderLine.removeClass('processing');
    if ($quantityEl) {
      $quantityEl.val($quantityEl.data('prev-value'));
      $quantityEl.attr('disabled', false);
    }
  });

  save_request.then(function (response) {
    $orderLine.remove();
    updateTotals(response);
    updateHeader(response);
  });
}

function updateTotals(response) {
  var order = response.order;
  var totals = order.totals
  var $currentOrder = $(`[data-order-id='${order.id}']`);
  var $footer =  $currentOrder.find('.table-order__footer');
  $footer.find('.order-subtotal').text(toCurrency(totals.subtotal));
  $footer.find('.order-delivery').text(toCurrency(totals.delivery));
  $footer.find('.order-discount').text(toCurrency(toNum(totals.discount)*-1 ));
  $footer.find('.order-gst').text(toCurrency(totals.gst));
  $footer.find('.order-total').text(toCurrency(totals.total));

  if (totals.topup && totals.topup != '$0.00') {
     $footer.find('.order-topup').text(toCurrency(totals.topup));
     $footer.find('.topup-wrapper').removeClass('hidden');
  } else {
    $footer.find('.topup-wrapper').addClass('hidden');
  }

  if (totals.surcharge && totals.surcharge != '$0.00') {
     $footer.find('.order-surcharge').text(toCurrency(totals.surcharge));
     $footer.find('.surcharge-wrapper').removeClass('hidden');
  } else {
    $footer.find('.surcharge-wrapper').addClass('hidden');
  }

  if ($currentOrder.find('.level-total')) {
    getLevelTotal($currentOrder);
  }
  show_discount(order.discount);
}

function show_discount(discount) {
  if (discount && parseInt(toNum(discount)) > 0) {
    $('tr.discount_wrapper').removeClass('hidden');
  } else {
    $('tr.discount_wrapper').addClass('hidden');
  }
}

function updateHeader(response) {
  var order = response.order;
  var $headerCounter = $('.shopcart-item-counter');
  if ($headerCounter.length > 0) {
    $headerCounter.text(order.order_line_count);
    $headerCounter.removeClass('hidden');
  }
}

function getLevelTotal($currentOrder) {
  var level_total = 0;
  $currentOrder.find('.price-field > span',).each(function () {
    level_total += toNum($(this).text());
  });
  if ($currentOrder) {
    return $currentOrder.find('.level-total').text(toCurrency(level_total));
  }
}

function toNum(val) {
  if (val === undefined) {
    return 0.0;
  }
  val = val.toString().replace(/[^0-9\-\.]/g, ''); // force to be a string and remove non digits
  return parseFloat(val);
}

function toCurrency(val) {
  return `$ ${toNum(val).toFixed(2)}`;
}

function applyCoupon (event) {
  event.preventDefault();
  var $couponWrapper = $(event.currentTarget).parents('.coupon-wrapper');
  var $couponInput = $couponWrapper.find('input[name=coupon]');
  var coupon_code = $.trim($couponInput.val());
  var $couponMessage = $couponWrapper.find('.coupon-message');
  $couponMessage.text('').removeClass('error');
  if (coupon_code) {
    var $order = $couponWrapper.parents('.table-order');

    var data = {}
    data.order = { coupon_code: coupon_code };

    var coupon_request = $.ajax({
      url: `${BASE_ORDER_URL}/${$order.data('order-id')}`,
      dataType: 'JSON',
      type: 'PUT',
      data: data,
    });

    coupon_request.done(function(response) {
      $couponMessage.text('Coupon applied');
      updateTotals(response);
    });

    coupon_request.fail(function(response) {
      $couponMessage.text(response.responseJSON.errors.join('. '));
      $couponMessage.addClass('error');
    });
  } else {
    // alert('cannot apply a blank coupon');
  }
}

function checkoutOrder (event) {
  event.preventDefault();
  var $submitButton = $(event.currentTarget);
  if ($('.order-line-item').length < 1) {
    $submitButton.yordarPOP({
      title: 'Checkout Error',
      innerContent: 'Your order is currently empty.',
      cancel: false
    });

  } else if ($submitButton.data('validate-order-url')) {
    validateOrder($submitButton);
  } else if ($submitButton.data('min-spend-url')) {
    checkMinimumSpend ($submitButton);
  } else {
    $submitButton.html(spinner_html);
    window.location = $submitButton.attr('data-next-url');
  }
}

function validateOrder ($submitButton) {
  $submitButton.html(spinner_html);
  var valiation_request = $.ajax({
    url: $submitButton.data('validate-order-url'),
    type: 'GET'
  });
  valiation_request.fail(function(response) {
    $submitButton.html($submitButton.data('button-text'));
    var errorMessages = '';
    var warningMessages = '';
    var validationErrors = response.responseJSON ? response.responseJSON.errors : [];
    var validationWarnings = response.responseJSON ? response.responseJSON.warnings : [];

    if (validationErrors && validationErrors.length) {
      errorMessages = '<p class="mb-0">The order has the following errors:</p>';
      validationErrors.forEach((error) => {
        errorMessages += `<p><strong>${error.name}</strong><br/><span class="error-msg">${error.message}</span></p>`;
      });
      errorMessages += '<p><strong>Remove errorneous items to continue with order!</strong></p>';
    }

    if (validationWarnings && validationWarnings.length) {
      warningMessages = '<p class="mb-0">Please check the following within the items:</p>';
      validationWarnings.forEach((warning) => {
        warningMessages += `<p><strong>${warning.name}</strong><br/>${warning.message}</p>`;
      });
    }

    var validationMessage = errorMessages + warningMessages;
    
    $submitButton.yordarPOP({
      title: `Order Validation${errorMessages ? ' Failure' : ''}`,
      innerContent: validationMessage,
      submit: errorMessages ? false : 'CONTINUE',
      cancel: 'CLOSE',
      submitHandler: function(e) {
        checkMinimumSpend ($submitButton);
      }
    });
    $submitButton.text($submitButton.data('button-text'));
  });
  valiation_request.done(function (response) {
    checkMinimumSpend ($submitButton);
  });
}

function checkMinimumSpend ($submitButton) {
  $submitButton.html(spinner_html);
  var minimum_spend_request = $.ajax({
    url: $submitButton.data('min-spend-url'),
    type: 'GET'
  });
  minimum_spend_request.fail(function() {
    $submitButton.html($submitButton.data('button-text'));
    $submitButton.yordarPOP({
      title: 'Opps! Something went wrong.',
      innerContent: 'We are having some trouble checking the minimum spend for the order. Please try again.',
      cancel: false
    });
    $submitButton.text($submitButton.data('button-text'));
  });
  minimum_spend_request.done(function (response) {
    if (response.is_under) {
      $submitButton.html($submitButton.data('button-text'));
      showMinimumSpendErrorMessage (response, $submitButton)
    } else {
      window.location = $submitButton.attr('data-next-url');
    }
  });
}

function showMinimumSpendErrorMessage (response, $submitButton) {
  var display_msg = "You've ordered below the minimum order value for the following supplier(s): <br/><br/>";

  $.each(response.suppliers_min_spends, function (index, order_spend) {
    if (order_spend.is_under) {
      if (!response.is_single_order) {
        display_msg += `Order for: ${order_spend.order_name.toUpperCase()} <br/>`;
      }
      $.each(order_spend.supplier_spends, function (index, supplier_spend) {
        if (supplier_spend.is_under) {
          display_msg += `${supplier_spend.supplier_name}: ${supplier_spend.minimum_spend} (remaining: ${supplier_spend.remaining_spend})`;
          display_msg += '.<br/>';
        }
      });
      display_msg += '<br/>';
    }
  });

  display_msg += 'We recommend you add more products to your cart to <strong>prevent the supplier(s) from rejecting the order.</strong>';
  $submitButton.text($submitButton.data('button-text'));
  var canChargeToMinimum = $submitButton.data('min-charge-url');
  $submitButton.yordarPOP({
    mainClass: 'min-spend-warning',
    container: '#yordarPopUp',
    innerContent: display_msg,
    title: 'Below Supplier Minimum Spend',
    cancel: 'Add more items',
    submit: 'Continue',
    submitHandler: function (event) {
      $submitButton.html(spinner_html);
      window.location = $submitButton.attr('data-next-url');
    },
    alternate: canChargeToMinimum ? 'Top Me Up to Minimum' : '',
    alternateHandler: function(event) {
      if (canChargeToMinimum) {
        chargeToMinimum(event, $submitButton)
      }
    }
  });
}

function chargeToMinimum (event, $button) {
  var data = {};
  data.order = { charge_to_minimum: true };

  var deliveryChangeRequest = $.ajax({
    url: $button.data('min-charge-url'),
    dataType: 'JSON',
    type: 'PUT',
    data: data,
  });

  deliveryChangeRequest.done(function(response) {
    updateTotals(response);
  });

  deliveryChangeRequest.fail(function(response) {
    alert("Sorry we couldn't charge the order");
  });
}

function selectDeliveryWindow (event) {
  event.preventDefault();
  var $selectedWindow = $(event.currentTarget);
  var $order = $selectedWindow.parents('.table-order');
  $selectedWindow.addClass('processing');

  var woolworths_order_id = $selectedWindow.data('woolworths-order-id');
  var window_id = $selectedWindow.data('delivery-window-id');
  var delivery_at = $selectedWindow.data('delivery-at');
  var data = {};
  data.order = {
    delivery_at: (delivery_at ? delivery_at : null),
  }
  if (woolworths_order_id) {
    data.order.associated_woolworths_order_attributes = {
      id: woolworths_order_id,
      delivery_window_id: (window_id ? window_id : null)
    };
  }
  if (!window_id) {
    $selectedWindow.text('Fetching Available Delivery Windows...');
    $selectedWindow.attr('disabled', true);
  }

  var delivery_request = $.ajax({
    url: `${BASE_ORDER_URL}/${$order.data('order-id')}`,
    dataType: 'JSON',
    type: 'PUT',
    data: data,
  });

  delivery_request.done(function(response) {
    $order.find('.woolworths-delivery-window').removeClass('selected');
    if ($selectedWindow.data('reload')) {      
      window.location = window.location;
    } else {
      $selectedWindow.addClass('selected');
    }
  });

  delivery_request.fail(function(response) {
    var error_message;
    if (window_id) {
      error_message = 'We were unable to select the delivery window. Please try again!';
    } else {
      $selectedWindow.text($selectedWindow.data('button-text'));
      $selectedWindow.attr('disabled', false);
      error_message = 'We were unable to fetch available delivery windows. Please try again!';
    }
    $selectedWindow.yordarPOP({
      container: '#yordarPopUp',
      title: 'Delivery Windows Error',
      innerContent: error_message,
      cancel: false,
      submit: 'Ok',
    });
  });
}

function toggleDeliveryWindowDay (event) {
  var $deliveryDays = $('.delivery-window-day');
  var $deliveryDayPanels = $('.delivery-window-day-panel');
  $deliveryDays.removeClass('active');
  $deliveryDayPanels.removeClass('active');

  var $currentDay = $(event.currentTarget);
  var $currentDayPanel = $(`.delivery-window-day-panel${$currentDay.attr('id')}`);
  $currentDay.addClass('active');
  $currentDayPanel.addClass('active');
}
