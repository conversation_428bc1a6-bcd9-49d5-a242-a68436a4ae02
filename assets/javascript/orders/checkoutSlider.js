export default function checkoutSlider (el, props) {
  const $el = $(el)
  bindEvents($el, props);
  showFirstLocation();
}

function bindEvents ($wrapper, props) {
  $wrapper.on('click', '.collapsible', function(e) { toggleCollapsible(e) })
  $wrapper.on('click', '.table-order__order-button', function(e) { onChangeOrder(e) })
}

function toggleCollapsible(event) {
  $(event.currentTarget).toggleClass("spin");
  var content = event.currentTarget.nextElementSibling;
  if (content.style.maxHeight) {
    content.style.maxHeight = null;
  } else {
    content.style.maxHeight = content.scrollHeight + "px";
  }
}

function showFirstLocation() {
  var first_locations = document.getElementsByClassName("first-location");
  for (var i = 0; i < first_locations.length; i++) {
    $(first_locations[i]).prev().toggleClass("spin");
    first_locations[i].style.maxHeight = first_locations[i].scrollHeight + "px";
  }
}

function onChangeOrder (event) {
  var button = event.currentTarget
  var order_buttons = document.querySelectorAll(".table-order__order-button");
  if (order_buttons) {
    var tables = document.querySelectorAll(".customer-order__orders");
    var order_selection = document.querySelectorAll(".order-" + button.id);
    for (var i = 0; i < order_buttons.length; i++) {
      order_buttons[i].classList.remove("table-order__active-order-button");
    }
    button.classList.add("table-order__active-order-button");
    hideAll(tables);
    showSelection(order_selection);
    showFirstLocation();
  }
}

function hideAll (elements) {
  for (var i = 0; i < elements.length; i++) {
    $(elements[i]).addClass("hidden");
  }
}
function showSelection (elements) {
  for (var i = 0; i < elements.length; i++) {
    $(elements[i]).removeClass("hidden");
  }
}
