export default function customerMultiLevelOrderSummary(el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents($wrapper, props) {
  $wrapper.on('click', '.table-order__order-button', function (e) {
    changeOrder(e);
  });
  $wrapper.on('change', '.table-order__location-select', function (e) {
    changeLocation(e);
  });

  const first_order_id = $wrapper.find('.table-order').data('order-id');
  updateLocationTotals(first_order_id);
}

function updateLocationTotals(order_id, hide_totals = false) {
  let location_total = 0;
  const order = document.querySelector(`#order-${order_id}`);
  $('.price-field > span:visible').each(function () {
    location_total += toNum($(this).text());
  });
  const location_total_el = order.querySelector('.location-total');
  if (location_total_el) {
    const location_total_wrapper = order.querySelector('.location-total-wrapper');
    location_total_el.innerText = toCurrency(location_total);
    if (hide_totals) {
      location_total_wrapper.classList.add('hidden');
    } else {
      location_total_wrapper.classList.remove('hidden');
    }
  }
}

function addLocationSelectListeners() {
  const location_select = document.querySelectorAll('.table-order__location-select');
  for (let i = 0; i < location_select.length; i++) {
    location_select[i].addEventListener('change', onChangeLocation);
  }
}

function changeOrder(event) {
  const button = event.currentTarget;
  if (!button.id) {
    return;
  }
  const order_buttons = document.querySelectorAll('.table-order__order-button');
  const orders = document.querySelectorAll('.table-order');
  const locations = document.querySelectorAll('.table-order__details');
  const location_headers = document.querySelectorAll('.table-order-location-all');
  const selected_order = document.querySelector(`#order-${button.id}`);
  for (let i = 0; i < order_buttons.length; i++) {
    order_buttons[i].classList.remove('table-order__active-order-button');
  }
  const currentLocationSelector = document.querySelector('.table-order:not(.hidden)').querySelector('select');
  const selected_location = currentLocationSelector ? currentLocationSelector.value : null;
  button.classList.add('table-order__active-order-button');
  hideAll(orders);
  showSelection(selected_order);

  const location_select_box = selected_order.querySelector('select.table-order__location-select');
  const first_location = selected_order.querySelector('.table-order-location');
  hideAll(locations);
  hideAll(location_headers);
  if (location_select_box) {
    if (selected_location && selected_location == 'all') {
      location_select_box.value = selected_location;
    } else {
      location_select_box.value = first_location.attributes['data-location-id'].value;
    }
    $(location_select_box).trigger('change');
    // location_select_box.dispatchEvent(new Event("change"))
  } else {
    first_location.classList.remove('hidden');
  }
}

function toNum(val) {
  val = val.toString().replace(/[^0-9\.]/g, ''); // force to be a string and remove non digits
  return parseFloat(val);
}

function toCurrency(val) {
  return `$ ${val.toFixed(2)}`;
}

function changeLocation(event) {
  const locationSelect = event.currentTarget;
  const $order = $(event.currentTarget).parents('.table-order');
  const location_headers = $order[0].querySelectorAll('.table-order-location-all');
  const locations = document.querySelectorAll('.table-order__details');

  hideAll(locations);
  hideAll(location_headers);
  if (locationSelect.value == 'all') {
    showSelection(locations);
    showSelection(location_headers);
    updateLocationTotals($order.data('order-id'), true);
  } else {
    const selected_location = document.querySelectorAll(`.location-${locationSelect.value}`);
    showSelection(selected_location);
    updateLocationTotals($order.data('order-id'));
  }
}

function hideAll(elements) {
  for (let i = 0; i < elements.length; i++) {
    $(elements[i]).addClass('hidden');
  }
}

function showSelection(elements) {
  elements = [elements].flat();
  for (let i = 0; i < elements.length; i++) {
    $(elements[i]).removeClass('hidden');
  }
}
