const CARD_BRANDS = {
  visa: {
    percent: '1.75%',
    fee: '30c',
    label: 'Visa',
    icon: 'visa_icon',
  },
  mastercard: {
    percent: '1.75%',
    fee: '30c',
    label: 'MasterCard',
    icon: 'mastercard_icon',
  },
  amex: {
    percent: '2.9%',
    fee: '30c',
    label: 'American Express',
    icon: 'amex_icon',
  },
  diners: {
    percent: '2.9%',
    fee: '30c',
    label: "Diner's Club",
    icon: 'diners-club_icon',
  },
};

export function bindStripeCardToForm($form, $wrapper) {
  const elements = window.stripe.elements();
  const elementStyles = {
    base: {
      color: '#32325D',
      fontWeight: 500,
      fontFamily: 'Museo Sans, Helvetica, Roboto, Arial, sans-serif', // 'Source Code Pro, Consolas, Menlo, monospace',
      fontSize: '16px',
      fontSmoothing: 'antialiased',

      '::placeholder': {
        color: '#CFD7DF',
      },
      ':-webkit-autofill': {
        color: '#e39f48',
      },
    },
    invalid: {
      color: '#E25950',

      '::placeholder': {
        color: '#FFCCA5',
      },
    },
  };

  const elementClasses = {
    focus: 'focused',
    empty: 'empty',
    invalid: 'invalid',
  };

  // set card number element
  const cardNumber = elements.create('cardNumber', {
    style: elementStyles,
    classes: elementClasses,
  });
  const numberId = $form.find('.card-number').attr('id');
  cardNumber.mount(`#${numberId}`);

  // set card expiry dates element
  const cardExpiry = elements.create('cardExpiry', {
    style: elementStyles,
    classes: elementClasses,
  });
  const expiryId = $form.find('.card-expiry').attr('id');
  cardExpiry.mount(`#${expiryId}`);

  // set card cvc element
  const cardCvc = elements.create('cardCvc', {
    style: elementStyles,
    classes: elementClasses,
  });
  const cvcId = $form.find('.card-cvc').attr('id');
  cardCvc.mount(`#${cvcId}`);

  const cardElements = [cardNumber, cardExpiry, cardCvc];

  const $chargeNote = $wrapper.find('#new-credit-card-charge-note');
  if ($chargeNote.length) {
    cardNumber.on('change', function (event) {
      displayChargeNote(event.brand, $chargeNote);
    });
  }
  $form.data('card-elements', cardElements);
}

function displayChargeNote(brand, $chargeNote) {
  const current_card = CARD_BRANDS[brand];
  var card_type;
  let card_charge;
  if (current_card) {
    var card_type = current_card.label;
    var card_percent = current_card.percent;
  }
  if (card_type && card_percent) {
    $chargeNote.find('.card-type').text(card_type);
    $chargeNote.find('.card-charge').text(card_percent);
    $chargeNote.find('.card-fee').text(current_card.fee);
    $chargeNote.removeClass('hidden');
  } else {
    $chargeNote.addClass('hidden');
  }
}

export function submitStripeCard($form, $wrapper, callback) {
  const cardElements = $form.data('card-elements');
  const $submitButton = $(document).find('[data-credit-card-button]');
  if (typeof cardElements === 'undefined') {
    $form.yordarPOP({
      title: 'Card Error',
      innerContent: "Couldn't save the card details! Please try again.",
      cancel: false,
    });
    if ($submitButton.length) {
      $submitButton.text($submitButton.data('credit-card-button'));
    }
    return;
  }
  const cardholder_name = $form.find('input[name="credit_card[name]"]').val();
  if (cardholder_name !== '') {
    const cardholder_email = $form.find('input[name=customer_email]').val();
    const stripe_payment = window.stripe.createPaymentMethod({
      type: 'card',
      card: cardElements[0],
      billing_details: {
        name: cardholder_name,
        email: cardholder_email,
      },
    });
    stripe_payment.then(function (response) {
      if (response.error) {
        $form.yordarPOP({
          title: 'Card Error',
          innerContent: response.error.message,
          cancel: false,
        });
        if ($submitButton.length) {
          $submitButton.text($submitButton.data('credit-card-button'));
        }
      } else {
        saveStripeCard($form, $wrapper, response.paymentMethod, callback);
      }
    });
  } else {
    $form.yordarPOP({
      title: 'Card Error',
      innerContent: 'Credit card requires a cardholder name',
      cancel: false,
    });
    if ($submitButton.length) {
      $submitButton.text($submitButton.data('credit-card-button'));
    }
  }
}

// found this at https://stackoverflow.com/questions/11338774/serialize-form-data-to-json#answer-11339012
// plus some changes for nested nodes
// might use this in other places
function serialzedFormData($form) {
  const unindexed_params = $form.serializeArray();
  const indexed_params = {};
  $.map(unindexed_params, function (node, i) {
    const nested_node = node.name.match(/(.*)\[(.*)\]/);
    if (nested_node) {
      if (!indexed_params[nested_node[1]]) {
        indexed_params[nested_node[1]] = {};
      }
      indexed_params[nested_node[1]][nested_node[2]] = node.value;
    } else {
      indexed_params[node.name] = node.value;
    }
  });
  return indexed_params;
}

function saveStripeCard($form, $wrapper, paymentMethod, callback) {
  var $submitButton = $(document).find('[data-credit-card-button]');
  const formData = serialzedFormData($form);
  formData.wants_html = $form.data('context');

  const $invoice = $(document).find('input[name="payment[invoice_uuid]"]');
  if ($invoice.length > 0) {
    formData.invoice_id = $invoice.val();
  }

  // stripe data
  const cardData = formData.credit_card;
  cardData.last4 = paymentMethod.card.last4;
  cardData.brand = paymentMethod.card.brand;
  cardData.country_code = paymentMethod.card.country;
  cardData.expiry_month = paymentMethod.card.exp_month;
  cardData.expiry_year = paymentMethod.card.exp_year;
  cardData.stripe_token = paymentMethod.id;

  $form.find('input:not([type=hidden]), select').attr('disabled', 'disabled');
  var $submitButton = $form.find('input[type=submit]');

  let buttonText = '';
  if ($submitButton.length) {
    buttonText = $submitButton.text();
    $submitButton.html(spinner_html);
  }

  const submit_request = $.ajax({
    url: $form.attr('action'),
    type: $form.data('form-method'),
    dataType: 'JSON',
    data: formData,
  });

  submit_request.done(function (response) {
    $form.find('input:not([type=hidden]), select').val('');
    const cardElements = $form.data('card-elements');
    $(cardElements).each(function (idx, element) {
      element.clear();
    });
    callback(response, $form, $wrapper);
  });

  submit_request.fail(function (response) {
    const { errors } = response.responseJSON;
    const display_msg = errors.join('<br />');
    $form.yordarPOP({
      title: 'Card Error',
      innerContent: display_msg,
      cancel: false,
    });
    if ($submitButton.length) {
      $submitButton.text($submitButton.data('credit-card-button'));
    }
  });
  submit_request.always(function () {
    $form.find('input:not([type=hidden]), select').attr('disabled', false);
    if ($submitButton.length) {
      $submitButton.text(buttonText);
    }
  });
}
