export default function newOrderReview (el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents ($wrapper, props) {
  $wrapper.on('click', '.view-review-order-lines', function(e) { toggleOrderLines(e, $wrapper) });
  $wrapper.on('click', '.stars li', function(e) { setRating(e) });
  $wrapper.on('click', '.submit-review', function(e) { submitReview(e) });
}

function toggleOrderLines (event, $wrapper) {
  $wrapper.find('.review-order-lines').toggleClass('hidden');
}

function setRating (event) {
  var $star = $(event.currentTarget);
  var currentRating = $star.data('rating');
  var $ratings = $star.parents('.stars');

  for (var i = 1; i <= 5; i++) {
    var $rating = $ratings.find(`.star[data-rating=${i}]`);
    if (currentRating >= i) {
      $rating.addClass('selected');
    } else {
      $rating.removeClass('selected');
    }
  }

  var $field = $ratings.find('input[type=hidden]');
  $field.val(currentRating);
}

function submitReview (event) {
  event.preventDefault();
  var $button = $(event.currentTarget);
  var $form = $button.parents('form');
  
  var foodRating = $form.find('input[name="order_review[food_taste_score]"]').val();
  var presentationRating = $form.find('input[name="order_review[presentation_score]"]').val();
  var puctualityRating = $form.find('input[name="order_review[delivery_punctuality_score]"]').val();

  if ( foodRating && presentationRating && puctualityRating ) {
    $form.submit();
  } else {
    $form.yordarPOP({
      title: 'Ratings Required',
      innerContent: 'We need ratings for each field to submit the review.',
      cancel: false
    });
  }
}

