import { submitStripeCard } from 'javascript/stripe/handleStripeForm';

export default function invoicePayment(el, props) {
  const $el = $(el);
  bindEvents($el, props);
}

function bindEvents($wrapper, props) {
  $wrapper.on('click', '#pay-invoice-btn', function (e) {
    payInvoice(e, $wrapper);
  });
}

function payInvoice(event, $wrapper) {
  event.preventDefault();
  const $payInvoiceButton = $(event.currentTarget);
  const $form = $wrapper.find('form.new-credit-card');

  $payInvoiceButton.html(spinner_html);
  submitStripeCard($form, $wrapper, selectStripeCardAndSubmit);
}

function selectStripeCardAndSubmit(response, $form, $wrapper) {
  const $payInvoiceButton = $wrapper.find('#pay-invoice-btn');
  $payInvoiceButton.html(spinner_html);

  const $invoiceForm = $wrapper.find('#invoice-payment-form');
  $invoiceForm.find('input[name="payment[credit_card_id]"]').val(response.id);
  $invoiceForm.submit();
}
