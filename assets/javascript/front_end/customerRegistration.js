import { recaptchaSuccess } from 'utilities/plugins/recaptcha.js';

const LEAD_FIELDS = ['firstname', 'lastname', 'email']

export default function customerRegistration (el, props) {
  window.formValid = false;
  window.captchaValid = false;
  window.recaptchaSuccess = recaptchaSuccess; // expose recaptchaSuccess

  const $el = $(el);
  const $form = $('form.authorization-form');
  bindEvents($el, $form, props);
}

function bindEvents ($wrapper, $form, props) {
  $form.on('keydown', 'input[name="user[email]"]', function (e) { submitStepOnEnter(e, $form) });
  $form.on('click', '#continue-with-email', function (e) { submitStep1(e, $form) })
  $form.find('input').on('keyup', function () { validate($form) });
  $form.on('submit', function (e) { submitRegistration(e, $form) });
}

function submitStepOnEnter(event, $form) {
  if (event.which == 13 || event.keyCode == 13) {
    event.preventDefault();
    $form.find('#continue-with-email').click();
  }
}

// submit step 1
function submitStep1(event, $form) {
  var button = $(event.currentTarget);
  var $errors = $form.find('.step-1-errors');
  var $passwordReset = $form.find('.password-reset');
  var $cardTitle = $('.auth-card__title');
  var $step = $('.auth-card__step');
  $errors.addClass('hidden');
  $passwordReset.addClass('hidden');
  if (isValidForm($form)) {
    setInputsToRequired();
    let serializedValues = $form.serializeArray();
    let leadData = { lead_type: 'started_registration' };
    LEAD_FIELDS.forEach((field) => {
      leadData[field] = serializedValues.find(({name}) => name == `user[${field}]`).value;
    })
    $.ajax({
      url: button.data('url'),
      type: 'POST',
      data: { lead: leadData },
      dataType: 'JSON',
    }).then(function (response) {
      $('#register-step-1').addClass('hidden');
      $('#register-step-2').removeClass('hidden');
      $cardTitle.text('Setup Your Account');
      $step.text('Step 2/2');
    }).fail(function (err) {
      var errors = err.responseJSON.errors.join('. ');
      $errors.text(errors).removeClass('hidden');
      if (errors === 'A user with that email already exists!') {
        $passwordReset.removeClass('hidden');
      }
    })
  }
}

function setInputsToRequired() {
  $('input.required, select.required').prop('required', true);
}

function isValidForm($form) {
  $form.foundation('validateForm');
  return $form.find('[data-invalid]').length === 0;
}

function validate($form) {
  var inputsWithValues = 0;

  // get all input fields except for type='submit'
  var myInputs = $form.find('input.required:not([type="submit"]):not([type="hidden"])');
  myInputs.each(function (e) {
    // if it has a value, increment the counter
    if ($(this).val()) {
      inputsWithValues += 1;
    }
  });

  var validInputs = inputsWithValues == myInputs.length;
  // console.log('validInputs '+ validInputs);
  if (validInputs) {
    window.formValid = true;
    window.captchaValid && $('#sign-up-button').prop('disabled', false);
  } else {
    window.formValid = false;
    $('#sign-up-button').prop('disabled', true);
  }
}

function submitRegistration (event, $form) {
  event.preventDefault();

  validate($form);

  if (!isValidForm($form)) return;
  
  var $errors = $form.find('.step-2-errors');
  $errors.addClass('hidden');
  

  const registerRequest = $.ajax({
    url: $form.attr('action'),
    type: 'POST',
    data: $form.serialize(),
    dataType: 'JSON',
  });

  registerRequest.then(function (response) {
    window.location = response.redirect_to
  })

  registerRequest.fail(function (err) {
    var errors = err.responseJSON.errors.join('. ');
    $errors.text(errors).removeClass('hidden');
    if (window.grecaptcha) grecaptcha.reset();
  })
}
