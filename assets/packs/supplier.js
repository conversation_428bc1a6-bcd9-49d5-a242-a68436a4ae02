import { Foundation } from 'foundation-sites';
// Deps
import 'utilities/plugins/yordarPop.js';
import 'utilities/plugins/select2.js';
import 'utilities/spinner_html.js';
import 'utilities/ajax_setup.js';
import 'utilities/common_events.js';

import 'javascript/stripe/config.js';

import domready from 'domready';
import viewloader from 'viewloader';

// Components
import supplierViews from 'loader_views/supplier';
import utilitiyViews from 'loader_views/utilities';
import sharedViews from 'loader_views/shared';

// Kick off
domready(() => {
  const $wrapper = $(document);
  $wrapper.foundation();
  const allViews = { 
    ...supplierViews,
    ...utilitiyViews,
    ...sharedViews,
  };
  const manager = viewloader(allViews);
  // Call the view functions
  manager.callViews();
});
