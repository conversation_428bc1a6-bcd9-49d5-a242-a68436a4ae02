import { Foundation } from 'foundation-sites';
// Deps
import 'utilities/plugins/yordarPop.js';
import 'utilities/plugins/select2.js';
import 'utilities/spinner_html.js';
import 'utilities/ajax_setup.js';
import 'utilities/common_events.js';

import domready from 'domready';
import viewloader from 'viewloader';

// Components
import adminViews from 'loader_views/admin';
import utilityViews from 'loader_views/utilities';

// Kick off
domready(() => {
  const $wrapper = $(document);
  $wrapper.foundation();
  const allViews = {
    ...adminViews,
    ...utilityViews,
  };
  const manager = viewloader(allViews);
  // Call the view functions
  manager.callViews();
});
