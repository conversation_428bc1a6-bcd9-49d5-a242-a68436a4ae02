// require('../css/front_end.scss')

import { Foundation } from 'foundation-sites';
// Deps
import 'utilities/plugins/yordarPop.js';
import 'utilities/plugins/select2.js';
import 'utilities/spinner_html.js';
import 'utilities/ajax_setup.js';
import 'utilities/common_events.js';

import 'javascript/stripe/config.js';

import domready from 'domready';
import viewloader from 'viewloader';

// Components
import frontEndViews from 'loader_views/frontEnd';
import orderViews from 'loader_views/orders';
import stripeViews from 'loader_views/stripe';
import utilitiyViews from 'loader_views/utilities';

// Kick off
domready(() => {
  const $wrapper = $(document);
  $wrapper.foundation();
  const allViews = { 
    ...frontEndViews,
    ...orderViews,
    ...stripeViews,
    ...utilitiyViews,    
  };
  const manager = viewloader(allViews);
  // Call the view functions
  manager.callViews();
});

