import { Foundation } from 'foundation-sites';
// Deps
import 'utilities/plugins/yordarPop.js';
import 'utilities/spinner_html.js';
// import 'utilities/ajax_setup.js';
// import 'utilities/common_events.js';

import 'javascript/stripe/config.js';

import domready from 'domready';
import viewloader from 'viewloader';

// Components
import teamOrderViews from 'loader_views/team_order';
import utilitiyViews from 'loader_views/utilities';
import sharedViews from 'loader_views/shared';

// Kick off
domready(() => {
  const $wrapper = $(document);
  $wrapper.foundation();
  const allViews = { 
    ...teamOrderViews,
    ...utilitiyViews,
    ...sharedViews,
  };
  const manager = viewloader(allViews);
  // Call the view functions
  manager.callViews();
});
