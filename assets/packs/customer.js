import { Foundation } from 'foundation-sites';
// Deps
import 'utilities/plugins/yordarPop.js';
import 'utilities/plugins/select2.js';
import 'utilities/spinner_html.js';
import 'utilities/ajax_setup.js';
import 'utilities/common_events.js';

import 'javascript/stripe/config.js';

import domready from 'domready';
import viewloader from 'viewloader';

// Components
import customerViews from 'loader_views/customer';
import orderViews from 'loader_views/orders';
import stripeViews from 'loader_views/stripe';
import utilityViews from 'loader_views/utilities';
import sharedViews from 'loader_views/shared';

// Kick off
domready(() => {
  const $wrapper = $(document);
  $wrapper.foundation();
  const allViews = {
    ...customerViews,
    ...orderViews,
    ...stripeViews,
    ...utilityViews,
    ...sharedViews,    
  };
  const manager = viewloader(allViews);
  // Call the view functions
  manager.callViews();
});
