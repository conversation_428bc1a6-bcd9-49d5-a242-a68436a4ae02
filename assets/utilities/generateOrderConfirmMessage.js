export default function generateOrderConfirmMessage({
  isRecurringOrder,
  day,
  type,
}) {
  const skipOrCancel = isRecurringOrder ? "skip" : "cancel";
  const messages = {
    "reactivate-one-off":
      "Are you sure you want to reactivate this order only?",
    "reactivate-subsequent": `Are you sure you want to reactivate on hold ${day} orders?`,
    "cancel-one-off": `Are you sure you want to ${skipOrCancel} this order?`,
    "cancel-and-notify": `Are you sure you want to cancel this order? We'll also send a notification to the attendees!`,
    "cancel-subsequent": `Are you sure you want to cancel the ${day} orders for your recurring order?`,
    "cancel-related": `Are you sure you want to cancel all related orders?`,
    "cancel-on-hold": `Are you sure you want to place all related recurring orders on hold?`,
    "cancel-void": `Are you sure you want to Void this order?`,
  };
  return messages[type];
}
