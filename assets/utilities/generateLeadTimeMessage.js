export default function leadTimeMessages({ type, url }) {
  const messages = {
    error: {
      label: "Sorry Something Went Wrong",
      message: `There was an error when checking if this order can be edited within the lead time available. Please try again or contact support if the issue persists.`,
    },
    admin: {
      label: "Order Has Passed It's Lead Time",
      message: `This order has passed it's lead time. As admin you can still manage this order. Do you want to continue?`,
      url,
      redirect: true,
      type: "continuation",
    },
    customer: {
      label: "Problem with Managing This Order",
      message: `Unfortunately this order has passed it's lead time. Please contact support if this is an issue.`,
    },
  };
  return messages[type];
}
