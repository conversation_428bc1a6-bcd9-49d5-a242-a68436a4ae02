import axios from 'axios';
import { apiSuburbsPath } from 'routes';

export const getYordarSuburb = async ({ selectedLocation, countryCode }) => {
  const getParams = { country_code: countryCode };
  if (selectedLocation.postcode) {
    getParams.postcode = selectedLocation.postcode;
  } else {
    getParams.name = selectedLocation.locality;
  }
  if (selectedLocation.countryCode) {
    getParams.country_code = selectedLocation.countryCode;
  }
  getParams.best_matched_to = {
    postcode: selectedLocation.postcode,
    name: selectedLocation.locality,
  };

  const { data: responseSuburbs } = await axios({
    method: 'GET',
    url: apiSuburbsPath({ format: 'json' }),
    params: getParams,
  });

  if (responseSuburbs.length) {
    return responseSuburbs[0];
  }

  return null;
};

export const fetchSuburbByTerm = async ({ term, countryCode }) => {
  countryCode ||= 'AU';
  const { data: responseSuburbs } = await axios({
    method: 'GET',
    url: apiSuburbsPath({ format: 'json' }),
    params: { term, country_code: countryCode },
  });

  return responseSuburbs;
};
