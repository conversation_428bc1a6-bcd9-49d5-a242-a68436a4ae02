// this is a simple popup wrapper for
// bootstrap modal, works with partial _popup.html.erb

(function($){

  /**
   * popup() is a quick helper function to load bootstrap popup.
   * Parameters (See defaults)..
   */
  $.fn.yordarPOP = function(options) {

    var defaults = {
      mainClass: "",
      container: '#yordarPopUp',
      title : 'Yordar',
      divClass : 'popup',
      innerContent: '',
      cancel: 'Cancel',     // set to false to hide the cancel button
      submit: 'Ok',
      alternate: '',
      submitHandlerData: {},
      submitHandler: {},
      cancelHandlerData: {},
      cancelHandler: {},
      alternateHandler: {},
      alternateHandlerData: {},
    };

    // === merge the options with default settings ===
    options = $.extend(defaults, options);

    // shorthands
    titleObj = $(options.container + ' .modal-title');
    bodyObj  = $(options.container + ' .modal-body');
    btnOkObj = $(options.container + ' .form-footer .yordar-ok');
    btnCancelObj = $(options.container + ' .form-footer .yordar-cancel');
    btnAltObj = $(options.container + ' .form-footer .yordar-alternate');

    // show a popup with the style..

    // === set title ===
    titleObj.html(options.title);

    // === set the content ===
    bodyObj.html('<div class="'+ options.divClass +'">'+ options.innerContent +'</div>');

    // === button look/behaviour ==

    // attach the behaviour
    if(jQuery.isFunction(options.submitHandler)){
      // unbind any previous click functions
      btnOkObj.unbind('click');
      //
      // attach the custom function to the button
      //
      btnOkObj.bind("click", options.submitHandlerData, options.submitHandler);
    } else {
      btnOkObj.unbind('click');
    }

    // attach the behaviour
    if(jQuery.isFunction(options.cancelHandler)){
      // unbind any previous click functions
      btnCancelObj.unbind('click');
      //
      // attach the custom function to the button
      //
      btnCancelObj.bind("click", options.cancelHandlerData, options.cancelHandler);
    } else {
      btnCancelObj.unbind('click');
    }

    // attach the behaviour
    if(jQuery.isFunction(options.alternateHandler)){
      // unbind any previous click functions
      btnAltObj.unbind('click');
      //
      // attach the custom function to the button
      //
      btnAltObj.bind("click", options.alternateHandlerData, options.alternateHandler);
    } else {
      btnAltObj.unbind('click');
    }

    if(options.cancel){
      btnCancelObj.html(options.cancel);  // set label
      btnCancelObj.show();
    } else {
      // hide the secondary button
      btnCancelObj.hide();
    }

    if(options.submit){
      btnOkObj.html(options.submit);  // set label
      btnOkObj.show();
    } else {
      // hide the primary button
      btnOkObj.hide();
    }

    if(options.alternate){
      btnAltObj.html(options.alternate); // set label
      btnAltObj.show();
    } else {
      // hide the alternate button
      btnAltObj.hide();
    }

    $(options.container).addClass(options.mainClass);
    // === button look/behaviour (end) ==

    // == show the popup ===
    var popup = new Foundation.Reveal($(options.container));
    popup.open();
  }


//-- function wrapper closed
})(jQuery);
