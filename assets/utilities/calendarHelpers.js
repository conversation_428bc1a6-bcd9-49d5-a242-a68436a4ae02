import moment from 'moment';

export function filterOrdersForCalendar(orders, params) {
  return orders.filter((order) => {
    const filteredByType =
      params.order_type && params.order_type !== 'team-order' ? order.order_type == params.order_type : true;
    const filteredByVariant =
      params.order_type && params.order_type === 'team-order'
        ? ['team_order', 'recurring_team_order'].includes(order.order_variant)
        : true;
    const filteredByQuery = params.query
      ? order.id == params.query || order.name.toLowerCase().includes(params.query.toLowerCase())
      : true;
    const filteredBySupplier = params['supplier_ids[]']
      ? order.suppliers.map((supplier) => supplier.id).includes(params['supplier_ids[]'])
      : true;
    return filteredByType && filteredByVariant && filteredByQuery && filteredBySupplier;
  });
}

export function clearDateCookie(domain) {
  const name = 'delivery_date_filter';
  const date = new Date();
  const oneDayInMilliseconds = 1000 * 60 * 60 * 24;
  date.setTime(date.getTime() - oneDayInMilliseconds);
  const expires = `; expires=${date.toUTCString()}`;
  const domainAttribute = domain ? `; domain=${domain}` : '';
  document.cookie = `${name}=;${expires}${domainAttribute}; path=/`;
}

export function isTodayOrLater(inputDate) {
  const today = moment().startOf('day');
  const dateToCheck = moment(inputDate);

  return dateToCheck.isSameOrAfter(today);
}

export function sanitizeEventsForCalendar(orders, holidays) {
  const holidayEvents = holidays.map(({ name, states, on_date, color, description }, index) => ({
    title: name,
    start: new Date(on_date),
    end: new Date(on_date),
    description,
    states,
    index,
    suppliers: [],
    link: '',
    status: '',
    id: index,
    type: 'holiday',
    color,
  }));
  const orderEvents = orders.map(({ id, name, delivery_at_raw, delivery_time, status, suppliers, links }, index) => ({
    id,
    title: `${delivery_time} - ${name}`,
    start: new Date(delivery_at_raw),
    end: new Date(delivery_at_raw),
    status,
    link: links && links.find((link) => link.type === 'view')?.url,
    index,
    suppliers,
    type: 'order',
  }));
  return [...holidayEvents, ...orderEvents];
}

export function orderStyleGetter(event) {
  const needsColor = event.type === 'holiday';
  return {
    style: {
      ...(needsColor && { backgroundColor: event.color || '#eee' }),
      ...(!needsColor && { backgroundColor: 'white' }),
      color: 'black',
      fontSize: '13px',
      height: 'auto',
      // boxShadow: `inset 0 0 20px ${statusColors[order.status]}`,
    },
    className: event.status,
    data: { 'modal-view': true },
  };
}
