$(document).ready(function () {
  var $wrapper = $(document)
  bindCommonElementEvents($wrapper);
  bindResizeEvents($wrapper);
})

function bindCommonElementEvents ($wrapper) {
  $wrapper.on('click', 'a[data-method=delete]', function(e) { handleDeleteLink(e) });
  $wrapper.on('click', 'a[href$=supplier-search-office-catering]', function(e) { openSuburModal(e, $wrapper, 'office-catering') });
  $wrapper.on('click', 'a[href$=supplier-search-office-snacks]', function(e) { openSuburModal(e, $wrapper, 'office-snacks') });
}

function bindResizeEvents($wrapper){
  if ($('.delivery-schedule-table').length) {
    if ($(window).width() < 900) {
      $('.delivery-schedule-table').addClass('stack');
    }
    $(window).resize(function () {
      if ($(window).width() < 900) {
        $('.delivery-schedule-table').addClass('stack');
      } else {
        $('.delivery-schedule-table').removeClass('stack');
      }
    });
  }

  //make sure body is not locked if window is resized with docket open
  $(window).resize(function () {
    if ($(window).width() > 550) {
      $("body").removeClass("lock");
    }
  });
}

function openSuburModal (event, $wrapper, category_group) {
  event.preventDefault();
  var $modal = $wrapper.find('#enter-address-modal')
  var $locationInput = $modal.find('#location_search');
  $locationInput.data('category-group', category_group)
  $modal.foundation('open');
}

function handleDeleteLink (event) {
  event.preventDefault();
  var $link = $(event.currentTarget);
  var target = $link.attr('target');
  var csrfToken = $('meta[name="csrf-token"]').attr('content');
  var $form = $('<form method="post" action="' + $link.attr('href') + '"></form>');
  var metadataInput = '<input name="_method" value="delete" type="hidden" />';
  metadataInput += '<input name="authenticity_token" value="' + csrfToken + '" type="hidden" />';
  if (target) { $form.attr('target', target); }
  $form.hide().append(metadataInput).appendTo('body');
  $form.submit();
}
