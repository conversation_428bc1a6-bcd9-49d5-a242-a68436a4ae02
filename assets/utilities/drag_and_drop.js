export const dragConfig = ({ type, item, index, canDrag }) => ({
  type,
  item: () => ({ id: item.id, index }),
  collect: (monitor) => ({
    isDragging: monitor.isDragging(),
  }),
  canDrag,
});

export const dropConfig = ({ accepts, itemRef, index, dispatch, dragAction, sortAction }) => ({
  accept: accepts,
  collect(monitor) {
    return {
      handlerId: monitor.getHandlerId(),
    };
  },
  hover(item, monitor) {
    if (!itemRef.current) return;

    const dragIndex = item.index;
    const hoverIndex = index;

    if (dragIndex === hoverIndex) return;

    const hoverBoundingRect = itemRef.current?.getBoundingClientRect();
    const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
    const clientOffset = monitor.getClientOffset();
    const hoverClientY = clientOffset.y - hoverBoundingRect.top;
    if (
      (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) ||
      (dragIndex > hoverIndex && hoverClientY > hoverMiddleY)
    )
      return;

    dispatch({ ...dragAction, dragIndex, hoverIndex });

    item.index = hoverIndex;
  },
  drop(item, monitor) {
    if (!itemRef.current) return;
    sortAction();
  },
});

export const sortItemsOnDrop = ({ items, action, itemName, dispatch, withReturn }) => {
  const itemWeights = items.map(({ weight }) => weight);
  const sortedWeights = retrieveSortedWeights(itemWeights);
  items.forEach((item, idx) => {
    const newWeight = sortedWeights[idx];
    if (item.weight !== newWeight) {
      const itemnWitNewWeight = { ...item, weight: newWeight };

      if (withReturn) {
        action({
          [itemName]: itemnWitNewWeight,
          dispatch,
        });
      } else {
        dispatch(
          action({
            [itemName]: itemnWitNewWeight,
          })
        );
      }
    }
  });
};

const retrieveSortedWeights = (weights) => {
  // sort numerically
  let sortedweights = weights.sort((a, b) => a - b);

  // consolidate duplicate weights (can happen sometimes)
  let prevNum = null;
  let currentNum = null;
  sortedweights = sortedweights.map((num, idx) => {
    if (idx !== 0 && prevNum >= num) {
      prevNum = currentNum = prevNum + 1;
    } else {
      prevNum = currentNum = num;
    }
    return currentNum;
  });
  return sortedweights;
};
