export function rangedColorPalette(size, color1, color2) {
  if (size === 1) {
    return '#FFF0DD';
  }
  const colors = [];

  // Parse the hexadecimal values for each color channel (red, green, and blue)
  const red1 = parseInt(color1.substr(1, 2), 16);
  const green1 = parseInt(color1.substr(3, 2), 16);
  const blue1 = parseInt(color1.substr(5, 2), 16);
  const red2 = parseInt(color2.substr(1, 2), 16);
  const green2 = parseInt(color2.substr(3, 2), 16);
  const blue2 = parseInt(color2.substr(5, 2), 16);

  // Calculate the step size for each color channel
  const redStep = Math.round((red2 - red1) / (size - 1));
  const greenStep = Math.round((green2 - green1) / (size - 1));
  const blueStep = Math.round((blue2 - blue1) / (size - 1));

  // Generate the colors
  for (let i = 0; i < size; i++) {
    // Calculate the hexadecimal values for each color channel
    const red = (red1 + i * redStep).toString(16);
    const green = (green1 + i * greenStep).toString(16);
    const blue = (blue1 + i * blueStep).toString(16);

    // Pad the values with leading zeros if necessary
    const paddedRed = red.length < 2 ? `0${red}` : red;
    const paddedGreen = green.length < 2 ? `0${green}` : green;
    const paddedBlue = blue.length < 2 ? `0${blue}` : blue;

    // Add the color to the array
    colors.push(`#${paddedRed}${paddedGreen}${paddedBlue}`);
  }

  return colors;
}

const colors = {
  snacks: {
    'Office Fruit': '#BCF55A99',
    'Office Milk': '#D1C4FB',
    'Coffee Supplies': '#D1C4FB66',
    'Office Bread': '#fd9ba899',
    'Office Drinks': '#75E3F9',
    'Office Alcohol': '#75E3F966',
    'Office Snacks & Pantry': '#FDDF39aa',
    Kitchenware: '#fc6621cc',
    'Cleaning Supplies': '#241c1599',
    Others: '#FC662166',
  },
  catering: {
    'Afternoon Tea': '#D1C4FB',
    'Alcohol Catering': '#75E3F9',
    'BBQ Catering': '#BCF55A22',
    Breaky: '#FDDF39',
    Buffets: '#FC6621cc',
    Burgers: '#fd9ba8',
    'Chargrill Chicken': '#D1C4FBcc',
    'Chinese ': '#75E3F9cc',
    'Coffee/Gelato Carts': '#2f6bb066',
    'Finger Food': '#fd9ba8aa',
    Greek: '#BCF55Aaa',
    'Healthy  Catering': '#BCF55Acc',
    Indian: '#D1C4FBaa',
    Indigenous: '#75E3F933',
    'Individually boxed meals': '#FC662166',
    Italian: '#FDDF39aa',
    Japanese: '#FC6621aa',
    Korean: '#FDDF39cc',
    Malaysian: '#D1C4FB88',
    Mexican: '#75E3F988',
    'Middle Eastern': '#FDDF3988',
    'Morning Tea': '#BCF55A88',
    'Office Pizza': '#FC662188',
    Others: '#fd9ba888',
    Seminars: '#D1C4FB66',
    'Smoothie/Juice Carts': '#75E3F966',
    Spanish: '#BCF55A66',
    Staffing: '#FDDF3966',
    Thai: '#FC662166',
    Vietnamese: '#fd9ba866',
    'Whole Cakes and Sweets': '#D1C4FB33',
    'Working Lunch': '#75E3F9aa',
  },
};

export default colors;
