import { dateToDateString } from 'utilities/graph-options';
import { apiEmployeeSurveyEmployeeSurveySubmissionsPath } from 'routes';

export const SurveyKinds = {
  'catering-services': 'Catering',
  'kitchen-supplies': 'Pantry',
};

export const INPUT_OPTIONS = [
  {
    label: 'Text',
    value: 'text',
    withOptions: false,
  },
  {
    label: 'Ratings (0-10)',
    value: 'ratings',
    withOptions: false,
  },
  {
    label: 'Toggle',
    value: 'toggle',
    withOptions: true,
  },
  {
    label: 'Select One',
    value: 'single-select',
    withOptions: true,
  },
  // {
  //   label: 'Multi Select',
  //   value: 'multi-select',
  //   withOptions: true,
  // },
];

export const dragConfig = () => ({
  type: 'Question',
  collect: (monitor) => ({
    isDragging: monitor.isDragging(),
  }),
});

export const dropConfig = ({ survey, index, previewRef, dragAction, sortAction }) => ({
  accept: 'Question',
  collect: (monitor) => ({
    isOver: monitor.isOver(),
    canDrop: monitor.canDrop(),
  }),
  hover: (item, monitor) => {
    if (!previewRef.current) return;

    const dragIndex = item.index;
    const hoverIndex = index;

    if (dragIndex === hoverIndex) return;

    const hoverBoundingRect = previewRef.current?.getBoundingClientRect();
    const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
    const clientOffset = monitor.getClientOffset();
    const hoverClientY = clientOffset.y - hoverBoundingRect.top;
    if (
      (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) ||
      (dragIndex > hoverIndex && hoverClientY > hoverMiddleY)
    )
      return;

    // change position in list on hover
    dragAction({
      survey,
      dragIndex,
      hoverIndex,
    });

    item.index = hoverIndex;
  },
  drop: (item, monitor) => {
    if (!previewRef.current) return;

    // re-sort question in list
    sortAction({
      survey,
    });
  },
});

export const createCSVLink = ({ survey, dates }) => {
  const formattedDates = {
    start: dateToDateString(dates.start, '01'),
    end: dateToDateString(dates.end, new Date(dates.end.getFullYear(), dates.end.getMonth() + 1, 0).getDate()),
  };
  const { start, end } = formattedDates;
  const params = `starts_on=${start}&ends_on=${end}`;
  return `${apiEmployeeSurveyEmployeeSurveySubmissionsPath(survey, { format: 'csv' })}?${params}`;
};
