import axios from 'axios';
import { apiOrderCheckLeadTimePath } from 'routes';
import generateLeadTimeMessage from 'utilities/generateLeadTimeMessage';

const checkLeadTime = async ({ orderID, url, isAdmin }) => {
  const { data: leadTimeData } = await axios(apiOrderCheckLeadTimePath(orderID, { format: 'json' }));

  let leadTimeModalOptions;
  if (leadTimeData.can_process) {
    return { canProcess: true };
  }
  if (!leadTimeData) {
    leadTimeModalOptions = generateLeadTimeMessage({ type: 'error' });
  } else if (isAdmin) {
    leadTimeModalOptions = generateLeadTimeMessage({
      type: 'admin',
      url,
    });
  } else {
    leadTimeModalOptions = generateLeadTimeMessage({
      type: 'customer',
    });
  }
  return { leadTimeModalOptions };
};

export default checkLeadTime;
