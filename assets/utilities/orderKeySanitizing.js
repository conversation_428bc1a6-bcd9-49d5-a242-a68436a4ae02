export function sanitizeParamsForKey(params, isCalendarView) {
  const asArray = Object.entries(params);
  const sanitizedParams = asArray.reduce((acc, [key, value]) => {
    if (!isCalendarView && ['to_date', 'from_date'].includes(key)) {
      return acc
    }
    if (value !== "all" && key !== "page") {
      if (key === "show_past") {
        return [...acc, "show_past"];
      }
      return [...acc, value];
    }
    return acc;
  }, []);
  return sanitizedParams.length ? sanitizedParams.join(" ") : "all";
}

export function hasCachedOrders(orderKeys, params, isCalendarView) {
  const sanitizedParams = sanitizeParamsForKey(params, isCalendarView);
  if (!orderKeys.length || !sanitizedParams.length) return false;
  return orderKeys.find(
    (order) =>
      sanitizedParams.split(" ").every((param) => order.includes(param)) &&
      order.length === sanitizedParams.length
  );
}
