import {
  FETCH_SAVED_ADDRESSS_SUCCESS,
  FETCH_MORE_SAVED_ADDRESSS_SUCCESS,
  CREATE_SAVED_ADDRESS_SUCCESS,
  UPDATE_SAVED_ADDRESS_SUCCESS,
  REMOVE_SAVED_ADDRESS_SUCCESS,
} from 'actions/savedAddressActionTypes';

const initialState = {
  saved_addresses: [],
  page: 1,
  hasMore: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case FETCH_SAVED_ADDRESSS_SUCCESS:
    case FETCH_MORE_SAVED_ADDRESSS_SUCCESS:
      return {
        ...state,
        saved_addresses: [...state.saved_addresses, ...action.payload.saved_addresses],
        hasMore: action.payload.hasMore,
        page: state.page + 1,
      };
    case CREATE_SAVED_ADDRESS_SUCCESS:
      return {
        ...state,
        saved_addresses: [
          action.payload,
          ...state.saved_addresses.filter((saved_address) => saved_address.id != action.payload.id),
        ],
      };
    case UPDATE_SAVED_ADDRESS_SUCCESS:
      return {
        ...state,
        saved_addresses: state.saved_addresses.map((saved_address) =>
          saved_address.id === action.payload.id ? action.payload : saved_address
        ),
      };
    case REMOVE_SAVED_ADDRESS_SUCCESS:
      return {
        ...state,
        saved_addresses: state.saved_addresses.filter((saved_address) => saved_address.id != action.payload.id),
      };
    default:
      return state;
  }
};
