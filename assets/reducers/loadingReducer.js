export default function loadingReducer(state = {}, action) {
  const { type, payload } = action;
  const { id } = !!payload && payload;
  const isCreateAction = !!/CREATE_.*/.exec(type);

  const matches = /(.*)_(REQUEST|SUCCESS|FAILURE)/.exec(type);

  if (!matches) return state;

  const { 1: requestName, 2: requestState } = matches;
  const isActive = requestState === 'REQUEST';

  return {
    ...state,
    [requestName]: !!id && !isCreateAction ? { ...state[requestName], [id]: isActive } : isActive,
  };
}
