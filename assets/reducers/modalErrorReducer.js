import {
  ORDER_PAYMENT_FAILURE,
  CHECK_SWIPE_CARD_ACCESS_FAILURE,
  FIND_SUBURB_FAILURE,
  CHECK_LEAD_TIME_FAILURE,
  CHECK_CLOSURE_DATES_FAILURE,
  SU<PERSON>IT_ORDER_FAILURE,
  CANCEL_ORDER_FAILURE,
  CLEAR_MODAL_ERRORS,
} from 'actions/checkoutActionTypes';

const initialState = '';

export default (state = initialState, action) => {
  switch (action.type) {
    case FIND_SUBURB_FAILURE:
      return `Sorry couldn't find a valid suburb in our system`;
    case FIND_SUBURB_FAILURE:
      return `Sorry couldn't find a valid suburb in our system`;
    case CHECK_LEAD_TIME_FAILURE:
      return 'Sorry we were unable to check supplier lead times. Please try again!';
    case CHECK_CLOSURE_DATES_FAILURE:
      return 'Sorry we were unable to check supplier closure times. Please try again!';
    case ORDER_PAYMENT_FAILURE:
      return 'Please select a payment method!';
    case SUBMIT_ORDER_FAILURE:
    case CANCEL_ORDER_FAILURE:
      return action.errors
        ? action.errors.join(',')
        : 'Something went wrong while submitting the order. Please try again.';
    case CLEAR_MODAL_ERRORS:
      return initialState;
    default:
      return state;
  }
};
