import {
  FETCH_BILLING_DETAILS_SUCCESS,
  UPDATE_BILLING_FIELD,
  UPDATE_BILLING_SUBURB,
} from 'actions/checkoutActionTypes';

export default (state = {}, action) => {
  switch (action.type) {
    case FETCH_BILLING_DETAILS_SUCCESS:
      return action.payload;
    case UPDATE_BILLING_FIELD:
      return { ...state, ...action.payload };
    case UPDATE_BILLING_SUBURB:
      return { ...state, suburb_label: action.suburb_label, suburb_id: action.suburb_id };
    default:
      return state;
  }
};
