import {
  FETCH_ORDERS_FROM_CACHE,
  FETCH_ORDERS_SUCCESS,
  FETCH_MORE_ORDERS_SUCCESS,
  UPDATE_ORDER_SUCCESS,
} from 'actions/actionTypes';

const initialState = {
  orders: {},
  pages: { all: 1 },
  hasMoreOrders: {},
  currentOrders: { orders: [], key: 'all' },
  initialLoad: true,
};

const updateOrders = (orders, payload) =>
  Object.keys(orders).reduce(
    (updatedOrders, key) => {
      updatedOrders[key] = orders[key].map((order) => (order.id === payload.id ? payload : order));
      return updatedOrders;
    },
    { ...orders }
  );

export default (state = initialState, action) => {
  switch (action.type) {
    case FETCH_ORDERS_FROM_CACHE:
      return {
        ...state,
        currentOrders: {
          orders: state.orders[action.payload.cachedKey],
          key: action.payload.cachedKey,
        },
      };
    case FETCH_ORDERS_SUCCESS:
    case FETCH_MORE_ORDERS_SUCCESS: {
      const { key, orders, hasMoreOrders } = action.payload;
      const existingOrders = state.orders[key] || [];
      const updatedOrders = [...existingOrders, ...orders];

      return {
        ...state,
        orders: { ...state.orders, [key]: updatedOrders },
        currentOrders: { orders: updatedOrders, key: key || 'all' },
        pages: { ...state.pages, [key]: (state.pages[key] || 1) + 1 },
        hasMoreOrders: { ...state.hasMoreOrders, [key]: hasMoreOrders },
        initialLoad: false,
      };
    }
    case UPDATE_ORDER_SUCCESS: {
      const updatedOrders = updateOrders(state.orders, action.payload);
      const updatedCurrentOrders = state.currentOrders.orders.map((order) =>
        order.id === action.payload.id ? action.payload : order
      );

      return {
        ...state,
        orders: updatedOrders,
        currentOrders: { orders: updatedCurrentOrders, key: state.currentOrders.key },
      };
    }
    default:
      return state;
  }
};
