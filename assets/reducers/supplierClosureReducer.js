import {
  FET<PERSON>_CLOSURE_DATES_SUCCESS,
  FETCH_MORE_CLOSURE_DATES_SUCCESS,
  CREATE_CLOSURE_DATE_SUCCESS,
  UPDATE_CLOSURE_DATE_SUCCESS,
  REMOVE_CLOSURE_DATE_SUCCESS,
} from 'actions/closureActionTypes';

const initialState = {
  closure_dates: [],
  page: 1,
  hasMore: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case FETCH_CLOSURE_DATES_SUCCESS:
    case FETCH_MORE_CLOSURE_DATES_SUCCESS:
      return {
        ...state,
        closure_dates: [...state.closure_dates, ...action.payload.closure_dates],
        hasMore: action.payload.hasMore,
        page: state.page + 1,
      };
    case CREATE_CLOSURE_DATE_SUCCESS:
      return {
        ...state,
        closure_dates: [action.payload, ...state.closure_dates],
      };
    case UPDATE_CLOSURE_DATE_SUCCESS:
      return {
        ...state,
        closure_dates: state.closure_dates.map((closure_date) =>
          closure_date.id === action.payload.id ? action.payload : closure_date
        ),
      };
    case REMOVE_CLOSURE_DATE_SUCCESS:
      return {
        ...state,
        closure_dates: state.closure_dates.filter((closure_date) => closure_date.id != action.payload.id),
      };
    default:
      return state;
  }
};
