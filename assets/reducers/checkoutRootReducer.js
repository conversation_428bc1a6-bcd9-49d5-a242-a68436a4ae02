import { combineReducers } from 'redux';
import loading from './loadingReducer';
import order from './orderReducer';
import billing_details from './billingDetailsReducer';
import customer from './orderCustomerReducer';
import modalErrors from './modalErrorReducer';
import form from './formReducer';

const appReducer = combineReducers({
  loading,
  order,
  billing_details,
  customer,
  modalErrors,
  form,
});

const rootReducer = (state, action) => {
  if (action.type === 'CLEAR_DATA') {
    state = null;
  }
  return appReducer(state, action);
};

export default rootReducer;
