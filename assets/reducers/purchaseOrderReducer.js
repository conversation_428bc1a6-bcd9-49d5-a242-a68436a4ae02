import {
  FETCH_PURCHASE_ORDERS_SUCCESS,
  FETCH_MORE_PURCHASE_ORDERS_SUCCESS,
  CREATE_PURCHASE_ORDER_SUCCESS,
  UPDATE_PURCHASE_ORDER_SUCCESS,
  REMOVE_PURCHASE_ORDER_SUCCESS,
  FET<PERSON>_ORDERS_SUCCESS,
  UPDATE_ORDER_SUCCESS,
} from 'actions/purhaseOrderActionTypes';

const initialState = {
  purchase_orders: [],
  orders: [],
  page: 1,
  hasMore: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case FETCH_PURCHASE_ORDERS_SUCCESS:
    case FETCH_MORE_PURCHASE_ORDERS_SUCCESS:
      return {
        ...state,
        purchase_orders: [...state.purchase_orders, ...action.payload.purchase_orders],
        hasMore: action.payload.hasMore,
        page: state.page + 1,
      };
    case CREATE_PURCHASE_ORDER_SUCCESS:
      return {
        ...state,
        purchase_orders: [
          action.payload,
          ...state.purchase_orders.filter((purchase_order) => purchase_order.id != action.payload.id),
        ],
      };
    case UPDATE_PURCHASE_ORDER_SUCCESS:
      return {
        ...state,
        purchase_orders: state.purchase_orders.map((purchase_order) =>
          purchase_order.id === action.payload.id ? action.payload : purchase_order
        ),
      };
    case REMOVE_PURCHASE_ORDER_SUCCESS:
      return {
        ...state,
        purchase_orders: state.purchase_orders.filter((purchase_order) => purchase_order.id != action.payload.id),
      };
    case FETCH_ORDERS_SUCCESS:
      return {
        ...state,
        orders: action.payload,
      };
    case UPDATE_ORDER_SUCCESS:
      return {
        ...state,
        orders: state.orders.map((order) => (order.id === action.payload.id ? action.payload : order)),
      };
    default:
      return state;
  }
};
