import {
  UPDATE_CURRENT_PANEL,
  CHECK_CLOSURE_DATES_SUCCESS,
  CHECK_LEAD_TIME_SUCCESS,
  CHECK_SWIPE_CARD_ACCESS_SUCCESS,
  CHECK_SUBURB_AVAILABILITY_SUCCESS,
  UPDATE_NEW_CARD,
  UPDATE_SUBMIT_NEW_CARD,
  UPDATE_AS_QUOTE_ORDER,
  UPDATE_MODE,
  UPDATE_SUBMIT_ORDER,
  SAVE_STRIPE_CARD_REQUEST,
  SAVE_STRIPE_CARD_FAILURE,
  UPDATE_ERRORS,
} from 'actions/checkoutActionTypes';

const initialState = {
  currentPanel: '',
  closureSuppliers: [],
  supplierLeadTime: null,
  swipeCardSuppliers: [],
  newCard: false,
  submitNewCard: false,
  asQuote: false,
  quoteDetails: {
    emails: '',
    message: '',
  },
  mode: '',
  canSubmitOrder: false,
  deliverableSuppliers: [],
  errors: {},
};

export default (state = initialState, action) => {
  switch (action.type) {
    case UPDATE_CURRENT_PANEL:
      return {
        ...state,
        currentPanel: action.payload,
      };
    case CHECK_CLOSURE_DATES_SUCCESS:
      return {
        ...state,
        closureSuppliers: action.payload,
      };
    case CHECK_LEAD_TIME_SUCCESS:
      return {
        ...state,
        supplierLeadTime: action.payload,
      };
    case CHECK_SUBURB_AVAILABILITY_SUCCESS:
      return {
        ...state,
        deliverableSuppliers: action.payload,
      };
    case CHECK_SWIPE_CARD_ACCESS_SUCCESS:
      return {
        ...state,
        swipeCardSuppliers: action.payload,
      };
    case UPDATE_NEW_CARD:
      return {
        ...state,
        newCard: action.payload,
      };
    case UPDATE_SUBMIT_NEW_CARD:
      return {
        ...state,
        submitNewCard: action.payload,
      };
    case UPDATE_MODE:
      return {
        ...state,
        mode: action.payload,
      };
    case UPDATE_AS_QUOTE_ORDER:
      return {
        ...state,
        asQuote: action.payload,
      };
    case UPDATE_SUBMIT_ORDER:
      return {
        ...state,
        ...(action.quoteDetails && { quoteDetails: action.quoteDetails }),
        canSubmitOrder: action.payload,
      };
    case SAVE_STRIPE_CARD_REQUEST:
      return {
        ...state,
        errors: {
          ...state.errors,
          ...clearCardErrors(),
        },
      };
    case SAVE_STRIPE_CARD_FAILURE:
      return {
        ...state,
        errors: {
          ...state.errors,
          ...stripeCardErrors(action.payload),
        },
      };
    case UPDATE_ERRORS:
      return {
        ...state,
        errors: {
          ...state.errors,
          ...action.payload,
        },
      };
    default:
      return state;
  }
};

const stripeErrorsMap = [
  {
    str: 'number',
    field: 'card_number',
  },
  {
    str: 'expiration',
    field: 'card_expiry',
  },
  {
    str: 'code',
    field: 'card_cvv',
  },
];

const clearCardErrors = () => {
  const errors = {};
  stripeErrorsMap.forEach(({ field }) => {
    errors[field] = false;
  });
  return errors;
};

const stripeCardErrors = (error) => {
  const erroredItem = stripeErrorsMap.find(({ str }) => {
    const regex = new RegExp(str);
    return regex.test(error);
  });
  if (erroredItem) {
    return { [erroredItem.field]: error };
  }
  return clearCardErrors();
};
