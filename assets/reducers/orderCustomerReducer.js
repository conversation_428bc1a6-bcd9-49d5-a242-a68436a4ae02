import { FETCH_CUSTOMER_DETAILS_SUCCESS } from 'actions/checkoutActionTypes';

const initialState = {
  hide_po_number: false,
  requires_purchase_order: false,
  required_department_identity_format: '',
  can_pay_on_account: true,
  can_pay_by_credit_card: true,
  has_nominated_card: false,
  saved_credit_cards: [],
  saved_addresses: [],
  recent_addresses: [],
  purchase_orders: [],
  billing_preference: 'instantly',
};

export default (state = initialState, action) => {
  switch (action.type) {
    case FETCH_CUSTOMER_DETAILS_SUCCESS:
      return action.payload;
    default:
      return state;
  }
};
