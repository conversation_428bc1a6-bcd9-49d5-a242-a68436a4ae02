import {
  <PERSON>ET<PERSON>_<PERSON><PERSON>_SUCCESS,
  <PERSON><PERSON><PERSON>_COMPANIES_SUCCESS,
  <PERSON><PERSON><PERSON>_ME<PERSON>,
  CREATE_<PERSON>NU_SECTION_SUCCESS,
  UPDATE_<PERSON><PERSON>_SECTION_SUCCESS,
  REMOVE_<PERSON><PERSON>_SECTION_SUCCESS,
  <PERSON><PERSON>_<PERSON><PERSON>_SECTION_SUCCESS,
  CREATE_MENU_ITEM_SUCCESS,
  CLONE_MENU_ITEM_SUCCESS,
  UPDATE_MENU_ITEM_SUCCESS,
  REMOVE_ME<PERSON>_ITEM_SUCCESS,
  DRAG_MENU_ITEM_SUCCESS,
  MENU_<PERSON>EM_ORDERS_SUCCESS,
  CLEAR_MENU_ITEM_ORDERS,
  UPLOAD_MENU_ITEM_IMAGE_SUCCESS,
  REMOVE_MENU_ITEM_IMAGE_SUCCESS,
  CREATE_MENU_EXTRA_SECTION_SUCCESS,
  UPDATE_MENU_EXTRA_SECTION_SUCCESS,
  REMOVE_MENU_EXTRA_SECTION_SUCCESS,
  DRAG_MENU_EXTRA_SECTION_SUCCESS,
  CREATE_MENU_EXTRA_SUCCESS,
  UPDATE_MENU_EXTRA_SUCCESS,
  REMOVE_MENU_EXTRA_SUCCESS,
  DRAG_MENU_EXTRA_SUCCESS,
  CREATE_SERVING_SIZE_SUCCESS,
  UPDATE_SERVING_SIZE_SUCCESS,
  REMOVE_SERVING_SIZE_SUCCESS,
  CREATE_RATE_CARD_SUCCESS,
  UPDATE_RATE_CARD_SUCCESS,
  REMOVE_RATE_CARD_SUCCESS,
  DRAG_SERVING_SIZE_SUCCESS,
} from 'actions/menuActionTypes';

const initialState = {
  menu_sections: [],
  companies: [],
};

export default (state = initialState, action) => {
  switch (action.type) {
    case FETCH_MENU_SUCCESS:
      return {
        ...state,
        menu_sections: action.payload,
      };
    case FETCH_COMPANIES_SUCCESS:
      return {
        ...state,
        companies: action.payload,
      };
    case SEARCH_MENU:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              is_searched: true,
              menu_items: menu_section.menu_items.map((menu_item) => ({
                ...menu_item,
                is_searched: menu_item.id === action.payload.id,
              })),
            };
          }
          return { ...menu_section, is_searched: false };
        }),
      };
    case CREATE_MENU_SECTION_SUCCESS:
      return {
        ...state,
        menu_sections: [...state.menu_sections, { ...action.payload, is_new: true }],
      };
    case UPDATE_MENU_SECTION_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) =>
          menu_section.id === action.payload.id ? action.payload : menu_section
        ),
      };
    case REMOVE_MENU_SECTION_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.filter((menu_section) => menu_section.id != action.payload.id),
      };
    case DRAG_MENU_SECTION_SUCCESS:
      return {
        ...state,
        menu_sections: sortedItems({ ...action, items: state.menu_sections }),
      };
    case CREATE_MENU_ITEM_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return { ...menu_section, menu_items: [...menu_section.menu_items, { ...action.payload, is_new: true }] };
          }
          return menu_section;
        }),
      };
    case CLONE_MENU_ITEM_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.cloned_payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: [...menu_section.menu_items, { ...action.cloned_payload, is_new: true, is_cloned: true }],
            };
          }
          return menu_section;
        }),
      };
    case UPDATE_MENU_ITEM_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) =>
                menu_item.id === action.payload.id ? { ...action.payload, is_updated: true } : menu_item
              ),
            };
          }
          return menu_section;
        }),
      };
    case REMOVE_MENU_ITEM_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.filter((menu_item) => menu_item.id != action.payload.id),
            };
          }
          return menu_section;
        }),
      };
    case DRAG_MENU_ITEM_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: sortedItems({ ...action, items: menu_section.menu_items }),
            };
          }
          return menu_section;
        }),
      };
    case MENU_ITEM_ORDERS_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) =>
                menu_item.id === action.payload.id ? { ...menu_item, orders: action.orders } : menu_item
              ),
            };
          }
          return menu_section;
        }),
      };
    case CLEAR_MENU_ITEM_ORDERS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) =>
                menu_item.id === action.payload.id ? { ...menu_item, orders: [] } : menu_item
              ),
            };
          }
          return menu_section;
        }),
      };
    case UPLOAD_MENU_ITEM_IMAGE_SUCCESS:
    case REMOVE_MENU_ITEM_IMAGE_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.id) {
                  return {
                    ...menu_item,
                    image: action.payload.image,
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case CREATE_MENU_EXTRA_SECTION_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    menu_extra_sections: [...menu_item.menu_extra_sections, action.payload],
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case UPDATE_MENU_EXTRA_SECTION_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    menu_extra_sections: menu_item.menu_extra_sections.map((menu_extra_section) =>
                      menu_extra_section.id === action.payload.id ? action.payload : menu_extra_section
                    ),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case REMOVE_MENU_EXTRA_SECTION_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    menu_extra_sections: menu_item.menu_extra_sections.filter(
                      (menu_extra_section) => menu_extra_section.id != action.payload.id
                    ),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case DRAG_MENU_EXTRA_SECTION_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    menu_extra_sections: sortedItems({ ...action, items: menu_item.menu_extra_sections }),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case CREATE_MENU_EXTRA_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    menu_extra_sections: menu_item.menu_extra_sections.map((menu_extra_section) => {
                      if (menu_extra_section.id === action.payload.menu_extra_section_id) {
                        return {
                          ...menu_extra_section,
                          menu_extras: [...menu_extra_section.menu_extras, action.payload],
                        };
                      }
                      return menu_extra_section;
                    }),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case UPDATE_MENU_EXTRA_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    menu_extra_sections: menu_item.menu_extra_sections.map((menu_extra_section) => {
                      if (menu_extra_section.id === action.payload.menu_extra_section_id) {
                        return {
                          ...menu_extra_section,
                          menu_extras: menu_extra_section.menu_extras.map((menu_extra) =>
                            menu_extra.id === action.payload.id ? action.payload : menu_extra
                          ),
                        };
                      }
                      return menu_extra_section;
                    }),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case REMOVE_MENU_EXTRA_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    menu_extra_sections: menu_item.menu_extra_sections.map((menu_extra_section) => {
                      if (menu_extra_section.id === action.payload.menu_extra_section_id) {
                        return {
                          ...menu_extra_section,
                          menu_extras: menu_extra_section.menu_extras.filter(
                            (menu_extra) => menu_extra.id != action.payload.id
                          ),
                        };
                      }
                      return menu_extra_section;
                    }),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };

    case DRAG_MENU_EXTRA_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    menu_extra_sections: menu_item.menu_extra_sections.map((menu_extra_section) => {
                      if (menu_extra_section.id === action.payload.menu_extra_section_id) {
                        return {
                          ...menu_extra_section,
                          menu_extras: sortedItems({ ...action, items: menu_extra_section.menu_extras }),
                        };
                      }
                      return menu_extra_section;
                    }),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case CREATE_SERVING_SIZE_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    serving_sizes: [...menu_item.serving_sizes, action.payload],
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case UPDATE_SERVING_SIZE_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    serving_sizes: menu_item.serving_sizes.map((serving_size) =>
                      serving_size.id === action.payload.id ? action.payload : serving_size
                    ),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case REMOVE_SERVING_SIZE_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    serving_sizes: menu_item.serving_sizes.filter(
                      (serving_size) => serving_size.id != action.payload.id
                    ),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case DRAG_SERVING_SIZE_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    serving_sizes: sortedItems({ ...action, items: menu_item.serving_sizes }),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case CREATE_RATE_CARD_SUCCESS:
    case UPDATE_RATE_CARD_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  const existingRateCards = menu_item.rate_cards;
                  if (
                    existingRateCards.length &&
                    existingRateCards.find((rate_card) => rate_card.id == action.payload.id)
                  ) {
                    return {
                      ...menu_item,
                      rate_cards: menu_item.rate_cards.map((rate_card) =>
                        rate_card.id === action.payload.id ? action.payload : rate_card
                      ),
                    };
                  }
                  return {
                    ...menu_item,
                    rate_cards: [...menu_item.rate_cards, action.payload],
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    case REMOVE_RATE_CARD_SUCCESS:
      return {
        ...state,
        menu_sections: state.menu_sections.map((menu_section) => {
          if (menu_section.id === action.payload.menu_section_id) {
            return {
              ...menu_section,
              menu_items: menu_section.menu_items.map((menu_item) => {
                if (menu_item.id === action.payload.menu_item_id) {
                  return {
                    ...menu_item,
                    rate_cards: menu_item.rate_cards.filter((rate_card) => rate_card.id != action.payload.id),
                  };
                }
                return menu_item;
              }),
            };
          }
          return menu_section;
        }),
      };
    default:
      return state;
  }
};

const sortedItems = ({ items, dragIndex, hoverIndex }) => {
  const draggedItem = { ...items[dragIndex], is_sorted: true };
  const hoveredItem = { ...items[hoverIndex], is_sorted: true };
  const updatedItems = [...items];
  updatedItems.splice(hoverIndex, 1, hoveredItem); // update hovered Item (mark as sorted)
  updatedItems.splice(dragIndex, 1); // remove dragged item
  updatedItems.splice(hoverIndex, 0, draggedItem); // add the dragged item in new place (and mark as sorted)
  return updatedItems;
};
