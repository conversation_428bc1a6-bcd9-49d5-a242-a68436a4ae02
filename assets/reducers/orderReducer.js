import {
  FETCH_ORDER_DETAILS_SUCCESS,
  UPDATE_ORDER_FIELD,
  SUBMIT_ORDER_SUCCESS,
  UPDATE_PURCHASE_ORDER,
  UPDATE_DELIVERY_DATE,
  UPDATE_DELIVERY_ADDRESS_VIA_SAVED_ADDRESS,
  UPDATE_DELIVERY_ADDRESS,
  UPDATE_DELIVERY_SUBURB,
  UPDATE_DELIVERY_TYPE,
  UPDATE_ORDER_PAY_ON_ACCOUNT,
  UPDATE_ORDER_PAY_BY_CARD,
  SET_REQUIRES_GST_FREE_PO,
  UPDATE_WOOLWORTHS_ORDER_ID,
} from 'actions/checkoutActionTypes';

const initialState = {
  id: null,
  delivery_address: '',
  delivery_suburb_id: null,
  delivery_address_arr: [],
  is_catering_order: true,
  order_suppliers: [],
  requires_gst_free_purchase_order: false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case FETCH_ORDER_DETAILS_SUCCESS:
    case UPDATE_ORDER_FIELD:
    case SUBMIT_ORDER_SUCCESS:
      return { ...state, ...action.payload };
    case UPDATE_PURCHASE_ORDER:
      return { 
        ...state,
        ...(action.gstFree && { gst_free_cpo_id: action.payload }),
        ...(!action.gstFree && { cpo_id: action.payload })
      };
    case UPDATE_DELIVERY_DATE:
      return { ...state, delivery_at: action.payload };
    case UPDATE_DELIVERY_ADDRESS:
      return { ...state, delivery_address: action.payload };
    case UPDATE_DELIVERY_ADDRESS_VIA_SAVED_ADDRESS:
      return {
        ...state,
        delivery_address: action.payload.street_address,
        delivery_address_level: action.payload.level,
        delivery_suburb_id: action.payload.suburb_id,
        delivery_suburb_label: action.payload.suburb_label,
        delivery_instruction: action.payload.instructions ? action.payload.instructions : state.delivery_instruction,
      };
    case UPDATE_DELIVERY_SUBURB:
      return { ...state, delivery_suburb_label: action.payload.label, delivery_suburb_id: action.payload.id };
    case UPDATE_DELIVERY_TYPE:
      return { ...state, delivery_type: action.payload };
    case UPDATE_ORDER_PAY_ON_ACCOUNT:
      return { ...state, credit_card_id: 1 };
    case UPDATE_ORDER_PAY_BY_CARD:
      return { ...state, invoice_individually: false, credit_card_id: action.payload ? action.payload : null };
    case SET_REQUIRES_GST_FREE_PO:
      return { ...state, requires_gst_free_purchase_order: action.payload };
    case UPDATE_WOOLWORTHS_ORDER_ID:
      return {
        ...state,
        associated_woolworths_order_attributes: {
          ...state.associated_woolworths_order_attributes,
          woolworths_order_id: action.payload,
        },
      };
    default:
      return state;
  }
};
