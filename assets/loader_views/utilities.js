import { loadJSView, loadReactView } from './loadView';

// JS Views
const hamburger = loadJSView('utilities/hamburger');
const adminBanner = loadJSView('utilities/adminBanner');
const autocompleteInput = loadJSView('utilities/autocompleteInput');
const datepickerInput = loadJSView('utilities/datepickerInput');
const tooltipEl = loadJSView('utilities/tooltipEl');
const googleLocationInput = loadJSView('utilities/googleLocationInput');
const googleLocationSearch = loadJSView('utilities/googleLocationSearch');
const orderSlideout = loadJSView('utilities/orderSlideout');
const newOrderDropdown = loadJSView('utilities/newOrderDropdown');
const sidebarNav = loadJSView('utilities/sidebarNav');

const toastifyEl = loadReactView('utilities/toastifyEl');

const utilitiy_views = {
  hamburger,
  adminBanner,
  autocompleteInput,
  datepickerInput,
  tooltipEl,
  googleLocationInput,
  googleLocationSearch,
  orderSlideout,
  newOrderDropdown,
  sidebarNav,
  
  toastifyEl,
};

export default utilitiy_views;
