import { loadJSView, loadReactView } from './loadView';

const notificationsCount = loadJSView('admin/notificationsCount');

const yordarAdmin = loadReactView('admin/yordarAdmin');
const customerAdmin = loadReactView('admin/customerAdmin');
const companyAdmin = loadReactView('admin/companyAdmin');
const supplierAdmin = loadReactView('admin/supplierAdmin');
const invoiceAdmin = loadReactView('admin/invoiceAdmin');
const invoiceSummaryAdmin = loadReactView('admin/invoiceSummaryAdmin');
const orderAdmin = loadReactView('admin/orderAdmin');
const reportsAdmin = loadReactView('admin/reportsAdmin');
const pantryManagerAdmin = loadReactView('admin/pantryManagerAdmin');
const adminNotifications = loadReactView('admin/adminNotifications');
const couponAdmin = loadReactView('admin/couponAdmin');
const suburbAdmin = loadReactView('admin/suburbAdmin');
const holidayAdmin = loadReactView('admin/holidayAdmin');
const promotionAdmin = loadReactView('admin/promotionAdmin');
const reminderAdmin = loadReactView('admin/reminderAdmin');
const woolworthsAccountAdmin = loadReactView('admin/woolworthsAccountAdmin');
const staffOnBoarding = loadReactView('admin/staffOnBoarding');

const adminViews = {
  notificationsCount,

  yordarAdmin,
  customerAdmin,
  supplierAdmin,
  invoiceAdmin,
  invoiceSummaryAdmin,
  orderAdmin,
  companyAdmin,
  reportsAdmin,
  pantryManagerAdmin,
  adminNotifications,
  couponAdmin,
  suburbAdmin,
  holidayAdmin,
  promotionAdmin,
  reminderAdmin,
  woolworthsAccountAdmin,
  staffOnBoarding,
};

export default adminViews;
