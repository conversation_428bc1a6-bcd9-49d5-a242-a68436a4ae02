import { loadJSView, loadReactView } from './loadView';

// Customer Settings
const customerSettings = loadJSView('customer/customerSettings');

// Team Order Pages (JS)
const becomeTeamAdmin = loadReactView('team_orders/becomeTeamAdmin');
const teamOrderShow = loadJSView('customer/team_orders/show/teamOrderShow');
const teamOrderLoadMore = loadJSView('customer/team_orders/show/teamOrderLoadMore');
const teamOrderShowPackage = loadJSView('customer/team_orders/show/teamOrderShowPackage');
const teamOrderAttendeeSlideout = loadJSView('customer/team_orders/show/teamOrderAttendeeSlideout');

// Team Order Creation Pages (JS)
const teamOrderForm = loadJSView('customer/team_orders/creation/teamOrderForm');
const teamOrderEditForm = loadJSView('customer/team_orders/creation/teamOrderEditForm');
const teamOrderAttendeeForm = loadJSView('customer/team_orders/creation/teamOrderAttendeeForm');
const teamOrderSupplierForm = loadJSView('customer/team_orders/creation/teamOrderSupplierForm');
const teamOrderSupplierFiltering = loadJSView('customer/team_orders/creation/teamOrderSupplierFiltering');
const teamOrderSupplierMenu = loadJSView('customer/team_orders/creation/teamOrderSupplierMenu');
const teamOrderPaymentForm = loadJSView('customer/team_orders/creation/teamOrderPaymentForm');
const teamOrderPaymentSlideout = loadJSView('customer/team_orders/creation/teamOrderPaymentSlideout');

// Team Order Events
const customerTeamOrderManifest = loadJSView('customer/team_orders/customerTeamOrderManifest');

// Event Attendees Page (JS)
const customerContactList = loadJSView('customer/event_attendees/customerContactList');
const customerContactFiltering = loadJSView('customer/event_attendees/customerContactFiltering');
const customerContactTeam = loadJSView('customer/event_attendees/customerContactTeam');
const customerContactCreation = loadJSView('customer/event_attendees/customerContactCreation');

// Customer Pages (JS)
const customerFavSuppliers = loadJSView('customer/customerFavSuppliers');
const customerPaymentOptions = loadJSView('customer/customerPaymentOptions');
const customerReportsJq = loadJSView('customer/customerReportsJq');

// React Views
const customerOrders = loadReactView('customers/customerOrders');
const customerPurchaseOrders = loadReactView('customers/purchaseOrders');
const customerSavedAddresses = loadReactView('customers/savedAddresses');
const customerReports = loadReactView('customers/customerReports');
const customerEmployeeSurveys = loadReactView('customers/employeeSurveys');
const customerInvoices = loadReactView('customers/customerInvoices');
const customerMealPlans = loadReactView('customers/customerMealPlans');
const becomeCompanyTeamAdmin = loadReactView('customers/becomeCompanyTeamAdmin');
const customerQuotes = loadReactView('customers/customerQuotes');

const customerViews = {
  customerSettings,

  becomeTeamAdmin,
  teamOrderShow,
  teamOrderLoadMore,
  teamOrderShowPackage,
  teamOrderAttendeeSlideout,

  teamOrderForm,
  teamOrderEditForm,
  teamOrderAttendeeForm,
  teamOrderSupplierForm,
  teamOrderSupplierFiltering,
  teamOrderSupplierMenu,
  teamOrderPaymentForm,
  teamOrderPaymentSlideout,
  customerTeamOrderManifest,

  customerContactList,
  customerContactFiltering,
  customerContactTeam,
  customerContactCreation,

  customerFavSuppliers,
  customerPaymentOptions,
  customerReportsJq,

  // React Views
  customerOrders,
  customerPurchaseOrders,
  customerSavedAddresses,
  customerReports,
  customerEmployeeSurveys,
  customerInvoices,
  customerMealPlans,
  becomeCompanyTeamAdmin,
  customerQuotes,
};

export default customerViews;
