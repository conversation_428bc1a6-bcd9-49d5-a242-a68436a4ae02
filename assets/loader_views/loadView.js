export function loadReactView(path, name) {
  return function (el, props) {
    return import(`views/${path}.js`).then((module) => {
      if (name === undefined) {
        return module.default(el, props);
      }
      return module.default[name](el, props);
    });
  };
}

export function loadJSView(path, name) {
  return function (el, props) {
    return import(`javascript/${path}.js`).then((module) => {
      if (name === undefined) {
        return module.default(el, props);
      }
      return module.default[name](el, props);
    });
  };
}
