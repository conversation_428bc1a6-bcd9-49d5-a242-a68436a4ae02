import { useEffect, useContext } from 'react';

// actions
import { UPDATE_CURRENT_PANEL } from 'actions/checkoutActionTypes';
import { fetchOrder, fetchCustomer } from 'actions/checkoutActions';
import { useSelector, useDispatch } from 'react-redux';

// context
import appContext from 'contexts/appContext';

const useCheckoutDetails = () => {
  const order = useSelector((state) => state.order);
  const dispatch = useDispatch();
  const { panels: checkoutPanels, isEditPage, orderID } = useContext(appContext);

  // setup order in redux state
  useEffect(async () => {
    if (!order.id) {
      await fetchOrder({
        dispatch,
        orderID,
      });
      await fetchCustomer({
        dispatch,
        orderID,
      });
      dispatch({ type: UPDATE_CURRENT_PANEL, payload: checkoutPanels[0].key });
    }
  }, [order.id]);

  return {
    checkoutPanels,
    isEditPage,
  };
};

export default useCheckoutDetails;
