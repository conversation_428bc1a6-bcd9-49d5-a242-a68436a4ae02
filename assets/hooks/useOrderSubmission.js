import { useEffect } from 'react';

// actions
import { UPDATE_CURRENT_PANEL } from 'actions/checkoutActionTypes';
import { customerProfilePath } from 'routes';
import { submitOrder } from 'actions/checkoutActions';
import { useDispatch, useSelector } from 'react-redux';

const useOrderSubmission = () => {
  const dispatch = useDispatch();
  const order = useSelector((state) => state.order);
  const billingDetails = useSelector((state) => state.billing_details);
  const { mode, quoteDetails, canSubmitOrder } = useSelector((state) => state.form);

  useEffect(async () => {
    if (canSubmitOrder) {
      const { submittedOrder } = await submitOrder({
        order,
        billingDetails,
        mode,
        quoteDetails,
        dispatch,
      });
      const { redirect_to: redirectTo } = submittedOrder;
      window.location = redirectTo || customerProfilePath();
    }
  }, [canSubmitOrder]);

  useEffect(() => {
    if (['new', 'quoted'].includes(order.status)) {
      dispatch({ type: UPDATE_CURRENT_PANEL, payload: 'order-success' });
    }
  }, [order.status]);
};

export default useOrderSubmission;
