import axios from 'axios';

// actions
import {
  apiOrderCheckClosureDatesPath,
  apiOrderCheckLeadTimePath,
  apiOrderCheckSupplierSuburbAvailabilityPath,
} from 'routes';
import { useDispatch } from 'react-redux';
import {
  CHECK_CLOSURE_DATES_REQUEST,
  CHECK_CLOSURE_DATES_SUCCESS,
  CHECK_CLOSURE_DATES_FAILURE,
  CHECK_LEAD_TIME_REQUEST,
  CHECK_LEAD_TIME_SUCCESS,
  CHECK_LEAD_TIME_FAILURE,
  CHECK_SUBURB_AVAILABILITY_REQUEST,
  CHECK_SUBURB_AVAILABILITY_SUCCESS,
  CHECK_SUBURB_AVAILABILITY_FAILURE,
} from 'actions/checkoutActionTypes';

const useSupplierChecks = ({ order }) => {
  const dispatch = useDispatch();

  const checkClosureDates = async () => {
    if (!order.delivery_at) return false;

    dispatch({ type: CHECK_CLOSURE_DATES_REQUEST });

    try {
      const {
        data: { can_process, outside_operating_hours, supplier_closure_dates },
      } = await axios({
        method: 'GET',
        url: apiOrderCheckClosureDatesPath(order, { format: 'json' }),
        params: { order_delivery_at: order.delivery_at, suburb_id: order.delivery_suburb_id },
      });

      dispatch({ type: CHECK_CLOSURE_DATES_SUCCESS, payload: supplier_closure_dates.length ? supplier_closure_dates : [] });

      return can_process && !outside_operating_hours;
    } catch {
      dispatch({ type: CHECK_CLOSURE_DATES_FAILURE });
      return false;
    }
  };

  const checkLeadTime = async () => {
    if (!order.delivery_at) return false;

    dispatch({ type: CHECK_LEAD_TIME_REQUEST });
    try {
      const { data: supplierLeadTime } = await axios({
        method: 'GET',
        url: apiOrderCheckLeadTimePath(order, { format: 'json' }),
        params: { order_delivery_at: order.delivery_at },
      });

      dispatch({ type: CHECK_LEAD_TIME_SUCCESS, payload: !supplierLeadTime.can_process ? supplierLeadTime : null });
      return supplierLeadTime.can_process;
    } catch {
      dispatch({ type: CHECK_LEAD_TIME_FAILURE });
      return false;
    }
  };

  const checkSupplierSuburbRestriction = async ({ yordarSuburb, suburbRestriction }) => {
    if (yordarSuburb.id === suburbRestriction.id) {
      return true;
    }

    dispatch({ type: CHECK_SUBURB_AVAILABILITY_REQUEST });
    try {
      const {
        data: { suppliers, is_available: isAvailable },
      } = await axios({
        method: 'GET',
        url: apiOrderCheckSupplierSuburbAvailabilityPath(order, { format: 'json' }),
        params: { suburb_id: yordarSuburb.id },
      });

      if (isAvailable) {
        dispatch({ type: CHECK_SUBURB_AVAILABILITY_SUCCESS, payload: [] });
      } else {
        dispatch({ type: CHECK_SUBURB_AVAILABILITY_SUCCESS, payload: suppliers });
      }
      return isAvailable;
    } catch {
      dispatch({ type: CHECK_SUBURB_AVAILABILITY_FAILURE });
      return false;
    }
  };

  return {
    checkClosureDates,
    checkLeadTime,
    checkSupplierSuburbRestriction,
  };
};

export default useSupplierChecks;
