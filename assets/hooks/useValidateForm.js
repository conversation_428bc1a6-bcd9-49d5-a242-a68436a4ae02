import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UPDATE_ERRORS } from 'actions/checkoutActionTypes';

const useValidateForm = () => {
  const { errors } = useSelector((state) => state.form);
  const dispatch = useDispatch();

  useEffect(() => {
    const errorsEl = document.getElementsByClassName('is-invalid-input');
    if (errorsEl.length) {
      const errorRect = errorsEl[0].getBoundingClientRect();
      const errorsTop = errorRect.top + window.scrollY - 20;
      window.scrollTo({ top: errorsTop, behavior: 'smooth' });
    }
  }, [errors]);

  // actual function to validate
  const validate = ({ object, fields }) => {
    let isValid = true;
    let validationErrors = {};
    fields.forEach((field) => {
      if (!object[field]) isValid = false;
      validationErrors = { ...validationErrors, [field]: !object[field] };
    });

    dispatch({ type: UPDATE_ERRORS, payload: validationErrors });
    return isValid;
  };

  return validate;
};

export default useValidateForm;
