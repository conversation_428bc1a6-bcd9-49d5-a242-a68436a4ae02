import { useEffect, useState } from 'react';

export default function useCustomerSuppliers(orders, hasOrders) {
  const [customerSuppliers, setCustomerSuppliers] = useState([]);

  useEffect(() => {
    if (hasOrders) {
      const suppliers = [];
      orders.forEach((order) =>
        order.suppliers.forEach((supplier) => {
          if (!suppliers.find((a) => a.id === supplier.id)) {
            suppliers.push(supplier);
          }
        })
      );
      const suppliersForFilter = suppliers
        .sort((a, b) => (b.name > a.name ? -1 : 1))
        .map((supplier) => ({ value: supplier.id, label: supplier.name }));
      setCustomerSuppliers([{ value: 'all', label: 'All' }, ...suppliersForFilter]);
    }
  }, [hasOrders]);

  return customerSuppliers;
}
