import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { adminFields } from 'utilities/adminHelpers';
import { defaultToastOptions } from 'utilities/toastHelpers';

import {
  apiAdminAdminsPath,
  apiAdminAdminPath
} from 'routes';

const AdminsPaginationLimit = 20;

const initialState = {
  admins: [],
  kind: 'yordar_admin',
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const yordarAdminStore = create((set, get) => ({
  ...initialState,
  setFilters: (filters) => set({ ...filters }),
  setKind: (kind) => {
    const { fetchAdmins } = get();
    set({ kind });
    setLocalStorage({ kind });
    fetchAdmins({ page: 1 });
  },
  setQuery: (query) => {
    const { fetchAdmins } = get();
    set({ query });
    fetchAdmins({ page: 1 });
  },
  fetchAdmins: async ({ page }) => {
    const { kind, query } = get();
    set({ loadingList: page == 1, loadingMore: page > 1 });
    if (page == 1) {
      set({ admins: [] });
    }

    const { data: admins } = await axios({
      method: 'GET',
      url: apiAdminAdminsPath(),
      params: {
        page,
        kind,
        ...(!!query && { query }),
      },
    });

    set((state) => ({
      admins: page == 1 ? admins : [...state.admins, ...admins],
      page: page + 1,
      hasMore: admins.length == AdminsPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  updateAdmin: async ({ admin }) => {
    try {
      const sanitizedAdmin = {}
      Object.keys(adminFields).forEach((flag) => (sanitizedAdmin[flag] = admin[flag]));
      const { data: updatedAdmin } = await axios({
        method: 'PUT',
        url: apiAdminAdminPath(admin),
        data: { user: sanitizedAdmin },
        headers: csrfHeaders(),
      });

      set((state) => ({
        admins: state.admins.map((admin) => (admin.id === updatedAdmin.id ? updatedAdmin : admin)),
      }));
      toast.success(`Updated ${updatedAdmin.name}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error updating the admin');
    }
  },
}));

function setLocalStorage({ ...filter }) {
  const storageKey = 'YordarAdminFilters';
  const existingFilters = JSON.parse(localStorage.getItem(storageKey) || JSON.stringify({}));
  delete (existingFilters.query); // only required for a while. TO-DO - remove after a few weeks

  const updatedFilter = { ...existingFilters, ...filter };
  localStorage.setItem(storageKey, JSON.stringify(updatedFilter));
}

function handleError(error, message = '') {
  let errorMessage = message;
  if (error?.response?.status === 422) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default yordarAdminStore;
