import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';

import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import { companyUpdateFields, companyFlags } from 'utilities/adminHelpers';
import { apiAdminCompaniesPath, apiAdminCompanyPath } from 'routes';

const CompanyPaginationLimit = 20;

const initialState = {
  favouritesOnly: true,
  companies: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
  editAll: false,
  changedCompanyIDs: [],
};

const companyAdminStore = create((set, get) => ({
  ...initialState,
  setFavourites: (favouritesOnly) => set({ favouritesOnly }),
  fetchCompanies: async ({ page, hasFavourites, passedQuery = '' }) => {
    const { query, favouritesOnly } = get();
    set({ loadingList: page == 1, loadingMore: page > 1 });
    if (passedQuery) {
      set({ query: passedQuery });
    }
    if (page == 1) {
      set({ companies: [] });
    }
    const { data: companies } = await axios({
      method: 'GET',
      url: apiAdminCompaniesPath(),
      params: {
        query: query || passedQuery,
        page,
        ...(!!hasFavourites && !!favouritesOnly && { favourites_only: true }),
      },
    });

    set((state) => ({
      companies: page == 1 ? companies : [...state.companies, ...companies],
      page: page + 1,
      hasMore: companies.length == CompanyPaginationLimit,
      loadingList: false,
      loadingMore: false,
      editAll: page == 1 ? false : state.editAll,
    }));
  },
  createCompany: async ({ company }) => {
    try {
      const { data: createdCompany } = await axios({
        method: 'POST',
        url: apiAdminCompaniesPath(company),
        data: { company },
        headers: csrfHeaders(),
      });

      set((state) => ({
        companies: [createdCompany, ...state.companies],
      }));
      toast.success(`Create new company named '${createdCompany.name}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error creating a new Company');
    }
  },
  updateCompany: async ({ company }) => {
    try {
      const sanitizedCompany = {}
      companyUpdateFields.forEach((field) => (sanitizedCompany[field] = company[field]));
      Object.keys(companyFlags).forEach((flag) => (sanitizedCompany[flag] = company[flag]));
      const { data: updatedCompany } = await axios({
        method: 'PUT',
        url: apiAdminCompanyPath(company),
        data: { company: sanitizedCompany },
        headers: csrfHeaders(),
      });

      set((state) => ({
        companies: state.companies.map((company) => (company.id === updatedCompany.id ? updatedCompany : company)),
      }));
      toast.success(`Updated ${updatedCompany.name}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error updating the company');
      throw error;
    }
  },
  setEditAll: () => {
    set((state) => ({ editAll: !state.editAll }));
  },
  setSaveAll: () => {
    const { changedCompanyIDs } = get();
    set((state) => ({
      companies: state.companies.map((company) =>
        changedCompanyIDs.includes(company.id) ? { ...company, triggerSave: true } : company
      ),
      changedCompanyIDs: [],
    }));
  },
  markCompanyAsChanged: ({ company, remove = false }) => {
    const { changedCompanyIDs } = get();
    if (remove) {
      return set({ changedCompanyIDs: changedCompanyIDs.filter((ID) => ID !== company.id) });
    }
    if (changedCompanyIDs.includes(company.id)) return;

    set({ changedCompanyIDs: [...changedCompanyIDs, company.id] });
  },
  setQuery: (query) => set({ query }),
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if (error?.response?.status === 422) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default companyAdminStore;
