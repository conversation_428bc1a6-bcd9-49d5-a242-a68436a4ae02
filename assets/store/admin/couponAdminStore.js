import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import { apiAdminCouponsPath, apiAdminCouponPath } from 'routes';

const CouponPaginationLimit = 20;

const initialState = {
  coupons: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const couponAdminStore = create((set, get) => ({
  ...initialState,
  fetchCoupons: async ({ page }) => {
    const { query } = get();
    set({ loadingList: page === 1, loadingMore: page > 1 });
    if (page === 1) {
      set({ coupons: [] });
    }
    const { data: coupons } = await axios({
      method: 'GET',
      url: apiAdminCouponsPath(),
      params: {
        query,
        page,
      },
    });

    set((state) => ({
      coupons: page === 1 ? coupons : [...state.coupons, ...coupons],
      page: page + 1,
      hasMore: coupons.length === CouponPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  createCoupon: async ({ coupon }) => {
    try {
      const { data: createdCoupon } = await axios({
        method: 'POST',
        url: apiAdminCouponsPath(),
        data: { coupon },
        headers: csrfHeaders(),
      });

      set((state) => ({
        coupons: [createdCoupon, ...state.coupons],
      }));
      toast.success(`Create new coupon with code '${createdCoupon.code}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error creating a new Coupon');
      throw new Error('There was an error creating a new coupon');
    }
  },
  updateCoupon: async ({ coupon }) => {
    try {
      const { data: updatedCoupon } = await axios({
        method: 'PUT',
        url: apiAdminCouponPath(coupon),
        data: { coupon },
        headers: csrfHeaders(),
      });
      set((state) => ({
        coupons: state.coupons.map((coupon) => (coupon.id === updatedCoupon.id ? updatedCoupon : coupon)),
      }));
      toast.success(`Updated coupon: ${updatedCoupon.code}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error updating the coupon');
      throw new Error('There was an error updating the coupon');
    }
  },
  setQuery: (query) => set({ query }),
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if (error?.response?.status === 422) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default couponAdminStore;
