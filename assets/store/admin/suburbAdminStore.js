import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import { apiAdminSuburbsPath, apiAdminSuburbPath } from 'routes';

const SuburbPaginationLimit = 20;

const initialState = {
  suburbs: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const suburbAdminStore = create((set, get) => ({
  ...initialState,
  fetchSuburbs: async ({ page }) => {
    const { query } = get();
    set({ loadingList: page === 1, loadingMore: page > 1 });
    if (page === 1) {
      set({ suburbs: [] });
    }
    const { data: suburbs } = await axios({
      method: 'GET',
      url: apiAdminSuburbsPath(),
      params: {
        query,
        page,
      },
    });

    set((state) => ({
      suburbs: page === 1 ? suburbs : [...state.suburbs, ...suburbs],
      page: page + 1,
      hasMore: suburbs.length === SuburbPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  createSuburb: async ({ suburb }) => {
    try {
      const { data: createdSuburb } = await axios({
        method: 'POST',
        url: apiAdminSuburbsPath(),
        data: { suburb },
        headers: csrfHeaders(),
      });

      set((state) => ({
        suburbs: [createdSuburb, ...state.suburbs],
      }));
      toast.success(`Create new suburb with code '${createdSuburb.label}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error creating a new Suburb');
      throw new Error('There was an error creating a new suburb');
    }
  },
  updateSuburb: async ({ suburb }) => {
    try {
      const { data: updatedSuburb } = await axios({
        method: 'PUT',
        url: apiAdminSuburbPath(suburb),
        data: { suburb },
        headers: csrfHeaders(),
      });
      set((state) => ({
        suburbs: state.suburbs.map((suburb) => (suburb.id === updatedSuburb.id ? updatedSuburb : suburb)),
      }));
      toast.success(`Updated suburb: ${updatedSuburb.label}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error updating the suburb');
      throw new Error('There was an error updating the suburb');
    }
  },
  setQuery: (query) => set({ query }),
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if ([422, 404].includes(error?.response?.status)) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default suburbAdminStore;
