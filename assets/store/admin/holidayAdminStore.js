import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import { apiAdminHolidaysPath, apiAdminHolidayPath } from 'routes';

const HolidayPaginationLimit = 20;

const initialState = {
  holidays: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const holidayAdminStore = create((set, get) => ({
  ...initialState,
  fetchHolidays: async ({ page }) => {
    const { query } = get();
    set({ loadingList: page === 1, loadingMore: page > 1 });
    if (page === 1) {
      set({ holidays: [] });
    }
    const { data: holidays } = await axios({
      method: 'GET',
      url: apiAdminHolidaysPath(),
      params: {
        query,
        page,
      },
    });

    set((state) => ({
      holidays: page === 1 ? holidays : [...state.holidays, ...holidays],
      page: page + 1,
      hasMore: holidays.length === HolidayPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  createHoliday: async ({ holiday }) => {
    try {
      const { data: createdHoliday } = await axios({
        method: 'POST',
        url: apiAdminHolidaysPath(),
        data: { holiday },
        headers: csrfHeaders(),
      });

      set((state) => ({
        holidays: [createdHoliday, ...state.holidays],
      }));
      toast.success(`Created new holiday with name '${createdHoliday.name}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error creating a new Holiday');
      throw new Error('There was an error creating a new holiday');
    }
  },
  updateHoliday: async ({ holiday }) => {
    try {
      const { data: updatedHoliday } = await axios({
        method: 'PUT',
        url: apiAdminHolidayPath(holiday),
        data: { holiday },
        headers: csrfHeaders(),
      });
      set((state) => ({
        holidays: state.holidays.map((holiday) => (holiday.id === updatedHoliday.id ? updatedHoliday : holiday)),
      }));
      toast.success(`Updated holiday: ${updatedHoliday.name}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error updating the holiday');
      throw new Error('There was an error updating the holiday');
    }
  },
  removeHoliday: async ({ holiday }) => {
    try {
      await axios({
        method: 'DELETE',
        url: apiAdminHolidayPath(holiday),
        data: { holiday },
        headers: csrfHeaders(),
      });
      set((state) => ({
        holidays: state.holidays.filter((stateHoliday) => stateHoliday.id !== holiday.id),
      }));
      toast.success(`Removed holiday: ${holiday.name}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      const errorMessage = 'There was an error removing the holiday' 
      handleError(error, errorMessage);
      throw new Error(errorMessage);
    }
  },
  setQuery: (query) => set({ query }),
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if ([422, 404].includes(error?.response?.status)) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default holidayAdminStore;
