import create from 'zustand';
import axios from 'axios';
import moment from 'moment';

import { apiAdminOrdersPath } from 'routes';

moment.locale('en-gb');

const ListPaginationLimit = 20;
const WeeklyPaginationLimit = 1000;

const initialState = {
  viewType: null,
  query: '',
  hasFavourites: false,
  favouritesOnly: false,
  customOrdersOnly: false,
  showPast: false,

  orders: [],
  dates: {
    from_date: moment(new Date()).startOf('week'),
    to_date: moment(new Date()).endOf('week'),
  },
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const orderAdminStore = create((set, get) => ({
  ...initialState,
  setFilters: (filters) => set({ ...filters }),
  setViewType: (viewType) => {
    const { customOrdersOnly } = get();
    setLocalStorage({ customOrdersOnly, viewType });

    set({ viewType, orders: [], page: 1, hasMore: true });
  },
  setFavourites: (favouritesOnly) => {
    const { customOrdersOnly, fetchOrders } = get();
    setLocalStorage({ customOrdersOnly, favouritesOnly });

    set({ favouritesOnly });
    fetchOrders({ page: 1 });
  },
  setShowPast: (showPast) => {
    const { customOrdersOnly, fetchOrders } = get();
    setLocalStorage({ customOrdersOnly, showPast });

    set({ showPast });
    fetchOrders({ page: 1 });
  },
  setQuery: (query) => {
    const { customOrdersOnly, fetchOrders } = get();
    setLocalStorage({ customOrdersOnly, query });

    set({ query });
    fetchOrders({ page: 1 });
  },
  setDates: ({ dates }) => {
    const { fetchOrders } = get();
    // will require manipulation in OrderAdminApp to convert string back to dates
    // setLocalStorage({ customOrdersOnly, dates });

    set({ dates });
    fetchOrders({ page: 1 });
  },
  fetchOrders: async ({ page }) => {
    const { viewType, query, dates, hasFavourites, favouritesOnly, showPast, customOrdersOnly } = get();
    const withDates = viewType === 'weekly';

    set({ loadingList: page === 1, loadingMore: page > 1 });
    if (page == 1) {
      set({ orders: [] });
    }
    const { data: orders } = await axios({
      method: 'GET',
      url: apiAdminOrdersPath(),
      params: {
        page,
        limit: withDates ? WeeklyPaginationLimit : ListPaginationLimit,
        ...(!!query && { query }),
        ...(!!hasFavourites && !!favouritesOnly && { favourites_only: true }),
        ...(viewType === 'list' && !!showPast && { show_past: true }),
        ...(!!customOrdersOnly && { custom_orders_only: true }),
        ...(withDates && {
          from_date: dates.from_date.format('YYYY/MM/DD'),
          to_date: dates.to_date.format('YYYY/MM/DD'),
        }),
      },
    });

    set((state) => ({
      orders: page == 1 ? orders : [...state.orders, ...orders],
      page: page + 1,
      hasMore: orders.length == ListPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
}));

function setLocalStorage({ customOrdersOnly, ...filter }) {
  const storageKey = customOrdersOnly ? 'CustomOrderFilters' : 'OrderFilters';
  const existingFilters = JSON.parse(localStorage.getItem(storageKey) || JSON.stringify({}));

  const updatedFilter = { ...existingFilters, ...filter };
  localStorage.setItem(storageKey, JSON.stringify(updatedFilter));
}

export default orderAdminStore;
