import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';

import {
  apiAdminNotificationsPath,
  apiAdminNotificationMarkNotificationAsViewedPath,
  apiAdminNotificationMarkNotificationAsAssignedPath,
} from 'routes';

const NotificationPaginationLimit = 20;

const initialState = {
  hasFavourites: false,
  favouritesOnly: true,
  query: '',
  severity: '',
  showAll: false,
  notifications: [],
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const adminNotificationsStore = create((set, get) => ({
  ...initialState,
  setFilters: (filters) => set({ ...filters }),
  setFavourites: (favouritesOnly) => {
    const { fetchNotifications } = get();
    setLocalStorage({ favouritesOnly });

    set({ favouritesOnly });
    fetchNotifications({ page: 1 });
  },
  setShowAll: (showAll) => {
    const { fetchNotifications } = get();
    setLocalStorage({ showAll });

    set({ showAll });
    fetchNotifications({ page: 1 });
  },
  setQuery: (query) => {
    const { hasFavourites, fetchNotifications } = get();
    const searchUnFavourited = !!query && !!hasFavourites;
    if (searchUnFavourited) {
      setLocalStorage({
        favouritesOnly: false,
      });
    }
    set({
      query,
      ...(searchUnFavourited && { favouritesOnly: false }),
    });

    set({ query });
    fetchNotifications({ page: 1 });
  },
  setSeverity: (severity) => {
    const { fetchNotifications } = get();

    set({ severity });
    fetchNotifications({ page: 1 });
  },
  fetchNotifications: async ({ page }) => {
    const { hasFavourites, favouritesOnly, query, severity, showAll } = get();
    set({ loadingList: page == 1, loadingMore: page > 1 });
    if (page == 1) {
      set({ notifications: [] });
    }
    const { data: notifications } = await axios({
      method: 'GET',
      url: apiAdminNotificationsPath(),
      params: {
        page,
        ...(!!query && { query }),
        ...(!!severity && { severity }),
        ...(!!hasFavourites && !!favouritesOnly && { favourites_only: true }),
        ...(!!showAll && { show_all_events: true }),
      },
    });

    set((state) => ({
      notifications: page == 1 ? notifications : [...state.notifications, ...notifications],
      page: page + 1,
      hasMore: notifications.length == NotificationPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  markNotification: async ({ type, notification }) => {
    const markEndpoint =
      type === 'viewed'
        ? apiAdminNotificationMarkNotificationAsViewedPath(notification)
        : apiAdminNotificationMarkNotificationAsAssignedPath(notification);
    const { data: updatedNotification } = await axios({
      method: 'POST',
      url: markEndpoint,
      headers: csrfHeaders(),
    });

    set((state) => ({
      notifications: state.notifications.map((notification) =>
        notification.id === updatedNotification.id ? updatedNotification : notification
      ),
    }));
  },
  setMarkAllAsViewed: () => {
    set((state) => ({
      notifications: state.notifications.map((notification) => ({
        ...notification,
        viewed: true,
      })),
    }));
  },
}));

function setLocalStorage({ ...filter }) {
  const storageKey = 'NotificationFilters';
  const existingFilters = JSON.parse(localStorage.getItem(storageKey) || JSON.stringify({}));
  delete existingFilters.query; // only required for a while. TO-DO - remove after a few weeks

  const updatedFilter = { ...existingFilters, ...filter };
  localStorage.setItem(storageKey, JSON.stringify(updatedFilter));
}

export default adminNotificationsStore;
