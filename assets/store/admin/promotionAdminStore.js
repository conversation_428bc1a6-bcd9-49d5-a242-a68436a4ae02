import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import { apiAdminPromotionsPath, apiAdminPromotionPath, apiAdminPromotionSubscriptionsPath } from 'routes';

const PromotionPaginationLimit = 20;

const initialState = {
  promotions: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const promotionAdminStore = create((set, get) => ({
  ...initialState,
  fetchPromotions: async ({ page }) => {
    const { query } = get();
    set({ loadingList: page === 1, loadingMore: page > 1 });
    if (page === 1) {
      set({ promotions: [] });
    }
    const { data: promotions } = await axios({
      method: 'GET',
      url: apiAdminPromotionsPath(),
      params: {
        query,
        page,
      },
    });

    set((state) => ({
      promotions: page === 1 ? promotions : [...state.promotions, ...promotions],
      page: page + 1,
      hasMore: promotions.length === PromotionPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  createPromotion: async ({ promotion }) => {
    try {
      const { data: createdPromotion } = await axios({
        method: 'POST',
        url: apiAdminPromotionsPath(),
        data: { promotion },
        headers: csrfHeaders(),
      });

      set((state) => ({
        promotions: [createdPromotion, ...state.promotions],
      }));
      toast.success(`Create new promotion with name '${createdPromotion.name}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error creating a new Promotion');
      throw new Error('There was an error creating a new promotion');
    }
  },
  updatePromotion: async ({ promotion }) => {
    try {
      const { data: updatedPromotion } = await axios({
        method: 'PUT',
        url: apiAdminPromotionPath(promotion),
        data: { promotion },
        headers: csrfHeaders(),
      });
      set((state) => ({
        promotions: state.promotions.map((promotion) => (promotion.id === updatedPromotion.id ? updatedPromotion : promotion)),
      }));
      toast.success(`Updated promotion: ${updatedPromotion.name}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error updating the promotion');
      throw new Error('There was an error updating the promotion');
    }
  },
  updateSubscriptions: async ({ promotion, subscriptions }) => {
    const sanitizedSubscriptions = subscriptions.map(({ id, subscriber_type, subscriber_id, active, _delete }) => ({
      id,
      subscriber_type,
      subscriber_id,
      active,
      ...(!!_delete && { _delete: true }),
    }))
    const { data: updatedPromotion } = await axios({
      method: 'POST',
      url: apiAdminPromotionSubscriptionsPath(promotion),
      headers: csrfHeaders(),
      data: { subscriptions: sanitizedSubscriptions },
    });
    set((state) => ({
      promotions: state.promotions.map((promotion) => (promotion.id === updatedPromotion.id ? updatedPromotion : promotion)),
    }));
    toast.success(`Updated subscriptions for '${promotion.name}'`, { ...defaultToastOptions, autoClose: 5000 });
  },
  setQuery: (query) => set({ query }),
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if (error?.response?.status === 422) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default promotionAdminStore;
