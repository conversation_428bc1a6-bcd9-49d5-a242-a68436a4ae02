import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { supplierUpdateFields } from 'utilities/adminHelpers';
import {
  apiAdminSuppliersPath,
  apiAdminSupplierPath,
  apiAdminSupplierSupplierMarkupOverridesPath,
  apiAdminUserPath,
  apiAdminUserDeprecateUserPath,
} from 'routes';

import { userUpdateFields } from 'utilities/adminHelpers';

import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const SupplierPaginationLimit = 20;

const initialState = {
  suppliers: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
  editAll: false,
  changedSupplierIDs: [],
};

const supplierAdminStore = create((set, get) => ({
  ...initialState,
  fetchSuppliers: async ({ page, passedQuery = '' }) => {
    const { query } = get();
    set({ loadingList: page == 1, loadingMore: page > 1 });
    if (passedQuery) {
      set({ query: passedQuery });
    }
    if (page == 1) {
      set({ suppliers: [] });
    }
    const { data: suppliers } = await axios({
      method: 'GET',
      url: apiAdminSuppliersPath(),
      params: {
        query: query || passedQuery,
        page,
      },
    });

    set((state) => ({
      suppliers: page == 1 ? suppliers : [...state.suppliers, ...suppliers],
      page: page + 1,
      hasMore: suppliers.length == SupplierPaginationLimit,
      loadingList: false,
      loadingMore: false,
      editAll: page == 1 ? false : state.editAll,
    }));
  },
  updateSupplier: async ({ supplier }) => {
    const sanitizedSupplier = {}
    supplierUpdateFields.forEach((field) => (sanitizedSupplier[field] = supplier[field]));
    const { data: updatedSupplier } = await axios({
      method: 'PUT',
      url: apiAdminSupplierPath(supplier),
      data: { supplier_profile: sanitizedSupplier },
      headers: csrfHeaders(),
    });

    set((state) => ({
      suppliers: state.suppliers.map((supplier) => (supplier.id === updatedSupplier.id ? updatedSupplier : supplier)),
    }));
  },
  setEditAll: () => {
    const { editAll } = get();
    set({ editAll: !editAll });
  },
  setSaveAll: () => {
    const { changedSupplierIDs } = get();
    set((state) => ({
      suppliers: state.suppliers.map((supplier) =>
        changedSupplierIDs.includes(supplier.id) ? { ...supplier, triggerSave: true } : supplier
      ),
      changedSupplierIDs: [],
    }));
  },
  markSupplierAsChanged: ({ supplier, remove = false }) => {
    const { changedSupplierIDs } = get();
    if (remove) {
      return set({ changedSupplierIDs: changedSupplierIDs.filter((ID) => ID !== supplier.id) });
    }
    if (changedSupplierIDs.includes(supplier.id)) return;

    set({ changedSupplierIDs: [...changedSupplierIDs, supplier.id] });
  },
  updateSupplierMarkupOverrides: async ({ supplier, markupOverrides }) => {
    const sanitizedMarkupOverrides = markupOverrides.map(({ id, overridable_type, overridable_id, markup, commission_rate, active, _delete }) => ({
      id,
      overridable_type,
      overridable_id,
      markup,
      commission_rate,
      active,
      ...(!!_delete && { _delete: true }),
    }));
    const { data: updatedSupplier } = await axios({
      method: 'POST',
      url: apiAdminSupplierSupplierMarkupOverridesPath(supplier),
      headers: csrfHeaders(),
      data: { markup_overrides: sanitizedMarkupOverrides },
    });

    set((state) => ({
      suppliers: state.suppliers.map((supplier) => (supplier.id === updatedSupplier.id ? updatedSupplier : supplier)),
    }));
    toast.success(`Updated markup overrides for '${supplier.name}'`, { ...defaultToastOptions, autoClose: 5000 });
  },
  updateSupplierUser: async ({ profileable, user }) => {
    try {
      const sanitizedUser = {}
      userUpdateFields.forEach((field) => {
        if (user[field]) {
          sanitizedUser[field] = user[field]  
        }
      });

      if (!Object.keys(sanitizedUser).length) return;

      const { data: updatedSupplier } = await axios({
        method: 'PUT',
        url: apiAdminUserPath(profileable, { profileable_type: 'SupplierProfile'}),
        headers: csrfHeaders(),
        data: { user: sanitizedUser },
      });

      set((state) => ({
        suppliers: state.suppliers.map((supplier) => (supplier.id === updatedSupplier.id ? updatedSupplier : supplier)),
      }));
      toast.success(`Updated user settings for '${profileable.name}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      const {
        data: { errors },
      } = error.response;
      errors.map((errorMessage) => toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 }));
    }
  },
  deprecateSupplierUser: async ({ profileable }) => {
    try {
      const { data: updatedSupplier } = await axios({
        method: 'POST',
        url: apiAdminUserDeprecateUserPath(profileable, { profileable_type: 'SupplierProfile'}),
        headers: csrfHeaders(),
      });
      set((state) => ({
        suppliers: state.suppliers.map((supplier) => (supplier.id === updatedSupplier.id ? updatedSupplier : supplier)),
      }));
      toast.warning('User successfully deprecated!', { ...defaultToastOptions, autoClose: 10000 });
    } catch (error) {
      const {
        data: { errors },
      } = error.response;
      errors.map((errorMessage) => toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 }));
    }
  },
  setQuery: (query) => set({ query }),
}));

export default supplierAdminStore;
