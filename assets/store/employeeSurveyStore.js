import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import {
  apiSurveyQuestionsPath,
  apiSurveyQuestionPath,
  apiEmployeeSurveyEmployeeSurveySubmissionsPath,
  apiEmployeeSurveyPath,
  apiEmployeeSurveysPath,
} from 'routes';

const surveyInitialState = {
  id: null,
  questions: [],
  active: true,
  loading: true,
};

const initialState = {
  surveys: [
    { ...surveyInitialState, category_group: 'catering-services' },
    { ...surveyInitialState, category_group: 'kitchen-supplies' },
  ],
  activeSurvey: null,
};

const useSurveyStore = create((set, get) => ({
  ...initialState,
  fetchSurveys: async () => {
    const { data: surveys } = await axios(apiEmployeeSurveysPath({ format: 'json' }));
    const activeSurvey = surveys[0]?.category_group_name ? surveys[0]?.category_group_name.toLowerCase() : 'catering';
    set((state) => ({
      surveys: state.surveys.map((stateSurvey) => {
        const survey = surveys.find((survey) => survey.category_group === stateSurvey.category_group);
        return survey || { ...stateSurvey, loading: false };
      }),
      activeSurvey,
    }));
  },
  createSurvey: async ({ category_group, prefill }) => {
    try {
      const { data: survey } = await axios({
        method: 'POST',
        url: apiEmployeeSurveysPath({ format: 'json' }),
        data: { prefill, employee_survey: { category_group } },
        headers: csrfHeaders(),
      });
      set((state) => ({
        surveys: state.surveys.map((stateSurvey) => {
          if (survey.category_group === stateSurvey.category_group) {
            return survey;
          }
          return stateSurvey;
        }),
      }));
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
  updateSurvey: async ({ survey, prefill }) => {
    const { questions, ...sanitizedSurvey } = survey;
    try {
      const { data: responseSurvey } = await axios({
        method: 'PUT',
        url: apiEmployeeSurveyPath(survey, { format: 'json' }),
        data: { prefill, employee_survey: sanitizedSurvey },
        headers: csrfHeaders(),
      });
      set((state) => ({
        surveys: state.surveys.map((stateSurvey) => {
          if (responseSurvey.id === stateSurvey.id) {
            return responseSurvey;
          }
          return stateSurvey;
        }),
      }));
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
  setActiveSurvey: (activeSurvey) => set({ activeSurvey }),
  createQuestion: async ({ question, survey }) => {
    try {
      const { data: responseQuestion } = await axios({
        method: 'POST',
        url: apiSurveyQuestionsPath({ format: 'json' }),
        data: { survey_question: question },
        headers: csrfHeaders(),
      });
      set((state) => ({
        surveys: state.surveys.map((stateSurvey) => {
          if (stateSurvey.id === survey.id) {
            return {
              ...stateSurvey,
              questions: [...stateSurvey.questions, responseQuestion],
            };
          }

          return stateSurvey;
        }),
      }));
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
  dragQuestion: ({ survey, dragIndex, hoverIndex }) => {
    if (!dragIndex) return;
    const { questions } = survey;
    const draggedItem = { ...questions[dragIndex], is_sorted: true };
    const hoveredItem = { ...questions[hoverIndex], is_sorted: true };
    const draggedPosition = draggedItem.position;
    const hoveredPosition = hoveredItem.position;
    const updatedQuestions = [...questions];
    updatedQuestions.splice(hoverIndex, 1, { ...hoveredItem, position: draggedPosition }); // update hovered Item (mark as sorted)
    updatedQuestions.splice(dragIndex, 1); // remove dragged item
    updatedQuestions.splice(hoverIndex, 0, { ...draggedItem, position: hoveredPosition }); // add the dragged item in new place (and mark as sorted)
    set((state) => ({
      surveys: state.surveys.map((stateSurvey) => {
        if (stateSurvey.id === survey.id) {
          return {
            ...stateSurvey,
            questions: updatedQuestions,
          };
        }

        return stateSurvey;
      }),
    }));
  },
  sortQuestions: ({ survey }) => {
    const currentSurvey = get().surveys.find((stateSurvey) => stateSurvey.id == survey.id);
    const sortedQuestions = currentSurvey.questions.filter((question) => question.is_sorted);
    sortedQuestions.forEach((question) => {
      get().updateQuestion({
        question,
        survey,
      });
    });
  },
  updateQuestion: async ({ question, survey }) => {
    try {
      const { data: responseQuestion } = await axios({
        method: 'PUT',
        url: apiSurveyQuestionPath(question, { format: 'json' }),
        data: { survey_question: question },
        headers: csrfHeaders(),
      });
      set((state) => ({
        surveys: state.surveys.map((stateSurvey) => {
          if (stateSurvey.id === survey.id) {
            return {
              ...stateSurvey,
              questions: stateSurvey.questions
                .map((question) => {
                  if (question.id === responseQuestion.id) {
                    return responseQuestion;
                  }
                  return question;
                })
                .filter((question) => question.active),
            };
          }

          return stateSurvey;
        }),
      }));
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
  fetchSubmissions: async ({ dates, survey }) => {
    try {
      const { data: response } = await axios({
        method: 'GET',
        url: apiEmployeeSurveyEmployeeSurveySubmissionsPath(survey, { format: 'json' }),
        params: { starts_on: dates.start, ends_on: dates.end },
      });
      set((state) => ({
        surveys: state.surveys.map((stateSurvey) => {
          if (stateSurvey.id === survey.id) {
            return {
              ...stateSurvey,
              ...response,
            };
          }
          return stateSurvey;
        }),
      }));
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
}));

function handleError(error) {
  if (error.response.status === 422) {
    const error_messages = error.response.data.errors;
    alert(error_messages.join('. '));
  } else {
    alert('Oops! Something went wrong!. Please try again');
  }
}

export default useSurveyStore;
