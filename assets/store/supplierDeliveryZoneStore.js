import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';

import { apiDeliveryZonesPath, apiDeliveryZonePath, apiSupplierPath } from 'routes';

import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const initialState = {
  loadingList: false,
  loadingZones: [],
  delivery_zones: [],
  minimumDeliveryFee: 0,
};

const useSupplierDeliveryZonesStore = create((set, get) => ({
  ...initialState,
  setMinimumDeliveryFee: (minimumDeliveryFee) => set({ minimumDeliveryFee }),
  fetchDeliveryZones: async () => {
    set({ loadingList: true });

    const { data: delivery_zones } = await axios({
      method: 'GET',
      url: apiDeliveryZonesPath({ format: 'json' }),
    });

    set({
      delivery_zones,
      loadingList: false,
    });
  },
  createDeliveryZone: async ({ deliveryZone }) => {
    set((state) => ({ loadingZones: [...state.loadingZones, 'new'] }));
    try {
      const { id, ...sanitizedDeliveryZone } = deliveryZone;
      const { data: createdDeliveryZone } = await axios({
        method: 'POST',
        url: apiDeliveryZonesPath({ format: 'json' }),
        data: { delivery_zone: sanitizedDeliveryZone },
        headers: csrfHeaders(),
      });
      set((state) => ({
        loadingZones: state.loadingZones.filter((zoneId) => zoneId != 'new'),
        delivery_zones: [createdDeliveryZone, ...state.delivery_zones],
      }));
      toast.dismiss();
      toast.success(
        `Successfully created a delivery zone for ${createdDeliveryZone.suburb_label}`,
        defaultToastOptions
      );
    } catch (error) {
      set((state) => ({ loadingZones: state.loadingZones.filter((zoneId) => zoneId != 'new') }));
      handleError(error);
      return Promise.reject(error);
    }
  },
  updateDeliveryZone: async ({ deliveryZone }) => {
    set((state) => ({ loadingZones: [...state.loadingZones, deliveryZone.id] }));
    try {
      const { suburb_id, radius, delivery_fee, operating_wdays, operating_hours_start, operating_hours_end, ...rest } =
        deliveryZone;
      const sanitizedDeliveryZone = {
        suburb_id,
        radius,
        delivery_fee,
        operating_wdays,
        operating_hours_start,
        operating_hours_end,
      };
      const { data: updatedDeliveryZone } = await axios({
        method: 'PUT',
        url: apiDeliveryZonePath(deliveryZone, { format: 'json' }),
        data: { delivery_zone: sanitizedDeliveryZone },
        headers: csrfHeaders(),
      });
      set((state) => ({
        loadingZones: state.loadingZones.filter((zoneId) => zoneId != updatedDeliveryZone.id),
        delivery_zones: state.delivery_zones.map((stateDeliveryZone) => {
          if (updatedDeliveryZone.id === stateDeliveryZone.id) {
            return updatedDeliveryZone;
          }
          return stateDeliveryZone;
        }),
      }));
      toast.success(`Successfully UPDATED delivery zone for ${updatedDeliveryZone.suburb_label}`, defaultToastOptions);
    } catch (error) {
      set((state) => ({ loadingZones: state.loadingZones.filter((zoneId) => zoneId != deliveryZone.id) }));
      handleError(error);
      return Promise.reject(error);
    }
  },
  removeDeliveryZone: async ({ deliveryZone }) => {
    set((state) => ({ loadingZones: [...state.loadingZones, deliveryZone.id] }));
    try {
      await axios({
        method: 'DELETE',
        url: apiDeliveryZonePath(deliveryZone, { format: 'json' }),
        headers: csrfHeaders(),
      });
      set((state) => ({
        loadingZones: state.loadingZones.filter((zoneId) => zoneId != deliveryZone.id),
        delivery_zones: state.delivery_zones.filter((stateDeliveryZone) => stateDeliveryZone.id != deliveryZone.id),
      }));
      toast.success(`Successfully REMOVED the delivery zone for ${deliveryZone.suburb_label}`, defaultToastOptions);
    } catch (error) {
      set((state) => ({ loadingZones: state.loadingZones.filter((zoneId) => zoneId != deliveryZone.id) }));
      handleError(error);
      return Promise.reject(error);
    }
  },
  updateMinimDeliveryFee: async ({ minimumDeliveryFee, supplierID }) => {
    try {
      const { data: payload } = await axios({
        method: 'PUT',
        url: apiSupplierPath(supplierID, { format: 'json' }),
        data: { supplier_profile: { minimum_delivery_fee: minimumDeliveryFee } },
        headers: csrfHeaders(),
      });
      set({ minimumDeliveryFee });
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
}));

function handleError(error) {
  let errorMessage;
  if (error?.response?.status === 422) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default useSupplierDeliveryZonesStore;
