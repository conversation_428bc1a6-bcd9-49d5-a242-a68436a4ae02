import create from 'zustand';
import axios from 'axios';
import { apiMinimumsPath, apiMinimumPath, apiSupplierPath } from 'routes';
import { csrfHeaders } from 'utilities/csrfHeaders';

const initialState = {
  minimums: [],
  leadMode: 'by_hour',
  loadingList: false,
  loadingCategories: [],
};

const useSupplierMinimumStore = create((set, get) => ({
  ...initialState,
  setLeadMode: (leadMode) => set({ leadMode }),
  fetchMinimums: async () => {
    set({ loadingList: true });
    const { data: minimums } = await axios({
      method: 'GET',
      url: apiMinimumsPath({ format: 'json' }),
    });

    set({
      minimums,
      loadingList: false,
    });
  },
  createMinimum: async ({ minimum }) => {
    set((state) => ({ loadingCategories: [...state.loadingCategories, minimum.category_id] }));
    try {
      const { data: createdMinimum } = await axios({
        method: 'POST',
        url: apiMinimumsPath({ format: 'json' }),
        data: { minimum },
        headers: csrfHeaders(),
      });
      set((state) => ({
        loadingCategories: state.loadingCategories.filter((category_id) => category_id != createdMinimum.category_id),
        minimums: state.minimums.map((stateMinimum) => {
          if (createdMinimum.category_id === stateMinimum.category_id) {
            return createdMinimum;
          }
          return stateMinimum;
        }),
      }));
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
  updateMinimum: async ({ minimum }) => {
    set((state) => ({ loadingCategories: [...state.loadingCategories, minimum.category_id] }));
    try {
      const { data: updatedMinimum } = await axios({
        method: 'PUT',
        url: apiMinimumPath(minimum, { format: 'json' }),
        data: { minimum },
        headers: csrfHeaders(),
      });
      set((state) => ({
        loadingCategories: state.loadingCategories.filter((category_id) => category_id != updatedMinimum.category_id),
        minimums: state.minimums.map((stateMinimum) => {
          if (updatedMinimum.id === stateMinimum.id) {
            return updatedMinimum;
          }
          return stateMinimum;
        }),
      }));
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
  removeMinimum: async ({ minimum }) => {
    set((state) => ({ loadingCategories: [...state.loadingCategories, minimum.category_id] }));
    try {
      const { data: resetMinimum } = await axios({
        method: 'DELETE',
        url: apiMinimumPath(minimum, { format: 'json' }),
        headers: csrfHeaders(),
      });
      set((state) => ({
        loadingCategories: state.loadingCategories.filter((category_id) => category_id != resetMinimum.category_id),
        minimums: state.minimums.map((stateMinimum) => {
          if (resetMinimum.category_id === stateMinimum.category_id) {
            return resetMinimum;
          }
          return stateMinimum;
        }),
      }));
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
  updateLeadMode: async ({ supplierID, leadMode }) => {
    set({ loadingList: true });
    try {
      const { data: payload } = await axios({
        method: 'PUT',
        url: apiSupplierPath(supplierID, { format: 'json' }),
        data: { supplier_profile: { lead_mode: leadMode } },
        headers: csrfHeaders(),
      });
      set({ leadMode });
      get().fetchMinimums();
    } catch (error) {
      set({ loadingList: false });
      handleError(error);
      return Promise.reject(error);
    }
  },
}));

function handleError(error) {
  if (error.response.status === 422) {
    const error_messages = error.response.data.errors;
    alert(error_messages.join('. '));
  } else {
    alert('Oops! Something went wrong!. Please try again');
  }
}

export default useSupplierMinimumStore;
