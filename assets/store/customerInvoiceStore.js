import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiInvoicesPath, apiInvoicePath, apiInvoiceAttachOrdersPath, apiInvoiceGenerateDocumentsPath} from 'routes';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const InvoicePaginationLimit = 10;

const initialState = {
  loadingInvoices: false,
  loadingMore: false,
  hasMore: true,
  invoices: [],
  page: 1,
  query: '',
};

const useCustomerInvoiceStore = create((set, get) => ({
  ...initialState,
  fetchInvoices: async ({ page }) => {
    set({ loadingInvoices: page == 1, loadingMore: page > 1 });

    const { data: invoices } = await axios({
      method: 'GET',
      url: apiInvoicesPath({ format: 'json' }),
      params: { page },
    });

    set((state) => ({
      invoices: [...state.invoices, ...invoices],
      page: page + 1,
      hasMore: invoices.length == InvoicePaginationLimit,
      loadingInvoices: false,
      loadingMore: false,
    }));

    // filter invoices after fetch
    if (get().query) get().filterInvoices(get().query);
  },
  setQuery: (query) => {
    set({ query });
    // filter after query update
    get().filterInvoices(query);
  },
  createInvoice: async ({ invoice }) => {
    try {
      const { data: createdInvoice } = await axios({
        method: 'POST',
        url: apiInvoicesPath({ format: 'json' }),
        data: { invoice },
        headers: csrfHeaders(),
      });

      set((state) => ({
        invoices: [createdInvoice, ...state.invoices],
      }));
      toast.success(`Created new invoice with number '${createdInvoice.number}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error creating a new Invoice');
      return Promise.reject(error);
    }
  },
  updateInvoice: async ({ invoice }) => {
    const { data: updatedInvoice } = await axios({
      method: 'PUT',
      url: apiInvoicePath(invoice, { format: 'json' }),
      data: { invoice },
      headers: csrfHeaders(),
    });

    set((state) => ({
      invoices: state.invoices.map((invoice) => {
        if (invoice.id == updatedInvoice.id) {
          return updatedInvoice;
        }
        return invoice;
      }),
    }));
  },
  filterInvoices: (query) => {
    const { invoices } = get();
    const regex = new RegExp(`^${query}`);
    const filteredInvoices = invoices.map((invoice) => {
      const foundByNumber = invoice.number.toString().match(regex);
      const foundByID = invoice.order_ids.find((id) => id.toString().match(regex));
      const foundByName = invoice.order_names.find((name) => name.toLowerCase().includes(query.toLowerCase()));
      return { ...invoice, filteredOut: !foundByNumber && !foundByID && !foundByName };
    });
    set({ invoices: filteredInvoices });
  },
  setInvoiceOrders: ({invoice, invoiceOrders}) => {
    set((state) => {
      return {
        invoices: state.invoices.map((stateInvoice) => {
          return stateInvoice.id == invoice.id ? {...stateInvoice, orders: invoiceOrders } : stateInvoice
        })
      }
    })
  },
  updateInvoiceOrders: async ({invoice, invoiceOrders }) => {
    try {
      const order_ids = invoiceOrders.map((order) => order.id );
      const { data: updatedInvoice } = await axios({
        method: 'POST',
        url: apiInvoiceAttachOrdersPath(invoice, { format: 'json' }),
        data: { order_ids },
        headers: csrfHeaders(),
      });
      set((state) => ({
        invoices: state.invoices.map((stateInvoice) => {
          return (stateInvoice.id == updatedInvoice.id) ? {...updatedInvoice, orders: invoiceOrders} : stateInvoice;
        }),
      }));
    } catch (error) {
      handleError(error, 'Could not save orders against the Invoice');
      return Promise.reject(error);
    }
  },
  generateDocuments: async ({ invoice, notifyCustomer }) => {
    try {
      const { data: updatedInvoice } = await axios({
        method: 'GET',
        url: apiInvoiceGenerateDocumentsPath(invoice),
        params: {
          ...(notifyCustomer && { notify_customer: true })
        }
      });
      set((state) => ({
        invoices: state.invoices.map((stateInvoice) => {
          return stateInvoice.id == updatedInvoice.id ? {...stateInvoice, status: updatedInvoice.status, documents: updatedInvoice.documents, emails: updatedInvoice.emails } : stateInvoice;
        }),
      }));
      const newlyGeneratedDocuments = updatedInvoice.documents[0].documents.filter((document) => document.is_newly_generated);
      return newlyGeneratedDocuments
    } catch (error) {
      handleError(error, 'Could not generate new invoice documents. Please try again!');
      return Promise.reject(error);
    }
  },
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if ([422, 404].includes(error?.response?.status) && error?.response?.data?.errors) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default useCustomerInvoiceStore;
