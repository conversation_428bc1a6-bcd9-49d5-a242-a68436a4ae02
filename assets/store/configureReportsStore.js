import create from 'zustand';
import axios from 'axios';
import { apiBudgetsPath, apiReportsPath } from 'routes';
import { dateToDateString, getMonthsAgoDate, getReportMonthLabel } from 'utilities/graph-options';

const initialState = {
  activeDoughnut: { category: 'catering', supplier: 'ethical' },
  csv: null,
  dates: {
    start: getMonthsAgoDate(5),
    end: getMonthsAgoDate(0),
  },
  labels: {
    start: getReportMonthLabel(getMonthsAgoDate(5)),
    end: getReportMonthLabel(getMonthsAgoDate(0)),
    PO: null,
  },
  loadingReport: true,
  report: null,
  PO: {
    id: null,
    label: null,
  },
  exporting: false,
  loadingBudgets: false,
  budgets: [],
};

const useReportsStore = create((set, get) => ({
  ...initialState,
  fetchReport: async () => {
    const { start, end } = get().dates;
    const { id } = get().PO;

    set({ loadingReport: true });
    const { data: report } = await axios(apiReportsPath({ react_data: true, format: 'json', report_type: 'monthly' }), {
      params: {
        start_date: dateToDateString(start, '01'),
        end_date: dateToDateString(end, new Date(end.getFullYear(), end.getMonth() + 1, 0).getDate()),
        purchase_order_id: id,
      },
    });
    set({ report, loadingReport: false });
  },
  fetchBudgets: async () => {
    set({ loadingBudgets: true });
    const { data: budgets } = await axios.get(apiBudgetsPath({ format: 'json' }));

    set({ budgets, loadingBudgets: false });
  },
  setDate: (key, value) => set((state) => ({ dates: { ...state.dates, [key]: value } })),
  setActiveDoughnut: (doughnut) => set((state) => ({ activeDoughnut: { ...state.activeDoughnut, ...doughnut } })),
  setLabels: (newLabels) => set((state) => ({ labels: { ...state.labels, ...newLabels } })),
  setPO: (PO) => set((state) => ({ PO: { ...state.PO, ...PO } })),
  setExporting: (bool) => set(() => ({ exporting: bool })),
}));

export default useReportsStore;
