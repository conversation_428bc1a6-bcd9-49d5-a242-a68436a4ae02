import create from 'zustand';
import axios from 'axios';
import { apiQuotesPath, apiQuotePath } from 'routes';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const QuotePaginationLimit = 10;

const initialState = {
  loadingQuotes: false,
  loadingMore: false,
  hasMore: true,
  quotes: [],
  page: 1,
};

const customerQuotesStore = create((set, get) => ({
  ...initialState,
  fetchQuotes: async ({ page }) => {
    set({ loadingQuotes: page == 1, loadingMore: page > 1 });

    const { data: quotes } = await axios({
      method: 'GET',
      url: apiQuotesPath({ format: 'json' }),
      params: { page },
    });

    set((state) => ({
      quotes: [...state.quotes, ...quotes],
      page: page + 1,
      hasMore: quotes.length == QuotePaginationLimit,
      loadingQuotes: false,
      loadingMore: false,
    }));
  },
  removeQuote: async ({ quote }) => {
    try {
      const { data } = await axios({
        method: 'DELETE',
        url: apiQuotePath(quote, { format: 'json' }),
        headers: csrfHeaders(),
      });

      set((state) => ({
        quotes: state.quotes.filter((stateQuote) => stateQuote.id !== quote.id),
      }));
      toast.success('Quote Removed!', { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error removing the quote');
    }
  },
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if ([422, 404].includes(error?.response?.status) && error?.response?.data?.errors) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default customerQuotesStore;
