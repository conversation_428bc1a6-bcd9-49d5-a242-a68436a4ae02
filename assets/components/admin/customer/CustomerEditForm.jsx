import { useState, useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import customerAdminStore from 'store/admin/customerAdminStore';
import appContext from 'contexts/appContext';

const CustomerEditForm = ({ customer, setIsEdit }) => {
  const [localCustomer, setLocalCustomer] = useState(customer);

  const { updateCustomer, markCustomerAsChanged, changedCustomerIDs } = customerAdminStore(
    (state) => ({
      updateCustomer: state.updateCustomer,
      markCustomerAsChanged: state.markCustomerAsChanged,
      changedCustomerIDs: state.changedCustomerIDs,
    }),
    shallow
  );

  const { isSuperAdmin } = useContext(appContext);

  const isUpdated = changedCustomerIDs.includes(customer.id);

  const handleSave = async () => {
    await updateCustomer({
      customer: localCustomer,
    });
    setIsEdit(false);
    markCustomerAsChanged({
      customer: localCustomer,
      remove: true,
    });
  };

  const handleCancel = () => {
    markCustomerAsChanged({
      customer: localCustomer,
      remove: true,
    });
    setIsEdit(false);
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalCustomer((state) => ({ ...state, [field]: value }));
    markCustomerAsChanged({
      customer: localCustomer,
    });
  };

  useEffect(() => {
    if (customer.triggerSave) {
      handleSave();
    }
  }, [customer.triggerSave]);

  return (
    <div className="list-item">
      <div className="list-flex-4">
        <strong>{customer.name}</strong>
        <br />({isUpdated ? 'Updated' : 'Editing'})
      </div>
      {isSuperAdmin && (
        <div className="list-flex-3">
          <label>
            <input
              type="checkbox"
              name="admin"
              checked={localCustomer.admin}
              onChange={handleChange}
            />
            Is Yordar Admin
          </label>
        </div>  
      )}
      <div className="list-flex-3">
        <label>
          <input type="checkbox" name="team_admin" checked={localCustomer.team_admin} onChange={handleChange} />
          Is Team Admin
        </label>
      </div>
      <div className="list-flex-3">
        <label>
          <input
            type="checkbox"
            name="company_team_admin"
            checked={localCustomer.company_team_admin}
            onChange={handleChange}
          />
          Is Company Team Admin
        </label>
      </div>
      <div className="list-flex-2">
        <a className="button tiny mr-1-2" onClick={handleSave}>
          Save
        </a>
        <a className="button tiny gray-btn" onClick={handleCancel}>
          Cancel
        </a>
      </div>
    </div>
  );
};

export default CustomerEditForm;
