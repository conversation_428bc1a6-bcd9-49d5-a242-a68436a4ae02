import { useRef, useState, useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import customerAdminStore from 'store/admin/customerAdminStore';
import appContext from 'contexts/appContext';
import InvitationForm from './invite/InvitationForm';

const CustomerSearch = () => {
  const isMounted = useRef(false);
  const [inviteUsers, setInviteUsers] = useState(false);

  const { customerInviteLink, canManageCustomers } = useContext(appContext);

  const { query, setQuery, editAll, setEditAll, setSaveAll, changedCustomerIDs } = customerAdminStore(
    (state) => ({
      query: state.query,
      setQuery: state.setQuery,
      editAll: state.editAll,
      setEditAll: state.setEditAll,
      setSaveAll: state.setSaveAll,
      changedCustomerIDs: state.changedCustomerIDs,
    }),
    shallow
  );

  const [localQuery, setLocalQuery] = useState(query);

  useEffect(() => {
    if (localQuery && localQuery.length < 3) return;
    if (isMounted.current && query !== localQuery) {
      const delayDebounceFn = setTimeout(() => {
        setQuery(localQuery);
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [localQuery]);

  useEffect(() => setLocalQuery(query), [query]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by name, email address or company name"
        style={{ maxWidth: '500px' }}
        type="search"
        value={localQuery}
        onChange={(event) => setLocalQuery(event.target.value)}
      />
      {!!customerInviteLink && (
        <a className="invite-button button" onClick={() => setInviteUsers((state) => !state)}>
          Invite Users
        </a>
      )}
      <InvitationForm
        inviteType='customer'
        inviteLink={customerInviteLink}
        sliderOpen={inviteUsers}
        setSliderOpen={setInviteUsers}
      />
      {canManageCustomers && (
        <div>
          {editAll && !!changedCustomerIDs.length && (
            <a className="mr-1 button" onClick={() => setSaveAll()}>
              Save ({changedCustomerIDs.length}) Customers
            </a>
          )}
          <a className={`button${editAll ? ' gray-btn' : ''}`} onClick={() => setEditAll()}>
            {editAll ? 'Cancel Edit All' : 'Edit All'}
          </a>
        </div>
      )}
    </div>
  );
};

export default CustomerSearch;
