import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import { apiAdminCustomersPath, apiAdminCustomerAccessPermissionsPath } from 'routes';

// store
import shallow from 'zustand/shallow';
import customerAdminStore from 'store/admin/customerAdminStore';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

// components
import AccessPermission from './AccessPermission';

const initialPermission = {
  customer_profile_id: null,
  scope: 'full_access',
  customer: {
    name: '',
    email: '',
  }
}

const AccessPermissions = ({ customer, setEditPermissions }) => {
  const [accessPermissions, setAccessPermissions] = useState([]);

  useEffect(async () => {
    const { data: responsePermissions } = await axios({
      method: 'GET',
      url: apiAdminCustomerAccessPermissionsPath(customer),
    });

    setAccessPermissions(responsePermissions)
  }, [])

  const { updateCustomerPermissions } = customerAdminStore(
    (state) => ({
      updateCustomerPermissions: state.updateCustomerPermissions,
    }),
    shallow
  );

  const promiseOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: { query },
    });

    return responseCustomers.map((customer) => {
      let label = `${customer.name} [${customer.email}]`;
      if (customer.company_team_admin)
        label +=  ' [CA]';
      return {
        value: customer.id,
        label,
        id: customer.id,
        name: customer.name,
        email: customer.email,
        company_team_admin: customer.company_team_admin,
      };
    });
  }, 1000);

  const handleSave = async () => {
    try {
      await updateCustomerPermissions({
        customer,
        accessPermissions
      });
      setEditPermissions(false);
    } catch (error) {
      const {
        data: { errors },
      } = error.response;
      errors.map((errorMessage) => toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 }));
    }
  };

  const handleSelect = (selectedCustomer) => {
    if (accessPermissions.find((permission) => permission.customer_profile_id === selectedCustomer.id)) {
      toast.warning('Customer already within list of access permissions', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }

    const customerPermissions = {
      ...initialPermission,
      customer_profile_id: selectedCustomer.id,
      customer: {
        name: selectedCustomer.name,
        email: selectedCustomer.email,
        company_team_admin: selectedCustomer.company_team_admin,
      }
    }
    setAccessPermissions((state) => [customerPermissions ,...state])
  };

  const handleUpdate = (updatedPermission) => {
    setAccessPermissions((state) => {
      return state.map((statePermission) => {
        const isSameID = updatedPermission.id && statePermission.id === updatedPermission.id;
        const isSameCustomerID = statePermission.customer_profile_id === updatedPermission.customer_profile_id
        return (isSameID || isSameCustomerID) ? updatedPermission : statePermission
      })
    })
  }

  const handleRemove = (removedPermission) => {
    if (removedPermission.id) {
      setAccessPermissions((state) => {
        return state.map((statePermission) => {
          const isSameID = removedPermission.id && statePermission.id === removedPermission.id;
          const isSameCustomerID = statePermission.customer_profile_id === removedPermission.customer_profile_id
          if (isSameID || isSameCustomerID) {
            return {...statePermission, _delete: true}
          } else {
            return statePermission
          }
        })
      });
    } else {
      setAccessPermissions((state) => {
        return state.filter((statePermission) => {
          const isSameID = removedPermission.id && statePermission.id === removedPermission.id;
          const isSameCustomerID = statePermission.customer_profile_id === removedPermission.customer_profile_id
          return !isSameID && !isSameCustomerID
        })
      });
    }
  };

  const availablePermissions = accessPermissions.filter((permission) => !permission._delete);

  return (
    <>
      <div className="overlay show" onClick={() => setEditPermissions(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list" style={{ marginBottom: 0 }}>
            <div>
              <h3>
                {customer.name} Permissions: ({availablePermissions?.length})
              </h3>

              <AsyncSelect
                className="form-input"
                cacheOptions
                defaultOptions
                placeholder="Search and add more customers"
                loadOptions={promiseOptions}
                onChange={handleSelect}
                value=""
                styles={{
                  control: (baseStyles, state) => ({
                    ...baseStyles,
                    zIndex: 5,
                  }),
                }}
              />
              
              <ul className="customer-permissions">
                {accessPermissions.map((permission) => (
                  <AccessPermission permission={permission} handleUpdate={handleUpdate} handleRemove={handleRemove} />
                ))}
              </ul>
            </div>
          </div>
          <div className="between-flex mt-1">
            <a className="button" onClick={handleSave}>
              Save with {availablePermissions.length} permissions
            </a>
            <a className="button gray-btn" onClick={() => setEditPermissions(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

AccessPermissions.propTypes = {
  customer: PropTypes.object.isRequired,
  setEditPermissions: PropTypes.func.isRequired,
};

export default AccessPermissions;
