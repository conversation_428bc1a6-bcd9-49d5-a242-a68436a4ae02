import { useState, useContext } from 'react';
import { usePlacesWidget } from 'react-google-autocomplete';

// actions
import appContext from 'contexts/appContext';
import { getYordarSuburb } from 'utilities/locationSelection';

const GOOGLE_AUTOCOMPLETE_OPTIONS = (countryCode) => ({
  types: ['geocode'],
  fields: ['place_id', 'types', 'formatted_address', 'address_components'],
  componentRestrictions: {
    country: countryCode || 'au',
  },
});

const GoogleLocationInput = ({ setCurrentLocation }) => {
  const [currentValue, setCurrentValue] = useState('');

  const { googleApiKey, countryCode } = useContext(appContext);

  const handlePlaceSelected = async (places) => {
    const selectedLocation = {
      postcode: null,
      locality: null,
      streetAddress: null,
    };

    const postcodeComponent = places.address_components.find(
      (component) => component.types.indexOf('postal_code') != -1
    );
    if (postcodeComponent) {
      selectedLocation.postcode = postcodeComponent.short_name;
    }

    const sublocalityComponent = places.address_components.find(
      (component) => component.types.indexOf('sublocality') != -1
    );
    if (sublocalityComponent) {
      selectedLocation.locality = sublocalityComponent.long_name;
    }

    const localityComponent = places.address_components.find((component) => component.types.indexOf('locality') != -1);
    if (localityComponent) {
      selectedLocation.locality ||= localityComponent.long_name;
    }

    if (places.types.find((type) => type === 'street_address' || type === 'premise')) {
      const streetAddressComponents = places.formatted_address
        .split(', ')
        .filter((address) => !address.startsWith('Level'));
      selectedLocation.streetAddress = streetAddressComponents[0];
    }

    // // get yordar suburb
    const yordarSuburb = await getYordarSuburb({
      selectedLocation,
      countryCode,
    });

    if (yordarSuburb) {
      setCurrentValue(places.formatted_address);
      setCurrentLocation((state) => ({ ...state, suburb: yordarSuburb, ...selectedLocation }));
    } else {
      // could not find suburb
      alert('could not find suburb');
      setCurrentValue('');
      setCurrentLocation((state) => ({ ...state, suburb: null }));
    }
  };

  const { ref: googleRef } = usePlacesWidget({
    apiKey: googleApiKey,
    onPlaceSelected: (place) => handlePlaceSelected(place),
    options: GOOGLE_AUTOCOMPLETE_OPTIONS(countryCode),
  });

  return (
    <input
      ref={googleRef}
      autoComplete="no"
      className="form-input"
      required
      type="text"
      onChange={(event) => setCurrentValue(event.target.value)}
      value={currentValue}
    />
  );
};

export default GoogleLocationInput;
