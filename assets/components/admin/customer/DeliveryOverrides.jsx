import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import { apiAdminSuppliersPath, apiAdminCustomerDeliveryOverridesPath } from 'routes';

// store
import shallow from 'zustand/shallow';
import customerAdminStore from 'store/admin/customerAdminStore';
import { deliveryOverrideKinds } from 'utilities/adminHelpers';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

// components
import DeliveryOverride from './DeliveryOverride';

const initialOverride = {
  supplier_kind: '',
  supplier_profile_id: null,
  supplier: {
    name: '',
  },
  customer_override: '',
  supplier_override: '',
  active: true,
}

const DeliveryOverrides = ({ customer, setEditOverrides }) => {
  const [localSupplierKind, setLocalSupplierKind] = useState('');
  const [deliveryOverrides, setDeliveryOverrides] = useState([]);

  useEffect(async () => {
    const { data: reponseOverrides } = await axios({
      method: 'GET',
      url: apiAdminCustomerDeliveryOverridesPath(customer),
    });

    setDeliveryOverrides(reponseOverrides)
  }, [])

  const { updateCustomerDeliveryOverrides } = customerAdminStore(
    (state) => ({
      updateCustomerDeliveryOverrides: state.updateCustomerDeliveryOverrides,
    }),
    shallow
  );

  const promiseOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseSuppliers } = await axios({
      method: 'GET',
      url: apiAdminSuppliersPath(),
      params: { query },
    });

    return responseSuppliers.map((supplier) => {
      return {
        value: supplier.id,
        label: supplier.name,
        id: supplier.id,
        name: supplier.name,
      };
    });
  }, 1000);

  const handleSave = async () => {
    try {
      await updateCustomerDeliveryOverrides({
        customer,
        deliveryOverrides
      });
      setEditOverrides(false);
    } catch (err) {
      // do nothing
    }
  };

  const handleKindSelect = async (event) => {
    const selectedKind = event.target.value;
    if (selectedKind == 'specific') {
      setLocalSupplierKind(selectedKind);
      return;
    }

    if (deliveryOverrides.find((override) => override.supplier_kind === selectedKind)) {
      toast.warning(`Override for all ${selectedKind} orders already exists.`, { ...defaultToastOptions, autoClose: 5000 });
      return;
    }
    
    const customerOverride = {
      ...initialOverride,
      supplier_kind: selectedKind,
    };

    await setDeliveryOverrides((state) => [customerOverride ,...state])
  }

  const handleSupplierSelect = (selectedSupplier) => {
    if (deliveryOverrides.find((override) => override.supplier_profile_id === selectedSupplier.id)) {
      toast.warning('Supplier already within list of overrides', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }

    const customerOverride = {
      ...initialOverride,
      supplier_kind: 'specific',
      supplier_profile_id: selectedSupplier.id,
      supplier: {
        name: selectedSupplier.name,
      }
    }
    setDeliveryOverrides((state) => [customerOverride ,...state])
    setLocalSupplierKind('');
  };

  const handleUpdate = (updatedOverride) => {
    setDeliveryOverrides((state) => {
      return state.map((stateOverride) => {
        const isSameID = updatedOverride.id && stateOverride.id === updatedOverride.id;
        const isSameKind = stateOverride.supplier_kind === updatedOverride.supplier_kind;
        const isSameSupplierID = stateOverride.customer_profile_id === updatedOverride.customer_profile_id
        return (isSameID || (isSameKind && isSameSupplierID)) ? updatedOverride : stateOverride
      })
    })
  }

  const handleRemove = (removedOverride) => {
    if (removedOverride.id) {
      setDeliveryOverrides((state) => {
        return state.map((stateOverride) => {
          const isSameID = removedOverride.id && stateOverride.id === removedOverride.id;
          const isSameKind = stateOverride.supplier_kind === removedOverride.supplier_kind;
          const isSameSupplierID = stateOverride.supplier_profile_id === removedOverride.customer_profile_id;
          if (isSameID || (isSameKind && isSameSupplierID)) {
            return {...stateOverride, _delete: true}
          } else {
            return stateOverride
          }
        })
      });
    } else {
      setDeliveryOverrides((state) => {
        return state.filter((stateOverride) => {
          const isSameID = removedOverride.id && stateOverride.id === removedOverride.id;
          const isSameKind = stateOverride.supplier_kind === removedOverride.supplier_kind;
          const isSameSupplierID = stateOverride.supplier_profile_id === removedOverride.supplier_profile_id;
          return !isSameID && !(isSameKind && isSameSupplierID)
        })
      });
    }
  };

  const availableOverrides = deliveryOverrides.filter((override) => !override._delete);
  // console.log({availableOverrides})

  return (
    <>
      <div className="overlay show" onClick={() => setEditOverrides(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list" style={{ marginBottom: 0 }}>
            <div>
              <h3>
                {customer.name} Delivery Overrides: ({availableOverrides?.length})
              </h3>


              <label>Kind of override:</label>
              <select
                className='form-input'
                name='scope'
                value={localSupplierKind}
                onChange={handleKindSelect}>
                <option value='' />
                {Object.keys(deliveryOverrideKinds).map((kind) => (
                  <option key={`delivery-override-kind-${kind}`} value={kind}>
                    {deliveryOverrideKinds[kind]}
                  </option>
                ))}
              </select>

              {localSupplierKind == 'specific' && (
                <AsyncSelect
                  className="form-input"
                  cacheOptions
                  defaultOptions
                  placeholder="Search supplier"
                  loadOptions={promiseOptions}
                  onChange={handleSupplierSelect}
                  value=""
                  styles={{
                    control: (baseStyles, state) => ({
                      ...baseStyles,
                      zIndex: 5,
                    }),
                  }}
                />
              )}              
              
              <ul className="customer-permissions">
                {deliveryOverrides.map((override) => (
                  <DeliveryOverride key={`override-${override.supplier_kind}${override.supplier_profile_id ? `-${override.supplier_profile_id}`  : ''}`} override={override} handleUpdate={handleUpdate} handleRemove={handleRemove} />
                ))}
              </ul>
            </div>
          </div>
          <div className="between-flex mt-1">
            <a className="button" onClick={handleSave}>
              Save with {availableOverrides.length} overrides
            </a>
            <a className="button gray-btn" onClick={() => setEditOverrides(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

DeliveryOverrides.propTypes = {
  customer: PropTypes.object.isRequired,
  setEditOverrides: PropTypes.func.isRequired,
};

export default DeliveryOverrides;
