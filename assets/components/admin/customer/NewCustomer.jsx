import { useState, useContext } from 'react';
import appContext from 'contexts/appContext';

// store
import shallow from 'zustand/shallow';
import customerAdminStore from 'store/admin/customerAdminStore';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const initialCustomer = {
  firstname: '',
  lastname: '',
};

const NewCustomer = () => {
  const { userName, customerUUID } = useContext(appContext);

  const [isNew, setIsNew] = useState(false);
  const [newCustomer, setNewCustomer] = useState(initialCustomer);

  const { createAdminableCustomer } = customerAdminStore(
    (state) => ({
      createAdminableCustomer: state.createAdminableCustomer,
    }),
    shallow
  );

  const handleSave = async () => {
    if (!newCustomer.firstname || !newCustomer.lastname) {
      toast.error('Need both the first name and last name', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }
    try {
      await createAdminableCustomer({
        newCustomer,
        customerUUID,
      });
      setIsNew(false);  
    } catch (err) {
      // do nothing - error message showed from Store
    }
  };

  const handleChange = (event) => {
    setNewCustomer((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  if (isNew) {
    return (
      <div className="list-item">
        <div className="list-flex-2 pr-1">
          <label>First Name</label>
          <input type="text" name="firstname" className='form-input' onChange={handleChange} />
        </div>
        <div className="list-flex-2 pr-1">
          <label>Last Name</label>
          <input type="text" name="lastname" className='form-input' onChange={handleChange} />
        </div>
        <div className="list-flex-3">
          <a className="button tiny" onClick={handleSave}>
            Save
          </a>
          <a className="button gray-btn ml-1 tiny" onClick={() => setIsNew(false)}>
            cancel
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="list-item">
      <a onClick={() => setIsNew(true)}>Add new Customer under {userName}</a>
    </div>
  );
};

export default NewCustomer;
