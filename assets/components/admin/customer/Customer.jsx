import { useState, useContext, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Menu, MenuItem, MenuButton } from '@szhsin/react-menu';
import "@szhsin/react-menu/dist/index.css";

// store
import shallow from 'zustand/shallow';
import customerAdminStore from 'store/admin/customerAdminStore';
import appContext from 'contexts/appContext';
import { companiesAdminPath } from 'routes';

// components
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import AccessPermissions from './AccessPermissions';
import DeliveryOverrides from './DeliveryOverrides';
import CustomerEditForm from './CustomerEditForm';
import NewCustomerOrder from './NewCustomerOrder';
import UserEditForm from 'components/admin/user/UserEditForm';
import InvitationForm from './invite/InvitationForm';

const Customer = ({ customer, index }) => {
  const circleColor = getCircleIconColor(index);
  const [isEdit, setIsEdit] = useState(false);
  const [editUser, setEditUser] = useState(false);
  const [editPermissions, setEditPermissions] = useState(false);
  const [editOverrides, setEditOverrides] = useState(false);
  const [inviteAdmins, setInviteAdmins] = useState(false);
  const [imageError, setImageError] = useState(false);

  const { canManageCustomers, adminInviteLink } = useContext(appContext);

  const domain = customer?.email?.split('@')[1];

  const EXCLUDED_DOMAINS = [
    'gmail.com',
    'hotmail.com',
    'yahoo.com.au',
    'news.com.au',
    'tpg.com.au',
    'auspost.com.au',
    'instagram.com',
    'icloud.com',
    'yordar.com.au',
    'optusnet.com.au',
  ];

  const { favouriteCustomer, editAll, updateCustomerUser, deprecateCustomerUser } = customerAdminStore(
    (state) => ({
      favouriteCustomer: state.favouriteCustomer,
      editAll: state.editAll,
      updateCustomerUser: state.updateCustomerUser,
      deprecateCustomerUser: state.deprecateCustomerUser,
    }),
    shallow
  );

  const handleFavourite = (event) => {
    favouriteCustomer({
      customer,
    });
  };

  const handleImageError = () => {
    setImageError(true);
  };

  useEffect(() => {
    setIsEdit(editAll);
  }, [editAll]);

  if (canManageCustomers && isEdit) {
    return <CustomerEditForm customer={customer} setIsEdit={setIsEdit} />;
  }

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
        {!EXCLUDED_DOMAINS.includes(domain) && !imageError && canManageCustomers ? (
          <div>
            <img
              alt="company-logo"
              src={`https://logo.clearbit.com/${domain}`}
              onError={handleImageError}
              style={{ width: '40px', height: '40px', borderRadius: '50%' }}
            />
          </div>
        ) : customer.is_own_account ? (
          <span className="circle-icon own-account" style={{ backgroundColor: 'black' }} />
        ) : (
          <span className="circle-icon" style={{ background: circleColor }}>
            {customer.name[0]}
          </span>
        )}
      </div>
      <div className="list-flex-2">
        <a href={customer.sign_in_path} style={{ fontWeight: 'bold' }}>
          {customer.is_own_account ? 'My Account' : customer.name}
        </a>
      </div>
      <div className="list-flex-2">
        {canManageCustomers && customer.has_attached_company && (
          <a href={companiesAdminPath({ company_name: customer.company_name })} target="_blank" rel="noreferrer">
            {customer.company_name}
          </a>
        )}
        {(!canManageCustomers || !customer.has_attached_company) && <span>{customer.company_name}</span>}
      </div>
      <div className="list-flex-3" style={{ color: 'grey' }}>
        <p style={{ marginBottom: 0 }}>{customer.email}</p>
        <p style={{ marginBottom: 0 }}>{customer.contact_numbers[0]}</p>
        {customer.admin_sign_in_path && <p style={{ marginBottom: 0 }}><a href={customer.admin_sign_in_path}>(sign in as admin)</a></p>}
      </div>
      <div className="list-flex-1 company-team-admin">
        {canManageCustomers && (
          <>
            {customer.is_new && <span className="admin-new-customer">NEW</span>}
            {customer.company_team_admin && (
              <span title="Company Admin" className="admin-flag company">
                CA
              </span>
            )}
            {customer.team_admin && (
              <span title="Team Admin" className="admin-flag team">
                TA
              </span>
            )}
          </>
        )}
        {!canManageCustomers && <span>{customer.role}</span>}
      </div>
      <div className="list-flex-2 text-center">
        <div className="between-flex" style={{ justifyContent: 'space-evenly' }}>
          <span
            onClick={handleFavourite}
            style={{ cursor: 'pointer', paddingRight: '8px', backgroundPosition: 'center' }}
            className={customer.is_favourite ? 'hr-icon' : 'he-icon'}
          />
          {canManageCustomers && <a className="icon-edit" onClick={() => setIsEdit(!isEdit)} />}
          <NewCustomerOrder customer={customer} />
        </div>
        {canManageCustomers && (
          <Menu menuButton={<MenuButton className='mt-1 button tiny hollow' >Manage Customer</MenuButton>}>
            <MenuItem onClick={() => setEditPermissions(true)}>Access Permissions</MenuItem>
            <MenuItem onClick={() => setEditOverrides(true)}>Delivery Overrides</MenuItem>
            <MenuItem onClick={() => setEditUser(true)}>User Settings</MenuItem>
            <MenuItem onClick={() => setInviteAdmins(true)}>Invite Admins</MenuItem>
          </Menu>
        )}
      </div>
      {canManageCustomers && editPermissions && (
        <AccessPermissions customer={customer} setEditPermissions={setEditPermissions} />
      )}
      {canManageCustomers && editOverrides && (
        <DeliveryOverrides customer={customer} setEditOverrides={setEditOverrides} />
      )}
      {canManageCustomers && editUser && (
        <UserEditForm
          profileable={customer}
          profileableType='CustomerProfile'
          setEditUser={setEditUser}
          updateUser={updateCustomerUser}
          deprecateUser={deprecateCustomerUser}
        />
      )}
      {canManageCustomers && inviteAdmins && (
        <InvitationForm
          customer={customer}
          sliderOpen={inviteAdmins}
          setSliderOpen={setInviteAdmins}
          inviteType='admin'
          inviteLink={adminInviteLink.replace('_customer_code', customer.uuid)}
        />
      )}
    </div>
  );
};

Customer.propTypes = {
  customer: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Customer;
