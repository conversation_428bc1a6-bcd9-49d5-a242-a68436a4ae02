import { useState, useEffect } from 'react';
import { Modal } from 'react-responsive-modal';

import GoogleLocationInput from './GoogleLocationInput';

// very high value cause react-responsibe-model sets a very high z-index
const customCss = `
    .pac-container {
      z-index: 2147483648 !important;
    }
`;

const initialLocationValue = {
  suburb: null,
  streetAddress: null,
};

const SuburbSelectionModal = ({ newSuburbLink, setNewSuburbLink }) => {
  const [currentLocation, setCurrentLocation] = useState(initialLocationValue);

  useEffect(() => {
    if (!currentLocation.suburb) return;

    searchSuppliers();
  }, [currentLocation.suburb]);

  const { customer, value: searchLink, label } = newSuburbLink;

  const searchSuppliers = () => {
    const { suburb, streetAddress } = currentLocation;
    let newOrderLink = searchLink.replace('_state_', suburb.state).replace('_suburb_', suburb.name);
    if (streetAddress) {
      newOrderLink += `?street_address=${streetAddress}`;
    }
    window.location = newOrderLink;
  };

  return (
    <Modal
      classNames={{ modal: 'reveal customer-form admin-suburb-selection' }}
      open
      onClose={() => setNewSuburbLink(null)}
      center
      showCloseIcon={false}
    >
      <div className="text-center">
        <style>{customCss}</style>
        <h3>
          Select suburb for a new <em>{label}</em> for <em>{customer?.name}</em>
        </h3>

        <div className="suppliers-cta-form">
          <GoogleLocationInput currentLocation={currentLocation} setCurrentLocation={setCurrentLocation} />
        </div>
      </div>
    </Modal>
  );
};

export default SuburbSelectionModal;
