import { useState } from 'react';
import DropDown from 'react-dropdown';

import SuburbSelectionModal from './SuburbSelectionModal';

const NewCustomerOrder = ({ customer }) => {
  const [newSuburbLink, setNewSuburbLink] = useState(null);

  const newOrderOptions = [
    {
      label: 'Catering Order',
      value: customer.new_catering_order,
    },
    {
      label: 'Pantry Order',
      value: customer.new_pantry_order,
    },
    {
      label: 'Team Order',
      value: customer.new_team_order,
    },
    {
      label: 'Custom Order',
      value: customer.new_custom_order,
    },
  ].filter((option) => !!option.value);

  const handleNewLink = (selectedOption) => {
    const { value: selectedLink } = selectedOption;
    if (selectedLink.includes('_suburb_')) {
      setNewSuburbLink({ ...selectedOption, customer });
    } else {
      window.location = selectedLink;
    }
  };

  return (
    <>
      <DropDown
        placeholder=""
        value=""
        options={newOrderOptions}
        onChange={handleNewLink}
        controlClassName="icon-plus relative"
        menuClassName="menu customer-header__dropdown no-reset admin-create-order is-dropdown-submenu first-sub vertical js-dropdown-active"
        className="new-order-dropdown"
      />
      {!!newSuburbLink && <SuburbSelectionModal newSuburbLink={newSuburbLink} setNewSuburbLink={setNewSuburbLink} />}
    </>
  );
};

export default NewCustomerOrder;
