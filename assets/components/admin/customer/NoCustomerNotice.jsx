import { useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import customerAdminStore from 'store/admin/customerAdminStore';
import appContext from 'contexts/appContext';

const NoCustomerNotice = ({ isLoading, customers }) => {
  const { hasFavourites } = useContext(appContext);

  if (isLoading || customers.length) return null;

  const { favouritesOnly, setFavourites } = customerAdminStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
    }),
    shallow
  );

  return (
    <div className="text-center mt-1">
      <h3>No Customers Found</h3>
      {hasFavourites && favouritesOnly && (
        <p>
          Try toggling to <a onClick={() => setFavourites(false)}>show all customers</a>.
        </p>
      )}
    </div>
  );
};

export default NoCustomerNotice;
