import { useRef, useState, useEffect } from 'react';
import { permissionScopes } from 'utilities/adminHelpers';

const AccessPermission = ({ permission, handleUpdate, handleRemove }) => {
  const isMounted = useRef(false);
  const [localPermission, setLocalPermission] = useState(permission);
    
  useEffect(() => {
    if (isMounted.current) {
      setLocalPermission(permission);
    }
    isMounted.current = true;
  }, [permission]);

  useEffect(() => {
    if (isMounted.current) {
      handleUpdate(localPermission)
    }
    isMounted.current = true;
  }, [localPermission.active, localPermission.scope]);

  const handleChange = (event) => {
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalPermission((state) => ({ ...state, [event.target.name]: value }));
  }

  if (!!permission._delete) {
    return null;
  }

  return (
    <li>
      <div className="between-flex">
        {
          `${localPermission.customer.name} [${localPermission.customer.email}]${localPermission.customer.company_team_admin ? ' [CA]' : ''}`
        }
        
        {!!localPermission.id && (
          <label className="drop-text admin-action" style={{ marginLeft: '12px' }}>
            <input
              type="checkbox"
              className="checkbox-content"
              name="active"
              checked={localPermission.active}
              onChange={handleChange}
            />
            <span
              className="checkbox-content-tick"
              style={{ minWidth: '20px', height: '20px' }}
            />
          </label>
        )}
        {(!localPermission.active || !localPermission.id) && <a className="icon-trash admin-action" onClick={() => handleRemove(permission)} />}
      </div>
      <div className="between-flex mt-1-4">
        <label>Scope:</label>
        <select className='form-input' name='scope' onChange={handleChange}>
          {Object.keys(permissionScopes).map((scope) => (
            <option key={`permission-${permission.customer_profile_id}-scope-${scope}`} value={scope} selected={scope === localPermission.scope}>
              {permissionScopes[scope]}
            </option>
          ))}
        </select>
      </div>
    </li>
  )
}

export default AccessPermission;