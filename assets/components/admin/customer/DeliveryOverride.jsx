import { useRef, useState, useEffect } from 'react';

const DeliveryOverride = ({ override, handleUpdate, handleRemove }) => {
  const isMounted = useRef(false);
  const [localOverride, setLocalOverride] = useState(override);
    
  // useEffect(() => {
  //   if (isMounted.current) {
  //     setLocalOverride(override);
  //   }
  //   isMounted.current = true;
  // }, [override]);

  useEffect(() => {
    if (isMounted.current) {
      handleUpdate(localOverride)
    }
    isMounted.current = true;
  }, [localOverride.active, localOverride.customer_override, localOverride.supplier_override]);

  const handleChange = (event) => {
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalOverride((state) => ({ ...state, [event.target.name]: value }));
  }

  if (!!override._delete) {
    return null;
  }

  return (
    <li>
      <div className="between-flex">
        {localOverride.supplier_kind !== 'specific' && <label>For all <strong>{localOverride.supplier_kind}</strong> orders.</label>}
        {localOverride.supplier_kind === 'specific' && <label>For <strong>{localOverride.supplier.name}</strong> orders.</label>}
        
        {!!localOverride.id && (
          <label className="drop-text admin-action" style={{ marginLeft: '12px' }}>
            <input
              type="checkbox"
              className="checkbox-content"
              name="active"
              checked={localOverride.active}
              onChange={handleChange}
            />
            <span
              className="checkbox-content-tick"
              style={{ minWidth: '20px', height: '20px' }}
            />
          </label>
        )}
        {(!localOverride.active || !localOverride.id) && <a className="icon-trash admin-action" onClick={() => handleRemove(override)} />}
      </div>
      <div className="between-flex mt-1-4">
        <div>
          <label>Customer Override</label>
          <input type='number' name='customer_override' className='form-input' value={localOverride.customer_override} onChange={handleChange} />
        </div>
        <div>
          <label>Supplier Override</label>
          <input type='number' name='supplier_override' className='form-input' value={localOverride.supplier_override} onChange={handleChange} />
        </div>
      </div>
    </li>
  )
}

export default DeliveryOverride;