import { useState, useContext } from 'react';

// action
import axios from 'axios';
import appContext from 'contexts/appContext';
import { apiAdminCustomerInviteCustomerPath } from 'routes';
import { csrfHeaders } from 'utilities/csrfHeaders';

// helpers
import { requiredNewUserFields } from 'utilities/adminHelpers';

const SendInvite = ({ customer, inviteType, setSliderOpen }) => {  
  const [localUser, setLocalUser] = useState({});
  const [erroredFields, setErroredFields] = useState([]);
  const [sendingInvite, setSendingInvite] = useState(false);
  const [inviteSent, setInviteSent] = useState(false);
  const { customerUUID } = useContext(appContext);

  const handleChange = (event) => {
    setErroredFields((state) => state.filter((field) => field !== event.target.name))
    setLocalUser((state) => ({...state, [event.target.name]: event.target.value }))
  }

  const isValidForm = () => {
    let errors = [];
    setErroredFields({});
    requiredNewUserFields.forEach((field) => {
      if (!localUser[field]) {
        setErroredFields((state) => ({...state, [field]: `Invitation requires ${field}`}));
        errors.push(field)
      }
    })
    return !errors.length
  }

  const handleSubmit = async (event) => {
    event.preventDefault();
    if (isValidForm()) {
      setSendingInvite(true);
      let currentCustomerUUID = customerUUID;
      if (inviteType === 'admin' && customer) {
        currentCustomerUUID = customer.uuid;
      }
      try {
        const { data } = await axios({
          method: 'POST',
          url: apiAdminCustomerInviteCustomerPath(currentCustomerUUID, { invite_type: inviteType }),
          data: {...localUser},
          headers: csrfHeaders(),
        });
        setLocalUser({})
        setInviteSent(true)
        setTimeout(() => {
          setInviteSent(false);
        }, 3000);
      } catch (error) {
        let error_message = 'Oops! Something went wrong!. Please try again';
        if (error.response.status === 422) {
          error_message = error.response.data.errors.join('. ');
        }
        setErroredFields((state) => ({...state, 'form': error_message}));
      }
      setSendingInvite(false);
    }
  }


  return (
    <form className='new-team-contact-form mt-2' onSubmit={handleSubmit}>
      <div className='between-flex'>
        <div className='list-flex-1 pr-1'>
          <label className='team-order-label'>First Name</label>
          <input                
            type='text'
            className={`form-input${Object.keys(erroredFields).includes('first_name') ? ' is-invalid-input' : ''}`}
            placeholder='Jane'
            name='first_name'
            value={localUser.first_name || ''}
            onChange={handleChange}
          />
        </div>

        <div className='list-flex-1'>
          <label className='team-order-label'>Last Name</label>
          <input                
            type='text'
            className={`form-input${Object.keys(erroredFields).includes('last_name') ? ' is-invalid-input' : ''}`}
            placeholder='Doe'
            name='last_name'
            value={localUser.last_name || ''}
            onChange={handleChange}
          />
        </div>
      </div>    
      <div>
        <label className='team-order-label'>Email address</label>
        <input                
          type='email'
          className={`form-input${Object.keys(erroredFields).includes('email') ? ' is-invalid-input' : ''}`}
          placeholder='<EMAIL>'
          name='email'
          value={localUser.email || ''}
          onChange={handleChange}
        />
      </div>
      {Object.keys(erroredFields).includes('form') && <span className='is-invalid-label'>{erroredFields['form']}</span>}
      <div className="between-flex mt-1">
        <button className={`button small${inviteSent || sendingInvite ? ' hollow' : ''}`} data-button_text="Add Contact">
          {inviteSent && 'Invitation Sent!'}
          {!inviteSent && (sendingInvite ? 'Sending Invite...' : 'Send Invite')}
        </button>
        <a className="button small gray-btn" onClick={() => !sendingInvite && setSliderOpen(false)}>
          Cancel
        </a>
      </div>
    </form>
  )
}

export default SendInvite;