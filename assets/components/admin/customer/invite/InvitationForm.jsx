import { useState } from 'react';
import { Modal } from 'react-responsive-modal';

// components
import InviteLink from './InviteLink';
import SendInvite from './SendInvite';

const InvitationForm = ({ customer, inviteType, inviteLink, sliderOpen, setSliderOpen }) => {
  const [activePanel, setActivePanel] = useState('invite-link');

  const handlePanelChange = (panel) => {
    setActivePanel(panel);
  };

  return (
    <Modal
      classNames={{ modal: 'reveal modal modal-drawer budget-modal-slider' }}
      open={sliderOpen}
      showCloseIcon={false}
      onClose={() => setSliderOpen(false)}
    >
      <div className='form-header'>
        <div className='pt-1 px-1 between-flex'>
          <h3 className='p-0'>
            Invite {inviteType == 'customer' ? 'Users' : 'Admins'}
            {!!customer && <span className='ml-1-4'>- for {customer.name}</span>}
          </h3>
          <span className='close-modal-drawer' onClick={() => setSliderOpen(false)}>
            X
          </span>
        </div>
        <div className='add-contact-tabs row'>
          <div
            className={`small-6 columns change-panel ${activePanel === 'invite-link' ? 'active' : ''}`}
            onClick={() => handlePanelChange('invite-link')}
          >
            <span>Invite Link</span>
          </div>
          <div
            className={`small-6 columns change-panel ${activePanel === 'send-invite' ? 'active' : ''}`}
            onClick={() => handlePanelChange('send-invite')}
          >
            <span>Send Invite</span>
          </div>
        </div>
      </div>

      {activePanel === 'invite-link' && <InviteLink inviteType={inviteType} inviteLink={inviteLink} />}
      {activePanel === 'send-invite' && <SendInvite customer={customer} inviteType={inviteType} setSliderOpen={setSliderOpen} />}
    </Modal>
  );
};

export default InvitationForm;
