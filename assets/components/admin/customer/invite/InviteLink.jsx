import { useState, useContext } from 'react';

// action
import axios from 'axios';
import appContext from 'contexts/appContext';
import { apiAdminCustomerSendCustomerInvitationLinkPath } from 'routes';

const InviteLink = ({ inviteType, inviteLink }) => {
  const [linkCopied, setLinkCopied] = useState(false);
  const { customerUUID } = useContext(appContext);

  const handleCopyLink = () => {
    if (linkCopied) return;

    const input = document.createElement('input');
    input.type = 'text';
    input.value = inviteLink;
    document.getElementsByClassName('invite-button')[0].after(input);
    input.focus();
    input.select();
    document.execCommand('copy');
    input.remove();
    setLinkCopied(true);
    if (inviteType === 'customer') {
      sendInvitationLinkEmail();  
    }
    setTimeout(() => {
      setLinkCopied(false);
    }, 5000);
  };

  const sendInvitationLinkEmail = async () => {
    try {
      const { data } = await axios({
        method: 'GET',
        url: apiAdminCustomerSendCustomerInvitationLinkPath(customerUUID, { invite_type: inviteType }),
      });
    } catch (error) {
      // do nothing
    }
  }

  return (
    <div className="mt-2">
      <div>
        <p>
          {inviteType == 'customer' && <strong>This is a magic link that you can send to your co-workers so they can join your team</strong>}
          {inviteType == 'admin' && <strong>This is a magic link that you can send to your co-workers so they can join Yordar to control this account</strong>}
        </p>
        <p>
          Drop it in a group email, a slack channel or wherever you'd like and let everyone enter their own details
          to save you the work!
        </p>

        <div className='between-flex'>
          <a className={`invite-button button small${linkCopied ? ' hollow' : ''}`} onClick={handleCopyLink}>
            {linkCopied ? 'Link Copied!' : 'Copy Invite Link'}
          </a>
        </div>
      </div>
    </div>
  )
}

export default InviteLink;