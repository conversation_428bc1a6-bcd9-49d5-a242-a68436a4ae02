import { useRef, useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import supplierAdminStore from 'store/admin/supplierAdminStore';
import appContext from 'contexts/appContext';

const SupplierSearch = () => {
  const isMounted = useRef(false);

  const { canManageSuppliers } = useContext(appContext);

  const { suppliers, fetchSuppliers, query, setQuery, editAll, setEditAll, setSaveAll, changedSupplierIDs } =
    supplierAdminStore(
      (state) => ({
        suppliers: state.suppliers,
        fetchSuppliers: state.fetchSuppliers,
        query: state.query,
        setQuery: state.setQuery,
        editAll: state.editAll,
        setEditAll: state.setEditAll,
        setSaveAll: state.setSaveAll,
        changedSupplierIDs: state.changedSupplierIDs,
      }),
      shallow
    );

  useEffect(() => {
    if (query && query.length < 3) return;
    if (isMounted.current) {
      const delayDebounceFn = setTimeout(() => {
        fetchSuppliers({ page: 1 });
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [query]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by name or email address"
        style={{ maxWidth: '500px' }}
        type="search"
        value={query}
        onChange={(event) => setQuery(event.target.value)}
      />
      {!!query && query.length >= 3 && <span># of suppliers: {suppliers.length}</span>}
      {canManageSuppliers && (
        <div>
          {editAll && !!changedSupplierIDs.length && (
            <a className="mr-1 button" onClick={() => setSaveAll()}>
              Save ({changedSupplierIDs.length}) Suppliers
            </a>
          )}
          <a className={`button${editAll ? ' gray-btn' : ''}`} onClick={() => setEditAll()}>
            {editAll ? 'Cancel Edit All' : 'Edit All'}
          </a>
        </div>
      )}
    </div>
  );
};

export default SupplierSearch;
