import { useState } from 'react';
import PropTypes from 'prop-types';

// actions
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import { apiAdminCustomersPath } from 'routes';

// store
import shallow from 'zustand/shallow';
import supplierAdminStore from 'store/admin/supplierAdminStore';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const RestrictionsEditForm = ({ supplier, setRestrictionsEdit }) => {
  const [localSupplier, setLocalSupplier] = useState(supplier);

  const { updateSupplier } = supplierAdminStore(
    (state) => ({
      updateSupplier: state.updateSupplier,
    }),
    shallow
  );

  const promiseOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: { query },
    });

    return responseCustomers.map((customer) => {
      let label = `${customer.name} [${customer.email}]`;
      return {
        value: customer.id,
        label,
        id: customer.id,
        name: customer.name,
        email: customer.email,
      };
    });
  }, 1000);

  const handleSave = async () => {
    try {
      await updateSupplier({
        supplier: localSupplier,
      });
      setRestrictionsEdit(false);
    } catch (err) {
      console.log(err)
      debugger;
    }
  };

  const handleSelect = (selectedCustomer) => {
    if (localSupplier.customer_profile_ids.includes(selectedCustomer.id)) {
      toast.warning('Customer already exists within list', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }
    
    setLocalSupplier((state) => ({
      ...state,
      customers: [selectedCustomer, ...state.customers],
      customer_profile_ids: [selectedCustomer.id, ...state.customer_profile_ids],
    }));
  };

  const handleRemove = (removedCustomer) => {
    setLocalSupplier((state) => ({
      ...state,
      customers: state.customers.filter((customer) => customer.id != removedCustomer.id),
      customer_profile_ids: state.customer_profile_ids.filter((id) => id != removedCustomer.id),
    }));
  };

  return (
    <>
      <div className="overlay show" onClick={() => setRestrictionsEdit(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list" style={{ marginBottom: 0 }}>
            <div>
              <h3>
                {supplier.name} customers: ({localSupplier.customers.length})
              </h3>
              <AsyncSelect
                className="form-input"
                cacheOptions
                defaultOptions
                placeholder="Search and add more customers"
                loadOptions={promiseOptions}
                onChange={handleSelect}
                value=""
                styles={{
                  control: (baseStyles, state) => ({
                    ...baseStyles,
                    zIndex: 5,
                  }),
                }}
              />
              <ul className="company-customers">
                {localSupplier.customers.map((customer) => (
                  <li className="between-flex">
                    {`${customer.name} [${customer.email}]`}
                    <a className="icon-trash remove" onClick={() => handleRemove(customer)} />
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="between-flex mt-1">
            <a className="button" onClick={handleSave}>
              Save with {localSupplier.customers.length} customers
            </a>
            <a className="button gray-btn" onClick={() => setRestrictionsEdit(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

RestrictionsEditForm.propTypes = {
  supplier: PropTypes.object.isRequired,
  setRestrictionsEdit: PropTypes.func.isRequired,
};

export default RestrictionsEditForm;
