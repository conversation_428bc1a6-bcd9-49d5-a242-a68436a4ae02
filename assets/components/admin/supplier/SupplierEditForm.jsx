import { useState, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import moment from 'moment';

// store
import shallow from 'zustand/shallow';
import supplierAdminStore from 'store/admin/supplierAdminStore';

const SupplierEditForm = ({ supplier, setIsEdit }) => {
  const [localSupplier, setLocalSupplier] = useState(supplier);

  const { updateSupplier } = supplierAdminStore(
    (state) => ({
      updateSupplier: state.updateSupplier,
    }),
    shallow
  );

  const handleSave = async () => {
    await updateSupplier({
      supplier: localSupplier,
    });
    setIsEdit(false);
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalSupplier((state) => ({ ...state, [field]: value }));
  };

  const handleDateChange = (name, datetime) => {
    const selectedDate = moment(datetime, 'dddd, MMM DD, YYYY')?.format('YYYY/MM/DD');
    setLocalSupplier((state) => ({ ...state, [name]: selectedDate }));
  };

  return (
    <>
      <div className="overlay show" onClick={() => setIsEdit(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div>
            <h3>Edit {supplier.name}</h3>
          </div>
          <div className='mt-1'> 
            <fieldset style={{ padding: '0.5rem' }}>
              <legend>Supplier Flags</legend>           
              <div>
                <label>
                  <input type="checkbox" name="is_searchable" checked={localSupplier.is_searchable} onChange={handleChange} />
                  Is Searchable
                </label>
              </div>
              
              <div>
                <label>
                  <input type="checkbox" name="team_supplier" checked={localSupplier.team_supplier} onChange={handleChange} />
                  Team Supplier
                </label>
              </div>
            </fieldset>
            <fieldset style={{ padding: '0.5rem' }}>
              <legend>Christmas Closure Periods</legend>
              <div>
                <label>Close From </label>
                <DatePicker
                  selected={localSupplier.close_from ? new Date(localSupplier.close_from) : ''}
                  onChange={(datetime) => handleDateChange('close_from', datetime)}
                  name="close_from"
                  dateFormat="dd-MM-yyyy"
                  className="form-input"
                  autoComplete="off"
                />
              </div>
              <div>
                <label>Close To</label>
                <DatePicker
                  selected={localSupplier.close_to ? new Date(localSupplier.close_to) : ''}
                  onChange={(datetime) => handleDateChange('close_to', datetime)}
                  name="close_to"
                  dateFormat="dd-MM-yyyy"
                  className="form-input"
                  autoComplete="off"
                />
              </div>
              <small>
                Non Chirstmas closures should be handled in 
                <a href={supplier.closures_path} className='ml-1-4' style={{ fontSize: '100%' }}>
                  Supplier Closure dates.
                </a>
              </small>
            </fieldset>
          </div>

          <div className="between-flex mt-1 admin-sidebar-buttons">
            <a className="button tinyx" onClick={handleSave}>Update</a>
            <a className="button tinyx gray-btn" onClick={() => setIsEdit(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupplierEditForm;
