import { useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import supplierAdminStore from 'store/admin/supplierAdminStore';
import appContext from 'contexts/appContext';

// components
import Supplier from './Supplier';
import SupplierActions from './SupplierActions';
import SupplierSkeleton from './SupplierSkeleton';
import { ToastContainer } from 'react-toastify';

const SupplierAdminApp = () => {
  const { companyName } = useContext(appContext);
  const { fetchSuppliers, suppliers, loadingList, loadingMore, page, hasMore } = supplierAdminStore(
    (state) => ({
      fetchSuppliers: state.fetchSuppliers,
      suppliers: state.suppliers,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchSuppliers({
          page,
          passedQuery: companyName || '',
        });
      }
    },
  });

  return (
    <>
      <SupplierActions />
      <div className="item-list__headings">
        <span className="list-flex-3">Name</span>
        <span className="list-flex-3">Login Email</span>
        <span className="list-flex-1 text-center">Searchable</span>
        <span className="list-flex-1 text-center">Team Supplier</span>
        <span className="list-flex-2" />
      </div>
      {suppliers.map((supplier, idx) => (
        <Supplier key={`supplier-${supplier.id}`} supplier={supplier} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <SupplierSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default SupplierAdminApp;
