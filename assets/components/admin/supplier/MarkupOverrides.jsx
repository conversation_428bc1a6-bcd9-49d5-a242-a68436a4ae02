import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import { apiAdminCustomersPath, apiAdminCompaniesPath, apiAdminSupplierSupplierMarkupOverridesPath } from 'routes';

// store
import shallow from 'zustand/shallow';
import supplierAdminStore from 'store/admin/supplierAdminStore';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

// components
import MarkupOverride from './MarkupOverride';

const initialOverride = {
  overridable_type: '',
  overridable_id: null,
  overridable_name: '',
  markup: null,
  commission_rate: null,
  active: true,
}

const MarkupOverrides = ({ supplier, setEditOverrides }) => {
  const [markupOverrides, setMarkupOverrides] = useState([]);

  useEffect(async () => {
    const { data: reponseOverrides } = await axios({
      method: 'GET',
      url: apiAdminSupplierSupplierMarkupOverridesPath(supplier),
    });

    setMarkupOverrides(reponseOverrides)
  }, [])

  const { updateSupplierMarkupOverrides } = supplierAdminStore(
    (state) => ({
      updateSupplierMarkupOverrides: state.updateSupplierMarkupOverrides,
    }),
    shallow
  );

  const customerOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: { query },
    });

    return responseCustomers.map((customer) => {
      return {
        value: customer.id,
        label: `${customer.name} [${customer.email}]`,
        id: customer.id,
        name: customer.name,
        email: customer.email,
      };
    });
  }, 1000);

  const companyOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCompanies } = await axios({
      method: 'GET',
      url: apiAdminCompaniesPath(),
      params: { query },
    });

    return responseCompanies.map((company) => {
      return {
        value: company.id,
        label: company.name,
        id: company.id,
        name: company.name,
      };
    });
  }, 1000);

  const handleSave = async () => {
    // try {
      await updateSupplierMarkupOverrides({
        supplier,
        markupOverrides
      });
      setEditOverrides(false);
    // } catch (err) {
    //   // do nothing
    // }
  };

  const handleCustomerSelect = (selectedCustomer) => {
    if (markupOverrides.find((override) => override.overridable_type == 'CustomerProfile' && override.overridable_id === selectedCustomer.id)) {
      toast.warning('Customer already within list of overrides', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }

    const markupOverride = {
      ...initialOverride,
      overridable_type: 'CustomerProfile',
      overridable_id: selectedCustomer.id,
      overridable_name: selectedCustomer.name,
    }
    setMarkupOverrides((state) => [markupOverride ,...state])
  };

  const handleCompanySelect = (selectedCompany) => {
    if (markupOverrides.find((override) => override.overridable_type == 'Company' && override.overridable_id === selectedCompany.id)) {
      toast.warning('Company already within list of overrides', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }

    const markupOverride = {
      ...initialOverride,
      overridable_type: 'Company',
      overridable_id: selectedCompany.id,
      overridable_name: selectedCompany.name,
    }
    setMarkupOverrides((state) => [markupOverride ,...state])
  };

  const handleUpdate = (updatedOverride) => {
    setMarkupOverrides((state) => {
      return state.map((stateOverride) => {
        const isSameID = updatedOverride.id && stateOverride.id === updatedOverride.id;
        const isSameOverridableType = stateOverride.overridable_type === updatedOverride.overridable_type;
        const isSameOverridableID = stateOverride.overridable_id === updatedOverride.overridable_id;
        return isSameID || (isSameOverridableType && isSameOverridableID) ? updatedOverride : stateOverride
      })
    })
  }

  const handleRemove = (removedOverride) => {
    if (removedOverride.id) {
      setMarkupOverrides((state) => {
        return state.map((stateOverride) => {
          const isSameID = removedOverride.id && stateOverride.id === removedOverride.id;
          if (isSameID) {
            return {...stateOverride, _delete: true}
          } else {
            return stateOverride
          }
        })
      });
    } else {
      setMarkupOverrides((state) => {
        return state.filter((stateOverride) => {
          const isSameOverridableType = stateOverride.overridable_type === removedOverride.overridable_type;
          const isSameOverridableID = stateOverride.overridable_id === removedOverride.overridable_id;
          return !(isSameOverridableType && isSameOverridableID)
        })
      });
    }
  };

  const selectionStyle = {
    menu: (baseStyles, state) => ({
      ...baseStyles,
      zIndex: 5,
    }),
  }

  const availableOverrides = markupOverrides.filter((override) => !override._delete);

  return (
    <>
      <div className="overlay show" onClick={() => setEditOverrides(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list" style={{ marginBottom: 0 }}>
            <div>
              <h3>
                {supplier.name} Markup Overrides: ({availableOverrides?.length})
              </h3>

              <div>
                <label>Customer Override</label>
                <AsyncSelect
                  className="form-input"
                  cacheOptions
                  defaultOptions
                  placeholder="Search and add customers"
                  loadOptions={customerOptions}
                  onChange={handleCustomerSelect}
                  value=""
                  styles={selectionStyle}
                />
              </div>
              <div className='my-2'>
                <label>Company Override</label>
                <AsyncSelect
                  className="form-input"
                  cacheOptions
                  defaultOptions
                  placeholder="Search and add company"
                  loadOptions={companyOptions}
                  onChange={handleCompanySelect}
                  value=""
                  styles={selectionStyle}
                />
              </div>
              
              <ul className="customer-permissions">
                {markupOverrides.map((override) => (
                  <MarkupOverride
                    key={`supplier-${supplier.id}-override-${override.id}-${override.overridable_type}-${override.overridable_id}`}
                    supplier={supplier}
                    override={override}
                    handleUpdate={handleUpdate}
                    handleRemove={handleRemove}
                  />
                ))}
              </ul>
            </div>
          </div>
          <div className="between-flex mt-1">
            <a className="button" onClick={handleSave}>
              Save with {availableOverrides.length} overrides
            </a>
            <a className="button gray-btn" onClick={() => setEditOverrides(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

MarkupOverrides.propTypes = {
  supplier: PropTypes.object.isRequired,
  setEditOverrides: PropTypes.func.isRequired,
};

export default MarkupOverrides;
