import { useState, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';
import { Menu, MenuItem, MenuButton } from '@szhsin/react-menu';
import "@szhsin/react-menu/dist/index.css";

// store
import shallow from 'zustand/shallow';
import supplierAdminStore from 'store/admin/supplierAdminStore';
import appContext from 'contexts/appContext';

// components
import SupplierMarginEditForm from './SupplierMarginEditForm';
import SupplierEditForm from './SupplierEditForm';
import RestrictionsEditForm from './RestrictionsEditForm';
import MarkupOverrides from './MarkupOverrides';
import UserEditForm from 'components/admin/user/UserEditForm';

const Supplier = ({ supplier }) => {
  const { canManageSuppliers } = useContext(appContext);

  const { editAll, updateSupplierUser, deprecateSupplierUser } = supplierAdminStore(
    (state) => ({
      editAll: state.editAll,
      updateSupplierUser: state.updateSupplierUser,
      deprecateSupplierUser: state.deprecateSupplierUser,
    }),
    shallow
  );

  const [editMargins, setEditMargins] = useState(!!editAll || false);
  const [isEdit, setIsEdit] = useState(false);
  const [restrictionsEdit, setRestrictionsEdit] = useState(false);
  const [editOverrides, setEditOverrides] = useState(false);
  const [editUser, setEditUser] = useState(false);

  useEffect(() => {
    setEditMargins(editAll);
  }, [editAll]);

  if (canManageSuppliers && editMargins) {
    return <SupplierMarginEditForm supplier={supplier} setEditMargins={setEditMargins} />;
  }

  return (
    <div className="list-item">
      <div className="list-flex-3" style={{ display: 'flex', alignItems: 'center' }}>
        <div className="circle-icon" style={{ background: 'black' }}>
          {supplier.img ? <img src={supplier.img} /> : <span>{supplier.name[0]}</span>}
        </div>
        <a href={supplier.sign_in_path} style={{ fontWeight: 'bold' }}>
          {supplier.name}
          {!!supplier.customer_profile_ids.length && <small className='is-invalid-label' style={{ display: 'block' }}>(restricted to {supplier.customer_profile_ids.length} {supplier.customer_profile_ids.length > 1 ? 'customers' : 'customer'})</small>}
        </a>
        {supplier.is_new && <span className="admin-new-customer small">NEW</span>}
      </div>

      <div className="list-flex-3">
        <ul className="ml-0" style={{ marginBottom: 0 }}>
          <li>
            <strong>{supplier.login_email}</strong>
          </li>
        </ul>
      </div>
      <div className="list-flex-1">
        <div className={`list-flex-1 text-center admin-flags ${supplier.is_searchable ? 'active' : 'inactive'}`} />
      </div>
      <div className="list-flex-1">
        <div className={`list-flex-1 text-center admin-flags ${supplier.team_supplier ? 'active' : 'inactive'}`} />
      </div>
      <div className="list-flex-2 text-center">
        {canManageSuppliers && <a className="icon-cost mr-1" onClick={() => setEditMargins(!editMargins)} />}
        {canManageSuppliers && <a className="icon-edit mr" onClick={() => setIsEdit(!isEdit)} />}
        {supplier.menu_path && <a style={{ display: 'block' }} href={supplier.menu_path}>Manage Menu</a>}
        {canManageSuppliers && (
          <Menu menuButton={<MenuButton className='mt-1-4 button tiny hollow' >Manage Supplier</MenuButton>}>
            <MenuItem onClick={() => setRestrictionsEdit(true)}>
              Customer Restrictions
              {!!supplier.restrictions_count && <small className='ml-1-4'>({supplier.restrictions_count})</small>}
            </MenuItem>
            <MenuItem onClick={() => setEditOverrides(true)}>
              Markup Overrides
              {!!supplier.overrides_count && <small className='ml-1-4'>({supplier.overrides_count})</small>}
            </MenuItem>
            <MenuItem onClick={() => setEditUser(true)}>User Settings</MenuItem>
          </Menu>
        )}
      </div>
      {isEdit && <SupplierEditForm supplier={supplier} setIsEdit={setIsEdit} />}
      {restrictionsEdit && <RestrictionsEditForm supplier={supplier} setRestrictionsEdit={setRestrictionsEdit} />}
      {editOverrides && <MarkupOverrides supplier={supplier} setEditOverrides={setEditOverrides} />}
      {editUser && (
        <UserEditForm
          profileable={supplier}
          profileableType='SupplierProfile'
          setEditUser={setEditUser}
          updateUser={updateSupplierUser}
          deprecateUser={deprecateSupplierUser}
        />
      )}
    </div>
  );
};

Supplier.propTypes = {
  supplier: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Supplier;
