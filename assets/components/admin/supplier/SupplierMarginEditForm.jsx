import { useState, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import supplierAdminStore from 'store/admin/supplierAdminStore';

const SupplierMarginEditForm = ({ supplier, setEditMargins }) => {
  const [localSupplier, setLocalSupplier] = useState(supplier);

  const { updateSupplier, markSupplierAsChanged, changedSupplierIDs } = supplierAdminStore(
    (state) => ({
      updateSupplier: state.updateSupplier,
      markSupplierAsChanged: state.markSupplierAsChanged,
      changedSupplierIDs: state.changedSupplierIDs,
    }),
    shallow
  );

  const isUpdated = changedSupplierIDs.includes(supplier.id);

  const handleSave = async () => {
    await updateSupplier({
      supplier: localSupplier,
    });
    setEditMargins(false);
    markSupplierAsChanged({
      supplier: localSupplier,
      remove: true,
    });
  };

  const handleCancel = () => {
    markSupplierAsChanged({
      supplier: localSupplier,
      remove: true,
    });
    setEditMargins(false);
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalSupplier((state) => ({ ...state, [field]: value }));
    markSupplierAsChanged({
      supplier: localSupplier,
    });
  };

  useEffect(() => {
    if (supplier.triggerSave) {
      handleSave();
    }
  }, [supplier.triggerSave]);

  const yordarCommission =
    (1 - (1 - parseFloat(localSupplier.commission_rate) / 100) / (1 + parseFloat(localSupplier.markup) / 100)) * 100;

  return (
    <div className="list-item">
      <div className="list-flex-3">
        {supplier.name}
        <br />({isUpdated ? 'Updated' : 'Editing'})
      </div>
      <div className="list-flex-2 px-1 text-center">
        <label>Markdown</label>
        <input type="text" name="commission_rate" className="form-input" value={localSupplier.commission_rate} onChange={handleChange} />
      </div>
      <div className="list-flex-2 px-1 text-center">
        <label>Markup</label>
        <input type="text" name="markup" className="form-input" value={localSupplier.markup} onChange={handleChange} />
      </div>
      <div className="list-flex-2">
        <label>Yordar Comission</label>
        {yordarCommission.toFixed(2)} %
      </div>
      <div className="list-flex-2">
        <a className="button tiny mr-1-2" onClick={handleSave}>
          Save
        </a>
        <a className="button tiny gray-btn" onClick={handleCancel}>
          Cancel
        </a>
      </div>
    </div>
  );
};

export default SupplierMarginEditForm;
