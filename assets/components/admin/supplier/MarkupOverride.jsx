import { useRef, useState, useEffect } from 'react';

const MarkupOverride = ({ supplier, override, handleUpdate, handleRemove }) => {
  const isMounted = useRef(false);
  const [localOverride, setLocalOverride] = useState(override);
    
  useEffect(() => {
    if (isMounted.current) {
      handleUpdate(localOverride)
    }
    isMounted.current = true;
  }, [localOverride.active, localOverride.markup, localOverride.commission_rate]);

  const handleChange = (event) => {
    const value = event.target.type == 'checkbox' ? event.target.checked : (event.target.value || null);
    setLocalOverride((state) => ({ ...state, [event.target.name]: value }));
  }

  if (!!override._delete) {
    return null;
  }

  return (
    <li>
      <div className="between-flex">
        <span>
          <strong>{localOverride.overridable_name}</strong> <small>({localOverride.overridable_type === 'CustomerProfile' ? 'CUSTOMER' : 'COMPANY' })</small>
        </span>
        {!!localOverride.id && (
          <label className="drop-text admin-action" style={{ marginLeft: '12px' }}>
            <input
              type="checkbox"
              className="checkbox-content"
              name="active"
              checked={localOverride.active}
              onChange={handleChange}
            />
            <span
              className="checkbox-content-tick"
              style={{ minWidth: '20px', height: '20px' }}
            />
          </label>
        )}
        {(!localOverride.active || !localOverride.id) && <a className="icon-trash admin-action" onClick={() => handleRemove(override)} />}
      </div>
      <div className="between-flex mt-1-4">
        <div>
          <label>Markup Override</label>
          <input type='number' name='markup' className='form-input' placeholder='Leave blank to use defaults' value={localOverride.markup || ''} onChange={handleChange} />
          <small>(defaults to {supplier.markup}%)</small>
        </div>
        <div>
          <label>Commission Override</label>
          <input type='number' name='commission_rate' className='form-input' placeholder='Leave blank to use defaults' value={localOverride.commission_rate || ''} onChange={handleChange} />
          <small>(defaults to {supplier.commission_rate}%)</small>
        </div>
      </div>
    </li>
  )
}

export default MarkupOverride;