import { useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import suburbAdminStore from 'store/admin/suburbAdminStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewSuburb from './NewSuburb';
import Suburb from './Suburb';
import SuburbSearch from './SuburbSearch';
import SuburbSkeleton from './SuburbSkeleton';
import NoSuburbNotice from './NoSuburbNotice';

const SuburbAdminApp = () => {
  const { hasFavourites } = useContext(appContext);

  const { fetchSuburbs, suburbs, loadingList, loadingMore, page, hasMore, query } = suburbAdminStore(
    (state) => ({
      fetchSuburbs: state.fetchSuburbs,
      suburbs: state.suburbs,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      query: state.query,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchSuburbs({
          page,
          hasFavourites,
        });
      }
    },
  });

  return (
    <>
      <SuburbSearch />
      <div style={{ position: 'sticky', top: 0, zIndex: 999 }}>
        <div className="item-list__headings sticky">
          <span className="list-flex-1">Deliverable Zones</span>
          <span className="list-flex-2">Name</span>
          <span className="list-flex-2">Lat / Long</span>
          <span className="list-flex-1" />
        </div>
      </div>
      {!loadingList && !loadingMore && !query && <NewSuburb />}
      <NoSuburbNotice isLoading={loadingList || loadingMore} suburbs={suburbs} />
      {suburbs.map((suburb, idx) => (
        <Suburb key={`suburb-${suburb.id}`} suburb={suburb} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <SuburbSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default SuburbAdminApp;
