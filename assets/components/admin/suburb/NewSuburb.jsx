import { useState } from 'react';

// components
import SuburbForm from './SuburbForm';

const initialSuburb = {
  name: '',
  state: 'NSW',
  postcode: '',
  latitude: '',
  longitude: '',
  country_code: 'AU',
};

const NewSuburb = () => {
  const [isNew, setIsNew] = useState(false);

  if (isNew) {
    return <SuburbForm suburb={initialSuburb} showForm={setIsNew} />;
  }

  return (
    <div className="list-item">
      <a onClick={() => setIsNew(true)}>Add new Suburb</a>
    </div>
  );
};

export default NewSuburb;
