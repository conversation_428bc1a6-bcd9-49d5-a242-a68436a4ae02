import { useState } from 'react';
import moment from 'moment';

// store
import shallow from 'zustand/shallow';
import suburbAdminStore from 'store/admin/suburbAdminStore';

import { statesOptions, contryCodeOptions } from 'utilities/adminHelpers';

const SuburbForm = ({ suburb, showForm }) => {
  const [localSuburb, setLocalSuburb] = useState(suburb);

  const { createSuburb, updateSuburb } = suburbAdminStore(
    (state) => ({
      createSuburb: state.createSuburb,
      updateSuburb: state.updateSuburb,
    }),
    shallow
  );

  const handleSave = async () => {
    try {
      if (localSuburb.id) {
        await updateSuburb({
          suburb: localSuburb,
        });
      } else {
        await createSuburb({
          suburb: localSuburb,
        });
      }
      showForm(false);
    } catch (error) {
      // do nothing
    }
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalSuburb((state) => ({ ...state, [field]: value }));
  };

  return (
    <div className="list-item">
      <div className="list-flex-3 pr-1">
        <label>Name</label>
        <input type="text" name="name" className="form-input" value={localSuburb.name || ''} onChange={handleChange} />
      </div>
      <div className="list-flex-3 pr-1">
        <label>State</label>
        <select name='state' onChange={handleChange} className='form-input'>
          {statesOptions.map((state) => (
            <option key={`suburb-${localSuburb.id}-state-${state}`} value={state} selected={state === localSuburb.state}>
              {state}
            </option>
          ))}
        </select>
      </div>
      <div className="list-flex-3 pr-1">
        <label>Postcode</label>
        <input type="text" name="postcode" className="form-input" value={localSuburb.postcode || ''} onChange={handleChange} />
      </div>
      <div className="list-flex-3 pr-1">
        <label>Latitude</label>
        <input type="text" name="latitude" className="form-input" value={localSuburb.latitude || ''} onChange={handleChange} />
      </div>
      <div className="list-flex-3 pr-1">
        <label>Longitude</label>
        <input type="text" name="longitude" className="form-input" value={localSuburb.longitude || ''} onChange={handleChange} />
      </div>
      <div className="list-flex-1 pr-1">
        <label>Country Code</label>
        <select name='country_code' onChange={handleChange} className='form-input'>
          {contryCodeOptions.map((country_code) => (
            <option key={`suburb-${localSuburb.id}-country-code-${country_code}`} value={country_code} selected={country_code === localSuburb.country_code}>
              {country_code}
            </option>
          ))}
        </select>
      </div>
      <div className="list-flex-2">
        <a className="button tiny mr-1-2" onClick={handleSave}>
          Save
        </a>
        <a className="button tiny gray-btn" onClick={() => showForm(false)}>
          Cancel
        </a>
      </div>
    </div>
  );
};

export default SuburbForm;
