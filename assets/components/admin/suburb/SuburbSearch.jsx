import { useRef, useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import suburbAdminStore from 'store/admin/suburbAdminStore';
import appContext from 'contexts/appContext';

const SuburbSearch = () => {
  const isMounted = useRef(false);

  const { hasFavourites } = useContext(appContext);

  const { fetchSuburbs, query, setQuery } = suburbAdminStore(
    (state) => ({
      fetchSuburbs: state.fetchSuburbs,
      query: state.query,
      setQuery: state.setQuery,
    }),
    shallow
  );

  useEffect(() => {
    if (query && query.length < 3) return;
    if (isMounted.current) {
      const delayDebounceFn = setTimeout(() => {
        fetchSuburbs({
          page: 1,
          hasFavourites,
        });
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [query]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by suburb name or postcode"
        style={{ maxWidth: '500px' }}
        type="search"
        value={query}
        onChange={(event) => setQuery(event.target.value)}
      />
    </div>
  );
};

export default SuburbSearch;
