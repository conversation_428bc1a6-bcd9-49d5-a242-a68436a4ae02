import { useState } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import SuburbForm from './SuburbForm';

const Suburb = ({ suburb, index }) => {
  const circleColor = getCircleIconColor(index);
  const [isEdit, setIsEdit] = useState(false);

  if (isEdit) {
    return <SuburbForm suburb={suburb} showForm={setIsEdit} />;
  }

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
        <span className="circle-icon" style={{ background: circleColor }}>
          {suburb.deliverable_suburb_count ? suburb.deliverable_suburb_count : suburb.name[0]}
        </span>
      </div>
      <div className="list-flex-2">{suburb.label} ({suburb.country_code})</div>
      <div className="list-flex-2">{suburb.latitude} / {suburb.longitude}</div>
      <div className="list-flex-1 text-right">
        <a className="icon-edit mr-1" onClick={() => setIsEdit(!isEdit)} />
      </div>
    </div>
  );
};

Suburb.propTypes = {
  suburb: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Suburb;
