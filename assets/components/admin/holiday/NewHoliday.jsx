import { useState } from 'react';

// components
import HolidayForm from './HolidayForm';

const initialHoliday = {
  name: '',
  state: '',
  on_date: '',
  push_to: '',
  effective_from: '',
  effective_to: '',
};

const NewHoliday = () => {
  const [isNew, setIsNew] = useState(false);

  if (isNew) {
    return <HolidayForm holiday={initialHoliday} showForm={setIsNew} />;
  }

  return (
    <div className="list-item">
      <a onClick={() => setIsNew(true)}>Add new Holiday</a>
    </div>
  );
};

export default NewHoliday;
