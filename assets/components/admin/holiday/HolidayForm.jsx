import { useState, useContext } from 'react';
import DatePicker from 'react-datepicker';
import moment from 'moment';

// store
import shallow from 'zustand/shallow';
import holidayAdminStore from 'store/admin/holidayAdminStore';
import appContext from 'contexts/appContext';

// helpers
import { statesOptions } from 'utilities/adminHelpers';

// component
import { Modal } from 'react-responsive-modal';

const HolidayForm = ({ holiday, showForm }) => {
  const { tinyAPI } = useContext(appContext);
  const [localHoliday, setLocalHoliday] = useState(holiday);

  const { createHoliday, updateHoliday } = holidayAdminStore(
    (state) => ({
      createHoliday: state.createHoliday,
      updateHoliday: state.updateHoliday,
    }),
    shallow
  );

  const handleSave = async () => {
    try {
      if (localHoliday.id) {
        await updateHoliday({
          holiday: localHoliday,
        });
      } else {
        await createHoliday({
          holiday: localHoliday,
        });
      }
      showForm(false);
    } catch (error) {
      // do nothing
    }
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalHoliday((state) => ({ ...state, [field]: value }));
  };

  const handleDateChange = (name, datetime) => {
    const selectedDate = moment(datetime, 'dddd, MMM DD, YYYY')?.format('YYYY/MM/DD');
    setLocalHoliday((state) => ({ ...state, [name]: selectedDate }));
  };

  const handleMCEChange = (name, text) => {
    setLocalHoliday((state) => ({ ...state, [name]: text }));
  }

  return (
    <Modal
        classNames={{ modal: 'reveal modal modal-drawer budget-modal-slider' }}
        open={true}
        showCloseIcon={false}
        onClose={() => showForm(false)}
      >
      <style dangerouslySetInnerHTML={{__html: `
        .react-datepicker-popper { z-index: 3 !important; }
        .tox-tinymce-aux{z-index:99999999999 !important;}
      `}} />
      <h3 className='mt-1'>{!!localHoliday.id ? `Edit ${holiday.name}` : 'Create new Holiday'}</h3>
      <div>
        <label>Name</label>
        <input type="text" name="name" className="form-input" value={localHoliday.name || ''} onChange={handleChange} />
      </div>
      <div>
        <label>Date</label>
        <DatePicker
          selected={localHoliday.on_date ? new Date(localHoliday.on_date) : ''}
          onChange={(datetime) => handleDateChange('on_date', datetime)}
          name="on_date"
          dateFormat="dd-MM-yyyy"
          className="form-input"
          autoComplete="off"
        />
      </div>
      <div className='mb-1-2'>
        <label>Description</label>
        <textarea className='form-input' name='description' value={localHoliday.description || ''} onChange={handleChange} rows='3' />
      </div>
      <div>
        <label>State</label>
        <select name='state' onChange={handleChange} className='form-input'>
          <option key={`holiday-${localHoliday.id}-state-blank`} value='' selected={!localHoliday.state} >All States</option>
          {statesOptions.map((state) => (
            <option key={`holiday-${localHoliday.id}-state-${state}`} value={state} selected={state === localHoliday.state}>
              {state}
            </option>
          ))}
        </select>
      </div>
      <div>
        <label>Color</label>
        <input
          type="color"
          name="color"
          className="form-input"
          value={localHoliday.color || ''}
          onChange={handleChange}
          style={{ height: '50px', width: '50px' }}
        />
      </div>

      <fieldset className='mt-1'>
        <legend>Info that affects orders</legend>
        <div>
          <label>Push To</label>
          <DatePicker
            selected={localHoliday.push_to ? new Date(localHoliday.push_to) : ''}
            onChange={(datetime) => handleDateChange('push_to', datetime)}
            name="push_to"
            dateFormat="dd-MM-yyyy"
            className="form-input"
            autoComplete="off"
          />
        </div>
        <div>
          <label>Effective From</label>
          <DatePicker
            selected={localHoliday.effective_from ? new Date(localHoliday.effective_from) : ''}
            onChange={(datetime) => handleDateChange('effective_from', datetime)}
            name="effective_from"
            dateFormat="dd-MM-yyyy"
            className="form-input"
            autoComplete="off"
          />
        </div>
        <div>
          <label>Effective To</label>
          <DatePicker
            selected={localHoliday.effective_to ? new Date(localHoliday.effective_to) : ''}
            onChange={(datetime) => handleDateChange('effective_to', datetime)}
            name="effective_to"
            dateFormat="dd-MM-yyyy"
            className="form-input"
            autoComplete="off"
          />
        </div>
      </fieldset>
      <div>
        <a className="button mr-1-2" onClick={handleSave}>
          Save
        </a>
        <a className="button gray-btn" onClick={() => showForm(false)}>
          Cancel
        </a>
      </div>
    </Modal>
  );
};

export default HolidayForm;
