import { useState } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

// components
import HolidayForm from './HolidayForm';
import HolidayRemoveModal from './HolidayRemoveModal';

const Holiday = ({ holiday, index }) => {
  const circleColor = holiday.color || '#eee8e8';
  const [isEdit, setIsEdit] = useState(false);
  const [isRemove, setIsRemove] = useState(false);
  const dateSplit = holiday.on_date.split(' ');

  return (
    <>
      <div className="list-item">
        <div className="list-flex-1 invoice-header">
          <span className="circle-icon" style={{ background: circleColor, textAlign: 'center', lineHeight: '16px' }}>
            {`${dateSplit[0]} ${dateSplit[1].substring(0,3)}`}
          </span>
        </div>
        <div className="list-flex-2">{holiday.name}</div>
        <div className="list-flex-2">{holiday.on_date}</div>
        <div className="list-flex-2">{holiday.push_to || '-'}</div>
        <div className="list-flex-2">{holiday.effective_from} - {holiday.effective_to}</div>
        <div className="list-flex-2 text-center">{holiday.state || '-'}</div>
        <div className="list-flex-1 text-right">
          <a className="icon-edit mr-1" onClick={() => setIsEdit(!isEdit)} />
          <a className="icon-trash" onClick={() => setIsRemove(!isRemove)} />
        </div>
      </div>
      {isEdit && <HolidayForm holiday={holiday} showForm={setIsEdit} />}
      {isRemove && <HolidayRemoveModal holiday={holiday} setShowModal={setIsRemove} />}
    </>
  );
};

Holiday.propTypes = {
  holiday: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Holiday;
