import { useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import holidayAdminStore from 'store/admin/holidayAdminStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewHoliday from './NewHoliday';
import Holiday from './Holiday';
import HolidaySearch from './HolidaySearch';
import HolidaySkeleton from './HolidaySkeleton';
import NoHolidayNotice from './NoHolidayNotice';

const HolidayAdminApp = () => {
  const { hasFavourites } = useContext(appContext);

  const { fetchHolidays, holidays, loadingList, loadingMore, page, hasMore, query } = holidayAdminStore(
    (state) => ({
      fetchHolidays: state.fetchHolidays,
      holidays: state.holidays,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      query: state.query,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchHolidays({
          page,
          hasFavourites,
        });
      }
    },
  });

  return (
    <>
      <HolidaySearch />
      <div style={{ position: 'sticky', top: 0, zIndex: 999 }}>
        <div className="item-list__headings sticky">
          <span className="list-flex-1" />
          <span className="list-flex-2">Name</span>
          <span className="list-flex-2">Date</span>
          <span className="list-flex-2">Push To</span>
          <span className="list-flex-2">Effective</span>
          <span className="list-flex-2 text-center">State</span>
          <span className="list-flex-1" />
        </div>
      </div>
      {!loadingList && !loadingMore && !query && <NewHoliday />}
      <NoHolidayNotice isLoading={loadingList || loadingMore} holidays={holidays} />
      {holidays.map((holiday, idx) => (
        <Holiday key={`holiday-${holiday.id}`} holiday={holiday} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <HolidaySkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default HolidayAdminApp;
