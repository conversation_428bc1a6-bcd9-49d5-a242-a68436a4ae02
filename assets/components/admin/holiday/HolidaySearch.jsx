import { useRef, useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import holidayAdminStore from 'store/admin/holidayAdminStore';
import appContext from 'contexts/appContext';

const HolidaySearch = () => {
  const isMounted = useRef(false);

  const { hasFavourites } = useContext(appContext);

  const { fetchHolidays, query, setQuery } = holidayAdminStore(
    (state) => ({
      fetchHolidays: state.fetchHolidays,
      query: state.query,
      setQuery: state.setQuery,
    }),
    shallow
  );

  useEffect(() => {
    if (query && query.length < 3) return;
    if (isMounted.current) {
      const delayDebounceFn = setTimeout(() => {
        fetchHolidays({
          page: 1,
          hasFavourites,
        });
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [query]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by holiday name or state"
        style={{ maxWidth: '500px' }}
        type="search"
        value={query}
        onChange={(event) => setQuery(event.target.value)}
      />
    </div>
  );
};

export default HolidaySearch;
