import PropTypes  from 'prop-types';
import { Modal } from 'react-responsive-modal';

// store
import shallow from 'zustand/shallow';
import holidayAdminStore from 'store/admin/holidayAdminStore';


const HolidayRemoveModal = ({ holiday, setShowModal }) => {
  const { removeHoliday } = holidayAdminStore(
    (state) => ({
      removeHoliday: state.removeHoliday,
    }),
    shallow
  );

  const handleClose = () => {
    setShowModal(false);
  };

  const handleRemove = () => {
    try {
      removeHoliday({
        holiday
      });
      setShowModal(false);
    } catch (err) {
      // do nothing
    }
  };

  return (
    <Modal
      classNames={{ modal: 'reveal' }}
      open
      onClose={handleClose}
      center
      showCloseIcon={false}
    >
      <div className="text-center">
        <h3>
          Are you sure you want to remove Holiday?
        </h3>

        <p>
          <strong>{holiday.name}</strong>  on {holiday.on_date}
          {holiday.state && (
            <>
              <br />
              For State: {holiday.state}
            </>
          )}
          {holiday.push_to && (
            <>
              <br />
              <span className='is-invalid-label'>This is a public Holiday!</span>
            </>
          )}
        </p>        

        <a className='button small mr-1' onClick={handleRemove}>Confirm</a>
        <a className='button gray-btn small' onClick={handleClose}>Cancel</a>
      </div>
    </Modal>
  );
};

HolidayRemoveModal.propTypes = {
  holiday: PropTypes.object.isRequired,
  setShowModal: PropTypes.func.isRequired,
}

export default HolidayRemoveModal;
