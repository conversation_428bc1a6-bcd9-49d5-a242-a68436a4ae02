import { useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import invoiceAdminStore from 'store/admin/invoiceAdminStore';
import appContext from 'contexts/appContext';

const NoInvoiceNotice = ({ isLoading, invoices }) => {
  const { hasFavourites } = useContext(appContext);

  if (isLoading || invoices.length) return null;

  const { favouritesOnly, setFavourites } = invoiceAdminStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
    }),
    shallow
  );

  return (
    <div className="text-center mt-1">
      <h3>No Invoices Found</h3>
      {hasFavourites && favouritesOnly && (
        <p>
          Try toggling to <a onClick={() => setFavourites(false)}>show invoices of all customers</a>.
        </p>
      )}
    </div>
  );
};

export default NoInvoiceNotice;
