import { useState, useEffect } from 'react';

// action
import axios from 'axios';

// components
import { apiAdminInvoicePath } from 'routes';

const InvoiceDetails = ({ invoice, setActiveInvoice }) => {
  const [invoiceInfo, setInvoiceInfo] = useState({});
  const [loadingInfo, setLoadingInfo] = useState(false);

  const fetchOrderInfo = async ({ fetchableInvoice }) => {
    setLoadingInfo(true);
    try {
      const { data: responseInvoice } = await axios({
        method: 'GET',
        url: apiAdminInvoicePath(fetchableInvoice),
      });
      setInvoiceInfo(responseInvoice);
    } catch (e) {
      // do nothing
    }
    setLoadingInfo(false);
  };

  useEffect(() => {
    setInvoiceInfo({});
    fetchOrderInfo({
      fetchableInvoice: invoice,
    });
  }, [invoice.id]);

  const { totals } = invoice;

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="order-show-details-section">
        <h6 className="order-show-detail-title order">
          Invoice #{invoice.number}
        </h6>
        {!!invoice.customer && (
          <p>
            <strong>Customer: </strong>
            <span>
              {invoice.customer.name}
              {!!invoice.customer.company_name && (
                <>
                  <br />({invoice.customer.company_name})
                </>
              )}
            </span>
          </p>
        )}        
        <p>
          <strong>Status: </strong>
          <span>
            {invoice.payment_status}
            {invoice.is_overdue && (
              <>
                <br />
                <span className="invoice-overdue">Overdue by: {invoice.due_distance}</span>
              </>
            )}
          </span>
        </p>
        <p>
          <strong>Invoice Dates: </strong>
          <span>{invoice.dates.join(' - ')}</span>
        </p>
        {!!invoice.due_date && (
          <p>
            <strong>Due Date: </strong>
            <span>{invoice.due_date}</span>
          </p>
        )}
      </div>

      {loadingInfo && <div className="order-map order-map--small loading" />}
      {!!invoiceInfo.orders?.length && (
        <div className="order-show-details-section">
          <p>
            <strong>Orders ({invoiceInfo.orders.length})</strong>
          </p>
          <ul className='m-0'>
            {invoiceInfo.orders.map((order) => (
              <li>
                <p>
                  <a href={order.show_path} target='blank'>
                    <strong>#{order.id}</strong> - {order.name}
                  </a>
                </p>
                <p>{order.delivery_at}</p>
                <p>
                  <strong>Status: </strong>
                  <span>{order.status.toUpperCase()}</span>
                </p>
                <p>
                  <strong>Total: </strong>
                  <span>{order.total}</span>
                </p>
                <hr />
              </li>
            ))}
          </ul>

          <p className="between-flex grey">
            Invoice Total
            <span>{invoice.total}</span>
          </p>
        </div>  
      )}

      <div className="order-show-details-section">
        {!!invoice.document && (
          <a className="button mb-1-2" style={{ width: '100%' }} href={invoice.document.url} target="blank">
            View PDF
          </a>
        )}
        {!!invoiceInfo.customer_invoices_path && (
          <a className="button mb-1-2 hollow" style={{ width: '100%' }} href={invoiceInfo.customer_invoices_path} target="blank">
            Access Profile
          </a>  
        )}
        <a className="button gray-btn" style={{ width: '100%' }} onClick={() => setActiveInvoice(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default InvoiceDetails;
