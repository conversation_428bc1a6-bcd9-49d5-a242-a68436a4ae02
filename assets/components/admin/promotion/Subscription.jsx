import { useRef, useState, useEffect } from 'react';
import { subscriptionScopes } from 'utilities/adminHelpers';

const Subscription = ({ subscription, handleUpdate, handleRemove }) => {
  const isMounted = useRef(false);
  const [localSubscription, setLocalSubscription] = useState(subscription);
    
  useEffect(() => {
    if (isMounted.current) {
      setLocalSubscription(subscription);
    }
    isMounted.current = true;
  }, [subscription]);

  useEffect(() => {
    if (isMounted.current) {
      handleUpdate(localSubscription)
    }
    isMounted.current = true;
  }, [localSubscription.active]);

  const handleChange = (event) => {
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalSubscription((state) => ({ ...state, [event.target.name]: value }));
  }

  if (!!subscription._delete) {
    return null;
  }

  return (
    <li>
      <div className="between-flex">
        {localSubscription.subscriber_type === 'Company' && <span>{localSubscription.subscriber.name} (company)</span>}
        {localSubscription.subscriber_type === 'CustomerProfile' && <span>{`${localSubscription.subscriber.name} [${localSubscription.subscriber.email}]`}</span>}
        {!!localSubscription.id && (
          <label className="drop-text admin-action" style={{ marginLeft: '12px' }}>
            <input
              type="checkbox"
              className="checkbox-content"
              name="active"
              checked={localSubscription.active}
              onChange={handleChange}
            />
            <span
              className="checkbox-content-tick"
              style={{ minWidth: '20px', height: '20px' }}
            />
          </label>
        )}
        {(!localSubscription.active || !localSubscription.id) && <a className="icon-trash admin-action" onClick={() => handleRemove(subscription)} />}
      </div>
    </li>
  )
}

export default Subscription;