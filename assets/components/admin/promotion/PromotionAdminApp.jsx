import { useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import promotionAdminStore from 'store/admin/promotionAdminStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewPromotion from './NewPromotion';
import Promotion from './Promotion';
import PromotionSkeleton from './PromotionSkeleton';
import NoPromotionNotice from './NoPromotionNotice';

const PromotionAdminApp = () => {
  const { hasFavourites } = useContext(appContext);

  const { fetchPromotions, promotions, loadingList, loadingMore, page, hasMore, query } = promotionAdminStore(
    (state) => ({
      fetchPromotions: state.fetchPromotions,
      promotions: state.promotions,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      query: state.query,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchPromotions({
          page,
          hasFavourites,
        });
      }
    },
  });

  return (
    <>
      <div style={{ position: 'sticky', top: 0, zIndex: 999 }}>
        <div className="item-list__headings sticky">
          <span className="list-flex-1">Subscriptions</span>
          <span className="list-flex-2">Name</span>
          <span className="list-flex-2">Description</span>
          <span className="list-flex-2 text-center">Discount</span>
          <span className="list-flex-2 text-center">Valid From</span>
          <span className="list-flex-2 text-center">Valid Until</span>
          <span className="list-flex-1 text-center">Restrictions</span>
          <span className="list-flex-1 text-center">Active</span>
          <span className="list-flex-1" />
        </div>
      </div>
      {!loadingList && !loadingMore && !query && <NewPromotion />}
      <NoPromotionNotice isLoading={loadingList || loadingMore} promotions={promotions} />
      {promotions.map((promotion, idx) => (
        <Promotion key={`promotion-${promotion.id}`} promotion={promotion} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <PromotionSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default PromotionAdminApp;
