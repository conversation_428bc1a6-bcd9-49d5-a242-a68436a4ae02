import { useState } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import PromotionForm from './PromotionForm';
import Subscriptions from './Subscriptions';
import { promotionCategoryOptions } from 'utilities/adminHelpers';

const Promotion = ({ promotion, index }) => {
  const circleColor = getCircleIconColor(index);
  const [isEdit, setIsEdit] = useState(false);
  const [editSubscriptions, setEditSubscriptions] = useState(false);

  if (isEdit) {
    return <PromotionForm promotion={promotion} showForm={setIsEdit} />;
  }

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
        <span className="circle-icon" style={{ background: circleColor }}>
          {promotion.subscription_count}
        </span>
      </div>
      <div className="list-flex-2">{promotion.name}</div>
      <div className="list-flex-2">{promotion.description || '-'}</div>
      <div className="list-flex-2 text-center">
        {promotion.kind === 'amount' ? `\$${promotion.amount} OFF` : `${promotion.amount}% OFF`}
      </div>
      <div className="list-flex-2 text-center">{promotion.formatted_valid_from}</div>
      <div className="list-flex-2 text-center">{promotion.formatted_valid_until || 'Forever'}</div>
      <div className="list-flex-1 text-center">
        {promotion.category_restriction ? promotionCategoryOptions[promotion.category_restriction] : '-'}
      </div>
      <div className="list-flex-1 text-center">
        <div className={`text-center admin-flags ${promotion.active ? 'active' : 'inactive'}`} />
      </div>
      <div className="list-flex-1 text-center">
        <div className="between-flex" style={{ justifyContent: 'space-evenly' }}>
          <a className="icon-edit mr-1" onClick={() => setIsEdit(!isEdit)} />
          {promotion.active && (
            <div className="mt-1-2">
              <a onClick={() => setEditSubscriptions(true)}>Manage Subscriptions</a>
            </div>
          )}
        </div>
      </div>
      {promotion.active && editSubscriptions && (
        <Subscriptions promotion={promotion} setEditSubscriptions={setEditSubscriptions} />
      )}
    </div>
  );
};

Promotion.propTypes = {
  promotion: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Promotion;
