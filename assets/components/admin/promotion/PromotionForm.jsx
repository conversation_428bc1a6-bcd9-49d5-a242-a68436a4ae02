import { useState } from 'react';
import DatePicker from 'react-datepicker';
import ReactTooltip from 'react-tooltip';
import moment from 'moment';

// store
import shallow from 'zustand/shallow';
import promotionAdminStore from 'store/admin/promotionAdminStore';

import { promotionKinds, promotionCategoryOptions } from 'utilities/adminHelpers';

const PromotionForm = ({ promotion, showForm }) => {
  const [localPromotion, setLocalPromotion] = useState(promotion);

  const { createPromotion, updatePromotion } = promotionAdminStore(
    (state) => ({
      createPromotion: state.createPromotion,
      updatePromotion: state.updatePromotion,
    }),
    shallow
  );

  const handleSave = async () => {
    try {
      if (localPromotion.id) {
        await updatePromotion({
          promotion: localPromotion,
        });
      } else {
        await createPromotion({
          promotion: localPromotion,
        });
      }
      showForm(false);
    } catch (error) {
      // do nothing
    }
  };

  const handleDateChange = (name, datetime) => {
    const selectedDate = moment(datetime, 'dddd, MMM DD, YYYY')?.format('YYYY/MM/DD');
    setLocalPromotion((state) => ({ ...state, [name]: selectedDate }));
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalPromotion((state) => ({ ...state, [field]: value }));
  };

  return (
    <div className="list-item">
      <div className="list-flex-3 pr-1">
        <label>Name</label>
        <input type="text" name="name" className="form-input" value={localPromotion.name || ''} onChange={handleChange} />
      </div>
      <div className="list-flex-3 pr-1">
        <label>Description</label>
        <input
          type="text"
          name="description"
          className="form-input"
          value={localPromotion.description || ''}
          onChange={handleChange}
        />
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Type
          <span className="icon-info-circle ml-1-4" data-tip data-for={`promotion-${localPromotion.id}-type-info`} />
          <ReactTooltip id={`promotion-${localPromotion.id}-type-info`} place="right" effect="solid">
            <small>Select how this promotion will apply the discount.</small>
          </ReactTooltip>
        </label>
        <select name='kind' onChange={handleChange} className='form-input'>
          {Object.keys(promotionKinds).map((kind) => (
            <option key={`promotion-${localPromotion.id}-kind-${kind}`} value={kind} selected={kind === localPromotion.type}>
              {promotionKinds[kind]}
            </option>
          ))}
        </select>
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Amount
          <span className="icon-info-circle ml-1-4" data-tip data-for={`promotion-${localPromotion.id}-amount-info`} />
          <ReactTooltip id={`promotion-${localPromotion.id}-amount-info`} place="right" effect="solid">
            <small>This is the actual discount amount. 0-100 for percentage, zero or positive number for amount.</small>
          </ReactTooltip>
        </label>
        <input
          type="text"
          name="amount"
          className="form-input"
          value={localPromotion.amount || ''}
          onChange={handleChange}
        />
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Valid From
          <span className="icon-info-circle ml-1-4" data-tip data-for={`promotion-${localPromotion.id}-valid-from-info`} />
          <ReactTooltip id={`promotion-${localPromotion.id}-valid-from-info`} place="right" effect="solid">
            <small>When this promotion will be available.</small>
          </ReactTooltip>
        </label>
        <DatePicker
          selected={localPromotion.valid_from ? new Date(localPromotion.valid_from) : ''}
          onChange={(datetime) => handleDateChange('valid_from', datetime)}
          name="valid_from"
          dateFormat="dd-MM-yyyy"
          className="form-input"
          autoComplete="off"
        />
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Valid Until
          <span className="icon-info-circle ml-1-4" data-tip data-for={`promotion-${localPromotion.id}-valid-until-info`} />
          <ReactTooltip id={`promotion-${localPromotion.id}-valid-until-info`} place="right" effect="solid">
            <small>When will this promotion expire. By default, it does not expire.</small>
          </ReactTooltip>
        </label>
        <DatePicker
          selected={localPromotion.valid_until ? new Date(localPromotion.valid_until) : ''}
          onChange={(datetime) => handleDateChange('valid_until', datetime)}
          name="valid_until"
          dateFormat="dd-MM-yyyy"
          className="form-input"
          autoComplete="off"
        />
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Category Restrictions
          <span className="icon-info-circle ml-1-4" data-tip data-for={`promotion-${localPromotion.id}-category-info`} />
          <ReactTooltip id={`promotion-${localPromotion.id}-category-info`} place="right" effect="solid">
            <small>Select if promotion needs to be restricted to a particular category.</small>
          </ReactTooltip>
        </label>
        <select name='category_restriction' onChange={handleChange} className='form-input'>
          <option key={`promotion-${localPromotion.id}-category-blank`} value='' selected={!localPromotion.category_restriction} />
          {Object.keys(promotionCategoryOptions).map((category) => (
            <option key={`promotion-${localPromotion.id}-category-${category}`} value={category} selected={category === localPromotion.category_restriction}>
              {promotionCategoryOptions[category]}
            </option>
          ))}
        </select>
      </div>
      <div className="list-flex-2">
        <a className="button tiny mr-1-2" onClick={handleSave}>
          Save
        </a>
        <a className="button tiny gray-btn" onClick={() => showForm(false)}>
          Cancel
        </a>
      </div>
    </div>
  );
};

export default PromotionForm;
