import { useState } from 'react';

// components
import PromotionForm from './PromotionForm';

const initialPromotion = {
  name: '',
  kind: 'amount',
  category_restriction: '',
};

const NewPromotion = () => {
  const [isNew, setIsNew] = useState(false);

  if (isNew) {
    return <PromotionForm promotion={initialPromotion} showForm={setIsNew} />;
  }

  return (
    <div className="list-item">
      <a onClick={() => setIsNew(true)}>Add new Promotion</a>
    </div>
  );
};

export default NewPromotion;
