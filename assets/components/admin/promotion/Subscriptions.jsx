import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import { apiAdminCustomersPath, apiAdminCompaniesPath, apiAdminPromotionSubscriptionsPath } from 'routes';

// store
import shallow from 'zustand/shallow';
import promotionAdminStore from 'store/admin/promotionAdminStore';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

// components
import Subscription from './Subscription';

const initialSubscription = {
  subscriber_type: '',
  subscriber_id: null,
  active: true,
  subscriber: {
    name: '',
    email: '',
  },
}

const Subscriptions = ({ promotion, setEditSubscriptions }) => {
  const [subscriptions, setSubscriptions] = useState([]);

  useEffect(async () => {
    const { data: responseSubscriptions } = await axios({
      method: 'GET',
      url: apiAdminPromotionSubscriptionsPath(promotion),
    });

    setSubscriptions(responseSubscriptions)
  }, [])

  const { updateSubscriptions } = promotionAdminStore(
    (state) => ({
      updateSubscriptions: state.updateSubscriptions,
    }),
    shallow
  );

  const customerOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: { query },
    });

    return responseCustomers.map((customer) => {
      let label = `${customer.name} [${customer.email}]`;
      return {
        value: customer.id,
        label,
        id: customer.id,
        name: customer.name,
        email: customer.email,
      };
    });
  }, 1000);

  const handleCustomerSelect = (selectedCustomer) => {
    if (subscriptions.find((subscription) => subscription.subscriber_type === 'CustomerProfile' && subscription.subscriber_id === selectedCustomer.id)) {
      toast.warning('Customer already exists within list', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }

    const newSubscription = {
      ...initialSubscription,
      subscriber_type: 'CustomerProfile',
      subscriber_id: selectedCustomer.id,
      subscriber: {
        name: selectedCustomer.name,
        email: selectedCustomer.email
      }
    }
    setSubscriptions((state) => [newSubscription ,...state])
  };

  const companyOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCompanies } = await axios({
      method: 'GET',
      url: apiAdminCompaniesPath(),
      params: { query },
    });

    return responseCompanies.map((company) => {
      return {
        value: company.id,
        label: company.name,
        id: company.id,
        name: company.name,
      };
    });
  }, 1000);

  const handleCompanySelect = (selectedCompany) => {
    if (subscriptions.find((subscription) => subscription.subscriber_type === 'Company' && subscription.subscriber_id === selectedCompany.id)) {
      toast.warning('Company already exists within list', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }

    const newSubscription = {
      ...initialSubscription,
      subscriber_type: 'Company',
      subscriber_id: selectedCompany.id,
      subscriber: {
        name: selectedCompany.name,
        email: null
      }
    }
    setSubscriptions((state) => [newSubscription ,...state])
  };

  const handleSave = async () => {
    try {
      await updateSubscriptions({
        promotion,
        subscriptions
      });
      setEditSubscriptions(false);
    } catch (err) {
      // do nothing
    }
  };

  const handleUpdate = (updatedSubscription) => {
    setSubscriptions((state) => {
      return state.map((statePermission) => {
        const isSameID = updatedSubscription.id && statePermission.id === updatedSubscription.id;
        const isSameSubscriber = statePermission.subscriber_type === updatedSubscription.subscriber_type && statePermission.subscriber_id === updatedSubscription.subscriber_id
        return (isSameID || isSameSubscriber) ? updatedSubscription : statePermission
      })
    })
  }

  const handleRemove = (removedPermission) => {
    if (removedPermission.id) {
      setSubscriptions((state) => {
        return state.map((statePermission) => {
          const isSameID = removedPermission.id && statePermission.id === removedPermission.id;
          const isSameSubscriber = statePermission.subscriber_type === removedPermission.subscriber_type && statePermission.subscriber_id === removedPermission.subscriber_id
          if (isSameID || isSameSubscriber) {
            return {...statePermission, _delete: true}
          } else {
            return statePermission
          }
        })
      });
    } else {
      setSubscriptions((state) => {
        return state.filter((statePermission) => {
          const isSameID = removedPermission.id && statePermission.id === removedPermission.id;
          const isSameCustomerID = statePermission.customer_profile_id === removedPermission.customer_profile_id
          return !isSameID && !isSameCustomerID
        })
      });
    }
  };

  const availableSubscriptions = subscriptions.filter((permission) => !permission._delete);

  const selectionStyle = {
    menu: (baseStyles, state) => ({
      ...baseStyles,
      zIndex: 5,
    }),
  }

  return (
    <>
      <div className="overlay show" onClick={() => setEditSubscriptions(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list">
            <div>
              <h3>
                {promotion.name} Subscriptions: ({availableSubscriptions?.length})
              </h3>

              <div>
                <label>Subscribe Customer</label>
                <AsyncSelect
                  className="form-input"
                  cacheOptions
                  defaultOptions
                  placeholder="Search and add customers"
                  loadOptions={customerOptions}
                  onChange={handleCustomerSelect}
                  value=""
                  styles={selectionStyle}
                />
              </div>

              <div className='my-2'>
                <label>Subscribe Company</label>
                <AsyncSelect
                  className="form-input"
                  cacheOptions
                  defaultOptions
                  placeholder="Search and add company"
                  loadOptions={companyOptions}
                  onChange={handleCompanySelect}
                  value=""
                  styles={selectionStyle}
                />
              </div>
              <ul className="customer-permissions">
                {subscriptions.map((subscription, idx) => (
                  <Subscription key={`promotion-${promotion.id}-subscription-${subscription.id || subscription.subscriber_id}`}  subscription={subscription} handleUpdate={handleUpdate} handleRemove={handleRemove} />
                ))}
              </ul>
            </div>
          </div>
          <div className="between-flex mt-1">
            <a className="button" onClick={handleSave}>
              Save with {availableSubscriptions.length} subscriptions
            </a>
            <a className="button gray-btn" onClick={() => setEditSubscriptions(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

Subscriptions.propTypes = {
  promotion: PropTypes.object.isRequired,
  setEditSubscriptions: PropTypes.func.isRequired,
};

export default Subscriptions;
