import { useRef, useState, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import yordarAdminStore from 'store/admin/yordarAdminStore';

const AdminActions = () => {
  const isMounted = useRef(false);

  const adminKinds = {
    yordar_admin: 'Yordar Admins',
    account_manager: 'Account Managers',
    pantry_manager: 'Pantry Managers',
    company_team_admin: 'Company Team Admins',
    team_admin: 'Team Admins',
  };

  const { kind, setKind, query, setQuery } = yordarAdminStore(
    (state) => ({
      kind: state.kind,
      setKind: state.setKind,
      query: state.query,
      setQuery: state.setQuery,
    }),
    shallow
  );

  const [localQuery, setLocalQuery] = useState(query);

  useEffect(() => {
    if (localQuery && localQuery.length < 3) return;
    if (isMounted.current && query !== localQuery) {
      const delayDebounceFn = setTimeout(() => {
        setQuery(localQuery);
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [localQuery]);

  useEffect(() => setLocalQuery(query), [query]);

  function copyStaffLink(e) {
    const copyText = e.currentTarget.dataset.staffRegisterLink;
    navigator.clipboard.writeText(copyText);

    const span = e.currentTarget.querySelector('span');
    if (span) {
      span.textContent = 'Link Copied!';
      setTimeout(() => {
        span.textContent = 'Copy Staff Invite Link';
      }, 4000);
    }
  }

  return (
    <>
      <div
        className="copy-staff-register-link"
        data-staff-register-link="https://yordar.com/staff-register"
        onClick={copyStaffLink}
      >
        <span>Copy Staff Invite Link</span>
      </div>
      <div className="between-flex">
        <input
          className="search-input form-input"
          placeholder="Search by name or email address"
          style={{ maxWidth: '500px' }}
          type="search"
          value={localQuery}
          onChange={(event) => setLocalQuery(event.target.value)}
        />
        <div className="float-right between-flex">
          {Object.keys(adminKinds).map((adminKind, idx) => {
            let linkClass = 'button';
            if (idx > 0) linkClass += ' ml-1-4';
            if (adminKind != kind) linkClass += ' hollow';
            return (
              <a className={linkClass} onClick={() => setKind(adminKind)}>
                {adminKinds[adminKind]}
              </a>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default AdminActions;
