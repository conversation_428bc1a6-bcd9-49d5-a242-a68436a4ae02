import { useState } from 'react';

import ManageAccessPermissions from 'components/admin/customer/AccessPermissions';

const AccessPermissions = ({admin, kind}) => {
  const [editPermissions, setEditPermissions] = useState(false);

  return (
    <>
      <span className="list-flex-2">{admin.company_name}</span>
      <div className="list-flex-4">
        <ul>
          {admin.customers.map((customer) => <li className='bullet-list'>{customer.name} [{customer.email}]{customer.scope && kind != customer.scope ? ` (scope: ${customer.scope})` : ''}</li>)}
        </ul>
      </div>
      <div className="list-flex-2">
        <a onClick={() => setEditPermissions(true)}>Manage Access Permissions</a>
      </div>
      {editPermissions && (
        <ManageAccessPermissions customer={admin.customer} setEditPermissions={setEditPermissions} />
      )}
    </>
  );
}

export default AccessPermissions