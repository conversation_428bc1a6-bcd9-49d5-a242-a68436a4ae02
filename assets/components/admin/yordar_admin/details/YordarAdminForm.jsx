import { useState, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import yordarAdminStore from 'store/admin/yordarAdminStore';
import { adminFields } from 'utilities/adminHelpers';

const YordarAdminForm = ({ admin, circleColor, setIsEdit }) => {
  const [localAdmin, setLocalAdmin] = useState(admin);

  const { updateAdmin } = yordarAdminStore(
    (state) => ({
      updateAdmin: state.updateAdmin,
    }),
    shallow
  );

  const handleSave = async () => {
    await updateAdmin({
      admin: localAdmin,
    });
    setIsEdit(false);
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setLocalAdmin((state) => ({ ...state, [field]: value }));
  };

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
         <span className="circle-icon" style={{ background: circleColor }}>
          {localAdmin.name[0]}
        </span>
      </div>
      <div className="list-flex-2"><strong>{localAdmin.name}</strong></div>
      <div className="list-flex-2">{localAdmin.email}</div>
      {Object.keys(adminFields).map((field) => (
        <div className="list-flex-2 text-center">
          <label>
            {adminFields[field]}
            <br />
            <input type="checkbox" name={field} checked={!!localAdmin[field]} onChange={handleChange} />
          </label>
        </div>
      ))}
      <div className="list-flex-2 text-center">
        <a className="button tiny mr-1-2" onClick={handleSave}>
          Save
        </a>
        <a className="button tiny gray-btn" onClick={() => setIsEdit(false)}>
          Cancel
        </a>
      </div>
    </div>
  );
};

export default YordarAdminForm;
