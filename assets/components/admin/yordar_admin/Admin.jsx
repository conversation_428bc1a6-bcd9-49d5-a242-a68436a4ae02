import { useState, useContext } from 'react';
import PropTypes from 'prop-types';

// components
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import YordarAdminForm from './details/YordarAdminForm';
import YordarAdmin from './details/YordarAdmin';
import TeamAdmin from './details/TeamAdmin';
import AccessPermissions from './details/AccessPermissions';
import appContext from 'contexts/appContext';

const Admin = ({ admin, index, kind }) => {
  const circleColor = getCircleIconColor(index);
  const { canManageAdmins } = useContext(appContext);

  const [isEdit, setIsEdit] = useState(false);

  const renderAdminDetails = () => {
    switch (kind) {
      case 'yordar_admin':
        return <YordarAdmin admin={admin} />;
      case 'team_admin':
        return <TeamAdmin admin={admin} />;
      case 'account_manager':
      case 'pantry_manager':
      case 'company_team_admin':
        return <AccessPermissions admin={admin} kind={kind} />
      default:
        return null;
    }
  };

  if (canManageAdmins && kind === 'yordar_admin' && isEdit) {
    return <YordarAdminForm admin={admin} setIsEdit={setIsEdit} circleColor={circleColor}/>
  }

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
         <span className="circle-icon" style={{ background: circleColor }}>
          {admin.name[0]}
        </span>
      </div>
      <div className="list-flex-2">
        <a href={admin.sign_in_path} style={{ fontWeight: 'bold' }}>
          {admin.name}
        </a>
      </div>
      <div className="list-flex-2">{admin.email}</div>
      {renderAdminDetails()}
      {canManageAdmins && kind === 'yordar_admin' && (
        <div className="list-flex-2 text-center">
          <a onClick={() => setIsEdit(true)}>Manage</a>
        </div>
      )}
    </div>
  );
};

Admin.propTypes = {
  admin: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  kind: PropTypes.string.isRequired,
};

export default Admin;
