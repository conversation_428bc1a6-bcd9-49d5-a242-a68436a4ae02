import { useContext, useEffect } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import yordarAdminStore from 'store/admin/yordarAdminStore';
import appContext from 'contexts/appContext';

// components
import Admin from './Admin';
import AdminActions from './AdminActions';
import AdminSkeleton from './AdminSkeleton';
import YordarAdmin from './headers/YordarAdmin';
import TeamAdmin from './headers/TeamAdmin';
import AccessPermissions from './headers/AccessPermissions';
import { ToastContainer } from 'react-toastify';

const YordarAdminApp = () => {
  const { canManageAdmins } = useContext(appContext);

  const { setFilters, fetchAdmins, admins, loadingList, loadingMore, page, hasMore, kind } = yordarAdminStore(
    (state) => ({
      setFilters: state.setFilters,
      fetchAdmins: state.fetchAdmins,
      admins: state.admins,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      kind: state.kind,
    }),
    shallow
  );

  useEffect(() => {
    const existingFilters = JSON.parse(localStorage.getItem('YordarAdminFilters') || JSON.stringify({}));
    delete (existingFilters.query); // only required for a while. TO-DO - remove after a few weeks
    
    setFilters({
      query: '',
      ...existingFilters,
    });
  }, []);

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchAdmins({
          page,
        });
      }
    },
  });

  const renderAdminHeader = () => {
    switch (kind) {
      case 'yordar_admin':
        return <YordarAdmin />;
      case 'team_admin':
        return <TeamAdmin />;
      case 'account_manager':
      case 'pantry_manager':
      case 'company_team_admin':
        return <AccessPermissions />
      default:
        return null;
    }
  };

  return (
    <>
      <AdminActions />
      <div className="sticky-container">
        <div className="item-list__headings sticky">
          <span className="list-flex-1" />
          <span className="list-flex-2">Name</span>
          <span className="list-flex-2">Email</span>
          {renderAdminHeader()}
          {canManageAdmins && kind === 'yordar_admin' && <span className="list-flex-2" />}
        </div>
      </div>

      {admins.map((admin, idx) => (
        <Admin key={`admin-${admin.id}`} admin={admin} index={idx} kind={kind} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <AdminSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default YordarAdminApp;
