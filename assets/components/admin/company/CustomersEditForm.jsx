import { useState } from 'react';
import PropTypes from 'prop-types';

// actions
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import { apiAdminCustomersPath } from 'routes';

// store
import shallow from 'zustand/shallow';
import companyAdminStore from 'store/admin/companyAdminStore';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const CustomersEditForm = ({ company, setCustomerEdit }) => {
  const [localCompany, setLocalCompany] = useState(company);

  const { updateCompany } = companyAdminStore(
    (state) => ({
      updateCompany: state.updateCompany,
    }),
    shallow
  );

  const promiseOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: { query },
    });

    return responseCustomers.map((customer) => {
      let label = `${customer.name} [${customer.email}]`;
      if (customer.has_attached_company) {
        label += ` [attached to '${customer.company_name}']`;
      }
      return {
        value: customer.id,
        label,
        id: customer.id,
        name: customer.name,
        email: customer.email,
      };
    });
  }, 1000);

  const handleSave = async () => {
    try {
      await updateCompany({
        company: localCompany,
      });
      setCustomerEdit(false);
    } catch (err) {
      debugger;
    }
  };

  const handleSelect = (selectedCustomer) => {
    if (localCompany.customer_profile_ids.includes(selectedCustomer.id)) {
      toast.warning('Customer already exists within list', { ...defaultToastOptions, autoClose: 5000 });
      return;
    }

    setLocalCompany((state) => ({
      ...state,
      customers: [selectedCustomer, ...state.customers],
      customer_profile_ids: [selectedCustomer.id, ...state.customer_profile_ids],
    }));
  };

  const handleRemove = (removedCustomer) => {
    setLocalCompany((state) => ({
      ...state,
      customers: state.customers.filter((customer) => customer.id != removedCustomer.id),
      customer_profile_ids: state.customer_profile_ids.filter((id) => id != removedCustomer.id),
    }));
  };

  return (
    <>
      <div className="overlay show" onClick={() => setCustomerEdit(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list">
            <div>
              <h3>
                {company.name} customers: ({localCompany.customers.length})
              </h3>
              <AsyncSelect
                className="form-input"
                cacheOptions
                defaultOptions
                placeholder="Search and add more customers"
                loadOptions={promiseOptions}
                onChange={handleSelect}
                value=""
                styles={{
                  control: (baseStyles, state) => ({
                    ...baseStyles,
                    zIndex: 5,
                  }),
                }}
              />
              <ul className="company-customers">
                {localCompany.customers.map((customer) => (
                  <li className="between-flex">
                    {`${customer.name} [${customer.email}]`}
                    <a className="icon-trash remove" onClick={() => handleRemove(customer)} />
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="between-flex mt-1">
            <a className="button" onClick={handleSave}>
              Save with {localCompany.customers.length} customers
            </a>
            <a className="button gray-btn" onClick={() => setCustomerEdit(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

CustomersEditForm.propTypes = {
  company: PropTypes.object.isRequired,
  setCustomerEdit: PropTypes.func.isRequired,
};

export default CustomersEditForm;
