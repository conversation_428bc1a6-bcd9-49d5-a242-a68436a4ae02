import { useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import companyAdminStore from 'store/admin/companyAdminStore';
import appContext from 'contexts/appContext';

const NoCompanyNotice = ({ isLoading, companies }) => {
  const { hasFavourites } = useContext(appContext);

  if (isLoading || companies.length) return null;

  const { favouritesOnly, setFavourites } = companyAdminStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
    }),
    shallow
  );

  return (
    <div className="text-center mt-1">
      <h3>No Companies Found</h3>
      {hasFavourites && favouritesOnly && (
        <p>
          Try toggling to <a onClick={() => setFavourites(false)}>show companies of all customers</a>.
        </p>
      )}
    </div>
  );
};

export default NoCompanyNotice;
