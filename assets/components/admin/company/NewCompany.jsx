import { useState } from 'react';

// store
import shallow from 'zustand/shallow';
import companyAdminStore from 'store/admin/companyAdminStore';

const initialCompany = {
  name: '',
};

const NewCompany = () => {
  const [isNew, setIsNew] = useState(false);
  const [newCompany, setNewCompany] = useState(initialCompany);

  const { createCompany } = companyAdminStore(
    (state) => ({
      createCompany: state.createCompany,
    }),
    shallow
  );

  const handleSave = async () => {
    if (newCompany.name) {
      await createCompany({
        company: newCompany,
      });
      setIsNew(false);
    }
  };

  const handleChange = (event) => {
    setNewCompany((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  if (isNew) {
    return (
      <div className="list-item">
        <div className="list-flex-3 pr-1">
          <label>New Company Name</label>
          <input type="text" name="name" onChange={handleChange} />
        </div>
        <div className="list-flex-3">
          <a className="button tiny" onClick={handleSave}>
            Save
          </a>
          <a className="button gray-btn tiny" onClick={() => setIsNew(false)}>
            cancel
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="list-item">
      <a onClick={() => setIsNew(true)}>Add new Company</a>
    </div>
  );
};

export default NewCompany;
