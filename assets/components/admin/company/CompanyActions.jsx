import { useRef, useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import companyAdminStore from 'store/admin/companyAdminStore';
import appContext from 'contexts/appContext';

const CompanyActions = () => {
  const isMounted = useRef(false);

  const { hasFavourites, canManageCompanies, companyName } = useContext(appContext);

  const { companies, fetchCompanies, query, setQuery, editAll, setEditAll, setSaveAll, changedCompanyIDs } =
    companyAdminStore(
      (state) => ({
        companies: state.companies,
        fetchCompanies: state.fetchCompanies,
        query: state.query,
        setQuery: state.setQuery,
        editAll: state.editAll,
        setEditAll: state.setEditAll,
        setSaveAll: state.setSaveAll,
        changedCompanyIDs: state.changedCompanyIDs,
      }),
      shallow
    );

  useEffect(() => {
    if ((query && query.length < 3) || query === companyName) return;
    if (isMounted.current) {
      const delayDebounceFn = setTimeout(() => {
        fetchCompanies({
          page: 1,
          hasFavourites,
        });
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [query]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by company name or customer name"
        style={{ maxWidth: '500px' }}
        type="search"
        value={query}
        onChange={(event) => setQuery(event.target.value)}
      />
      {canManageCompanies && (
        <div>
          {editAll && !!changedCompanyIDs.length && (
            <a className="mr-1 button" onClick={() => setSaveAll()}>
              Save ({changedCompanyIDs.length}) Companies
            </a>
          )}
          <a className={`button${editAll || !companies.length ? ' gray-btn' : ''}`} onClick={() => setEditAll()}>
            {editAll ? 'Cancel Edit All' : 'Edit All'}
          </a>
        </div>
      )}
    </div>
  );
};

export default CompanyActions;
