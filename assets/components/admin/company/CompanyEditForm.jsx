import { useState, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import companyAdminStore from 'store/admin/companyAdminStore';
import { companyFlags, paymentTermOptions } from 'utilities/adminHelpers';

const CompanyEditForm = ({ company, setIsEdit }) => {
  const [localCompany, setLocalCompany] = useState(company);

  const { updateCompany, markCompanyAsChanged, changedCompanyIDs } = companyAdminStore(
    (state) => ({
      updateCompany: state.updateCompany,
      markCompanyAsChanged: state.markCompanyAsChanged,
      changedCompanyIDs: state.changedCompanyIDs,
    }),
    shallow
  );

  const isUpdated = changedCompanyIDs.includes(company.id);

  const handleSave = async () => {
    try {
      await updateCompany({
        company: localCompany,
      });
      setIsEdit(false);
      markCompanyAsChanged({
        company: localCompany,
        remove: true,
      });  
    } catch (err) {
      // do nothing - error message showed from Store
    }
  };

  const handleCancel = () => {
    markCompanyAsChanged({
      company: localCompany,
      remove: true,
    });
    setIsEdit(false);
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setLocalCompany((state) => ({ ...state, [field]: value }));
    markCompanyAsChanged({
      company: localCompany,
    });
  };

  useEffect(() => {
    if (company.triggerSave) {
      handleSave();
    }
  }, [company.triggerSave]);

  return (
    <div className="list-item">
      <div className="list-flex-4">
        <label>Name</label>
        <input type="text" name="name" value={localCompany.name || ''} onChange={handleChange} />
      </div>
      {Object.keys(companyFlags).map((flag) => (
        <div className="list-flex-2 text-center">
          <label>
            {companyFlags[flag]}
            <br />
            <input type="checkbox" name={flag} checked={!!localCompany[flag]} onChange={handleChange} />
          </label>
        </div>
      ))}
      <div className="list-flex-2 px-1">
        <label>Payment Term Days</label>
        <select
          name="payment_term_days"
          className="form-input"
          value={localCompany.payment_term_days}
          onChange={handleChange}
        >
          {paymentTermOptions.map((option) => (
            <option key={`payment-term-${option}`} value={option}>
              {option} days
            </option>
          ))}
        </select>
      </div>
      <div className="list-flex-3">
        <a className="button tiny mr-1-2" onClick={handleSave}>
          Save
        </a>
        <a className="button tiny gray-btn" onClick={handleCancel}>
          Cancel
        </a>
        <br />
        {!!isUpdated && '(Updated)'}
      </div>
    </div>
  );
};

export default CompanyEditForm;
