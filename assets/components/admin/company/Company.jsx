import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { companyFlags } from 'utilities/adminHelpers';

// store
import shallow from 'zustand/shallow';
import companyAdminStore from 'store/admin/companyAdminStore';

// components
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import CompanyEditForm from './CompanyEditForm';
import CompanyModalForm from './CompanyModalForm';
import CustomersEditForm from './CustomersEditForm';

const Company = ({ company, index }) => {
  const noCustomers = !company.customers.length;
  const circleColor = noCustomers ? '#cccccc' : getCircleIconColor(index);

  const { editAll } = companyAdminStore(
    (state) => ({
      editAll: state.editAll,
    }),
    shallow
  );

  const [isEdit, setIsEdit] = useState(!!editAll || false);
  const [customerEdit, setCustomerEdit] = useState(false);

  useEffect(() => {
    setIsEdit(editAll);
  }, [editAll]);

  if (isEdit && editAll) {
    return <CompanyEditForm company={company} setIsEdit={setIsEdit} />;
  }

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
        <span className="circle-icon" style={{ background: circleColor }}>
          {company.customer_profile_ids.length}
        </span>
      </div>
      <div className="list-flex-3">
        {company.name}
        {noCustomers && <small className="ml-1-4">(no customers)</small>}
      </div>
      {Object.keys(companyFlags).map((flag) => (
        <div className="list-flex-2 text-center">
          <div className={`admin-flags ${company[flag] ? 'active' : 'inactive'}`} />
        </div>
      ))}
      <div className="list-flex-2 text-center">
        <span>{company.payment_term_days} days</span>
      </div>
      <div className="list-flex-3 text-center">
        <a className="icon-edit mr-1" onClick={() => setIsEdit(!isEdit)} />
        <a onClick={() => setCustomerEdit(!customerEdit)}>Manage Customers</a>
      </div>

      {customerEdit && <CustomersEditForm company={company} setCustomerEdit={setCustomerEdit} />}
      {isEdit && !editAll && <CompanyModalForm company={company} setIsEdit={setIsEdit} />}
    </div>
  );
};

Company.propTypes = {
  company: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Company;
