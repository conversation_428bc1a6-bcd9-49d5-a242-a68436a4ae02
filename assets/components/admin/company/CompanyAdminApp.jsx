import { useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import companyAdminStore from 'store/admin/companyAdminStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewCompany from './NewCompany';
import Company from './Company';
import CompanyActions from './CompanyActions';
import CompanySkeleton from './CompanySkeleton';
import NoCompanyNotice from './NoCompanyNotice';
import CompanyFilters from './CompanyFilters';

const CompanyAdminApp = () => {
  const { hasFavourites, companyName } = useContext(appContext);

  const { fetchCompanies, companies, loadingList, loadingMore, page, hasMore, editAll, favouritesOnly, query } =
    companyAdminStore(
      (state) => ({
        fetchCompanies: state.fetchCompanies,
        companies: state.companies,
        loadingList: state.loadingList,
        loadingMore: state.loadingMore,
        page: state.page,
        hasMore: state.hasMore,
        editAll: state.editAll,
        favouritesOnly: state.favouritesOnly,
        query: state.query,
      }),
      shallow
    );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchCompanies({
          page,
          hasFavourites,
          passedQuery: companyName || '',
        });
      }
    },
  });

  return (
    <>
      <CompanyActions />
      <div className="sticky-container">
        <CompanyFilters />
        {!editAll && (
          <div className="item-list__headings sticky">
            <span className="list-flex-1" />
            <span className="list-flex-3">Name</span>
            <span className="list-flex-2 text-center">Pay On Account</span>
            <span className="list-flex-2 text-center">Pay By Card</span>
            <span className="list-flex-2 text-center">Requires PO Number</span>
            <span className="list-flex-2 text-center">Invoice By PO</span>
            <span className="list-flex-2 text-center">Payment Terms</span>
            <span className="list-flex-3" />
          </div>
        )}
      </div>
      {(!hasFavourites || !favouritesOnly) && !query && <NewCompany />}
      <NoCompanyNotice isLoading={loadingList || loadingMore} companies={companies} />
      {companies.map((company, idx) => (
        <Company key={`company-${company.id}`} company={company} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <CompanySkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default CompanyAdminApp;
