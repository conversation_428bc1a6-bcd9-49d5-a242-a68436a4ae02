import { useState, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import companyAdminStore from 'store/admin/companyAdminStore';
import { companyFlags, paymentTermOptions, accountingSoftwareOptions } from 'utilities/adminHelpers';

const CompanyModalForm = ({ company, setIsEdit }) => {
  const [localCompany, setLocalCompany] = useState(company);

  const { updateCompany } = companyAdminStore(
    (state) => ({
      updateCompany: state.updateCompany,
    }),
    shallow
  );

  const handleSave = async () => {
    try {
      await updateCompany({
        company: localCompany,
      });  
      setIsEdit(false);
    } catch (err) {
      // do nothing - error message showed from Store
    }
  };

  const handleCancel = () => {
    setIsEdit(false);
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setLocalCompany((state) => ({ ...state, [field]: value }));
  };

  useEffect(() => {
    if (company.triggerSave) {
      handleSave();
    }
  }, [company.triggerSave]);

  return (
    <>
      <div className="overlay show" onClick={() => setIsEdit(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div>
            <h3>Edit Company</h3>
          </div>

          <div className='mt-1'>
            <div>
              <label>Name</label>
              <input type="text" name="name" value={localCompany.name || ''} onChange={handleChange} className='form-input' />
            </div>
            <fieldset style={{ padding: '0.5rem' }}>
              <legend>Company Flags</legend>

              {Object.keys(companyFlags).map((flag) => (
                <div className="list-flex-2">
                  <div>
                    <label>
                      <input type="checkbox" name={flag} checked={!!localCompany[flag]} onChange={handleChange} />
                      {companyFlags[flag]}
                    </label>
                  </div>
                </div>
              ))}
            </fieldset>
          </div>

          <div className='mt-1'>
            <label>Payment Term Days</label>
            <select
              name="payment_term_days"
              className="form-input"
              value={localCompany.payment_term_days}
              onChange={handleChange}
            >
              {paymentTermOptions.map((option) => (
                <option key={`payment-term-${option}`} value={option}>
                  {option} days
                </option>
              ))}
            </select>
          </div>

          <div className='mt-1'>
            <label>Accounting Software</label>
            <select
              name="accounting_software"
              className="form-input"
              value={localCompany.accounting_software}
              onChange={handleChange}
            >
              {Object.keys(accountingSoftwareOptions).map((option) => (
                <option key={`account-software-${option}`} value={option}>
                  {accountingSoftwareOptions[option]}
                </option>
              ))}
            </select>
          </div>

          <div className="between-flex mt-1 admin-sidebar-buttons">
            <a className="button tinyx" onClick={handleSave}>Update</a>
            <a className="button tinyx gray-btn" onClick={handleCancel}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

export default CompanyModalForm;
