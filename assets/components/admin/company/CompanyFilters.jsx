import { useContext } from 'react';
import shallow from 'zustand/shallow';

import companyAdminStore from 'store/admin/companyAdminStore';
import appContext from 'contexts/appContext';

const CompanyFilters = () => {
  const { favouritesOnly, setFavourites, fetchCompanies } = companyAdminStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
      fetchCompanies: state.fetchCompanies,
    }),
    shallow
  );

  const { hasFavourites } = useContext(appContext);

  async function toggleFavourites(e) {
    setFavourites(e.target.checked);
    await fetchCompanies({
      page: 1,
      hasFavourites,
    });
  }

  if (!hasFavourites) {
    return null;
  }

  return (
    <div className="order-list-options">
      <span style={{ paddingRight: '8px' }}>Filters: </span>
      <label className="drop-text admin-order-option between-flex">
        <input
          type="checkbox"
          name="show-favourites"
          className="checkbox-content"
          checked={favouritesOnly}
          onChange={(e) => toggleFavourites(e)}
        />
        <span className="checkbox-content-tick" style={{ backgroundColor: '#ffffff' }} />
        Favourite Customer Companies
      </label>
    </div>
  );
};

export default CompanyFilters;
