import { useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import orderAdminStore from 'store/admin/orderAdminStore';
import appContext from 'contexts/appContext';

const NoOrderNotice = ({ isLoading, orders }) => {
  const { hasFavourites } = useContext(appContext);

  if (isLoading || orders.length) return null;

  const { favouritesOnly, setFavourites, viewType, showPast, setShowPast, setViewType } = orderAdminStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
      viewType: state.viewType,
      showPast: state.showPast,
      setShowPast: state.setShowPast,
      setViewType: state.setViewType,
    }),
    shallow
  );

  return (
    <div className="text-center">
      <h3>No Orders Found</h3>
      {viewType === 'list' && (
        <p>
          Try toggling to{' '}
          <a onClick={() => setShowPast(!showPast)}>{showPast ? 'show upcoming orders' : 'show past orders'}</a>.
        </p>
      )}
      {hasFavourites && favouritesOnly && (
        <p>
          Try toggling to <a onClick={() => setFavourites(false)}>show orders of all customers</a>.
        </p>
      )}
      {viewType === 'weekly' && (
        <p>
          Try switching to <a onClick={() => setViewType('list')}>List view</a>.
        </p>
      )}
    </div>
  );
};

export default NoOrderNotice;
