import PropTypes from 'prop-types';

const Order = ({ order, activeOrderId, setActiveOrder, viewType }) => (
  <div
    className={`list-item order-list-item${order.id === activeOrderId ? ' active' : ''}`}
    onClick={() => setActiveOrder(order)}
  >
    <div className="list-flex-2 invoice-header">
      {viewType === 'weekly' && (
        <>
          <span style={{ fontWeight: 'bold' }}>{order.delivery_time}</span>
          <span href={order.customer_sign_in_path} style={{ paddingLeft: '24px' }}>
            {order.customer_name}
          </span>
        </>
      )}
      {viewType === 'list' && (
        <span style={{ fontWeight: 'bold' }}>
          {order.delivery_date}, {order.delivery_time}
        </span>
      )}
    </div>
    {viewType === 'list' && (
      <div className="list-flex-2">
        <span href={order.customer_sign_in_path} style={{ paddingLeft: '24px' }}>
          {order.customer_name}
        </span>
      </div>
    )}

    <div className={`list-flex-2 order-list-item__status ${order.status}`}>{order.name}</div>

    <div className="list-flex-3 admin-order-address">
      <p>{order.address}</p>
    </div>
    <div className="list-flex-1 admin-suppliers">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div className="supplier-images">
          {order?.suppliers?.length > 2 ? (
            <span className="circle-icon" style={{ background: 'black', color: 'white' }}>
              {order?.suppliers?.length}
            </span>
          ) : (
            order?.suppliers?.map((supplier) => (
              <img src={supplier.img} alt="supplier-icon" className="circle-icon" title={supplier.name} />
            ))
          )}
        </div>

        <div>
          {!!order.yordar_commission && (
            <span className={order.commission_below_recommendation ? 'text-alert' : ''}>
              {order.totals.total} ({order.yordar_commission})
            </span>
          )}
          {!order.yordar_commission && order.totals.total}
        </div>
      </div>
    </div>
  </div>
);

Order.propTypes = {
  order: PropTypes.object.isRequired,
  activeOrderId: PropTypes.number,
  setActiveOrder: PropTypes.func.isRequired,
  viewType: PropTypes.string.isRequired,
};

export default Order;
