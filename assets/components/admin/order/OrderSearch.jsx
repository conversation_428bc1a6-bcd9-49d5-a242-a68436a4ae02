import { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';

// store
import shallow from 'zustand/shallow';
import orderAdminStore from 'store/admin/orderAdminStore';

const OrderSearch = ({ viewType, setViewType }) => {
  const isMounted = useRef(false);

  const { query, setQuery } = orderAdminStore(
    (state) => ({
      query: state.query,
      setQuery: state.setQuery,
    }),
    shallow
  );

  const [localQuery, setLocalQuery] = useState(query);

  useEffect(() => {
    if (localQuery && localQuery.length < 3) return;
    if (isMounted.current) {
      const delayDebounceFn = setTimeout(() => {
        setQuery(localQuery);
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [localQuery]);

  return (
    <div className="between-flex order-functions">
      <input
        className="search-input form-input"
        placeholder="Search by order ID / name or customer name / company name"
        style={{ maxWidth: '500px' }}
        type="search"
        value={localQuery}
        onChange={(event) => setLocalQuery(event.target.value)}
      />
      <div className="between-flex list-toggle">
        <span
          className={`admin-order-toggle ${viewType === 'weekly' ? 'active' : ''} order`}
          onClick={() => setViewType('weekly')}
        >
          Weekly View
        </span>
        <span
          className={`admin-order-toggle ${viewType === 'list' ? 'active' : ''} order`}
          onClick={() => setViewType('list')}
        >
          List View
        </span>
      </div>
    </div>
  );
};

OrderSearch.propTypes = {
  viewType: PropTypes.string.isRequired,
  setViewType: PropTypes.func.isRequired,
};

export default OrderSearch;
