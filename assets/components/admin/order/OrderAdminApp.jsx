import { useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import orderAdminStore from 'store/admin/orderAdminStore';
import appContext from 'contexts/appContext';

// components
import OrderSearch from './OrderSearch';
import OrderList from './OrderList';
import OrderCalendar from './OrderCalendar';

const OrderAdminApp = () => {
  const { hasFavourites, customOrdersOnly } = useContext(appContext);

  const { setFilters, viewType, setViewType } = orderAdminStore(
    (state) => ({
      fetchEndpoint: state.fetchEndpoint,
      setFilters: state.setFilters,
      viewType: state.viewType,
      setViewType: state.setViewType,
    }),
    shallow
  );

  useEffect(() => {
    const storageKey = customOrdersOnly ? 'CustomOrderFilters' : 'OrderFilters';
    const existingFilters = JSON.parse(localStorage.getItem(storageKey));

    setFilters({
      hasFavourites,
      customOrdersOnly,
      favouritesOnly: !!hasFavourites,
      showPast: false,
      query: '',
      viewType: 'weekly',
      ...(customOrdersOnly && { favouritesOnly: false, viewType: 'list' }),
      ...existingFilters, // overwrite filters if exist in localStorage
    });
  }, []);

  if (!viewType) {
    return null;
  }

  return (
    <>
      <OrderSearch viewType={viewType} setViewType={setViewType} />

      {viewType === 'weekly' && <OrderCalendar />}
      {viewType === 'list' && <OrderList />}
    </>
  );
};

export default OrderAdminApp;
