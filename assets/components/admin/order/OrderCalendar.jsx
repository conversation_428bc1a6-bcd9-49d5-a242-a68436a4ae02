import { useState, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import orderAdminStore from 'store/admin/orderAdminStore';

// calendar specific
import CalendarToolbar from './calendar/CalendarToolbar';
import WeekdayOrders from './calendar/WeekdayOrders';
import OrderSkeleton from './OrderSkeleton';
import OrderDetails from './details/OrderDetails';
import NoOrderNotice from './NoOrderNotice';
import OrderFilters from './OrderFilters';

const OrderCalendar = () => {
  const [activeOrder, setActiveOrder] = useState(null);

  const { fetchOrders, orders, loadingList, dates, setDates } = orderAdminStore(
    (state) => ({
      fetchOrders: state.fetchOrders,
      orders: state.orders,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      dates: state.dates,
      setDates: state.setDates,
    }),
    shallow
  );

  // Fetch orders on load
  useEffect(async () => {
    await fetchOrders({ page: 1 });
  }, []);

  // Calculate weekdays
  let weekdays = [];
  let currentDate = dates.from_date.clone();
  while (currentDate && currentDate.isBefore(dates.to_date)) {
    weekdays = [...weekdays, currentDate.clone()];
    currentDate = currentDate.add(1, 'days').clone();
  }

  return (
    <>
      <CalendarToolbar dates={dates} setDates={setDates} />
      {!!activeOrder && <div className="overlay show" onClick={() => setActiveOrder(null)} />}
      <div className="admin-orders">
        <OrderFilters viewType="calendar" />
        <div className="weekdays">
          <NoOrderNotice isLoading={loadingList} orders={orders} />
          {orders.length > 0 &&
            weekdays.map((weekday) => (
              <WeekdayOrders
                key={`weekday-${weekday.format('DD')}`}
                weekday={weekday}
                orders={orders}
                activeOrderId={activeOrder?.id}
                setActiveOrder={setActiveOrder}
              />
            ))}
          {loadingList && <OrderSkeleton />}
        </div>
        {!!activeOrder && (
          <div className={`sidebar-overlay ${activeOrder ? 'open' : 'closed'}`}>
            <div className="admin-order-slider">
              <OrderDetails order={activeOrder} setActiveOrder={setActiveOrder} />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default OrderCalendar;
