import PropTypes from 'prop-types';
import moment from 'moment';

// components
import Order from '../Order';

const WeekdayOrders = ({ weekday, orders, activeOrderId, setActiveOrder }) => {
  const weekOrders = orders.filter(
    (order) => moment(order.delivery_at_raw).format('dd-mm-yyyy') == weekday.format('dd-mm-yyyy')
  );

  return (
    <div className="weekday">
      <h3 className="admin-order-date">{weekday.format('ddd Do, MMM')}</h3>
      {weekOrders.map((order) => (
        <Order
          key={`order-${order.id}`}
          order={order}
          activeOrderId={activeOrderId}
          setActiveOrder={setActiveOrder}
          viewType="weekly"
        />
      ))}
      {!weekOrders.length && (
        <div>
          <p className="admin-no-orders">-</p>
        </div>
      )}
    </div>
  );
};

WeekdayOrders.propTypes = {
  weekday: PropTypes.object.isRequired,
  orders: PropTypes.array.isRequired,
  setActiveOrder: PropTypes.func.isRequired,
  // activeOrderId: PropTypes.number.isRequired,
};

export default WeekdayOrders;
