import { useState } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import orderAdminStore from 'store/admin/orderAdminStore';

// components
import Order from './Order';
import OrderSkeleton from './OrderSkeleton';
import OrderDetails from './details/OrderDetails';
import NoOrderNotice from './NoOrderNotice';
import OrderFilters from './OrderFilters';

const OrderList = () => {
  const [activeOrder, setActiveOrder] = useState(null);

  const { fetchOrders, orders, loadingList, loadingMore, page, hasMore } = orderAdminStore(
    (state) => ({
      fetchOrders: state.fetchOrders,
      orders: state.orders,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchOrders({ page });
      }
    },
  });

  return (
    <div className="mt-2">
      {!!activeOrder && <div className="overlay show" onClick={() => setActiveOrder(null)} />}
      <div className="admin-orders">
        <OrderFilters viewType="list" />
        <div className="weekdays">
          <div className="weekday">
            <NoOrderNotice isLoading={loadingList || loadingMore} orders={orders} />
            {orders.map((order) => (
              <Order
                key={`order-${order.id}`}
                order={order}
                activeOrderId={activeOrder?.id}
                setActiveOrder={setActiveOrder}
                viewType="list"
              />
            ))}
          </div>
          <div ref={sentryRef}>{(loadingList || loadingMore) && <OrderSkeleton />}</div>
        </div>
        {!!activeOrder && (
          <div className={`sidebar-overlay ${activeOrder ? 'open' : 'closed'}`}>
            <div className="admin-order-slider">
              <OrderDetails order={activeOrder} setActiveOrder={setActiveOrder} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderList;
