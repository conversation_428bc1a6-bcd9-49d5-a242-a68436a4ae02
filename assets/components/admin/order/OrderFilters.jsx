import { useContext } from 'react';
import shallow from 'zustand/shallow';

import orderAdminStore from 'store/admin/orderAdminStore';
import appContext from 'contexts/appContext';

const OrderFilters = ({ viewType }) => {
  const { favouritesOnly, showPast, setShowPast, setFavourites } = orderAdminStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
      showPast: state.showPast,
      setShowPast: state.setShowPast,
    }),
    shallow
  );
  const { hasFavourites } = useContext(appContext);

  const noFilters = !hasFavourites && viewType !== 'list';

  if (noFilters) {
    return null;
  }

  return (
    <div className="order-list-options">
      <span style={{ paddingRight: '8px' }}>Filters: </span>
      {hasFavourites && (
        <label className="drop-text admin-order-option between-flex">
          <input
            type="checkbox"
            name="show-favourites"
            className="checkbox-content"
            checked={favouritesOnly}
            onChange={() => setFavourites(!favouritesOnly)}
          />
          <span className="checkbox-content-tick" style={{ backgroundColor: '#ffffff' }} />
          Favourite Customer Orders
        </label>
      )}
      {viewType === 'list' && (
        <label className="drop-text admin-order-option between-flex">
          <input
            type="checkbox"
            name="show-past"
            className="checkbox-content"
            checked={showPast}
            onChange={() => setShowPast(!showPast)}
          />
          <span className="checkbox-content-tick" style={{ backgroundColor: '#ffffff' }} />
          Show Past Orders
        </label>
      )}
    </div>
  );
};

export default OrderFilters;
