import PropTypes from 'prop-types';

const InvoiceDetails = ({ orderInfo }) => {
  const { invoice, billing_frequency, invoice_individually, billing_date, order_card } = orderInfo;

  const payOnAccountByCard = order_card && order_card?.pay_on_account;
  if (order_card && invoice) {
    return (
      <>
        <p>
          <strong>Invoiced: </strong>
          <a href={invoice.document_url} target="_blank" rel="noreferrer">
            {`#${invoice.number} `}
          </a>
          (on {invoice.date})
          {payOnAccountByCard && billing_frequency !== 'instantly' && !invoice_individually && (
            <small> (as part of {billing_frequency} billing)</small> 
          )}
        </p>
        <p>
          <strong>Paid with: </strong>
          Card ending in ..{order_card.last4}.
        </p>
      </>
    );
  }

  if (order_card && !invoice) {
    return (
      <>
        <p>
          <strong>Attached to: </strong>
          Card ending in ..{order_card.last4}.
        </p>
        {(!payOnAccountByCard || billing_frequency === 'instantly' || invoice_individually) && (
          <p>This order will be invoiced individually on delivery.</p> 
        )}
        {payOnAccountByCard && billing_frequency !== 'instantly' && !invoice_individually && (
          <p>
            Will be invoiced as part of {billing_frequency} billing on {billing_date}.
          </p> 
        )}
      </>
    );
  }

  if (invoice) {
    return (
      <p>
        <strong>Invoiced: </strong>
        <a href={invoice.document_url} target="_blank" rel="noreferrer">
          {`#${invoice.number} `}
        </a>
        (on {invoice.date})
        {billing_frequency !== 'instantly' && invoice_individually && (
          <small> (invoiced individually outside {billing_frequency} billing)</small>
        )}
        {billing_frequency !== 'instantly' && !invoice_individually && (
          <small> (invoiced as part of {billing_frequency} billing)</small>
        )}
      </p>
    )
  }

  return (
    <>
      {(invoice_individually || billing_frequency == 'instantly') && (
        <p>
          This order will be invoiced individually on delivery
        </p>
      )}
      {!invoice_individually && billing_frequency != 'instantly' && (
        <p>
          Will be invoiced as part of {billing_frequency} billing on {billing_date}.
        </p>
      )}
    </>
  );
};

InvoiceDetails.propTypes = {
  orderInfo: PropTypes.object.isRequired,
};

export default InvoiceDetails;
