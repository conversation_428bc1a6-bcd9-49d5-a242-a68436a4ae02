const EmailHistory = ({ emails }) => {
  return (
    <>
      <h6 className='order-show-detail-title order'>Email History</h6>
      <ul className='m-0'>
        {emails.map((email, index) => <Email email={email} index={index} />)}
      </ul>
    </>
  )
}

const Email = ({ email, index }) => (
  <li className='mb-1'>
    <h6>Email: #{index + 1}</h6>
    <p>
      <strong>{email.ref}</strong>
    </p>
    <p>
      <strong>Subject: </strong>
      <span>{email.subject}</span>
    </p>
    <p>
      <strong>Recipients: </strong>
      <span>{email.recipient}</span>
    </p>
    <p>
      <strong>Sent At: </strong>
      <span>{email.sent_at}</span>
    </p>
  </li>
)
export default EmailHistory;