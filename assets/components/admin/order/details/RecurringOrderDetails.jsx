const RecurringOrderDetails = ({ orderInfo }) => {
  const {
    pattern,
    template_id: templateID,
    recurrent_id: recurrentID,
    renewed_from_id: renewedFromID,
    renewed_to_id: renewedToID,
  } = orderInfo.recurring_details;

  return (
    <div className="order-show-details-section">
      <h6 className="order-show-detail-title order">Recurring Order Details</h6>
      <p>
        <strong>Frequency: </strong>
        {pattern}
      </p>
      <p>
        <strong>Template ID: </strong>
        {templateID}
      </p>
      <p>
        <strong>Recurrent ID: </strong>
        {recurrentID}
      </p>
      <p>
        <strong>Renewed From ID: </strong>
        {renewedFromID}
      </p>
      <p>
        <strong>Renewed To ID: </strong>
        {renewedToID}
      </p>
    </div>
  );
}

export default RecurringOrderDetails;