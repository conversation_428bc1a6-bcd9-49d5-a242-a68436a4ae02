const OrderHistory = ({ versions }) => {
  return (
    <>
      <h6 className='order-show-detail-title order'>Order History</h6>
      <ul className='m-0'>
        {versions.map((version, index) => <OrderVersion version={version} index={index} />)}
      </ul>
    </>
  )
}

const OrderVersion = ({ version, index }) => (
  <li className='mb-1'>
    <h6>Version: #{index + 1}</h6>
    <p>
      <strong>Who: </strong>
      <span>{version.whodunnit}</span>
    </p>
    <p>
      <strong>Event: </strong>
      <span>{version.event}</span>
    </p>
    <p>
      <strong>Created At: </strong>
      <span>{version.created_at}</span>
    </p>
    {version.changes.map((change) => (
      <p>
        <strong>{change.field}: </strong>
        {!!change.old_value && <span>is changed from {change.old_value} to {change.new_value}</span>}
        {!change.old_value && <span>is set to {change.new_value}</span>}
      </p>
    ))}
  </li>
)

export default OrderHistory;