const DocumentHistory = ({ documents }) => {
  return (
    <>
      <h6 className='order-show-detail-title order'>Document History</h6>
      <ul className='m-0'>
        {documents.map((document, index) => <Document document={document} index={index} />)}
      </ul>
    </>
  )
}

const Document = ({ document, index }) => (
  <li className='mb-1'>
    {!!document.supplier && <h6>{document.supplier}</h6>}
    {document.documents.map((innerDocument) => (
      <p>
        <a href={innerDocument.url} target='_blank'>{innerDocument.name}</a>
      </p>  
    ))}
  </li>
);

export default DocumentHistory;