import { useState } from 'react';

// action
import axios from 'axios';
import { apiAdminOrderPath } from 'routes';

// components
import OrderHistory from './OrderHistory';
import EmailHistory from './EmailHistory';
import DocumentHistory from './DocumentHistory';

const HistoryDetails = ({ order, type }) => {
  const [loadingInfo, setLoadingInfo] = useState(false);
  const [noHistory, setNoHistory] = useState(false);
  const [history, setHistory] = useState([]);

  const fetchHistory = async () => {
    setLoadingInfo(true);
    try {
      const { data: responseHistory } = await axios({
        method: 'GET',
        url: apiAdminOrderPath(order),
        params: { with_history: type.toLowerCase() },
      });
      setHistory(responseHistory);
      if (!responseHistory?.length) setNoHistory(true);
    } catch (e) {
      // do nothing
      setNoHistory(true)
    }
    setLoadingInfo(false);
  };
  return (
    <div className="order-show-details-section">
      {!history.length && !noHistory && !loadingInfo && (
        <a onClick={() => fetchHistory()}>
          Show {type} History
        </a>
      )}
      {loadingInfo && <div className="order-map order-map--small loading" />}
      {!loadingInfo && noHistory && <p><strong>No {type} history found!</strong></p>}
      {!!history.length && type == 'Order' && <OrderHistory versions={history} />}
      {!!history.length && type == 'Email' && <EmailHistory emails={history} />}
      {!!history.length && type == 'Document' && <DocumentHistory documents={history} />}
    </div>
  )  
}

export default HistoryDetails;