import { useState } from 'react';

const AccordionSection = ({ title, subtitle, icon, children, isCompleted = false, defaultOpen = false }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className={`staff-accordion ${isOpen ? 'active' : ''}`}>
      <button className={`staff-accordion__header ${isOpen ? 'active' : ''}`} onClick={toggleOpen} type="button">
        <div className="staff-accordion__header-content">
          <div className={`staff-accordion__header-icon ${isCompleted ? 'completed' : ''}`}>
            {isCompleted ? '✓' : icon}
          </div>
          <div>
            <h3 className="staff-accordion__header-title">{title}</h3>
            {subtitle && <p className="staff-accordion__header-subtitle">{subtitle}</p>}
          </div>
        </div>
        <svg
          className={`staff-accordion__header-chevron ${isOpen ? 'active' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      <div className={`staff-accordion__content ${isOpen ? 'active' : ''}`}>
        <div className="staff-accordion__content-inner">{children}</div>
      </div>
    </div>
  );
};

export default AccordionSection;
