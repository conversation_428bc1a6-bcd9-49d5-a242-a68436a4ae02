import { useState, useEffect } from 'react';

// actions
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { apiAdminStaffDetailsPath } from 'routes';

// components
import AccordionSection from './AccordionSection';
import PersonalDetails from './PersonalDetails';
import BankDetails from './BankDetails';
import TaxDocuments from './TaxDocuments';
import StaffDocuments from './StaffDocuments';

const personalDetails = {
  preferred_name: '',
  email: '',
  date_of_birth: '',
  address: '',
};

const emergencyContactDetails = {
  name: '',
  number: '',
  relationship: '',
};

const bankDetails = {
  account_name: '',
  bsb: '',
  account_number: '',
};

const taxDetails = {
  abn: false,
  tfn: '',
  super: '',
};

const documentDetails = {
  handbook: '',
  safety: '',
};

const initialStaffDetail = {
  personal: personalDetails,
  emergency_contact: emergencyContactDetails,
  bank: bankDetails,
  tax: taxDetails,
  documents: documentDetails,
};

const StaffOnBoardingApp = () => {
  const [staffDetails, setStaffDetails] = useState(initialStaffDetail);
  const [isSaving, setIsSaving] = useState(false);
  const [teamNotified, setTeamNotified] = useState(false);

  useEffect(() => {
    fetchStaffDetails();
  }, []);

  const fetchStaffDetails = async () => {
    try {
      const { data: fetchedDetails } = await axios({
        method: 'GET',
        url: apiAdminStaffDetailsPath({ format: 'json' }),
      });
      setStaffDetails((state) => ({
        ...state,
        ...(!!fetchedDetails.personal && { personal: { ...personalDetails, ...fetchedDetails.personal } }),
        ...(!!fetchedDetails.emergency_contact && {
          emergency_contact: { ...emergencyContactDetails, ...fetchedDetails.emergency_contact },
        }),
        ...(!!fetchedDetails.bank && { bank: { ...bankDetails, ...fetchedDetails.bank } }),
        ...(!!fetchedDetails.tax && { tax: { ...taxDetails, ...fetchedDetails.tax } }),
        ...(!!fetchedDetails.documents && { documents: { ...documentDetails, ...fetchedDetails.documents } }),
      }));
    } catch (error) {
      // do nothing
    }
  };

  const saveDetails = async () => {
    if (isSaving) return;

    setIsSaving(true);
    try {
      const { data } = await axios({
        method: 'POST',
        url: apiAdminStaffDetailsPath({ format: 'json' }),
        data: { staff_details: staffDetails },
        headers: csrfHeaders(),
      });
      if (data.accounts_team_notified) setTeamNotified(true);
    } catch (error) {
      // Could add error handling here if needed
      console.error('Error saving staff details:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (e, kind) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    setStaffDetails((state) => ({
      ...state,
      [kind]: { ...state[kind], [e.target.name]: value },
    }));
  };

  const isPersonalComplete = () =>
    !Object.keys(personalDetails).some((field) => !staffDetails.personal[field]) &&
    !Object.keys(emergencyContactDetails).some((field) => !staffDetails.emergency_contact[field]);

  const isBankComplete = () => !Object.keys(bankDetails).some((field) => !staffDetails.bank[field]);
  const isTaxComplete = () => staffDetails.tax.abn || staffDetails.tax.tfn;
  const isDocumentsComplete = () => !Object.keys(documentDetails).some((field) => !staffDetails.documents[field]);

  // Calculate progress based on individual fields completed
  const calculateFieldProgress = () => {
    const baseFields = [
      // Personal fields
      ...Object.keys(personalDetails).map((field) => staffDetails.personal[field]),
      // Emergency contact fields
      ...Object.keys(emergencyContactDetails).map((field) => staffDetails.emergency_contact[field]),
      // Bank fields
      ...Object.keys(bankDetails).map((field) => staffDetails.bank[field]),
      // Document fields
      ...Object.keys(documentDetails).map((field) => staffDetails.documents[field]),
    ];

    // Tax fields - different based on ABN vs TFN
    const taxFields = staffDetails.tax.abn ? [staffDetails.tax.abn] : [staffDetails.tax.tfn];

    const allFields = [...baseFields, ...taxFields];
    const completedFields = allFields.filter((field) => field && field.toString().trim() !== '').length;
    const totalFields = allFields.length;

    return {
      completed: completedFields,
      total: totalFields,
      percentage: Math.round((completedFields / totalFields) * 100),
      isComplete: completedFields >= totalFields,
    };
  };

  const fieldProgress = calculateFieldProgress();

  const progressSteps = [
    { label: 'Personal', completed: isPersonalComplete() },
    { label: 'Banking', completed: isBankComplete() },
    { label: 'Tax Docs', completed: isTaxComplete() },
    { label: 'Staff Docs', completed: isDocumentsComplete() },
  ];

  const completedCount = progressSteps.filter((step) => step.completed).length;

  return (
    <div className="staff-onboarding">
      <div className="staff-onboarding__header">
        <h1>Staff Onboarding</h1>
        <p>Complete all sections to finish your onboarding process</p>
      </div>

      <div className="staff-onboarding__progress">
        {progressSteps.map((step, index) => (
          <div key={step.label} className={`staff-onboarding__progress-step ${step.completed ? 'completed' : ''}`}>
            <div className={`staff-onboarding__progress-step-circle ${step.completed ? 'completed' : ''}`}>
              {step.completed ? '✓' : index + 1}
            </div>
            <span className="staff-onboarding__progress-step-label">{step.label}</span>
          </div>
        ))}
      </div>

      <AccordionSection
        title="Personal Information"
        subtitle="Basic details and emergency contact"
        icon="1"
        isCompleted={isPersonalComplete()}
        defaultOpen
      >
        <PersonalDetails staffDetails={staffDetails} handleChange={handleChange} />
      </AccordionSection>

      <AccordionSection
        title="Banking Details"
        subtitle="Account information for payroll"
        icon="2"
        isCompleted={isBankComplete()}
      >
        <BankDetails staffDetails={staffDetails} handleChange={handleChange} />
      </AccordionSection>

      <AccordionSection
        title="Tax Documents"
        subtitle="TFN/ABN and tax arrangements"
        icon="3"
        isCompleted={isTaxComplete()}
      >
        <TaxDocuments staffDetails={staffDetails} setStaffDetails={setStaffDetails} handleChange={handleChange} />
      </AccordionSection>

      <AccordionSection
        title="Staff Documents"
        subtitle="Employee handbook and safety documents"
        icon="4"
        isCompleted={isDocumentsComplete()}
      >
        <StaffDocuments staffDetails={staffDetails} setStaffDetails={setStaffDetails} />
      </AccordionSection>

      {/* Floating Save Container */}
      <div className="staff-onboarding__save-container">
        {teamNotified ? (
          <>
            <div className="staff-onboarding__save-container-info">
              <p className="staff-onboarding__save-container-info-title">✅ Form Submitted Successfully!</p>
              <p className="staff-onboarding__save-container-info-subtitle">
                Your onboarding information has been received
              </p>
            </div>

            <div className="staff-onboarding__save-container-actions">
              <div className="staff-onboarding__save-container-submitted">
                <div className="staff-onboarding__save-container-submitted-text">
                  <span>Thanks! The Yordar team will be in contact soon</span>
                </div>
              </div>
            </div>
          </>
        ) : (
          /* Normal State */
          <>
            <div className="staff-onboarding__save-container-info">
              <p className="staff-onboarding__save-container-info-title">Staff Onboarding Progress</p>
              <p className="staff-onboarding__save-container-info-subtitle">
                {fieldProgress.completed} of {fieldProgress.total} fields completed
              </p>
            </div>

              <div className="staff-onboarding__save-container-actions">
                <span className="staff-onboarding__save-container-progress">{fieldProgress.percentage}% Complete</span>
                <button
                  className="staff-onboarding__save-container-button primary"
                  onClick={saveDetails}
                  type="button"
                  disabled={isSaving}
                >
                  {isSaving && (
                    <>
                      <span>Saving...</span>
                      <div className="spinner" />
                    </>
                  )}
                  {!isSaving && (
                    <>
                      {fieldProgress.isComplete ? (
                        'Submit Data'
                      ) : (
                        <>
                          <span>💾</span>
                          Save Data
                        </>
                      )}
                    </>
                  )}
                </button>
              </div>
          </>
        )}
      </div>
    </div>
  );
};

export default StaffOnBoardingApp;
