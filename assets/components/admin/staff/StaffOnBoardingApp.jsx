import { useState } from 'react';

// components
import AccordionSection from './AccordionSection';
import PersonalDetails from './PersonalDetails';
import BankDetails from './BankDetails';
import TaxDocuments from './TaxDocuments';
import StaffDocuments from './StaffDocuments';

const initialStaffDetail = {
  preffered_name: '',
  email: '',
  date_of_birth: '',
  address: '',
  emergency_contact_name: '',
  emergency_contact_relationship: '',
  emergency_contact_number: '',
  account_name: '',
  bsb: '',
  account_number: '',
};

const StaffOnBoardingApp = () => {
  const [staffDetails, setStaffDetails] = useState(initialStaffDetail);
  const [completedSections, setCompletedSections] = useState({
    personal: false,
    bank: false,
    tax: false,
    documents: false,
  });

  const handleChange = (e) => {
    setStaffDetails((state) => ({ ...state, [e.target.name]: e.target.value }));
  };

  const markSectionComplete = (section) => {
    setCompletedSections((prev) => ({ ...prev, [section]: true }));
  };

  const isPersonalComplete = () =>
    staffDetails.preffered_name &&
    staffDetails.email &&
    staffDetails.date_of_birth &&
    staffDetails.address &&
    staffDetails.emergency_contact_name &&
    staffDetails.emergency_contact_number &&
    staffDetails.emergency_contact_relationship;

  const isBankComplete = () => staffDetails.account_name && staffDetails.bsb && staffDetails.account_number;

  const progressSteps = [
    { label: 'Personal', completed: isPersonalComplete() },
    { label: 'Banking', completed: isBankComplete() },
    { label: 'Tax Docs', completed: completedSections.tax },
    { label: 'Staff Docs', completed: completedSections.documents },
  ];

  const completedCount = progressSteps.filter((step) => step.completed).length;
  const allComplete = completedCount === progressSteps.length;

  return (
    <div className="staff-onboarding">
      <div className="staff-onboarding__header">
        <h1>Staff Onboarding</h1>
        <p>Complete all sections to finish your onboarding process</p>
      </div>

      <div className="staff-onboarding__progress">
        {progressSteps.map((step, index) => (
          <div key={step.label} className={`staff-onboarding__progress-step ${step.completed ? 'completed' : ''}`}>
            <div className={`staff-onboarding__progress-step-circle ${step.completed ? 'completed' : ''}`}>
              {step.completed ? '✓' : index + 1}
            </div>
            <span className="staff-onboarding__progress-step-label">{step.label}</span>
          </div>
        ))}
      </div>

      <AccordionSection
        title="Personal Information"
        subtitle="Basic details and emergency contact"
        icon="1"
        isCompleted={isPersonalComplete()}
        defaultOpen
      >
        <PersonalDetails
          staffDetails={staffDetails}
          handleChange={handleChange}
          onComplete={() => markSectionComplete('personal')}
        />
      </AccordionSection>

      <AccordionSection
        title="Banking Details"
        subtitle="Account information for payroll"
        icon="2"
        isCompleted={isBankComplete()}
      >
        <BankDetails
          staffDetails={staffDetails}
          handleChange={handleChange}
          onComplete={() => markSectionComplete('bank')}
        />
      </AccordionSection>

      <AccordionSection
        title="Tax Documents"
        subtitle="TFN and superannuation forms"
        icon="3"
        isCompleted={completedSections.tax}
      >
        <TaxDocuments
          staffDetails={staffDetails}
          handleChange={handleChange}
          onComplete={() => markSectionComplete('tax')}
        />
      </AccordionSection>

      <AccordionSection
        title="Staff Documents"
        subtitle="Employee handbook and safety documents"
        icon="4"
        isCompleted={completedSections.documents}
      >
        <StaffDocuments
          staffDetails={staffDetails}
          handleChange={handleChange}
          onComplete={() => markSectionComplete('documents')}
        />
      </AccordionSection>

      {allComplete && (
        <div className="staff-completion">
          <div className="staff-completion__icon">✓</div>
          <h3>Onboarding Complete!</h3>
          <p>All sections have been completed. Welcome to the team!</p>
        </div>
      )}
    </div>
  );
};

export default StaffOnBoardingApp;
