import { useState, useEffect } from 'react';

// actions
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
// import debounce from 'debounce-promise'; 
import { apiAdminStaffDetailsPath } from 'routes';

// components
import AccordionSection from './AccordionSection';
import PersonalDetails from './PersonalDetails';
import BankDetails from './BankDetails';
import TaxDocuments from './TaxDocuments';
import StaffDocuments from './StaffDocuments';

const personalDetails = {
  preffered_name: '',
  email: '',
  date_of_birth: '',
  address: '',
}

const emergencyContactDetails = {
  name: '',
  number: '',
  relationship: '',  
}

const bankDetails = {
  account_name: '',
  bsb: '',
  account_number: '',
}

const taxDetails = {
  tfn: '',
  super: '',
}

const documentDetails = {
  handbook: '',
  safety: '',
}

const initialStaffDetail = {
  personal: personalDetails,
  emergency_contact: emergencyContactDetails,
  bank: bankDetails,
  tax: taxDetails,
  documents: documentDetails
}

const StaffOnBoardingApp = () => {
  const [staffDetails, setStaffDetails] = useState(initialStaffDetail);
  const [completedSections, setCompletedSections] = useState({
    personal: false,
    bank: false,
    tax: false,
    documents: false,
  });

  useEffect(() => {
    fetchStaffDetails();
  }, [])

  const fetchStaffDetails = async () => {
    try {
      const { data: fetchedDetails } = await axios({
        method: 'GET',
        url: apiAdminStaffDetailsPath({ format: 'json' }),
      });
      setStaffDetails((state) => ({
        ...state,
        ...(!!fetchedDetails.personal && { personal: fetchedDetails.personal }),
        ...(!!fetchedDetails.emergency_contact && { emergency_contact: fetchedDetails.emergency_contact }),
        ...(!!fetchedDetails.bank && { bank: fetchedDetails.bank }),
        ...(!!fetchedDetails.tax && { tax: fetchedDetails.tax }),
        ...(!!fetchedDetails.documents && { documents: fetchedDetails.documents }),
      }));
    } catch (error) {
      // do nothing
    }
  }


  const saveDetails = async () => {
    try {
      const { data } = await axios({
        method: 'POST',
        url: apiAdminStaffDetailsPath({ format: 'json' }),
        data: { staff_details: staffDetails },
        headers: csrfHeaders(),
      });
    } catch (error) {
      // do nothing
    }
  }

  const handleChange = (e, kind) => {
    setStaffDetails((state) => (
      {
        ...state,
        [kind]: { ...state[kind], [e.target.name]: e.target.value },
      }
    ));
  };

  const markSectionComplete = (section) => {
    setCompletedSections((prev) => ({ ...prev, [section]: true }));
  };

  const isPersonalComplete = () => (
    !(Object.keys(personalDetails).some((field) => !staffDetails.personal[field])) && 
      !(Object.keys(emergencyContactDetails).some((field) => !staffDetails.emergency_contact[field]))
  );

  const isBankComplete = () => !(Object.keys(bankDetails).some((field) => !staffDetails.bank[field]));
  const isTaxComplete = () => !(Object.keys(taxDetails).some((field) => !staffDetails.tax[field]));
  const isDocumentsComplete = () => !(Object.keys(documentDetails).some((field) => !staffDetails.documents[field]));

  const progressSteps = [
    { label: 'Personal', completed: isPersonalComplete() },
    { label: 'Banking', completed: isBankComplete() },
    { label: 'Tax Docs', completed: isTaxComplete() },
    { label: 'Staff Docs', completed: isDocumentsComplete() },
  ];

  const completedCount = progressSteps.filter((step) => step.completed).length;
  const allComplete = completedCount === progressSteps.length;

  return (
    <div className="staff-onboarding">
      <div className="staff-onboarding__header">
        <h1>Staff Onboarding</h1>
        <p>Complete all sections to finish your onboarding process</p>
        <a className='button tiny' onClick={saveDetails}>Save Data</a>
      </div>

      <div className="staff-onboarding__progress">
        {progressSteps.map((step, index) => (
          <div key={step.label} className={`staff-onboarding__progress-step ${step.completed ? 'completed' : ''}`}>
            <div className={`staff-onboarding__progress-step-circle ${step.completed ? 'completed' : ''}`}>
              {step.completed ? '✓' : index + 1}
            </div>
            <span className="staff-onboarding__progress-step-label">{step.label}</span>
          </div>
        ))}
      </div>

      <AccordionSection
        title="Personal Information"
        subtitle="Basic details and emergency contact"
        icon="1"
        isCompleted={isPersonalComplete()}
        defaultOpen
      >
        <PersonalDetails
          staffDetails={staffDetails}
          handleChange={handleChange}
        />
      </AccordionSection>

      <AccordionSection
        title="Banking Details"
        subtitle="Account information for payroll"
        icon="2"
        isCompleted={isBankComplete()}
      >
        <BankDetails
          staffDetails={staffDetails}
          handleChange={handleChange}
        />
      </AccordionSection>

      <AccordionSection
        title="Tax Documents"
        subtitle="TFN and superannuation forms"
        icon="3"
        isCompleted={isTaxComplete()}
      >
        <TaxDocuments
          staffDetails={staffDetails}
          setStaffDetails={setStaffDetails}
        />
      </AccordionSection>

      <AccordionSection
        title="Staff Documents"
        subtitle="Employee handbook and safety documents"
        icon="4"
        isCompleted={isDocumentsComplete()}
      >
        <StaffDocuments
          staffDetails={staffDetails}
          setStaffDetails={setStaffDetails}
        />
      </AccordionSection>

      {allComplete && (
        <div className="staff-completion">
          <div className="staff-completion__icon">✓</div>
          <h3>Onboarding Complete!</h3>
          <p>All sections have been completed. Welcome to the team!</p>
        </div>
      )}
    </div>
  );
};

export default StaffOnBoardingApp;
