import { useState, useEffect } from 'react';

// actions
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
// import debounce from 'debounce-promise';
import { apiAdminStaffDetailsPath } from 'routes';

// components
import AccordionSection from './AccordionSection';
import PersonalDetails from './PersonalDetails';
import BankDetails from './BankDetails';
import TaxDocuments from './TaxDocuments';
import StaffDocuments from './StaffDocuments';

const personalDetails = {
  preffered_name: '',
  email: '',
  date_of_birth: '',
  address: '',
};

const emergencyContactDetails = {
  name: '',
  number: '',
  relationship: '',
};

const bankDetails = {
  account_name: '',
  bsb: '',
  account_number: '',
};

const taxDetails = {
  tfn: '',
  super: '',
};

const documentDetails = {
  handbook: '',
  safety: '',
};

const initialStaffDetail = {
  personal: personalDetails,
  emergency_contact: emergencyContactDetails,
  bank: bankDetails,
  tax: taxDetails,
  documents: documentDetails,
};

const StaffOnBoardingApp = () => {
  const [staffDetails, setStaffDetails] = useState(initialStaffDetail);
  const [completedSections, setCompletedSections] = useState({
    personal: false,
    bank: false,
    tax: false,
    documents: false,
  });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchStaffDetails();
  }, []);

  const fetchStaffDetails = async () => {
    try {
      const { data: fetchedDetails } = await axios({
        method: 'GET',
        url: apiAdminStaffDetailsPath({ format: 'json' }),
      });
      setStaffDetails((state) => ({
        ...state,
        ...(!!fetchedDetails.personal && { personal: fetchedDetails.personal }),
        ...(!!fetchedDetails.emergency_contact && { emergency_contact: fetchedDetails.emergency_contact }),
        ...(!!fetchedDetails.bank && { bank: fetchedDetails.bank }),
        ...(!!fetchedDetails.tax && { tax: fetchedDetails.tax }),
        ...(!!fetchedDetails.documents && { documents: fetchedDetails.documents }),
      }));
    } catch (error) {
      // do nothing
    }
  };

  const saveDetails = async () => {
    if (saving) return; // Prevent multiple saves

    setSaving(true);
    try {
      await axios({
        method: 'POST',
        url: apiAdminStaffDetailsPath({ format: 'json' }),
        data: { staff_details: staffDetails },
        headers: csrfHeaders(),
      });
      // Could add toast notification here if needed
    } catch (error) {
      // Could add error handling here if needed
      console.error('Error saving staff details:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (e, kind) => {
    setStaffDetails((state) => ({
      ...state,
      [kind]: { ...state[kind], [e.target.name]: e.target.value },
    }));
  };

  const markSectionComplete = (section) => {
    setCompletedSections((prev) => ({ ...prev, [section]: true }));
  };

  const isPersonalComplete = () =>
    !Object.keys(personalDetails).some((field) => !staffDetails.personal[field]) &&
    !Object.keys(emergencyContactDetails).some((field) => !staffDetails.emergency_contact[field]);

  const isBankComplete = () => !Object.keys(bankDetails).some((field) => !staffDetails.bank[field]);
  const isTaxComplete = () => !Object.keys(taxDetails).some((field) => !staffDetails.tax[field]);
  const isDocumentsComplete = () => !Object.keys(documentDetails).some((field) => !staffDetails.documents[field]);

  // Calculate progress based on individual fields completed
  const calculateFieldProgress = () => {
    const allFields = [
      // Personal fields
      ...Object.keys(personalDetails).map((field) => staffDetails.personal[field]),
      // Emergency contact fields
      ...Object.keys(emergencyContactDetails).map((field) => staffDetails.emergency_contact[field]),
      // Bank fields
      ...Object.keys(bankDetails).map((field) => staffDetails.bank[field]),
      // Tax fields
      ...Object.keys(taxDetails).map((field) => staffDetails.tax[field]),
      // Document fields
      ...Object.keys(documentDetails).map((field) => staffDetails.documents[field]),
    ];

    const completedFields = allFields.filter((field) => field && field.toString().trim() !== '').length;
    const totalFields = allFields.length;

    return {
      completed: completedFields,
      total: totalFields,
      percentage: Math.round((completedFields / totalFields) * 100),
    };
  };

  const fieldProgress = calculateFieldProgress();

  const progressSteps = [
    { label: 'Personal', completed: isPersonalComplete() },
    { label: 'Banking', completed: isBankComplete() },
    { label: 'Tax Docs', completed: isTaxComplete() },
    { label: 'Staff Docs', completed: isDocumentsComplete() },
  ];

  const completedCount = progressSteps.filter((step) => step.completed).length;
  const allComplete = completedCount === progressSteps.length;

  return (
    <div className="staff-onboarding">
      <div className="staff-onboarding__header">
        <h1>Staff Onboarding</h1>
        <p>Complete all sections to finish your onboarding process</p>
      </div>

      <div className="staff-onboarding__progress">
        {progressSteps.map((step, index) => (
          <div key={step.label} className={`staff-onboarding__progress-step ${step.completed ? 'completed' : ''}`}>
            <div className={`staff-onboarding__progress-step-circle ${step.completed ? 'completed' : ''}`}>
              {step.completed ? '✓' : index + 1}
            </div>
            <span className="staff-onboarding__progress-step-label">{step.label}</span>
          </div>
        ))}
      </div>

      <AccordionSection
        title="Personal Information"
        subtitle="Basic details and emergency contact"
        icon="1"
        isCompleted={isPersonalComplete()}
        defaultOpen
      >
        <PersonalDetails staffDetails={staffDetails} handleChange={handleChange} />
      </AccordionSection>

      <AccordionSection
        title="Banking Details"
        subtitle="Account information for payroll"
        icon="2"
        isCompleted={isBankComplete()}
      >
        <BankDetails staffDetails={staffDetails} handleChange={handleChange} />
      </AccordionSection>

      <AccordionSection
        title="Tax Documents"
        subtitle="TFN and superannuation forms"
        icon="3"
        isCompleted={isTaxComplete()}
      >
        <TaxDocuments staffDetails={staffDetails} setStaffDetails={setStaffDetails} />
      </AccordionSection>

      <AccordionSection
        title="Staff Documents"
        subtitle="Employee handbook and safety documents"
        icon="4"
        isCompleted={isDocumentsComplete()}
      >
        <StaffDocuments staffDetails={staffDetails} setStaffDetails={setStaffDetails} />
      </AccordionSection>

      {allComplete && (
        <div className="staff-completion">
          <div className="staff-completion__icon">✓</div>
          <h3>Onboarding Complete!</h3>
          <p>All sections have been completed. Welcome to the team!</p>
        </div>
      )}

      {/* Floating Save Container */}
      <div className="staff-onboarding__save-container">
        <div className="staff-onboarding__save-container-info">
          <p className="staff-onboarding__save-container-info-title">Staff Onboarding Progress</p>
          <p className="staff-onboarding__save-container-info-subtitle">
            {fieldProgress.completed} of {fieldProgress.total} fields completed
          </p>
        </div>

        <div className="staff-onboarding__save-container-actions">
          <span className="staff-onboarding__save-container-progress">{fieldProgress.percentage}% Complete</span>
          <button
            className="staff-onboarding__save-container-button secondary"
            onClick={() => window.location.reload()}
            type="button"
          >
            Reset Form
          </button>
          <button
            className="staff-onboarding__save-container-button primary"
            onClick={saveDetails}
            type="button"
            disabled={saving}
          >
            {saving ? (
              <>
                <span>Saving...</span>
                <div
                  style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid white',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                  }}
                />
              </>
            ) : (
              <>
                <span>💾</span>
                Save Data
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default StaffOnBoardingApp;
