import { useState } from 'react';

// components
import PersonalDetails from './PersonalDetails';
import BankDetails from './BankDetails';
import TaxDocuments from './TaxDocuments';
import StaffDocuments from './StaffDocuments';

const initialStaffDetail = {
  preffered_name: '',
  email: '',
  date_of_birth: '',
  emergency_contact_name: '',
  emergency_contact_relationship: '',
  emergency_contact_number: '',
  account_name: '',
  bsb: '',
  account_number: '',
}

const StaffOnBoardingApp = () => {
  const [staffDetails, setStaffDetails] = useState(initialStaffDetail);

  const handleChange = (e) => {
    setStaffDetails((state) => ({ ...state, [e.target.name]: e.target.value }));
  }

  return (
    <div style={{ width: '600px' }}>
      <PersonalDetails staffDetails={staffDetails} handleChange={handleChange} />
      <BankDetails staffDetails={staffDetails} handleChange={handleChange} />
      <TaxDocuments staffDetails={staffDetails} handleChange={handleChange} />
      <StaffDocuments staffDetails={staffDetails} handleChange={handleChange} />
    </div>
  );
}

export default StaffOnBoardingApp;