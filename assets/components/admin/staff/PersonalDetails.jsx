import { useState } from 'react';

const PersonalDetails = ({ staffDetails, handleChange }) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className='mb-1'>
      <a className='button hollow' style={{ width: '100%' }} onClick={() => setShowDetails(!showDetails)}>Personal Information</a>
      {showDetails && (
        <>
          <div>
            <label>Preffered Name</label>
            <input
              type='text'
              name='preffered_name'
              className='form-input'
              value={staffDetails.preffered_name}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>Email (non-yordar)</label>
            <input
              type='email'
              name='email'
              className='form-input'
              value={staffDetails.email}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>Date of Birth</label>
            <input
              type='text'
              name='date_of_birth'
              className='form-input'
              value={staffDetails.date_of_birth}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>Address</label>
            <input
              type='text'
              name='address'
              className='form-input'
              value={staffDetails.address}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>Emergency Contact Name</label>
            <input 
              type='text'
              name='emergency_contact_name'
              className='form-input'
              value={staffDetails.emergency_contact_name}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>Emergency Contact No.</label>
            <input
              type='text'
              name='emergency_contact_number'
              className='form-input'
              value={staffDetails.emergency_contact_number}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>Emergency Relationship</label>
            <input
              type='text'
              name='emergency_contact_relationship'
              className='form-input'
              value={staffDetails.emergency_contact_relationship}
              onChange={handleChange}
            />
          </div>
        </>
      )}
    </div>
  )
}

export default PersonalDetails;