import GoogleAddressInput from './GoogleAddressInput';

const PersonalDetails = ({ staffDetails, handleChange }) => {
  const {
    personal: personalDetails,
    emergency_contact: emergencyContactDetails,
  } = staffDetails;

  return (
    <div className="staff-form">
      <div className="staff-form__row">
        <div className="staff-form__field">
          <label>Preferred Name *</label>
          <input
            type="text"
            name="preferred_name"
            placeholder="Enter your preferred name"
            value={personalDetails.preferred_name}
            onChange={(e) => handleChange(e, 'personal')}
            required
          />
        </div>
        <div className="staff-form__field">
          <label>Email (non-yordar) *</label>
          <input
            type="email"
            name="email"
            placeholder="<EMAIL>"
            value={personalDetails.email}
            onChange={(e) => handleChange(e, 'personal')}
            required
          />
        </div>
      </div>

      <div className="staff-form__row">
        <div className="staff-form__field">
          <label>Date of Birth *</label>
          <input type="date" name="date_of_birth" value={personalDetails.date_of_birth} onChange={(e) => handleChange(e, 'personal')} required />
        </div>
        <div className="staff-form__field">
          <label>Address *</label>
          <GoogleAddressInput
            name="address"
            value={personalDetails.address}
            onChange={(e) => handleChange(e, 'personal')}
            placeholder="Enter your full address"
            required
          />
        </div>
      </div>

      <h4 className='staff-form__subsection-header'>
        Emergency Contact Information
      </h4>

      <div className="staff-form__row">
        <div className="staff-form__field">
          <label>Emergency Contact Name *</label>
          <input
            type="text"
            name="name"
            placeholder="Full name of emergency contact"
            value={emergencyContactDetails.name}
            onChange={(e) => handleChange(e, 'emergency_contact')}
            required
          />
        </div>
        <div className="staff-form__field">
          <label>Emergency Contact Number *</label>
          <input
            type="tel"
            name="number"
            placeholder="Phone number"
            value={emergencyContactDetails.number}
            onChange={(e) => handleChange(e, 'emergency_contact')}
            required
          />
        </div>
      </div>

      <div className="staff-form__row single">
        <div className="staff-form__field">
          <label>Relationship to Emergency Contact *</label>
          <input
            type="text"
            name="relationship"
            placeholder="e.g., Spouse, Parent, Sibling"
            value={emergencyContactDetails.relationship}
            onChange={(e) => handleChange(e, 'emergency_contact')}
            required
          />
        </div>
      </div>
    </div>
  );
}

export default PersonalDetails;
