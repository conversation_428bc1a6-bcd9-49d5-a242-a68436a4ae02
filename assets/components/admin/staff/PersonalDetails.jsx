import GoogleAddressInput from './GoogleAddressInput';

const PersonalDetails = ({ staffDetails, handleChange }) => {
  const {
    personal: personalDetails,
    emergency_contact: emergencyContactDetails,
  } = staffDetails;

  return (
    <div className="staff-form">
      <div className="staff-form__row">
        <div className="staff-form__field">
          <label>Preferred Name *</label>
          <input
            type="text"
            name="preffered_name"
            placeholder="Enter your preferred name"
            value={personalDetails.preffered_name}
            onChange={(e) => handleChange(e, 'personal')}
            required
          />
        </div>
        <div className="staff-form__field">
          <label>Email (non-yordar) *</label>
          <input
            type="email"
            name="email"
            placeholder="<EMAIL>"
            value={personalDetails.email}
            onChange={(e) => handleChange(e, 'personal')}
            required
          />
        </div>
      </div>

      <div className="staff-form__row">
        <div className="staff-form__field">
          <label>Date of Birth *</label>
          <input type="date" name="date_of_birth" value={personalDetails.date_of_birth} onChange={(e) => handleChange(e, 'personal')} required />
        </div>
        <div className="staff-form__field">
          <label>Address *</label>
          <GoogleAddressInput
            name="address"
            value={personalDetails.address}
            onChange={(e) => handleChange(e, 'personal')}
            placeholder="Enter your full address"
            required
          />
        </div>
      </div>

      <h4 style={{ margin: '1rem 0 2rem', color: '#333', borderBottom: '2px solid #e5e5e5', paddingBottom: '0.5rem' }}>
        Emergency Contact Information
      </h4>

      <div className="staff-form__row">
        <div className="staff-form__field">
          <label>Emergency Contact Name *</label>
          <input
            type="text"
            name="name"
            placeholder="Full name of emergency contact"
            value={emergencyContactDetails.name}
            onChange={(e) => handleChange(e, 'emergency_contact')}
            required
          />
        </div>
        <div className="staff-form__field">
          <label>Emergency Contact Number *</label>
          <input
            type="tel"
            name="number"
            placeholder="Phone number"
            value={emergencyContactDetails.number}
            onChange={(e) => handleChange(e, 'emergency_contact')}
            required
          />
        </div>
      </div>

      <div className="staff-form__row single">
        <div className="staff-form__field">
          <label>Relationship to Emergency Contact *</label>
          <input
            type="text"
            name="relationship"
            placeholder="e.g., Spouse, Parent, Sibling"
            value={emergencyContactDetails.relationship}
            onChange={(e) => handleChange(e, 'emergency_contact')}
            required
          />
        </div>
      </div>
    </div>
  );
}

export default PersonalDetails;
