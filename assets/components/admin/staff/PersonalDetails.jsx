const PersonalDetails = ({ staffDetails, handleChange }) => (
  <div className="staff-form">
    <div className="staff-form__row">
      <div className="staff-form__field">
        <label>Preferred Name *</label>
        <input
          type="text"
          name="preffered_name"
          placeholder="Enter your preferred name"
          value={staffDetails.preffered_name}
          onChange={handleChange}
          required
        />
      </div>
      <div className="staff-form__field">
        <label>Email (non-yordar) *</label>
        <input
          type="email"
          name="email"
          placeholder="<EMAIL>"
          value={staffDetails.email}
          onChange={handleChange}
          required
        />
      </div>
    </div>

    <div className="staff-form__row">
      <div className="staff-form__field">
        <label>Date of Birth *</label>
        <input type="date" name="date_of_birth" value={staffDetails.date_of_birth} onChange={handleChange} required />
      </div>
      <div className="staff-form__field">
        <label>Address *</label>
        <input
          type="text"
          name="address"
          placeholder="Enter your full address"
          value={staffDetails.address}
          onChange={handleChange}
          required
        />
      </div>
    </div>

    <h4 style={{ margin: '1rem 0 2rem', color: '#333', borderBottom: '2px solid #e5e5e5', paddingBottom: '0.5rem' }}>
      Emergency Contact Information
    </h4>

    <div className="staff-form__row">
      <div className="staff-form__field">
        <label>Emergency Contact Name *</label>
        <input
          type="text"
          name="emergency_contact_name"
          placeholder="Full name of emergency contact"
          value={staffDetails.emergency_contact_name}
          onChange={handleChange}
          required
        />
      </div>
      <div className="staff-form__field">
        <label>Emergency Contact Number *</label>
        <input
          type="tel"
          name="emergency_contact_number"
          placeholder="Phone number"
          value={staffDetails.emergency_contact_number}
          onChange={handleChange}
          required
        />
      </div>
    </div>

    <div className="staff-form__row single">
      <div className="staff-form__field">
        <label>Relationship to Emergency Contact *</label>
        <input
          type="text"
          name="emergency_contact_relationship"
          placeholder="e.g., Spouse, Parent, Sibling"
          value={staffDetails.emergency_contact_relationship}
          onChange={handleChange}
          required
        />
      </div>
    </div>
  </div>
);

export default PersonalDetails;
