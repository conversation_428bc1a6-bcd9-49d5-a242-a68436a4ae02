import { useState, useContext } from 'react';
import { usePlacesWidget } from 'react-google-autocomplete';
import appContext from 'contexts/appContext';

const GOOGLE_AUTOCOMPLETE_OPTIONS = (countryCode) => ({
  types: ['geocode'],
  fields: ['place_id', 'types', 'formatted_address', 'address_components'],
  componentRestrictions: {
    country: countryCode || 'au',
  },
});

const GoogleAddressInput = ({
  name,
  value,
  onChange,
  placeholder = "Enter your full address",
  required = false
}) => {
  const [currentValue, setCurrentValue] = useState(value || '');
  const [isSelected, setIsSelected] = useState(false);
  const [error, setError] = useState(null);
  const context = useContext(appContext);

  // Fallback if context is not available
  const googleApiKey = context?.googleApiKey;
  const countryCode = context?.countryCode || 'au';

  const handlePlaceSelected = (place) => {
    if (!place || !place.formatted_address) return;

    const formattedAddress = place.formatted_address;
    setCurrentValue(formattedAddress);
    setIsSelected(true);

    // Create a synthetic event to match the expected onChange signature
    const syntheticEvent = {
      target: {
        name: name,
        value: formattedAddress
      }
    };

    onChange(syntheticEvent);
  };

  const handleInputChange = (event) => {
    const newValue = event.target.value;
    setCurrentValue(newValue);
    setIsSelected(false);

    // Pass through the change event
    onChange(event);
  };

  const handleBlur = () => {
    // If user typed something but didn't select from Google suggestions,
    // we'll allow it but show a subtle indication
    if (currentValue && !isSelected) {
      // You could add validation logic here if needed
    }
  };

  // Only initialize Google Places if API key is available
  const { ref } = usePlacesWidget({
    apiKey: googleApiKey,
    onPlaceSelected: handlePlaceSelected,
    options: GOOGLE_AUTOCOMPLETE_OPTIONS(countryCode),
  });

  // Fallback to regular input if Google API key is not available
  if (!googleApiKey) {
    return (
      <input
        type="text"
        name={name}
        placeholder={placeholder}
        value={currentValue}
        onChange={handleInputChange}
        onBlur={handleBlur}
        required={required}
        autoComplete="off"
        className="staff-form__input"
      />
    );
  }

  return (
    <div className="google-address-input">
      <input
        ref={ref}
        type="text"
        name={name}
        placeholder={placeholder}
        value={currentValue}
        onChange={handleInputChange}
        onBlur={handleBlur}
        required={required}
        autoComplete="off"
        className={`staff-form__input ${isSelected ? 'google-selected' : ''}`}
      />
      {currentValue && !isSelected && (
        <div className="google-address-input__hint">
          <small>💡 Select from dropdown for best results</small>
        </div>
      )}
      {error && (
        <div className="google-address-input__error">
          <small style={{ color: '#dc3545' }}>⚠️ {error}</small>
        </div>
      )}
    </div>
  );
};

export default GoogleAddressInput;
