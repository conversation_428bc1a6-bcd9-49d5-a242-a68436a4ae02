import { useContext, useState } from 'react';
import appContext from 'contexts/appContext';

const TaxDocuments = ({ staffDetails, setStaffDetails }) => {
  const { tax: taxDetails } = staffDetails;
  const { tfnDocument, superDocument } = useContext(appContext);

  const handleDocumentUpload = (docType) => {
    setStaffDetails((state) => (
      {
        ...state,
        tax: { ...state.tax, [docType]: true },
      }
    ));
  };

  return (
    <div className="staff-form">
      <p className="staff-form__p">
        Please download, complete, and upload the required tax documents. These are mandatory for employment.
      </p>

      <div className="staff-form__document-section">
        <h4>Tax File Number (TFN) Declaration</h4>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          Please complete a TFN declaration form and upload it here. With a myGov account, you can generate a form
          through the ATO > Employment > New Employment.
        </p>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          If you do not have a myGov account, you can download the form from the ATO website and complete it manually.
        </p>
        <div className="staff-form__document-section-actions">
          <button
            className={`staff-form__button ${taxDetails.tfn ? 'primary' : ''}`}
            onClick={() => handleDocumentUpload('tfn')}
            type="button"
          >
            {taxDetails.tfn ? '✓ Uploaded' : '📤 Upload TFN Declaration'}
          </button>
        </div>
      </div>

      <div className="staff-form__document-section">
        <h4>Superannuation Details</h4>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          Please attach any relevant super annuation documents. Leave this blank if you've provided these details in the
          TFN form or if this option does not apply.
        </p>
        <div className="staff-form__document-section-actions">
          <button
            className={`staff-form__button ${taxDetails.super ? 'primary' : ''}`}
            onClick={() => handleDocumentUpload('super')}
            type="button"
          >
            {taxDetails.super ? '✓ Uploaded' : '📤 Upload Form'}
          </button>
        </div>
      </div>

      {taxDetails.tfn && taxDetails.super && (
        <div className="staff-form__notice staff-form__notice--green">
          <p style={{ margin: 0, fontSize: '0.9rem', color: '#155724' }}>
            <strong>✓ All tax documents completed!</strong> Your forms have been received and will be processed.
          </p>
        </div>
      )}
    </div>
  );
};

export default TaxDocuments;
