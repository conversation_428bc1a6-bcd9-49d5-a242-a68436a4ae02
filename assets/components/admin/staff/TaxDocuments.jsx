import { useContext, useState } from 'react';
import appContext from 'contexts/appContext';

// actions
import axios from 'axios';

const TaxDocuments = ({ staffDetails, setStaffDetails }) => {
  const [loading, setLoading] = useState({});
  const { tax: taxDetails } = staffDetails;
  const { cloudinary } = useContext(appContext);

  const handleDocumentUpload = async (event, docType) => {
    const newFile = event.target.files[0];
    if (newFile) {
      setLoading((state) => ({ ...state, [docType]: true }));
      try {
        const imageData = new FormData();
        imageData.append('file', newFile);
        imageData.append('upload_preset', cloudinary.uploadPreset);

        const { data: cloudinaryData } = await axios({
          method: 'POST',
          url: cloudinary.uploadUrl,
          data: imageData,
        });
        setStaffDetails((state) => ({
          ...state,
          tax: { ...state.tax, [docType]: cloudinaryData.secure_url },
        }));
      } catch (error) {
        // do nothing
      }
      setLoading((state) => ({ ...state, [docType]: false }));
    }
  };

  return (
    <div className="staff-form">
      <p className="staff-form__p">
        Please download, complete, and upload the required tax documents. These are mandatory for employment.
      </p>

      <div className="staff-form__document-section">
        <h4>Tax File Number (TFN) Declaration</h4>
        <p>
          Please complete a TFN declaration form and upload it here. With a myGov account, you can generate a form
          through the ATO > Employment > New Employment.
        </p>
        <p>
          If you do not have a myGov account, you can download the form from the ATO website and complete it manually.
        </p>

        <div className="staff-form__document-section-actions">
          <input id="tfn-uploader" type="file" className="hidden" onChange={(e) => handleDocumentUpload(e, 'tfn')} />
          <label
            htmlFor="tfn-uploader"
            className={`staff-form__button staff-form__button-label ${taxDetails.tfn && !loading.tfn ? 'primary' : ''}`}
          >
            {loading.tfn ? '📤 Uploading...' : taxDetails.tfn ? '✓ Uploaded' : '📤 Upload TFN Declaration'}
          </label>

          {!!taxDetails.tfn && !loading.tfn && (
            <a className="ml-2 mt-1-2" href={taxDetails.tfn} target="_blank" rel="noreferrer">
              Uploaded Document
            </a>
          )}
        </div>
      </div>

      <div className="staff-form__document-section">
        <h4>Superannuation Details</h4>
        <p>
          Please attach any relevant super annuation documents. Leave this blank if you've provided these details in the
          TFN form or if this option does not apply.
        </p>
        <div className="staff-form__document-section-actions">
          <input
            id="super-uploader"
            type="file"
            className="hidden"
            onChange={(e) => handleDocumentUpload(e, 'super')}
          />
          <label
            htmlFor="super-uploader"
            className={`staff-form__button staff-form__button-label ${taxDetails.super && !loading.super ? 'primary' : ''
              }`}
          >
            {loading.super ? '📤 Uploading...' : taxDetails.super ? '✓ Uploaded' : '📤 Upload Form'}
          </label>

          {!!taxDetails.super && !loading.super && (
            <a className="ml-2 mt-1-2" href={taxDetails.super} target="_blank" rel="noreferrer">
              Uploaded Document
            </a>
          )}
        </div>
      </div>

      {taxDetails.tfn && taxDetails.super && (
        <div className="staff-form__notice staff-form__notice--green">
          <p>
            <strong>✓ All tax documents completed!</strong> Your forms have been received and will be processed.
          </p>
        </div>
      )}
    </div>
  );
};

export default TaxDocuments;
