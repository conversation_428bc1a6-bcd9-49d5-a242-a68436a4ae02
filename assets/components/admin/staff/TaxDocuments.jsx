import { useContext, useState } from 'react';
import appContext from 'contexts/appContext';

const TaxDocuments = ({ staffDetails, handleChange, onComplete }) => {
  const { tfnDocument, superDocument } = useContext(appContext);
  const [uploadedDocs, setUploadedDocs] = useState({
    tfn: false,
    super: false,
  });

  const handleDocumentUpload = (docType) => {
    // Simulate document upload
    setUploadedDocs((prev) => ({ ...prev, [docType]: true }));

    // Mark section as complete if both documents are uploaded
    if ((docType === 'tfn' && uploadedDocs.super) || (docType === 'super' && uploadedDocs.tfn)) {
      onComplete && onComplete();
    }
  };

  return (
    <div className="staff-form">
      <p className="staff-form__p">
        Please download, complete, and upload the required tax documents. These are mandatory for employment.
      </p>

      <div className="staff-form__document-section">
        <h4>Tax File Number (TFN) Declaration</h4>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          Complete this form to ensure correct tax deductions from your salary.
        </p>
        <div className="staff-form__document-section-actions">
          <a className="staff-form__button" href={tfnDocument} target="_blank" rel="noopener noreferrer">
            📄 Download TFN Form
          </a>
          <button
            className={`staff-form__button ${uploadedDocs.tfn ? 'primary' : ''}`}
            onClick={() => handleDocumentUpload('tfn')}
            type="button"
          >
            {uploadedDocs.tfn ? '✓ Uploaded' : '📤 Upload Completed Form'}
          </button>
        </div>
      </div>

      <div className="staff-form__document-section">
        <h4>Superannuation Choice Form</h4>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          Choose your superannuation fund or use the default company fund.
        </p>
        <div className="staff-form__document-section-actions">
          <a className="staff-form__button" href={superDocument} target="_blank" rel="noopener noreferrer">
            📄 Download Super Form
          </a>
          <button
            className={`staff-form__button ${uploadedDocs.super ? 'primary' : ''}`}
            onClick={() => handleDocumentUpload('super')}
            type="button"
          >
            {uploadedDocs.super ? '✓ Uploaded' : '📤 Upload Completed Form'}
          </button>
        </div>
      </div>

      {uploadedDocs.tfn && uploadedDocs.super && (
        <div className="staff-form__notice staff-form__notice--green">
          <p style={{ margin: 0, fontSize: '0.9rem', color: '#155724' }}>
            <strong>✓ All tax documents completed!</strong> Your forms have been received and will be processed.
          </p>
        </div>
      )}
    </div>
  );
};

export default TaxDocuments;
