import { useState, useContext } from 'react';
import appContext from 'contexts/appContext';

const TaxDocuments = ({ staffDetails, handleChange }) => {
  const [showDetails, setShowDetails] = useState(false);
  const { tfnDocument, superDocument } = useContext(appContext);  

  return (
    <div className='mb-1'>
      <a className='button hollow' style={{ width: '100%' }} onClick={() => setShowDetails(!showDetails)}>Tax Documents</a>
      {showDetails && (
        <>
          <div>
            <label>Tax File Number (TFN)</label>
            <div>
              <a className='button tiny' href={tfnDocument}>Dowload Document</a>  
              <a className='button tiny ml-1-2'>Upload Signed Document</a>  
            </div>
          </div>
          <div>
            <label>Superannuation</label>
            <div>
              <a className='button tiny' href={superDocument}>Dowload Document</a>  
              <a className='button tiny ml-1-2'>Upload Signed Document</a>  
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default TaxDocuments;