import { useContext, useState } from 'react';
import appContext from 'contexts/appContext';

// actions
import axios from 'axios';

const TaxDocuments = ({ staffDetails, setStaffDetails }) => {
  const [loading, setLoading] = useState({});
  const { tax: taxDetails } = staffDetails;
  const { cloudinary } = useContext(appContext);

  // Get ABN toggle state from staffDetails, default to false
  const useABN = taxDetails.useABN || false;

  const handleDocumentUpload = async (event, docType) => {
    const newFile = event.target.files[0];
    if (newFile) {
      setLoading((state) => ({ ...state, [docType]: true }));
      try {
        const imageData = new FormData();
        imageData.append('file', newFile);
        imageData.append('upload_preset', cloudinary.uploadPreset);

        const { data: cloudinaryData } = await axios({
          method: 'POST',
          url: cloudinary.uploadUrl,
          data: imageData,
        });
        setStaffDetails((state) => ({
          ...state,
          tax: { ...state.tax, [docType]: cloudinaryData.secure_url },
        }));
      } catch (error) {
        // do nothing
      }
      setLoading((state) => ({ ...state, [docType]: false }));
    }
  };

  const handleABNToggle = () => {
    const newUseABN = !useABN;

    if (newUseABN) {
      // Switching to ABN - clear TFN and super fields, set ABN flag
      setStaffDetails((state) => ({
        ...state,
        tax: { ...state.tax, tfn: '', super: '', useABN: true },
      }));
    } else {
      // Switching to TFN - clear ABN field, remove ABN flag
      setStaffDetails((state) => ({
        ...state,
        tax: { ...state.tax, abn: '', useABN: false },
      }));
    }
  };

  const handleABNChange = (e) => {
    setStaffDetails((state) => ({
      ...state,
      tax: { ...state.tax, abn: e.target.value },
    }));
  };

  return (
    <div className="staff-form">
      <p className="staff-form__p">Please choose your tax arrangement and complete the required documents.</p>

      {/* ABN/TFN Toggle */}
      <div className="staff-form__document-section">
        <h4>Tax Arrangement</h4>
        <p>Choose whether you'll be working as an employee (TFN) or contractor (ABN).</p>

        <div className="staff-form__toggle-container">
          <label className="staff-form__toggle">
            <input type="checkbox" checked={useABN} onChange={handleABNToggle} />
            <span className="staff-form__toggle-slider" />
            <span className="staff-form__toggle-label">
              {useABN ? 'Using ABN (Contractor)' : 'Using TFN (Employee)'}
            </span>
          </label>
        </div>
      </div>

      {/* TFN and Superannuation sections - Only show when TFN is selected */}
      {!useABN && (
        <>
          <div className="staff-form__document-section">
            <h4>Tax File Number (TFN) Declaration</h4>
            <p>
              Please complete a TFN declaration form and upload it here. With a myGov account, you can generate a form
              through the ATO &gt; Employment &gt; New Employment.
            </p>
            <p>
              If you do not have a myGov account, you can download the form from the ATO website and complete it
              manually.
            </p>

            <div className="staff-form__document-section-actions">
              <input
                id="tfn-uploader"
                type="file"
                className="hidden"
                onChange={(e) => handleDocumentUpload(e, 'tfn')}
              />
              <label
                htmlFor="tfn-uploader"
                className={`staff-form__button staff-form__button-label ${taxDetails.tfn && !loading.tfn ? 'primary' : ''
                  }`}
              >
                {loading.tfn ? '📤 Uploading...' : taxDetails.tfn ? '✓ Uploaded' : '📤 Upload TFN Declaration'}
              </label>

              {!!taxDetails.tfn && !loading.tfn && (
                <a className="ml-2 mt-1-2" href={taxDetails.tfn} target="_blank" rel="noreferrer">
                  Uploaded Document
                </a>
              )}
            </div>
          </div>

          <div className="staff-form__document-section">
            <h4>Superannuation Details</h4>
            <p>
              Please attach any relevant super annuation documents. Leave this blank if you've provided these details in
              the TFN form or if this option does not apply.
            </p>
            <div className="staff-form__document-section-actions">
              <input
                id="super-uploader"
                type="file"
                className="hidden"
                onChange={(e) => handleDocumentUpload(e, 'super')}
              />
              <label
                htmlFor="super-uploader"
                className={`staff-form__button staff-form__button-label ${taxDetails.super && !loading.super ? 'primary' : ''
                  }`}
              >
                {loading.super ? '📤 Uploading...' : taxDetails.super ? '✓ Uploaded' : '📤 Upload Form'}
              </label>

              {!!taxDetails.super && !loading.super && (
                <a className="ml-2 mt-1-2" href={taxDetails.super} target="_blank" rel="noreferrer">
                  Uploaded Document
                </a>
              )}
            </div>
          </div>

          {taxDetails.tfn && taxDetails.super && (
            <div className="staff-form__notice staff-form__notice--green">
              <p>
                <strong>✓ All tax documents completed!</strong> Your forms have been received and will be processed.
              </p>
            </div>
          )}
        </>
      )}

      {/* ABN completion notice */}
      {useABN && taxDetails.abn && (
        <div className="staff-form__notice staff-form__notice--green">
          <p>
            <strong>✓ ABN provided!</strong> Your business number has been recorded.
          </p>
        </div>
      )}
    </div>
  );
};

export default TaxDocuments;
