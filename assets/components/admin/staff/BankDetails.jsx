const BankDetails = ({ staffDetails, handleChange }) => (
  <div className="staff-form">
    <p className="staff-form__p">
      Please provide your banking details for payroll processing. All information is securely stored and encrypted.
    </p>

    <div className="staff-form__row single">
      <div className="staff-form__field">
        <label>Account Name *</label>
        <input
          type="text"
          name="account_name"
          placeholder="Name as it appears on your bank account"
          value={staffDetails.account_name}
          onChange={handleChange}
          required
        />
      </div>
    </div>

    <div className="staff-form__row">
      <div className="staff-form__field">
        <label>BSB *</label>
        <input
          type="text"
          name="bsb"
          placeholder="123-456"
          value={staffDetails.bsb}
          onChange={handleChange}
          maxLength="7"
          pattern="[0-9]{3}-?[0-9]{3}"
          required
        />
      </div>
      <div className="staff-form__field">
        <label>Account Number *</label>
        <input
          type="text"
          name="account_number"
          placeholder="Account number"
          value={staffDetails.account_number}
          onChange={handleChange}
          required
        />
      </div>
    </div>

    <div
      style={{
        background: '#e8f4fd',
        border: '1px solid #bee5eb',
        borderRadius: '6px',
        padding: '1rem',
        marginTop: '1rem',
      }}
    >
      <p style={{ margin: 0, fontSize: '0.9rem', color: '#0c5460' }}>
        <strong>Security Note:</strong> Your banking information is encrypted and stored securely. It will only be used
        for payroll purposes and will not be shared with third parties.
      </p>
    </div>
  </div>
);

export default BankDetails;
