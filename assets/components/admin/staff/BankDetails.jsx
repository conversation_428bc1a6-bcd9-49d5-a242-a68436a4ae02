const BankDetails = ({ staffDetails, handleChange }) => {
  const { bank: bankDetails } = staffDetails;

  return (
    <div className="staff-form">
      <p className="staff-form__p">
        Please provide your banking details for payroll processing. All information is securely stored and encrypted.
      </p>

      <div className="staff-form__row single">
        <div className="staff-form__field">
          <label>Account Name *</label>
          <input
            type="text"
            name="account_name"
            placeholder="Name as it appears on your bank account"
            value={bankDetails.account_name}
            onChange={(e) => handleChange(e, 'bank')}
            required
          />
        </div>
      </div>

      <div className="staff-form__row">
        <div className="staff-form__field">
          <label>BSB *</label>
          <input
            type="text"
            name="bsb"
            placeholder="123-456"
            value={bankDetails.bsb}
            onChange={(e) => handleChange(e, 'bank')}
            maxLength="7"
            pattern="[0-9]{3}-?[0-9]{3}"
            required
          />
        </div>
        <div className="staff-form__field">
          <label>Account Number *</label>
          <input
            type="text"
            name="account_number"
            placeholder="Account number"
            value={bankDetails.account_number}
            onChange={(e) => handleChange(e, 'bank')}
            required
          />
        </div>
      </div>

      <div className="staff-form__notice staff-form__notice--blue">
        <p>
          <strong>Security Note:</strong> Your banking information is encrypted and stored securely. It will only be used
          for payroll purposes and will not be shared with third parties.
        </p>
      </div>
    </div>
  );
}

export default BankDetails;
