import { useState } from 'react';

const BankDetails = ({ staffDetails, handleChange }) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className='mb-1'>
      <a className='button hollow' style={{ width: '100%' }} onClick={() => setShowDetails(!showDetails)}>Bank Details</a>
      {showDetails && (
        <>
          <div>
            <label>Account Name</label>
            <input
              type='text'
              name='account_name'
              className='form-input'
              value={staffDetails.account_name}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>BSB</label>
            <input
              type='text'
              name='bsb'
              className='form-input'
              value={staffDetails.bsb}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>Account #</label>
            <input
              type='text'
              name='account_number'
              className='form-input'
              value={staffDetails.account_number}
              onChange={handleChange}
            />
          </div>
        </>
      )}
    </div>
  );
}

export default BankDetails;