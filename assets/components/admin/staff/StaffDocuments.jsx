import { useContext, useState } from 'react';
import appContext from 'contexts/appContext';

const StaffDocuments = ({ staffDetails, handleChange, onComplete }) => {
  const { employeeHandbook, healthAndSafety } = useContext(appContext);
  const [acknowledgedDocs, setAcknowledgedDocs] = useState({
    handbook: false,
    safety: false,
  });

  const handleDocumentAcknowledge = (docType) => {
    const newState = { ...acknowledgedDocs, [docType]: true };
    setAcknowledgedDocs(newState);

    // Mark section as complete if both documents are acknowledged
    if (newState.handbook && newState.safety) {
      onComplete && onComplete();
    }
  };

  return (
    <div className="staff-form">
      <p className="staff-form__p">
        Please review and acknowledge the following company documents. These outline important policies and procedures.
      </p>

      <div className="staff-form__document-section">
        <h4>Employee Handbook</h4>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          Contains company policies, procedures, benefits information, and code of conduct.
        </p>
        <div className="staff-form__document-section-actions">
          <a className="staff-form__button" href={employeeHandbook} target="_blank" rel="noopener noreferrer">
            📖 View Employee Handbook
          </a>
          <button
            className={`staff-form__button ${acknowledgedDocs.handbook ? 'primary' : ''}`}
            onClick={() => handleDocumentAcknowledge('handbook')}
            type="button"
            disabled={acknowledgedDocs.handbook}
          >
            {acknowledgedDocs.handbook ? '✓ Acknowledged' : 'I Have Read & Understand'}
          </button>
        </div>
      </div>

      <div className="staff-form__document-section">
        <h4>Health and Safety Policy</h4>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          Important workplace safety guidelines and emergency procedures.
        </p>
        <div className="staff-form__document-section-actions">
          <a className="staff-form__button" href={healthAndSafety} target="_blank" rel="noopener noreferrer">
            🛡️ View Safety Policy
          </a>
          <button
            className={`staff-form__button ${acknowledgedDocs.safety ? 'primary' : ''}`}
            onClick={() => handleDocumentAcknowledge('safety')}
            type="button"
            disabled={acknowledgedDocs.safety}
          >
            {acknowledgedDocs.safety ? '✓ Acknowledged' : 'I Have Read & Understand'}
          </button>
        </div>
      </div>

      {acknowledgedDocs.handbook && acknowledgedDocs.safety && (
        <div className="staff-form__notice staff-form__notice--green">
          <p style={{ margin: 0, fontSize: '0.9rem', color: '#155724' }}>
            <strong>✓ All documents acknowledged!</strong> Thank you for reviewing the company policies.
          </p>
        </div>
      )}

      <div className="staff-form__notice staff-form__notice--yellow">
        <p style={{ margin: 0, fontSize: '0.9rem', color: '#856404' }}>
          <strong>Important:</strong> By acknowledging these documents, you confirm that you have read, understood, and
          agree to comply with all company policies and procedures.
        </p>
      </div>
    </div>
  );
};

export default StaffDocuments;
