import { useContext, useState } from 'react';
import appContext from 'contexts/appContext';

const StaffDocuments = ({ staffDetails, setStaffDetails }) => {
  const { documents: documentDetails } = staffDetails
  const { employeeHandbook, healthAndSafety } = useContext(appContext);

  const handleDocument = (docType) => {
    setStaffDetails((state) => (
      {
        ...state,
        documents: { ...state.documents, [docType]: true },
      }
    ));
  };

  return (
    <div className="staff-form">
      <p className="staff-form__p">
        Please review and acknowledge the following company documents. These outline important policies and procedures.
      </p>

      <div className="staff-form__document-section">
        <h4>Employee Handbook</h4>
        <p>Contains company policies, procedures, benefits information, and code of conduct.</p>
        <div className="staff-form__document-section-actions">
          <a className="staff-form__button" href={employeeHandbook} target="_blank" rel="noopener noreferrer">
            📖 View Employee Handbook
          </a>

          <button
            className={`staff-form__button ${documentDetails.handbook ? 'primary' : ''}`}
            onClick={() => handleDocument('handbook')}
            type="button"
            disabled={documentDetails.handbook}
          >
            {documentDetails.handbook ? '✓ Acknowledged' : 'I Have Read & Understand'}
          </button>
        </div>
      </div>

      <div className="staff-form__document-section">
        <h4>Health and Safety Handbook</h4>
        <p>Important workplace safety guidelines and emergency procedures.</p>
        <div className="staff-form__document-section-actions">
          <a className="staff-form__button" href={healthAndSafety} target="_blank" rel="noopener noreferrer">
            🛡️ View Safety Handbook
          </a>

          <button
            className={`staff-form__button ${documentDetails.safety ? 'primary' : ''}`}
            onClick={() => handleDocument('safety')}
            type="button"
            disabled={documentDetails.safety}
          >
            {documentDetails.safety ? '✓ Acknowledged' : 'I Have Read & Understand'}
          </button>
        </div>
      </div>

      {documentDetails.handbook && documentDetails.safety && (
        <div className="staff-form__notice staff-form__notice--green">
          <p>
            <strong>✓ All documents acknowledged!</strong> Thank you for reviewing the company policies.
          </p>
        </div>
      )}

      <div className="staff-form__notice staff-form__notice--yellow">
        <p>
          <strong>Important:</strong> By acknowledging these documents, you confirm that you have read, understood, and
          agree to comply with all company policies and procedures.
        </p>
      </div>
    </div>
  );
};

export default StaffDocuments;
