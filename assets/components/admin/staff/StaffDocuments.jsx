import { useContext, useState } from 'react';
import appContext from 'contexts/appContext';

const StaffDocuments = ({ staffDetails, setStaffDetails }) => {
  const { documents: documentDetails } = staffDetails
  const { employeeHandbook, healthAndSafety } = useContext(appContext);

  const handleDocumentAcknowledge = (docType) => {
    setStaffDetails((state) => (
      {
        ...state,
        documents: { ...state.documents, [docType]: true },
      }
    ));
  };

  return (
    <div className="staff-form">
      <p className="staff-form__p">
        Please review and acknowledge the following company documents. These outline important policies and procedures.
      </p>

      <div className="staff-form__document-section">
        <h4>Employee Handbook</h4>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          Contains company policies, procedures, benefits information, and code of conduct.
        </p>
        <div className="staff-form__document-section-actions">
          <a className="staff-form__button" href={employeeHandbook} target="_blank" rel="noopener noreferrer">
            📖 View Employee Handbook
          </a>
          <button
            className={`staff-form__button ${documentDetails.handbook ? 'primary' : ''}`}
            onClick={() => handleDocumentAcknowledge('handbook')}
            type="button"
            disabled={documentDetails.handbook}
          >
            {documentDetails.handbook ? '✓ Acknowledged' : 'I Have Read & Understand'}
          </button>
        </div>
      </div>

      <div className="staff-form__document-section">
        <h4>Health and Safety Policy</h4>
        <p style={{ fontSize: '0.9rem', color: '#666', margin: '0 0 1rem 0' }}>
          Important workplace safety guidelines and emergency procedures.
        </p>
        <div className="staff-form__document-section-actions">
          <a className="staff-form__button" href={healthAndSafety} target="_blank" rel="noopener noreferrer">
            🛡️ View Safety Policy
          </a>
          <button
            className={`staff-form__button ${documentDetails.safety ? 'primary' : ''}`}
            onClick={() => handleDocumentAcknowledge('safety')}
            type="button"
            disabled={documentDetails.safety}
          >
            {documentDetails.safety ? '✓ Acknowledged' : 'I Have Read & Understand'}
          </button>
        </div>
      </div>

      {documentDetails.handbook && documentDetails.safety && (
        <div className="staff-form__notice staff-form__notice--green">
          <p style={{ margin: 0, fontSize: '0.9rem', color: '#155724' }}>
            <strong>✓ All documents acknowledged!</strong> Thank you for reviewing the company policies.
          </p>
        </div>
      )}

      <div className="staff-form__notice staff-form__notice--yellow">
        <p style={{ margin: 0, fontSize: '0.9rem', color: '#856404' }}>
          <strong>Important:</strong> By acknowledging these documents, you confirm that you have read, understood, and
          agree to comply with all company policies and procedures.
        </p>
      </div>
    </div>
  );
};

export default StaffDocuments;
