import { useState, useContext } from 'react';
import appContext from 'contexts/appContext';

const StaffDocuments = ({ staffDetails, handleChange }) => {
  const [showDetails, setShowDetails] = useState(false);
  const { employeeHandbook, healthAndSafety } = useContext(appContext);

  return (
    <div className='mb-1'>
      <a className='button hollow' style={{ width: '100%' }} onClick={() => setShowDetails(!showDetails)}>Staff Documents</a>
      {showDetails && (
        <>
          <div>
            <label>Employee Handbook</label>
            <div>
              <a className='button tiny' href={employeeHandbook}>Dowload Document</a>  
              <a className='button tiny ml-1-2'>Upload Signed Document</a>  
            </div>
          </div>
          <div>
            <label>Health and Safety</label>
            <div>
              <a className='button tiny' href={healthAndSafety}>Dowload Document</a>  
              <a className='button tiny ml-1-2'>Upload Signed Document</a>  
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default StaffDocuments;