import { useState, useContext, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import axios from 'axios';
import { apiAdminUserPath } from 'routes';

// store
import appContext from 'contexts/appContext';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

const UserEditForm = ({ profileable, profileableType, setEditUser, deprecateUser, updateUser }) => {
  const [localUser, setLocalUser] = useState({})
  const [linkCopied, setLinkCopied] = useState(false);

  const { isSuperAdmin } = useContext(appContext);

  useEffect(async () => {
    try {
      const { data: responseUser } = await axios({
        method: 'GET',
        url: apiAdminUserPath(profileable, { profileable_type: profileableType }),
      });
      setLocalUser(responseUser)
    } catch (error) {
      const {
        data: { errors },
      } = error.response;
      errors.map((errorMessage) => toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 }));
      setEditUser(false);
    }
  }, [])

  const validateForm = () => {
    let isValid = true;
    if (localUser.password || localUser.password_confirmation) {
      if (localUser.password !== localUser.password_confirmation) {
        toast.error('Passwords and Confirmation do not match', { ...defaultToastOptions, autoClose: 10000 })
        isValid = false;
      }
    }
    return isValid;
  }
 
  const handleSave = async () => {
    const isValid = validateForm();
    if (!isValid) return;

    try {
      updateUser({
        profileable,
        user: localUser
      });
      setEditUser(false);
    } catch (err) {
      // do nothing
    }
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalUser((state) => ({ ...state, [field]: value }));
  };

  const copyConfirmationLink = () => {
    if (linkCopied) return;

    const input = document.createElement('input');
    input.type = 'text';
    input.value = localUser.confirmation_link;
    document.getElementsByClassName('invite-button')[0].after(input);
    input.focus();
    input.select();
    document.execCommand('copy');
    input.remove();
    setLinkCopied(true);
    setTimeout(() => {
      setLinkCopied(false);
    }, 5000);
  };

  const handleDeprecate = () => {
    deprecateUser({
      profileable
    });
  }

  if (!localUser.id) return null;

  return (
    <>
      <div className="overlay show" onClick={() => setEditUser(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="admin-sidebar-list" style={{ marginBottom: 0 }}>
            <div>
              <h3>
                User Settings: `{profileable.name}`
              </h3>
            </div>
            <fieldset style={{ padding: '0.5rem' }}>
              <div>
                <label>First Name</label>                
                <input
                  type="text"
                  name="firstname"
                  className='form-input'
                  value={localUser.firstname}
                  onChange={handleChange} />
              </div>
              <div>
                <label>Last Name</label>                
                <input
                  type="text"
                  name="lastname"
                  className='form-input'
                  value={localUser.lastname}
                  onChange={handleChange} />
              </div>
              <div>
                <label>Email</label>                
                <input
                  type="text"
                  name="email"
                  className='form-input'
                  value={localUser.email}
                  onChange={handleChange} />
              </div>
            </fieldset>
            <div className='mt-1'> 
              {localUser.unconfirmed_email && (
                <fieldset style={{ padding: '0.5rem' }}>
                  <legend>User Confirmation</legend>
                  {localUser.unconfirmed_email && (
                    <p>
                      Email change to <strong>{localUser.unconfirmed_email}</strong> needs confirmation.
                      (current active email: {localUser.email})
                    </p>
                  )}
                  <a className={`invite-button button small${linkCopied ? ' hollow' : ''}`} onClick={copyConfirmationLink}>
                    {linkCopied ? 'Link Copied!' : 'Copy Confirmation Link'}
                  </a>
                  <p>
                    <small>
                      Last Confirmation email sent @
                      <br />
                      {localUser.confirmation_sent_at}
                    </small>
                  </p>
                </fieldset>
              )}              
              <fieldset style={{ padding: '0.5rem' }}>
                <legend>Password Reset</legend>
                <div>
                  <label>Password</label>                
                  <input
                    type="text"
                    name="password"
                    className='form-input'
                    value={localUser.password}
                    onChange={handleChange} />
                </div>
                <div>
                  <label>Password Confirmation</label>                
                  <input
                    type="text"
                    name="password_confirmation"
                    className='form-input'
                    value={localUser.password_confirmation}
                    onChange={handleChange} />
                </div>
              </fieldset>

              {isSuperAdmin && (
                <p>
                  <a className='button alert-btn' onClick={handleDeprecate}>Deprecate User</a>
                  <small>(super admin only)</small>
                </p>
              )}
            </div>
          </div>
          <div className="between-flex mt-1">
            <a className="button" onClick={handleSave}>
              Update User
            </a>
            <a className="button gray-btn" onClick={() => setEditUser(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

UserEditForm.propTypes = {
  profileable: PropTypes.object.isRequired,
  profileableType: PropTypes.string.isRequired,
  setEditUser: PropTypes.func.isRequired,
  deprecateUser: PropTypes.func.isRequired,
  updateUser: PropTypes.func.isRequired,
};

export default UserEditForm;
