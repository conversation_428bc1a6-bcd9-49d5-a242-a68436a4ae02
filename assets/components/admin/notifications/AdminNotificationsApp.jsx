import { useEffect, useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import adminNotificationsStore from 'store/admin/adminNotificationsStore';
import appContext from 'contexts/appContext';

// components
import Notification from './Notification';
import NotificationSearch from './NotificationSearch';
import NotificationSkeleton from './NotificationSkeleton';
import NotificationFilters from './NotificationFilters';
import NoNotificationNotice from './NoNotificationNotice';
import { ToastContainer } from 'react-toastify';

const AdminNotificationsApp = () => {
  const { hasFavourites } = useContext(appContext);

  const { setFilters, fetchNotifications, notifications, loadingList, loadingMore, page, hasMore } =
    adminNotificationsStore(
      (state) => ({
        setFilters: state.setFilters,
        fetchNotifications: state.fetchNotifications,
        notifications: state.notifications,
        loadingList: state.loadingList,
        loadingMore: state.loadingMore,
        page: state.page,
        hasMore: state.hasMore,
      }),
      shallow
    );

  useEffect(() => {
    const existingFilters = JSON.parse(localStorage.getItem('NotificationFilters') || JSON.stringify({}));
    delete (existingFilters.query); // only required for a while. TO-DO - remove after a few weeks

    setFilters({
      hasFavourites,
      favouritesOnly: !!hasFavourites,
      query: '',
      ...existingFilters,
    });
  }, []);

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchNotifications({ page });
      }
    },
  });

  return (
    <>
      <NotificationSearch />
      <div className="sticky-container">
        <NotificationFilters />
      </div>
      <NoNotificationNotice isLoading={loadingList || loadingMore} notifications={notifications} />
      {notifications.map((notification, idx) => (
        <Notification key={`notification-${notification.id}`} notification={notification} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <NotificationSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default AdminNotificationsApp;
