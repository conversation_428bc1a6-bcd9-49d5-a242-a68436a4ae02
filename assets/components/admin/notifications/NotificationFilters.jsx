import { useContext } from 'react';
import shallow from 'zustand/shallow';

import adminNotificationsStore from 'store/admin/adminNotificationsStore';
import appContext from 'contexts/appContext';

const NotificationFilters = () => {
  const { favouritesOnly, setFavourites, showAll, setShowAll } = adminNotificationsStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
      showAll: state.showAll,
      setShowAll: state.setShowAll,
    }),
    shallow
  );
  const { hasFavourites, canShowAll } = useContext(appContext);

  if (!hasFavourites && !canShowAll) {
    return null;
  }

  return (
    <div className="order-list-options">
      <span style={{ paddingRight: '8px' }}>Filters: </span>
      {hasFavourites && (
        <label className="drop-text admin-order-option between-flex">
          <input
            type="checkbox"
            name="show-favourites"
            className="checkbox-content"
            checked={favouritesOnly}
            onChange={() => setFavourites(!favouritesOnly)}
          />
          <span className="checkbox-content-tick" style={{ backgroundColor: '#ffffff' }} />
          Favourite Customer Notifications
        </label>
      )}
      {canShowAll && (
        <label className="drop-text admin-order-option between-flex">
          <input
            type="checkbox"
            name="show-favourites"
            className="checkbox-content"
            checked={showAll}
            onChange={() => setShowAll(!showAll)}
          />
          <span className="checkbox-content-tick" style={{ backgroundColor: '#ffffff' }} />
          Show All Events
        </label>
      )}
    </div>
  );
};

export default NotificationFilters;
