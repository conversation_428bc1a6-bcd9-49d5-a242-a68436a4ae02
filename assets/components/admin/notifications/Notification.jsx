import { useState } from 'react';
import PropTypes from 'prop-types';

// store/utils
import shallow from 'zustand/shallow';
import adminNotificationsStore from 'store/admin/adminNotificationsStore';
import { getNotificationIcon, getTimeAgo, getSliderType } from 'utilities/adminHelpers';

// components
import ReactTooltip from 'react-tooltip';
import OrderDetails from './details/OrderDetails';
import CustomerQuoteDetails from './details/CustomerQuoteDetails';
import PendingOrderDetails from './details/PendingOrderDetails';
import HolidayOrdersDetails from './details/HolidayOrdersDetails';
import AutoConfirmedOrderDetails from './details/AutoConfirmedOrderDetails';
import NewCustomerDetails from './details/NewCustomerDetails';
import CustomerBudgetDetails from './details/CustomerBudgetDetails';

const Notification = ({ notification, withoutActions }) => {
  const [notificationInfo, setNotificationInfo] = useState(null);

  const { markNotification } = adminNotificationsStore(
    (state) => ({
      markNotification: state.markNotification,
    }),
    shallow
  );

  const markNotificationAsViewed = () => {
    if (notification.viewed) return;

    markNotification({
      type: 'viewed',
      notification,
    });
  };

  const handleViewed = (event) => {
    event.stopPropagation();
    markNotificationAsViewed();
  };

  const handleAssign = (event) => {
    event.stopPropagation();
    markNotification({
      type: 'assigned',
      notification,
    });
  };

  const handleClick = () => {
    markNotificationAsViewed();
    setNotificationInfo(getSliderType(notification));
  };

  return (
    <>
      <div
        className={`admin-notification ${notification.severity} ${notification.viewed ? 'read' : ''}`}
        onClick={handleClick}
      >
        <div className="between-flex">
          <div className="notification-header">
            <span className={`circle-icon ${getNotificationIcon(notification)}`} />
          </div>
          <div>
            <span dangerouslySetInnerHTML={{ __html: notification.message }} />
            <p style={{ marginBottom: 0, color: 'grey' }}>{getTimeAgo(notification.event_at)}</p>
          </div>
        </div>

        {(notification.is_assignable || !!notification.assigned_to || !notification.viewed) && (
          <div className={`between-flex${notification.is_assignable ? ' notification-header' : ''}`}>
            {notification.is_assignable && !withoutActions && (
              <>
                <span
                  data-tip
                  data-for={`notification-${notification.id}-assigned-to`}
                  className={`user-assign${notification.assigned_to ? ' assigned' : ''}`}
                  onClick={handleAssign}
                />
                <ReactTooltip id={`notification-${notification.id}-assigned-to`} place="left" effect="solid">
                  Click to {notification.assigned_to ? 'Un-Assign from me' : 'Assign to me'}
                </ReactTooltip>
              </>
            )}
            {!notification.is_assignable && !!notification.assigned_to && !withoutActions && (
              <>
                <span data-tip data-for={`notification-${notification.id}-assigned-to`} className="circle-icon">
                  {notification.assigned_to.initials}
                </span>
                <ReactTooltip id={`notification-${notification.id}-assigned-to`} place="left" effect="solid">
                  Assigned to: {notification.assigned_to.name}
                </ReactTooltip>
              </>
            )}
            {!notification.viewed && !withoutActions && (
              <label className="drop-text" style={{ marginLeft: '12px' }}>
                <input
                  type="checkbox"
                  className="checkbox-content"
                  checked={notification.viewed}
                  onChange={handleViewed}
                  onClick={(e) => e.stopPropagation()}
                />
                <span
                  className="checkbox-content-tick"
                  onClick={(e) => e.stopPropagation()}
                  style={{ minWidth: '20px', height: '20px' }}
                />
              </label>
            )}
          </div>
        )}
      </div>
      {!!notificationInfo && (
        <>
          <div className="overlay show" onClick={() => setNotificationInfo(null)} />
          <div className="sidebar-overlay open">
            <div className="admin-order-slider">
              {notificationInfo === 'Order' && (
                <OrderDetails notification={notification} setNotificationInfo={setNotificationInfo} />
              )}
              {notificationInfo === 'OrdersAutoConfirmed' && (
                <AutoConfirmedOrderDetails notification={notification} setNotificationInfo={setNotificationInfo} />
              )}
              {notificationInfo === 'PendingOrders' && (
                <PendingOrderDetails notification={notification} setNotificationInfo={setNotificationInfo} />
              )}
              {notificationInfo === 'CustomerQuote' && (
                <CustomerQuoteDetails notification={notification} setNotificationInfo={setNotificationInfo} />
              )}
              {notificationInfo === 'HolidayOrders' && (
                <HolidayOrdersDetails notification={notification} setNotificationInfo={setNotificationInfo} />
              )}
              {notificationInfo === 'CustomerRegistration' && (
                <NewCustomerDetails notification={notification} setNotificationInfo={setNotificationInfo} />
              )}
              {notificationInfo === 'CustomerBudget' && (
                <CustomerBudgetDetails notification={notification} setNotificationInfo={setNotificationInfo} />
              )}
            </div>
          </div>
        </>
      )}
    </>
  );
};

Notification.propTypes = {
  notification: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Notification;
