import { useState } from 'react';

// component
import { Modal } from 'react-responsive-modal';

// store
import shallow from 'zustand/shallow';
import adminNotificationsStore from 'store/admin/adminNotificationsStore';

// toastr
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

// axios call
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { markAllNotificationsAsViewedAPIAdminNotificationsPath } from 'routes';
import Notification from './Notification';

const MarkAllAsViewed = () => {
  const [isMarking, setIsMarking] = useState(false);
  const [pendingNotifications, setPendingNotifications] = useState([]);
  const [viewedNotificationsCount, setViewedNotificationsCount] = useState(0);
  const [sliderOpen, setSliderOpen] = useState(false);

  const { hasFavourites, favouritesOnly, query, severity, setMarkAllAsViewed } = adminNotificationsStore(
    (state) => ({
      hasFavourites: state.hasFavourites,
      favouritesOnly: state.favouritesOnly,
      query: state.query,
      severity: state.severity,
      setMarkAllAsViewed: state.setMarkAllAsViewed,
    }),
    shallow
  );

  const markAsViewed = async (includeHighSeverity) => {
    if (isMarking) return;

    try {
      setViewedNotificationsCount(0);
      setIsMarking(true);
      if (!includeHighSeverity) {
        setSliderOpen(false);
        setPendingNotifications([]);
      }
      await axios({
        method: 'POST',
        url: markAllNotificationsAsViewedAPIAdminNotificationsPath(),
        data: {
          ...(!!query && { query }),
          ...(!!severity && { severity }),
          ...(!!hasFavourites && !!favouritesOnly && { favourites_only: true }),
          ...(!!includeHighSeverity && { include_high_severity: true }),
        },
        headers: csrfHeaders(),
      });
      setMarkAllAsViewed();
      toast.success('All notifications marked as viewed', { ...defaultToastOptions, autoClose: 5000 });
      if (sliderOpen) setSliderOpen(false);
      setIsMarking(false);
    } catch (error) {
      setPendingNotifications(error.response.data.pending_notifications);
      setViewedNotificationsCount(error.response.data.viewed_count);
      setSliderOpen(true);
      setIsMarking(false);
    }
  };

  return (
    <>
      <a className={`button float-right${isMarking ? ' disabled' : ''}`} onClick={() => markAsViewed(false)}>
        {isMarking ? 'Marking...' : 'Mark all as Viewed'}
      </a>
      <Modal
        classNames={{ modal: 'reveal modal modal-drawer budget-modal-sliderx' }}
        open={sliderOpen}
        showCloseIcon={false}
        onClose={() => setSliderOpen(false)}
      >
        <div className="form-header">
          <div className="pt-1 px-1 between-flex">
            <h3 className="p-0">Notifications</h3>
            <span className="close-modal-drawer" onClick={() => setSliderOpen(false)}>
              X
            </span>
          </div>
        </div>
        <p>
          {!!viewedNotificationsCount && (
            <>
              Viewed {viewedNotificationsCount} low severity notifications.
              <br />
            </>
          )}
          {!!pendingNotifications.length && (
            <strong className="is-invalid-label">
              You still have {pendingNotifications.length} high severity notifications left.
            </strong>
          )}
        </p>
        {pendingNotifications.map((notification, idx) => (
          <Notification
            key={`notification-${notification.id}`}
            notification={notification}
            index={idx}
            withoutActions
          />
        ))}
        <p className="text-center">
          <a className={`button${isMarking ? ' disabled' : ''}`} onClick={() => markAsViewed(true)}>
            {isMarking ? 'Marking...' : 'Mark all High Severity as Viewed'}
          </a>
        </p>
      </Modal>
    </>
  );
};

export default MarkAllAsViewed;
