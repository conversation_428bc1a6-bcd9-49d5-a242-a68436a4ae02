import PropTypes from 'prop-types';

const OrderSuppliersDetails = ({ suppliers }) => (
  <div>
    <h6 className="order-show-detail-title supplier">Suppliers</h6>
    {!suppliers?.length && (
      <div className="supplier-list">
        <div className="circle-icon loading" />
        <div>
          <div className="order-list-item loading" style={{ height: '1rem', width: '290px' }} />{' '}
          <div className="order-list-item loading" style={{ height: '1rem', width: '290px' }} />
        </div>
      </div>
    )}
    {!!suppliers?.length &&
      suppliers.map((supplier) => (
        <div key={`order-supplier-${supplier.id}`}>
          <div className="delivery-details-suppliers--customer" style={{ paddingBlock: '0.125rem' }}>
            <div className="circle-icon">{supplier.image ? <img src={supplier.image} /> : <span>AB</span>}</div>
            <p>{supplier.name}</p>
          </div>
          {!!supplier.phone && (
            <p>
              Phone: <a href={`tel:${supplier.phone}`}>{supplier.phone}</a>
            </p>
          )}
          {!!supplier.mobile && (
            <p>
              Mobile: <a href={`tel:${supplier.mobile}`}>{supplier.mobile}</a>
            </p>
          )}
          {!!supplier.email && (
            <p>
              Email: <a href={`mailto:${supplier.email}`}>{supplier.email}</a>
            </p>
          )}
        </div>
      ))}
  </div>
);

OrderSuppliersDetails.propTypes = {
  suppliers: PropTypes.array.isRequred,
};

export default OrderSuppliersDetails;
