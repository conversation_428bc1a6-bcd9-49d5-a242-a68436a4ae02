import { useState, useEffect } from 'react';

// action
import axios from 'axios';
import { apiAdminNotificationPath } from 'routes';

// components
import Spinner from 'components/checkout/Spinner';

const CustomerBudgetDetails = ({ notification, setNotificationInfo }) => {
  const { 
    po_number: poNumber,
    number_of_orders: numberOfOrders,
    budget,
    spend,
    remaining: remainingSpend,
    spend_percentage: spendPercentage
  } = notification.info;

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="between-flex">
        <h3 className="delivery-details-title">
          {poNumber && `Customer Budget for ${poNumber}`}
          {!poNumber && 'Monthly Customer Budget'}
        </h3>
      </div>
      <div className="order-show-details-section">
        <p>
          <strong>Number of Orders: </strong>
          <span>{numberOfOrders}</span>
        </p>
        <p>
          <strong>Budget: </strong>
          <span>${budget}</span>
        </p>
        <p>
          <strong>Spend So Far: </strong>
          <span>${spend}</span>
        </p>
        {remainingSpend >= 0.0 && (
          <p>
            <strong>Remaining Spend: </strong>
            <span>${remainingSpend}</span>
          </p>
        )}
        {remainingSpend < 0.0 && (
          <p>
            <strong>Overshot budget by: </strong>
            <span>${remainingSpend}</span>
          </p>
        )}
        <p>
          <strong>Percentage of budget reached: </strong>
          <span>{spendPercentage}%</span>
        </p>
      </div>
      <div className="order-show-details-section">
        <a className="button mt-1-2 gray-btn" style={{ width: '100%' }} onClick={() => setNotificationInfo(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default CustomerBudgetDetails;
