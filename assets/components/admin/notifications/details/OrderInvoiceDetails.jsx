import PropTypes from 'prop-types';

const OrderInvoiceDetails = ({ order }) => {
  const { invoice, billing_frequency, invoice_individually, billing_date, order_card } = order;
  return (
    <>
      {!!invoice && (
        <p>
          <strong>Invoiced: </strong>
          <a href={invoice.document_url} target="_blank" rel="noreferrer">
            {`#${invoice.number} `}
          </a>
          (on {invoice.date})
          {billing_frequency != 'instantly' && invoice_individually && (
            <small> (invoiced individually outside {billing_frequency} billing)</small>
          )}
          {billing_frequency != 'instantly' && !order.invoice_individually && (
            <small> (invoiced as part of {billing_frequency} billing)</small>
          )}
        </p>
      )}
      {!invoice && (invoice_individually || billing_frequency == 'instantly') && (
        <p>This order will be invoiced individually on delivery.</p>
      )}
      {!invoice && !invoice_individually && billing_frequency != 'instantly' && (
        <p>
          Will be invoiced as part of {billing_frequency} billing on {billing_date}.
        </p>
      )}
      {order_card && (
        <p>
          <strong>{invoice ? 'Paid with' : 'Attached to'}: </strong>
          Card ending in ..{order_card.last4}.
        </p>
      )}
    </>
  );
};

OrderInvoiceDetails.propTypes = {
  order: PropTypes.object.isRequired,
};

export default OrderInvoiceDetails;
