import { useState, useEffect } from 'react';

// action
import axios from 'axios';
import { apiAdminNotificationPath } from 'routes';

// components
import Spinner from 'components/checkout/Spinner';
import SimpleOrderDetails from './SimpleOrderDetails';

const AutoConfirmedOrderDetails = ({ notification, setNotificationInfo }) => {
  const [orders, setOrders] = useState([]);
  const [loadingInfo, setLoadingInfo] = useState(true);

  const fetchNotificationInfo = async ({ notification }) => {
    try {
      const { data: responseOrders } = await axios({
        method: 'GET',
        url: apiAdminNotificationPath(notification),
      });
      setOrders(responseOrders);
    } catch (e) {
      // do nothing
    }
    setLoadingInfo(false);
  };

  useEffect(() => {
    fetchNotificationInfo({ notification });
  }, [notification.id]);

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="between-flex">
        <h3 className="delivery-details-title">Auto Confirmed Orders (x{notification.auto_confirmed_orders_count})</h3>
      </div>
      {loadingInfo && <Spinner wheel />}
      {!loadingInfo && !!orders.length && orders.map((order) => <SimpleOrderDetails order={order} highlight={'suppliers'} />)}
      <div className="order-show-details-section">
        <a className="button mt-1-2 gray-btn" style={{ width: '100%' }} onClick={() => setNotificationInfo(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default AutoConfirmedOrderDetails;
