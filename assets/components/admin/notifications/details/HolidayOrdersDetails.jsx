import { useState, useEffect } from 'react';

// action
import axios from 'axios';
import { apiAdminNotificationPath } from 'routes';

// components
import Spinner from 'components/checkout/Spinner';
import SimpleOrderDetails from './SimpleOrderDetails';

const HolidayOrdersDetails = ({ notification, setNotificationInfo }) => {
  const [skippedOrders, setSkippedOrders] = useState([]);
  const [pushedOrders, setPushedOrders] = useState([]);
  const [loadingInfo, setLoadingInfo] = useState(true);

  const fetchNotificationInfo = async ({ notification }) => {
    try {
      const { data } = await axios({
        method: 'GET',
        url: apiAdminNotificationPath(notification),
      });
      const { skipped_orders: skippedOrders, pushed_orders: pushedOrders } = data;
      setSkippedOrders(skippedOrders || []);
      setPushedOrders(pushedOrders || []);
    } catch (e) {
      // do nothing
    }
    setLoadingInfo(false);
  };

  useEffect(() => {
    fetchNotificationInfo({ notification });
  }, [notification.id]);

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="between-flex">
        <h3 className="delivery-details-title">
          Public Holiday Affected Orders (x{notification.affected_orders_count})
        </h3>
      </div>
      <p>
        <strong>{notification.name}</strong> on {notification.date}
      </p>
      {loadingInfo && <Spinner wheel />}
      {!loadingInfo && !!skippedOrders.length && (
        <div className="order-show-details-section">
          <p className="text-center">
            <strong>Skipped Orders (x{skippedOrders.length})</strong>
          </p>
          {skippedOrders.map((order) => (
            <SimpleOrderDetails order={order} />
          ))}
        </div>
      )}
      {!loadingInfo && !!pushedOrders.length && (
        <div className="order-show-details-section">
          <p className="text-center">
            <strong>Pushed Orders (x{pushedOrders.length})</strong>
          </p>
          {pushedOrders.map((order) => (
            <SimpleOrderDetails order={order} />
          ))}
        </div>
      )}
      <div className="order-show-details-section">
        <a className="button mt-1-2 gray-btn" style={{ width: '100%' }} onClick={() => setNotificationInfo(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default HolidayOrdersDetails;
