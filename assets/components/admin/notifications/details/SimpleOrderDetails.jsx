import { useState } from 'react';
import OrderSuppliersDetails from './OrderSuppliersDetails';

const Detail = ({ label, value, className }) => (
  <p>
    <strong>{label}: </strong>
    <span className={className}>{value}</span>
  </p>
);

const SimpleOrderDetails = ({ order, highlight = 'customers' }) => {
  const [open, setOpen] = useState(false);
  const capitalizedStatus = order?.status?.charAt(0)?.toUpperCase() + order?.status?.slice(1);

  return (
    <div className="order-show-details-section">
      <div onClick={() => setOpen((state) => !state)} className={`simple-order${open ? ' open' : ''}`}>
        {highlight == 'customers' && `#${order.id} - ${order.customer_name}`}
        {highlight == 'suppliers' && order?.suppliers?.length && order.suppliers.length > 1 && `#${order.id} - Multiple Suppliers`}
        {highlight == 'suppliers' && order?.suppliers?.length && order.suppliers.length === 1 && `#${order.id} - ${order.suppliers[0].name}`}
      </div>
      {open && (
        <div className="simple-order-details">
          <h6 className="order-show-detail-title order">
            {order.order_variant === 'event_order' ? 'Custom Order' : 'Order'}
          </h6>
          <Detail label="Customer" value={order.customer_name} />
          <Detail label="Name" value={order.name} />
          <Detail label="Order" value={order.id} />
          <Detail label="Status" value={capitalizedStatus} className={`status-icon ${order.status}`} />
          <Detail label="Delivery" value={order.delivery_at} />
          <Detail label="Address" value={order.address} />
          <OrderSuppliersDetails suppliers={order?.suppliers} />
          <div className="simple-order-details__buttons">
            <a className="button tiny" href={order.order_view_path} target="blank">
              View Order
            </a>
            <a className="button tiny ml-1-4" href={order.order_edit_path} target="blank">
              Edit Order
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleOrderDetails;
