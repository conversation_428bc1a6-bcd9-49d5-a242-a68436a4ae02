import { useState, useEffect } from 'react';

// action
import axios from 'axios';
import Spinner from 'components/checkout/Spinner';
import { apiAdminNotificationPath } from 'routes';
import { customerFields, nonCustomerFields } from 'utilities/adminHelpers';

const CustomerQuoteDetails = ({ notification, setNotificationInfo }) => {
  const [customerQuote, setCustomerQuote] = useState(null);
  const [loadingInfo, setLoadingInfo] = useState(true);

  const fetchNotificationInfo = async ({ notification }) => {
    try {
      const { data: responseCustomerQuote } = await axios({
        method: 'GET',
        url: apiAdminNotificationPath(notification),
      });
      setCustomerQuote(responseCustomerQuote);
    } catch (e) {
      // do nothing
    }
    setLoadingInfo(false);
  };

  useEffect(() => {
    fetchNotificationInfo({ notification });
  }, [notification.id]);

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="between-flex">
        <h3 className="delivery-details-title">{customerQuote?.kind} Quote</h3>
      </div>
      {loadingInfo && <Spinner wheel />}
      {!loadingInfo && (
        <>
          <div className="order-show-details-section">
            <h6 className="order-show-detail-title order">Customer</h6>
            {customerFields.map(
              (field) =>
                !!customerQuote[field] && (
                  <p>
                    <strong>{field}: </strong>
                    {field === 'Email' && <a href={`mailto:${customerQuote[field]}`}>{customerQuote[field]}</a>}
                    {field === 'Phone' && <a href={`tel:${customerQuote[field]}`}>{customerQuote[field]}</a>}
                    {!['Email', 'Phone'].includes(field) && <span>{customerQuote[field]}</span>}
                  </p>
                )
            )}
          </div>
          {nonCustomerFields.map(
            (field) =>
              !!customerQuote[field] && (
                <p>
                  <strong>{field}: </strong>
                  {field === 'Date' ? (
                    <span>{customerQuote[field].split(' ')[2]}</span>
                  ) : (
                      <span>{customerQuote[field]}</span>
                  )}
                </p>
              )
          )}
        </>
      )}

      <div className="order-show-details-section">
        <a className="button mt-1-2 gray-btn" style={{ width: '100%' }} onClick={() => setNotificationInfo(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default CustomerQuoteDetails;
