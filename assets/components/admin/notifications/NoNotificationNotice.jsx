import { useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import adminNotificationsStore from 'store/admin/adminNotificationsStore';
import appContext from 'contexts/appContext';

const NoNotificationNotice = ({ isLoading, notifications }) => {
  const { hasFavourites } = useContext(appContext);

  if (isLoading || notifications.length) return null;

  const { favouritesOnly, setFavourites } = adminNotificationsStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
    }),
    shallow
  );

  return (
    <div className="text-center mt-1">
      <h3>No Notifications Found</h3>
      {hasFavourites && favouritesOnly && (
        <p>
          Try toggling to <a onClick={() => setFavourites(false)}>show notifications of all customers</a>.
        </p>
      )}
    </div>
  );
};

export default NoNotificationNotice;
