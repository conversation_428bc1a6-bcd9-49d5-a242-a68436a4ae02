import { useState, useRef, useEffect } from 'react';

//components
import MarkAllAsViewed from './MarkAllAsViewed';
import { notificationSeverityOptions } from 'utilities/adminHelpers';

// store
import shallow from 'zustand/shallow';
import adminNotificationsStore from 'store/admin/adminNotificationsStore';

const NotificationSearch = () => {
  const isMounted = useRef(false);

  const { query, setQuery, setSeverity } = adminNotificationsStore(
    (state) => ({
      query: state.query,
      setQuery: state.setQuery,
      setSeverity: state.setSeverity,
    }),
    shallow
  );

  const [localQuery, setLocalQuery] = useState(query);

  useEffect(() => {
    if (localQuery && localQuery.length < 3) return;
    if (isMounted.current && query !== localQuery) {
      const delayDebounceFn = setTimeout(() => {
        setQuery(localQuery);
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [localQuery]);

  useEffect(() => setLocalQuery(query), [query]);

  return (
    <div className="between-flex notification-search">
      <input
        className="search-input form-input"
        placeholder="Search by customer name or company name"
        style={{ maxWidth: '500px' }}
        type="search"
        value={localQuery}
        onChange={(event) => setLocalQuery(event.target.value)}
      />
      <select className="form-input" onChange={(event) => setSeverity(event.target.value)} style={{ maxWidth: '150px' }}>
        <option value={''}>All severities</option>
        {Object.keys(notificationSeverityOptions).map((option) => <option key={`notification-severity-${option}`} value={option}>{notificationSeverityOptions[option]}</option>)}
      </select>
      <MarkAllAsViewed />
    </div>
  );
};

export default NotificationSearch;
