import { useState } from 'react';

// components
import CouponForm from './CouponForm';

const initialCoupon = {
  code: '',
};

const NewCoupon = () => {
  const [isNew, setIsNew] = useState(false);

  if (isNew) {
    return <CouponForm coupon={initialCoupon} showForm={setIsNew} />;
  }

  return (
    <div className="list-item">
      <a onClick={() => setIsNew(true)}>Add new Coupon</a>
    </div>
  );
};

export default NewCoupon;
