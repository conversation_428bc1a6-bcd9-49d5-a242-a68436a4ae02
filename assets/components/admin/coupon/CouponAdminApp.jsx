import { useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import couponAdminStore from 'store/admin/couponAdminStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewCoupon from './NewCoupon';
import Coupon from './Coupon';
import CouponSearch from './CouponSearch';
import CouponSkeleton from './CouponSkeleton';
import NoCouponNotice from './NoCouponNotice';

const CouponAdminApp = () => {
  const { hasFavourites } = useContext(appContext);

  const { fetchCoupons, coupons, loadingList, loadingMore, page, hasMore, query } = couponAdminStore(
    (state) => ({
      fetchCoupons: state.fetchCoupons,
      coupons: state.coupons,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      query: state.query,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchCoupons({
          page,
          hasFavourites,
        });
      }
    },
  });

  return (
    <>
      <CouponSearch />
      <div style={{ position: 'sticky', top: 0, zIndex: 999 }}>
        <div className="item-list__headings sticky">
          <span className="list-flex-1">Redeemed</span>
          <span className="list-flex-2">Code</span>
          <span className="list-flex-2">Description</span>
          <span className="list-flex-2 text-center">Discount</span>
          <span className="list-flex-2 text-center">Valid From</span>
          <span className="list-flex-2 text-center">Valid Until</span>
          <span className="list-flex-1 text-center">Expired</span>
          <span className="list-flex-1 text-center">Can Redeem</span>
          <span className="list-flex-1" />
        </div>
      </div>
      {!loadingList && !loadingMore && !query && <NewCoupon />}
      <NoCouponNotice isLoading={loadingList || loadingMore} coupons={coupons} />
      {coupons.map((coupon, idx) => (
        <Coupon key={`coupon-${coupon.id}`} coupon={coupon} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <CouponSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default CouponAdminApp;
