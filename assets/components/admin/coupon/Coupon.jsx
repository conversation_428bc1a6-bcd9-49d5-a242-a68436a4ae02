import { useState } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import CouponForm from './CouponForm';

const Coupon = ({ coupon, index }) => {
  const circleColor = getCircleIconColor(index);
  const [isEdit, setIsEdit] = useState(false);

  if (isEdit) {
    return <CouponForm coupon={coupon} showForm={setIsEdit} />;
  }

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
        {!!coupon.order_ids.length && (
          <>
            <span
              className="icon-info-circle ml-1-4"
              data-tip
              data-for={`coupon-${coupon.id}-orders-info`}
              className="circle-icon"
              style={{ background: circleColor }}
            >
              {coupon.redeemed}
            </span>
            <ReactTooltip id={`coupon-${coupon.id}-orders-info`} place="right" effect="solid">
              <small>{coupon.order_ids.map((id) => `#${id}`).join(', ')}</small>
            </ReactTooltip>
          </>
        )}
        {!coupon.order_ids.length && (
          <span className="circle-icon" style={{ background: circleColor }}>
            {coupon.redeemed}
          </span>
        )}
      </div>
      <div className="list-flex-2">{coupon.code}</div>
      <div className="list-flex-2">{coupon.description || '-'}</div>
      <div className="list-flex-2 text-center">
        {coupon.type === 'amount' ? `\$${coupon.amount} OFF` : `${coupon.amount}% OFF`}
      </div>
      <div className="list-flex-2 text-center">{coupon.formatted_valid_from}</div>
      <div className="list-flex-2 text-center">{coupon.formatted_valid_until || 'Forever'}</div>
      <div className="list-flex-1 text-center">
        <div className={`text-center admin-flags ${coupon.expired ? 'active' : 'inactive'}`} />
      </div>
      <div className="list-flex-1 text-center">
        <div className={`text-center admin-flags ${coupon.can_redeem ? 'active' : 'inactive'}`} />
      </div>
      <div className="list-flex-1 text-right">
        <a className="icon-edit mr-1" onClick={() => setIsEdit(!isEdit)} />
      </div>
    </div>
  );
};

Coupon.propTypes = {
  coupon: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Coupon;
