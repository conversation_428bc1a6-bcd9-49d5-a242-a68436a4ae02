import { useState } from 'react';
import DatePicker from 'react-datepicker';
import ReactTooltip from 'react-tooltip';
import moment from 'moment';

// store
import shallow from 'zustand/shallow';
import couponAdminStore from 'store/admin/couponAdminStore';

import { couponTypes } from 'utilities/adminHelpers';

const CouponForm = ({ coupon, showForm }) => {
  const [localCoupon, setLocalCoupon] = useState(coupon);

  const { createCoupon, updateCoupon } = couponAdminStore(
    (state) => ({
      createCoupon: state.createCoupon,
      updateCoupon: state.updateCoupon,
    }),
    shallow
  );

  const handleSave = async () => {
    try {
      if (localCoupon.id) {
        await updateCoupon({
          coupon: localCoupon,
        });
      } else {
        await createCoupon({
          coupon: localCoupon,
        });
      }
      showForm(false);
    } catch (error) {
      // do nothing
    }
  };

  const handleDateChange = (name, datetime) => {
    const selectedDate = moment(datetime, 'dddd, MMM DD, YYYY')?.format('YYYY/MM/DD');
    setLocalCoupon((state) => ({ ...state, [name]: selectedDate }));
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalCoupon((state) => ({ ...state, [field]: value }));
  };

  return (
    <div className="list-item">
      <div className="list-flex-3 pr-1">
        <label>Code</label>
        <input type="text" name="code" className="form-input" value={localCoupon.code || ''} onChange={handleChange} />
      </div>
      <div className="list-flex-3 pr-1">
        <label>Description</label>
        <input
          type="text"
          name="description"
          className="form-input"
          value={localCoupon.description || ''}
          onChange={handleChange}
        />
      </div>
      <div className="list-flex-3 pr-1">
        <label>
          Redemption Limit
          <span className="icon-info-circle ml-1-4" data-tip data-for={`coupon-${localCoupon.id}-redemption-info`} />
          <ReactTooltip id={`coupon-${localCoupon.id}-redemption-info`} place="right" effect="solid">
            <small>Set how many times this coupon can be used. Zero means no limit.</small>
          </ReactTooltip>
        </label>
        <input
          type="text"
          name="redemption_limit"
          className="form-input"
          value={localCoupon.redemption_limit || ''}
          onChange={handleChange}
        />
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Type
          <span className="icon-info-circle ml-1-4" data-tip data-for={`coupon-${localCoupon.id}-type-info`} />
          <ReactTooltip id={`coupon-${localCoupon.id}-type-info`} place="right" effect="solid">
            <small>Select how this coupon will apply the discount.</small>
          </ReactTooltip>
        </label>
        <select name='type' onChange={handleChange} className='form-input'>
          {Object.keys(couponTypes).map((type) => (
            <option key={`coupon-${localCoupon.id}-type-${type}`} value={type} selected={type === localCoupon.type}>
              {couponTypes[type]}
            </option>
          ))}
        </select>
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Amount
          <span className="icon-info-circle ml-1-4" data-tip data-for={`coupon-${localCoupon.id}-amount-info`} />
          <ReactTooltip id={`coupon-${localCoupon.id}-amount-info`} place="right" effect="solid">
            <small>This is the actual discount amount. 0-100 for percentage, zero or positive number for amount.</small>
          </ReactTooltip>
        </label>
        <input
          type="text"
          name="amount"
          className="form-input"
          value={localCoupon.amount || ''}
          onChange={handleChange}
        />
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Valid From
          <span className="icon-info-circle ml-1-4" data-tip data-for={`coupon-${localCoupon.id}-valid-from-info`} />
          <ReactTooltip id={`coupon-${localCoupon.id}-valid-from-info`} place="right" effect="solid">
            <small>When this coupon will be available.</small>
          </ReactTooltip>
        </label>
        <DatePicker
          selected={localCoupon.valid_from ? new Date(localCoupon.valid_from) : ''}
          onChange={(datetime) => handleDateChange('valid_from', datetime)}
          name="valid_from"
          dateFormat="dd-MM-yyyy"
          className="form-input"
          autoComplete="off"
        />
      </div>
      <div className="list-flex-2 pr-1">
        <label>
          Valid Until
          <span className="icon-info-circle ml-1-4" data-tip data-for={`coupon-${localCoupon.id}-valid-until-info`} />
          <ReactTooltip id={`coupon-${localCoupon.id}-valid-until-info`} place="right" effect="solid">
            <small>When will this coupon expire. By default, it does not expire.</small>
          </ReactTooltip>
        </label>
        <DatePicker
          selected={localCoupon.valid_until ? new Date(localCoupon.valid_until) : ''}
          onChange={(datetime) => handleDateChange('valid_until', datetime)}
          name="valid_until"
          dateFormat="dd-MM-yyyy"
          className="form-input"
          autoComplete="off"
        />
      </div>
      <div className="list-flex-2">
        <a className="button tiny mr-1-2" onClick={handleSave}>
          Save
        </a>
        <a className="button tiny gray-btn" onClick={() => showForm(false)}>
          Cancel
        </a>
      </div>
    </div>
  );
};

export default CouponForm;
