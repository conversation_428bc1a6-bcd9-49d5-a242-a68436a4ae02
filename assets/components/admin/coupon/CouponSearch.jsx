import { useRef, useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import couponAdminStore from 'store/admin/couponAdminStore';
import appContext from 'contexts/appContext';

const CouponSearch = () => {
  const isMounted = useRef(false);

  const { hasFavourites } = useContext(appContext);

  const { fetchCoupons, query, setQuery } = couponAdminStore(
    (state) => ({
      fetchCoupons: state.fetchCoupons,
      query: state.query,
      setQuery: state.setQuery,
    }),
    shallow
  );

  useEffect(() => {
    if (query && query.length < 3) return;
    if (isMounted.current) {
      const delayDebounceFn = setTimeout(() => {
        fetchCoupons({
          page: 1,
          hasFavourites,
        });
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [query]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by coupon code, description, order ID, customer or company name"
        style={{ maxWidth: '500px' }}
        type="search"
        value={query}
        onChange={(event) => setQuery(event.target.value)}
      />
    </div>
  );
};

export default CouponSearch;
