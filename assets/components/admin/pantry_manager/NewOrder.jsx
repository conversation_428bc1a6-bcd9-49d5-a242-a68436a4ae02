import { useContext } from 'react';
import axios from 'axios';
import debounce from 'debounce-promise';
import AsyncSelect from 'react-select/async';

import { apiAdminCustomersPath } from 'routes';
import appContext from 'contexts/appContext';

const NewOrder = () => {
  const { supplierPage } = useContext(appContext);

  const promiseOptionsCustomers = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: { query },
    });

    return responseCustomers.map((customer) => ({
      value: customer.id,
      label: `${customer.name} [${customer.email}]`,
      sign_in_path: customer.sign_in_path,
    }));
  }, 1000);

  const placeOrderFor = (selectedCustomer) => {
    let pageLink = selectedCustomer.sign_in_path;
    pageLink += `?redirect_path=${supplierPage}`;
    window.open(pageLink, '_blank');
  }

  return (
    <div className='mr-5' style={{ width: '400px' }}>
      <AsyncSelect
        className="form-input reports-search"
        cacheOptions
        defaultOptions
        isClearable
        placeholder="New Staffing Order (search for customer)"
        loadOptions={promiseOptionsCustomers}
        onChange={(selected) => placeOrderFor(selected)}
        value={null}
        styles={{
          control: (baseStyles) => ({
            ...baseStyles,
            zIndex: 5,
          }),
        }}
      />
    </div>
  )
}
// sign_in_as_customer_path(customer_user, redirect_path: next_app_supplier_search_url(category_group: 'office-catering', state: '_state_', suburb: '_suburb_'))

export default NewOrder;