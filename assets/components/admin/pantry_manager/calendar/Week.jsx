import PropTypes from 'prop-types';
import moment from 'moment';

import Day from './Day';

const Week = ({ weekStartDate, orders, holidays, setActiveOrder, activePantryManager }) => {
  const weekEndDate = moment(weekStartDate).add(4, 'days');

  const weekDays = Array(5)
    .fill(0)
    .map((_, index) => moment(weekStartDate).add(index, 'days').format('YYYY-MM-DD'));

  const week = moment(weekStartDate).week();
  const isStriped = week % 2 === 0;

  return (
    <div className={`calendar-week ${isStriped ? 'striped' : ''}`}>
      {weekDays.map((day, index) => (
        <Day
          key={`calendar-week-${week}-day-${index}`}
          day={day}
          index={index}
          orders={orders}
          holidays={holidays}
          setActiveOrder={setActiveOrder}
          activePantryManager={activePantryManager}
        />
      ))}
    </div>
  );
};

Week.proptypes = {
  weekStartDate: PropTypes.string.isRequired,
  orders: PropTypes.array.isRequired,
  holidays: PropTypes.array.isRequired,
  setActiveOrder: PropTypes.func.isRequired,
  activePantryManager: PropTypes.object,
};

export default Week;
