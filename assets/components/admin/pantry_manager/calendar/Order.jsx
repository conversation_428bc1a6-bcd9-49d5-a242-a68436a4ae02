import PropTypes from 'prop-types';

const Order = ({ order, setActiveOrder, activePantryManager }) => {
  const timeString = order.delivery_time;
  const unAssingedClass = order.pantry_manager.is_unassigned ? ' un-assigned' : ''
  const filteredClass = activePantryManager && order.pantry_manager.name !== activePantryManager.value ? ' filtered-out' : '';

  return (
    <div className={`calendar-event link${unAssingedClass}${filteredClass}`}>
      <a
        className="calendar-view-slider"
        data-modal-view="true"
        title={timeString}
        onClick={() => setActiveOrder(order)}
      />
      <img src={order.pantry_manager.img} alt="pantry manager" className="event-image" />
      <span className={`event-type ${order.status}`} />
      <div className="event-time">
        {order.pantry_manager.name}
        <br/>
        <strong>{order.customer_name}</strong>
      </div>
      <span className={`event-info${order.is_allocated_for_week ? ' week-long' : ''}`}>
        {order.hours}hrs
      </span>
    </div>
  );
};

Order.propTypes = {
  order: PropTypes.object.isRequired,
  setActiveOrder: PropTypes.func.isRequired,
  activePantryManager: PropTypes.object,
}

export default Order;