import React, { useState } from 'react';
import PropTypes from 'prop-types';
import DatePicker from '@hassanmojab/react-modern-calendar-datepicker';
import '@hassanmojab/react-modern-calendar-datepicker/lib/DatePicker.css';
import moment from 'moment';

moment.locale('en-gb');

const CalendarToolbar = ({ dates, setDates }) => {
  // State to manage calendar visibility
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const handleDateArrow = (direction) => {
    setDates({
      from_date: dates.from_date.add(direction, 'weeks').startOf('week'),
      to_date: dates.to_date.add(direction, 'weeks').endOf('week'),
    });
  };

  // Converts selected day to moment object and updates date range
  const handleDateChange = (selectedDay) => {
    if (!selectedDay) return;

    const selectedDate = new Date(selectedDay.year, selectedDay.month - 1, selectedDay.day);
    setDates({
      from_date: moment(selectedDate).startOf('week'),
      to_date: moment(selectedDate).endOf('week'),
    });

    setIsCalendarOpen(false);
  };

  function getDisplayDate() {
    return `${dates.from_date.format('MMMM')} ${dates.from_date.format('DD')} - ${dates.to_date.format(
      'DD'
    )}, ${dates.from_date.format('YYYY')}`;
  }

  const renderCustomDay = (day) => {
    // Create a new date for the current day
    const date = new Date(day.year, day.month - 1, day.day);
    // Check if the day is not a Monday
    const isNotMonday = date.getDay() !== 1;

    // Apply custom styling if it's not Monday
    const dayStyle = isNotMonday ? { color: '#ccc', backgroundColor: '#e9e9e9' } : {};

    return <div style={dayStyle}>{day.day}</div>;
  };

  return (
    <div className="between-flex" style={{ width: '300px' }}>
      <div className="button calendar-date-change" onClick={() => handleDateArrow(-1)} />
      <div className="admin-order-calendar-container" onClick={() => setIsCalendarOpen(!isCalendarOpen)}>
        <DatePicker
          value={null}
          onChange={handleDateChange}
          formatInputText={() => getDisplayDate()}
          renderCustomDay={renderCustomDay}
          shouldHighlightWeekends
        />
      </div>
      <div className="button calendar-date-change forward" onClick={() => handleDateArrow(1)} />
    </div>
  );
};

CalendarToolbar.propTypes = {
  dates: PropTypes.object.isRequired,
  setDates: PropTypes.func.isRequired,
};

export default CalendarToolbar;
