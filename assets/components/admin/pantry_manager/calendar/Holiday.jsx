import PropTypes from 'prop-types';

const Holiday = ({ holiday, index }) => {
  const holidayColors = ['#30f9fc', '#756CBC', '#FBB5EE'];
  const fallbackHolidayColor = holidayColors[index % 3];
  const backgroundColor = holiday.color || fallbackHolidayColor;

  return (
    <div
      key={index}
      className="calendar-event calendar-event--holiday"
      style={{ backgroundColor }}
    >
      <div>
        {holiday.name}
        {!!holiday.states.length && ` (${holiday.states.join(', ')})`}
      </div>
    </div>
  );
};

Holiday.propTypes = {
  holiday: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Holiday;
