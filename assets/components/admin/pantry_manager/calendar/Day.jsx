import PropTypes from 'prop-types';
import moment from 'moment';

// components
import Holiday from './Holiday';
import Order from './Order';

const Day = ({ day, index, orders, holidays, setActiveOrder, activePantryManager }) => {
  const dayHolidays = holidays.filter((event) => event.on_date === day);
  const dayOrders = orders.filter((order) => moment(order.delivery_date).format('YYYY-MM-DD') === day);
  const dayNumber = day.split('-')[2];
  const isToday = moment().isSame(day, 'day');

  return (
    <div key={index} className={`calendar-day ${isToday ? 'today' : ''}`} data-date={day} >
      <div className="events">
        <div className={`day-number ${!dayHolidays.length ? 'spacer' : ''}`}>
          {`${dayNumber} ${moment(day).format('MMMM')}`}
        </div>
        {dayHolidays.map((holiday, index) => (
          <Holiday
            key={`calendar-day-holiday-${holiday.id}`}
            holiday={holiday}
            index={index}
          />
        ))}
        {dayOrders.map((order, index) => (
          <Order
            key={`calendar-day-order-${order.id}`}
            order={order}
            index={index}
            setActiveOrder={setActiveOrder}
            activePantryManager={activePantryManager}
          />
        ))}
      </div>
    </div>
  );
};

Day.propTypes = {
  day: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
  orders: PropTypes.array.isRequired,
  holidays: PropTypes.array.isRequired,
  setActiveOrder: PropTypes.func.isRequired,
  activePantryManager: PropTypes.object,
};

export default Day;
