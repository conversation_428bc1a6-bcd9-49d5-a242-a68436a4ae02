import { useState } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

// components
import NewOrder from './NewOrder';
import Week from './calendar/Week';
import OrderDetails from 'components/admin/order/details/OrderDetails';

const PantryManagerOrders = ({ orders, holidays, dates, activePantryManager }) => {
  const [activeOrder, setActiveOrder] = useState(null);

  return (
    <div className="mt-2">
      <div className='between-flex pantry-manager-header'>
        <h3>Pantry Orders</h3>
        <NewOrder />
      </div>
      {!!activeOrder && <div className="overlay show" onClick={() => setActiveOrder(null)} />}
      
      <div className="weekly-calendar-days">
        <span>MON</span>
        <span>TUE</span>
        <span>WED</span>
        <span>THU</span>
        <span>FRI</span>
      </div>      
      <Week
        weekStartDate={dates.from_date}
        orders={orders}
        holidays={holidays}
        setActiveOrder={setActiveOrder}
        activePantryManager={activePantryManager}
      />
      {!!activeOrder && (
        <div className={`sidebar-overlay ${activeOrder ? 'open' : 'closed'}`}>
          <div className="admin-order-slider">
            <OrderDetails order={activeOrder} setActiveOrder={setActiveOrder} />
          </div>
        </div>
      )}
    </div>
  )
}

PantryManagerOrders.propTypes = {
  orders: PropTypes.array.isRequired,
  holidays: PropTypes.array.isRequired,
  dates: PropTypes.object.isRequired,
  activePantryManager: PropTypes.object
}

export default PantryManagerOrders;