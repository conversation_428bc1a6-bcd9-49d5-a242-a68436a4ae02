import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import axios from 'axios';
import { sendStaffingLogsAPIAdminAdminsPath } from 'routes';
import DatePicker from '@hassanmojab/react-modern-calendar-datepicker';
import { csrfHeaders } from 'utilities/csrfHeaders';

const getPayrollDatesFor = (date) => {
  const evenWeek = Number(date.format('W')) % 2 == 1;
  let fromDate = date.clone().startOf('week');
  let toDate = date.clone().endOf('week');
  if (evenWeek) {
    fromDate.add(-1, 'week');
  } else {
    toDate.add(1, 'week');
  }

  return {
    from_date: fromDate,
    to_date: toDate,
  }
}

const StaffingLogs = ({ dates }) => {
  const [logDates, setLogDates] = useState(getPayrollDatesFor(dates.from_date));
  const [showForm, setShowForm] = useState(false);
  const [inProgress, setInProgress] = useState(false);
  const [logsSent, setLogsSent] = useState(false);

  useEffect(() => {
    setLogDates(getPayrollDatesFor(dates.from_date));
  }, [dates])

  const sendStaffingLogs = async () => {
    setInProgress(true);
    setLogsSent(false);

    try {
      await axios({
        method: 'POST',
        url: sendStaffingLogsAPIAdminAdminsPath({ format: 'json' }),
        params: {
          date: moment(logDates.from_date).format('YYYY-MM-DD'),
        },
        headers: csrfHeaders(),
      });
      setInProgress(false);
      setLogsSent(true);
    } catch (err) {
      alert('Sorry Something Went Wrong');
      setInProgress(false);
    }
  }

  function getDisplayDate() {
    return `${logDates.from_date.format('MMMM')} ${logDates.from_date.format('DD')} - ${(logDates.from_date.format('MMMM') != logDates.to_date.format('MMMM')) ? `${logDates.to_date.format('MMMM')} ` : ''}${logDates.to_date.format('DD')}, ${logDates.from_date.format('YYYY')}`;
  }

  const handleDateChange = (selectedDay) => {
    if (!selectedDay) return;

    const selectedDate = new Date(selectedDay.year, selectedDay.month - 1, selectedDay.day);
    const newDates = getPayrollDatesFor(moment(selectedDate));
    setLogDates(newDates);
  };

  if (showForm) {
    const selectedDate = {
      day: Number(logDates.to_date.format('DD')),
      month: Number(logDates.to_date.format('MM')),
      year: Number(logDates.to_date.format('YYYY')),
    }

    return (
      <>
        <div className="overlay show" onClick={() => setShowForm(false)} />
        <div className="sidebar-overlay open">
          <div style={{ width: '400px', padding: '16px' }}>
            <h3 className="report-modal-heading">Send Staffing Logs</h3>
            <p className='text-center'>
              <DatePicker
                value={selectedDate}
                onChange={handleDateChange}
                formatInputText={() => getDisplayDate()}
                shouldHighlightWeekends
              />
            </p>

            <p>
              For payroll fortnight ending in:
              <br/>
              <b>{moment(logDates.to_date).format('DD-MM-YYYY')}</b>
            </p>
            <div className="between-flex">            
              <a className="button" onClick={sendStaffingLogs}>
                {inProgress ? 'Sending Logs...' : 'Send Email To Accounts'}
              </a>
              <a className="button gray-btn" onClick={() => setShowForm(false)}>
                Cancel
              </a>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <a className="button hollow" onClick={() => setShowForm(true)}>
        Send Staffing Logs
      </a>
    </>
  )
}

export default StaffingLogs;