import { useState } from 'react';
import Select from 'react-select';

const PantryManagerFilter = ({ pantryManagers, activePantryManager, setActivePantryManager }) => {
  const pantryManagerOptions = pantryManagers.map((pantryManager) => (
    {
      label: pantryManager,
      value: pantryManager,
    }
  ));

  return (
    <div style={{ width: '350px' }}>
      <Select
        isSearchable
        isClearable
        value={activePantryManager}
        placeholder="Filter by Pantry Manager"
        name="manager-filter"
        options={pantryManagerOptions}
        onChange={(option) => setActivePantryManager(option)}
      />
    </div>
  )
}

export default PantryManagerFilter;