import { useState, useEffect } from 'react';
import axios from 'axios';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  LineController,
  BarController,
} from 'chart.js';

import { pantryManagerSpendsAPIAdminAdminsPath, apiHolidaysPath } from 'routes';

import CalendarToolbar from './calendar/CalendarToolbar';
import PantryManagerFilter from './PantryManagerFilter';
import PantryBarGraph from './PantryBarGraph';
import PantryManagerOrders from './PantryManagerOrders';
import StaffingLogs from './StaffingLogs';

moment.locale('en-gb');

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Toolt<PERSON>,
  Legend,
  LineController,
  BarController
);

const PantryManagerAdminApp = () => {
  const [activePantryManager, setActivePantryManager] = useState(null);
  const [loading, setLoading] = useState(false);
  const today = new Date();
  const startOfWeek = moment(today).startOf('week');
  const endOfWeek = moment(today).endOf('week');
  const [dates, setDates] = useState({ from_date: startOfWeek, to_date: endOfWeek });
  const [spends, setSpends] = useState([]);
  const [holidays, setHolidays] = useState([]);

  function onDateChange(newDates) {
    const [from_date, to_date] = newDates;
    setDates({ from_date, to_date });
  }

  useEffect(async () => {
    if (!dates.from_date || !dates.to_date) return;

    setLoading(true);
    const { data: managerSpends } = await axios({
      method: 'GET',
      url: pantryManagerSpendsAPIAdminAdminsPath({ format: 'json' }),
      params: {
        from_date: dates.from_date.format('YYYY-MM-DD'),
        to_date: dates.to_date.format('YYYY-MM-DD'),
      },
    });
    setLoading(false);
    setSpends(managerSpends);

    const { data: weekHolidays } = await axios({
      method: 'GET',
      params: {
        from_date: moment(dates.from_date).add(-1, 'days').format('YYYY-MM-DD'),
        to_date: dates.to_date.format('YYYY-MM-DD'),
      },
      url: apiHolidaysPath({ format: 'json' }),
    });
    setHolidays(weekHolidays);

  }, [dates]);

  const exportData = () => {
    if (!dates.from_date || !dates.to_date) return;

    window.location = `${pantryManagerSpendsAPIAdminAdminsPath({
      format: 'csv',
      from_date: dates.from_date.format('YYYY-MM-DD'),
      to_date: dates.to_date.format('YYYY-MM-DD'),
    })}`;
  };

  const pantryManagers = spends.length ? spends.map((spend) => spend.pantry_manager) : [];
  const orders = spends.length ? spends.map((spend) => spend.orders).flat() : [];

  return (
    <>
      <div className="between-flex pantry-manager-header">
        <CalendarToolbar dates={dates} setDates={setDates} />
        <PantryManagerFilter pantryManagers={pantryManagers} activePantryManager={activePantryManager} setActivePantryManager={setActivePantryManager} />        

        <div>
          <StaffingLogs dates={dates} />
          <a className="button ml-1" onClick={exportData}>
            Export Data
          </a>
        </div>
      </div>
      <PantryBarGraph spends={spends} activePantryManager={activePantryManager} />
      <PantryManagerOrders orders={orders} holidays={holidays} dates={dates} activePantryManager={activePantryManager} />
    </>
  );
};

export default PantryManagerAdminApp;
