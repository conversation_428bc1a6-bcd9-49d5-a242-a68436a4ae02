import { Chart } from 'react-chartjs-2';
import { generatePantryManagerData, pantryManagerBarOptions } from 'utilities/graph-options';

const PantryBarGraph = ({ spends, activePantryManager }) => (
  <div className="reporting-graph">
    <div className="bar-container">
      <Chart options={pantryManagerBarOptions} data={generatePantryManagerData(spends, activePantryManager)} />
    </div>
  </div>
);

export default PantryBarGraph;
