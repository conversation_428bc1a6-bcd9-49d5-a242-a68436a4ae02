import { useContext, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import useSupplierDeliveryZoneStore from 'store/supplierDeliveryZoneStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import MinimumDeliveryFee from './MinimumDeliveryFee';
import DeliveryZoneSkeleton from './DeliveryZoneSkeleton';
import NewDeliveryZone from './NewDeliveryZone';
import DeliveryZone from './DeliveryZone';

const DeliveryZoneApp = () => {
  const { minimumDeliveryFee: contextualMinimumDeliveryFee } = useContext(appContext);

  const { setMinimumDeliveryFee, fetchDeliveryZones, loadingList, delivery_zones } = useSupplierDeliveryZoneStore(
    (state) => ({
      setMinimumDeliveryFee: state.setMinimumDeliveryFee,
      fetchDeliveryZones: state.fetchDeliveryZones,
      loadingList: state.loadingList,
      delivery_zones: state.delivery_zones,
    }),
    shallow
  );

  useEffect(() => {
    // set initial Minimum Delivery Fee in store-state from context
    setMinimumDeliveryFee(contextualMinimumDeliveryFee);
    fetchDeliveryZones();
  }, []);

  return (
    <div className="supplier-delivery-zones">
      <MinimumDeliveryFee />
      <div className="item-list__headings">
        <span className="list-flex-1"># of suburbs</span>
        <span className="list-flex-4">Suburb</span>
        <span className="list-flex-1">Radius</span>
        <span className="list-flex-1">Delivery Fee</span>
        <span className="list-flex-3">Operating Days</span>
        <span className="list-flex-2">Hours</span>
        <span className="list-flex-1 text-center">Actions</span>
      </div>
      {loadingList && <DeliveryZoneSkeleton />}
      {!loadingList && <NewDeliveryZone />}
      {delivery_zones.map((delivery_zone, idx) => (
        <DeliveryZone key={`delivery-zone-${delivery_zone.id}`} delivery_zone={delivery_zone} index={idx} />
      ))}
      {!!(delivery_zones.length > 20) && (
        <strong>
          To add new Delivery Zones => <a onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>Go To Top</a>
        </strong>
      )}
      <ToastContainer />
    </div>
  );
};

export default DeliveryZoneApp;
