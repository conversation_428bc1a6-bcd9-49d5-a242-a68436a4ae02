import { useState, useContext } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

// store
import shallow from 'zustand/shallow';
import useSupplierDeliveryZoneStore from 'store/supplierDeliveryZoneStore';
import adminContext from 'contexts/adminContext';

// components
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import DeliveryZoneForm from './DeliveryZoneForm';

const DeliveryZone = ({ delivery_zone, index }) => {
  const [editZone, setEditZone] = useState(false);
  const circleColor = getCircleIconColor(index);

  const { isAdmin } = useContext(adminContext);

  const { removeDeliveryZone, loadingZones } = useSupplierDeliveryZoneStore(
    (state) => ({
      removeDeliveryZone: state.removeDeliveryZone,
      loadingZones: state.loadingZones,
    }),
    shallow
  );

  const isLoading = loadingZones.includes(delivery_zone.id);

  const handleRemove = async (event) => {
    if (isLoading) return;

    await removeDeliveryZone({
      deliveryZone: delivery_zone,
    });
  };

  if (editZone) {
    return <DeliveryZoneForm delivery_zone={delivery_zone} setFormVisibility={setEditZone} />;
  }

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
        <span className="circle-icon" style={{ background: circleColor }}>
          {isAdmin && delivery_zone.recently_updated && <span className="icon-info-circle" />}
          {isAdmin && !delivery_zone.recently_updated && delivery_zone.deliverable_suburb_count}
          {!isAdmin && delivery_zone.suburb_label[0]}
        </span>
      </div>
      <div className="list-flex-4">
        {delivery_zone.suburb_label}
        {isAdmin && delivery_zone.recently_updated && (
          <span style={{ color: '#9f9f9f', display: 'block' }}>(calculating deliverable suburbs...)</span>
        )}
        {isAdmin && !delivery_zone.recently_updated && (
          <>
            <span
              style={{ cursor: 'help', color: '#9f9f9f', display: 'block' }}
              data-tip
              data-for={`delivery-zone-${delivery_zone.id}-deliverable-suburbs`}
            >
              (visible in {delivery_zone.deliverable_suburb_count} suburbs)
            </span>
            <ReactTooltip
              id={`delivery-zone-${delivery_zone.id}-deliverable-suburbs`}
              className="reveal"
              place="bottom"
              effect="solid"
            >
              <ul>
                {delivery_zone.deliverable_suburbs.map((suburb, idx) => (
                  <li key={`deliverable-suburb-${delivery_zone.id}-${idx}`}>
                    <strong>{suburb.name}</strong> - {suburb.distance}
                  </li>
                ))}
                {delivery_zone.deliverable_suburb_count > 15 && (
                  <li>
                    and <strong>{delivery_zone.deliverable_suburb_count - 15} more</strong>...
                  </li>
                )}
              </ul>
            </ReactTooltip>
          </>
        )}
      </div>
      <div className="list-flex-1">{delivery_zone.radius}km</div>
      <div className="list-flex-1">${delivery_zone.delivery_fee}</div>
      <div className="list-flex-2">
        <ul className="opening-days-list">
          {delivery_zone.operating_days.map((operatingDay, idx) => (
            <li
              key={`operating-day-${delivery_zone.id}-${idx}`}
              className={`ml-1-4 ${idx >= 5 ? 'weekend' : ''} ${operatingDay.active ? 'active' : ''}`}
            >
              {operatingDay.weekday}
            </li>
          ))}
        </ul>
      </div>
      <div className="list-flex-2">{delivery_zone.operating_hours}</div>
      <div className="list-flex-1 text-center">
        {!isLoading && (
          <>
            <a className="icon-edit" onClick={() => setEditZone(true)} />
            <a className="icon-trash ml-2" onClick={handleRemove} />
          </>
        )}
        {isLoading && <span>processing...</span>}
      </div>
    </div>
  );
};

DeliveryZone.propTypes = {
  delivery_zone: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default DeliveryZone;
