import { useState } from 'react';

// components
import DeliveryZoneForm from './DeliveryZoneForm';

const initialDeliveryZone = {
  id: 'new',
  suburb_id: null,
  suburb_label: '',
  radius: '',
  delivery_fee: '',
  operating_wdays: '0000000',
  operating_hours_start: '',
  operating_hours_end: '',
};

const NewDeliveryZone = () => {
  const [newZone, setNewZone] = useState(false);

  if (newZone) {
    return <DeliveryZoneForm delivery_zone={initialDeliveryZone} setFormVisibility={setNewZone} />;
  }

  return (
    <div className="customer-data">
      <a onClick={() => setNewZone(true)}>Add new delivery zone</a>
    </div>
  );
};

export default NewDeliveryZone;
