import PropTypes from 'prop-types';

const OperatingHoursSelector = ({ deliveryZone, handleChange, isLoading }) => (
  <div className="between-flex">
    <input
      className="form-input"
      type="time"
      name="operating_hours_start"
      readOnly={isLoading}
      value={deliveryZone.operating_hours_start}
      onChange={handleChange}
    />
    <strong className="mx-1-2 my-1-4 text-center" style={{ display: 'block' }}>
      TO
    </strong>
    <input
      className="form-input"
      type="time"
      name="operating_hours_end"
      readOnly={isLoading}
      value={deliveryZone.operating_hours_end}
      onChange={handleChange}
    />
  </div>
);

OperatingHoursSelector.propTypes = {
  deliveryZone: PropTypes.object.isRequired,
  handleChange: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
};

export default OperatingHoursSelector;
