import PropTypes from 'prop-types';

const WEEKDAYS = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

const OperatingDaysSelector = ({ deliveryZone, setDeliveryZone, isLoading }) => {
  const operatingDays = deliveryZone.operating_wdays.split('');

  const handleClick = (changeIndex) => {
    if (isLoading) return;

    const newDayValue = operatingDays[changeIndex] == '1' ? '0' : '1';
    const updatedOperatingDays = operatingDays.map((day, idx) => (idx == changeIndex ? newDayValue : day));
    setDeliveryZone((state) => ({ ...state, operating_wdays: updatedOperatingDays.join('') }));
  };

  return (
    <div className="day-selector">
      {WEEKDAYS.map((weekday, idx) => (
        <div
          key={`operating-day-${deliveryZone.id}-selector-${idx}`}
          className={`button ${operatingDays[idx] == 1 ? 'active' : ''}`}
          onClick={() => handleClick(idx)}
          style={{ marginBottom: 0 }}
        >
          {weekday}
        </div>
      ))}
    </div>
  );
};

OperatingDaysSelector.propTypes = {
  deliveryZone: PropTypes.object.isRequired,
  setDeliveryZone: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
};

export default OperatingDaysSelector;
