import { useState, useContext } from 'react';
import PropTypes from 'prop-types';

// actions
import appContext from 'contexts/appContext';
import { fetchSuburbByTerm } from 'utilities/locationSelection';

// components
import AsyncSelect from 'react-select/async';

const SuburbSelector = ({ deliveryZone, setDeliveryZone, isLoading }) => {
  const [selectedSuburb, setSelectedSuburb] = useState(
    deliveryZone.suburb_id && deliveryZone.suburb_label
      ? {
        id: deliveryZone.suburb_id,
        label: deliveryZone.suburb_label,
      }
      : null
  );

  const { countryCode } = useContext(appContext);

  const handleSelection = (suburb) => {
    setDeliveryZone((state) => ({ ...state, suburb_id: suburb.id }));
    setSelectedSuburb({ label: suburb.label, id: suburb.id });
  };

  const promiseOptions = async (term) => {
    if (!term || term.length < 3) return [];
    const suburbs = await fetchSuburbByTerm({
      term,
      countryCode,
    });
    return suburbs;
  };

  return (
    <AsyncSelect
      className="form-input"
      cacheOptions
      defaultOptions
      value={selectedSuburb}
      loadOptions={promiseOptions}
      onChange={handleSelection}
      isDisabled={isLoading}
      placeholder="Delivery Suburb"
    />
  );
};

SuburbSelector.propTypes = {
  deliveryZone: PropTypes.object.isRequired,
  setDeliveryZone: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
};

export default SuburbSelector;
