import { useState } from 'react';
import PropTypes from 'prop-types';

// store
import shallow from 'zustand/shallow';
import useSupplierDeliveryZoneStore from 'store/supplierDeliveryZoneStore';

// components
import SuburbSelector from './inputs/SuburbSelector';
import OperatingDaysSelector from './inputs/OperatingDaysSelector';
import OperatingHoursSelector from './inputs/OperatingHoursSelector';

const DeliveryZoneForm = ({ delivery_zone, setFormVisibility }) => {
  const [deliveryZone, setDeliveryZone] = useState(delivery_zone);

  const { updateDeliveryZone, createDeliveryZone, loadingZones } = useSupplierDeliveryZoneStore(
    (state) => ({
      updateDeliveryZone: state.updateDeliveryZone,
      createDeliveryZone: state.createDeliveryZone,
      loadingZones: state.loadingZones,
    }),
    shallow
  );

  const isLoading = loadingZones.includes(delivery_zone.id);

  const handleChange = (event) => {
    setDeliveryZone((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  const handleSubmit = async () => {
    if (isLoading) return;

    if (deliveryZone.id == 'new') {
      await createDeliveryZone({
        deliveryZone,
      });
    } else {
      await updateDeliveryZone({
        deliveryZone,
      });
    }

    setFormVisibility(false);
  };

  return (
    <div className="list-item">
      <div className="list-flex-3 p-1-4">
        <SuburbSelector deliveryZone={deliveryZone} setDeliveryZone={setDeliveryZone} isLoading={isLoading} />
      </div>
      <div className="list-flex-1 p-1-4">
        <input
          type="text"
          name="radius"
          className="form-input"
          readOnly={!!isLoading}
          value={deliveryZone.radius}
          onChange={handleChange}
          placeholder="Radius"
        />
      </div>
      <div className="list-flex-1 p-1-4">
        <input
          type="text"
          name="delivery_fee"
          className="form-input"
          readOnly={!!isLoading}
          value={deliveryZone.delivery_fee}
          onChange={handleChange}
          placeholder="$ Fee"
        />
      </div>
      <div className="list-flex-3 p-1-4 text-center">
        <OperatingDaysSelector deliveryZone={deliveryZone} setDeliveryZone={setDeliveryZone} isLoading={isLoading} />
      </div>
      <div className="list-flex-4 p-1-4">
        <OperatingHoursSelector deliveryZone={deliveryZone} handleChange={handleChange} isLoading={isLoading} />
      </div>
      <div className="list-flex-1 between-flex text-center">
        <a className="button small" onClick={handleSubmit} style={{ margin: 0 }}>
          Save
        </a>
        <a className="button small gray-btn" onClick={() => setFormVisibility(false)} style={{ margin: 0 }}>
          Cancel
        </a>
      </div>
    </div>
  );
};

DeliveryZoneForm.propTypes = {
  delivery_zone: PropTypes.object.isRequired,
  setFormVisibility: PropTypes.func.isRequired,
};

export default DeliveryZoneForm;
