import { useContext } from 'react';
// store
import shallow from 'zustand/shallow';
import useSupplierDeliveryZoneStore from 'store/supplierDeliveryZoneStore';
import appContext from 'contexts/appContext';

// components
import ReactTooltip from 'react-tooltip';

const MinimumDeliveryFee = () => {
  const { supplierID } = useContext(appContext);
  const { minimumDeliveryFee, setMinimumDeliveryFee, updateMinimDeliveryFee } = useSupplierDeliveryZoneStore(
    (state) => ({
      minimumDeliveryFee: state.minimumDeliveryFee,
      setMinimumDeliveryFee: state.setMinimumDeliveryFee,
      updateMinimDeliveryFee: state.updateMinimDeliveryFee,
    }),
    shallow
  );

  const handleChange = (event) => {
    setMinimumDeliveryFee(event.target.value);
  };

  const handleBlur = async (event) => {
    await updateMinimDeliveryFee({
      supplierID,
      minimumDeliveryFee: event.target.value,
    });
  };

  return (
    <div className="between-flex">
      <div className="between-flex">
        <label>
          Minimum Delivery Fee
          <span
            className="icon-info-circle ml-1-4 mr-1"
            style={{ cursor: 'help' }}
            data-tip
            data-for="supplier-minimum-delivery-fee-hint"
          />
          <ReactTooltip id="supplier-minimum-delivery-fee-hint" className="reveal" place="bottom" effect="solid">
            This fee will be applied to orders that are below your minimum order value and have no delivery fee set.
          </ReactTooltip>
        </label>
        <input
          type="text"
          className="form-input"
          style={{ width: '100px' }}
          value={minimumDeliveryFee}
          onChange={handleChange}
          onBlur={handleBlur}
        />
      </div>
      <div />
    </div>
  );
};

export default MinimumDeliveryFee;
