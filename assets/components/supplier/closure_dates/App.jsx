import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// components
import useInfiniteScroll from 'react-infinite-scroll-hook';
import { fetchClosureDates } from 'actions/closureActions';
import { createLoadingSelector } from 'selectors';
import ClosureDateSkeleton from './ClosureDateSkeleton';
import ClosureDate from './ClosureDate';
import ClosureDateForm from './ClosureDateForm';

// loader

const loadingSelector = createLoadingSelector(['FETCH_CLOSURE_DATES']);
const infiniteLoadingSelector = createLoadingSelector(['FETCH_MORE_DATES']);

const App = () => {
  const [newClosureDate, setNewClosureDate] = useState(false);
  const { closure_dates, page, hasMore } = useSelector((state) => state.supplier_closure);
  const dispatch = useDispatch();

  const loadingState = useSelector((state) => state.loading);

  const loading = loadingSelector(loadingState);
  const infiniteLoading = infiniteLoadingSelector(loadingState);

  useEffect(() => {
    dispatch(
      fetchClosureDates({
        page,
        infiniteLoading: false,
      })
    );
  }, []);

  const [sentryRef] = useInfiniteScroll({
    loading: infiniteLoading,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (closure_dates.length && !loading) {
        await dispatch(
          fetchClosureDates({
            page,
            infiniteLoading: true,
          })
        );
      }
    },
  });

  return (
    <>
      <div className="supplier-data-list__headings">
        <span className="list-flex-1" label="Days">
          Days
        </span>
        <span className="list-flex-3" label="Starts At">
          Starts At
        </span>
        <span className="list-flex-3" label="Ends At">
          Ends At
        </span>
        <span className="list-flex-5" label="Reason">
          Reason
        </span>
        <span className="list-flex-2 text-right pr-2" label="Actions">
          Actions
        </span>
      </div>

      {!newClosureDate && (
        <div className="supplier-data">
          <a onClick={() => setNewClosureDate(true)}>Add new closure date</a>
        </div>
      )}
      {newClosureDate && <ClosureDateForm setFormShow={setNewClosureDate} />}

      {closure_dates.map((closure_date, index) => (
        <ClosureDate key={`closure-date-${closure_date.id}`} closureDate={closure_date} index={index} />
      ))}

      <div ref={sentryRef}>{(loading || infiniteLoading) && <ClosureDateSkeleton />}</div>
    </>
  );
};

export default App;
