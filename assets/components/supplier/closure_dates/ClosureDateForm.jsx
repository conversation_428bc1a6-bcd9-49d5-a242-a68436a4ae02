import { useState } from 'react';
import PropTypes from 'prop-types';

// components
import DatePicker from 'react-datepicker';

// actions
import { useDispatch } from 'react-redux';
import { createClosureDate, updateClosureDate } from 'actions/closureActions';

const initialFormState = { starts_at: '', ends_at: '', reason: '' };

const ClosureDateForm = ({ currentClosureDate = initialFormState, setFormShow, edit }) => {
  const [closureDate, setClosureDate] = useState(currentClosureDate);
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const handleDateChange = (name, datetime) => {
    setClosureDate((state) => ({ ...state, [name]: datetime }));
  };

  const handleChange = (event) => {
    setClosureDate((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  const handleSubmit = async (event) => {
    setLoading(true);
    try {
      if (edit) {
        await dispatch(updateClosureDate({ closureDate }));
      } else {
        await dispatch(createClosureDate({ closureDate }));
      }
      setLoading(false);
      setFormShow(false);
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <div className="supplier-data form">
      <div className="list-flex-4 px-1-2">
        <label>Starts At</label>
        <DatePicker
          selected={closureDate.starts_at ? new Date(closureDate.starts_at) : ''}
          onChange={(datetime) => handleDateChange('starts_at', datetime)}
          name="starts_at"
          dateFormat="yyyy-MM-dd"
          className="form-input"
          autoComplete="off"
        />
      </div>
      <div className="list-flex-4 px-1-2">
        <label>Ends At</label>
        <DatePicker
          selected={closureDate.ends_at ? new Date(closureDate.ends_at) : ''}
          onChange={(datetime) => handleDateChange('ends_at', datetime)}
          name="ends_at"
          dateFormat="yyyy-MM-dd"
          className="form-input"
          autoComplete="off"
        />
      </div>
      <div className="list-flex-4 px-1-2">
        <label>Reason</label>
        <input
          type="text"
          name="reason"
          className="form-input"
          readOnly={loading}
          value={closureDate.reason}
          onChange={handleChange}
        />
      </div>
      <div className="list-flex-3 pr-1 supplier-data__flex-action-field">
        <a className="button small" onClick={handleSubmit}>
          {loading ? 'Saving...' : 'Save'}
        </a>
        <span className="ml-1-2" onClick={() => setFormShow(false)}>
          Cancel
        </span>
      </div>
    </div>
  );
};

ClosureDateForm.propTypes = {
  currentClosureDate: PropTypes.object,
  edit: PropTypes.bool,
};

export default ClosureDateForm;
