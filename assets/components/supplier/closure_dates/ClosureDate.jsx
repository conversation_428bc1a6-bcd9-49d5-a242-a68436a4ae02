import { useState } from 'react';
import PropTypes from 'prop-types';

// components
import { useDispatch } from 'react-redux';

// actions
import { removeClosureDate } from 'actions/closureActions';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import ClosureDateForm from './ClosureDateForm';

const ClosureDate = ({ closureDate, index }) => {
  const [edit, setEdit] = useState(false);
  const dispatch = useDispatch();

  const handleRemove = async (event) => {
    await dispatch(
      removeClosureDate({
        closureDate,
      })
    );
  };

  if (edit) {
    return <ClosureDateForm currentClosureDate={closureDate} setFormShow={setEdit} edit />;
  }

  return (
    <div className="supplier-data">
      <div className="list-flex-1" label="Days">
        <span className="circle-icon" style={{ background: getCircleIconColor(index) }}>
          {closureDate.number_of_days}
        </span>
      </div>
      <div className="list-flex-3" label="Starts At">
        {closureDate.formatted_starts_at}
      </div>
      <div className="list-flex-3" label="Ends At">
        {closureDate.formatted_ends_at}
      </div>
      <div className="list-flex-5" label="Reason">
        <strong>
          {closureDate.reason}
          {closureDate.is_expired ? <small className="text-grey ml-1-4">(expired)</small> : ''}
        </strong>
      </div>
      <div className="list-flex-2 pr-2 supplier-data__flex-action-field" label="Edit">
        <a className="icon-edit mr-1" onClick={() => setEdit(true)} />
        <a className="icon-trash" onClick={handleRemove} />
      </div>
    </div>
  );
};

ClosureDate.propTypes = {
  closureDate: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default ClosureDate;
