import { useContext, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import useSupplierMinimumStore from 'store/supplierMinimumsStore';
import appContext from 'contexts/appContext';

// components
import SupplierLeadMode from './SupplierLeadMode';
import MininumsList from './MininumsList';

const MininumsApp = () => {
  const { leadMode: contextualLeadMode } = useContext(appContext);

  const { setLeadMode, fetchMinimums, leadMode } = useSupplierMinimumStore(
    (state) => ({
      setLeadMode: state.setLeadMode,
      fetchMinimums: state.fetchMinimums,
      leadMode: state.leadMode,
    }),
    shallow
  );

  useEffect(() => {
    setLeadMode(contextualLeadMode);
    fetchMinimums();
  }, []);

  return (
    <>
      <SupplierLeadMode leadMode={leadMode} />
      <MininumsList leadMode={leadMode} />
    </>
  );
};

export default MininumsApp;
