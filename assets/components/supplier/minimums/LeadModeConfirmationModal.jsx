import PropTypes from 'prop-types';
import { useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import useSupplierMinimumStore from 'store/supplierMinimumsStore';
import appContext from 'contexts/appContext';

// components
import { Modal } from 'react-responsive-modal';

const LeadModeConfirmationModal = ({ leadMode, setConfirmChange }) => {
  const potentialNewLoadMode = leadMode === 'by_day_before' ? 'by_hour' : 'by_day_before';
  const { supplierID } = useContext(appContext);

  const { updateLeadMode } = useSupplierMinimumStore(
    (state) => ({
      updateLeadMode: state.updateLeadMode,
    }),
    shallow
  );

  const handleConfirm = async () => {
    await updateLeadMode({
      supplierID,
      leadMode: potentialNewLoadMode,
    });
    setConfirmChange(false);
  };

  return (
    <Modal
      classNames={{ modal: 'reveal customer-form' }}
      open
      onClose={() => setConfirmChange(false)}
      center
      showCloseIcon={false}
    >
      <div className="form-content has-small-gutter text-center">
        <h3 className="modal-title">Warning!</h3>
        <p>
          Are you sure you want to swap mode to
          <strong className="ml-1-4">{potentialNewLoadMode === 'by_hour' ? 'By Hour' : 'By Day Before'}?</strong>
        </p>
        <p>This will result in all lead time data being deleted.</p>
      </div>
      <div className="form-footer light-gray-bg medium-text-right">
        <a className="button small uppercase gray-btn" onClick={() => setConfirmChange(false)}>
          Cancel
        </a>
        <a className="button small uppercase" onClick={handleConfirm}>
          Confirm
        </a>
      </div>
    </Modal>
  );
};

LeadModeConfirmationModal.propTypes = {
  leadMode: PropTypes.string.isRequired,
  setConfirmChange: PropTypes.func.isRequired,
};

export default LeadModeConfirmationModal;
