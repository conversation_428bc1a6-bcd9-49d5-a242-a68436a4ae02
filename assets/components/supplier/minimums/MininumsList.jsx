import { useContext } from 'react';
import PropTypes from 'prop-types';
import adminContext from 'contexts/adminContext';

// store
import shallow from 'zustand/shallow';
import useSupplierMinimumStore from 'store/supplierMinimumsStore';

// components
import MinimumsSkeleton from './MinimumsSkeleton';
import SupplierMinimum from './SupplierMinimum';

const leadTimeHeaderMap = {
  by_hour: 'Lead Time (in hrs)',
  by_day_before: 'Lead time of day',
};

const MininumsList = ({ leadMode }) => {
  const { minimums, loadingList } = useSupplierMinimumStore(
    (state) => ({
      minimums: state.minimums,
      loadingList: state.loadingList,
    }),
    shallow
  );
  const { isAdmin } = useContext(adminContext);

  const leadTimeHeader = leadTimeHeaderMap[leadMode];

  return (
    <>
      <div className="supplier-data-list__headings">
        <span className="list-flex-1" label="Number Menu Sections">
          # of Sections
        </span>
        <span className="list-flex-5" label="Category">
          Category
        </span>
        <span className="list-flex-2 mr-1" label="Minimum Spend (in $)">
          Minimum Spend (in $)
        </span>
        {isAdmin && (
          <span className="list-flex-2 mr-1" label="Minimum Spend (in $)">
            Commission based Spends
          </span>
        )}
        <span className="list-flex-2" label={leadTimeHeader}>
          {leadTimeHeader}
        </span>
        <span className="list-flex-2 text-right pr-2" label="Actions">
          Actions
        </span>
      </div>

      {loadingList && <MinimumsSkeleton />}
      {!loadingList &&
        minimums.map((minimum, index) => (
          <SupplierMinimum
            key={`supplier-minimum-${minimum.id || minimum.category_id}`}
            minimum={minimum}
            leadMode={leadMode}
            index={index}
          />
        ))}
    </>
  );
};

MininumsList.propTypes = {
  leadMode: PropTypes.string.isRequired,
};

export default MininumsList;
