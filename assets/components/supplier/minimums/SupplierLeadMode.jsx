import { useState, useContext } from 'react';
import PropTypes from 'prop-types';

// data
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

// components
import LeadModeConfirmationModal from './LeadModeConfirmationModal';

const SupplierLeadMode = ({ leadMode }) => {
  const [confirmChange, setConfirmChange] = useState(false);
  const { markup, commissionRate } = useContext(appContext);
  const { isAdmin } = useContext(adminContext);

  const handleChange = (event) => {
    event.preventDefault();
    setConfirmChange(true);
  };

  return (
    <>
      <div className="between-flex">
        <div className="between-flex">
          <label>Lead mode:</label>
          <span className="ml-1">By Hour</span>
          <div className="switch small ml-1">
            <input
              id="toggle-lead-mode-switch"
              className="switch-input"
              name="lead_mode"
              type="checkbox"
              onChange={handleChange}
              checked={leadMode === 'by_day_before'}
            />
            <label className="switch-paddle" htmlFor="toggle-lead-mode-switch" />
          </div>
          <span className="ml-1">By Day Before</span>
        </div>
        {isAdmin && (
          <div>
            Markup: {markup}%  - Commission: {commissionRate}%
          </div>
        )}
      </div>
      {confirmChange && <LeadModeConfirmationModal leadMode={leadMode} setConfirmChange={setConfirmChange} />}
    </>
  );
};

SupplierLeadMode.propTypes = {
  leadMode: PropTypes.string.isRequired,
};
export default SupplierLeadMode;
