import PropTypes from 'prop-types';
import { useState, useContext, useEffect } from 'react';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

// store
import shallow from 'zustand/shallow';
import useSupplierMinimumStore from 'store/supplierMinimumsStore';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import LeadTimeSelector from './LeadTimeSelector';

const SupplierMinimum = ({ minimum, index, leadMode }) => {
  const [localMinimum, setLocalMinimum] = useState(minimum);
  const { markup, commissionRate } = useContext(appContext);
  const { isAdmin } = useContext(adminContext);

  const { createMinimum, updateMinimum, removeMinimum, loadingCategories } = useSupplierMinimumStore(
    (state) => ({
      createMinimum: state.createMinimum,
      updateMinimum: state.updateMinimum,
      removeMinimum: state.removeMinimum,
      loadingCategories: state.loadingCategories,
    }),
    shallow
  );

  const isLoading = loadingCategories.includes(minimum.category_id);

  useEffect(() => {
    setLocalMinimum(minimum);
  }, [minimum]);

  const handleChange = (event) => {
    const field = event.target.name;
    let { value } = event.target;
    if (value === '') value = null;
    setLocalMinimum((state) => ({ ...state, is_updated: true, [field]: value }));
  };

  // submit minimum on enter
  const handleSubmit = (event) => {
    if (event.which === 13 || event.keyCode === 13) submitMinimum(event);
  };

  const submitMinimum = (event) => {
    if (!localMinimum.spend_price || (!localMinimum.lead_time && !localMinimum.lead_time_day_before)) return;
    const fieldName = event.target.name;
    if (minimum[fieldName] === event.target.value) return;

    localMinimum.id ? updateExistingMinimum() : createNewMinimum();
  };

  const updateExistingMinimum = async () => {
    if (isLoading) return;

    await updateMinimum({
      minimum: localMinimum,
    });
  };

  const createNewMinimum = async () => {
    if (isLoading) return;

    await createMinimum({
      minimum: localMinimum,
    });
  };

  const deleteMinimum = async () => {
    if (isLoading) return;

    await removeMinimum({
      minimum: localMinimum,
    });
  };

  const baselineSpend = parseFloat(localMinimum.spend_price) / (1 + (parseFloat(markup) / 100));
  const discountSpend = parseFloat(baselineSpend) * (1 - (parseFloat(commissionRate) / 100));

  return (
    <div className="supplier-data">
      <div className="list-flex-1" label="# of Sections">
        <span className="circle-icon" style={{ background: getCircleIconColor(index) }}>
          {localMinimum.number_of_menu_sections || '-'}
        </span>
      </div>
      <div className="list-flex-5" label="Category">
        {localMinimum.category_name}
        {!localMinimum.number_of_menu_sections && <strong className="error-notice">(unused)</strong>}
      </div>
      <div className="list-flex-2 mr-1" label="Minimum Spend (in $)">
        <input
          className="form-input"
          type="text"
          name="spend_price"
          value={localMinimum.spend_price || ''}
          onChange={handleChange}
          onBlur={submitMinimum}
          onKeyUp={handleSubmit}
          readOnly={isLoading}
        />
      </div>
      {isAdmin && (
        <div className="list-flex-2 mr-1" label="Commission based Spends">
          {!!localMinimum.spend_price && (
            <>
              <small>Baseline - ${baselineSpend.toFixed(2)}</small>
              <br/>
              <small>With discount - ${discountSpend.toFixed(2)}</small>
            </>
          )}
        </div>
      )}
      {leadMode === 'by_hour' && (
        <div className="list-flex-2" label="Lead Time (in hrs)">
          <input
            className="form-input"
            type="text"
            name="lead_time"
            value={localMinimum.lead_time || ''}
            onChange={handleChange}
            onBlur={submitMinimum}
            onKeyUp={handleSubmit}
            readOnly={isLoading}
          />
        </div>
      )}
      {leadMode === 'by_day_before' && (
        <div className="list-flex-2" label="Lead time of day">
          <LeadTimeSelector
            localMinimum={localMinimum}
            setLocalMinimum={setLocalMinimum}
            submitMinimum={submitMinimum}
            readOnly={isLoading}
          />
        </div>
      )}

      <div className="list-flex-2 pr-2 supplier-data__flex-action-field" label="Action">
        {minimum.id && <a className="icon-trash" onClick={deleteMinimum} />}
      </div>
    </div>
  );
};

SupplierMinimum.propTypes = {
  minimum: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  leadMode: PropTypes.string.isRequired,
};

export default SupplierMinimum;
