import { useState, useContext } from 'react';
import PropTypes from 'prop-types';

import Select from 'react-select';
import appContext from 'contexts/appContext';

const LeadTimeSelector = ({ localMinimum, setLocalMinimum, submitMinimum, readOnly }) => {
  const { potentialLeadTimes } = useContext(appContext);
  const selectOptions = potentialLeadTimes.map((time) => ({
    label: time,
    value: time,
  }));
  const [selectedOption, setSelectedOption] = useState(
    selectOptions.find((leadTime) => leadTime.value == localMinimum.lead_time_day_before)
  );

  const handleSelection = (selection) => {
    setSelectedOption(selection);
    setLocalMinimum((state) => ({ ...state, is_updated: true, lead_time_day_before: selection.value }));
  };

  return (
    <Select
      isSearchable={false}
      isClearable={false}
      value={selectedOption}
      placeholder="Select Time of day"
      name="lead_time_day_before"
      options={selectOptions}
      onChange={handleSelection}
      onBlur={submitMinimum}
      className="form-input"
      isDisabled={readOnly}
    />
  );
};

LeadTimeSelector.propTypes = {
  localMinimum: PropTypes.object.isRequired,
  setLocalMinimum: PropTypes.func.isRequired,
  submitMinimum: PropTypes.func.isRequired,
  readOnly: PropTypes.bool.isRequired,
};

export default LeadTimeSelector;
