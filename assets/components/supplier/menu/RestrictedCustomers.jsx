import { useState, useContext } from 'react';
import { Modal } from 'react-responsive-modal';

import { menuContext } from 'contexts/menuContext';

const RestrictedCustomers = () => {
  const [openModal, setOpenModal] = useState(false);
  const { supplier } = useContext(menuContext);
  const { restricted_customers: restrictedCustomers } = supplier;

  if (!restrictedCustomers.length) return null;

  return (
    <>
      <br />
      <span className="label secondary" onClick={() => setOpenModal(true)}>
        Restricted to {restrictedCustomers.length} {restrictedCustomers.length > 1 ? 'customers' : 'customer'}
      </span>
      <Modal classNames={{ modal: 'reveal customer-form' }} open={openModal} onClose={() => setOpenModal(false)} center>
        <h2>Restricted to:</h2>
        <ul className="bullet-list">
          {restrictedCustomers.map((customer, idx) => (
            <li key={`restricted-customer-${idx}`}>{customer}</li>
          ))}
        </ul>
      </Modal>
    </>
  );
};

export default RestrictedCustomers;
