import PropTypes from 'prop-types';
import { useDispatch } from 'react-redux';
import moment from 'moment';

// actions
import { updateSupplierFlags } from 'actions/supplierActions';

const MenuReminder = ({ supplier, isSearchable, menuLastUpdated, setMenuLastUpdated }) => {
  const dispatch = useDispatch();

  const MarkMenuAsUpdated = (event) => {
    const today = new Date();
    const updatedSupplier = {
      id: supplier.id,
      menu_last_updated_on: today
    };
    updateSupplierFlags({
      supplier: updatedSupplier,
      dispatch,
    })
      .then((response) => {
        setMenuLastUpdated(today)
      })
      .catch((error) => {
        // do nothing
      });
  };

  let nextReminder;
  if (menuLastUpdated) {
    nextReminder = moment(menuLastUpdated).add(1, 'month').toDate();
    while (nextReminder < new Date()) {
      nextReminder = moment(nextReminder).add(1, 'month').toDate();
    }
    switch(supplier.menu_reminder_frequency) {
    case 'monthly':
      // do nothing
      break;
    case '3.months':
      while (nextReminder.getMonth() % 3 != menuLastUpdated.getMonth() % 3) {
        nextReminder = moment(nextReminder).add(1, 'month').toDate();
      }
      break;
    case '6.months':
      while (nextReminder.getMonth() % 6 != menuLastUpdated.getMonth() % 6) {
        nextReminder = moment(nextReminder).add(1, 'month').toDate();
      }
      break;
    default:
      break;
    }
  }

  return (
    <div className='text-center'>
      <div className='between-flex'>
        {!!menuLastUpdated && (
          <label style={{ marginRight: '0.5rem' }}>Menu last updated: <strong>{moment(menuLastUpdated).format('ddd Do, MMM YYYY')}</strong></label>
        )}
        {!moment().isSame(menuLastUpdated, 'day') && (
          <a
            className='button tiny hollow mb-0'
            onClick={MarkMenuAsUpdated}
            style={!menuLastUpdated ? { margin: 'auto' } : {}}
          >
            Mark as Updated
          </a>
        )}
      </div>
      {!!menuLastUpdated && <small>(next reminder on {moment(nextReminder).format('Do, MMM YYYY')})</small>}
      {!menuLastUpdated && <small>(marking will start reminders)</small>}
    </div>
  )
}

MenuReminder.propTypes = {
  supplier: PropTypes.object.isRequired,
  isSearchable: PropTypes.bool.isRequired,
  // menuLastUpdated: PropTypes.date,
  setMenuLastUpdated: PropTypes.func.isRequired,
}

export default MenuReminder;