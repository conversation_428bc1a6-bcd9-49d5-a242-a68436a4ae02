
import PropTypes from 'prop-types';

const MenuItemPreference = ({ preference, menuItem, handlePreferenceChange }) => (
  <div>
    <label>
      <input
        type="checkbox"
        name={preference.field}
        checked={!!menuItem[preference.field]}
        onChange={handlePreferenceChange}
      />
      {preference.label}
    </label>
  </div>
);

MenuItemPreference.propTypes = {
  preference: PropTypes.object.isRequired,
  menuItem: PropTypes.object.isRequired,
  handlePreferenceChange: PropTypes.func.isRequired,
};

export default MenuItemPreference;
