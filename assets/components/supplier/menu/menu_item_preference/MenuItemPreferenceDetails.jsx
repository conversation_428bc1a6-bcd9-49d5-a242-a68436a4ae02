
import PropTypes from 'prop-types';

// delayHide on ReactTooltip to prevent drag preview from getting confused due to zIndex and absolute positioning of tooltip
const MenuItemPreferenceDetails = ({ menu_item }) =>
  menu_item.dietary_preferences.map((preference) => (
    <span
      key={`menu-item-${menu_item.id}-dietary-preference-${preference.letter}`}
      className={`letter-icon ml-1-4 ${preference.field}`}
      data-tip={preference.label}
      title={preference.label}
    >
      {preference.letter}
    </span>
  ));

MenuItemPreferenceDetails.propTypes = {
  menu_item: PropTypes.object.isRequired,
};

export default MenuItemPreferenceDetails;
