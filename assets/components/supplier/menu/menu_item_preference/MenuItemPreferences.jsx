import PropTypes from 'prop-types';

// components
import MenuItemPreference from './MenuItemPreference';

const MenuItemPreferences = ({ menuItem, setMenuItem, title, preferences }) => {
  const handlePreferenceChange = (event) => {
    if (event.target.name === 'team_order_only' && event.target.checked && menuItem.team_order) {
      setMenuItem({ ...menuItem, team_order: false, [event.target.name]: event.target.checked });
    } else if (event.target.name === 'team_order' && event.target.checked && menuItem.team_order_only) {
      setMenuItem({ ...menuItem, team_order_only: false, [event.target.name]: event.target.checked });
    } else {
      setMenuItem({ ...menuItem, [event.target.name]: event.target.checked });
    }
  };

  return (
    <div>
      {!!title && <p className="menu-item-preferences-label">{title}</p>}
      <div className="menu-item-preferences-section">
        {preferences.map((preference) => (
          <MenuItemPreference
            key={`menu-item-${menuItem.id}-preference-${preference.field}`}
            preference={preference}
            menuItem={menuItem}
            handlePreferenceChange={handlePreferenceChange}
          />
        ))}
      </div>
    </div>
  );
};

MenuItemPreferences.propTypes = {
  menuItem: PropTypes.object.isRequired,
  setMenuItem: PropTypes.func.isRequired,
};

export default MenuItemPreferences;
