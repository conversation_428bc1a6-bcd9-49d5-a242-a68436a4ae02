import { useEffect, useContext } from 'react';
import { useDispatch } from 'react-redux';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

// components
import { fetchMenu, fetchCompanies } from 'actions/menuActions';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';
import SupplierSearchableForm from './SupplierSearchableForm';
import SupplierMarkups from './SupplierMarkups';
import MenuBanner from './MenuBanner';
import MenuSearch from './MenuSearch';
import AsyncMenuSearch from './AsyncMenuSearch';
import MenuSectionList from './menu_section/MenuSectionList';
import { ToastContainer } from 'react-toastify';

// actions

const MenuApp = () => {
  const {
    showArchived,
    supplier: { id, image, is_woolworths: isWoolworths },
  } = useContext(menuContext);
  const { isAdmin, canManageMenu } = useContext(adminContext);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchData = async () => {
      if (!isWoolworths) {
        await fetchMenu({
          supplierID: id,
          showArchived,
          dispatch,
        });
      }
      if (isAdmin) {
        fetchCompanies({
          dispatch,
        });
      }
    };

    fetchData(isAdmin);
  }, []);

  return (
    <>
      <MenuBanner image={image} />
      <div className="supplier-menu-admin between-flex">
        {!!canManageMenu && <SupplierSearchableForm />}
        {!!isAdmin && <SupplierMarkups />}
      </div>
      {isWoolworths ? <AsyncMenuSearch /> : <MenuSearch />}
      <DndProvider backend={HTML5Backend}>
        <MenuSectionList />
      </DndProvider>
      <ToastContainer />
    </>
  );
};

export default MenuApp;
