import { useContext } from 'react';

import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';
import { supplierMenuPath } from 'routes';
import MenuArchive from './MenuArchive';
import MenuExport from './MenuExport';

const MenuBanner = () => {
  const {
    showArchived,
    supplier: { 
      name,
      image,
      id,
      team_supplier,
      preview_url: previewUrl,
      team_preview_url: teamPreviewUrl,
    },
  } = useContext(menuContext);
  const { isAdmin } = useContext(adminContext);

  return (
    <>
      <div style={{ position: 'relative', width: '100%', height: '100px', marginBottom: '20px' }}>
        <img
          alt="hello"
          src={`http://res.cloudinary.com/yordar-p/image/upload/c_fill,q_auto,fl_lossy,f_auto/${image}`}
          style={{ position: 'absolute', inset: 0, width: '100%', height: '100%', objectFit: 'cover' }}
        />
      </div>
      <div className="between-flex">
        <h3 style={{ fontWeight: 'bold' }}>
          {name}
          {isAdmin && showArchived && <small> - also showing archived items</small>}
        </h3>
        <div>
          {isAdmin && !showArchived && (
            <a className="mr-1" href={`${supplierMenuPath()}?show_archived=true`} rel="noreferrer">
              <u>Show Archived Items</u>
            </a>
          )}
          <a target="_blank" className="button small tiny rounded" href={previewUrl} rel="noreferrer">
            Preview Menu
          </a>
          {team_supplier && (
            <a
              target="_blank"
              className="ml-1-2 button small tiny rounded"
              href={teamPreviewUrl}
              rel="noreferrer"
            >
              Team Order Menu
            </a>
          )}
          {!!isAdmin && (
            <>
              <MenuExport />
              <MenuArchive />
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default MenuBanner;
