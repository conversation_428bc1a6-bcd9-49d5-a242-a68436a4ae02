import { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { useDrag, useDrop } from 'react-dnd';
import {
  createMenuExtraSection,
  updateMenuExtraSection,
  removeMenuExtraSection,
} from 'actions/menuExtraSectionActions';

// DnD
import { dragConfig, dropConfig } from 'utilities/drag_and_drop';
import { DRAG_MENU_EXTRA_SECTION_SUCCESS } from 'actions/menuActionTypes';

// loader
import { createLoadingSelector } from 'selectors';

// component
import MenuExtraList from '../menu_extra/MenuExtraList';

const updateSelector = createLoadingSelector(['UPDATE_MENU_EXTRA_SECTION', 'REMOVE_MENU_EXTRA_SECTION']);
const createSelctor = createLoadingSelector(['CREATE_MENU_EXTRA_SECTION']);

const MenuExtraSectionForm = ({
  menuItem,
  menu_extra_section,
  index,
  removeNewMenuExtraSection,
  sortMenuExtraSections,
}) => {
  const dragRef = useRef(null);
  const previewRef = useRef(null);
  const inputRef = useRef(null);

  const loadingState = useSelector((state) => state.loading);
  const isUpdating = updateSelector(loadingState, menu_extra_section.id);
  const isCreating = createSelctor(loadingState);
  const readOnly = isUpdating || isCreating;
  const dispatch = useDispatch();

  const [menuExtraSection, setMenuExtraSection] = useState(menu_extra_section);

  useEffect(() => {
    if (menuExtraSection.is_new) inputRef.current.focus();
  }, [inputRef.current]);

  useEffect(() => {
    setMenuExtraSection(menu_extra_section);
  }, [menu_extra_section]);

  const handleChange = (event) => {
    const value = event.target.value === '' ? null : event.target.value;
    setMenuExtraSection({ ...menuExtraSection, is_updated: true, [event.target.name]: value });
  };

  const handleBlur = () => {
    if (menuExtraSection.is_new) return;

    submitMenuExtraSection();
  };

  // submit menu extra on enter
  const handleSubmit = (event) => {
    if (event.which === 13 || event.keyCode === 13) submitMenuExtraSection();
  };

  const submitMenuExtraSection = () => {
    if (isUpdating || isCreating) return;

    if (menuExtraSection.is_new) {
      dispatch(
        createMenuExtraSection({
          menuExtraSection,
        })
      );
    } else if (menuExtraSection.is_updated) {
      dispatch(
        updateMenuExtraSection({
          menuExtraSection,
        })
      );
    }
  };

  const deleteMenuExtraSection = () => {
    if (isUpdating) return;

    dispatch(
      removeMenuExtraSection({
        menuExtraSection,
      })
    );
  };

  const [{ isDragging }, drag, preview] = useDrag(
    dragConfig({
      type: 'MenuExtraSectionForm',
      item: menuExtraSection,
      index,
      canDrag: !isUpdating && !isCreating,
    })
  );

  const [{ handlerId }, drop] = useDrop(
    dropConfig({
      accepts: 'MenuExtraSectionForm',
      itemRef: previewRef,
      index,
      dispatch,
      dragAction: { type: DRAG_MENU_EXTRA_SECTION_SUCCESS, payload: menuExtraSection },
      sortAction: sortMenuExtraSections,
    })
  );

  useEffect(() => {
    drag(dragRef);
    drop(preview(previewRef));
  }, [dragRef, [previewRef]]);

  let sectionStyle = {};
  if (!menuExtraSection.is_new) {
    sectionStyle = { ...sectionStyle, borderBottom: '1px dotted #eee' };
  }
  if (isDragging) {
    sectionStyle = { ...sectionStyle, opacity: 0.1 };
  }

  return (
    <div className="mt-1">
      <div className="row" style={sectionStyle} ref={previewRef} data-handler-id={handlerId}>
        <span className={`drag-handle mt-2 ${!menuExtraSection.is_new ? ' drag-reorder-icon' : ''}`} ref={dragRef}>
          &nbsp;
        </span>
        <div className="small-12 medium-4 columns no-gutter">
          <label>Name</label>
          <input
            ref={inputRef}
            readOnly={readOnly}
            placeholder="Name"
            className="menu-item-extra-section"
            type="text"
            name="name"
            value={menuExtraSection.name || ''}
            onChange={handleChange}
            onBlur={handleBlur}
            onKeyUp={handleSubmit}
          />
        </div>
        <div className="small-12 medium-2 columns">
          <label>Min Limit</label>
          <input
            readOnly={readOnly}
            placeholder="Min Limit"
            className="menu-item-extra-section"
            type="number"
            name="min_limit"
            value={menuExtraSection.min_limit || ''}
            onChange={handleChange}
            onBlur={handleBlur}
            onKeyUp={handleSubmit}
          />
        </div>
        <div className="small-12 medium-2 columns">
          <label>Max Limit</label>
          <input
            readOnly={readOnly}
            placeholder="Max Limit (blank - unlimited)"
            className="menu-item-extra-section"
            type="number"
            name="max_limit"
            value={menuExtraSection.max_limit || ''}
            onChange={handleChange}
            onBlur={handleBlur}
            onKeyUp={handleSubmit}
          />
        </div>
        <div className="small-12 medium-2 columns">
          <label>&nbsp;</label>
          {!isUpdating && !menuExtraSection.is_new && (
            <a
              tabIndex="0"
              className="delete-icon ml-1-4"
              onClick={deleteMenuExtraSection}
              style={{ marginLeft: '12px' }}
            >
              Delete
            </a>
          )}

          {menuExtraSection.is_new && isCreating && <span>creating...</span>}
          {menuExtraSection.is_new && !isCreating && (
            <a tabIndex="0" className="button tiny" onClick={submitMenuExtraSection}>
              create
            </a>
          )}
          {menuExtraSection.is_new && !isCreating && (
            <a tabIndex="0" className="ml-1-4" onClick={() => removeNewMenuExtraSection(menuExtraSection)}>
              cancel
            </a>
          )}
        </div>
      </div>

      <div style={{ marginLeft: '20px' }}>
        {!isDragging && !menuExtraSection.is_new && (
          <MenuExtraList
            menuItem={menuItem}
            menuExtraSection={menuExtraSection}
            setMenuExtraSection={setMenuExtraSection}
          />
        )}
      </div>
    </div>
  );
};

MenuExtraSectionForm.propTypes = {
  menuItem: PropTypes.object.isRequired,
  menu_extra_section: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  removeNewMenuExtraSection: PropTypes.func.isRequired,
  sortMenuExtraSections: PropTypes.func.isRequired,
};

export default MenuExtraSectionForm;
