import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { sortItemsOnDrop } from 'utilities/drag_and_drop';
import { updateMenuExtraSection } from 'actions/menuExtraSectionActions';

// components
import MenuExtraSectionForm from './MenuExtraSectionForm';

const MenuExtraSectionList = ({ menuItem, setMenuItem }) => {
  const dispatch = useDispatch();

  const addNewMenuExtraSection = () => {
    const defaultName = menuItem.menu_extra_sections.length
      ? `Section ${menuItem.menu_extra_sections.length + 1}`
      : 'Add Extra(s)';
    const newMenuExtraSection = {
      id: `new-menu-extra-section-${Math.floor(Math.random() * 1000)}`,
      name: defaultName,
      min_limit: '',
      max_limit: '',
      menu_item_id: menuItem.id,
      is_new: true,
    };
    setMenuItem({ ...menuItem, menu_extra_sections: [...menuItem.menu_extra_sections, newMenuExtraSection] });
  };

  const removeNewMenuExtraSection = (menuExtraSection) => {
    setMenuItem({
      ...menuItem,
      menu_extra_sections: menuItem.menu_extra_sections.filter(
        (menu_extra_section) => menu_extra_section.id !== menuExtraSection.id
      ),
    });
  };

  const sortMenuExtraSections = useCallback(() => {
    sortItemsOnDrop({
      items: menuItem.menu_extra_sections,
      itemName: 'menuExtraSection',
      action: updateMenuExtraSection,
      dispatch,
    });
  }, [menuItem.menu_extra_sections]);

  return (
    <div className="mt-2">
      <h3 className="menu-item-section-title">Item Extras</h3>
      {!!menuItem.menu_extra_sections.length &&
        menuItem.menu_extra_sections.map((menu_extra_section, index) => (
          <MenuExtraSectionForm
            key={`menu-extra-${menu_extra_section.id}`}
            menuItem={menuItem}
            menu_extra_section={menu_extra_section}
            index={index}
            removeNewMenuExtraSection={removeNewMenuExtraSection}
            sortMenuExtraSections={sortMenuExtraSections}
          />
        ))}
      {!menuItem.menu_extra_sections.some((section) => section.is_new) && (
        <a className="add-new-menu-option" onClick={addNewMenuExtraSection}>
          Add Menu Extra Section
        </a>
      )}
    </div>
  );
};

MenuExtraSectionList.propTypes = {
  menuItem: PropTypes.object.isRequired,
  setMenuItem: PropTypes.func.isRequired,
};

export default MenuExtraSectionList;
