import { useState } from 'react';
import PropTypes from 'prop-types';

// components
import NewMenuItemForm from './NewMenuItemForm';

const NewMenuItem = ({ menu_section }) => {
  const [isNewMenuItem, setIsNewMenuItem] = useState(false);

  if (isNewMenuItem) {
    return <NewMenuItemForm menu_section={menu_section} setIsNewMenuItem={setIsNewMenuItem} />;
  }

  return (
    <li className="menu-item-item new-menu-item">
      <a onClick={() => setIsNewMenuItem(true)}>+ Add new menu item</a>
    </li>
  );
};

NewMenuItem.propTypes = {
  menu_section: PropTypes.object.isRequired,
};

export default NewMenuItem;
