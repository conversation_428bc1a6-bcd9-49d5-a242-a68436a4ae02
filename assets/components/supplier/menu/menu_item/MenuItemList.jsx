import { useCallback, useContext } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { sortItemsOnDrop } from 'utilities/drag_and_drop';
import { updateMenuItem } from 'actions/menuItemActions';
import adminContext from 'contexts/adminContext';

// components
import MenuItem from './MenuItem';
import NewMenuItem from './NewMenuItem';

const MenuItemList = ({ menu_section }) => {
  const { menu_items } = menu_section;
  const { canManageMenu } = useContext(adminContext);
  const dispatch = useDispatch();

  const sortMenuItems = useCallback(() => {
    sortItemsOnDrop({
      items: menu_items,
      itemName: 'menuItem',
      action: updateMenuItem,
      dispatch,
    });
  }, [menu_section.menu_items]);

  return (
    <ul className="list-unstyled section-menu-item-list">
      {menu_items.map((menu_item, index) => (
        <MenuItem key={`menu-item-${menu_item.id}`} menu_item={menu_item} index={index} sortMenuItems={sortMenuItems} />
      ))}
      {canManageMenu && <NewMenuItem menu_section={menu_section} />}
    </ul>
  );
};

MenuItemList.propTypes = {
  menu_section: PropTypes.object.isRequired,
};

export default MenuItemList;
