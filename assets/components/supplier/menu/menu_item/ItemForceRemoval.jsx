import PropTypes from 'prop-types';

// components
import { Modal } from 'react-responsive-modal';

const ItemForceRemoval = ({ forcedRemovalWarning, setForcedRemovalWarning, menu_item, removeItem, setCheckUsage }) => (
  <Modal
    open
    onClose={() => setForcedRemovalWarning(false)}
    center
    showCloseIcon={false}
    styles={{ modal: { maxWidth: '500px' } }}
  >
    <h2>
      <small>Force Archive</small> `{menu_item.name}`
    </h2>
    <p>
      <strong className="text-alert">Warning!</strong> <em>{forcedRemovalWarning}</em>
    </p>
    <p>
      Are you sure you want to <strong>force</strong> remove/archive this menu item?
    </p>
    <a className="button gray-btn small" onClick={() => setForcedRemovalWarning(false)}>
      Cancel
    </a>
    <a
      className="button small ml-1"
      onClick={() => {
        removeItem(true);
        setForcedRemovalWarning(null);
      }}
    >
      Force Archive
    </a>
    <a className="button small float-right hollow" onClick={() => setCheckUsage(true)}>
      Check Usage
    </a>
  </Modal>
);

ItemForceRemoval.propTypes = {
  forcedRemovalWarning: PropTypes.string.isRequired,
  setForcedRemovalWarning: PropTypes.func.isRequired,
  menu_item: PropTypes.object.isRequired,
  removeItem: PropTypes.func.isRequired,
  setCheckUsage: PropTypes.func.isRequired,
};

export default ItemForceRemoval;
