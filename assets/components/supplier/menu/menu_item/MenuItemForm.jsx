import { useState, useRef, useEffect, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { updateMenuItem } from 'actions/menuItemActions';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';

// loader
import { createLoadingSelector } from 'selectors';

// components
import MenuItemPreferences from '../menu_item_preference/MenuItemPreferences';
import MenuItemImage from '../menu_item_image/MenuItemImage';
import MenuExtraSectionList from '../menu_extra_section/MenuExtraSectionList';
import ServingSizeList from '../serving_size/ServingSizeList';
import RateCardList from '../rate_card/RateCardList';
import MenuItemPrice from './MenuItemPrice';
import MenuItemPromoPrice from './MenuItemPromoPrice';

const loadingSelector = createLoadingSelector(['UPDATE_MENU_ITEM']);

const MenuItemForm = ({ menu_item, setEditMenuItem, setCheckUsage }) => {
  const {
    supplier: { is_woolworths: isWoolworths, has_skus: hasSkus },
    itemPreferences: { dietary: dietaryPreferences, misc: miscPreferences },
  } = useContext(menuContext);
  const { isAdmin } = useContext(adminContext);
  const loadingState = useSelector((state) => state.loading);
  const isUpdating = loadingSelector(loadingState, menu_item.id);
  const dispatch = useDispatch();

  const [menuItem, setMenuItem] = useState(menu_item);

  const firstInputRef = useRef(null);
  useEffect(() => {
    firstInputRef.current.focus();
  }, [firstInputRef.current]);

  const descriptionRef = useRef(null);
  useEffect(() => {
    if (descriptionRef && descriptionRef.current) {
      descriptionRef.current.style.height = 'auto';
      descriptionRef.current.style.height = `${descriptionRef.current.scrollHeight}px`;
    }
  }, [menuItem.description]);

  useEffect(() => {
    setMenuItem({ ...menuItem, image: menu_item.image });
  }, [menu_item.image]);

  useEffect(() => {
    setMenuItem({ ...menuItem, menu_extra_sections: menu_item.menu_extra_sections });
  }, [menu_item.menu_extra_sections]);

  useEffect(() => {
    setMenuItem({ ...menuItem, serving_sizes: menu_item.serving_sizes });
  }, [menu_item.serving_sizes]);

  useEffect(() => {
    setMenuItem({ ...menuItem, rate_cards: menu_item.rate_cards });
  }, [menu_item.rate_cards]);

  useEffect(() => {
    if (menu_item.is_cloned) {
      const menuItemForm = document.getElementById(`menu-item-form-${menu_item.id}`);
      window.scrollTo({ top: menuItemForm.offsetTop, behavior: 'smooth' });
    }
  }, [menu_item.is_cloned]);

  const handleChange = (event) => {
    const field = event.target.name;
    let { value } = event.target;
    if (field === 'stock_quantity' && value === '') value = null;
    if (field === 'description') value = value.replace(/\n|\r/g, '');
    setMenuItem((state) => ({ ...state, [field]: value }));
  };

  const handleSubmit = () => {
    if (isUpdating) return;

    dispatch(
      updateMenuItem({
        menuItem,
      })
    );
  };

  const hasServingSizes = menuItem.serving_sizes.length !== 0;
  const itemRateCards = menuItem.rate_cards.filter((rateCard) => !rateCard.serving_size_id);
  const [canManageRateCards, setCanManageRateCards] = useState(itemRateCards.length === 0 && !hasServingSizes);

  return (
    <div id={`menu-item-form-${menu_item.id}`} className="menu-item-form edit-menu-item">
      <h3 className="menu-item-section-title">{menu_item.name}</h3>
      <div className="menu-item-details">
        <div className="row list-flex-1" style={{ marginRight: '20px' }}>
          <div className="small-8 columns no-gutter">
            <label>Name</label>
            <input
              ref={firstInputRef}
              required="required"
              placeholder="Name"
              className="form-input"
              type="text"
              name="name"
              value={menuItem.name}
              onChange={handleChange}
            />
          </div>
          <div className="small-4 columns">
            <label>Min Qty</label>
            <input
              placeholder="Minimum Quantity"
              className="form-input"
              type="number"
              name="minimum_quantity"
              value={menuItem.minimum_quantity || ''}
              onChange={handleChange}
            />
          </div>
          <div className="row">
            <div className={`columns ${hasSkus ? 'small-8' : 'small-12'} no-gutter`}>
              <label>
                Description <small>(keep description in one line only)</small>
              </label>
              <textarea
                ref={descriptionRef}
                placeholder="Description"
                className="form-input"
                type="text"
                name="description"
                value={menuItem.description || ''}
                onChange={handleChange}
                style={{ minHeight: '110px' }}
              />
            </div>
            {hasSkus && (
              <div className="medium-4 columns">
                <label>SKU</label>
                <input
                  placeholder="SKU/Item Code"
                  className="form-input"
                  type="text"
                  name="sku"
                  value={menuItem.sku || ''}
                  onChange={handleChange}
                />

                <label>Stock Qty</label>
                <input
                  placeholder="In Stock quantity"
                  className="form-input"
                  type="text"
                  name="stock_quantity"
                  value={menuItem.stock_quantity === null ? '' : menuItem.stock_quantity}
                  onChange={handleChange}
                />
              </div>
            )}
          </div>
        </div>
        <MenuItemImage menuItem={menuItem} setMenuItem={setMenuItem} />
      </div>

      <div className="list-flex-2 menu-item-preferences">
        <MenuItemPreferences
          menuItem={menuItem}
          setMenuItem={setMenuItem}
          title="Dietary"
          preferences={dietaryPreferences}
        />
        <MenuItemPreferences menuItem={menuItem} setMenuItem={setMenuItem} title="Misc" preferences={miscPreferences} />
      </div>

      <h3 className="menu-item-section-title">Pricing & Servings</h3>

      {!menuItem.serving_sizes.length && <MenuItemPrice menuItem={menuItem} setMenuItem={setMenuItem} />}
      {isWoolworths && !!menuItem.price && <MenuItemPromoPrice menuItem={menuItem} setMenuItem={setMenuItem} />}

      {(!menuItem.price || hasServingSizes) && (
        <ServingSizeList
          menuItem={menuItem}
          setMenuItem={setMenuItem}
          canManageRateCards={canManageRateCards}
          setCanManageRateCards={setCanManageRateCards}
        />
      )}

      <MenuExtraSectionList menuItem={menuItem} setMenuItem={setMenuItem} />

      {!!isAdmin && (!!itemRateCards.length || !hasServingSizes) && (
        <div className="mt-2">
          <h3 className="menu-item-section-title">
            Rate Cards
            {hasServingSizes && <small className="ml-1-2">(please archive these item based rate cards)</small>}
            {!!itemRateCards.length && (
              <>
                <a className="ml-2" onClick={() => setCanManageRateCards(!canManageRateCards)}>
                  {canManageRateCards ? 'Hide Rate Cards' : 'Manage Rate Cards'}
                </a>
                <small className="ml-1-4">({itemRateCards.length})</small>
              </>
            )}
          </h3>
          {canManageRateCards && (
            <RateCardList menuItem={menuItem} setMenuItem={setMenuItem} rateCards={itemRateCards} />
          )}
        </div>
      )}
      <div className="clearfix">
        <div className="small-12 columns no-gutter mt-1">
          <a
            className="button small gray-btn cancel"
            onClick={() => {
              !isUpdating && setEditMenuItem(false);
            }}
          >
            CLOSE
          </a>
          <a className="button small ml-1-2" onClick={handleSubmit}>
            {isUpdating ? 'SAVING...' : 'SAVE & CLOSE'}
          </a>
          {!!isAdmin && (
            <a className="button small ml-1-2 hollow float-right" onClick={() => setCheckUsage(true)}>
              Check Usage
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

MenuItemForm.propTypes = {
  menu_item: PropTypes.object.isRequired,
  setEditMenuItem: PropTypes.func.isRequired,
  setCheckUsage: PropTypes.func.isRequired,
};

export default MenuItemForm;
