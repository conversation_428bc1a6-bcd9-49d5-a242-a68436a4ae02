import { useRef, useState, useEffect, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDrag, useDrop } from 'react-dnd';
import PropTypes from 'prop-types';

// actions
import { removeMenuItem, cloneMenuItem } from 'actions/menuItemActions';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';

// DnD
import { dragConfig, dropConfig } from 'utilities/drag_and_drop';
import { DRAG_MENU_ITEM_SUCCESS } from 'actions/menuActionTypes';

// loader
import { createLoadingSelector } from 'selectors';

// components
import RateCardCompanies from '../rate_card/RateCardCompanies';
import MenuItemPreferenceDetails from '../menu_item_preference/MenuItemPreferenceDetails';
import ServingSizeDetails from '../serving_size/ServingSizeDetails';
import MenuItemForm from './MenuItemForm';
import ItemForceRemoval from './ItemForceRemoval';
import MenuItemUsage from './MenuItemUsage';

const updateLoadingSelector = createLoadingSelector(['UPDATE_MENU_ITEM', 'REMOVE_MENU_ITEM']);
const cloneLoadingSelector = createLoadingSelector(['CLONE_MENU_ITEM']);

const MenuItem = ({ menu_item, index, sortMenuItems }) => {
  const dragRef = useRef(null);
  const previewRef = useRef(null);
  const [editMenuItem, setEditMenuItem] = useState(!!menu_item.is_new);
  const [forcedRemovalWarning, setForcedRemovalWarning] = useState(null);
  const [checkUsage, setCheckUsage] = useState(false);

  const { supplier } = useContext(menuContext);
  const { canManageMenu, isAdmin } = useContext(adminContext);
  const loadingState = useSelector((state) => state.loading);
  const isUpdating = updateLoadingSelector(loadingState, menu_item.id);
  const isCloning = cloneLoadingSelector(loadingState, menu_item.id);
  const dispatch = useDispatch();

  useEffect(() => {
    if (menu_item.is_searched) {
      const menuItemEl = document.getElementById(`menu-item-${menu_item.id}`);
      window.scrollTo({ top: menuItemEl.offsetTop, behavior: 'smooth' });
    }
  }, [menu_item.is_searched]);

  // close form on Save and Close
  useEffect(() => {
    if (editMenuItem && menu_item.is_updated) setEditMenuItem(false);
  }, [menu_item.is_updated]);

  // scroll to menu item when form is closed and item is updated with Save and Close
  useEffect(() => {
    if (!editMenuItem && menu_item.is_updated) {
      const menuItemEl = document.getElementById(`menu-item-${menu_item.id}`);
      window.scrollTo({ top: menuItemEl.offsetTop, behavior: 'smooth' });
      menu_item.is_updated = false; // reset
    }
  }, [editMenuItem]);

  const removeItem = (isForced) => {
    removeMenuItem({
      menuItem: menu_item,
      isForced,
      dispatch,
    }).catch((error) => {
      if (error.response.status === 422) {
        const { errors } = error.response.data;
        const { warnings } = error.response.data;
        if (!isForced && canManageMenu && warnings) {
          setForcedRemovalWarning(warnings.join('. '));
        } else if (warnings) {
          alert(warnings.join('. '));
        } else if (errors) {
          alert(errors.join('.'));
        }
      } else {
        alert('Something went wrong');
      }
    });
  };

  const cloneItem = () => {
    if (isCloning) return;

    dispatch(
      cloneMenuItem({
        menuItem: menu_item,
      })
    );
  };

  const [{ isDragging }, drag, preview] = useDrag(
    dragConfig({
      type: 'MenuItem',
      item: menu_item,
      index,
      canDrag: !isUpdating && !isCloning && !editMenuItem,
    })
  );

  const [{ handlerId }, drop] = useDrop(
    dropConfig({
      accepts: 'MenuItem',
      itemRef: previewRef,
      index,
      dispatch,
      dragAction: { type: DRAG_MENU_ITEM_SUCCESS, payload: menu_item },
      sortAction: sortMenuItems,
    })
  );

  useEffect(() => {
    drag(dragRef);
    drop(preview(previewRef));
  }, [dragRef, [previewRef]]);

  if (editMenuItem) {
    return (
      <li className="menu-item-item">
        <MenuItemForm menu_item={menu_item} setEditMenuItem={setEditMenuItem} setCheckUsage={setCheckUsage} />
        {checkUsage && <MenuItemUsage menu_item={menu_item} setCheckUsage={setCheckUsage} />}
      </li>
    );
  }

  return (
    <li
      className="menu-item-item"
      id={`menu-item-${menu_item.id}`}
      style={isDragging ? { opacity: 0.1 } : {}}
      ref={previewRef}
      data-handler-id={handlerId}
    >
      <div className={`menu-item-detail ${isUpdating && 'updating'}`}>
        <div className="list-flex-4 menu-item-name-image">
          {canManageMenu && <span ref={dragRef} className="drag-reorder-icon mr-1" />}
          <a>
            {menu_item?.logo ? (
              <img src={menu_item.logo} className="menu-item-image" />
            ) : (
              <span className="menu-item-image no-image">{menu_item.name[0]}</span>
            )}
          </a>
          <div>
            <span className="menu-item-name">{menu_item.name}</span>
            {!!menu_item.dietary_preferences.length && <MenuItemPreferenceDetails menu_item={menu_item} />}
            {!!supplier.has_skus && !!menu_item.sku && <small> (SKU: {menu_item.sku})</small>}
            {!!supplier.has_skus && menu_item.stock_quantity !== null && (
              <small> ({menu_item.stock_quantity} in stock)</small>
            )}
            {!!menu_item.archived_at && <small className="ml-1-2 label warning">Used while ARCHIVED</small>}
            <p style={{ margin: 0, marginRight: '4px' }}>
              {!!menu_item.description && (
                <>
                  {menu_item.description}
                  <br />
                </>
              )}
              Min Qty: {menu_item.minimum_quantity}
            </p>
          </div>
        </div>
        <div className="list-flex-2 text-right mr-1">
          {!menu_item.serving_sizes.length && <strong className="mr-1-2">${menu_item.price}</strong>}
          {!!menu_item.serving_sizes.length && <ServingSizeDetails menu_item={menu_item} />}
          {!!menu_item.menu_extra_sections.length && !!menu_item.menu_extra_sections[0].menu_extras.length && (
            <small>+ extras</small>
          )}
          {!!isAdmin && !!menu_item.rate_cards.length && (
            <RateCardCompanies menu_item={menu_item} rate_cards={menu_item.rate_cards} />
          )}
        </div>
        <div className="list-flex-1 actions text-center">
          {isUpdating && 'processing....'}
          {!isUpdating && canManageMenu && (
            <>
              <div className={`${isAdmin ? 'small-4' : 'small-6'} columns`}>
                <a className="edit-icon" title="Edit Item" onClick={() => setEditMenuItem(!editMenuItem)} />
              </div>
              <div className={`${isAdmin ? 'small-4' : 'small-6'} columns`}>
                <a
                  className="delete-icon"
                  title="Remove Item"
                  onClick={(event) => {
                    event.stopPropagation();
                    removeItem(false);
                  }}
                />
              </div>
              {!!isAdmin && (
                <div className="small-4 columns">
                  <a
                    className="icon-usage"
                    title="Check Usage"
                    onClick={(event) => {
                      event.stopPropagation();
                      setCheckUsage(true);
                    }}
                  />
                </div>
              )}
              {!supplier.is_woolworths && (
                  <a className="clone-icon mt-1-2 small-12 columns button tiny border-black" onClick={cloneItem}>
                    {isCloning ? 'Cloning...' : 'Clone'}
                </a>
              )}
            </>
          )}
        </div>
      </div>
      {forcedRemovalWarning && (
        <ItemForceRemoval
          menu_item={menu_item}
          removeItem={removeItem}
          forcedRemovalWarning={forcedRemovalWarning}
          setForcedRemovalWarning={setForcedRemovalWarning}
          setCheckUsage={setCheckUsage}
        />
      )}
      {checkUsage && <MenuItemUsage menu_item={menu_item} setCheckUsage={setCheckUsage} />}
    </li>
  );
};

MenuItem.propTypes = {
  menu_item: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  sortMenuItems: PropTypes.func.isRequired,
};

export default MenuItem;
