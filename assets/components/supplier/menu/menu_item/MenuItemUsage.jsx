import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Modal } from 'react-responsive-modal';
import PropTypes from 'prop-types';

// actions
import { checkMenuItemUsage } from 'actions/menuItemActions';
import { CLEAR_MENU_ITEM_ORDERS } from 'actions/menuActionTypes';

// loader
import { createLoadingSelector } from 'selectors';

const ordersLoadingSelector = createLoadingSelector(['MENU_ITEM_ORDERS']);

const MenuItemUsage = ({ menu_item, setCheckUsage }) => {
  const dispatch = useDispatch();

  const loadingState = useSelector((state) => state.loading);
  const isLoadingOrders = ordersLoadingSelector(loadingState, menu_item.id);

  const clearOrders = () => {
    dispatch({ type: CLEAR_MENU_ITEM_ORDERS, payload: menu_item });
    setCheckUsage(false);
  };

  useEffect(() => {
    if (isLoadingOrders || menu_item?.orders?.length) return;

    dispatch(
      checkMenuItemUsage({
        menuItem: menu_item,
      })
    );
  }, []);

  const orders = (!isLoadingOrders && menu_item.orders) || [];
  const ordersNotFound = orders && orders.length == 1 && !orders[0]?.id;

  return (
    <Modal classNames={{ modal: 'reveal customer-form' }} open onClose={clearOrders} center>
      <h3>
        Order(s) containing
        <br />`{menu_item.name}`
      </h3>
      {isLoadingOrders && <UsageLoadingSkeleton />}
      {!isLoadingOrders && ordersNotFound && (
        <p>
          The item is <strong>NOT USED</strong> in any recent/future orders.
        </p>
      )}
      {!isLoadingOrders && !ordersNotFound && (
        <>
          <p>
            We found <strong>{orders.length} recent/future orders</strong> containing this item!
          </p>
          <ul className="bullet-list">
            {orders.map((order) => (
              <ItemOrder key={`item-${menu_item.id}-order-${order?.id}`} order={order} />
            ))}
          </ul>
        </>
      )}
      {!isLoadingOrders && (
        <a className="button gray-btn small" onClick={clearOrders}>
          OK
        </a>
      )}
    </Modal>
  );
};

MenuItemUsage.propTypes = {
  menu_item: PropTypes.object.isRequired,
  setCheckUsage: PropTypes.func.isRequired,
};

export default MenuItemUsage;

const UsageLoadingSkeleton = () => (
  <ul>
    {Array.from(Array(3).keys()).map((num) => (
      <li key={`menu-item-usage-skeleton-${num}`} className="menu-skeleton mb-1 p-1" />
    ))}
  </ul>
);

const ItemOrder = ({ order }) => {
  if (!order?.id) return <h4>No Orders Found</h4>;

  return (
    <li className="mb-1-4">
      <a href={order.url} target="_blank" rel="noreferrer">
        {order.name} - #{order.id} - (Qty: {order.quantity})
      </a>
      <br />
      {order.company_name}
      <br />
      {order.delivery_at}
    </li>
  );
};
