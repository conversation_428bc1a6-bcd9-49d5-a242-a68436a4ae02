import { useState, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';

import { gstPricing } from 'utilities/pricingHelpers';
import { menuContext } from 'contexts/menuContext';

const MenuItemPrice = ({ menuItem, setMenuItem }) => {
  const { countryCode } = useContext(menuContext);
  const [priceWithoutGst, setPriceWithoutGst] = useState(menuItem.price || '');
  const [priceWithGst, setPriceWithGst] = useState(
    menuItem.is_gst_free ? menuItem.price : gstPricing({
      price: menuItem.price,
      inc_gst: true,
      countryCode
    })
  );

  useEffect(() => {
    setPriceWithGst(menuItem.is_gst_free ? priceWithoutGst : gstPricing({
      price: priceWithoutGst,
      inc_gst: true,
      countryCode
    }));
  }, [menuItem.is_gst_free]);

  useEffect(() => {
    setMenuItem({ ...menuItem, price: priceWithoutGst });
  }, [priceWithoutGst]);

  const handlePriceChange = (event) => {
    if (event.target.name === 'price') {
      const gstIncPrice = menuItem.is_gst_free ? event.target.value : gstPricing({
        price: event.target.value,
        inc_gst: true,
        countryCode
      });
      setPriceWithGst(gstIncPrice);
    } else {
      const gstExcPrice = menuItem.is_gst_free ? event.target.value : gstPricing({
        price: event.target.value,
        inc_gst: false,
        countryCode
      });
      setPriceWithoutGst(gstExcPrice);
    }
  };

  return (
    <div className="clearfix mt-1">
      <div className="small-12 medium-6 columns no-gutter">
        <label>Price {!menuItem.is_gst_free && <small>(exc gst)</small>}</label>
        <input
          className="form-input"
          type="text"
          name="price"
          value={priceWithoutGst}
          onBlur={handlePriceChange}
          onChange={(e) => setPriceWithoutGst(e.target.value)}
        />
      </div>
      {!menuItem.is_gst_free && (
        <div className="small-12 medium-6 columns no-gutter-small">
          <label>
            Price <small>(inc gst)</small>
          </label>
          <input
            className="form-input"
            type="text"
            name="price_inc_gst"
            value={priceWithGst}
            onBlur={handlePriceChange}
            onChange={(e) => setPriceWithGst(e.target.value)}
          />
        </div>
      )}
    </div>
  );
};

MenuItemPrice.propTypes = {
  menuItem: PropTypes.object.isRequired,
  setMenuItem: PropTypes.func.isRequired,
};

export default MenuItemPrice;
