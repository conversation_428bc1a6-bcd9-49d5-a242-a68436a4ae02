import { useState, useRef, useContext, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import { useDispatch, useSelector } from 'react-redux';
import { createMenuItem } from 'actions/menuItemActions';
import { menuContext } from 'contexts/menuContext';

// loader
import { createLoadingSelector } from 'selectors';

const loadingSelector = createLoadingSelector(['CREATE_MENU_ITEM']);

const NewMenuItemForm = ({ menu_section, setIsNewMenuItem }) => {
  const { supplier } = useContext(menuContext);
  const inputRef = useRef(null);
  const loadingState = useSelector((state) => state.loading);
  const isLoading = loadingSelector(loadingState);
  const dispatch = useDispatch();

  const initialState = {
    menu_section_id: menu_section.id,
    supplier_profile_id: supplier.id,
    name: '',
  };
  const [menuItem, setMenuItem] = useState(initialState);

  useEffect(() => {
    inputRef.current.focus();
  }, [inputRef.current]);

  const handleChange = (event) => {
    setMenuItem({ ...menuItem, [event.target.name]: event.target.value });
  };

  const handleSubmit = (event) => {
    if (event.which === 13 || event.keyCode === 13) submitItem();
  };

  const submitItem = () => {
    if (isLoading || !menuItem.name) return;

    createMenuItem({
      menuItem,
      dispatch,
    }).then(() => {
      setIsNewMenuItem(false);
    });
  };

  return (
    <li className="menu-item-item new-menu-item-form-container">
      <div className="pt-1 pl-1 pr-1 new-menu-item-form">
        <input
          ref={inputRef}
          required="required"
          placeholder="Menu item name"
          type="text"
          name="name"
          value={menuItem.name}
          onChange={handleChange}
          onKeyUp={handleSubmit}
        />
        <a className="button small" onClick={submitItem}>
          {isLoading ? 'SAVING...' : 'SAVE'}
        </a>
        <a
          className="button small gray-btn ml-1"
          onClick={() => {
            !isLoading && setIsNewMenuItem(false);
          }}
        >
          CANCEL
        </a>
      </div>
    </li>
  );
};

NewMenuItemForm.propTypes = {
  menu_section: PropTypes.object.isRequired,
  setIsNewMenuItem: PropTypes.func.isRequired,
};

export default NewMenuItemForm;
