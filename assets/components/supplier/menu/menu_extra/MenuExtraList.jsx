import { useContext, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { sortItemsOnDrop } from 'utilities/drag_and_drop';
import { updateMenuExtra } from 'actions/menuExtraActions';
import { menuContext } from 'contexts/menuContext';

// components
import MenuExtraForm from './MenuExtraForm';

const MenuExtraList = ({ menuItem, menuExtraSection, setMenuExtraSection }) => {
  const {
    supplier: { has_skus: hasSkus },
  } = useContext(menuContext);
  const dispatch = useDispatch();

  const addNewMenuExtra = () => {
    const newMenuExtra = {
      id: `new-menu-extra-${Math.floor(Math.random() * 1000)}`,
      name: '',
      price: '',
      menu_item_id: menuItem.id,
      menu_extra_section_id: menuExtraSection.id,
      is_new: true,
    };
    setMenuExtraSection({ ...menuExtraSection, menu_extras: [...menuExtraSection.menu_extras, newMenuExtra] });
  };

  const removeNewMenuExtra = (menuExtra) => {
    setMenuExtraSection({
      ...menuExtraSection,
      menu_extras: menuExtraSection.menu_extras.filter((menu_extra) => menu_extra.id != menuExtra.id),
    });
  };

  const sortMenuExtras = useCallback(() => {
    sortItemsOnDrop({
      items: menuExtraSection.menu_extras,
      itemName: 'menuExtra',
      action: updateMenuExtra,
      dispatch,
    });
  }, [menuExtraSection.menu_extras]);

  return (
    <>
      {!!menuExtraSection.menu_extras.length && (
        <div className="clearfix mt-1">
          <div
            className={`small-12 columns no-gutter ${hasSkus ? 'medium-3' : 'medium-4'}`}
            style={{ marginLeft: '24px' }}
          >
            <label>Name</label>
          </div>
          {hasSkus && (
            <div className="small-12 columns medium-1">
              <label>SKU</label>
            </div>
          )}

          <div className="small-12 medium-2 columns">
            <label>Price {!menuItem.is_gst_free && <small>(exc gst)</small>}</label>
          </div>
          {!menuItem.is_gst_free && (
            <div className="small-12 medium-2 columns">
              <label>
                Price <small>(inc gst)</small>
              </label>
            </div>
          )}
        </div>
      )}
      {!!menuExtraSection.menu_extras.length &&
        menuExtraSection.menu_extras.map((menu_extra, index) => (
          <MenuExtraForm
            key={`menu-extra-${menu_extra.id}`}
            menuItem={menuItem}
            menu_extra={menu_extra}
            index={index}
            removeNewMenuExtra={removeNewMenuExtra}
            sortMenuExtras={sortMenuExtras}
          />
        ))}
      {!menuExtraSection.menu_extras.some((extra) => extra.is_new) && (
        <a onClick={addNewMenuExtra} style={{ marginLeft: '24px' }}>
          + Add menu extra
        </a>
      )}
    </>
  );
};

MenuExtraList.propTypes = {
  menuItem: PropTypes.object.isRequired,
  menuExtraSection: PropTypes.object.isRequired,
  setMenuExtraSection: PropTypes.func.isRequired,
};

export default MenuExtraList;
