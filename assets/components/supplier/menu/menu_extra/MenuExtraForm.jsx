import { useState, useRef, useEffect, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { useDrag, useDrop } from 'react-dnd';
import { createMenuExtra, updateMenuExtra, removeMenuExtra } from 'actions/menuExtraActions';
import { menuContext } from 'contexts/menuContext';

// DnD
import { dragConfig, dropConfig } from 'utilities/drag_and_drop';
import { DRAG_MENU_EXTRA_SUCCESS } from 'actions/menuActionTypes';

// loader
import { createLoadingSelector } from 'selectors';

// components
import MenuExtraPrice from './MenuExtraPrice';

const updatingSelector = createLoadingSelector(['UPDATE_MENU_EXTRA', 'REMOVE_MENU_EXTRA']);
const creationSelector = createLoadingSelector(['CREATE_MENU_EXTRA']);

const MenuExtraForm = ({ menuItem, menu_extra, index, removeNewMenuExtra, sortMenuExtras }) => {
  const dragRef = useRef(null);
  const previewRef = useRef(null);
  const inputRef = useRef(null);
  const {
    supplier: { has_skus: hasSkus },
  } = useContext(menuContext);
  const loadingState = useSelector((state) => state.loading);
  const isUpdating = updatingSelector(loadingState, menu_extra.id);
  const isCreating = creationSelector(loadingState);
  const readOnly = isUpdating || isCreating;
  const dispatch = useDispatch();

  const [menuExtra, setMenuExtra] = useState(menu_extra);
  const [skuFocus, setSkuFocus] = useState(false);

  useEffect(() => {
    setMenuExtra(menu_extra);
  }, [menu_extra]);

  useEffect(() => {
    if (menuExtra.is_new) inputRef.current.focus();
  }, [inputRef.current]);

  const handleChange = (event) => {
    setMenuExtra({ ...menuExtra, is_updated: true, [event.target.name]: event.target.value });
  };

  // submit menu extra on enter
  const handleSubmit = (event) => {
    if (event.which === 13 || event.keyCode === 13) submitMenuExtra();
  };

  // submit menu extra on section name change
  useEffect(() => {
    if (menuExtra.is_updated) submitMenuExtra();
  }, [menuExtra.price]);

  const submitMenuExtra = () => {
    if (isUpdating || isCreating) return;
    if (!menuExtra.name || !menuExtra.price) return;

    if (menuExtra.is_new) {
      dispatch(
        createMenuExtra({
          menuExtra,
        })
      );
    } else if (menuExtra.is_updated) {
      dispatch(
        updateMenuExtra({
          menuExtra,
        })
      );
    }
  };

  const deleteMenuExtra = () => {
    if (isUpdating) return;

    dispatch(
      removeMenuExtra({
        menuExtra,
      })
    );
  };

  const [{ isDragging }, drag, preview] = useDrag(
    dragConfig({
      type: 'MenuExtraForm',
      item: menuExtra,
      index,
      canDrag: !isUpdating && !isCreating && !menuExtra.is_new,
    })
  );

  const [{ handlerId }, drop] = useDrop(
    dropConfig({
      accepts: 'MenuExtraForm',
      itemRef: previewRef,
      index,
      dispatch,
      dragAction: { type: DRAG_MENU_EXTRA_SUCCESS, payload: menuExtra },
      sortAction: sortMenuExtras,
    })
  );

  useEffect(() => {
    drag(dragRef);
    drop(preview(previewRef));
  }, [dragRef, previewRef]);

  return (
    <div className="row" ref={previewRef} style={isDragging ? { opacity: 0.1 } : {}} data-handler-id={handlerId}>
      <span className={`drag-handle${!menuExtra.is_new ? ' drag-reorder-icon mt-1-2' : ''}`} ref={dragRef}>
        &nbsp;
      </span>

      <div
        className={`small-12 columns no-gutter expandable ${hasSkus ? (skuFocus ? 'medium-1' : 'medium-3') : 'medium-4'
          }`}
        style={menuExtra.is_new ? { marginLeft: '20px' } : {}}
      >
        <input
          ref={inputRef}
          readOnly={readOnly}
          required="required"
          placeholder="Name"
          className="form-input"
          type="text"
          name="name"
          value={menuExtra.name}
          onChange={handleChange}
          onBlur={submitMenuExtra}
          onKeyUp={handleSubmit}
        />
      </div>

      {hasSkus && (
        <div className={`small-12 columns expandable ${skuFocus ? 'medium-3' : 'medium-1'}`}>
          <input
            readOnly={readOnly}
            placeholder="SKU"
            className="form-input"
            type="text"
            name="sku"
            value={menuExtra.sku}
            onChange={handleChange}
            onFocus={() => setSkuFocus(true)}
            onBlur={() => {
              setSkuFocus(false);
              submitMenuExtra();
            }}
            onKeyUp={handleSubmit}
          />
        </div>
      )}

      <MenuExtraPrice menuItem={menuItem} menuExtra={menuExtra} setMenuExtra={setMenuExtra} readOnly={readOnly} />

      <div className="small-12 medium-2 columns">
        {!menuExtra.is_new && menuExtra.is_updated && <span tabIndex="0" className="settings-icon" />}
        {!isUpdating && !menuExtra.is_new && (
          <a className="delete-icon ml-1-4" onClick={deleteMenuExtra}>
            Delete
          </a>
        )}

        {menuExtra.is_new && isCreating && <span>creating...</span>}
        {menuExtra.is_new && !isCreating && <a onClick={() => removeNewMenuExtra(menuExtra)}>cancel</a>}
      </div>
    </div>
  );
};

MenuExtraForm.propTypes = {
  menuItem: PropTypes.object.isRequired,
  menu_extra: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  removeNewMenuExtra: PropTypes.func.isRequired,
  sortMenuExtras: PropTypes.func.isRequired,
};

export default MenuExtraForm;
