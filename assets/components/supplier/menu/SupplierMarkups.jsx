import { useContext } from 'react';

import RestrictedCustomers from './RestrictedCustomers';
import { menuContext } from 'contexts/menuContext';

const SupplierMarkups = () => {
  const { supplier } = useContext(menuContext);

  return (
    <div className="text-center">
      <span>
        Markup:
        <strong className="ml-1-4">{supplier.markup}</strong>
      </span>
      <span className="ml-1-2">
        Supplier Commission:
        <strong className="ml-1-4">{supplier.commission}</strong>
      </span>
      <RestrictedCustomers />
    </div>
  );
};

export default SupplierMarkups;
