import { useState, useRef, useContext } from 'react';

// context
import { Modal } from 'react-responsive-modal';
import { menuContext } from 'contexts/menuContext';

import { apiSupplierArchiveMenuPath } from 'routes';

// components

const CONFIRMATION_PROMPT = 'archive-menu';

const MenuArchive = () => {
  const [openModal, setOpenModal] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const errorMsg = useRef(null);
  const { supplier } = useContext(menuContext);

  const handleArchive = () => {
    errorMsg.current.style.display = 'none';
    if (confirmationText === CONFIRMATION_PROMPT) {
      window.location = apiSupplierArchiveMenuPath(supplier, { format: 'json' });
    } else {
      errorMsg.current.style.display = 'block';
    }
  };

  return (
    <>
      <a className="button tiny alert uppercase  ml-1 rounded" onClick={() => setOpenModal(true)}>
        Archive Menu
      </a>
      <Modal open={openModal} onClose={() => setOpenModal(false)} center>
        <h2>{supplier.name} Menu</h2>
        <p>
          Are you sure you want to archive the entire menu?
          <br />
          <strong className="text-alert">Warning!</strong> <em>There is no way to undo this action!</em>
        </p>
        <p>
          Type <strong>`{CONFIRMATION_PROMPT}`</strong> and press confirm.
        </p>
        <input
          className="form-input"
          type="text"
          value={confirmationText}
          onChange={(e) => setConfirmationText(e.target.value)}
        />
        <div ref={errorMsg} className="form-error">
          confirmation message is missing / does not match
        </div>

        <a className="button gray-btn small" onClick={() => setOpenModal(false)}>
          Cancel
        </a>
        <a className="button small ml-1" onClick={handleArchive}>
          Confirm
        </a>
      </Modal>
    </>
  );
};

export default MenuArchive;
