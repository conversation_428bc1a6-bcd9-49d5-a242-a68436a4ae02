import { useState, useContext } from 'react';

// context
import { Modal } from 'react-responsive-modal';
import { menuContext } from 'contexts/menuContext';
import { exportMenuPath } from 'routes';

// components

const MenuExport = () => {
  const {
    supplier: { id, name },
  } = useContext(menuContext);
  const [openModal, setOpenModal] = useState(false);
  const [exportEmail, setExportEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleEnter = (event) => {
    if (event.which === 13 || event.keyCode === 13) downloadMenu();
  };

  const downloadMenu = () => {
    if (isLoading) return;

    setIsLoading(true);
    window.location = `${exportMenuPath({ supplier_profile_id: id, format: 'csv' })}&email=${exportEmail}`;
  };

  return (
    <>
      <a className="button tiny border-black ml-1 rounded" onClick={() => setOpenModal(true)}>
        Download Menu
      </a>
      <Modal open={openModal} onClose={() => setOpenModal(false)} center>
        <h2>`{name}` Download Menu</h2>
        <p>
          The menu export function takes a long time.
          <br />
          Please enter the email where you want the menu emailed to.
        </p>
        <input
          required
          className="form-input"
          type="email"
          value={exportEmail}
          onChange={(e) => setExportEmail(e.target.value)}
          onKeyUp={handleEnter}
        />
        <a className="button gray-btn small" onClick={() => setOpenModal(false)}>
          Cancel
        </a>
        <a className="button small ml-1" onClick={downloadMenu}>
          {isLoading ? 'Downloading...' : 'Download'}
        </a>
      </Modal>
    </>
  );
};

export default MenuExport;
