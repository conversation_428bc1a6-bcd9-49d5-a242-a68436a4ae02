
import PropTypes from 'prop-types';

// components
import RateCardForm from './RateCardForm';

const RateCardList = ({ menuItem, setMenuItem, servingSize, rateCards }) => {
  const addNewRateCard = () => {
    const newRateCard = {
      id: `new-rate-card-${Math.floor(Math.random() * 1000)}`,
      company_id: null,
      company_name: '',
      menu_item_id: menuItem.id,
      serving_size_id: servingSize ? servingSize.id : null,
      price: '',
      cost: '',
      is_new: true,
    };
    setMenuItem({ ...menuItem, rate_cards: [...menuItem.rate_cards, newRateCard] });
  };

  const removeNewRateCard = (rateCard) => {
    setMenuItem({ ...menuItem, rate_cards: menuItem.rate_cards.filter((rate_card) => rate_card.id !== rateCard.id) });
  };

  return (
    <>
      {!!rateCards.length && (
        <div className={`small-12 columns no-gutter ${servingSize ? 'ml-2' : ''}`}>
          <div className="small-12 medium-4 columns no-gutter">
            <label>Company</label>
          </div>

          <div className="small-12 medium-2 columns">
            <label>Price {!menuItem.is_gst_free && <small>(exc gst)</small>}</label>
          </div>

          <div className="small-12 medium-2 columns">
            <label>Cost {!menuItem.is_gst_free && <small>(exc gst)</small>}</label>
          </div>
        </div>
      )}
      {!!rateCards.length &&
        rateCards.map((rate_card, index) => (
          <RateCardForm
            key={`rate-card-${rate_card.id}`}
            menuItem={menuItem}
            rate_card={rate_card}
            index={index}
            removeNewRateCard={removeNewRateCard}
          />
        ))}

      <p className={`small-12 columns no-gutter ${servingSize ? 'ml-2 mb-1' : ''}`}>
        {!rateCards.some((rateCard) => rateCard.is_new) && (
          <a className="add-menu-extra-section" onClick={addNewRateCard}>
            Add Rate Card
          </a>
        )}
      </p>
    </>
  );
};

RateCardList.propTypes = {
  menuItem: PropTypes.object.isRequired,
  setMenuItem: PropTypes.func.isRequired,
  rateCards: PropTypes.array.isRequired,
};

export default RateCardList;
