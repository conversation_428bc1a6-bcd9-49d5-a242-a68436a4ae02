import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import { useDispatch, useSelector } from 'react-redux';
import Select from 'react-select';
import { createRateCard, updateRateCard, removeRateCard } from 'actions/rateCardActions';

// loader
import { createLoadingSelector } from 'selectors';

// components
import RateCardForceRemoval from './RateCardForceRemoval';

const updateSelector = createLoadingSelector(['UPDATE_RATE_CARD', 'REMOVE_RATE_CARD']);
const createSelector = createLoadingSelector(['CREATE_RATE_CARD']);

const RateCardForm = ({ rate_card, removeNewRateCard }) => {
  const { companies } = useSelector((state) => state.menu);
  const loadingState = useSelector((state) => state.loading);
  const isUpdating = updateSelector(loadingState, rate_card.id);
  const isCreating = createSelector(loadingState);
  const readOnly = isUpdating || isCreating;
  const dispatch = useDispatch();

  const [rateCard, setRateCard] = useState(rate_card);
  const [forcedRemovalWarning, setForcedRemovalWarning] = useState(null);
  const hasValidPricing = rateCard.price && rateCard.cost;

  const selectOptions = companies.map((company) => ({
    label: company.name,
    value: company.id,
  }));

  const [selectedCompany, setSelectedCompany] = useState(
    selectOptions.find((company) => company.value == rate_card.company_id)
  );

  useEffect(() => {
    setRateCard(rate_card);
  }, [rate_card]);

  const handleChange = (event) => {
    setRateCard({ ...rateCard, is_updated: true, [event.target.name]: event.target.value });
  };

  const handleCompanySelection = (selection) => {
    setSelectedCompany(selection);
    setRateCard({ ...rateCard, is_updated: true, company_id: selection.value });
  };

  // submit rate card on enter
  const handleSubmit = (event) => {
    if (event.which === 13 || event.keyCode === 13) submitRateCard();
  };

  const submitRateCard = () => {
    if (isUpdating || isCreating) return;
    if (!hasValidPricing || !rateCard.company_id) return;

    if (rateCard.is_new) {
      dispatch(
        createRateCard({
          rateCard,
        })
      );
    } else {
      dispatch(
        updateRateCard({
          rateCard,
        })
      );
    }
  };

  const removeCard = (isForced) => {
    if (isUpdating) return;

    removeRateCard({
      rateCard,
      isForced,
      dispatch,
    }).catch((error) => {
      if (error.response.status === 422) {
        const error_messages = error.response.data.errors;
        const warnings_messages = error.response.data.warnings;
        if (!isForced && warnings_messages) {
          setForcedRemovalWarning(warnings_messages.join('. '));
        } else if (warnings_messages) {
          alert(warnings_messages.join('. '));
        } else if (error_messages) {
          alert(error_messages.join('.'));
        }
      } else {
        alert('Something went wrong');
      }
    });
  };

  const canSelectCompany = rateCard.is_new && !readOnly;
  const margin = hasValidPricing ? ((rateCard.price - rateCard.cost) / rateCard.price) * 100 : 0;

  return (
    <div className={`small-12 columns no-gutter ${rate_card.serving_size_id ? 'ml-2' : ''}`}>
      <div className="small-12 columns medium-4 no-gutter">
        {canSelectCompany && (
          <Select
            isSearchable
            isClearable={false}
            value={selectedCompany}
            placeholder="Company"
            name="company_id"
            options={selectOptions}
            onChange={handleCompanySelection}
            onBlur={submitRateCard}
            className="form-input"
            isDisabled={!canSelectCompany}
          />
        )}
        {!canSelectCompany && (
          <input readOnly={!canSelectCompany} className="form-input" type="text" value={selectedCompany.label} />
        )}
      </div>

      <div className="small-12 columns medium-2">
        <input
          required="required"
          readOnly={readOnly}
          placeholder="Price"
          className="form-input"
          type="text"
          name="price"
          value={rateCard.price}
          onChange={handleChange}
          onBlur={submitRateCard}
          onKeyUp={handleSubmit}
        />
      </div>

      <div className="small-12 columns medium-2">
        <input
          required="required"
          readOnly={readOnly}
          placeholder="Cost"
          className="form-input"
          type="text"
          name="cost"
          value={rateCard.cost}
          onChange={handleChange}
          onBlur={submitRateCard}
          onKeyUp={handleSubmit}
        />
      </div>

      <div className="small-12 columns medium-2">
        {!!hasValidPricing && <p className="rate-card-margin">Margin: {margin.toFixed(2)}%</p>}
      </div>

      <div className="small-12 medium-1 columns">
        {!rateCard.is_new && rateCard.is_updated && <span className="settings-icon" />}

        {isUpdating && <span>processing...</span>}
        {!isUpdating && !rateCard.is_new && !rateCard.is_updated && <span className="checked-icon-green" />}
        {!isUpdating && !rateCard.is_new && (
          <a
            className="delete-icon"
            onClick={(event) => {
              event.stopPropagation();
              removeCard(false);
            }}
          >
            Delete
          </a>
        )}

        {rateCard.is_new && isCreating && <span>creating</span>}
        {rateCard.is_new && !isCreating && <a onClick={() => removeNewRateCard(rateCard)}>cancel</a>}
      </div>
      {forcedRemovalWarning && (
        <RateCardForceRemoval
          rateCard={rateCard}
          removeCard={removeCard}
          forcedRemovalWarning={forcedRemovalWarning}
          setForcedRemovalWarning={setForcedRemovalWarning}
        />
      )}
    </div>
  );
};

RateCardForm.propTypes = {
  menuItem: PropTypes.object.isRequired,
  rate_card: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  removeNewRateCard: PropTypes.func.isRequired,
};

export default RateCardForm;
