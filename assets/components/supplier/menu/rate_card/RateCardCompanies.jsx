import { useState } from 'react';
import PropTypes from 'prop-types';

// components
import { Modal } from 'react-responsive-modal';

const RateCardCompanies = ({ menu_item, rate_cards }) => {
  const [openModal, setOpenModal] = useState(false);
  const rate_card_companies = [...new Set(rate_cards.map((rate_card) => rate_card.company_name))].sort();

  return (
    <>
      <p>
        <a onClick={() => setOpenModal(true)}>
          with {rate_cards.length} rate {rate_cards.length > 1 ? 'cards' : 'card'}
        </a>
      </p>
      <Modal classNames={{ modal: 'reveal customer-form' }} open={openModal} onClose={() => setOpenModal(false)} center>
        <h2>Contains rate cards for:</h2>
        <ul className="bullet-list">
          {rate_card_companies.map((company) => (
            <li key={`menu-item-${menu_item.id}-resricted-company-${company}`}>{company}</li>
          ))}
        </ul>
        <a className="button gray-btn small" onClick={() => setOpenModal(false)}>
          Close
        </a>
      </Modal>
    </>
  );
};

RateCardCompanies.propTypes = {
  menu_item: PropTypes.object.isRequired,
  rate_cards: PropTypes.array.isRequired,
};

export default RateCardCompanies;
