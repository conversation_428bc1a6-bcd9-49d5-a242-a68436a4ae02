
import PropTypes from 'prop-types';

// components
import { Modal } from 'react-responsive-modal';

const RateCardForceRemoval = ({ forcedRemovalWarning, setForcedRemovalWarning, rateCard, removeCard }) => (
  <Modal
    open
    onClose={() => setForcedRemovalWarning(null)}
    center
    showCloseIcon={false}
    styles={{ modal: { maxWidth: '500px' } }}
  >
    <h2>
      <small>Force Archive Rate Card for </small> `{rateCard.company_name}`
    </h2>
    <p>
      <strong className="text-alert">Warning!</strong> <em>{forcedRemovalWarning}</em>
    </p>
    <p>
      Are you sure you want to <strong>force</strong> remove/archive this rate card?
    </p>
    <a className="button gray-btn small" onClick={() => setForcedRemovalWarning(null)}>
      Cancel
    </a>
    <a
      className="button small ml-1"
      onClick={() => {
        removeCard(true);
        setForcedRemovalWarning(null);
      }}
    >
      Force
    </a>
  </Modal>
);

RateCardForceRemoval.propTypes = {
  forcedRemovalWarning: PropTypes.string.isRequired,
  setForcedRemovalWarning: PropTypes.func.isRequired,
  rateCard: PropTypes.object.isRequired,
  removeCard: PropTypes.func.isRequired,
};

export default RateCardForceRemoval;
