import { useContext, useState } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { uploadImage, removeImage } from 'actions/menuItemImageActions';
import { menuContext } from 'contexts/menuContext';

const MenuItemImage = ({ menuItem }) => {
  const { imageUrls } = useContext(menuContext);

  const [loading, setLoading] = useState(false);

  const placeholderClassName = `${menuItem.image ? ' opaque' : ''}${loading ? ' menu-skeleton' : ''}`;

  const dispatch = useDispatch();

  const handleChange = async (event) => {
    const newFile = event.target.files[0];
    if (newFile) {
      setLoading(true);
      try {
        await dispatch(
          uploadImage({
            uploadUrl: imageUrls.upload,
            uploadPreset: imageUrls.upload_preset,
            menuItem,
            image: newFile,
          })
        );
      } catch (error) {
        console.error(error);
      }
      setLoading(false);
    }
  };

  const handleRemove = async (event) => {
    setLoading(true);
    try {
      await dispatch(
        removeImage({
          menuItem,
        })
      );
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  };

  return (
    <>
      <div className="item-image-container">
        <input id={`menu-item-image-uploader-${menuItem.id}`} type="file" className="hidden" onChange={handleChange} />
        <label
          htmlFor={`menu-item-image-uploader-${menuItem.id}`}
          className={`menu-item-image-placeholder${placeholderClassName}`}
        />
        <img alt={menuItem.name} src={menuItem.image} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
        {!!menuItem.image && (
          <a className="menu-item-image-remove" onClick={handleRemove}>
            Remove
          </a>
        )}
      </div>
    </>
  );
};

MenuItemImage.propTypes = {
  menuItem: PropTypes.object.isRequired,
};

export default MenuItemImage;
