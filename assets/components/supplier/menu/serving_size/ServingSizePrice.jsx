import { useState, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';

import { gstPricing } from 'utilities/pricingHelpers';
import { menuContext } from 'contexts/menuContext';

const ServingSizePrice = ({ menuItem, servingSize, setServingSize, readOnly }) => {
  const { countryCode } = useContext(menuContext);
  const [priceWithoutGst, setPriceWithoutGst] = useState(servingSize.price || '');
  const [priceWithGst, setPriceWithGst] = useState(
    menuItem.is_gst_free ? servingSize.price : gstPricing({
      price: servingSize.price,
      inc_gst: true,
      countryCode
    })
  );

  useEffect(() => {
    setPriceWithGst(menuItem.is_gst_free ? priceWithoutGst : gstPricing({
        price: priceWithoutGst,
        inc_gst: true,
        countryCode
      }));
  }, [menuItem.is_gst_free]);

  const handlePriceChange = (event) => {
    let gstExcPrice;
    if (event.target.name == 'price') {
      gstExcPrice = event.target.value;
      const gstIncPrice = menuItem.is_gst_free ? event.target.value : gstPricing({
        price: gstExcPrice,
        inc_gst: true,
        countryCode
      });
      setPriceWithGst(gstIncPrice);
    } else {
      gstExcPrice = menuItem.is_gst_free ? event.target.value : gstPricing({
        price: event.target.value,
        inc_gst: false,
        countryCode
      });
      setPriceWithoutGst(gstExcPrice);
    }
    setServingSize({ ...servingSize, is_updated: true, price: gstExcPrice });
  };

  return (
    <>
      <div className="small-12 medium-2 columns">
        <div className="input-group">
          <span className="input-group-label">$</span>
          <input
            required="required"
            readOnly={readOnly}
            placeholder="Price (exc gst)"
            className="form-input text-right with-group-label"
            type="number"
            name="price"
            value={priceWithoutGst}
            onBlur={handlePriceChange}
            onChange={(e) => setPriceWithoutGst(e.target.value)}
          />
        </div>
      </div>
      {!menuItem.is_gst_free && (
        <div className="small-12 medium-2 columns">
          <div className="input-group">
            <span className="input-group-label">$</span>
            <input
              required="required"
              readOnly={readOnly}
              placeholder="Price (inc gst)"
              className="form-input text-right with-group-label"
              type="number"
              name="price_inc_gst"
              value={priceWithGst}
              onBlur={handlePriceChange}
              onChange={(e) => setPriceWithGst(e.target.value)}
            />
          </div>
        </div>
      )}
    </>
  );
};

ServingSizePrice.propTypes = {
  menuItem: PropTypes.object.isRequired,
  servingSize: PropTypes.object.isRequired,
  setServingSize: PropTypes.func.isRequired,
  readOnly: PropTypes.bool.isRequired,
};

export default ServingSizePrice;
