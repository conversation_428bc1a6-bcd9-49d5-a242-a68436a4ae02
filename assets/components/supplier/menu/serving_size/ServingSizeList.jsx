import { useContext, useCallback } from 'react';
import PropTypes from 'prop-types';

// actions
import { useDispatch } from 'react-redux';
import { sortItemsOnDrop } from 'utilities/drag_and_drop';
import { updateServingSize } from 'actions/servingSizeActions';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';

// components
import ServingSizeForm from './ServingSizeForm';

const ServingSizeList = ({ menuItem, setMenuItem, canManageRateCards, setCanManageRateCards }) => {
  const {
    supplier: { has_skus: hasSkus },
  } = useContext(menuContext);
  const { isAdmin } = useContext(adminContext);
  const dispatch = useDispatch();

  const addNewServingSize = () => {
    const newServingSize = {
      id: `new-serving-size-${Math.floor(Math.random() * 1000)}`,
      name: '',
      price: '',
      menu_item_id: menuItem.id,
      is_default: menuItem.serving_sizes.length === 0,
      available_for_team_order: true,
      is_new: true,
    };
    setMenuItem({ ...menuItem, serving_sizes: [...menuItem.serving_sizes, newServingSize] });
  };

  const removeNewServingSize = (servingSize) => {
    setMenuItem({
      ...menuItem,
      serving_sizes: menuItem.serving_sizes.filter((serving_size) => serving_size.id != servingSize.id),
    });
  };

  const handleDefaultServingSize = useCallback(
    (servingSize) => {
      const existingDefault = menuItem.serving_sizes.filter((servingSize) => servingSize.is_default)[0];
      if (existingDefault) {
        setMenuItem({
          ...menuItem,
          serving_sizes: menuItem.serving_sizes.map((serving_size) => {
            if (serving_size.id === existingDefault.id) {
              return { ...serving_size, is_updated: true, is_default: false };
            }
            return serving_size;
          }),
        });
      }
    },
    [menuItem.serving_sizes]
  );

  const sortServingSizes = useCallback(() => {
    sortItemsOnDrop({
      items: menuItem.serving_sizes,
      itemName: 'servingSize',
      action: updateServingSize,
      dispatch,
    });
  }, [menuItem.serving_sizes]);

  const servingRateCards = menuItem.rate_cards.filter((rate_card) => rate_card.serving_size_id);
  const servingSizes = menuItem.serving_sizes;

  return (
    <div className="mt-1">
      <h3>
        Serving Sizes
        {!!isAdmin && !!servingSizes.length && (
          <>
            <a className="ml-2" onClick={() => setCanManageRateCards(!canManageRateCards)}>
              {canManageRateCards ? 'Hide Rate Cards' : 'Manage Rate Cards'}
            </a>
            {!!servingRateCards.length && <small className="ml-1-4">({servingRateCards.length})</small>}
          </>
        )}
      </h3>
      {!!servingSizes.length && (
        <div className="row">
          <span className="drag-handle" style={{ width: '24px' }}>
            &nbsp;
          </span>
          <div className={`small-12 columns ${hasSkus ? 'medium-2' : 'medium-4'} no-gutter`}>
            <label>Name</label>
          </div>

          {hasSkus && (
            <>
              <div className="small-12 medium-1 columns">
                <label>SKU</label>
              </div>
              <div className="small-12 medium-1 columns">
                <label>Stock Qty</label>
              </div>
            </>
          )}

          <div className="small-12 medium-2 columns">
            <label>Price {!menuItem.is_gst_free && <small>(exc gst)</small>}</label>
          </div>
          {!menuItem.is_gst_free && (
            <div className="small-12 medium-2 columns">
              <label>
                Price <small>(inc gst)</small>
              </label>
            </div>
          )}
        </div>
      )}
      {!!servingSizes.length &&
        servingSizes.map((serving_size, index) => (
          <ServingSizeForm
            key={`serving-size-${serving_size.id}`}
            menuItem={menuItem}
            setMenuItem={setMenuItem}
            serving_size={serving_size}
            index={index}
            sortServingSizes={sortServingSizes}
            removeNewServingSize={removeNewServingSize}
            handleDefaultServingSize={handleDefaultServingSize}
            canManageRateCards={canManageRateCards}
          />
        ))}
      {!servingSizes.some((serving) => serving.is_new) && <a onClick={addNewServingSize}>+ Add serving size</a>}
    </div>
  );
};

ServingSizeList.propTypes = {
  menuItem: PropTypes.object.isRequired,
  setMenuItem: PropTypes.func.isRequired,
  canManageRateCards: PropTypes.bool.isRequired,
  setCanManageRateCards: PropTypes.func.isRequired,
};

export default ServingSizeList;
