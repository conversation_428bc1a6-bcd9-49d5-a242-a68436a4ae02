import { useRef, useState, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';

// actions
import { useDispatch, useSelector } from 'react-redux';
import { useDrag, useDrop } from 'react-dnd';
import { createServingSize, updateServingSize, removeServingSize } from 'actions/servingSizeActions';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';

// DnD
import { dragConfig, dropConfig } from 'utilities/drag_and_drop';
import { DRAG_SERVING_SIZE_SUCCESS } from 'actions/menuActionTypes';

// loader
import { createLoadingSelector } from 'selectors';

// component(s)
import RateCardList from '../rate_card/RateCardList';
import ServingSizePrice from './ServingSizePrice';

const updateSelector = createLoadingSelector(['UPDATE_SERVING_SIZE', 'REMOVE_SERVING_SIZE']);
const createSelector = createLoadingSelector(['CREATE_SERVING_SIZE']);

const ServingSizeForm = ({
  menuItem,
  setMenuItem,
  serving_size,
  index,
  sortServingSizes,
  removeNewServingSize,
  handleDefaultServingSize,
  canManageRateCards,
}) => {
  const dragRef = useRef(null);
  const previewRef = useRef(null);

  const {
    supplier: { has_skus: hasSkus },
  } = useContext(menuContext);
  const { isAdmin } = useContext(adminContext);
  const loadingState = useSelector((state) => state.loading);
  const isUpdating = updateSelector(loadingState, serving_size.id);
  const isCreating = createSelector(loadingState);
  const readOnly = isUpdating || isCreating;
  const dispatch = useDispatch();

  const [servingSize, setServingSize] = useState(serving_size);
  const [skuFocus, setSkuFocus] = useState(false);
  const [stockFocus, setStockFocus] = useState(false);

  useEffect(() => {
    setServingSize(serving_size);
  }, [serving_size]);

  const handleChange = (event) => {
    const field = event.target.name;
    let { value } = event.target;
    if (field === 'stock_quantity' && value === '') value = null;
    setServingSize((state) => ({ ...state, is_updated: true, [field]: value }));
  };

  const handleBoolChange = (event) => {
    setServingSize({ ...servingSize, is_updated: true, [event.target.name]: event.target.checked });
  };

  // handle default serving size within menu item if is_default flag is updated to true
  useEffect(() => {
    if (servingSize.is_updated && servingSize.is_default) handleDefaultServingSize(servingSize, setServingSize);
  }, [servingSize.is_default]);

  // submit serving size on boolean value change or if changed by handleDefaultServingSize
  useEffect(() => {
    if (servingSize.is_updated) submitServingSize();
  }, [servingSize.price, servingSize.is_default, servingSize.available_for_team_order]);

  // submit serving size on enter
  const handleSubmit = (event) => {
    if (event.which === 13 || event.keyCode === 13) submitServingSize();
  };

  const submitServingSize = () => {
    if (isUpdating || isCreating) return;
    if (!servingSize.name || !servingSize.price) return;

    if (servingSize.is_new) {
      dispatch(
        createServingSize({
          servingSize,
        })
      );
    } else {
      dispatch(
        updateServingSize({
          servingSize,
        })
      );
    }
  };

  const deleteServingSize = () => {
    if (isUpdating) return;

    dispatch(
      removeServingSize({
        servingSize,
      })
    );
  };

  const [{ isDragging }, drag, preview] = useDrag(
    dragConfig({
      type: 'ServingSizeForm',
      item: servingSize,
      index,
      canDrag: !isUpdating && !isCreating && !servingSize.is_new,
    })
  );

  const [{ handlerId }, drop] = useDrop(
    dropConfig({
      accepts: 'ServingSizeForm',
      itemRef: previewRef,
      index,
      dispatch,
      dragAction: { type: DRAG_SERVING_SIZE_SUCCESS, payload: servingSize },
      sortAction: sortServingSizes,
    })
  );

  useEffect(() => {
    drag(dragRef);
    drop(preview(previewRef));
  }, [dragRef, [previewRef]]);

  const servingRateCards = menuItem.rate_cards.filter(
    (rate_card) => rate_card.serving_size_id && rate_card.serving_size_id == servingSize.id
  );

  return (
    <div className="row" style={isDragging ? { opacity: 0.1 } : {}} ref={previewRef} data-handler-id={handlerId}>
      <span className={`drag-handle mt-1-2${!servingSize.is_new ? ' drag-reorder-icon' : ''}`} ref={dragRef} />

      <div
        className={`small-12 columns no-gutter ${hasSkus ? `expandable${skuFocus || stockFocus ? ' medium-1' : ' medium-2'}` : 'medium-4'
          }`}
        style={servingSize.is_new ? { marginLeft: '24px' } : {}}
      >
        <input
          required="required"
          readOnly={readOnly}
          placeholder="Name"
          className="form-input"
          type="text"
          name="name"
          value={servingSize.name}
          onChange={handleChange}
          onBlur={submitServingSize}
          onKeyUp={handleSubmit}
        />
      </div>

      {hasSkus && (
        <div className={`small-12 columns expandable ${skuFocus ? 'medium-2' : 'medium-1'}`}>
          <input
            readOnly={readOnly}
            placeholder="SKU"
            className="form-input"
            type="text"
            name="sku"
            value={servingSize.sku || ''}
            onChange={handleChange}
            onFocus={() => setSkuFocus(true)}
            onBlur={() => {
              setSkuFocus(false);
              submitServingSize();
            }}
            onKeyUp={handleSubmit}
          />
        </div>
      )}

      {hasSkus && (
        <div className={`small-12 columns expandable ${stockFocus ? 'medium-2' : 'medium-1'}`}>
          <input
            readOnly={readOnly}
            placeholder="In Stock quantity"
            className="form-input"
            type="text"
            name="stock_quantity"
            value={servingSize.stock_quantity === null ? '' : servingSize.stock_quantity}
            onChange={handleChange}
            onFocus={() => setStockFocus(true)}
            onBlur={() => {
              setStockFocus(false);
              submitServingSize();
            }}
            onKeyUp={handleSubmit}
          />
        </div>
      )}

      <ServingSizePrice
        menuItem={menuItem}
        servingSize={servingSize}
        setServingSize={setServingSize}
        readOnly={readOnly}
      />

      <div className="small-12 medium-2 columns serving-size__flags">
        <label>
          <input
            type="checkbox"
            readOnly={readOnly}
            name="is_default"
            checked={servingSize.is_default}
            onChange={handleBoolChange}
          />
          default
        </label>
        {(menuItem.team_order || menuItem.team_order_only) && (
          <label>
            <input
              type="checkbox"
              readOnly={readOnly}
              name="available_for_team_order"
              checked={servingSize.available_for_team_order}
              onChange={handleBoolChange}
            />
            Team Order
          </label>
        )}
      </div>
      <div className="small-12 medium-1 columns">
        {!servingSize.is_new && servingSize.is_updated && <span className="settings-icon" />}
        {!isUpdating && !servingSize.is_new && (
          <a className="delete-icon" onClick={deleteServingSize}>
            Delete
          </a>
        )}

        {servingSize.is_new && isCreating && <span>creating</span>}
        {servingSize.is_new && !isCreating && <a onClick={() => removeNewServingSize(servingSize)}>cancel</a>}
      </div>

      {!!isAdmin && canManageRateCards && !servingSize.is_new && (
        <RateCardList
          menuItem={menuItem}
          setMenuItem={setMenuItem}
          servingSize={servingSize}
          rateCards={servingRateCards}
        />
      )}
    </div>
  );
};

ServingSizeForm.propTypes = {
  menuItem: PropTypes.object.isRequired,
  setMenuItem: PropTypes.func.isRequired,
  serving_size: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  sortServingSizes: PropTypes.func.isRequired,
  removeNewServingSize: PropTypes.func.isRequired,
  handleDefaultServingSize: PropTypes.func.isRequired,
  canManageRateCards: PropTypes.bool.isRequired,
};

export default ServingSizeForm;
