
import PropTypes from 'prop-types';

const ServingSizeDetails = ({ menu_item }) => (
  <table className="item-options-table">
    <tbody>
      {menu_item.serving_sizes.map((serving_size) => (
        <ServingSizeDetail key={`serving-detail-${serving_size.id}`} servingSize={serving_size} />
      ))}
    </tbody>
  </table>
);

ServingSizeDetails.propTypes = {
  menu_item: PropTypes.object.isRequired,
};

export default ServingSizeDetails;

const ServingSizeDetail = ({ servingSize }) => (
  <tr>
    <td>{servingSize.name}</td>
    <td>${servingSize.price}</td>
  </tr>
);

ServingSizeDetail.propTypes = {
  servingSize: PropTypes.object.isRequired,
};
