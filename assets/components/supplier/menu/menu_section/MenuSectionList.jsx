import { useCallback, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// actions
import { sortItemsOnDrop } from 'utilities/drag_and_drop';
import { updateMenuSection } from 'actions/menuSectionActions';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';

// components
import { createLoadingSelector } from 'selectors';
import MenuSkeleton from './MenuSkeleton';
import MenuSection from './MenuSection';
import NewMenuSection from './NewMenuSection';

// loader

const loadingSelector = createLoadingSelector(['FETCH_MENU']);

const MenuSectionList = () => {
  const { menu_sections: menuSections } = useSelector((state) => state.menu);
  const { canManageMenu } = useContext(adminContext);
  const dispatch = useDispatch();

  const loadingState = useSelector((state) => state.loading);
  const isLoading = loadingSelector(loadingState);

  const sortMenuSections = useCallback(() => {
    sortItemsOnDrop({
      items: menuSections,
      itemName: 'menuSection',
      action: updateMenuSection,
      dispatch,
      withReturn: true,
    });
  }, [menuSections]);

  return (
    <ul className="supplier-menu-dash list-unstyled">
      {isLoading && <MenuSkeleton />}
      {!isLoading &&
        !!menuSections.length &&
        menuSections.map((menuSection, index) => (
          <MenuSection
            key={`menu-section-${menuSection.id}`}
            menuSection={menuSection}
            index={index}
            sortMenuSections={sortMenuSections}
          />
        ))}
      {!isLoading && canManageMenu && <NewMenuSection />}
    </ul>
  );
};

export default MenuSectionList;
