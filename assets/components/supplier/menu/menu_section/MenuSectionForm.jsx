import { useState, useRef, useEffect, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { updateMenuSection } from 'actions/menuSectionActions';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';

// loader
import { createLoadingSelector } from 'selectors';

// components
import Select from 'react-select';
import SectionCategoryForm from '../menu_section_categories/SectionCategoryForm';

const loadingSelector = createLoadingSelector(['UPDATE_MENU_SECTION']);

const MenuSectionForm = ({ menu_section, setEditMenuSection }) => {
  const firstInputRef = useRef(null);
  const { sectionCategories } = useContext(menuContext);
  const { isAdmin } = useContext(adminContext);
  const { companies } = useSelector((state) => state.menu);
  const loadingState = useSelector((state) => state.loading);
  const isUpdating = loadingSelector(loadingState, menu_section.id);
  const dispatch = useDispatch();

  const [menuSection, setMenuSection] = useState(menu_section);

  useEffect(() => {
    firstInputRef.current.focus();
  }, [firstInputRef.current]);

  const companySelectOptions = companies.map((company) => ({
    label: company.name,
    value: company.id,
  }));

  const [selectedCompanyIds, setSelectedCompanyIds] = useState(
    companySelectOptions.filter((company) => menu_section.company_ids.includes(company.value))
  );

  const handleChange = (event) => {
    setMenuSection({ ...menuSection, [event.target.name]: event.target.value });
  };

  const handleBoolChange = (event) => {
    setMenuSection({ ...menuSection, [event.target.name]: event.target.checked });
  };

  const handleCompanySelection = (selection) => {
    setSelectedCompanyIds(selection);
    setMenuSection({ ...menuSection, company_ids: selection.map((company) => company.value) });
  };

  const handleSubmit = () => {
    if (isUpdating) return;

    updateMenuSection({
      menuSection,
      dispatch,
    }).then(() => {
      setEditMenuSection(false);
    });
  };

  return (
    <div className="menu-section-edit-form">
      <h3>Edit {menu_section.name}</h3>
      <div className="row pb-1">
        <div className="small-6 columns">
          <label>Name</label>
          <input
            ref={firstInputRef}
            required="required"
            type="text"
            name="name"
            value={menuSection.name}
            onChange={handleChange}
            className="form-input"
          />
        </div>
        <div className="small-2 columns pt-2">
          <label>
            <input type="checkbox" name="is_hidden" checked={menuSection.is_hidden} onChange={handleBoolChange} />
            Hide this?
          </label>
        </div>
      </div>

      {sectionCategories.map((sectionCategory) => (
        <SectionCategoryForm
          key={`menu-section-${menuSection.id}-section-category-${sectionCategory.group}`}
          menuSection={menuSection}
          setMenuSection={setMenuSection}
          sectionCategory={sectionCategory}
        />
      ))}

      {isAdmin && (
        <div className="row pb-1">
          <div className="small-12 columns">
            <label>
              Company Restrictions
              <small className="ml-1-2">(admins only)</small>
            </label>
            <Select
              isSearchable
              isClearable={false}
              isMulti
              value={selectedCompanyIds}
              placeholder="Company"
              name="company_id"
              options={companySelectOptions}
              onChange={handleCompanySelection}
            />
          </div>
        </div>
      )}

      <div className="row margin-top-1">
        <div className="small-12 columns">
          <a
            className="button small gray-btn"
            onClick={() => {
              !isUpdating && setEditMenuSection(false);
            }}
          >
            CLOSE
          </a>
          <a className="button small red-btn ml-1" onClick={handleSubmit}>
            {isUpdating ? 'SAVING...' : 'SAVE'}
          </a>
        </div>
      </div>
    </div>
  );
};

MenuSectionForm.propTypes = {
  menu_section: PropTypes.object.isRequired,
  setEditMenuSection: PropTypes.func.isRequired,
};

export default MenuSectionForm;
