import { useRef, useState, useEffect, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { useDrag, useDrop } from 'react-dnd';

// actions
import { removeMenuSection } from 'actions/menuSectionActions';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';
import { DRAG_MENU_SECTION_SUCCESS } from 'actions/menuActionTypes';

// DnD
import { dragConfig, dropConfig } from 'utilities/drag_and_drop';

// loader
import { createLoadingSelector } from 'selectors';

// components
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import SectionCategoryLabels from '../menu_section_categories/SectionCategoryLabels';
import MenuItemList from '../menu_item/MenuItemList';
import SectionCompanies from './SectionCompanies';
import SectionForceRemoval from './SectionForceRemoval';
import MenuSectionForm from './MenuSectionForm';

const loadingSelector = createLoadingSelector(['UPDATE_MENU_SECTION', 'REMOVE_MENU_SECTION']);

const FORCE_REMOVAL_ERROR = 'Menu Sections can only be archived/removed after archiving all associated menu items';

const MenuSection = ({ menuSection, index, sortMenuSections }) => {
  const dragRef = useRef(null);
  const previewRef = useRef(null);

  const { canManageMenu, isAdmin } = useContext(adminContext);
  const loadingState = useSelector((state) => state.loading);
  const isLoading = loadingSelector(loadingState, menuSection.id);
  const dispatch = useDispatch();

  const [showItems, setShowItems] = useState(false);
  const [editMenuSection, setEditMenuSection] = useState(!!menuSection.is_new);
  const [forcedRemoval, setForcedRemoval] = useState(false);

  useEffect(() => {
    setShowItems(!!menuSection.is_searched);
  }, [menuSection.is_searched]);

  const handleRemove = (event) => {
    event.stopPropagation();
    if (isLoading) return;

    removeSection(false);
  };

  const removeSection = (isForced) => {
    removeMenuSection({
      menuSection,
      isForced,
      dispatch,
    }).catch((error) => {
      const error_messages = error.response.status === 422 && error.response.data.errors;
      if (!isForced && canManageMenu && error_messages && error_messages.indexOf(FORCE_REMOVAL_ERROR) != -1) {
        setForcedRemoval(true);
      } else if (error_messages) alert(error_messages.join('.'));
    });
  };

  const [{ isDragging }, drag, preview] = useDrag(
    dragConfig({
      type: 'MenuSection',
      item: menuSection,
      index,
      canDrag: !isLoading && !showItems && !editMenuSection,
    })
  );

  const [{ handlerId }, drop] = useDrop(
    dropConfig({
      accepts: 'MenuSection',
      itemRef: previewRef,
      index,
      dispatch,
      dragAction: { type: DRAG_MENU_SECTION_SUCCESS },
      sortAction: sortMenuSections,
    })
  );

  useEffect(() => {
    drag(dragRef);
    drop(preview(previewRef));
  }, [dragRef, [previewRef]]);

  return (
    <li style={isDragging ? { opacity: 0.1 } : {}} ref={previewRef} data-handler-id={handlerId}>
      <div
        className={`menu-section-detail ${showItems ? 'open' : ''} ${isLoading ? 'updating' : ''}`}
        onClick={() => setShowItems(!showItems)}
      >
        <div className="list-flex-1" style={{ display: 'flex', alignItems: 'center' }}>
          {canManageMenu && <span className="drag-handle drag-reorder-icon mr-1" ref={dragRef} />}
          <span className="drag-numbered-icon" style={{ background: getCircleIconColor(index) }} />
          <span className="menu-item-title">{menuSection.name}</span>
          {menuSection.menu_items.length && (
            <span style={{ color: '#989898', fontWeight: 'bold' }}> ({menuSection.menu_items.length} Items) </span>
          )}
          {!!isAdmin && !!menuSection.restricted_companies.length && <SectionCompanies menu_section={menuSection} />}
          {!!menuSection.archived_at && <small className="ml-1-2 label warning">Used while ARCHIVED</small>}
        </div>

        <SectionCategoryLabels menu_section={menuSection} />

        {!isLoading && !showItems && !editMenuSection && canManageMenu && (
          <div className="actions">
            <a
              className="edit-icon"
              onClick={(e) => {
                e.stopPropagation();
                !isLoading && setEditMenuSection(!editMenuSection);
              }}
            >
              &nbsp;
            </a>
            <a className="delete-icon ml-1" onClick={handleRemove} />
          </div>
        )}
      </div>
      {forcedRemoval && (
        <SectionForceRemoval
          menu_section={menuSection}
          removeSection={removeSection}
          forcedRemoval={forcedRemoval}
          setForcedRemoval={setForcedRemoval}
          error_message={FORCE_REMOVAL_ERROR}
        />
      )}
      {editMenuSection && <MenuSectionForm menu_section={menuSection} setEditMenuSection={setEditMenuSection} />}
      {showItems && <MenuItemList menu_section={menuSection} />}
    </li>
  );
};

MenuSection.propTypes = {
  menuSection: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  sortMenuSections: PropTypes.func.isRequired,
};

export default MenuSection;
