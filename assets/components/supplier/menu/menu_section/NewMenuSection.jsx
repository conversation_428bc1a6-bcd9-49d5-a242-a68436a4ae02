import { useState, useContext } from 'react';

// components
import NewMenuSectionForm from './NewMenuSectionForm';
import { menuContext } from 'contexts/menuContext';

const NewMenuSection = () => {
  const {
    supplier: { is_woolworths: isWoolworths },
  } = useContext(menuContext);
  const [isNewSection, setIsNewSection] = useState(false);

  if (isWoolworths) {
    return null;
  }

  if (isNewSection) {
    return <NewMenuSectionForm setIsNewSection={setIsNewSection} />;
  }

  return (
    <li className="new-menu-section">
      <a onClick={() => setIsNewSection(true)}>+ Add new menu section</a>
    </li>
  );
};

export default NewMenuSection;
