
import PropTypes from 'prop-types';

// components
import { Modal } from 'react-responsive-modal';

const SectionForceRemoval = ({ forcedRemoval, setForcedRemoval, menu_section, removeSection, error_message }) => (
  <Modal
    open={forcedRemoval}
    onClose={() => setForcedRemoval(false)}
    center
    showCloseIcon={false}
    styles={{ modal: { maxWidth: '500px' } }}
  >
    <h2>
      <small>Force Archive</small> `{menu_section.name}`
    </h2>
    <p>
      <strong className="text-alert">Warning!</strong> <em>{error_message}</em>
    </p>
    <p>
      Are you sure you want to <strong>force</strong> remove/archive this menu section?
    </p>
    <a className="button gray-btn small" onClick={() => setForcedRemoval(false)}>
      Cancel
    </a>
    <a
      className="button small ml-1"
      onClick={() => {
        removeSection(true);
        setForcedRemoval(false);
      }}
    >
      Force
    </a>
  </Modal>
);

SectionForceRemoval.propTypes = {
  forcedRemoval: PropTypes.bool.isRequired,
  setForcedRemoval: PropTypes.func.isRequired,
  menu_section: PropTypes.object.isRequired,
  removeSection: PropTypes.func.isRequired,
  error_message: PropTypes.string.isRequired,
};

export default SectionForceRemoval;
