import PropTypes from 'prop-types';

// components
import ReactTooltip from 'react-tooltip';

const SectionCompanies = ({ menu_section }) => (
  <>
    <span className="ml-1 label secondary" data-tip data-for={`menu-section-companies-${menu_section.id}`}>
      Company Restrictions
    </span>
    <ReactTooltip id={`menu-section-companies-${menu_section.id}`} place="bottom" effect="solid">
      <p>Restricted to:</p>
      <ul>
        {menu_section.restricted_companies.map((company) => (
          <li key={`menu-section-${menu_section.id}-resricted-company-${company.id}`}>{company.name}</li>
        ))}
      </ul>
    </ReactTooltip>
  </>
);

SectionCompanies.propTypes = {
  menu_section: PropTypes.object.isRequired,
};

export default SectionCompanies;
