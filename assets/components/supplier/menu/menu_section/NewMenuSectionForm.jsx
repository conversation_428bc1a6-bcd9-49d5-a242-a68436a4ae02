import { useState, useRef, useContext, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { createMenuSection } from 'actions/menuSectionActions';
import { menuContext } from 'contexts/menuContext';

// loader
import { createLoadingSelector } from 'selectors';

const loadingSelector = createLoadingSelector(['CREATE_MENU_SECTION']);

const NewMenuSectionForm = ({ setIsNewSection }) => {
  const { supplier } = useContext(menuContext);
  const inputRef = useRef(null);
  const loadingState = useSelector((state) => state.loading);
  const isLoading = loadingSelector(loadingState);
  const dispatch = useDispatch();

  const initialState = {
    supplier_profile_id: supplier.id,
    name: '',
  };
  const [menuSection, setMenuSection] = useState(initialState);

  useEffect(() => {
    inputRef.current.focus();
  }, [inputRef.current]);

  const handleChange = (event) => {
    setMenuSection({ ...menuSection, [event.target.name]: event.target.value });
  };

  const handleSubmit = (event) => {
    if (event.which === 13 || event.keyCode === 13) submitSection();
  };

  const submitSection = () => {
    if (isLoading || !menuSection.name) return;

    createMenuSection({
      menuSection,
      dispatch,
    }).then(() => {
      setIsNewSection(false);
    });
  };

  return (
    <li className="new-menu-section-form-container">
      <div className="new-menu-section-form">
        <input
          ref={inputRef}
          readOnly={isLoading}
          required="required"
          placeholder="Menu section name"
          type="text"
          name="name"
          value={menuSection.name}
          onChange={handleChange}
          onKeyUp={handleSubmit}
        />
        <a className="button small" onClick={submitSection}>
          {isLoading ? 'SAVING...' : 'SAVE'}
        </a>
        <a
          className="button small gray-btn ml-1"
          onClick={() => {
            !isLoading && setIsNewSection(false);
          }}
        >
          CANCEL
        </a>
      </div>
    </li>
  );
};

NewMenuSectionForm.propTypes = {
  setIsNewSection: PropTypes.func.isRequired,
};

export default NewMenuSectionForm;
