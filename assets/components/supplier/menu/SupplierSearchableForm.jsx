import { useState, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// actions
import { Modal } from 'react-responsive-modal';
import { updateSupplier } from 'actions/supplierActions';
import { menuContext } from 'contexts/menuContext';

// loader
import { createLoadingSelector } from 'selectors';

// components
import MenuReminder from './MenuReminder';

const loadingSelector = createLoadingSelector(['UPDATE_SUPPLIER']);

const SupplierSearchableForm = () => {
  const { supplier } = useContext(menuContext);
  const [updateErrors, setUpdateErrors] = useState('');
  const [isSearchable, setIsSearchable] = useState(supplier.is_searchable);
  const [menuLastUpdated, setMenuLastUpdated] = useState(supplier.menu_last_updated_on ? new Date(supplier.menu_last_updated_on) : null);
  const [confirmChange, setConfirmChange] = useState(false);

  const loadingState = useSelector((state) => state.loading);
  const isLoading = loadingSelector(loadingState);
  const dispatch = useDispatch();

  const handleChange = (event) => {
    if (isLoading) return;

    const searchableFlag = !!isSearchable;
    const updatedSupplier = {
      id: supplier.id,
      is_searchable: !searchableFlag,
    };
    setUpdateErrors('');
    updateSupplier({
      supplier: updatedSupplier,
      dispatch,
    })
      .then((response) => {
        setIsSearchable(!searchableFlag);
        setMenuLastUpdated(new Date());
        setConfirmChange(false);
      })
      .catch((error) => {
        if (error.response.status === 422) {
          setUpdateErrors(error.response.data.errors.join('. '));
        }
      });
  };

  return (
    <>
      <div>
        <label className="supplier-searchable">
          Supplier is Searchable
          <div className="toggle-checkbox">
            <input className="ml-1" type="checkbox" checked={isSearchable} onChange={() => setConfirmChange(true)} />
            <span className="toggle-checkbox__switch" />
          </div>
        </label>
        <Modal open={confirmChange} onClose={() => setConfirmChange(false)} center>
          <h2>{supplier.name}</h2>
          <p>
            Are you sure you want to mark the supplier as <strong>{isSearchable ? 'un-searchable' : 'searchable'}</strong>
            ?
          </p>
          {!!updateErrors && <p><small className='is-invalid-label'>{updateErrors}</small></p>}
          <a className="button gray-btn small" onClick={() => setConfirmChange(false)}>
            Cancel
          </a>
          <a className="button small ml-1" onClick={handleChange}>
            {isLoading ? 'Confirming...' : 'Confirm'}
          </a>
        </Modal>
      </div>
      {!!supplier.menu_reminder_frequency && isSearchable && <MenuReminder supplier={supplier} isSearchable={isSearchable} menuLastUpdated={menuLastUpdated} setMenuLastUpdated={setMenuLastUpdated} />}
    </>
  );
};

export default SupplierSearchableForm;
