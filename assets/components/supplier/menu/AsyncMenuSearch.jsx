import { useContext, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// context
import { menuContext } from 'contexts/menuContext';

// components
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';

// actions
import axios from 'axios';
import { searchAPIMenuItemsPath } from 'routes';
import { fetchMenu } from 'actions/menuActions';

const AsyncMenuSearch = () => {
  const {
    showArchived,
    supplier: { id: supplierID },
  } = useContext(menuContext);

  const dispatch = useDispatch();

  const promiseOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseItems } = await axios({
      method: 'GET',
      url: searchAPIMenuItemsPath(),
      params: {
        query,
        // thorough_search: true,
        ...(showArchived && { show_archived: true }),
        supplier_ids: [supplierID],
        for_admin: true,
        page: 1,
        limit: 100,
      },
    });

    return responseItems.map((menuItem) => {
      let label = `${menuItem.name} - $${menuItem.price}`;
      if (!!menuItem.promo_price) {
        label += ` (promo: $${menuItem.promo_price})`;
      }
      if (!!menuItem.is_archived) {
        label += ' [archived]';
      }
      return {
        value: menuItem.id,
        label,
        id: menuItem.id,
        query,
      };
    });
  }, 1000);

  const handleSelect = async (selectedItem) => {
    await fetchMenu({
      supplierID,
      showArchived,
      menuItem: {
        id: selectedItem.id,
        query: selectedItem.query
      },
      dispatch,
    });
  };

  return (
    <AsyncSelect
      className="form-input"
      cacheOptions
      defaultOptions
      placeholder="Search by Menu Item"
      loadOptions={promiseOptions}
      onChange={handleSelect}
      value=""
      styles={{
        control: (baseStyles, state) => ({
          ...baseStyles,
          zIndex: 5,
        }),
      }}
    />
  );
};

export default AsyncMenuSearch;
