import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// components
import Select from 'react-select';
import { SEARCH_MENU } from 'actions/menuActionTypes';

const MenuSearch = () => {
  const { menu_sections } = useSelector((state) => state.menu);

  const [selectedOption, setSelectedOption] = useState('');

  const selectOptions = menu_sections.map((menu_section) => ({
    label: menu_section.name,
    options: menuItemOptions(menu_section),
  }));

  const dispatch = useDispatch();

  useEffect(() => {
    if (selectedOption) {
      dispatch({ type: SEARCH_MENU, payload: selectedOption });
      setSelectedOption('');
    }
  }, [selectedOption]);

  return (
    <Select
      isSearchable
      isClearable
      value={selectedOption}
      placeholder="Search by Menu Item"
      options={selectOptions}
      onChange={setSelectedOption}
    />
  );
};

export default MenuSearch;

const menuItemOptions = (menu_section) => {
  const optionSet = [];
  menu_section.menu_items.map((menu_item) => {
    const isArchived = menu_item.archived_at;
    if (menu_item.serving_sizes.length) {
      menu_item.serving_sizes.map((serving_size) => {
        let servingLabel = `${menu_item.name} - ${serving_size.name}`;
        if (isArchived) servingLabel += ' (archived)';
        optionSet.push({
          label: servingLabel,
          id: menu_item.id,
          menu_section_id: menu_item.menu_section_id,
        });
      });
    } else {
      let itemLabel = menu_item.name;
      if (isArchived) itemLabel += ' (archived)';
      optionSet.push({
        label: itemLabel,
        id: menu_item.id,
        menu_section_id: menu_item.menu_section_id,
      });
    }
  });
  return optionSet;
};
