import { useContext } from 'react';
import PropTypes from 'prop-types';

// context
import { menuContext } from 'contexts/menuContext';

const SectionCategoryLabels = ({ menu_section }) => {
  const { sectionCategories } = useContext(menuContext);
  const allCategories = sectionCategories.map((section) => section.categories).flat();
  let categoryLabels = menu_section.category_ids.map((categoryID) => {
    const sectionCategory = allCategories.find((category) => category.id === categoryID);
    if (sectionCategory)
      return (
        <span key={`section-${menu_section.id}-categories-${sectionCategory.id}`} className="label mx-1-4 my-1-4">
          {sectionCategory.name}
        </span>
      );
    return null;
  });

  if (menu_section.is_hidden)
    categoryLabels.unshift(
      <span key={`section-${menu_section.id}-hidden`} className="label warning mr-1-4">
        Hidden
      </span>
    );

  categoryLabels = categoryLabels.filter((label) => label);
  return <div className="mr-1">{!!categoryLabels.length && categoryLabels}</div>;
};

SectionCategoryLabels.propTypes = {
  menu_section: PropTypes.object.isRequired,
};

export default SectionCategoryLabels;
