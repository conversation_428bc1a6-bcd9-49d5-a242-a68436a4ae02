import PropTypes from 'prop-types';

// components
import SectionCategoryInput from './SectionCategoryInput';

const SectionCategoryForm = ({ menuSection, setMenuSection, sectionCategory }) => (
  <div className="small-12 columns pb-1">
    <label className="pb-1">
      <strong>
        {sectionCategory.label}
        <small className="ml-1">({sectionCategory.descirption})</small>
      </strong>
    </label>
    {sectionCategory.categories.map((category) => (
      <SectionCategoryInput
        key={`menu-section-${menuSection.id}-category-${category.id}`}
        category={category}
        menuSection={menuSection}
        setMenuSection={setMenuSection}
      />
    ))}
  </div>
);

SectionCategoryForm.propTypes = {
  menuSection: PropTypes.object.isRequired,
  setMenuSection: PropTypes.func.isRequired,
  sectionCategory: PropTypes.object.isRequired,
};

export default SectionCategoryForm;
