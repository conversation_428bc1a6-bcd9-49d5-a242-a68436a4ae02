import { useState } from 'react';
import PropTypes from 'prop-types';

const SectionCategoryInput = ({ category, menuSection, setMenuSection }) => {
  const [selected, setSelected] = useState(menuSection.category_ids.indexOf(category.id) != -1);

  const handleChange = (event) => {
    if (selected) {
      setMenuSection({
        ...menuSection,
        category_ids: menuSection.category_ids.filter((category_id) => category_id != category.id),
      });
    } else {
      setMenuSection({ ...menuSection, category_ids: [...menuSection.category_ids, category.id] });
    }
    setSelected(!selected);
  };

  return (
    <div className="small-12 medium-6 large-4 columns">
      <label>
        <input type="checkbox" value={category.id} name="category_ids" checked={selected} onChange={handleChange} />
        {category.name}
      </label>
    </div>
  );
};

SectionCategoryInput.propTypes = {
  category: PropTypes.object.isRequired,
  menuSection: PropTypes.object.isRequired,
  setMenuSection: PropTypes.func.isRequired,
};

export default SectionCategoryInput;
