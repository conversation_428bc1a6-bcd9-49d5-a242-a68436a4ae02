const FormSkeleton = () => (
  <>
    <div className="checkout-panel order-details">
      <div className="order-details__container about-the-order">
        <div className="form-heading about-the-order_icon">
          <h3 className="uppercase">Order Details</h3>
        </div>
        <div className="checkout__fields-container">
          <div className="row">
            <SkeletonInput />
            <SkeletonInput />
            <div className="small-12 columns">
              <div className="callout category-notice">
                <div className="form-input form-skeleton" />
              </div>
            </div>
            <SkeletonInput />
            <SkeletonInput />
          </div>
        </div>
      </div>
    </div>
    <div className="checkout-panel billing-details">
      <div className="add-new-address-box order-details__container">
        <div className="form-heading billing-details_icon">
          <h3 className="uppercase">Billing Details</h3>
        </div>
        <div className="checkout__fields-container">
          <div className="row">
            <SkeletonInput />
            <SkeletonInput />
            <SkeletonInput />
            <SkeletonInput />
            <div className="small-12 columns">
              <div className="callout category-notice">
                <div className="form-input form-skeleton" />
              </div>
            </div>
            <SkeletonInput />
            <SkeletonInput />
            <SkeletonInput />
            <SkeletonInput />
          </div>
        </div>
      </div>
    </div>
  </>
);

export default FormSkeleton;

const SkeletonInput = () => (
  <div className="small-6 columns">
    <div className="form-input form-skeleton" style={{ padding: '12px 16px' }} />
  </div>
);
