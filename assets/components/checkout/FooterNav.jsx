import PropTypes from 'prop-types';

import { useSelector } from 'react-redux';

// hooks
import useOrderSubmission from 'hooks/useOrderSubmission';

// footer components
import OrderEditFooter from './footer/OrderEditFooter';
import DetailsFooter from './footer/DetailsFooter';
import BillingsFooter from './footer/BillingsFooter';
import PaymentsFooter from './footer/PaymentsFooter';

// modal components
import ClosureModal from './modals/ClosureModal';
import LeadTimeModal from './modals/LeadTimeModal';
import QuoteModal from './modals/QuoteModal';

const FooterNav = ({ currentPanel, isEditPage }) => {
  useOrderSubmission();

  if (isEditPage) {
    return (
      <>
        <OrderEditFooter />
        <FooterModals />
      </>
    );
  }

  return (
    <>
      {currentPanel === 'order-details' && <DetailsFooter />}
      {currentPanel === 'billing-details' && <BillingsFooter />}
      {currentPanel === 'payment-details' && <PaymentsFooter />}
      <FooterModals />
    </>
  );
};

FooterNav.propTypes = {
  currentPanel: PropTypes.string.isRequired,
  isEditPage: PropTypes.bool.isRequired,
};

export default FooterNav;

const FooterModals = () => {
  const { closureSuppliers, supplierLeadTime, asQuote: isQuoteOrder } = useSelector((state) => state.form);
  const order = useSelector((state) => state.order);

  return (
    <>
      {!!closureSuppliers.length && (
        <ClosureModal delivery_at={order.delivery_at} closureSuppliers={closureSuppliers} />
      )}
      {!!supplierLeadTime && <LeadTimeModal supplierLeadTime={supplierLeadTime} />}
      {isQuoteOrder && <QuoteModal />}
    </>
  );
};
