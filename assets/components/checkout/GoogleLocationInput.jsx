import { useState, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';

// actions
import { useDispatch } from 'react-redux';
import { usePlacesWidget } from 'react-google-autocomplete';
import { UPDATE_ERRORS, FIND_SUBURB_FAILURE } from 'actions/checkoutActionTypes';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';
import useSupplierChecks from 'hooks/useSupplierChecks';
import { getYordarSuburb } from 'utilities/locationSelection';

const GOOGLE_AUTOCOMPLETE_OPTIONS = (countryCode) => ({
  types: ['geocode'],
  fields: ['place_id', 'types', 'formatted_address', 'address_components'],
  componentRestrictions: {
    country: countryCode || 'au',
  },
});

const GoogleLocationInput = ({
  dataType,
  field,
  countryCode,
  location,
  suburb,
  handleAddressChange,
  readOnly,
  isRequired,
  isValid,
  suburbRestriction,
}) => {
  const { checkSupplierSuburbRestriction } = useSupplierChecks({});
  const dispatch = useDispatch();

  const initialValue = {
    value: location,
    suburb: suburb || null,
  };
  const initialSelection = {
    isSelected: false,
    tries: 0,
    allowFree: false,
  };
  const [currentLocation, setCurrentLocation] = useState(initialValue);
  const [locationSelection, setLocationSelection] = useState(initialSelection);
  const { googleConfig } = useContext(appContext);
  const { isAdmin } = useContext(adminContext);

  // check if address was selected or just typed / Copy / Pasted
  const handleBlur = (event) => {
    if (!locationSelection.allowFree) {
      dispatch({ type: UPDATE_ERRORS, payload: { [field]: true } });
      setCurrentLocation({ ...currentLocation, value: '', suburb: null });
      setLocationSelection({ ...locationSelection, tries: locationSelection.tries + 1 });
    }
  };

  // handle current value change typing value
  const handleChange = (event) => {
    setCurrentLocation({ ...currentLocation, value: event.target.value, suburb: null });
    setLocationSelection({ ...locationSelection, isSelected: false });
    dispatch({ type: UPDATE_ERRORS, payload: { [field]: false } }); // clear field error
  };

  // handle address change if current location changes
  useEffect(() => {
    handleAddressChange(currentLocation);
  }, [currentLocation]);

  // handle current location change if passed in location changes
  useEffect(() => {
    setCurrentLocation({ ...currentLocation, value: location });
  }, [location]);

  const { ref } = usePlacesWidget({
    apiKey: googleConfig.api_key,
    onPlaceSelected: (place) => handlePlaceSelected(place, countryCode),
    options: GOOGLE_AUTOCOMPLETE_OPTIONS(countryCode),
  });

  const handlePlaceSelected = async (places, countryCode) => {
    setLocationSelection({ ...locationSelection, isSelected: true });
    dispatch({ type: UPDATE_ERRORS, payload: { [field]: false } }); // clear field error
    const selectedLocation = {
      postcode: null,
      locality: null,
      streetAddress: null,
      countryCode,
    };
    const postcodeComponent = places.address_components.find(
      (component) => component.types.indexOf('postal_code') != -1
    );
    if (postcodeComponent) {
      selectedLocation.postcode = postcodeComponent.short_name;
    }
    const sublocalityComponent = places.address_components.find(
      (component) => component.types.indexOf('sublocality') != -1
    );
    if (sublocalityComponent) {
      selectedLocation.locality = sublocalityComponent.long_name;
    }
    const localityComponent = places.address_components.find((component) => component.types.indexOf('locality') != -1);
    if (localityComponent) {
      selectedLocation.locality ||= localityComponent.long_name;
    }

    if (places.types.find((type) => type === 'street_address' || type === 'premise' || type === 'subpremise')) {
      const streetAddressComponents = places.formatted_address
        .split(', ')
        .filter((address) => !address.startsWith('Level'));
      selectedLocation.streetAddress = streetAddressComponents[0];
    }

    let currentValue = selectedLocation.streetAddress;

    // get yordar suburb
    const yordarSuburb = await getYordarSuburb({
      selectedLocation,
      countryCode,
    });

    if (dataType === 'suburbLabel') {
      currentValue = yordarSuburb ? yordarSuburb.label : '';
    }

    if (yordarSuburb && suburbRestriction) {
      // check supplier suburb Restriction
      const restrictionCheck = await checkSupplierSuburbRestriction({
        yordarSuburb,
        suburbRestriction,
      });

      if (restrictionCheck || isAdmin) {
        setCurrentLocation({ ...currentLocation, value: currentValue, suburb: yordarSuburb });
      } else {
        setCurrentLocation({ ...currentLocation, value: '', suburb: null });
      }
    } else if (yordarSuburb) {
      setCurrentLocation({ ...currentLocation, value: currentValue, suburb: yordarSuburb });
    } else {
      // could not find suburb
      dispatch({ type: FIND_SUBURB_FAILURE });
      setCurrentLocation({ ...currentLocation, value: '', suburb: null });
    }
  };

  return (
    <>
      {readOnly && (
        <input
          readOnly
          className={`form-input ${!isValid && 'is-invalid-input'}`}
          required={isRequired}
          type="text"
          value={currentLocation.value || ''}
        />
      )}
      {!readOnly && (
        <input
          ref={ref}
          autoComplete="no"
          className={`form-input ${!isValid && 'is-invalid-input'}`}
          required={isRequired}
          type="text"
          onBlur={(e) => locationSelection.isSelected || handleBlur()}
          onChange={handleChange}
          value={currentLocation.value || ''}
        />
      )}
      {locationSelection.tries >= 3 && (
        <label>
          <input
            type="checkbox"
            onClick={(e) => setLocationSelection({ ...locationSelection, allowFree: e.target.checked })}
          />
          Couldn't find {field == 'delivery_address' ? 'street address' : 'suburb'}? - check to allow copy-paste
          location
        </label>
      )}
    </>
  );
};

GoogleLocationInput.propTypes = {
  dataType: PropTypes.string.isRequired,
  location: PropTypes.string.isRequired,
  handleAddressChange: PropTypes.func.isRequired,
  readOnly: PropTypes.bool.isRequired,
  isRequired: PropTypes.bool.isRequired,
  isValid: PropTypes.bool.isRequired,
  suburbRestriction: PropTypes.object,
};

export default GoogleLocationInput;
