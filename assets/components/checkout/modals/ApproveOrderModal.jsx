import PropTypes from 'prop-types';

// components
import { Modal } from 'react-responsive-modal';
import Spinner from '../Spinner';

const ApproveOrderModal = ({ order, isProcessing, handleSubmit, setApproveOrder }) => {
  const supplierNames = order.order_suppliers.map((supplier) => supplier.name);
  return (
    <Modal
      classNames={{ modal: 'reveal customer-form' }}
      open
      onClose={() => setApproveOrder(false)}
      center
      showCloseIcon={false}
    >
      <div className="form-content has-small-gutter text-center">
        <h3 className="modal-title">Approve Order</h3>
        <p>
          Are you sure you want to Approve this order?
          <br />
          {supplierNames.length > 1 ? 'Suppliers' : 'Supplier'}: {supplierNames.join(', ')}
          <br />
          Order total: {order.customer_total}
        </p>
      </div>
      <div className="form-footer light-gray-bg medium-text-right">
        <a className="button small uppercase gray-btn" onClick={() => setApproveOrder(false)}>
          Close
        </a>
        <a
          className="button small uppercase"
          onClick={() => {
            handleSubmit('one-off');
            setApproveOrder(false);
          }}
        >
          {isProcessing ? <Spinner /> : 'Confirm'}
        </a>
      </div>
    </Modal>
  );
};

ApproveOrderModal.propTypes = {
  order: PropTypes.object.isRequired,
  isProcessing: PropTypes.bool.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  setApproveOrder: PropTypes.func.isRequired,
};

export default ApproveOrderModal;
