import { useState, useRef, useEffect } from 'react';

// actions
import { useDispatch, useSelector } from 'react-redux';
import { Modal } from 'react-responsive-modal';
import { UPDATE_AS_QUOTE_ORDER, UPDATE_SUBMIT_ORDER } from 'actions/checkoutActionTypes';

// loader
import { createLoadingSelector } from 'selectors';

// components
import Spinner from '../Spinner';

const submitSelector = createLoadingSelector(['SUBMIT_ORDER']);

const QuoteModal = () => {
  const dispatch = useDispatch();
  const [addMessage, setAddMessage] = useState(false);
  const [quoteDetails, setQuoteDetails] = useState({
    emails: '',
    message: '',
  });
  const [quoteErrors, setQuoteErrors] = useState('');
  const customer = useSelector((state) => state.customer);
  const order = useSelector((state) => state.order);

  const loadingState = useSelector((state) => state.loading);
  const isSubmitting = submitSelector(loadingState, order.id);

  const emailsRef = useRef(null);
  useEffect(() => {
    if (emailsRef && emailsRef.current) {
      emailsRef.current.style.height = 'auto';
      emailsRef.current.style.height = `${emailsRef.current.scrollHeight + 20}px`;
    }
  }, [quoteDetails.emails]);

  const handleEnter = (event) => {
    if (event.which === 13 || event.keyCode === 13 || event.keyCode === 32) {
      let emailsValue = event.target.value;
      emailsValue = emailsValue.replace(/\n|\r|\s/g, ';');
      setQuoteDetails((state) => ({
        ...state,
        emails: emailsValue,
      }));
    }
  };

  const handleCancel = () => {
    !isSubmitting && dispatch({ type: UPDATE_AS_QUOTE_ORDER, payload: false });
  };

  const quoteCustomer = () => {
    let quoteEmails = quoteDetails.emails;
    if (!quoteEmails.includes(customer.email)) {
      setQuoteDetails((state) => ({
        ...state,
        emails: `${customer.email};${quoteEmails}`,
      }));
    }
  }

  const handleQuoteSubmit = () => {
    setQuoteErrors('');
    if (!quoteDetails.emails) {
      setQuoteErrors('Need at least a single email to send quote');
    } else {
      dispatch({ type: UPDATE_SUBMIT_ORDER, payload: true, quoteDetails });  
    }
  };

  const handleQuoteEmail = (event) => {
    setQuoteErrors('');
    setQuoteDetails((state) => ({
      ...state,
      emails: event.target.value,
    }));
    console.log({quoteDetails})
  };

  const handleQuoteMessage = (event) => {
    setQuoteDetails((state) => ({
      ...state,
      message: event.target.value,
    }));
    console.log({quoteDetails})
  };

  return (
    <Modal classNames={{ modal: 'reveal customer-form' }} open center showCloseIcon={false} onClose={handleCancel}>
      <div className="pb-1 has-small-gutter">
        <h3 className="modal-title text-center">Order Quote</h3>
        <small className='float-right'>Separate multiple emails with semicolon ';'</small>
        <textarea
          ref={emailsRef}
          placeholder='Email address(es) to send the quote to'
          className={`form-input${quoteErrors ? ' is-invalid-input' : ''}`}
          value={quoteDetails.emails}
          onChange={handleQuoteEmail}
          onKeyUp={handleEnter}
        />
        <a onClick={quoteCustomer}>Click to add customer email <em>`{customer.email}`</em></a>

        <div className='mt-1-2'>
          {!addMessage && (
            <a className='button tiny hollow mt-1-4' style={{ display: 'block', width: '200px' }}onClick={() => setAddMessage(true)}>
              Add Quote Message
            </a>
          )}
          {addMessage && (
            <textarea
              placeholder='Message to be sent with the quote'
              className="form-input mt-1-2"
              value={quoteDetails.message}
              onChange={handleQuoteMessage}
            />
          )}
        </div>
      </div>

      {!!quoteErrors && <p className='is-invalid-label'>{quoteErrors}</p>}
      <div className="form-footer light-gray-bg medium-text-right">
        <a className="button gray-btn small" onClick={handleCancel}>
          Cancel
        </a>
        <a className="button small" onClick={handleQuoteSubmit}>
          {isSubmitting ? <Spinner /> : 'Send Quote'}
        </a>
      </div>
    </Modal>
  );
};

export default QuoteModal;
