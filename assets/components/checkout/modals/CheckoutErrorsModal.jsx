// actions
import { useSelector, useDispatch } from 'react-redux';

// components
import { Modal } from 'react-responsive-modal';
import { CLEAR_MODAL_ERRORS } from 'actions/checkoutActionTypes';

const CheckoutErrorsModal = () => {
  const dispatch = useDispatch();
  const modalErrors = useSelector((state) => state.modalErrors);

  if (!modalErrors) {
    return null;
  }

  const clearErrors = () => {
    dispatch({ type: CLEAR_MODAL_ERRORS });
  };

  return (
    <Modal
      classNames={{ modal: 'reveal customer-form' }}
      open
      onClose={() => clearErrors()}
      center
      showCloseIcon={false}
    >
      <div className="form-content has-small-gutter text-center">
        <h3 className="modal-title">Error</h3>
        <p dangerouslySetInnerHTML={{ __html: modalErrors }} />
      </div>
      <div className="form-footer light-gray-bg medium-text-right">
        <a className="button small" onClick={clearErrors}>
          OK
        </a>
      </div>
    </Modal>
  );
};

export default CheckoutErrorsModal;
