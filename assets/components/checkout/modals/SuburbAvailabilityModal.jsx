import { useContext } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { Modal } from 'react-responsive-modal';
import { CHECK_SUBURB_AVAILABILITY_SUCCESS } from 'actions/checkoutActionTypes';
import adminContext from 'contexts/adminContext';

const SuburbAvailabilityModal = ({ order, suppliers }) => {
  const { isAdmin } = useContext(adminContext);
  const dispatch = useDispatch();

  if (suppliers.length === 0) {
    return null;
  }
  const nonDeliverableSuppliers = suppliers.filter(({ has_delivery_zones }) => !has_delivery_zones);

  const clearDeliverableSuppliers = () => {
    dispatch({ type: CHECK_SUBURB_AVAILABILITY_SUCCESS, payload: [] });
  };

  return (
    <Modal
      classNames={{ modal: 'reveal checkout-modal' }}
      open
      onClose={clearDeliverableSuppliers}
      center
      showCloseIcon={false}
    >
      <div className="form-content has-small-gutter">
        <h2 className="modal-title text-center">Supplier Availability</h2>
        {isAdmin && <h3 className="text-center">WARNING!</h3>}
        {nonDeliverableSuppliers.length == 1 ? (
          <SingleSupplierMessage supplier={nonDeliverableSuppliers[0]} />
        ) : (
          <MultiSupplierMessage suppliers={suppliers} nonDeliverableSuppliers={nonDeliverableSuppliers} />
        )}
        {!isAdmin && (
          <ExtraSteps suppliers={suppliers} nonDeliverableSuppliers={nonDeliverableSuppliers} order={order} />
        )}
      </div>
      <div className="model-footer medium-text-right">
        <a className="button small gray-btn" onClick={clearDeliverableSuppliers}>
          OK
        </a>
      </div>
    </Modal>
  );
};

const SingleSupplierMessage = ({ supplier }) => (
  <p>
    The supplier `{supplier.name}` <strong>DOES NOT SUPPLY</strong> in the selected delivery address / suburb.
  </p>
);

const MultiSupplierMessage = ({ suppliers, nonDeliverableSuppliers }) => (
  <>
    <p>
      {suppliers.length == nonDeliverableSuppliers.length
        ? 'All of the Order Suppliers '
        : 'The following order suppliers '}
      <strong>DO NOT SUPPLY</strong> in the selected delivery address / suburb.
    </p>
    <ul className="bullet-list">
      {nonDeliverableSuppliers.map((supplier, idx) => (
        <li key={`supplier-delivery-${idx}`}> {supplier.name} </li>
      ))}
    </ul>
  </>
);

const ExtraSteps = ({ suppliers, nonDeliverableSuppliers, order }) => {
  if (!order.delivery_suburb_label) return null;

  return (
    <p>
      Please select an address near <em>{order.delivery_suburb_label}</em>
      {suppliers.length != nonDeliverableSuppliers.length && (
        <span>
          , or remove items from
          {nonDeliverableSuppliers.length > 1 ? ' these suppliers ' : ' this supplier '}
          before continuing
        </span>
      )}
      .
    </p>
  );
};

SuburbAvailabilityModal.propTypes = {
  order: PropTypes.object.isRequired,
  suppliers: PropTypes.array.isRequired,
};

export default SuburbAvailabilityModal;
