import { useState } from 'react';
import PropTypes from 'prop-types';

// actions
import { useSelector, useDispatch } from 'react-redux';
import { Modal } from 'react-responsive-modal';
import { UPDATE_MODE } from 'actions/checkoutActionTypes';
import { cancelOrder } from 'actions/checkoutActions';

// loading
import { createLoadingSelector } from 'selectors';

// hooks
import useSupplierChecks from 'hooks/useSupplierChecks';

// components
import Spinner from '../Spinner';

const cancelSelector = createLoadingSelector(['CANCEL_ORDER']);

const CancelOrderModal = ({ setCancelOrder }) => {
  const dispatch = useDispatch();
  const order = useSelector((state) => state.order);
  const { checkLeadTime } = useSupplierChecks({
    order,
  });

  const handleCancel = async () => {
    try {
      setRedirecting(false);
      dispatch({ type: UPDATE_MODE, payload: 'cancel' });
      let isWithinLeadTime = true;
      if (order.status !== 'quoted') {
        isWithinLeadTime = await checkLeadTime();
      }
      if (isWithinLeadTime) {
        await cancelOrder({
          order,
          mode: 'one-off',
          dispatch,
        });
        setRedirecting(true);
        window.location = order.order_path;
      } else {
        setCancelOrder(false);
      }
    } catch (err) {
      setCancelOrder(false);
    }
  };

  const loadingState = useSelector((state) => state.loading);
  const isCancelling = cancelSelector(loadingState, order.id);
  const [redirecting, setRedirecting] = useState(false);

  return (
    <Modal
      classNames={{ modal: 'reveal customer-form' }}
      open
      onClose={() => {
        !isCancelling && !redirecting && setCancelOrder(false);
      }}
      center
      showCloseIcon={false}
    >
      <div className="form-content has-small-gutter text-center">
        {!redirecting && <p>Are you sure you want to {order.is_recurrent ? 'skip' : 'cancel'} this order?</p>}
        {redirecting && <p>Order {order.is_recurrent ? 'skip' : 'cancel'}ed! Redirecting now...</p>}
      </div>
      {!redirecting && (
        <div className="form-footer light-gray-bg medium-text-right">
          <a
            className="button small uppercase gray-btn"
            onClick={() => {
              !isCancelling && setCancelOrder(false);
            }}
          >
            No
          </a>
          <a className="button small uppercase" onClick={handleCancel}>
            {isCancelling ? <Spinner /> : 'Confirm'}
          </a>
        </div>
      )}
    </Modal>
  );
};

CancelOrderModal.propTypes = {
  setCancelOrder: PropTypes.func.isRequired,
};

export default CancelOrderModal;
