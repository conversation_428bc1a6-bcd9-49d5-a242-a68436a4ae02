import { useState } from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-responsive-modal';

import { clearCartPath } from 'routes';

// components
import Spinner from '../Spinner';

const CancelEditModal = ({ setCancelEdit }) => {
  const [redirecting, setRedirecting] = useState(false);

  return (
    <Modal
      classNames={{ modal: 'reveal customer-form' }}
      open
      onClose={() => setCancelEdit(false)}
      center
      showCloseIcon={false}
    >
      <div className="form-content has-small-gutter text-center">
        <h3 className="modal-title">Cancel Order Edit</h3>
        <p>
          Note: Only order details (like name, instructions) won't be saved/updated.
          <br />
          Any item updates (including quantity) are already saved.
        </p>
      </div>
      <div className="form-footer light-gray-bg medium-text-right">
        <a className="button small uppercase gray-btn" onClick={() => setCancelEdit(false)}>
          Close
        </a>
        <a className="button small uppercase" href={clearCartPath()} onClick={() => setRedirecting(true)}>
          {redirecting ? <Spinner /> : 'OK'}
        </a>
      </div>
    </Modal>
  );
};

CancelEditModal.propTypes = {
  setCancelEdit: PropTypes.func.isRequired,
};

export default CancelEditModal;
