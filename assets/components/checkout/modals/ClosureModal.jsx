import { useContext } from 'react';
import PropTypes from 'prop-types';

// actions
import { useSelector, useDispatch } from 'react-redux';
import { Modal } from 'react-responsive-modal';
import {
  CHECK_CLOSURE_DATES_SUCCESS,
  UPDATE_CURRENT_PANEL,
  UPDATE_SUBMIT_NEW_CARD,
  UPDATE_AS_QUOTE_ORDER,
  UPDATE_SUBMIT_ORDER,
  ORDER_PAYMENT_FAILURE,
} from 'actions/checkoutActionTypes';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

const ClosureModal = ({ delivery_at, closureSuppliers }) => {
  const dispatch = useDispatch();
  const order = useSelector((state) => state.order);
  const { mode, newCard } = useSelector((state) => state.form);
  const { panels, isEditPage } = useContext(appContext);
  const { isAdmin } = useContext(adminContext);

  const handleChangeDate = () => {
    const deliveryAtInput = document.getElementsByName('delivery_at')[0];
    deliveryAtInput.focus();
    deliveryAtInput.click(); // to open Datepicker
    clearClosureSuppliers();
  };

  const handleContinue = async () => {
    clearClosureSuppliers();
    if (isEditPage) {
      if (order.credit_card_id != 1 && newCard) {
        dispatch({ type: UPDATE_SUBMIT_NEW_CARD, payload: true });
      } else if (mode === 'quote') {
        dispatch({ type: UPDATE_AS_QUOTE_ORDER, payload: true });
      } else if (order.credit_card_id) {
        dispatch({ type: UPDATE_SUBMIT_ORDER, payload: true });
      } else {
        dispatch({ type: ORDER_PAYMENT_FAILURE });
      }
    } else {
      dispatch({ type: UPDATE_CURRENT_PANEL, payload: panels[1].key });
    }
  };

  const clearClosureSuppliers = () => {
    dispatch({ type: CHECK_CLOSURE_DATES_SUCCESS, payload: [] });
  };

  return (
    <Modal
      classNames={{ modal: 'reveal checkout-modal' }}
      open
      onClose={clearClosureSuppliers}
      center
      showCloseIcon={false}
    >
      <div className="form-content has-small-gutter text-center">
        <h3 className="modal-title">Supplier Closures</h3>
        {closureSuppliers.map((supplier) => (
          <div key={`closure-supplier-${supplier.id}`}>
            <ClosureSupplier supplier={supplier} />
          </div>
        ))}
        <p>Please place your order before or after these dates.</p>
      </div>
      <div className="model-footer medium-text-right">
        <a className="button gray-btn small" onClick={clearClosureSuppliers}>
          Cancel
        </a>
        <a className="button small ml-1" onClick={handleChangeDate}>
          Change Date
        </a>
        {isAdmin && (
          <a className="button small black-btn ml-1" onClick={handleContinue}>
            Continue
          </a>
        )}
      </div>
    </Modal>
  );
};

ClosureModal.propTypes = {
  // delivery_at: PropTypes.string.isRequired, // can also be date
  closureSuppliers: PropTypes.array.isRequired,
};

export default ClosureModal;

const ClosureSupplier = ({ supplier }) => {
  if (supplier?.outside_operating_hours) {
    return (
      <p className="delivery-date-error">
        {supplier.name} operates from {supplier.operating_hours}.
      </p>
    );
  }
  
  if (supplier.close_from == supplier.close_to) {
    return (
      <p>
        {supplier.name} will be closed on {supplier.close_from}.
      </p>
    );
  }

  return (
    <p>
      {supplier.name} will be closed from {supplier.close_from} to {supplier.close_to}.
    </p>
  );
};

ClosureSupplier.propTypes = {
  supplier: PropTypes.object.isRequired,
};
