import { useContext } from 'react';
import PropTypes from 'prop-types';

// actions
import { useSelector, useDispatch } from 'react-redux';
import { Modal } from 'react-responsive-modal';
import {
  UPDATE_CURRENT_PANEL,
  CHECK_LEAD_TIME_SUCCESS,
  UPDATE_SUBMIT_NEW_CARD,
  UPDATE_AS_QUOTE_ORDER,
  UPDATE_SUBMIT_ORDER,
  ORDER_PAYMENT_FAILURE,
} from 'actions/checkoutActionTypes';
import appContext from 'contexts/appContext';

// cancellation actions
import { cancelOrder } from 'actions/checkoutActions';
import adminContext from 'contexts/adminContext';

const LeadTimeModal = ({ supplierLeadTime }) => {
  const dispatch = useDispatch();
  const order = useSelector((state) => state.order);
  const { mode, newCard } = useSelector((state) => state.form);
  const { panels, isEditPage } = useContext(appContext);
  const { isAdmin } = useContext(adminContext);
  const isCancellingOrder = ['skip', 'cancel'].includes(mode);

  const handleChangeDate = () => {
    clearSupplierLeadTime();
    const deliveryAtInput = document.getElementsByName('delivery_at')[0];
    deliveryAtInput.focus();
    deliveryAtInput.click(); // to open Datepicker
  };

  const handleContinue = async () => {
    clearSupplierLeadTime();
    if (isEditPage) {
      if (order.credit_card_id != 1 && newCard) {
        dispatch({ type: UPDATE_SUBMIT_NEW_CARD, payload: true });
      } else if (mode === 'quote') {
        dispatch({ type: UPDATE_AS_QUOTE_ORDER, payload: true });
      } else if (order.credit_card_id) {
        dispatch({ type: UPDATE_SUBMIT_ORDER, payload: true });
      } else {
        dispatch({ type: ORDER_PAYMENT_FAILURE });
      }
    } else {
      dispatch({ type: UPDATE_CURRENT_PANEL, payload: panels[1].key });
    }
  };

  const clearSupplierLeadTime = () => {
    dispatch({ type: CHECK_LEAD_TIME_SUCCESS, payload: null });
  };

  const handleCancelation = async () => {
    await cancelOrder({
      order,
      mode: 'one-off',
      dispatch,
    });
    window.location = order.order_path;
  }

  if (isCancellingOrder) {
    return (
      <Modal classNames={{ modal: 'reveal checkout-modal' }} open onClose={clearSupplierLeadTime} center showCloseIcon>
        <div className="text-center">
          <h2 className="modal-title">Supplier Lead Times</h2>
          <p>Sorry, this order can no longer be cancelled because the order has passed the supplier lead time.</p>
        </div>
        <div className="modal-footer light-gray-bg medium-text-right">
          <a className="button gray-btn small" onClick={clearSupplierLeadTime}>
            Ok
          </a>
          {isAdmin && (
            <a className="button small ml-1" onClick={handleCancelation}>
              Continue Anyways
              <small className='mt-1-4' style={{ display: 'block' }}>(admin override)</small>
            </a>  
          )}
        </div>
      </Modal>
    );
  }

  // else
  return (
    <Modal
      classNames={{ modal: 'reveal checkout-modal' }}
      open
      onClose={clearSupplierLeadTime}
      center
      showCloseIcon={false}
    >
      <div className="text-center">
        <h2 className="modal-title">Supplier Lead Times</h2>
        <p>The delivery date you've selected does not give the supplier enough time to prepare for the order.</p>
        <p>
          The supplier's lead time is: <strong>{supplierLeadTime.formatted_lead_time}</strong>
        </p>
        <p>
          If you continue with this order, it might get <strong>rejected</strong>.
        </p>
        <small>
          N.B. You can successfully place an order with delivery after {supplierLeadTime.formatted_minimum_delivery_at}.
        </small>
      </div>

      <div className="modal-footer light-gray-bg medium-text-right">
        <a className="button gray-btn small" onClick={handleContinue}>
          Continue
        </a>
        <a className="button small ml-1" onClick={handleChangeDate}>
          Change Date
        </a>
      </div>
    </Modal>
  );
};

LeadTimeModal.propTypes = {
  supplierLeadTime: PropTypes.object.isRequired,
};

export default LeadTimeModal;
