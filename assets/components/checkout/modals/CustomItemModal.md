# Custom Item Modal

A comprehensive modal component for creating custom items with full item details and order line information.

## Features

### Item Details
- **Name**: Required text field (max 255 characters)
- **Description**: Optional textarea for item description
- **Baseline Price**: Required numeric field for supplier cost
- **Customer Price**: Auto-calculated marked-up price with GST
- **GST Options**: Toggle for GST-free items
- **Save for Later**: Option to save item for future use

### Dietary Flags
Support for all standard dietary preferences:
- Vegetarian (V)
- Vegan (VE) 
- Gluten Free (GF)
- Dairy Free (DF)
- Egg Free (EF)
- Nut Free (NF)
- Halal (H)
- <PERSON><PERSON> (K)
- Individually Packed (IP)

### Order Line Details
- **Quantity**: Required numeric field (minimum 1)
- **Total Price**: Auto-calculated based on quantity and customer price
- **Special Instructions**: Optional textarea for order notes

## Usage

### Basic Implementation

```jsx
import CustomItemModal from './modals/CustomItemModal';

const MyComponent = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleItemAdded = (orderLine) => {
    console.log('New custom item added:', orderLine);
    // Handle the new order line (add to cart, update state, etc.)
  };

  return (
    <>
      <button onClick={() => setIsModalOpen(true)}>
        Add Custom Item
      </button>

      <CustomItemModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        supplierId={123}
        onItemAdded={handleItemAdded}
      />
    </>
  );
};
```

### Using the Trigger Component

For simpler integration, use the provided trigger component:

```jsx
import CustomItemTrigger from '../CustomItemTrigger';

const MyComponent = () => {
  const handleItemAdded = (orderLine) => {
    // Handle the new order line
  };

  return (
    <CustomItemTrigger
      supplierId={123}
      onItemAdded={handleItemAdded}
      buttonText="Add Custom Item"
      buttonClass="button primary-btn"
    />
  );
};
```

## Props

### CustomItemModal

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `isOpen` | boolean | Yes | Controls modal visibility |
| `onClose` | function | Yes | Callback when modal is closed |
| `supplierId` | string/number | Yes | ID of the supplier for the custom item |
| `onItemAdded` | function | No | Callback when item is successfully added |

### CustomItemTrigger

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `supplierId` | string/number | Yes | - | ID of the supplier |
| `onItemAdded` | function | No | - | Callback when item is added |
| `buttonText` | string | No | "Add Custom Item" | Text for trigger button |
| `buttonClass` | string | No | "button small" | CSS classes for button |

## API Integration

The modal expects to call a custom order line API endpoint:

```javascript
POST /api/custom_order_lines
Content-Type: application/json

{
  "order_line": {
    "menu_item_description": "Custom Item Name",
    "description": "Item description",
    "baseline": 10.50,
    "supplier_id": 123,
    "quantity": 2,
    "note": "Special instructions",
    "gst_option": "gst_free", // or "with_gst"
    "is_vegetarian": true,
    "is_vegan": false,
    // ... other dietary flags
    "save_for_later": false
  }
}
```

Expected response:
```javascript
{
  "success": true,
  "order_line": {
    "id": 456,
    "name": "Custom Item Name",
    "quantity": 2,
    "price": 12.60,
    // ... other order line data
  }
}
```

## Styling

The modal uses the following CSS classes:
- `.custom-item-modal` - Main modal container
- `.custom-item-form` - Form styling
- `.custom-item-section` - Section containers
- `.dietary-flag-letter` - Dietary flag badges

All styling is defined in `app/assets/stylesheets/components/_custom-item-modal.scss`.

## Price Calculation

The modal automatically calculates:
1. **Marked-up Price**: Baseline price + supplier markup (currently hardcoded to 20%)
2. **GST**: Added unless item is marked as GST-free
3. **Total Price**: Customer price × quantity

## Validation

- Item name is required (max 255 characters)
- Baseline price is required (minimum 0)
- Quantity is required (minimum 1)
- Form cannot be submitted while loading

## Responsive Design

The modal is fully responsive and adapts to mobile screens:
- Single column layout on mobile
- Adjusted padding and spacing
- Optimized dietary flags grid

## Integration Points

This modal integrates with:
- Existing custom order line service (`OrderLines::UpsertCustomOrderLine`)
- Supplier markup calculations
- GST calculations based on country
- Dietary preference system
- Order management system
