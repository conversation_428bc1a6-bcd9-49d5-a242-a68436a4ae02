// hooks
import { useSelector } from 'react-redux';
import useCheckoutDetails from 'hooks/useCheckoutDetails';

// actions

// components
import CheckoutErrorsModal from './modals/CheckoutErrorsModal';
import HeaderNav from './HeaderNav';
import FooterNav from './FooterNav';
import FormSkeleton from './FormSkeleton';
import OrderDetails from './order/OrderDetails';
import BillingDetails from './billing/BillingDetails';
import PaymentDetails from './payment/PaymentDetails';
import OrderSuccess from './order/OrderSuccess';

const CheckoutApp = () => {
  const { currentPanel } = useSelector((state) => state.form);
  const { checkoutPanels, isEditPage } = useCheckoutDetails();

  return (
    <div className="checkout-form">
      <CheckoutErrorsModal />
      {!isEditPage && currentPanel !== 'order-success' && (
        <HeaderNav panels={checkoutPanels} currentPanel={currentPanel} />
      )}
      {(isEditPage || currentPanel === 'order-details') && <OrderDetails />}
      {!isEditPage && currentPanel === 'billing-details' && <BillingDetails />}
      {(isEditPage || currentPanel === 'payment-details') && <PaymentDetails />}
      {!isEditPage && currentPanel === 'order-success' && <OrderSuccess />}

      {!isEditPage && !currentPanel && <FormSkeleton />}

      <FooterNav currentPanel={currentPanel} isEditPage={isEditPage} />
    </div>
  );
};

export default CheckoutApp;
