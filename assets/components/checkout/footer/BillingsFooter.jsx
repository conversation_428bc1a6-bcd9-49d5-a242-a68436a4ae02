// actions
import { useSelector, useDispatch } from 'react-redux';
import { UPDATE_CURRENT_PANEL } from 'actions/checkoutActionTypes';

// hooks
import useValidateForm from 'hooks/useValidateForm';

const BILLING_REQUIRED_FIELDS = ['email', 'phone', 'address', 'name', 'suburb_id'];

const BillingsFooter = () => {
  const dispatch = useDispatch();
  const billingDetails = useSelector((state) => state.billing_details);

  const formValidator = useValidateForm();

  const handlePanelChange = (panel, withValidation) => {
    if (withValidation) {
      const isValid = formValidator({
        object: billingDetails,
        fields: BILLING_REQUIRED_FIELDS,
      });
      if (isValid) {
        dispatch({ type: UPDATE_CURRENT_PANEL, payload: panel });
      }
    } else {
      dispatch({ type: UPDATE_CURRENT_PANEL, payload: panel });
    }
  };

  return (
    <div className="between-flex checkout-btns">
      <button
        className="checkout-step-btn checkout-btns__back"
        type="button"
        onClick={() => handlePanelChange('order-details', false)}
      >
        Back to Order Details
      </button>
      <button
        className="checkout-step-btn checkout-btns__forward"
        type="button"
        onClick={() => handlePanelChange('payment-details', true)}
      >
        Payment Details
      </button>
    </div>
  );
};

export default BillingsFooter;
