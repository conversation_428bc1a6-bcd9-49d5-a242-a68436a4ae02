import { useContext } from 'react';

// actions
import { useSelector, useDispatch } from 'react-redux';
import { UPDATE_CURRENT_PANEL } from 'actions/checkoutActionTypes';
import appContext from 'contexts/appContext';

// hooks
import useValidateForm from 'hooks/useValidateForm';
import useSupplierChecks from 'hooks/useSupplierChecks';

// components
import { createLoadingSelector } from 'selectors';
import Spinner from '../Spinner';

// loader

const loadingSelector = createLoadingSelector(['CHECK_CLOSURE_DATES', 'CHECK_LEAD_TIME']);

const ORDER_REQUIRED_FIELDS = [
  'name',
  'delivery_at',
  'delivery_address',
  'delivery_instruction',
  'contact_name',
  'phone',
];

const DetailsFooter = () => {
  const dispatch = useDispatch();
  const order = useSelector((state) => state.order);
  const customer = useSelector((state) => state.customer);
  const loadingState = useSelector((state) => state.loading);
  const { panels } = useContext(appContext);

  const formValidator = useValidateForm();
  const { checkClosureDates, checkLeadTime } = useSupplierChecks({
    order,
  });

  const isLoading = loadingSelector(loadingState);

  const handleNext = async (panel) => {
    if (isLoading) return;

    const requiredFields = ORDER_REQUIRED_FIELDS;
    if (order.is_catering_order) requiredFields.push('number_of_people');
    if (customer.requires_purchase_order) requiredFields.push('cpo_id');
    if (customer.required_department_identity_format) requiredFields.push('department_identity');
    const isValid = formValidator({
      object: order,
      fields: requiredFields,
    });
    const outsideClosureDates = isValid && (await checkClosureDates());
    const withinLeadTimes = outsideClosureDates && (await checkLeadTime());
    if (isValid && outsideClosureDates && withinLeadTimes) {
      dispatch({ type: UPDATE_CURRENT_PANEL, payload: panels[1].key });
    }
  };

  return (
    <div className="checkout-btns">
      <a className="checkout-btns__back" href="/order/summary">
        Back to Order Review
      </a>
      <button className="checkout-step-btn checkout-btns__forward" type="button" onClick={handleNext}>
        {isLoading ? <Spinner /> : panels[1].label}
      </button>
    </div>
  );
};

export default DetailsFooter;
