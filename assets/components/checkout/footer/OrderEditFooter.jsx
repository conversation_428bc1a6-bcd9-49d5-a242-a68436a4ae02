import { useState, useContext, useEffect } from 'react';

// actions
import { useSelector, useDispatch } from 'react-redux';
import {
  UPDATE_SUBMIT_NEW_CARD,
  ORDER_PAYMENT_FAILURE,
  UPDATE_MODE,
  UPDATE_AS_QUOTE_ORDER,
  UPDATE_SUBMIT_ORDER,
} from 'actions/checkoutActionTypes';

// hooks
import useValidateForm from 'hooks/useValidateForm';
import useSupplierChecks from 'hooks/useSupplierChecks';

// loader
import { createLoadingSelector } from 'selectors';

// components
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';
import CancelEditModal from '../modals/CancelEditModal';
import CancelOrderModal from '../modals/CancelOrderModal';
import ApproveOrderModal from '../modals/ApproveOrderModal';
import Spinner from '../Spinner';

// context

const submitSelector = createLoadingSelector(['SUBMIT_ORDER']);
const cardSumbitSelector = createLoadingSelector(['SAVE_STRIPE_CARD', 'SAVE_YORDAR_CARD']);
const supplierCheckSelector = createLoadingSelector(['CHECK_CLOSURE_DATES', 'CHECK_LEAD_TIME']);

const ORDER_REQUIRED_FIELDS = [
  'name',
  'delivery_at',
  'delivery_address',
  'delivery_instruction',
  'contact_name',
  'phone',
];

const OrderEditFooter = () => {
  const dispatch = useDispatch();
  const order = useSelector((state) => state.order);
  const isPayOnAccount = order.credit_card_id == 1;
  const isQuoteOrder = order.status === 'quoted';

  const [cancelEdit, setCancelEdit] = useState(false);
  const [cancelOrder, setCancelOrder] = useState(false);
  const [approveOrder, setApproveOrder] = useState(false);

  const customer = useSelector((state) => state.customer);
  const { newCard, mode } = useSelector((state) => state.form);
  const isQuoting = ['quote', 'save-quote'].includes(mode) || order.status === 'quoted';
  const isRecurringEdit = order.is_recurrent && order.status !== 'delivered';

  const loadingState = useSelector((state) => state.loading);
  const isChecking = supplierCheckSelector(loadingState);
  const isSubmittingCard = cardSumbitSelector(loadingState);
  const isSubmitting = submitSelector(loadingState, order.id);

  const isProcessing = isChecking || isSubmitting || isSubmittingCard;

  const { finaliseQuote } = useContext(appContext);

  const formValidator = useValidateForm();
  const { checkClosureDates, checkLeadTime } = useSupplierChecks({
    order,
  });

  // check form validations
  // check order outside closure dates
  // check order within supplier lead time
  const handleSubmit = async (submitMode) => {
    if (isProcessing) return;

    dispatch({ type: UPDATE_MODE, payload: submitMode });

    const requiredFields = ORDER_REQUIRED_FIELDS;
    if (order.is_catering_order) requiredFields.push('number_of_people');
    if (customer.requires_purchase_order) requiredFields.push('cpo_id');
    if (customer.requires_purchase_order && order.requires_gst_free_purchase_order)
      requiredFields.push('gst_free_cpo_id');
    if (customer.required_department_identity_format) requiredFields.push('department_identity');
    const formIsValid = formValidator({
      object: order,
      fields: requiredFields,
    });

    let validChecks = false;
    if (order.status === 'delivered') {
      validChecks = true;
    } else {
      const outsideClosureDates = formIsValid && (await checkClosureDates());
      const withinLeadTimes = outsideClosureDates && (await checkLeadTime());
      validChecks = outsideClosureDates && withinLeadTimes;
    }

    if (formIsValid && validChecks) {
      if (!isPayOnAccount && newCard) {
        dispatch({ type: UPDATE_SUBMIT_NEW_CARD, payload: true });
      } else if (submitMode === 'quote') {
        dispatch({ type: UPDATE_AS_QUOTE_ORDER, payload: true });
      } else if (submitMode === 'save-quote' || order.credit_card_id) {
        dispatch({ type: UPDATE_SUBMIT_ORDER, payload: true });
      } else {
        dispatch({ type: ORDER_PAYMENT_FAILURE });
      }
    }
  };

  useEffect(() => {
    if (!order?.id) return;

    if (order.status === 'quoted' && finaliseQuote) setApproveOrder(true);
  }, [order.id]);

  return (
    <div className="between-flex checkout-btns form-footer">
      <div>
        {!['delivered', 'cancelled', 'skipped'].includes(order.status) && (
          <a className="button small alert-btn mr-1-4" onClick={() => setCancelOrder(true)}>
            {`${order.is_recurrent ? 'Skip/Cancel' : 'Cancel'} this order`}
          </a>
        )}
        <a onClick={() => setCancelEdit(true)} className="button small gray-btn">
          Cancel Edit
        </a>
      </div>
      <div>
        {isQuoteOrder && (
          <>
            <a className="button small gray-btn" onClick={() => handleSubmit('save-quote')}>
              {isQuoting && mode === 'save-quote' && isProcessing ? <Spinner /> : 'Save as Quote'}
            </a>
            <a className="button small hollow" onClick={() => handleSubmit('quote')}>
              {isQuoting && mode === 'quote' && isProcessing ? <Spinner /> : 'Send Quote'}
            </a>
          </>
        )}
        {isRecurringEdit && (
          <>
            <a className="button small save-all mr-1-4" onClick={() => handleSubmit('subsequent')}>
              {!isQuoting && isProcessing && mode == 'subsequent' ? <Spinner /> : 'Save All Future Orders'}
            </a>
            <a className="button small" onClick={() => handleSubmit('one-off')}>
              {!isQuoting && isProcessing && mode == 'one-off' ? <Spinner /> : 'Save This Only'}
            </a>
          </>
        )}
        {!isRecurringEdit && !isQuoting && (
          <a className="button small" onClick={() => handleSubmit('one-off')}>
            {isProcessing ? <Spinner /> : 'Save'}
          </a>
        )}
        {!isRecurringEdit && isQuoting && (
          <a className="button small" onClick={() => setApproveOrder(true)}>
            {isProcessing && !['save-quote', 'quote'].includes(mode) ? <Spinner /> : 'Approve'}
          </a>
        )}
      </div>
      {cancelEdit && <CancelEditModal setCancelEdit={setCancelEdit} />}
      {cancelOrder && <CancelOrderModal setCancelOrder={setCancelOrder} />}
      {approveOrder && (
        <ApproveOrderModal
          order={order}
          handleSubmit={handleSubmit}
          isProcessing={isProcessing}
          setApproveOrder={setApproveOrder}
        />
      )}
    </div>
  );
};

export default OrderEditFooter;
