import { useContext } from 'react';
import { useSelector, useDispatch } from 'react-redux';

// actions
import {
  UPDATE_CURRENT_PANEL,
  ORDER_PAYMENT_FAILURE,
  UPDATE_SUBMIT_NEW_CARD,
  UPDATE_MODE,
  UPDATE_AS_QUOTE_ORDER,
  UPDATE_SUBMIT_ORDER,
} from 'actions/checkoutActionTypes';

// context
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

// components
import { createLoadingSelector } from 'selectors';
import Spinner from '../Spinner';

// loader

const submitSelector = createLoadingSelector(['SUBMIT_ORDER']);
const cardSumbitSelector = createLoadingSelector(['SAVE_STRIPE_CARD', 'SAVE_YORDAR_CARD']);

const PaymentsFooter = () => {
  const { panels, isWoolworths, showBillingDetails } = useContext(appContext);
  const { isAdmin } = useContext(adminContext);
  const order = useSelector((state) => state.order);
  const isPayOnAccount = order.credit_card_id == 1;
  const canQuote = isAdmin && !order.is_recurrent && !isWoolworths;

  const loadingState = useSelector((state) => state.loading);
  const isSubmitting = submitSelector(loadingState, order.id);
  const isSubmittingCard = cardSumbitSelector(loadingState);

  const { newCard } = useSelector((state) => state.form);

  const dispatch = useDispatch();

  const handlePanelChange = (panel) => {
    dispatch({ type: UPDATE_CURRENT_PANEL, payload: panel });
  };

  const handlePlaceOrder = (submitMode) => {
    if (isSubmitting || isSubmittingCard) return;

    dispatch({ type: UPDATE_MODE, payload: submitMode });

    if (!isPayOnAccount && newCard) {
      dispatch({ type: UPDATE_SUBMIT_NEW_CARD, payload: true });
    } else if (submitMode === 'quote') {
      dispatch({ type: UPDATE_AS_QUOTE_ORDER, payload: true });
    } else if (order.credit_card_id) {
      dispatch({ type: UPDATE_SUBMIT_ORDER, payload: true });
    } else {
      dispatch({ type: ORDER_PAYMENT_FAILURE });
    }
  };

  return (
    <div className="between-flex checkout-btns">
      {showBillingDetails && (
        <button
          className="checkout-step-btn checkout-btns__back"
          type="button"
          onClick={() => handlePanelChange(panels[1].key)}
        >
          Back to {panels[1].label}
        </button>
      )}
      {!showBillingDetails && (
        <button
          className="checkout-step-btn checkout-btns__back"
          type="button"
          onClick={() => handlePanelChange(panels[0].key)}
        >
          Back to {panels[0].label}
        </button>
      )}

      <div>
        {canQuote && (
          <button
            className="checkout-btns__forward checkout-btns__alternate save-all mr-1"
            type="button"
            onClick={() => handlePlaceOrder('quote')}
          >
            Save as Quote
          </button>
        )}
        <button className="checkout-btns__forward" type="button" onClick={() => handlePlaceOrder('submit')}>
          {isSubmittingCard || isSubmitting ? <Spinner /> : 'Place Order'}
        </button>
      </div>
    </div>
  );
};

export default PaymentsFooter;
