import PropTypes from 'prop-types';

// actions
import { useSelector, useDispatch } from 'react-redux';
import { UPDATE_ORDER_FIELD } from 'actions/checkoutActionTypes';

const PayOnAccount = ({ order }) => {
   const dispatch = useDispatch();
   const { billing_preference: billingPreference } = useSelector((state) => state.customer);

   const handleChange = (event) => {
      dispatch({ type: UPDATE_ORDER_FIELD, payload: { [event.target.name]: !!event.target.value } });
   };

   return (
      <div className="small-12 columns payment-options__panel">
         <div className="payment-options__panel--content">
            {billingPreference == 'instantly' && (
               <>
                  <h5> Individual Billing</h5>
                  <p>
                     This order will be invoiced individually on delivery. You've currently set your account to be billed per
                     order.
                  </p>
               </>
            )}
            {billingPreference != 'instantly' && (
               <>
                  <h5>Choose Billing Method</h5>
                  <p>
                     You've currently set your account to be billed {billingPreference}. You can choose to either add this to
                     your {billingPreference} billing or invoice it separately as an individual order.
                  </p>
                  <select
                     className="form-input"
                     name="invoice_individually"
                     value={order.invoice_individually}
                     onChange={handleChange}
                  >
                     <option value="">Add to my {billingPreference} billing</option>
                     <option value>Invoice separately from {billingPreference} billing</option>
                  </select>
               </>
            )}
         </div>
      </div>
   );
};

PayOnAccount.propTypes = {
   order: PropTypes.object.isRequired,
};

export default PayOnAccount;
