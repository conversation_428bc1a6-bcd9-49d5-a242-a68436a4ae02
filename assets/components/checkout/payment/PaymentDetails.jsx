// actions
import { useSelector } from 'react-redux';

// components
import PaymentOptions from './PaymentOptions';
import PayOnAccount from './PayOnAccount';
import PayByCard from './PayByCard';

const PaymentDetails = () => {
  const order = useSelector((state) => state.order);
  const isPayOnAccount = order.credit_card_id == 1;

  return (
    <div className="checkout-panel payment-details">
      <div className="edit_order order-details__container">
        <div className="form-heading payment-details_icon">
          <h3 className="text-center">Payment Details</h3>
        </div>

        <div className="checkout-container payment-details__container">
          <div className="row">
            <PaymentOptions />
            {!!isPayOnAccount && <PayOnAccount order={order} />}
            {!isPayOnAccount && <PayByCard order={order} />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentDetails;
