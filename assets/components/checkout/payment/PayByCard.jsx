import { useState, useEffect, useContext } from 'react';

// actions
import { useSelector, useDispatch } from 'react-redux';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import {
  UPDATE_ORDER_PAY_BY_CARD,
  UPDATE_NEW_CARD,
  UPDATE_AS_QUOTE_ORDER,
  UPDATE_SUBMIT_ORDER,
} from 'actions/checkoutActionTypes';

// context
import appContext from 'contexts/appContext';

// components
import CardOptions from './CardOptions';
import NewCreditCardForm from './NewCreditCardForm';

const PayByCard = ({ order }) => {
  const customer = useSelector((state) => state.customer);
  const { newCard, mode } = useSelector((state) => state.form);
  const { stripeKey } = useContext(appContext);
  const [customerCards, setCustomerCards] = useState([]);
  const selectedCard = !!customerCards.length && customerCards.find((card) => card.id === order.credit_card_id);
  const stripePromise = loadStripe(stripeKey);

  const dispatch = useDispatch();

  const handleNewCardChange = () => {
    if (!newCard) dispatch({ type: UPDATE_ORDER_PAY_BY_CARD, payload: null });
    dispatch({ type: UPDATE_NEW_CARD, payload: !newCard });
  };

  useEffect(() => {
    setCustomerCards(customer.saved_credit_cards);
    dispatch({ type: UPDATE_NEW_CARD, payload: !customer.saved_credit_cards.length });
  }, [customer.saved_credit_cards]);

  useEffect(async () => {
    if (selectedCard && selectedCard.is_new) {
      if (mode === 'quote') {
        dispatch({ type: UPDATE_AS_QUOTE_ORDER, payload: true });
      } else {
        dispatch({ type: UPDATE_SUBMIT_ORDER, payload: true });
      }
    }
  }, [order.credit_card_id]);

  return (
    <div className="small-12 columns payment-options__panel">
      <div className="payment-options__panel--content">
        <div className="payment-options__panel--card-heading">
          <h5>{newCard ? 'Add Credit Card' : 'Select Credit Card'}</h5>
          {!!customerCards.length && (
            <a className="add-new-credit-card-btn button small" onClick={handleNewCardChange}>
              {newCard ? 'Saved Cards' : 'Add New Card'}
            </a>
          )}
        </div>

        <p>This order will be individually invoiced.</p>

        {!newCard && <CardOptions order={order} customerCards={customerCards} selectedCard={selectedCard} />}
        {newCard && (
          <Elements stripe={stripePromise}>
            <NewCreditCardForm order={order} customerCards={customerCards} setCustomerCards={setCustomerCards} />
          </Elements>
        )}
      </div>
    </div>
  );
};

export default PayByCard;
