import PropTypes from 'prop-types';

// actions
import { useDispatch } from 'react-redux';
import { customerPaymentOptionsPath } from 'routes';
import { UPDATE_ORDER_PAY_BY_CARD } from 'actions/checkoutActionTypes';

const CardOptions = ({ order, customerCards, selectedCard }) => {
  const dispatch = useDispatch();

  const handleCardChange = (event) => {
    dispatch({ type: UPDATE_ORDER_PAY_BY_CARD, payload: event.target.value });
  };

  return (
    <>
      <select
        name="credit_card_id"
        className="form-input visa"
        value={order.credit_card_id || ''}
        onChange={handleCardChange}
      >
        <option value="">Please Select</option>
        {customerCards.map((card) => (
          <option key={`payment-card-${card.id}`} value={card.id}>
            {`${card.brand_label} (ending in ${card.last4}) -> ${card.name}`}{' '}
          </option>
        ))}
      </select>

      {!!selectedCard && (
        <div className="card-charge-note">
          {selectedCard.brand_label} credit card payments will incur a {selectedCard.surcharge_percent}%{' '}
          {!!selectedCard.surcharge_fee && `+ ${selectedCard.surcharge_fee * 100}c surcharge`}.
          {!!selectedCard.is_old && (
            <small style={{ display: 'block' }}>
              <strong className="card-deprecation">
                This card is saved in our older system and we advise not to use it.
                <br />
                Please{' '}
                <a href={customerPaymentOptionsPath()} target="_blank" rel="noreferrer">
                  re-enter your card details
                </a>{' '}
                or add a new card.
              </strong>
            </small>
          )}
        </div>
      )}
    </>
  );
};

CardOptions.propTypes = {
  order: PropTypes.object.isRequired,
  customerCards: PropTypes.array.isRequired,
};

export default CardOptions;
