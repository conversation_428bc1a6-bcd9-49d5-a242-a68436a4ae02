// actions
import { useDispatch, useSelector } from 'react-redux';
import { UPDATE_ORDER_PAY_ON_ACCOUNT, UPDATE_ORDER_PAY_BY_CARD } from 'actions/checkoutActionTypes';

const PaymentOptions = () => {
  const order = useSelector((state) => state.order);
  const customer = useSelector((state) => state.customer);
  const dispatch = useDispatch();
  const currentPaymentOption = order.credit_card_id == 1 ? 'pay-on-account' : 'pay-by-card';

  const handleChange = (paymentOption) => {
    if (currentPaymentOption === paymentOption) return;

    if (customer.can_pay_on_account && paymentOption === 'pay-on-account') {
      dispatch({ type: UPDATE_ORDER_PAY_ON_ACCOUNT });
    } else if (customer.can_pay_by_credit_card && paymentOption === 'pay-by-card') {
      let { credit_card_id } = order;
      if (customer.saved_credit_cards.length) {
        credit_card_id = customer.saved_credit_cards[0].id;
      } else if (credit_card_id == 1) {
        credit_card_id = null;
      }
      dispatch({ type: UPDATE_ORDER_PAY_BY_CARD, payload: credit_card_id });
    }
  };

  const validPaymentOptions = [
    {
      key: 'pay-on-account',
      label: 'Pay on Account',
      active: currentPaymentOption === 'pay-on-account',
      disabled: !customer.can_pay_on_account,
    },
    {
      key: 'pay-by-card',
      label: 'Pay by Credit Card',
      active: currentPaymentOption === 'pay-by-card',
      disabled: !customer.can_pay_by_credit_card,
    },
  ];

  return (
    <div className="small-12 columns no-gutter">
      <div className="payment-options">
        {validPaymentOptions.map((option) => (
          <div
            key={`payment-options-${option.key}`}
            className={`payment-options__toggle ${option.disabled ? 'disabled' : ''} ${option.active ? 'active' : ''}`}
            onClick={() => handleChange(option.key)}
          >
            <p>
              {option.label}
              {option.disabled && ' (not approved)'}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PaymentOptions;
