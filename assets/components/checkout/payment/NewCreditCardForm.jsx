import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import { useSelector, useDispatch } from 'react-redux';
import { useStripe, useElements, CardNumberElement, CardExpiryElement, CardCvcElement } from '@stripe/react-stripe-js';
import { UPDATE_NEW_CARD, UPDATE_AS_QUOTE_ORDER, UPDATE_SUBMIT_NEW_CARD } from 'actions/checkoutActionTypes';
import { saveStripeCard, saveYordarCard } from 'actions/cardActions';

// hooks
import useValidateForm from 'hooks/useValidateForm';

// components

const REQUIRED_CARD_FIELDS = ['cardholder_name'];

const NewCreditCardForm = ({ customerCards, setCustomerCards }) => {
  const customer = useSelector((state) => state.customer);
  const { submitNewCard, mode, errors } = useSelector((state) => state.form);
  const dispatch = useDispatch();
  const newCardStripe = useStripe();
  const newCardElements = useElements();

  const initialCard = {
    cardholder_name: '',
    email: customer.email,
    saved_for_future: true,
  };

  const [creditCard, setCreditCard] = useState(initialCard);
  const formValidator = useValidateForm();

  const handleChange = (event) => {
    let { value } = event.target;
    if (event.target.name === 'saved_for_future') {
      value = event.target.checked;
    }
    setCreditCard({ ...creditCard, [event.target.name]: value });
  };

  useEffect(() => {
    if (submitNewCard) {
      submitStripeCard();
      dispatch({ type: UPDATE_SUBMIT_NEW_CARD, payload: false });
    }
  }, [submitNewCard]);

  const submitStripeCard = () => {
    const isValid = formValidator({
      object: creditCard,
      fields: REQUIRED_CARD_FIELDS,
    });
    if (!isValid && mode === 'quote') {
      dispatch({ type: UPDATE_AS_QUOTE_ORDER, payload: true });
    } else if (isValid) {
      dispatch(
        saveStripeCard({
          stripe: newCardStripe,
          stripeElements: newCardElements,
          creditCard: { ...creditCard, name: creditCard.cardholder_name },
          setCreditCard,
        })
      );
    }
  };

  // saved credit card to Stripe (from handleSubmit)
  useEffect(() => {
    if (creditCard.stripe_token) {
      dispatch(
        saveYordarCard({
          creditCard,
          setCreditCard,
        })
      );
    }
  }, [creditCard.stripe_token]);

  // saved credit card to Yordar
  useEffect(() => {
    if (creditCard.id) {
      setCustomerCards([...customerCards, creditCard]); // add card to list
      dispatch({ type: UPDATE_NEW_CARD, payload: false });
    }
  }, [creditCard.id]);

  return (
    <>
      <div className="row">
        <div className="small-12 medium-12 columns">
          <label className="uppercase">Card number</label>
          <div className={`stripe-element form-input ${errors.card_number ? 'is-invalid-input' : ''}`}>
            <CardNumberElement />
          </div>
          {!!errors.card_number && <span className="form-error is-visible">{errors.card_number}</span>}
        </div>
      </div>
      <div className="row">
        <div className="small-12 medium-6 columns">
          <label className="uppercase">Name on card</label>
          <input
            required="required"
            className={`form-input ${errors.cardholder_name ? 'is-invalid-input' : ''}`}
            type="text"
            name="cardholder_name"
            placeholder={`(e.g. ${customer.name})`}
            value={creditCard.name}
            onChange={handleChange}
          />
          {!!errors.cardholder_name && <span className="form-error is-visible">Please enter a card holder name.</span>}
        </div>
        <div className="small-12 medium-4 columns">
          <label className="uppercase">Expiry Dates</label>
          <div className={`stripe-element form-input ${errors.card_expiry ? 'is-invalid-input' : ''}`}>
            <CardExpiryElement />
          </div>
          {!!errors.card_expiry && <span className="form-error is-visible">{errors.card_expiry}</span>}
        </div>
        <div className="small-6 medium-2 columns">
          <label className="uppercase">CVV</label>
          <div className={`stripe-element form-input ${errors.card_cvv ? 'is-invalid-input' : ''}`}>
            <CardCvcElement />
          </div>
          {!!errors.card_cvv && <span className="form-error is-visible">{errors.card_cvv}</span>}
        </div>
      </div>
      <div className="row">
        <div className="small-12 medium-6 columns">
          <label className="uppercase">
            <input
              type="checkbox"
              name="saved_for_future"
              checked={creditCard.saved_for_future}
              onChange={handleChange}
            />
            Save card for future use
          </label>
        </div>
      </div>
      <div className="small-12 columns no-gutter">
        <div className="small-12 columns hidden card-charge-note" id="new-credit-card-charge-note">
          <span className="card-type" />
          credit card payments will incur a<span className="card-charge" />+<span className="card-fee" />
          surcharge.
        </div>
      </div>
    </>
  );
};

NewCreditCardForm.propTypes = {
  order: PropTypes.object.isRequired,
  customerCards: PropTypes.array.isRequired,
  setCustomerCards: PropTypes.func.isRequired,
};

export default NewCreditCardForm;
