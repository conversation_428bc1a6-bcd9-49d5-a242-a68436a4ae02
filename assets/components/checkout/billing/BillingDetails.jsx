import { useRef, useEffect, useContext } from 'react';

// actions
import { useDispatch, useSelector } from 'react-redux';
import { UPDATE_BILLING_FIELD, UPDATE_BILLING_SUBURB } from 'actions/checkoutActionTypes';
import appContext from 'contexts/appContext';

// components
import GoogleLocationInput from '../GoogleLocationInput';

const BillingDetails = () => {
  const dispatch = useDispatch();
  const { errors } = useSelector((state) => state.form);
  const billingDetails = useSelector((state) => state.billing_details);
  const { countryCode } = useContext(appContext);

  const handleChange = (event) => {
    dispatch({ type: UPDATE_BILLING_FIELD, payload: { [event.target.name]: event.target.value } });
  };

  const emailsRef = useRef(null);
  useEffect(() => {
    if (emailsRef && emailsRef.current) {
      emailsRef.current.style.height = 'auto';
      emailsRef.current.style.height = `${emailsRef.current.scrollHeight + 10}px`;
    }
  }, [billingDetails.email]);

  const addressRef = useRef(null);
  useEffect(() => {
    if (addressRef && addressRef.current) {
      addressRef.current.style.height = 'auto';
      addressRef.current.style.height = `${addressRef.current.scrollHeight + 20}px`;
    }
  }, [billingDetails.address]);

  const handleEmailEnter = (event) => {
    if (event.which === 13 || event.keyCode === 13 || event.keyCode === 32) {
      let emailsValue = event.target.value;
      emailsValue = emailsValue.replace(/\n|\r|\s/g, ';');
      dispatch({ type: UPDATE_BILLING_FIELD, payload: { [event.target.name]: emailsValue } });
    }
  };

  const handleSuburbChange = ({ value: suburbLabel, suburb }) => {
    dispatch({ type: UPDATE_BILLING_SUBURB, suburb_label: suburbLabel, suburb_id: suburb ? suburb.id : null });
  };

  return (
    <div className="checkout-panel billing-details">
      <div className="add-new-address-box order-details__container">
        <div className="form-heading billing-details_icon">
          <h3 className="uppercase">Billing Details</h3>
        </div>
        <div className="checkout__fields-container">
          <div className="row">
            <div className="small-12 columns">
              <label className="uppercase">Suburb</label>
              {
                <GoogleLocationInput
                  dataType="suburbLabel"
                  field="suburb_id"
                  countryCode={countryCode}
                  location={(billingDetails.suburb && billingDetails.suburb.label) || ''}
                  suburb={billingDetails.suburb}
                  handleAddressChange={handleSuburbChange}
                  readOnly={false}
                  isRequired
                  isValid={errors.suburb_id ? !errors.suburb_id : true}
                />
              }
              {!!errors.suburb_id && (
                <span className="form-error is-visible">Please enter and select a valid postcode/suburb</span>
              )}
            </div>
          </div>
          <div className="row">
            <div className="small-12 medium-6 columns">
              <label className="uppercase">Billing Emails</label>
              <textarea
                ref={emailsRef}
                name="email"
                type="email"
                className={`form-input ${errors.email ? 'is-invalid-input' : ''}`}
                onChange={handleChange}
                onKeyUp={handleEmailEnter}
                value={billingDetails.email || ''}
              />
              <span className="checkout-hint">Multiple email recipients can be separated by a semicolon ;</span>
              {!!errors.email && <span className="form-error is-visible">Please enter a billing email address</span>}
            </div>
            <div className="small-12 medium-6 columns">
              <label className="uppercase">Phone</label>
              <input
                type="text"
                name="phone"
                className={`form-input ${errors.phone ? 'is-invalid-input' : ''}`}
                onChange={handleChange}
                value={billingDetails.phone || ''}
              />
              {!!errors.phone && <span className="form-error is-visible">Please enter billing phone number</span>}
            </div>
          </div>
          <div className="row">
            <div className="small-12 columns">
              <label className="uppercase">Address</label>
              <textarea
                ref={addressRef}
                name="address"
                rows="3"
                className={`form-input ${errors.address ? 'is-invalid-input' : ''}`}
                onChange={handleChange}
                value={billingDetails.address || ''}
              />
              {!!errors.address && <span className="form-error is-visible">Please enter billing address</span>}
            </div>
          </div>
          <div className="row">
            <div className="medium-12 columns">
              <label className="uppercase">Company Billing Name</label>
              <input
                type="text"
                name="name"
                className={`form-input ${errors.name ? 'is-invalid-input' : ''}`}
                onChange={handleChange}
                value={billingDetails.name || ''}
              />
              {!!errors.name && (
                <span className="form-error is-visible">Please enter your a billing name for your company</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingDetails;
