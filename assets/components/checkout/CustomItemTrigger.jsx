import { useState } from 'react';
import PropTypes from 'prop-types';

// components
import CustomItemModal from './modals/CustomItemModal';

const CustomItemTrigger = ({
  supplierId,
  onItemAdded,
  displayAs = 'button', // "button" or "card"
  buttonText = 'Add Custom Item',
  buttonClass = 'button small',
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleItemAdded = (orderLine) => {
    // Close modal and notify parent component
    setIsModalOpen(false);
    if (onItemAdded) {
      onItemAdded(orderLine);
    }
  };

  const renderButton = () => (
    <button className={buttonClass} onClick={handleOpenModal} type="button">
      {buttonText}
    </button>
  );

  const renderCard = () => (
    <div className="menu-item custom-item-card" onClick={handleOpenModal}>
      <div className="item-heading">
        <h4 className="item-title">Add Custom Item</h4>
      </div>

      <div className="item-details">
        <div className="description-detail">
          <p className="item-description">Create a custom item with your own pricing and details</p>
        </div>

        <div className="custom-item-card__icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="16" />
            <line x1="8" y1="12" x2="16" y2="12" />
          </svg>
        </div>
      </div>

      <div className="custom-item-card__footer">
        <span className="custom-item-card__cta">Click to create</span>
      </div>
    </div>
  );

  return (
    <>
      {displayAs === 'card' ? renderCard() : renderButton()}

      <CustomItemModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        supplierId={supplierId}
        onItemAdded={handleItemAdded}
      />
    </>
  );
};

CustomItemTrigger.propTypes = {
  supplierId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onItemAdded: PropTypes.func,
  displayAs: PropTypes.oneOf(['button', 'card']),
  buttonText: PropTypes.string,
  buttonClass: PropTypes.string,
};

CustomItemTrigger.defaultProps = {
  onItemAdded: null,
  displayAs: 'button',
  buttonText: 'Add Custom Item',
  buttonClass: 'button small',
};

export default CustomItemTrigger;
