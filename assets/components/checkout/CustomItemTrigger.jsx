import { useState } from 'react';
import PropTypes from 'prop-types';

// components
import CustomItemModal from './modals/CustomItemModal';

const CustomItemTrigger = ({ 
  supplierId, 
  onItemAdded, 
  buttonText = "Add Custom Item",
  buttonClass = "button small"
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleItemAdded = (orderLine) => {
    // Close modal and notify parent component
    setIsModalOpen(false);
    onItemAdded && onItemAdded(orderLine);
  };

  return (
    <>
      <button 
        className={buttonClass}
        onClick={handleOpenModal}
        type="button"
      >
        {buttonText}
      </button>

      <CustomItemModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        supplierId={supplierId}
        onItemAdded={handleItemAdded}
      />
    </>
  );
};

CustomItemTrigger.propTypes = {
  supplierId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onItemAdded: PropTypes.func,
  buttonText: PropTypes.string,
  buttonClass: PropTypes.string,
};

export default CustomItemTrigger;
