import { useEffect } from 'react';
import PropTypes from 'prop-types';

const HeaderNav = ({ panels, currentPanel }) => {
  useEffect(() => {
    const header = document.getElementsByClassName('action-bar')[0];
    if (header) {
      const headerTop = header.getBoundingClientRect().top + window.scrollY - 10;
      window.scrollTo({ top: headerTop, behavior: 'smooth' });
    }
  }, [currentPanel]);

  return (
    <ul className="action-bar menu expanded text-center">
      {panels.map((panel, idx) => (
        <li key={panel.key} className={`checkout-progress-heading ${currentPanel === panel.key ? 'active' : ''}`}>
          {idx + 1}. {panel.label}
        </li>
      ))}
    </ul>
  );
};

HeaderNav.propTypes = {
  panels: PropTypes.array.isRequired,
  currentPanel: PropTypes.string.isRequired,
};

export default HeaderNav;
