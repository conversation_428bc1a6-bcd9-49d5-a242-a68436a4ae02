import { useState, useEffect } from 'react';

const PantryManagerDetails = ({ customer, order, handleChange, errors }) => {
  const [pantryManagers, setPantryMangers] = useState(customer.pantry_managers || [])
  const [selectedPantryManager, setSelectedPantryManager] = useState('');

  useEffect(() => {
    if (!customer.pantry_managers || !customer.pantry_managers.length) return;
    
    setPantryMangers(customer.pantry_managers);
  }, [customer.pantry_managers]);

  const pantryManagerOptions = pantryManagers.map((manager) => ({
    label: manager.name,
    value: manager.id,
  }));

  if (!pantryManagers.length) return null;

  return (
    <div className="small-12 medium-6 columns">
      <label className="uppercase">Pantry Manager</label>
      <select
        name="pantry_manager_id"
        className="form-input"
        value={order.pantry_manager_id || ''}
        onChange={handleChange}
      >
        <option value="">Please Select</option>
        {pantryManagers.map((manager) => (
          <option key={`pantry-manager-${manager.id}`} value={manager.id}>
            {manager.name}
          </option>
        ))}
      </select>
      {!!errors.pantry_manager_id && <span className="form-error is-visible">Please select a Pantry Manager.</span>}
    </div>
  )
}

export default PantryManagerDetails;