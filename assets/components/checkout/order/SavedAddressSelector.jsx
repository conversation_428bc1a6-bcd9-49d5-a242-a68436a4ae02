import { useState, useContext } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { Modal } from 'react-responsive-modal';

// actions
import {
  UPDATE_DELIVERY_ADDRESS_VIA_SAVED_ADDRESS,
  UPDATE_DELIVERY_ADDRESS,
  UPDATE_ORDER_FIELD,
} from 'actions/checkoutActionTypes';
import useSupplierChecks from 'hooks/useSupplierChecks';
import adminContext from 'contexts/adminContext';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

const SavedAddressSelector = () => {
  const dispatch = useDispatch();
  const { checkSupplierSuburbRestriction } = useSupplierChecks({});
  const { saved_addresses: savedAddresses, recent_addresses: recentAddresses } = useSelector((state) => state.customer);
  const { isAdmin } = useContext(adminContext);
  const [startSelection, setStartSelection] = useState(false);

  const order = useSelector((state) => state.order);

  // selected saved changes
  const handleAddressSelection = (address) => {
    setStartSelection(false);
    if (address) {
      checkSuburbRestriction(address);
    }
  };

  const checkSuburbRestriction = async (address) => {
    let suburbRestriction = null;
    if (order.delivery_suburb_id) {
      suburbRestriction = {
        id: order.delivery_suburb_id,
        label: order.delivery_suburb_label,
        order_id: order.id,
      };
    }
    const selectedSuburb = {
      id: address.suburb_id,
      label: address.suburb_label,
    };
    const restrictionCheck = await checkSupplierSuburbRestriction({
      yordarSuburb: selectedSuburb,
      suburbRestriction,
    });

    if (restrictionCheck || isAdmin) {
      dispatch({ type: UPDATE_DELIVERY_ADDRESS_VIA_SAVED_ADDRESS, payload: address });
    } else {
      dispatch({ type: UPDATE_ORDER_FIELD, payload: { delivery_address_level: null } });
      dispatch({ type: UPDATE_DELIVERY_ADDRESS, payload: null });
    }
  };

  if (!savedAddresses.length && !recentAddresses.length) return null;

  return (
    <>
      <label className="uppercase">Saved Address</label>
      <select name="selected_address" className="form-input" onClick={() => setStartSelection(true)} />

      <Modal
        classNames={{ modal: 'reveal customer-form' }}
        open={startSelection}
        onClose={() => setStartSelection(false)}
        center
      >
        <div className="form-content has-small-gutter">
          {!!savedAddresses.length && (
            <>
              <h3 className="modal-title text-center">Saved {savedAddresses.length > 1 ? 'Addresses' : 'Address'}</h3>
              {savedAddresses.map((address, index) => (
                <AddressOption
                  key={`saved-address-${address.id}`}
                  address={address}
                  handleAddressSelection={handleAddressSelection}
                  index={index}
                />
              ))}
            </>
          )}
          {!!recentAddresses.length && (
            <>
              <h3 className="modal-title text-center">Recent {recentAddresses.length > 1 ? 'Addresses' : 'Address'}</h3>
              {recentAddresses.map((address, index) => (
                <AddressOption
                  key={`saved-address-${address.id}`}
                  address={address}
                  handleAddressSelection={handleAddressSelection}
                  index={index}
                />
              ))}
            </>
          )}
        </div>
      </Modal>
    </>
  );
};

export default SavedAddressSelector;

const AddressOption = ({ address, handleAddressSelection, index }) => (
  <div className="saved-address" onClick={() => handleAddressSelection(address)}>
    <div className="saved-address-header" style={{ background: getCircleIconColor(index) }}>
      <strong>
        {address.label}, {address.suburb_label}
      </strong>
      <a className="ml-1-2 button tiny hollow" onClick={() => handleAddressSelection(address)}>
        Select
      </a>
    </div>
    <div className="saved-address-instructions">
      <strong>Instructions: </strong>
      <span>{address.instructions}</span>
    </div>
  </div>
);

AddressOption.propTypes = {
  address: PropTypes.object,
  handleAddressSelection: PropTypes.func.isRequired,
  index: PropTypes.number.isRequired,
};
