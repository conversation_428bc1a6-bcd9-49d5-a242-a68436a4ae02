import { useEffect, useContext, useState } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import Cookies from 'js-cookie';
import DatePicker from 'react-datepicker';
import moment from 'moment';

// actions
import { UPDATE_DELIVERY_DATE } from 'actions/checkoutActionTypes';
import appContext from 'contexts/appContext';
import { checkSwipeAccess } from 'actions/checkoutActions';
import { clearDateCookie } from 'utilities/cookieHelpers';

const DeliveryDate = ({ order, errors }) => {
  const dispatch = useDispatch();
  const { isWoolworths, isEditPage, domain } = useContext(appContext);

  const [dateOnly, setDateOnly] = useState(!order.delivery_at);

  useEffect(() => {
    setDateOnly(!order.delivery_at);
  }, [order.delivery_at]);

  const handleChange = (datetime) => {
    clearDateCookie(domain);
    const formattedDate = moment(datetime).format('YYYY-MM-DD h:mm a');
    dispatch({ type: UPDATE_DELIVERY_DATE, payload: formattedDate });
    checkSwipeAccess({
      orderID: order.id,
      deliveryAt: formattedDate,
      dispatch,
    });
  };

  function parseDateString(dateString) {
    return moment(dateString, 'DD/MM/YYYY').toDate();
  }

  function handleIsSelected() {
    if (order.delivery_at) {
      return new Date(order.delivery_at);
    }
    if (Cookies.get('delivery_date_filter')) {
      const dateString = Cookies.get('delivery_date_filter');
      const date = parseDateString(dateString);
      return date;
    }
    return '';
  }

  function CustomInput({ value, onClick, dateOnly }) {
    const displayValue = dateOnly && value ? `${moment(value).format('YYYY-MM-DD')} Select A Time` : value;
    return (
      <input
        className={`form-input ${errors.delivery_at ? 'is-invalid-input' : ''}`}
        onClick={onClick}
        value={displayValue}
        readOnly={isWoolworths && !isEditPage}
        style={{ width: '100%' }}
        name="delivery_at"
      />
    );
  }

  return (
    <>
      <DatePicker
        selected={handleIsSelected()}
        onChange={handleChange}
        showTimeSelect
        timeIntervals={15}
        minDate={new Date()}
        name="delivery_at"
        dateFormat="yyyy-MM-dd h:mm aa"
        autoComplete="off"
        readOnly={isWoolworths && !isEditPage}
        customInput={<CustomInput dateOnly={dateOnly} />}
      />
      {isWoolworths && !isEditPage && (
        <span className="checkout-hint">Delivery time set according to selected delivery window</span>
      )}
      {!!errors.delivery_at && (
        <span className="form-error is-visible">Please enter a valid date and time for your order.</span>
      )}
    </>
  );
};

DeliveryDate.propTypes = {
  order: PropTypes.object.isRequired,
  errors: PropTypes.object.isRequired,
};

export default DeliveryDate;
