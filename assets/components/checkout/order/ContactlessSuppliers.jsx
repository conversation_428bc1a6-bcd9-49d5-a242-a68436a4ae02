import { useState } from 'react';
import PropTypes from 'prop-types';

// actions
import { useDispatch } from 'react-redux';

// components
import { Modal } from 'react-responsive-modal';
import { UPDATE_DELIVERY_TYPE } from 'actions/checkoutActionTypes';

const ContactlessSuppliers = ({ order }) => {
  const { order_suppliers: orderSuppliers } = order;
  const dispatch = useDispatch();
  const [seeWhy, setSeeWhy] = useState(false);
  const contactSuppliers = orderSuppliers.filter((supplier) => !supplier.provides_contactless_delivery);
  const hasContactSuppliers = !!contactSuppliers.length && contactSuppliers.length != orderSuppliers.length;

  // return if all suppliers are contact suppliers
  if (!!contactSuppliers.length && contactSuppliers.length === orderSuppliers.length) return null;

  const labelStyle = hasContactSuppliers ? { textDecoration: 'line-through' } : {};

  const handleChange = (event) => {
    const deliveryType = event.target.checked ? event.target.value : 'normal';
    dispatch({ type: UPDATE_DELIVERY_TYPE, payload: deliveryType });
  };

  return (
    <>
      <div className="small-12 columns">
        <div className="callout">
          <input
            id="order-delivery-type"
            className="mr-1-4"
            checked={order.delivery_type === 'contactless'}
            disabled={hasContactSuppliers}
            type="checkbox"
            value="contactless"
            name="delivery_type"
            onChange={handleChange}
          />
          <label htmlFor="order-delivery-type" style={labelStyle} className="uppercase">
            Contactless Delivery
          </label>
          <a className="ml-1-2" onClick={() => setSeeWhy(true)}>
            see why!
          </a>
        </div>
      </div>

      {seeWhy && (
        <Modal classNames={{ modal: 'reveal customer-form' }} open onClose={() => setSeeWhy(false)} center>
          <div className="form-content has-small-gutter">
            <h3 className="modal-title text-center">Contactless Delivery</h3>
            {hasContactSuppliers ? (
              <ContactSupplierInformation suppliers={contactSuppliers} />
            ) : (
              <ContactLessSupplierInformation />
            )}
          </div>
          <div className="form-footer light-gray-bg medium-text-right">
            <a
              className={`button small uppercase ${hasContactSuppliers ? 'gray-btn' : ''}`}
              onClick={() => setSeeWhy(false)}
            >
              {hasContactSuppliers ? 'Close' : 'Ok'}
            </a>
          </div>
        </Modal>
      )}
    </>
  );
};

ContactlessSuppliers.propTypes = {
  order: PropTypes.object.isRequired,
};

export default ContactlessSuppliers;

const ContactSupplierInformation = ({ suppliers }) => (
  <>
    <p>Due to the following supplier(s) not providing contactless delivery:</p>
    <ul className="bullet-list">
      {suppliers.map((supplier) => (
        <li key={`contact-supplier-${supplier.id}`}>{supplier.name}</li>
      ))}
    </ul>
    <p>...this order cannot be placed as contactless, please remove these supplier(s) to place a contactless order.</p>
  </>
);

ContactSupplierInformation.propTypes = {
  suppliers: PropTypes.array.isRequired,
};

const ContactLessSupplierInformation = () => (
  <>
    <p>In line with public health recommendations, contactless deliveries will provide the following;</p>
    <ul className="bullet-list">
      <li> Food items are prepared, packaged and delivered following hygiene, health and safety standards.</li>
      <li>
        Drivers will wear disposable gloves and masks during delivery, from packing of their van to unpacking at your
        site, and throughout the drop.
      </li>
      <li>
        Drivers will place the order in the designated space, step away from the delivery by at least 1.5m if collection
        is required, and allow the client to receive the order while adhering to recommended social distancing
        guidelines.
      </li>
      <li>
        Drivers will not ask for a signature for delivery, if needed, a photo of the order will be taken when dropped in
        the specified delivery location.
      </li>
    </ul>
  </>
);
