import { useContext } from 'react';

// state
import { useSelector } from 'react-redux';
import { customerProfilePath } from 'routes';

// context
import appContext from 'contexts/appContext';

const OrderSuccess = () => {
  const order = useSelector((state) => state.order);
  const customer = useSelector((state) => state.customer);
  const { supplierIndexUrl, googleConfig, isWoolworths } = useContext(appContext);
  const { woolworths_order_id: woolworthsOrderId, woolworths_account: woolworthsAccount } = order;
  return (
    <>
      <div className="success-container">
        <svg className="success-checkmark" version="1.1" viewBox="0 0 130.2 130.2" xmlns="http://www.w3.org/2000/svg">
          <circle
            className="path circle"
            cx="65.1"
            cy="65.1"
            fill="none"
            r="62.1"
            strokeMiterlimit="10"
            strokeWidth="6"
            stroke="#5ec9bd"
          />
          <polyline
            className="path check"
            fill="none"
            points="100.2,40.2 51.5,88.8 29.8,67.5 "
            strokeLinecap="round"
            strokeMiterlimit="10"
            strokeWidth="6"
            stroke="#5ec9bd"
          />
        </svg>
        <div className="success-info">
          {order.status === 'new' && <p className="success-message">Thank You {order.customer_name}!</p>}
          {order.status === 'quoted' && (
            <>
              <p className="success-message">Successfully created an Order Quote!</p>
              <p>and sent {order.quote_emails ? 'emails' : 'an email'} to:</p>
              <ul>
                {order.quote_emails && (
                  <>
                    {order.quote_emails.split(';').map((email) => (
                      <li key={`quote-email-${email}`}>{email}</li>
                    ))}
                  </>
                )}
                {<li>customer's email ({customer.email})</li>}
              </ul>
            </>
          )}
          <p>
            Order reference:
            <a href={order.order_path}>
              #{order.id} {order.name}
            </a>
          </p>
          {isWoolworths && !!woolworthsOrderId && (
            <p>
              Woolworths ID: #{woolworthsOrderId} - {woolworthsAccount}
            </p>
          )}
        </div>
      </div>
      <div className="order-details__container">
        <div className="form-heading completed-order_icon">
          <h3 className="uppercase">{order.name}</h3>
        </div>
        <div className="checkout__fields-container row">
          <div className="small-12 medium-6 columns">
            <h3>Your Details</h3>
            <p className="success-confirmation company-icon">{order.company_name}</p>
            <p className="success-confirmation user-grey-icon">{order.contact_name || order.customer_name}</p>
            <p className="success-confirmation phone-icon">{order.phone}</p>
          </div>
          <div className="medium-12 large-6 columns">
            <h3>Delivery Information</h3>
            <p className="success-confirmation frequency-icon">{order.is_recurrent ? 'Recurring' : 'One-Off'} Order</p>
            <p className="success-confirmation date-icon">{order.delivery_at}</p>
            {!!order.delivery_address_arr.length && (
              <p className="success-confirmation marker-grey-icon">{order.delivery_address_arr.join(', ')}</p>
            )}
          </div>
          <div className="small-12 columns">
            <h3>Delivery Instructions</h3>
            <p className="delivery-instructions">{order.delivery_instruction}</p>
          </div>
          {!!order.geo_coordinates && (
            <div className="small-12 columns order-map">
              <div className="instructions-container">
                <p className="instructions-reminder">
                  Please ensure you've provided detailed delivery instructions if the address below is not the address
                  to deliver to. You can change the instructions by editing the order from your dashboard
                </p>
              </div>
              <img
                alt="Map"
                src={`${googleConfig.map_url}?zoom=18&scale=2&size=700x300&markers=${order.geo_coordinates[0]},${order.geo_coordinates[1]}&key=${googleConfig.map_key}`}
              />
            </div>
          )}
        </div>
      </div>
      <div className="between-flex checkout-btns">
        <a className="checkout-btns__back" href={customerProfilePath()}>
          Back to My Account
        </a>
        <a className="button checkout-btns__forward" href={supplierIndexUrl}>
          PLACE ANOTHER ORDER
        </a>
      </div>
    </>
  );
};

export default OrderSuccess;
