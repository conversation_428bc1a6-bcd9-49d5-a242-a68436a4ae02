import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import Creatable from 'react-select/creatable';

import { UPDATE_PURCHASE_ORDER, SET_REQUIRES_GST_FREE_PO } from 'actions/checkoutActionTypes';
import { COST_CENTER_ID_FORMATS } from 'utilities/checkoutHelpers';

const PurchaseOrderDetails = ({
    order,
    customer: { 
      requires_purchase_order: reqiuresPO,
      required_department_identity_format: requiresCostCentreID,
      has_gst_split_invoicing: hasGstSplitInvoicing,
      has_gst_split_items: hasGstSplitItems,
      purchase_orders,
    },
    handleOrderChange,
    errors,
  }) => {
  const dispatch = useDispatch();
  const [allowSplitPO, setAllowSplitPO] = useState(!!order?.gst_free_cpo_id ? true : null);

  useEffect(() => {
    if (!!order?.gst_free_cpo_id){
      setAllowSplitPO(true);
      if (hasGstSplitInvoicing && reqiuresPO) {
        dispatch({ type: SET_REQUIRES_GST_FREE_PO, payload: true });
      }
    }
  }, [order?.gst_free_cpo_id, reqiuresPO])

  const handlePoChange = (purchaseOrder, gstFree=false) => {
    const cpoId = purchaseOrder ? purchaseOrder.value : null;
    dispatch({ type: UPDATE_PURCHASE_ORDER, payload: cpoId, gstFree });
  };

  const poOptions = purchase_orders.map((purchase_order) => ({
    value: purchase_order.id,
    label: purchase_order.po_number,
    description: purchase_order.description,
    isInactive: purchase_order.inactive,
  }));

  const formatOptionLabel = ({ value, label, isInactive, description }, { context }) => (
    <>
      <div style={{ display: 'flex' }}>
        <div>{label}</div>
        {isInactive && <div style={{ marginLeft: '10px', color: '#ccc' }}>(inactive)</div>}
      </div>
      {context == 'menu' && description && (
        <small style={{ fontSize: '0.8rem' }}>({description})</small>
      )}
    </>
  );

  const costCenterIDFormat = COST_CENTER_ID_FORMATS[requiresCostCentreID] || { type: 'text', label: '', limit: ''};

  const handleDepartmentIDChange = (event) => {
    const value = event.target.value;
    if (!!costCenterIDFormat.limit && value.split('').length > costCenterIDFormat.limit) return;

    handleOrderChange(event);
  }

  const needsSplitPO = hasGstSplitInvoicing && hasGstSplitItems;
  const handleAllowSplitPOs = (canAllow) => {
    setAllowSplitPO(canAllow);
    if (hasGstSplitItems && reqiuresPO && canAllow) {
      dispatch({ type: SET_REQUIRES_GST_FREE_PO, payload: true });
    } else {
      dispatch({ type: SET_REQUIRES_GST_FREE_PO, payload: false });
    }
  }

  return (
    <div className="row">
      {needsSplitPO && allowSplitPO === null && (
        <div className="small-12 columns">
          <div className="callout category-notice clearfix">
            <div className="small-12 medium-6 columns">
              <p>
                This order contains a combination of GST-free and GST items.
                <br />
                Would you like to invoice these items separately? <small>(requires split purchase orders)</small>
              </p>
            </div>
            <div className="small-12 medium-6 columns">
              <a className="button small" onClick={() => handleAllowSplitPOs(true)}>YES</a>
              <a className="button hollow small" style={{ marginLeft: '0.5rem' }} onClick={() => handleAllowSplitPOs(false)}>NO</a>
            </div>
          </div>
        </div>
      )}
      {needsSplitPO && allowSplitPO && (
        <>
          <div className="small-12 medium-6 columns">
            <label className="uppercase">Purchase Order (for GST items)</label>
            <Creatable
              placeholder="Select or Type a new PO number"
              isClearable
              className={`form-input ${errors.cpo_id ? 'is-invalid-input' : ''}`}
              options={poOptions}
              onChange={(PO) => handlePoChange(PO)}
              noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
              formatOptionLabel={formatOptionLabel}
              value={order.cpo_id ? poOptions.find((option) => option.value == order.cpo_id) : null}
            />
            {!!reqiuresPO && !!errors.cpo_id && (
              <span className="form-error is-visible">Please select/type a PO number for GST items.</span>
            )}
            {!!reqiuresPO && (
              <small>
                <strong style={{ color: '#de1f52' }}>Please note: </strong>
                Your company requires a purchase order number for GST items of the order.
              </small>
            )}
          </div>
          <div className="small-12 medium-6 columns">
            <label className="uppercase">Purchase Order (for GST-free items)</label>
            <Creatable
              placeholder="Select or Type a new PO number"
              isClearable
              className={`form-input ${errors.gst_free_cpo_id ? 'is-invalid-input' : ''}`}
              options={poOptions}
              onChange={(PO) => handlePoChange(PO, true)}
              noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
              formatOptionLabel={formatOptionLabel}
              value={order.gst_free_cpo_id ? poOptions.find((option) => option.value == order.gst_free_cpo_id) : null}
            />
            {!!reqiuresPO && !!errors.gst_free_cpo_id && (
              <span className="form-error is-visible">Please select/type a PO number for GST-free items.</span>
            )}
            {!!reqiuresPO && (
              <small>
                <strong style={{ color: '#de1f52' }}>Please note: </strong>
                Your company requires a purchase order number for for GST-free items of order.
              </small>
            )}
          </div>
        </>
      )}
      {!(needsSplitPO && allowSplitPO) && (
        <div className="small-12 medium-6 columns">
          <label className="uppercase">Purchase Order</label>
          <Creatable
            placeholder="Select or Type a new PO number"
            isClearable
            className={`form-input ${errors.cpo_id ? 'is-invalid-input' : ''}`}
            options={poOptions}
            onChange={(PO) => handlePoChange(PO)}
            noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
            formatOptionLabel={formatOptionLabel}
            value={order.cpo_id ? poOptions.find((option) => option.value == order.cpo_id) : null}
          />
          {!!reqiuresPO && !!errors.cpo_id && (
            <span className="form-error is-visible">Please select/type a PO number.</span>
          )}
          {!!reqiuresPO && (
            <small>
              <strong style={{ color: '#de1f52' }}>Please note: </strong>
              Your company requires a purchase order number for an order. If you don't have a PO number please enter your
              full name.
            </small>
          )}
        </div>
      )}      
      <div className="small-12 medium-6 columns">
        <label className="uppercase">Cost Centre ID</label>
        <input
          className="form-input validate"
          type={costCenterIDFormat.type}
          name="department_identity"
          className={`form-input ${errors.department_identity ? 'is-invalid-input' : ''}`}
          onChange={handleDepartmentIDChange}
          value={order.department_identity || ''}
        />
        {!!requiresCostCentreID && !!errors.department_identity && (
          <span className="form-error is-visible">Please enter a {costCenterIDFormat.label} Cost Centre ID.</span>
        )}
        {!!requiresCostCentreID && (
          <small>
            <strong style={{ color: '#de1f52' }}>Please note: </strong>
            Your company requires a {costCenterIDFormat.label} Cost Centre ID for an order.
          </small>
        )}
      </div>
    </div>
  );
};

PurchaseOrderDetails.propTypes = {
  order: PropTypes.object.isRequired,
  handleOrderChange: PropTypes.func.isRequired,
  errors: PropTypes.object.isRequired,
};

export default PurchaseOrderDetails;
