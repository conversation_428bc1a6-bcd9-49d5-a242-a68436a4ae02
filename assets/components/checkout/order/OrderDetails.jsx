import { useContext } from 'react';

// actions
import { useDispatch, useSelector } from 'react-redux';
import { UPDATE_ORDER_FIELD, UPDATE_WOOLWORTHS_ORDER_ID } from 'actions/checkoutActionTypes';

// components
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';
import PurchaseOrderDetails from './PurchaseOrderDetails';
import DeliveryDate from './DeliveryDate';
import DeliveryDetails from './DeliveryDetails';
import PantryManagerDetails from './PantryManagerDetails'

const OrderDetails = () => {
  const dispatch = useDispatch();
  const order = useSelector((state) => state.order);
  const customer = useSelector((state) => state.customer);
  const { swipeCardSuppliers, errors } = useSelector((state) => state.form);
  const { isAdmin } = useContext(adminContext);
  const { isWoolworths } = useContext(appContext);

  const handleChange = (event) => {
    let { value } = event.target;
    if (event.target.type == 'radio') {
      value = event.target.value == 'true';
    }
    dispatch({ type: UPDATE_ORDER_FIELD, payload: { [event.target.name]: value } });
  };

  const handleWoolworthsIdChange = (event) => {
    dispatch({ type: UPDATE_WOOLWORTHS_ORDER_ID, payload: event.target.value });
  };

  return (
    <div className="checkout-panel order-details">
      <div className="order-details__container about-the-order">
        <div className="form-heading about-the-order_icon">
          <h3 className="uppercase">About the order</h3>
        </div>
        <div className="checkout__fields-container">
          <div className="row">
            <div className="small-12 columns">
              <label className="uppercase">Name for Order *</label>
              <input
                className={`form-input ${errors.name ? 'is-invalid-input' : ''}`}
                required="required"
                type="text"
                name="name"
                onChange={handleChange}
                value={order.name || ''}
              />
              {!!errors.name && <span className="form-error is-visible">Please enter a name for your order.</span>}
            </div>

            {!!order.is_catering_order && (
              <div className="small-12 columns">
                <div className="callout category-notice">
                  <p>
                    <b>Please note:</b> Hot food may arrive up to 15 minutes prior to the delivery time, cold food may
                    arrive up to 60 minutes prior to the delivery time.
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="row">
            <div className="small-12 medium-6 columns">
              <label className="uppercase" htmlFor="order_delivery_at">
                Delivery Date and Time *
              </label>
              <DeliveryDate order={order} errors={errors} />
            </div>

            {isWoolworths && !!order.associated_woolworths_order_attributes && (
              <div className="small-12 medium-6 columns">
                <label className="uppercase">Woolworths Order ID</label>
                <input
                  className="form-input"
                  required="required"
                  type="text"
                  name="woolworths_order_id"
                  onChange={handleWoolworthsIdChange}
                  value={order.associated_woolworths_order_attributes.woolworths_order_id || ''}
                />
                <span className="checkout-hint">
                  Attahced to account: {order.associated_woolworths_order_attributes.account}
                </span>
              </div>
            )}

            {!!order.is_catering_order && (
              <div className="small-12 medium-6 columns">
                <label className="uppercase">Number of People *</label>
                <input
                  className={`form-input ${errors.number_of_people ? 'is-invalid-input' : ''}`}
                  required="required"
                  pattern="number"
                  type="number"
                  name="number_of_people"
                  onChange={handleChange}
                  value={order.number_of_people || ''}
                />
                {!!errors.number_of_people && (
                  <span className="form-error is-visible">Please enter number of people.</span>
                )}
              </div>
            )}

            {isAdmin && !!order.is_recurrent && (
              <div className="small-12 medium-6 columns">
                <label className="uppercase">
                  Handle related orders on public holidays{' '}
                  <small style={{ textTransform: 'initial' }}>(admin only)</small>
                </label>
                <div className="pt-1-2">
                  <span>
                    <input
                      type="radio"
                      name="skip"
                      value="true"
                      checked={order.skip}
                      onChange={handleChange}
                      id={`order-${order.id}-skip-true`}
                    />
                    <label style={{ textTransform: 'initial' }} htmlFor={`order-${order.id}-skip-true`}>
                      Skip the Delivery
                    </label>
                  </span>
                  <span className="ml-2">
                    <input
                      type="radio"
                      name="skip"
                      value="false"
                      checked={!order.skip}
                      onChange={handleChange}
                      id={`order-${order.id}-skip-false`}
                    />
                    <label style={{ textTransform: 'initial' }} htmlFor={`order-${order.id}-skip-false`}>
                      Deliver on next business day
                    </label>
                  </span>
                </div>
              </div>
            )}
          </div>

          <div className="row">
            {!!swipeCardSuppliers.length && (
              <div className="small-12 columns supplier-swipe-card-note">
                <div className="callout category-notice">
                  <p>
                    <span className="swipe-card-supplier">{swipeCardSuppliers.join(', ')}</span> requires access outside
                    of business hours to fulfil this delivery.
                    <br />
                    Please contact <a href="mailto:<EMAIL>"> <EMAIL></a> or ring 02 8123 4044
                    to arrange swipe card access
                  </p>
                </div>
              </div>
            )}
            {!customer.hide_po_number && (
              <PurchaseOrderDetails
                order={order}
                handleOrderChange={handleChange}
                customer={customer}
                errors={errors}
              />
            )}
          </div>
        </div>
      </div>

      <div className="order-details__container delivery-details">
        <div className="form-heading delivery-details_icon">
          <h3 className="uppercase">Delivery Details</h3>
        </div>

        <div className="checkout__fields-container">
          <DeliveryDetails order={order} errors={errors} handleOrderChange={handleChange} />

          <div className="row">
            <div className="small-12 medium-6 columns">
              <label className="uppercase">Contact Name*</label>
              <input
                className={`form-input ${errors.contact_name ? 'is-invalid-input' : ''}`}
                required="required"
                type="text"
                name="contact_name"
                onChange={handleChange}
                value={order.contact_name || ''}
              />
              {!!errors.contact_name && <span className="form-error is-visible">Please enter contact name.</span>}
            </div>
            <div className="small-12 medium-6 columns">
              <label className="uppercase">Company Name</label>
              <input
                className="form-input"
                type="text"
                name="company_name"
                onChange={handleChange}
                value={order.company_name || ''}
              />
            </div>
            <div className="small-12 medium-6 columns">
              <label className="uppercase">Contact Phone*</label>
              <input
                className={`form-input ${errors.phone ? 'is-invalid-input' : ''}`}
                required="required"
                type="text"
                name="phone"
                onChange={handleChange}
                value={order.phone || ''}
              />
              {!!errors.phone && <span className="form-error is-visible">Please enter a phone number.</span>}
            </div>
            {isAdmin && <PantryManagerDetails customer={customer} order={order} errors={errors} handleChange={handleChange} />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
