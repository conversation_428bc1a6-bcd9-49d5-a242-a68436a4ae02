import { useRef, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';

// actions
import { useSelector, useDispatch } from 'react-redux';
import { UPDATE_DELIVERY_ADDRESS, UPDATE_DELIVERY_SUBURB } from 'actions/checkoutActionTypes';
import appContext from 'contexts/appContext';

// components
import GoogleLocationInput from '../GoogleLocationInput';
import SuburbAvailabilityModal from '../modals/SuburbAvailabilityModal';
import SavedAddressSelector from './SavedAddressSelector';
import ContactlessSuppliers from './ContactlessSuppliers';

const DeliveryDetails = ({ order, handleOrderChange, errors }) => {
  const descriptionRef = useRef(null);
  const { isWoolworths, isEditPage, countryCode } = useContext(appContext);
  const { deliverableSuppliers } = useSelector((state) => state.form);
  const dispatch = useDispatch();

  useEffect(() => {
    descriptionRef.current.style.height = 'auto';
    descriptionRef.current.style.height = `${descriptionRef.current.scrollHeight + 20}px`;
  }, [order.delivery_instruction]);

  // from Google Auto complete
  const handleAddressChange = ({ suburb, value: streetAddress }) => {
    dispatch({ type: UPDATE_DELIVERY_ADDRESS, payload: streetAddress });
    if (suburb) dispatch({ type: UPDATE_DELIVERY_SUBURB, payload: suburb });
  };

  let addressRestrictions = null;
  if (order.delivery_suburb_id) {
    addressRestrictions = {
      id: order.delivery_suburb_id,
      label: order.delivery_suburb_label,
      order_id: order.id,
    };
  }

  return (
    <>
      {!!deliverableSuppliers.length && <SuburbAvailabilityModal order={order} suppliers={deliverableSuppliers} />}
      <div className="row">
        <div className="small-12 medium-6 columns">{!isWoolworths && <SavedAddressSelector />}</div>
        <div className="small-12 medium-6 columns">
          <label className="uppercase">Level/Unit</label>
          <input
            placeholder="Delivery address level"
            autoComplete="no"
            className="form-input validate"
            type="text"
            name="delivery_address_level"
            readOnly={isWoolworths && !isEditPage}
            onChange={handleOrderChange}
            value={order.delivery_address_level || ''}
          />
        </div>
      </div>

      <div className="row">
        <div className="small-12 medium-6 columns">
          <label className="uppercase">Street Address *</label>
          {!!addressRestrictions && (
            <GoogleLocationInput
              dataType="streetAddress"
              field="delivery_address"
              countryCode={countryCode}
              location={order.delivery_address || ''}
              handleAddressChange={handleAddressChange}
              readOnly={isWoolworths && !isEditPage}
              isRequired
              suburbRestriction={addressRestrictions}
              isValid={errors.delivery_address ? !errors.delivery_address : true}
            />
          )}
          {isWoolworths && !isEditPage && (
            <span className="checkout-hint">Delivery information set during initial order creation</span>
          )}
          {!!errors.delivery_address && (
            <span className="form-error is-visible">Please enter and select an address.</span>
          )}
        </div>

        <div className="small-12 medium-6 columns">
          <label className="uppercase" htmlFor="order_suburb">
            Suburb *
          </label>
          <input
            autoComplete="no"
            className="form-input string required"
            readOnly="readonly"
            size="50"
            type="text"
            value={order.delivery_suburb_label || ''}
          />
        </div>
      </div>

      <div className="row">
        <div className="small-12 columns">
          <label className="uppercase">Delivery Instructions*</label>
          <textarea
            ref={descriptionRef}
            readOnly={isWoolworths && !isEditPage}
            placeholder="Provide specific instructions to supplier about where to deliver. eg. Deliver to Loading Dock located at 123 Smith St"
            className={`form-input ${errors.delivery_instruction ? 'is-invalid-input' : ''}`}
            rows="3"
            required="required"
            name="delivery_instruction"
            onChange={handleOrderChange}
            value={order.delivery_instruction || ''}
          />
          {!!errors.delivery_instruction && (
            <span className="form-error is-visible">Please enter instructions for supplier.</span>
          )}
        </div>

        {!!order.order_suppliers.length && <ContactlessSuppliers order={order} handleOrderChange={handleOrderChange} />}
      </div>
    </>
  );
};

DeliveryDetails.propTypes = {
  order: PropTypes.object.isRequired,
  handleOrderChange: PropTypes.func.isRequired,
  errors: PropTypes.object.isRequired,
};

export default DeliveryDetails;
