import { useState } from 'react';
import PropTypes from 'prop-types';

// components
import CustomItemCard from '../CustomItemCard';

/**
 * Example component showing how to integrate CustomItemCard 
 * into a supplier menu alongside regular menu items
 */
const SupplierMenuWithCustomItem = ({ supplier, menuItems, onItemAdded }) => {
  const [orderLines, setOrderLines] = useState([]);

  const handleItemAdded = (orderLine) => {
    // Add the new custom item to the order
    setOrderLines(prev => [...prev, orderLine]);
    
    // Notify parent component if needed
    if (onItemAdded) {
      onItemAdded(orderLine);
    }
    
    console.log('Custom item added:', orderLine);
  };

  return (
    <div className="supplier-menu">
      <div className="supplier-menu__header">
        <h2>{supplier.name}</h2>
        <p>{supplier.description}</p>
      </div>

      <div className="supplier-menu__items">
        <div className="menu-items-grid">
          {/* Regular menu items would go here */}
          {menuItems.map((item) => (
            <div key={item.id} className="menu-item">
              <h4>{item.name}</h4>
              <p>{item.description}</p>
              <span>${item.price}</span>
            </div>
          ))}
          
          {/* Custom Item Card */}
          <CustomItemCard
            supplierId={supplier.id}
            onItemAdded={handleItemAdded}
          />
        </div>
      </div>

      {/* Show added custom items */}
      {orderLines.length > 0 && (
        <div className="custom-items-added">
          <h3>Custom Items Added:</h3>
          <ul>
            {orderLines.map((orderLine, index) => (
              <li key={index}>
                {orderLine.name} - Qty: {orderLine.quantity} - ${orderLine.total_price}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

SupplierMenuWithCustomItem.propTypes = {
  supplier: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
  }).isRequired,
  menuItems: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    price: PropTypes.number.isRequired,
  })),
  onItemAdded: PropTypes.func,
};

SupplierMenuWithCustomItem.defaultProps = {
  menuItems: [],
  onItemAdded: null,
};

export default SupplierMenuWithCustomItem;
