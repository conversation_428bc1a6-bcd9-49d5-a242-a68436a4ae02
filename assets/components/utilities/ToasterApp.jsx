import { useEffect } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import { toastTypes, defaultToastOptions, toastTypeOptions } from 'utilities/toastHelpers';

function isHTML(str) {
  const doc = new DOMParser().parseFromString(str, 'text/html');
  return Array.from(doc.body.childNodes).some((node) => node.nodeType === 1);
}

const ToastHtml = ({ message }) => (
  <div className="toastify-injected-html" dangerouslySetInnerHTML={{ __html: message }} />
);

const ToasterApp = ({ notifications }) => {

  const createToast = (type, message) => {
    const sanitizedMessage = isHTML(message) ? <ToastHtml message={message} /> : message;
    const toastType = toastTypes[type] || 'info';
    const toastOptions = toastTypeOptions[toastType] || {};
    toast[toastType](sanitizedMessage, { ...defaultToastOptions, ...toastOptions });  
  }

  useEffect(() => {
    toast.dismiss();

    notifications.forEach(({ type, message }) => {
      if (Array.isArray(message)) {
        message.forEach((toastrMessage) => {
          createToast(type, toastrMessage)
        })
      } else {
        createToast(type, message)
      }
    });
  }, [notifications]);

  return <ToastContainer />;
};

export default ToasterApp;
