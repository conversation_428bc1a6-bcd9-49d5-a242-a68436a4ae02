import shallow from 'zustand/shallow';
import useCustomerInvoiceStore from 'store/customerInvoiceStore';

const InvoiceSearch = () => {
  const { query, setQuery } = useCustomerInvoiceStore(
    (state) => ({
      query: state.query,
      setQuery: state.setQuery,
    }),
    shallow
  );

  const handleChange = (event) => setQuery(event.target.value);

  return (
    <input
      className="search-input form-input"
      placeholder="Search invoices by number, order name or order id"
      style={{ maxWidth: '500px' }}
      type="search"
      value={query}
      onChange={handleChange}
    />
  );
};

export default InvoiceSearch;
