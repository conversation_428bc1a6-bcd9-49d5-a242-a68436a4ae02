import shallow from 'zustand/shallow';
import useCustomerInvoiceStore from 'store/customerInvoiceStore';

const BlankInvoices = () => {
  const { invoices, query } = useCustomerInvoiceStore(
    (state) => ({
      invoices: state.invoices,
      query: state.query,
    }),
    shallow
  );

  let hasInvoices = invoices.length;
  if (hasInvoices && query) hasInvoices = invoices.filter((invoice) => !invoice.filteredOut).length;

  if (hasInvoices) return null;

  return (
    <div className="customer-invoice customer-data text-center">
      <div style={{ width: '100%' }}>No Invoices Found!</div>
    </div>
  );
};

export default BlankInvoices;
