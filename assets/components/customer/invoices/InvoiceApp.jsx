import { useContext } from 'react';
import shallow from 'zustand/shallow';
import useCustomerInvoiceStore from 'store/customerInvoiceStore';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import adminContext from 'contexts/adminContext';

import InvoiceSearch from './InvoiceSearch';
import NewInvoice from './NewInvoice';
import Invoice from './Invoice';
import BlankInvoices from './BlankInvoices';
import InvoiceSkeleton from './InvoiceSkeleton';
import { ToastContainer } from 'react-toastify';

const InvoiceApp = () => {
  const { isAdmin } = useContext(adminContext);
  const { fetchInvoices, invoices, loadingInvoices, loadingMore, hasMore, page } = useCustomerInvoiceStore(
    (state) => ({
      fetchInvoices: state.fetchInvoices,
      invoices: state.invoices,
      loadingInvoices: state.loadingInvoices,
      loadingMore: state.loadingMore,
      hasMore: state.hasMore,
      page: state.page,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingInvoices && !loadingMore) {
        await fetchInvoices({
          page,
        });
      }
    },
  });

  return (
    <>
      <InvoiceSearch />
      <div className="invoice-list">
        <div className="customer-invoices__headings">
          <span className="list-flex-2">Invoice Date</span>
          <span className="list-flex-2">Number</span>
          <span className="list-flex-2">Orders</span>
          <span className="list-flex-3 text-center">Status</span>
          <span className="list-flex-1">Amount</span>
          <span className="list-flex-2">Download</span>
          <span className="list-flex-1 text-center">Actions</span>
        </div>
        {isAdmin && <NewInvoice />}
        {!hasMore && <BlankInvoices />}
        {invoices.map((invoice, idx) => (
          <Invoice key={`customer-invoice-${invoice.id}`} invoice={invoice} index={idx} />
        ))}
        <div ref={sentryRef}>{(loadingInvoices || loadingMore) && <InvoiceSkeleton />}</div>
      </div>
      <ToastContainer />
    </>
  );
};

export default InvoiceApp;
