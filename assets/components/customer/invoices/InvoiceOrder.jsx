import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// components
import SupplierIcon from 'components/customer/SupplierIcon';

const InvoiceOrder = ({ invoice, order, handleOrderAssign }) => {
  const [localOrder, setLocalOrder] = useState({
    ...order,
    attached: (order.invoice_id === invoice.id || order.gst_free_invoice_id === invoice.id),
    is_updated: false,
  });

  const handleClick = (event) => {
    handleOrderAssign({ order, invoice })
  };

  return (
    <div className={`boxed-data po-order ${localOrder.attached ? 'attached' : ''}`} onClick={handleClick}>
      <div className="po-order-banner">
        <h6 className="po-order-heading">{localOrder.delivery_date}</h6>
        <div className="between-flex">
          {localOrder.suppliers.map((supplier) => (
            <SupplierIcon key={supplier.name} supplier={supplier} />
          ))}
        </div>
      </div>
      <p>
        <strong>Order No:</strong> #{localOrder.id}
      </p>
      <p>
        <strong>Name:</strong> {localOrder.name}
      </p>
      <p>
        <strong>Status: </strong>
        <span className={`status-icon ${order.status}`}>
          {order?.status?.charAt(0)?.toUpperCase() + order?.status?.slice(1)}
        </span>
      </p>
      <p>
        <strong>Address:</strong> {localOrder.delivery_address_arr.join(', ')}
      </p>
      {!localOrder.gst_free_po_number && (
        <p>
          <strong>PO #:</strong> {localOrder.po_number || 'not attached to any PO'}
        </p>
      )}
      {!!localOrder.gst_free_po_number && (
        <>
          <p>
            <strong>GST PO #:</strong> {localOrder.po_number || 'not attached to any PO'}
          </p>
          <p>
            <strong>GST-free PO #:</strong> {localOrder.gst_free_po_number}
          </p>
        </>
      )}
      {!localOrder.attached && order.invoice_id && (
        <p>
          <strong>Currently attached to:</strong> <em>{invoice.number}</em>
        </p>
      )}
    </div>
  );
};

InvoiceOrder.propTypes = {
  invoice: PropTypes.object.isRequired,
  order: PropTypes.object.isRequired,
  handleOrderAssign: PropTypes.func.isRequired,
};

export default InvoiceOrder;
