import { useRef, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import axios from 'axios';

import shallow from 'zustand/shallow';
import useCustomerInvoiceStore from 'store/customerInvoiceStore';

import { apiInvoiceOrdersPath, apiOrderPath } from 'routes';
import InvoiceOrdersSkeleton from './InvoiceOrdersSkeleton';
import InvoiceOrder from './InvoiceOrder';
import DocumentGenerator from './DocumentGenerator';

const InvoiceManagement = ({ invoice, setManageInvoice }) => {
  const isMounted = useRef(false);
  const [loadingOrders, setLoadingOrders] = useState(true);
  const [orders, setOrders] = useState([]);
  const [showDocumentGenerator, setShowDocumentGenerator] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const { setInvoiceOrders, updateInvoiceOrders } = useCustomerInvoiceStore(
    (state) => ({
      setInvoiceOrders: state.setInvoiceOrders,
      updateInvoiceOrders: state.updateInvoiceOrders,
    }),
    shallow
  );

  useEffect(async () => {
    setLoadingOrders(true);
    try {
      const { data: responseOrders } = await axios({
        method: 'GET',
        url: apiInvoiceOrdersPath(invoice),
      });
      setOrders(responseOrders);
      const invoiceOrders = responseOrders.filter((order) => order.invoice_id === invoice.id || order.gst_free_invoice_id === invoice.id);
      setInvoiceOrders({
        invoice,
        invoiceOrders,
      });
    } catch (err) {
      // do nothing
    }
    setLoadingOrders(false);
  }, []);

  const handleOrderAssign = async ({ order, invoice }) => {
    const invoiceID = order.invoice_id ? null : invoice.id;
    setOrders((state) => state.map((stateOrder) => {
      return stateOrder.id == order.id ? {...stateOrder, invoice_id: invoiceID, gst_free_invoice_id: null } : stateOrder
    })); 
  }

  // show the confirm invoice orders button if there are changes
  useEffect(() => {
    if (isMounted.current) {
      if (orders && invoice.hasOwnProperty('orders')) {
        const orderInvoiceOrderIds = orders.filter((order) => order.invoice_id === invoice.id || order.gst_free_invoice_id === invoice.id).map((order) => order.id);
        const invoiceOrderIDs = invoice.orders.map((order) => order.id);
        const hasSameIDs = JSON.stringify(orderInvoiceOrderIds.sort()) === JSON.stringify(invoiceOrderIDs.sort());
        setHasChanges(!hasSameIDs);
      }
    }
    isMounted.current = true;
  }, [orders, invoice.orders]);

  const attachedOrders = [];
  const unAttachedOrders = [];
  orders.forEach((order) => {
    if (order.invoice_id === invoice.id || order.gst_free_invoice_id === invoice.id) {
      attachedOrders.push(order);
    } else {
      unAttachedOrders.push(order);
    }
  });

  const handleSave = () => {
    const invoiceOrders = orders.filter((order) => order.invoice_id === invoice.id)
    updateInvoiceOrders({invoice, invoiceOrders})
  }

  if (loadingOrders) {
    return (
      <>
        <div className="po-grouped-orders-heading">Manage Invoice #{invoice.number}</div>
        <InvoiceOrdersSkeleton />
      </>
    )
  }

  return (
    <>
      <div className="po-grouped-orders-heading">
        Manage Invoice #{invoice.number}
        {hasChanges && <a className='button tiny ml-1' onClick={handleSave}>Confirm Invoice Orders ({attachedOrders.length})</a>}
        <a className='float-right button gray-btn tiny ml-1-2' onClick={() => setManageInvoice(false)}>Close</a>
        {!hasChanges && !!invoice.orders?.length && <a className='float-right button tiny' onClick={() => setShowDocumentGenerator(true)}>Generate Document(s)</a>}
      </div>

      <h6 className="po-grouped-orders-heading">
        Attached to Invoice #{invoice.number}
        <span className="ml-1-2">({attachedOrders.length})</span>
      </h6>
      {attachedOrders.length > 0 && (
        <div className="po-order-list">
          {attachedOrders.map((order) => (
            <InvoiceOrder
              key={`invoice-order-${invoice.id}-order-${order.id}`}
              invoice={invoice}
              order={order}
              handleOrderAssign={handleOrderAssign}
            />
          ))}
        </div>
      )}
      {unAttachedOrders.length > 0 && (
        <>
          <h6 className="po-grouped-orders-heading">Orphan Orders ({unAttachedOrders.length})</h6>
          <div className="po-order-list">
            {unAttachedOrders.map((order) => (
              <InvoiceOrder
                key={`invoice-order-${invoice.id}-order-${order.id}`}
                invoice={invoice}
                order={order}
                handleOrderAssign={handleOrderAssign}
              />
            ))}
          </div>
        </>
      )}
      {!!showDocumentGenerator && <DocumentGenerator invoice={invoice} setShowDocumentGenerator={setShowDocumentGenerator} />}
    </>
  )
}

InvoiceManagement.propTypes = {
  invoice: PropTypes.object.isRequired,
  setManageInvoice: PropTypes.func.isRequired,
}

export default InvoiceManagement;