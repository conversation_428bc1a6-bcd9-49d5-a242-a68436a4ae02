import { useState, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import moment from 'moment';

// actions
import shallow from 'zustand/shallow';
import useCustomerInvoiceStore from 'store/customerInvoiceStore';

const NUMBER_HEX_SIZE = 4

const initialInvoice = {
  number: '',
  from_at: '',
  to_at: '',
}

const NewInvoice = () => {
  const [isNew, setIsNew] = useState(false);
  const [invoice, setInvoice] = useState(initialInvoice);

  const { createInvoice } = useCustomerInvoiceStore(
    (state) => ({
      createInvoice: state.createInvoice,
    }),
    shallow
  );

  // set randomized Invoice number
  useEffect(() => {
    if (!isNew) return;

    const today = new Date();
    const randomisedNumber = moment(today).format('YYMMDD') + [...Array(NUMBER_HEX_SIZE)].map(() => Math.floor(Math.random() * 16).toString(16)).join('')
    setInvoice((state) => ({...state, number: randomisedNumber }))
  }, [isNew])

  const handleChange = (event) => {
    setInvoice((state) => ({...state, [event.target.name]: event.target.value }));
  }

  const handleDateChange = (name, datetime) => {
    const selectedDate = moment(datetime, 'dddd, MMM DD, YYYY')?.format('YYYY/MM/DD');
    setInvoice((state) => ({ ...state, [name]: selectedDate }));
  };

  const handleSave = async () => {
    try {
      await createInvoice({ invoice });
      setInvoice(initialInvoice); // reset invoice
      setIsNew(false); // close form
    } catch (error) {
      // do nothing
    }
  };

  if (isNew) {
    return (
      <div className="customer-invoice customer-data">
        <div className="list-flex-3 pr-1">
          <label>Number</label>
          <input type="text" name="number" className="form-input" value={invoice.number} onChange={handleChange} />
        </div>
        <div className="list-flex-3 pr-1">
          <label>From</label>
          <DatePicker
            selected={invoice.from_at ? new Date(invoice.from_at) : ''}
            onChange={(datetime) => handleDateChange('from_at', datetime)}
            name="from_at"
            dateFormat="dd-MM-yyyy"
            className="form-input"
            autoComplete="off"
          />
        </div>
        <div className="list-flex-3 pr-1">
          <label>To</label>
          <DatePicker
            selected={invoice.to_at ? new Date(invoice.to_at) : ''}
            onChange={(datetime) => handleDateChange('to_at', datetime)}
            name="to_at"
            dateFormat="dd-MM-yyyy"
            className="form-input"
            autoComplete="off"
          />
        </div>
        <div className="list-flex-3">
          <a className="button" onClick={handleSave}>
            Save
          </a>
          <a className="button gray-btn ml-1" onClick={() => setIsNew(false)}>
            cancel
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="customer-invoice customer-data">
      <a onClick={() => setIsNew(true)}>Create New Invoice</a>
    </div>
  )
}

export default NewInvoice;