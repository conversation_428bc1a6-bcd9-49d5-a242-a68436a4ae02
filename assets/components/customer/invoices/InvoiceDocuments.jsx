import PropTypes from 'prop-types';
import { Modal } from 'react-responsive-modal';

const InvoiceDocuments = ({versionedDocuments, setShowAllDocuments}) => (
  <Modal
    classNames={{ modal: 'reveal customer-form' }}
    open
    onClose={() => setShowAllDocuments(false)}
    center
    showCloseIcon={false}
  >
    <div className="text-center">
      <h3>Invoice Documents</h3>
      {versionedDocuments.map((versionDocuments) => (
        <>
          {versionDocuments.documents.length > 1 && <strong>Version {versionDocuments.version}:</strong>}
          <ul>
            {versionDocuments.documents.map((document) => <li><a target="_blank" href={document.url}>{document.name}</a></li>)}
          </ul>
        </>
      ))}
    </div>
  </Modal>
)

InvoiceDocuments.propTypes = {
  versionedDocuments: PropTypes.array.isRequired,
  setShowAllDocuments: PropTypes.func.isRequired,
}

export default InvoiceDocuments;