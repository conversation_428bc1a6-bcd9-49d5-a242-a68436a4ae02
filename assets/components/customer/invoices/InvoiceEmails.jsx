import PropTypes from 'prop-types';
import { Modal } from 'react-responsive-modal';

const InvoiceEmails = ({ invoice, setShowEmails }) => {
  return (
    <Modal
      classNames={{ modal: 'reveal modal modal-drawerx budget-modal-sliderx' }}
      open={true}
      showCloseIcon={false}
      onClose={() => setShowEmails(false)}
    >
      <div className='form-header'>
        <div className='pt-1 px-1 between-flex'>
          <h3 className='p-0'>Invoice Emails</h3>
          <span className='close-modal-drawer' onClick={() => setShowEmails(false)}>
            X
          </span>
        </div>
        <div className='add-contact-tabs row'>
          <ul>
            {invoice.emails.map((email, idx) => (
              <li>
                <p>
                  <strong>{email.sent_at}</strong>
                  <br />
                  To: {email.recipient}
                </p>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </Modal>
  )
}

InvoiceEmails.propTypes = {
  invoice: PropTypes.object.isRequired,
  setShowEmails: PropTypes.func.isRequired,
}

export default InvoiceEmails;