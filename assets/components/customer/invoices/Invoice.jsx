import { useState, useContext } from 'react';
import PropTypes from 'prop-types';
import shallow from 'zustand/shallow';
import ReactTooltip from 'react-tooltip';

import adminContext from 'contexts/adminContext';
import useCustomerInvoiceStore from 'store/customerInvoiceStore';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

// components
import InvoiceManagement from './InvoiceManagement';
import InvoiceDocuments from './InvoiceDocuments';
import InvoiceEmails from './InvoiceEmails';

const Invoice = ({ invoice, index }) => {
  const { isAdmin } = useContext(adminContext);
  const [manageInvoice, setManageInvoice] = useState(false);
  const [showAllDocuments, setShowAllDocuments] = useState(false);
  const [showEmails, setShowEmails] = useState(false);

  const { updateInvoice } = useCustomerInvoiceStore(
    (state) => ({
      updateInvoice: state.updateInvoice,
    }),
    shallow
  );

  if (invoice.filteredOut) return null;

  let circleColor = getCircleIconColor(index);
  if (invoice.failed_payment) circleColor = '#eee';

  const handleDoNotNotify = () => {
    updateInvoice({
      invoice: { id: invoice.id, do_not_notify: !invoice.do_not_notify },
    });
  };

  const numberOfOrders = invoice.hasOwnProperty('orders') ? invoice.orders.length : invoice.number_of_orders;
  const invoiceOrderIds = invoice.hasOwnProperty('orders') ? invoice.orders.map((order) => order.id) : invoice.order_ids;
  const invoiceOrderNames = invoice.hasOwnProperty('orders') ? invoice.orders.map((order) => order.name) : invoice.order_names;

  return (
    <>
      <div className="customer-invoice customer-data">
        <div className="list-flex-2 invoice-header">
          <span className="circle-icon" style={{ background: circleColor }}>
            {invoice.to_at}
          </span>
          <strong className="invoice-date">{invoice.invoice_date}</strong>
        </div>
        <div className="list-flex-2">
          {invoice.failed_payment && <span>&#9888</span>}
          {`#${invoice.number}`}
          {isAdmin && invoice.status && <span style={{ display: 'block' }}>({invoice.status})</span>}
        </div>
        <div className="list-flex-2" style={{ cursor: 'help', color: '#9f9f9f' }}>
          {(!invoice.do_not_notify || isAdmin) && !!invoiceOrderIds.length && (
            <>
              <span data-tip data-for={`invoice-${invoice.id}-orders`}>
                {numberOfOrders > 1 ? 'Multiple Orders 📄' : `Order #${invoiceOrderIds[0]}`}
              </span>
              <ReactTooltip id={`invoice-${invoice.id}-orders`} className="reveal" place="bottom" effect="solid">
                {numberOfOrders > 1
                  ? invoiceOrderIds.map((id) => `#${id}`).join(' ')
                  : invoiceOrderNames.join()}
              </ReactTooltip>
            </>
          )}
          {!!invoice.po_number && (
            <p>PO: #{invoice.po_number}</p>
          )}
        </div>
        <div className="list-flex-3 text-center">
          {invoice.is_due && <p className="invoice-not-overdue">Due: {invoice.due_at}</p>}
          {!invoice.is_due && !invoice.is_overdue && <p className="invoice-not-overdue">{invoice.payment_status}</p>}
          {invoice.is_overdue && (
            <>
              <p className="invoice-overdue" data-tip data-for={`invoice-${invoice.id}-overdue`}>
                Overdue by: {invoice.due_distance}
              </p>
              <ReactTooltip id={`invoice-${invoice.id}-overdue`} place="bottom" effect="solid">
                Due on: {invoice.due_at}
              </ReactTooltip>
            </>
          )}
          {invoice.can_manage && ['AMENDED', 'UNPAID'].includes(invoice.payment_status) && (
            <a onClick={handleDoNotNotify}>{invoice.do_not_notify ? 'SET TO NOTIFY' : 'DO NOT NOTIFY'}</a>
          )}
        </div>
        <div className="list-flex-1">{invoice.amount}</div>
        <div className="list-flex-2">
          {invoice.document_url && <a href={invoice.document_url}>Invoice #{invoice.number}</a>}
          {invoice.documents.length > 1 && <a onClick={() => setShowAllDocuments(true)} style={{ display: 'block', fontSize: '0.8rem' }}>(show more)</a>}
        </div>
        <div className="list-flex-1 text-center">
          {invoice.pay_url && <a className="button pay-button" href={invoice.pay_url}>Pay</a>}
          {!!invoice.emails?.length && <a style={{ display: 'block' }} onClick={() => setShowEmails(true)}>Show Email(s)</a>}
          {invoice.can_manage && <a style={{ display: 'block' }} onClick={() => setManageInvoice(!manageInvoice)}>Manage Invoice</a>}
        </div>
      </div>
      {invoice.can_manage && manageInvoice && <InvoiceManagement invoice={invoice} setManageInvoice={setManageInvoice} />}
      {showAllDocuments && <InvoiceDocuments versionedDocuments={invoice.documents} setShowAllDocuments={setShowAllDocuments} />}
      {!!invoice.emails?.length && showEmails && <InvoiceEmails invoice={invoice} setShowEmails={setShowEmails} />}
    </>
  );
};

Invoice.propTypes = {
  invoice: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Invoice;
