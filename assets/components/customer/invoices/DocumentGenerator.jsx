import { useState } from 'react';
import PropTypes from 'prop-types';

// store
import shallow from 'zustand/shallow';
import useCustomerInvoiceStore from 'store/customerInvoiceStore';

// components
import { Modal } from 'react-responsive-modal';

const DocumentGenerator = ({invoice, setShowDocumentGenerator}) => {

  const [generatedDocuments, setGeneratedDocuments] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const { generateDocuments } = useCustomerInvoiceStore(
    (state) => ({
      generateDocuments: state.generateDocuments,
    }),
    shallow
  );

  const generateDocument = async (notifyCustomer) => {
    if (isGenerating) return;

    setIsGenerating(true);
    try {
      const newlyGeneratedDocuments = await generateDocuments({ invoice, notifyCustomer });
      setGeneratedDocuments(newlyGeneratedDocuments);
    } catch (err) {
      // do nothing
    }
    setIsGenerating(false)
  }

  return (
    <Modal
      classNames={{ modal: 'reveal customer-form' }}
      open
      onClose={() => setShowDocumentGenerator(false)}
      center
      showCloseIcon={false}
    >
      <div className="text-center">
        <h3>Choose one</h3>

        {isGenerating && <strong>Generating Document(s)...Please wait!</strong>}

        {!isGenerating && (
          <>
            <a className='button' onClick={() => generateDocument(false)}>Only Generate Document</a>
            <a className='button ml-1-2' onClick={() => generateDocument(true)}>Generate Document<br/> and Email Customer</a>
          </>
        )}
        {!isGenerating && !!generatedDocuments.length && (
          <>
            <h6>Generated Documents</h6>
            <ul>
            {generatedDocuments.map((document) => <li><a target="_blank" href={document.url}>{document.name}</a></li>)}
            </ul>
          </>
        )}
      </div>
    </Modal>
  )
}

DocumentGenerator.propTypes = {
  invoice: PropTypes.object.isRequired,
  setShowDocumentGenerator: PropTypes.func.isRequired,
}

export default DocumentGenerator;