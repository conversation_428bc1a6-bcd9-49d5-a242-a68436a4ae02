import { useState } from 'react';
import axios from 'axios';

import { becomeTeamAdminAPITeamOrdersPath } from 'routes';
import TeamIllustration from 'images/icons/team.svg';
import OrderIllustration from 'images/illustrations/ordering.png';
import AttendeeOrder from 'images/attendee-order.png';
import Lineup from 'images/lineup.png';
import TeamAdmin from 'images/team-ordering.png';

import Introduction from './Introduction';

const Panel = ({ panel, setPanel }) => {
  const [sentRequest, setSentRequest] = useState(false);
  const [error, setError] = useState(false);

  const panelData = [
    {
      title: 'Team Meal Ordering',
      brief:
        'Gone are the days of asking 20 people what they’d like to eat. Simply create an order, send out our provided magic link and allow your staff to place their own meal order. All orders are collated, individually bagged, labelled and shipped together. How good is that!',
      images: {
        illustration: TeamIllustration,
      },
    },
    {
      title: 'Easy Employee Ordering',
      brief:
        "Once you've sent out your team order link, it's a simple process for staff to order. All they need to do is enter their name and email, and choose what they'd like from the menu.",
      images: {
        illustration: OrderIllustration,
        main: AttendeeOrder,
      },
    },
    {
      title: 'Simple Team Meal Management',
      brief:
        "As a team admin, everything you need for managing staff meal orders is available. You can see who's ordered what, the status of each order, cancel individual orders, change suppliers or budgets, the power is yours!",
      images: {
        illustration: Lineup,
        main: TeamAdmin,
      },
    },
  ];

  async function makeTeamAdminRequest() {
    if (sentRequest) return;
    try {
      await axios.get(becomeTeamAdminAPITeamOrdersPath());
      setSentRequest(true);
    } catch {
      setError(true);
    }
  }

  let requestButtonText = 'Request to Become Team Admin';
  if (error) requestButtonText = 'Sorry, Something Went Wrong';
  if (sentRequest) requestButtonText = "Thanks! We'll be in touch";

  return (
    <>
      <div className="become-team-admin__info">
        <h3>{panelData[panel].title}</h3>
        <img src={panelData[panel].images.illustration} style={panel === 0 ? { width: '200px' } : {}} />
        <p className="become-team-admin__blurb">{panelData[panel].brief}</p>
        {!sentRequest && (
          <button
            className={`button ${error ? 'alert-btn' : ''} ${sentRequest ? 'grey-btn' : ''}`}
            onClick={makeTeamAdminRequest}
          >
            {requestButtonText}
          </button>
        )}
        {sentRequest && <span style={{ fontWeight: 'bold', paddingTop: '12px' }}>Thanks! We'll Be In Touch</span>}
        {panel !== 2 && (
          <button className="button black-btn" onClick={() => setPanel((state) => state + 1)}>
            Learn More
          </button>
        )}
      </div>
      <div className={`become-team-admin__options ${panel !== 0 && 'no-grid'}`}>
        {panel === 0 && <Introduction />}
        {panel !== 0 && <img src={panelData[panel].images.main} />}
      </div>
    </>
  );
};
export default Panel;
