import { useState } from 'react';
import Panel from './Panel';

const BecomeTeamOrderAdminApp = () => {
  const [panel, setPanel] = useState(0);
  return (
    <div>
      <div className="become-team-admin">
        <Panel panel={panel} setPanel={setPanel} />
      </div>
      <div className="info-navigate">
        <span className={`navigate-ellipsis ${panel === 0 ? 'active' : ''}`} onClick={() => setPanel(0)} />
        <span className={`navigate-ellipsis ${panel === 1 ? 'active' : ''}`} onClick={() => setPanel(1)} />
        <span className={`navigate-ellipsis ${panel === 2 ? 'active' : ''}`} onClick={() => setPanel(2)} />
      </div>
    </div>
  );
};

export default BecomeTeamOrderAdminApp;
