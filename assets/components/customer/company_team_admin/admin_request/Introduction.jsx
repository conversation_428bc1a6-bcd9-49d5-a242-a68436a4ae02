import Book from 'images/icons/boook.svg';
import Dollar from 'images/icons/dollar-sign.svg';
import Mail from 'images/icons/mail.svg';
import Packed from 'images/icons/packed.svg';
import Rotate from 'images/icons/rotate.svg';
import Select from 'images/icons/select.svg';

const Introduction = () => (
  <>
    <div>
      <img src={Select} className="become-team-admin__image" />
      <h6>Team Choice</h6>
      <p>
        Each team member can choose what they’d like to eat when you place a team order. A unique link will allow them
        to join your order and choose want they want from the supplier of your choice
      </p>
    </div>
    <div>
      <img src={Mail} className="become-team-admin__image" />
      <h6>Easy Invite</h6>
      <p>
        Send a single link out to your team so that each team member can add their details and choose what they’d like.
      </p>
    </div>
    <div>
      <img src={Rotate} className="become-team-admin__image" />
      <h6>Recur or Defer</h6>
      <p>
        With our team ordering service, you can get orders to recur and switch delivery days as needed. Making sure your
        team gets the meals they love, when they need them.
      </p>
    </div>
    <div>
      <img src={Dollar} className="become-team-admin__image" />
      <h6>Control Budgets</h6>
      <p>Stop cheeky staff from ordering too much, set a per head budget that they are not permitted to go over.</p>
    </div>
    <div>
      <img src={Book} className="become-team-admin__image" />
      <h6>Customise Menus</h6>
      <p>
        Control which menu sections your team members can order from. Streamline diverse dietary preferences, budget
        constraints, and meal preferences.{' '}
      </p>
    </div>
    <div>
      <img src={Packed} className="become-team-admin__image" />
      <h6>Packed & Labelled</h6>
      <p>
        No need to get out the share plates and divide all the food up. Each meal arrives in a labelled bag for each
        team members meal.
      </p>
    </div>
  </>
);

export default Introduction;
