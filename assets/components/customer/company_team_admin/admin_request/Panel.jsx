import { useState } from 'react';

import AdminIllustration from 'images/illustrations/management.png';

import RequestForm from './RequestForm';

const Panel = () => {
  const [openRequestForm, setOpenRequestForm] = useState(false);
  const [sentRequest, setSentRequest] = useState(false);

  let requestButtonText = 'Request to Become Team Admin';
  if (sentRequest) requestButtonText = "Thanks! We'll be in touch";

  return (
    <>
      <div className="become-team-admin__info">
        <h3>Company Team Admin Request</h3>
        <img src={AdminIllustration} style={{ width: '360px' }} />
        <p className="become-team-admin__blurb">
          Unlock the power of seamless team management by becoming an Admin. Gain full access to your organization’s
          accounts, manage user permissions, and stay in control with a powerful dashboard that gives you real-time
          insights into your team’s activity. Streamline operations, enhance security, and lead with confidence—all from
          one central hub.
        </p>
        {!sentRequest && (
          <button
            className={`button ${sentRequest ? 'grey-btn' : ''}`}
            onClick={() => setOpenRequestForm(!openRequestForm)}
          >
            {requestButtonText}
          </button>
        )}
        {sentRequest && <span style={{ fontWeight: 'bold', paddingTop: '12px' }}>Thanks! We'll Be In Touch</span>}
      </div>
      <div className="become-team-admin__options no-grid">
        <h6 style={{ padding: '16px', fontWeight: 'normal' }}>Become an Admin and Take Control</h6>
        <div className="video-container">
          <iframe
            src="https://www.youtube.com/embed/KH51FL2cxI8"
            title="YouTube video player"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
          />
        </div>
      </div>
      {!!openRequestForm && <RequestForm setSentRequest={setSentRequest} setOpenRequestForm={setOpenRequestForm} />}
    </>
  );
};
export default Panel;
