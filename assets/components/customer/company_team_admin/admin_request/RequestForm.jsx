import { useState } from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-responsive-modal';

import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { becomeCompanyTeamAdminAPICustomersPath } from 'routes';

const RequestForm = ({ setOpenRequestForm, setSentRequest }) => {
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState([]);

  const makeTeamAdminRequest = async () => {
    try {
      setErrors([]);
      setIsLoading(true);
      const { data } = await axios({
        method: 'POST',
        url: becomeCompanyTeamAdminAPICustomersPath(),
        data: { message },
        headers: csrfHeaders(),
      });
      setSentRequest(true);
      setOpenRequestForm(false);
    } catch (err) {
      setErrors(err?.response?.data?.errors);
    }
    setIsLoading(false);
  };

  return (
    <Modal
      classNames={{ modal: 'reveal customer-form' }}
      open
      showCloseIcon={false}
      center
      onClose={() => setOpenRequestForm(false)}
    >
      <h3 style={{ fontSize: '20px', fontWeight: 'bold' }}>Become a Company Team Admin</h3>
      <p>
        We'd love to get you setup as a company team admin. To help us process your request, please add any relevant
        details about your team management here
      </p>
      <div className="mb-1-2">
        <label>
          Message <small>(optional)</small>
        </label>
        <textarea
          className="form-input"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          rows="3"
          placeholder={"I'd like access to <name>"}
        />
      </div>
      {!!errors.length && <p className="is-invalid-label">{errors.join('. ')}</p>}
      <div>
        <a className="button mr-1-2 mb-0" onClick={makeTeamAdminRequest}>
          {isLoading ? 'Sending Request...' : 'Send Request'}
        </a>
        <a className="button gray-btn mb-0" onClick={() => setOpenRequestForm(false)}>
          Cancel
        </a>
      </div>
    </Modal>
  );
};

RequestForm.propTypes = {
  setOpenRequestForm: PropTypes.func.isRequired,
  setSentRequest: PropTypes.func.isRequired,
};

export default RequestForm;
