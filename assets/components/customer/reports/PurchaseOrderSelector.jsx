import { useContext } from 'react';
import PropType from 'prop-types';

import DropDown from 'react-dropdown';
import appContext from 'contexts/appContext';

const PurchaseOrderSelector = ({ formType, PO, setPO }) => {
  const { poOptions } = useContext(appContext);

  const sanitizedPOOptions = [
    formType === 'edit' ? ['None', ''] : ['All', ''],
    ...poOptions,
    ...(formType === 'report' ? [['Not Connected to PO', 'no-po']] : []),
  ];

  return (
    <DropDown
      value={PO}
      options={sanitizedPOOptions.map(([label, value]) => ({ label, value }))}
      className="budget-field po dashboard-filter customer-order-dropdown po-dropdown"
      menuClassName="dropdown"
      controlClassName="dashboard-filter__type black"
      placeholder="Po Number"
      onChange={({ label, value }) => setPO({ label, id: value })}
    />
  );
};

PurchaseOrderSelector.propTypes = {
  formType: PropType.string.isRequired,
  PO: PropType.object,
  setPO: PropType.func.isRequired,
};

export default PurchaseOrderSelector;
