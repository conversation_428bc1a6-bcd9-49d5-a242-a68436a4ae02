import { useEffect } from 'react';
import shallow from 'zustand/shallow';
import DatePicker from 'react-datepicker';
import useReportsStore from 'store/configureReportsStore';
import { getReportMonthLabel } from 'utilities/graph-options';
import PurchaseOrderSelector from './PurchaseOrderSelector';

const ReportGenerator = ({ setManageBudgets }) => {
  const { loading, dates, setDate, setLabels, setPO, fetchReport, PO } = useReportsStore(
    (state) => ({
      loading: state.loadingReport,
      dates: state.dates,
      setDate: state.setDate,
      setPO: state.setPO,
      setLabels: state.setLabels,
      fetchReport: state.fetchReport,
      PO: state.PO,
    }),
    shallow
  );

  useEffect(async () => {
    if (!loading && dates.start && dates.end) {
      fetchReport();
    }
  }, [dates, PO]);

  function onDateChange(newDates) {
    const [start, end] = newDates;
    if (start && end) {
      setLabels({ start: getReportMonthLabel(start), end: getReportMonthLabel(end) });
    }
    setDate('start', start);
    setDate('end', end);
  }

  function onPOChange(newPO) {
    let poLabel = '';
    if (newPO.id == 'no-po') {
      poLabel = newPO.label;
    } else if (newPO.id) {
      poLabel = `PO: ${newPO.label}`;
    }
    setLabels({ PO: poLabel });
    setPO(newPO);
  }

  return (
    <div className="dashboard-filters reports-filters">
      <div className="dashboard-filter dashboard-filter__datepicker">
        <span className="between-flex">For Dates: </span>
        <div>
          <DatePicker
            startDate={dates.start}
            endDate={dates.end}
            selected={dates.start}
            onChange={(newDates) => onDateChange(newDates)}
            dateFormat="MM/yyyy"
            showMonthYearPicker
            selectsRange
            className="dashboard-filter"
          />
        </div>
      </div>

      <PurchaseOrderSelector formType="report" PO={null} setPO={onPOChange} />
      <button type="button" className="generate-report-button ml-1-4" onClick={() => setManageBudgets(true)}>
        Manage Budgets
      </button>
    </div>
  );
};
export default ReportGenerator;
