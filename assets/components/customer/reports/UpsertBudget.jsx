import { useContext, useEffect, useState } from 'react';
import axios from 'axios';
import shallow from 'zustand/shallow';
import DatePicker from 'react-datepicker';
import moment from 'moment';
import { csrfHeaders } from 'utilities/csrfHeaders';
import appContext from 'contexts/appContext';
import { apiBudgetsPath, apiBudgetPath } from 'routes';

import useReportsStore from 'store/configureReportsStore';
import PurchaseOrderSelector from './PurchaseOrderSelector';

const UpsertBudget = ({
  budget = { starts_on: null, ends_on: null, customer_purchase_order_id: null, value: 0 },
  POFromFilter,
  setShowBudgetForm,
  showBudgetForm,
}) => {
  const { poOptions } = useContext(appContext);

  const startsOnDate = budget.starts_on ? new Date(budget.starts_on) : null;
  const endsOnDate = budget.ends_on ? new Date(budget.ends_on) : null;
  const [budgetAmount, setBudgetAmount] = useState(budget.value);
  const [startDate, setStartDate] = useState(startsOnDate);
  const [endDate, setEndDate] = useState(endsOnDate);
  const { fetchReport, fetchBudgets } = useReportsStore(
    (state) => ({
      fetchReport: state.fetchReport,
      fetchBudgets: state.fetchBudgets,
    }),
    shallow
  );
  const selectedPO = poOptions.reduce((acc, [label, value]) => {
    if (value === budget?.customer_purchase_order_id && showBudgetForm === 'Edit') {
      return { label, value };
    }
    // Only show budget selected from report filters if not editing and the value is not 'All' or 'No Po'
    if (POFromFilter && value === POFromFilter.id && showBudgetForm === 'Create' && value !== '' && value !== 'no-po') {
      return { label, id: value };
    }
    return acc;
  }, null);
  const [PO, setPO] = useState(selectedPO);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const [endDateNotValid, setEndDateNotValid] = useState(false);

  // DatePickerComponent prevents whole component from rendering unless a conditional render is used
  useEffect(() => setShowDatePicker(true), []);

  async function handleBudgetRequest() {
    const requestData = {
      budget: {
        ...(showBudgetForm === 'Edit' && { id: budget?.id }),
        value: budgetAmount,
        frequency: 'monthly',
        starts_on: moment(startDate).format('YYYY-MM-DD'),
        ends_on: endDate && moment(endDate).format('YYYY-MM-DD'),
        ...(PO?.id && { customer_purchase_order_id: PO.id }),
      },
    };

    const requestMethod = showBudgetForm === 'Create' ? 'POST' : 'PUT';
    const requestUrl =
      showBudgetForm === 'Create' ? apiBudgetsPath({ format: 'json' }) : apiBudgetPath(budget, { format: 'json' });

    const requestConfig = {
      method: requestMethod,
      url: requestUrl,
      data: requestData,
      headers: csrfHeaders(),
    };

    try {
      await axios(requestConfig);
      fetchBudgets();
      setShowBudgetForm(false);
      fetchReport();
    } catch (err) {
      if (err.response.data.errors.includes('Must end after it starts')) {
        setEndDateNotValid(true);
      }
    }
  }

  async function deleteBudget() {
    await axios({
      method: 'DELETE',
      url: apiBudgetPath(budget),
      headers: csrfHeaders(),
    });
    setShowBudgetForm(false);
    fetchBudgets();
    fetchReport();
  }

  return (
    <>
      <h2 className="modal-title mt-1">
        {showBudgetForm} Budget{' '}
        <button className="close-budget-form" onClick={() => setShowBudgetForm(false)}>
          X
        </button>
      </h2>
      <div className="between-flex">
        <label className="list-flex-1">From</label>
        <label className="list-flex-1">To</label>
      </div>
      {showDatePicker && (
        <div className="between-flex">
          <DatePicker
            selected={startDate}
            onChange={(newDate) => setStartDate(newDate)}
            dateFormat="MM/yyyy"
            showMonthYearPicker
            className="form-input budget-field"
            placeholderText="Start Date"
          />

          <DatePicker
            selected={endDate}
            onChange={(newDate) => setEndDate(newDate)}
            dateFormat="MM/yyyy"
            showMonthYearPicker
            className={`form-input budget-field${endDateNotValid ? ' error' : ''}`}
            placeholderText="Continuous"
          />
        </div>
      )}
      <label style={{ marginTop: '1rem' }}>PO Number (leave blank for general budget)</label>
      <PurchaseOrderSelector formType="edit" PO={PO} setPO={setPO} />

      <label style={{ marginTop: '1.6rem' }}>Monthly Budget Amount</label>
      <span className="budget-amount-dollar">$</span>
      <input
        type="number"
        className="budget-amount-input"
        value={budgetAmount}
        onChange={(e) => setBudgetAmount(e.target.value)}
      />

      <div className="form-footer between-flex">
        <p className="budget-details">
          Budget of ${budgetAmount}
          {!!startDate && ` from ${moment(startDate).format('MMM YYYY')}`}
          {!!endDate && ` to ${moment(endDate).format('MMM YYYY')}`}
        </p>
      </div>
      <div className="form-button-container">
        <button className="button form-button" onClick={() => handleBudgetRequest()}>
          {showBudgetForm === 'Edit' ? 'Update' : showBudgetForm} Budget
        </button>
        {showBudgetForm === 'Edit' && (
          <button className="button form-button warning" onClick={deleteBudget}>
            Delete Budget
          </button>
        )}
      </div>
    </>
  );
};

export default UpsertBudget;
