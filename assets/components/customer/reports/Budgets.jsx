import { useContext, useState } from 'react';
import shallow from 'zustand/shallow';
import UpsertBudget from './UpsertBudget';
import PurchaseOrderSelector from './PurchaseOrderSelector';
import BudgetList from './BudgetList';
import appContext from 'contexts/appContext';
import useReportsStore from 'store/configureReportsStore';

const Budgets = () => {
  const [showBudgetForm, setShowBudgetForm] = useState(false);
  const [budgetToEdit, setBudgetToEdit] = useState(null);

  const { poOptions } = useContext(appContext);
  const { loading, budgets, POFromReport } = useReportsStore(
    (state) => ({
      loading: state.loadingBudgets,
      budgets: state.budgets,
      POFromReport: state.PO,
    }),
    shallow
  );

  const selectedPO = poOptions.reduce((acc, [label, value]) => {
    // Only show budget selected from report filters if not editing and the value is not 'All' or 'No Po'
    if (value === POFromReport.id && value !== '' && value !== 'no-po') {
      return { label, id: value };
    }
    return acc;
  }, null);
  const [PO, setPO] = useState(selectedPO);

  const filteredBudgets = budgets.filter(
    (budget) => !PO || PO.id === '' || budget.customer_purchase_order_id === PO.id
  );

  if (showBudgetForm) {
    return (
      <UpsertBudget
        POFromFilter={PO}
        budget={budgetToEdit || undefined}
        setShowBudgetForm={setShowBudgetForm}
        showBudgetForm={showBudgetForm}
      />
    );
  }

  return (
    <div>
      <h2 className="mt-1">Budgets</h2>
      <div className="between-flex no-center mb-1">
        <p style={{ fontSize: '14px', maxWidth: '220px', color: '#9e9e9e', margin: 0 }}>
          Manage your budgets with ease. You can attach PO's, select for a range of months, or set it to recur.
        </p>
        <button
          type="button"
          className="create-budget"
          onClick={() => {
            setBudgetToEdit(null);
            setShowBudgetForm('Create');
          }}
        >
          Create Budget
        </button>
      </div>

      <PurchaseOrderSelector formType="list" PO={PO} setPO={setPO} />
      {loading && <p style={{ fontSize: '18px' }}>Loading Budgets...</p>}
      {!loading && (
        <BudgetList budgets={filteredBudgets} setBudgetToEdit={setBudgetToEdit} setShowBudgetForm={setShowBudgetForm} />
      )}
    </div>
  );
};

export default Budgets;
