import PropType from 'prop-types';
import moment from 'moment';

const BudgetList = ({ budgets, setBudgetToEdit, setShowBudgetForm }) => {
  if (!budgets.length) return <p>You currently have no budgets</p>;

  return (
    <>
      <p style={{ fontSize: '18px' }}>Current Budgets</p>
      <div className="edit-budget-row">
        <p className="budget-label">Start</p>
        <p className="budget-label">End</p>
        <p className="budget-label">Amount</p>
        <p className="budget-label" />
      </div>
      {budgets.map((budget) => (
        <div className="edit-budget-row" key={budget.id}>
          {budget.po_number ? (
            <p className="budget-type">PO: {budget.po_number}</p>
          ) : (
            <p className="budget-type">General Budget</p>
          )}
          <p className="">{moment(budget.starts_on).format('MMM YYYY')}</p>
          <p className="">
            {moment(budget.ends_on).format('MMM YYYY') !== 'Invalid date'
              ? moment(budget.ends_on).format('MMM YYYY')
              : 'Continuous'}
          </p>
          <p className="">${budget.value}</p>
          <button
            className="button"
            type="button"
            onClick={() => {
              setBudgetToEdit(budget);
              setShowBudgetForm('Edit');
            }}
          >
            Edit
          </button>
        </div>
      ))}
    </>
  );
};

BudgetList.propTypes = {
  budgets: PropType.array.isRequired,
};

export default BudgetList;
