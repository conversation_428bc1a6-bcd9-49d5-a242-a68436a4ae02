import { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  LineController,
  BarController,
} from 'chart.js';
import shallow from 'zustand/shallow';
import useReportsStore from 'store/configureReportsStore';
import ExportReportModal from './ExportReportModal';
import ReportGenerator from './ReportGenerator';
import ReportBarGraph from './ReportBarGraph';
import ReportDoughnut from './ReportDoughnut';
import BudgetSlider from './BudgetSlider';

ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  LineController,
  BarController
);

const ReportsApp = () => {
  const { fetchReport, loading, exporting } = useReportsStore(
    (state) => ({
      fetchReport: state.fetchReport,
      loading: state.loadingReport,
      exporting: state.exporting,
    }),
    shallow
  );

  const [manageBudgets, setManageBudgets] = useState(false);

  useEffect(async () => {
    await fetchReport();
  }, []);

  return (
    <>
      <ReportGenerator setManageBudgets={setManageBudgets} />
      {loading && <ReportSkeleton />}
      {!loading && (
        <div className="reporting">
          <ReportBarGraph />
          <ReportDoughnut title="category" fields={['catering', 'snacks']} />
          <ReportDoughnut title="supplier" fields={['ethical', 'suppliers']} />
        </div>
      )}
      {manageBudgets && <BudgetSlider manageBudgets={manageBudgets} setManageBudgets={setManageBudgets} />}
      {exporting && <ExportReportModal />}
    </>
  );
};

const ReportSkeleton = () => {
  const { activeDoughnut, labels } = useReportsStore(
    (state) => ({
      activeDoughnut: state.activeDoughnut,
      labels: state.labels,
    }),
    shallow
  );
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      if (dots.length === 3) {
        setDots('');
      } else {
        setDots(`${dots}.`);
      }
    }, 300);

    return () => clearInterval(interval);
  }, [dots]);

  return (
    <div className="reporting loading">
      <div className="reporting-graph">
        <div className="between-flex">
          <h3 className="reports-heading">
            Spend for {labels.start} - {labels.end}
            {!!labels.PO && <small> ({labels.PO})</small>}
            {dots}
          </h3>
          <div>
            <a className="button icon csv">Export as CSV</a>
            {/* <a className="button icon pdf">Export as PDF</a> */}
          </div>
        </div>
        <div className="between-flex">
          <div className="between-flex" style={{ marginLeft: 'auto' }}>
            <span className="legend legend-catering">Catering</span>
            <span className="legend legend-snacks">Snacks</span>
          </div>
        </div>
        <div className="bar-container" />
      </div>
      <div className="reporting-doughnut">
        <h3>Category Spend</h3>
        <div style={{ marginBottom: '12px' }}>
          <a className={`category-spend ${activeDoughnut?.category === 'catering' ? 'active' : ''}`}>Catering</a>
          <a className={`category-spend ${activeDoughnut?.category === 'snacks' ? 'active' : ''}`}>Snacks</a>
        </div>
        <div className="doughnut-container" />
      </div>
      <div className="reporting-doughnut">
        <h3>Supplier Spend</h3>
        <div style={{ marginBottom: '12px' }}>
          <a className={`category-spend ${activeDoughnut?.supplier === 'ethical' ? 'active' : ''}`}>Ethical</a>
          <a className={`category-spend ${activeDoughnut?.supplier === 'suppliers' ? 'active' : ''}`}>All</a>
        </div>
        <div className="doughnut-container" />
      </div>
    </div>
  );
};

export default ReportsApp;
