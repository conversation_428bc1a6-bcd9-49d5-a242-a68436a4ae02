import { Chart } from 'react-chartjs-2';
import shallow from 'zustand/shallow';
import { barOptions, generateBarData } from 'utilities/graph-options';
import useReportsStore from 'store/configureReportsStore';

const ReportBarGraph = () => {
  const { labels, activePOLabel, report, setExporting } = useReportsStore(
    (state) => ({
      labels: state.labels,
      activePOLabel: state.PO.activeLabel,
      report: state.report,
      setExporting: state.setExporting,
    }),
    shallow
  );

  const showBudgetLegend = report.report_data.some((data) => data.budget !== 0);

  return (
    <div className="reporting-graph">
      <div className="between-flex">
        <h3 className="reports-heading">
          Spend for {labels.start} - {labels.end}
          {!!labels.PO && <small> ({labels.PO})</small>}
        </h3>
        <div className="between-flex">
          <a className="button icon csv" onClick={() => setExporting(true)}>
            Export as CSV
          </a>
          {/* Need to create PDF generation capablities with Prawn */}
          {/* <a className="button icon pdf">Export as PDF</a> */}
        </div>
      </div>
      <div className="between-flex">
        {!!activePOLabel && <p className="reports-po-number">{activePOLabel}</p>}
        <div className="between-flex" style={{ marginLeft: 'auto' }}>
          {showBudgetLegend && <span className="legend legend-below-budget">Below Budget</span>}
          {showBudgetLegend && <span className="legend legend-over-budget">Over Budget</span>}
          <span className="legend legend-catering">Catering</span>
          <span className="legend legend-snacks">Snacks</span>
        </div>
      </div>
      <div className="bar-container">
        <Chart options={barOptions} data={generateBarData(report.report_data)} />
      </div>
    </div>
  );
};
export default ReportBarGraph;
