import { useContext, useEffect } from 'react';
import shallow from 'zustand/shallow';
import { Modal } from 'react-responsive-modal';

import appContext from 'contexts/appContext';
import useReportsStore from 'store/configureReportsStore';
import Budgets from './Budgets';

const BudgetSlider = ({ manageBudgets, setManageBudgets }) => {
  const { fetchBudgets } = useReportsStore(
    (state) => ({
      fetchBudgets: state.fetchBudgets,
    }),
    shallow
  );

  useEffect(async () => {
    await fetchBudgets();
  }, []);

  return (
    <Modal
      classNames={{ modal: 'reveal modal modal-drawer budget-modal-slider' }}
      open={manageBudgets}
      showCloseIcon={false}
      onClose={() => setManageBudgets(false)}
    >
      <Budgets />
    </Modal>
  );
};

export default BudgetSlider;
