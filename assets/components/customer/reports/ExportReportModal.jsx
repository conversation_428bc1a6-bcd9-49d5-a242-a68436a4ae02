import { useContext, useState } from 'react';
import { Modal } from 'react-responsive-modal';
import shallow from 'zustand/shallow';
import { apiReportsPath } from 'routes';
import adminContext from 'contexts/adminContext';
import useReportsStore from 'store/configureReportsStore';
import { dateToDateString } from 'utilities/graph-options';

const ExportReportModal = () => {
  const { isAdmin, isTeamAdmin } = useContext(adminContext);
  const [type, setType] = useState('orders');
  const [companyWideData, setCompanyWideData] = useState(false);
  const [supplierCosts, setSupplierCosts] = useState(false);
  const { dates, POID, setExporting } = useReportsStore(
    (state) => ({
      dates: state.dates,
      POID: state.PO.id,
      setExporting: state.setExporting,
    }),
    shallow
  );

  async function generateReport(e) {
    e.preventDefault();
    window.location = createCSVLink();
  }

  function createCSVLink() {
    const formattedDates = {
      start: dateToDateString(dates.start, '01'),
      end: dateToDateString(dates.end, new Date(dates.end.getFullYear(), dates.end.getMonth() + 1, 0).getDate()),
    };
    const { start, end } = formattedDates;
    const POParam = POID ? `&purchase_order_id=${POID}` : '';
    const companyWideParam = companyWideData ? '&company_wide=1' : '';
    const supplierCostParam = supplierCosts ? '&with_supplier_costs=1' : '';
    const params = `&detail_type=${type}&start_date=${start}&end_date=${end}${POParam}${companyWideParam}${supplierCostParam}`;
    return `${apiReportsPath({ react_data: true, format: 'csv', report_type: 'monthly' })}${params}`;
  }

  return (
    <Modal classNames={{ modal: 'reveal customer-form' }} open center showCloseIcon onClose={() => setExporting(false)}>
      <h3 className="report-modal-heading">Export Report CSV</h3>
      <h6>What Details To Include?</h6>
      <div className="report-modal-options" onChange={(e) => setType(e.target.value)}>
        <input type="radio" id="orders" name="export-option" value="orders" defaultChecked />
        <label htmlFor="orders">Orders Data</label>
        <br />
        <input type="radio" id="products" name="export-option" value="products" />
        <label htmlFor="products">Products Data</label>
        <br />
        <input type="radio" id="order_lines" name="export-option" value="order_lines" />
        <label htmlFor="order_lines">Orderline Data</label>
        <br />
        <input type="radio" id="invoices" name="export-option" value="invoices" />
        <label htmlFor="invoices">Invoice Data</label>
        <br />
        <input type="radio" id="suppliers" name="export-option" value="suppliers" />
        <label htmlFor="suppliers">Suppliers Data</label>
        {isTeamAdmin && (
          <>
            <br />
            <input type="radio" id="team_order_attendees" name="export-option" value="team_order_attendees" />
            <label htmlFor="team_order_attendees">Team Order Attendee Data</label>
          </>
        )}
      </div>

      {isAdmin && (
        <p>
          <strong>Admin Only</strong>
          <br />
          <input
            type="checkbox"
            id="company-wide"
            name="company-wide"
            value="Gather Company Wide Data"
            className="mr-1-2"
            onChange={() => setCompanyWideData((state) => !state)}
          />
          <label htmlFor="company-wide">Gather Company Wide Data</label>
          <br />
          {type == 'orders' && (
            <>
              <input
                type="checkbox"
                id="supplier-costs"
                name="supplier-costs"
                value="Add Supplier Costs"
                className="mr-1-2"
                onChange={() => setSupplierCosts((state) => !state)}
              />
              <label htmlFor="supplier-costs">
                Add Supplier Costs
                <small className="ml-1-4">( Orders Data Only)</small>
              </label>
            </>
          )}
        </p>
      )}

      <a className="button generate-report-button export" onClick={generateReport}>
        Generate CSV
      </a>
    </Modal>
  );
};

export default ExportReportModal;
