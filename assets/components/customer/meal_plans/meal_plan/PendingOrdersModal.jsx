import { useState } from 'react';
import moment from 'moment';

const PendingOrdersModal = ({ mode, pendingOrders, handleCancel, handleSave }) => {
  const initialManagableOrders = pendingOrders.filter((order) => !order.has_changes)
  const [managableOrders, setManagableOrders] = useState(initialManagableOrders);

  const handleSubmit = (mode) => {
    const orderIDs = mode === 'one-off' ? [] : managableOrders.map((order) => order.id);
    handleSave({
      mode,
      orderIDs,
    })
  };

  return (
    <>
      <div className="overlay show" onClick={handleCancel} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div className="meal-orders-list">
            <div>
              <h3>Pending Meal Plan Order(s)</h3>
            </div>
            <p className='is-invalid-label'>You have {pendingOrders.length} pending {pendingOrders.length > 1 ? 'orders' : 'order'} that might be affected by these changes!</p>
            <p>Do you want to {mode == 'update' ? 'update' : 'cancel'} {pendingOrders.length > 1 ? 'these orders' : ' the order'}?</p>
            <ul className='m-0'>
              {pendingOrders.map((order) => <PendingOrder key={`pending-order-${order.id}`} order={order} managableOrders={managableOrders} setManagableOrders={setManagableOrders} />)}
            </ul>
          </div>
        </div>
        <div className="between-flex mt-1 meal-orders-list-buttons">
          <a className="button tiny hollow uppercase" onClick={() => handleSubmit('one-off')}>
            Do not {mode == 'update' ? 'update' : 'affect'} {pendingOrders.length > 1 ? 'orders' : 'order'}
          </a>
          {!!managableOrders.length && (
            <a className="button tiny uppercase" onClick={() => handleSubmit('subsequent')}>
              {mode == 'update' ? 'Update' : 'Cancel'} {managableOrders.length} {managableOrders.length > 1 ? 'orders' : 'order'}
            </a>
          )}
          <a className="button tiny uppercase gray-btn" onClick={handleCancel}>
            Close
          </a>
        </div>
      </div>
    </>
  )
}

const PendingOrder = ({ order, managableOrders, setManagableOrders }) => {
  const isSelected = managableOrders.find((updatableOrder) => updatableOrder.id == order.id);
  const handleChange = (event) => {
    if (event.target.checked) {
      setManagableOrders((state) => [...state, order])
    } else {
      setManagableOrders((state) => state.filter((stateOrder) => stateOrder.id != order.id) )
    }
  }

  return (
    <li className="meal-event" key={`pending-order-item-${order.id}`} style={{ justifyContent: 'space-between' }}>
      <img src={order.suppliers[0].image} alt="event" className="event-image" />
      <span>#{order.id}</span>
      <span className={`event-type ${order.status}`} />
      <div className='event-time'>
        {moment(order.delivery_date).format('DD-MM-YYYY')}
        {order.has_changes && <span style={{ display: 'block', textAlign: 'center' }} className="is-invalid-label">({order.delivery_time})</span>}
      </div>
      <label className="drop-text admin-action" style={{ marginLeft: '12px' }}>
        <input
          type="checkbox"
          className="checkbox-content"
          name="active"
          checked={isSelected}
          onChange={handleChange}
        />
        <span
          className="checkbox-content-tick"
          style={{ minWidth: '20px', height: '20px' }}
        />
      </label>
    </li>
  )
}

export default PendingOrdersModal;