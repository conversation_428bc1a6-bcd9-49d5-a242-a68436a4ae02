import PropTypes from 'prop-types';

const MealPlanCard = ({ mealPlan, selectedMealPlan, setSelectedMealPlan }) => {
  const isSelected = selectedMealPlan?.id === mealPlan.id;

  const handleClick = async () => {
    if (isSelected) return;

    setSelectedMealPlan(mealPlan);
  };

  return (
    <div onClick={handleClick} className={`meal-plan ${isSelected ? 'selected' : ''}`}>
      <span className="meal-plan-name">{mealPlan.name}</span>
      {!!mealPlan.number_of_people && <span className="meal-plan-staff-count">{mealPlan.number_of_people} Staff</span>}
    </div>
  );
};

MealPlanCard.propTypes = {
  mealPlan: PropTypes.object.isRequired,
  selectedMealPlan: PropTypes.object,
  setSelectedMealPlan: PropTypes.func.isRequired,
};

export default MealPlanCard;
