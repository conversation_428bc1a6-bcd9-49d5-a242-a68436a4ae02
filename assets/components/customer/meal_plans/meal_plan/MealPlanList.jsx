import PropTypes from 'prop-types';
import shallow from 'zustand/shallow';
import mealPlansStore from 'store/mealPlansStore';

import MealPlanCard from './MealPlanCard';

const MealPlanList = ({ setIsNew }) => {
  const { mealPlans, selectedMealPlan, setSelectedMealPlan } = mealPlansStore(
    (state) => ({
      mealPlans: state.mealPlans,
      loadingMealPlans: state.loadingMealPlans,
      selectedMealPlan: state.selectedMealPlan,
      setSelectedMealPlan: state.setSelectedMealPlan,
    }),
    shallow
  );

  return (
    <div className="meal-plans">
      {mealPlans.map((mealPlan) => (
        <MealPlanCard
          key={`meal-plan-${mealPlan.id}`}
          mealPlan={mealPlan}
          selectedMealPlan={selectedMealPlan}
          setSelectedMealPlan={setSelectedMealPlan}
        />
      ))}
      <div className="add-meal-plan" onClick={() => setIsNew(true)}>
        Add A New Meal Plan
      </div>
    </div>
  );
};

MealPlanList.propTypes = {
  setIsNew: PropTypes.func.isRequired,
};

export default MealPlanList;
