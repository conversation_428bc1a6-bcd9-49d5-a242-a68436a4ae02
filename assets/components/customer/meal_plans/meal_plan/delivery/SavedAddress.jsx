const SavedAddress = ({ address = {}, setActiveAddress }) => {
  const { level, street_address, suburb_label } = address;

  return (
    <div
      className="saved-address"
      onClick={() =>
        setActiveAddress({ ...address, label: `${street_address}, ${suburb_label}`, suburbLabel: address.suburb_label })
      }
    >
      <div>
        <p className="bold">
          {level ? `${level}/` : ''}
          {street_address}
        </p>
        <p className="grey">{suburb_label}</p>
      </div>
      <div className="saved-address__edit" />
    </div>
  );
};

export default SavedAddress;
