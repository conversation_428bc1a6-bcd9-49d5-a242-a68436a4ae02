import { useState } from 'react';
import Modal from 'react-responsive-modal';
import AddressList from './AddressList';
import AddressForm from './AddressForm';

const DeliveryAddressModal = ({ setMealPlan, setModalOpen }) => {
  const [activeAddress, setActiveAddress] = useState(null)

  return (
    <Modal
      open={true}
      center
      styles={{
        modal: { padding: 20, borderRadius: '10px', width: '460px', minHeight: '500px' },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
      showCloseIcon={false}
      onClose={() => setModalOpen(false)}
    >
      <div className="meal-plan-address">
        <div style={{ display: 'flex' }}>
          {activeAddress && (
            <i
              className="icon address-back"
              style={{ marginRight: '5px' }}
              onClick={() => {
                setActiveAddress(null);
              }}
            />
          )}
          <h3 style={{ fontFamily: 'Museo Sans', fontWeight: 'bold' }}>Delivery Address</h3>
        </div>
        {!activeAddress && <AddressList setActiveAddress={setActiveAddress} />}
        {activeAddress && (
          <AddressForm
            activeAddress={activeAddress}
            setActiveAddress={setActiveAddress}
            setMealPlan={setMealPlan}
            setModalOpen={setModalOpen}
          />
        )}
      </div>
    </Modal>
  )
}

export default DeliveryAddressModal;