import { useContext } from 'react';

import appContext from 'contexts/appContext';
import axios from 'axios';
import { apiSuburbsPath } from 'routes.js';

function humanizeStr(str) {
  if (!str) return '';

  return str
    .split('-')
    .map((word) => `${word[0].toUpperCase()}${word.slice(1)}`)
    .join()
    .replace(/,/g, ' ');
}

const AddressForm = ({ activeAddress = {}, setActiveAddress, setMealPlan, setModalOpen }) => {
  const { locality: countryCode } = useContext(appContext);

  const setDeliveryAddress = async () => {
    let suburbResponse;

    if (!activeAddress.suburb_id || !activeAddress.suburb_label) {
      // fetch best suburb based on passed in suburb info and check order supplier availability\
      const suburbParams = {
        country_code: countryCode,
        postcode: activeAddress?.postcode,
        name: humanizeStr(activeAddress?.suburb),
        best_matched_to: {
          postcode: activeAddress?.postcode,
          name: humanizeStr(activeAddress?.suburb),
        },
      };

      const { data: fetchedSuburbs } = await axios({
        method: 'GET',
        url: apiSuburbsPath({ format: 'json' }),
        params: suburbParams,
      });
      suburbResponse = fetchedSuburbs[0];
    }

    setMealPlan((state) => ({
      ...state,
      delivery_address_level: activeAddress.level,
      delivery_address: activeAddress.street_address,
      delivery_instruction: activeAddress.instructions,
      delivery_suburb_id: activeAddress.suburb_id || suburbResponse?.id,
      suburb_label: activeAddress.suburb_label || suburbResponse?.label,
    }));
    setModalOpen(false);
  };

  return (
    <div>
      <input
        type="text"
        className="form-input"
        placeholder="Street Address"
        value={activeAddress.label || ''}
        readOnly
      />
      <input
        type="text"
        className="form-input"
        placeholder="Level, Floor or Suite"
        value={activeAddress.level || ''}
        onChange={(e) => setActiveAddress((state) => ({ ...state, level: e.target.value }))}
      />
      <textarea
        className="form-input"
        rows={5}
        placeholder="Delivery Instructions"
        value={activeAddress.instructions || ''}
        onChange={(e) => setActiveAddress((state) => ({ ...state, instructions: e.target.value }))}
      />

      <button
        type="button"
        className="button black-btn"
        onClick={setDeliveryAddress}
        style={{ width: '100%', fontSize: '16px', marginTop: '1rem' }}
      >
        Submit
      </button>
    </div>
  );
};

export default AddressForm;
