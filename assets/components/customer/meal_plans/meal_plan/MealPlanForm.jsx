import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import shallow from 'zustand/shallow';
import mealPlansStore from 'store/mealPlansStore';

// components
import MealPlanDeliveryDetails from './delivery/MealPlanDeliveryDetails';
import MealPlanPaymentDetails from './payment/MealPlanPaymentDetails';
import PendingOrdersModal from './PendingOrdersModal';

const MealPlanForm = ({ mealPlan, setOpenForm }) => {
  const [panel, setPanel] = useState('delivery');
  const [localMealPlan, setLocalMealPlan] = useState(mealPlan);
  const [pendingOrders, setPendingOrders] = useState([]);

  const { createMealPlan, updateMealPlan, checkoutDetails, fetchCheckoutDetails } = mealPlansStore(
    (state) => ({
      createMealPlan: state.createMealPlan,
      updateMealPlan: state.updateMealPlan,
      checkoutDetails: state.checkoutDetails,
      fetchCheckoutDetails: state.fetchCheckoutDetails,
    }),
    shallow
  );

  useEffect(() => {
    setLocalMealPlan(mealPlan);
  }, [mealPlan.id]);

  useEffect(async () => {
    if (!checkoutDetails.name) {
      await fetchCheckoutDetails();
    }
  }, []);

  const handleSave = async ({ mode, orderIDs }) => {
    try {
      if (localMealPlan.id) {
        await updateMealPlan({
          mealPlan: localMealPlan,
          mode,
          orderIDs,
        });
      } else {
        await createMealPlan({
          mealPlan: localMealPlan,
        });
      }
      setOpenForm(false);
    } catch (error) {
      if (error?.response?.data?.pending_orders?.length) {
        const { pending_orders } = error.response.data;
        setPendingOrders(pending_orders);
      }
    }
  };

  const handleCancel = () => {
    setPendingOrders([]);
  }

  const isNewPlan = !localMealPlan.id;

  return (
    <>
      {(panel === 'delivery' || !isNewPlan) && (
        <MealPlanDeliveryDetails
          mealPlan={localMealPlan}
          setMealPlan={setLocalMealPlan}
          isNewPlan={isNewPlan}
          setPanel={setPanel}
          setOpenForm={setOpenForm}
        />
      )}
      {(panel === 'payment' || !isNewPlan) && (
        <MealPlanPaymentDetails
          mealPlan={localMealPlan}
          setMealPlan={setLocalMealPlan}
          isNewPlan={isNewPlan}
          setPanel={setPanel}
          handleSave={handleSave}
          setOpenForm={setOpenForm}
        />
      )}
      {!isNewPlan && !!pendingOrders.length && (
        <PendingOrdersModal
          mode="update"
          pendingOrders={pendingOrders}
          handleSave={handleSave}
          handleCancel={handleCancel}
        />
      )}
    </>
  );
};

MealPlanForm.propTypes = {
  mealPlan: PropTypes.object.isRequired,
  setOpenForm: PropTypes.func.isRequired,
};
export default MealPlanForm;
