import { useState, useEffect } from 'react';
import shallow from 'zustand/shallow';
import mealPlansStore from 'store/mealPlansStore';
import { toast } from 'react-toastify';
import { defaultToastOptions, toastTypeOptions } from 'utilities/toastHelpers';

import axios from 'axios';
import { apiStripeCreditCardsPath } from 'routes';

import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import VisaIcon from 'images/icons/visa.svg';
import MasterCardIcon from 'images/icons/mastercard.svg';
import AmexIcon from 'images/icons/amex.svg';

const initialCard = {
  id: null,
  name: '',
  stripe_token: null,
  saved_for_future: true,
};

const iconsMap = {
  visa: VisaIcon,
  mastercard: MasterCardIcon,
  amex: AmexIcon,
};

const StripeForm = ({ setMealPlan }) => {
  const [cardError, setCardError] = useState(null);
  const [creditCard, setCreditCard] = useState(initialCard);
  const [savedCard, setSavedCard] = useState({});

  const [inProgress, setInProgress] = useState(false);
  const [processingState, setProcessingState] = useState(null);

  const stripe = useStripe();
  const elements = useElements();

  const { checkoutDetails, addSavedCard } = mealPlansStore(
    (state) => ({
      checkoutDetails: state.checkoutDetails,
      addSavedCard: state.addSavedCard,
    }),
    shallow
  );

  const {
    name: customerName,
    email: customerEmail,
  } = checkoutDetails;

  const submitStripeCard = async (e) => {
    setCardError(null);

    if (processingState) return;

    if (!stripe || !elements) {
      setCardError('We cannot connect to the card agency! Please try again later.')
      setInProgress(false);
      return;
    }

    if (!creditCard.name) {
      setCardError('Cardholder Name is required');
      setInProgress(false);
      toast.error('New Card Errors detected. Please Check Payment Options.', {
        ...defaultToastOptions,
        ...toastTypeOptions.info,
      });
      return;
    }

    const cardElement = elements.getElement(CardElement);
    setProcessingState('stripe-card-validation');
    try {
      const { paymentMethod: stripePaymentMethod, error: stripeError } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          name: customerName,
          email: customerEmail,
        },
      });
      if (stripeError) {
        setCardError(stripeError.message);
        setProcessingState(null);
        setInProgress(false);
        toast.error('Card Errors detected', { ...defaultToastOptions, ...toastTypeOptions.info });
      } else {
        updateCardFromStripe(stripePaymentMethod);
      }
    } catch (err) {
      setProcessingState(null);
      setCardError('We were unable to connect to our secure card provider. Please try again.');
      setInProgress(false);
      toast.error('New Card Errors detected. Please Check Payment Options.', {
        ...defaultToastOptions,
        ...toastTypeOptions.info,
      });
    }
  };

  const handleUpdate = (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setCreditCard((state) => ({ ...state, [event.target.name]: value }));
  };

  // sets creditCard with details from Stripe (including stripe_token)
  const updateCardFromStripe = (stripePaymentMethod) => {
    const { card: stripeCard } = stripePaymentMethod;
    setCreditCard((state) => ({
      ...state,
      last4: stripeCard.last4,
      brand: stripeCard.brand,
      country_code: stripeCard.country,
      expiry_month: stripeCard.exp_month,
      expiry_year: stripeCard.exp_year,
      stripe_token: stripePaymentMethod.id,
    }));
  };

  useEffect(() => {
    if (!creditCard.stripe_token) return;

    setProcessingState('saving-card');
    saveCardInYordar(creditCard);
  }, [creditCard.stripe_token]);

  const saveCardInYordar = async (card) => {
    const { id, ...sanitizedCard } = card;
    try {
      const { data: yordarCard } = await axios({
        method: 'post',
        url: apiStripeCreditCardsPath({ format: 'json' }),
        data: {
          credit_card: sanitizedCard,
        },
      });
      setProcessingState(null);
      setSavedCard(yordarCard);
      addSavedCard(yordarCard);
      setMealPlan((state) => ({...state, credit_card_id: yordarCard.id }))
    } catch (err) {
      setProcessingState(null);
      setInProgress(false);
      const responseError = err?.request?.response;
      if (responseError) {
        const { errors } = JSON.parse(responseError);
        setCardError(errors.join('. '));
      } else {
        setCardError('We were unable to save Card! Please try again.');
      }
      toast.error('Card Errors detected', { ...defaultToastOptions, ...infoToastOptions });
    }
  };

  const stripeCardOptions = {
    hidePostalCode: true,
    style: {
      base: {
        fontSize: '14px',
        fontFamily: 'sans-serif',
        fontSmoothing: 'antialiased',
        color: '#000',
        '::placeholder': {
          color: '#888',
        },
        borderBottom: '1px solid #000',
      },
      invalid: {
        color: '#9e2146',
      },
    },
  };

  if (savedCard.id) {
    return (
      <>
        <h3>Saved Card</h3>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img src={iconsMap[savedCard.brand]} height={30} width={30} />
          <span style={{ marginLeft: '0.5rem' }}>
            {savedCard.name} ending in {savedCard.last4} (expires {savedCard.expiry_month}/{savedCard.expiry_year})
            {savedCard.saved_for_future && <small> - Saved for Future Use</small>}
          </span>
        </div>
        <div className="notice">
          {savedCard.brand_label} credit card payments will incur a {savedCard.surcharge_percent}%{' '}
          {!!savedCard.surcharge_fee && `+ ${savedCard.surcharge_fee * 100}c surcharge`}.
        </div>
      </>
    );
  }

  return (
    <>
      <input
        id="cardholder-name"
        className={`form-input cardholder-name${cardError === 'Cardholder Name is required' ? ' is-invalid-input' : ''}`}
        type="text"
        name="name"
        value={creditCard.name}
        onChange={handleUpdate}
        required
        placeholder="Cardholder name"
      />
      <div className={`card-element${cardError ? ' is-invalid-input' : ''}`}>
        <CardElement options={stripeCardOptions} />
      </div>
      {cardError && (
        <p className="is-invalid-label">
          <strong>Error:</strong> {cardError}
        </p>
      )}
      {!!processingState && (
        <div className='mt-1' style={{ display: 'flex' }}>
          <button type="button" className="button hollow tiny">
            {processingState === 'stripe-card-validation' && <span>Validating Card in Stripe...</span>}
            {processingState === 'saving-card' && <span>Saving card against customer...</span>}
          </button>
        </div>
      )}
      {!processingState && (
        <div className='mt-1' style={{ display: 'flex' }}>
          <button type="button" className="button hollow tiny" onClick={(e) => submitStripeCard(e)}>
            Save Card
          </button>
        </div>
      )}
    </>
  );
};

export default StripeForm;
