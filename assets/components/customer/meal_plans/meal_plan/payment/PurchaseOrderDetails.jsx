import { useState, useEffect, useMemo } from 'react';
import shallow from 'zustand/shallow';
import mealPlansStore from 'store/mealPlansStore';
import Creatable from 'react-select/creatable';

const COST_CENTER_ID_FORMATS = {
  'free-text': {
    type: 'text',
    label: '',
    limit: '',
  },
  digits: {
    type: 'number',
    label: 'Numerical',
    limit: '',
  },
  '4-digits': {
    type: 'number',
    label: '4 digit',
    limit: 4,
  },
};

const customStyles = {
  control: (base) => ({
    ...base,
    height: '37px',
    minHeight: '37px',
  }),
  valueContainer: (base) => ({
    ...base,
    height: '37px',
    padding: '0 8px',
  }),
  input: (base) => ({
    ...base,
    margin: '0px',
    padding: '0px',
  }),
  indicatorsContainer: (base) => ({
    ...base,
    height: '37px',
  }),
  placeholder: (base) => ({
    ...base,
    color: '#aaaaaa',
  }),
};

const PurchaseOrderDetails = ({ mealPlan, setMealPlan }) => {
  const { checkoutDetails } = mealPlansStore(
    (state) => ({
      checkoutDetails: state.checkoutDetails,
    }),
    shallow
  );

  const {
    purchase_orders: purchaseOrders,
    requires_purchase_order: requiresPO,
    required_department_identity_format: requiredCostCentreIDFormat,
    has_gst_split_invoicing: hasGstSplitInvoicing,
  } = checkoutDetails;

  const poOptions = useMemo(
    () =>
      purchaseOrders?.map((PO) => ({
        value: PO.id,
        label: PO.po_number,
        description: PO.description,
        isInactive: PO.inactive,
      })) || [],
    [purchaseOrders]
  );

  const formatPOOptionLabel = ({ label, isInactive, description }, { context }) => (
    <>
      <div style={{ display: 'flex' }}>
        <div>{label}</div>
        {isInactive && <div style={{ marginLeft: '10px', color: '#ccc' }}>(inactive)</div>}
      </div>
      {context === 'menu' && description && <small style={{ fontSize: '0.8rem' }}>({description})</small>}
    </>
  );

  const [selectedPO, setSelectedPO] = useState(null);
  const [selectedNonGstPO, setSelectedNonGstPO] = useState(null);
  const [allowSplitPO, setAllowSplitPO] = useState(mealPlan.gst_free_cpo_id ? true : null);

  useEffect(() => {
    if (poOptions?.length) {
      if (mealPlan.cpo_id) {
        setSelectedPO(poOptions.find((option) => option.value === mealPlan.cpo_id));
      }
      if (mealPlan.gst_free_cpo_id) {
        setSelectedNonGstPO(poOptions.find((option) => option.value === mealPlan.gst_free_cpo_id));
      }
    }
  }, [poOptions]);

  const handlePOChange = (PO, gstFree = false) => {
    if (gstFree) {
      setSelectedNonGstPO(PO);
      setMealPlan((state) => ({ ...state, gst_free_cpo_id: PO?.value ? PO.value : null }));
    } else {
      setSelectedPO(PO);
      setMealPlan((state) => ({ ...state, cpo_id: PO?.value ? PO.value : null }));
    }
  };

  const costCenterIDFormat = COST_CENTER_ID_FORMATS[requiredCostCentreIDFormat] || {
    type: 'text',
    label: '',
    limit: '',
  };

  const handleDepartmentIDChange = (event) => {
    const { value } = event.target;
    if (!!costCenterIDFormat.limit && value.split('').length > costCenterIDFormat.limit) return;

    setMealPlan((state) => ({ ...state, department_identity: event.target.value }));
  };

  const optionalBillingDetails = !requiresPO && !requiredCostCentreIDFormat ;

  return (
    <div className="form-section meal-plan-billing">
      <h3 className="section-heading">Billing Details{optionalBillingDetails ? ' (optional)' : ''}</h3>
      <div className="invoice-details">
        {hasGstSplitInvoicing && allowSplitPO === null && (
          <div className="checkout-detail" style={{ gridColumn: '1/3' }}>
            <p>
              Would you like to separately invoice GST and GST-Free items within your orders?{' '}
              <small>(requires split purchase orders)</small>
            </p>
            <a className="button primary checkout-button" onClick={() => setAllowSplitPO(true)}>
              YES
            </a>
            <a
              className="button secondary checkout-button"
              style={{ marginLeft: '0.5rem' }}
              onClick={() => setAllowSplitPO(false)}
            >
              NO
            </a>
          </div>
        )}
        {hasGstSplitInvoicing && allowSplitPO && (
          <>
            <div className="checkout-detail">
              <p className="title">PO Number for GST items</p>
              <Creatable
                styles={customStyles}
                placeholder="Select or type a new PO number"
                isClearable
                options={poOptions}
                onChange={(PO) => handlePOChange(PO)}
                className="po-select"
                noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
                formatOptionLabel={formatPOOptionLabel}
                value={selectedPO}
              />
              {requiresPO && (
                <p>
                  <strong>Please note: </strong>
                  Your company requires a purchase order number for the GST items for its orders.
                </p>
              )}
            </div>
            <div className="checkout-detail">
              <p className="title">PO Number for GST free items</p>
              <Creatable
                styles={customStyles}
                placeholder="Select or type a new PO number"
                isClearable
                options={poOptions}
                onChange={(PO) => handlePOChange(PO, true)}
                className="po-select"
                noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
                formatOptionLabel={formatPOOptionLabel}
                value={selectedNonGstPO}
              />
              {requiresPO && (
                <p>
                  <strong>Please note: </strong>
                  Your company requires a purchase order number for the GST-free items in its orders.
                </p>
              )}
            </div>
          </>
        )}
        {!(hasGstSplitInvoicing && allowSplitPO) && (
          <div className="checkout-detail">
            <p className="title">PO Number</p>
            <Creatable
              placeholder="Select or type a new PO number"
              isClearable
              options={poOptions}
              onChange={(PO) => handlePOChange(PO)}
              className="po-select po-meal-plan"
              noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
              formatOptionLabel={formatPOOptionLabel}
              value={selectedPO}
              styles={customStyles}
            />
            {requiresPO && (
              <p style={{ marginTop: '4px' }}>
                <strong>Please note: </strong>
                Your company requires a purchase order number for an order. If you don't have a PO number please enter
                your full name.
              </p>
            )}
          </div>
        )}
        <div className="checkout-detail">
          <p className="title">Cost Centre ID</p>
          <input
            type={costCenterIDFormat.type}
            value={mealPlan.department_identity}
            onChange={handleDepartmentIDChange}
            className={`form-input ${!mealPlan.department_identity ? ' empty' : ''}`}
            placeholder="Set A Cost Centre ID"
          />
          {!!requiredCostCentreIDFormat && (
            <p>
              <strong>Please note: </strong>
              Your company requires a {costCenterIDFormat.label} Cost Centre ID for an order.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PurchaseOrderDetails;
