const PayOnAccount = ({ billingPreference }) => {
  if (billingPreference === 'instantly') {
    return (
      <p>
        You've currently set your account to be <span className="bold">billed per order</span>. The orders will be{' '}
        <span className="bold">invoiced individually on delivery</span>.
      </p>
    );
  }

  return (
    <p>
      The orders will be billed as part of your <span className="bold">{billingPreference} invoice</span>.
    </p>
  );
};

export default PayOnAccount;
