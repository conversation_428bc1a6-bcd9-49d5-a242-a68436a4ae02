import { useContext } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';

import StripeForm from './StripeForm';
import appContext from 'contexts/appContext'

const elementsOptions = {
  appearance: { theme: 'stripe' },
};

const PayByNewCard = ({ customer, setMealPlan }) => {
  const { stripeKey } = useContext(appContext);
  const stripePromise = loadStripe(stripeKey);

  return (
    <Elements options={elementsOptions} stripe={stripePromise}>
      <StripeForm customer={customer} setMealPlan={setMealPlan} />
    </Elements>
  );
}

export default PayByNewCard;
