import Select from 'react-select';

import VisaIcon from 'images/icons/visa.svg';
import MasterCardIcon from 'images/icons/mastercard.svg';
import AmexIcon from 'images/icons/amex.svg';

const PayBySavedCard = ({ savedCreditCards, mealPlan, setMealPlan }) => {
  const iconsMap = {
    visa: VisaIcon,
    mastercard: MasterCardIcon,
    amex: AmexIcon,
  };
  const { credit_card_id: creditCardId } = mealPlan;

  const setMealPlanCard = (selectedOption) => {
    setMealPlan((state) => ({ ...state, credit_card_id: selectedOption.value }));
  };

  const cardOptions = savedCreditCards.map((card) => ({
    value: card.id,
    label: `${card.name} ending in ${card.last4} (expires ${card.expiry_month}/${card.expiry_year})`,
    brand: card.brand,
    icon: iconsMap[card.brand],
  }));

  const selectedCard = savedCreditCards?.find((savedCard) => savedCard.id === creditCardId);

  return (
    <div className="credit-card-container">
      <div className="saved-cards-select">
        {!!creditCardId && (
          <p>
            This order will be <strong>individually invoiced</strong>.
          </p>
        )}

        <Select
          name="credit_card_id"
          options={cardOptions}
          value={cardOptions.find((option) => option.value === creditCardId)}
          onChange={setMealPlanCard}
          isSearchable={false}
          classNamePrefix="react-select"
          getOptionLabel={(e) => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <img src={e.icon} height={30} width={30} />
              <span style={{ marginLeft: 5 }}>{e.label}</span>
            </div>
          )}
          placeholder="Select Saved Card"
        />
        {!!selectedCard && (
          <div className={mealPlan ? ' notice-card' : ''}>
            {selectedCard.brand_label} credit card payments will incur a {selectedCard.surcharge_percent}%{' '}
            {!!selectedCard.surcharge_fee && `+ ${selectedCard.surcharge_fee * 100}c surcharge`}.
          </div>
        )}
      </div>
    </div>
  );
};

export default PayBySavedCard;
