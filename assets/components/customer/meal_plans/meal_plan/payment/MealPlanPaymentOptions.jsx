import { useState, useEffect } from 'react';
import shallow from 'zustand/shallow';
import mealPlansStore from 'store/mealPlansStore';
import { toast } from 'react-toastify';
import { defaultToastOptions, toastTypeOptions } from 'utilities/toastHelpers';

import CardIcon from 'images/icons/card-filled.svg';
import AccountIcon from 'images/icons/account.svg';

// components
import PayByNewCard from './PayByNewCard';
import PayBySavedCard from './PayBySavedCard';
import PayOnAccount from './PayOnAccount';

const MealPlanPaymentOptions = ({ mealPlan, setMealPlan }) => {
  const { checkoutDetails } = mealPlansStore(
    (state) => ({
      checkoutDetails: state.checkoutDetails,
    }),
    shallow
  );

  const {
    billing_preference: billingPreference,
    saved_credit_cards: savedCreditCards,
    can_pay_on_account: canPayOnAccount,
    can_pay_by_credit_card: canPayByCard,
    name: customerName,
    email: customerEmail,
  } = checkoutDetails;

  const customer = {
    name: customerName,
    email: customerEmail,
  };

  let paymentMethods = [
    {
      key: 'pay-on-account',
      label: 'Pay on Account',
      disabled: !canPayOnAccount,
      icon: AccountIcon,
      weight: 1,
      credit_card_id: 1,
    },
    {
      key: 'pay-by-saved-card',
      label: 'Use A Saved Card',
      disabled: !canPayByCard || !savedCreditCards?.length,
      icon: CardIcon,
      hidden: !savedCreditCards?.length,
      weight: 2,
      credit_card_id: null,
    },
    {
      key: 'pay-by-new-card',
      label: 'Use A New Card',
      disabled: !canPayByCard,
      icon: CardIcon,
      weight: 3,
      credit_card_id: null,
    },
  ];

  if (!canPayOnAccount) {
    // re-set paymentMethod weights
    paymentMethods = paymentMethods.map((paymentMethod) => {
      let methodWeight = paymentMethod.weight;
      if (savedCreditCards?.length) {
        methodWeight = paymentMethod.key == 'pay-by-saved-card' ? 1 : 2;
      } else {
        methodWeight = paymentMethod.key == 'pay-by-new-card' ? 1 : 2;
      }
      methodWeight = paymentMethod.key == 'pay-on-account' ? 3 : methodWeight;
      return { ...paymentMethod, weight: methodWeight };
    });
    // re-sort paymentMethod based on new weights
    paymentMethods = paymentMethods.sort((a, b) => a.weight - b.weight);
  }

  const [activePaymentMethod, setActivePaymentMethod] = useState(paymentMethods[0].key);

  const handlePaymentMethodChange = (selectedPaymentMethod) => {
    if (activePaymentMethod === selectedPaymentMethod.key) return;

    if (selectedPaymentMethod.disabled) {
      toast.error('You do not have access to this payment option!', {
        ...defaultToastOptions,
        ...toastTypeOptions.info,
      });
      return;
    }
    setMealPlan((state) => ({ ...state, credit_card_id: selectedPaymentMethod.credit_card_id }));
    setActivePaymentMethod(selectedPaymentMethod.key);
  };

  useEffect(() => {
    if (mealPlan.uuid && mealPlan.credit_card_id) {
      let preSelectedMethod = paymentMethods[0].key;

      if (savedCreditCards?.length && mealPlan.credit_card_id && mealPlan.credit_card_id !== 1) {
        preSelectedMethod = 'pay-by-saved-card';
      } else if (mealPlan.credit_card_id == 1) {
        preSelectedMethod = 'pay-on-account';
      }
      setActivePaymentMethod(preSelectedMethod);
    } else {
      setMealPlan((state) => ({ ...state, credit_card_id: paymentMethods[0].credit_card_id }));
    }
  }, [mealPlan?.uuid, checkoutDetails?.name]);

  return (
    <div className="form-section meal-plan-payment">
      <h3 className="section-heading">Payment Options</h3>
      <div className="payment-methods">
        {paymentMethods
          .filter((method) => !method.hidden)
          .map((paymentMethod) => (
            <div
              onClick={() => handlePaymentMethodChange(paymentMethod)}
              className={`credit-card${paymentMethod.key === activePaymentMethod ? ' active' : ''} payment-input`}
            >
              <img src={paymentMethod.icon} width={24} height={24} />
              <p>{paymentMethod.label}</p>
            </div>
          ))}
      </div>

      {activePaymentMethod === 'pay-by-new-card' && <PayByNewCard customer={customer} setMealPlan={setMealPlan} />}
      {activePaymentMethod === 'pay-by-saved-card' && (
        <PayBySavedCard savedCreditCards={savedCreditCards} mealPlan={mealPlan} setMealPlan={setMealPlan} />
      )}
      {activePaymentMethod === 'pay-on-account' && canPayOnAccount && (
        <PayOnAccount billingPreference={billingPreference} />
      )}
    </div>
  );
};

export default MealPlanPaymentOptions;
