import { useState } from 'react';
import PropTypes from 'prop-types';
import shallow from 'zustand/shallow';
import mealPlansStore from 'store/mealPlansStore';

// components
import PendingOrdersModal from './PendingOrdersModal';

const MealPlanCancelModal = ({ mealPlan, setIsRemove }) => {
  const [inputPrompt, setInputPrompt] = useState('');
  const [pendingOrders, setPendingOrders] = useState([]);

  const { removeMealPlan } = mealPlansStore(
    (state) => ({
      removeMealPlan: state.removeMealPlan,
    }),
    shallow
  );

  const deletePrompt = `remove ${mealPlan.name}`. toLowerCase();
  const canRemove = inputPrompt === deletePrompt;

  const handleRemove = async ({ mode, orderIDs }) => {
    try {
      await removeMealPlan({
        mealPlan: mealPlan,
        mode,
        orderIDs,
      });
      setIsRemove(false);
    } catch (error) {
      if (error?.response?.data?.pending_orders?.length) {
        const { pending_orders } = error.response.data;
        setPendingOrders(pending_orders);
      }
    }
  };

  const handleCancel = () => {
    setPendingOrders([]);
    setIsRemove(false);
  }

  return (
    <>
      <div className="overlay show" onClick={() => setIsRemove(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div>
            <div>
              <h3>Remove Meal Plan</h3>
            </div>
            <p>
              Please type
              <br />
              <strong className='is-invalid-label'>`{deletePrompt}`</strong>
              <br />
              below and click the `Remove` button to remove the meal plan from your list.
            </p>
            <input className='form-input is-invalid-input' type='text' value={inputPrompt} onChange={(e) => setInputPrompt(e.target.value)} />

            <a className="button small mr-1 uppercase remove-meal-button" disabled={!canRemove} onClick={() => canRemove && handleRemove({})}>
              Remove
            </a>
            <a className="button small uppercase gray-btn" onClick={() => setIsRemove(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
      {!!pendingOrders.length && <PendingOrdersModal mode='cancel' pendingOrders={pendingOrders} handleSave={handleRemove} handleCancel={handleCancel} />}
    </>
  )
}

MealPlanCancelModal.propTypes = {
  mealPlan: PropTypes.object.isRequired,
  setIsRemove: PropTypes.func.isRequired,
}

export default MealPlanCancelModal;