import { useContext, useEffect, useRef, useState } from 'react';

import adminContext from 'contexts/adminContext';
import OrderLink from 'components/customer/orders/OrderLink';

const MealPlanOrder = ({ order, index, setOpenModal, setModalInfo }) => {
  const [showOrderLinks, setShowOrderLinks] = useState(false);

  const { isAdmin } = useContext(adminContext);

  const wrapperRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setShowOrderLinks(false);
      }
    }

    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [wrapperRef]);

  const suppliersString = order.suppliers.map((supplier) => supplier.name).join(', ');

  return (
    <div key={index} className="meal-event link">
      <a
        href={order.links.find((link) => link.type === 'view').url}
        className="calendar-view-slider"
        data-modal-view="true"
        title={suppliersString}
      />
      <img src={order.suppliers[0].image} title="hello" alt="event" className="event-image" />
      <span className={`event-type ${order.status}`} />
      <div className="event-time">{order.delivery_time}</div>
      <div className="order-list-item__options" style={{ marginLeft: 'auto' }} ref={wrapperRef}>
        <button
          type="button"
          className="event-info ellipsis"
          onClick={(e) => {
            e.stopPropagation();
            setShowOrderLinks((state) => !state);
          }}
        />
        {showOrderLinks && (
          <div className="order-list-item__options-dropdown">
            {order.links.map((link, lidx) => (
              <OrderLink
                key={`order-${order.id}-link-${lidx}`}
                order={order}
                day={order.delivery_day}
                isAdmin={isAdmin}
                isRecurringOrder={order.order_type === 'recurrent'}
                link={link}
                setOpenModal={setOpenModal}
                setModalInfo={setModalInfo}
                setShowOrderLinks={setShowOrderLinks}
                forCalendar
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MealPlanOrder;
