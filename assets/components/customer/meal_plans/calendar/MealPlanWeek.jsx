import PropTypes from 'prop-types';
import moment from 'moment';
import mealPlanStore from 'store/mealPlansStore';
import MealPlanDay from './MealPlanDay';

const MealPlanWeek = ({ weekStartDate, filteredOrders, setOpenModal, setModalInfo, forOrders }) => {
  const weekEndDate = moment(weekStartDate).add(4, 'days');
  const { orders, holidays, selectedMealPlan } = mealPlanStore((state) => ({
    orders: state.orders,
    selectedMealPlan: state?.selectedMealPlan,
    holidays: state.holidays,
  }));

  const weekDays = Array(5)
    .fill(0)
    .map((_, index) => moment(weekStartDate).add(index, 'days').format('YYYY-MM-DD'));

  const week = moment(weekStartDate).week();
  const isStriped = week % 2 === 0;
  const weekHolidays = holidays.filter((holiday) => holiday.on_date_week === week);
  const weekOrders = filteredOrders
    ? filteredOrders.filter((order) => order.delivery_week === week)
    : orders.filter((order) => order.delivery_week === week);
  const weekOrdersCount = weekOrders?.length;
  let weekOrdersText;
  if (weekOrdersCount === 0) {
    weekOrdersText = 'No Orders';
  } else if (weekOrdersCount === 1) {
    weekOrdersText = '1 Order';
  } else if (weekOrdersCount > 1) {
    weekOrdersText = `${weekOrdersCount} Orders`;
  } else {
    weekOrdersText = '';
  }

  return (
    <div className={`meal-plan-week ${isStriped ? 'striped' : ''}`}>
      <div className="meal-plan-week-dates">
        {`${weekStartDate.format('MMM D')} - ${weekEndDate.format('MMM D')}`}{' '}
        <p className="meal-plan-orders-count">{weekOrdersText}</p>
      </div>

      {weekDays.map((day, index) => (
        <MealPlanDay
          key={`meal-plan-week-${week}-day-${index}`}
          day={day}
          index={index}
          selectedMealPlan={selectedMealPlan}
          holidays={weekHolidays}
          orders={weekOrders}
          setOpenModal={setOpenModal}
          setModalInfo={setModalInfo}
          forOrders={forOrders}
        />
      ))}
    </div>
  );
};

MealPlanWeek.proptypes = {
  weekStartDate: PropTypes.string.isRequired,
};

export default MealPlanWeek;
