import Modal from 'react-responsive-modal';
import axios from 'axios';
import mealPlansStore from 'store/mealPlansStore';

const ClearCartModal = () => {
  const { clearCartModalOpen, setClearCartModalOpen, savedOrderUrl } = mealPlansStore((state) => ({
    clearCartModalOpen: state.clearCartModalOpen,
    setClearCartModalOpen: state.setClearCartModalOpen,
    savedOrderUrl: state.savedOrderUrl,
  }));
  const clearCart = async () => {
    try {
      await axios('http://localhost:3000/cart/clear.json', {
        method: 'get',
        withCredentials: true,
      });
      window.location = savedOrderUrl;
    } catch (err) {
      throw new Error(`Error clearing cart ${err}`);
    }
  };
  return (
    <Modal
      open={clearCartModalOpen}
      center
      styles={{
        modal: { maxWidth: '500px', padding: 0, borderRadius: '10px' },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
      showCloseIcon={false}
    >
      <div className="menu-modal">
        <h3 className="menu-modal__heading menu-modal__heading-woolworths">Cart Error</h3>
        <p>You currently have an order in your cart which is not a meal plan</p>
        <p>Do you wish to clear the cart so you can proceed?</p>
        <button className="modal-button" onClick={() => setClearCartModalOpen(false)}>
          Back
        </button>
        <a className="modal-button primary" onClick={() => clearCart()}>
          Clear My Cart
        </a>
      </div>
    </Modal>
  );
};

export default ClearCartModal;
