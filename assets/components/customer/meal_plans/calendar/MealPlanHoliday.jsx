import { useState } from 'react';
import PropTypes from 'prop-types';
import HolidayModal from './HolidayModal';

const MealPlanHoliday = ({ holiday, index, date, selectedMealPlan, forOrders }) => {
  const [openHolidayModal, setOpenHolidayModal] = useState(false);
  const holidayColors = ['#30f9fc', '#756CBC', '#FBB5EE'];
  const fallbackHolidayColor = holidayColors[index % 3];
  const backgroundColor = holiday.color || fallbackHolidayColor;

  return (
    <>
      <div
        key={index}
        className="meal-event meal-event--holiday"
        style={{ backgroundColor }}
        onClick={() => setOpenHolidayModal(true)}
      >
        <div>
          {holiday.name}
          {!!holiday.states.length && ` (${holiday.states.join(', ')})`}
        </div>
      </div>
      {openHolidayModal && (
        <HolidayModal
          holiday={holiday}
          index={index}
          date={date}
          selectedMealPlan={selectedMealPlan}
          setOpenHolidayModal={setOpenHolidayModal}
          forOrders={forOrders}
        />
      )}
    </>
  );
};

MealPlanHoliday.propTypes = {
  holiday: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  date: PropTypes.string.isRequired,
  selectedMealPlan: PropTypes.object,
};

export default MealPlanHoliday;
