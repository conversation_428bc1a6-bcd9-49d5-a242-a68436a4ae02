import { useContext } from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-responsive-modal';
import moment from 'moment';

import appContext from 'contexts/appContext';

const HolidayModal = ({ holiday, index, date, selectedMealPlan, setOpenHolidayModal, forOrders }) => {
  const holidayColors = ['#FCC030', '#756CBC', '#FBB5EE'];
  const fallbackHolidayColor = holidayColors[index % 3];
  const {
    externalOrderUrls: { catering: cateringUrl, quote: quoteUrl },
  } = useContext(appContext);

  let newOrderUrl = '';
  let newQuoteUrl = '';

  const canOrder = moment().isBefore(date, 'day') && !holiday.is_public_holiday;

  if (canOrder) {
    newOrderUrl = `${cateringUrl
      .replace('_suburb', selectedMealPlan?.suburb)
      .replace('_state', selectedMealPlan?.state)}?mealUUID=${selectedMealPlan?.uuid}&for_date=${date}`;
    newQuoteUrl = `${quoteUrl}?date=${date}`;
  }

  return (
    <Modal open onClose={() => setOpenHolidayModal(false)} center classNames={{ modal: 'reveal holiday-modal' }}>
      <div className="holiday-container">
        <div className="holiday-image" style={{ backgroundColor: holiday.color || fallbackHolidayColor }}>
          <p>{holiday.name}</p>
        </div>
        <div className="holiday-description">
          <p className="holiday-date">
            {moment(holiday.on_date).format('MMM Do')}
            {holiday?.states?.length ? <span className="holiday-states">{` (${holiday.states.join(', ')})`}</span> : ''}
          </p>
          {!!holiday.description && <p>{holiday.description}</p>}
          {holiday.is_public_holiday && (
            <>
              <p>*Is a Public Holiday</p>
              <small>*no orders can be placed for public holidays</small>
            </>
          )}
          {newOrderUrl && newQuoteUrl && (
            <div className="holiday-buttons">
              <a
                href={forOrders ? cateringUrl : newOrderUrl}
                className="button"
                onClick={() => setOpenHolidayModal(false)}
              >
                Create An Order
              </a>
              <a href={newQuoteUrl} className="button">
                Get A Quote
              </a>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

HolidayModal.proptypes = {
  holiday: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  date: PropTypes.string.isRequired,
  selectedMealPlan: PropTypes.object,
  setOpenHolidayModal: PropTypes.func.isRequired,
};

export default HolidayModal;
