import { useState } from 'react';
import moment from 'moment';
import mealPlansStore from 'store/mealPlansStore';
import MealPlanWeek from './MealPlanWeek';
import OrderListModal from 'components/customer/orders/OrderListModal';

const MealPlanCalendar = ({ forOrders = false, filteredOrders }) => {
  const [openModal, setOpenModal] = useState(false);
  const [modalInfo, setModalInfo] = useState('');

  const { fetchEvents, mealPlanID, startDate, setStartDate } = mealPlansStore((state) => ({
    fetchEvents: state.fetchEvents,
    mealPlanID: state?.selectedMealPlan?.id,
    startDate: state.startDate,
    setStartDate: state.setStartDate,
  }));

  const handleDateChange = async (direction) => {
    const newStartDate =
      direction === 'back' ? moment(startDate).subtract(7, 'days') : moment(startDate).add(7, 'days');

    setStartDate(newStartDate);

    const fetchFromDate = direction === 'back' ? newStartDate : moment(startDate).add(28, 'days');
    if (mealPlanID || forOrders) {
      await fetchEvents({
        from_date: fetchFromDate,
        mealPlanID,
        forOrders,
      });
    }
  };

  return (
    <>
      <div className="meal-plan-calendar">
        <div className="meal-plan-days">
          <div className="change-container">
            <button className="meal-plan-change-week back" onClick={() => handleDateChange('back')} />
            <button className="meal-plan-change-week forward" onClick={() => handleDateChange('forward')} />
          </div>
          <span>MON</span>
          <span>TUE</span>
          <span>WED</span>
          <span>THU</span>
          <span>FRI</span>
        </div>
        {Array(4)
          .fill(0)
          .map((_, index) => (
            <MealPlanWeek
              key={index}
              weekStartDate={moment(startDate).add(index * 7, 'days')}
              filteredOrders={filteredOrders}
              setOpenModal={setOpenModal}
              setModalInfo={setModalInfo}
              forOrders={forOrders}
            />
          ))}
      </div>
      <OrderListModal openModal={openModal} setOpenModal={setOpenModal} modalInfo={modalInfo} forCalendar />
    </>
  );
};

export default MealPlanCalendar;
