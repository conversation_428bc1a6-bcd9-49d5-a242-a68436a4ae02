import { useEffect } from 'react';
import shallow from 'zustand/shallow';
import useSurveyStore from 'store/employeeSurveyStore';

import Survey from './Survey';

const SurveyApp = () => {
  const { fetchSurveys, setActiveSurvey, activeSurvey } = useSurveyStore(
    (state) => ({
      fetchSurveys: state.fetchSurveys,
      setActiveSurvey: state.setActiveSurvey,
      activeSurvey: state.activeSurvey,
    }),
    shallow
  );

  useEffect(async () => {
    await fetchSurveys();
  }, []);

  return (
    <>
      <div className="switch-survey-buttons">
        <button
          className={`button${activeSurvey === 'catering' ? ' black-btn' : ' white-btn'}`}
          onClick={() => setActiveSurvey('catering')}
          style={{ marginRight: '12px' }}
        >
          Catering Survey
        </button>
        <button
          className={`button${activeSurvey === 'pantry' ? ' black-btn' : ' white-btn'}`}
          onClick={() => setActiveSurvey('pantry')}
        >
          Pantry Survey
        </button>
      </div>
      <div>
        {activeSurvey === 'catering' && <Survey category_group="catering-services" />}
        {activeSurvey === 'pantry' && <Survey category_group="kitchen-supplies" />}
      </div>
    </>
  );
};

export default SurveyApp;
