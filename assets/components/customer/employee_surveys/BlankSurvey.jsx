import { useContext, useState } from 'react';
import shallow from 'zustand/shallow';
import useSurveyStore from 'store/employeeSurveyStore';
import { SurveyKinds } from 'utilities/employeeSurveyHelpers';
import adminContext from 'contexts/adminContext';
import DefaultQuestionsList from './questions/DefaultQuestionsList';

const BlankSurvey = ({ category_group, survey }) => {
  const { isAdmin } = useContext(adminContext);

  const defaultQuestions = {
    'catering-services': [
      'How would you rate the quality of the meals?',
      'How would you rate the portion sizes?',
      'Is there anything about our meals you would improve, and if so, what?',
      'If you have a dietary restriction please rate how accommodated you are in the meals offered?',
      'How would you rate the presentation of the meals?',
      "Is there any menu you've had before you would like to see more of? Please specify.",
      'How would you rate the variety in the meal offerings?',
    ],
    'kitchen-supplies': [
      'How would you rate the quality of the products?',
      'How would you rate the variety of products?',
      'What new options would you like us to offer?',
      'If you have a dietary restriction please rate how accommodated you are in the products offered?',
      'Are there enough healthy products on offer?',
    ],
  };

  const { createSurvey } = useSurveyStore(
    (state) => ({
      createSurvey: state.createSurvey,
    }),
    shallow
  );

  const [selectedOption, setSelectedOption] = useState(isAdmin ? 'blank' : 'default');

  const handleCreate = () => {
    if (selectedOption === 'blank') {
      createSurvey({
        category_group,
        prefill: false,
      });
    } else if (selectedOption === 'default') {
      createSurvey({
        category_group,
        prefill: true,
      });
    }
  };

  return (
    <div className="small-12 medium-6 columns">
      <h4>{SurveyKinds[category_group]} Survey</h4>
      {survey.loading && <strong>Loading Surveys....</strong>}
      {!survey.loading && (
        <>
          {isAdmin && (
            <div>
              <label>
                <input
                  type="radio"
                  className="survey-create-radio"
                  name="surveyType"
                  value="blank"
                  checked={selectedOption === 'blank'}
                  onChange={() => setSelectedOption('blank')}
                />
                Create Blank Survey
              </label>
            </div>
          )}
          <div>
            <label>
              <input
                type="radio"
                className="survey-create-radio"
                name="surveyType"
                value="default"
                checked={selectedOption === 'default'}
                onChange={() => setSelectedOption('default')}
              />
              Create Survey with Default Questions
            </label>

            {selectedOption === 'default' && (
              <>
                <p style={{ fontWeight: '900' }}>You can always edit and remove questions after creating</p>
                <div className="survey-container preview">
                  <DefaultQuestionsList questions={defaultQuestions[category_group]} />
                </div>
              </>
            )}
          </div>

          <button className="button small mt-2" onClick={handleCreate}>
            Create Survey
          </button>
        </>
      )}
    </div>
  );
};

export default BlankSurvey;
