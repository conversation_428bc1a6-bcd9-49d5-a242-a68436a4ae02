import { useState } from 'react';
import PropTypes from 'prop-types';

import Question from './Question';

const initialQuestion = (survey_id) => ({
  id: null,
  employee_survey_id: survey_id,
  label: null,
  input_type: 'text',
  options: [],
  active: true,
});

const NewQuestion = ({ survey }) => {
  const [isNew, setIsNew] = useState(false);

  if (isNew) {
    return (
      <Question
        key={`survey-${survey.id}-new-question`}
        survey={survey}
        question={initialQuestion(survey.id)}
        setIsNew={setIsNew}
        index="new"
        defaultExpanded
      />
    );
  }

  return (
    <a className="button hollow add-question-button" onClick={() => setIsNew(true)}>
      Add Question
    </a>
  );
};

NewQuestion.propTypes = {
  survey: PropTypes.object.isRequired,
};

export default NewQuestion;
