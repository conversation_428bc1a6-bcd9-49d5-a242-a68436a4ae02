import { useRef, useState, useContext, useEffect } from 'react';
import PropTypes from 'prop-types';
import Select from 'react-select';
import CreatableSelect from 'react-select/creatable';
import { useDrag, useDrop } from 'react-dnd';
import shallow from 'zustand/shallow';

import useSurveyStore from 'store/employeeSurveyStore';
import adminContext from 'contexts/adminContext';

import { INPUT_OPTIONS, dragConfig, dropConfig } from 'utilities/employeeSurveyHelpers';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

const Question = ({ survey, question, setIsNew, index, defaultExpanded = false }) => {
  const dragRef = useRef(null);
  const previewRef = useRef(null);
  const { isAdmin } = useContext(adminContext);

  const [localQuestion, setLocalQuestion] = useState(question);

  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const handleToggleExpand = (e) => {
    e.stopPropagation();
    if (index === 'new') {
      return setIsNew(false);
    }
    setIsExpanded(!isExpanded);
  };

  const { createQuestion, updateQuestion, dragQuestion, sortQuestions } = useSurveyStore(
    (state) => ({
      createQuestion: state.createQuestion,
      updateQuestion: state.updateQuestion,
      dragQuestion: state.dragQuestion,
      sortQuestions: state.sortQuestions,
    }),
    shallow
  );

  const handleChange = (event) => {
    setLocalQuestion((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  // set Input Type Selector
  const [selectedInputType, setSelectedInputType] = useState(
    INPUT_OPTIONS.find((option) => option.value == localQuestion.input_type)
  );
  const handleInputTypeSelection = (selectedOption) => {
    setSelectedInputType(selectedOption);
    setLocalQuestion((state) => ({ ...state, input_type: selectedOption.value }));
  };

  // set Options Selector
  const createOption = (label) => ({
    label,
    value: label,
  });
  const [optionsInputValue, setOptionsInputValue] = useState('');
  const [potentialOptions, setPotentialOptions] = useState(question.options.map((option) => createOption(option)));

  const handleKeyDown = (event) => {
    const actionableKeys = ['Enter', 'Tab'];

    if (!optionsInputValue || !actionableKeys.includes(event.key)) return;

    event.preventDefault();
    setPotentialOptions((prev) => [...prev, createOption(optionsInputValue)]);
    setOptionsInputValue('');
    setLocalQuestion((state) => ({ ...state, options: [...state.options, optionsInputValue] }));
  };

  const handleOptionsChange = (newValue) => {
    setPotentialOptions(newValue);
    setLocalQuestion((state) => ({ ...state, options: newValue.map((option) => option.value) }));
  };

  const handleRemove = async () => {
    if (localQuestion.loading) return;

    setLocalQuestion((state) => ({ ...state, loading: true }));
    try {
      await updateQuestion({
        question: { ...localQuestion, active: false },
        survey,
      });
    } catch (error) {
      // do nothing
      setLocalQuestion((state) => ({ ...state, loading: false }));
    }
  };

  // Save localQuestion
  const handleSave = async () => {
    if (localQuestion.loading) return;

    setLocalQuestion((state) => ({ ...state, loading: true }));
    try {
      if (localQuestion.id) {
        await updateQuestion({
          question: localQuestion,
          survey,
        });
      } else {
        await createQuestion({
          question: localQuestion,
          survey,
        });
        setIsNew(false);
      }
      setIsExpanded(false);
    } catch (error) {
      // do nothing
      setLocalQuestion((state) => ({ ...state, loading: false }));
    }
  };

  useEffect(() => {
    setLocalQuestion(question);
  }, [question]);

  const [{ isDragging }, drag, dragPreview] = useDrag(dragConfig);
  const [{ handlerId }, drop] = useDrop(
    dropConfig({
      survey,
      index,
      previewRef,
      dragAction: dragQuestion,
      sortAction: sortQuestions,
    })
  );

  useEffect(() => {
    drag(dragRef);
    drop(dragPreview(previewRef));
  }, [dragRef, [previewRef]]);

  return (
    <div
      className="question-name-container"
      ref={previewRef}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      data-handler-id={handlerId}
    >
      <div onClick={handleToggleExpand} className={`question-expand${isExpanded ? ' expanded' : ''}`}>
        <div style={{ flex: 1 }}>
          <span ref={dragRef} className="drag-handle drag-reorder-icon float-rightx mr-1-2" />
          {index !== 'new' && (
            <p className="question-name">
              <span className="question-number" style={{ background: getCircleIconColor(index) }}>
                {index + 1}
              </span>{' '}
              {localQuestion.label}
            </p>
          )}
        </div>
      </div>
      {isExpanded && (
        <div className="question-fields">
          {index === 'new' && (
            <>
              <p className="mt-1">New Question</p>
            </>
          )}
          <p className="question-tag">Label</p>
          <input
            type="text"
            name="label"
            readOnly={!isAdmin}
            className="form-input"
            value={localQuestion.label || ''}
            onChange={handleChange}
            disabled={!!localQuestion.loading}
            onClick={(e) => e.stopPropagation()}
            style={{ fontSize: '16px' }}
          />
          <div>
            <p className="question-tag">Type</p>
            <Select
              isSearchable={false}
              isClearable={false}
              value={selectedInputType}
              placeholder="Select Type of Input"
              name="input_type"
              options={INPUT_OPTIONS}
              onChange={handleInputTypeSelection}
              className="form-input mt-1"
              isDisabled={!isAdmin || !!localQuestion.loading}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
          {selectedInputType?.withOptions && (
            <div>
              <p className="question-tag">Options</p>
              <CreatableSelect
                components={{ DropdownIndicator: null }}
                inputValue={optionsInputValue}
                isClearable
                isMulti
                menuIsOpen={false}
                onChange={(newValue) => handleOptionsChange(newValue)}
                onInputChange={(newValue) => setOptionsInputValue(newValue)}
                onKeyDown={handleKeyDown}
                placeholder="Type something and press enter..."
                value={potentialOptions}
                isDisabled={!isAdmin || !!localQuestion.loading}
                className="mt-1"
                onClick={(e) => e.stopPropagation()}
              />
              {localQuestion.input_type === 'toggle' && (
                <small className="text-grey">(only the First Two options will be used for a toggle question)</small>
              )}
            </div>
          )}

          {isAdmin && (
            <div>
              <a className="button tiny rounded mt-1" disabled={!!localQuestion.loading} onClick={handleSave}>
                {localQuestion.loading ? 'Saving...' : 'Save'}
              </a>
              {index !== 'new' && (
                <a className="button tiny rounded hollow mt-1 ml-1" onClick={handleRemove}>
                  Remove
                </a>
              )}
              {index === 'new' && (
                <a className="button tiny rounded hollow float-right" onClick={() => setIsNew(false)}>
                  Cancel
                </a>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

Question.propTypes = {
  survey: PropTypes.object.isRequired,
  question: PropTypes.object.isRequired,
  setIsNew: PropTypes.func,
  // index: PropTypes.number,
};

export default Question;
