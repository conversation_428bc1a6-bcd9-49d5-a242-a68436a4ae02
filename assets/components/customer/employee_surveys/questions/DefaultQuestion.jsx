import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

const DefaultQuestion = ({ question, index }) => (
  <div className="question-name-container">
    <div>
      <div style={{ flex: 1 }}>
        <p className="question-name">
          <span className="question-number" style={{ background: getCircleIconColor(index) }}>
            {index + 1}
          </span>{' '}
          {question}
        </p>
      </div>
    </div>
  </div>
);

export default DefaultQuestion;
