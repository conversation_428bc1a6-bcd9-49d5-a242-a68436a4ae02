import { useContext } from 'react';
import PropTypes from 'prop-types';
import shallow from 'zustand/shallow';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import useSurveyStore from 'store/employeeSurveyStore';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';
import { apiEmployeeSurveyPath } from 'routes';
import Question from './Question';
import NewQuestion from './NewQuestion';

const QuestionsList = ({ survey }) => {
  const { isAdmin } = useContext(adminContext);
  const { surveyPreviewUrl } = useContext(appContext);
  const printUrl = !!survey.questions.length && apiEmployeeSurveyPath(survey, { format: 'pdf' });
  const previewUrl = !!survey.questions.length && surveyPreviewUrl.replace('_employee_survey_id', survey.uuid);

  const { updateSurvey } = useSurveyStore(
    (state) => ({
      updateSurvey: state.updateSurvey,
    }),
    shallow
  );

  const handlePrefill = () => {
    updateSurvey({
      survey,
      prefill: true,
    });
  };

  return (
    <>
      <h5>
        {survey.questions.length} Questions
        {!!printUrl && (
          <a className="button tiny hollow rounded float-right" href={printUrl}>
            Print
          </a>
        )}
        {!!previewUrl && (
          <a
            className="button tiny hollow rounded float-right mr-1-2"
            target="_blank"
            href={previewUrl}
            rel="noreferrer"
          >
            Preview
          </a>
        )}
      </h5>
      <DndProvider backend={HTML5Backend}>
        {survey.questions.map(
          (question, idx) =>
            question.active && (
              <Question key={`survey-${survey.id}-question-${idx}`} survey={survey} question={question} index={idx} />
            )
        )}
        {isAdmin && <NewQuestion survey={survey} />}
      </DndProvider>
      {!survey.questions.length && (
        <a className="button hollow tiny ml-1" onClick={handlePrefill}>
          Prefill Questions
        </a>
      )}
    </>
  );
};

QuestionsList.propTypes = {
  survey: PropTypes.object.isRequired,
};
export default QuestionsList;
