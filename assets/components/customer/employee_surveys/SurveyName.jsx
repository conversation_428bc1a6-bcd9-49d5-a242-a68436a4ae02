import { useState, useContext } from 'react';
import shallow from 'zustand/shallow';
import useSurveyStore from 'store/employeeSurveyStore';
import { SurveyKinds } from 'utilities/employeeSurveyHelpers';
import adminContext from 'contexts/adminContext';

const SurveyName = ({ survey, category_group }) => {
  const { isAdmin } = useContext(adminContext);

  const { updateSurvey } = useSurveyStore(
    (state) => ({
      updateSurvey: state.updateSurvey,
    }),
    shallow
  );

  const [editName, setEditName] = useState(false);
  const [localName, setLocalName] = useState(survey.name || `${SurveyKinds[category_group]} Survey`);

  const handleChange = (event) => {
    setLocalName(event.target.value);
  };

  const handleSave = async (event) => {
    await updateSurvey({
      survey: { ...survey, name: localName },
    });
    setEditName(false);
  };

  if (editName) {
    return (
      <h4 className="survey-title">
        <label>Survey Name</label>
        <input type="text" className="form-input" value={localName} onChange={handleChange} />
        <a className="button tiny rounded" onClick={handleSave}>
          Save
        </a>
        <a className="button tiny hollow ml-1-2" onClick={() => setEditName(false)}>
          Cancel
        </a>
      </h4>
    );
  }

  return (
    <h4 className="survey-title">
      {localName}
      {isAdmin && (
        <a className="ml-1" onClick={() => setEditName(true)}>
          Edit
        </a>
      )}
    </h4>
  );
};

export default SurveyName;
