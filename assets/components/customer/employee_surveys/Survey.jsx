import PropTypes from 'prop-types';
import shallow from 'zustand/shallow';
import useSurveyStore from 'store/employeeSurveyStore';
import BlankSurvey from './BlankSurvey';
import QuestionsList from './questions/QuestionsList';
import SurveyName from './SurveyName';
import Submissions from './submissions/Submissions';

const Survey = ({ category_group }) => {
  const { surveys } = useSurveyStore(
    (state) => ({
      surveys: state.surveys,
    }),
    shallow
  );

  const survey = surveys.find((categorySurvey) => categorySurvey?.category_group === category_group);

  if (!survey?.id) {
    return <BlankSurvey category_group={category_group} survey={survey} />;
  }

  return (
    <div className="survey-panel-container">
      <div className="survey-container">
        <SurveyName survey={survey} category_group={category_group} />
        <p>
          Last Updated: <strong>{survey.updated_at}</strong>
        </p>
        <QuestionsList survey={survey} />
      </div>

      <Submissions survey={survey} />
    </div>
  );
};

Survey.propTypes = {
  category_group: PropTypes.string.isRequired,
};

export default Survey;
