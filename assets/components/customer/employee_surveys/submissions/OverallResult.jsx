import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement } from 'chart.js';

ChartJS.register(ArcElement);

export const doughnutPlugins = [
  {
    beforeDraw(chart) {
      const { width, height, ctx, config } = chart;
      const rating = config._config.data.datasets[0].data[0];
      const overallRating = !rating ? 'No Ratings' : rating;
      ctx.restore();
      ctx.font = !rating ? '20px Museo Slab' : '900 50px Museo Slab';
      ctx.textBaseline = 'top';
      const overallRatingX = Math.round((width - ctx.measureText(overallRating).width) / 2);
      const overallRatingY = !rating ? height / 2 - 8 : height / 2 - 16;
      ctx.fillText(overallRating, overallRatingX, overallRatingY);
      ctx.save();
    },
  },
];

const OverallResult = ({ overallRating }) => {
  const data = {
    labels: ['Rating', 'Blank'],
    datasets: [
      {
        data: [overallRating, 10 - overallRating],
        backgroundColor: ['#2bffc6', '#ECEFF1'],
        border: '1px solid black',
      },
    ],
  };

  return (
    <div className="survey-result-container">
      <h4 className="survey-title">Overall Rating</h4>
      <div className="doughnut-container">
        <Doughnut data={data} options={{ responsive: true, cutout: '75%' }} plugins={doughnutPlugins} />
      </div>
    </div>
  );
};

export default OverallResult;
