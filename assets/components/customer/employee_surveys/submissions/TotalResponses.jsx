import { useEffect, useState } from 'react';

import { createCSVLink } from 'utilities/employeeSurveyHelpers';
import OnVisible from 'react-on-visible';
import { CountUp } from 'countup.js';

const TotalResponses = ({ dates, survey, totalResponses }) => {
  const [visible, setVisible] = useState(false);

  const handleExport = () => {
    if (dates.start && dates.end) {
      window.location = createCSVLink({
        survey,
        dates,
      });
    }
  };

  useEffect(() => {
    if (visible) {
      const target = document.getElementById('total-responses');
      new CountUp(target, parseInt(totalResponses, 0)).start();
    }
  }, [visible]);

  return (
    <OnVisible onChange={(isVisible) => setVisible(isVisible)} className="stats-section">
      <div className="survey-result-container total">
        <p id="total-responses" className="total-responses">
          {totalResponses}
        </p>
        <p style={{ fontSize: '18px' }}>Total Responses</p>
        {!!totalResponses && (
          <button
            type="button"
            className="generate-report-button"
            onClick={() => handleExport(true)}
            style={{ padding: '12px 26px', borderRadius: '4px' }}
          >
            Download
          </button>
        )}
      </div>
    </OnVisible>
  );
};

export default TotalResponses;
