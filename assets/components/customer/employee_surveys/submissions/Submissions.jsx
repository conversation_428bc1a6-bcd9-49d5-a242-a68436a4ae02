import { useState, useEffect } from 'react';
import shallow from 'zustand/shallow';
import useSurveyStore from 'store/employeeSurveyStore';

import { getMonthsAgoDate } from 'utilities/graph-options';

import DatePicker from 'react-datepicker';

import OverallResult from './OverallResult';
import TotalResponses from './TotalResponses';

const Submissions = ({ survey }) => {
  const [dates, setDates] = useState({
    start: getMonthsAgoDate(5),
    end: getMonthsAgoDate(0),
  });

  const { fetchSubmissions } = useSurveyStore(
    (state) => ({
      fetchSubmissions: state.fetchSubmissions,
    }),
    shallow
  );

  function onDateChange(newDates) {
    const [start, end] = newDates;
    setDates((state) => ({ ...state, start, end }));
  }

  useEffect(async () => {
    if (dates.start && dates.end) {
      fetchSubmissions({
        dates,
        survey,
      });
    }
  }, [dates]);

  return (
    <div className="survey-overall-result">
      <div className="dashboard-filters reports-filters survey-filters">
        <div className="dashboard-filter dashboard-filter__datepicker">
          <span className="between-flex">For Dates: </span>
          <div>
            <DatePicker
              startDate={dates.start}
              endDate={dates.end}
              selected={dates.start}
              onChange={(newDates) => onDateChange(newDates)}
              dateFormat="MM/yyyy"
              showMonthYearPicker
              selectsRange
              className="dashboard-filter"
            />
          </div>
        </div>
      </div>
      <OverallResult overallRating={survey.overall_rating} />
      <TotalResponses totalResponses={survey.number_of_submissions} survey={survey} dates={dates} />
    </div>
  );
};

export default Submissions;
