import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import { useDispatch } from 'react-redux';
import { updateOrder } from 'actions/purchaseOrderActions';
import SupplierIcon from 'components/customer/SupplierIcon';

const PurchaseOrderAttachable = ({ purchaseOrder, order }) => {
  const [localOrder, setLocalOrder] = useState({
    ...order,
    attached: order.cpo_id == purchaseOrder.id,
    is_updated: false,
  });
  const dispatch = useDispatch();

  const handleClick = (event) => {
    setLocalOrder((state) => ({ ...state, is_updated: true, attached: !state.attached }));
  };

  useEffect(() => {
    if (localOrder.is_updated) {
      dispatch(
        updateOrder({
          order: localOrder,
          purchaseOrder,
        })
      );
    }
  }, [localOrder.attached]);

  return (
    <div className={`boxed-data po-order ${localOrder.attached ? 'attached' : ''}`} onClick={handleClick}>
      <div className="po-order-banner">
        <h6 className="po-order-heading">{localOrder.delivery_date}</h6>
        <div className="between-flex">
          {localOrder.suppliers.map((supplier) => (
            <SupplierIcon key={supplier.name} supplier={supplier} />
          ))}
        </div>
      </div>
      <p>
        <strong>Order No:</strong> #{localOrder.id}
      </p>
      <p>
        <strong>Name:</strong> {localOrder.name}
      </p>
      <p>
        <strong>Address:</strong> {localOrder.delivery_address_arr.join(', ')}
      </p>
      {!localOrder.attached && order.po_number && (
        <p>
          <strong>Currently attached to:</strong> {!order.po_active ? '(inactive)' : ''} <em>{order.po_number}</em>
        </p>
      )}
    </div>
  );
};

PurchaseOrderAttachable.propTypes = {
  purchaseOrder: PropTypes.object.isRequired,
  order: PropTypes.object.isRequired,
};

export default PurchaseOrderAttachable;
