import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { createPurchaseOrder, updatePurchaseOrder } from 'actions/purchaseOrderActions';

// loader
import { createLoadingSelector } from 'selectors';

const updateSelector = createLoadingSelector(['UPDATE_PURCHASE_ORDER']);
const createSelector = createLoadingSelector(['CREATE_PURCHASE_ORDER']);

const descriptionPlaceholder = 'Briefly describe the usage of this PO number';

const PurchaseOrderForm = ({ purchase_order, setFormShow }) => {
  const loadingState = useSelector((state) => state.loading);
  const isUpdating = updateSelector(loadingState, purchase_order.id);
  const isCreating = createSelector(loadingState);
  const isLoading = (purchase_order.id && isUpdating) || isCreating;

  const [purchaseOrder, setPurchaseOrder] = useState(purchase_order);
  const dispatch = useDispatch();

  const handleChange = (event) => {
    setPurchaseOrder((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  const handleUpdate = async (event) => {
    if (!isUpdating && purchaseOrder.id) {
      await updatePurchaseOrder({
        purchaseOrder,
        dispatch,
      });
      setFormShow(false);
    } else if (!isCreating) {
      await createPurchaseOrder({
        purchaseOrder,
        dispatch,
      });
      setFormShow(false);
    }
  };

  return (
    <div className="customer-data">
      <div className="list-flex-3 px-1-2">
        <label>PO Number</label>
        <input
          type="text"
          name="po_number"
          className="form-input"
          readOnly={!!isLoading}
          value={purchaseOrder.po_number}
          onChange={handleChange}
        />
      </div>

      <div className="list-flex-5 px-1-2">
        <label>
          Description
          {purchaseOrder.description && <small className="label-hint ml-1-2">({descriptionPlaceholder})</small>}
        </label>
        <input
          type="text"
          placeholder={descriptionPlaceholder}
          className="form-input"
          name="description"
          onChange={handleChange}
          readOnly={!!isLoading}
          value={purchaseOrder.description || ''}
        />
      </div>

      <div className="list-flex-3 pr-1 customer-data__flex-action-field">
        <a className="button small" onClick={handleUpdate}>
          {isLoading ? 'Saving...' : 'Save'}
        </a>
        <span className="ml-1-2" onClick={() => setFormShow(false)}>
          cancel
        </span>
      </div>
    </div>
  );
};

PurchaseOrderForm.propTypes = {
  purchase_order: PropTypes.object.isRequired,
};

export default PurchaseOrderForm;
