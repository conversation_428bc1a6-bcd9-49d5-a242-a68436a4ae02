import { useEffect } from 'react';
import PropTypes from 'prop-types';

// components
import { useSelector, useDispatch } from 'react-redux';

// actions
import { fetchOrders } from 'actions/purchaseOrderActions';

// loader
import { createLoadingSelector } from 'selectors';
import OrderSkeleton from './PurchaseOrderSkeleton';
import Order from './PurchaseOrder';

const loadingSelector = createLoadingSelector(['FETCH_ORDERS']);

const PurchaseOrderList = ({ purchaseOrder }) => {
  const { orders } = useSelector((state) => state.purchase_order);
  const dispatch = useDispatch();

  const loadingState = useSelector((state) => state.loading);
  const loading = loadingSelector(loadingState);

  useEffect(() => {
    if (!orders.length) {
      dispatch(
        fetchOrders({
          purchaseOrder,
        })
      );
    }
  }, []);

  if (loading) {
    return <OrderSkeleton />;
  }

  const attachedOrders = [];
  const unAttachedOrders = [];
  orders.forEach((order) => {
    if (order.cpo_id === purchaseOrder.id) {
      attachedOrders.push(order);
    } else {
      unAttachedOrders.push(order);
    }
  });

  return (
    <>
      {attachedOrders.length > 0 && (
        <>
          <h6 className="po-grouped-orders-heading">
            Attached to {purchaseOrder.po_number} ({attachedOrders.length})
          </h6>
          <div className="po-order-list">
            {attachedOrders.map((order) => (
              <Order
                key={`purchase-order-${purchaseOrder.id}-order-${order.id}`}
                purchaseOrder={purchaseOrder}
                order={order}
              />
            ))}
          </div>
        </>
      )}
      {unAttachedOrders.length > 0 && (
        <>
          <h6 className="po-grouped-orders-heading">Non-Attached Orders ({unAttachedOrders.length})</h6>
          <div className="po-order-list">
            {unAttachedOrders.map((order) => (
              <Order
                key={`purchase-order-${purchaseOrder.id}-order-${order.id}`}
                purchaseOrder={purchaseOrder}
                order={order}
              />
            ))}
          </div>
        </>
      )}
    </>
  );
};

PurchaseOrderList.propTypes = {
  purchaseOrder: PropTypes.object.isRequired,
};

export default PurchaseOrderList;
