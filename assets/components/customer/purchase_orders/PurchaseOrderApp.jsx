import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// components
import { fetchPurchaseOrders } from 'actions/purchaseOrderActions';
import { createLoadingSelector } from 'selectors';
import PurchaseOrderSkeleton from './PurchaseOrderSkeleton';
import PurchaseOrder from './PurchaseOrder';
import PurchaseOrderForm from './PurchaseOrderForm';

// actions

const loadingSelector = createLoadingSelector(['FETCH_PURCHASE_ORDERS']);
const infiniteLoadingSelector = createLoadingSelector(['FETCH_MORE_PURCHASE_ORDERS']);

const PurchaseOrderApp = () => {
  const [newPurchaseOrder, setNewPurchaseOrder] = useState(false);
  const { purchase_orders, page, hasMore } = useSelector((state) => state.purchase_order);

  const dispatch = useDispatch();

  const loadingState = useSelector((state) => state.loading);

  const loading = loadingSelector(loadingState);
  const infiniteLoading = infiniteLoadingSelector(loadingState);

  const initialPurchaseOrder = {
    po_number: '',
    descritpion: '',
    orders: [],
  };

  useEffect(() => {
    dispatch(
      fetchPurchaseOrders({
        page,
        infiniteLoading: false,
      })
    );
  }, []);

  const [sentryRef] = useInfiniteScroll({
    loading: infiniteLoading,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (purchase_orders.length && !loading) {
        await dispatch(
          fetchPurchaseOrders({
            page,
            infiniteLoading: true,
          })
        );
      }
    },
  });

  return (
    <>
      <div className="customer-data-list__headings">
        <span className="list-flex-1"># of orders</span>
        <span className="list-flex-3">PO number</span>
        <span className="list-flex-5">Latest Order</span>
        <span className="list-flex-2 text-center">Active</span>
        <span className="list-flex-2 text-right pr-2">Edit</span>
      </div>

      {!newPurchaseOrder && (
        <div className="customer-data">
          <a onClick={() => setNewPurchaseOrder(true)}>Add new purchase order</a>
        </div>
      )}
      {newPurchaseOrder && (
        <PurchaseOrderForm purchase_order={initialPurchaseOrder} setFormShow={setNewPurchaseOrder} />
      )}

      {purchase_orders.map((purchase_order, idx) => (
        <PurchaseOrder
          key={`purchase-order-${purchase_order.id}-${purchase_order.active}`}
          purchase_order={purchase_order}
          index={idx}
        />
      ))}

      <div ref={sentryRef}>{(loading || infiniteLoading) && <PurchaseOrderSkeleton />}</div>
    </>
  );
};

export default PurchaseOrderApp;
