import { useState, useEffect, useContext } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';

// components

// actions
import { updatePurchaseOrder, removePurchaseOrder } from 'actions/purchaseOrderActions';
import adminContext from 'contexts/adminContext';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import OrderList from './PurchaseOrderAttachableList';
import PurchaseOrderForm from './PurchaseOrderForm';

const PurchaseOrder = ({ purchase_order, index }) => {
  const [purchaseOrder, setPurchaseOrder] = useState(purchase_order);
  const [edit, setEdit] = useState(false);
  const [manageOrders, setManageOrders] = useState(false);
  const { isAdmin } = useContext(adminContext);
  const dispatch = useDispatch();

  const lastestOrder = purchaseOrder.latest_order;
  let circleColor = getCircleIconColor(index);
  if (!purchaseOrder.active) circleColor = '#eee';

  const canManage = purchaseOrder.active && isAdmin;

  const handleActiveChange = () => {
    setPurchaseOrder((state) => ({ ...state, is_updated: true, active: !purchaseOrder.active }));
  };

  useEffect(() => {
    if (purchaseOrder.is_updated) {
      updatePurchaseOrder({
        purchaseOrder,
        dispatch,
      });
    }
  }, [purchaseOrder.active]);

  const handleRemove = (event) => {
    dispatch(
      removePurchaseOrder({
        purchaseOrder: purchase_order,
      })
    );
  };

  if (edit) {
    return <PurchaseOrderForm purchase_order={purchase_order} setFormShow={setEdit} />;
  }

  return (
    <>
      <div className={`customer-data ${!purchaseOrder.active ? 'inactive' : ''}`}>
        <div className="list-flex-1">
          <span className="circle-icon" style={{ background: circleColor }}>
            {purchase_order.number_of_orders}
          </span>
        </div>
        <div className="list-flex-3">
          {purchase_order.po_number}
          {!!purchase_order.description && <small style={{ display: 'block' }}>{purchase_order.description}</small>}
        </div>
        <div className="list-flex-5">
          {!!lastestOrder && (
            <span tile={lastestOrder.name}>
              #{lastestOrder.id} - {lastestOrder.delivery_at}
            </span>
          )}
          {!lastestOrder && <span>Not attached to any Orders</span>}
        </div>
        <div className="list-flex-2 text-center">
          {!!lastestOrder && (
            <div className="toggle-checkbox">
              <input name="active" type="checkbox" checked={purchaseOrder.active} readOnly />
              <span className="toggle-checkbox__switch" onClick={handleActiveChange} />
            </div>
          )}
          {!lastestOrder && <a onClick={handleRemove}>remove</a>}
        </div>
        <div className="list-flex-2 pr-2 customer-data__flex-action-field">
          <a className="icon-edit mr-1" onClick={() => setEdit(true)} />
          {!!canManage && (
            <a className="icon-link mr-1" onClick={() => setManageOrders(!manageOrders)}>
              manage
            </a>
          )}
        </div>
      </div>
      {!!manageOrders && <OrderList purchaseOrder={purchase_order} setManageOrders={setManageOrders} />}
    </>
  );
};

PurchaseOrder.propTypes = {
  purchase_order: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default PurchaseOrder;
