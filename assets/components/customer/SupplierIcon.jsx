import { useState } from 'react';
import PropTypes from 'prop-types';

const SupplierIcon = ({ supplier }) => {
  const [showFallbackImage, setShowFallbackImage] = useState(false);

  if (!supplier) return null;

  if (showFallbackImage) {
    return (
      <span className="circle-icon" style={{ background: 'black', color: 'white' }} data-tip={supplier.name}>
        {supplier.name.substring(0, 2).toUpperCase()}
      </span>
    );
  }
  return (
    <img
      className="circle-icon"
      src={supplier.image}
      data-tip={supplier.name}
      onError={() => setShowFallbackImage(true)}
    />
  );
};

SupplierIcon.propTypes = {
  supplier: PropTypes.object.isRequired,
};
export default SupplierIcon;
