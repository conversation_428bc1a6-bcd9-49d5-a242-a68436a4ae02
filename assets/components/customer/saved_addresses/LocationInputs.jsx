import { useState, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';
import { usePlacesWidget } from 'react-google-autocomplete';

// actions
import appContext from 'contexts/appContext';
import { getYordarSuburb } from 'utilities/locationSelection';

// components
import SuburbSelector from './SuburbSelector';

const GOOGLE_AUTOCOMPLETE_OPTIONS = (countryCode) => ({
  types: ['geocode'],
  fields: ['place_id', 'types', 'formatted_address', 'address_components'],
  componentRestrictions: {
    country: countryCode || 'au',
  },
});

const LocationInputs = ({ savedAddress, setSavedAddress }) => {
  const initialValue = {
    value: savedAddress.street_address || null,
    suburb: savedAddress.suburb || null,
  };
  const initialSelection = {
    isSelected: false,
    tries: 0,
    allowFree: false,
  };
  const [currentLocation, setCurrentLocation] = useState(initialValue);
  const [locationSelection, setLocationSelection] = useState(initialSelection);
  const { googleApiKey, countryCode } = useContext(appContext);

  // check if address was selected or just typed / Copy / Pasted
  const handleBlur = (event) => {
    if (!locationSelection.allowFree) {
      setCurrentLocation((state) => ({ ...state, value: '' }));
      setLocationSelection((state) => ({ ...state, tries: state.tries + 1 }));
    }
  };

  // handle current value change typing value
  const handleChange = (event) => {
    setCurrentLocation((state) => ({
      ...state,
      value: event.target.value,
      suburb: locationSelection.allowFree ? state.suburb : null,
    }));
    setLocationSelection((state) => ({ ...state, isSelected: false }));
  };

  // handle address change if current location changes
  useEffect(() => {
    if (currentLocation.value && currentLocation.suburb) {
      const { suburb } = currentLocation;
      setSavedAddress((state) => ({
        ...state,
        street_address: currentLocation.value,
        suburb_label: suburb.label,
        suburb_id: suburb.id,
      }));
    }
  }, [currentLocation.value, currentLocation.suburb]);

  const { ref } = usePlacesWidget({
    apiKey: googleApiKey,
    onPlaceSelected: (place) => handlePlaceSelected(place),
    options: GOOGLE_AUTOCOMPLETE_OPTIONS(countryCode),
  });

  const handlePlaceSelected = async (places) => {
    setLocationSelection((state) => ({ ...state, isSelected: true }));
    const selectedLocation = {
      postcode: null,
      locality: null,
      streetAddress: null,
    };
    const postcodeComponent = places.address_components.find(
      (component) => component.types.indexOf('postal_code') != -1
    );
    if (postcodeComponent) {
      selectedLocation.postcode = postcodeComponent.short_name;
    }

    const sublocalityComponent = places.address_components.find(
      (component) => component.types.indexOf('sublocality') != -1
    );
    if (sublocalityComponent) {
      selectedLocation.locality = sublocalityComponent.long_name;
    }

    const localityComponent = places.address_components.find((component) => component.types.indexOf('locality') != -1);
    if (localityComponent) {
      selectedLocation.locality ||= localityComponent.long_name;
    }

    if (places.types.find((type) => type === 'street_address' || type === 'premise')) {
      const streetAddressComponents = places.formatted_address
        .split(', ')
        .filter((address) => !address.startsWith('Level'));
      selectedLocation.streetAddress = streetAddressComponents[0];
    }

    const currentValue = selectedLocation.streetAddress;

    // // get yordar suburb
    const yordarSuburb = await getYordarSuburb({
      selectedLocation,
      countryCode,
    });

    if (yordarSuburb) {
      setCurrentLocation((state) => ({ ...state, value: currentValue, suburb: yordarSuburb }));
    } else {
      // could not find suburb
      alert('could not find suburb');
      setCurrentLocation((state) => ({ ...state, value: '', suburb: null }));
    }
  };

  return (
    <>
      <div className="row">
        <div className="small-12 medium-6 columns">
          <label>Street Address</label>
          <input
            ref={ref}
            autoComplete="no"
            className="form-input"
            required
            type="text"
            onBlur={(e) => locationSelection.isSelected || handleBlur()}
            onChange={handleChange}
            value={currentLocation.value || ''}
          />
          {locationSelection.tries >= 3 && (
            <label>
              <input
                type="checkbox"
                onClick={(e) => setLocationSelection({ ...locationSelection, allowFree: e.target.checked })}
              />
              Couldn't find street address? - check to allow copy-paste location
            </label>
          )}
        </div>

        <div className="small-12 medium-6 columns">
          <label>Suburb</label>
          {!locationSelection.allowFree && (
            <input
              type="text"
              name="suburb_label"
              required="required"
              className="form-input"
              readOnly
              value={savedAddress.suburb_label || ''}
              placeholder="Set by entering and selecting the Street Address"
            />
          )}
          {locationSelection.allowFree && (
            <SuburbSelector
              savedAddress={savedAddress}
              setCurrentLocation={setCurrentLocation}
              allowFree={locationSelection.allowFree}
            />
          )}
        </div>
      </div>
    </>
  );
};

LocationInputs.propTypes = {
  savedAddress: PropTypes.object.isRequired,
  setSavedAddress: PropTypes.func.isRequired,
};

export default LocationInputs;
