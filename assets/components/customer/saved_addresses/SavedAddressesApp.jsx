import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// components
import { fetchSavedAddresss } from 'actions/savedAddressActions';
import { createLoadingSelector } from 'selectors';
import SavedAddressSkeleton from './SavedAddressSkeleton';
import SavedAddress from './SavedAddress';
import SavedAddressForm from './SavedAddressForm';

// actions

// loader

const loadingSelector = createLoadingSelector(['FETCH_SAVED_ADDRESSS']);
const infiniteLoadingSelector = createLoadingSelector(['FETCH_MORE_SAVED_ADDRESSS']);

const SavedAddressesApp = () => {
  const [newSavedAddress, setNewSavedAddress] = useState(false);
  const { saved_addresses, page, hasMore } = useSelector((state) => state.saved_address);
  const dispatch = useDispatch();

  const loadingState = useSelector((state) => state.loading);

  const loading = loadingSelector(loadingState);
  const infiniteLoading = infiniteLoadingSelector(loadingState);

  const initialSavedAddress = {
    level: '',
    street_address: '',
    suburb_id: '',
    suburb_label: '',
    instructions: '',
  };

  useEffect(() => {
    dispatch(
      fetchSavedAddresss({
        page,
        infiniteLoading: false,
      })
    );
  }, []);

  const [sentryRef] = useInfiniteScroll({
    loading: infiniteLoading,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (saved_addresses.length && !loading) {
        await dispatch(
          fetchSavedAddresss({
            page,
            infiniteLoading: true,
          })
        );
      }
    },
  });

  return (
    <>
      <div className="customer-data-list__headings">
        <span className="list-flex-5 pl-1">Address</span>
        <span className="list-flex-5">Instructions</span>
        <span className="list-flex-2 text-right pr-2">Actions</span>
      </div>

      {!newSavedAddress && (
        <div className="customer-data">
          <a onClick={() => setNewSavedAddress(true)}>Add new address</a>
        </div>
      )}
      {newSavedAddress && <SavedAddressForm saved_address={initialSavedAddress} setFormShow={setNewSavedAddress} />}

      {saved_addresses.map((saved_address, idx) => (
        <SavedAddress key={`saved-address-${saved_address.id}`} saved_address={saved_address} index={idx} />
      ))}

      <div ref={sentryRef}>{(loading || infiniteLoading) && <SavedAddressSkeleton />}</div>
    </>
  );
};

export default SavedAddressesApp;
