import { useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';

// components

// actions
import { createSavedAddress, updateSavedAddress } from 'actions/savedAddressActions';

// loader
import { createLoadingSelector } from 'selectors';
import LocationInputs from './LocationInputs';

const updateSelector = createLoadingSelector(['UPDATE_SAVED_ADDRESS']);
const createSelector = createLoadingSelector(['CREATE_SAVED_ADDRESS']);

const SavedAddressForm = ({ saved_address, setFormShow }) => {
  const loadingState = useSelector((state) => state.loading);
  const isUpdating = updateSelector(loadingState, saved_address.id);
  const isCreating = createSelector(loadingState);
  const isLoading = (saved_address.id && isUpdating) || isCreating;

  const [savedAddress, setSavedAddress] = useState(saved_address);
  const dispatch = useDispatch();

  const handleChange = (event) => {
    setSavedAddress((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  const handleUpdate = async (event) => {
    if (!isUpdating && savedAddress.id) {
      await updateSavedAddress({
        savedAddress,
        dispatch,
      });
      setFormShow(false);
    } else if (!isCreating) {
      await createSavedAddress({
        savedAddress,
        dispatch,
      });
      setFormShow(false);
    }
  };

  return (
    <div className="customer-data" style={{ display: 'block' }}>
      <div className="row">
        <div className="small-12 columns">
          <label>
            Level
            <small className="ml-1-4">(optional)</small>
          </label>
          <input
            type="text"
            name="level"
            className="form-input"
            readOnly={!!isLoading}
            value={savedAddress.level}
            onChange={handleChange}
          />
        </div>
      </div>

      <LocationInputs savedAddress={savedAddress} setSavedAddress={setSavedAddress} isRequired />

      <div className="row">
        <div className="small-12 columns">
          <label>Instructions</label>
          <textarea
            placeholder="Please enter instructions for supplier."
            className="form-input"
            rows="3"
            required="required"
            name="instructions"
            onChange={handleChange}
            value={savedAddress.instructions || ''}
          />
        </div>
      </div>

      <div className="row">
        <div className="small-12 columns">
          <a className="button small" onClick={handleUpdate}>
            {isLoading ? 'Saving...' : 'Save'}
          </a>
          <span className="ml-1-2 button small gray-btn" onClick={() => setFormShow(false)}>
            cancel
          </span>
        </div>
      </div>
    </div>
  );
};

SavedAddressForm.propTypes = {
  saved_address: PropTypes.object.isRequired,
};

export default SavedAddressForm;
