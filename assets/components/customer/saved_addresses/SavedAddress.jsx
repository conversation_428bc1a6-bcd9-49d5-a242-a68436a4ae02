import { useState } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';

// actions
import { removeSavedAddress } from 'actions/savedAddressActions';
import SavedAddressForm from './SavedAddressForm';

const SavedAddress = ({ saved_address }) => {
  const [edit, setEdit] = useState(false);
  const dispatch = useDispatch();

  const handleRemove = (event) => {
    dispatch(
      removeSavedAddress({
        savedAddress: saved_address,
      })
    );
  };

  if (edit) {
    return <SavedAddressForm saved_address={saved_address} setFormShow={setEdit} />;
  }

  return (
    <div className="customer-data">
      <div className="list-flex-5 pl-1">
        <p dangerouslySetInnerHTML={{ __html: saved_address.address_arr.join(',<br/>') }} />
      </div>
      <div className="list-flex-5">{saved_address.instructions}</div>
      <div className="list-flex-2 pr-2 customer-data__flex-action-field">
        <a className="icon-edit" onClick={() => setEdit(true)} />
        <a className="icon-trash ml-1" onClick={handleRemove} />
      </div>
    </div>
  );
};

SavedAddress.propTypes = {
  saved_address: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default SavedAddress;
