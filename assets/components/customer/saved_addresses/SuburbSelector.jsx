import { useEffect } from 'react';
import PropTypes from 'prop-types';

// actions
import AsyncSelect from 'react-select/async';
import { fetchSuburbByTerm } from 'utilities/locationSelection';

// components

const SuburbSelector = ({ setCurrentLocation }) => {
  useEffect(() => {
    setCurrentLocation((state) => ({ ...state, suburb: null }));
  }, []);

  const handleSelection = (suburb) => {
    setCurrentLocation((state) => ({ ...state, suburb }));
  };

  const promiseOptions = async (term) => {
    if (!term || term.length < 3) return [];
    const suburbs = await fetchSuburbByTerm({
      term,
    });
    return suburbs;
  };

  return (
    <AsyncSelect
      className="form-input"
      cacheOptions
      defaultOptions
      loadOptions={promiseOptions}
      onChange={handleSelection}
    />
  );
};

SuburbSelector.propTypes = {
  setCurrentLocation: PropTypes.func.isRequired,
};

export default SuburbSelector;
