import shallow from 'zustand/shallow';
import useCustomerInvoiceStore from 'store/customerQuotesStore';

const NoQuotesNotice = () => {
  const { quotes } = useCustomerInvoiceStore(
    (state) => ({
      quotes: state.quotes,
    }),
    shallow
  );

  if (quotes.length) return null;

  return (
    <div className="customer-invoice customer-data text-center">
      <div style={{ width: '100%' }}>No Quotes Found!</div>
    </div>
  );
};

export default NoQuotesNotice;
