import { useState, useContext } from 'react';

import appContext from 'contexts/appContext';
import shallow from 'zustand/shallow';
import customerQuotesStore from 'store/customerQuotesStore';
import useInfiniteScroll from 'react-infinite-scroll-hook';

import Quote from './Quote';
import NoQuotesNotice from './NoQuotesNotice';
import QuoteSkeleton from './QuoteSkeleton';
import QuoteDetails from './QuoteDetails';
import { ToastContainer } from 'react-toastify';

const QuotesApp = () => {
  const [activeQuote, setActiveQuote] = useState(null);
  const [linkCopied, setLinkCopied] = useState(false);
  const { externalOrderUrls } = useContext(appContext);

  const { fetchQuotes, quotes, loadingQuotes, loadingMore, hasMore, page } = customerQuotesStore(
    (state) => ({
      fetchQuotes: state.fetchQuotes,
      quotes: state.quotes,
      loadingQuotes: state.loadingQuotes,
      loadingMore: state.loadingMore,
      hasMore: state.hasMore,
      page: state.page,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingQuotes && !loadingMore) {
        await fetchQuotes({
          page,
        });
      }
    },
  });

  const copyShareLink = () => {
    if (linkCopied) return;

    const input = document.createElement('input');
    input.type = 'text';
    input.value = externalOrderUrls.share;
    document.getElementsByClassName('share-button')[0].after(input);
    input.focus();
    input.select();
    document.execCommand('copy');
    input.remove();
    setLinkCopied(true);
    setTimeout(() => {
      setLinkCopied(false);
    }, 5000);
  };

  return (
    <>
      {!!activeQuote && (
        <>
          <div className="overlay show" onClick={() => setActiveQuote(null)} />
          <div className={`sidebar-overlay ${activeQuote ? 'open' : 'closed'}`}>
            <div className="admin-order-slider">
              <QuoteDetails quote={activeQuote} setActiveQuote={setActiveQuote} />
            </div>
          </div>
        </>
      )}
      <div className='between-flex'>
        <a href={externalOrderUrls.quote} className="button black-btn">
          Create New Quote
        </a>
        <a onClick={copyShareLink} className="button hollow share-button">
          {linkCopied ? 'Link Copied!' : 'Copy Share Link'}
        </a>
      </div>
      <div className="invoice-list">
        <div className="customer-invoices__headings">
          <span className="list-flex-2">Created</span>
          <span className="list-flex-2">Occasion</span>
          <span className="list-flex-2 text-center">For Date</span>
          <span className="list-flex-2 text-center">Status</span>
          <span className="list-flex-1 px-1-2">Actions</span>
        </div>
        {!hasMore && <NoQuotesNotice />}
        {quotes.map((quote, idx) => (
          <Quote
            key={`customer-quote-${quote.id}`}
            quote={quote}
            index={idx}
            setActiveQuote={setActiveQuote}
          />
        ))}
        <div ref={sentryRef}>{(loadingQuotes || loadingMore) && <QuoteSkeleton />}</div>
      </div>
      <ToastContainer />
    </>
  );
};

export default QuotesApp;
