import { useState, useEffect } from 'react';

// action
import axios from 'axios';
import { apiQuotePath } from 'routes';

import { customerFields, nonCustomerFields } from 'utilities/adminHelpers';

const QuoteDetails = ({ quote, setActiveQuote }) => {
  const [quoteInfo, setQuoteInfo] = useState({});
  const [loadingInfo, setLoadingInfo] = useState(false);

  const fetchQuoteInfo = async () => {
    setLoadingInfo(true);
    try {
      const { data: responseInvoice } = await axios({
        method: 'GET',
        url: apiQuotePath(quote, { format: 'json' }),
      });
      setQuoteInfo(responseInvoice);
    } catch (e) {
      // do nothing
    }
    setLoadingInfo(false);
  };

  useEffect(() => {
    fetchQuoteInfo();
  }, [quote.id]);

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="order-show-details-section">
        <h6 className="order-show-detail-title supplier">Customer</h6>
        {customerFields.map(
          (field) =>
            !!quote[field] && (
              <p key={`quote-${quote.id}-field-${field}`}>
                <strong>{field}: </strong>
                {field === 'Email' && <a href={`mailto:${quote[field]}`}>{quote[field]}</a>}
                {field === 'Phone' && <a href={`tel:${quote[field]}`}>{quote[field]}</a>}
                {!['Email', 'Phone'].includes(field) && <span>{quote[field]}</span>}
              </p>
            )
        )}
      </div>
      <div className="order-show-details-section">
        <h6 className="order-show-detail-title status">Quote Details</h6>
        {nonCustomerFields.map(
          (field) =>
            !!quote[field] && (
              <p key={`quote-${quote.id}-field-${field}`}>
                <strong>{field}: </strong>
                <span>{quote[field]}</span>
              </p>
            )
        )}
      </div>

      {loadingInfo && <div className="order-map order-map--small loading" />}
      {!!quoteInfo.orders?.length && (
        <div className="order-show-details-section">
          <h6 className="order-show-detail-title order">Orders ({quoteInfo.orders.length})</h6>
          <ul className='m-0'>
            {quoteInfo.orders.map((order) => (
              <li key={`quote-${quote.id}-order-${order.id}`}>
                <p>
                  <a href={order.show_path} target='blank'>
                    <strong>#{order.id}</strong> - {order.name}
                  </a>
                </p>
                <p>{order.delivery_at}</p>
                <p>
                  <strong>Status: </strong>
                  <span>{order.status.toUpperCase()}</span>
                </p>
                <p>
                  <strong>Total: </strong>
                  <span>{order.total}</span>
                </p>
                <a className="button tiny hollow" style={{ width: '100%' }} href={order.show_path} target='_blank'>
                  View Order
                </a>
                <hr />
              </li>
            ))}
          </ul>
        </div>  
      )}
      
      <a className="button gray-btn" style={{ width: '100%' }} onClick={() => setActiveQuote(null)}>
        Close
      </a>
    </div>
  );
}

export default QuoteDetails;
