import { useEffect, useState, useContext } from 'react';

import { useSelector } from 'react-redux';
import DropDown from 'react-dropdown';
import ExportPdfForm from './ExportPdfForm';
import { hasCachedOrders } from 'utilities/orderKeySanitizing';
import useCustomerSuppliers from 'hooks/useCustomerSuppliers';

import { orderTypeOptions } from 'utilities/dropdownOptions';
import appContext from 'contexts/appContext';

// actions
import axios from 'axios';
import { apiCustomerPath } from 'routes';
import { csrfHeaders } from 'utilities/csrfHeaders';
import mealPlansStore from 'store/mealPlansStore';

const OrderFilters = ({ supplierName, params, setParams, isCalendarView, setIsCalendarView }) => {
  const [searchValue, setSearchValue] = useState(undefined);
  const [activeType, setActiveType] = useState('all');
  const [exportPdf, setExportPdf] = useState(false);
  const { customer } = useContext(appContext);
  const mealPlanOrders = mealPlansStore((state) => state.orders);

  const { orders, currentOrders } = useSelector((state) => state.customer);
  const cachedKey = hasCachedOrders(Object.keys(orders), params, isCalendarView);
  const customerSuppliers = isCalendarView
    ? useCustomerSuppliers(mealPlanOrders || [], mealPlanOrders?.length)
    : useCustomerSuppliers(
      orders?.all || orders?.show_past || (cachedKey && orders[cachedKey]) || [],
      currentOrders.orders.length
    );

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchValue !== undefined) {
        if (searchValue) {
          setParams((state) => ({ ...state, query: searchValue }));
        } else {
          setParams(({ query, ...state }) => ({ ...state }));
        }
      }
    }, 1000);
    return () => clearTimeout(delayDebounceFn);
  }, [searchValue]);

  const handleDropDownChange = ({ value, paramKey, clear }) => {
    setActiveType(value);
    setParams((state) => {
      if (paramKey === 'supplier_ids[]') {
        delete state.supplier_ids;
      }
      return { ...state, [paramKey]: value };
    });
    if (clear)
      setParams((state) => {
        delete state[paramKey];
        return state;
      });
  };

  const updateOrderView = (viewType) => {
    try {
      axios({
        method: 'PUT',
        url: apiCustomerPath(customer, { format: 'json' }),
        data: { customer_flags: { default_orders_view: viewType } },
        headers: csrfHeaders(),
      });
    } catch (error) {
      // do nothing
    }
    setIsCalendarView(viewType === 'calendar');
  };

  return (
    <>
      <div className="between-flex">
        <div className='dashboard-filters'>
          <button
            className={`admin-order-toggle${isCalendarView ? '' : ' active'} order`}
            onClick={() => updateOrderView('list')}
          >
            List
          </button>
          <button
            className={`admin-order-toggle${isCalendarView ? ' active' : ''} order`}
            onClick={() => updateOrderView('calendar')}
          >
            Calendar
          </button>
        </div>
        <button className='button small' onClick={() => setExportPdf(true)}>
          Export PDF
        </button>
      </div>
      <div className="dashboard-filters">
        <DropDown
          options={orderTypeOptions}
          onChange={({ value }) =>
            handleDropDownChange({
              value,
              paramKey: 'order_type',
              clear: value === 'all',
            })
          }
          className="dashboard-filter customer-order-dropdown customer-order-dropdown--order-type"
          menuClassName="dropdown"
          controlClassName={`dashboard-filter__type dashboard-filter__type--${activeType}`}
          placeholder="Type"
        />
        <DropDown
          options={customerSuppliers}
          onChange={({ value }) =>
            handleDropDownChange({
              value,
              paramKey: 'supplier_ids[]',
              clear: value === 'all',
            })
          }
          className="dashboard-filter customer-order-dropdown"
          menuClassName="dropdown"
          controlClassName="dashboard-filter__supplier"
          placeholder={supplierName || 'Supplier'}
        />
        <input
          placeholder="Search Orders"
          className="dashboard-filter__search dashboard-filter"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
        />
      </div>
      {!!exportPdf && <ExportPdfForm setExportPdf={setExportPdf} customerSuppliers={customerSuppliers} />}
    </>
  );
};

export default OrderFilters;
