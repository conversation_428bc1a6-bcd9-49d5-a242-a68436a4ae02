import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import DropDown from 'react-dropdown';
import DatePicker from 'react-datepicker';
import moment from 'moment';
import 'react-datepicker/dist/react-datepicker.css';

import axios from 'axios';
import { apiMealPlansPath, apiOrdersPath, exportPdfAPIOrdersPath } from 'routes';
import { orderTypeOptions } from 'utilities/dropdownOptions';

const EXPORTABLE_ORDER_STATUSES = ['new', 'quoted', 'amended', 'confirmed', 'delivered', 'pending'];

const ExportPdfForm = ({ setExportPdf, selectedMealPlan = null, customerSuppliers = [] }) => {
  const initialFilters = {
    order_type: 'all',
    from_date: new Date(),
    to_date: new Date(moment(new Date()).add(1, 'month').add(-1, 'day')),
    ...(selectedMealPlan ? { meal_plan_id: selectedMealPlan.id } : {}),
  };
  const [mealPlans, setMealPlans] = useState([]);
  const [params, setParams] = useState(initialFilters);
  const [fetchedOrders, setFetchedOrders] = useState([]);
  const [noOrdersFetched, setNoOrdersFetched] = useState(false);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [inProgress, setInProgress] = useState({});
  const [document, setDocument] = useState(null);
 
  useEffect(() => {
    if (selectedMealPlan || mealPlans.length) return;

    fetchMealPlans();
  }, [])

  const fetchMealPlans = async () => {
    const { data: fetchedMealPlans } = await axios({
      method: 'GET',
      url: apiMealPlansPath({ format: 'json' }),
    });
    if (fetchedMealPlans?.length) {
      setMealPlans((state) => (
        [
          ...(!!fetchedMealPlans?.length ? [{ name: 'None', id: ''}] : []),
          ...fetchedMealPlans
        ]
      ));
    }    
  }

  let mealPlanOptions = []
  if (mealPlans.length) {
    mealPlanOptions = mealPlans.map((mealPlan) => ({
      label: mealPlan.name,
      value: mealPlan.id,
    }));
  }

  function handleDateChange(newDates) {
    setFetchedOrders([]);
    setNoOrdersFetched(false);
    const [start, end] = newDates;
    setParams((state) => ({
      ...state,
      from_date: start,
      to_date: end,
    }));
  }

  const handleDropDownChange = ({ value, paramKey, clear }) => {
    setFetchedOrders([]);
    setNoOrdersFetched(false);
    setParams((state) => {
      if (paramKey === 'supplier_ids[]') {
        delete state.supplier_ids;
      }
      return { ...state, [paramKey]: value };
    });
    if (clear)
      setParams((state) => {
        delete state[paramKey];
        return state;
      });
  };

  const fetchOrders = async () => {
    if (inProgress.orders) return;

    setInProgress((state) => ({
      ...state,
      orders: true,
    }));
    try {
      const { data: orders } = await axios({
        method: 'GET',
        url: apiOrdersPath({ format: 'json' }),
        params: {
          ...params,
          ...(params.order_type === 'all' && { order_type: null }),
          from_date: moment(params.from_date).format('YYYY-MM-DD'),
          to_date: moment(params.to_date).format('YYYY-MM-DD'),
          statuses: EXPORTABLE_ORDER_STATUSES,
        },
      });
      setFetchedOrders(orders);
      setSelectedOrders(orders);
      if (!orders.length) {
        setNoOrdersFetched(true);
      }
      setInProgress((state) => ({
        ...state,
        orders: false,
      }));
    } catch (err) {
      alert('Sorry Something Went Wrong');
      setInProgress((state) => ({
        ...state,
        orders: false,
      }));
    }
  }

  const generateReport = async ({variation}) => {
    if (inProgress['pdf-normal'] || inProgress['pdf-no-pricing']) return;

    setDocument(null);
    setInProgress((state) => ({
      ...state,
      [`pdf-${variation}`]: true,
    }));

    const order_ids = selectedOrders.map((order) => order.id);
    let urlParams = `variation=${variation}`;
    if (params.meal_plan_id) {
      urlParams += `&meal_plan_id=${params.meal_plan_id}`  
    }
    order_ids.forEach((orderID) => {
      urlParams += `&order_ids[]=${orderID}`
    });
    window.location = `${exportPdfAPIOrdersPath({ format: 'pdf' })}?${urlParams}`;

    // eventually return to not in progress
    const waitTime = order_ids.length * 3000;
    setTimeout(() => {
      setInProgress((state) => ({
        ...state,
        [`pdf-${variation}`]: false,
      }));
    }, waitTime);
  }

  const generateReportInBackground = async ({variation}) => {
    try {
      const order_ids = selectedOrders.map((order) => order.id);
      const { data: generatedDocument } = await axios({
        method: 'GET',
        url: exportPdfAPIOrdersPath({ format: 'json' }),
        params: {
          order_ids,
          ...(params.meal_plan_id ? { meal_plan_id: params.meal_plan_id } : {}),
          variation
        },
      });
      setDocument(generatedDocument);
      window.open(generatedDocument.url, '_blank');
      setInProgress((state) => ({
        ...state,
        [`pdf-${variation}`]: false,
      }));
    } catch (err) {
      alert('Something went wrong! Please try again.');
      setInProgress((state) => ({
        ...state,
        [`pdf-${variation}`]: false,
      }));
    }
  }

  return (
    <>
      <div className="overlay show" onClick={() => setExportPdf(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <h3 className="report-modal-heading">Export Orders PDF</h3>

          <div className="dashboard-filter dashboard-filter__datepicker">
            <span className="between-flex">For Dates: </span>
            <div className="between-flex">
              <DatePicker
                startDate={params.from_date}
                endDate={params.to_date}
                selected={params.from_date}
                onChange={(newDates) => handleDateChange(newDates)}
                dateFormat="dd/MM/yyyy"
                selectsRange
                className="dashboard-filter"
              />
            </div>
          </div>
          <p className='dashboard-filters'>
            <DropDown
              options={orderTypeOptions}
              onChange={({ value }) =>
                handleDropDownChange({
                  value,
                  paramKey: 'order_type',
                  clear: value === 'all',
                })
              }
              className="dashboard-filter customer-order-dropdown customer-order-dropdown--order-type"
              menuClassName="dropdown"
              controlClassName={`dashboard-filter__type dashboard-filter__type--${params.order_type}`}
              placeholder="Type"
            />
          </p>
          {!!mealPlans.length && (
            <p className='dashboard-filters'>
              <DropDown
                options={mealPlanOptions}
                onChange={({ value }) =>
                  handleDropDownChange({
                    value,
                    paramKey: 'meal_plan_id',
                    clear: value === '',
                  })
                }
                className="dashboard-filter customer-order-dropdown customer-order-dropdown--order-type"
                menuClassName="dropdown"
                controlClassName={`dashboard-filter__type`}
                placeholder="Meal Plan"
              />
            </p>
          )}
          {!!customerSuppliers.length && (
            <p className='dashboard-filters'>
              <DropDown
                options={customerSuppliers}
                onChange={({ value }) =>
                  handleDropDownChange({
                    value,
                    paramKey: 'supplier_ids[]',
                    clear: value === 'all',
                  })
                }
                className="dashboard-filter customer-order-dropdown"
                menuClassName="dropdown"
                controlClassName="dashboard-filter__supplier"
                placeholder='Supplier'
              />
            </p>
          )}
          <a className="button tiny hollow" onClick={fetchOrders}>
            {inProgress.orders ? 'Fetching Orders...' : 'Fetch Orders'}
          </a>
          {!fetchedOrders.length && noOrdersFetched && <p className='is-invalid-label'><strong>No Orders found for the dates</strong></p>}
          {!!fetchedOrders.length && (
            <>
              <h2>Fetched {fetchedOrders.length} orders</h2>
              <ul className='m-0'>
                {fetchedOrders.map((order) => <ExportListOrder order={order} selectedOrders={selectedOrders} setSelectedOrders={setSelectedOrders} />)}
              </ul>
              {!!selectedOrders.length && (
                <>
                  <h3 className='mt-2'>Download PDF for {selectedOrders.length} orders</h3>
                  <div className='between-flex'>
                    <a className="button upcase" onClick={() => generateReport({ variation: 'normal' })}>
                      {inProgress['pdf-normal'] ? 'Generating...' : `With Pricing`} 
                    </a>
                    <a className="button hollow upcase" onClick={() => generateReport({ variation: 'no-pricing' })}>
                      {inProgress['pdf-no-pricing'] ? 'Generating...' : `Without Pricing`} 
                    </a>
                  </div>
                  {!!document && (
                    <p className='text-center'>
                      <a className='button black-btn' href={document.url} target='_blank'>
                        View Document
                      </a>
                    </p>
                  )}
                </>
              )}
            </>
          )}
        </div>
      </div>
    </>
  )
}

const ExportListOrder = ({order, selectedOrders, setSelectedOrders}) => {
  const isSelected = selectedOrders.find((updatableOrder) => updatableOrder.id == order.id);
  const handleChange = (event) => {
    if (event.target.checked) {
      setSelectedOrders((state) => [...state, order])
    } else {
      setSelectedOrders((state) => state.filter((stateOrder) => stateOrder.id != order.id) )
    }
  }
  const supplier = order.suppliers[0]
  return (
    <li className="meal-event" key={`pending-order-item-${order.id}`} style={{ justifyContent: 'space-between' }}>
      {supplier?.image && <img src={supplier.image} alt={supplier.name} title={supplier.name} className="event-image" />}
      <span>#{order.id}</span>
      <span className={`event-type ${order.status}`} />
      <div className='event-time'>
        {moment(order.delivery_date).format('DD-MM-YYYY')}
      </div>
      <label className="drop-text admin-action" style={{ marginLeft: '12px' }}>
        <input
          type="checkbox"
          className="checkbox-content"
          name="active"
          checked={isSelected}
          onChange={handleChange}
        />
        <span
          className="checkbox-content-tick"
          style={{ minWidth: '20px', height: '20px' }}
        />
      </label>
    </li>
  )
}

ExportPdfForm.propTypes = {
  setExportPdf: PropTypes.func.isRequired,
}

export default ExportPdfForm;