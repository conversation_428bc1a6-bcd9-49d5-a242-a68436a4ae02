import { useDispatch } from 'react-redux';
import { Modal } from 'react-responsive-modal';

import { changeOrderStatus } from 'actions/orderActions';
import mealPlansStore from 'store/mealPlansStore';

const OrderListModal = ({ openModal, setOpenModal, modalInfo, forCalendar }) => {
  const dispatch = !forCalendar ? useDispatch() : null;
  const changeOrderStatusMealPlan = mealPlansStore((state) => state.changeOrderStatus);

  const handleClick = () => {
    if (modalInfo.redirect) return;

    if (forCalendar) {
      changeOrderStatusMealPlan({
        apiEndpoint: modalInfo.url,
        reactivate: modalInfo.reactivate,
      });
    } else {
      dispatch(
        changeOrderStatus({
          apiEndpoint: modalInfo.url,
          reactivate: modalInfo.reactivate,
        })
      );
    }
    setOpenModal(false);
  };

  return (
    <Modal
      open={openModal}
      onClose={() => setOpenModal(false)}
      center
      classNames={{ modal: 'reveal customer-order-modal' }}
    >
      {!!modalInfo.component && modalInfo.component}
      {!modalInfo.component && (
        <>
          <h2 className="modal-title">{modalInfo.label}</h2>
          <p style={{ marginBottom: '30px' }}>{modalInfo.message}</p>
          <div className="form-footer">
            <a className="button gray-btn small" style={{ marginBottom: '0' }} onClick={() => setOpenModal(false)}>
              {modalInfo.url ? 'Cancel' : 'Ok'}
            </a>
            {modalInfo.url && (
              <a
                className="button small ml-1"
                style={{ marginBottom: '0' }}
                href={modalInfo.redirect && modalInfo.url}
                onClick={handleClick}
              >
                {modalInfo.type === 'continuation' ? 'Continue' : 'Confirm'}
              </a>
            )}
          </div>
        </>
      )}
    </Modal>
  );
};

export default OrderListModal;
