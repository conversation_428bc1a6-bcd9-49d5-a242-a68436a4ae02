import { useDispatch } from 'react-redux';

import { changeOrderStatus } from 'actions/orderActions';
import mealPlansStore from 'store/mealPlansStore';

const OrderCancelLinks = ({ order, setOpenModal, forCalendar }) => {
  const dispatch = !forCalendar ? useDispatch() : null;
  const changeOrderStatusMealPlan = mealPlansStore((state) => state.changeOrderStatus);

  const handleCancelLink = (link) => {
    if (forCalendar) {
      changeOrderStatusMealPlan({
        apiEndpoint: link.url,
        reactivate: link.type.includes('reactivate'),
      });
    } else {
      dispatch(
        changeOrderStatus({
          apiEndpoint: link.url,
          reactivate: link.type.includes('reactivate'),
        })
      );
    }

    setOpenModal(false);
  };

  return (
    <>
      <h2 className="modal-title text-center">Cancellation Option</h2>
      <div className="cancel-link-options">
        {order.cancel_links.map((link) => (
          <a
            key={`order-${order.id}-cancel-link-${link.type}`}
            className="button small primary-btn bordered ml-1 mb-1"
            onClick={() => handleCancelLink(link)}
          >
            {link.label}
          </a>
        ))}
      </div>
    </>
  );
};

export default OrderCancelLinks;
