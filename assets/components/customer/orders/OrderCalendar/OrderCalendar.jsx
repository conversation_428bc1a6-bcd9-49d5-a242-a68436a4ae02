import { useEffect } from 'react';
import moment from 'moment';

import mealPlanStore from 'store/mealPlansStore';
import MealPlanCalendar from 'components/customer/meal_plans/calendar/MealPlanCalendar';
import { filterOrdersForCalendar } from 'utilities/calendarHelpers';

moment.locale('en-gb');

const OrderCalendar = ({ params }) => {
  const { fetchEvents, startDate, orders } = mealPlanStore((state) => ({
    fetchEvents: state.fetchEvents,
    startDate: state.startDate,
    orders: state.orders,
  }));

  useEffect(async () => {
    await fetchEvents({ forOrders: true, initialLoad: true, from_date: startDate });
  }, []);

  const filteredOrders = filterOrdersForCalendar(orders, params);

  return (
    <>
      <div>
        <MealPlanCalendar
          forOrders
          filteredOrders={filteredOrders}
        />
      </div>
    </>
  );
};

export default OrderCalendar;
