import { useContext } from 'react';

import appContext from 'contexts/appContext';

const CalendarOrderCreatePopup = ({ dateParam, showDate }) => {
  const { externalOrderUrls } = useContext(appContext);

  return (
    <div className="popup-order-create">
      <p style={{ marginBottom: 0, fontWeight: 'bold' }}>{showDate}</p>
      <a className="order-link catering" href={`${externalOrderUrls.catering}?for_date=${dateParam}T12:00`}>
        New Catering Order
      </a>
      <a className="order-link snacks" href={`${externalOrderUrls.pantry}?for_date=${dateParam}T07:00`}>
        New Pantry Order
      </a>
    </div>
  );
};

export default CalendarOrderCreatePopup;
