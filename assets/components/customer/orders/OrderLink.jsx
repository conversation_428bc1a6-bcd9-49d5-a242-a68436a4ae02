import generateOrderConfirmMessage from 'utilities/generateOrderConfirmMessage';
import checkLeadTime from 'utilities/checkLeadTime';
import OrderCancelLinks from './OrderCancelLinks';

const OrderLink = ({
  order,
  day,
  isAdmin,
  isRecurringOrder,
  link,
  setModalInfo,
  setOpenModal,
  setShowOrderLinks,
  forCalendar,
}) => {
  const { url, type, label } = link;

  const checkOrderLeadTime = async () =>
    checkLeadTime({
      orderID: order.id,
      url,
      isAdmin,
    });

  const handleDirectLinkClick = (e) => {
    setShowOrderLinks(false);
  };

  const handleEditLink = async (e) => {
    e.preventDefault();
    const { canProcess, leadTimeModalOptions } = await checkOrderLeadTime();
    if (!canProcess) {
      openModalWithOptions(leadTimeModalOptions);
    } else {
      setShowOrderLinks(false);
      window.location.href = url;
    }
  };

  const handleConfirmationlLink = async (e) => {
    e.preventDefault();
    const { canProcess, leadTimeModalOptions } = await checkOrderLeadTime();
    if (!canProcess) {
      openModalWithOptions(leadTimeModalOptions);
    } else {
      const modalOptions = {
        url,
        message: generateOrderConfirmMessage({
          isRecurringOrder,
          day,
          type,
        }),
        reactivate: type.includes('reactivate'),
        label,
      };
      openModalWithOptions(modalOptions);
    }
  };

  const handleCancelLink = async (e) => {
    e.preventDefault();
    const { canProcess, leadTimeModalOptions } = await checkOrderLeadTime();
    if (!canProcess) {
      openModalWithOptions(leadTimeModalOptions);
    } else {
      const modalOptions = {
        component: <OrderCancelLinks order={order} setOpenModal={setOpenModal} forCalendar={forCalendar} />,
      };
      openModalWithOptions(modalOptions);
    }
  };

  const openModalWithOptions = (modalOptions) => {
    setOpenModal(true);
    setModalInfo(modalOptions);
  };

  const handlers = {
    view: handleDirectLinkClick,
    'view-package': handleDirectLinkClick,
    clone: handleDirectLinkClick,
    edit: handleEditLink,
    approve: handleEditLink,
    'cancel-options': handleCancelLink,
    'reactivate-one-off': handleConfirmationlLink,
    'reactivate-subsequent': handleConfirmationlLink,
    'cancel-one-off': handleConfirmationlLink,
    'cancel-subsequent': handleConfirmationlLink,
    'cancel-related': handleConfirmationlLink,
    'cancel-on-hold': handleConfirmationlLink,
    'cancel-void': handleConfirmationlLink,
    'cancel-and-notify': handleConfirmationlLink,
  };

  return (
    <a href={url} onClick={handlers[type]}>
      {label}
    </a>
  );
};

export default OrderLink;
