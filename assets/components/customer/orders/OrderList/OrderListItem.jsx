import { useEffect, useRef, useState } from 'react';
import ReactTooltip from 'react-tooltip';

import SupplierIcon from 'components/customer/SupplierIcon';
import OrderLink from '../OrderLink';

const OrderListItem = ({ order, setOpenModal, setModalInfo, isAdmin }) => {
  const [showOrderLinks, setShowOrderLinks] = useState(false);

  const wrapperRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setShowOrderLinks(false);
      }
    }

    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [wrapperRef]);

  const viewLink = order.links && order.links.find((link) => link.type == 'view');
  const approveLink = order.links && order.links.find((link) => link.type == 'approve');
  const cloneLink = order.links && order.links.find((link) => link.type === 'clone');
  const savedWoolworthsOrder = order.status == 'saved' && order.is_woolworths_order

  return (
    <div className="order-list-item">
      <a className="view-slider-link" href={viewLink && viewLink.url} data-modal-view="true" />
      <ReactTooltip place="bottom" effect="solid" />
      <span className="order-list-item__suppliers list-flex-2">
        {order.suppliers.map((supplier) => (
          <SupplierIcon key={`order-${order.id}-supplier-image-${supplier.id}`} supplier={supplier} />
        ))}
      </span>
      <span className="order-list-item__id list-flex-2">#{order.id}</span>
      <span className="order-list-item__date list-flex-4">{order.delivery_at}</span>
      <span className="order-list-item__name list-flex-5">{order.name}</span>
      <span className="order-list-item__total list-flex-2">{order.customer_total}</span>
      <span
        className={`order-list-item__type list-flex-1 ${order.is_team_order ? 'team-order' : order.order_type}-icon`}
      />
      <span className={`order-list-item__status list-flex-2 ${order.status}`}>
        {order.status === 'paused' ? 'On Hold' : order?.status?.charAt(0)?.toUpperCase() + order?.status?.slice(1)}
      </span>

      <div className="list-flex-2" style={{ textAlign: 'center' }}>
        {approveLink && (
          <a
            className="reorder-button"
            href={approveLink.url}
            onClick={(e) => e.stopPropagation()}
            style={{ position: 'relative', zIndex: 2 }}
          >
            approve
          </a>
        )}
        {!approveLink && cloneLink && (
          <a
            className="reorder-button"
            href={cloneLink.url}
            onClick={(e) => e.stopPropagation()}
            style={{ position: 'relative', zIndex: 2 }}
          >
            {savedWoolworthsOrder ? 'place order' : 'reorder'}
          </a>
        )}
      </div>

      <div className="list-flex-1 order-list-item__options" ref={wrapperRef}>
        <button className="order-list-item__options-button" onClick={() => setShowOrderLinks((state) => !state)} />
        {showOrderLinks && (
          <div className="order-list-item__options-dropdown">
            {order.links.map((link, lidx) => (
              <OrderLink
                key={`order-${order.id}-link-${lidx}`}
                order={order}
                day={order.delivery_day}
                isAdmin={isAdmin}
                isRecurringOrder={order.order_type === 'recurrent'}
                link={link}
                setOpenModal={setOpenModal}
                setModalInfo={setModalInfo}
                setShowOrderLinks={setShowOrderLinks}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderListItem;
