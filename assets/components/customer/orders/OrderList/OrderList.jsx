import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import useInfiniteScroll from 'react-infinite-scroll-hook';

import { createLoadingSelector } from 'selectors';
import { fetchOrders } from 'actions/orderActions';
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';

import { hasCachedOrders } from 'utilities/orderKeySanitizing';

// components
import OrderListSkeleton from './OrderListSkeleton';
import OrderListHeadings from './OrderListHeadings';
import OrderListItem from './OrderListItem';
import OrderListModal from '../OrderListModal';
import NoOrders from './NoOrders';

const OrderList = ({ params }) => {
  const { isAdmin } = useContext(adminContext);
  const { externalOrderUrls, onlyQuotes } = useContext(appContext);

  const dispatch = useDispatch();
  const loadingSelector = createLoadingSelector(['FETCH_ORDERS']);
  const loadingState = useSelector((state) => state.loading);
  const loading = loadingSelector(loadingState);

  const infiniteLoadingSelector = createLoadingSelector(['FETCH_MORE_ORDERS']);
  const infiniteLoadingState = useSelector((state) => state.loading);
  const infiniteLoading = infiniteLoadingSelector(infiniteLoadingState);

  const [openModal, setOpenModal] = useState(false);
  const [modalInfo, setModalInfo] = useState('');

  const {
    orders: allOrders,
    hasMoreOrders,
    pages,
    currentOrders,
    initialLoad,
  } = useSelector((state) => state.customer);
  const cachedKey = hasCachedOrders(Object.keys(allOrders), params, false);
  const { orders } = currentOrders;

  useEffect(async () => {
    await dispatch(
      fetchOrders({
        params,
        cachedKey,
        isCalendarView: false,
      })
    );
  }, [params.query, params.order_type, params['supplier_ids[]']]);

  const [sentryRef] = useInfiniteScroll({
    loading: infiniteLoading,
    hasNextPage: hasMoreOrders[currentOrders.key],
    onLoadMore: async () => {
      if (orders.length && !loading) {
        await dispatch(
          fetchOrders({
            params: { ...params, page: pages[currentOrders.key] },
            infiniteLoading: true,
            isCalendarView: false,
          })
        );
      }
    },
  });

  return (
    <div>
      {onlyQuotes && !!orders.length && (
        <a href={externalOrderUrls.quote} className="button black-btn">
          Create New Quote
        </a>
      )}
      {!!orders.length && <OrderListHeadings />}
      {!loading && !orders.length && !initialLoad && <NoOrders />}
      {!loading &&
        orders.map((order) => (
          <OrderListItem
            order={order}
            key={`order-list-item-${order.id}`}
            setOpenModal={setOpenModal}
            setModalInfo={setModalInfo}
            isAdmin={isAdmin}
          />
        ))}
      <OrderListModal openModal={openModal} setOpenModal={setOpenModal} modalInfo={modalInfo} />
      <div ref={sentryRef}>{(loading || infiniteLoading) && <OrderListSkeleton />}</div>
    </div>
  );
};

export default OrderList;
