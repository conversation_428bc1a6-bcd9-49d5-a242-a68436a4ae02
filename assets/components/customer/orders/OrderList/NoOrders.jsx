import { useContext, useEffect, useRef, useState } from 'react';

import appContext from 'contexts/appContext';
import { customerNewTeamOrdersPath } from 'routes';
import CateringStar from 'images/illustrations/catering-star.svg';

const NoOrders = () => {
  const [tooltipOpen, setTooltipOpen] = useState(false);
  const tooltipRef = useRef(null);

  const { externalOrderUrls, onlyQuotes } = useContext(appContext);

  const handleClickOutside = (event) => {
    if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {
      setTooltipOpen(false);
    }
  };
  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);
  const toggleTooltip = () => {
    setTooltipOpen(!tooltipOpen);
  };
  return (
    <div className="no-orders">
      <img src={CateringStar} />
      <p>{onlyQuotes ? 'No Quotes Found' : 'No Orders Found'}</p>
      <div className="options-toggle" ref={tooltipRef}>
        {!onlyQuotes && (
          <button className="button black-btn" onClick={toggleTooltip}>
            Create New Order
          </button>
        )}
        {onlyQuotes && (
          <a href={externalOrderUrls.quote} className="button black-btn">
            Create New Quote
          </a>
        )}
        {tooltipOpen && (
          <div className="tooltip">
            <a href={externalOrderUrls.catering}>Catering Order</a>
            <a href={externalOrderUrls.pantry}>Pantry Order</a>
            <a href={customerNewTeamOrdersPath()}>Team Order</a>
            <a href={externalOrderUrls.quote}>Quote</a>
          </div>
        )}
      </div>
    </div>
  );
};

export default NoOrders;
