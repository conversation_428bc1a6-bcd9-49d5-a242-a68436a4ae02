# ENV Variables
IS_STAGING
TZ
HEROKU_APP_ID
HEROKU_APP_NAME
HEROKU_POSTGRESQL_MAUVE_URL
HEROKU_RELEASE_CREATED_AT
HEROKU_RELEASE_VERSION
HEROKU_SLUG_COMMIT
HEROKU_SLUG_DESCRIPTION
LANG
MEMCACHIER_PASSWORD
MEMCACHIER_SERVERS
MEMCACHIER_USERNAME
PAPERTRAIL_API_TOKEN
RACK_ENV
RACK_TIMEOUT_SERVICE_TIMEOUT
RAILS_ENV
SECRET_KEY_BASE
SKYLIGHT_AUTHENTICATION
SKYLIGHT_ENV

#Rails Credentials
supplier_closure_email_threshold:
customer_closure_email_threshold:
final_closure_email_threshold:
customer_order_ids_not_to_show:

default_host:

disable:
  faraday_logging:
  welcome_email:

active_campaign:
  customer_list_id:
  supplier_list_id:
  lead_list_id:

docusign:
  field_list:
  yordar_role_name:
  supplier_role_name:

google:
  api_key:
  maps_api_key:

woolworths:
  api_key:
  supplier_profile_id:
  yordar_account_email:
  yordar_account_password:

yordar:
  accounts_email:
  admin_email:
  error_recipient_email:
  order_check_email:
  orders_email:
  supplier_email:
  gst_percent:
  closure_start_date:
  closure_end_date:

  review_invitation_start_date:
  report_folder_path:

eway:
  login:
  password:
  username:

stripe:
  migration_date:

calendarific:
  api_key:

fixie:
  url:

slack:
  webhook_url:

sentry_dns:

# Used as the base secret for all MessageVerifiers in Rails, including the one protecting cookies.
secret_key_base:

development:
  next_app_subdomain:
  next_app_secret:

  docusign:
    auth_server:
    integrator_key:
    redirect_uri:
    rest_host:
    user_id:
    template_id:
    webhook_url:
    yordar_email:
    yordar_name:
    private_key:

  xero:
    key:
    secret:
    customer_gst_free:
    customer_non_gst_free:
    supplier_gst_free:
    supplier_non_gst_free:
    surcharge_code:
    delivery_code:
    discount_code:

  eway:
    login:
    password:
    username:

  stripe:
    api_key:
    publishable_key:
    slack_notification:
    webhook_secret:

  slack:
    webhook_url:

  woolworths:
    host:
    pricing_check:

  sendgrid:
    username:
    password:

staging:
  is_staging:
  <<:
  slack:
    webhook_url:
  yordar:
    accounts_email:
    admin_email:
    order_check_email:
    orders_email:
    supplier_email:

production:
  rails_subdomain:
  prismic_subdomain:

  sentry_dns:

  docusign:
    auth_server:
    integrator_key:
    redirect_uri:
    rest_host:
    user_id:
    template_id:
    webhook_url:
    yordar_email:
    yordar_name:
    private_key:

  xero:
    key:
    secret:
    customer_gst_free:
    customer_non_gst_free:
    supplier_gst_free:
    supplier_non_gst_free:
    surcharge_code:
    delivery_code:
    discount_code:

  eway:
    login:
    password:
    username:

  stripe:
    api_key:
    migration_date:
    publishable_key:
    slack_notification:
    webhook_secret:

  slack:
    webhook_url:

  woolworths:
    host:
    pricing_check:

  sendgrid:
    username:
    password:

  active_campaign:
    api_key:
    api_url:
