=== Welcome to YORDAR App

== Getting Started
1. At the command prompt, install the bundle:
       bundle install (from the application directory)

2. Setup all dev database (for the first time): (make sure postgres server is running locally with postgres user) (check config/database.yml for database config)
       rake db:drop db:create db:migrate

3. Install node modules, run
       yarn

4. Load sample data (for the first time):
       rake db:seed

4.1. Load sample data (from production, requires heroku cli with login):
       live_data=true rake db:seed

5. Setup the migration for test db
       RAILS_ENV=test rake db:schema:load

5.1 Run rspec tests
       rspec

6 To Run server
       On one tab run
              rails s
       On another tab run (as we have webpack setup for the app)
              ./bin/webpack-dev-server


== Rake Tasks (optional):
* Import some suburbs with an optional limit. it also removes suburbs in postcodes starting with 1
       rake import:suburbs_with_geo[400]

== Heroku Release
* Login to Heroku (only necessary once)
       heroku login

* If you have not already add the heroku remote repository to your local branch (only necessary once):
       heroku git:remote -a yordar

* To push from master to heroku (Ensure you have the latest css files using compass compile and commit these into master):
       git push heroku master

* If you have database changes (ensure that there are default values for new columns if necessary!):
       heroku run rake db:migrate

* If you need to run and individual command use the "heroku run" command (to load data)
       heroku run rake load:reboot -a yordar

* If you need to run and individual seed file (to load data)
       heroku run rake db:seed:categories -a yordar (the seed file name)

* To restart the dynos (similar to restarting "rails s"):
       heroku restart -a yordar

