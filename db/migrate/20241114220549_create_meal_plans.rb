class CreateMealPlans < ActiveRecord::Migration[6.1]
  def change
    create_table :meal_plans do |t|
      t.references :customer_profile, foreign_key: true
      t.string :kind
      t.string :name
      t.datetime :delivery_time
      t.string :delivery_address_level
      t.string :delivery_address
      t.integer :delivery_suburb_id
      t.text :delivery_instruction
      t.integer :number_of_people
      t.integer :cpo_id
      t.integer :gst_free_cpo_id
      t.integer :credit_card_id
      t.string :uuid

      t.timestamps
    end
  end
end
