class DeprecateCustomerProfileBillingFields < ActiveRecord::Migration[5.2]
  def change
    # deprecated billing fields - moved to billing details
    remove_column :customer_profiles, :billing_frequency_pref, default: 'instantly'
    remove_column :customer_profiles, :requires_order_summaries
    remove_column :customer_profiles, :requires_summary_report, default: false
    remove_column :customer_profiles, :hide_pricing_and_gst
    remove_column :customer_profiles, :send_invoice_reports, default: false
    remove_column :customer_profiles, :invoice_order_grouping, default: 'address_po'

    # deprecated fields
    remove_column :customer_profiles, :old_id
    remove_column :customer_profiles, :notification_type, default: "all"
  end
end
