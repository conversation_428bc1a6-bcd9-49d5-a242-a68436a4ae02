class RemoveEmailTemplatesFromApp < ActiveRecord::Migration[5.2]
  def change
    remove_column :emails, :email_template_id, :integer
    remove_column :notification_preferences, :email_template_id, :integer, index: true

    drop_table :email_templates do |t|
      t.string 'name', limit: 255
      t.string 'subject', limit: 255
      t.text 'html'
      t.text 'text'
      t.datetime 'created_at', null: false
      t.datetime 'updated_at', null: false
      t.string 'cc', limit: 255
      t.string 'required_variables', default: [], array: true
      t.string 'sendgrid_id'
    end
  end
end
