class CreateBillingDetails < ActiveRecord::Migration[5.2]
  def change
    create_table :billing_details do |t|
      t.references :customer_profile, foreign_key: true
      t.string :name
      t.string :email      
      t.string :address
      t.references :suburb, foreign_key: true
      t.string :phone
      t.string :frequency, default: 'instantly'
      t.boolean :order_summaries, default: false
      t.boolean :summary_report, default: false
      t.boolean :invoice_reports, default: false
      t.boolean :hide_pricing_and_gst, default: false
      t.string :invoice_order_grouping, default: 'address_po'

      t.timestamps
    end
  end
end
