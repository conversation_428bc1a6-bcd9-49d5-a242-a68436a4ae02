class CreateSupplierInvoices < ActiveRecord::Migration[5.2]
  def change
    create_table :supplier_invoices do |t|
      t.references :supplier_profile
      t.string :number
      t.datetime :from_at
      t.datetime :to_at
      t.datetime :due_at
      t.decimal :amount, precision: 10, scale: 2, default: 0.0
      t.boolean :pushed_to_xero, default: false      
      t.string :payment_status, default: 'unpaid'
      t.string :uuid

      t.timestamps
    end
  end
end
