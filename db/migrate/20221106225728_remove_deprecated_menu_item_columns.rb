class RemoveDeprecatedMenuItemColumns < ActiveRecord::Migration[5.2]
  def change
    remove_column :menu_items, :delivery_lead_time, :decimal, precision: 10, scale: 2
    remove_column :menu_items, :num_per_pallet, :integer

    # menu extra based columns
    remove_column :menu_extras, :section_name, :string

    remove_column :menu_items, :restrict_extras, :boolean
    remove_column :menu_items, :section_1_description, :string
    remove_column :menu_items, :section_2_description, :string
    remove_column :menu_items, :section_3_description, :string
    remove_column :menu_items, :section_4_description, :string
    remove_column :menu_items, :section_1_limit, :integer     
    remove_column :menu_items, :section_2_limit, :integer     
    remove_column :menu_items, :section_3_limit, :integer     
    remove_column :menu_items, :section_4_limit, :integer    
  end
end
