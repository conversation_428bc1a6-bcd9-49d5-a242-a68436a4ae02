class DeprecateAttachments < ActiveRecord::Migration[5.2]
  def change
    drop_table :attachments do |t|
      t.string :caption
      t.integer :attachable_id
      t.string :attachable_type
      t.datetime :created_at
      t.datetime :updated_at
      t.string :file_file_name
      t.string :file_content_type
      t.integer :file_file_size
      t.datetime :file_updated_at
      t.string :file
    end

    remove_column :coupons, :attachments, :text, default: '{}', null: false
  end
end
