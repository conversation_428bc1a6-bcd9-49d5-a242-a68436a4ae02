class CreateDearAccounts < ActiveRecord::Migration[5.2]
  def change
    create_table :dear_accounts do |t|
      t.references :supplier_profile, foreign_key: true
      t.string :account_id, null: false
      t.string :api_key, null: false
      t.string :customer_id, null: false
      t.boolean :active, default: true
      t.string :price_tier
      t.string :dietary_attribute

      t.timestamps
    end
  end
end
