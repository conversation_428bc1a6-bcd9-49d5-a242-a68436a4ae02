class InitSchema < ActiveRecord::Migration[5.0]
  def up
    # These are extensions that must be enabled in order to support this database
    enable_extension 'plpgsql'
    enable_extension 'pg_stat_statements'

    create_table 'attachments' do |t|
      t.string   'caption'
      t.string   'attachable_type'
      t.integer  'attachable_id'
      t.string   'file_file_name'
      t.string   'file_content_type'
      t.bigint   'file_file_size'
      t.datetime 'file_updated_at'
      t.string   'file'
      t.timestamps
    end

    create_table 'billing_addresses' do |t|
      t.integer 'customer_profile_id'
      t.string  'name'
      t.string  'email'
      t.string  'phone'
      t.text    'address'
      t.integer 'suburb_id'
    end

    create_table 'categories' do |t|
      t.string   'name'
      t.string   'slug'
      t.string   'group'
      t.text     'body' # deprecated
      t.string   'keywords' # deprecated
      t.text     'teaser' # deprecated
      t.boolean  'show_in_menu'
      t.boolean  'show_in_homepage'
      t.integer  'group_weight'
      t.integer  'weight'
      t.string   'picture'
      t.timestamps
    end

    create_table 'category_menu_sections' do |t|
      t.integer  'category_id'
      t.integer  'menu_section_id'
      t.integer  'weight'
      t.timestamps
    end

    create_table 'companies' do |t|
      t.string   'name'
      t.string   'email'
      t.text     'address'
      t.integer  'suburb_id'
      t.string   'contact_name'
      t.string   'contact_phone'
      t.integer  'ABN_ACN'
      t.integer  'old_id' # do we really need this, only 1 with valid record
      t.integer  'payment_term_days'
      t.boolean  'invoice_by_po', default: false
      t.boolean  'requires_po'
      t.boolean  'can_pay_on_account'
      t.boolean  'can_pay_by_credit_card', default: true
      t.timestamps
    end

    create_table 'companies_credit_cards', id: false do |t|
      t.integer 'company_id'
      t.integer 'credit_card_id'
    end

    create_table 'companies_menu_sections', id: false do |t|
      t.integer 'menu_section_id', null: false
      t.integer 'company_id', null: false
    end

    create_table 'coupon_redemptions', force: :cascade do |t|
      t.integer  'coupon_id',  null: false
      t.string   'user_id'
      t.string   'order_id'
      t.timestamps
    end

    create_table 'coupons', force: :cascade do |t|
      t.string   'code', null: false
      t.string   'description'
      t.date     'valid_from', null: false
      t.date     'valid_until'
      t.integer  'redemption_limit', default: 1, null: false
      t.integer  'coupon_redemptions_count', default: 0, null: false
      t.integer  'amount', default: 0, null: false
      t.string   'type', null: false
      t.text     'attachments', default: '{}', null: false
      t.timestamps
    end

    create_table 'credit_cards' do |t|
      t.string   'number'
      t.string   'name'
      t.string   'cvv' # should be removed
      t.string   'label'
      t.string   'brand'
      t.string   'country_code'
      t.integer  'expiry_month'
      t.integer  'expiry_year'
      t.integer  'event_attendee_id'
      t.string   'gateway_token'
      t.string   'stripe_token'
      t.boolean  'enabled', default: true
      t.boolean  'saved_for_future', default: true
      t.boolean  'pay_on_account', default: false
      t.boolean  'auto_pay_invoice', default: false
      t.timestamps
    end

    create_table 'credit_cards_customer_profiles', id: false do |t|
      t.integer 'customer_profile_id'
      t.integer 'credit_card_id'
    end

    create_table 'custom_order_suppliers' do |t|
      t.integer  'order_id'
      t.integer  'supplier_profile_id'
      t.datetime 'delivery_at'
      t.decimal  'delivery_fee', precision: 10, scale: 2
      t.text     'delivery_note'
      t.timestamps
    end

    create_table 'customer_admin_permissions' do |t|
      t.integer  'customer_admin_id'
      t.timestamps
    end

    create_table 'customer_profile_menu_items' do |t|
      t.integer  'customer_profile_id', null: false
      t.integer  'menu_item_id', null: false
      t.timestamps
      t.index ['customer_profile_id', 'menu_item_id'], name: 'unique_index_on_customer_profiles_menu_items', unique: true, using: :btree
      t.index ['customer_profile_id'], name: 'index_cpmi_on_customer_profile_id', using: :btree
      t.index ['menu_item_id'], name: 'index_cpmi_on_menu_item_id', using: :btree
    end

    create_table 'customer_profiles' do |t|
      t.string   'customer_name'
      t.string   'uuid'
      t.string   'stripe_token'
      t.string   'role'
      t.string   'company_name'
      t.integer  'company_id'
      t.string   'contact_phone'
      t.string   'mobile'
      t.string   'fax'
      t.string   'notes'
      t.text     'admin_notes'
      t.integer  'old_id' # do we really need this, only 9 with valid records
      t.integer  'customer_admin_permissions_id'
      t.string   'billing_frequency_pref', default: 'instantly'
      t.string   'notification_type', default: 'all' # deprecated
      t.string   'invoice_order_grouping', default: 'address_po'
      t.text     'favourite_supp', default: [], array: true
      t.integer  'favourite_team_supp', default: [], array: true
      t.boolean  'requires_order_summaries'
      t.boolean  'hide_pricing_and_gst' # no record with value of true
      t.boolean  'dont_receive_review_requests' # can be made into a notification preference
      t.boolean  'send_invoice_reports', default: false
      t.boolean  'team_admin', default: false
      t.boolean  'requires_summary_report', default: false
      t.boolean  'company_team_admin', default: false
      t.timestamps
    end

    create_table 'customer_profiles_supplier_profiles', id: false do |t|
      t.integer 'customer_profile_id'
      t.integer 'supplier_profile_id'
      t.index ['customer_profile_id', 'supplier_profile_id'], name: 'unique_index_on_customer_profiles_supplier_profiles', unique: true, using: :btree
      t.index ['customer_profile_id'], name: 'index_cpsp_on_customer_profile_id', using: :btree
      t.index ['supplier_profile_id'], name: 'index_cpsp_on_supplier_profile_id', using: :btree
    end

    create_table 'customer_profiles_users' do |t| # don't know if this is used
      t.integer 'customer_profile_id'
      t.integer 'user_id'
    end

    create_table 'customer_purchase_orders' do |t|
      t.integer  'customer_profile_id'
      t.string   'po_number'
      t.text     'description'
      t.decimal  'weekly_budget'
      t.timestamps
    end

    create_table 'delayed_jobs' do |t|
      t.integer  'priority', default: 0
      t.integer  'attempts', default: 0
      t.text     'handler'
      t.text     'last_error'
      t.datetime 'run_at'
      t.datetime 'locked_at'
      t.datetime 'failed_at'
      t.string   'locked_by'
      t.string   'queue'
      t.timestamps
      t.index ['priority', 'run_at'], name: 'delayed_jobs_priority', using: :btree
    end

    create_table 'delivery_zones' do |t|
      t.integer  'supplier_profile_id'
      t.integer  'suburb_id'
      t.integer  'radius'
      t.decimal  'delivery_fee', precision: 10, scale: 2
      t.string   'operating_wdays'
      t.integer  'operating_hours_start'
      t.integer  'operating_hours_end'
      t.timestamps
    end

    create_table 'email_schedules' do |t| # deprecated - no records
      t.string   'title'
      t.string   'interval'
      t.string   'email_recipients'
      t.text     'info'
      t.integer  'email_template_id'
      t.string   'report_type'
      t.datetime 'last_run'
      t.timestamps
    end
    create_table 'email_templates' do |t|
      t.string   'name'
      t.string   'subject'
      t.text     'html'
      t.text     'text' # deprecated
      t.string   'cc'
      t.timestamps
    end


    create_table 'emails' do |t|
      t.integer  'email_template_id'
      t.text     'recipient'
      t.text     'options'
      t.datetime 'sent_at'
      t.string   'ref'
      t.integer  'fk_id'
      t.timestamps
    end

    create_table 'event_attendees' do |t|
      t.integer  'team_admin_id'
      t.string   'first_name'
      t.string   'last_name'
      t.string   'email'
      t.string   'order_status', default: 'invited' # deprecated
      t.integer  'customer_profile_id' # deprecated as duplicate of team admin id
      t.boolean  'active', default: true
      t.timestamps
      t.index ['customer_profile_id'], name: 'index_event_attendees_on_customer_profile_id', using: :btree
    end

    create_table 'event_attendees_teams' do |t|
      t.integer 'event_attendee_id'
      t.integer 'event_team_id'
      t.index ['event_attendee_id'], name: 'index_event_attendees_teams_on_event_attendee_id', using: :btree
      t.index ['event_team_id'], name: 'index_event_attendees_teams_on_event_team_id', using: :btree
    end

    create_table 'event_teams' do |t|
      t.integer  'customer_profile_id'
      t.string   'name'
      t.boolean  'active', default: true
      t.timestamps
      t.index ['customer_profile_id'], name: 'index_event_teams_on_customer_profile_id', using: :btree
    end

    create_table 'external_documents' do |t|
      t.string   'doc_type'
      t.string   'url'
      t.text     'status'
      t.string   'rejected_reason'
      t.integer  'order_id'
      t.string   'ref_number'
      t.timestamps
    end

    create_table 'ffc_profiles' do |t|
      t.string   'business_name'
      t.string   'contact_name'
      t.text     'billing_address'
      t.integer  'billing_suburb_id'
      t.string   'email'
      t.string   'phone'
      t.timestamps
    end

    create_table 'freight_forwarding_rates' do |t|
      t.integer  'source_from_postcode'
      t.integer  'source_to_postcode'
      t.integer  'destination_from_postcode'
      t.integer  'destination_to_postcode'
      t.string   'freight_type', default: 'chilled'
      t.decimal  'price_per_pallet', precision: 10, scale: 2
      t.integer  'travel_time'
      t.integer  'ffc_profile_id'
      t.timestamps
      t.index ['ffc_profile_id'], name: 'idx_ffc_profile_id', using: :btree # maybe saved as `index_freight_forwarding_rates_on_freight_forwarding_company_id`
    end

    create_table 'groups' do |t| # deprecated
      t.string   'name'
      t.timestamps
    end

    create_table 'groups_permissions' do |t| # deprecated
      t.integer 'group_id'
      t.integer 'permission_id'
    end

    create_table 'groups_users' do |t| # deprecated
      t.integer 'group_id'
      t.integer 'user_id'
    end

    create_table 'holidays' do |t|
      t.string   'name'
      t.datetime 'on_date'
      t.datetime 'push_to'
      t.string   'state'
      t.datetime 'effective_from'
      t.datetime 'effective_to'
    end

    create_table 'invoices' do |t|
      t.string   'number'
      t.decimal  'amount_price', precision: 10, scale: 2
      t.decimal  'payment_value', precision: 10, scale: 2, default: '0.0'
      t.string   'payment_status', default: 'unpaid'
      t.boolean  'pushed_to_xero', default: false
      t.string   'uuid'
      t.timestamps
    end

    create_table 'leads' do |t|
      t.string   'lead_type'
      t.string   'progress'
      t.string   'email'
      t.integer  'user_id'
      t.integer  'ac_id'
      t.string   'firstname'
      t.string   'lastname'
      t.string   'company_name'
      t.string   'phone'
      t.timestamps
    end

    create_table 'locations' do |t|
      t.text     'details'
      t.string   'note'
      t.integer  'order_id'
      t.timestamps
      t.index ['order_id'], name: 'index_locations_on_order_id', using: :btree
    end

    create_table 'logfiles' do |t| # no idea what this is used for.. no records
      t.string 'name'
      t.string 'file'
    end

    create_table 'menu_extras' do |t|
      t.integer  'menu_item_id'
      t.string   'name'
      t.string   'price'
      t.integer  'weight'
      t.datetime 'archived_at'
      t.string   'section_name'
      t.timestamps
    end

    create_table 'menu_items' do |t|
      t.integer  'menu_section_id'
      t.integer  'supplier_profile_id'
      t.string   'name'
      t.text     'description'
      t.decimal  'price', precision: 10, scale: 2
      t.integer  'weight'
      t.integer  'minimum_quantity', default: 1
      t.integer  'sub_quantity'
      t.text     'image'
      t.datetime 'archived_at'
      t.string   'item_code'
      t.string   'section_1_description'
      t.string   'section_2_description'
      t.string   'section_3_description'
      t.string   'section_4_description'
      t.integer  'section_1_limit'
      t.integer  'section_2_limit'
      t.integer  'section_3_limit'
      t.integer  'section_4_limit'
      t.string   'account_code' # deprecated - used for Category Solutions
      t.integer  'num_per_pallet' # only 1 record has a value
      t.decimal  'delivery_lead_time', precision: 10, scale: 2 # only 2 records have a value
      t.string   'freight_type', default: 'chilled' # used for Category Solutions
      t.boolean  'restrict_extras' # deprecated
      t.boolean  'team_order_only'
      t.boolean  'team_order'
      t.boolean  'is_vegan', default: false
      t.boolean  'is_vegetarian', default: false
      t.boolean  'is_gluten_free', default: false
      t.boolean  'is_gst_free', default: false
      t.boolean  'is_hidden', default: false
      t.boolean  'is_dairy_free', default: false
      t.boolean  'is_individually_packed', default: false
      t.timestamps
    end

    create_table 'menu_sections' do |t|
      t.integer  'supplier_profile_id'
      t.string   'name'
      t.integer  'weight'
      t.datetime 'archived_at'
      t.string   'group_name'
      t.boolean  'is_hidden', default: false
      t.timestamps
    end

    create_table 'minimums' do |t|
      t.integer  'supplier_profile_id'
      t.integer  'category_id'
      t.decimal  'spend_price', precision: 10, scale: 2, default: '0.0'
      t.decimal  'lead_time', precision: 10, scale: 2, default: '0.0'
      t.string   'lead_time_day_before', default: ''
      t.timestamps
    end

    create_table 'notification_preferences' do |t|
      t.integer  'account_id'
      t.string   'account_type'
      t.integer  'email_template_id'
      t.string   'variation'
      t.string   'email_recipients'
      t.string   'salutation'
      t.boolean  'active', default: true
      t.timestamps
      t.index ['email_template_id'], name: 'index_notification_preferences_on_email_template_id', using: :btree
    end

    create_table 'order_charges' do |t|
      t.integer  'order_id'
      t.decimal  'amount'
      t.string   'stripe_token'
      t.string   'refund_token'
      t.datetime 'expires_at'
      t.timestamps
      t.index ['order_id'], name: 'index_order_charges_on_order_id', using: :btree
    end

    create_table 'order_lines' do |t|
      t.integer  'order_id'
      t.integer  'location_id'
      t.integer  'supplier_profile_id'
      t.integer  'menu_item_id'
      t.integer  'serving_size_id'
      t.integer  'category_id'
      t.integer  'attendee_id'
      t.integer  'team_admin_id' # deprecated
      t.string   'name'
      t.integer  'quantity'
      t.text     'selected_menu_extras'
      t.decimal  'price', precision: 10, scale: 2
      t.decimal  'cost', precision: 10, scale: 2
      t.text     'note'
      t.string   'status', default: 'pending'
      t.boolean  'is_gst_free', default: false
      t.boolean  'is_gst_inc', default: false
      t.integer  'payment_id'
      t.string   'payment_status'
      t.boolean  'sent_as_rgi_to_xero'
      t.timestamps
    end

    create_table 'order_reviews' do |t|
      t.integer  'supplier_profile_id', null: false
      t.integer  'order_id', null: false
      t.integer  'product_quality_score'
      t.integer  'delivery_punctuality_score'
      t.integer  'food_taste_score'
      t.integer  'presentation_score'
      t.text     'comment'
      t.timestamps
      t.index ['supplier_profile_id', 'order_id'], name: 'index_order_reviews_on_supplier_profile_id_and_order_id', unique: true, using: :btree
    end

    create_table 'order_suppliers' do |t|
      t.integer  'order_id'
      t.integer  'supplier_profile_id'
      t.string   'status'
      t.integer  'selected_menu_sections', default: [], array: true
      t.datetime 'cutoff_reminder'
      t.decimal  'surcharge' # add precision
      t.decimal  'subtotal', precision: 10, scale: 2
      t.decimal  'delivery', precision: 10, scale: 2
      t.decimal  'gst', precision: 10, scale: 2
      t.decimal  'total', precision: 10, scale: 2
    end

    create_table 'orders' do |t|
      t.integer  'customer_profile_id'
      t.string   'name'
      t.string   'order_type'
      t.string   'order_variant', default: 'general'
      t.string   'delivery_type', default: 'normal'
      t.datetime 'delivery_at'
      t.datetime 'old_delivery_at'
      t.string   'delivery_address_level'
      t.string   'delivery_address'
      t.integer  'delivery_suburb_id'
      t.text     'delivery_instruction'
      t.integer  'number_of_people'
      t.text     'status'
      t.integer  'credit_card_id'
      t.string   'unique_event_id'
      t.string   'order_category'

      t.integer  'cpo_id'
      t.integer  'coupon_id'
      t.string   'po_number'
      t.string   'department_identity'

      t.string   'contact_name'
      t.string   'company_name'
      t.string   'contact_email'
      t.string   'phone'

      t.string   'pattern'
      t.text     'recurring_order_params'
      t.integer  'renewed_from_id'
      t.integer  'template_id'
      t.integer  'renewed_to_id'
      t.integer  'recurrent_id'
      t.boolean  'skip',  default: true

      t.string   'version_ref'
      t.text     'document_urls'
      t.datetime 'suppliers_notified_at'
      t.integer  'pdf_version_num'


      t.boolean  'invoice_individually', default: false
      t.integer  'invoice_id'
      t.integer  'payment_id'
      t.string   'payment_status', default: 'unpaid'
      t.boolean  'pushed_to_xero', default: false
      t.boolean  'pushed_as_rgi_to_xero'

      t.boolean  'charge_to_minimum', default: false
      t.boolean  'no_delivery_charge', default: false
      t.decimal  'customer_subtotal', precision: 10, scale: 2
      t.decimal  'customer_delivery', precision: 10, scale: 2
      t.decimal  'customer_gst', precision: 10, scale: 2
      t.decimal  'customer_surcharge', precision: 10, scale: 2
      t.decimal  'customer_topup', precision: 10, scale: 2
      t.decimal  'discount', precision: 10, scale: 2, default: '0.0'
      t.decimal  'customer_total', precision: 10, scale: 2
      t.decimal  'commission', precision: 10, scale: 2

      t.integer  'freight_forwarding_rate_id'
      t.integer  'split_order_id'

      # fields that are deprecated
      t.integer  'previous_order_id'
      t.datetime 'estimated_delivery'
      t.boolean  'ffc_bill_pushed_to_xero', default: false
      t.string   'rejected_reason'
      t.string   'invite_link'

      # can remove team order fields as moved to # team_order_details
      t.integer  'team_order_id'
      t.string   'budget'
      t.datetime 'admin_cutoff_2hr_reminder'
      t.datetime 'admin_cutoff_30m_reminder'
      t.boolean  'attendee_pays', default: false

      # can remove woolworths fields as moved to woolworths_orders
      t.integer  'woolworths_account_id'
      t.integer  'woolworths_delivery_window_id'
      t.integer  'woolworths_delivery_address_id'
      t.text     'woolworths_status'
      t.integer  'woolworths_synced_order_lines', default: [], null: false, array: true

      t.integer  'whodunnit_id'
      t.timestamps
      t.index ['woolworths_account_id'], name: 'index_orders_on_woolworths_account_id', unique: true, using: :btree
    end

    create_table 'pages' do |t|
      t.string   'name'
      t.string   'slug'
      t.string   'keywords'
      t.text     'teaser'
      t.text     'body'
      t.string   'nav_name'
      t.string   'tracking_code'
      t.timestamps
      t.index ['slug'], name: 'index_pages_on_slug', unique: true, using: :btree
    end

    create_table 'payments' do |t|
      t.integer  'invoice_id'
      t.integer  'credit_card_id'
      t.integer  'order_id'
      t.integer  'attendee_id'
      t.decimal  'amount', precision: 10, scale: 2
      t.text     'response_text'
      t.string   'auth_code'
      t.string   'transaction_number'
      t.integer  'user_id'
      t.timestamps
    end

    create_table 'permissions' do |t| # deprecated
      t.string   'subject_class'
      t.string   'action'
      t.timestamps
    end

    create_table 'permissions_users' do |t| # deprecated
      t.integer 'user_id'
      t.integer 'permission_id'
    end

    create_table 'profiles' do |t|
      t.integer  'user_id'
      t.string   'profileable_type'
      t.integer  'profileable_id'
      t.string   'avatar'
      t.timestamps
    end

    create_table 'rate_cards' do |t|
      t.integer  'company_id'
      t.integer  'menu_item_id'
      t.integer  'serving_size_id'
      t.decimal  'price', precision: 10, scale: 2
      t.decimal  'cost', precision: 10, scale: 2
      t.timestamps
    end

    create_table 'report_data' do |t|
      t.integer  'report_source_id'
      t.integer  'customer_purchase_order_id'
      t.string   'category'
      t.decimal  'total_spend', precision: 10, scale: 2
      t.integer  'order_ids', default: [], array: true
      t.timestamps
      t.index ['customer_purchase_order_id'], name: 'index_report_data_on_customer_purchase_order_id', using: :btree
      t.index ['report_source_id'], name: 'index_report_data_on_report_source_id', using: :btree
    end

    create_table 'report_sources' do |t|
      t.integer  'source_id'
      t.string   'source_type'
      t.date     'key_date'
      t.timestamps
      t.index ['source_id'], name: 'index_report_sources_on_source_id', using: :btree
    end

    create_table 'rich_rich_files' do |t|
      t.string   'rich_file_file_name'
      t.string   'rich_file_content_type'
      t.integer  'rich_file_file_size'
      t.datetime 'rich_file_updated_at'
      t.string   'owner_type'
      t.integer  'owner_id'
      t.text     'uri_cache'
      t.string   'simplified_type', default: 'file'
      t.timestamps
    end

    create_table 'serving_sizes' do |t|
      t.integer  'menu_item_id'
      t.string   'name'
      t.decimal  'price', precision: 10, scale: 2
      t.integer  'weight'
      t.datetime 'archived_at'
      t.boolean  'is_default', default: false
      t.timestamps
    end

    create_table 'sessions' do |t|
      t.string   'session_id', null: false
      t.text     'data'
      t.timestamps
      t.index ['session_id'], name: 'index_sessions_on_session_id', using: :btree
      t.index ['updated_at'], name: 'index_sessions_on_updated_at', using: :btree
    end

    create_table 'shortened_urls' do |t|
      t.integer  'owner_id'
      t.string   'owner_type', limit: 20
      t.string   'url', null: false
      t.string   'unique_key', limit: 10, null: false
      t.integer  'use_count', default: 0, null: false
      t.string   'category'
      t.datetime 'expires_at'
      t.timestamps
      t.index ['category'], name: 'index_shortened_urls_on_category', using: :btree
      t.index ['owner_id', 'owner_type'], name: 'index_shortened_urls_on_owner_id_and_owner_type', using: :btree
      t.index ['unique_key'], name: 'index_shortened_urls_on_unique_key', unique: true, using: :btree
      t.index ['url'], name: 'index_shortened_urls_on_url', using: :btree
    end

    create_table 'suburb_category_contents' do |t|
      t.integer  'suburb_id'
      t.integer  'category_id'
      t.text     'body'
      t.timestamps
    end

    create_table 'suburbs' do |t|
      t.string   'name'
      t.string   'state'
      t.string   'postcode'
      t.decimal  'latitude', precision: 15, scale: 10
      t.decimal  'longitude', precision: 15, scale: 10
      t.text     'body'
      t.string   'title'
      t.text     'description'
      t.string   'category' # deprecated
      t.timestamps
    end

    create_table 'summary_reports' do |t|
      t.integer  'company_id'
      t.timestamps
    end

    create_table 'supplier_agreement_documents' do |t|
      t.integer  'supplier_profile_id', null: false
      t.string   'docusign_envelope_id', null: false
      t.string   'status', null: false
      t.jsonb    'status_changes', default: [], null: false
      t.timestamps
      t.index ['docusign_envelope_id'], name: 'index_supplier_agreement_documents_on_docusign_envelope_id', unique: true, using: :btree
    end

    create_table 'supplier_categories' do |t|
      t.integer 'supplier_id'
      t.integer 'category_id'
      t.string  'image'
    end

    create_table 'supplier_profiles' do |t|
      t.string   'company_name'
      t.string   'slug'
      t.string   'avatar_url'
      t.string   'company_address'
      t.string   'company_address_suburb_id'
      t.text     'email'
      t.string   'phone'
      t.string   'mobile'
      t.string   'bank_account_number'
      t.string   'bsb_number'
      t.string   'abn_acn'
      t.text     'description'
      t.string   'price_indication'
      t.string   'liquor_license_no'
      t.datetime 'close_from'
      t.datetime 'close_to'
      t.string   'uuid'

      t.decimal  'commission_rate', precision: 5, scale: 3, default: '10.0'
      t.decimal  'markup', precision: 5, scale: 3, default: '0.0'
      t.decimal  'minimum_delivery_fee', precision: 10, scale: 2, default: '0.0'

      t.string   'lead_mode', default: 'by_hour'
      t.string   'purchase_order_push_frequency', default: 'weekly'
      t.boolean  'push_purchase_orders_to_xero', default: false
      t.boolean  'send_supplier_agreement', default: false, null: false # can be removed

      t.integer  'rating_score', default: 0, null: false
      t.integer  'rating_count', default: 0, null: false

      # cached values from menu items
      t.boolean  'has_gluten_free'
      t.boolean  'has_vegetarian'
      t.boolean  'has_vegan'
      t.boolean  'has_dairy_free', default: false

      # cached values from delivery zones
      t.decimal  'delivery_fee', precision: 10, scale: 2
      t.string   'operating_days'
      t.string   'operating_hours'

      # boolean values that can be moved to another table
      t.boolean  'is_searchable', default: true
      t.boolean  'is_yordar_approved'
      t.boolean  'is_new'
      t.boolean  'is_haccp'
      t.boolean  'is_certified_organic'
      t.boolean  'is_gold_licensed'
      t.boolean  'is_kosher'
      t.boolean  'is_halal'
      t.boolean  'needs_swipe_card_access'
      t.boolean  'needs_freight'
      t.boolean  'team_supplier', default: false
      t.boolean  'working_hours'
      t.boolean  'multi_service_point'
      t.boolean  'featured'
      t.boolean  'has_contactless_delivery'
      t.boolean  'is_socially_responsible', default: false
      t.boolean  'is_eco_friendly', default: false
      t.boolean  'can_manage_menu', default: false
      t.timestamps
    end

    create_table 'supplier_profiles_users' do |t| # Don't know if this is used
      t.integer 'supplier_profile_id'
      t.integer 'user_id'
    end

    create_table 'team_order_attendees' do |t|
      t.integer  'order_id'
      t.integer  'event_attendee_id'
      t.boolean  'anonymous', default: false
      t.string   'uniq_code'
      t.string   'status', default: 'invited'
      t.datetime 'cutoff_4hr_reminder'
      t.datetime 'cutoff_2hr_reminder'
      t.datetime 'cutoff_30m_reminder'
      t.datetime 'delivery_30m_reminder'

      t.string   'budget' # deprecated or used for budget override
      t.boolean  'invitation_email', default: false # deprecated
      t.integer  'customer_profile_id' # deprecated
      t.timestamps
      t.index ['customer_profile_id'], name: 'index_team_order_attendees_on_customer_profile_id', using: :btree
      t.index ['event_attendee_id'], name: 'index_team_order_attendees_on_event_attendee_id', using: :btree
      t.index ['order_id'], name: 'index_team_order_attendees_on_order_id', using: :btree
    end

    create_table 'team_order_details' do |t|
      t.integer  'order_id'
      t.string   'package_id'
      t.string   'cutoff_option'
      t.decimal  'budget', precision: 10, scale: 2
      t.boolean  'hide_budget', default: false
      t.boolean  'attendee_pays', default: false
      t.datetime 'cutoff_2hr_reminder'
      t.datetime 'cutoff_30m_reminder'
      t.datetime 'anonymous_attendees_reminder'
      t.timestamps
      t.index ['order_id'], name: 'index_team_order_details_on_order_id', using: :btree
    end

    create_table 'users' do |t|
      t.string   'firstname', null: false
      t.string   'lastname'
      t.string   'gender', default: 'Other'
      t.integer  'suburb_id'

      t.string   'email', default: '',null: false
      t.string   'encrypted_password', default: '',null: false
      t.string   'reset_password_token'
      t.datetime 'reset_password_sent_at'
      t.string   'secondary_email'

      t.boolean  'admin', default: false
      t.boolean  'super_admin', default: false
      t.boolean  'can_access_customers'
      t.boolean  'can_access_suppliers'
      t.boolean  'can_access_orders'
      t.boolean  'allow_all_customer_access'
      t.boolean  'allow_all_supplier_access'

      t.datetime 'remember_created_at'
      t.integer  'sign_in_count', default: 0
      t.datetime 'current_sign_in_at'
      t.datetime 'last_sign_in_at'
      t.string   'current_sign_in_ip'
      t.string   'last_sign_in_ip'

      t.string   'confirmation_token'
      t.datetime 'confirmed_at'
      t.datetime 'confirmation_sent_at'
      t.string   'unconfirmed_email'
      t.string   'mailchimp_email'
      t.boolean  'mailchimp_subscribed', default: false

      t.boolean  'xero_push_fault', default: false
      t.string   'business', default: 'yr'
      t.boolean  'is_active', default: true

      t.integer  'ac_id'
      t.text     'ac_lists'
      t.text     'ac_list_statuses'
      t.text     'ac_tags'
      t.text     'ac_custom_fields'
      t.timestamps
      t.index ['email'], name: 'index_users_on_email', unique: true, using: :btree
      t.index ['reset_password_token'], name: 'index_users_on_reset_password_token', unique: true, using: :btree
    end

    create_table 'versions' do |t|
      t.string   'item_type',null: false
      t.integer  'item_id', null: false
      t.string   'event', null: false
      t.string   'whodunnit'
      t.text     'object'
      t.text     'object_changes'
      t.datetime 'created_at'
      t.index ['item_type', 'item_id'], name: 'index_versions_on_item_type_and_item_id', using: :btree
    end

    create_table 'weekly_menu_clients' do |t|
      t.integer 'customer_profile_id'
      t.string  'title'
      t.string  'navigation_title'
      t.string  'slug'
      t.string  'header_image'
      t.string  'logo'
      t.text    'header_content'
      t.text    'abbreviations'
      t.boolean 'monday', default: false
      t.boolean 'tuesday', default: false
      t.boolean 'wednesday', default: false
      t.boolean 'thursday', default: false
      t.boolean 'friday', default: false
      t.boolean 'saturday', default: false
      t.boolean 'sunday', default: false
      t.index ['customer_profile_id'], name: 'index_weekly_menu_clients_on_customer_profile_id', using: :btree
    end

    create_table 'weekly_menu_reviews' do |t|
      t.integer  'weekly_menu_id'
      t.string   'first_name'
      t.string   'last_name'
      t.integer  'day'
      t.integer  'taste'
      t.integer  'presentation'
      t.integer  'quantity'
      t.boolean  'see_again'
      t.text     'comments'
      t.timestamps
      t.index ['weekly_menu_id'], name: 'index_weekly_menu_reviews_on_weekly_menu_id', using: :btree
    end

    create_table 'weekly_menus' do |t|
      t.integer  'weekly_menu_client_id'
      t.datetime 'week_of'
      t.text     'monday'
      t.text     'tuesday'
      t.text     'wednesday'
      t.text     'thursday'
      t.text     'friday'
      t.text     'saturday'
      t.text     'sunday'
      t.integer  'monday_supplier_id'
      t.integer  'tuesday_supplier_id'
      t.integer  'wednesday_supplier_id'
      t.integer  'thursday_supplier_id'
      t.integer  'friday_supplier_id'
      t.integer  'saturday_supplier_id'
      t.integer  'sunday_supplier_id'
      t.timestamps
      t.index ['weekly_menu_client_id'], name: 'index_weekly_menus_on_weekly_menu_client_id', using: :btree
    end

    create_table 'widget_data' do |t|
      t.string   'name'
      t.text     'value'
      t.datetime 'key_date'
      t.boolean  'is_display'
      t.integer  'widget_id'
      t.timestamps
    end

    create_table 'widgets' do |t|
      t.string   'name'
      t.integer  'customer_purchase_order_id'
      t.timestamps
    end

    create_table 'woolworths_accounts' do |t|
      t.string   'email', null: false
      t.string   'password', null: false
      t.string   'access_token'
      t.string   'refresh_token'
      t.datetime 'token_expires_at'
      t.timestamps
      t.index ['email'], name: 'index_woolworths_accounts_on_email', unique: true, using: :btree
    end

    create_table 'woolworths_orders' do |t|
      t.integer  'order_id'
      t.integer  'account_id'
      t.boolean  'account_in_use', default: false, null: false
      t.string   'status'
      t.integer  'delivery_window_id'
      t.integer  'delivery_address_id'
      t.string   'woolworths_order_id'
      t.decimal  'delivery_fee'
      t.timestamps
      t.index ['account_id'], name: 'index_woolworths_orders_on_account_id', using: :btree
      t.index ['order_id'], name: 'index_woolworths_orders_on_order_id', using: :btree
    end

    create_table 'woolworths_store_availabilities' do |t|
      t.integer  'menu_item_id', null: false
      t.integer  'store_id', null: false
      t.integer  'stock_quantity', null: false
      t.timestamps
      t.index ['menu_item_id', 'store_id'], name: 'index_woolworths_store_availabilities_on_menu_item_id_and_store', unique: true, using: :btree
      t.index ['menu_item_id'], name: 'index_woolworths_store_availabilities_on_menu_item_id', using: :btree
      t.index ['store_id'], name: 'index_woolworths_store_availabilities_on_store_id', using: :btree
    end

    create_table 'woolworths_trolley_products' do |t|
      t.integer  'woolworths_order_id'
      t.integer  'order_line_id'
      t.boolean  'synced', default: false, null: false
      t.timestamps
      t.string   'trolley_errors', default: [], null: false, array: true
    end

    add_foreign_key 'customer_profile_menu_items', 'customer_profiles'
    add_foreign_key 'customer_profile_menu_items', 'menu_items'
    add_foreign_key 'customer_profiles_supplier_profiles', 'customer_profiles'
    add_foreign_key 'customer_profiles_supplier_profiles', 'supplier_profiles'
    add_foreign_key 'event_attendees_teams', 'event_attendees'
    add_foreign_key 'event_attendees_teams', 'event_teams'
    add_foreign_key 'event_teams', 'customer_profiles'
    add_foreign_key 'notification_preferences', 'email_templates'
    add_foreign_key 'order_charges', 'orders'
    add_foreign_key 'orders', 'woolworths_accounts'
    add_foreign_key 'report_data', 'customer_purchase_orders'
    add_foreign_key 'report_data', 'report_sources'
    add_foreign_key 'team_order_details', 'orders'
    add_foreign_key 'weekly_menu_clients', 'customer_profiles'
    add_foreign_key 'weekly_menu_reviews', 'weekly_menus'
    add_foreign_key 'weekly_menus', 'weekly_menu_clients'
    add_foreign_key 'woolworths_store_availabilities', 'menu_items'
  end

  def down
    raise ActiveRecord::IrreversibleMigration, 'The initial migration is not revertable'
  end
end
