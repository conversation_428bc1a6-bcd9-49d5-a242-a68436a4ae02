class RemoveEmailSchedules < ActiveRecord::Migration[5.2]

  def change
    drop_table :email_schedules do |t|
      t.string 'title', limit: 255
      t.string 'interval', limit: 255
      t.string 'email_recipients', limit: 255
      t.text 'info'
      t.datetime 'created_at', null: false
      t.datetime 'updated_at', null: false
      t.integer 'email_template_id'
      t.string 'report_type', limit: 255
      t.datetime 'last_run'
    end
  end
    
end
