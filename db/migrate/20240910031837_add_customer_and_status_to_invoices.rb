class AddCustomerAndStatusToInvoices < ActiveRecord::Migration[6.1]
  def change
    add_column :invoices, :customer_profile_id, :integer
    add_column :invoices, :status, :string

    add_index :invoices, :customer_profile_id
    add_index :invoices, :status

    # add remaining indexes
    add_index :invoices, :number
    add_index :invoices, :uuid    
    add_index :invoices, :payment_status
    add_index :invoices, :created_at
  end
end
