class CreateSupplierMarkupOverrides < ActiveRecord::Migration[5.2]
  def change
    create_table :supplier_markup_overrides do |t|
      t.references :supplier_profile, foreign_key: true
      t.references :company, foreign_key: true
      t.decimal :markup, precision: 5, scale: 3, default: '0.0'
      t.decimal :commission_rate, precision: 5, scale: 3, default: '0.0'
      t.boolean :active, default: true
      t.timestamps
    end
  end
end
