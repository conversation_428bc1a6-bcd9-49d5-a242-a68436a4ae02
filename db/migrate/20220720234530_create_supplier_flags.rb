class CreateSupplierFlags < ActiveRecord::Migration[5.2]
  def change
    create_table :supplier_flags do |t|
      t.references :supplier_profile, foreign_key: true
      t.boolean :is_new, default: false
      t.boolean :is_featured, default: false

      t.boolean :needs_swipe_card_access, default: false
      t.boolean :supplies_in_working_hours, default: false
      t.boolean :provides_multi_service_point, default: false
      t.boolean :provides_contactless_delivery, default: false
      t.boolean :can_manage_menu_dashboard, default: false

      t.boolean :has_gluten_free_items, default: false
      t.boolean :has_vegetarian_items, default: false
      t.boolean :has_vegan_items, default: false
      t.boolean :has_dairy_free_items, default: false

      t.boolean :is_socially_responsible, default: false
      t.boolean :is_eco_friendly, default: false
      
      t.timestamps
    end
  end
end
