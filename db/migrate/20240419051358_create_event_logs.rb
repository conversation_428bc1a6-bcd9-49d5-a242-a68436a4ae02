class CreateEventLogs < ActiveRecord::Migration[6.1]
  def change
    create_table :event_logs do |t|
      t.string :loggable_type
      t.integer :loggable_id

      t.string :scopable_type
      t.integer :scopable_id

      t.string :event, index: true
      t.jsonb :info, default: {}

      t.timestamps
    end
    add_index :event_logs, %i[loggable_type loggable_id], name: 'index_event_logs_loggable'
    add_index :event_logs, %i[scopable_type scopable_id], name: 'index_event_logs_scopable'
  end
end
