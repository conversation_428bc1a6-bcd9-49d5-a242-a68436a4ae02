class RemoveDeprecatedSupplierProfileFields < ActiveRecord::Migration[5.2]
  def change
    remove_column :supplier_profiles, :is_new, :boolean, default: false
    remove_column :supplier_profiles, :featured, :boolean, default: false
    remove_column :supplier_profiles, :needs_swipe_card_access, :boolean, default: false
    remove_column :supplier_profiles, :working_hours, :boolean, default: false
    remove_column :supplier_profiles, :multi_service_point, :boolean, default: false
    remove_column :supplier_profiles, :has_contactless_delivery, :boolean, default: false
    remove_column :supplier_profiles, :can_manage_menu, :boolean, default: false
    remove_column :supplier_profiles, :has_gluten_free, :boolean, default: false
    remove_column :supplier_profiles, :has_vegetarian, :boolean, default: false
    remove_column :supplier_profiles, :has_vegan, :boolean, default: false
    remove_column :supplier_profiles, :has_dairy_free, :boolean, default: false
    remove_column :supplier_profiles, :is_certified_organic, :boolean, default: false
    remove_column :supplier_profiles, :is_gold_licensed, :boolean, default: false
    remove_column :supplier_profiles, :is_kosher, :boolean, default: false
    remove_column :supplier_profiles, :is_halal, :boolean, default: false
    remove_column :supplier_profiles, :is_haccp, :boolean, default: false
    remove_column :supplier_profiles, :is_yordar_approved, :boolean, default: false
    remove_column :supplier_profiles, :is_socially_responsible, :boolean, default: false
    remove_column :supplier_profiles, :is_eco_friendly, :boolean, default: false
  end
end
