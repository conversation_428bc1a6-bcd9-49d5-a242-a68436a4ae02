class CreateDeliveryOverrides < ActiveRecord::Migration[5.2]
  def change
    create_table :delivery_overrides do |t|
      t.references :customer_profile, foreign_key: true
      t.string :supplier_kind
      t.references :supplier_profile, index: true
      t.decimal :customer_override, precision: 10, scale: 2
      t.decimal :supplier_override, precision: 10, scale: 2
      t.boolean :active, default: true

      t.timestamps
    end
  end
end
