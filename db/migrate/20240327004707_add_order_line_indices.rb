class AddOrderLineIndices < ActiveRecord::Migration[6.1]
  def change
    add_index :order_lines, :order_id
    add_index :order_lines, :location_id, name: 'order_line_location'
    add_index :order_lines, :attendee_id, name: 'order_line_attendee'
    add_index :order_lines, :supplier_profile_id, name: 'order_line_supplier'
    add_index :order_lines, :category_id, name: 'order_line_category'
  end
end



