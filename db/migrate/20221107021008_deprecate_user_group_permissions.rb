class DeprecateUserGroupPermissions < ActiveRecord::Migration[5.2]
  def change
    drop_table :groups_permissions do |t|
      t.integer :group_id
      t.integer :permission_id
    end

    drop_table :groups_users do |t|
      t.integer :group_id
      t.integer :user_id
    end

    drop_table :permissions_users do |t|
      t.integer :user_id
      t.integer :permission_id
    end

    drop_table :groups do |t|
      t.string :name
      t.timestamps
    end

    drop_table :permissions do |t|
      t.string :subject_class
      t.string :action
      t.timestamps
    end
  end
end
