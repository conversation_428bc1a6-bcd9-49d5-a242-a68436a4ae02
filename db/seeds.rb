# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the rake db:seed (or created alongside the db with db:setup).
#
def seed_live_data
  system("heroku pg:backups:capture --app yordar")
  system("heroku pg:backups:download --app yordar")
  system("pg_restore --verbose --clean --no-acl --no-owner -h localhost -U #{ENV['USER']} -d yordar_development latest.dump")
  system("mv latest.dump ~/Desktop/$(date +%d-%m-%Y).dump")
end

if ENV['live_data'] == "true"
  self.seed_live_data
end
