# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_07_22_022056) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_stat_statements"
  enable_extension "plpgsql"

  create_table "access_permissions", force: :cascade do |t|
    t.bigint "admin_id"
    t.bigint "customer_profile_id"
    t.boolean "active", default: true
    t.string "scope"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_id"], name: "index_access_permissions_on_admin_id"
    t.index ["customer_profile_id"], name: "index_access_permissions_on_customer_profile_id"
  end

  create_table "billing_details", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.string "name"
    t.string "email"
    t.string "address"
    t.bigint "suburb_id"
    t.string "phone"
    t.string "frequency", default: "instantly"
    t.boolean "order_summaries", default: false
    t.boolean "summary_report", default: false
    t.string "invoice_order_grouping", default: "address_po"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "billing_day"
    t.boolean "hide_delivery_pricing", default: false
    t.boolean "invoice_spreadsheet", default: false
    t.index ["customer_profile_id"], name: "index_billing_details_on_customer_profile_id"
    t.index ["suburb_id"], name: "index_billing_details_on_suburb_id"
  end

  create_table "categories", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.string "slug", limit: 255
    t.string "group", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "show_in_menu"
    t.boolean "show_in_homepage"
    t.integer "group_weight"
    t.integer "weight"
    t.boolean "is_generic", default: false
  end

  create_table "category_menu_sections", id: :serial, force: :cascade do |t|
    t.integer "category_id"
    t.integer "menu_section_id"
    t.integer "weight"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "companies", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.text "address"
    t.string "contact_name", limit: 255
    t.string "contact_phone", limit: 255
    t.string "email", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "can_pay_on_account", default: false
    t.integer "suburb_id"
    t.integer "old_id"
    t.integer "payment_term_days", default: 21
    t.boolean "invoice_by_po", default: false
    t.boolean "requires_po", default: false
    t.boolean "can_pay_by_credit_card", default: true
    t.string "abn_acn"
    t.string "accounting_software"
  end

  create_table "companies_menu_sections", id: false, force: :cascade do |t|
    t.integer "menu_section_id", null: false
    t.integer "company_id", null: false
  end

  create_table "coupon_redemptions", id: :serial, force: :cascade do |t|
    t.integer "coupon_id", null: false
    t.string "user_id"
    t.string "order_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "coupons", id: :serial, force: :cascade do |t|
    t.string "code", null: false
    t.string "description"
    t.date "valid_from", null: false
    t.date "valid_until"
    t.integer "redemption_limit", default: 1, null: false
    t.integer "coupon_redemptions_count", default: 0, null: false
    t.integer "amount", default: 0, null: false
    t.string "type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "credit_cards", id: :serial, force: :cascade do |t|
    t.string "number", limit: 255
    t.integer "expiry_month"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "expiry_year"
    t.string "gateway_token", limit: 255
    t.string "name", limit: 255
    t.string "cvv"
    t.string "label", limit: 255
    t.boolean "enabled", default: true
    t.integer "event_attendee_id"
    t.boolean "saved_for_future", default: true
    t.boolean "pay_on_account", default: false
    t.boolean "auto_pay_invoice", default: false
    t.string "stripe_token"
    t.string "brand"
    t.string "country_code"
  end

  create_table "credit_cards_customer_profiles", id: false, force: :cascade do |t|
    t.integer "customer_profile_id"
    t.integer "credit_card_id"
  end

  create_table "custom_order_suppliers", id: :serial, force: :cascade do |t|
    t.integer "order_id"
    t.integer "supplier_profile_id"
    t.datetime "delivery_at"
    t.decimal "delivery_fee", precision: 10, scale: 2
    t.text "delivery_note"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "customer_budgets", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.decimal "value", precision: 10, scale: 2
    t.string "frequency"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "starts_on"
    t.date "ends_on"
    t.integer "customer_purchase_order_id"
    t.index ["customer_profile_id"], name: "index_customer_budgets_on_customer_profile_id"
  end

  create_table "customer_flags", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.boolean "cancel_review_requests", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "default_orders_view", default: "list"
    t.string "requires_department_identity"
    t.boolean "has_gst_split_invoicing", default: false
    t.boolean "requires_supplier_markup", default: false
    t.boolean "requires_loading_dock_code", default: false
    t.string "accounting_software"
    t.index ["customer_profile_id"], name: "index_customer_flags_on_customer_profile_id"
  end

  create_table "customer_profile_menu_items", id: :serial, force: :cascade do |t|
    t.integer "customer_profile_id", null: false
    t.integer "menu_item_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_profile_id", "menu_item_id"], name: "unique_index_on_customer_profiles_menu_items", unique: true
    t.index ["customer_profile_id"], name: "index_cpmi_on_customer_profile_id"
    t.index ["menu_item_id"], name: "index_cpmi_on_menu_item_id"
  end

  create_table "customer_profiles", id: :serial, force: :cascade do |t|
    t.string "contact_phone", limit: 255
    t.string "mobile", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "company_id"
    t.string "company_name", limit: 255
    t.string "fax", limit: 255
    t.string "notes", limit: 255
    t.string "customer_name", limit: 255
    t.boolean "team_admin", default: false
    t.boolean "company_team_admin", default: false
    t.text "admin_notes"
    t.string "role"
    t.string "uuid"
    t.string "stripe_token"
  end

  create_table "customer_profiles_supplier_profiles", id: false, force: :cascade do |t|
    t.integer "customer_profile_id"
    t.integer "supplier_profile_id"
    t.index ["customer_profile_id", "supplier_profile_id"], name: "unique_index_on_customer_profiles_supplier_profiles", unique: true
    t.index ["customer_profile_id"], name: "index_cpsp_on_customer_profile_id"
    t.index ["supplier_profile_id"], name: "index_cpsp_on_supplier_profile_id"
  end

  create_table "customer_profiles_users", id: :serial, force: :cascade do |t|
    t.integer "customer_profile_id"
    t.integer "user_id"
  end

  create_table "customer_purchase_orders", id: :serial, force: :cascade do |t|
    t.string "po_number"
    t.decimal "weekly_budget"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.text "description"
    t.integer "customer_profile_id"
    t.boolean "active", default: true
  end

  create_table "customer_quotes", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.jsonb "form_data", default: {}
    t.string "status"
    t.string "kind"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uuid"
    t.index ["customer_profile_id"], name: "index_customer_quotes_on_customer_profile_id"
    t.index ["uuid"], name: "index_customer_quotes_on_uuid", unique: true
  end

  create_table "dear_accounts", force: :cascade do |t|
    t.bigint "supplier_profile_id"
    t.string "account_id", null: false
    t.string "api_key", null: false
    t.string "customer_id", null: false
    t.boolean "active", default: true
    t.string "price_tier"
    t.string "dietary_attribute"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["supplier_profile_id"], name: "index_dear_accounts_on_supplier_profile_id"
  end

  create_table "dear_categories", force: :cascade do |t|
    t.bigint "dear_account_id"
    t.string "category_id"
    t.string "name"
    t.string "override_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dear_account_id"], name: "index_dear_categories_on_dear_account_id"
  end

  create_table "dear_sales", force: :cascade do |t|
    t.bigint "order_id"
    t.bigint "supplier_profile_id"
    t.string "sale_id"
    t.string "location"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_dear_sales_on_order_id"
    t.index ["supplier_profile_id"], name: "index_dear_sales_on_supplier_profile_id"
  end

  create_table "delayed_jobs", id: :serial, force: :cascade do |t|
    t.integer "priority", default: 0
    t.integer "attempts", default: 0
    t.text "handler"
    t.text "last_error"
    t.datetime "run_at"
    t.datetime "locked_at"
    t.datetime "failed_at"
    t.string "locked_by", limit: 255
    t.string "queue", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["priority", "run_at"], name: "delayed_jobs_priority"
  end

  create_table "deliverable_suburbs", force: :cascade do |t|
    t.bigint "delivery_zone_id"
    t.bigint "supplier_profile_id"
    t.bigint "suburb_id"
    t.decimal "distance"
    t.index ["delivery_zone_id"], name: "index_deliverable_suburbs_on_delivery_zone_id"
    t.index ["suburb_id"], name: "index_deliverable_suburbs_on_suburb_id"
    t.index ["supplier_profile_id"], name: "index_deliverable_suburbs_on_supplier_profile_id"
  end

  create_table "delivery_overrides", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.string "supplier_kind"
    t.bigint "supplier_profile_id"
    t.decimal "customer_override", precision: 10, scale: 2
    t.decimal "supplier_override", precision: 10, scale: 2
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_profile_id"], name: "index_delivery_overrides_on_customer_profile_id"
    t.index ["supplier_profile_id"], name: "index_delivery_overrides_on_supplier_profile_id"
  end

  create_table "delivery_zones", id: :serial, force: :cascade do |t|
    t.integer "suburb_id"
    t.integer "radius"
    t.decimal "delivery_fee", precision: 10, scale: 2
    t.integer "supplier_profile_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "operating_wdays", limit: 255
    t.integer "operating_hours_start"
    t.integer "operating_hours_end"
  end

  create_table "documents", force: :cascade do |t|
    t.integer "documentable_id"
    t.string "documentable_type"
    t.string "kind"
    t.integer "version"
    t.string "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
  end

  create_table "email_templates", force: :cascade do |t|
    t.string "name", null: false
    t.string "account_type"
    t.string "kind"
    t.string "variations", default: [], array: true
    t.boolean "can_override", default: false
    t.integer "position"
  end

  create_table "emails", id: :serial, force: :cascade do |t|
    t.text "recipient"
    t.text "options"
    t.datetime "sent_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ref", limit: 255
    t.integer "fk_id"
    t.jsonb "details", default: {}
    t.string "template_name"
  end

  create_table "employee_survey_submissions", force: :cascade do |t|
    t.bigint "employee_survey_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "overall_rating"
    t.index ["employee_survey_id"], name: "index_employee_survey_submissions_on_employee_survey_id"
  end

  create_table "employee_surveys", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.string "category_group"
    t.boolean "active", default: true
    t.string "uuid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.text "description"
    t.index ["customer_profile_id"], name: "index_employee_surveys_on_customer_profile_id"
  end

  create_table "event_attendees", id: :serial, force: :cascade do |t|
    t.string "first_name", limit: 255
    t.string "last_name", limit: 255
    t.string "email", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "team_admin_id"
    t.boolean "active", default: true
  end

  create_table "event_attendees_teams", id: :serial, force: :cascade do |t|
    t.integer "event_attendee_id"
    t.integer "event_team_id"
    t.index ["event_attendee_id"], name: "index_event_attendees_teams_on_event_attendee_id"
    t.index ["event_team_id"], name: "index_event_attendees_teams_on_event_team_id"
  end

  create_table "event_log_views", force: :cascade do |t|
    t.bigint "event_log_id"
    t.bigint "user_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "in_bulk", default: false
    t.index ["event_log_id", "user_id"], name: "index_event_log_user_view"
    t.index ["event_log_id"], name: "index_event_log_views_on_event_log_id"
    t.index ["user_id"], name: "index_event_log_views_on_user_id"
  end

  create_table "event_logs", force: :cascade do |t|
    t.string "loggable_type"
    t.integer "loggable_id"
    t.string "scopable_type"
    t.integer "scopable_id"
    t.string "event"
    t.jsonb "info", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "severity", default: "info"
    t.integer "assigned_user_id"
    t.index ["event"], name: "index_event_logs_on_event"
    t.index ["loggable_type", "loggable_id"], name: "index_event_logs_loggable"
    t.index ["scopable_type", "scopable_id"], name: "index_event_logs_scopable"
    t.index ["severity"], name: "index_event_logs_on_severity"
  end

  create_table "event_teams", id: :serial, force: :cascade do |t|
    t.integer "customer_profile_id"
    t.string "name"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_profile_id"], name: "index_event_teams_on_customer_profile_id"
  end

  create_table "external_documents", id: :serial, force: :cascade do |t|
    t.string "doc_type", limit: 255
    t.string "url", limit: 255
    t.text "status"
    t.string "rejected_reason", limit: 255
    t.integer "order_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ref_number", limit: 255
  end

  create_table "favourite_customers", force: :cascade do |t|
    t.integer "favouriter_id"
    t.string "favouriter_type"
    t.bigint "customer_profile_id"
    t.index ["customer_profile_id"], name: "index_favourite_customers_on_customer_profile_id"
    t.index ["favouriter_id", "favouriter_type"], name: "index_customer_favouriter"
  end

  create_table "favourite_suppliers", force: :cascade do |t|
    t.integer "favouriter_id"
    t.string "favouriter_type"
    t.string "kind"
    t.bigint "supplier_profile_id"
    t.index ["favouriter_id", "favouriter_type"], name: "index_supplier_favouriter"
    t.index ["supplier_profile_id"], name: "index_favourite_suppliers_on_supplier_profile_id"
  end

  create_table "ffc_profiles", id: :serial, force: :cascade do |t|
    t.string "business_name", limit: 255
    t.string "contact_name", limit: 255
    t.text "billing_address"
    t.integer "billing_suburb_id"
    t.string "email", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "phone", limit: 255
  end

  create_table "freight_forwarding_rates", id: :serial, force: :cascade do |t|
    t.integer "source_from_postcode"
    t.integer "source_to_postcode"
    t.integer "destination_from_postcode"
    t.integer "destination_to_postcode"
    t.string "freight_type", limit: 255, default: "chilled"
    t.decimal "price_per_pallet", precision: 10, scale: 2
    t.integer "travel_time"
    t.integer "ffc_profile_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ffc_profile_id"], name: "index_freight_forwarding_rates_on_freight_forwarding_company_id"
  end

  create_table "holidays", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.datetime "on_date"
    t.datetime "push_to"
    t.string "state", limit: 255
    t.datetime "effective_from"
    t.datetime "effective_to"
    t.string "image"
    t.text "description"
    t.string "color"
  end

  create_table "invoices", id: :serial, force: :cascade do |t|
    t.string "number", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "amount_price", precision: 10, scale: 2
    t.string "payment_status", limit: 255, default: "unpaid"
    t.decimal "payment_value", precision: 10, scale: 2, default: "0.0"
    t.boolean "pushed_to_xero", default: false
    t.string "uuid"
    t.datetime "from_at"
    t.datetime "to_at"
    t.datetime "due_at"
    t.boolean "do_not_notify", default: false
    t.integer "customer_profile_id"
    t.string "status"
    t.integer "cpo_id"
    t.index ["created_at"], name: "index_invoices_on_created_at"
    t.index ["customer_profile_id"], name: "index_invoices_on_customer_profile_id"
    t.index ["number"], name: "index_invoices_on_number"
    t.index ["payment_status"], name: "index_invoices_on_payment_status"
    t.index ["status"], name: "index_invoices_on_status"
    t.index ["uuid"], name: "index_invoices_on_uuid"
  end

  create_table "leads", id: :serial, force: :cascade do |t|
    t.string "lead_type"
    t.string "progress"
    t.string "email"
    t.integer "user_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "firstname"
    t.string "lastname"
    t.string "company_name"
    t.string "phone"
    t.integer "hs_id"
    t.string "hs_contact_id"
  end

  create_table "loading_docks", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.text "code"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "saved_for_future", default: false
    t.string "file_url"
    t.index ["customer_profile_id"], name: "index_loading_docks_on_customer_profile_id"
  end

  create_table "locations", id: :serial, force: :cascade do |t|
    t.text "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "note", limit: 255
    t.integer "order_id"
    t.index ["order_id"], name: "index_locations_on_order_id"
  end

  create_table "meal_plans", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.string "kind"
    t.string "name"
    t.datetime "delivery_time"
    t.string "delivery_address_level"
    t.string "delivery_address"
    t.integer "delivery_suburb_id"
    t.text "delivery_instruction"
    t.integer "number_of_people"
    t.integer "cpo_id"
    t.integer "gst_free_cpo_id"
    t.integer "credit_card_id"
    t.string "uuid"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "department_identity"
    t.datetime "archived_at"
    t.string "reminder_frequency"
    t.text "admin_notes"
    t.index ["customer_profile_id"], name: "index_meal_plans_on_customer_profile_id"
  end

  create_table "menu_extra_sections", force: :cascade do |t|
    t.bigint "menu_item_id"
    t.string "name"
    t.integer "weight"
    t.integer "min_limit"
    t.integer "max_limit"
    t.datetime "archived_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["menu_item_id"], name: "index_menu_extra_sections_on_menu_item_id"
  end

  create_table "menu_extras", id: :serial, force: :cascade do |t|
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "name"
    t.string "price"
    t.integer "menu_item_id"
    t.integer "weight"
    t.datetime "archived_at"
    t.integer "menu_extra_section_id"
    t.string "sku"
  end

  create_table "menu_items", id: :serial, force: :cascade do |t|
    t.integer "menu_section_id"
    t.string "name", limit: 255
    t.text "description"
    t.decimal "price", precision: 10, scale: 2
    t.integer "minimum_quantity", default: 1
    t.boolean "is_vegan", default: false
    t.boolean "is_vegetarian", default: false
    t.boolean "is_gluten_free", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "supplier_profile_id"
    t.boolean "is_gst_free", default: false
    t.integer "weight"
    t.boolean "is_hidden", default: false
    t.text "image"
    t.string "freight_type", limit: 255, default: "chilled"
    t.string "sku", limit: 255
    t.string "account_code", limit: 255
    t.boolean "team_order_only"
    t.integer "sub_quantity"
    t.boolean "team_order"
    t.datetime "archived_at"
    t.boolean "is_dairy_free", default: false
    t.boolean "is_individually_packed", default: false
    t.integer "stock_quantity"
    t.decimal "promo_price", precision: 10, scale: 2
    t.boolean "is_egg_free", default: false
    t.boolean "is_halal", default: false
    t.boolean "is_kosher", default: false
    t.boolean "is_nut_free", default: false
  end

  create_table "menu_sections", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "supplier_profile_id"
    t.integer "weight"
    t.datetime "archived_at"
    t.string "group_name"
    t.boolean "is_hidden", default: false
  end

  create_table "minimums", id: :serial, force: :cascade do |t|
    t.decimal "spend_price", precision: 10, scale: 2, default: "0.0"
    t.integer "category_id"
    t.integer "supplier_profile_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "lead_time", precision: 10, scale: 2, default: "0.0"
    t.string "lead_time_day_before", limit: 255, default: ""
  end

  create_table "notification_preferences", id: :serial, force: :cascade do |t|
    t.integer "account_id"
    t.string "account_type"
    t.string "email_recipients"
    t.string "salutation"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "variation"
    t.string "template_name"
  end

  create_table "oauth_access_grants", force: :cascade do |t|
    t.bigint "resource_owner_id", null: false
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.integer "expires_in", null: false
    t.text "redirect_uri", null: false
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.string "scopes", default: "", null: false
    t.index ["application_id"], name: "index_oauth_access_grants_on_application_id"
    t.index ["resource_owner_id"], name: "index_oauth_access_grants_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_grants_on_token", unique: true
  end

  create_table "oauth_access_tokens", force: :cascade do |t|
    t.bigint "resource_owner_id"
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.string "refresh_token"
    t.integer "expires_in"
    t.datetime "revoked_at"
    t.datetime "created_at", null: false
    t.string "scopes"
    t.string "previous_refresh_token", default: "", null: false
    t.index ["application_id"], name: "index_oauth_access_tokens_on_application_id"
    t.index ["refresh_token"], name: "index_oauth_access_tokens_on_refresh_token", unique: true
    t.index ["resource_owner_id"], name: "index_oauth_access_tokens_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_tokens_on_token", unique: true
  end

  create_table "oauth_applications", force: :cascade do |t|
    t.string "name", null: false
    t.string "uid", null: false
    t.string "secret", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.boolean "confidential", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "owner_id"
    t.string "owner_type"
    t.index ["owner_id", "owner_type"], name: "index_oauth_applications_on_owner_id_and_owner_type"
    t.index ["uid"], name: "index_oauth_applications_on_uid", unique: true
  end

  create_table "order_charges", id: :serial, force: :cascade do |t|
    t.integer "order_id"
    t.decimal "amount"
    t.string "stripe_token"
    t.string "refund_token"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "status"
    t.index ["order_id"], name: "index_order_charges_on_order_id"
  end

  create_table "order_lines", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.decimal "price", precision: 10, scale: 2
    t.integer "location_id"
    t.integer "quantity"
    t.text "note"
    t.integer "order_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "supplier_profile_id"
    t.string "status", limit: 255, default: "pending"
    t.decimal "cost", precision: 10, scale: 2
    t.integer "category_id"
    t.boolean "is_gst_free", default: false
    t.integer "menu_item_id"
    t.integer "serving_size_id"
    t.integer "attendee_id"
    t.integer "team_admin_id"
    t.boolean "is_gst_inc", default: false
    t.text "selected_menu_extras"
    t.boolean "sent_as_rgi_to_xero"
    t.integer "payment_id"
    t.string "payment_status"
    t.decimal "baseline", precision: 10, scale: 2
    t.jsonb "last_errors", default: []
    t.index ["attendee_id"], name: "order_line_attendee"
    t.index ["category_id"], name: "order_line_category"
    t.index ["location_id"], name: "order_line_location"
    t.index ["order_id"], name: "index_order_lines_on_order_id"
    t.index ["supplier_profile_id"], name: "order_line_supplier"
  end

  create_table "order_reviews", id: :serial, force: :cascade do |t|
    t.integer "supplier_profile_id", null: false
    t.integer "order_id", null: false
    t.integer "product_quality_score"
    t.integer "delivery_punctuality_score"
    t.integer "food_taste_score"
    t.integer "presentation_score"
    t.text "comment"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["supplier_profile_id", "order_id"], name: "index_order_reviews_on_supplier_profile_id_and_order_id", unique: true
  end

  create_table "order_suppliers", id: :serial, force: :cascade do |t|
    t.integer "order_id"
    t.integer "supplier_profile_id"
    t.string "status", limit: 255
    t.decimal "surcharge"
    t.integer "selected_menu_sections", default: [], array: true
    t.datetime "cutoff_4hr_reminder"
    t.decimal "subtotal", precision: 10, scale: 2
    t.decimal "delivery", precision: 10, scale: 2
    t.decimal "gst", precision: 10, scale: 2
    t.decimal "total", precision: 10, scale: 2
    t.datetime "cutoff_day_reminder"
    t.integer "supplier_invoice_id"
    t.decimal "delivery_fee_override", precision: 10, scale: 2
  end

  create_table "orders", id: :serial, force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "customer_profile_id"
    t.integer "invoice_id"
    t.text "status"
    t.string "pattern", limit: 255
    t.text "recurring_order_params"
    t.string "contact_name", limit: 255
    t.string "company_name", limit: 255
    t.string "po_number", limit: 255
    t.string "department_identity", limit: 255
    t.string "contact_email", limit: 255
    t.string "name", limit: 255
    t.datetime "delivery_at"
    t.integer "renewed_from_id"
    t.integer "template_id"
    t.integer "renewed_to_id"
    t.integer "recurrent_id"
    t.boolean "skip", default: true
    t.string "version_ref", limit: 255
    t.string "delivery_address", limit: 255
    t.integer "delivery_suburb_id"
    t.integer "credit_card_id"
    t.string "phone", limit: 255
    t.string "payment_status", limit: 255, default: "unpaid"
    t.text "delivery_instruction"
    t.decimal "customer_subtotal", precision: 10, scale: 2
    t.decimal "customer_gst", precision: 10, scale: 2
    t.decimal "customer_total", precision: 10, scale: 2
    t.decimal "customer_delivery", precision: 10, scale: 2
    t.decimal "customer_surcharge", precision: 10, scale: 2
    t.boolean "pushed_to_xero", default: false
    t.string "order_type", limit: 255
    t.datetime "old_delivery_at"
    t.datetime "suppliers_notified_at"
    t.integer "pdf_version_num"
    t.integer "freight_forwarding_rate_id"
    t.integer "split_order_id"
    t.text "document_urls"
    t.datetime "estimated_delivery"
    t.boolean "ffc_bill_pushed_to_xero", default: false
    t.string "rejected_reason", limit: 255
    t.string "order_variant", limit: 255, default: "general"
    t.decimal "commission", precision: 10, scale: 2
    t.boolean "no_delivery_charge", default: false
    t.integer "cpo_id"
    t.decimal "discount", precision: 10, scale: 2, default: "0.0"
    t.integer "coupon_id"
    t.integer "number_of_people"
    t.boolean "pushed_as_rgi_to_xero"
    t.boolean "attendee_pays", default: false
    t.integer "payment_id"
    t.string "unique_event_id"
    t.string "order_category"
    t.string "delivery_address_level"
    t.string "delivery_type", default: "normal"
    t.integer "whodunnit_id"
    t.boolean "charge_to_minimum", default: false
    t.decimal "customer_topup", precision: 10, scale: 2
    t.boolean "invoice_individually", default: false
    t.integer "promotion_id"
    t.integer "gst_free_cpo_id"
    t.integer "gst_free_invoice_id"
    t.integer "major_category_id"
    t.integer "meal_plan_id"
    t.integer "pantry_manager_id"
    t.integer "loading_dock_id"
    t.string "uuid"
    t.integer "customer_quote_id"
    t.index ["meal_plan_id"], name: "index_orders_on_meal_plan_id"
    t.index ["pantry_manager_id"], name: "index_orders_on_pantry_manager_id"
    t.index ["uuid"], name: "index_orders_on_uuid", unique: true
  end

  create_table "pages", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.string "slug", limit: 255
    t.string "keywords", limit: 255
    t.text "teaser"
    t.text "body"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "nav_name", limit: 255
    t.string "tracking_code", limit: 255
    t.index ["slug"], name: "index_pages_on_slug", unique: true
  end

  create_table "payments", id: :serial, force: :cascade do |t|
    t.integer "invoice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "credit_card_id"
    t.text "response_text"
    t.string "auth_code", limit: 255
    t.string "transaction_number", limit: 255
    t.integer "user_id"
    t.decimal "amount", precision: 10, scale: 2
    t.integer "order_id"
    t.integer "attendee_id"
  end

  create_table "profiles", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "profileable_id"
    t.string "profileable_type", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "avatar", limit: 255
  end

  create_table "promotion_subscriptions", force: :cascade do |t|
    t.bigint "promotion_id"
    t.integer "subscriber_id"
    t.string "subscriber_type"
    t.boolean "active", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["promotion_id"], name: "index_promotion_subscriptions_on_promotion_id"
  end

  create_table "promotions", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.date "valid_from", null: false
    t.date "valid_until"
    t.integer "amount"
    t.string "kind"
    t.boolean "active", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "category_restriction"
  end

  create_table "rate_cards", id: :serial, force: :cascade do |t|
    t.integer "serving_size_id"
    t.integer "menu_item_id"
    t.integer "company_id"
    t.decimal "price", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "cost", precision: 10, scale: 2
  end

  create_table "reminders", force: :cascade do |t|
    t.string "title"
    t.text "message"
    t.string "frequency"
    t.datetime "starting_at"
    t.boolean "active", default: true
    t.integer "remindable_id"
    t.string "remindable_type"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "recipients", default: "account_manager"
    t.index ["remindable_type", "remindable_id"], name: "index_reminders_on_remindable_type_and_remindable_id"
  end

  create_table "report_data", id: :serial, force: :cascade do |t|
    t.integer "report_source_id"
    t.integer "customer_purchase_order_id"
    t.decimal "total_spend", precision: 10, scale: 2
    t.integer "order_ids", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "category"
    t.index ["customer_purchase_order_id"], name: "index_report_data_on_customer_purchase_order_id"
    t.index ["report_source_id"], name: "index_report_data_on_report_source_id"
  end

  create_table "report_order_data", force: :cascade do |t|
    t.bigint "report_datum_id"
    t.string "data_kind"
    t.string "kind"
    t.decimal "total_spend", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["report_datum_id"], name: "index_report_order_data_on_report_datum_id"
  end

  create_table "report_sources", id: :serial, force: :cascade do |t|
    t.integer "source_id"
    t.date "key_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "source_type"
    t.index ["source_id"], name: "index_report_sources_on_source_id"
  end

  create_table "rich_rich_files", id: :serial, force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "rich_file_file_name", limit: 255
    t.string "rich_file_content_type", limit: 255
    t.integer "rich_file_file_size"
    t.datetime "rich_file_updated_at"
    t.string "owner_type", limit: 255
    t.integer "owner_id"
    t.text "uri_cache"
    t.string "simplified_type", limit: 255, default: "file"
  end

  create_table "saved_addresses", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.string "level"
    t.string "street_address"
    t.integer "suburb_id"
    t.text "instructions"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["customer_profile_id"], name: "index_saved_addresses_on_customer_profile_id"
  end

  create_table "serving_sizes", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.decimal "price", precision: 10, scale: 2
    t.integer "menu_item_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "weight"
    t.datetime "archived_at"
    t.boolean "is_default", default: false
    t.boolean "available_for_team_order", default: true
    t.string "sku"
    t.integer "stock_quantity"
  end

  create_table "sessions", id: :serial, force: :cascade do |t|
    t.string "session_id", limit: 255, null: false
    t.text "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["session_id"], name: "index_sessions_on_session_id"
    t.index ["updated_at"], name: "index_sessions_on_updated_at"
  end

  create_table "shortened_urls", id: :serial, force: :cascade do |t|
    t.integer "owner_id"
    t.string "owner_type", limit: 20
    t.string "url", limit: 255, null: false
    t.string "unique_key", limit: 10, null: false
    t.integer "use_count", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "category"
    t.datetime "expires_at"
    t.index ["category"], name: "index_shortened_urls_on_category"
    t.index ["owner_id", "owner_type"], name: "index_shortened_urls_on_owner_id_and_owner_type"
    t.index ["unique_key"], name: "index_shortened_urls_on_unique_key", unique: true
    t.index ["url"], name: "index_shortened_urls_on_url"
  end

  create_table "staff_details", force: :cascade do |t|
    t.bigint "customer_profile_id"
    t.jsonb "personal"
    t.jsonb "emergency_contact"
    t.jsonb "bank"
    t.jsonb "tax"
    t.jsonb "documents"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["customer_profile_id"], name: "index_staff_details_on_customer_profile_id"
  end

  create_table "suburbs", id: :serial, force: :cascade do |t|
    t.string "postcode", limit: 255
    t.string "name", limit: 255
    t.string "state", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "latitude", precision: 15, scale: 10
    t.decimal "longitude", precision: 15, scale: 10
    t.string "country_code"
  end

  create_table "supplier_agreement_documents", id: :serial, force: :cascade do |t|
    t.integer "supplier_profile_id", null: false
    t.string "status", null: false
    t.string "docusign_envelope_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "status_changes", default: [], null: false
    t.index ["docusign_envelope_id"], name: "index_supplier_agreement_documents_on_docusign_envelope_id", unique: true
  end

  create_table "supplier_closures", force: :cascade do |t|
    t.bigint "supplier_profile_id"
    t.datetime "starts_at"
    t.datetime "ends_at"
    t.string "reason"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["supplier_profile_id"], name: "index_supplier_closures_on_supplier_profile_id"
  end

  create_table "supplier_flags", force: :cascade do |t|
    t.bigint "supplier_profile_id"
    t.boolean "is_new", default: false
    t.boolean "is_featured", default: false
    t.boolean "needs_swipe_card_access", default: false
    t.boolean "supplies_in_working_hours", default: false
    t.boolean "provides_multi_service_point", default: false
    t.boolean "provides_contactless_delivery", default: false
    t.boolean "can_manage_menu_dashboard", default: false
    t.boolean "has_gluten_free_items", default: false
    t.boolean "has_vegetarian_items", default: false
    t.boolean "has_vegan_items", default: false
    t.boolean "has_dairy_free_items", default: false
    t.boolean "is_socially_responsible", default: false
    t.boolean "is_eco_friendly", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "needs_multi_day_summary", default: false
    t.boolean "has_skus", default: false
    t.boolean "is_indigenous_owned", default: false
    t.boolean "is_event_caterer", default: false
    t.boolean "uses_flex_catering", default: false
    t.string "billing_frequency", default: "weekly"
    t.integer "payment_term_days", default: 45
    t.boolean "has_catering_services", default: false
    t.boolean "has_kitchen_supplies", default: false
    t.datetime "is_new_expires_at"
    t.boolean "needs_recurring_reminder", default: false
    t.boolean "admin_only", default: false
    t.boolean "is_environmentally_accredited", default: false
    t.boolean "is_registered_charity", default: false
    t.boolean "is_female_owned", default: false
    t.boolean "is_rainforest_alliance_certified", default: false
    t.boolean "has_egg_free_items", default: false
    t.boolean "has_halal_items", default: false
    t.boolean "has_kosher_items", default: false
    t.boolean "has_nut_free_items", default: false
    t.boolean "is_lgbtqi_owned", default: false
    t.string "menu_reminder_frequency", default: "3.months"
    t.datetime "menu_last_updated_on"
    t.index ["supplier_profile_id"], name: "index_supplier_flags_on_supplier_profile_id"
  end

  create_table "supplier_invoices", force: :cascade do |t|
    t.bigint "supplier_profile_id"
    t.string "number"
    t.datetime "from_at"
    t.datetime "to_at"
    t.datetime "due_at"
    t.decimal "amount", precision: 10, scale: 2, default: "0.0"
    t.boolean "pushed_to_xero", default: false
    t.string "payment_status", default: "unpaid"
    t.string "uuid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["supplier_profile_id"], name: "index_supplier_invoices_on_supplier_profile_id"
  end

  create_table "supplier_markup_overrides", force: :cascade do |t|
    t.bigint "supplier_profile_id"
    t.decimal "markup", precision: 5, scale: 3
    t.decimal "commission_rate", precision: 5, scale: 3
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "overridable_type"
    t.integer "overridable_id"
    t.index ["overridable_id", "overridable_type"], name: "index_markup_override_overridable"
    t.index ["supplier_profile_id"], name: "index_supplier_markup_overrides_on_supplier_profile_id"
  end

  create_table "supplier_profiles", id: :serial, force: :cascade do |t|
    t.string "company_name", limit: 255
    t.string "company_address", limit: 255
    t.string "company_address_suburb_id", limit: 255
    t.text "email"
    t.string "phone", limit: 255
    t.string "mobile", limit: 255
    t.string "price_indication", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "abn_acn", limit: 255
    t.decimal "commission_rate", precision: 5, scale: 3, default: "10.0"
    t.text "description"
    t.string "slug", limit: 255
    t.boolean "is_searchable", default: true
    t.string "avatar_url", limit: 255
    t.datetime "close_from"
    t.datetime "close_to"
    t.boolean "needs_freight"
    t.boolean "team_supplier", default: false
    t.string "lead_mode", limit: 255, default: "by_hour"
    t.string "bank_account_number"
    t.string "bsb_number"
    t.decimal "markup", precision: 5, scale: 3, default: "0.0"
    t.integer "rating_score", default: 0, null: false
    t.integer "rating_count", default: 0, null: false
    t.boolean "send_supplier_agreement", default: false, null: false
    t.decimal "minimum_delivery_fee", precision: 10, scale: 2, default: "0.0"
    t.string "liquor_license_no"
    t.string "uuid"
    t.string "operating_days"
    t.string "operating_hours"
    t.decimal "delivery_fee", precision: 10, scale: 2
  end

  create_table "supplier_profiles_users", id: :serial, force: :cascade do |t|
    t.integer "supplier_profile_id"
    t.integer "user_id"
  end

  create_table "survey_answers", force: :cascade do |t|
    t.bigint "employee_survey_submission_id"
    t.bigint "survey_question_id"
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "question_label"
    t.index ["employee_survey_submission_id"], name: "index_survey_answers_on_employee_survey_submission_id"
    t.index ["survey_question_id"], name: "index_survey_answers_on_survey_question_id"
  end

  create_table "survey_questions", force: :cascade do |t|
    t.bigint "employee_survey_id"
    t.string "label"
    t.string "input_type"
    t.text "options", default: [], array: true
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.index ["employee_survey_id"], name: "index_survey_questions_on_employee_survey_id"
  end

  create_table "team_order_attendees", id: :serial, force: :cascade do |t|
    t.integer "order_id"
    t.integer "event_attendee_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uniq_code", limit: 255
    t.string "status", limit: 255, default: "invited"
    t.datetime "cutoff_4hr_reminder"
    t.datetime "cutoff_2hr_reminder"
    t.datetime "cutoff_30m_reminder"
    t.datetime "delivery_30m_reminder"
    t.boolean "anonymous", default: false
    t.bigint "team_order_level_id"
    t.datetime "cutoff_24hr_reminder"
    t.index ["event_attendee_id"], name: "index_team_order_attendees_on_event_attendee_id"
    t.index ["order_id"], name: "index_team_order_attendees_on_team_order_id"
    t.index ["team_order_level_id"], name: "index_team_order_attendees_on_team_order_level_id"
  end

  create_table "team_order_details", id: :serial, force: :cascade do |t|
    t.integer "order_id"
    t.boolean "attendee_pays", default: false
    t.datetime "cutoff_2hr_reminder"
    t.datetime "cutoff_30m_reminder"
    t.string "cutoff_option"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "anonymous_attendees_reminder"
    t.decimal "budget", precision: 10, scale: 2
    t.boolean "hide_budget", default: false
    t.string "package_id"
    t.index ["order_id"], name: "index_team_order_details_on_order_id"
  end

  create_table "team_order_levels", force: :cascade do |t|
    t.bigint "team_order_detail_id"
    t.string "name"
    t.boolean "is_default", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["team_order_detail_id"], name: "index_team_order_levels_on_team_order_detail_id"
  end

  create_table "users", id: :serial, force: :cascade do |t|
    t.string "firstname", limit: 255, null: false
    t.string "lastname", limit: 255
    t.string "gender", limit: 255, default: "Other"
    t.boolean "admin", default: false
    t.boolean "super_admin", default: false
    t.string "email", limit: 255, default: "", null: false
    t.string "encrypted_password", limit: 255, default: "", null: false
    t.string "reset_password_token", limit: 255
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip", limit: 255
    t.string "last_sign_in_ip", limit: 255
    t.string "confirmation_token", limit: 255
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email", limit: 255
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "mailchimp_email", limit: 255
    t.boolean "mailchimp_subscribed", default: false
    t.boolean "xero_push_fault", default: false
    t.string "business", limit: 255, default: "yr"
    t.boolean "is_active", default: true
    t.integer "suburb_id"
    t.string "secondary_email"
    t.boolean "can_access_customers"
    t.boolean "can_access_suppliers"
    t.boolean "can_access_orders"
    t.boolean "allow_all_customer_access"
    t.boolean "allow_all_supplier_access"
    t.integer "hs_id"
    t.boolean "can_manage_custom_orders", default: false
    t.string "hs_contact_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "versions", id: :serial, force: :cascade do |t|
    t.string "item_type", limit: 255, null: false
    t.integer "item_id", null: false
    t.string "event", limit: 255, null: false
    t.string "whodunnit", limit: 255
    t.text "object"
    t.text "object_changes"
    t.datetime "created_at"
    t.index ["created_at"], name: "index_versions_on_created_at"
    t.index ["event"], name: "index_versions_on_event"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "weekly_menu_clients", id: :serial, force: :cascade do |t|
    t.string "title"
    t.string "navigation_title"
    t.string "slug"
    t.string "header_image"
    t.string "logo"
    t.text "header_content"
    t.text "abbreviations"
    t.boolean "monday", default: false
    t.boolean "tuesday", default: false
    t.boolean "wednesday", default: false
    t.boolean "thursday", default: false
    t.boolean "friday", default: false
    t.boolean "saturday", default: false
    t.boolean "sunday", default: false
    t.integer "customer_profile_id"
    t.index ["customer_profile_id"], name: "index_weekly_menu_clients_on_customer_profile_id"
  end

  create_table "weekly_menu_reviews", id: :serial, force: :cascade do |t|
    t.integer "weekly_menu_id"
    t.integer "taste"
    t.integer "presentation"
    t.integer "quantity"
    t.boolean "see_again"
    t.text "comments"
    t.integer "day"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string "first_name"
    t.string "last_name"
    t.index ["weekly_menu_id"], name: "index_weekly_menu_reviews_on_weekly_menu_id"
  end

  create_table "weekly_menus", id: :serial, force: :cascade do |t|
    t.integer "weekly_menu_client_id"
    t.datetime "week_of"
    t.text "monday"
    t.text "tuesday"
    t.text "wednesday"
    t.text "thursday"
    t.text "friday"
    t.text "saturday"
    t.text "sunday"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.integer "monday_supplier_id"
    t.integer "tuesday_supplier_id"
    t.integer "wednesday_supplier_id"
    t.integer "thursday_supplier_id"
    t.integer "friday_supplier_id"
    t.integer "saturday_supplier_id"
    t.integer "sunday_supplier_id"
    t.index ["weekly_menu_client_id"], name: "index_weekly_menus_on_weekly_menu_client_id"
  end

  create_table "woolworths_accounts", id: :serial, force: :cascade do |t|
    t.string "email", null: false
    t.string "password", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "access_token"
    t.string "refresh_token"
    t.datetime "token_expires_at"
    t.boolean "active", default: true
    t.index ["email"], name: "index_woolworths_accounts_on_email", unique: true
  end

  create_table "woolworths_orders", id: :serial, force: :cascade do |t|
    t.integer "order_id"
    t.integer "account_id"
    t.boolean "account_in_use", default: false, null: false
    t.string "status"
    t.integer "delivery_window_id"
    t.integer "delivery_address_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "woolworths_order_id"
    t.decimal "delivery_fee"
    t.string "delivery_window_text"
    t.index ["account_id"], name: "index_woolworths_orders_on_account_id"
    t.index ["order_id"], name: "index_woolworths_orders_on_order_id"
  end

  create_table "woolworths_store_availabilities", id: :serial, force: :cascade do |t|
    t.integer "menu_item_id", null: false
    t.integer "store_id", null: false
    t.integer "stock_quantity", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["menu_item_id", "store_id"], name: "index_woolworths_store_availabilities_on_menu_item_id_and_store", unique: true
    t.index ["menu_item_id"], name: "index_woolworths_store_availabilities_on_menu_item_id"
    t.index ["store_id"], name: "index_woolworths_store_availabilities_on_store_id"
  end

  create_table "woolworths_trolley_products", id: :serial, force: :cascade do |t|
    t.integer "woolworths_order_id"
    t.integer "order_line_id"
    t.boolean "synced", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "trolley_errors", default: [], null: false, array: true
  end

  create_table "xero_invoices", force: :cascade do |t|
    t.string "invoice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "failed_at"
    t.text "last_errors"
  end

  add_foreign_key "access_permissions", "customer_profiles"
  add_foreign_key "billing_details", "customer_profiles"
  add_foreign_key "billing_details", "suburbs"
  add_foreign_key "customer_budgets", "customer_profiles"
  add_foreign_key "customer_profile_menu_items", "customer_profiles"
  add_foreign_key "customer_profile_menu_items", "menu_items"
  add_foreign_key "customer_profiles_supplier_profiles", "customer_profiles"
  add_foreign_key "customer_profiles_supplier_profiles", "supplier_profiles"
  add_foreign_key "dear_accounts", "supplier_profiles"
  add_foreign_key "dear_categories", "dear_accounts"
  add_foreign_key "dear_sales", "orders"
  add_foreign_key "dear_sales", "supplier_profiles"
  add_foreign_key "deliverable_suburbs", "delivery_zones"
  add_foreign_key "deliverable_suburbs", "suburbs"
  add_foreign_key "delivery_overrides", "customer_profiles"
  add_foreign_key "employee_survey_submissions", "employee_surveys"
  add_foreign_key "employee_surveys", "customer_profiles"
  add_foreign_key "event_attendees_teams", "event_attendees"
  add_foreign_key "event_attendees_teams", "event_teams"
  add_foreign_key "event_teams", "customer_profiles"
  add_foreign_key "favourite_customers", "customer_profiles"
  add_foreign_key "favourite_suppliers", "supplier_profiles"
  add_foreign_key "loading_docks", "customer_profiles"
  add_foreign_key "meal_plans", "customer_profiles"
  add_foreign_key "menu_extra_sections", "menu_items"
  add_foreign_key "oauth_access_grants", "oauth_applications", column: "application_id"
  add_foreign_key "oauth_access_grants", "users", column: "resource_owner_id"
  add_foreign_key "oauth_access_tokens", "oauth_applications", column: "application_id"
  add_foreign_key "oauth_access_tokens", "users", column: "resource_owner_id"
  add_foreign_key "order_charges", "orders"
  add_foreign_key "report_data", "customer_purchase_orders"
  add_foreign_key "report_data", "report_sources"
  add_foreign_key "report_order_data", "report_data"
  add_foreign_key "saved_addresses", "customer_profiles"
  add_foreign_key "staff_details", "customer_profiles"
  add_foreign_key "supplier_closures", "supplier_profiles"
  add_foreign_key "supplier_flags", "supplier_profiles"
  add_foreign_key "supplier_markup_overrides", "supplier_profiles"
  add_foreign_key "survey_answers", "employee_survey_submissions"
  add_foreign_key "survey_answers", "survey_questions"
  add_foreign_key "survey_questions", "employee_surveys"
  add_foreign_key "team_order_details", "orders"
  add_foreign_key "team_order_levels", "team_order_details"
  add_foreign_key "weekly_menu_clients", "customer_profiles"
  add_foreign_key "weekly_menu_reviews", "weekly_menus"
  add_foreign_key "weekly_menus", "weekly_menu_clients"
  add_foreign_key "woolworths_store_availabilities", "menu_items"
end
