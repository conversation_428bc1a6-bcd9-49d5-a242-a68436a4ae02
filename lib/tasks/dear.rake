namespace :dear do

  desc 'Import and Sync Product / Inventory'
  task :sync_products, %i[verbose refresh] => :environment do |_, args|
    is_verbose = args[:verbose].presence || false
    refresh = args[:refresh].presence || false

    dear_supplier_accounts = Dear::Account.where(active: true)

    dear_supplier_accounts.each do |dear_account|
      supplier = dear_account.supplier_profile
      puts "Syncing Dear Products for #{supplier.name}"
      products_syncer = Dear::SyncSupplierProducts.new(supplier: supplier, refresh: refresh, verbose: is_verbose).call
      if !products_syncer.success? && !Rails.env.production?
        debugger
      end
    end
  end

  desc 'Sync Orders for Dear Suppliers'
  task :sync_orders, %i[verbose time] => :environment do |_, args|
    is_verbose = args[:verbose].presence || false
    time = args[:time].present? ? Time.zone.parse(args[:time]) : Time.zone.now

    dear_supplier_accounts = Dear::Account.where(active: true)

    dear_supplier_accounts.each do |dear_account|
      supplier = dear_account.supplier_profile
      puts "Syncing Orders for Supplier for #{supplier.name}"
      order_syncer = Dear::SyncSupplierOrders.new(supplier: supplier, time: time, verbose: is_verbose).call
      if !order_syncer.success? && !Rails.env.production?
        debugger
      end
    end
  end

end
