namespace :orders do

  task set_to_delivered: :environment do
    puts "=========== Starting orders.set_to_delivered at #{Time.zone.now} ==========="

    order_canceller = Orders::CancelBlankOrders.new.call # cancel blank orders before marking orders as delivered

    puts ''
    puts "Out of #{order_canceller.possible_orders.size} possible blank order"
    puts "#{order_canceller.cancelled_orders.size} orders were cancelled"

    order_marker = Orders::MarkOrdersAsDelivered.new.call

    delivered_orders = order_marker.delivered_orders
    if !order_marker.success?
      @errors.push({
        task: 'set_to_delivered',
        error_list: order_marker.errors
      })
    end
    puts "Setting as delivered . . . . . . . . . . . . #{delivered_orders.size}"
    puts "=========== Completed orders.set_to_delivered at #{Time.zone.now} =========="
  end

  # Order health check
  # Get all future orders with pdf versions > 20
  task order_health_check: :environment do
    puts "=========== Starting orders.order_health_check at #{Time.zone.now} ==========="
    Orders::HealthCheck.new(time: Time.zone.now).call
    puts "=========== Completed orders.order_health_check at #{Time.zone.now} =========="
  end

  # will update order status to delivered
  # will renew orders
  #
  desc 'Renews any recurring order till the next 6 weeks'
  task renew_recurring_orders: :environment do
    puts "=========== Starting orders.renew_recurring_orders at #{Time.zone.now} ==========="

    orders_renewer = Orders::RenewOrders.new(time: Time.zone.now).call
    if orders_renewer.success?
      renewed_orders = orders_renewer.renewed_orders
      puts "# of order renewed . . . . . . . . . . . . #{renewed_orders.size}"
    else
      Raven.capture_exception(RakeTaskError.new('There was an error Order Renewal automated task'),
        extra: { errors: orders_renewer.errors },
        transaction: 'rake orders:renew_recurring_orders'
      )
    end

    puts "=========== Completed orders.renew_recurring_orders at #{Time.zone.now} =========="
  end

  # Auto confirm Yordar Orders
  task auto_confirm_orders: :environment do
    puts "=========== Started orders.auto_confirm_orders at #{Time.zone.now} =========="
    auto_confirmer = Orders::AutoConfirm.new(time: Time.zone.now).call
    if auto_confirmer.success?
      puts "Confirmed #{auto_confirmer.auto_confirmed_orders.size} orders"
    else
      Raven.capture_exception(RakeTaskError.new('There was an error running the Order Auto Confirmation automated task'),
        extra: { errors: auto_confirmer.errors },
        transaction: 'rake orders:auto_confirm_orders'
      )
    end
    puts "=========== Completed orders.auto_confirm_orders at #{Time.zone.now} =========="
  end

  # check public holidays, set 'skip' to orders on public holidays, cancel the delivery OR push delivery time to the next business day
  # send emails to customer and suppliers too
  task :handle_public_holiday, [:future_days] => :environment do |_, args|
    puts "=========== Starting orders.handle_public_holiday at #{Time.zone.now} ==========="

    # Allow passing how many days to search for public holidays into the future
    future_days = 2.week
    begin
      if args[:future_days].present?
        future_days = args[:future_days].to_i.days.from_now
      end
    rescue
      # do nothing
    end
    now = Time.zone.now
    future_pushable_holidays = Holiday.where('effective_from >= :now AND effective_to <= :future', now: now, future: (now + future_days))
    future_pushable_holidays = future_pushable_holidays.where.not(push_to: nil)

    handling_errors = []
    handled_orders = []
    future_pushable_holidays.each do |holiday|
      holiday_handler = Orders::HandlePublicHolidayOrders.new(holiday: holiday).call
      if holiday_handler.success?
        handled_orders += holiday_handler.handled_orders
      else
        handling_errors += holiday_handler.errors
      end
    end

    if handling_errors.present?
      Raven.capture_exception(RakeTaskError.new('There was an error running the Handle Public Holiday task'),
        extra: { errors: handling_errors },
        transaction: 'rake orders:handle_public_holiday'
      )
    end
    puts "Handled......... #{handled_orders.size} orders for #{future_pushable_holidays.size} holidays"
    puts "=========== Completed orders.handle_public_holiday at #{Time.zone.now} ==========="
  end

  # will trigger background emails
  # notifications to be sent
  #
  task amendment_emails: :environment do
    puts "=========== Starting orders.amendment_emails at #{Time.zone.now} ==========="

    begin
      notifications_sender = Orders::Notifications::SendOrderAmendedNotifications.new(time: Time.zone.now, delayed: true).call
      puts "Notifying #{notifications_sender.notifiable_orders.size} orders"
    rescue => exception
      error_message = "Failed to send order changed email to suppliers. #{exception.inspect}"
      puts error_message
      puts exception.backtrace.join("\n")
      Raven.capture_exception(exception,
        transaction: 'rake orders:amendment_emails'
      )
    end

    puts "=========== Completed orders.amendment_emails at #{Time.zone.now} ==========="
  end

  # will trigger background emails
  # notifications to be sent
  #
  task send_loading_dock_requests: :environment do
    puts "=========== Starting orders.send_loading_dock_requests at #{Time.zone.now} ==========="

    begin
      today = Date.today
      notifications_sender = Orders::Notifications::RequestLoadingDockCode.new(date: today).call
      puts "Notifying #{notifications_sender.notified_customers.size} customers"
    rescue => exception
      error_message = "Failed to send order loading dock request emails to customers. #{exception.inspect}"
      puts error_message
      puts exception.backtrace.join("\n")
      Raven.capture_exception(exception,
        transaction: 'rake orders:send_loading_dock_requests'
      )
    end

    puts "=========== Completed orders.send_loading_dock_requests at #{Time.zone.now} ==========="
  end

end
