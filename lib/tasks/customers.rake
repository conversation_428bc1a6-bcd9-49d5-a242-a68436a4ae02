namespace :customers do

  task cache_order_supplier_ids: :environment do |_, args|
    puts "=========== Starting customers.cache_order_supplier_ids at #{Time.zone.now} ==========="
    customer_ids = args.to_a.map(&:to_i)
    # puts "Running the cache_order_suppliers tasks with arguments #{customer_ids.present? ? customer_ids.inspect : 'ALL customers'}"
    customers = customer_ids.present? ? CustomerProfile.where(id: customer_ids) : CustomerProfile.joins(:user, :orders).where(users: { is_active: true }).distinct
    puts "Running the cache_order_suppliers tasks for #{customers.count} customers"

    customers.each do |customer|
      print 'c-'
      # puts "Customer #{customer.name} (#{customer.id})"
      # cache upcomming order suppliers
      Customers::ListOrderSupplierIds.new(customer: customer, show_past: false, save_cache: true).call

      # cache past order suppliers
      Customers::ListOrderSupplierIds.new(customer: customer, show_past: true, save_cache: true).call
    end
    puts "=========== Completed customers.cache_order_supplier_ids at #{Time.zone.now} ==========="
  end

  task :notify_budget_spends, [:since] => :environment do |_, args|
    puts "=========== Starting customers:notify_budget_spends at #{Time.zone.now} ==========="
    since = args[:since].present? ? Time.zone.parse(args[:since]) : Time.zone.now
    notifications = Customers::Notifications::NotifyBudgetSpends.new(since: since).call

    puts "Created #{notifications.size} notifications for #{since.to_s(:date_verbose)}"
    puts "=========== Completed customers:notify_budget_spends at #{Time.zone.now} ==========="
  end

  desc 'Send Meal plan order reminders for upcomming week/month'
  task :send_meal_plan_order_reminders, %i[frequency reminder_on] => :environment do |_, args|
    reminder_on = args[:reminder_on].present? ? Time.zone.parse(args[:reminder_on]) : Time.zone.now
    reminder_frequencies = args[:frequency].present? ? (MealPlan::VALID_REMINDER_FREQUENCIES & [args[:frequency]]) : MealPlan::VALID_REMINDER_FREQUENCIES
    reminder_weekday = 4 # Thursday

    # puts "Sending reminders for #{reminder_frequencies.join(' and ')} for #{reminder_on.to_s(:date_verbose)} - #{reminder_on.wday}"
    reminder_frequencies.each do |frequency|
      can_send_reminder = case frequency
      when 'weekly'
        reminder_on.wday == reminder_weekday # Every Thursday
      when 'monthly'
        last_thursday = reminder_on.end_of_month
        while last_thursday.wday != reminder_weekday
          last_thursday -= 1.day
        end
        reminder_on.to_date == last_thursday.to_date # Last Thursday of the month
      end
      # puts "Can send reminder for #{frequency} - #{can_send_reminder}"
      next if !can_send_reminder

      puts "=========== Starting customers:send_meal_plan_order_reminders [#{frequency}] at #{Time.zone.now} ==========="
      notifications_sender = MealPlans::Notifications::SendOrderReminders.new(time: reminder_on, frequency: frequency).call
      if notifications_sender.success?
        puts "Sent reminders for #{notifications_sender.notified_meal_plans.size} meal plans"
      else
        Raven.capture_exception(RakeTaskError.new("There was an error sending meal plan #{frequency} order reminders #{Time.zone.now}"),
          extra: { errors: notifications_sender.errors },
          transaction: 'rake customers:send_meal_plan_order_reminders'
        )
      end
      puts "=========== Completed customers:send_meal_plan_order_reminders [#{frequency}] at #{Time.zone.now} ==========="
    end
  end

end
