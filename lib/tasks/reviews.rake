namespace :reviews do

  task send_invitations: :environment do
    puts "=========== Starting reviews.send_invitations at #{Time.zone.now} ==========="
    invitation_sender = OrderReviews::SendInvitations.new.call
    if !invitation_sender.success?
      Raven.capture_exception(RakeTaskError.new('Failed to send order review invitations'),
        extra: { errors: invitation_sender.errors },
        transaction: 'rake reviews:send_invitations'
      )
    end
    puts "Sending invitations . . . . . . . . . . . . #{invitation_sender.sent_invitations.size}"
    puts "=========== Completed reviews.send_invitations at #{Time.zone.now} =========="
  end

  task send_summary: :environment do
    if Time.zone.now.beginning_of_day == Time.zone.now.beginning_of_week
      puts "=========== Starting reviews.send_summary at #{Time.zone.now} ==========="
      Admin::Emails::SendOrderReviewsSummaryEmail.new(time: Time.zone.now - 1.week).call
      puts 'Sending summary . . . . . . . . . . . . '
      puts "=========== Completed reviews.send_summary at #{Time.zone.now} =========="
    end
  end

  task send_supplier_notifications: :environment do
    if Time.zone.now.beginning_of_day == Time.zone.now.beginning_of_week
      puts "=========== Starting reviews.send_supplier_notifications at #{Time.zone.now} ==========="
      notifications_sent = OrderReviews::SendSupplierNotifications.new(time: (Time.zone.now - 1.week)).call
      puts "Sending notifications . . . . . . . . . . . .#{notifications_sent}"
      puts "=========== Completed reviews.send_supplier_notifications at #{Time.zone.now} =========="
    end
  end

end
