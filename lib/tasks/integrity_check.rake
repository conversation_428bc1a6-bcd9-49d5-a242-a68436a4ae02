namespace :integrity_check do

  task :check_supplier_minimum_to_menu_section_categories, [:notify_via_slack] => :environment do |_, args|
    notify_via_slack = args[:notify_via_slack].presence || false

    searchable_suppliers = SupplierProfile.where(is_searchable: true).includes(:categories, minimums: :category)
    searchable_suppliers.each do |supplier|
      slack_attachments = []
      minimums = supplier.minimums
      active_section_categories = supplier.menu_sections.where(archived_at: nil).map(&:categories).flatten.uniq
      extra_minimums = minimums.where.not(category: active_section_categories)

      next if extra_minimums.blank?

      extra_minimums = extra_minimums.sort_by{|x| x.category.name }
      active_minmums = minimums.where.not(id: extra_minimums.map(&:id)).sort_by{|x| x.category.name }

      supplier_name = "Supplier: #{supplier.name} (#{supplier.id})"
      extras_info = extra_minimums.map{|extra| "#{extra.category.name} (#{extra.spend_price})" }
      extras = "Extra minimums: #{extras_info.join(' | ')}"

      active_info = active_minmums.map{|active| "#{active.category.name} (#{active.spend_price})" }
      actives = "Active minimums: #{active_info.join(' | ')}"
      price_mismatch = (extra_minimums.map(&:spend_price) - active_minmums.map(&:spend_price)).present? ? '***** with mismatched spend price *****' : ''

      puts ''
      puts supplier_name
      puts extras
      puts actives
      puts price_mismatch if price_mismatch.present?

      if notify_via_slack
        message = ":warning: #{supplier_name} found with minimums for categories not connected to any active menu sections #{price_mismatch}"
        slack_attachments << {
          type: 'mrkdwn',
          text: extras,
          color: 'warning'
        }
        if price_mismatch.present?
          slack_attachments << {
            type: 'mrkdwn',
            text: actives,
            color: 'warning'
          }
        end
        SlackNotifier.send(message, attachments: slack_attachments)
        puts '- notified via Slack -'
      end

      puts '**********'
    end
  end

end
