namespace :stats do

  desc 'Generate Annual Customer Spend Report'
  task :generate_annual_customer_spend_report, %i[starts ends cumulative verbose include_order_ids] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting stats.generate_annual_customer_spend_report at #{start_time} ==========="

    starts = args[:starts].present? ? Time.zone.parse(args[:starts]).beginning_of_month : Time.zone.now.beginning_of_year
    ends = args[:ends].present? ? Time.zone.parse(args[:ends]).end_of_month : Time.zone.now.end_of_year
    is_cumulative = args[:cumulative].present?
    is_verbose = args[:verbose].present?
    include_order_ids = args[:include_order_ids].present?

    begin
      puts "Generating Data from #{starts} to #{ends}"
      orders = Stats::ListOrders.new(starts: starts, ends: ends).call
      puts "Number of Orders #{orders.size}"
      report_name = is_cumulative ? 'customer_cumulative' : 'customer'
      report_name += "_stats_with_costs-#{starts.year}"
      customer_grouped_orders = Stats::Group::OrdersByCustomer.new(orders: orders).call
      file_path = Stats::Generate::CustomerSpendsReport.new(report_data: customer_grouped_orders, report_name: report_name, include_order_ids: include_order_ids, verbose: is_verbose, cumulative: is_cumulative).call
      puts ''
      puts "Report found @ #{file_path}"
    rescue => exception
      debugger
    end
    stats_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting stats.generate_annual_customer_spend_report at #{Time.zone.now} - Duration: #{stats_duration} mins ==========="
  end

  desc 'Generate Annual Category Spend Report'
  task :generate_annual_category_spend_report, %i[starts ends verbose include_order_ids] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting stats.generate_annual_category_spend_report at #{start_time} ==========="

    starts = args[:starts].present? ? Time.zone.parse(args[:starts]).beginning_of_month : Time.zone.now.beginning_of_year
    ends = args[:ends].present? ? Time.zone.parse(args[:ends]).end_of_month : Time.zone.now.end_of_year
    is_verbose = args[:verbose].present?
    include_order_ids = args[:include_order_ids].present?

    begin
      puts "Generating Data from #{starts} to #{ends}"
      orders = Stats::ListOrders.new(starts: starts, ends: ends).call
      puts "Number of Orders #{orders.size}"
      customer_grouped_orders = Stats::Group::OrdersByCustomer.new(orders: orders).call
      file_path = Stats::Generate::CategorySpendsReport.new(report_data: customer_grouped_orders, report_name: "category_stats_with_costs-with_suppliers-#{starts.year}", include_order_ids: include_order_ids, verbose: is_verbose).call
      puts ''
      puts "Report found @ #{file_path}"
    rescue => exception
      debugger
    end
    stats_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting stats.generate_annual_category_spend_report at #{Time.zone.now} - Duration: #{stats_duration} mins ==========="
  end

  desc 'Generate Annual Location Spend Report'
  task :generate_annual_location_spend_report, %i[starts ends verbose include_order_ids] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting stats.generate_annual_location_spend_report at #{start_time} ==========="

    starts = args[:starts].present? ? Time.zone.parse(args[:starts]).beginning_of_month : Time.zone.now.beginning_of_year
    ends = args[:ends].present? ? Time.zone.parse(args[:ends]).end_of_month : Time.zone.now.end_of_year
    is_verbose = args[:verbose].present?
    include_order_ids = args[:include_order_ids].present?

    begin
      puts "Generating Data from #{starts} to #{ends}"
      orders = Stats::ListOrders.new(starts: starts, ends: ends).call
      puts "Number of Orders #{orders.size}"
      location_grouped_orders = Stats::Group::OrdersBySuburb.new(orders: orders).call
      file_path = Stats::Generate::LocationSpendsReport.new(report_data: location_grouped_orders, report_name: "location_stats_with_costs-#{starts.year}", include_order_ids: include_order_ids, verbose: is_verbose).call
      puts ''
      puts "Report found @ #{file_path}"
    rescue => exception
      debugger
    end
    stats_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting stats.generate_annual_location_spend_report at #{Time.zone.now} - Duration: #{stats_duration} mins ==========="
  end

  desc 'Generate Cumulative Supplier Creation Report'
  task :generate_cumulative_supplier_creation_report, [:verbose] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting stats.generate_cumulative_supplier_creation_report at #{start_time} ==========="

    is_verbose = args[:verbose].present?

    begin
      suppliers = SupplierProfile.all.order(created_at: :asc)
      puts "Number of Suppliers #{suppliers.size}"
      month_grouped_suppliers = Stats::Group::SuppliersByCreationDate.new(suppliers: suppliers).call
      file_path = Stats::Generate::SupplierCreationReport.new(report_data: month_grouped_suppliers, verbose: is_verbose).call
      puts ''
      puts "Report found @ #{file_path}"
    rescue => exception
      debugger
    end
    stats_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting stats.generate_cumulative_supplier_creation_report at #{Time.zone.now} - Duration: #{stats_duration} mins ==========="
  end

  desc 'Generate Supplier Suburb Report'
  task :generate_supplier_suburb_report, [:verbose] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting stats.generate_cumulative_supplier_creation_report at #{start_time} ==========="

    is_verbose = args[:verbose].present?

    begin
      suppliers = SupplierProfile.all.order(created_at: :asc)
      puts "Number of Suppliers #{suppliers.size}"
      suburb_grouped_suppliers = Stats::Group::SuppliersBySuburb.new(suppliers: suppliers).call
      file_path = Stats::Generate::SupplierSuburbReport.new(report_data: suburb_grouped_suppliers, verbose: is_verbose).call
      puts ''
      puts "Report found @ #{file_path}"
    rescue => exception
      debugger
    end
    stats_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting stats.generate_cumulative_supplier_creation_report at #{Time.zone.now} - Duration: #{stats_duration} mins ==========="
  end

  desc 'Generate Monthly Item Spend Report'
  task :generate_monthly_item_spend_report, %i[starts ends verbose] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting stats.generate_monthly_item_spend_report at #{start_time} ==========="

    starts = args[:starts].present? ? Time.zone.parse(args[:starts]).beginning_of_month : Time.zone.now.beginning_of_month
    ends = args[:ends].present? ? Time.zone.parse(args[:ends]).end_of_month : Time.zone.now.end_of_month
    is_verbose = args[:verbose].present?

    begin
      puts "Generating Data from #{starts} to #{ends}"
      orders = Stats::ListOrders.new(starts: starts, ends: ends).call
      puts "Number of Orders #{orders.size}"
      item_grouped_order_lines = Stats::Group::OrderLinesByItems.new(orders: orders).call
      file_path = Stats::Generate::ItemSpendsReport.new(report_data: item_grouped_order_lines, report_name: "item_stats_with_costs-#{starts.to_s(:date)}", verbose: is_verbose).call
      puts ''
      puts "Report found @ #{file_path}"
    rescue => exception
      debugger
    end
    stats_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting stats.generate_monthly_item_spend_report at #{Time.zone.now} - Duration: #{stats_duration} mins ==========="
  end

  desc 'Generate Breakdown of orders per category per month'
  task :generate_order_category_breakdown, %i[starts ends verbose include_order_ids] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting stats.generate_order_category_breakdown at #{start_time} ==========="

    starts = args[:starts].present? ? Time.zone.parse(args[:starts]).beginning_of_month : Time.zone.now.beginning_of_month
    ends = args[:ends].present? ? Time.zone.parse(args[:ends]).end_of_month : Time.zone.now.end_of_month
    is_verbose = args[:verbose].present?
    include_order_ids = args[:include_order_ids].present?

    begin
      puts "Generating Data from #{starts} to #{ends}"
      orders = Stats::ListOrders.new(starts: starts, ends: ends).call
      puts "Number of Orders #{orders.size}"
      category_grouped_orders = Stats::Group::OrdersByVariantAndCategory.new(orders: orders.order(delivery_at: :asc)).call

      file_path = Stats::Generate::OrderCategoryBreakdownReport.new(report_data: category_grouped_orders, report_name: "order_category_revenue-#{starts.to_s(:date)}", verbose: is_verbose, include_order_ids: include_order_ids).call
      puts ''
      puts "Report found @ #{file_path}"
    rescue => exception
      debugger
    end

    stats_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting stats.generate_order_category_breakdown at #{Time.zone.now} - Duration: #{stats_duration} mins ==========="
  end

  desc 'Generate Supplier Category Revenue'
  task :generate_supplier_category_revenue, %i[starts ends verbose include_order_ids] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting stats.generate_supplier_category_revenue at #{start_time} ==========="

    starts = args[:starts].present? ? Time.zone.parse(args[:starts]) : Time.zone.now.beginning_of_month
    ends = args[:ends].present? ? Time.zone.parse(args[:ends]) : Time.zone.now.end_of_month
    is_verbose = args[:verbose].present?
    include_order_ids = args[:include_order_ids].present?

    begin
      puts "Generating Data from #{starts} to #{ends}"
      orders = Stats::ListOrders.new(starts: starts, ends: ends).call
      puts "Number of Orders #{orders.size}"
      supplier_grouped_orders = Stats::Group::OrdersBySuppliers.new(orders: orders.includes(:supplier_profiles, :order_suppliers).order(delivery_at: :asc), verbose: is_verbose).call

      puts ''
      puts 'Generating Report'
      file_path = Stats::Generate::CategoryRevenueReport.new(report_data: supplier_grouped_orders, report_name: "category_revenue_with_costs-#{starts.to_s(:date)}", verbose: is_verbose, include_order_ids: include_order_ids).call
      puts ''
      puts "Report found @ #{file_path}"
    rescue => exception
      debugger
    end

    stats_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting stats.generate_supplier_category_revenue at #{Time.zone.now} - Duration: #{stats_duration} mins ==========="
  end

end
