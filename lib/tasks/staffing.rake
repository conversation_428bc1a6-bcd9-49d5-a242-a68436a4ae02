namespace :staffing do

  desc 'Send Staffing (Pantry Manager) Schedules for next week'
  task :send_staffing_schedules, [:forced] => :environment do |_, args|
    now = Time.zone.now
    is_forced = args[:forced].present?

    # only runs on Thursday unless forced
    if now.wday != 4 && !is_forced
      next
    end

    puts "=========== Starting staffing.send_staffing_schedules at #{now} ==========="
    notifications_sender = Admin::Notifications::SendStaffingSchedules.new(time: now).call
    if notifications_sender.notified_pantry_managers.present?
      puts "Notified #{notifications_sender.notified_pantry_managers.size} pantry managers about their schedule"
    end

    if notifications_sender.errors.present?
      Raven.capture_exception(RakeTaskError.new('There was an error when sending pantry manager staffing schedules'),
        extra: { errors: notifications_sender.errors, date: now.to_date },
        transaction: 'rake staffing.send_staffing_schedules'
      )
    end

    puts "=========== Completed staffing.send_staffing_schedules at #{Time.zone.now} ==========="
  end

  desc 'Send Staffing (Pantry Manager) Logs for the current fortnight'
  task :send_staffing_logs, [:forced] => :environment do |_, args|
    now = Time.zone.now
    is_forced = args[:forced].present?
    year_week = now.strftime('%V').to_i

    # only runs on Friday every (odd) fortnight @ 10am unless forced
    if (now.wday != 5 || now.hour != 10 || (year_week % 2) != 1) && !is_forced
      next
    end

    puts "=========== Starting staffing.send_staffing_logs at #{now} ==========="
    notifications_sender = Admin::Notifications::SendStaffingLogs.new(time: now).call
    if notifications_sender.notified_accounts_team
      puts "Notified accounts team about the current fortnight's staffing log"
    end
    if notifications_sender.notified_pantry_managers.present?
      puts "Notified #{notifications_sender.notified_pantry_managers.size} pantry managers about their current fortnight's staffing log"
    end

    if notifications_sender.errors.present?
      Raven.capture_exception(RakeTaskError.new('There was an error when sending pantry manager staffing schedules'),
        extra: { errors: notifications_sender.errors, date: now.to_date },
        transaction: 'rake staffing.send_staffing_logs'
      )
    end

    puts "=========== Completed staffing.send_staffing_logs at #{Time.zone.now} ==========="
  end

end
