namespace :woolworths do

  task :sync_products, %i[importable_store_ids reimport verbose] => :environment do |_, args|
    importable_store_ids = args[:importable_store_ids].present? ? args[:importable_store_ids].split('-').map(&:to_i) : nil
    is_reimport = args[:reimport].present?
    is_verbose = args[:verbose].present?

    products_importer = Woolworths::Import::ImportProducts.new(supplier: SupplierProfile.woolworths, importable_store_ids: importable_store_ids, is_reimport: is_reimport, is_verbose: is_verbose).call

    if !products_importer.success? && products_importer.errors.present?
      debugger if Rails.env.development?

      Raven.capture_exception(RakeTaskError.new('There was an error when Importing Woolworths Products'),
        extra: {
          errors: products_importer.errors,
          is_reimport: is_reimport
        },
        transaction: 'rake woolworths:sync_products'
      )
    end
  end

  task detach_stale_orders: :environment do
    order_detacher = Woolworths::Cleanup::DetachStaleAccounts.new.call
    puts "Detached #{order_detacher.stale_orders.size} accounts from #{order_detacher.attached_woolworths_orders.size} orders."
  end

  task :remove_saved_addresses, [:dry_run] => :environment do |_, args|
    dry_run = args[:dry_run].presence || false
    if dry_run || Time.zone.now.wday == 6 # only runs on Saturdays unless as a dry run
      address_remover = Woolworths::Cleanup::RemoveSavedAddresses.new(dry_run: dry_run, verbose: true).call
      if address_remover.success?
        puts "Removed #{address_remover.deleted_addresses.size} adddresses from #{address_remover.accounts.size} accounts."
      else
        Raven.capture_exception(RakeTaskError.new('There was an error when removing saved Woolworths Addresses'),
          extra: {
            errors: address_remover.errors,
            dry_run: dry_run
          },
          transaction: 'rake woolworths:remove_saved_addresses'
        )
      end
    else
      puts 'Only runs on Saturdays (or if its a dry run)'
    end
  end

  desc 'Checks if the system can authenticate when connecting to the API'
  task check_accounts_connection: :environment do
    now = Time.zone.now
    if now <= Time.zone.parse(yordar_credentials(:yordar, :closure_start_date)) || now >= Time.zone.parse(yordar_credentials(:yordar, :closure_end_date))
      Woolworths::Analysis::CheckAccountConnection.new.call
    end
  end

  desc 'Analyse Usage of Wooworths accounts + detach accounts for passed in order ids (separated by dash `-`)'
  task :analyse_woolworths_accounts, [:order_ids] => :environment do |_, args|
    order_ids = args[:order_ids].present? ? args[:order_ids].split('-').map(&:to_i) : []
    Woolworths::Analysis::CheckAccountUsage.new(order_ids: order_ids).call
  end

  desc 'Check Woolworths Product Availability'
  task :analyse_woolworths_product_availability, %i[dry_run verbose] => :environment do |_, args|
    dry_run = args[:dry_run].presence || false
    verbose = args[:verbose].presence || false
    puts ''
    puts "Running woolworths:analyse_woolworths_product_availability with dry_run: #{dry_run} and verbose: #{verbose}"
    Woolworths::Analysis::CheckProductAvailability.new(dry_run: dry_run, verbose: verbose).call
  end

  desc 'Setup for Testing'
  task setup_for_testing: :environment do
    if Rails.env.production?
      puts 'Task cannot be be run in Production'
      next
   end

    account_email = yordar_credentials(:woolworths, :yordar_account_email)
    account_password = yordar_credentials(:woolworths, :yordar_account_password)
    account_id = 15

    potential_test_account = Woolworths::Account.where(id: account_id).first

    if potential_test_account.email != account_email
      potential_test_account.update_columns({
        email: account_email,
        password: account_password,
        access_token: nil,
        refresh_token: nil,
        token_expires_at: nil,
      })
    end

    # mark all other accounts as in use and hence un-usable
    non_usable_accounts = Woolworths::Account.where.not(id: potential_test_account.id)
    woolworths_orders = Woolworths::Order.where(account: non_usable_accounts, account_in_use: false)
    woolworths_orders.each do |order|
      order.update_column(:account_in_use, true)
      print 't-'
    end

    # mark potential_test_account as free
    Woolworths::Order.where(account: potential_test_account, account_in_use: true).each do |order|
      order.update_column(:account_in_use, false)
      print 'f-'
    end

    # check if this correct account is available
    available_account = Woolworths::GetAvailableAccount.new.call
    puts "Availble Account #{available_account.email}"

    Woolworths::Cleanup::RemoveSavedAddresses.new(account: available_account).call
  end

end
