namespace :suburbs do
  
  desc 'Populate AU / NZ suburbs'
  task :populate_suburbs, [:country_code] => :environment do |_, args|
    country_codes = args[:country_code].present? ? [args[:country_code]].map(&:upcase) : %w[AU NZ]

    country_suburb_files = {
      'AU' => 'suburbs.csv',
      'NZ' => 'suburbs_nz.csv'
    }

    country_codes.each do |country_code|
      puts ''
      puts "Country => #{country_code}"

      suburbs_filepath = Rails.root.join('lib/files', country_suburb_files[country_code])
      puts "Populating suburbs from file #{suburbs_filepath}"

      CSV.foreach(suburbs_filepath, headers: true) do |row|
        sanitized_state = row['state'] || row['territory']&.sub(/District$|City$/, '') || row['region']
        suburb = Suburb.where(
          country_code: country_code,
          name: row['name'].strip,
          state: sanitized_state.strip,
          postcode: row['postcode'],
        ).first_or_initialize
        suburb.update(
          latitude: row['latitude'],
          longitude: row['longitude'],
        )
        puts "#{country_code} Suburb (##{suburb.id}) => #{suburb.label} - #{suburb.longitude} x #{suburb.latitude}"
      end
    end
  end

end