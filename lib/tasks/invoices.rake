require "#{Rails.root}/app/helpers/cloudinary_helper"
include CloudinaryHelper

namespace :invoices do

  # will generate invoice object
  # will create invoice pdf(?) + send email
  #
  task :generate_invoices, [:frequency] => :environment do |_, args|
    puts "=========== Starting invoices.generate_invoices at #{Time.zone.now} ==========="
    possible_frequency_types = %w[cards-only instantly weekly monthly]

    frequency_types = case
    when args[:frequency].present? && possible_frequency_types.include?(args[:frequency])
      [args[:frequency]]
    when args[:frequency].present?
      []
    else
      possible_frequency_types
    end
    puts "Generating Invoices with frequencies of #{frequency_types.join(', ')}"

    invoice_generation_errors = []
    frequency_types.each do |frequency|
      generated_invoices = []

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: frequency, notify_customers: true).call
      if invoices_generator.success?
        generated_invoices += invoices_generator.generated_invoices
      else
        invoice_generation_errors += invoices_generator.errors
      end
      puts "#{frequency.capitalize} => Generated Invoices: #{generated_invoices.size}"
    end

    if invoice_generation_errors.present?
      Raven.capture_exception(RakeTaskError.new('There was an error when generating Order(s) Invoices'),
        extra: { errors: invoice_generation_errors },
        transaction: 'rake invoices:generate_invoices'
      )
    end
    puts "=========== Completed invoices.generate_invoices at #{Time.zone.now} ==========="
  end

  desc 'send overdue invoice notifications'
  task :send_overdue_invoice_notifications, [:kind] => :environment do |_, args|
    now = Time.zone.now
    notifiable_kinds = args[:kind].present? ? [args[:kind]] : Invoices::Notifications::SendOverdueNotifications::DAY_THRESHOLDS.keys
    if [0, 6].include?(now.wday)
      puts 'invoices.send_overdue_invoice_notifications task is not run on a weekend'
      next
    end
    puts "=========== Starting invoices.send_overdue_invoice_notifications at #{now} ==========="
    notifiable_kinds.each do |kind|
      notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: now).call
      if notifications_sender.success?
        puts "Notification kind - #{kind}: Notified #{notifications_sender.notified_customers.size} customers for in total of #{notifications_sender.notified_invoices.size} invoices."
      else
        Raven.capture_exception(RakeTaskError.new('There was an error when sending overdue invoice notifications'),
          extra: { errors: notifications_sender.errors },
          transaction: 'rake invoices:send_overdue_invoice_notifications'
        )
      end
    end
    puts "=========== Completed invoices.send_overdue_invoice_notifications at #{Time.zone.now} ==========="
  end

end
