# required by invoice - daily summary report
require "#{Rails.root}/app/helpers/cloudinary_helper"

include CloudinaryHelper

namespace :scheduled_task do
	@errors = []

	#
	# This rake file has two main tasks to run (which are hooked with prod crons)
	# scheduled_task:run_daily_cleanup  : runs once daily @ cache 3am
	# scheduled_task:runs_daily  : runs once daily @ 3pm
	# scheduled_task:runs_hourly : runs every hour
	# scheduled_task:runs_xero_hourly : runs xero related tasks every hour
	# scheduled_task:runs_every_10_minutes : runs every 10 minutes

	# chained root tasks
	task run_daily_cleanup: [
		'orders:order_health_check', # order health check
		'woolworths:detach_stale_orders', # detaches any woolworths accounts from orders which are 2 days old
		'woolworths:remove_saved_addresses', # removes saved address on non-used woolworths accounts
		'woolworths:check_accounts_connection', # check and notify via slack if there are issues with connecting to the Woolworths API
		'suppliers:cache_category_groups', # Cache supplier's category groups based on menu section categories
		'suppliers:cache_delivery_details', # Cache supplier's delivery details if similar
		'suppliers:cache_dietary_preferences', # Cache supplier's dietary preferences
		'suppliers:save_deliverable_suburbs', # Save supplier's delivery suburbs based on delivery zones
		'customers:cache_order_supplier_ids' # Cache order suppliers for all customers
	]

	task runs_daily:  [
		'orders:set_to_delivered',	# all the 'confirmed' orders are set to 'delivered' of the delivery_at date has crossed
		'orders:auto_confirm_orders', # all the delivery-due-tomorrow orders (new/amended) are set to 'confirmed' status
		'reports:daily_summaries', # send suppliers summary of orders to be delivered in the next few days
		'invoices:generate_invoices', # generate invoices for orders
		'invoices:send_overdue_invoice_notifications', # notify customers about their overdue invoices
		'orders:renew_recurring_orders', # renew recurring orders
		'reports:supplier_order_reminders', # remind suppliers 48hrs before monthly and fortnightly orders
		'suppliers:supplier_menu_reminders', # remind suppliers about their un-updated menus
		'yordar:closure_emails', # send emails to Suppliers and Customers about the closure periods during Christmas and New Years
		'orders:handle_public_holiday', # skip or push orders which coincide with a public holiday
		'reviews:send_invitations', # send review invitation to customers with orders
		'reviews:send_summary', # Send review <NAME_EMAIL>
		'reviews:send_supplier_notifications',
		'orders:send_loading_dock_requests', # send loading dock request to cusotmer who have orders with delivery type of loading_dock
		'xero:notify_failed_invoices_push_to_xero', # Send any invoices which failed to push xero
		'yordar:send_daily_errors_email', # Send any rake task related errors via email
		'staffing:send_staffing_schedules', # Send Staffing (Pantry Manager) Schedules for next week - only on Thursday
		'team_orders:auto_extend_recurring_team_orders', # Auto Extend Recurring Team Orders unless manually extended by Team Admin
		'yordar:log_calendar_events', # Log Calendar Events at the start of every month
		'customers:notify_budget_spends', # Create notifications for customer spends reaching above notifiable percentages
		'customers:send_meal_plan_order_reminders' # Send Meal plan order reminders for upcomming week/month
	]

	task runs_hourly: [
		'reports:daily_morning_summaries', # send supplier
		'team_orders:auto_confirm_team_orders', # confirm any new/amended team orders which are past the cutoff and grace period.
		'yordar:notify_pending_orders', # notify admins about any pending orders (custom orders with status = draft and/or quoted orders) that are set to be delivered tommorrow
		'team_orders:recurring_team_order_extension_reminder', # Send customers reminder to extend their recurring team orders
		'suppliers:supplier_recurring_order_reminder', # Send recurring order reminder emails to suppliers
		'staffing:send_staffing_logs', # Send Staffing (Pantry Manager) Logs for the current fortnight - only run every (odd) fortnight on Friday @ 10am
		'yordar:send_admin_reminders' # Send Reminders to Admins and account managers
	]

	task runs_xero_hourly: [
		'suppliers:generate_supplier_invoices', # generate and push supplier invoices to Xero + send to suppliers
		'xero:push_invoices_to_xero',
		'xero:retry_outstanding_xero_errors'
	]

	task runs_every_10_minutes: [
		'team_orders:notify_attendee_30m_before_delivery',
		'team_orders:notify_admin_and_attendee_30m_before_cutoff',
		# 'team_orders:notify_admin_about_anonymous_attendees_1hr_before_cutoff',
		'team_orders:notify_admin_and_attendee_2hr_before_cutoff',
		'team_orders:notify_attendee_24hr_before_cutoff',
		'team_orders:notify_attendee_4hr_before_cutoff',
		'team_orders:notify_supplier_before_cutoff',
		'team_orders:submit_team_orders',
		'orders:amendment_emails' # send orders changed emails to suppliers
	]

end
