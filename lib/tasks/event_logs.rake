namespace :event_logs do

  desc 'create dummy event logs'
  task create_dummy_data: :environment do

    EventLog.all.each(&:destroy)
    created_logs = []
    errored_events = []

    recent_orders = Order.order(delivery_at: :desc).where.not(customer_profile: nil).limit(200)
    supplier_spends = { name: recent_orders.where(status: 'delivered').sample.supplier_profiles.sample.name, minimum: 200.0, remaining: 50.0 }
    start_of_month = (Time.zone.now - 1.month).beginning_of_month

    event_logs = [
      {
        event: 'new-order-quoted',
        event_object: recent_orders.where(status: 'quoted').sample
      },
      {
        event: 'new-order-submitted',
        event_object: recent_orders.where(status: 'delivered').sample
      },
      {
        event: 'new-order-submitted',
        event_object: recent_orders.where(status: 'delivered').sample,
        severity: 'warning',
        after_cutoff: true
      },
      {
        event: 'new-order-submitted',
        event_object: recent_orders.where(status: 'delivered').sample,
        severity: 'warning',
        under_supplier_minimum: true,        
        supplier_spends: [supplier_spends]
      },
      {
        event: 'woolworths-checkout-failed',
        event_object: recent_orders.where(status: 'delivered').joins(:woolworths_order).last(200).sample,
        severity: 'error',
        account: recent_orders.where(status: 'delivered').joins(:woolworths_order).last(200).sample.woolworths_order.account.short_name
      },
      {
        event: 'order-amended',
        event_object: recent_orders.where(status: 'delivered').sample,
        severity: 'warning',
        after_cutoff: true
      },
      {
        event: 'order-amended',
        event_object: recent_orders.where(status: 'delivered').sample,
        severity: 'warning',
        under_supplier_minimum: true,
        supplier_spends: [supplier_spends]
      },
      {
        event: 'order-rejected',
        event_object: recent_orders.where(status: 'delivered').sample,
        severity: 'warning',
        supplier: recent_orders.where(status: 'delivered').sample.supplier_profiles.sample.name
      },
      {
        event: 'order-canceled',
        severity: 'warning',
        event_object: recent_orders.where(status: 'delivered').sample
      },
      {
        event: 'order-canceled-permanently',
        severity: 'warning',
        event_object: recent_orders.where(status: 'delivered', order_type: 'recurrent').sample
      },
      {
        event: 'on-hold-charge-failed',
        event_object: recent_orders.where(status: 'delivered', order_type: 'recurrent').sample,
        severity: 'error',
        message: 'Your card was declined.',
        decline_code: 'fraudulent'
      },
      {
        event: 'new-team-order-created',
        event_object: recent_orders.where(status: 'delivered', order_variant: 'team_order').sample
      },
      {
        event: 'new-package-created',
        event_object: recent_orders.where(status: 'delivered', order_variant: 'recurring_team_order').sample
      },
      {
        event: 'package-extended',
        event_object: recent_orders.where(status: 'delivered', order_variant: 'recurring_team_order').sample
      },
      {
        event: 'approaching-cutoff',
        event_object: recent_orders.where(status: 'delivered', order_variant: 'team_order').sample,
        cutoff: %w[4hr 2hr 30m].sample
      },
      {
        event: 'approaching-cutoff-below-minimum',
        event_object: recent_orders.where(status: 'delivered', order_variant: 'team_order').sample,
        severity: 'warning',
        cutoff: %w[4hr 2hr 30m].sample,
        cutoff_option: recent_orders.where(status: 'delivered', order_variant: 'team_order').sample.cutoff_option,
        remaining_spend: supplier_spends[:remaining]
      },
      {
        event: 'custom-order-saved-as-draft',
        event_object: recent_orders.where(status: 'draft', order_variant: 'event_order').sample
      },
      {
        event: 'new-custom-order-quoted',
        event_object: recent_orders.where(status: 'delivered', order_variant: 'event_order').sample
      },
      {
        event: 'new-custom-order-submitted',
        event_object: recent_orders.where(status: 'delivered', order_variant: 'event_order').sample
      },
      {
        event: 'order-below-margin-threshold',
        event_object: recent_orders.where(status: 'delivered', order_variant: 'event_order').sample,
        severity: 'warning',
        commission: 13.06
      },
      {
        event: 'new-supplier-registration',
        event_object: SupplierProfile.last(20).sample
      },
      {
        event: 'supplier-agreement-signed',
        event_object: SupplierProfile.last(20).sample
      },
      {
        event: 'searchable-updated',
        event_object: SupplierProfile.last(20).sample,
        is_searchable: [true, false].sample
      },
      {
        event: 'margin-updated',
        event_object: SupplierProfile.last(20).sample,
        markup: 10.2,
        commission_rate: 15.3
      },
      {
        event: 'new-customer-registration',
        event_object: CustomerProfile.last(20).sample,
        role: CustomerProfile::VALID_ROLES.sample
      },
      {
        event: 'new-quote-submitted',
        event_object: CustomerQuote.last(20).sample
      },
      {
        event: 'invoice-overdue',
        event_object: Invoice.last(20).sample,
        severity: 'warning',
        overdue: ['3 days', '7 days', '21 days'].sample
      },
      {
        event: 'pending-orders',
        delivery_on: recent_orders.where(status: 'delivered', order_variant: 'general').sample.delivery_at.to_s(:full_date),
        severity: 'warning',
        custom_orders: recent_orders.where(status: 'delivered', order_variant: 'event_order').sample(5).map(&:id),
        normal_orders: recent_orders.where(status: 'delivered', order_variant: 'general').sample(5).map(&:id)
      },
      {
        event: 'orders-auto-confirmed',
        orders: recent_orders.where(status: 'delivered', order_type: 'recurrent').sample(5).map(&:id)
      },
      {
        event: 'upcoming-public-holiday',
        event_object: Holiday.where.not(push_to: nil).last(10).sample,
        pushed_orders: recent_orders.where(status: 'delivered', order_type: 'recurrent').sample(5).map(&:id),
        skipped_orders: recent_orders.where(status: 'delivered', order_type: 'recurrent').sample(5).map(&:id)
      },
      {
        event: 'monthly-calendar-event',
        event_object: Holiday.where(push_to: nil).last(10).sample,
      },
      {
        event: 'monthly-calendar-event',
        event_object: Holiday.where.not(push_to: nil).last(10).sample,
      }
    ]

    event_logs.each do |event_log|
      puts event_log[:event]
      event_creator = EventLogs::Create.new(**event_log).call
      if event_creator.success?
        created_event_log = event_creator.event_log
        created_event_log.update_column(:created_at, start_of_month + rand(1..28).days)
        created_logs << created_event_log
      else
        errored_events << event_log[:event]
      end
    end

    debugger if errored_events.present?
  end

end