namespace :reports do

  # deprecated.. but kept for legacy purposes to check
  # was run as part of the daily scheduled task
  # 'reports:summary_report_data', # send a monthly order summary report on the 3rd of each month
  task summary_report_data: :environment do
    puts "=========== Starting reports.summary_report_data at #{Time.zone.now} ==========="
    # runs every 3rd day of the month
    if Date.today.strftime('%d') == '03'
      start_date = (Time.zone.now - 1.month).beginning_of_month
      end_date = start_date.end_of_month
      monthly_report_customers = CustomerProfile.joins(:billing_details).where(billing_details: { summary_report: true })
      monthly_report_customers.group_by(&:company).each do |company, company_customers|
        next if company.blank?

        begin
          report_csv = Customers::CreateMonthlyReport.new(customer: company_customers.first, start_date: start_date, end_date: end_date, company_wide: true).call
          company_customers.each do |customer|
            Customers::Emails::SendMonthlySummaryReportEmail.new(customer: customer, start_date: start_date, end_date: end_date, report_csv: report_csv).call
          end
        rescue => exception
          Raven.capture_exception(exception,
            message: "There was an error running the Monthly Summary report for #{company.try(:id)}",
            extra: { company_id: company.try(:id), company_customers: company_customers.map(&:id) },
            transaction: 'rake reports.summary_report_data'
          )
        end
      end
    end
    puts "=========== Completed reports.summary_report_data at #{Time.zone.now} ==========="
  end

  # will generate daily supplier's upcoming delivery
  # summary pdf + send email
  #
  task daily_summaries: :environment do
    puts "=========== Starting reports.daily_summaries at #{Time.zone.now} ==========="
    notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: Time.zone.now, summary_type: 'daily').call
    if notifications_sender.errors.present?
      Raven.capture_exception(RakeTaskError.new("There was an error running the daily summaries report on #{Time.zone.now}"),
        extra: { errors: notifications_sender.errors },
        transaction: 'rake reports.daily_summaries'
      )
    end
    puts "Sent Supplier notifications: #{notifications_sender.supplier_notifications.size}"
    puts "Sent Customer notifications: #{notifications_sender.customer_notifications.size}"
    puts "=========== Completed reports.daily_summaries at #{Time.zone.now} ==========="
  end

  # will generate daily supplier's upcoming delivery
  # summary pdf + send email
  # runs at 7am every day
  task daily_morning_summaries: :environment do
    # Runs at 7am
    if Time.zone.now.to_s(:hour_only) == '07'
      puts "=========== Starting reports.daily_morning_summaries at #{Time.zone.now} ==========="
      notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: Time.zone.now, summary_type: 'morning').call
      if notifications_sender.errors.present?
        Raven.capture_exception(RakeTaskError.new("There was an error running the daily morning summaries report on #{Time.zone.now}"),
          extra: { errors: notifications_sender.errors },
          transaction: 'rake reports.daily_morning_summaries'
        )
      end
      puts "Sent Supplier notifications: #{notifications_sender.supplier_notifications.size}"
      puts "=========== Completed reports.daily_morning_summaries at #{Time.zone.now} ==========="
    end
  end

  # will generate supplier's reminders for upcoming fortnightly/monthly delivery
  # summary pdf + send email
  #
  task supplier_order_reminders: :environment do
    puts "=========== Starting reports.supplier_order_reminders at #{Time.zone.now} ==========="
    notifications_sender = Orders::Notifications::SendOrderSummaries.new(time: Time.zone.now, summary_type: 'reminder').call
    if notifications_sender.errors.present?
      Raven.capture_exception(RakeTaskError.new("There was an error running the supplier order reminder on #{Time.zone.now}"),
        extra: { errors: notifications_sender.errors },
        transaction: 'rake reports.supplier_order_reminders'
      )
    end
    puts "Sent Supplier notifications: #{notifications_sender.supplier_notifications.size}"
    puts "=========== Completed reports.supplier_order_reminders at #{Time.zone.now} ==========="
  end

  task :collect_report_data, %i[starts ends data_type] => :environment do |_, args|
    now = Time.zone.now

    starts = args[:starts].present? ? Time.zone.parse(args[:starts]) : (now - 3.weeks)
    starts = starts.beginning_of_week
    ends = args[:ends].present? ? Time.zone.parse(args[:ends]) : (now + 2.months)
    ends = ends.end_of_month

    puts "=========== Starting reports.collect_report_data at #{now} ==========="
    possible_data_types = %w[supplier customer]
    data_type = args[:data_type].present? && possible_data_types.include?(args[:data_type]) ? args[:data_type] : 'both'

    puts "Running report to capture #{data_type} from #{starts} to #{ends}"
    report_capturer = Reports::CaptureData.new(starts: starts, ends: ends, data_type: data_type, verbose: true).call
    puts "Collected #{report_capturer.weekly_stats.size} #{data_type} stats"

    puts "=========== Completed reports.collect_report_data at #{Time.zone.now} ==========="
  end

end
