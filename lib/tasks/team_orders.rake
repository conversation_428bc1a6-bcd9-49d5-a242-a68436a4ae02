namespace :team_orders do

  task notify_supplier_before_cutoff: :environment do
    now = Time.zone.now
    puts "=========== Starting notify_supplier_before_cutoff at #{Time.zone.now} ==========="
    TeamOrders::Notifications::SupplierCutoff.new(cutoff_time: '4hr').call

    if now.hour == 10 # 10am
      TeamOrders::Notifications::SupplierCutoff.new(cutoff_time: 'day').call
    end
    puts "=========== Completed notify_supplier_before_cutoff at #{Time.zone.now} ==========="
  end

  task notify_attendee_24hr_before_cutoff: :environment do
    puts "=========== Starting notify_attendee_24hr_before_cutoff at #{Time.zone.now} ==========="
    if Time.zone.now.hour == 10 # 10am
      TeamOrderAttendees::Notifications::CutoffReminder.new(cutoff_time: '24hr').call
    end
    puts "=========== Completed notify_attendee_24hr_before_cutoff at #{Time.zone.now} ==========="
  end

  task notify_attendee_4hr_before_cutoff: :environment do
    puts "=========== Starting notify_attendee_4hr_before_cutoff at #{Time.zone.now} ==========="
    TeamOrderAttendees::Notifications::CutoffReminder.new(cutoff_time: '4hr').call
    puts "=========== Completed notify_attendee_4hr_before_cutoff at #{Time.zone.now} ==========="
  end

  task notify_admin_and_attendee_2hr_before_cutoff: :environment do
    puts "=========== Starting notify_admin_and_attendee_2hr_before_cutoff at #{Time.zone.now} ==========="
    TeamOrderAttendees::Notifications::CutoffReminder.new(cutoff_time: '2hr').call
    TeamOrders::Notifications::AdminCutoff.new(cutoff_time: '2hr').call
    puts "=========== Completed notify_admin_and_attendee_2hr_before_cutoff at #{Time.zone.now} ==========="
  end

  task notify_admin_about_anonymous_attendees_1hr_before_cutoff: :environment do
    puts "=========== Starting notify_admin_about_anonymous_attendees_1hr_before_cutoff at #{Time.zone.now} ==========="
    TeamOrders::Notifications::AdminAnonymousAttendees.new(cutoff_time: '1hr').call
    puts "=========== Completed notify_admin_about_anonymous_attendees_1hr_before_cutoff at #{Time.zone.now} ==========="
  end

  task notify_admin_and_attendee_30m_before_cutoff: :environment do
    puts "=========== Starting notify_admin_and_attendee_30m_before_cutoff at #{Time.zone.now} ==========="
    TeamOrderAttendees::Notifications::CutoffReminder.new(cutoff_time: '30m').call
    TeamOrders::Notifications::AdminCutoff.new(cutoff_time: '30m').call
    puts "=========== Completed notify_admin_and_attendee_30m_before_cutoff at #{Time.zone.now} ==========="
  end

  task notify_attendee_30m_before_delivery: :environment do
    puts "=========== Starting notify_attendee_30m_before_delivery at #{Time.zone.now} ==========="
    TeamOrderAttendees::Notifications::DeliveryReminder.new.call
    puts "=========== Completed notify_attendee_30m_before_delivery at #{Time.zone.now} ==========="
  end

  # this submits pending team orders which are past their supplier cutoff lead time
  task submit_team_orders: :environment do
    puts "=========== Starting submit_team_orders at #{Time.zone.now} ==========="
    now = Time.zone.now
    team_orders_to_process = Order.where(order_variant: %w[team_order recurring_team_order], status: 'pending', delivery_at: [now..(now + 1.week)])
    count = 0

    team_orders_to_process.each do |team_order|
      order_submitter = TeamOrders::Submit.new(team_order: team_order).call
      count += 1 if order_submitter.success?
    end

    puts "Team Order submitted . . . . . . . . . . . . #{count}"
    puts "=========== Completed submit_team_orders at #{Time.zone.now} =========="
  end

  # This auto confirms new/amended orders which are past the cutoff period and the grace period
  task auto_confirm_team_orders: :environment do
    TeamOrders::AutoConfirm.new(time: Time.zone.now).call
  end

  task :recurring_team_order_extension_reminder, [:time] => :environment do |_, args|
    time = args[:time].present? ? Time.zone.parse(args[:time]) : Time.zone.now
    is_first_reminder = time.wday == 3 && time.hour == 15 # on Wednesdays 3pm
    is_final_reminder = time.wday == 5 && time.hour == 8 # on Friday 8am
    if is_first_reminder || is_final_reminder
      puts "=========== Starting team_orders:recurring_team_order_extension_reminder at #{Time.zone.now} ==========="
      notifications = TeamOrders::Notifications::AdminRecurringTeamOrderExtension.new(time: time, final_reminder: (is_final_reminder && !is_first_reminder)).call
      puts "Sent #{notifications.size} notification"
      puts "=========== Completed team_orders:recurring_team_order_extension_reminder at #{Time.zone.now} ==========="
    else
      puts '=========== Task team_orders:recurring_team_order_extension_reminder only runs on either Wedneday-3pm or Friday-8am ==========='
    end
  end

  # This auto extends any recurring team orders
  task :auto_extend_recurring_team_orders, [:time] => :environment do |_, args|
    time = args[:time].present? ? Time.zone.parse(args[:time]) : Time.zone.now
    if time.wday == 6 || args[:time].present? # only auto extends on Saturday, unless forced
      puts "=========== Starting team_orders:auto_extend_recurring_team_orders at #{Time.zone.now} ==========="
      auto_extender = TeamOrders::AutoExtend.new(time: time).call
      if auto_extender.success?
        puts "Generated #{auto_extender.extended_orders.size} recurring team orders as extensions"
      else
        Raven.capture_exception(RakeTaskError.new('There was an error when auto extending recurring team orders'),
          extra: { errors: auto_extender.errors, time: time },
          transaction: 'rake team_orders:auto_extend_recurring_team_orders'
        )
      end
      puts "=========== Completed team_orders:auto_extend_recurring_team_orders at #{Time.zone.now} ==========="
    else
      puts '=========== Task team_orders:auto_extend_recurring_team_orders only runs on Saturday ==========='
    end
  end

end
