namespace :xero do

  # push invoice and related customer to xero if they don't exist in xero yet
  task push_invoices_to_xero: :environment do # ran hourly
    puts "=========== Starting xero.push_invoices_to_xero at #{Time.zone.now} ==========="
    sleep(1.minute)

    invoices_pusher = Xero::PushInvoices.new(time: Time.zone.now, verbose: true).call

    puts "Pushed invoices: #{invoices_pusher.pushed_invoices.map(&:number).join(', ')} to Xero"

    if !invoices_pusher.success?
      puts "Errored invoices: #{invoices_pusher.errored_invoices.map(&:number).join(', ')} to Xero"
      Raven.capture_exception(RakeTaskError.new('There was an error when pushing invoice data to Xero'),
        extra: { errors: invoices_pusher.errors },
        transaction: 'rake xero.push_invoices_to_xero'
      )
    end
    puts "=========== Completed xero.push_invoices_to_xero at #{Time.zone.now} ==========="
  end

  task :retry_outstanding_xero_errors, [:forced] => :environment do |_, args|
    now = Time.zone.now
    is_forced = args[:forced].present?

    # only runs after 6pm unless forced
    if now.to_s(:hour_only).to_i < 18 && !is_forced
      next
    end

    puts "=========== Starting xero.retry_outstanding_xero_errors at #{Time.zone.now} ==========="
    sleep(1.minute)
    invoices_re_pusher = Xero::PushInvoices.new(time: Time.zone.now, retrial: true, verbose: true).call

    puts "Re-Pushed Invoices: #{invoices_re_pusher.pushed_invoices.map(&:number).join(', ')}"
    puts "Errored again Invoices: #{invoices_re_pusher.errored_invoices.map(&:number).join(', ')}"
    puts "=========== Completed xero.retry_outstanding_xero_errors at #{Time.zone.now} ==========="
  end

  task notify_failed_invoices_push_to_xero: :environment do # ran daily at 3pm
    puts "=========== Started xero.notify_failed_invoices_push_to_xero at #{Time.zone.now} ==========="
    Admin::Emails::SendFailedXeroInvoicesEmail.new(time: Time.zone.now).call
    puts "=========== Completed xero.notify_failed_invoices_push_to_xero at #{Time.zone.now} ==========="
  end

  task sync_webhook_invoices: :environment do
    start_time = Time.zone.now.dup
    puts "=========== Started xero.sync_webhook_invoices at #{start_time} ==========="
    invoice_syncer = Xero::SyncInvoices.new(verbose: true).call

    if invoice_syncer.success?
      puts "Synced #{invoice_syncer.synced_invoices.size} invoices"
    else
      Raven.capture_exception(RakeTaskError.new('There was an error when syncing Xero Webhook invoices'),
        extra: { errors: invoice_syncer.errors },
        transaction: 'rake xero.sync_webhook_invoices'
      )
    end
    sync_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting xero.sync_webhook_invoices at #{Time.zone.now} - Duration: #{sync_duration} mins ==========="
  end

end
