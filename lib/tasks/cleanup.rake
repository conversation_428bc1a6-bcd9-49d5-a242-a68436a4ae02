namespace :cleanup do

  desc 'Cleanup Staging Data to hide identity - ONLY FOR STAGING'
  task :clean_staging_data, [:verbose] => :environment do |_, args|
    start_time = Time.zone.now
    puts "=========== Starting cleanup.purge_staging_data at #{start_time} ==========="
    is_verbose = args[:verbose].present?
    puts "The environment you are running is => #{Rails.env.to_s.upcase}"
    puts 'This task is only to be run on Staging... confirm that it is staging by typing `stagingyordar`'
    confirm_environment = STDIN.gets.strip

    if confirm_environment != 'stagingyordar'
      puts ''
      puts 'confirmation invalid - Task can only be run on STAGING'
    else
      puts 'Thank you for confirming! .. Cleaning Data now'
      Admin::Cleanup::ScrambleStaging.new(verbose: is_verbose).call
    end
    end_time = Time.zone.now
    duration = ((end_time - start_time) / 1.minute).round(2)
    puts ''
    puts "=========== Completed cleanup.purge_staging_data at #{end_time} - finished in #{duration}mins =========="
  end

  desc 'Manually clear long running Delayed Jobs' # mostly used to clear our jobs which raise a lot of Sentry errors
  task :clear_delayed_jobs, [:job_id] => [:environment] do |_, args|
    job_id = args[:job_id]
    if job_id.present?
      delayed_job = Delayed::Job.find(job_id)
      if delayed_job.present?
        puts "Removing Delayed::Job with ID #{job_id}"
        delayed_job.destroy
      else
        puts "Couldn't find Delayed::Job with ID #{job_id}"
      end
    else
      puts 'Need a Job ID to find and clear a Delayed::Job'
    end
  end

  desc 'Retrospectively generate delivery documents with the correct version number for existing orders'
  task :generate_delivery_documents, [:per_orders] => :environment do |_, args|
    per_orders = args[:per_orders].present? ? args[:per_orders].to_i : 1000

    delivery_detailed_orders = Order.joins(order_suppliers: :documents).where(documents: { kind: 'supplier_order_delivery_details' }).select(:id)
    detailed_orders = Order.joins(order_suppliers: %i[supplier_profile documents]).where.not(orders: { id: delivery_detailed_orders.map(&:id) }).where.not(orders: { status: %w[cancelled paused] }).where(documents: { kind: 'supplier_order_details' }).order(delivery_at: :desc).first(per_orders)

    detailed_orders.each_with_index do |order, idx|
      puts ''
      puts "#{idx} => Order ##{order.id} - #{order.order_type} #{order.status} => #{order.delivery_at.to_s(:datetime)}"
      order_suppliers = order.order_suppliers.joins(:documents).uniq
      order_suppliers.each do |order_supplier|
        supplier = order_supplier.supplier_profile
        puts "Supplier #{supplier.name}"
        supplier_order_detail_documents = order_supplier.documents.where(kind: 'supplier_order_details')

        if supplier_order_detail_documents.size > 1
          max_version = supplier_order_detail_documents.map(&:version).max
          details_document = supplier_order_detail_documents.detect{|document| document.version == max_version }
          delivery_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: "order-#{order.id}-#{order.version_ref}", variation: 'delivery_docket', version_override: max_version).call
        else
          details_document = supplier_order_detail_documents.first
          delivery_document = Documents::Generate::SupplierOrderDetails.new(order: order, supplier: supplier, reference: "order-#{order.id}-#{order.version_ref}", variation: 'delivery_docket', version_override: details_document.version).call
        end
        puts "#{details_document.kind} => #{details_document.version}"
        puts "#{delivery_document.kind} => #{delivery_document.version}"
      end
    end
  end

  desc 'Retrospectively set the category for order lines without any category'
  task :set_order_line_categories, %i[starts ends supplier_id] => :environment do |_, args|
    now = Time.zone.now
    starts_at = args[:starts].present? ? Time.zone.parse(args[:starts]) : now.beginning_of_year
    ends_at = args[:ends].present? ? Time.zone.parse(args[:ends]) : now
    supplier = args[:supplier_id].present? ? SupplierProfile.where(id: args[:supplier_id]).first : nil

    start_time = now.dup
    puts "=========== Starting cleanup.set_order_line_catgories at #{start_time} ==========="

    puts ''
    puts "Getting data for #{starts_at.to_s(:date)} and #{ends_at.to_s(:date)}"
    puts "With Supplier #{supplier.name}" if supplier.present?
    order_lines = OrderLine.all
    order_lines = order_lines.joins(:order)
    order_lines = order_lines.where(orders: { status: %w[new amended confirmed delivered pending] })
    order_lines = order_lines.where(orders: { delivery_at: [starts_at..ends_at] })
    if supplier.present?
      order_lines = order_lines.where(supplier_profile: supplier)
    else
      order_lines = order_lines.where(category_id: nil)
    end
    order_lines = order_lines.includes(menu_section: :categories)
    section_grouped_order_lines = order_lines.group_by(&:menu_section)

    puts ''
    puts "Order Lines #{order_lines.size}"
    puts "Sections #{section_grouped_order_lines.size}"

    section_grouped_order_lines.each_with_index do |(menu_section, section_lines), idx|
      next if menu_section.blank?

      potential_category = menu_section.categories.order(weight: :asc).first
      next if potential_category.blank?

      puts ''
      puts "#{idx + 1} Menu Section: #{menu_section.name} => #{potential_category.name}"
      section_lines.each do |order_line|
        order_line.update_column(:category_id, potential_category.id)
        print 'lc-'
      end
    end
    sync_duration = ((Time.zone.now - start_time) / 1.minute).round(2)
    puts "=========== Starting cleanup.set_order_line_catgories at #{Time.zone.now} - Duration: #{sync_duration} mins ==========="
  end

  desc 'set special holday colors'
  task set_special_holiday_colors: :environment do
    filepath = Rails.root.join('lib', 'files', 'special_holidays.csv')
    csv_holidays = CSV.foreach(filepath, { headers: true, header_converters: lambda { |h| h.downcase.gsub(' ', '_').to_sym } })

    csv_holidays.each_with_index do |csv_holiday, idx|
      holiday = Holiday.where(
        name: csv_holiday[:name],
        on_date: Time.zone.parse(csv_holiday[:date]).beginning_of_day
      ).first
      if holiday.update(color: csv_holiday[:color])
        puts "#{idx + 1} #{csv_holiday[:name]} - #{Date.parse(csv_holiday[:date])} updated #{holiday.color}"
      end
    end
  end

  desc 'cleanup access permission scopes'
  task access_permission_scopes: :environment do
    access_permissions = AccessPermission.where(scope: ['full_access', '', nil, 'company_team_admin'])

    access_permissions.each_with_index do |access_permission, idx|
      puts "#{idx + 1}/#{access_permissions.size}"
      access_permission.update(scope: 'company_team_admin')
    end
  end

  desc 'set supplier sustainable flags'
  task set_supplier_sustainable_flags: :environment do
    suppliers = SupplierProfile.where(is_searchable: true).includes(:supplier_flags)

    filepath = Rails.root.join('lib', 'files', 'supplier_flags.csv')
    csv_suppliers = CSV.foreach(filepath, { headers: true, header_converters: lambda { |h| h.downcase.gsub(' ', '_').to_sym } })

    csv_suppliers.each_with_index do |csv_supplier, idx|
      supplier = suppliers.detect{|s| s.id.to_i == csv_supplier[:id].to_i }
      flags = {}
      next if supplier.blank? || supplier.name.strip != csv_supplier[:name].strip

      SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS.each do |field|
        # next if supplier.send(field) && field != :is_eco_friendly

        flag_true = csv_supplier[field].present? && csv_supplier[field] == 'YES'
        next if flag_true == supplier.send(field)

        flags[field] = flag_true
      end
      next if flags.keys.blank?

      puts ''
      puts "#{idx + 1} - #{supplier.name} - #{supplier.id}"
      flags.each do |k, v|
        puts "#{k} => #{v}"
      end

      supplier.supplier_flags.update(flags)
    end # csv suppliers
  end

  desc 'Retrospectively set Customer Quote UUIDs'
  task set_customer_quote_uuids: :environment do
    quotes = CustomerQuote.where(uuid: nil).order(id: :asc)

    quotes.each_with_index do |quote, idx|
      quote.update_column(:uuid, SecureRandom.uuid)
      customer = quote.customer_profile
      puts "#{idx + 1}/#{quotes.size} #{customer.present? ? "=> #{customer.name}" : ''}"
    end
  end

  desc 'Archive old quotes'
  task :archive_old_quotes, [:since] => :environment do |_, args|
    since = args[:since].present? ? Time.zone.parse(args[:since]) : Time.zone.now.beginning_of_year

    quotes = CustomerQuote.where(status: 'submitted').where('created_at < ?', since)

    quotes.each_with_index do |quote, idx|
      quote.update(status: 'archived')
      customer = quote.customer_profile
      puts "#{idx + 1}/#{quotes.size} #{customer.present? ? "=> #{customer.name}" : ''}"
    end
  end

end
