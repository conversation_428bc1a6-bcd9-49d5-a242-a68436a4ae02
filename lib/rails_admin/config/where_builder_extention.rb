# as per the solution from the rails_admin issue @ https://github.com/sferik/rails_admin/issues/1434#issuecomment-223698197
require 'rails_admin/adapters/active_record'

module RailsAdmin::Adapters::ActiveRecord
  module WhereBuilderExtension
    def initialize(scope)
      @includes = []
      super(scope)
    end

    def add(field, value, operator)
      @includes.push(field.name) if field.association?
      super(field, value, operator)
    end

    def build
      scope = super
      scope = scope.includes(*@includes.uniq) if @includes.any?
      scope
    end
  end

  class WhereBuilder
    prepend WhereBuilderExtension
  end
end
