require 'rails_admin/config/actions'
require 'rails_admin/config/actions/base'

module RailsAdmin
  module Config
    module Actions
      class DeprecateUser < RailsAdmin::Config::Actions::Base
        RailsAdmin::Config::Actions.register(self)

        register_instance_option :member? do
          true
        end

        register_instance_option :bulkable? do
          false
        end

        register_instance_option :only do
          User
        end

        register_instance_option :visible do
          user = bindings[:controller].current_user
          user.present? && user.super_admin? && bindings[:object].present? && bindings[:object].is_a?(User)
        end

        register_instance_option :controller do
          proc do
            user_deprecater = Admin::DeprecateUser.new(user: @object, admin: current_user).call
            if user_deprecater.success?
              flash[:notice] = 'Successfully Deprecated User'
            else
              flash[:error] = "Error deprecating user: #{user_deprecater.errors.join(',')}"
            end
            redirect_to rails_admin.edit_path('user', @object.id)
          end
        end

        register_instance_option :link_icon do
          'icon-trash'
        end

      end
    end
  end
end
