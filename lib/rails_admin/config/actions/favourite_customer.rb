require 'rails_admin/config/actions'
require 'rails_admin/config/actions/base'

module RailsAdmin
  module Config
    module Actions
      class FavouriteCustomer < RailsAdmin::Config::Actions::Base
        RailsAdmin::Config::Actions.register(self)

        register_instance_option :member? do
          true
        end

        register_instance_option :bulkable? do
          false
        end

        register_instance_option :controller do
          Proc.new do
            current_customer = @object.instance_of?(CustomerProfile) ? @object : CustomerProfile.where(id: @object.id).first
            existing_favourites = current_user.present? && current_user.favourite_customer_profiles.presence || []
            if existing_favourites.include?(current_customer)
              new_favourites = existing_favourites - [current_customer]
            else
              new_favourites = existing_favourites + [current_customer]
            end
            current_user.update(favourite_customer_profiles: new_favourites.uniq)
            redirect_params = {}
            base_uri = URI.parse(request.referer)
            if base_uri.query.present?
              redirect_params = base_uri.query.split('&').map{|x| x.split('=') }.to_h
            end

            if request.referer.include?('my_favourite_customers')
              redirect_to rails_admin.my_favourite_customers_path(**redirect_params)
            else
              redirect_to rails_admin.index_path(**redirect_params)
            end
          end
        end

        register_instance_option :link_icon do
          user = bindings[:controller].current_user
          is_favourite = user.present? && user.favourite_customers.where(customer_profile_id: bindings[:object].id).present?
          is_favourite ? 'icon-heart text-success' : 'icon-heart text-danger'
        end

      end
    end
  end
end
