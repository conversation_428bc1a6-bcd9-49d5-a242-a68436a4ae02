require 'rails_admin/config/actions'
require 'rails_admin/config/actions/base'

module RailsAdmin
  module Config
    module Actions
      class Clone < Base
        RailsAdmin::Config::Actions.register(self)

        register_instance_option :member do
          true
        end

        register_instance_option :http_methods do
          [:get]
        end

        register_instance_option :controller do
          Proc.new do
            model_cloner  = RailsAdminClone::ModelCloner.new(@object)
            custom_method = model_config.clone_config.custom_method

            if custom_method.present?
              @object = model_cloner.method_clone(custom_method)
            else
              @object = model_cloner.default_clone
            end

            @authorization_adapter && @authorization_adapter.attributes_for(:new, @abstract_model).each do |name, value|
              @object.send("#{name}=", value)
            end

            if (object_params = params[@abstract_model.to_param])
              @object.set_attributes(@object.attributes.merge(object_params))
            end

            # SUPP-1655 - custom orders that are cloned are all using the same location_id
            # we need to explicity create a new one per cloned order
            old_location = Location.find(@object.order_lines.first.location_id)
            dup_location = old_location.dup
            dup_location.order_id = nil
            dup_location.save

            @object.order_lines.each do |ol|
              ol.location_id = dup_location.id
            end

            # coerce the order to be set to draft
            @object.send('status=', 'draft')

            respond_to do |format|
              format.html do
                if @object.class.name == 'CustomOrder'
                  render 'rails_admin/main/custom_order'
                else
                  render @action.template_name
                end
              end
              format.js { render @action.template_name, layout: false }
            end
          end
        end

        register_instance_option :template_name do
          :clone
        end

        register_instance_option :link_icon do
          'icon-copy fa fa-files-o'
        end
      end
    end
  end
end
