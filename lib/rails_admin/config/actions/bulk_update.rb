# following solution from https://github.com/sferik/rails_admin/issues/1818#issuecomment-27398321
require 'rails_admin/config/actions'
require 'rails_admin/config/actions/base'

module RailsAdmin
  module Config
    module Actions
      class BulkUpdate < Base
        RailsAdmin::Config::Actions.register(self)

        register_instance_option :bulkable? do
          true
        end

        register_instance_option :controller do
          Proc.new do
            @response = {}
            if request.post?
              Admin::BulkUpdate.new(model_name: params[:model_name], bulk_ids: params[:bulk_ids], update_params: params[params[:model_name]]).call
            end
            redirect_back(fallback_location: Rails.application.routes.url_helpers.prismic_root_url(host: yordar_credentials(:default_host)))
          end
        end

      end
    end
  end
end
