require 'rails_admin/config/actions'
require 'rails_admin/config/actions/base'

module RailsAdmin
	module Config
		module Actions
			class SignInAs < RailsAdmin::Config::Actions::Base
				RailsAdmin::Config::Actions.register(self)

				register_instance_option :member do
					true
				end

				register_instance_option :link_icon do
					'icon-user'
				end

				register_instance_option :controller do
					Proc.new do
						redirect_to main_app.sign_in_as_path @object.is_a?(User) ? @object.id : @object.user.id
					end
				end
			end
		end
	end
end
