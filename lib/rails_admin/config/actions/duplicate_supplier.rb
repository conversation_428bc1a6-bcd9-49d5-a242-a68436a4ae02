require 'rails_admin/config/actions'
require 'rails_admin/config/actions/base'

module RailsAdmin
	module Config
		module Actions
			class DuplicateSupplier < Base
				RailsAdmin::Config::Actions.register(self)

				register_instance_option :pjax? do
					false
				end

			 	register_instance_option :member do
 					true
 				end

			 	register_instance_option :link_icon do
 					'icon-copy fa fa-files-o'
 				end

			 	register_instance_option :controller do
 					Proc.new do
 						supplier_duplicator = Suppliers::DuplicateSupplier.new(supplier: @object).call
 						duplicate_supplier = supplier_duplicator.duplicate_supplier
 						redirect_to rails_admin.edit_path('supplier_profile', duplicate_supplier.id)
 					end
 				end
			end
		end
	end
end
