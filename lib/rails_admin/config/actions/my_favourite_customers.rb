require 'rails_admin/config/actions'
require 'rails_admin/config/actions/base'

module RailsAdmin
  module Config
    module Actions
      class MyFavouriteCustomers < RailsAdmin::Config::Actions::Base
        RailsAdmin::Config::Actions.register(self)

        register_instance_option :collection do
          true
        end

        register_instance_option :only do
          CustomerProfile
        end

        register_instance_option :controller do
          proc do
            @objects = current_user.favourite_customer_profiles
            render :index
          end
        end

        register_instance_option :link_icon do
          'icon-heart'
        end

      end
    end
  end
end
