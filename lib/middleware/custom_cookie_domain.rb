# Dynamically set the session cookie domain based on request host
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def initialize(app, default_domain)
    @app = app
    @default_domain = default_domain
    @host_pattern = Regexp.new("#{default_domain}\\.")
  end

  def call(env)
    host = Rack::Request.new(env).host
    env["rack.session.options"][:domain] = determine_cookie_domain(host)

    @app.call(env)
  end

private

  def determine_cookie_domain(host)
    if host.match(@host_pattern).present?
      host_tld = host.split(@host_pattern).last
      ".#{@default_domain}.#{host_tld}"
    else
      host
    end
  end

end