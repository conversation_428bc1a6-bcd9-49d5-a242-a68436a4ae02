# Dynamically set the TLD length based on request host
class DynamicTldLength

  def initialize(app, default_domain, default_tld)
    @app = app
    @host_pattern = Regexp.new("#{default_domain}\\.")
    @default_tld = default_tld
  end

  def call(env)
    request_host = Rack::Request.new(env).host
    ActionDispatch::Http::URL.tld_length = determine_tld_length(request_host)

    @app.call(env)
  ensure
    ActionDispatch::Http::URL.tld_length = @default_tld
  end

private

  def determine_tld_length(host)
    if host.match(@host_pattern).present?
      host.split(@host_pattern).last.split('.').length
    else
      @default_tld
    end
  end
end