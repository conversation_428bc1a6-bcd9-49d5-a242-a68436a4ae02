GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.0.9)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_record_union (1.3.0)
      activerecord (>= 4.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemerchant (1.133.0)
      activesupport (>= 4.2)
      builder (>= 2.1.2, < 4.0.0)
      i18n (>= 0.6.9)
      nokogiri (~> 1.4)
      rexml (~> 3.2.5)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.2)
      activemodel (> 5.x)
      activesupport (> 5.x)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    ast (2.4.2)
    awesome_print (1.9.2)
    aws_cf_signer (0.1.3)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bigdecimal (3.1.8)
    bootsnap (1.17.0)
      msgpack (~> 1.2)
    builder (3.3.0)
    bullet (6.1.5)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (11.1.3)
    cancancan (3.3.0)
    capybara (2.18.0)
      addressable
      mini_mime (>= 0.1.3)
      nokogiri (>= 1.3.3)
      rack (>= 1.0.0)
      rack-test (>= 0.5.4)
      xpath (>= 2.0, < 4.0)
    carrierwave (3.0.5)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    chunky_png (1.4.0)
    cliver (0.3.2)
    cloudinary (1.18.1)
      aws_cf_signer
      rest-client
    coderay (1.1.3)
    concurrent-ruby (1.3.3)
    connection_pool (2.4.1)
    crass (1.0.6)
    css_parser (1.16.0)
      addressable
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.3.4)
    delayed_job (4.1.11)
      activesupport (>= 3.0, < 8.0)
    delayed_job_active_record (4.1.8)
      activerecord (>= 3.0, < 8.0)
      delayed_job (>= 3.0, < 5)
    devise (4.9.3)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.0)
    docile (1.4.0)
    docusign_esign (1.0.0)
      json (~> 1.8, >= 1.8.3)
      jwt (~> 2.0, >= 2.0.0)
      typhoeus (~> 1.0, >= 1.0.1)
    domain_name (0.6.20231109)
    doorkeeper (5.6.8)
      railties (>= 5)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    erubi (1.13.0)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    execjs (2.9.1)
    factory_bot (6.4.2)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.2)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.2.2)
      i18n (>= 1.8.11, < 2)
    faraday (0.17.6)
      multipart-post (>= 1.2, < 3)
    ffi (1.17.0)
    formatador (1.1.0)
    geocoder (1.8.2)
    get_process_mem (1.0.0)
      bigdecimal (>= 2.0)
      ffi (~> 1.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    guard (2.18.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-rspec (4.7.3)
      guard (~> 2.1)
      guard-compat (~> 1.1)
      rspec (>= 2.99.0, < 4.0)
    haml (5.2.2)
      temple (>= 0.8.0)
      tilt
    haml-rails (2.1.0)
      actionpack (>= 5.1)
      activesupport (>= 5.1)
      haml (>= 4.0.6)
      railties (>= 5.1)
    hashie (5.0.0)
    haversine (0.3.2)
    highcharts-rails (6.0.3)
      railties (>= 3.1)
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    hubspot-ruby (0.9.0)
      activesupport (>= 3.0.0)
      httparty (>= 0.10.0)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jquery-fileupload-rails (1.0.0)
      actionpack (>= 3.1)
      railties (>= 3.1)
      sassc
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-timepicker-addon-rails (1.4.1)
      railties (>= 3.1)
    jquery-ui-rails (6.0.1)
      railties (>= 3.2.16)
    js-routes (2.2.8)
      railties (>= 4)
    js_cookie_rails (2.2.0)
      railties (>= 3.1)
    json (1.8.6)
    jwt (2.7.1)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lumberjack (1.2.10)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    method_source (1.1.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.1205)
    mini_magick (4.12.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.7)
    minitest (5.24.1)
    msgpack (1.7.2)
    multi_xml (0.6.0)
    multipart-post (2.3.0)
    nenv (0.3.0)
    nested_form (0.3.2)
    net-http-persistent (3.1.0)
      connection_pool (~> 2.2)
    net-imap (0.3.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    netrc (0.11.0)
    newrelic_rpm (9.6.0)
      base64
    nio4r (2.7.3)
    nokogiri (1.15.6)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.15.6-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.15.6-x86_64-linux)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    oauth (1.1.0)
      oauth-tty (~> 1.0, >= 1.0.1)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oauth-tty (1.0.5)
      version_gem (~> 1.1, >= 1.1.1)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    orm_adapter (0.5.0)
    paper_trail (14.0.0)
      activerecord (>= 6.0)
      request_store (~> 1.4)
    parallel (1.24.0)
    parser (3.3.0.2)
      ast (~> 2.4.1)
      racc
    pdf-core (0.9.0)
    pg (1.5.4)
    pg_search (2.3.6)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    poltergeist (1.18.1)
      capybara (>= 2.1, < 4)
      cliver (~> 0.3.1)
      websocket-driver (>= 0.2.0)
    prawn (2.4.0)
      pdf-core (~> 0.9.0)
      ttfunk (~> 1.7)
    prawn-svg (0.32.0)
      css_parser (~> 1.6)
      prawn (>= 0.11.1, < 3)
      rexml (~> 3.2)
    prawn-table (0.2.2)
      prawn (>= 1.3.0, < 3.0.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    public_suffix (5.0.4)
    puma (6.4.2)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.9)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-pjax (1.1.0)
      nokogiri (~> 1.5)
      rack (>= 1.1)
    rack-proxy (0.7.7)
      rack
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
      sprockets-rails (>= 2.0.0)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    rails_admin (2.2.1)
      activemodel-serializers-xml (>= 1.0)
      builder (~> 3.1)
      haml (>= 4.0, < 6)
      jquery-rails (>= 3.0, < 5)
      jquery-ui-rails (>= 5.0, < 7)
      kaminari (>= 0.14, < 2.0)
      nested_form (~> 0.3)
      rack-pjax (>= 0.7)
      rails (>= 5.0, < 7)
      remotipart (~> 1.3)
      sassc-rails (>= 1.3, < 3)
    rails_admin_clone (0.0.6)
      rails (>= 3.2)
      rails_admin (>= 0.4)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    recaptcha (5.16.0)
    redis (4.8.1)
    regexp_parser (2.9.0)
    remote_syslog_logger (1.0.4)
      syslog_protocol
    remotipart (1.4.4)
    request_store (1.5.1)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.2.6)
    rqrcode (2.2.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    rspec (3.12.0)
      rspec-core (~> 3.12.0)
      rspec-expectations (~> 3.12.0)
      rspec-mocks (~> 3.12.0)
    rspec-core (3.12.2)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.6)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (6.1.0)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.12)
      rspec-expectations (~> 3.12)
      rspec-mocks (~> 3.12)
      rspec-support (~> 3.12)
    rspec-support (3.12.1)
    rubocop (1.31.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.18.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.30.0)
      parser (>= *******)
    rubocop-rails (2.15.2)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.7.0, < 2.0)
    rubocop-rspec (2.12.1)
      rubocop (~> 1.31)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.0)
      ffi (~> 1.12)
    ruby_http_client (3.5.5)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sass-rails (5.1.0)
      railties (>= 5.2.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    select2-rails (4.0.13)
    semantic_range (3.0.0)
    sendgrid-ruby (6.7.0)
      ruby_http_client (~> 3.4)
    sentry-raven (2.13.0)
      faraday (>= 0.7.6, < 1.0)
    shellany (0.0.1)
    shortener (1.0.1)
      voight_kampff (~> 2.0)
    shoulda-matchers (4.0.1)
      activesupport (>= 4.2.0)
    simple_form (5.3.0)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    slack-notifier (2.4.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-helpers (1.4.0)
      sprockets (>= 2.2)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    ssrf_filter (1.1.2)
    stripe (4.24.0)
      faraday (~> 0.13)
      net-http-persistent (~> 3.0)
    syslog_protocol (0.9.2)
    temple (0.10.3)
    thor (1.3.1)
    tilt (2.3.0)
    timeout (0.4.1)
    tinymce-rails (********)
      railties (>= 3.1.1)
    ttfunk (1.7.0)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    unicode-display_width (2.5.0)
    uniform_notifier (1.16.0)
    version_gem (1.1.3)
    voight_kampff (2.0.0)
      rack (>= 1.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    webpacker (5.4.4)
      activesupport (>= 5.2)
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xeroizer (3.0.1)
      activesupport
      builder (>= 2.1.2)
      i18n
      nokogiri
      oauth (>= 0.4.5)
      oauth2 (>= 1.4.0)
      tzinfo
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.17)

PLATFORMS
  ruby
  x86_64-darwin-20
  x86_64-linux

DEPENDENCIES
  active_record_union
  activemerchant
  annotate
  awesome_print
  bootsnap
  bullet (~> 6.1.5)
  byebug
  cancancan (~> 3.3.0)
  capybara (~> 2.18.0)
  carrierwave
  cloudinary (= 1.18.1)
  database_cleaner-active_record (>= 2.0)
  delayed_job_active_record
  devise
  docusign_esign (= 1.0.0)
  doorkeeper (~> 5.5)
  dotenv-rails
  factory_bot (= 6.4.2)
  factory_bot_rails (= 6.4.2)
  faker
  geocoder (~> 1.5)
  get_process_mem (~> 1.0.0)
  guard-rspec
  haml (~> 5.1)
  haml-rails
  haversine
  highcharts-rails
  hubspot-ruby
  jbuilder
  jquery-fileupload-rails
  jquery-rails
  jquery-timepicker-addon-rails
  jquery-ui-rails
  js-routes
  js_cookie_rails
  kaminari
  newrelic_rpm
  paper_trail
  pg
  pg_search
  poltergeist (~> 1.18.1)
  prawn (= 2.4.0)
  prawn-svg (~> 0.32.0)
  prawn-table (~> 0.2.2)
  puma
  rack-attack
  rack-cors
  rails (~> 6.1, >= *******)
  rails-controller-testing
  rails_admin (~> 2.2, >= 2.2.1)
  rails_admin_clone
  recaptcha (~> 5.16)
  redis (~> 4.8.1)
  remote_syslog_logger
  rqrcode (~> 2.0)
  rspec-rails
  rubocop
  rubocop-rails
  rubocop-rspec
  sass-rails (~> 5.0)
  select2-rails
  sendgrid-ruby
  sentry-raven
  shortener
  shoulda-matchers (~> 4.0.1)
  simple_form
  simplecov
  slack-notifier
  sprockets (= 3.7.2)
  sprockets-helpers
  stripe (~> 4.24.0)
  tinymce-rails (~> ********)
  uglifier (>= 1.0.3)
  webpacker (~> 5.4.2)
  xeroizer (~> 3.0.1)

RUBY VERSION
   ruby 2.7.1p83

BUNDLED WITH
   2.3.5
